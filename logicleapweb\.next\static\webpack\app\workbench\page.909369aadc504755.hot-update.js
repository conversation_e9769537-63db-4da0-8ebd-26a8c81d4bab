"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/workbench/page",{

/***/ "(app-pages-browser)/./app/workbench/components/CourseListEditModal.tsx":
/*!**********************************************************!*\
  !*** ./app/workbench/components/CourseListEditModal.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FileText,Plus,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FileText,Plus,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FileText,Plus,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FileText,Plus,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FileText,Plus,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _lib_api_course_management__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api/course-management */ \"(app-pages-browser)/./lib/api/course-management.ts\");\n/* harmony import */ var _lib_api_course__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api/course */ \"(app-pages-browser)/./lib/api/course.ts\");\n/* harmony import */ var _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api/upload */ \"(app-pages-browser)/./lib/api/upload.ts\");\n/* harmony import */ var _lib_api_works__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/api/works */ \"(app-pages-browser)/./lib/api/works.ts\");\n/* harmony import */ var _barrel_optimize_names_Select_antd__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Select!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/select/index.js\");\n/* harmony import */ var logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\");\n/* harmony import */ var _TemplatePickerModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./TemplatePickerModal */ \"(app-pages-browser)/./app/workbench/components/TemplatePickerModal.tsx\");\n/* harmony import */ var _CourseListEditModal_css__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./CourseListEditModal.css */ \"(app-pages-browser)/./app/workbench/components/CourseListEditModal.css\");\n/* harmony import */ var _NewPublishTaskModal_css__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./NewPublishTaskModal.css */ \"(app-pages-browser)/./app/workbench/components/NewPublishTaskModal.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n// 获取系列课程详情\nconst fetchSeriesDetail = async (seriesId)=>{\n    console.log(\"\\uD83D\\uDD0D 获取系列课程详情，seriesId:\", seriesId);\n    console.log(\"\\uD83D\\uDD17 调用API: GET /api/v1/course-management/series/{seriesId}\");\n    const response = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getSeriesDetail(seriesId);\n    console.log(\"\\uD83D\\uDCE1 系列详情API响应:\", response);\n    return response.data;\n};\n// API调用函数 - 获取系列下的所有课程（包含未发布和已发布）\nconst fetchCourseList = async (seriesId)=>{\n    console.log(\"\\uD83D\\uDD0D 获取系列课程列表，seriesId:\", seriesId);\n    console.log(\"\\uD83D\\uDD17 调用API: GET /api/v1/course-management/series/{seriesId}/courses\");\n    // 使用课程管理API获取所有状态的课程\n    const response = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getManagementSeriesCourses(seriesId, {\n        page: 1,\n        pageSize: 100\n    });\n    console.log(\"\\uD83D\\uDCE1 API响应:\", response);\n    return response.data;\n};\n// 获取课程详情\nconst fetchCourseDetail = async (seriesId, courseId)=>{\n    return await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getCourseMarketplaceDetail(seriesId, courseId);\n};\n// 获取课程标签\nconst fetchCourseTags = async ()=>{\n    try {\n        console.log(\"\\uD83D\\uDD0D 开始调用 courseApi.getCourseTags\");\n        const result = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getCourseTags({\n            page: 1,\n            pageSize: 100,\n            status: 1 // 只获取启用的标签\n        });\n        console.log(\"\\uD83D\\uDD0D courseApi.getCourseTags 返回结果:\", result);\n        return result;\n    } catch (error) {\n        console.error(\"\\uD83D\\uDD0D courseApi.getCourseTags 调用失败:\", error);\n        throw error;\n    }\n};\nconst CourseListEditModal = (param)=>{\n    let { isVisible, onClose, onSave, seriesTitle, seriesCoverImage, seriesId = 123 // 默认值，实际使用时应该传入真实的seriesId\n     } = param;\n    var _getSelectedCourse, _getSelectedCourse1, _getSelectedCourse2, _getSelectedCourse3, _courseDetail_contentConfig_video, _courseDetail_contentConfig, _courseDetail_contentConfig_document, _courseDetail_contentConfig1, _courseList_find, _courseList_find1;\n    _s();\n    const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_6__.GetNotification)();\n    const [courseList, setCourseList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [rightPanelType, setRightPanelType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"none\");\n    const [selectedCourseId, setSelectedCourseId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [uploadingFiles, setUploadingFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [isPublishing, setIsPublishing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isCreating, setIsCreating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPublishingSeries, setIsPublishingSeries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [seriesStatus, setSeriesStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0); // 0=草稿，1=已发布，2=已归档\n    // 删除确认弹窗状态\n    const [deleteConfirmVisible, setDeleteConfirmVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [courseToDelete, setCourseToDelete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isDeleting, setIsDeleting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 模板选择弹窗状态\n    const [isTemplatePickerOpen, setIsTemplatePickerOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingTitle, setEditingTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(seriesTitle);\n    const [courseGoals, setCourseGoals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [courseObjectives, setCourseObjectives] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [projectMembers, setProjectMembers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // 作品相关状态\n    const [works, setWorks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loadingWorks, setLoadingWorks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedWorkIds, setSelectedWorkIds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // 课程标签相关状态\n    const [courseTags, setCourseTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedTags, setSelectedTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [tagsLoading, setTagsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 课程详细编辑状态\n    const [courseDetail, setCourseDetail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: \"\",\n        description: \"\",\n        coverImage: \"\",\n        videoUrl: \"\",\n        videoName: \"\",\n        isVideoEnabled: false,\n        attachmentUrl: \"\",\n        attachmentName: \"\",\n        isAttachmentEnabled: false,\n        teachingMaterials: [],\n        // 支持teachingInfo结构\n        teachingInfo: [],\n        // 支持contentConfig结构\n        contentConfig: {\n            hasVideo: 0,\n            hasDocument: 0,\n            hasAudio: 0,\n            video: {\n                url: \"\",\n                name: \"\"\n            },\n            document: {\n                url: \"\",\n                name: \"\"\n            },\n            audio: {\n                url: \"\",\n                name: \"\"\n            }\n        },\n        courseContent: {\n            topic: \"\",\n            content: \"\"\n        },\n        isOneKeyOpen: false,\n        isDistributionEnabled: false,\n        distributionReward: \"\",\n        selectedTemplate: \"\",\n        isDistributionWater: false,\n        requiredEnergy: \"\",\n        energyAmount: \"\",\n        isDistributionLimit: false,\n        distributionConditions: {\n            inviteCount: \"\",\n            taskCount: \"\",\n            experience: \"\"\n        },\n        isDistributionTime: false,\n        distributionTimeConditions: {\n            startTime: \"\",\n            endTime: \"\"\n        },\n        distributionMaterials: [],\n        // 任务配置相关状态\n        taskConfig: {\n            taskName: \"\",\n            taskDuration: \"\",\n            taskDescription: \"\",\n            selfAssessmentItems: [\n                \"\"\n            ],\n            referenceWorks: [],\n            referenceResources: []\n        }\n    });\n    // 获取课程列表数据\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isVisible && seriesId) {\n            // 检查用户登录状态\n            const token = localStorage.getItem(\"token\");\n            console.log(\"\\uD83D\\uDD10 检查登录状态，token存在:\", !!token);\n            console.log(\"\\uD83D\\uDD0D seriesId:\", seriesId);\n            if (!token) {\n                console.error(\"❌ 用户未登录，无法获取课程列表\");\n                // 设置空列表，显示空状态\n                setCourseList([]);\n                setLoading(false);\n                return;\n            }\n            loadCourseList();\n            loadCourseTags();\n            loadSeriesDetail();\n            loadUserWorks();\n        }\n    }, [\n        isVisible,\n        seriesId\n    ]);\n    const loadCourseList = async ()=>{\n        try {\n            setLoading(true);\n            console.log(\"\\uD83D\\uDD0D 开始加载课程列表，seriesId:\", seriesId);\n            const response = await fetchCourseList(seriesId);\n            console.log(\"\\uD83D\\uDCE1 API响应:\", response);\n            if (response.code === 200) {\n                console.log(\"✅ 课程列表数据:\", response.data);\n                const courses = response.data.list || [];\n                console.log(\"✅ 解析的课程数组:\", courses);\n                console.log(\"\\uD83D\\uDCCA 课程ID详情:\", courses.map((c)=>({\n                        id: c.id,\n                        type: typeof c.id,\n                        title: c.title,\n                        status: c.status\n                    })));\n                console.log(\"\\uD83D\\uDCCA 课程状态统计:\", {\n                    total: courses.length,\n                    draft: courses.filter((c)=>c.status === 0).length,\n                    published: courses.filter((c)=>c.status === 1).length\n                });\n                setCourseList(courses);\n            } else {\n                console.error(\"❌ API返回错误:\", response);\n                setCourseList([]);\n            }\n        } catch (error) {\n            var _error_response, _error_response1, _error_response2;\n            console.error(\"❌ 加载课程列表失败:\", error);\n            // 检查是否是认证错误\n            if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 401) {\n                console.error(\"\\uD83D\\uDD10 认证失败，用户未登录或token已过期\");\n            } else if (((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status) === 403) {\n                console.error(\"\\uD83D\\uDEAB 权限不足，无法访问该系列课程\");\n            } else if (((_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.status) === 404) {\n                console.error(\"\\uD83D\\uDCED 系列课程不存在，seriesId:\", seriesId);\n            } else {\n                console.error(\"\\uD83D\\uDD27 其他错误:\", error.message);\n            }\n            setCourseList([]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 加载系列课程详情\n    const loadSeriesDetail = async ()=>{\n        try {\n            console.log(\"\\uD83D\\uDD0D 开始加载系列课程详情，seriesId:\", seriesId);\n            const response = await fetchSeriesDetail(seriesId);\n            console.log(\"\\uD83D\\uDCE1 系列详情响应:\", response);\n            if (response.code === 200 && response.data) {\n                const seriesData = response.data;\n                console.log(\"✅ 系列课程详情:\", seriesData);\n                setSeriesStatus(seriesData.status || 0);\n                console.log(\"\\uD83D\\uDCCA 系列课程状态:\", seriesData.status, \"(0=草稿，1=已发布，2=已归档)\");\n            } else {\n                console.error(\"❌ 获取系列详情失败:\", response.message);\n            }\n        } catch (error) {\n            console.error(\"❌ 加载系列详情异常:\", error);\n        }\n    };\n    // 加载用户作品数据\n    const loadUserWorks = async ()=>{\n        try {\n            var _response_data;\n            setLoadingWorks(true);\n            // 从localStorage获取用户ID\n            const userId = localStorage.getItem(\"userId\") || \"2896\"; // 默认使用2896\n            const response = await _lib_api_works__WEBPACK_IMPORTED_MODULE_5__.worksApi.getTeacherWorks(Number(userId), 1, 1000);\n            // 检查多种可能的数据结构\n            let worksList = [];\n            if (((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.code) === 200) {\n                var _response_data_data, _response_data1, _response_data2;\n                // 情况1: response.data.data.list\n                if ((_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : (_response_data_data = _response_data1.data) === null || _response_data_data === void 0 ? void 0 : _response_data_data.list) {\n                    worksList = response.data.data.list;\n                } else if (Array.isArray((_response_data2 = response.data) === null || _response_data2 === void 0 ? void 0 : _response_data2.data)) {\n                    worksList = response.data.data;\n                } else if (Array.isArray(response.data)) {\n                    worksList = response.data;\n                }\n            } else if (Array.isArray(response)) {\n                worksList = response;\n            }\n            setWorks(worksList);\n        } catch (error) {\n            console.error(\"加载作品数据失败:\", error);\n            setWorks([]);\n        } finally{\n            setLoadingWorks(false);\n        }\n    };\n    // 处理作品选择\n    const handleWorkSelect = (workId)=>{\n        if (selectedWorkIds.includes(workId)) {\n            // 取消选中\n            setSelectedWorkIds((prev)=>prev.filter((id)=>id !== workId));\n        } else {\n            // 选中\n            setSelectedWorkIds((prev)=>[\n                    ...prev,\n                    workId\n                ]);\n        }\n    };\n    // 鼠标滚轮事件处理 - 将垂直滚轮转换为水平滚动\n    const handleWheel = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e)=>{\n        const container = e.currentTarget;\n        const { scrollWidth, clientWidth } = container;\n        // 检查是否可以滚动\n        if (scrollWidth <= clientWidth) {\n            return; // 内容不够长，不需要滚动\n        }\n        // 阻止默认的垂直滚动行为\n        e.preventDefault();\n        // 将垂直滚轮转换为水平滚动\n        container.scrollLeft += e.deltaY;\n    }, []);\n    // 加载课程标签\n    const loadCourseTags = async ()=>{\n        try {\n            setTagsLoading(true);\n            console.log(\"\\uD83C\\uDFF7️ 开始加载课程标签\");\n            const response = await fetchCourseTags();\n            console.log(\"\\uD83D\\uDCE1 标签API完整响应:\", response);\n            // 检查响应结构\n            if (response && response.data) {\n                console.log(\"\\uD83D\\uDCCA 响应数据:\", response.data);\n                let tags = [];\n                // 处理标准的API响应格式 (response.data.list) - 根据实际API响应\n                if (response.data.list && Array.isArray(response.data.list)) {\n                    tags = response.data.list;\n                    console.log(\"✅ 从 data.list 解析到标签:\", tags.length, \"个\");\n                } else if (Array.isArray(response.data)) {\n                    tags = response.data;\n                    console.log(\"✅ 从 data 数组解析到标签:\", tags.length, \"个\");\n                } else if (response.data.data && response.data.data.list && Array.isArray(response.data.data.list)) {\n                    tags = response.data.data.list;\n                    console.log(\"✅ 从 data.data.list 解析到标签:\", tags.length, \"个\");\n                }\n                // 验证标签数据格式\n                console.log(\"\\uD83D\\uDD0D 原始标签数据:\", tags);\n                console.log(\"\\uD83D\\uDD0D 标签数据类型检查:\");\n                tags.forEach((tag, index)=>{\n                    var _tag_name;\n                    console.log(\"标签\".concat(index, \":\"), {\n                        tag,\n                        hasTag: !!tag,\n                        idType: typeof (tag === null || tag === void 0 ? void 0 : tag.id),\n                        nameType: typeof (tag === null || tag === void 0 ? void 0 : tag.name),\n                        nameValue: tag === null || tag === void 0 ? void 0 : tag.name,\n                        nameNotEmpty: (tag === null || tag === void 0 ? void 0 : (_tag_name = tag.name) === null || _tag_name === void 0 ? void 0 : _tag_name.trim()) !== \"\"\n                    });\n                });\n                const validTags = tags.filter((tag)=>{\n                    const isValid = tag && typeof tag.id === \"number\" && typeof tag.name === \"string\" && tag.name.trim() !== \"\";\n                    if (!isValid) {\n                        console.log(\"❌ 无效标签:\", tag, {\n                            hasTag: !!tag,\n                            idType: typeof (tag === null || tag === void 0 ? void 0 : tag.id),\n                            nameType: typeof (tag === null || tag === void 0 ? void 0 : tag.name),\n                            nameValue: tag === null || tag === void 0 ? void 0 : tag.name\n                        });\n                    }\n                    return isValid;\n                });\n                console.log(\"✅ 有效标签数量:\", validTags.length);\n                console.log(\"✅ 有效标签详情:\", validTags);\n                if (validTags.length > 0) {\n                    setCourseTags(validTags);\n                    console.log(\"✅ 成功设置真实标签数据\");\n                    return;\n                } else {\n                    console.warn(\"⚠️ 没有有效的标签数据\");\n                }\n            } else {\n                console.warn(\"⚠️ API响应格式不正确:\", response);\n            }\n            // 如果没有真实数据，设置空数组\n            console.log(\"\\uD83D\\uDCED 没有标签数据，设置空数组\");\n            setCourseTags([]);\n        } catch (error) {\n            var _error_response, _error_response1, _error_response2;\n            console.error(\"❌ 加载课程标签失败:\", error);\n            console.error(\"❌ 错误详情:\", {\n                message: error.message,\n                status: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status,\n                statusText: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.statusText,\n                data: (_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.data\n            });\n            // 发生错误时设置空数组\n            setCourseTags([]);\n        } finally{\n            setTagsLoading(false);\n        }\n    };\n    // 添加新课程\n    const addNewCourse = ()=>{\n        const newCourse = {\n            id: Date.now(),\n            seriesId: seriesId,\n            title: \"第\".concat(courseList.length + 1, \"课 - 新课时\"),\n            description: \"\",\n            coverImage: \"\",\n            orderIndex: courseList.length + 1,\n            status: 0,\n            statusLabel: \"草稿\",\n            hasVideo: 0,\n            hasDocument: 0,\n            hasAudio: 0,\n            videoDuration: 0,\n            videoDurationLabel: \"\",\n            videoName: \"\",\n            firstTeachingTitle: \"\",\n            resourcesCount: 0,\n            createdAt: new Date().toISOString(),\n            updatedAt: new Date().toISOString()\n        };\n        setCourseList([\n            ...courseList,\n            newCourse\n        ]);\n        // 自动选中新添加的课程\n        showCoursePanel(newCourse.id);\n    };\n    // 显示删除确认弹窗\n    const showDeleteConfirm = (id)=>{\n        setCourseToDelete(id);\n        setDeleteConfirmVisible(true);\n    };\n    // 确认删除课程\n    const confirmDeleteCourse = async ()=>{\n        if (!courseToDelete) return;\n        try {\n            setIsDeleting(true);\n            // 调用删除API\n            await _lib_api_course_management__WEBPACK_IMPORTED_MODULE_2__.courseManagementApi.deleteCourse(courseToDelete);\n            // 从列表中移除课程\n            setCourseList(courseList.filter((course)=>course.id !== courseToDelete));\n            // 如果删除的是当前选中的课程，清空右侧面板\n            if (selectedCourseId === courseToDelete) {\n                setRightPanelType(\"none\");\n                setSelectedCourseId(null);\n            }\n            // 关闭确认弹窗\n            setDeleteConfirmVisible(false);\n            setCourseToDelete(null);\n            // 显示成功提示\n            notification.success(\"课程已成功删除\");\n        } catch (error) {\n            console.error(\"删除课程失败:\", error);\n            notification.error(\"删除课程失败，请重试\");\n        } finally{\n            setIsDeleting(false);\n        }\n    };\n    // 取消删除\n    const cancelDelete = ()=>{\n        if (isDeleting) return; // 正在删除时不允许取消\n        setDeleteConfirmVisible(false);\n        setCourseToDelete(null);\n    };\n    // 更新课程标题\n    const updateCourseTitle = (id, newTitle)=>{\n        setCourseList(courseList.map((course)=>course.id === id ? {\n                ...course,\n                title: newTitle\n            } : course));\n    };\n    // 处理课程封面上传\n    const handleCoverUpload = async (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (file) {\n            // 检查文件类型\n            const allowedTypes = [\n                \"image/jpeg\",\n                \"image/jpg\",\n                \"image/png\",\n                \"image/gif\"\n            ];\n            if (!allowedTypes.includes(file.type)) {\n                notification.error(\"请选择 JPG、PNG 或 GIF 格式的图片文件\");\n                return;\n            }\n            // 检查文件大小 (10MB)\n            if (file.size > 10 * 1024 * 1024) {\n                notification.error(\"文件大小不能超过 10MB\");\n                return;\n            }\n            try {\n                console.log(\"\\uD83D\\uDCE4 开始上传课程封面:\", file.name);\n                // 添加到上传中的文件列表\n                setUploadingFiles((prev)=>new Set(prev).add(\"cover\"));\n                // 先显示预览图片\n                const previewUrl = URL.createObjectURL(file);\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        coverImage: previewUrl\n                    }));\n                // 上传到OSS\n                const imageUrl = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n                console.log(\"✅ 课程封面上传成功:\", imageUrl);\n                // 更新课程详情中的封面为真实URL\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        coverImage: imageUrl\n                    }));\n                // 同时更新课程列表中的封面\n                if (selectedCourseId) {\n                    setCourseList((prev)=>prev.map((course)=>course.id === selectedCourseId ? {\n                                ...course,\n                                coverImage: imageUrl\n                            } : course));\n                }\n                alert(\"课程封面上传成功！\");\n            } catch (error) {\n                console.error(\"❌ 课程封面上传失败:\", error);\n                notification.error(\"课程封面上传失败，请重试\");\n                // 上传失败时清除预览图片\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        coverImage: \"\"\n                    }));\n            } finally{\n                // 从上传中的文件列表移除\n                setUploadingFiles((prev)=>{\n                    const newSet = new Set(prev);\n                    newSet.delete(\"cover\");\n                    return newSet;\n                });\n            }\n        }\n    };\n    // 处理视频上传\n    const handleVideoUpload = async (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (file) {\n            // 检查文件类型\n            const allowedTypes = [\n                \"video/mp4\",\n                \"video/avi\",\n                \"video/mov\",\n                \"video/wmv\",\n                \"video/flv\"\n            ];\n            if (!allowedTypes.includes(file.type)) {\n                alert(\"请选择 MP4、AVI、MOV、WMV 或 FLV 格式的视频文件\");\n                return;\n            }\n            // 检查文件大小 (100MB)\n            if (file.size > 100 * 1024 * 1024) {\n                alert(\"视频文件大小不能超过 100MB\");\n                return;\n            }\n            try {\n                console.log(\"\\uD83D\\uDCE4 开始上传课程视频:\", file.name);\n                // 添加到上传中的文件列表\n                setUploadingFiles((prev)=>new Set(prev).add(\"video\"));\n                // 先显示预览视频\n                const previewUrl = URL.createObjectURL(file);\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        contentConfig: {\n                            ...prev.contentConfig,\n                            video: {\n                                url: previewUrl,\n                                name: file.name\n                            }\n                        }\n                    }));\n                // 上传到OSS\n                const videoUrl = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n                console.log(\"✅ 课程视频上传成功:\", videoUrl);\n                // 更新课程详情中的视频信息为真实URL\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        contentConfig: {\n                            ...prev.contentConfig,\n                            video: {\n                                url: videoUrl,\n                                name: file.name\n                            }\n                        }\n                    }));\n                // 同时更新课程列表中的视频信息\n                if (selectedCourseId) {\n                    setCourseList((prev)=>prev.map((course)=>course.id === selectedCourseId ? {\n                                ...course,\n                                contentConfig: {\n                                    ...course.contentConfig,\n                                    video: {\n                                        url: videoUrl,\n                                        name: file.name\n                                    }\n                                }\n                            } : course));\n                }\n                alert(\"课程视频上传成功！\");\n            } catch (error) {\n                console.error(\"❌ 课程视频上传失败:\", error);\n                alert(\"课程视频上传失败，请重试\");\n                // 上传失败时清除视频信息\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        contentConfig: {\n                            ...prev.contentConfig,\n                            video: {\n                                url: \"\",\n                                name: \"\"\n                            }\n                        }\n                    }));\n            } finally{\n                // 从上传中的文件列表移除\n                setUploadingFiles((prev)=>{\n                    const newSet = new Set(prev);\n                    newSet.delete(\"video\");\n                    return newSet;\n                });\n            }\n        }\n    };\n    // 触发视频文件选择\n    const triggerVideoUpload = ()=>{\n        const input = document.createElement(\"input\");\n        input.type = \"file\";\n        input.accept = \"video/mp4,video/avi,video/mov,video/wmv,video/flv\";\n        input.onchange = (e)=>handleVideoUpload(e);\n        input.click();\n    };\n    // 处理附件上传\n    const handleAttachmentUpload = async (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (file) {\n            // 检查文件类型\n            const allowedTypes = [\n                \"application/pdf\",\n                \"application/msword\",\n                \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\",\n                \"application/vnd.ms-excel\",\n                \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\",\n                \"application/vnd.ms-powerpoint\",\n                \"application/vnd.openxmlformats-officedocument.presentationml.presentation\",\n                \"text/plain\",\n                \"image/jpeg\",\n                \"image/png\",\n                \"image/gif\"\n            ];\n            if (!allowedTypes.includes(file.type)) {\n                alert(\"请选择支持的文件格式：PDF、DOC、DOCX、XLS、XLSX、PPT、PPTX、TXT、JPG、PNG、GIF\");\n                return;\n            }\n            // 检查文件大小 (10MB)\n            if (file.size > 10 * 1024 * 1024) {\n                alert(\"文件大小不能超过 10MB\");\n                return;\n            }\n            try {\n                console.log(\"\\uD83D\\uDCE4 开始上传课程附件:\", file.name);\n                // 添加到上传中的文件列表\n                setUploadingFiles((prev)=>new Set(prev).add(\"document\"));\n                // 上传到OSS\n                const documentUrl = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n                console.log(\"✅ 课程附件上传成功:\", documentUrl);\n                // 更新课程详情中的附件信息\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        contentConfig: {\n                            ...prev.contentConfig,\n                            hasDocument: 1,\n                            document: {\n                                url: documentUrl,\n                                name: file.name\n                            }\n                        }\n                    }));\n                alert(\"课程附件上传成功！\");\n            } catch (error) {\n                console.error(\"❌ 课程附件上传失败:\", error);\n                alert(\"课程附件上传失败，请重试\");\n            } finally{\n                // 从上传中的文件列表移除\n                setUploadingFiles((prev)=>{\n                    const newSet = new Set(prev);\n                    newSet.delete(\"document\");\n                    return newSet;\n                });\n            }\n        }\n    };\n    // 触发附件文件选择\n    const triggerAttachmentUpload = ()=>{\n        const input = document.createElement(\"input\");\n        input.type = \"file\";\n        input.accept = \".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.jpg,.png,.gif\";\n        input.onchange = (e)=>handleAttachmentUpload(e);\n        input.click();\n    };\n    // 处理教学附件上传\n    const handleTeachingMaterialUpload = async (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (file) {\n            // 检查文件类型\n            const allowedTypes = [\n                \"application/pdf\",\n                \"application/msword\",\n                \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\",\n                \"application/vnd.ms-excel\",\n                \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\",\n                \"application/vnd.ms-powerpoint\",\n                \"application/vnd.openxmlformats-officedocument.presentationml.presentation\",\n                \"text/plain\",\n                \"image/jpeg\",\n                \"image/png\",\n                \"image/gif\"\n            ];\n            if (!allowedTypes.includes(file.type)) {\n                alert(\"请选择支持的文件格式：PDF、DOC、DOCX、XLS、XLSX、PPT、PPTX、TXT、JPG、PNG、GIF\");\n                return;\n            }\n            // 检查文件大小 (10MB)\n            if (file.size > 10 * 1024 * 1024) {\n                alert(\"文件大小不能超过 10MB\");\n                return;\n            }\n            try {\n                console.log(\"\\uD83D\\uDCE4 开始上传教学材料:\", file.name);\n                // 添加到上传中的文件列表\n                setUploadingFiles((prev)=>new Set(prev).add(\"teaching-\".concat(Date.now())));\n                // 上传到OSS\n                const materialUrl = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n                console.log(\"✅ 教学材料上传成功:\", materialUrl);\n                // 添加到教学附件列表\n                const newMaterial = {\n                    type: file.type,\n                    name: file.name,\n                    url: materialUrl\n                };\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        teachingMaterials: [\n                            ...prev.teachingMaterials,\n                            newMaterial\n                        ]\n                    }));\n                alert(\"教学材料上传成功！\");\n            } catch (error) {\n                console.error(\"❌ 教学材料上传失败:\", error);\n                alert(\"教学材料上传失败，请重试\");\n            } finally{\n                // 从上传中的文件列表移除\n                setUploadingFiles((prev)=>{\n                    const newSet = new Set(prev);\n                    // 移除所有teaching-开头的项目\n                    Array.from(newSet).forEach((item)=>{\n                        if (item.startsWith(\"teaching-\")) {\n                            newSet.delete(item);\n                        }\n                    });\n                    return newSet;\n                });\n            }\n        }\n    };\n    // 触发教学附件文件选择\n    const triggerTeachingMaterialUpload = ()=>{\n        const input = document.createElement(\"input\");\n        input.type = \"file\";\n        input.accept = \".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.jpg,.png,.gif\";\n        input.onchange = (e)=>handleTeachingMaterialUpload(e);\n        input.click();\n    };\n    // 删除教学附件\n    const removeTeachingMaterial = (index)=>{\n        setCourseDetail((prev)=>({\n                ...prev,\n                teachingMaterials: prev.teachingMaterials.filter((_, i)=>i !== index)\n            }));\n    };\n    // 课程内容管理函数\n    const addTeachingInfoItem = ()=>{\n        setCourseDetail((prev)=>({\n                ...prev,\n                teachingInfo: [\n                    ...prev.teachingInfo,\n                    {\n                        title: \"\",\n                        content: \"\"\n                    }\n                ]\n            }));\n    };\n    const removeTeachingInfoItem = (index)=>{\n        setCourseDetail((prev)=>({\n                ...prev,\n                teachingInfo: prev.teachingInfo.filter((_, i)=>i !== index)\n            }));\n    };\n    const updateTeachingInfoTitle = (index, title)=>{\n        setCourseDetail((prev)=>{\n            const newTeachingInfo = [\n                ...prev.teachingInfo\n            ];\n            newTeachingInfo[index] = {\n                ...newTeachingInfo[index],\n                title\n            };\n            return {\n                ...prev,\n                teachingInfo: newTeachingInfo\n            };\n        });\n    };\n    const updateTeachingInfoContent = (index, content)=>{\n        setCourseDetail((prev)=>{\n            const newTeachingInfo = [\n                ...prev.teachingInfo\n            ];\n            newTeachingInfo[index] = {\n                ...newTeachingInfo[index],\n                content\n            };\n            return {\n                ...prev,\n                teachingInfo: newTeachingInfo\n            };\n        });\n    };\n    // 将UI格式的teachingInfo转换为API格式\n    const convertTeachingInfoForAPI = (teachingInfo)=>{\n        return teachingInfo.map((info)=>({\n                title: info.title,\n                content: info.content.split(\"\\n\").filter((line)=>line.trim()) // 按换行分割并过滤空行\n            }));\n    };\n    // 检查是否有未上传完成的文件（blob URL）\n    const checkForBlobUrls = (course)=>{\n        var _course_contentConfig_video, _course_contentConfig, _course_contentConfig_document, _course_contentConfig1;\n        const issues = [];\n        // 检查封面图片\n        if (course.coverImage && course.coverImage.startsWith(\"blob:\")) {\n            issues.push(\"课程封面图片\");\n        }\n        // 检查视频文件\n        if (((_course_contentConfig = course.contentConfig) === null || _course_contentConfig === void 0 ? void 0 : (_course_contentConfig_video = _course_contentConfig.video) === null || _course_contentConfig_video === void 0 ? void 0 : _course_contentConfig_video.url) && course.contentConfig.video.url.startsWith(\"blob:\")) {\n            issues.push(\"课程视频\");\n        }\n        // 检查文档附件\n        if (((_course_contentConfig1 = course.contentConfig) === null || _course_contentConfig1 === void 0 ? void 0 : (_course_contentConfig_document = _course_contentConfig1.document) === null || _course_contentConfig_document === void 0 ? void 0 : _course_contentConfig_document.url) && course.contentConfig.document.url.startsWith(\"blob:\")) {\n            issues.push(\"课程文档\");\n        }\n        // 检查教学材料\n        if (course.additionalResources) {\n            course.additionalResources.forEach((resource, index)=>{\n                if (resource.url && resource.url.startsWith(\"blob:\")) {\n                    issues.push(\"教学材料\".concat(index + 1));\n                }\n            });\n        }\n        return issues;\n    };\n    // 保存单个课程到后端\n    const saveCourse = async (course)=>{\n        try {\n            var _course_contentConfig_video, _course_contentConfig, _course_contentConfig_document, _course_contentConfig1;\n            // 验证必要的数据\n            if (!course.title || course.title.trim() === \"\") {\n                throw new Error(\"课程标题不能为空\");\n            }\n            // 检查是否有未上传完成的文件\n            const blobIssues = checkForBlobUrls(course);\n            if (blobIssues.length > 0) {\n                throw new Error(\"以下文件尚未上传完成，请等待上传完成后再保存：\".concat(blobIssues.join(\"、\")));\n            }\n            // 准备课程数据\n            const courseData = {\n                seriesId: seriesId,\n                title: course.title,\n                description: course.description || \"\",\n                coverImage: course.coverImage || \"\",\n                hasVideo: ((_course_contentConfig = course.contentConfig) === null || _course_contentConfig === void 0 ? void 0 : (_course_contentConfig_video = _course_contentConfig.video) === null || _course_contentConfig_video === void 0 ? void 0 : _course_contentConfig_video.url) ? 1 : 0,\n                hasDocument: ((_course_contentConfig1 = course.contentConfig) === null || _course_contentConfig1 === void 0 ? void 0 : (_course_contentConfig_document = _course_contentConfig1.document) === null || _course_contentConfig_document === void 0 ? void 0 : _course_contentConfig_document.url) ? 1 : 0,\n                hasAudio: 0,\n                videoDuration: 0,\n                contentConfig: course.contentConfig || {},\n                teachingInfo: convertTeachingInfoForAPI(course.teachingInfo || []),\n                additionalResources: course.additionalResources || [],\n                orderIndex: course.orderIndex || 1\n            };\n            console.log(\"\\uD83D\\uDCBE 准备保存课程数据:\", courseData);\n            let result;\n            // 判断是新课程还是更新课程\n            if (course.id && course.id > 1000000) {\n                // 新课程，使用创建API\n                console.log(\"\\uD83D\\uDCE4 创建新课程\");\n                const { data: response } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.createCourse(courseData);\n                result = response;\n                console.log(\"✅ 课程创建成功\");\n            } else {\n                // 现有课程，使用更新API\n                console.log(\"\\uD83D\\uDCE4 更新现有课程:\", course.id);\n                const { data: response } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.updateCourse(course.id, courseData);\n                result = response;\n                console.log(\"✅ 课程更新成功\");\n            }\n            return result;\n        } catch (error) {\n            console.error(\"❌ 保存课程失败:\", error);\n            throw error;\n        }\n    };\n    // 发布选中的课程\n    const handlePublishCourse = async ()=>{\n        if (!selectedCourseId) {\n            alert(\"请先选择要发布的课程\");\n            return;\n        }\n        const selectedCourse = courseList.find((course)=>course.id === selectedCourseId);\n        if (!selectedCourse) {\n            alert(\"未找到选中的课程\");\n            return;\n        }\n        // 检查课程是否已经发布\n        if (selectedCourse.status === 1) {\n            alert(\"该课程已经发布，无需重复发布\");\n            return;\n        }\n        try {\n            setIsPublishing(true);\n            console.log(\"\\uD83D\\uDCE4 开始发布课程\");\n            console.log(\"\\uD83D\\uDCE4 selectedCourseId:\", selectedCourseId, \"类型:\", typeof selectedCourseId);\n            console.log(\"\\uD83D\\uDCE4 课程信息:\", selectedCourse);\n            console.log(\"\\uD83D\\uDCE4 课程ID字段:\", selectedCourse.id, \"类型:\", typeof selectedCourse.id);\n            console.log(\"\\uD83D\\uDCE4 课程列表中的所有ID:\", courseList.map((c)=>({\n                    id: c.id,\n                    type: typeof c.id,\n                    title: c.title\n                })));\n            // 确保使用正确的课程ID\n            const courseIdToPublish = selectedCourse.id;\n            console.log(\"\\uD83D\\uDCE4 即将发布的课程ID:\", courseIdToPublish, \"类型:\", typeof courseIdToPublish);\n            const { data: response } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.publishCourse(courseIdToPublish);\n            if (response.code === 200) {\n                console.log(\"✅ 课程发布成功:\", response.data);\n                alert(\"课程发布成功！\");\n                // 刷新课程列表以更新状态\n                await loadCourseList();\n            } else {\n                console.error(\"❌ 发布课程失败:\", response.message);\n                alert(response.message || \"发布课程失败\");\n            }\n        } catch (error) {\n            var _error_response, _error_response_data, _error_response1;\n            console.error(\"❌ 发布课程失败:\", error);\n            console.error(\"❌ 错误详情:\", (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data);\n            // 处理具体的错误信息\n            if ((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : (_error_response_data = _error_response1.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) {\n                alert(error.response.data.message);\n            } else if (error.message) {\n                alert(error.message);\n            } else {\n                alert(\"发布课程失败，请重试\");\n            }\n        } finally{\n            setIsPublishing(false);\n        }\n    };\n    // 保存课程列表\n    const handleSave = async ()=>{\n        try {\n            setIsCreating(true);\n            // 检查是否有文件正在上传\n            if (uploadingFiles.size > 0) {\n                alert(\"有文件正在上传中，请等待上传完成后再保存\");\n                setIsCreating(false);\n                return;\n            }\n            console.log(\"\\uD83D\\uDCBE 开始保存课程列表\");\n            // 如果有选中的课程且在编辑状态，先保存当前课程\n            if (selectedCourseId && rightPanelType === \"course\") {\n                const selectedCourse = courseList.find((c)=>c.id === selectedCourseId);\n                if (selectedCourse) {\n                    var _courseDetail_teachingMaterials;\n                    // 更新课程数据\n                    const updatedCourse = {\n                        ...selectedCourse,\n                        title: courseDetail.title,\n                        description: courseDetail.description,\n                        coverImage: courseDetail.coverImage,\n                        hasVideo: courseDetail.isVideoEnabled ? 1 : 0,\n                        hasDocument: courseDetail.isAttachmentEnabled ? 1 : 0,\n                        contentConfig: courseDetail.contentConfig,\n                        teachingInfo: courseDetail.teachingInfo,\n                        additionalResources: ((_courseDetail_teachingMaterials = courseDetail.teachingMaterials) === null || _courseDetail_teachingMaterials === void 0 ? void 0 : _courseDetail_teachingMaterials.map((material)=>({\n                                title: material.name,\n                                url: material.url,\n                                description: material.name\n                            }))) || []\n                    };\n                    // 先更新课程列表中的数据\n                    setCourseList((prev)=>prev.map((course)=>course.id === selectedCourseId ? updatedCourse : course));\n                    // 然后保存到后端\n                    await saveCourse(updatedCourse);\n                }\n            }\n            // 保存系列课程信息\n            const data = {\n                title: editingTitle,\n                courseGoals,\n                courseObjectives,\n                courseList\n            };\n            onSave(data);\n            onClose();\n            console.log(\"✅ 课程列表保存完成\");\n        } catch (error) {\n            console.error(\"❌ 保存失败:\", error);\n            alert(\"保存失败: \".concat(error.message || \"请重试\"));\n        } finally{\n            setIsCreating(false);\n        }\n    };\n    // 发布系列课程\n    const handlePublish = async ()=>{\n        // 如果系列已发布，不执行任何操作\n        if (seriesStatus === 1) {\n            return;\n        }\n        try {\n            setIsPublishingSeries(true);\n            // 检查是否有课程\n            if (courseList.length === 0) {\n                alert(\"发布失败：课程系列中至少需要包含一个课程\");\n                return;\n            }\n            console.log(\"\\uD83D\\uDCE4 开始发布系列课程，系列ID:\", seriesId);\n            const { data: response } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.publishCourseSeries(seriesId);\n            if (response.code === 200) {\n                console.log(\"✅ 系列课程发布成功:\", response.data);\n                // 构建成功消息\n                const publishData = response.data;\n                let successMessage = '系列课程\"'.concat(publishData.title, '\"发布成功！');\n                // 如果有发布统计信息，添加到消息中\n                if (publishData.publishStats) {\n                    const stats = publishData.publishStats;\n                    successMessage += \"\\n\\n发布统计：\\n• 总课程数：\".concat(publishData.totalCourses, \"\\n• 已发布课程：\").concat(publishData.publishedCourses, \"\\n• 视频课程：\").concat(stats.videoCourseCount, \"个\\n• 文档课程：\").concat(stats.documentCourseCount, \"个\\n• 总资源数：\").concat(stats.totalResourcesCount, \"个\");\n                    if (stats.totalVideoDuration > 0) {\n                        const durationMinutes = Math.round(stats.totalVideoDuration / 60);\n                        successMessage += \"\\n• 视频总时长：\".concat(durationMinutes, \"分钟\");\n                    }\n                }\n                alert(successMessage);\n                // 更新系列状态为已发布\n                setSeriesStatus(1);\n                // 刷新课程列表以更新状态\n                await loadCourseList();\n                // 通知父组件刷新数据\n                onSave({\n                    type: \"publish_series\",\n                    seriesId: seriesId,\n                    message: \"系列课程发布成功\"\n                });\n            } else {\n                console.error(\"❌ 发布系列课程失败:\", response.message);\n                alert(response.message || \"发布系列课程失败\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"❌ 发布系列课程出错:\", error);\n            // 处理具体的错误信息\n            let errorMessage = \"发布系列课程失败\";\n            if ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) {\n                errorMessage = error.response.data.message;\n            } else if (error.message) {\n                errorMessage = error.message;\n            }\n            alert(errorMessage);\n        } finally{\n            setIsPublishingSeries(false);\n        }\n    };\n    // 退出编辑模式 - 保存数据并关闭\n    const handleExitEdit = ()=>{\n        handleSave();\n    };\n    // 显示设置面板\n    const showSettingsPanel = ()=>{\n        setRightPanelType(\"settings\");\n        setSelectedCourseId(null);\n    };\n    // 处理模板选择\n    const handleTemplateSelect = (template)=>{\n        setCourseDetail((prev)=>({\n                ...prev,\n                selectedTemplate: template.templateName\n            }));\n        setIsTemplatePickerOpen(false);\n    };\n    // 打开模板选择弹窗\n    const handleOpenTemplatePicker = ()=>{\n        setIsTemplatePickerOpen(true);\n    };\n    // 显示课程编辑面板\n    const showCoursePanel = async (courseId)=>{\n        setRightPanelType(\"course\");\n        setSelectedCourseId(courseId);\n        // 如果作品数据还没有加载，重新加载\n        if (works.length === 0 && !loadingWorks) {\n            loadUserWorks();\n        }\n        // 获取选中的课程并更新courseDetail状态\n        const selectedCourse = courseList.find((course)=>course.id === courseId);\n        console.log(\"\\uD83C\\uDFAF 找到的课程:\", selectedCourse);\n        if (selectedCourse) {\n            try {\n                console.log(\"\\uD83D\\uDD04 获取课程详情，seriesId:\", seriesId, \"courseId:\", courseId);\n                // 获取真实的课程详情数据\n                const { data: res } = await fetchCourseDetail(seriesId, courseId);\n                if (res.code === 200 && res.data) {\n                    var _courseDetailData_additionalResources, _courseDetailData_teachingInfo;\n                    const courseDetailData = res.data;\n                    console.log(\"✅ 获取到课程详情:\", courseDetailData);\n                    // 将真实的 additionalResources 映射到 teachingMaterials\n                    const teachingMaterials = ((_courseDetailData_additionalResources = courseDetailData.additionalResources) === null || _courseDetailData_additionalResources === void 0 ? void 0 : _courseDetailData_additionalResources.map((resource)=>({\n                            type: \"application/octet-stream\",\n                            name: resource.title || resource.name || \"附件\",\n                            url: resource.url\n                        }))) || [];\n                    // 将API返回的teachingInfo数组格式转换为新的格式\n                    const mappedTeachingInfo = ((_courseDetailData_teachingInfo = courseDetailData.teachingInfo) === null || _courseDetailData_teachingInfo === void 0 ? void 0 : _courseDetailData_teachingInfo.map((info)=>({\n                            title: info.title || \"\",\n                            content: Array.isArray(info.content) ? info.content.join(\"\\n\") : info.content || \"\"\n                        }))) || [];\n                    console.log(\"\\uD83D\\uDCCE 映射的教学附件:\", teachingMaterials);\n                    console.log(\"\\uD83D\\uDCDA 映射的教学信息:\", mappedTeachingInfo);\n                    setCourseDetail((prev)=>{\n                        var _courseDetailData_contentConfig_video, _courseDetailData_contentConfig, _courseDetailData_contentConfig_video1, _courseDetailData_contentConfig1, _courseDetailData_contentConfig_document, _courseDetailData_contentConfig2, _courseDetailData_contentConfig_document1, _courseDetailData_contentConfig3;\n                        return {\n                            ...prev,\n                            title: courseDetailData.title,\n                            description: courseDetailData.description,\n                            coverImage: courseDetailData.coverImage || \"\",\n                            isVideoEnabled: courseDetailData.hasVideo === 1,\n                            isAttachmentEnabled: courseDetailData.hasDocument === 1,\n                            contentConfig: courseDetailData.contentConfig || {},\n                            teachingInfo: mappedTeachingInfo,\n                            teachingMaterials: teachingMaterials,\n                            videoUrl: ((_courseDetailData_contentConfig = courseDetailData.contentConfig) === null || _courseDetailData_contentConfig === void 0 ? void 0 : (_courseDetailData_contentConfig_video = _courseDetailData_contentConfig.video) === null || _courseDetailData_contentConfig_video === void 0 ? void 0 : _courseDetailData_contentConfig_video.url) || \"\",\n                            videoName: ((_courseDetailData_contentConfig1 = courseDetailData.contentConfig) === null || _courseDetailData_contentConfig1 === void 0 ? void 0 : (_courseDetailData_contentConfig_video1 = _courseDetailData_contentConfig1.video) === null || _courseDetailData_contentConfig_video1 === void 0 ? void 0 : _courseDetailData_contentConfig_video1.name) || \"\",\n                            attachmentUrl: ((_courseDetailData_contentConfig2 = courseDetailData.contentConfig) === null || _courseDetailData_contentConfig2 === void 0 ? void 0 : (_courseDetailData_contentConfig_document = _courseDetailData_contentConfig2.document) === null || _courseDetailData_contentConfig_document === void 0 ? void 0 : _courseDetailData_contentConfig_document.url) || \"\",\n                            attachmentName: ((_courseDetailData_contentConfig3 = courseDetailData.contentConfig) === null || _courseDetailData_contentConfig3 === void 0 ? void 0 : (_courseDetailData_contentConfig_document1 = _courseDetailData_contentConfig3.document) === null || _courseDetailData_contentConfig_document1 === void 0 ? void 0 : _courseDetailData_contentConfig_document1.name) || \"\"\n                        };\n                    });\n                } else {\n                    console.error(\"❌ 获取课程详情失败:\", res.message);\n                    // 使用基础数据作为后备\n                    setCourseDetail((prev)=>({\n                            ...prev,\n                            title: selectedCourse.title,\n                            description: selectedCourse.description,\n                            coverImage: selectedCourse.coverImage || \"\",\n                            isVideoEnabled: selectedCourse.hasVideo === 1,\n                            isAttachmentEnabled: selectedCourse.hasDocument === 1,\n                            teachingMaterials: [] // 清空附件列表\n                        }));\n                }\n            } catch (error) {\n                console.error(\"❌ 获取课程详情异常:\", error);\n                // 使用基础数据作为后备\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        title: selectedCourse.title,\n                        description: selectedCourse.description,\n                        coverImage: selectedCourse.coverImage || \"\",\n                        isVideoEnabled: selectedCourse.hasVideo === 1,\n                        isAttachmentEnabled: selectedCourse.hasDocument === 1,\n                        teachingMaterials: [] // 清空附件列表\n                    }));\n            }\n        }\n    };\n    // 获取选中的课程\n    const getSelectedCourse = ()=>{\n        return courseList.find((course)=>course.id === selectedCourseId);\n    };\n    if (!isVisible) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"course-list-modal-overlay\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"course-list-modal\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"course-list-header\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"course-list-title-section\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"course-list-title\",\n                                        children: \"课程列表\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 1341,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"course-list-actions\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: showSettingsPanel,\n                                                className: \"course-list-settings-btn \".concat(rightPanelType === \"settings\" ? \"active\" : \"\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1347,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1343,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: addNewCourse,\n                                                className: \"course-list-add-btn\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1350,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1349,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 1342,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                lineNumber: 1340,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onClose,\n                                className: \"course-list-close-btn\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 1355,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                lineNumber: 1354,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                        lineNumber: 1339,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"course-list-content\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"course-list-sidebar\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"course-list-items\",\n                                    children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"course-list-loading\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"加载中...\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 1366,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 1365,\n                                        columnNumber: 17\n                                    }, undefined) : courseList.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"course-list-empty\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"course-list-empty-icon\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"w-12 h-12 text-gray-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1371,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1370,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"course-list-empty-title\",\n                                                children: \"暂无课时\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1373,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"course-list-empty-description\",\n                                                children: \"点击右上角的 + 按钮添加第一个课时\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1374,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: addNewCourse,\n                                                className: \"course-list-empty-btn\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1381,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    \"添加课时\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1377,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 1369,\n                                        columnNumber: 17\n                                    }, undefined) : courseList.map((course)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"course-list-item \".concat(selectedCourseId === course.id ? \"active\" : \"\"),\n                                            onClick: ()=>showCoursePanel(course.id),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-list-item-content\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"course-list-item-text\",\n                                                            children: course.title\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1393,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"course-status-badge \".concat(course.status === 1 ? \"published\" : \"draft\"),\n                                                            children: course.status === 1 ? \"已发布\" : \"未发布\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1394,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1392,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: (e)=>{\n                                                        e.stopPropagation();\n                                                        showDeleteConfirm(course.id);\n                                                    },\n                                                    className: \"course-list-item-delete\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"w-3 h-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1405,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1398,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, course.id, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 1387,\n                                            columnNumber: 19\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 1363,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                lineNumber: 1362,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"course-list-edit-area\",\n                                children: [\n                                    rightPanelType === \"none\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"course-edit-empty\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"course-edit-empty-icon\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"w-16 h-16 text-gray-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1418,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1417,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"course-edit-empty-title\",\n                                                children: \"无课程详情\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1420,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"course-edit-empty-description\",\n                                                children: \"点击左侧课程或设置按钮查看详情\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1421,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 1416,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    rightPanelType === \"settings\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"course-series-cover\",\n                                                children: seriesCoverImage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: seriesCoverImage,\n                                                    alt: \"系列课程封面\",\n                                                    className: \"course-series-cover-image\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1432,\n                                                    columnNumber: 21\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-series-cover-placeholder\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"系列课程封面\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1439,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1438,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1430,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"course-edit-form\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"course-edit-field\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"course-edit-label\",\n                                                                children: \"系列课程标题\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1448,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                value: editingTitle,\n                                                                onChange: (e)=>setEditingTitle(e.target.value),\n                                                                className: \"course-edit-input\",\n                                                                placeholder: \"请输入系列课程标题\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1449,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1447,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"course-edit-field\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"course-edit-label\",\n                                                                children: \"课程标签\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1460,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Select_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                mode: \"multiple\",\n                                                                style: {\n                                                                    width: \"100%\"\n                                                                },\n                                                                placeholder: \"请选择课程标签\",\n                                                                value: selectedTags,\n                                                                onChange: setSelectedTags,\n                                                                loading: tagsLoading,\n                                                                options: courseTags.map((tag)=>{\n                                                                    console.log(\"\\uD83C\\uDFF7️ 渲染标签选项:\", tag);\n                                                                    return {\n                                                                        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            style: {\n                                                                                color: tag.color\n                                                                            },\n                                                                            children: tag.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1472,\n                                                                            columnNumber: 29\n                                                                        }, void 0),\n                                                                        value: tag.id\n                                                                    };\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1461,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    fontSize: \"12px\",\n                                                                    color: \"#666\",\n                                                                    marginTop: \"4px\"\n                                                                },\n                                                                children: [\n                                                                    \"调试: 当前标签数量 \",\n                                                                    courseTags.length,\n                                                                    \", 加载状态: \",\n                                                                    tagsLoading ? \"是\" : \"否\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1481,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1459,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"course-edit-field\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"course-edit-label\",\n                                                                children: \"课程项目成员\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1488,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                value: projectMembers,\n                                                                onChange: (e)=>setProjectMembers(e.target.value),\n                                                                className: \"course-edit-input\",\n                                                                placeholder: \"请输入项目成员，如：张老师、李助教、王同学\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1489,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1487,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1445,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true),\n                                    rightPanelType === \"course\" && getSelectedCourse() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"course-detail-edit\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-detail-top\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"course-detail-cover\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"course-cover-upload-area\",\n                                                                    onClick: ()=>{\n                                                                        var _document_getElementById;\n                                                                        return (_document_getElementById = document.getElementById(\"cover-upload-input\")) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.click();\n                                                                    },\n                                                                    children: courseDetail.coverImage || ((_getSelectedCourse = getSelectedCourse()) === null || _getSelectedCourse === void 0 ? void 0 : _getSelectedCourse.coverImage) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                        src: courseDetail.coverImage || ((_getSelectedCourse1 = getSelectedCourse()) === null || _getSelectedCourse1 === void 0 ? void 0 : _getSelectedCourse1.coverImage),\n                                                                        alt: \"课程封面\",\n                                                                        className: \"course-cover-image\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1513,\n                                                                        columnNumber: 27\n                                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"course-cover-placeholder\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"点击上传课程封面\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1520,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1519,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1508,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    id: \"cover-upload-input\",\n                                                                    type: \"file\",\n                                                                    accept: \"image/jpeg,image/jpg,image/png,image/gif\",\n                                                                    onChange: handleCoverUpload,\n                                                                    style: {\n                                                                        display: \"none\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1524,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"one-click-class-btn\",\n                                                                    onClick: ()=>{\n                                                                        // TODO: 实现一键上课功能\n                                                                        console.log(\"一键上课按钮被点击\");\n                                                                        notification.info(\"一键上课功能开发中...\");\n                                                                    },\n                                                                    children: \"一键上课\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1532,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1507,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"course-detail-basic\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"course-detail-field\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            children: \"课程标题\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1545,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            value: courseDetail.title || ((_getSelectedCourse2 = getSelectedCourse()) === null || _getSelectedCourse2 === void 0 ? void 0 : _getSelectedCourse2.title) || \"\",\n                                                                            onChange: (e)=>{\n                                                                                setCourseDetail((prev)=>({\n                                                                                        ...prev,\n                                                                                        title: e.target.value\n                                                                                    }));\n                                                                                updateCourseTitle(selectedCourseId, e.target.value);\n                                                                            },\n                                                                            placeholder: \"请输入课程标题\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1546,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1544,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"course-detail-field\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            children: \"课程介绍\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1557,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                            value: courseDetail.description || ((_getSelectedCourse3 = getSelectedCourse()) === null || _getSelectedCourse3 === void 0 ? void 0 : _getSelectedCourse3.description) || \"\",\n                                                                            onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                        ...prev,\n                                                                                        description: e.target.value\n                                                                                    })),\n                                                                            placeholder: \"请输入课程介绍\",\n                                                                            rows: 3\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1558,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1556,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1543,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1506,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-detail-section\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            children: \"课程资源\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1570,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"course-resource-item\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"resource-header-right\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"课程视频\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1575,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"switch\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: \"checkbox\",\n                                                                                    checked: courseDetail.isVideoEnabled,\n                                                                                    onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                ...prev,\n                                                                                                isVideoEnabled: e.target.checked\n                                                                                            }))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1577,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"slider\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1582,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1576,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1574,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                courseDetail.isVideoEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"video-content-area\",\n                                                                    children: ((_courseDetail_contentConfig = courseDetail.contentConfig) === null || _courseDetail_contentConfig === void 0 ? void 0 : (_courseDetail_contentConfig_video = _courseDetail_contentConfig.video) === null || _courseDetail_contentConfig_video === void 0 ? void 0 : _courseDetail_contentConfig_video.url) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"video-info-section\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"video-preview\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                                                                    className: \"video-thumbnail\",\n                                                                                    controls: true,\n                                                                                    poster: courseDetail.coverImage,\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                                                                                            src: courseDetail.contentConfig.video.url,\n                                                                                            type: \"video/mp4\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1596,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined),\n                                                                                        \"您的浏览器不支持视频播放\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1591,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1590,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"video-name-centered\",\n                                                                                children: courseDetail.contentConfig.video.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1600,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"upload-btn-horizontal\",\n                                                                                onClick: triggerVideoUpload,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"重新上传\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1602,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1601,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1589,\n                                                                        columnNumber: 29\n                                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"video-upload-section\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"video-placeholder-centered\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"play-icon\",\n                                                                                    children: \"▶\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1608,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1607,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"upload-btn-horizontal\",\n                                                                                onClick: triggerVideoUpload,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"上传视频\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1611,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1610,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1606,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1586,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1573,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"course-resource-item\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"resource-header-right\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"课程附件\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1622,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"switch\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: \"checkbox\",\n                                                                                    checked: courseDetail.isAttachmentEnabled,\n                                                                                    onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                ...prev,\n                                                                                                isAttachmentEnabled: e.target.checked\n                                                                                            }))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1624,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"slider\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1629,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1623,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1621,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                courseDetail.isAttachmentEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"attachment-content-area\",\n                                                                    children: ((_courseDetail_contentConfig1 = courseDetail.contentConfig) === null || _courseDetail_contentConfig1 === void 0 ? void 0 : (_courseDetail_contentConfig_document = _courseDetail_contentConfig1.document) === null || _courseDetail_contentConfig_document === void 0 ? void 0 : _courseDetail_contentConfig_document.url) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"attachment-info-section\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"attachment-preview\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"document-icon\",\n                                                                                        children: \"\\uD83D\\uDCC4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1638,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"attachment-details\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"attachment-name\",\n                                                                                            children: courseDetail.contentConfig.document.name\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1640,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1639,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1637,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"upload-btn-horizontal\",\n                                                                                onClick: triggerAttachmentUpload,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"重新上传\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1644,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1643,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1636,\n                                                                        columnNumber: 29\n                                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"attachment-upload-section\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            className: \"upload-btn-horizontal\",\n                                                                            onClick: triggerAttachmentUpload,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"上传附件\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1650,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1649,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1648,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1633,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1620,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"course-resource-item\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"resource-header-simple\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"教学附件\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1661,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1660,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"teaching-materials\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            className: \"add-material-btn\",\n                                                                            onClick: triggerTeachingMaterialUpload,\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"+\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1665,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"上传\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1666,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1664,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        courseDetail.teachingMaterials && courseDetail.teachingMaterials.length > 0 ? courseDetail.teachingMaterials.map((material, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"material-item\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"material-name\",\n                                                                                        onClick: ()=>{\n                                                                                            if (material.url) {\n                                                                                                window.open(material.url, \"_blank\");\n                                                                                            }\n                                                                                        },\n                                                                                        style: {\n                                                                                            cursor: material.url ? \"pointer\" : \"default\",\n                                                                                            color: material.url ? \"#1890ff\" : \"inherit\",\n                                                                                            textDecoration: material.url ? \"underline\" : \"none\"\n                                                                                        },\n                                                                                        title: material.url ? \"点击下载附件\" : material.name,\n                                                                                        children: [\n                                                                                            \"\\uD83D\\uDCCE \",\n                                                                                            material.name\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1671,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        className: \"remove-material-btn\",\n                                                                                        onClick: ()=>removeTeachingMaterial(index),\n                                                                                        title: \"删除附件\",\n                                                                                        children: \"\\xd7\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1687,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, index, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1670,\n                                                                                columnNumber: 29\n                                                                            }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"empty-materials-hint\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                style: {\n                                                                                    color: \"#999\",\n                                                                                    fontSize: \"14px\"\n                                                                                },\n                                                                                children: \"暂无教学附件\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1698,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1697,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1663,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1659,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1569,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-detail-section\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"section-header\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    children: \"课程内容\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1708,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"add-content-section-btn\",\n                                                                    onClick: addTeachingInfoItem,\n                                                                    title: \"添加课程内容\",\n                                                                    children: \"+ 添加课程内容\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1709,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1707,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"course-content-area\",\n                                                            children: courseDetail.teachingInfo && courseDetail.teachingInfo.length > 0 ? courseDetail.teachingInfo.map((info, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"teaching-info-card\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"card-header\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"card-title\",\n                                                                                    children: [\n                                                                                        \"课程内容 \",\n                                                                                        index + 1\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1722,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    className: \"remove-card-btn\",\n                                                                                    onClick: ()=>removeTeachingInfoItem(index),\n                                                                                    title: \"删除此内容\",\n                                                                                    children: \"\\xd7\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1723,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1721,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"card-content\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"input-group\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                            children: \"标题\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1733,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                            type: \"text\",\n                                                                                            value: info.title,\n                                                                                            onChange: (e)=>updateTeachingInfoTitle(index, e.target.value),\n                                                                                            placeholder: \"请输入标题，如：教学目标、教学方法等\",\n                                                                                            className: \"title-input\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1734,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1732,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"input-group\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                            children: \"内容\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1743,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                                            value: info.content,\n                                                                                            onChange: (e)=>updateTeachingInfoContent(index, e.target.value),\n                                                                                            placeholder: \"请输入具体内容，多个内容项可用换行分隔\",\n                                                                                            className: \"content-textarea\",\n                                                                                            rows: 4\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1744,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1742,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1731,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, index, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1720,\n                                                                    columnNumber: 27\n                                                                }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"empty-content-hint\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: \"暂无课程内容，点击右上角按钮添加\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1757,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1756,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1717,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1706,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-detail-section\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"one-key-section\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"one-key-item\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"开始上课\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1767,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"switch\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"checkbox\",\n                                                                                checked: courseDetail.isOneKeyOpen,\n                                                                                onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                            ...prev,\n                                                                                            isOneKeyOpen: e.target.checked\n                                                                                        }))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1769,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"slider\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1774,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1768,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1766,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            courseDetail.isOneKeyOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"one-key-item\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"分配积木\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1781,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"switch\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                        type: \"checkbox\",\n                                                                                        checked: courseDetail.isDistributionEnabled,\n                                                                                        onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    isDistributionEnabled: e.target.checked\n                                                                                                }))\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1783,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"slider\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1788,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1782,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            courseDetail.isDistributionEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"block-template-section\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        className: \"select-template-btn\",\n                                                                                        onClick: handleOpenTemplatePicker,\n                                                                                        children: \"选择积木模板\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1792,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"selected-template-display\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            children: courseDetail.selectedTemplate || \"选中的模板名字\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1799,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1798,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1791,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1780,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"one-key-item\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"分配能量\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1806,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"switch\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                        type: \"checkbox\",\n                                                                                        checked: courseDetail.isDistributionWater,\n                                                                                        onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    isDistributionWater: e.target.checked\n                                                                                                }))\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1808,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"slider\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1813,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1807,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1805,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    courseDetail.isDistributionWater && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"energy-input-section\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"需要能量：\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1819,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"text\",\n                                                                                value: courseDetail.requiredEnergy || \"\",\n                                                                                onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                            ...prev,\n                                                                                            requiredEnergy: e.target.value\n                                                                                        })),\n                                                                                placeholder: \"请输入需要的能量值\",\n                                                                                className: \"energy-input\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1820,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1818,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"one-key-item\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"分配任务\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1831,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"switch\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                        type: \"checkbox\",\n                                                                                        checked: courseDetail.isDistributionLimit,\n                                                                                        onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    isDistributionLimit: e.target.checked\n                                                                                                }))\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1833,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"slider\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1838,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1832,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1830,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    courseDetail.isDistributionLimit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"task-config-form\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"task-config-row\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"task-config-field\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                                children: \"任务名称:\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                lineNumber: 1848,\n                                                                                                columnNumber: 35\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                type: \"text\",\n                                                                                                value: courseDetail.taskConfig.taskName,\n                                                                                                onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                            ...prev,\n                                                                                                            taskConfig: {\n                                                                                                                ...prev.taskConfig,\n                                                                                                                taskName: e.target.value\n                                                                                                            }\n                                                                                                        })),\n                                                                                                placeholder: \"请输入任务名称\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                lineNumber: 1849,\n                                                                                                columnNumber: 35\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1847,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"task-config-field\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                                children: \"任务持续天数:\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                lineNumber: 1860,\n                                                                                                columnNumber: 35\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                type: \"number\",\n                                                                                                value: courseDetail.taskConfig.taskDuration,\n                                                                                                onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                            ...prev,\n                                                                                                            taskConfig: {\n                                                                                                                ...prev.taskConfig,\n                                                                                                                taskDuration: e.target.value\n                                                                                                            }\n                                                                                                        })),\n                                                                                                placeholder: \"请输入天数\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                lineNumber: 1861,\n                                                                                                columnNumber: 35\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1859,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1846,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"task-config-field task-config-full\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        children: \"任务描述:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1875,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                                        value: courseDetail.taskConfig.taskDescription,\n                                                                                        onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    taskConfig: {\n                                                                                                        ...prev.taskConfig,\n                                                                                                        taskDescription: e.target.value\n                                                                                                    }\n                                                                                                })),\n                                                                                        placeholder: \"请输入任务描述\",\n                                                                                        rows: 4\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1876,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1874,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"task-config-field task-config-full\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        children: [\n                                                                                            \"任务自评项: \",\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"item-number\",\n                                                                                                children: courseDetail.taskConfig.selfAssessmentItems.length\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                lineNumber: 1889,\n                                                                                                columnNumber: 47\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1889,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    courseDetail.taskConfig.selfAssessmentItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"self-assessment-item\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                type: \"text\",\n                                                                                                value: item,\n                                                                                                onChange: (e)=>{\n                                                                                                    const newItems = [\n                                                                                                        ...courseDetail.taskConfig.selfAssessmentItems\n                                                                                                    ];\n                                                                                                    newItems[index] = e.target.value;\n                                                                                                    setCourseDetail((prev)=>({\n                                                                                                            ...prev,\n                                                                                                            taskConfig: {\n                                                                                                                ...prev.taskConfig,\n                                                                                                                selfAssessmentItems: newItems\n                                                                                                            }\n                                                                                                        }));\n                                                                                                },\n                                                                                                placeholder: \"请输入自评项内容\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                lineNumber: 1892,\n                                                                                                columnNumber: 37\n                                                                                            }, undefined)\n                                                                                        }, index, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1891,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        type: \"button\",\n                                                                                        className: \"add-assessment-btn\",\n                                                                                        onClick: ()=>setCourseDetail((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    taskConfig: {\n                                                                                                        ...prev.taskConfig,\n                                                                                                        selfAssessmentItems: [\n                                                                                                            ...prev.taskConfig.selfAssessmentItems,\n                                                                                                            \"\"\n                                                                                                        ]\n                                                                                                    }\n                                                                                                })),\n                                                                                        children: \"+\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1907,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1888,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"task-config-field task-config-full\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        children: \"任务参考作品:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1924,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"reference-works-section\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"works-section\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                    className: \"help-text\",\n                                                                                                    children: \"选择作品作为任务参考资料（可多选）\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                    lineNumber: 1927,\n                                                                                                    columnNumber: 37\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"relative works-scroll-wrapper\",\n                                                                                                    style: {\n                                                                                                        minHeight: \"200px\",\n                                                                                                        cursor: \"grab\",\n                                                                                                        userSelect: \"none\"\n                                                                                                    },\n                                                                                                    children: loadingWorks ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"loading-container\",\n                                                                                                        children: [\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                className: \"loading-spinner\"\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                lineNumber: 1938,\n                                                                                                                columnNumber: 43\n                                                                                                            }, undefined),\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                                children: \"加载中...\"\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                lineNumber: 1939,\n                                                                                                                columnNumber: 43\n                                                                                                            }, undefined)\n                                                                                                        ]\n                                                                                                    }, void 0, true, {\n                                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                        lineNumber: 1937,\n                                                                                                        columnNumber: 41\n                                                                                                    }, undefined) : works.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"works-horizontal-scroll\",\n                                                                                                        onWheel: handleWheel,\n                                                                                                        children: works.map((work)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                className: \"work-card \".concat(selectedWorkIds.includes(work.id) ? \"selected\" : \"\"),\n                                                                                                                onClick: ()=>handleWorkSelect(work.id),\n                                                                                                                children: [\n                                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                        className: \"work-image\",\n                                                                                                                        children: [\n                                                                                                                            work.coverImage || work.screenShotImage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                                                                src: work.coverImage || work.screenShotImage,\n                                                                                                                                alt: work.title,\n                                                                                                                                onError: (e)=>{\n                                                                                                                                    const target = e.currentTarget;\n                                                                                                                                    target.style.display = \"none\";\n                                                                                                                                    const nextElement = target.nextElementSibling;\n                                                                                                                                    if (nextElement) {\n                                                                                                                                        nextElement.style.display = \"flex\";\n                                                                                                                                    }\n                                                                                                                                }\n                                                                                                                            }, void 0, false, {\n                                                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                                lineNumber: 1954,\n                                                                                                                                columnNumber: 51\n                                                                                                                            }, undefined) : null,\n                                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                                className: \"work-placeholder\",\n                                                                                                                                style: {\n                                                                                                                                    display: work.coverImage || work.screenShotImage ? \"none\" : \"flex\"\n                                                                                                                                },\n                                                                                                                                children: \"作品\"\n                                                                                                                            }, void 0, false, {\n                                                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                                lineNumber: 1967,\n                                                                                                                                columnNumber: 49\n                                                                                                                            }, undefined)\n                                                                                                                        ]\n                                                                                                                    }, void 0, true, {\n                                                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                        lineNumber: 1952,\n                                                                                                                        columnNumber: 47\n                                                                                                                    }, undefined),\n                                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                        className: \"work-title\",\n                                                                                                                        children: work.title || work.name || work.workName || \"未命名作品\"\n                                                                                                                    }, void 0, false, {\n                                                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                        lineNumber: 1971,\n                                                                                                                        columnNumber: 47\n                                                                                                                    }, undefined),\n                                                                                                                    selectedWorkIds.includes(work.id) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                        className: \"selected-indicator\",\n                                                                                                                        children: \"✓\"\n                                                                                                                    }, void 0, false, {\n                                                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                        lineNumber: 1973,\n                                                                                                                        columnNumber: 49\n                                                                                                                    }, undefined)\n                                                                                                                ]\n                                                                                                            }, work.id, true, {\n                                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                lineNumber: 1947,\n                                                                                                                columnNumber: 45\n                                                                                                            }, undefined))\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                        lineNumber: 1942,\n                                                                                                        columnNumber: 41\n                                                                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"empty-placeholder\",\n                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                            className: \"empty-text\",\n                                                                                                            children: \"暂无作品\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                            lineNumber: 1980,\n                                                                                                            columnNumber: 43\n                                                                                                        }, undefined)\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                        lineNumber: 1979,\n                                                                                                        columnNumber: 41\n                                                                                                    }, undefined)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                    lineNumber: 1928,\n                                                                                                    columnNumber: 37\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1926,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1925,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1923,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"task-config-field task-config-full\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        children: \"任务参考资源:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1990,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"reference-resources-section\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"reference-resources-grid\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                    type: \"button\",\n                                                                                                    className: \"upload-resource-btn\",\n                                                                                                    onClick: ()=>{\n                                                                                                        // 触发文件上传\n                                                                                                        const input = document.createElement(\"input\");\n                                                                                                        input.type = \"file\";\n                                                                                                        input.accept = \".pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx,.txt,.jpg,.png,.gif\";\n                                                                                                        input.onchange = (e)=>{\n                                                                                                            var _e_target_files;\n                                                                                                            const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n                                                                                                            if (file) {\n                                                                                                                setCourseDetail((prev)=>({\n                                                                                                                        ...prev,\n                                                                                                                        taskConfig: {\n                                                                                                                            ...prev.taskConfig,\n                                                                                                                            referenceResources: [\n                                                                                                                                ...prev.taskConfig.referenceResources,\n                                                                                                                                {\n                                                                                                                                    type: \"file\",\n                                                                                                                                    name: file.name\n                                                                                                                                }\n                                                                                                                            ]\n                                                                                                                        }\n                                                                                                                    }));\n                                                                                                            }\n                                                                                                        };\n                                                                                                        input.click();\n                                                                                                    },\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                                            size: 24\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                            lineNumber: 2019,\n                                                                                                            columnNumber: 39\n                                                                                                        }, undefined),\n                                                                                                        \"上传\"\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                    lineNumber: 1993,\n                                                                                                    columnNumber: 37\n                                                                                                }, undefined),\n                                                                                                courseDetail.taskConfig.referenceResources.map((resource, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"reference-resource-item\",\n                                                                                                        children: [\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                                children: resource.name\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                lineNumber: 2024,\n                                                                                                                columnNumber: 41\n                                                                                                            }, undefined),\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                                type: \"button\",\n                                                                                                                className: \"remove-resource-btn\",\n                                                                                                                onClick: ()=>{\n                                                                                                                    const newResources = courseDetail.taskConfig.referenceResources.filter((_, i)=>i !== index);\n                                                                                                                    setCourseDetail((prev)=>({\n                                                                                                                            ...prev,\n                                                                                                                            taskConfig: {\n                                                                                                                                ...prev.taskConfig,\n                                                                                                                                referenceResources: newResources\n                                                                                                                            }\n                                                                                                                        }));\n                                                                                                                },\n                                                                                                                children: \"\\xd7\"\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                lineNumber: 2025,\n                                                                                                                columnNumber: 41\n                                                                                                            }, undefined)\n                                                                                                        ]\n                                                                                                    }, index, true, {\n                                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                        lineNumber: 2023,\n                                                                                                        columnNumber: 39\n                                                                                                    }, undefined))\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1992,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1991,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1989,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1844,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1765,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1764,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 1504,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                lineNumber: 1414,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                        lineNumber: 1360,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"course-list-footer\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"course-list-footer-left\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handlePublish,\n                                    className: \"course-list-btn course-list-btn-publish\",\n                                    disabled: courseList.length === 0 || isPublishingSeries || seriesStatus === 1,\n                                    title: seriesStatus === 1 ? \"系列课程已发布\" : courseList.length === 0 ? \"发布失败：课程系列中至少需要包含一个课程\" : isPublishingSeries ? \"正在发布系列课程...\" : \"发布系列课程\",\n                                    children: seriesStatus === 1 ? \"已发布\" : isPublishingSeries ? \"正在发布...\" : \"发布系列课程\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 2058,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                lineNumber: 2057,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"course-list-footer-right\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleExitEdit,\n                                        className: \"course-list-btn course-list-btn-exit\",\n                                        children: \"退出编辑模式\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 2081,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handlePublishCourse,\n                                        className: \"course-list-btn course-list-btn-publish-course\",\n                                        disabled: !selectedCourseId || ((_courseList_find = courseList.find((c)=>c.id === selectedCourseId)) === null || _courseList_find === void 0 ? void 0 : _courseList_find.status) === 1 || isPublishing,\n                                        title: !selectedCourseId ? \"请先选择要发布的课程\" : ((_courseList_find1 = courseList.find((c)=>c.id === selectedCourseId)) === null || _courseList_find1 === void 0 ? void 0 : _courseList_find1.status) === 1 ? \"该课程已发布\" : isPublishing ? \"正在发布课程...\" : \"发布选中的课程\",\n                                        children: isPublishing ? \"正在发布...\" : \"发布课程\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 2084,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleSave,\n                                        className: \"course-list-btn course-list-btn-save\",\n                                        disabled: uploadingFiles.size > 0 || isCreating || courseList.length === 0,\n                                        title: courseList.length === 0 ? \"请先添加课程内容\" : uploadingFiles.size > 0 ? \"有文件正在上传中，请等待上传完成\" : isCreating ? courseList.some((c)=>c.id > 1000000) ? \"正在创建课程...\" : \"正在保存课程...\" : courseList.some((c)=>c.id > 1000000) ? \"创建课程\" : \"保存课程\",\n                                        children: uploadingFiles.size > 0 ? \"上传中...\" : isCreating ? courseList.some((c)=>c.id > 1000000) ? \"正在创建...\" : \"正在保存...\" : courseList.some((c)=>c.id > 1000000) ? \"创建课程\" : \"保存课程\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 2100,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                lineNumber: 2080,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                        lineNumber: 2056,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                lineNumber: 1337,\n                columnNumber: 7\n            }, undefined),\n            deleteConfirmVisible && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"modal-overlay\",\n                onClick: cancelDelete,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"delete-confirm-modal\",\n                    onClick: (e)=>e.stopPropagation(),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"delete-confirm-header\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    children: \"确认删除\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 2130,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: cancelDelete,\n                                    className: \"close-btn\",\n                                    disabled: isDeleting,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 2136,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 2131,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                            lineNumber: 2129,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"delete-confirm-content\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: isDeleting ? \"正在删除课程，请稍候...\" : \"确定要删除这个课程吗？删除后无法恢复。\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 2140,\n                                    columnNumber: 15\n                                }, undefined),\n                                isDeleting && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"delete-loading\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"loading-spinner\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 2148,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 2147,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                            lineNumber: 2139,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"delete-confirm-footer\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: cancelDelete,\n                                    className: \"cancel-btn\",\n                                    disabled: isDeleting,\n                                    children: \"取消\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 2153,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: confirmDeleteCourse,\n                                    className: \"confirm-btn\",\n                                    disabled: isDeleting,\n                                    children: isDeleting ? \"正在删除...\" : \"确认删除\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 2160,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                            lineNumber: 2152,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                    lineNumber: 2128,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                lineNumber: 2127,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TemplatePickerModal__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                isOpen: isTemplatePickerOpen,\n                onClose: ()=>setIsTemplatePickerOpen(false),\n                onTemplateSelect: handleTemplateSelect\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                lineNumber: 2173,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n        lineNumber: 1336,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CourseListEditModal, \"K9y9nBivRyxVacvGCVrfZ92qkII=\");\n_c = CourseListEditModal;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CourseListEditModal);\nvar _c;\n$RefreshReg$(_c, \"CourseListEditModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC93b3JrYmVuY2gvY29tcG9uZW50cy9Db3Vyc2VMaXN0RWRpdE1vZGFsLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUVnRTtBQUNLO0FBQ0g7QUFDckI7QUFDQTtBQUNGO0FBQ2I7QUFDOEM7QUFDcEI7QUFDckI7QUFDQTtBQWlFbkMsV0FBVztBQUNYLE1BQU1nQixvQkFBb0IsT0FBT0M7SUFDL0JDLFFBQVFDLEdBQUcsQ0FBQyxtQ0FBeUJGO0lBQ3JDQyxRQUFRQyxHQUFHLENBQUM7SUFFWixNQUFNQyxXQUFXLE1BQU1WLHNEQUFTQSxDQUFDVyxlQUFlLENBQUNKO0lBQ2pEQyxRQUFRQyxHQUFHLENBQUMsMkJBQWlCQztJQUM3QixPQUFPQSxTQUFTRSxJQUFJO0FBQ3RCO0FBRUEsa0NBQWtDO0FBQ2xDLE1BQU1DLGtCQUFrQixPQUFPTjtJQUM3QkMsUUFBUUMsR0FBRyxDQUFDLG1DQUF5QkY7SUFDckNDLFFBQVFDLEdBQUcsQ0FBQztJQUVaLHFCQUFxQjtJQUNyQixNQUFNQyxXQUFXLE1BQU1WLHNEQUFTQSxDQUFDYywwQkFBMEIsQ0FBQ1AsVUFBVTtRQUNwRVEsTUFBTTtRQUNOQyxVQUFVO0lBRVo7SUFFQVIsUUFBUUMsR0FBRyxDQUFDLHVCQUFhQztJQUN6QixPQUFPQSxTQUFTRSxJQUFJO0FBQ3RCO0FBRUEsU0FBUztBQUNULE1BQU1LLG9CQUFvQixPQUFPVixVQUFrQlc7SUFDakQsT0FBTyxNQUFNbEIsc0RBQVNBLENBQUNtQiwwQkFBMEIsQ0FBQ1osVUFBVVc7QUFDOUQ7QUFFQSxTQUFTO0FBQ1QsTUFBTUUsa0JBQWtCO0lBQ3RCLElBQUk7UUFDRlosUUFBUUMsR0FBRyxDQUFDO1FBQ1osTUFBTVksU0FBUyxNQUFNckIsc0RBQVNBLENBQUNzQixhQUFhLENBQUM7WUFDM0NQLE1BQU07WUFDTkMsVUFBVTtZQUNWTyxRQUFRLEVBQUUsV0FBVztRQUN2QjtRQUNBZixRQUFRQyxHQUFHLENBQUMsOENBQW9DWTtRQUNoRCxPQUFPQTtJQUNULEVBQUUsT0FBT0csT0FBTztRQUNkaEIsUUFBUWdCLEtBQUssQ0FBQyw4Q0FBb0NBO1FBQ2xELE1BQU1BO0lBQ1I7QUFDRjtBQUVBLE1BQU1DLHNCQUEwRDtRQUFDLEVBQy9EQyxTQUFTLEVBQ1RDLE9BQU8sRUFDUEMsTUFBTSxFQUNOQyxXQUFXLEVBQ1hDLGdCQUFnQixFQUNoQnZCLFdBQVcsSUFBSSwyQkFBMkI7SUFBNUIsRUFDZjtRQW0yQ21Ed0Isb0JBRVFBLHFCQWtDTEEscUJBV01BLHFCQTZCbENDLG1DQUFBQSw2QkErQ0FBLHNDQUFBQSw4QkFvY2tCQyxrQkFJekJBOztJQXI2RGxCLE1BQU1DLGVBQWU5QiwwRkFBZUE7SUFFcEMsTUFBTSxDQUFDNkIsWUFBWUUsY0FBYyxHQUFHNUMsK0NBQVFBLENBQWUsRUFBRTtJQUM3RCxNQUFNLENBQUM2QyxTQUFTQyxXQUFXLEdBQUc5QywrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUMrQyxnQkFBZ0JDLGtCQUFrQixHQUFHaEQsK0NBQVFBLENBQWlDO0lBQ3JGLE1BQU0sQ0FBQ2lELGtCQUFrQkMsb0JBQW9CLEdBQUdsRCwrQ0FBUUEsQ0FBZ0I7SUFDeEUsTUFBTSxDQUFDbUQsZ0JBQWdCQyxrQkFBa0IsR0FBR3BELCtDQUFRQSxDQUFjLElBQUlxRDtJQUN0RSxNQUFNLENBQUNDLGNBQWNDLGdCQUFnQixHQUFHdkQsK0NBQVFBLENBQUM7SUFDakQsTUFBTSxDQUFDd0QsWUFBWUMsY0FBYyxHQUFHekQsK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDMEQsb0JBQW9CQyxzQkFBc0IsR0FBRzNELCtDQUFRQSxDQUFDO0lBQzdELE1BQU0sQ0FBQzRELGNBQWNDLGdCQUFnQixHQUFHN0QsK0NBQVFBLENBQVMsSUFBSSxtQkFBbUI7SUFFaEYsV0FBVztJQUNYLE1BQU0sQ0FBQzhELHNCQUFzQkMsd0JBQXdCLEdBQUcvRCwrQ0FBUUEsQ0FBQztJQUNqRSxNQUFNLENBQUNnRSxnQkFBZ0JDLGtCQUFrQixHQUFHakUsK0NBQVFBLENBQWdCO0lBQ3BFLE1BQU0sQ0FBQ2tFLFlBQVlDLGNBQWMsR0FBR25FLCtDQUFRQSxDQUFDO0lBRTdDLFdBQVc7SUFDWCxNQUFNLENBQUNvRSxzQkFBc0JDLHdCQUF3QixHQUFHckUsK0NBQVFBLENBQUM7SUFFakUsTUFBTSxDQUFDc0UsY0FBY0MsZ0JBQWdCLEdBQUd2RSwrQ0FBUUEsQ0FBQ3NDO0lBQ2pELE1BQU0sQ0FBQ2tDLGFBQWFDLGVBQWUsR0FBR3pFLCtDQUFRQSxDQUFDO0lBQy9DLE1BQU0sQ0FBQzBFLGtCQUFrQkMsb0JBQW9CLEdBQUczRSwrQ0FBUUEsQ0FBQztJQUN6RCxNQUFNLENBQUM0RSxnQkFBZ0JDLGtCQUFrQixHQUFHN0UsK0NBQVFBLENBQUM7SUFFckQsU0FBUztJQUNULE1BQU0sQ0FBQzhFLE9BQU9DLFNBQVMsR0FBRy9FLCtDQUFRQSxDQUFRLEVBQUU7SUFDNUMsTUFBTSxDQUFDZ0YsY0FBY0MsZ0JBQWdCLEdBQUdqRiwrQ0FBUUEsQ0FBQztJQUNqRCxNQUFNLENBQUNrRixpQkFBaUJDLG1CQUFtQixHQUFHbkYsK0NBQVFBLENBQVcsRUFBRTtJQUVuRSxXQUFXO0lBQ1gsTUFBTSxDQUFDb0YsWUFBWUMsY0FBYyxHQUFHckYsK0NBQVFBLENBQWMsRUFBRTtJQUM1RCxNQUFNLENBQUNzRixjQUFjQyxnQkFBZ0IsR0FBR3ZGLCtDQUFRQSxDQUFXLEVBQUU7SUFDN0QsTUFBTSxDQUFDd0YsYUFBYUMsZUFBZSxHQUFHekYsK0NBQVFBLENBQUM7SUFFL0MsV0FBVztJQUNYLE1BQU0sQ0FBQ3lDLGNBQWNpRCxnQkFBZ0IsR0FBRzFGLCtDQUFRQSxDQUFDO1FBQy9DMkYsT0FBTztRQUNQQyxhQUFhO1FBQ2JDLFlBQVk7UUFDWkMsVUFBVTtRQUNWQyxXQUFXO1FBQ1hDLGdCQUFnQjtRQUNoQkMsZUFBZTtRQUNmQyxnQkFBZ0I7UUFDaEJDLHFCQUFxQjtRQUNyQkMsbUJBQW1CLEVBQUU7UUFDckIsbUJBQW1CO1FBQ25CQyxjQUFjLEVBQUU7UUFDaEIsb0JBQW9CO1FBQ3BCQyxlQUFlO1lBQ2JDLFVBQVU7WUFDVkMsYUFBYTtZQUNiQyxVQUFVO1lBQ1ZDLE9BQU87Z0JBQUVDLEtBQUs7Z0JBQUlDLE1BQU07WUFBRztZQUMzQkMsVUFBVTtnQkFBRUYsS0FBSztnQkFBSUMsTUFBTTtZQUFHO1lBQzlCRSxPQUFPO2dCQUFFSCxLQUFLO2dCQUFJQyxNQUFNO1lBQUc7UUFDN0I7UUFDQUcsZUFBZTtZQUNiQyxPQUFPO1lBQ1BDLFNBQVM7UUFDWDtRQUNBQyxjQUFjO1FBQ2RDLHVCQUF1QjtRQUN2QkMsb0JBQW9CO1FBQ3BCQyxrQkFBa0I7UUFDbEJDLHFCQUFxQjtRQUNyQkMsZ0JBQWdCO1FBQ2hCQyxjQUFjO1FBQ2RDLHFCQUFxQjtRQUNyQkMsd0JBQXdCO1lBQ3RCQyxhQUFhO1lBQ2JDLFdBQVc7WUFDWEMsWUFBWTtRQUNkO1FBQ0FDLG9CQUFvQjtRQUNwQkMsNEJBQTRCO1lBQzFCQyxXQUFXO1lBQ1hDLFNBQVM7UUFDWDtRQUNBQyx1QkFBdUIsRUFBRTtRQUN6QixXQUFXO1FBQ1hDLFlBQVk7WUFDVkMsVUFBVTtZQUNWQyxjQUFjO1lBQ2RDLGlCQUFpQjtZQUNqQkMscUJBQXFCO2dCQUFDO2FBQUc7WUFDekJDLGdCQUFnQixFQUFFO1lBQ2xCQyxvQkFBb0IsRUFBRTtRQUN4QjtJQUNGO0lBRUEsV0FBVztJQUNYeEksZ0RBQVNBLENBQUM7UUFDUixJQUFJa0MsYUFBYW5CLFVBQVU7WUFDekIsV0FBVztZQUNYLE1BQU0wSCxRQUFRQyxhQUFhQyxPQUFPLENBQUM7WUFDbkMzSCxRQUFRQyxHQUFHLENBQUMsZ0NBQXNCLENBQUMsQ0FBQ3dIO1lBQ3BDekgsUUFBUUMsR0FBRyxDQUFDLDBCQUFnQkY7WUFFNUIsSUFBSSxDQUFDMEgsT0FBTztnQkFDVnpILFFBQVFnQixLQUFLLENBQUM7Z0JBQ2QsY0FBYztnQkFDZFcsY0FBYyxFQUFFO2dCQUNoQkUsV0FBVztnQkFDWDtZQUNGO1lBRUErRjtZQUNBQztZQUNBQztZQUNBQztRQUNGO0lBQ0YsR0FBRztRQUFDN0c7UUFBV25CO0tBQVM7SUFFeEIsTUFBTTZILGlCQUFpQjtRQUNyQixJQUFJO1lBQ0YvRixXQUFXO1lBQ1g3QixRQUFRQyxHQUFHLENBQUMsbUNBQXlCRjtZQUVyQyxNQUFNRyxXQUFXLE1BQU1HLGdCQUFnQk47WUFDdkNDLFFBQVFDLEdBQUcsQ0FBQyx1QkFBYUM7WUFFekIsSUFBSUEsU0FBUzhILElBQUksS0FBSyxLQUFLO2dCQUN6QmhJLFFBQVFDLEdBQUcsQ0FBQyxhQUFhQyxTQUFTRSxJQUFJO2dCQUN0QyxNQUFNNkgsVUFBVS9ILFNBQVNFLElBQUksQ0FBQzhILElBQUksSUFBSSxFQUFFO2dCQUN4Q2xJLFFBQVFDLEdBQUcsQ0FBQyxjQUFjZ0k7Z0JBQzFCakksUUFBUUMsR0FBRyxDQUFDLHdCQUFjZ0ksUUFBUUUsR0FBRyxDQUFDLENBQUNDLElBQVk7d0JBQ2pEQyxJQUFJRCxFQUFFQyxFQUFFO3dCQUNSQyxNQUFNLE9BQU9GLEVBQUVDLEVBQUU7d0JBQ2pCM0QsT0FBTzBELEVBQUUxRCxLQUFLO3dCQUNkM0QsUUFBUXFILEVBQUVySCxNQUFNO29CQUNsQjtnQkFDQWYsUUFBUUMsR0FBRyxDQUFDLHdCQUFjO29CQUN4QnNJLE9BQU9OLFFBQVFPLE1BQU07b0JBQ3JCQyxPQUFPUixRQUFRUyxNQUFNLENBQUMsQ0FBQ04sSUFBV0EsRUFBRXJILE1BQU0sS0FBSyxHQUFHeUgsTUFBTTtvQkFDeERHLFdBQVdWLFFBQVFTLE1BQU0sQ0FBQyxDQUFDTixJQUFXQSxFQUFFckgsTUFBTSxLQUFLLEdBQUd5SCxNQUFNO2dCQUM5RDtnQkFDQTdHLGNBQWNzRztZQUNoQixPQUFPO2dCQUNMakksUUFBUWdCLEtBQUssQ0FBQyxjQUFjZDtnQkFDNUJ5QixjQUFjLEVBQUU7WUFDbEI7UUFDRixFQUFFLE9BQU9YLE9BQVk7Z0JBSWZBLGlCQUVPQSxrQkFFQUE7WUFQWGhCLFFBQVFnQixLQUFLLENBQUMsZUFBZUE7WUFFN0IsWUFBWTtZQUNaLElBQUlBLEVBQUFBLGtCQUFBQSxNQUFNZCxRQUFRLGNBQWRjLHNDQUFBQSxnQkFBZ0JELE1BQU0sTUFBSyxLQUFLO2dCQUNsQ2YsUUFBUWdCLEtBQUssQ0FBQztZQUNoQixPQUFPLElBQUlBLEVBQUFBLG1CQUFBQSxNQUFNZCxRQUFRLGNBQWRjLHVDQUFBQSxpQkFBZ0JELE1BQU0sTUFBSyxLQUFLO2dCQUN6Q2YsUUFBUWdCLEtBQUssQ0FBQztZQUNoQixPQUFPLElBQUlBLEVBQUFBLG1CQUFBQSxNQUFNZCxRQUFRLGNBQWRjLHVDQUFBQSxpQkFBZ0JELE1BQU0sTUFBSyxLQUFLO2dCQUN6Q2YsUUFBUWdCLEtBQUssQ0FBQyxrQ0FBd0JqQjtZQUN4QyxPQUFPO2dCQUNMQyxRQUFRZ0IsS0FBSyxDQUFDLHNCQUFZQSxNQUFNNEgsT0FBTztZQUN6QztZQUVBakgsY0FBYyxFQUFFO1FBQ2xCLFNBQVU7WUFDUkUsV0FBVztRQUNiO0lBQ0Y7SUFFQSxXQUFXO0lBQ1gsTUFBTWlHLG1CQUFtQjtRQUN2QixJQUFJO1lBQ0Y5SCxRQUFRQyxHQUFHLENBQUMscUNBQTJCRjtZQUV2QyxNQUFNRyxXQUFXLE1BQU1KLGtCQUFrQkM7WUFDekNDLFFBQVFDLEdBQUcsQ0FBQyx3QkFBY0M7WUFFMUIsSUFBSUEsU0FBUzhILElBQUksS0FBSyxPQUFPOUgsU0FBU0UsSUFBSSxFQUFFO2dCQUMxQyxNQUFNeUksYUFBYTNJLFNBQVNFLElBQUk7Z0JBQ2hDSixRQUFRQyxHQUFHLENBQUMsYUFBYTRJO2dCQUN6QmpHLGdCQUFnQmlHLFdBQVc5SCxNQUFNLElBQUk7Z0JBQ3JDZixRQUFRQyxHQUFHLENBQUMsd0JBQWM0SSxXQUFXOUgsTUFBTSxFQUFFO1lBQy9DLE9BQU87Z0JBQ0xmLFFBQVFnQixLQUFLLENBQUMsZUFBZWQsU0FBUzBJLE9BQU87WUFDL0M7UUFDRixFQUFFLE9BQU81SCxPQUFZO1lBQ25CaEIsUUFBUWdCLEtBQUssQ0FBQyxlQUFlQTtRQUMvQjtJQUNGO0lBRUEsV0FBVztJQUNYLE1BQU0rRyxnQkFBZ0I7UUFDcEIsSUFBSTtnQkFXRTdIO1lBVko4RCxnQkFBZ0I7WUFFaEIsc0JBQXNCO1lBQ3RCLE1BQU04RSxTQUFTcEIsYUFBYUMsT0FBTyxDQUFDLGFBQWEsUUFBUSxXQUFXO1lBRXBFLE1BQU16SCxXQUFXLE1BQU1SLG9EQUFRQSxDQUFDcUosZUFBZSxDQUFDQyxPQUFPRixTQUFTLEdBQUc7WUFFbkUsY0FBYztZQUNkLElBQUlHLFlBQVksRUFBRTtZQUVsQixJQUFJL0ksRUFBQUEsaUJBQUFBLFNBQVNFLElBQUksY0FBYkYscUNBQUFBLGVBQWU4SCxJQUFJLE1BQUssS0FBSztvQkFFM0I5SCxxQkFBQUEsaUJBSW1CQTtnQkFMdkIsK0JBQStCO2dCQUMvQixLQUFJQSxrQkFBQUEsU0FBU0UsSUFBSSxjQUFiRix1Q0FBQUEsc0JBQUFBLGdCQUFlRSxJQUFJLGNBQW5CRiwwQ0FBQUEsb0JBQXFCZ0ksSUFBSSxFQUFFO29CQUM3QmUsWUFBWS9JLFNBQVNFLElBQUksQ0FBQ0EsSUFBSSxDQUFDOEgsSUFBSTtnQkFDckMsT0FFSyxJQUFJZ0IsTUFBTUMsT0FBTyxFQUFDakosa0JBQUFBLFNBQVNFLElBQUksY0FBYkYsc0NBQUFBLGdCQUFlRSxJQUFJLEdBQUc7b0JBQzNDNkksWUFBWS9JLFNBQVNFLElBQUksQ0FBQ0EsSUFBSTtnQkFDaEMsT0FFSyxJQUFJOEksTUFBTUMsT0FBTyxDQUFDakosU0FBU0UsSUFBSSxHQUFHO29CQUNyQzZJLFlBQVkvSSxTQUFTRSxJQUFJO2dCQUMzQjtZQUNGLE9BRUssSUFBSThJLE1BQU1DLE9BQU8sQ0FBQ2pKLFdBQVc7Z0JBQ2hDK0ksWUFBWS9JO1lBQ2Q7WUFFQTRELFNBQVNtRjtRQUNYLEVBQUUsT0FBT2pJLE9BQU87WUFDZGhCLFFBQVFnQixLQUFLLENBQUMsYUFBYUE7WUFDM0I4QyxTQUFTLEVBQUU7UUFDYixTQUFVO1lBQ1JFLGdCQUFnQjtRQUNsQjtJQUNGO0lBRUEsU0FBUztJQUNULE1BQU1vRixtQkFBbUIsQ0FBQ0M7UUFDeEIsSUFBSXBGLGdCQUFnQnFGLFFBQVEsQ0FBQ0QsU0FBUztZQUNwQyxPQUFPO1lBQ1BuRixtQkFBbUJxRixDQUFBQSxPQUFRQSxLQUFLYixNQUFNLENBQUNMLENBQUFBLEtBQU1BLE9BQU9nQjtRQUN0RCxPQUFPO1lBQ0wsS0FBSztZQUNMbkYsbUJBQW1CcUYsQ0FBQUEsT0FBUTt1QkFBSUE7b0JBQU1GO2lCQUFPO1FBQzlDO0lBQ0Y7SUFFQSwwQkFBMEI7SUFDMUIsTUFBTUcsY0FBY3ZLLGtEQUFXQSxDQUFDLENBQUN3SztRQUMvQixNQUFNQyxZQUFZRCxFQUFFRSxhQUFhO1FBQ2pDLE1BQU0sRUFBRUMsV0FBVyxFQUFFQyxXQUFXLEVBQUUsR0FBR0g7UUFFckMsV0FBVztRQUNYLElBQUlFLGVBQWVDLGFBQWE7WUFDOUIsUUFBUSxjQUFjO1FBQ3hCO1FBRUEsY0FBYztRQUNkSixFQUFFSyxjQUFjO1FBRWhCLGVBQWU7UUFDZkosVUFBVUssVUFBVSxJQUFJTixFQUFFTyxNQUFNO0lBQ2xDLEdBQUcsRUFBRTtJQUVMLFNBQVM7SUFDVCxNQUFNbkMsaUJBQWlCO1FBQ3JCLElBQUk7WUFDRnJELGVBQWU7WUFDZnhFLFFBQVFDLEdBQUcsQ0FBQztZQUlaLE1BQU1DLFdBQVcsTUFBTVU7WUFDdkJaLFFBQVFDLEdBQUcsQ0FBQywyQkFBaUJDO1lBRTdCLFNBQVM7WUFDVCxJQUFJQSxZQUFZQSxTQUFTRSxJQUFJLEVBQUU7Z0JBQzdCSixRQUFRQyxHQUFHLENBQUMsc0JBQVlDLFNBQVNFLElBQUk7Z0JBRXJDLElBQUk2SixPQUFvQixFQUFFO2dCQUUxQixnREFBZ0Q7Z0JBQ2hELElBQUkvSixTQUFTRSxJQUFJLENBQUM4SCxJQUFJLElBQUlnQixNQUFNQyxPQUFPLENBQUNqSixTQUFTRSxJQUFJLENBQUM4SCxJQUFJLEdBQUc7b0JBQzNEK0IsT0FBTy9KLFNBQVNFLElBQUksQ0FBQzhILElBQUk7b0JBQ3pCbEksUUFBUUMsR0FBRyxDQUFDLHdCQUF3QmdLLEtBQUt6QixNQUFNLEVBQUU7Z0JBQ25ELE9BRUssSUFBSVUsTUFBTUMsT0FBTyxDQUFDakosU0FBU0UsSUFBSSxHQUFHO29CQUNyQzZKLE9BQU8vSixTQUFTRSxJQUFJO29CQUNwQkosUUFBUUMsR0FBRyxDQUFDLHFCQUFxQmdLLEtBQUt6QixNQUFNLEVBQUU7Z0JBQ2hELE9BRUssSUFBSXRJLFNBQVNFLElBQUksQ0FBQ0EsSUFBSSxJQUFJRixTQUFTRSxJQUFJLENBQUNBLElBQUksQ0FBQzhILElBQUksSUFBSWdCLE1BQU1DLE9BQU8sQ0FBQ2pKLFNBQVNFLElBQUksQ0FBQ0EsSUFBSSxDQUFDOEgsSUFBSSxHQUFHO29CQUNoRytCLE9BQU8vSixTQUFTRSxJQUFJLENBQUNBLElBQUksQ0FBQzhILElBQUk7b0JBQzlCbEksUUFBUUMsR0FBRyxDQUFDLDZCQUE2QmdLLEtBQUt6QixNQUFNLEVBQUU7Z0JBQ3hEO2dCQUVBLFdBQVc7Z0JBQ1h4SSxRQUFRQyxHQUFHLENBQUMsd0JBQWNnSztnQkFDMUJqSyxRQUFRQyxHQUFHLENBQUM7Z0JBQ1pnSyxLQUFLQyxPQUFPLENBQUMsQ0FBQ0MsS0FBS0M7d0JBT0REO29CQU5oQm5LLFFBQVFDLEdBQUcsQ0FBQyxLQUFXLE9BQU5tSyxPQUFNLE1BQUk7d0JBQ3pCRDt3QkFDQUUsUUFBUSxDQUFDLENBQUNGO3dCQUNWRyxRQUFRLFFBQU9ILGdCQUFBQSwwQkFBQUEsSUFBSzlCLEVBQUU7d0JBQ3RCa0MsVUFBVSxRQUFPSixnQkFBQUEsMEJBQUFBLElBQUt4RSxJQUFJO3dCQUMxQjZFLFNBQVMsRUFBRUwsZ0JBQUFBLDBCQUFBQSxJQUFLeEUsSUFBSTt3QkFDcEI4RSxjQUFjTixDQUFBQSxnQkFBQUEsMkJBQUFBLFlBQUFBLElBQUt4RSxJQUFJLGNBQVR3RSxnQ0FBQUEsVUFBV08sSUFBSSxRQUFPO29CQUN0QztnQkFDRjtnQkFFQSxNQUFNQyxZQUFZVixLQUFLdkIsTUFBTSxDQUFDeUIsQ0FBQUE7b0JBQzVCLE1BQU1TLFVBQVVULE9BQ2QsT0FBT0EsSUFBSTlCLEVBQUUsS0FBSyxZQUNsQixPQUFPOEIsSUFBSXhFLElBQUksS0FBSyxZQUNwQndFLElBQUl4RSxJQUFJLENBQUMrRSxJQUFJLE9BQU87b0JBRXRCLElBQUksQ0FBQ0UsU0FBUzt3QkFDWjVLLFFBQVFDLEdBQUcsQ0FBQyxXQUFXa0ssS0FBSzs0QkFDMUJFLFFBQVEsQ0FBQyxDQUFDRjs0QkFDVkcsUUFBUSxRQUFPSCxnQkFBQUEsMEJBQUFBLElBQUs5QixFQUFFOzRCQUN0QmtDLFVBQVUsUUFBT0osZ0JBQUFBLDBCQUFBQSxJQUFLeEUsSUFBSTs0QkFDMUI2RSxTQUFTLEVBQUVMLGdCQUFBQSwwQkFBQUEsSUFBS3hFLElBQUk7d0JBQ3RCO29CQUNGO29CQUVBLE9BQU9pRjtnQkFDVDtnQkFFQTVLLFFBQVFDLEdBQUcsQ0FBQyxhQUFhMEssVUFBVW5DLE1BQU07Z0JBQ3pDeEksUUFBUUMsR0FBRyxDQUFDLGFBQWEwSztnQkFFekIsSUFBSUEsVUFBVW5DLE1BQU0sR0FBRyxHQUFHO29CQUN4QnBFLGNBQWN1RztvQkFDZDNLLFFBQVFDLEdBQUcsQ0FBQztvQkFDWjtnQkFDRixPQUFPO29CQUNMRCxRQUFRNkssSUFBSSxDQUFDO2dCQUNmO1lBQ0YsT0FBTztnQkFDTDdLLFFBQVE2SyxJQUFJLENBQUMsa0JBQWtCM0s7WUFDakM7WUFFQSxpQkFBaUI7WUFDakJGLFFBQVFDLEdBQUcsQ0FBQztZQUNabUUsY0FBYyxFQUFFO1FBRWxCLEVBQUUsT0FBT3BELE9BQVk7Z0JBSVRBLGlCQUNJQSxrQkFDTkE7WUFMUmhCLFFBQVFnQixLQUFLLENBQUMsZUFBZUE7WUFDN0JoQixRQUFRZ0IsS0FBSyxDQUFDLFdBQVc7Z0JBQ3ZCNEgsU0FBUzVILE1BQU00SCxPQUFPO2dCQUN0QjdILE1BQU0sR0FBRUMsa0JBQUFBLE1BQU1kLFFBQVEsY0FBZGMsc0NBQUFBLGdCQUFnQkQsTUFBTTtnQkFDOUIrSixVQUFVLEdBQUU5SixtQkFBQUEsTUFBTWQsUUFBUSxjQUFkYyx1Q0FBQUEsaUJBQWdCOEosVUFBVTtnQkFDdEMxSyxJQUFJLEdBQUVZLG1CQUFBQSxNQUFNZCxRQUFRLGNBQWRjLHVDQUFBQSxpQkFBZ0JaLElBQUk7WUFDNUI7WUFFQSxhQUFhO1lBQ2JnRSxjQUFjLEVBQUU7UUFDbEIsU0FBVTtZQUNSSSxlQUFlO1FBQ2pCO0lBQ0Y7SUFFQSxRQUFRO0lBQ1IsTUFBTXVHLGVBQWU7UUFDbkIsTUFBTUMsWUFBd0I7WUFDNUIzQyxJQUFJNEMsS0FBS0MsR0FBRztZQUNabkwsVUFBVUE7WUFDVjJFLE9BQU8sSUFBMEIsT0FBdEJqRCxXQUFXK0csTUFBTSxHQUFHLEdBQUU7WUFDakM3RCxhQUFhO1lBQ2JDLFlBQVk7WUFDWnVHLFlBQVkxSixXQUFXK0csTUFBTSxHQUFHO1lBQ2hDekgsUUFBUTtZQUNScUssYUFBYTtZQUNiOUYsVUFBVTtZQUNWQyxhQUFhO1lBQ2JDLFVBQVU7WUFDVjZGLGVBQWU7WUFDZkMsb0JBQW9CO1lBQ3BCeEcsV0FBVztZQUNYeUcsb0JBQW9CO1lBQ3BCQyxnQkFBZ0I7WUFDaEJDLFdBQVcsSUFBSVIsT0FBT1MsV0FBVztZQUNqQ0MsV0FBVyxJQUFJVixPQUFPUyxXQUFXO1FBQ25DO1FBQ0EvSixjQUFjO2VBQUlGO1lBQVl1SjtTQUFVO1FBQ3hDLGFBQWE7UUFDYlksZ0JBQWdCWixVQUFVM0MsRUFBRTtJQUM5QjtJQUVBLFdBQVc7SUFDWCxNQUFNd0Qsb0JBQW9CLENBQUN4RDtRQUN6QnJGLGtCQUFrQnFGO1FBQ2xCdkYsd0JBQXdCO0lBQzFCO0lBRUEsU0FBUztJQUNULE1BQU1nSixzQkFBc0I7UUFDMUIsSUFBSSxDQUFDL0ksZ0JBQWdCO1FBRXJCLElBQUk7WUFDRkcsY0FBYztZQUVkLFVBQVU7WUFDVixNQUFNM0QsMkVBQW1CQSxDQUFDd00sWUFBWSxDQUFDaEo7WUFFdkMsV0FBVztZQUNYcEIsY0FBY0YsV0FBV2lILE1BQU0sQ0FBQ3NELENBQUFBLFNBQVVBLE9BQU8zRCxFQUFFLEtBQUt0RjtZQUV4RCx1QkFBdUI7WUFDdkIsSUFBSWYscUJBQXFCZSxnQkFBZ0I7Z0JBQ3ZDaEIsa0JBQWtCO2dCQUNsQkUsb0JBQW9CO1lBQ3RCO1lBRUEsU0FBUztZQUNUYSx3QkFBd0I7WUFDeEJFLGtCQUFrQjtZQUVsQixTQUFTO1lBQ1R0QixhQUFhdUssT0FBTyxDQUFDO1FBQ3ZCLEVBQUUsT0FBT2pMLE9BQU87WUFDZGhCLFFBQVFnQixLQUFLLENBQUMsV0FBV0E7WUFDekJVLGFBQWFWLEtBQUssQ0FBQztRQUNyQixTQUFVO1lBQ1JrQyxjQUFjO1FBQ2hCO0lBQ0Y7SUFFQSxPQUFPO0lBQ1AsTUFBTWdKLGVBQWU7UUFDbkIsSUFBSWpKLFlBQVksUUFBUSxhQUFhO1FBQ3JDSCx3QkFBd0I7UUFDeEJFLGtCQUFrQjtJQUNwQjtJQUVBLFNBQVM7SUFDVCxNQUFNbUosb0JBQW9CLENBQUM5RCxJQUFZK0Q7UUFDckN6SyxjQUFjRixXQUFXMEcsR0FBRyxDQUFDNkQsQ0FBQUEsU0FDM0JBLE9BQU8zRCxFQUFFLEtBQUtBLEtBQUs7Z0JBQUUsR0FBRzJELE1BQU07Z0JBQUV0SCxPQUFPMEg7WUFBUyxJQUFJSjtJQUV4RDtJQUVBLFdBQVc7SUFDWCxNQUFNSyxvQkFBb0IsT0FBT0M7WUFDbEJBO1FBQWIsTUFBTUMsUUFBT0Qsc0JBQUFBLE1BQU1FLE1BQU0sQ0FBQ0MsS0FBSyxjQUFsQkgsMENBQUFBLG1CQUFvQixDQUFDLEVBQUU7UUFDcEMsSUFBSUMsTUFBTTtZQUNSLFNBQVM7WUFDVCxNQUFNRyxlQUFlO2dCQUFDO2dCQUFjO2dCQUFhO2dCQUFhO2FBQVk7WUFDMUUsSUFBSSxDQUFDQSxhQUFhcEQsUUFBUSxDQUFDaUQsS0FBS2pFLElBQUksR0FBRztnQkFDckM1RyxhQUFhVixLQUFLLENBQUM7Z0JBQ25CO1lBQ0Y7WUFFQSxnQkFBZ0I7WUFDaEIsSUFBSXVMLEtBQUtJLElBQUksR0FBRyxLQUFLLE9BQU8sTUFBTTtnQkFDaENqTCxhQUFhVixLQUFLLENBQUM7Z0JBQ25CO1lBQ0Y7WUFFQSxJQUFJO2dCQUNGaEIsUUFBUUMsR0FBRyxDQUFDLDBCQUFnQnNNLEtBQUs1RyxJQUFJO2dCQUVyQyxjQUFjO2dCQUNkeEQsa0JBQWtCb0gsQ0FBQUEsT0FBUSxJQUFJbkgsSUFBSW1ILE1BQU1xRCxHQUFHLENBQUM7Z0JBRTVDLFVBQVU7Z0JBQ1YsTUFBTUMsYUFBYUMsSUFBSUMsZUFBZSxDQUFDUjtnQkFDdkM5SCxnQkFBZ0I4RSxDQUFBQSxPQUFTO3dCQUFFLEdBQUdBLElBQUk7d0JBQUUzRSxZQUFZaUk7b0JBQVc7Z0JBRTNELFNBQVM7Z0JBQ1QsTUFBTUcsV0FBVyxNQUFNdk4sc0RBQVNBLENBQUN3TixXQUFXLENBQUNWO2dCQUM3Q3ZNLFFBQVFDLEdBQUcsQ0FBQyxlQUFlK007Z0JBRTNCLG1CQUFtQjtnQkFDbkJ2SSxnQkFBZ0I4RSxDQUFBQSxPQUFTO3dCQUFFLEdBQUdBLElBQUk7d0JBQUUzRSxZQUFZb0k7b0JBQVM7Z0JBRXpELGVBQWU7Z0JBQ2YsSUFBSWhMLGtCQUFrQjtvQkFDcEJMLGNBQWM0SCxDQUFBQSxPQUFRQSxLQUFLcEIsR0FBRyxDQUFDNkQsQ0FBQUEsU0FDN0JBLE9BQU8zRCxFQUFFLEtBQUtyRyxtQkFBbUI7Z0NBQUUsR0FBR2dLLE1BQU07Z0NBQUVwSCxZQUFZb0k7NEJBQVMsSUFBSWhCO2dCQUUzRTtnQkFFQWtCLE1BQU07WUFDUixFQUFFLE9BQU9sTSxPQUFPO2dCQUNkaEIsUUFBUWdCLEtBQUssQ0FBQyxlQUFlQTtnQkFDN0JVLGFBQWFWLEtBQUssQ0FBQztnQkFFbkIsY0FBYztnQkFDZHlELGdCQUFnQjhFLENBQUFBLE9BQVM7d0JBQUUsR0FBR0EsSUFBSTt3QkFBRTNFLFlBQVk7b0JBQUc7WUFDckQsU0FBVTtnQkFDUixjQUFjO2dCQUNkekMsa0JBQWtCb0gsQ0FBQUE7b0JBQ2hCLE1BQU00RCxTQUFTLElBQUkvSyxJQUFJbUg7b0JBQ3ZCNEQsT0FBT0MsTUFBTSxDQUFDO29CQUNkLE9BQU9EO2dCQUNUO1lBQ0Y7UUFDRjtJQUNGO0lBRUEsU0FBUztJQUNULE1BQU1FLG9CQUFvQixPQUFPZjtZQUNsQkE7UUFBYixNQUFNQyxRQUFPRCxzQkFBQUEsTUFBTUUsTUFBTSxDQUFDQyxLQUFLLGNBQWxCSCwwQ0FBQUEsbUJBQW9CLENBQUMsRUFBRTtRQUNwQyxJQUFJQyxNQUFNO1lBQ1IsU0FBUztZQUNULE1BQU1HLGVBQWU7Z0JBQUM7Z0JBQWE7Z0JBQWE7Z0JBQWE7Z0JBQWE7YUFBWTtZQUN0RixJQUFJLENBQUNBLGFBQWFwRCxRQUFRLENBQUNpRCxLQUFLakUsSUFBSSxHQUFHO2dCQUNyQzRFLE1BQU07Z0JBQ047WUFDRjtZQUVBLGlCQUFpQjtZQUNqQixJQUFJWCxLQUFLSSxJQUFJLEdBQUcsTUFBTSxPQUFPLE1BQU07Z0JBQ2pDTyxNQUFNO2dCQUNOO1lBQ0Y7WUFFQSxJQUFJO2dCQUNGbE4sUUFBUUMsR0FBRyxDQUFDLDBCQUFnQnNNLEtBQUs1RyxJQUFJO2dCQUVyQyxjQUFjO2dCQUNkeEQsa0JBQWtCb0gsQ0FBQUEsT0FBUSxJQUFJbkgsSUFBSW1ILE1BQU1xRCxHQUFHLENBQUM7Z0JBRTVDLFVBQVU7Z0JBQ1YsTUFBTUMsYUFBYUMsSUFBSUMsZUFBZSxDQUFDUjtnQkFDdkM5SCxnQkFBZ0I4RSxDQUFBQSxPQUFTO3dCQUN2QixHQUFHQSxJQUFJO3dCQUNQbEUsZUFBZTs0QkFDYixHQUFHa0UsS0FBS2xFLGFBQWE7NEJBQ3JCSSxPQUFPO2dDQUNMQyxLQUFLbUg7Z0NBQ0xsSCxNQUFNNEcsS0FBSzVHLElBQUk7NEJBQ2pCO3dCQUNGO29CQUNGO2dCQUVBLFNBQVM7Z0JBQ1QsTUFBTWQsV0FBVyxNQUFNcEYsc0RBQVNBLENBQUN3TixXQUFXLENBQUNWO2dCQUM3Q3ZNLFFBQVFDLEdBQUcsQ0FBQyxlQUFlNEU7Z0JBRTNCLHFCQUFxQjtnQkFDckJKLGdCQUFnQjhFLENBQUFBLE9BQVM7d0JBQ3ZCLEdBQUdBLElBQUk7d0JBQ1BsRSxlQUFlOzRCQUNiLEdBQUdrRSxLQUFLbEUsYUFBYTs0QkFDckJJLE9BQU87Z0NBQ0xDLEtBQUtiO2dDQUNMYyxNQUFNNEcsS0FBSzVHLElBQUk7NEJBQ2pCO3dCQUNGO29CQUNGO2dCQUVBLGlCQUFpQjtnQkFDakIsSUFBSTNELGtCQUFrQjtvQkFDcEJMLGNBQWM0SCxDQUFBQSxPQUFRQSxLQUFLcEIsR0FBRyxDQUFDNkQsQ0FBQUEsU0FDN0JBLE9BQU8zRCxFQUFFLEtBQUtyRyxtQkFBbUI7Z0NBQy9CLEdBQUdnSyxNQUFNO2dDQUNUM0csZUFBZTtvQ0FDYixHQUFHMkcsT0FBTzNHLGFBQWE7b0NBQ3ZCSSxPQUFPO3dDQUNMQyxLQUFLYjt3Q0FDTGMsTUFBTTRHLEtBQUs1RyxJQUFJO29DQUNqQjtnQ0FDRjs0QkFDRixJQUFJcUc7Z0JBRVI7Z0JBRUFrQixNQUFNO1lBQ1IsRUFBRSxPQUFPbE0sT0FBTztnQkFDZGhCLFFBQVFnQixLQUFLLENBQUMsZUFBZUE7Z0JBQzdCa00sTUFBTTtnQkFFTixjQUFjO2dCQUNkekksZ0JBQWdCOEUsQ0FBQUEsT0FBUzt3QkFDdkIsR0FBR0EsSUFBSTt3QkFDUGxFLGVBQWU7NEJBQ2IsR0FBR2tFLEtBQUtsRSxhQUFhOzRCQUNyQkksT0FBTztnQ0FDTEMsS0FBSztnQ0FDTEMsTUFBTTs0QkFDUjt3QkFDRjtvQkFDRjtZQUNGLFNBQVU7Z0JBQ1IsY0FBYztnQkFDZHhELGtCQUFrQm9ILENBQUFBO29CQUNoQixNQUFNNEQsU0FBUyxJQUFJL0ssSUFBSW1IO29CQUN2QjRELE9BQU9DLE1BQU0sQ0FBQztvQkFDZCxPQUFPRDtnQkFDVDtZQUNGO1FBQ0Y7SUFDRjtJQUVBLFdBQVc7SUFDWCxNQUFNRyxxQkFBcUI7UUFDekIsTUFBTUMsUUFBUTNILFNBQVM0SCxhQUFhLENBQUM7UUFDckNELE1BQU1qRixJQUFJLEdBQUc7UUFDYmlGLE1BQU1FLE1BQU0sR0FBRztRQUNmRixNQUFNRyxRQUFRLEdBQUcsQ0FBQ2pFLElBQU00RCxrQkFBa0I1RDtRQUMxQzhELE1BQU1JLEtBQUs7SUFDYjtJQUVBLFNBQVM7SUFDVCxNQUFNQyx5QkFBeUIsT0FBT3RCO1lBQ3ZCQTtRQUFiLE1BQU1DLFFBQU9ELHNCQUFBQSxNQUFNRSxNQUFNLENBQUNDLEtBQUssY0FBbEJILDBDQUFBQSxtQkFBb0IsQ0FBQyxFQUFFO1FBQ3BDLElBQUlDLE1BQU07WUFDUixTQUFTO1lBQ1QsTUFBTUcsZUFBZTtnQkFDbkI7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7YUFDRDtZQUVELElBQUksQ0FBQ0EsYUFBYXBELFFBQVEsQ0FBQ2lELEtBQUtqRSxJQUFJLEdBQUc7Z0JBQ3JDNEUsTUFBTTtnQkFDTjtZQUNGO1lBRUEsZ0JBQWdCO1lBQ2hCLElBQUlYLEtBQUtJLElBQUksR0FBRyxLQUFLLE9BQU8sTUFBTTtnQkFDaENPLE1BQU07Z0JBQ047WUFDRjtZQUVBLElBQUk7Z0JBQ0ZsTixRQUFRQyxHQUFHLENBQUMsMEJBQWdCc00sS0FBSzVHLElBQUk7Z0JBRXJDLGNBQWM7Z0JBQ2R4RCxrQkFBa0JvSCxDQUFBQSxPQUFRLElBQUluSCxJQUFJbUgsTUFBTXFELEdBQUcsQ0FBQztnQkFFNUMsU0FBUztnQkFDVCxNQUFNaUIsY0FBYyxNQUFNcE8sc0RBQVNBLENBQUN3TixXQUFXLENBQUNWO2dCQUNoRHZNLFFBQVFDLEdBQUcsQ0FBQyxlQUFlNE47Z0JBRTNCLGVBQWU7Z0JBQ2ZwSixnQkFBZ0I4RSxDQUFBQSxPQUFTO3dCQUN2QixHQUFHQSxJQUFJO3dCQUNQbEUsZUFBZTs0QkFDYixHQUFHa0UsS0FBS2xFLGFBQWE7NEJBQ3JCRSxhQUFhOzRCQUNiSyxVQUFVO2dDQUNSRixLQUFLbUk7Z0NBQ0xsSSxNQUFNNEcsS0FBSzVHLElBQUk7NEJBQ2pCO3dCQUNGO29CQUNGO2dCQUVBdUgsTUFBTTtZQUNSLEVBQUUsT0FBT2xNLE9BQU87Z0JBQ2RoQixRQUFRZ0IsS0FBSyxDQUFDLGVBQWVBO2dCQUM3QmtNLE1BQU07WUFDUixTQUFVO2dCQUNSLGNBQWM7Z0JBQ2QvSyxrQkFBa0JvSCxDQUFBQTtvQkFDaEIsTUFBTTRELFNBQVMsSUFBSS9LLElBQUltSDtvQkFDdkI0RCxPQUFPQyxNQUFNLENBQUM7b0JBQ2QsT0FBT0Q7Z0JBQ1Q7WUFDRjtRQUNGO0lBQ0Y7SUFFQSxXQUFXO0lBQ1gsTUFBTVcsMEJBQTBCO1FBQzlCLE1BQU1QLFFBQVEzSCxTQUFTNEgsYUFBYSxDQUFDO1FBQ3JDRCxNQUFNakYsSUFBSSxHQUFHO1FBQ2JpRixNQUFNRSxNQUFNLEdBQUc7UUFDZkYsTUFBTUcsUUFBUSxHQUFHLENBQUNqRSxJQUFNbUUsdUJBQXVCbkU7UUFDL0M4RCxNQUFNSSxLQUFLO0lBQ2I7SUFFQSxXQUFXO0lBQ1gsTUFBTUksK0JBQStCLE9BQU96QjtZQUM3QkE7UUFBYixNQUFNQyxRQUFPRCxzQkFBQUEsTUFBTUUsTUFBTSxDQUFDQyxLQUFLLGNBQWxCSCwwQ0FBQUEsbUJBQW9CLENBQUMsRUFBRTtRQUNwQyxJQUFJQyxNQUFNO1lBQ1IsU0FBUztZQUNULE1BQU1HLGVBQWU7Z0JBQ25CO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2FBQ0Q7WUFFRCxJQUFJLENBQUNBLGFBQWFwRCxRQUFRLENBQUNpRCxLQUFLakUsSUFBSSxHQUFHO2dCQUNyQzRFLE1BQU07Z0JBQ047WUFDRjtZQUVBLGdCQUFnQjtZQUNoQixJQUFJWCxLQUFLSSxJQUFJLEdBQUcsS0FBSyxPQUFPLE1BQU07Z0JBQ2hDTyxNQUFNO2dCQUNOO1lBQ0Y7WUFFQSxJQUFJO2dCQUNGbE4sUUFBUUMsR0FBRyxDQUFDLDBCQUFnQnNNLEtBQUs1RyxJQUFJO2dCQUVyQyxjQUFjO2dCQUNkeEQsa0JBQWtCb0gsQ0FBQUEsT0FBUSxJQUFJbkgsSUFBSW1ILE1BQU1xRCxHQUFHLENBQUMsWUFBdUIsT0FBWDNCLEtBQUtDLEdBQUc7Z0JBRWhFLFNBQVM7Z0JBQ1QsTUFBTThDLGNBQWMsTUFBTXZPLHNEQUFTQSxDQUFDd04sV0FBVyxDQUFDVjtnQkFDaER2TSxRQUFRQyxHQUFHLENBQUMsZUFBZStOO2dCQUUzQixZQUFZO2dCQUNaLE1BQU1DLGNBQWM7b0JBQ2xCM0YsTUFBTWlFLEtBQUtqRSxJQUFJO29CQUNmM0MsTUFBTTRHLEtBQUs1RyxJQUFJO29CQUNmRCxLQUFLc0k7Z0JBQ1A7Z0JBRUF2SixnQkFBZ0I4RSxDQUFBQSxPQUFTO3dCQUN2QixHQUFHQSxJQUFJO3dCQUNQcEUsbUJBQW1COytCQUFJb0UsS0FBS3BFLGlCQUFpQjs0QkFBRThJO3lCQUFZO29CQUM3RDtnQkFFQWYsTUFBTTtZQUNSLEVBQUUsT0FBT2xNLE9BQU87Z0JBQ2RoQixRQUFRZ0IsS0FBSyxDQUFDLGVBQWVBO2dCQUM3QmtNLE1BQU07WUFDUixTQUFVO2dCQUNSLGNBQWM7Z0JBQ2QvSyxrQkFBa0JvSCxDQUFBQTtvQkFDaEIsTUFBTTRELFNBQVMsSUFBSS9LLElBQUltSDtvQkFDdkIscUJBQXFCO29CQUNyQkwsTUFBTWdGLElBQUksQ0FBQ2YsUUFBUWpELE9BQU8sQ0FBQ2lFLENBQUFBO3dCQUN6QixJQUFJQSxLQUFLQyxVQUFVLENBQUMsY0FBYzs0QkFDaENqQixPQUFPQyxNQUFNLENBQUNlO3dCQUNoQjtvQkFDRjtvQkFDQSxPQUFPaEI7Z0JBQ1Q7WUFDRjtRQUNGO0lBQ0Y7SUFFQSxhQUFhO0lBQ2IsTUFBTWtCLGdDQUFnQztRQUNwQyxNQUFNZCxRQUFRM0gsU0FBUzRILGFBQWEsQ0FBQztRQUNyQ0QsTUFBTWpGLElBQUksR0FBRztRQUNiaUYsTUFBTUUsTUFBTSxHQUFHO1FBQ2ZGLE1BQU1HLFFBQVEsR0FBRyxDQUFDakUsSUFBTXNFLDZCQUE2QnRFO1FBQ3JEOEQsTUFBTUksS0FBSztJQUNiO0lBRUEsU0FBUztJQUNULE1BQU1XLHlCQUF5QixDQUFDbEU7UUFDOUIzRixnQkFBZ0I4RSxDQUFBQSxPQUFTO2dCQUN2QixHQUFHQSxJQUFJO2dCQUNQcEUsbUJBQW1Cb0UsS0FBS3BFLGlCQUFpQixDQUFDdUQsTUFBTSxDQUFDLENBQUM2RixHQUFHQyxJQUFNQSxNQUFNcEU7WUFDbkU7SUFDRjtJQUVBLFdBQVc7SUFDWCxNQUFNcUUsc0JBQXNCO1FBQzFCaEssZ0JBQWdCOEUsQ0FBQUEsT0FBUztnQkFDdkIsR0FBR0EsSUFBSTtnQkFDUG5FLGNBQWM7dUJBQUltRSxLQUFLbkUsWUFBWTtvQkFBRTt3QkFBRVYsT0FBTzt3QkFBSXNCLFNBQVM7b0JBQUc7aUJBQUU7WUFDbEU7SUFDRjtJQUVBLE1BQU0wSSx5QkFBeUIsQ0FBQ3RFO1FBQzlCM0YsZ0JBQWdCOEUsQ0FBQUEsT0FBUztnQkFDdkIsR0FBR0EsSUFBSTtnQkFDUG5FLGNBQWNtRSxLQUFLbkUsWUFBWSxDQUFDc0QsTUFBTSxDQUFDLENBQUM2RixHQUFHQyxJQUFNQSxNQUFNcEU7WUFDekQ7SUFDRjtJQUVBLE1BQU11RSwwQkFBMEIsQ0FBQ3ZFLE9BQWUxRjtRQUM5Q0QsZ0JBQWdCOEUsQ0FBQUE7WUFDZCxNQUFNcUYsa0JBQWtCO21CQUFJckYsS0FBS25FLFlBQVk7YUFBQztZQUM5Q3dKLGVBQWUsQ0FBQ3hFLE1BQU0sR0FBRztnQkFBRSxHQUFHd0UsZUFBZSxDQUFDeEUsTUFBTTtnQkFBRTFGO1lBQU07WUFDNUQsT0FBTztnQkFBRSxHQUFHNkUsSUFBSTtnQkFBRW5FLGNBQWN3SjtZQUFnQjtRQUNsRDtJQUNGO0lBRUEsTUFBTUMsNEJBQTRCLENBQUN6RSxPQUFlcEU7UUFDaER2QixnQkFBZ0I4RSxDQUFBQTtZQUNkLE1BQU1xRixrQkFBa0I7bUJBQUlyRixLQUFLbkUsWUFBWTthQUFDO1lBQzlDd0osZUFBZSxDQUFDeEUsTUFBTSxHQUFHO2dCQUFFLEdBQUd3RSxlQUFlLENBQUN4RSxNQUFNO2dCQUFFcEU7WUFBUTtZQUM5RCxPQUFPO2dCQUFFLEdBQUd1RCxJQUFJO2dCQUFFbkUsY0FBY3dKO1lBQWdCO1FBQ2xEO0lBQ0Y7SUFFQSw2QkFBNkI7SUFDN0IsTUFBTUUsNEJBQTRCLENBQUMxSjtRQUNqQyxPQUFPQSxhQUFhK0MsR0FBRyxDQUFDNEcsQ0FBQUEsT0FBUztnQkFDL0JySyxPQUFPcUssS0FBS3JLLEtBQUs7Z0JBQ2pCc0IsU0FBUytJLEtBQUsvSSxPQUFPLENBQUNnSixLQUFLLENBQUMsTUFBTXRHLE1BQU0sQ0FBQ3VHLENBQUFBLE9BQVFBLEtBQUt2RSxJQUFJLElBQUksYUFBYTtZQUM3RTtJQUNGO0lBRUEsMEJBQTBCO0lBQzFCLE1BQU13RSxtQkFBbUIsQ0FBQ2xEO1lBU3BCQSw2QkFBQUEsdUJBS0FBLGdDQUFBQTtRQWJKLE1BQU1tRCxTQUFTLEVBQUU7UUFFakIsU0FBUztRQUNULElBQUluRCxPQUFPcEgsVUFBVSxJQUFJb0gsT0FBT3BILFVBQVUsQ0FBQ3dKLFVBQVUsQ0FBQyxVQUFVO1lBQzlEZSxPQUFPQyxJQUFJLENBQUM7UUFDZDtRQUVBLFNBQVM7UUFDVCxJQUFJcEQsRUFBQUEsd0JBQUFBLE9BQU8zRyxhQUFhLGNBQXBCMkcsNkNBQUFBLDhCQUFBQSxzQkFBc0J2RyxLQUFLLGNBQTNCdUcsa0RBQUFBLDRCQUE2QnRHLEdBQUcsS0FBSXNHLE9BQU8zRyxhQUFhLENBQUNJLEtBQUssQ0FBQ0MsR0FBRyxDQUFDMEksVUFBVSxDQUFDLFVBQVU7WUFDMUZlLE9BQU9DLElBQUksQ0FBQztRQUNkO1FBRUEsU0FBUztRQUNULElBQUlwRCxFQUFBQSx5QkFBQUEsT0FBTzNHLGFBQWEsY0FBcEIyRyw4Q0FBQUEsaUNBQUFBLHVCQUFzQnBHLFFBQVEsY0FBOUJvRyxxREFBQUEsK0JBQWdDdEcsR0FBRyxLQUFJc0csT0FBTzNHLGFBQWEsQ0FBQ08sUUFBUSxDQUFDRixHQUFHLENBQUMwSSxVQUFVLENBQUMsVUFBVTtZQUNoR2UsT0FBT0MsSUFBSSxDQUFDO1FBQ2Q7UUFFQSxTQUFTO1FBQ1QsSUFBSXBELE9BQU9xRCxtQkFBbUIsRUFBRTtZQUM5QnJELE9BQU9xRCxtQkFBbUIsQ0FBQ25GLE9BQU8sQ0FBQyxDQUFDb0YsVUFBVWxGO2dCQUM1QyxJQUFJa0YsU0FBUzVKLEdBQUcsSUFBSTRKLFNBQVM1SixHQUFHLENBQUMwSSxVQUFVLENBQUMsVUFBVTtvQkFDcERlLE9BQU9DLElBQUksQ0FBQyxPQUFpQixPQUFWaEYsUUFBUTtnQkFDN0I7WUFDRjtRQUNGO1FBRUEsT0FBTytFO0lBQ1Q7SUFFQSxZQUFZO0lBQ1osTUFBTUksYUFBYSxPQUFPdkQ7UUFDeEIsSUFBSTtnQkFrQlVBLDZCQUFBQSx1QkFDR0EsZ0NBQUFBO1lBbEJmLFVBQVU7WUFDVixJQUFJLENBQUNBLE9BQU90SCxLQUFLLElBQUlzSCxPQUFPdEgsS0FBSyxDQUFDZ0csSUFBSSxPQUFPLElBQUk7Z0JBQy9DLE1BQU0sSUFBSThFLE1BQU07WUFDbEI7WUFFQSxnQkFBZ0I7WUFDaEIsTUFBTUMsYUFBYVAsaUJBQWlCbEQ7WUFDcEMsSUFBSXlELFdBQVdqSCxNQUFNLEdBQUcsR0FBRztnQkFDekIsTUFBTSxJQUFJZ0gsTUFBTSwwQkFBK0MsT0FBckJDLFdBQVdDLElBQUksQ0FBQztZQUM1RDtZQUVBLFNBQVM7WUFDVCxNQUFNQyxhQUFhO2dCQUNqQjVQLFVBQVVBO2dCQUNWMkUsT0FBT3NILE9BQU90SCxLQUFLO2dCQUNuQkMsYUFBYXFILE9BQU9ySCxXQUFXLElBQUk7Z0JBQ25DQyxZQUFZb0gsT0FBT3BILFVBQVUsSUFBSTtnQkFDakNVLFVBQVUwRyxFQUFBQSx3QkFBQUEsT0FBTzNHLGFBQWEsY0FBcEIyRyw2Q0FBQUEsOEJBQUFBLHNCQUFzQnZHLEtBQUssY0FBM0J1RyxrREFBQUEsNEJBQTZCdEcsR0FBRyxJQUFHLElBQUk7Z0JBQ2pESCxhQUFheUcsRUFBQUEseUJBQUFBLE9BQU8zRyxhQUFhLGNBQXBCMkcsOENBQUFBLGlDQUFBQSx1QkFBc0JwRyxRQUFRLGNBQTlCb0cscURBQUFBLCtCQUFnQ3RHLEdBQUcsSUFBRyxJQUFJO2dCQUN2REYsVUFBVTtnQkFDVjZGLGVBQWU7Z0JBQ2ZoRyxlQUFlMkcsT0FBTzNHLGFBQWEsSUFBSSxDQUFDO2dCQUN4Q0QsY0FBYzBKLDBCQUEwQjlDLE9BQU81RyxZQUFZLElBQUksRUFBRTtnQkFDakVpSyxxQkFBcUJyRCxPQUFPcUQsbUJBQW1CLElBQUksRUFBRTtnQkFDckRsRSxZQUFZYSxPQUFPYixVQUFVLElBQUk7WUFDbkM7WUFFQW5MLFFBQVFDLEdBQUcsQ0FBQywwQkFBZ0IwUDtZQUU1QixJQUFJOU87WUFFSixlQUFlO1lBQ2YsSUFBSW1MLE9BQU8zRCxFQUFFLElBQUkyRCxPQUFPM0QsRUFBRSxHQUFHLFNBQVM7Z0JBQ3BDLGNBQWM7Z0JBQ2RySSxRQUFRQyxHQUFHLENBQUM7Z0JBQ1osTUFBTSxFQUFFRyxNQUFNRixRQUFRLEVBQUUsR0FBRyxNQUFNVixzREFBU0EsQ0FBQ29RLFlBQVksQ0FBQ0Q7Z0JBQ3hEOU8sU0FBU1g7Z0JBQ1RGLFFBQVFDLEdBQUcsQ0FBQztZQUNkLE9BQU87Z0JBQ0wsZUFBZTtnQkFDZkQsUUFBUUMsR0FBRyxDQUFDLHdCQUFjK0wsT0FBTzNELEVBQUU7Z0JBQ25DLE1BQU0sRUFBRWpJLE1BQU1GLFFBQVEsRUFBRSxHQUFHLE1BQU1WLHNEQUFTQSxDQUFDcVEsWUFBWSxDQUFDN0QsT0FBTzNELEVBQUUsRUFBRXNIO2dCQUNuRTlPLFNBQVNYO2dCQUNURixRQUFRQyxHQUFHLENBQUM7WUFDZDtZQUVBLE9BQU9ZO1FBQ1QsRUFBRSxPQUFPRyxPQUFPO1lBQ2RoQixRQUFRZ0IsS0FBSyxDQUFDLGFBQWFBO1lBQzNCLE1BQU1BO1FBQ1I7SUFDRjtJQUVBLFVBQVU7SUFDVixNQUFNOE8sc0JBQXNCO1FBQzFCLElBQUksQ0FBQzlOLGtCQUFrQjtZQUNyQmtMLE1BQU07WUFDTjtRQUNGO1FBRUEsTUFBTTZDLGlCQUFpQnRPLFdBQVd1TyxJQUFJLENBQUNoRSxDQUFBQSxTQUFVQSxPQUFPM0QsRUFBRSxLQUFLckc7UUFDL0QsSUFBSSxDQUFDK04sZ0JBQWdCO1lBQ25CN0MsTUFBTTtZQUNOO1FBQ0Y7UUFFQSxhQUFhO1FBQ2IsSUFBSTZDLGVBQWVoUCxNQUFNLEtBQUssR0FBRztZQUMvQm1NLE1BQU07WUFDTjtRQUNGO1FBRUEsSUFBSTtZQUNGNUssZ0JBQWdCO1lBQ2hCdEMsUUFBUUMsR0FBRyxDQUFDO1lBQ1pELFFBQVFDLEdBQUcsQ0FBQyxrQ0FBd0IrQixrQkFBa0IsT0FBTyxPQUFPQTtZQUNwRWhDLFFBQVFDLEdBQUcsQ0FBQyxzQkFBWThQO1lBQ3hCL1AsUUFBUUMsR0FBRyxDQUFDLHdCQUFjOFAsZUFBZTFILEVBQUUsRUFBRSxPQUFPLE9BQU8wSCxlQUFlMUgsRUFBRTtZQUM1RXJJLFFBQVFDLEdBQUcsQ0FBQyw0QkFBa0J3QixXQUFXMEcsR0FBRyxDQUFDQyxDQUFBQSxJQUFNO29CQUFFQyxJQUFJRCxFQUFFQyxFQUFFO29CQUFFQyxNQUFNLE9BQU9GLEVBQUVDLEVBQUU7b0JBQUUzRCxPQUFPMEQsRUFBRTFELEtBQUs7Z0JBQUM7WUFFakcsY0FBYztZQUNkLE1BQU11TCxvQkFBb0JGLGVBQWUxSCxFQUFFO1lBQzNDckksUUFBUUMsR0FBRyxDQUFDLDJCQUFpQmdRLG1CQUFtQixPQUFPLE9BQU9BO1lBRTlELE1BQU0sRUFBRTdQLE1BQU1GLFFBQVEsRUFBRSxHQUFHLE1BQU1WLHNEQUFTQSxDQUFDMFEsYUFBYSxDQUFDRDtZQUV6RCxJQUFJL1AsU0FBUzhILElBQUksS0FBSyxLQUFLO2dCQUN6QmhJLFFBQVFDLEdBQUcsQ0FBQyxhQUFhQyxTQUFTRSxJQUFJO2dCQUN0QzhNLE1BQU07Z0JBRU4sY0FBYztnQkFDZCxNQUFNdEY7WUFDUixPQUFPO2dCQUNMNUgsUUFBUWdCLEtBQUssQ0FBQyxhQUFhZCxTQUFTMEksT0FBTztnQkFDM0NzRSxNQUFNaE4sU0FBUzBJLE9BQU8sSUFBSTtZQUM1QjtRQUNGLEVBQUUsT0FBTzVILE9BQVk7Z0JBRU1BLGlCQUdyQkEsc0JBQUFBO1lBSkpoQixRQUFRZ0IsS0FBSyxDQUFDLGFBQWFBO1lBQzNCaEIsUUFBUWdCLEtBQUssQ0FBQyxZQUFXQSxrQkFBQUEsTUFBTWQsUUFBUSxjQUFkYyxzQ0FBQUEsZ0JBQWdCWixJQUFJO1lBRTdDLFlBQVk7WUFDWixLQUFJWSxtQkFBQUEsTUFBTWQsUUFBUSxjQUFkYyx3Q0FBQUEsdUJBQUFBLGlCQUFnQlosSUFBSSxjQUFwQlksMkNBQUFBLHFCQUFzQjRILE9BQU8sRUFBRTtnQkFDakNzRSxNQUFNbE0sTUFBTWQsUUFBUSxDQUFDRSxJQUFJLENBQUN3SSxPQUFPO1lBQ25DLE9BQU8sSUFBSTVILE1BQU00SCxPQUFPLEVBQUU7Z0JBQ3hCc0UsTUFBTWxNLE1BQU00SCxPQUFPO1lBQ3JCLE9BQU87Z0JBQ0xzRSxNQUFNO1lBQ1I7UUFDRixTQUFVO1lBQ1I1SyxnQkFBZ0I7UUFDbEI7SUFDRjtJQUVBLFNBQVM7SUFDVCxNQUFNNk4sYUFBYTtRQUNqQixJQUFJO1lBQ0YzTixjQUFjO1lBQ2QsY0FBYztZQUNkLElBQUlOLGVBQWV5SyxJQUFJLEdBQUcsR0FBRztnQkFDM0JPLE1BQU07Z0JBQ04xSyxjQUFjO2dCQUNkO1lBQ0Y7WUFFQXhDLFFBQVFDLEdBQUcsQ0FBQztZQUVaLHlCQUF5QjtZQUN6QixJQUFJK0Isb0JBQW9CRixtQkFBbUIsVUFBVTtnQkFDbkQsTUFBTWlPLGlCQUFpQnRPLFdBQVd1TyxJQUFJLENBQUM1SCxDQUFBQSxJQUFLQSxFQUFFQyxFQUFFLEtBQUtyRztnQkFDckQsSUFBSStOLGdCQUFnQjt3QkFXS3ZPO29CQVZ2QixTQUFTO29CQUNULE1BQU00TyxnQkFBZ0I7d0JBQ3BCLEdBQUdMLGNBQWM7d0JBQ2pCckwsT0FBT2xELGFBQWFrRCxLQUFLO3dCQUN6QkMsYUFBYW5ELGFBQWFtRCxXQUFXO3dCQUNyQ0MsWUFBWXBELGFBQWFvRCxVQUFVO3dCQUNuQ1UsVUFBVTlELGFBQWF1RCxjQUFjLEdBQUcsSUFBSTt3QkFDNUNRLGFBQWEvRCxhQUFhMEQsbUJBQW1CLEdBQUcsSUFBSTt3QkFDcERHLGVBQWU3RCxhQUFhNkQsYUFBYTt3QkFDekNELGNBQWM1RCxhQUFhNEQsWUFBWTt3QkFDdkNpSyxxQkFBcUI3TixFQUFBQSxrQ0FBQUEsYUFBYTJELGlCQUFpQixjQUE5QjNELHNEQUFBQSxnQ0FBZ0MyRyxHQUFHLENBQUNrSSxDQUFBQSxXQUFhO2dDQUNwRTNMLE9BQU8yTCxTQUFTMUssSUFBSTtnQ0FDcEJELEtBQUsySyxTQUFTM0ssR0FBRztnQ0FDakJmLGFBQWEwTCxTQUFTMUssSUFBSTs0QkFDNUIsUUFBTyxFQUFFO29CQUNYO29CQUVBLGNBQWM7b0JBQ2RoRSxjQUFjNEgsQ0FBQUEsT0FBUUEsS0FBS3BCLEdBQUcsQ0FBQzZELENBQUFBLFNBQzdCQSxPQUFPM0QsRUFBRSxLQUFLckcsbUJBQW1Cb08sZ0JBQWdCcEU7b0JBR25ELFVBQVU7b0JBQ1YsTUFBTXVELFdBQVdhO2dCQUNuQjtZQUNGO1lBRUEsV0FBVztZQUNYLE1BQU1oUSxPQUFPO2dCQUNYc0UsT0FBT3JCO2dCQUNQRTtnQkFDQUU7Z0JBQ0FoQztZQUNGO1lBRUFMLE9BQU9oQjtZQUNQZTtZQUVBbkIsUUFBUUMsR0FBRyxDQUFDO1FBQ2QsRUFBRSxPQUFPZSxPQUFZO1lBQ25CaEIsUUFBUWdCLEtBQUssQ0FBQyxXQUFXQTtZQUN6QmtNLE1BQU0sU0FBZ0MsT0FBdkJsTSxNQUFNNEgsT0FBTyxJQUFJO1FBQ2xDLFNBQVU7WUFDUnBHLGNBQWM7UUFDaEI7SUFDRjtJQUVBLFNBQVM7SUFDVCxNQUFNOE4sZ0JBQWdCO1FBQ3BCLGtCQUFrQjtRQUNsQixJQUFJM04saUJBQWlCLEdBQUc7WUFDdEI7UUFDRjtRQUVBLElBQUk7WUFDRkQsc0JBQXNCO1lBRXRCLFVBQVU7WUFDVixJQUFJakIsV0FBVytHLE1BQU0sS0FBSyxHQUFHO2dCQUMzQjBFLE1BQU07Z0JBQ047WUFDRjtZQUVBbE4sUUFBUUMsR0FBRyxDQUFDLCtCQUFxQkY7WUFFakMsTUFBTSxFQUFFSyxNQUFNRixRQUFRLEVBQUUsR0FBRyxNQUFNVixzREFBU0EsQ0FBQytRLG1CQUFtQixDQUFDeFE7WUFFL0QsSUFBSUcsU0FBUzhILElBQUksS0FBSyxLQUFLO2dCQUN6QmhJLFFBQVFDLEdBQUcsQ0FBQyxlQUFlQyxTQUFTRSxJQUFJO2dCQUV4QyxTQUFTO2dCQUNULE1BQU1vUSxjQUFjdFEsU0FBU0UsSUFBSTtnQkFDakMsSUFBSXFRLGlCQUFpQixRQUEwQixPQUFsQkQsWUFBWTlMLEtBQUssRUFBQztnQkFFL0MsbUJBQW1CO2dCQUNuQixJQUFJOEwsWUFBWUUsWUFBWSxFQUFFO29CQUM1QixNQUFNQyxRQUFRSCxZQUFZRSxZQUFZO29CQUN0Q0Qsa0JBQWtCLHFCQUEwREQsT0FBckNBLFlBQVlJLFlBQVksRUFBQyxjQUFvREQsT0FBeENILFlBQVlLLGdCQUFnQixFQUFDLGFBQThDRixPQUFuQ0EsTUFBTUcsZ0JBQWdCLEVBQUMsY0FBa0RILE9BQXRDQSxNQUFNSSxtQkFBbUIsRUFBQyxjQUFzQyxPQUExQkosTUFBTUssbUJBQW1CLEVBQUM7b0JBRXZOLElBQUlMLE1BQU1NLGtCQUFrQixHQUFHLEdBQUc7d0JBQ2hDLE1BQU1DLGtCQUFrQkMsS0FBS0MsS0FBSyxDQUFDVCxNQUFNTSxrQkFBa0IsR0FBRzt3QkFDOURSLGtCQUFrQixhQUE2QixPQUFoQlMsaUJBQWdCO29CQUNqRDtnQkFDRjtnQkFFQWhFLE1BQU11RDtnQkFFTixhQUFhO2dCQUNiN04sZ0JBQWdCO2dCQUVoQixjQUFjO2dCQUNkLE1BQU1nRjtnQkFFTixZQUFZO2dCQUNaeEcsT0FBTztvQkFDTGtILE1BQU07b0JBQ052SSxVQUFVQTtvQkFDVjZJLFNBQVM7Z0JBQ1g7WUFDRixPQUFPO2dCQUNMNUksUUFBUWdCLEtBQUssQ0FBQyxlQUFlZCxTQUFTMEksT0FBTztnQkFDN0NzRSxNQUFNaE4sU0FBUzBJLE9BQU8sSUFBSTtZQUM1QjtRQUNGLEVBQUUsT0FBTzVILE9BQVk7Z0JBS2ZBLHNCQUFBQTtZQUpKaEIsUUFBUWdCLEtBQUssQ0FBQyxlQUFlQTtZQUU3QixZQUFZO1lBQ1osSUFBSXFRLGVBQWU7WUFDbkIsS0FBSXJRLGtCQUFBQSxNQUFNZCxRQUFRLGNBQWRjLHVDQUFBQSx1QkFBQUEsZ0JBQWdCWixJQUFJLGNBQXBCWSwyQ0FBQUEscUJBQXNCNEgsT0FBTyxFQUFFO2dCQUNqQ3lJLGVBQWVyUSxNQUFNZCxRQUFRLENBQUNFLElBQUksQ0FBQ3dJLE9BQU87WUFDNUMsT0FBTyxJQUFJNUgsTUFBTTRILE9BQU8sRUFBRTtnQkFDeEJ5SSxlQUFlclEsTUFBTTRILE9BQU87WUFDOUI7WUFFQXNFLE1BQU1tRTtRQUNSLFNBQVU7WUFDUjNPLHNCQUFzQjtRQUN4QjtJQUNGO0lBRUEsbUJBQW1CO0lBQ25CLE1BQU00TyxpQkFBaUI7UUFDckJuQjtJQUNGO0lBRUEsU0FBUztJQUNULE1BQU1vQixvQkFBb0I7UUFDeEJ4UCxrQkFBa0I7UUFDbEJFLG9CQUFvQjtJQUN0QjtJQUVBLFNBQVM7SUFDVCxNQUFNdVAsdUJBQXVCLENBQUNDO1FBQzVCaE4sZ0JBQWdCOEUsQ0FBQUEsT0FBUztnQkFDdkIsR0FBR0EsSUFBSTtnQkFDUG5ELGtCQUFrQnFMLFNBQVNDLFlBQVk7WUFDekM7UUFDQXRPLHdCQUF3QjtJQUMxQjtJQUVBLFdBQVc7SUFDWCxNQUFNdU8sMkJBQTJCO1FBQy9Cdk8sd0JBQXdCO0lBQzFCO0lBRUEsV0FBVztJQUNYLE1BQU13SSxrQkFBa0IsT0FBT2xMO1FBQzdCcUIsa0JBQWtCO1FBQ2xCRSxvQkFBb0J2QjtRQUVwQixtQkFBbUI7UUFDbkIsSUFBSW1ELE1BQU0yRSxNQUFNLEtBQUssS0FBSyxDQUFDekUsY0FBYztZQUN2Q2dFO1FBQ0Y7UUFFQSwyQkFBMkI7UUFDM0IsTUFBTWdJLGlCQUFpQnRPLFdBQVd1TyxJQUFJLENBQUNoRSxDQUFBQSxTQUFVQSxPQUFPM0QsRUFBRSxLQUFLM0g7UUFDL0RWLFFBQVFDLEdBQUcsQ0FBQyx1QkFBYThQO1FBRXpCLElBQUlBLGdCQUFnQjtZQUNsQixJQUFJO2dCQUNGL1AsUUFBUUMsR0FBRyxDQUFDLGlDQUF1QkYsVUFBVSxhQUFhVztnQkFFMUQsY0FBYztnQkFDZCxNQUFNLEVBQUVOLE1BQU13UixHQUFHLEVBQUUsR0FBRyxNQUFNblIsa0JBQWtCVixVQUFVVztnQkFFeEQsSUFBSWtSLElBQUk1SixJQUFJLEtBQUssT0FBTzRKLElBQUl4UixJQUFJLEVBQUU7d0JBS055Uix1Q0FPQ0E7b0JBWDNCLE1BQU1BLG1CQUFtQkQsSUFBSXhSLElBQUk7b0JBQ2pDSixRQUFRQyxHQUFHLENBQUMsY0FBYzRSO29CQUUxQixpREFBaUQ7b0JBQ2pELE1BQU0xTSxvQkFBb0IwTSxFQUFBQSx3Q0FBQUEsaUJBQWlCeEMsbUJBQW1CLGNBQXBDd0MsNERBQUFBLHNDQUFzQzFKLEdBQUcsQ0FBQyxDQUFDbUgsV0FBbUI7NEJBQ3RGaEgsTUFBTTs0QkFDTjNDLE1BQU0ySixTQUFTNUssS0FBSyxJQUFJNEssU0FBUzNKLElBQUksSUFBSTs0QkFDekNELEtBQUs0SixTQUFTNUosR0FBRzt3QkFDbkIsUUFBTyxFQUFFO29CQUVULGlDQUFpQztvQkFDakMsTUFBTW9NLHFCQUFxQkQsRUFBQUEsaUNBQUFBLGlCQUFpQnpNLFlBQVksY0FBN0J5TSxxREFBQUEsK0JBQStCMUosR0FBRyxDQUFDLENBQUM0RyxPQUFlOzRCQUM1RXJLLE9BQU9xSyxLQUFLckssS0FBSyxJQUFJOzRCQUNyQnNCLFNBQVNrRCxNQUFNQyxPQUFPLENBQUM0RixLQUFLL0ksT0FBTyxJQUFJK0ksS0FBSy9JLE9BQU8sQ0FBQzBKLElBQUksQ0FBQyxRQUFTWCxLQUFLL0ksT0FBTyxJQUFJO3dCQUNwRixRQUFPLEVBQUU7b0JBRVRoRyxRQUFRQyxHQUFHLENBQUMseUJBQWVrRjtvQkFDM0JuRixRQUFRQyxHQUFHLENBQUMseUJBQWU2UjtvQkFFM0JyTixnQkFBZ0I4RSxDQUFBQTs0QkFVSnNJLHVDQUFBQSxpQ0FDQ0Esd0NBQUFBLGtDQUNJQSwwQ0FBQUEsa0NBQ0NBLDJDQUFBQTsrQkFiTzs0QkFDdkIsR0FBR3RJLElBQUk7NEJBQ1A3RSxPQUFPbU4saUJBQWlCbk4sS0FBSzs0QkFDN0JDLGFBQWFrTixpQkFBaUJsTixXQUFXOzRCQUN6Q0MsWUFBWWlOLGlCQUFpQmpOLFVBQVUsSUFBSTs0QkFDM0NHLGdCQUFnQjhNLGlCQUFpQnZNLFFBQVEsS0FBSzs0QkFDOUNKLHFCQUFxQjJNLGlCQUFpQnRNLFdBQVcsS0FBSzs0QkFDdERGLGVBQWV3TSxpQkFBaUJ4TSxhQUFhLElBQUksQ0FBQzs0QkFDbERELGNBQWMwTTs0QkFDZDNNLG1CQUFtQkE7NEJBQ25CTixVQUFVZ04sRUFBQUEsa0NBQUFBLGlCQUFpQnhNLGFBQWEsY0FBOUJ3TSx1REFBQUEsd0NBQUFBLGdDQUFnQ3BNLEtBQUssY0FBckNvTSw0REFBQUEsc0NBQXVDbk0sR0FBRyxLQUFJOzRCQUN4RFosV0FBVytNLEVBQUFBLG1DQUFBQSxpQkFBaUJ4TSxhQUFhLGNBQTlCd00sd0RBQUFBLHlDQUFBQSxpQ0FBZ0NwTSxLQUFLLGNBQXJDb00sNkRBQUFBLHVDQUF1Q2xNLElBQUksS0FBSTs0QkFDMURYLGVBQWU2TSxFQUFBQSxtQ0FBQUEsaUJBQWlCeE0sYUFBYSxjQUE5QndNLHdEQUFBQSwyQ0FBQUEsaUNBQWdDak0sUUFBUSxjQUF4Q2lNLCtEQUFBQSx5Q0FBMENuTSxHQUFHLEtBQUk7NEJBQ2hFVCxnQkFBZ0I0TSxFQUFBQSxtQ0FBQUEsaUJBQWlCeE0sYUFBYSxjQUE5QndNLHdEQUFBQSw0Q0FBQUEsaUNBQWdDak0sUUFBUSxjQUF4Q2lNLGdFQUFBQSwwQ0FBMENsTSxJQUFJLEtBQUk7d0JBQ3BFOztnQkFDRixPQUFPO29CQUNMM0YsUUFBUWdCLEtBQUssQ0FBQyxlQUFlNFEsSUFBSWhKLE9BQU87b0JBQ3hDLGFBQWE7b0JBQ2JuRSxnQkFBZ0I4RSxDQUFBQSxPQUFTOzRCQUN2QixHQUFHQSxJQUFJOzRCQUNQN0UsT0FBT3FMLGVBQWVyTCxLQUFLOzRCQUMzQkMsYUFBYW9MLGVBQWVwTCxXQUFXOzRCQUN2Q0MsWUFBWW1MLGVBQWVuTCxVQUFVLElBQUk7NEJBQ3pDRyxnQkFBZ0JnTCxlQUFlekssUUFBUSxLQUFLOzRCQUM1Q0oscUJBQXFCNkssZUFBZXhLLFdBQVcsS0FBSzs0QkFDcERKLG1CQUFtQixFQUFFLENBQUMsU0FBUzt3QkFDakM7Z0JBQ0Y7WUFDRixFQUFFLE9BQU9uRSxPQUFPO2dCQUNkaEIsUUFBUWdCLEtBQUssQ0FBQyxlQUFlQTtnQkFDN0IsYUFBYTtnQkFDYnlELGdCQUFnQjhFLENBQUFBLE9BQVM7d0JBQ3ZCLEdBQUdBLElBQUk7d0JBQ1A3RSxPQUFPcUwsZUFBZXJMLEtBQUs7d0JBQzNCQyxhQUFhb0wsZUFBZXBMLFdBQVc7d0JBQ3ZDQyxZQUFZbUwsZUFBZW5MLFVBQVUsSUFBSTt3QkFDekNHLGdCQUFnQmdMLGVBQWV6SyxRQUFRLEtBQUs7d0JBQzVDSixxQkFBcUI2SyxlQUFleEssV0FBVyxLQUFLO3dCQUNwREosbUJBQW1CLEVBQUUsQ0FBQyxTQUFTO29CQUNqQztZQUNGO1FBQ0Y7SUFDRjtJQUVBLFVBQVU7SUFDVixNQUFNNUQsb0JBQW9CO1FBQ3hCLE9BQU9FLFdBQVd1TyxJQUFJLENBQUNoRSxDQUFBQSxTQUFVQSxPQUFPM0QsRUFBRSxLQUFLckc7SUFDakQ7SUFFQSxJQUFJLENBQUNkLFdBQVcsT0FBTztJQUV2QixxQkFDRSw4REFBQzZRO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDRDtnQkFBSUMsV0FBVTs7a0NBRWIsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDQzt3Q0FBR0QsV0FBVTtrREFBb0I7Ozs7OztrREFDbEMsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0U7Z0RBQ0NDLFNBQVNaO2dEQUNUUyxXQUFXLDRCQUEwRSxPQUE5Q2xRLG1CQUFtQixhQUFhLFdBQVc7MERBRWxGLDRFQUFDM0MsOEdBQVFBO29EQUFDNlMsV0FBVTs7Ozs7Ozs7Ozs7MERBRXRCLDhEQUFDRTtnREFBT0MsU0FBU3BIO2dEQUFjaUgsV0FBVTswREFDdkMsNEVBQUM1Uyw4R0FBSUE7b0RBQUM0UyxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FJdEIsOERBQUNFO2dDQUFPQyxTQUFTaFI7Z0NBQVM2USxXQUFVOzBDQUNsQyw0RUFBQzlTLDhHQUFDQTtvQ0FBQzhTLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQUtqQiw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUViLDhEQUFDRDtnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQ0Q7b0NBQUlDLFdBQVU7OENBQ1pwUSx3QkFDQyw4REFBQ21RO3dDQUFJQyxXQUFVO2tEQUNiLDRFQUFDSTtzREFBRTs7Ozs7Ozs7OztvREFFSDNRLFdBQVcrRyxNQUFNLEtBQUssa0JBQ3hCLDhEQUFDdUo7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDRDtnREFBSUMsV0FBVTswREFDYiw0RUFBQzNTLDhHQUFRQTtvREFBQzJTLFdBQVU7Ozs7Ozs7Ozs7OzBEQUV0Qiw4REFBQ0s7Z0RBQUdMLFdBQVU7MERBQTBCOzs7Ozs7MERBQ3hDLDhEQUFDSTtnREFBRUosV0FBVTswREFBZ0M7Ozs7OzswREFHN0MsOERBQUNFO2dEQUNDQyxTQUFTcEg7Z0RBQ1RpSCxXQUFVOztrRUFFViw4REFBQzVTLDhHQUFJQTt3REFBQzRTLFdBQVU7Ozs7OztvREFBWTs7Ozs7Ozs7Ozs7O29EQUtoQ3ZRLFdBQVcwRyxHQUFHLENBQUMsQ0FBQzZELHVCQUNkLDhEQUFDK0Y7NENBRUNDLFdBQVcsb0JBQW1FLE9BQS9DaFEscUJBQXFCZ0ssT0FBTzNELEVBQUUsR0FBRyxXQUFXOzRDQUMzRThKLFNBQVMsSUFBTXZHLGdCQUFnQkksT0FBTzNELEVBQUU7OzhEQUV4Qyw4REFBQzBKO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ007NERBQUtOLFdBQVU7c0VBQXlCaEcsT0FBT3RILEtBQUs7Ozs7OztzRUFDckQsOERBQUM0Tjs0REFBS04sV0FBVyx1QkFBbUUsT0FBNUNoRyxPQUFPakwsTUFBTSxLQUFLLElBQUksY0FBYztzRUFDekVpTCxPQUFPakwsTUFBTSxLQUFLLElBQUksUUFBUTs7Ozs7Ozs7Ozs7OzhEQUduQyw4REFBQ21SO29EQUNDQyxTQUFTLENBQUMxSTt3REFDUkEsRUFBRThJLGVBQWU7d0RBQ2pCMUcsa0JBQWtCRyxPQUFPM0QsRUFBRTtvREFDN0I7b0RBQ0EySixXQUFVOzhEQUVWLDRFQUFDOVMsOEdBQUNBO3dEQUFDOFMsV0FBVTs7Ozs7Ozs7Ozs7OzJDQWpCVmhHLE9BQU8zRCxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7MENBMEJ4Qiw4REFBQzBKO2dDQUFJQyxXQUFVOztvQ0FDWmxRLG1CQUFtQix3QkFDbEIsOERBQUNpUTt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEO2dEQUFJQyxXQUFVOzBEQUNiLDRFQUFDMVMsOEdBQVFBO29EQUFDMFMsV0FBVTs7Ozs7Ozs7Ozs7MERBRXRCLDhEQUFDSztnREFBR0wsV0FBVTswREFBMEI7Ozs7OzswREFDeEMsOERBQUNJO2dEQUFFSixXQUFVOzBEQUFnQzs7Ozs7Ozs7Ozs7O29DQU1oRGxRLG1CQUFtQiw0QkFDbEI7OzBEQUVFLDhEQUFDaVE7Z0RBQUlDLFdBQVU7MERBQ1oxUSxpQ0FDQyw4REFBQ2tSO29EQUNDQyxLQUFLblI7b0RBQ0xvUixLQUFJO29EQUNKVixXQUFVOzs7Ozs4RUFHWiw4REFBQ0Q7b0RBQUlDLFdBQVU7OERBQ2IsNEVBQUNNO2tFQUFLOzs7Ozs7Ozs7Ozs7Ozs7OzBEQU1aLDhEQUFDUDtnREFBSUMsV0FBVTs7a0VBRWIsOERBQUNEO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ1c7Z0VBQU1YLFdBQVU7MEVBQW9COzs7Ozs7MEVBQ3JDLDhEQUFDekU7Z0VBQ0NqRixNQUFLO2dFQUNMc0ssT0FBT3ZQO2dFQUNQd1AsVUFBVSxDQUFDcEosSUFBTW5HLGdCQUFnQm1HLEVBQUUrQyxNQUFNLENBQUNvRyxLQUFLO2dFQUMvQ1osV0FBVTtnRUFDVmMsYUFBWTs7Ozs7Ozs7Ozs7O2tFQUtoQiw4REFBQ2Y7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDVztnRUFBTVgsV0FBVTswRUFBb0I7Ozs7OzswRUFDckMsOERBQUNyUywyRUFBTUE7Z0VBQ0xvVCxNQUFLO2dFQUNMQyxPQUFPO29FQUFFQyxPQUFPO2dFQUFPO2dFQUN2QkgsYUFBWTtnRUFDWkYsT0FBT3ZPO2dFQUNQd08sVUFBVXZPO2dFQUNWMUMsU0FBUzJDO2dFQUNUMk8sU0FBUy9PLFdBQVdnRSxHQUFHLENBQUNnQyxDQUFBQTtvRUFDdEJuSyxRQUFRQyxHQUFHLENBQUMseUJBQWVrSztvRUFDM0IsT0FBTzt3RUFDTHdJLHFCQUNFLDhEQUFDTDs0RUFBS1UsT0FBTztnRkFBRUcsT0FBT2hKLElBQUlnSixLQUFLOzRFQUFDO3NGQUM3QmhKLElBQUl4RSxJQUFJOzs7Ozs7d0VBR2JpTixPQUFPekksSUFBSTlCLEVBQUU7b0VBQ2Y7Z0VBQ0Y7Ozs7OzswRUFHRiw4REFBQzBKO2dFQUFJaUIsT0FBTztvRUFBRUksVUFBVTtvRUFBUUQsT0FBTztvRUFBUUUsV0FBVztnRUFBTTs7b0VBQUc7b0VBQ3JEbFAsV0FBV3FFLE1BQU07b0VBQUM7b0VBQVNqRSxjQUFjLE1BQU07Ozs7Ozs7Ozs7Ozs7a0VBSy9ELDhEQUFDd047d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDVztnRUFBTVgsV0FBVTswRUFBb0I7Ozs7OzswRUFDckMsOERBQUN6RTtnRUFDQ2pGLE1BQUs7Z0VBQ0xzSyxPQUFPalA7Z0VBQ1BrUCxVQUFVLENBQUNwSixJQUFNN0Ysa0JBQWtCNkYsRUFBRStDLE1BQU0sQ0FBQ29HLEtBQUs7Z0VBQ2pEWixXQUFVO2dFQUNWYyxhQUFZOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OztvQ0FPckJoUixtQkFBbUIsWUFBWVAscUNBQzlCO2tEQUVFLDRFQUFDd1E7NENBQUlDLFdBQVU7OzhEQUViLDhEQUFDRDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNEOzREQUFJQyxXQUFVOzs4RUFDYiw4REFBQ0Q7b0VBQ0NDLFdBQVU7b0VBQ1ZHLFNBQVM7NEVBQU12TTtnRkFBQUEsMkJBQUFBLFNBQVMwTixjQUFjLENBQUMsbUNBQXhCMU4sK0NBQUFBLHlCQUErQytILEtBQUs7OzhFQUVsRW5NLGFBQWFvRCxVQUFVLE1BQUlyRCxxQkFBQUEsaUNBQUFBLHlDQUFBQSxtQkFBcUJxRCxVQUFVLGtCQUN6RCw4REFBQzROO3dFQUNDQyxLQUFLalIsYUFBYW9ELFVBQVUsTUFBSXJELHNCQUFBQSxpQ0FBQUEsMENBQUFBLG9CQUFxQnFELFVBQVU7d0VBQy9EOE4sS0FBSTt3RUFDSlYsV0FBVTs7Ozs7a0dBR1osOERBQUNEO3dFQUFJQyxXQUFVO2tGQUNiLDRFQUFDTTtzRkFBSzs7Ozs7Ozs7Ozs7Ozs7Ozs4RUFJWiw4REFBQy9FO29FQUNDbEYsSUFBRztvRUFDSEMsTUFBSztvRUFDTG1GLFFBQU87b0VBQ1BvRixVQUFVeEc7b0VBQ1YyRyxPQUFPO3dFQUFFTyxTQUFTO29FQUFPOzs7Ozs7OEVBRzNCLDhEQUFDckI7b0VBQ0NGLFdBQVU7b0VBQ1ZHLFNBQVM7d0VBQ1AsaUJBQWlCO3dFQUNqQm5TLFFBQVFDLEdBQUcsQ0FBQzt3RUFDWnlCLGFBQWFxTixJQUFJLENBQUM7b0VBQ3BCOzhFQUNEOzs7Ozs7Ozs7Ozs7c0VBSUgsOERBQUNnRDs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUNEO29FQUFJQyxXQUFVOztzRkFDYiw4REFBQ1c7c0ZBQU07Ozs7OztzRkFDUCw4REFBQ3BGOzRFQUNDakYsTUFBSzs0RUFDTHNLLE9BQU9wUixhQUFha0QsS0FBSyxNQUFJbkQsc0JBQUFBLGlDQUFBQSwwQ0FBQUEsb0JBQXFCbUQsS0FBSyxLQUFJOzRFQUMzRG1PLFVBQVUsQ0FBQ3BKO2dGQUNUaEYsZ0JBQWdCOEUsQ0FBQUEsT0FBUzt3RkFBRSxHQUFHQSxJQUFJO3dGQUFFN0UsT0FBTytFLEVBQUUrQyxNQUFNLENBQUNvRyxLQUFLO29GQUFDO2dGQUMxRHpHLGtCQUFrQm5LLGtCQUFtQnlILEVBQUUrQyxNQUFNLENBQUNvRyxLQUFLOzRFQUNyRDs0RUFDQUUsYUFBWTs7Ozs7Ozs7Ozs7OzhFQUdoQiw4REFBQ2Y7b0VBQUlDLFdBQVU7O3NGQUNiLDhEQUFDVztzRkFBTTs7Ozs7O3NGQUNQLDhEQUFDYTs0RUFDQ1osT0FBT3BSLGFBQWFtRCxXQUFXLE1BQUlwRCxzQkFBQUEsaUNBQUFBLDBDQUFBQSxvQkFBcUJvRCxXQUFXLEtBQUk7NEVBQ3ZFa08sVUFBVSxDQUFDcEosSUFBTWhGLGdCQUFnQjhFLENBQUFBLE9BQVM7d0ZBQUUsR0FBR0EsSUFBSTt3RkFBRTVFLGFBQWE4RSxFQUFFK0MsTUFBTSxDQUFDb0csS0FBSztvRkFBQzs0RUFDakZFLGFBQVk7NEVBQ1pXLE1BQU07Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4REFPZCw4REFBQzFCO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ0s7c0VBQUc7Ozs7OztzRUFHSiw4REFBQ047NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDRDtvRUFBSUMsV0FBVTs7c0ZBQ2IsOERBQUNNO3NGQUFLOzs7Ozs7c0ZBQ04sOERBQUNLOzRFQUFNWCxXQUFVOzs4RkFDZiw4REFBQ3pFO29GQUNDakYsTUFBSztvRkFDTG9MLFNBQVNsUyxhQUFhdUQsY0FBYztvRkFDcEM4TixVQUFVLENBQUNwSixJQUFNaEYsZ0JBQWdCOEUsQ0FBQUEsT0FBUztnR0FBRSxHQUFHQSxJQUFJO2dHQUFFeEUsZ0JBQWdCMEUsRUFBRStDLE1BQU0sQ0FBQ2tILE9BQU87NEZBQUM7Ozs7Ozs4RkFFeEYsOERBQUNwQjtvRkFBS04sV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7O2dFQUduQnhRLGFBQWF1RCxjQUFjLGtCQUMxQiw4REFBQ2dOO29FQUFJQyxXQUFVOzhFQUVaeFEsRUFBQUEsOEJBQUFBLGFBQWE2RCxhQUFhLGNBQTFCN0QsbURBQUFBLG9DQUFBQSw0QkFBNEJpRSxLQUFLLGNBQWpDakUsd0RBQUFBLGtDQUFtQ2tFLEdBQUcsa0JBQ3JDLDhEQUFDcU07d0VBQUlDLFdBQVU7OzBGQUNiLDhEQUFDRDtnRkFBSUMsV0FBVTswRkFDYiw0RUFBQ3ZNO29GQUNDdU0sV0FBVTtvRkFDVjJCLFFBQVE7b0ZBQ1JDLFFBQVFwUyxhQUFhb0QsVUFBVTs7c0dBRS9CLDhEQUFDaVA7NEZBQU9wQixLQUFLalIsYUFBYTZELGFBQWEsQ0FBQ0ksS0FBSyxDQUFDQyxHQUFHOzRGQUFFNEMsTUFBSzs7Ozs7O3dGQUFjOzs7Ozs7Ozs7Ozs7MEZBSTFFLDhEQUFDeUo7Z0ZBQUlDLFdBQVU7MEZBQXVCeFEsYUFBYTZELGFBQWEsQ0FBQ0ksS0FBSyxDQUFDRSxJQUFJOzs7Ozs7MEZBQzNFLDhEQUFDdU07Z0ZBQU9GLFdBQVU7Z0ZBQXdCRyxTQUFTN0U7MEZBQ2pELDRFQUFDZ0Y7OEZBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7a0dBSVYsOERBQUNQO3dFQUFJQyxXQUFVOzswRkFDYiw4REFBQ0Q7Z0ZBQUlDLFdBQVU7MEZBQ2IsNEVBQUNEO29GQUFJQyxXQUFVOzhGQUFZOzs7Ozs7Ozs7OzswRkFFN0IsOERBQUNFO2dGQUFPRixXQUFVO2dGQUF3QkcsU0FBUzdFOzBGQUNqRCw0RUFBQ2dGOzhGQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NFQVNsQiw4REFBQ1A7NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDRDtvRUFBSUMsV0FBVTs7c0ZBQ2IsOERBQUNNO3NGQUFLOzs7Ozs7c0ZBQ04sOERBQUNLOzRFQUFNWCxXQUFVOzs4RkFDZiw4REFBQ3pFO29GQUNDakYsTUFBSztvRkFDTG9MLFNBQVNsUyxhQUFhMEQsbUJBQW1CO29GQUN6QzJOLFVBQVUsQ0FBQ3BKLElBQU1oRixnQkFBZ0I4RSxDQUFBQSxPQUFTO2dHQUFFLEdBQUdBLElBQUk7Z0dBQUVyRSxxQkFBcUJ1RSxFQUFFK0MsTUFBTSxDQUFDa0gsT0FBTzs0RkFBQzs7Ozs7OzhGQUU3Riw4REFBQ3BCO29GQUFLTixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Z0VBR25CeFEsYUFBYTBELG1CQUFtQixrQkFDL0IsOERBQUM2TTtvRUFBSUMsV0FBVTs4RUFFWnhRLEVBQUFBLCtCQUFBQSxhQUFhNkQsYUFBYSxjQUExQjdELG9EQUFBQSx1Q0FBQUEsNkJBQTRCb0UsUUFBUSxjQUFwQ3BFLDJEQUFBQSxxQ0FBc0NrRSxHQUFHLGtCQUN4Qyw4REFBQ3FNO3dFQUFJQyxXQUFVOzswRkFDYiw4REFBQ0Q7Z0ZBQUlDLFdBQVU7O2tHQUNiLDhEQUFDRDt3RkFBSUMsV0FBVTtrR0FBZ0I7Ozs7OztrR0FDL0IsOERBQUNEO3dGQUFJQyxXQUFVO2tHQUNiLDRFQUFDRDs0RkFBSUMsV0FBVTtzR0FBbUJ4USxhQUFhNkQsYUFBYSxDQUFDTyxRQUFRLENBQUNELElBQUk7Ozs7Ozs7Ozs7Ozs7Ozs7OzBGQUc5RSw4REFBQ3VNO2dGQUFPRixXQUFVO2dGQUF3QkcsU0FBU3JFOzBGQUNqRCw0RUFBQ3dFOzhGQUFLOzs7Ozs7Ozs7Ozs7Ozs7O2tHQUlWLDhEQUFDUDt3RUFBSUMsV0FBVTtrRkFDYiw0RUFBQ0U7NEVBQU9GLFdBQVU7NEVBQXdCRyxTQUFTckU7c0ZBQ2pELDRFQUFDd0U7MEZBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzRUFTbEIsOERBQUNQOzREQUFJQyxXQUFVOzs4RUFDYiw4REFBQ0Q7b0VBQUlDLFdBQVU7OEVBQ2IsNEVBQUNNO2tGQUFLOzs7Ozs7Ozs7Ozs4RUFFUiw4REFBQ1A7b0VBQUlDLFdBQVU7O3NGQUNiLDhEQUFDRTs0RUFBT0YsV0FBVTs0RUFBbUJHLFNBQVM5RDs7OEZBQzVDLDhEQUFDaUU7OEZBQUs7Ozs7Ozs4RkFDTiw4REFBQ0E7OEZBQUs7Ozs7Ozs7Ozs7Ozt3RUFFUDlRLGFBQWEyRCxpQkFBaUIsSUFBSTNELGFBQWEyRCxpQkFBaUIsQ0FBQ3FELE1BQU0sR0FBRyxJQUN6RWhILGFBQWEyRCxpQkFBaUIsQ0FBQ2dELEdBQUcsQ0FBQyxDQUFDa0ksVUFBVWpHLHNCQUM1Qyw4REFBQzJIO2dGQUFnQkMsV0FBVTs7a0dBQ3pCLDhEQUFDTTt3RkFDQ04sV0FBVTt3RkFDVkcsU0FBUzs0RkFDUCxJQUFJOUIsU0FBUzNLLEdBQUcsRUFBRTtnR0FDaEJvTyxPQUFPQyxJQUFJLENBQUMxRCxTQUFTM0ssR0FBRyxFQUFFOzRGQUM1Qjt3RkFDRjt3RkFDQXNOLE9BQU87NEZBQ0xnQixRQUFRM0QsU0FBUzNLLEdBQUcsR0FBRyxZQUFZOzRGQUNuQ3lOLE9BQU85QyxTQUFTM0ssR0FBRyxHQUFHLFlBQVk7NEZBQ2xDdU8sZ0JBQWdCNUQsU0FBUzNLLEdBQUcsR0FBRyxjQUFjO3dGQUMvQzt3RkFDQWhCLE9BQU8yTCxTQUFTM0ssR0FBRyxHQUFHLFdBQVcySyxTQUFTMUssSUFBSTs7NEZBQy9DOzRGQUNLMEssU0FBUzFLLElBQUk7Ozs7Ozs7a0dBRW5CLDhEQUFDdU07d0ZBQ0NGLFdBQVU7d0ZBQ1ZHLFNBQVMsSUFBTTdELHVCQUF1QmxFO3dGQUN0QzFGLE9BQU07a0dBQ1A7Ozs7Ozs7K0VBckJPMEY7Ozs7MkdBMkJaLDhEQUFDMkg7NEVBQUlDLFdBQVU7c0ZBQ2IsNEVBQUNNO2dGQUFLVSxPQUFPO29GQUFFRyxPQUFPO29GQUFRQyxVQUFVO2dGQUFPOzBGQUFHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4REFRNUQsOERBQUNyQjtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNEOzREQUFJQyxXQUFVOzs4RUFDYiw4REFBQ0s7OEVBQUc7Ozs7Ozs4RUFDSiw4REFBQ0g7b0VBQ0NGLFdBQVU7b0VBQ1ZHLFNBQVMxRDtvRUFDVC9KLE9BQU07OEVBQ1A7Ozs7Ozs7Ozs7OztzRUFJSCw4REFBQ3FOOzREQUFJQyxXQUFVO3NFQUNaeFEsYUFBYTRELFlBQVksSUFBSTVELGFBQWE0RCxZQUFZLENBQUNvRCxNQUFNLEdBQUcsSUFDL0RoSCxhQUFhNEQsWUFBWSxDQUFDK0MsR0FBRyxDQUFDLENBQUM0RyxNQUFNM0Usc0JBQ25DLDhEQUFDMkg7b0VBQWdCQyxXQUFVOztzRkFDekIsOERBQUNEOzRFQUFJQyxXQUFVOzs4RkFDYiw4REFBQ007b0ZBQUtOLFdBQVU7O3dGQUFhO3dGQUFNNUgsUUFBUTs7Ozs7Ozs4RkFDM0MsOERBQUM4SDtvRkFDQ0YsV0FBVTtvRkFDVkcsU0FBUyxJQUFNekQsdUJBQXVCdEU7b0ZBQ3RDMUYsT0FBTTs4RkFDUDs7Ozs7Ozs7Ozs7O3NGQUlILDhEQUFDcU47NEVBQUlDLFdBQVU7OzhGQUNiLDhEQUFDRDtvRkFBSUMsV0FBVTs7c0dBQ2IsOERBQUNXO3NHQUFNOzs7Ozs7c0dBQ1AsOERBQUNwRjs0RkFDQ2pGLE1BQUs7NEZBQ0xzSyxPQUFPN0QsS0FBS3JLLEtBQUs7NEZBQ2pCbU8sVUFBVSxDQUFDcEosSUFBTWtGLHdCQUF3QnZFLE9BQU9YLEVBQUUrQyxNQUFNLENBQUNvRyxLQUFLOzRGQUM5REUsYUFBWTs0RkFDWmQsV0FBVTs7Ozs7Ozs7Ozs7OzhGQUdkLDhEQUFDRDtvRkFBSUMsV0FBVTs7c0dBQ2IsOERBQUNXO3NHQUFNOzs7Ozs7c0dBQ1AsOERBQUNhOzRGQUNDWixPQUFPN0QsS0FBSy9JLE9BQU87NEZBQ25CNk0sVUFBVSxDQUFDcEosSUFBTW9GLDBCQUEwQnpFLE9BQU9YLEVBQUUrQyxNQUFNLENBQUNvRyxLQUFLOzRGQUNoRUUsYUFBWTs0RkFDWmQsV0FBVTs0RkFDVnlCLE1BQU07Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7bUVBN0JKcko7Ozs7K0ZBb0NaLDhEQUFDMkg7Z0VBQUlDLFdBQVU7MEVBQ2IsNEVBQUNJOzhFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhEQU9YLDhEQUFDTDtvREFBSUMsV0FBVTs4REFDYiw0RUFBQ0Q7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDRDtnRUFBSUMsV0FBVTs7a0ZBQ2IsOERBQUNNO2tGQUFLOzs7Ozs7a0ZBQ04sOERBQUNLO3dFQUFNWCxXQUFVOzswRkFDZiw4REFBQ3pFO2dGQUNDakYsTUFBSztnRkFDTG9MLFNBQVNsUyxhQUFheUUsWUFBWTtnRkFDbEM0TSxVQUFVLENBQUNwSixJQUFNaEYsZ0JBQWdCOEUsQ0FBQUEsT0FBUzs0RkFBRSxHQUFHQSxJQUFJOzRGQUFFdEQsY0FBY3dELEVBQUUrQyxNQUFNLENBQUNrSCxPQUFPO3dGQUFDOzs7Ozs7MEZBRXRGLDhEQUFDcEI7Z0ZBQUtOLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs0REFJbkJ4USxhQUFheUUsWUFBWSxrQkFDeEI7O2tGQUNFLDhEQUFDOEw7d0VBQUlDLFdBQVU7OzBGQUNiLDhEQUFDTTswRkFBSzs7Ozs7OzBGQUNOLDhEQUFDSztnRkFBTVgsV0FBVTs7a0dBQ2YsOERBQUN6RTt3RkFDQ2pGLE1BQUs7d0ZBQ0xvTCxTQUFTbFMsYUFBYTBFLHFCQUFxQjt3RkFDM0MyTSxVQUFVLENBQUNwSixJQUFNaEYsZ0JBQWdCOEUsQ0FBQUEsT0FBUztvR0FBRSxHQUFHQSxJQUFJO29HQUFFckQsdUJBQXVCdUQsRUFBRStDLE1BQU0sQ0FBQ2tILE9BQU87Z0dBQUM7Ozs7OztrR0FFL0YsOERBQUNwQjt3RkFBS04sV0FBVTs7Ozs7Ozs7Ozs7OzRFQUVqQnhRLGFBQWEwRSxxQkFBcUIsa0JBQ2pDLDhEQUFDNkw7Z0ZBQUlDLFdBQVU7O2tHQUNiLDhEQUFDRTt3RkFDQ0YsV0FBVTt3RkFDVkcsU0FBU1I7a0dBQ1Y7Ozs7OztrR0FHRCw4REFBQ0k7d0ZBQUlDLFdBQVU7a0dBQ2IsNEVBQUNNO3NHQUFNOVEsYUFBYTRFLGdCQUFnQixJQUFJOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrRkFNaEQsOERBQUMyTDt3RUFBSUMsV0FBVTs7MEZBQ2IsOERBQUNNOzBGQUFLOzs7Ozs7MEZBQ04sOERBQUNLO2dGQUFNWCxXQUFVOztrR0FDZiw4REFBQ3pFO3dGQUNDakYsTUFBSzt3RkFDTG9MLFNBQVNsUyxhQUFhNkUsbUJBQW1CO3dGQUN6Q3dNLFVBQVUsQ0FBQ3BKLElBQU1oRixnQkFBZ0I4RSxDQUFBQSxPQUFTO29HQUFFLEdBQUdBLElBQUk7b0dBQUVsRCxxQkFBcUJvRCxFQUFFK0MsTUFBTSxDQUFDa0gsT0FBTztnR0FBQzs7Ozs7O2tHQUU3Riw4REFBQ3BCO3dGQUFLTixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7b0VBSW5CeFEsYUFBYTZFLG1CQUFtQixrQkFDL0IsOERBQUMwTDt3RUFBSUMsV0FBVTs7MEZBQ2IsOERBQUNNOzBGQUFLOzs7Ozs7MEZBQ04sOERBQUMvRTtnRkFDQ2pGLE1BQUs7Z0ZBQ0xzSyxPQUFPcFIsYUFBYThFLGNBQWMsSUFBSTtnRkFDdEN1TSxVQUFVLENBQUNwSixJQUFNaEYsZ0JBQWdCOEUsQ0FBQUEsT0FBUzs0RkFBRSxHQUFHQSxJQUFJOzRGQUFFakQsZ0JBQWdCbUQsRUFBRStDLE1BQU0sQ0FBQ29HLEtBQUs7d0ZBQUM7Z0ZBQ3BGRSxhQUFZO2dGQUNaZCxXQUFVOzs7Ozs7Ozs7Ozs7a0ZBS2hCLDhEQUFDRDt3RUFBSUMsV0FBVTs7MEZBQ2IsOERBQUNNOzBGQUFLOzs7Ozs7MEZBQ04sOERBQUNLO2dGQUFNWCxXQUFVOztrR0FDZiw4REFBQ3pFO3dGQUNDakYsTUFBSzt3RkFDTG9MLFNBQVNsUyxhQUFhZ0YsbUJBQW1CO3dGQUN6Q3FNLFVBQVUsQ0FBQ3BKLElBQU1oRixnQkFBZ0I4RSxDQUFBQSxPQUFTO29HQUFFLEdBQUdBLElBQUk7b0dBQUUvQyxxQkFBcUJpRCxFQUFFK0MsTUFBTSxDQUFDa0gsT0FBTztnR0FBQzs7Ozs7O2tHQUU3Riw4REFBQ3BCO3dGQUFLTixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7b0VBS25CeFEsYUFBYWdGLG1CQUFtQixrQkFDL0IsOERBQUN1TDt3RUFBSUMsV0FBVTs7MEZBRWIsOERBQUNEO2dGQUFJQyxXQUFVOztrR0FDYiw4REFBQ0Q7d0ZBQUlDLFdBQVU7OzBHQUNiLDhEQUFDVzswR0FBTTs7Ozs7OzBHQUNQLDhEQUFDcEY7Z0dBQ0NqRixNQUFLO2dHQUNMc0ssT0FBT3BSLGFBQWEwRixVQUFVLENBQUNDLFFBQVE7Z0dBQ3ZDMEwsVUFBVSxDQUFDcEosSUFBTWhGLGdCQUFnQjhFLENBQUFBLE9BQVM7NEdBQ3hDLEdBQUdBLElBQUk7NEdBQ1ByQyxZQUFZO2dIQUFFLEdBQUdxQyxLQUFLckMsVUFBVTtnSEFBRUMsVUFBVXNDLEVBQUUrQyxNQUFNLENBQUNvRyxLQUFLOzRHQUFDO3dHQUM3RDtnR0FDQUUsYUFBWTs7Ozs7Ozs7Ozs7O2tHQUdoQiw4REFBQ2Y7d0ZBQUlDLFdBQVU7OzBHQUNiLDhEQUFDVzswR0FBTTs7Ozs7OzBHQUNQLDhEQUFDcEY7Z0dBQ0NqRixNQUFLO2dHQUNMc0ssT0FBT3BSLGFBQWEwRixVQUFVLENBQUNFLFlBQVk7Z0dBQzNDeUwsVUFBVSxDQUFDcEosSUFBTWhGLGdCQUFnQjhFLENBQUFBLE9BQVM7NEdBQ3hDLEdBQUdBLElBQUk7NEdBQ1ByQyxZQUFZO2dIQUFFLEdBQUdxQyxLQUFLckMsVUFBVTtnSEFBRUUsY0FBY3FDLEVBQUUrQyxNQUFNLENBQUNvRyxLQUFLOzRHQUFDO3dHQUNqRTtnR0FDQUUsYUFBWTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBGQU1sQiw4REFBQ2Y7Z0ZBQUlDLFdBQVU7O2tHQUNiLDhEQUFDVztrR0FBTTs7Ozs7O2tHQUNQLDhEQUFDYTt3RkFDQ1osT0FBT3BSLGFBQWEwRixVQUFVLENBQUNHLGVBQWU7d0ZBQzlDd0wsVUFBVSxDQUFDcEosSUFBTWhGLGdCQUFnQjhFLENBQUFBLE9BQVM7b0dBQ3hDLEdBQUdBLElBQUk7b0dBQ1ByQyxZQUFZO3dHQUFFLEdBQUdxQyxLQUFLckMsVUFBVTt3R0FBRUcsaUJBQWlCb0MsRUFBRStDLE1BQU0sQ0FBQ29HLEtBQUs7b0dBQUM7Z0dBQ3BFO3dGQUNBRSxhQUFZO3dGQUNaVyxNQUFNOzs7Ozs7Ozs7Ozs7MEZBS1YsOERBQUMxQjtnRkFBSUMsV0FBVTs7a0dBQ2IsOERBQUNXOzs0RkFBTTswR0FBTyw4REFBQ0w7Z0dBQUtOLFdBQVU7MEdBQWV4USxhQUFhMEYsVUFBVSxDQUFDSSxtQkFBbUIsQ0FBQ2tCLE1BQU07Ozs7Ozs7Ozs7OztvRkFDOUZoSCxhQUFhMEYsVUFBVSxDQUFDSSxtQkFBbUIsQ0FBQ2EsR0FBRyxDQUFDLENBQUNnRyxNQUFNL0Qsc0JBQ3RELDhEQUFDMkg7NEZBQWdCQyxXQUFVO3NHQUN6Qiw0RUFBQ3pFO2dHQUNDakYsTUFBSztnR0FDTHNLLE9BQU96RTtnR0FDUDBFLFVBQVUsQ0FBQ3BKO29HQUNULE1BQU15SyxXQUFXOzJHQUFJMVMsYUFBYTBGLFVBQVUsQ0FBQ0ksbUJBQW1CO3FHQUFDO29HQUNqRTRNLFFBQVEsQ0FBQzlKLE1BQU0sR0FBR1gsRUFBRStDLE1BQU0sQ0FBQ29HLEtBQUs7b0dBQ2hDbk8sZ0JBQWdCOEUsQ0FBQUEsT0FBUzs0R0FDdkIsR0FBR0EsSUFBSTs0R0FDUHJDLFlBQVk7Z0hBQUUsR0FBR3FDLEtBQUtyQyxVQUFVO2dIQUFFSSxxQkFBcUI0TTs0R0FBUzt3R0FDbEU7Z0dBQ0Y7Z0dBQ0FwQixhQUFZOzs7Ozs7MkZBWk4xSTs7Ozs7a0dBZ0JaLDhEQUFDOEg7d0ZBQ0M1SixNQUFLO3dGQUNMMEosV0FBVTt3RkFDVkcsU0FBUyxJQUFNMU4sZ0JBQWdCOEUsQ0FBQUEsT0FBUztvR0FDdEMsR0FBR0EsSUFBSTtvR0FDUHJDLFlBQVk7d0dBQ1YsR0FBR3FDLEtBQUtyQyxVQUFVO3dHQUNsQkkscUJBQXFCOytHQUFJaUMsS0FBS3JDLFVBQVUsQ0FBQ0ksbUJBQW1COzRHQUFFO3lHQUFHO29HQUNuRTtnR0FDRjtrR0FDRDs7Ozs7Ozs7Ozs7OzBGQU1ILDhEQUFDeUs7Z0ZBQUlDLFdBQVU7O2tHQUNiLDhEQUFDVztrR0FBTTs7Ozs7O2tHQUNQLDhEQUFDWjt3RkFBSUMsV0FBVTtrR0FDYiw0RUFBQ0Q7NEZBQUlDLFdBQVU7OzhHQUNiLDhEQUFDSTtvR0FBRUosV0FBVTs4R0FBWTs7Ozs7OzhHQUN6Qiw4REFBQ0Q7b0dBQ0NDLFdBQVU7b0dBQ1ZnQixPQUFPO3dHQUNMbUIsV0FBVzt3R0FDWEgsUUFBUTt3R0FDUkksWUFBWTtvR0FDZDs4R0FFQ3JRLDZCQUNDLDhEQUFDZ087d0dBQUlDLFdBQVU7OzBIQUNiLDhEQUFDRDtnSEFBSUMsV0FBVTs7Ozs7OzBIQUNmLDhEQUFDTTswSEFBSzs7Ozs7Ozs7Ozs7b0hBRU56TyxNQUFNMkUsTUFBTSxHQUFHLGtCQUNqQiw4REFBQ3VKO3dHQUNDQyxXQUFVO3dHQUNWcUMsU0FBUzdLO2tIQUVSM0YsTUFBTXNFLEdBQUcsQ0FBQyxDQUFDbU0scUJBQ1YsOERBQUN2QztnSEFFQ0MsV0FBVyxhQUFpRSxPQUFwRC9OLGdCQUFnQnFGLFFBQVEsQ0FBQ2dMLEtBQUtqTSxFQUFFLElBQUksYUFBYTtnSEFDekU4SixTQUFTLElBQU0vSSxpQkFBaUJrTCxLQUFLak0sRUFBRTs7a0lBRXZDLDhEQUFDMEo7d0hBQUlDLFdBQVU7OzRIQUNac0MsS0FBSzFQLFVBQVUsSUFBSTBQLEtBQUtDLGVBQWUsaUJBQ3RDLDhEQUFDL0I7Z0lBQ0NDLEtBQUs2QixLQUFLMVAsVUFBVSxJQUFJMFAsS0FBS0MsZUFBZTtnSUFDNUM3QixLQUFLNEIsS0FBSzVQLEtBQUs7Z0lBQ2Y4UCxTQUFTLENBQUMvSztvSUFDUixNQUFNK0MsU0FBUy9DLEVBQUVFLGFBQWE7b0lBQzlCNkMsT0FBT3dHLEtBQUssQ0FBQ08sT0FBTyxHQUFHO29JQUN2QixNQUFNa0IsY0FBY2pJLE9BQU9rSSxrQkFBa0I7b0lBQzdDLElBQUlELGFBQWE7d0lBQ2ZBLFlBQVl6QixLQUFLLENBQUNPLE9BQU8sR0FBRztvSUFDOUI7Z0lBQ0Y7Ozs7OzRJQUVBOzBJQUNKLDhEQUFDeEI7Z0lBQUlDLFdBQVU7Z0lBQW1CZ0IsT0FBTztvSUFBRU8sU0FBU2UsS0FBSzFQLFVBQVUsSUFBSTBQLEtBQUtDLGVBQWUsR0FBRyxTQUFTO2dJQUFPOzBJQUFHOzs7Ozs7Ozs7Ozs7a0lBSW5ILDhEQUFDeEM7d0hBQUlDLFdBQVU7a0lBQWNzQyxLQUFLNVAsS0FBSyxJQUFJNFAsS0FBSzNPLElBQUksSUFBSTJPLEtBQUtLLFFBQVEsSUFBSTs7Ozs7O29IQUN4RTFRLGdCQUFnQnFGLFFBQVEsQ0FBQ2dMLEtBQUtqTSxFQUFFLG1CQUMvQiw4REFBQzBKO3dIQUFJQyxXQUFVO2tJQUFxQjs7Ozs7OzsrR0F6QmpDc0MsS0FBS2pNLEVBQUU7Ozs7Ozs7OztrSUErQmxCLDhEQUFDMEo7d0dBQUlDLFdBQVU7a0hBQ2IsNEVBQUNEOzRHQUFJQyxXQUFVO3NIQUFhOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEZBU3hDLDhEQUFDRDtnRkFBSUMsV0FBVTs7a0dBQ2IsOERBQUNXO2tHQUFNOzs7Ozs7a0dBQ1AsOERBQUNaO3dGQUFJQyxXQUFVO2tHQUNiLDRFQUFDRDs0RkFBSUMsV0FBVTs7OEdBQ2IsOERBQUNFO29HQUNDNUosTUFBSztvR0FDTDBKLFdBQVU7b0dBQ1ZHLFNBQVM7d0dBQ1AsU0FBUzt3R0FDVCxNQUFNNUUsUUFBUTNILFNBQVM0SCxhQUFhLENBQUM7d0dBQ3JDRCxNQUFNakYsSUFBSSxHQUFHO3dHQUNiaUYsTUFBTUUsTUFBTSxHQUFHO3dHQUNmRixNQUFNRyxRQUFRLEdBQUcsQ0FBQ2pFO2dIQUNIOzRHQUFiLE1BQU04QyxRQUFPLG9CQUFHQyxNQUFNLENBQXNCQyxLQUFLLGNBQXBDLHFEQUFzQyxDQUFDLEVBQUU7NEdBQ3RELElBQUlGLE1BQU07Z0hBQ1I5SCxnQkFBZ0I4RSxDQUFBQSxPQUFTO3dIQUN2QixHQUFHQSxJQUFJO3dIQUNQckMsWUFBWTs0SEFDVixHQUFHcUMsS0FBS3JDLFVBQVU7NEhBQ2xCTSxvQkFBb0I7bUlBQ2YrQixLQUFLckMsVUFBVSxDQUFDTSxrQkFBa0I7Z0lBQ3JDO29JQUFFYyxNQUFNO29JQUFRM0MsTUFBTTRHLEtBQUs1RyxJQUFJO2dJQUFDOzZIQUNqQzt3SEFDSDtvSEFDRjs0R0FDRjt3R0FDRjt3R0FDQTRILE1BQU1JLEtBQUs7b0dBQ2I7O3NIQUVBLDhEQUFDdk8sOEdBQUlBOzRHQUFDdU4sTUFBTTs7Ozs7O3dHQUFNOzs7Ozs7O2dHQUduQm5MLGFBQWEwRixVQUFVLENBQUNNLGtCQUFrQixDQUFDVyxHQUFHLENBQUMsQ0FBQ21ILFVBQVVsRixzQkFDekQsOERBQUMySDt3R0FBZ0JDLFdBQVU7OzBIQUN6Qiw4REFBQ007MEhBQU1oRCxTQUFTM0osSUFBSTs7Ozs7OzBIQUNwQiw4REFBQ3VNO2dIQUNDNUosTUFBSztnSEFDTDBKLFdBQVU7Z0hBQ1ZHLFNBQVM7b0hBQ1AsTUFBTXlDLGVBQWVwVCxhQUFhMEYsVUFBVSxDQUFDTSxrQkFBa0IsQ0FBQ2tCLE1BQU0sQ0FBQyxDQUFDNkYsR0FBR0MsSUFBTUEsTUFBTXBFO29IQUN2RjNGLGdCQUFnQjhFLENBQUFBLE9BQVM7NEhBQ3ZCLEdBQUdBLElBQUk7NEhBQ1ByQyxZQUFZO2dJQUFFLEdBQUdxQyxLQUFLckMsVUFBVTtnSUFBRU0sb0JBQW9Cb047NEhBQWE7d0hBQ3JFO2dIQUNGOzBIQUNEOzs7Ozs7O3VHQVpPeEs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FpQ3hDLDhEQUFDMkg7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDRDtnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQ0U7b0NBQ0NDLFNBQVM3QjtvQ0FDVDBCLFdBQVU7b0NBQ1Y2QyxVQUFVcFQsV0FBVytHLE1BQU0sS0FBSyxLQUFLL0Ysc0JBQXNCRSxpQkFBaUI7b0NBQzVFK0IsT0FDRS9CLGlCQUFpQixJQUNiLFlBQ0FsQixXQUFXK0csTUFBTSxLQUFLLElBQ3BCLHlCQUNBL0YscUJBQ0UsZ0JBQ0E7OENBR1RFLGlCQUFpQixJQUNkLFFBQ0FGLHFCQUNFLFlBQ0E7Ozs7Ozs7Ozs7OzBDQUlWLDhEQUFDc1A7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRTt3Q0FBT0MsU0FBU2I7d0NBQWdCVSxXQUFVO2tEQUF1Qzs7Ozs7O2tEQUdsRiw4REFBQ0U7d0NBQ0NDLFNBQVNyQzt3Q0FDVGtDLFdBQVU7d0NBQ1Y2QyxVQUFVLENBQUM3UyxvQkFBb0JQLEVBQUFBLG1CQUFBQSxXQUFXdU8sSUFBSSxDQUFDNUgsQ0FBQUEsSUFBS0EsRUFBRUMsRUFBRSxLQUFLckcsK0JBQTlCUCx1Q0FBQUEsaUJBQWlEVixNQUFNLE1BQUssS0FBS3NCO3dDQUNoR3FDLE9BQ0UsQ0FBQzFDLG1CQUNHLGVBQ0FQLEVBQUFBLG9CQUFBQSxXQUFXdU8sSUFBSSxDQUFDNUgsQ0FBQUEsSUFBS0EsRUFBRUMsRUFBRSxLQUFLckcsK0JBQTlCUCx3Q0FBQUEsa0JBQWlEVixNQUFNLE1BQUssSUFDMUQsV0FDQXNCLGVBQ0UsY0FDQTtrREFHVEEsZUFBZSxZQUFZOzs7Ozs7a0RBRTlCLDhEQUFDNlA7d0NBQ0NDLFNBQVNoQzt3Q0FDVDZCLFdBQVU7d0NBQ1Y2QyxVQUFVM1MsZUFBZXlLLElBQUksR0FBRyxLQUFLcEssY0FBY2QsV0FBVytHLE1BQU0sS0FBSzt3Q0FDekU5RCxPQUNFakQsV0FBVytHLE1BQU0sS0FBSyxJQUNsQixhQUNBdEcsZUFBZXlLLElBQUksR0FBRyxJQUNwQixxQkFDQXBLLGFBQ0dkLFdBQVdxVCxJQUFJLENBQUMxTSxDQUFBQSxJQUFLQSxFQUFFQyxFQUFFLEdBQUcsV0FBVyxjQUFjLGNBQ3JENUcsV0FBV3FULElBQUksQ0FBQzFNLENBQUFBLElBQUtBLEVBQUVDLEVBQUUsR0FBRyxXQUFXLFNBQVM7a0RBRzFEbkcsZUFBZXlLLElBQUksR0FBRyxJQUNuQixXQUNBcEssYUFDR2QsV0FBV3FULElBQUksQ0FBQzFNLENBQUFBLElBQUtBLEVBQUVDLEVBQUUsR0FBRyxXQUFXLFlBQVksWUFDbkQ1RyxXQUFXcVQsSUFBSSxDQUFDMU0sQ0FBQUEsSUFBS0EsRUFBRUMsRUFBRSxHQUFHLFdBQVcsU0FBUzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O1lBUTlEeEYsc0NBQ0MsOERBQUNrUDtnQkFBSUMsV0FBVTtnQkFBZ0JHLFNBQVNqRzswQkFDdEMsNEVBQUM2RjtvQkFBSUMsV0FBVTtvQkFBdUJHLFNBQVMsQ0FBQzFJLElBQU1BLEVBQUU4SSxlQUFlOztzQ0FDckUsOERBQUNSOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0s7OENBQUc7Ozs7Ozs4Q0FDSiw4REFBQ0g7b0NBQ0NDLFNBQVNqRztvQ0FDVDhGLFdBQVU7b0NBQ1Y2QyxVQUFVNVI7OENBRVYsNEVBQUMvRCw4R0FBQ0E7d0NBQUM4UyxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7OztzQ0FHakIsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0k7OENBQ0VuUCxhQUNHLGtCQUNBOzs7Ozs7Z0NBR0xBLDRCQUNDLDhEQUFDOE87b0NBQUlDLFdBQVU7OENBQ2IsNEVBQUNEO3dDQUFJQyxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7OztzQ0FJckIsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0U7b0NBQ0NDLFNBQVNqRztvQ0FDVDhGLFdBQVU7b0NBQ1Y2QyxVQUFVNVI7OENBQ1g7Ozs7Ozs4Q0FHRCw4REFBQ2lQO29DQUNDQyxTQUFTckc7b0NBQ1RrRyxXQUFVO29DQUNWNkMsVUFBVTVSOzhDQUVUQSxhQUFhLFlBQVk7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQVFwQyw4REFBQ3BELDREQUFtQkE7Z0JBQ2xCa1YsUUFBUTVSO2dCQUNSaEMsU0FBUyxJQUFNaUMsd0JBQXdCO2dCQUN2QzRSLGtCQUFrQnhEOzs7Ozs7Ozs7Ozs7QUFJMUI7R0F0Z0VNdlE7S0FBQUE7QUF3Z0VOLCtEQUFlQSxtQkFBbUJBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vYXBwL3dvcmtiZW5jaC9jb21wb25lbnRzL0NvdXJzZUxpc3RFZGl0TW9kYWwudHN4PzJkOGYiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgUmVhY3QsIHsgdXNlU3RhdGUsIHVzZUVmZmVjdCwgdXNlQ2FsbGJhY2sgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBYLCBTZXR0aW5ncywgUGx1cywgQm9va09wZW4sIEZpbGVUZXh0IH0gZnJvbSAnbHVjaWRlLXJlYWN0JztcbmltcG9ydCB7IGNvdXJzZU1hbmFnZW1lbnRBcGkgfSBmcm9tICdAL2xpYi9hcGkvY291cnNlLW1hbmFnZW1lbnQnO1xuaW1wb3J0IHsgY291cnNlQXBpIH0gZnJvbSAnQC9saWIvYXBpL2NvdXJzZSc7XG5pbXBvcnQgeyB1cGxvYWRBcGkgfSBmcm9tICdAL2xpYi9hcGkvdXBsb2FkJztcbmltcG9ydCB7IHdvcmtzQXBpIH0gZnJvbSAnQC9saWIvYXBpL3dvcmtzJztcbmltcG9ydCB7IFNlbGVjdCB9IGZyb20gJ2FudGQnO1xuaW1wb3J0IHsgR2V0Tm90aWZpY2F0aW9uIH0gZnJvbSAnbG9naWMtY29tbW9uL2Rpc3QvY29tcG9uZW50cy9Ob3RpZmljYXRpb24nO1xuaW1wb3J0IFRlbXBsYXRlUGlja2VyTW9kYWwgZnJvbSAnLi9UZW1wbGF0ZVBpY2tlck1vZGFsJztcbmltcG9ydCAnLi9Db3Vyc2VMaXN0RWRpdE1vZGFsLmNzcyc7XG5pbXBvcnQgJy4vTmV3UHVibGlzaFRhc2tNb2RhbC5jc3MnO1xuXG5pbnRlcmZhY2UgQ291cnNlSXRlbSB7XG4gIGlkOiBudW1iZXI7XG4gIHNlcmllc0lkOiBudW1iZXI7XG4gIHRpdGxlOiBzdHJpbmc7XG4gIGRlc2NyaXB0aW9uOiBzdHJpbmc7XG4gIGNvdmVySW1hZ2U6IHN0cmluZztcbiAgb3JkZXJJbmRleDogbnVtYmVyO1xuICBzdGF0dXM6IG51bWJlcjtcbiAgc3RhdHVzTGFiZWw6IHN0cmluZztcbiAgaGFzVmlkZW86IG51bWJlcjtcbiAgaGFzRG9jdW1lbnQ6IG51bWJlcjtcbiAgaGFzQXVkaW86IG51bWJlcjtcbiAgdmlkZW9EdXJhdGlvbjogbnVtYmVyO1xuICB2aWRlb0R1cmF0aW9uTGFiZWw6IHN0cmluZztcbiAgdmlkZW9OYW1lOiBzdHJpbmc7XG4gIGZpcnN0VGVhY2hpbmdUaXRsZTogc3RyaW5nO1xuICByZXNvdXJjZXNDb3VudDogbnVtYmVyO1xuICBjcmVhdGVkQXQ6IHN0cmluZztcbiAgdXBkYXRlZEF0OiBzdHJpbmc7XG4gIC8vIOaWsOWinuWtl+auteeUqOS6juWtmOWCqOWujOaVtOeahOivvueoi+ivpuaDhVxuICBjb250ZW50Q29uZmlnPzogYW55O1xuICB0ZWFjaGluZ0luZm8/OiBhbnlbXTtcbiAgYWRkaXRpb25hbFJlc291cmNlcz86IEFycmF5PHtcbiAgICB0aXRsZTogc3RyaW5nO1xuICAgIHVybDogc3RyaW5nO1xuICAgIGRlc2NyaXB0aW9uOiBzdHJpbmc7XG4gIH0+O1xufVxuXG5pbnRlcmZhY2UgQ291cnNlVGFnIHtcbiAgaWQ6IG51bWJlcjtcbiAgbmFtZTogc3RyaW5nO1xuICBjb2xvcjogc3RyaW5nO1xuICBjYXRlZ29yeTogbnVtYmVyO1xuICBkZXNjcmlwdGlvbjogc3RyaW5nO1xuICBzdGF0dXM6IG51bWJlcjtcbn1cblxuaW50ZXJmYWNlIEFwaVJlc3BvbnNlIHtcbiAgY29kZTogbnVtYmVyO1xuICBtZXNzYWdlOiBzdHJpbmc7XG4gIGRhdGE6IHtcbiAgICBsaXN0OiBDb3Vyc2VJdGVtW107XG4gICAgcGFnaW5hdGlvbjoge1xuICAgICAgcGFnZTogbnVtYmVyO1xuICAgICAgcGFnZVNpemU6IG51bWJlcjtcbiAgICAgIHRvdGFsOiBudW1iZXI7XG4gICAgICB0b3RhbFBhZ2VzOiBudW1iZXI7XG4gICAgICBoYXNOZXh0OiBib29sZWFuO1xuICAgICAgaGFzUHJldjogYm9vbGVhbjtcbiAgICB9O1xuICB9O1xufVxuXG5pbnRlcmZhY2UgQ291cnNlTGlzdEVkaXRNb2RhbFByb3BzIHtcbiAgaXNWaXNpYmxlOiBib29sZWFuO1xuICBvbkNsb3NlOiAoKSA9PiB2b2lkO1xuICBvblNhdmU6IChkYXRhOiBhbnkpID0+IHZvaWQ7XG4gIHNlcmllc1RpdGxlOiBzdHJpbmc7XG4gIHNlcmllc0NvdmVySW1hZ2U/OiBzdHJpbmc7XG4gIHNlcmllc0lkOiBudW1iZXI7XG59XG5cbi8vIOiOt+WPluezu+WIl+ivvueoi+ivpuaDhVxuY29uc3QgZmV0Y2hTZXJpZXNEZXRhaWwgPSBhc3luYyAoc2VyaWVzSWQ6IG51bWJlcik6IFByb21pc2U8YW55PiA9PiB7XG4gIGNvbnNvbGUubG9nKCfwn5SNIOiOt+WPluezu+WIl+ivvueoi+ivpuaDhe+8jHNlcmllc0lkOicsIHNlcmllc0lkKTtcbiAgY29uc29sZS5sb2coJ/CflJcg6LCD55SoQVBJOiBHRVQgL2FwaS92MS9jb3Vyc2UtbWFuYWdlbWVudC9zZXJpZXMve3Nlcmllc0lkfScpO1xuXG4gIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgY291cnNlQXBpLmdldFNlcmllc0RldGFpbChzZXJpZXNJZCk7XG4gIGNvbnNvbGUubG9nKCfwn5OhIOezu+WIl+ivpuaDhUFQSeWTjeW6lDonLCByZXNwb25zZSk7XG4gIHJldHVybiByZXNwb25zZS5kYXRhO1xufTtcblxuLy8gQVBJ6LCD55So5Ye95pWwIC0g6I635Y+W57O75YiX5LiL55qE5omA5pyJ6K++56iL77yI5YyF5ZCr5pyq5Y+R5biD5ZKM5bey5Y+R5biD77yJXG5jb25zdCBmZXRjaENvdXJzZUxpc3QgPSBhc3luYyAoc2VyaWVzSWQ6IG51bWJlcik6IFByb21pc2U8QXBpUmVzcG9uc2U+ID0+IHtcbiAgY29uc29sZS5sb2coJ/CflI0g6I635Y+W57O75YiX6K++56iL5YiX6KGo77yMc2VyaWVzSWQ6Jywgc2VyaWVzSWQpO1xuICBjb25zb2xlLmxvZygn8J+UlyDosIPnlKhBUEk6IEdFVCAvYXBpL3YxL2NvdXJzZS1tYW5hZ2VtZW50L3Nlcmllcy97c2VyaWVzSWR9L2NvdXJzZXMnKTtcblxuICAvLyDkvb/nlKjor77nqIvnrqHnkIZBUEnojrflj5bmiYDmnInnirbmgIHnmoTor77nqItcbiAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBjb3Vyc2VBcGkuZ2V0TWFuYWdlbWVudFNlcmllc0NvdXJzZXMoc2VyaWVzSWQsIHtcbiAgICBwYWdlOiAxLFxuICAgIHBhZ2VTaXplOiAxMDAsXG4gICAgLy8g5LiN5Lygc3RhdHVz5Y+C5pWw77yM6I635Y+W5omA5pyJ54q25oCB55qE6K++56iL77yIc3RhdHVzPTDmnKrlj5HluIPvvIxzdGF0dXM9MeW3suWPkeW4g++8iVxuICB9KTtcblxuICBjb25zb2xlLmxvZygn8J+ToSBBUEnlk43lupQ6JywgcmVzcG9uc2UpO1xuICByZXR1cm4gcmVzcG9uc2UuZGF0YTtcbn07XG5cbi8vIOiOt+WPluivvueoi+ivpuaDhVxuY29uc3QgZmV0Y2hDb3Vyc2VEZXRhaWwgPSBhc3luYyAoc2VyaWVzSWQ6IG51bWJlciwgY291cnNlSWQ6IG51bWJlcikgPT4ge1xuICByZXR1cm4gYXdhaXQgY291cnNlQXBpLmdldENvdXJzZU1hcmtldHBsYWNlRGV0YWlsKHNlcmllc0lkLCBjb3Vyc2VJZCk7XG59O1xuXG4vLyDojrflj5bor77nqIvmoIfnrb5cbmNvbnN0IGZldGNoQ291cnNlVGFncyA9IGFzeW5jICgpOiBQcm9taXNlPGFueT4gPT4ge1xuICB0cnkge1xuICAgIGNvbnNvbGUubG9nKCfwn5SNIOW8gOWni+iwg+eUqCBjb3Vyc2VBcGkuZ2V0Q291cnNlVGFncycpO1xuICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IGNvdXJzZUFwaS5nZXRDb3Vyc2VUYWdzKHtcbiAgICAgIHBhZ2U6IDEsXG4gICAgICBwYWdlU2l6ZTogMTAwLFxuICAgICAgc3RhdHVzOiAxIC8vIOWPquiOt+WPluWQr+eUqOeahOagh+etvlxuICAgIH0pO1xuICAgIGNvbnNvbGUubG9nKCfwn5SNIGNvdXJzZUFwaS5nZXRDb3Vyc2VUYWdzIOi/lOWbnue7k+aenDonLCByZXN1bHQpO1xuICAgIHJldHVybiByZXN1bHQ7XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcign8J+UjSBjb3Vyc2VBcGkuZ2V0Q291cnNlVGFncyDosIPnlKjlpLHotKU6JywgZXJyb3IpO1xuICAgIHRocm93IGVycm9yO1xuICB9XG59O1xuXG5jb25zdCBDb3Vyc2VMaXN0RWRpdE1vZGFsOiBSZWFjdC5GQzxDb3Vyc2VMaXN0RWRpdE1vZGFsUHJvcHM+ID0gKHtcbiAgaXNWaXNpYmxlLFxuICBvbkNsb3NlLFxuICBvblNhdmUsXG4gIHNlcmllc1RpdGxlLFxuICBzZXJpZXNDb3ZlckltYWdlLFxuICBzZXJpZXNJZCA9IDEyMyAvLyDpu5jorqTlgLzvvIzlrp7pmYXkvb/nlKjml7blupTor6XkvKDlhaXnnJ/lrp7nmoRzZXJpZXNJZFxufSkgPT4ge1xuICBjb25zdCBub3RpZmljYXRpb24gPSBHZXROb3RpZmljYXRpb24oKTtcblxuICBjb25zdCBbY291cnNlTGlzdCwgc2V0Q291cnNlTGlzdF0gPSB1c2VTdGF0ZTxDb3Vyc2VJdGVtW10+KFtdKTtcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbcmlnaHRQYW5lbFR5cGUsIHNldFJpZ2h0UGFuZWxUeXBlXSA9IHVzZVN0YXRlPCdub25lJyB8ICdzZXR0aW5ncycgfCAnY291cnNlJz4oJ25vbmUnKTtcbiAgY29uc3QgW3NlbGVjdGVkQ291cnNlSWQsIHNldFNlbGVjdGVkQ291cnNlSWRdID0gdXNlU3RhdGU8bnVtYmVyIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFt1cGxvYWRpbmdGaWxlcywgc2V0VXBsb2FkaW5nRmlsZXNdID0gdXNlU3RhdGU8U2V0PHN0cmluZz4+KG5ldyBTZXQoKSk7XG4gIGNvbnN0IFtpc1B1Ymxpc2hpbmcsIHNldElzUHVibGlzaGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtpc0NyZWF0aW5nLCBzZXRJc0NyZWF0aW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2lzUHVibGlzaGluZ1Nlcmllcywgc2V0SXNQdWJsaXNoaW5nU2VyaWVzXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW3Nlcmllc1N0YXR1cywgc2V0U2VyaWVzU3RhdHVzXSA9IHVzZVN0YXRlPG51bWJlcj4oMCk7IC8vIDA96I2J56i/77yMMT3lt7Llj5HluIPvvIwyPeW3suW9kuaho1xuXG4gIC8vIOWIoOmZpOehruiupOW8ueeql+eKtuaAgVxuICBjb25zdCBbZGVsZXRlQ29uZmlybVZpc2libGUsIHNldERlbGV0ZUNvbmZpcm1WaXNpYmxlXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2NvdXJzZVRvRGVsZXRlLCBzZXRDb3Vyc2VUb0RlbGV0ZV0gPSB1c2VTdGF0ZTxudW1iZXIgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW2lzRGVsZXRpbmcsIHNldElzRGVsZXRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuXG4gIC8vIOaooeadv+mAieaLqeW8ueeql+eKtuaAgVxuICBjb25zdCBbaXNUZW1wbGF0ZVBpY2tlck9wZW4sIHNldElzVGVtcGxhdGVQaWNrZXJPcGVuXSA9IHVzZVN0YXRlKGZhbHNlKTtcblxuICBjb25zdCBbZWRpdGluZ1RpdGxlLCBzZXRFZGl0aW5nVGl0bGVdID0gdXNlU3RhdGUoc2VyaWVzVGl0bGUpO1xuICBjb25zdCBbY291cnNlR29hbHMsIHNldENvdXJzZUdvYWxzXSA9IHVzZVN0YXRlKCcnKTtcbiAgY29uc3QgW2NvdXJzZU9iamVjdGl2ZXMsIHNldENvdXJzZU9iamVjdGl2ZXNdID0gdXNlU3RhdGUoJycpO1xuICBjb25zdCBbcHJvamVjdE1lbWJlcnMsIHNldFByb2plY3RNZW1iZXJzXSA9IHVzZVN0YXRlKCcnKTtcblxuICAvLyDkvZzlk4Hnm7jlhbPnirbmgIFcbiAgY29uc3QgW3dvcmtzLCBzZXRXb3Jrc10gPSB1c2VTdGF0ZTxhbnlbXT4oW10pO1xuICBjb25zdCBbbG9hZGluZ1dvcmtzLCBzZXRMb2FkaW5nV29ya3NdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbc2VsZWN0ZWRXb3JrSWRzLCBzZXRTZWxlY3RlZFdvcmtJZHNdID0gdXNlU3RhdGU8bnVtYmVyW10+KFtdKTtcblxuICAvLyDor77nqIvmoIfnrb7nm7jlhbPnirbmgIFcbiAgY29uc3QgW2NvdXJzZVRhZ3MsIHNldENvdXJzZVRhZ3NdID0gdXNlU3RhdGU8Q291cnNlVGFnW10+KFtdKTtcbiAgY29uc3QgW3NlbGVjdGVkVGFncywgc2V0U2VsZWN0ZWRUYWdzXSA9IHVzZVN0YXRlPG51bWJlcltdPihbXSk7XG4gIGNvbnN0IFt0YWdzTG9hZGluZywgc2V0VGFnc0xvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuXG4gIC8vIOivvueoi+ivpue7hue8lui+keeKtuaAgVxuICBjb25zdCBbY291cnNlRGV0YWlsLCBzZXRDb3Vyc2VEZXRhaWxdID0gdXNlU3RhdGUoe1xuICAgIHRpdGxlOiAnJyxcbiAgICBkZXNjcmlwdGlvbjogJycsXG4gICAgY292ZXJJbWFnZTogJycsXG4gICAgdmlkZW9Vcmw6ICcnLFxuICAgIHZpZGVvTmFtZTogJycsXG4gICAgaXNWaWRlb0VuYWJsZWQ6IGZhbHNlLFxuICAgIGF0dGFjaG1lbnRVcmw6ICcnLFxuICAgIGF0dGFjaG1lbnROYW1lOiAnJyxcbiAgICBpc0F0dGFjaG1lbnRFbmFibGVkOiBmYWxzZSxcbiAgICB0ZWFjaGluZ01hdGVyaWFsczogW10gYXMgeyB0eXBlOiBzdHJpbmc7IG5hbWU6IHN0cmluZzsgdXJsOiBzdHJpbmcgfVtdLFxuICAgIC8vIOaUr+aMgXRlYWNoaW5nSW5mb+e7k+aehFxuICAgIHRlYWNoaW5nSW5mbzogW10gYXMgeyB0aXRsZTogc3RyaW5nOyBjb250ZW50OiBzdHJpbmcgfVtdLFxuICAgIC8vIOaUr+aMgWNvbnRlbnRDb25maWfnu5PmnoRcbiAgICBjb250ZW50Q29uZmlnOiB7XG4gICAgICBoYXNWaWRlbzogMCxcbiAgICAgIGhhc0RvY3VtZW50OiAwLFxuICAgICAgaGFzQXVkaW86IDAsXG4gICAgICB2aWRlbzogeyB1cmw6ICcnLCBuYW1lOiAnJyB9LFxuICAgICAgZG9jdW1lbnQ6IHsgdXJsOiAnJywgbmFtZTogJycgfSxcbiAgICAgIGF1ZGlvOiB7IHVybDogJycsIG5hbWU6ICcnIH1cbiAgICB9LFxuICAgIGNvdXJzZUNvbnRlbnQ6IHtcbiAgICAgIHRvcGljOiAnJyxcbiAgICAgIGNvbnRlbnQ6ICcnXG4gICAgfSxcbiAgICBpc09uZUtleU9wZW46IGZhbHNlLFxuICAgIGlzRGlzdHJpYnV0aW9uRW5hYmxlZDogZmFsc2UsXG4gICAgZGlzdHJpYnV0aW9uUmV3YXJkOiAnJyxcbiAgICBzZWxlY3RlZFRlbXBsYXRlOiAnJyxcbiAgICBpc0Rpc3RyaWJ1dGlvbldhdGVyOiBmYWxzZSxcbiAgICByZXF1aXJlZEVuZXJneTogJycsXG4gICAgZW5lcmd5QW1vdW50OiAnJyxcbiAgICBpc0Rpc3RyaWJ1dGlvbkxpbWl0OiBmYWxzZSxcbiAgICBkaXN0cmlidXRpb25Db25kaXRpb25zOiB7XG4gICAgICBpbnZpdGVDb3VudDogJycsXG4gICAgICB0YXNrQ291bnQ6ICcnLFxuICAgICAgZXhwZXJpZW5jZTogJydcbiAgICB9LFxuICAgIGlzRGlzdHJpYnV0aW9uVGltZTogZmFsc2UsXG4gICAgZGlzdHJpYnV0aW9uVGltZUNvbmRpdGlvbnM6IHtcbiAgICAgIHN0YXJ0VGltZTogJycsXG4gICAgICBlbmRUaW1lOiAnJ1xuICAgIH0sXG4gICAgZGlzdHJpYnV0aW9uTWF0ZXJpYWxzOiBbXSBhcyB7IHR5cGU6IHN0cmluZzsgbmFtZTogc3RyaW5nIH1bXSxcbiAgICAvLyDku7vliqHphY3nva7nm7jlhbPnirbmgIFcbiAgICB0YXNrQ29uZmlnOiB7XG4gICAgICB0YXNrTmFtZTogJycsXG4gICAgICB0YXNrRHVyYXRpb246ICcnLFxuICAgICAgdGFza0Rlc2NyaXB0aW9uOiAnJyxcbiAgICAgIHNlbGZBc3Nlc3NtZW50SXRlbXM6IFsnJ10sXG4gICAgICByZWZlcmVuY2VXb3JrczogW10gYXMgeyB0eXBlOiBzdHJpbmc7IG5hbWU6IHN0cmluZyB9W10sXG4gICAgICByZWZlcmVuY2VSZXNvdXJjZXM6IFtdIGFzIHsgdHlwZTogc3RyaW5nOyBuYW1lOiBzdHJpbmc7IHVybD86IHN0cmluZyB9W11cbiAgICB9XG4gIH0pO1xuXG4gIC8vIOiOt+WPluivvueoi+WIl+ihqOaVsOaNrlxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChpc1Zpc2libGUgJiYgc2VyaWVzSWQpIHtcbiAgICAgIC8vIOajgOafpeeUqOaIt+eZu+W9leeKtuaAgVxuICAgICAgY29uc3QgdG9rZW4gPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgndG9rZW4nKTtcbiAgICAgIGNvbnNvbGUubG9nKCfwn5SQIOajgOafpeeZu+W9leeKtuaAge+8jHRva2Vu5a2Y5ZyoOicsICEhdG9rZW4pO1xuICAgICAgY29uc29sZS5sb2coJ/CflI0gc2VyaWVzSWQ6Jywgc2VyaWVzSWQpO1xuXG4gICAgICBpZiAoIXRva2VuKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDnlKjmiLfmnKrnmbvlvZXvvIzml6Dms5Xojrflj5bor77nqIvliJfooagnKTtcbiAgICAgICAgLy8g6K6+572u56m65YiX6KGo77yM5pi+56S656m654q25oCBXG4gICAgICAgIHNldENvdXJzZUxpc3QoW10pO1xuICAgICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuXG4gICAgICBsb2FkQ291cnNlTGlzdCgpO1xuICAgICAgbG9hZENvdXJzZVRhZ3MoKTtcbiAgICAgIGxvYWRTZXJpZXNEZXRhaWwoKTtcbiAgICAgIGxvYWRVc2VyV29ya3MoKTtcbiAgICB9XG4gIH0sIFtpc1Zpc2libGUsIHNlcmllc0lkXSk7XG5cbiAgY29uc3QgbG9hZENvdXJzZUxpc3QgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIHNldExvYWRpbmcodHJ1ZSk7XG4gICAgICBjb25zb2xlLmxvZygn8J+UjSDlvIDlp4vliqDovb3or77nqIvliJfooajvvIxzZXJpZXNJZDonLCBzZXJpZXNJZCk7XG5cbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2hDb3Vyc2VMaXN0KHNlcmllc0lkKTtcbiAgICAgIGNvbnNvbGUubG9nKCfwn5OhIEFQSeWTjeW6lDonLCByZXNwb25zZSk7XG5cbiAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDApIHtcbiAgICAgICAgY29uc29sZS5sb2coJ+KchSDor77nqIvliJfooajmlbDmja46JywgcmVzcG9uc2UuZGF0YSk7XG4gICAgICAgIGNvbnN0IGNvdXJzZXMgPSByZXNwb25zZS5kYXRhLmxpc3QgfHwgW107XG4gICAgICAgIGNvbnNvbGUubG9nKCfinIUg6Kej5p6Q55qE6K++56iL5pWw57uEOicsIGNvdXJzZXMpO1xuICAgICAgICBjb25zb2xlLmxvZygn8J+TiiDor77nqItJROivpuaDhTonLCBjb3Vyc2VzLm1hcCgoYzogYW55KSA9PiAoe1xuICAgICAgICAgIGlkOiBjLmlkLFxuICAgICAgICAgIHR5cGU6IHR5cGVvZiBjLmlkLFxuICAgICAgICAgIHRpdGxlOiBjLnRpdGxlLFxuICAgICAgICAgIHN0YXR1czogYy5zdGF0dXNcbiAgICAgICAgfSkpKTtcbiAgICAgICAgY29uc29sZS5sb2coJ/Cfk4og6K++56iL54q25oCB57uf6K6hOicsIHtcbiAgICAgICAgICB0b3RhbDogY291cnNlcy5sZW5ndGgsXG4gICAgICAgICAgZHJhZnQ6IGNvdXJzZXMuZmlsdGVyKChjOiBhbnkpID0+IGMuc3RhdHVzID09PSAwKS5sZW5ndGgsXG4gICAgICAgICAgcHVibGlzaGVkOiBjb3Vyc2VzLmZpbHRlcigoYzogYW55KSA9PiBjLnN0YXR1cyA9PT0gMSkubGVuZ3RoXG4gICAgICAgIH0pO1xuICAgICAgICBzZXRDb3Vyc2VMaXN0KGNvdXJzZXMpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgY29uc29sZS5lcnJvcign4p2MIEFQSei/lOWbnumUmeivrzonLCByZXNwb25zZSk7XG4gICAgICAgIHNldENvdXJzZUxpc3QoW10pO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDliqDovb3or77nqIvliJfooajlpLHotKU6JywgZXJyb3IpO1xuXG4gICAgICAvLyDmo4Dmn6XmmK/lkKbmmK/orqTor4HplJnor69cbiAgICAgIGlmIChlcnJvci5yZXNwb25zZT8uc3RhdHVzID09PSA0MDEpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcign8J+UkCDorqTor4HlpLHotKXvvIznlKjmiLfmnKrnmbvlvZXmiJZ0b2tlbuW3sui/h+acnycpO1xuICAgICAgfSBlbHNlIGlmIChlcnJvci5yZXNwb25zZT8uc3RhdHVzID09PSA0MDMpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcign8J+aqyDmnYPpmZDkuI3otrPvvIzml6Dms5Xorr/pl67or6Xns7vliJfor77nqIsnKTtcbiAgICAgIH0gZWxzZSBpZiAoZXJyb3IucmVzcG9uc2U/LnN0YXR1cyA9PT0gNDA0KSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ/Cfk60g57O75YiX6K++56iL5LiN5a2Y5Zyo77yMc2VyaWVzSWQ6Jywgc2VyaWVzSWQpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgY29uc29sZS5lcnJvcign8J+UpyDlhbbku5bplJnor686JywgZXJyb3IubWVzc2FnZSk7XG4gICAgICB9XG5cbiAgICAgIHNldENvdXJzZUxpc3QoW10pO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgLy8g5Yqg6L2957O75YiX6K++56iL6K+m5oOFXG4gIGNvbnN0IGxvYWRTZXJpZXNEZXRhaWwgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnNvbGUubG9nKCfwn5SNIOW8gOWni+WKoOi9veezu+WIl+ivvueoi+ivpuaDhe+8jHNlcmllc0lkOicsIHNlcmllc0lkKTtcblxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaFNlcmllc0RldGFpbChzZXJpZXNJZCk7XG4gICAgICBjb25zb2xlLmxvZygn8J+ToSDns7vliJfor6bmg4Xlk43lupQ6JywgcmVzcG9uc2UpO1xuXG4gICAgICBpZiAocmVzcG9uc2UuY29kZSA9PT0gMjAwICYmIHJlc3BvbnNlLmRhdGEpIHtcbiAgICAgICAgY29uc3Qgc2VyaWVzRGF0YSA9IHJlc3BvbnNlLmRhdGE7XG4gICAgICAgIGNvbnNvbGUubG9nKCfinIUg57O75YiX6K++56iL6K+m5oOFOicsIHNlcmllc0RhdGEpO1xuICAgICAgICBzZXRTZXJpZXNTdGF0dXMoc2VyaWVzRGF0YS5zdGF0dXMgfHwgMCk7XG4gICAgICAgIGNvbnNvbGUubG9nKCfwn5OKIOezu+WIl+ivvueoi+eKtuaAgTonLCBzZXJpZXNEYXRhLnN0YXR1cywgJygwPeiNieeov++8jDE95bey5Y+R5biD77yMMj3lt7LlvZLmoaMpJyk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBjb25zb2xlLmVycm9yKCfinYwg6I635Y+W57O75YiX6K+m5oOF5aSx6LSlOicsIHJlc3BvbnNlLm1lc3NhZ2UpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDliqDovb3ns7vliJfor6bmg4XlvILluLg6JywgZXJyb3IpO1xuICAgIH1cbiAgfTtcblxuICAvLyDliqDovb3nlKjmiLfkvZzlk4HmlbDmja5cbiAgY29uc3QgbG9hZFVzZXJXb3JrcyA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgc2V0TG9hZGluZ1dvcmtzKHRydWUpO1xuXG4gICAgICAvLyDku45sb2NhbFN0b3JhZ2Xojrflj5bnlKjmiLdJRFxuICAgICAgY29uc3QgdXNlcklkID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3VzZXJJZCcpIHx8ICcyODk2JzsgLy8g6buY6K6k5L2/55SoMjg5NlxuXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHdvcmtzQXBpLmdldFRlYWNoZXJXb3JrcyhOdW1iZXIodXNlcklkKSwgMSwgMTAwMCk7XG5cbiAgICAgIC8vIOajgOafpeWkmuenjeWPr+iDveeahOaVsOaNrue7k+aehFxuICAgICAgbGV0IHdvcmtzTGlzdCA9IFtdO1xuXG4gICAgICBpZiAocmVzcG9uc2UuZGF0YT8uY29kZSA9PT0gMjAwKSB7XG4gICAgICAgIC8vIOaDheWGtTE6IHJlc3BvbnNlLmRhdGEuZGF0YS5saXN0XG4gICAgICAgIGlmIChyZXNwb25zZS5kYXRhPy5kYXRhPy5saXN0KSB7XG4gICAgICAgICAgd29ya3NMaXN0ID0gcmVzcG9uc2UuZGF0YS5kYXRhLmxpc3Q7XG4gICAgICAgIH1cbiAgICAgICAgLy8g5oOF5Ya1MjogcmVzcG9uc2UuZGF0YS5kYXRhICjnm7TmjqXmmK/mlbDnu4QpXG4gICAgICAgIGVsc2UgaWYgKEFycmF5LmlzQXJyYXkocmVzcG9uc2UuZGF0YT8uZGF0YSkpIHtcbiAgICAgICAgICB3b3Jrc0xpc3QgPSByZXNwb25zZS5kYXRhLmRhdGE7XG4gICAgICAgIH1cbiAgICAgICAgLy8g5oOF5Ya1MzogcmVzcG9uc2UuZGF0YSAo55u05o6l5piv5pWw57uEKVxuICAgICAgICBlbHNlIGlmIChBcnJheS5pc0FycmF5KHJlc3BvbnNlLmRhdGEpKSB7XG4gICAgICAgICAgd29ya3NMaXN0ID0gcmVzcG9uc2UuZGF0YTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgICAgLy8g5oOF5Ya1NDog55u05o6l5qOA5p+lcmVzcG9uc2XmmK/lkKbmmK/mlbDnu4RcbiAgICAgIGVsc2UgaWYgKEFycmF5LmlzQXJyYXkocmVzcG9uc2UpKSB7XG4gICAgICAgIHdvcmtzTGlzdCA9IHJlc3BvbnNlO1xuICAgICAgfVxuXG4gICAgICBzZXRXb3Jrcyh3b3Jrc0xpc3QpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfliqDovb3kvZzlk4HmlbDmja7lpLHotKU6JywgZXJyb3IpO1xuICAgICAgc2V0V29ya3MoW10pO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRMb2FkaW5nV29ya3MoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICAvLyDlpITnkIbkvZzlk4HpgInmi6lcbiAgY29uc3QgaGFuZGxlV29ya1NlbGVjdCA9ICh3b3JrSWQ6IG51bWJlcikgPT4ge1xuICAgIGlmIChzZWxlY3RlZFdvcmtJZHMuaW5jbHVkZXMod29ya0lkKSkge1xuICAgICAgLy8g5Y+W5raI6YCJ5LitXG4gICAgICBzZXRTZWxlY3RlZFdvcmtJZHMocHJldiA9PiBwcmV2LmZpbHRlcihpZCA9PiBpZCAhPT0gd29ya0lkKSk7XG4gICAgfSBlbHNlIHtcbiAgICAgIC8vIOmAieS4rVxuICAgICAgc2V0U2VsZWN0ZWRXb3JrSWRzKHByZXYgPT4gWy4uLnByZXYsIHdvcmtJZF0pO1xuICAgIH1cbiAgfTtcblxuICAvLyDpvKDmoIfmu5rova7kuovku7blpITnkIYgLSDlsIblnoLnm7Tmu5rova7ovazmjaLkuLrmsLTlubPmu5rliqhcbiAgY29uc3QgaGFuZGxlV2hlZWwgPSB1c2VDYWxsYmFjaygoZTogUmVhY3QuV2hlZWxFdmVudDxIVE1MRGl2RWxlbWVudD4pID0+IHtcbiAgICBjb25zdCBjb250YWluZXIgPSBlLmN1cnJlbnRUYXJnZXQ7XG4gICAgY29uc3QgeyBzY3JvbGxXaWR0aCwgY2xpZW50V2lkdGggfSA9IGNvbnRhaW5lcjtcblxuICAgIC8vIOajgOafpeaYr+WQpuWPr+S7pea7muWKqFxuICAgIGlmIChzY3JvbGxXaWR0aCA8PSBjbGllbnRXaWR0aCkge1xuICAgICAgcmV0dXJuOyAvLyDlhoXlrrnkuI3lpJ/plb/vvIzkuI3pnIDopoHmu5rliqhcbiAgICB9XG5cbiAgICAvLyDpmLvmraLpu5jorqTnmoTlnoLnm7Tmu5rliqjooYzkuLpcbiAgICBlLnByZXZlbnREZWZhdWx0KCk7XG5cbiAgICAvLyDlsIblnoLnm7Tmu5rova7ovazmjaLkuLrmsLTlubPmu5rliqhcbiAgICBjb250YWluZXIuc2Nyb2xsTGVmdCArPSBlLmRlbHRhWTtcbiAgfSwgW10pO1xuXG4gIC8vIOWKoOi9veivvueoi+agh+etvlxuICBjb25zdCBsb2FkQ291cnNlVGFncyA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgc2V0VGFnc0xvYWRpbmcodHJ1ZSk7XG4gICAgICBjb25zb2xlLmxvZygn8J+Pt++4jyDlvIDlp4vliqDovb3or77nqIvmoIfnrb4nKTtcblxuXG5cbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2hDb3Vyc2VUYWdzKCk7XG4gICAgICBjb25zb2xlLmxvZygn8J+ToSDmoIfnrb5BUEnlrozmlbTlk43lupQ6JywgcmVzcG9uc2UpO1xuXG4gICAgICAvLyDmo4Dmn6Xlk43lupTnu5PmnoRcbiAgICAgIGlmIChyZXNwb25zZSAmJiByZXNwb25zZS5kYXRhKSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCfwn5OKIOWTjeW6lOaVsOaNrjonLCByZXNwb25zZS5kYXRhKTtcblxuICAgICAgICBsZXQgdGFnczogQ291cnNlVGFnW10gPSBbXTtcblxuICAgICAgICAvLyDlpITnkIbmoIflh4bnmoRBUEnlk43lupTmoLzlvI8gKHJlc3BvbnNlLmRhdGEubGlzdCkgLSDmoLnmja7lrp7pmYVBUEnlk43lupRcbiAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEubGlzdCAmJiBBcnJheS5pc0FycmF5KHJlc3BvbnNlLmRhdGEubGlzdCkpIHtcbiAgICAgICAgICB0YWdzID0gcmVzcG9uc2UuZGF0YS5saXN0O1xuICAgICAgICAgIGNvbnNvbGUubG9nKCfinIUg5LuOIGRhdGEubGlzdCDop6PmnpDliLDmoIfnrb46JywgdGFncy5sZW5ndGgsICfkuKonKTtcbiAgICAgICAgfVxuICAgICAgICAvLyDlpITnkIbnm7TmjqXmlbDnu4TmoLzlvI8gKHJlc3BvbnNlLmRhdGEpXG4gICAgICAgIGVsc2UgaWYgKEFycmF5LmlzQXJyYXkocmVzcG9uc2UuZGF0YSkpIHtcbiAgICAgICAgICB0YWdzID0gcmVzcG9uc2UuZGF0YTtcbiAgICAgICAgICBjb25zb2xlLmxvZygn4pyFIOS7jiBkYXRhIOaVsOe7hOino+aekOWIsOagh+etvjonLCB0YWdzLmxlbmd0aCwgJ+S4qicpO1xuICAgICAgICB9XG4gICAgICAgIC8vIOWkhOeQhuW1jOWll+eahEFQSeWTjeW6lOagvOW8jyAocmVzcG9uc2UuZGF0YS5kYXRhLmxpc3QpIC0g5aSH55So5qC85byPXG4gICAgICAgIGVsc2UgaWYgKHJlc3BvbnNlLmRhdGEuZGF0YSAmJiByZXNwb25zZS5kYXRhLmRhdGEubGlzdCAmJiBBcnJheS5pc0FycmF5KHJlc3BvbnNlLmRhdGEuZGF0YS5saXN0KSkge1xuICAgICAgICAgIHRhZ3MgPSByZXNwb25zZS5kYXRhLmRhdGEubGlzdDtcbiAgICAgICAgICBjb25zb2xlLmxvZygn4pyFIOS7jiBkYXRhLmRhdGEubGlzdCDop6PmnpDliLDmoIfnrb46JywgdGFncy5sZW5ndGgsICfkuKonKTtcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIOmqjOivgeagh+etvuaVsOaNruagvOW8j1xuICAgICAgICBjb25zb2xlLmxvZygn8J+UjSDljp/lp4vmoIfnrb7mlbDmja46JywgdGFncyk7XG4gICAgICAgIGNvbnNvbGUubG9nKCfwn5SNIOagh+etvuaVsOaNruexu+Wei+ajgOafpTonKTtcbiAgICAgICAgdGFncy5mb3JFYWNoKCh0YWcsIGluZGV4KSA9PiB7XG4gICAgICAgICAgY29uc29sZS5sb2coYOagh+etviR7aW5kZXh9OmAsIHtcbiAgICAgICAgICAgIHRhZyxcbiAgICAgICAgICAgIGhhc1RhZzogISF0YWcsXG4gICAgICAgICAgICBpZFR5cGU6IHR5cGVvZiB0YWc/LmlkLFxuICAgICAgICAgICAgbmFtZVR5cGU6IHR5cGVvZiB0YWc/Lm5hbWUsXG4gICAgICAgICAgICBuYW1lVmFsdWU6IHRhZz8ubmFtZSxcbiAgICAgICAgICAgIG5hbWVOb3RFbXB0eTogdGFnPy5uYW1lPy50cmltKCkgIT09ICcnXG4gICAgICAgICAgfSk7XG4gICAgICAgIH0pO1xuXG4gICAgICAgIGNvbnN0IHZhbGlkVGFncyA9IHRhZ3MuZmlsdGVyKHRhZyA9PiB7XG4gICAgICAgICAgY29uc3QgaXNWYWxpZCA9IHRhZyAmJlxuICAgICAgICAgICAgdHlwZW9mIHRhZy5pZCA9PT0gJ251bWJlcicgJiZcbiAgICAgICAgICAgIHR5cGVvZiB0YWcubmFtZSA9PT0gJ3N0cmluZycgJiZcbiAgICAgICAgICAgIHRhZy5uYW1lLnRyaW0oKSAhPT0gJyc7XG5cbiAgICAgICAgICBpZiAoIWlzVmFsaWQpIHtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfinYwg5peg5pWI5qCH562+OicsIHRhZywge1xuICAgICAgICAgICAgICBoYXNUYWc6ICEhdGFnLFxuICAgICAgICAgICAgICBpZFR5cGU6IHR5cGVvZiB0YWc/LmlkLFxuICAgICAgICAgICAgICBuYW1lVHlwZTogdHlwZW9mIHRhZz8ubmFtZSxcbiAgICAgICAgICAgICAgbmFtZVZhbHVlOiB0YWc/Lm5hbWVcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgIH1cblxuICAgICAgICAgIHJldHVybiBpc1ZhbGlkO1xuICAgICAgICB9KTtcblxuICAgICAgICBjb25zb2xlLmxvZygn4pyFIOacieaViOagh+etvuaVsOmHjzonLCB2YWxpZFRhZ3MubGVuZ3RoKTtcbiAgICAgICAgY29uc29sZS5sb2coJ+KchSDmnInmlYjmoIfnrb7or6bmg4U6JywgdmFsaWRUYWdzKTtcblxuICAgICAgICBpZiAodmFsaWRUYWdzLmxlbmd0aCA+IDApIHtcbiAgICAgICAgICBzZXRDb3Vyc2VUYWdzKHZhbGlkVGFncyk7XG4gICAgICAgICAgY29uc29sZS5sb2coJ+KchSDmiJDlip/orr7nva7nnJ/lrp7moIfnrb7mlbDmja4nKTtcbiAgICAgICAgICByZXR1cm47XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgY29uc29sZS53YXJuKCfimqDvuI8g5rKh5pyJ5pyJ5pWI55qE5qCH562+5pWw5o2uJyk7XG4gICAgICAgIH1cbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGNvbnNvbGUud2Fybign4pqg77iPIEFQSeWTjeW6lOagvOW8j+S4jeato+ehrjonLCByZXNwb25zZSk7XG4gICAgICB9XG5cbiAgICAgIC8vIOWmguaenOayoeacieecn+WunuaVsOaNru+8jOiuvue9ruepuuaVsOe7hFxuICAgICAgY29uc29sZS5sb2coJ/Cfk60g5rKh5pyJ5qCH562+5pWw5o2u77yM6K6+572u56m65pWw57uEJyk7XG4gICAgICBzZXRDb3Vyc2VUYWdzKFtdKTtcblxuICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDliqDovb3or77nqIvmoIfnrb7lpLHotKU6JywgZXJyb3IpO1xuICAgICAgY29uc29sZS5lcnJvcign4p2MIOmUmeivr+ivpuaDhTonLCB7XG4gICAgICAgIG1lc3NhZ2U6IGVycm9yLm1lc3NhZ2UsXG4gICAgICAgIHN0YXR1czogZXJyb3IucmVzcG9uc2U/LnN0YXR1cyxcbiAgICAgICAgc3RhdHVzVGV4dDogZXJyb3IucmVzcG9uc2U/LnN0YXR1c1RleHQsXG4gICAgICAgIGRhdGE6IGVycm9yLnJlc3BvbnNlPy5kYXRhXG4gICAgICB9KTtcblxuICAgICAgLy8g5Y+R55Sf6ZSZ6K+v5pe26K6+572u56m65pWw57uEXG4gICAgICBzZXRDb3Vyc2VUYWdzKFtdKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0VGFnc0xvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICAvLyDmt7vliqDmlrDor77nqItcbiAgY29uc3QgYWRkTmV3Q291cnNlID0gKCkgPT4ge1xuICAgIGNvbnN0IG5ld0NvdXJzZTogQ291cnNlSXRlbSA9IHtcbiAgICAgIGlkOiBEYXRlLm5vdygpLFxuICAgICAgc2VyaWVzSWQ6IHNlcmllc0lkLFxuICAgICAgdGl0bGU6IGDnrKwke2NvdXJzZUxpc3QubGVuZ3RoICsgMX3or74gLSDmlrDor77ml7ZgLFxuICAgICAgZGVzY3JpcHRpb246ICcnLFxuICAgICAgY292ZXJJbWFnZTogJycsXG4gICAgICBvcmRlckluZGV4OiBjb3Vyc2VMaXN0Lmxlbmd0aCArIDEsXG4gICAgICBzdGF0dXM6IDAsXG4gICAgICBzdGF0dXNMYWJlbDogJ+iNieeovycsXG4gICAgICBoYXNWaWRlbzogMCxcbiAgICAgIGhhc0RvY3VtZW50OiAwLFxuICAgICAgaGFzQXVkaW86IDAsXG4gICAgICB2aWRlb0R1cmF0aW9uOiAwLFxuICAgICAgdmlkZW9EdXJhdGlvbkxhYmVsOiAnJyxcbiAgICAgIHZpZGVvTmFtZTogJycsXG4gICAgICBmaXJzdFRlYWNoaW5nVGl0bGU6ICcnLFxuICAgICAgcmVzb3VyY2VzQ291bnQ6IDAsXG4gICAgICBjcmVhdGVkQXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgIHVwZGF0ZWRBdDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gICAgfTtcbiAgICBzZXRDb3Vyc2VMaXN0KFsuLi5jb3Vyc2VMaXN0LCBuZXdDb3Vyc2VdKTtcbiAgICAvLyDoh6rliqjpgInkuK3mlrDmt7vliqDnmoTor77nqItcbiAgICBzaG93Q291cnNlUGFuZWwobmV3Q291cnNlLmlkKTtcbiAgfTtcblxuICAvLyDmmL7npLrliKDpmaTnoa7orqTlvLnnqpdcbiAgY29uc3Qgc2hvd0RlbGV0ZUNvbmZpcm0gPSAoaWQ6IG51bWJlcikgPT4ge1xuICAgIHNldENvdXJzZVRvRGVsZXRlKGlkKTtcbiAgICBzZXREZWxldGVDb25maXJtVmlzaWJsZSh0cnVlKTtcbiAgfTtcblxuICAvLyDnoa7orqTliKDpmaTor77nqItcbiAgY29uc3QgY29uZmlybURlbGV0ZUNvdXJzZSA9IGFzeW5jICgpID0+IHtcbiAgICBpZiAoIWNvdXJzZVRvRGVsZXRlKSByZXR1cm47XG5cbiAgICB0cnkge1xuICAgICAgc2V0SXNEZWxldGluZyh0cnVlKTtcblxuICAgICAgLy8g6LCD55So5Yig6ZmkQVBJXG4gICAgICBhd2FpdCBjb3Vyc2VNYW5hZ2VtZW50QXBpLmRlbGV0ZUNvdXJzZShjb3Vyc2VUb0RlbGV0ZSk7XG5cbiAgICAgIC8vIOS7juWIl+ihqOS4reenu+mZpOivvueoi1xuICAgICAgc2V0Q291cnNlTGlzdChjb3Vyc2VMaXN0LmZpbHRlcihjb3Vyc2UgPT4gY291cnNlLmlkICE9PSBjb3Vyc2VUb0RlbGV0ZSkpO1xuXG4gICAgICAvLyDlpoLmnpzliKDpmaTnmoTmmK/lvZPliY3pgInkuK3nmoTor77nqIvvvIzmuIXnqbrlj7PkvqfpnaLmnb9cbiAgICAgIGlmIChzZWxlY3RlZENvdXJzZUlkID09PSBjb3Vyc2VUb0RlbGV0ZSkge1xuICAgICAgICBzZXRSaWdodFBhbmVsVHlwZSgnbm9uZScpO1xuICAgICAgICBzZXRTZWxlY3RlZENvdXJzZUlkKG51bGwpO1xuICAgICAgfVxuXG4gICAgICAvLyDlhbPpl63noa7orqTlvLnnqpdcbiAgICAgIHNldERlbGV0ZUNvbmZpcm1WaXNpYmxlKGZhbHNlKTtcbiAgICAgIHNldENvdXJzZVRvRGVsZXRlKG51bGwpO1xuXG4gICAgICAvLyDmmL7npLrmiJDlip/mj5DnpLpcbiAgICAgIG5vdGlmaWNhdGlvbi5zdWNjZXNzKCfor77nqIvlt7LmiJDlip/liKDpmaQnKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcign5Yig6Zmk6K++56iL5aSx6LSlOicsIGVycm9yKTtcbiAgICAgIG5vdGlmaWNhdGlvbi5lcnJvcign5Yig6Zmk6K++56iL5aSx6LSl77yM6K+36YeN6K+VJyk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldElzRGVsZXRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICAvLyDlj5bmtojliKDpmaRcbiAgY29uc3QgY2FuY2VsRGVsZXRlID0gKCkgPT4ge1xuICAgIGlmIChpc0RlbGV0aW5nKSByZXR1cm47IC8vIOato+WcqOWIoOmZpOaXtuS4jeWFgeiuuOWPlua2iFxuICAgIHNldERlbGV0ZUNvbmZpcm1WaXNpYmxlKGZhbHNlKTtcbiAgICBzZXRDb3Vyc2VUb0RlbGV0ZShudWxsKTtcbiAgfTtcblxuICAvLyDmm7TmlrDor77nqIvmoIfpophcbiAgY29uc3QgdXBkYXRlQ291cnNlVGl0bGUgPSAoaWQ6IG51bWJlciwgbmV3VGl0bGU6IHN0cmluZykgPT4ge1xuICAgIHNldENvdXJzZUxpc3QoY291cnNlTGlzdC5tYXAoY291cnNlID0+XG4gICAgICBjb3Vyc2UuaWQgPT09IGlkID8geyAuLi5jb3Vyc2UsIHRpdGxlOiBuZXdUaXRsZSB9IDogY291cnNlXG4gICAgKSk7XG4gIH07XG5cbiAgLy8g5aSE55CG6K++56iL5bCB6Z2i5LiK5LygXG4gIGNvbnN0IGhhbmRsZUNvdmVyVXBsb2FkID0gYXN5bmMgKGV2ZW50OiBSZWFjdC5DaGFuZ2VFdmVudDxIVE1MSW5wdXRFbGVtZW50PikgPT4ge1xuICAgIGNvbnN0IGZpbGUgPSBldmVudC50YXJnZXQuZmlsZXM/LlswXTtcbiAgICBpZiAoZmlsZSkge1xuICAgICAgLy8g5qOA5p+l5paH5Lu257G75Z6LXG4gICAgICBjb25zdCBhbGxvd2VkVHlwZXMgPSBbJ2ltYWdlL2pwZWcnLCAnaW1hZ2UvanBnJywgJ2ltYWdlL3BuZycsICdpbWFnZS9naWYnXTtcbiAgICAgIGlmICghYWxsb3dlZFR5cGVzLmluY2x1ZGVzKGZpbGUudHlwZSkpIHtcbiAgICAgICAgbm90aWZpY2F0aW9uLmVycm9yKCfor7fpgInmi6kgSlBH44CBUE5HIOaIliBHSUYg5qC85byP55qE5Zu+54mH5paH5Lu2Jyk7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cblxuICAgICAgLy8g5qOA5p+l5paH5Lu25aSn5bCPICgxME1CKVxuICAgICAgaWYgKGZpbGUuc2l6ZSA+IDEwICogMTAyNCAqIDEwMjQpIHtcbiAgICAgICAgbm90aWZpY2F0aW9uLmVycm9yKCfmlofku7blpKflsI/kuI3og73otoXov4cgMTBNQicpO1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG5cbiAgICAgIHRyeSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCfwn5OkIOW8gOWni+S4iuS8oOivvueoi+WwgemdojonLCBmaWxlLm5hbWUpO1xuXG4gICAgICAgIC8vIOa3u+WKoOWIsOS4iuS8oOS4reeahOaWh+S7tuWIl+ihqFxuICAgICAgICBzZXRVcGxvYWRpbmdGaWxlcyhwcmV2ID0+IG5ldyBTZXQocHJldikuYWRkKCdjb3ZlcicpKTtcblxuICAgICAgICAvLyDlhYjmmL7npLrpooTop4jlm77niYdcbiAgICAgICAgY29uc3QgcHJldmlld1VybCA9IFVSTC5jcmVhdGVPYmplY3RVUkwoZmlsZSk7XG4gICAgICAgIHNldENvdXJzZURldGFpbChwcmV2ID0+ICh7IC4uLnByZXYsIGNvdmVySW1hZ2U6IHByZXZpZXdVcmwgfSkpO1xuXG4gICAgICAgIC8vIOS4iuS8oOWIsE9TU1xuICAgICAgICBjb25zdCBpbWFnZVVybCA9IGF3YWl0IHVwbG9hZEFwaS51cGxvYWRUb09zcyhmaWxlKTtcbiAgICAgICAgY29uc29sZS5sb2coJ+KchSDor77nqIvlsIHpnaLkuIrkvKDmiJDlip86JywgaW1hZ2VVcmwpO1xuXG4gICAgICAgIC8vIOabtOaWsOivvueoi+ivpuaDheS4reeahOWwgemdouS4uuecn+WunlVSTFxuICAgICAgICBzZXRDb3Vyc2VEZXRhaWwocHJldiA9PiAoeyAuLi5wcmV2LCBjb3ZlckltYWdlOiBpbWFnZVVybCB9KSk7XG5cbiAgICAgICAgLy8g5ZCM5pe25pu05paw6K++56iL5YiX6KGo5Lit55qE5bCB6Z2iXG4gICAgICAgIGlmIChzZWxlY3RlZENvdXJzZUlkKSB7XG4gICAgICAgICAgc2V0Q291cnNlTGlzdChwcmV2ID0+IHByZXYubWFwKGNvdXJzZSA9PlxuICAgICAgICAgICAgY291cnNlLmlkID09PSBzZWxlY3RlZENvdXJzZUlkID8geyAuLi5jb3Vyc2UsIGNvdmVySW1hZ2U6IGltYWdlVXJsIH0gOiBjb3Vyc2VcbiAgICAgICAgICApKTtcbiAgICAgICAgfVxuXG4gICAgICAgIGFsZXJ0KCfor77nqIvlsIHpnaLkuIrkvKDmiJDlip/vvIEnKTtcbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDor77nqIvlsIHpnaLkuIrkvKDlpLHotKU6JywgZXJyb3IpO1xuICAgICAgICBub3RpZmljYXRpb24uZXJyb3IoJ+ivvueoi+WwgemdouS4iuS8oOWksei0pe+8jOivt+mHjeivlScpO1xuXG4gICAgICAgIC8vIOS4iuS8oOWksei0peaXtua4hemZpOmihOiniOWbvueJh1xuICAgICAgICBzZXRDb3Vyc2VEZXRhaWwocHJldiA9PiAoeyAuLi5wcmV2LCBjb3ZlckltYWdlOiAnJyB9KSk7XG4gICAgICB9IGZpbmFsbHkge1xuICAgICAgICAvLyDku47kuIrkvKDkuK3nmoTmlofku7bliJfooajnp7vpmaRcbiAgICAgICAgc2V0VXBsb2FkaW5nRmlsZXMocHJldiA9PiB7XG4gICAgICAgICAgY29uc3QgbmV3U2V0ID0gbmV3IFNldChwcmV2KTtcbiAgICAgICAgICBuZXdTZXQuZGVsZXRlKCdjb3ZlcicpO1xuICAgICAgICAgIHJldHVybiBuZXdTZXQ7XG4gICAgICAgIH0pO1xuICAgICAgfVxuICAgIH1cbiAgfTtcblxuICAvLyDlpITnkIbop4bpopHkuIrkvKBcbiAgY29uc3QgaGFuZGxlVmlkZW9VcGxvYWQgPSBhc3luYyAoZXZlbnQ6IFJlYWN0LkNoYW5nZUV2ZW50PEhUTUxJbnB1dEVsZW1lbnQ+KSA9PiB7XG4gICAgY29uc3QgZmlsZSA9IGV2ZW50LnRhcmdldC5maWxlcz8uWzBdO1xuICAgIGlmIChmaWxlKSB7XG4gICAgICAvLyDmo4Dmn6Xmlofku7bnsbvlnotcbiAgICAgIGNvbnN0IGFsbG93ZWRUeXBlcyA9IFsndmlkZW8vbXA0JywgJ3ZpZGVvL2F2aScsICd2aWRlby9tb3YnLCAndmlkZW8vd212JywgJ3ZpZGVvL2ZsdiddO1xuICAgICAgaWYgKCFhbGxvd2VkVHlwZXMuaW5jbHVkZXMoZmlsZS50eXBlKSkge1xuICAgICAgICBhbGVydCgn6K+36YCJ5oupIE1QNOOAgUFWSeOAgU1PVuOAgVdNViDmiJYgRkxWIOagvOW8j+eahOinhumikeaWh+S7ticpO1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG5cbiAgICAgIC8vIOajgOafpeaWh+S7tuWkp+WwjyAoMTAwTUIpXG4gICAgICBpZiAoZmlsZS5zaXplID4gMTAwICogMTAyNCAqIDEwMjQpIHtcbiAgICAgICAgYWxlcnQoJ+inhumikeaWh+S7tuWkp+Wwj+S4jeiDvei2hei/hyAxMDBNQicpO1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG5cbiAgICAgIHRyeSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCfwn5OkIOW8gOWni+S4iuS8oOivvueoi+inhumikTonLCBmaWxlLm5hbWUpO1xuXG4gICAgICAgIC8vIOa3u+WKoOWIsOS4iuS8oOS4reeahOaWh+S7tuWIl+ihqFxuICAgICAgICBzZXRVcGxvYWRpbmdGaWxlcyhwcmV2ID0+IG5ldyBTZXQocHJldikuYWRkKCd2aWRlbycpKTtcblxuICAgICAgICAvLyDlhYjmmL7npLrpooTop4jop4bpopFcbiAgICAgICAgY29uc3QgcHJldmlld1VybCA9IFVSTC5jcmVhdGVPYmplY3RVUkwoZmlsZSk7XG4gICAgICAgIHNldENvdXJzZURldGFpbChwcmV2ID0+ICh7XG4gICAgICAgICAgLi4ucHJldixcbiAgICAgICAgICBjb250ZW50Q29uZmlnOiB7XG4gICAgICAgICAgICAuLi5wcmV2LmNvbnRlbnRDb25maWcsXG4gICAgICAgICAgICB2aWRlbzoge1xuICAgICAgICAgICAgICB1cmw6IHByZXZpZXdVcmwsXG4gICAgICAgICAgICAgIG5hbWU6IGZpbGUubmFtZVxuICAgICAgICAgICAgfVxuICAgICAgICAgIH1cbiAgICAgICAgfSkpO1xuXG4gICAgICAgIC8vIOS4iuS8oOWIsE9TU1xuICAgICAgICBjb25zdCB2aWRlb1VybCA9IGF3YWl0IHVwbG9hZEFwaS51cGxvYWRUb09zcyhmaWxlKTtcbiAgICAgICAgY29uc29sZS5sb2coJ+KchSDor77nqIvop4bpopHkuIrkvKDmiJDlip86JywgdmlkZW9VcmwpO1xuXG4gICAgICAgIC8vIOabtOaWsOivvueoi+ivpuaDheS4reeahOinhumikeS/oeaBr+S4uuecn+WunlVSTFxuICAgICAgICBzZXRDb3Vyc2VEZXRhaWwocHJldiA9PiAoe1xuICAgICAgICAgIC4uLnByZXYsXG4gICAgICAgICAgY29udGVudENvbmZpZzoge1xuICAgICAgICAgICAgLi4ucHJldi5jb250ZW50Q29uZmlnLFxuICAgICAgICAgICAgdmlkZW86IHtcbiAgICAgICAgICAgICAgdXJsOiB2aWRlb1VybCxcbiAgICAgICAgICAgICAgbmFtZTogZmlsZS5uYW1lXG4gICAgICAgICAgICB9XG4gICAgICAgICAgfVxuICAgICAgICB9KSk7XG5cbiAgICAgICAgLy8g5ZCM5pe25pu05paw6K++56iL5YiX6KGo5Lit55qE6KeG6aKR5L+h5oGvXG4gICAgICAgIGlmIChzZWxlY3RlZENvdXJzZUlkKSB7XG4gICAgICAgICAgc2V0Q291cnNlTGlzdChwcmV2ID0+IHByZXYubWFwKGNvdXJzZSA9PlxuICAgICAgICAgICAgY291cnNlLmlkID09PSBzZWxlY3RlZENvdXJzZUlkID8ge1xuICAgICAgICAgICAgICAuLi5jb3Vyc2UsXG4gICAgICAgICAgICAgIGNvbnRlbnRDb25maWc6IHtcbiAgICAgICAgICAgICAgICAuLi5jb3Vyc2UuY29udGVudENvbmZpZyxcbiAgICAgICAgICAgICAgICB2aWRlbzoge1xuICAgICAgICAgICAgICAgICAgdXJsOiB2aWRlb1VybCxcbiAgICAgICAgICAgICAgICAgIG5hbWU6IGZpbGUubmFtZVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSA6IGNvdXJzZVxuICAgICAgICAgICkpO1xuICAgICAgICB9XG5cbiAgICAgICAgYWxlcnQoJ+ivvueoi+inhumikeS4iuS8oOaIkOWKn++8gScpO1xuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcign4p2MIOivvueoi+inhumikeS4iuS8oOWksei0pTonLCBlcnJvcik7XG4gICAgICAgIGFsZXJ0KCfor77nqIvop4bpopHkuIrkvKDlpLHotKXvvIzor7fph43or5UnKTtcblxuICAgICAgICAvLyDkuIrkvKDlpLHotKXml7bmuIXpmaTop4bpopHkv6Hmga9cbiAgICAgICAgc2V0Q291cnNlRGV0YWlsKHByZXYgPT4gKHtcbiAgICAgICAgICAuLi5wcmV2LFxuICAgICAgICAgIGNvbnRlbnRDb25maWc6IHtcbiAgICAgICAgICAgIC4uLnByZXYuY29udGVudENvbmZpZyxcbiAgICAgICAgICAgIHZpZGVvOiB7XG4gICAgICAgICAgICAgIHVybDogJycsXG4gICAgICAgICAgICAgIG5hbWU6ICcnXG4gICAgICAgICAgICB9XG4gICAgICAgICAgfVxuICAgICAgICB9KSk7XG4gICAgICB9IGZpbmFsbHkge1xuICAgICAgICAvLyDku47kuIrkvKDkuK3nmoTmlofku7bliJfooajnp7vpmaRcbiAgICAgICAgc2V0VXBsb2FkaW5nRmlsZXMocHJldiA9PiB7XG4gICAgICAgICAgY29uc3QgbmV3U2V0ID0gbmV3IFNldChwcmV2KTtcbiAgICAgICAgICBuZXdTZXQuZGVsZXRlKCd2aWRlbycpO1xuICAgICAgICAgIHJldHVybiBuZXdTZXQ7XG4gICAgICAgIH0pO1xuICAgICAgfVxuICAgIH1cbiAgfTtcblxuICAvLyDop6blj5Hop4bpopHmlofku7bpgInmi6lcbiAgY29uc3QgdHJpZ2dlclZpZGVvVXBsb2FkID0gKCkgPT4ge1xuICAgIGNvbnN0IGlucHV0ID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnaW5wdXQnKTtcbiAgICBpbnB1dC50eXBlID0gJ2ZpbGUnO1xuICAgIGlucHV0LmFjY2VwdCA9ICd2aWRlby9tcDQsdmlkZW8vYXZpLHZpZGVvL21vdix2aWRlby93bXYsdmlkZW8vZmx2JztcbiAgICBpbnB1dC5vbmNoYW5nZSA9IChlKSA9PiBoYW5kbGVWaWRlb1VwbG9hZChlIGFzIGFueSk7XG4gICAgaW5wdXQuY2xpY2soKTtcbiAgfTtcblxuICAvLyDlpITnkIbpmYTku7bkuIrkvKBcbiAgY29uc3QgaGFuZGxlQXR0YWNobWVudFVwbG9hZCA9IGFzeW5jIChldmVudDogUmVhY3QuQ2hhbmdlRXZlbnQ8SFRNTElucHV0RWxlbWVudD4pID0+IHtcbiAgICBjb25zdCBmaWxlID0gZXZlbnQudGFyZ2V0LmZpbGVzPy5bMF07XG4gICAgaWYgKGZpbGUpIHtcbiAgICAgIC8vIOajgOafpeaWh+S7tuexu+Wei1xuICAgICAgY29uc3QgYWxsb3dlZFR5cGVzID0gW1xuICAgICAgICAnYXBwbGljYXRpb24vcGRmJyxcbiAgICAgICAgJ2FwcGxpY2F0aW9uL21zd29yZCcsXG4gICAgICAgICdhcHBsaWNhdGlvbi92bmQub3BlbnhtbGZvcm1hdHMtb2ZmaWNlZG9jdW1lbnQud29yZHByb2Nlc3NpbmdtbC5kb2N1bWVudCcsXG4gICAgICAgICdhcHBsaWNhdGlvbi92bmQubXMtZXhjZWwnLFxuICAgICAgICAnYXBwbGljYXRpb24vdm5kLm9wZW54bWxmb3JtYXRzLW9mZmljZWRvY3VtZW50LnNwcmVhZHNoZWV0bWwuc2hlZXQnLFxuICAgICAgICAnYXBwbGljYXRpb24vdm5kLm1zLXBvd2VycG9pbnQnLFxuICAgICAgICAnYXBwbGljYXRpb24vdm5kLm9wZW54bWxmb3JtYXRzLW9mZmljZWRvY3VtZW50LnByZXNlbnRhdGlvbm1sLnByZXNlbnRhdGlvbicsXG4gICAgICAgICd0ZXh0L3BsYWluJyxcbiAgICAgICAgJ2ltYWdlL2pwZWcnLFxuICAgICAgICAnaW1hZ2UvcG5nJyxcbiAgICAgICAgJ2ltYWdlL2dpZidcbiAgICAgIF07XG5cbiAgICAgIGlmICghYWxsb3dlZFR5cGVzLmluY2x1ZGVzKGZpbGUudHlwZSkpIHtcbiAgICAgICAgYWxlcnQoJ+ivt+mAieaLqeaUr+aMgeeahOaWh+S7tuagvOW8j++8mlBERuOAgURPQ+OAgURPQ1jjgIFYTFPjgIFYTFNY44CBUFBU44CBUFBUWOOAgVRYVOOAgUpQR+OAgVBOR+OAgUdJRicpO1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG5cbiAgICAgIC8vIOajgOafpeaWh+S7tuWkp+WwjyAoMTBNQilcbiAgICAgIGlmIChmaWxlLnNpemUgPiAxMCAqIDEwMjQgKiAxMDI0KSB7XG4gICAgICAgIGFsZXJ0KCfmlofku7blpKflsI/kuI3og73otoXov4cgMTBNQicpO1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG5cbiAgICAgIHRyeSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCfwn5OkIOW8gOWni+S4iuS8oOivvueoi+mZhOS7tjonLCBmaWxlLm5hbWUpO1xuXG4gICAgICAgIC8vIOa3u+WKoOWIsOS4iuS8oOS4reeahOaWh+S7tuWIl+ihqFxuICAgICAgICBzZXRVcGxvYWRpbmdGaWxlcyhwcmV2ID0+IG5ldyBTZXQocHJldikuYWRkKCdkb2N1bWVudCcpKTtcblxuICAgICAgICAvLyDkuIrkvKDliLBPU1NcbiAgICAgICAgY29uc3QgZG9jdW1lbnRVcmwgPSBhd2FpdCB1cGxvYWRBcGkudXBsb2FkVG9Pc3MoZmlsZSk7XG4gICAgICAgIGNvbnNvbGUubG9nKCfinIUg6K++56iL6ZmE5Lu25LiK5Lyg5oiQ5YqfOicsIGRvY3VtZW50VXJsKTtcblxuICAgICAgICAvLyDmm7TmlrDor77nqIvor6bmg4XkuK3nmoTpmYTku7bkv6Hmga9cbiAgICAgICAgc2V0Q291cnNlRGV0YWlsKHByZXYgPT4gKHtcbiAgICAgICAgICAuLi5wcmV2LFxuICAgICAgICAgIGNvbnRlbnRDb25maWc6IHtcbiAgICAgICAgICAgIC4uLnByZXYuY29udGVudENvbmZpZyxcbiAgICAgICAgICAgIGhhc0RvY3VtZW50OiAxLFxuICAgICAgICAgICAgZG9jdW1lbnQ6IHtcbiAgICAgICAgICAgICAgdXJsOiBkb2N1bWVudFVybCxcbiAgICAgICAgICAgICAgbmFtZTogZmlsZS5uYW1lXG4gICAgICAgICAgICB9XG4gICAgICAgICAgfVxuICAgICAgICB9KSk7XG5cbiAgICAgICAgYWxlcnQoJ+ivvueoi+mZhOS7tuS4iuS8oOaIkOWKn++8gScpO1xuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcign4p2MIOivvueoi+mZhOS7tuS4iuS8oOWksei0pTonLCBlcnJvcik7XG4gICAgICAgIGFsZXJ0KCfor77nqIvpmYTku7bkuIrkvKDlpLHotKXvvIzor7fph43or5UnKTtcbiAgICAgIH0gZmluYWxseSB7XG4gICAgICAgIC8vIOS7juS4iuS8oOS4reeahOaWh+S7tuWIl+ihqOenu+mZpFxuICAgICAgICBzZXRVcGxvYWRpbmdGaWxlcyhwcmV2ID0+IHtcbiAgICAgICAgICBjb25zdCBuZXdTZXQgPSBuZXcgU2V0KHByZXYpO1xuICAgICAgICAgIG5ld1NldC5kZWxldGUoJ2RvY3VtZW50Jyk7XG4gICAgICAgICAgcmV0dXJuIG5ld1NldDtcbiAgICAgICAgfSk7XG4gICAgICB9XG4gICAgfVxuICB9O1xuXG4gIC8vIOinpuWPkemZhOS7tuaWh+S7tumAieaLqVxuICBjb25zdCB0cmlnZ2VyQXR0YWNobWVudFVwbG9hZCA9ICgpID0+IHtcbiAgICBjb25zdCBpbnB1dCA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ2lucHV0Jyk7XG4gICAgaW5wdXQudHlwZSA9ICdmaWxlJztcbiAgICBpbnB1dC5hY2NlcHQgPSAnLnBkZiwuZG9jLC5kb2N4LC54bHMsLnhsc3gsLnBwdCwucHB0eCwudHh0LC5qcGcsLnBuZywuZ2lmJztcbiAgICBpbnB1dC5vbmNoYW5nZSA9IChlKSA9PiBoYW5kbGVBdHRhY2htZW50VXBsb2FkKGUgYXMgYW55KTtcbiAgICBpbnB1dC5jbGljaygpO1xuICB9O1xuXG4gIC8vIOWkhOeQhuaVmeWtpumZhOS7tuS4iuS8oFxuICBjb25zdCBoYW5kbGVUZWFjaGluZ01hdGVyaWFsVXBsb2FkID0gYXN5bmMgKGV2ZW50OiBSZWFjdC5DaGFuZ2VFdmVudDxIVE1MSW5wdXRFbGVtZW50PikgPT4ge1xuICAgIGNvbnN0IGZpbGUgPSBldmVudC50YXJnZXQuZmlsZXM/LlswXTtcbiAgICBpZiAoZmlsZSkge1xuICAgICAgLy8g5qOA5p+l5paH5Lu257G75Z6LXG4gICAgICBjb25zdCBhbGxvd2VkVHlwZXMgPSBbXG4gICAgICAgICdhcHBsaWNhdGlvbi9wZGYnLFxuICAgICAgICAnYXBwbGljYXRpb24vbXN3b3JkJyxcbiAgICAgICAgJ2FwcGxpY2F0aW9uL3ZuZC5vcGVueG1sZm9ybWF0cy1vZmZpY2Vkb2N1bWVudC53b3JkcHJvY2Vzc2luZ21sLmRvY3VtZW50JyxcbiAgICAgICAgJ2FwcGxpY2F0aW9uL3ZuZC5tcy1leGNlbCcsXG4gICAgICAgICdhcHBsaWNhdGlvbi92bmQub3BlbnhtbGZvcm1hdHMtb2ZmaWNlZG9jdW1lbnQuc3ByZWFkc2hlZXRtbC5zaGVldCcsXG4gICAgICAgICdhcHBsaWNhdGlvbi92bmQubXMtcG93ZXJwb2ludCcsXG4gICAgICAgICdhcHBsaWNhdGlvbi92bmQub3BlbnhtbGZvcm1hdHMtb2ZmaWNlZG9jdW1lbnQucHJlc2VudGF0aW9ubWwucHJlc2VudGF0aW9uJyxcbiAgICAgICAgJ3RleHQvcGxhaW4nLFxuICAgICAgICAnaW1hZ2UvanBlZycsXG4gICAgICAgICdpbWFnZS9wbmcnLFxuICAgICAgICAnaW1hZ2UvZ2lmJ1xuICAgICAgXTtcblxuICAgICAgaWYgKCFhbGxvd2VkVHlwZXMuaW5jbHVkZXMoZmlsZS50eXBlKSkge1xuICAgICAgICBhbGVydCgn6K+36YCJ5oup5pSv5oyB55qE5paH5Lu25qC85byP77yaUERG44CBRE9D44CBRE9DWOOAgVhMU+OAgVhMU1jjgIFQUFTjgIFQUFRY44CBVFhU44CBSlBH44CBUE5H44CBR0lGJyk7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cblxuICAgICAgLy8g5qOA5p+l5paH5Lu25aSn5bCPICgxME1CKVxuICAgICAgaWYgKGZpbGUuc2l6ZSA+IDEwICogMTAyNCAqIDEwMjQpIHtcbiAgICAgICAgYWxlcnQoJ+aWh+S7tuWkp+Wwj+S4jeiDvei2hei/hyAxME1CJyk7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cblxuICAgICAgdHJ5IHtcbiAgICAgICAgY29uc29sZS5sb2coJ/Cfk6Qg5byA5aeL5LiK5Lyg5pWZ5a2m5p2Q5paZOicsIGZpbGUubmFtZSk7XG5cbiAgICAgICAgLy8g5re75Yqg5Yiw5LiK5Lyg5Lit55qE5paH5Lu25YiX6KGoXG4gICAgICAgIHNldFVwbG9hZGluZ0ZpbGVzKHByZXYgPT4gbmV3IFNldChwcmV2KS5hZGQoYHRlYWNoaW5nLSR7RGF0ZS5ub3coKX1gKSk7XG5cbiAgICAgICAgLy8g5LiK5Lyg5YiwT1NTXG4gICAgICAgIGNvbnN0IG1hdGVyaWFsVXJsID0gYXdhaXQgdXBsb2FkQXBpLnVwbG9hZFRvT3NzKGZpbGUpO1xuICAgICAgICBjb25zb2xlLmxvZygn4pyFIOaVmeWtpuadkOaWmeS4iuS8oOaIkOWKnzonLCBtYXRlcmlhbFVybCk7XG5cbiAgICAgICAgLy8g5re75Yqg5Yiw5pWZ5a2m6ZmE5Lu25YiX6KGoXG4gICAgICAgIGNvbnN0IG5ld01hdGVyaWFsID0ge1xuICAgICAgICAgIHR5cGU6IGZpbGUudHlwZSxcbiAgICAgICAgICBuYW1lOiBmaWxlLm5hbWUsXG4gICAgICAgICAgdXJsOiBtYXRlcmlhbFVybFxuICAgICAgICB9O1xuXG4gICAgICAgIHNldENvdXJzZURldGFpbChwcmV2ID0+ICh7XG4gICAgICAgICAgLi4ucHJldixcbiAgICAgICAgICB0ZWFjaGluZ01hdGVyaWFsczogWy4uLnByZXYudGVhY2hpbmdNYXRlcmlhbHMsIG5ld01hdGVyaWFsXVxuICAgICAgICB9KSk7XG5cbiAgICAgICAgYWxlcnQoJ+aVmeWtpuadkOaWmeS4iuS8oOaIkOWKn++8gScpO1xuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcign4p2MIOaVmeWtpuadkOaWmeS4iuS8oOWksei0pTonLCBlcnJvcik7XG4gICAgICAgIGFsZXJ0KCfmlZnlrabmnZDmlpnkuIrkvKDlpLHotKXvvIzor7fph43or5UnKTtcbiAgICAgIH0gZmluYWxseSB7XG4gICAgICAgIC8vIOS7juS4iuS8oOS4reeahOaWh+S7tuWIl+ihqOenu+mZpFxuICAgICAgICBzZXRVcGxvYWRpbmdGaWxlcyhwcmV2ID0+IHtcbiAgICAgICAgICBjb25zdCBuZXdTZXQgPSBuZXcgU2V0KHByZXYpO1xuICAgICAgICAgIC8vIOenu+mZpOaJgOaciXRlYWNoaW5nLeW8gOWktOeahOmhueebrlxuICAgICAgICAgIEFycmF5LmZyb20obmV3U2V0KS5mb3JFYWNoKGl0ZW0gPT4ge1xuICAgICAgICAgICAgaWYgKGl0ZW0uc3RhcnRzV2l0aCgndGVhY2hpbmctJykpIHtcbiAgICAgICAgICAgICAgbmV3U2V0LmRlbGV0ZShpdGVtKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9KTtcbiAgICAgICAgICByZXR1cm4gbmV3U2V0O1xuICAgICAgICB9KTtcbiAgICAgIH1cbiAgICB9XG4gIH07XG5cbiAgLy8g6Kem5Y+R5pWZ5a2m6ZmE5Lu25paH5Lu26YCJ5oupXG4gIGNvbnN0IHRyaWdnZXJUZWFjaGluZ01hdGVyaWFsVXBsb2FkID0gKCkgPT4ge1xuICAgIGNvbnN0IGlucHV0ID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnaW5wdXQnKTtcbiAgICBpbnB1dC50eXBlID0gJ2ZpbGUnO1xuICAgIGlucHV0LmFjY2VwdCA9ICcucGRmLC5kb2MsLmRvY3gsLnhscywueGxzeCwucHB0LC5wcHR4LC50eHQsLmpwZywucG5nLC5naWYnO1xuICAgIGlucHV0Lm9uY2hhbmdlID0gKGUpID0+IGhhbmRsZVRlYWNoaW5nTWF0ZXJpYWxVcGxvYWQoZSBhcyBhbnkpO1xuICAgIGlucHV0LmNsaWNrKCk7XG4gIH07XG5cbiAgLy8g5Yig6Zmk5pWZ5a2m6ZmE5Lu2XG4gIGNvbnN0IHJlbW92ZVRlYWNoaW5nTWF0ZXJpYWwgPSAoaW5kZXg6IG51bWJlcikgPT4ge1xuICAgIHNldENvdXJzZURldGFpbChwcmV2ID0+ICh7XG4gICAgICAuLi5wcmV2LFxuICAgICAgdGVhY2hpbmdNYXRlcmlhbHM6IHByZXYudGVhY2hpbmdNYXRlcmlhbHMuZmlsdGVyKChfLCBpKSA9PiBpICE9PSBpbmRleClcbiAgICB9KSk7XG4gIH07XG5cbiAgLy8g6K++56iL5YaF5a65566h55CG5Ye95pWwXG4gIGNvbnN0IGFkZFRlYWNoaW5nSW5mb0l0ZW0gPSAoKSA9PiB7XG4gICAgc2V0Q291cnNlRGV0YWlsKHByZXYgPT4gKHtcbiAgICAgIC4uLnByZXYsXG4gICAgICB0ZWFjaGluZ0luZm86IFsuLi5wcmV2LnRlYWNoaW5nSW5mbywgeyB0aXRsZTogJycsIGNvbnRlbnQ6ICcnIH1dXG4gICAgfSkpO1xuICB9O1xuXG4gIGNvbnN0IHJlbW92ZVRlYWNoaW5nSW5mb0l0ZW0gPSAoaW5kZXg6IG51bWJlcikgPT4ge1xuICAgIHNldENvdXJzZURldGFpbChwcmV2ID0+ICh7XG4gICAgICAuLi5wcmV2LFxuICAgICAgdGVhY2hpbmdJbmZvOiBwcmV2LnRlYWNoaW5nSW5mby5maWx0ZXIoKF8sIGkpID0+IGkgIT09IGluZGV4KVxuICAgIH0pKTtcbiAgfTtcblxuICBjb25zdCB1cGRhdGVUZWFjaGluZ0luZm9UaXRsZSA9IChpbmRleDogbnVtYmVyLCB0aXRsZTogc3RyaW5nKSA9PiB7XG4gICAgc2V0Q291cnNlRGV0YWlsKHByZXYgPT4ge1xuICAgICAgY29uc3QgbmV3VGVhY2hpbmdJbmZvID0gWy4uLnByZXYudGVhY2hpbmdJbmZvXTtcbiAgICAgIG5ld1RlYWNoaW5nSW5mb1tpbmRleF0gPSB7IC4uLm5ld1RlYWNoaW5nSW5mb1tpbmRleF0sIHRpdGxlIH07XG4gICAgICByZXR1cm4geyAuLi5wcmV2LCB0ZWFjaGluZ0luZm86IG5ld1RlYWNoaW5nSW5mbyB9O1xuICAgIH0pO1xuICB9O1xuXG4gIGNvbnN0IHVwZGF0ZVRlYWNoaW5nSW5mb0NvbnRlbnQgPSAoaW5kZXg6IG51bWJlciwgY29udGVudDogc3RyaW5nKSA9PiB7XG4gICAgc2V0Q291cnNlRGV0YWlsKHByZXYgPT4ge1xuICAgICAgY29uc3QgbmV3VGVhY2hpbmdJbmZvID0gWy4uLnByZXYudGVhY2hpbmdJbmZvXTtcbiAgICAgIG5ld1RlYWNoaW5nSW5mb1tpbmRleF0gPSB7IC4uLm5ld1RlYWNoaW5nSW5mb1tpbmRleF0sIGNvbnRlbnQgfTtcbiAgICAgIHJldHVybiB7IC4uLnByZXYsIHRlYWNoaW5nSW5mbzogbmV3VGVhY2hpbmdJbmZvIH07XG4gICAgfSk7XG4gIH07XG5cbiAgLy8g5bCGVUnmoLzlvI/nmoR0ZWFjaGluZ0luZm/ovazmjaLkuLpBUEnmoLzlvI9cbiAgY29uc3QgY29udmVydFRlYWNoaW5nSW5mb0ZvckFQSSA9ICh0ZWFjaGluZ0luZm86IHsgdGl0bGU6IHN0cmluZzsgY29udGVudDogc3RyaW5nIH1bXSkgPT4ge1xuICAgIHJldHVybiB0ZWFjaGluZ0luZm8ubWFwKGluZm8gPT4gKHtcbiAgICAgIHRpdGxlOiBpbmZvLnRpdGxlLFxuICAgICAgY29udGVudDogaW5mby5jb250ZW50LnNwbGl0KCdcXG4nKS5maWx0ZXIobGluZSA9PiBsaW5lLnRyaW0oKSkgLy8g5oyJ5o2i6KGM5YiG5Ymy5bm26L+H5ruk56m66KGMXG4gICAgfSkpO1xuICB9O1xuXG4gIC8vIOajgOafpeaYr+WQpuacieacquS4iuS8oOWujOaIkOeahOaWh+S7tu+8iGJsb2IgVVJM77yJXG4gIGNvbnN0IGNoZWNrRm9yQmxvYlVybHMgPSAoY291cnNlOiBDb3Vyc2VJdGVtKSA9PiB7XG4gICAgY29uc3QgaXNzdWVzID0gW107XG5cbiAgICAvLyDmo4Dmn6XlsIHpnaLlm77niYdcbiAgICBpZiAoY291cnNlLmNvdmVySW1hZ2UgJiYgY291cnNlLmNvdmVySW1hZ2Uuc3RhcnRzV2l0aCgnYmxvYjonKSkge1xuICAgICAgaXNzdWVzLnB1c2goJ+ivvueoi+WwgemdouWbvueJhycpO1xuICAgIH1cblxuICAgIC8vIOajgOafpeinhumikeaWh+S7tlxuICAgIGlmIChjb3Vyc2UuY29udGVudENvbmZpZz8udmlkZW8/LnVybCAmJiBjb3Vyc2UuY29udGVudENvbmZpZy52aWRlby51cmwuc3RhcnRzV2l0aCgnYmxvYjonKSkge1xuICAgICAgaXNzdWVzLnB1c2goJ+ivvueoi+inhumikScpO1xuICAgIH1cblxuICAgIC8vIOajgOafpeaWh+aho+mZhOS7tlxuICAgIGlmIChjb3Vyc2UuY29udGVudENvbmZpZz8uZG9jdW1lbnQ/LnVybCAmJiBjb3Vyc2UuY29udGVudENvbmZpZy5kb2N1bWVudC51cmwuc3RhcnRzV2l0aCgnYmxvYjonKSkge1xuICAgICAgaXNzdWVzLnB1c2goJ+ivvueoi+aWh+ahoycpO1xuICAgIH1cblxuICAgIC8vIOajgOafpeaVmeWtpuadkOaWmVxuICAgIGlmIChjb3Vyc2UuYWRkaXRpb25hbFJlc291cmNlcykge1xuICAgICAgY291cnNlLmFkZGl0aW9uYWxSZXNvdXJjZXMuZm9yRWFjaCgocmVzb3VyY2UsIGluZGV4KSA9PiB7XG4gICAgICAgIGlmIChyZXNvdXJjZS51cmwgJiYgcmVzb3VyY2UudXJsLnN0YXJ0c1dpdGgoJ2Jsb2I6JykpIHtcbiAgICAgICAgICBpc3N1ZXMucHVzaChg5pWZ5a2m5p2Q5paZJHtpbmRleCArIDF9YCk7XG4gICAgICAgIH1cbiAgICAgIH0pO1xuICAgIH1cblxuICAgIHJldHVybiBpc3N1ZXM7XG4gIH07XG5cbiAgLy8g5L+d5a2Y5Y2V5Liq6K++56iL5Yiw5ZCO56uvXG4gIGNvbnN0IHNhdmVDb3Vyc2UgPSBhc3luYyAoY291cnNlOiBDb3Vyc2VJdGVtKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIC8vIOmqjOivgeW/heimgeeahOaVsOaNrlxuICAgICAgaWYgKCFjb3Vyc2UudGl0bGUgfHwgY291cnNlLnRpdGxlLnRyaW0oKSA9PT0gJycpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCfor77nqIvmoIfpopjkuI3og73kuLrnqbonKTtcbiAgICAgIH1cblxuICAgICAgLy8g5qOA5p+l5piv5ZCm5pyJ5pyq5LiK5Lyg5a6M5oiQ55qE5paH5Lu2XG4gICAgICBjb25zdCBibG9iSXNzdWVzID0gY2hlY2tGb3JCbG9iVXJscyhjb3Vyc2UpO1xuICAgICAgaWYgKGJsb2JJc3N1ZXMubGVuZ3RoID4gMCkge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYOS7peS4i+aWh+S7tuWwmuacquS4iuS8oOWujOaIkO+8jOivt+etieW+heS4iuS8oOWujOaIkOWQjuWGjeS/neWtmO+8miR7YmxvYklzc3Vlcy5qb2luKCfjgIEnKX1gKTtcbiAgICAgIH1cblxuICAgICAgLy8g5YeG5aSH6K++56iL5pWw5o2uXG4gICAgICBjb25zdCBjb3Vyc2VEYXRhID0ge1xuICAgICAgICBzZXJpZXNJZDogc2VyaWVzSWQsXG4gICAgICAgIHRpdGxlOiBjb3Vyc2UudGl0bGUsXG4gICAgICAgIGRlc2NyaXB0aW9uOiBjb3Vyc2UuZGVzY3JpcHRpb24gfHwgJycsXG4gICAgICAgIGNvdmVySW1hZ2U6IGNvdXJzZS5jb3ZlckltYWdlIHx8ICcnLFxuICAgICAgICBoYXNWaWRlbzogY291cnNlLmNvbnRlbnRDb25maWc/LnZpZGVvPy51cmwgPyAxIDogMCxcbiAgICAgICAgaGFzRG9jdW1lbnQ6IGNvdXJzZS5jb250ZW50Q29uZmlnPy5kb2N1bWVudD8udXJsID8gMSA6IDAsXG4gICAgICAgIGhhc0F1ZGlvOiAwLFxuICAgICAgICB2aWRlb0R1cmF0aW9uOiAwLFxuICAgICAgICBjb250ZW50Q29uZmlnOiBjb3Vyc2UuY29udGVudENvbmZpZyB8fCB7fSxcbiAgICAgICAgdGVhY2hpbmdJbmZvOiBjb252ZXJ0VGVhY2hpbmdJbmZvRm9yQVBJKGNvdXJzZS50ZWFjaGluZ0luZm8gfHwgW10pLFxuICAgICAgICBhZGRpdGlvbmFsUmVzb3VyY2VzOiBjb3Vyc2UuYWRkaXRpb25hbFJlc291cmNlcyB8fCBbXSxcbiAgICAgICAgb3JkZXJJbmRleDogY291cnNlLm9yZGVySW5kZXggfHwgMVxuICAgICAgfTtcblxuICAgICAgY29uc29sZS5sb2coJ/Cfkr4g5YeG5aSH5L+d5a2Y6K++56iL5pWw5o2uOicsIGNvdXJzZURhdGEpO1xuXG4gICAgICBsZXQgcmVzdWx0OiBhbnk7XG5cbiAgICAgIC8vIOWIpOaWreaYr+aWsOivvueoi+i/mOaYr+abtOaWsOivvueoi1xuICAgICAgaWYgKGNvdXJzZS5pZCAmJiBjb3Vyc2UuaWQgPiAxMDAwMDAwKSB7XG4gICAgICAgIC8vIOaWsOivvueoi++8jOS9v+eUqOWIm+W7ukFQSVxuICAgICAgICBjb25zb2xlLmxvZygn8J+TpCDliJvlu7rmlrDor77nqIsnKTtcbiAgICAgICAgY29uc3QgeyBkYXRhOiByZXNwb25zZSB9ID0gYXdhaXQgY291cnNlQXBpLmNyZWF0ZUNvdXJzZShjb3Vyc2VEYXRhKTtcbiAgICAgICAgcmVzdWx0ID0gcmVzcG9uc2U7XG4gICAgICAgIGNvbnNvbGUubG9nKCfinIUg6K++56iL5Yib5bu65oiQ5YqfJyk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICAvLyDnjrDmnInor77nqIvvvIzkvb/nlKjmm7TmlrBBUElcbiAgICAgICAgY29uc29sZS5sb2coJ/Cfk6Qg5pu05paw546w5pyJ6K++56iLOicsIGNvdXJzZS5pZCk7XG4gICAgICAgIGNvbnN0IHsgZGF0YTogcmVzcG9uc2UgfSA9IGF3YWl0IGNvdXJzZUFwaS51cGRhdGVDb3Vyc2UoY291cnNlLmlkLCBjb3Vyc2VEYXRhKTtcbiAgICAgICAgcmVzdWx0ID0gcmVzcG9uc2U7XG4gICAgICAgIGNvbnNvbGUubG9nKCfinIUg6K++56iL5pu05paw5oiQ5YqfJyk7XG4gICAgICB9XG5cbiAgICAgIHJldHVybiByZXN1bHQ7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDkv53lrZjor77nqIvlpLHotKU6JywgZXJyb3IpO1xuICAgICAgdGhyb3cgZXJyb3I7XG4gICAgfVxuICB9O1xuXG4gIC8vIOWPkeW4g+mAieS4reeahOivvueoi1xuICBjb25zdCBoYW5kbGVQdWJsaXNoQ291cnNlID0gYXN5bmMgKCkgPT4ge1xuICAgIGlmICghc2VsZWN0ZWRDb3Vyc2VJZCkge1xuICAgICAgYWxlcnQoJ+ivt+WFiOmAieaLqeimgeWPkeW4g+eahOivvueoiycpO1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIGNvbnN0IHNlbGVjdGVkQ291cnNlID0gY291cnNlTGlzdC5maW5kKGNvdXJzZSA9PiBjb3Vyc2UuaWQgPT09IHNlbGVjdGVkQ291cnNlSWQpO1xuICAgIGlmICghc2VsZWN0ZWRDb3Vyc2UpIHtcbiAgICAgIGFsZXJ0KCfmnKrmib7liLDpgInkuK3nmoTor77nqIsnKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICAvLyDmo4Dmn6Xor77nqIvmmK/lkKblt7Lnu4/lj5HluINcbiAgICBpZiAoc2VsZWN0ZWRDb3Vyc2Uuc3RhdHVzID09PSAxKSB7XG4gICAgICBhbGVydCgn6K+l6K++56iL5bey57uP5Y+R5biD77yM5peg6ZyA6YeN5aSN5Y+R5biDJyk7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgdHJ5IHtcbiAgICAgIHNldElzUHVibGlzaGluZyh0cnVlKTtcbiAgICAgIGNvbnNvbGUubG9nKCfwn5OkIOW8gOWni+WPkeW4g+ivvueoiycpO1xuICAgICAgY29uc29sZS5sb2coJ/Cfk6Qgc2VsZWN0ZWRDb3Vyc2VJZDonLCBzZWxlY3RlZENvdXJzZUlkLCAn57G75Z6LOicsIHR5cGVvZiBzZWxlY3RlZENvdXJzZUlkKTtcbiAgICAgIGNvbnNvbGUubG9nKCfwn5OkIOivvueoi+S/oeaBrzonLCBzZWxlY3RlZENvdXJzZSk7XG4gICAgICBjb25zb2xlLmxvZygn8J+TpCDor77nqItJROWtl+autTonLCBzZWxlY3RlZENvdXJzZS5pZCwgJ+exu+WeizonLCB0eXBlb2Ygc2VsZWN0ZWRDb3Vyc2UuaWQpO1xuICAgICAgY29uc29sZS5sb2coJ/Cfk6Qg6K++56iL5YiX6KGo5Lit55qE5omA5pyJSUQ6JywgY291cnNlTGlzdC5tYXAoYyA9PiAoeyBpZDogYy5pZCwgdHlwZTogdHlwZW9mIGMuaWQsIHRpdGxlOiBjLnRpdGxlIH0pKSk7XG5cbiAgICAgIC8vIOehruS/neS9v+eUqOato+ehrueahOivvueoi0lEXG4gICAgICBjb25zdCBjb3Vyc2VJZFRvUHVibGlzaCA9IHNlbGVjdGVkQ291cnNlLmlkO1xuICAgICAgY29uc29sZS5sb2coJ/Cfk6Qg5Y2z5bCG5Y+R5biD55qE6K++56iLSUQ6JywgY291cnNlSWRUb1B1Ymxpc2gsICfnsbvlnos6JywgdHlwZW9mIGNvdXJzZUlkVG9QdWJsaXNoKTtcblxuICAgICAgY29uc3QgeyBkYXRhOiByZXNwb25zZSB9ID0gYXdhaXQgY291cnNlQXBpLnB1Ymxpc2hDb3Vyc2UoY291cnNlSWRUb1B1Ymxpc2gpO1xuXG4gICAgICBpZiAocmVzcG9uc2UuY29kZSA9PT0gMjAwKSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCfinIUg6K++56iL5Y+R5biD5oiQ5YqfOicsIHJlc3BvbnNlLmRhdGEpO1xuICAgICAgICBhbGVydCgn6K++56iL5Y+R5biD5oiQ5Yqf77yBJyk7XG5cbiAgICAgICAgLy8g5Yi35paw6K++56iL5YiX6KGo5Lul5pu05paw54q25oCBXG4gICAgICAgIGF3YWl0IGxvYWRDb3Vyc2VMaXN0KCk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBjb25zb2xlLmVycm9yKCfinYwg5Y+R5biD6K++56iL5aSx6LSlOicsIHJlc3BvbnNlLm1lc3NhZ2UpO1xuICAgICAgICBhbGVydChyZXNwb25zZS5tZXNzYWdlIHx8ICflj5HluIPor77nqIvlpLHotKUnKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfinYwg5Y+R5biD6K++56iL5aSx6LSlOicsIGVycm9yKTtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDplJnor6/or6bmg4U6JywgZXJyb3IucmVzcG9uc2U/LmRhdGEpO1xuXG4gICAgICAvLyDlpITnkIblhbfkvZPnmoTplJnor6/kv6Hmga9cbiAgICAgIGlmIChlcnJvci5yZXNwb25zZT8uZGF0YT8ubWVzc2FnZSkge1xuICAgICAgICBhbGVydChlcnJvci5yZXNwb25zZS5kYXRhLm1lc3NhZ2UpO1xuICAgICAgfSBlbHNlIGlmIChlcnJvci5tZXNzYWdlKSB7XG4gICAgICAgIGFsZXJ0KGVycm9yLm1lc3NhZ2UpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgYWxlcnQoJ+WPkeW4g+ivvueoi+Wksei0pe+8jOivt+mHjeivlScpO1xuICAgICAgfVxuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRJc1B1Ymxpc2hpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICAvLyDkv53lrZjor77nqIvliJfooahcbiAgY29uc3QgaGFuZGxlU2F2ZSA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgc2V0SXNDcmVhdGluZyh0cnVlKTtcbiAgICAgIC8vIOajgOafpeaYr+WQpuacieaWh+S7tuato+WcqOS4iuS8oFxuICAgICAgaWYgKHVwbG9hZGluZ0ZpbGVzLnNpemUgPiAwKSB7XG4gICAgICAgIGFsZXJ0KCfmnInmlofku7bmraPlnKjkuIrkvKDkuK3vvIzor7fnrYnlvoXkuIrkvKDlrozmiJDlkI7lho3kv53lrZgnKTtcbiAgICAgICAgc2V0SXNDcmVhdGluZyhmYWxzZSk7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cblxuICAgICAgY29uc29sZS5sb2coJ/Cfkr4g5byA5aeL5L+d5a2Y6K++56iL5YiX6KGoJyk7XG5cbiAgICAgIC8vIOWmguaenOaciemAieS4reeahOivvueoi+S4lOWcqOe8lui+keeKtuaAge+8jOWFiOS/neWtmOW9k+WJjeivvueoi1xuICAgICAgaWYgKHNlbGVjdGVkQ291cnNlSWQgJiYgcmlnaHRQYW5lbFR5cGUgPT09ICdjb3Vyc2UnKSB7XG4gICAgICAgIGNvbnN0IHNlbGVjdGVkQ291cnNlID0gY291cnNlTGlzdC5maW5kKGMgPT4gYy5pZCA9PT0gc2VsZWN0ZWRDb3Vyc2VJZCk7XG4gICAgICAgIGlmIChzZWxlY3RlZENvdXJzZSkge1xuICAgICAgICAgIC8vIOabtOaWsOivvueoi+aVsOaNrlxuICAgICAgICAgIGNvbnN0IHVwZGF0ZWRDb3Vyc2UgPSB7XG4gICAgICAgICAgICAuLi5zZWxlY3RlZENvdXJzZSxcbiAgICAgICAgICAgIHRpdGxlOiBjb3Vyc2VEZXRhaWwudGl0bGUsXG4gICAgICAgICAgICBkZXNjcmlwdGlvbjogY291cnNlRGV0YWlsLmRlc2NyaXB0aW9uLFxuICAgICAgICAgICAgY292ZXJJbWFnZTogY291cnNlRGV0YWlsLmNvdmVySW1hZ2UsXG4gICAgICAgICAgICBoYXNWaWRlbzogY291cnNlRGV0YWlsLmlzVmlkZW9FbmFibGVkID8gMSA6IDAsXG4gICAgICAgICAgICBoYXNEb2N1bWVudDogY291cnNlRGV0YWlsLmlzQXR0YWNobWVudEVuYWJsZWQgPyAxIDogMCxcbiAgICAgICAgICAgIGNvbnRlbnRDb25maWc6IGNvdXJzZURldGFpbC5jb250ZW50Q29uZmlnLFxuICAgICAgICAgICAgdGVhY2hpbmdJbmZvOiBjb3Vyc2VEZXRhaWwudGVhY2hpbmdJbmZvLFxuICAgICAgICAgICAgYWRkaXRpb25hbFJlc291cmNlczogY291cnNlRGV0YWlsLnRlYWNoaW5nTWF0ZXJpYWxzPy5tYXAobWF0ZXJpYWwgPT4gKHtcbiAgICAgICAgICAgICAgdGl0bGU6IG1hdGVyaWFsLm5hbWUsXG4gICAgICAgICAgICAgIHVybDogbWF0ZXJpYWwudXJsLFxuICAgICAgICAgICAgICBkZXNjcmlwdGlvbjogbWF0ZXJpYWwubmFtZVxuICAgICAgICAgICAgfSkpIHx8IFtdXG4gICAgICAgICAgfTtcblxuICAgICAgICAgIC8vIOWFiOabtOaWsOivvueoi+WIl+ihqOS4reeahOaVsOaNrlxuICAgICAgICAgIHNldENvdXJzZUxpc3QocHJldiA9PiBwcmV2Lm1hcChjb3Vyc2UgPT5cbiAgICAgICAgICAgIGNvdXJzZS5pZCA9PT0gc2VsZWN0ZWRDb3Vyc2VJZCA/IHVwZGF0ZWRDb3Vyc2UgOiBjb3Vyc2VcbiAgICAgICAgICApKTtcblxuICAgICAgICAgIC8vIOeEtuWQjuS/neWtmOWIsOWQjuerr1xuICAgICAgICAgIGF3YWl0IHNhdmVDb3Vyc2UodXBkYXRlZENvdXJzZSk7XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgLy8g5L+d5a2Y57O75YiX6K++56iL5L+h5oGvXG4gICAgICBjb25zdCBkYXRhID0ge1xuICAgICAgICB0aXRsZTogZWRpdGluZ1RpdGxlLFxuICAgICAgICBjb3Vyc2VHb2FscyxcbiAgICAgICAgY291cnNlT2JqZWN0aXZlcyxcbiAgICAgICAgY291cnNlTGlzdFxuICAgICAgfTtcblxuICAgICAgb25TYXZlKGRhdGEpO1xuICAgICAgb25DbG9zZSgpO1xuXG4gICAgICBjb25zb2xlLmxvZygn4pyFIOivvueoi+WIl+ihqOS/neWtmOWujOaIkCcpO1xuICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDkv53lrZjlpLHotKU6JywgZXJyb3IpO1xuICAgICAgYWxlcnQoYOS/neWtmOWksei0pTogJHtlcnJvci5tZXNzYWdlIHx8ICfor7fph43or5UnfWApO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRJc0NyZWF0aW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgLy8g5Y+R5biD57O75YiX6K++56iLXG4gIGNvbnN0IGhhbmRsZVB1Ymxpc2ggPSBhc3luYyAoKSA9PiB7XG4gICAgLy8g5aaC5p6c57O75YiX5bey5Y+R5biD77yM5LiN5omn6KGM5Lu75L2V5pON5L2cXG4gICAgaWYgKHNlcmllc1N0YXR1cyA9PT0gMSkge1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIHRyeSB7XG4gICAgICBzZXRJc1B1Ymxpc2hpbmdTZXJpZXModHJ1ZSk7XG5cbiAgICAgIC8vIOajgOafpeaYr+WQpuacieivvueoi1xuICAgICAgaWYgKGNvdXJzZUxpc3QubGVuZ3RoID09PSAwKSB7XG4gICAgICAgIGFsZXJ0KCflj5HluIPlpLHotKXvvJror77nqIvns7vliJfkuK3oh7PlsJHpnIDopoHljIXlkKvkuIDkuKror77nqIsnKTtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuXG4gICAgICBjb25zb2xlLmxvZygn8J+TpCDlvIDlp4vlj5HluIPns7vliJfor77nqIvvvIzns7vliJdJRDonLCBzZXJpZXNJZCk7XG5cbiAgICAgIGNvbnN0IHsgZGF0YTogcmVzcG9uc2UgfSA9IGF3YWl0IGNvdXJzZUFwaS5wdWJsaXNoQ291cnNlU2VyaWVzKHNlcmllc0lkKTtcblxuICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkge1xuICAgICAgICBjb25zb2xlLmxvZygn4pyFIOezu+WIl+ivvueoi+WPkeW4g+aIkOWKnzonLCByZXNwb25zZS5kYXRhKTtcblxuICAgICAgICAvLyDmnoTlu7rmiJDlip/mtojmga9cbiAgICAgICAgY29uc3QgcHVibGlzaERhdGEgPSByZXNwb25zZS5kYXRhO1xuICAgICAgICBsZXQgc3VjY2Vzc01lc3NhZ2UgPSBg57O75YiX6K++56iLXCIke3B1Ymxpc2hEYXRhLnRpdGxlfVwi5Y+R5biD5oiQ5Yqf77yBYDtcblxuICAgICAgICAvLyDlpoLmnpzmnInlj5HluIPnu5/orqHkv6Hmga/vvIzmt7vliqDliLDmtojmga/kuK1cbiAgICAgICAgaWYgKHB1Ymxpc2hEYXRhLnB1Ymxpc2hTdGF0cykge1xuICAgICAgICAgIGNvbnN0IHN0YXRzID0gcHVibGlzaERhdGEucHVibGlzaFN0YXRzO1xuICAgICAgICAgIHN1Y2Nlc3NNZXNzYWdlICs9IGBcXG5cXG7lj5HluIPnu5/orqHvvJpcXG7igKIg5oC76K++56iL5pWw77yaJHtwdWJsaXNoRGF0YS50b3RhbENvdXJzZXN9XFxu4oCiIOW3suWPkeW4g+ivvueoi++8miR7cHVibGlzaERhdGEucHVibGlzaGVkQ291cnNlc31cXG7igKIg6KeG6aKR6K++56iL77yaJHtzdGF0cy52aWRlb0NvdXJzZUNvdW50feS4qlxcbuKAoiDmlofmoaPor77nqIvvvJoke3N0YXRzLmRvY3VtZW50Q291cnNlQ291bnR95LiqXFxu4oCiIOaAu+i1hOa6kOaVsO+8miR7c3RhdHMudG90YWxSZXNvdXJjZXNDb3VudH3kuKpgO1xuXG4gICAgICAgICAgaWYgKHN0YXRzLnRvdGFsVmlkZW9EdXJhdGlvbiA+IDApIHtcbiAgICAgICAgICAgIGNvbnN0IGR1cmF0aW9uTWludXRlcyA9IE1hdGgucm91bmQoc3RhdHMudG90YWxWaWRlb0R1cmF0aW9uIC8gNjApO1xuICAgICAgICAgICAgc3VjY2Vzc01lc3NhZ2UgKz0gYFxcbuKAoiDop4bpopHmgLvml7bplb/vvJoke2R1cmF0aW9uTWludXRlc33liIbpkp9gO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuXG4gICAgICAgIGFsZXJ0KHN1Y2Nlc3NNZXNzYWdlKTtcblxuICAgICAgICAvLyDmm7TmlrDns7vliJfnirbmgIHkuLrlt7Llj5HluINcbiAgICAgICAgc2V0U2VyaWVzU3RhdHVzKDEpO1xuXG4gICAgICAgIC8vIOWIt+aWsOivvueoi+WIl+ihqOS7peabtOaWsOeKtuaAgVxuICAgICAgICBhd2FpdCBsb2FkQ291cnNlTGlzdCgpO1xuXG4gICAgICAgIC8vIOmAmuefpeeItue7hOS7tuWIt+aWsOaVsOaNrlxuICAgICAgICBvblNhdmUoe1xuICAgICAgICAgIHR5cGU6ICdwdWJsaXNoX3NlcmllcycsXG4gICAgICAgICAgc2VyaWVzSWQ6IHNlcmllc0lkLFxuICAgICAgICAgIG1lc3NhZ2U6ICfns7vliJfor77nqIvlj5HluIPmiJDlip8nXG4gICAgICAgIH0pO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgY29uc29sZS5lcnJvcign4p2MIOWPkeW4g+ezu+WIl+ivvueoi+Wksei0pTonLCByZXNwb25zZS5tZXNzYWdlKTtcbiAgICAgICAgYWxlcnQocmVzcG9uc2UubWVzc2FnZSB8fCAn5Y+R5biD57O75YiX6K++56iL5aSx6LSlJyk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgY29uc29sZS5lcnJvcign4p2MIOWPkeW4g+ezu+WIl+ivvueoi+WHuumUmTonLCBlcnJvcik7XG5cbiAgICAgIC8vIOWkhOeQhuWFt+S9k+eahOmUmeivr+S/oeaBr1xuICAgICAgbGV0IGVycm9yTWVzc2FnZSA9ICflj5HluIPns7vliJfor77nqIvlpLHotKUnO1xuICAgICAgaWYgKGVycm9yLnJlc3BvbnNlPy5kYXRhPy5tZXNzYWdlKSB7XG4gICAgICAgIGVycm9yTWVzc2FnZSA9IGVycm9yLnJlc3BvbnNlLmRhdGEubWVzc2FnZTtcbiAgICAgIH0gZWxzZSBpZiAoZXJyb3IubWVzc2FnZSkge1xuICAgICAgICBlcnJvck1lc3NhZ2UgPSBlcnJvci5tZXNzYWdlO1xuICAgICAgfVxuXG4gICAgICBhbGVydChlcnJvck1lc3NhZ2UpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRJc1B1Ymxpc2hpbmdTZXJpZXMoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICAvLyDpgIDlh7rnvJbovpHmqKHlvI8gLSDkv53lrZjmlbDmja7lubblhbPpl61cbiAgY29uc3QgaGFuZGxlRXhpdEVkaXQgPSAoKSA9PiB7XG4gICAgaGFuZGxlU2F2ZSgpO1xuICB9O1xuXG4gIC8vIOaYvuekuuiuvue9rumdouadv1xuICBjb25zdCBzaG93U2V0dGluZ3NQYW5lbCA9ICgpID0+IHtcbiAgICBzZXRSaWdodFBhbmVsVHlwZSgnc2V0dGluZ3MnKTtcbiAgICBzZXRTZWxlY3RlZENvdXJzZUlkKG51bGwpO1xuICB9O1xuXG4gIC8vIOWkhOeQhuaooeadv+mAieaLqVxuICBjb25zdCBoYW5kbGVUZW1wbGF0ZVNlbGVjdCA9ICh0ZW1wbGF0ZTogYW55KSA9PiB7XG4gICAgc2V0Q291cnNlRGV0YWlsKHByZXYgPT4gKHtcbiAgICAgIC4uLnByZXYsXG4gICAgICBzZWxlY3RlZFRlbXBsYXRlOiB0ZW1wbGF0ZS50ZW1wbGF0ZU5hbWVcbiAgICB9KSk7XG4gICAgc2V0SXNUZW1wbGF0ZVBpY2tlck9wZW4oZmFsc2UpO1xuICB9O1xuXG4gIC8vIOaJk+W8gOaooeadv+mAieaLqeW8ueeql1xuICBjb25zdCBoYW5kbGVPcGVuVGVtcGxhdGVQaWNrZXIgPSAoKSA9PiB7XG4gICAgc2V0SXNUZW1wbGF0ZVBpY2tlck9wZW4odHJ1ZSk7XG4gIH07XG5cbiAgLy8g5pi+56S66K++56iL57yW6L6R6Z2i5p2/XG4gIGNvbnN0IHNob3dDb3Vyc2VQYW5lbCA9IGFzeW5jIChjb3Vyc2VJZDogbnVtYmVyKSA9PiB7XG4gICAgc2V0UmlnaHRQYW5lbFR5cGUoJ2NvdXJzZScpO1xuICAgIHNldFNlbGVjdGVkQ291cnNlSWQoY291cnNlSWQpO1xuXG4gICAgLy8g5aaC5p6c5L2c5ZOB5pWw5o2u6L+Y5rKh5pyJ5Yqg6L2977yM6YeN5paw5Yqg6L29XG4gICAgaWYgKHdvcmtzLmxlbmd0aCA9PT0gMCAmJiAhbG9hZGluZ1dvcmtzKSB7XG4gICAgICBsb2FkVXNlcldvcmtzKCk7XG4gICAgfVxuXG4gICAgLy8g6I635Y+W6YCJ5Lit55qE6K++56iL5bm25pu05pawY291cnNlRGV0YWls54q25oCBXG4gICAgY29uc3Qgc2VsZWN0ZWRDb3Vyc2UgPSBjb3Vyc2VMaXN0LmZpbmQoY291cnNlID0+IGNvdXJzZS5pZCA9PT0gY291cnNlSWQpO1xuICAgIGNvbnNvbGUubG9nKCfwn46vIOaJvuWIsOeahOivvueoizonLCBzZWxlY3RlZENvdXJzZSk7XG5cbiAgICBpZiAoc2VsZWN0ZWRDb3Vyc2UpIHtcbiAgICAgIHRyeSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCfwn5SEIOiOt+WPluivvueoi+ivpuaDhe+8jHNlcmllc0lkOicsIHNlcmllc0lkLCAnY291cnNlSWQ6JywgY291cnNlSWQpO1xuXG4gICAgICAgIC8vIOiOt+WPluecn+WunueahOivvueoi+ivpuaDheaVsOaNrlxuICAgICAgICBjb25zdCB7IGRhdGE6IHJlcyB9ID0gYXdhaXQgZmV0Y2hDb3Vyc2VEZXRhaWwoc2VyaWVzSWQsIGNvdXJzZUlkKTtcblxuICAgICAgICBpZiAocmVzLmNvZGUgPT09IDIwMCAmJiByZXMuZGF0YSkge1xuICAgICAgICAgIGNvbnN0IGNvdXJzZURldGFpbERhdGEgPSByZXMuZGF0YTtcbiAgICAgICAgICBjb25zb2xlLmxvZygn4pyFIOiOt+WPluWIsOivvueoi+ivpuaDhTonLCBjb3Vyc2VEZXRhaWxEYXRhKTtcblxuICAgICAgICAgIC8vIOWwhuecn+WunueahCBhZGRpdGlvbmFsUmVzb3VyY2VzIOaYoOWwhOWIsCB0ZWFjaGluZ01hdGVyaWFsc1xuICAgICAgICAgIGNvbnN0IHRlYWNoaW5nTWF0ZXJpYWxzID0gY291cnNlRGV0YWlsRGF0YS5hZGRpdGlvbmFsUmVzb3VyY2VzPy5tYXAoKHJlc291cmNlOiBhbnkpID0+ICh7XG4gICAgICAgICAgICB0eXBlOiAnYXBwbGljYXRpb24vb2N0ZXQtc3RyZWFtJywgLy8g6buY6K6k57G75Z6LXG4gICAgICAgICAgICBuYW1lOiByZXNvdXJjZS50aXRsZSB8fCByZXNvdXJjZS5uYW1lIHx8ICfpmYTku7YnLFxuICAgICAgICAgICAgdXJsOiByZXNvdXJjZS51cmxcbiAgICAgICAgICB9KSkgfHwgW107XG5cbiAgICAgICAgICAvLyDlsIZBUEnov5Tlm57nmoR0ZWFjaGluZ0luZm/mlbDnu4TmoLzlvI/ovazmjaLkuLrmlrDnmoTmoLzlvI9cbiAgICAgICAgICBjb25zdCBtYXBwZWRUZWFjaGluZ0luZm8gPSBjb3Vyc2VEZXRhaWxEYXRhLnRlYWNoaW5nSW5mbz8ubWFwKChpbmZvOiBhbnkpID0+ICh7XG4gICAgICAgICAgICB0aXRsZTogaW5mby50aXRsZSB8fCAnJyxcbiAgICAgICAgICAgIGNvbnRlbnQ6IEFycmF5LmlzQXJyYXkoaW5mby5jb250ZW50KSA/IGluZm8uY29udGVudC5qb2luKCdcXG4nKSA6IChpbmZvLmNvbnRlbnQgfHwgJycpXG4gICAgICAgICAgfSkpIHx8IFtdO1xuXG4gICAgICAgICAgY29uc29sZS5sb2coJ/Cfk44g5pig5bCE55qE5pWZ5a2m6ZmE5Lu2OicsIHRlYWNoaW5nTWF0ZXJpYWxzKTtcbiAgICAgICAgICBjb25zb2xlLmxvZygn8J+TmiDmmKDlsITnmoTmlZnlrabkv6Hmga86JywgbWFwcGVkVGVhY2hpbmdJbmZvKTtcblxuICAgICAgICAgIHNldENvdXJzZURldGFpbChwcmV2ID0+ICh7XG4gICAgICAgICAgICAuLi5wcmV2LFxuICAgICAgICAgICAgdGl0bGU6IGNvdXJzZURldGFpbERhdGEudGl0bGUsXG4gICAgICAgICAgICBkZXNjcmlwdGlvbjogY291cnNlRGV0YWlsRGF0YS5kZXNjcmlwdGlvbixcbiAgICAgICAgICAgIGNvdmVySW1hZ2U6IGNvdXJzZURldGFpbERhdGEuY292ZXJJbWFnZSB8fCAnJyxcbiAgICAgICAgICAgIGlzVmlkZW9FbmFibGVkOiBjb3Vyc2VEZXRhaWxEYXRhLmhhc1ZpZGVvID09PSAxLFxuICAgICAgICAgICAgaXNBdHRhY2htZW50RW5hYmxlZDogY291cnNlRGV0YWlsRGF0YS5oYXNEb2N1bWVudCA9PT0gMSxcbiAgICAgICAgICAgIGNvbnRlbnRDb25maWc6IGNvdXJzZURldGFpbERhdGEuY29udGVudENvbmZpZyB8fCB7fSxcbiAgICAgICAgICAgIHRlYWNoaW5nSW5mbzogbWFwcGVkVGVhY2hpbmdJbmZvLCAvLyDkvb/nlKjmmKDlsITlkI7nmoTmlZnlrabkv6Hmga9cbiAgICAgICAgICAgIHRlYWNoaW5nTWF0ZXJpYWxzOiB0ZWFjaGluZ01hdGVyaWFscywgLy8g5L2/55So55yf5a6e55qE6ZmE5Lu25pWw5o2uXG4gICAgICAgICAgICB2aWRlb1VybDogY291cnNlRGV0YWlsRGF0YS5jb250ZW50Q29uZmlnPy52aWRlbz8udXJsIHx8ICcnLFxuICAgICAgICAgICAgdmlkZW9OYW1lOiBjb3Vyc2VEZXRhaWxEYXRhLmNvbnRlbnRDb25maWc/LnZpZGVvPy5uYW1lIHx8ICcnLFxuICAgICAgICAgICAgYXR0YWNobWVudFVybDogY291cnNlRGV0YWlsRGF0YS5jb250ZW50Q29uZmlnPy5kb2N1bWVudD8udXJsIHx8ICcnLFxuICAgICAgICAgICAgYXR0YWNobWVudE5hbWU6IGNvdXJzZURldGFpbERhdGEuY29udGVudENvbmZpZz8uZG9jdW1lbnQ/Lm5hbWUgfHwgJydcbiAgICAgICAgICB9KSk7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgY29uc29sZS5lcnJvcign4p2MIOiOt+WPluivvueoi+ivpuaDheWksei0pTonLCByZXMubWVzc2FnZSk7XG4gICAgICAgICAgLy8g5L2/55So5Z+656GA5pWw5o2u5L2c5Li65ZCO5aSHXG4gICAgICAgICAgc2V0Q291cnNlRGV0YWlsKHByZXYgPT4gKHtcbiAgICAgICAgICAgIC4uLnByZXYsXG4gICAgICAgICAgICB0aXRsZTogc2VsZWN0ZWRDb3Vyc2UudGl0bGUsXG4gICAgICAgICAgICBkZXNjcmlwdGlvbjogc2VsZWN0ZWRDb3Vyc2UuZGVzY3JpcHRpb24sXG4gICAgICAgICAgICBjb3ZlckltYWdlOiBzZWxlY3RlZENvdXJzZS5jb3ZlckltYWdlIHx8ICcnLFxuICAgICAgICAgICAgaXNWaWRlb0VuYWJsZWQ6IHNlbGVjdGVkQ291cnNlLmhhc1ZpZGVvID09PSAxLFxuICAgICAgICAgICAgaXNBdHRhY2htZW50RW5hYmxlZDogc2VsZWN0ZWRDb3Vyc2UuaGFzRG9jdW1lbnQgPT09IDEsXG4gICAgICAgICAgICB0ZWFjaGluZ01hdGVyaWFsczogW10gLy8g5riF56m66ZmE5Lu25YiX6KGoXG4gICAgICAgICAgfSkpO1xuICAgICAgICB9XG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCfinYwg6I635Y+W6K++56iL6K+m5oOF5byC5bi4OicsIGVycm9yKTtcbiAgICAgICAgLy8g5L2/55So5Z+656GA5pWw5o2u5L2c5Li65ZCO5aSHXG4gICAgICAgIHNldENvdXJzZURldGFpbChwcmV2ID0+ICh7XG4gICAgICAgICAgLi4ucHJldixcbiAgICAgICAgICB0aXRsZTogc2VsZWN0ZWRDb3Vyc2UudGl0bGUsXG4gICAgICAgICAgZGVzY3JpcHRpb246IHNlbGVjdGVkQ291cnNlLmRlc2NyaXB0aW9uLFxuICAgICAgICAgIGNvdmVySW1hZ2U6IHNlbGVjdGVkQ291cnNlLmNvdmVySW1hZ2UgfHwgJycsXG4gICAgICAgICAgaXNWaWRlb0VuYWJsZWQ6IHNlbGVjdGVkQ291cnNlLmhhc1ZpZGVvID09PSAxLFxuICAgICAgICAgIGlzQXR0YWNobWVudEVuYWJsZWQ6IHNlbGVjdGVkQ291cnNlLmhhc0RvY3VtZW50ID09PSAxLFxuICAgICAgICAgIHRlYWNoaW5nTWF0ZXJpYWxzOiBbXSAvLyDmuIXnqbrpmYTku7bliJfooahcbiAgICAgICAgfSkpO1xuICAgICAgfVxuICAgIH1cbiAgfTtcblxuICAvLyDojrflj5bpgInkuK3nmoTor77nqItcbiAgY29uc3QgZ2V0U2VsZWN0ZWRDb3Vyc2UgPSAoKSA9PiB7XG4gICAgcmV0dXJuIGNvdXJzZUxpc3QuZmluZChjb3Vyc2UgPT4gY291cnNlLmlkID09PSBzZWxlY3RlZENvdXJzZUlkKTtcbiAgfTtcblxuICBpZiAoIWlzVmlzaWJsZSkgcmV0dXJuIG51bGw7XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImNvdXJzZS1saXN0LW1vZGFsLW92ZXJsYXlcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY291cnNlLWxpc3QtbW9kYWxcIj5cbiAgICAgICAgey8qIOWktOmDqCAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb3Vyc2UtbGlzdC1oZWFkZXJcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvdXJzZS1saXN0LXRpdGxlLXNlY3Rpb25cIj5cbiAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJjb3Vyc2UtbGlzdC10aXRsZVwiPuivvueoi+WIl+ihqDwvaDI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvdXJzZS1saXN0LWFjdGlvbnNcIj5cbiAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9e3Nob3dTZXR0aW5nc1BhbmVsfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGNvdXJzZS1saXN0LXNldHRpbmdzLWJ0biAke3JpZ2h0UGFuZWxUeXBlID09PSAnc2V0dGluZ3MnID8gJ2FjdGl2ZScgOiAnJ31gfVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPFNldHRpbmdzIGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPlxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgPGJ1dHRvbiBvbkNsaWNrPXthZGROZXdDb3Vyc2V9IGNsYXNzTmFtZT1cImNvdXJzZS1saXN0LWFkZC1idG5cIj5cbiAgICAgICAgICAgICAgICA8UGx1cyBjbGFzc05hbWU9XCJ3LTQgaC00XCIgLz5cbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8YnV0dG9uIG9uQ2xpY2s9e29uQ2xvc2V9IGNsYXNzTmFtZT1cImNvdXJzZS1saXN0LWNsb3NlLWJ0blwiPlxuICAgICAgICAgICAgPFggY2xhc3NOYW1lPVwidy02IGgtNlwiIC8+XG4gICAgICAgICAgPC9idXR0b24+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiDkuLvopoHlhoXlrrkgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY291cnNlLWxpc3QtY29udGVudFwiPlxuICAgICAgICAgIHsvKiDlt6bkvqfor77nqIvliJfooaggKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb3Vyc2UtbGlzdC1zaWRlYmFyXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvdXJzZS1saXN0LWl0ZW1zXCI+XG4gICAgICAgICAgICAgIHtsb2FkaW5nID8gKFxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY291cnNlLWxpc3QtbG9hZGluZ1wiPlxuICAgICAgICAgICAgICAgICAgPHA+5Yqg6L295LitLi4uPC9wPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApIDogY291cnNlTGlzdC5sZW5ndGggPT09IDAgPyAoXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb3Vyc2UtbGlzdC1lbXB0eVwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb3Vyc2UtbGlzdC1lbXB0eS1pY29uXCI+XG4gICAgICAgICAgICAgICAgICAgIDxCb29rT3BlbiBjbGFzc05hbWU9XCJ3LTEyIGgtMTIgdGV4dC1ncmF5LTMwMFwiIC8+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJjb3Vyc2UtbGlzdC1lbXB0eS10aXRsZVwiPuaaguaXoOivvuaXtjwvaDM+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJjb3Vyc2UtbGlzdC1lbXB0eS1kZXNjcmlwdGlvblwiPlxuICAgICAgICAgICAgICAgICAgICDngrnlh7vlj7PkuIrop5LnmoQgKyDmjInpkq7mt7vliqDnrKzkuIDkuKror77ml7ZcbiAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17YWRkTmV3Q291cnNlfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJjb3Vyc2UtbGlzdC1lbXB0eS1idG5cIlxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICA8UGx1cyBjbGFzc05hbWU9XCJ3LTQgaC00XCIgLz5cbiAgICAgICAgICAgICAgICAgICAg5re75Yqg6K++5pe2XG4gICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICBjb3Vyc2VMaXN0Lm1hcCgoY291cnNlKSA9PiAoXG4gICAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICAgIGtleT17Y291cnNlLmlkfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2Bjb3Vyc2UtbGlzdC1pdGVtICR7c2VsZWN0ZWRDb3Vyc2VJZCA9PT0gY291cnNlLmlkID8gJ2FjdGl2ZScgOiAnJ31gfVxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzaG93Q291cnNlUGFuZWwoY291cnNlLmlkKX1cbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb3Vyc2UtbGlzdC1pdGVtLWNvbnRlbnRcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJjb3Vyc2UtbGlzdC1pdGVtLXRleHRcIj57Y291cnNlLnRpdGxlfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e2Bjb3Vyc2Utc3RhdHVzLWJhZGdlICR7Y291cnNlLnN0YXR1cyA9PT0gMSA/ICdwdWJsaXNoZWQnIDogJ2RyYWZ0J31gfT5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtjb3Vyc2Uuc3RhdHVzID09PSAxID8gJ+W3suWPkeW4gycgOiAn5pyq5Y+R5biDJ31cbiAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KGUpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGUuc3RvcFByb3BhZ2F0aW9uKCk7XG4gICAgICAgICAgICAgICAgICAgICAgICBzaG93RGVsZXRlQ29uZmlybShjb3Vyc2UuaWQpO1xuICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiY291cnNlLWxpc3QtaXRlbS1kZWxldGVcIlxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgPFggY2xhc3NOYW1lPVwidy0zIGgtM1wiIC8+XG4gICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgKSlcbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIOWPs+S+p+e8lui+keWMuuWfnyAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvdXJzZS1saXN0LWVkaXQtYXJlYVwiPlxuICAgICAgICAgICAge3JpZ2h0UGFuZWxUeXBlID09PSAnbm9uZScgJiYgKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvdXJzZS1lZGl0LWVtcHR5XCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb3Vyc2UtZWRpdC1lbXB0eS1pY29uXCI+XG4gICAgICAgICAgICAgICAgICA8RmlsZVRleHQgY2xhc3NOYW1lPVwidy0xNiBoLTE2IHRleHQtZ3JheS0zMDBcIiAvPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJjb3Vyc2UtZWRpdC1lbXB0eS10aXRsZVwiPuaXoOivvueoi+ivpuaDhTwvaDM+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwiY291cnNlLWVkaXQtZW1wdHktZGVzY3JpcHRpb25cIj5cbiAgICAgICAgICAgICAgICAgIOeCueWHu+W3puS+p+ivvueoi+aIluiuvue9ruaMiemSruafpeeci+ivpuaDhVxuICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApfVxuXG4gICAgICAgICAgICB7cmlnaHRQYW5lbFR5cGUgPT09ICdzZXR0aW5ncycgJiYgKFxuICAgICAgICAgICAgICA8PlxuICAgICAgICAgICAgICAgIHsvKiDns7vliJfor77nqIvlsIHpnaIgKi99XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb3Vyc2Utc2VyaWVzLWNvdmVyXCI+XG4gICAgICAgICAgICAgICAgICB7c2VyaWVzQ292ZXJJbWFnZSA/IChcbiAgICAgICAgICAgICAgICAgICAgPGltZ1xuICAgICAgICAgICAgICAgICAgICAgIHNyYz17c2VyaWVzQ292ZXJJbWFnZX1cbiAgICAgICAgICAgICAgICAgICAgICBhbHQ9XCLns7vliJfor77nqIvlsIHpnaJcIlxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImNvdXJzZS1zZXJpZXMtY292ZXItaW1hZ2VcIlxuICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb3Vyc2Utc2VyaWVzLWNvdmVyLXBsYWNlaG9sZGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4+57O75YiX6K++56iL5bCB6Z2iPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICB7Lyog57O75YiX6K6+572u6KGo5Y2VICovfVxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY291cnNlLWVkaXQtZm9ybVwiPlxuICAgICAgICAgICAgICAgICAgey8qIOezu+WIl+ivvueoi+agh+mimCAqL31cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY291cnNlLWVkaXQtZmllbGRcIj5cbiAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImNvdXJzZS1lZGl0LWxhYmVsXCI+57O75YiX6K++56iL5qCH6aKYPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtlZGl0aW5nVGl0bGV9XG4gICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRFZGl0aW5nVGl0bGUoZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImNvdXJzZS1lZGl0LWlucHV0XCJcbiAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIuivt+i+k+WFpeezu+WIl+ivvueoi+agh+mimFwiXG4gICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgey8qIOivvueoi+agh+etviAqL31cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY291cnNlLWVkaXQtZmllbGRcIj5cbiAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImNvdXJzZS1lZGl0LWxhYmVsXCI+6K++56iL5qCH562+PC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgPFNlbGVjdFxuICAgICAgICAgICAgICAgICAgICAgIG1vZGU9XCJtdWx0aXBsZVwiXG4gICAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3sgd2lkdGg6ICcxMDAlJyB9fVxuICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwi6K+36YCJ5oup6K++56iL5qCH562+XCJcbiAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17c2VsZWN0ZWRUYWdzfVxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtzZXRTZWxlY3RlZFRhZ3N9XG4gICAgICAgICAgICAgICAgICAgICAgbG9hZGluZz17dGFnc0xvYWRpbmd9XG4gICAgICAgICAgICAgICAgICAgICAgb3B0aW9ucz17Y291cnNlVGFncy5tYXAodGFnID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCfwn4+377iPIOa4suafk+agh+etvumAiemhuTonLCB0YWcpO1xuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgbGFiZWw6IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBzdHlsZT17eyBjb2xvcjogdGFnLmNvbG9yIH19PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3RhZy5uYW1lfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgKSxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU6IHRhZy5pZFxuICAgICAgICAgICAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgICAgICAgICAgICB9KX1cbiAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgey8qIOiwg+ivleS/oeaBryAqL31cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBzdHlsZT17eyBmb250U2l6ZTogJzEycHgnLCBjb2xvcjogJyM2NjYnLCBtYXJnaW5Ub3A6ICc0cHgnIH19PlxuICAgICAgICAgICAgICAgICAgICAgIOiwg+ivlTog5b2T5YmN5qCH562+5pWw6YePIHtjb3Vyc2VUYWdzLmxlbmd0aH0sIOWKoOi9veeKtuaAgToge3RhZ3NMb2FkaW5nID8gJ+aYrycgOiAn5ZCmJ31cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgey8qIOivvueoi+mhueebruaIkOWRmCAqL31cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY291cnNlLWVkaXQtZmllbGRcIj5cbiAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImNvdXJzZS1lZGl0LWxhYmVsXCI+6K++56iL6aG555uu5oiQ5ZGYPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtwcm9qZWN0TWVtYmVyc31cbiAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFByb2plY3RNZW1iZXJzKGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJjb3Vyc2UtZWRpdC1pbnB1dFwiXG4gICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLor7fovpPlhaXpobnnm67miJDlkZjvvIzlpoLvvJrlvKDogIHluIjjgIHmnY7liqnmlZnjgIHnjovlkIzlraZcIlxuICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAge3JpZ2h0UGFuZWxUeXBlID09PSAnY291cnNlJyAmJiBnZXRTZWxlY3RlZENvdXJzZSgpICYmIChcbiAgICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgICB7Lyog6K++56iL6K+m57uG57yW6L6R55WM6Z2iICovfVxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY291cnNlLWRldGFpbC1lZGl0XCI+XG4gICAgICAgICAgICAgICAgICB7Lyog6aG26YOo5Yy65Z+f77ya6K++56iL5bCB6Z2i5ZKM5Z+65pys5L+h5oGvICovfVxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb3Vyc2UtZGV0YWlsLXRvcFwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvdXJzZS1kZXRhaWwtY292ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJjb3Vyc2UtY292ZXItdXBsb2FkLWFyZWFcIlxuICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQoJ2NvdmVyLXVwbG9hZC1pbnB1dCcpPy5jbGljaygpfVxuICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtjb3Vyc2VEZXRhaWwuY292ZXJJbWFnZSB8fCBnZXRTZWxlY3RlZENvdXJzZSgpPy5jb3ZlckltYWdlID8gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICA8aW1nXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc3JjPXtjb3Vyc2VEZXRhaWwuY292ZXJJbWFnZSB8fCBnZXRTZWxlY3RlZENvdXJzZSgpPy5jb3ZlckltYWdlfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFsdD1cIuivvueoi+WwgemdolwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiY291cnNlLWNvdmVyLWltYWdlXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY291cnNlLWNvdmVyLXBsYWNlaG9sZGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+54K55Ye75LiK5Lyg6K++56iL5bCB6Z2iPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICBpZD1cImNvdmVyLXVwbG9hZC1pbnB1dFwiXG4gICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwiZmlsZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICBhY2NlcHQ9XCJpbWFnZS9qcGVnLGltYWdlL2pwZyxpbWFnZS9wbmcsaW1hZ2UvZ2lmXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVDb3ZlclVwbG9hZH1cbiAgICAgICAgICAgICAgICAgICAgICAgIHN0eWxlPXt7IGRpc3BsYXk6ICdub25lJyB9fVxuICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgey8qIOS4gOmUruS4iuivvuaMiemSriAqL31cbiAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJvbmUtY2xpY2stY2xhc3MtYnRuXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgLy8gVE9ETzog5a6e546w5LiA6ZSu5LiK6K++5Yqf6IO9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCfkuIDplK7kuIror77mjInpkq7ooqvngrnlh7snKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgbm90aWZpY2F0aW9uLmluZm8oJ+S4gOmUruS4iuivvuWKn+iDveW8gOWPkeS4rS4uLicpO1xuICAgICAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICDkuIDplK7kuIror75cbiAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY291cnNlLWRldGFpbC1iYXNpY1wiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY291cnNlLWRldGFpbC1maWVsZFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGxhYmVsPuivvueoi+agh+mimDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Y291cnNlRGV0YWlsLnRpdGxlIHx8IGdldFNlbGVjdGVkQ291cnNlKCk/LnRpdGxlIHx8ICcnfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRDb3Vyc2VEZXRhaWwocHJldiA9PiAoeyAuLi5wcmV2LCB0aXRsZTogZS50YXJnZXQudmFsdWUgfSkpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHVwZGF0ZUNvdXJzZVRpdGxlKHNlbGVjdGVkQ291cnNlSWQhLCBlLnRhcmdldC52YWx1ZSk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwi6K+36L6T5YWl6K++56iL5qCH6aKYXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb3Vyc2UtZGV0YWlsLWZpZWxkXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8bGFiZWw+6K++56iL5LuL57uNPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDx0ZXh0YXJlYVxuICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Y291cnNlRGV0YWlsLmRlc2NyaXB0aW9uIHx8IGdldFNlbGVjdGVkQ291cnNlKCk/LmRlc2NyaXB0aW9uIHx8ICcnfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldENvdXJzZURldGFpbChwcmV2ID0+ICh7IC4uLnByZXYsIGRlc2NyaXB0aW9uOiBlLnRhcmdldC52YWx1ZSB9KSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwi6K+36L6T5YWl6K++56iL5LuL57uNXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgcm93cz17M31cbiAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgIHsvKiDor77nqIvotYTmupDljLrln58gKi99XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvdXJzZS1kZXRhaWwtc2VjdGlvblwiPlxuICAgICAgICAgICAgICAgICAgICA8aDM+6K++56iL6LWE5rqQPC9oMz5cblxuICAgICAgICAgICAgICAgICAgICB7Lyog6K++56iL6KeG6aKRICovfVxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvdXJzZS1yZXNvdXJjZS1pdGVtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZXNvdXJjZS1oZWFkZXItcmlnaHRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPuivvueoi+inhumikTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJzd2l0Y2hcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImNoZWNrYm94XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjaGVja2VkPXtjb3Vyc2VEZXRhaWwuaXNWaWRlb0VuYWJsZWR9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRDb3Vyc2VEZXRhaWwocHJldiA9PiAoeyAuLi5wcmV2LCBpc1ZpZGVvRW5hYmxlZDogZS50YXJnZXQuY2hlY2tlZCB9KSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInNsaWRlclwiPjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAge2NvdXJzZURldGFpbC5pc1ZpZGVvRW5hYmxlZCAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInZpZGVvLWNvbnRlbnQtYXJlYVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7Lyog5pi+56S655yf5a6e6KeG6aKR5L+h5oGvICovfVxuICAgICAgICAgICAgICAgICAgICAgICAgICB7Y291cnNlRGV0YWlsLmNvbnRlbnRDb25maWc/LnZpZGVvPy51cmwgPyAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ2aWRlby1pbmZvLXNlY3Rpb25cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidmlkZW8tcHJldmlld1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8dmlkZW9cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ2aWRlby10aHVtYm5haWxcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnRyb2xzXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcG9zdGVyPXtjb3Vyc2VEZXRhaWwuY292ZXJJbWFnZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzb3VyY2Ugc3JjPXtjb3Vyc2VEZXRhaWwuY29udGVudENvbmZpZy52aWRlby51cmx9IHR5cGU9XCJ2aWRlby9tcDRcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIOaCqOeahOa1j+iniOWZqOS4jeaUr+aMgeinhumikeaSreaUvlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3ZpZGVvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInZpZGVvLW5hbWUtY2VudGVyZWRcIj57Y291cnNlRGV0YWlsLmNvbnRlbnRDb25maWcudmlkZW8ubmFtZX08L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b24gY2xhc3NOYW1lPVwidXBsb2FkLWJ0bi1ob3Jpem9udGFsXCIgb25DbGljaz17dHJpZ2dlclZpZGVvVXBsb2FkfT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+6YeN5paw5LiK5LygPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ2aWRlby11cGxvYWQtc2VjdGlvblwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ2aWRlby1wbGFjZWhvbGRlci1jZW50ZXJlZFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInBsYXktaWNvblwiPuKWtjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uIGNsYXNzTmFtZT1cInVwbG9hZC1idG4taG9yaXpvbnRhbFwiIG9uQ2xpY2s9e3RyaWdnZXJWaWRlb1VwbG9hZH0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPuS4iuS8oOinhumikTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgey8qIOivvueoi+mZhOS7tiAqL31cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb3Vyc2UtcmVzb3VyY2UtaXRlbVwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVzb3VyY2UtaGVhZGVyLXJpZ2h0XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj7or77nqIvpmYTku7Y8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwic3dpdGNoXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJjaGVja2JveFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2hlY2tlZD17Y291cnNlRGV0YWlsLmlzQXR0YWNobWVudEVuYWJsZWR9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRDb3Vyc2VEZXRhaWwocHJldiA9PiAoeyAuLi5wcmV2LCBpc0F0dGFjaG1lbnRFbmFibGVkOiBlLnRhcmdldC5jaGVja2VkIH0pKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwic2xpZGVyXCI+PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICB7Y291cnNlRGV0YWlsLmlzQXR0YWNobWVudEVuYWJsZWQgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhdHRhY2htZW50LWNvbnRlbnQtYXJlYVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7Lyog5pi+56S655yf5a6e6ZmE5Lu25L+h5oGvICovfVxuICAgICAgICAgICAgICAgICAgICAgICAgICB7Y291cnNlRGV0YWlsLmNvbnRlbnRDb25maWc/LmRvY3VtZW50Py51cmwgPyAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhdHRhY2htZW50LWluZm8tc2VjdGlvblwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhdHRhY2htZW50LXByZXZpZXdcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJkb2N1bWVudC1pY29uXCI+8J+ThDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImF0dGFjaG1lbnQtZGV0YWlsc1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYXR0YWNobWVudC1uYW1lXCI+e2NvdXJzZURldGFpbC5jb250ZW50Q29uZmlnLmRvY3VtZW50Lm5hbWV9PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uIGNsYXNzTmFtZT1cInVwbG9hZC1idG4taG9yaXpvbnRhbFwiIG9uQ2xpY2s9e3RyaWdnZXJBdHRhY2htZW50VXBsb2FkfT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+6YeN5paw5LiK5LygPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhdHRhY2htZW50LXVwbG9hZC1zZWN0aW9uXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uIGNsYXNzTmFtZT1cInVwbG9hZC1idG4taG9yaXpvbnRhbFwiIG9uQ2xpY2s9e3RyaWdnZXJBdHRhY2htZW50VXBsb2FkfT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+5LiK5Lyg6ZmE5Lu2PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICB7Lyog5pWZ5a2m6ZmE5Lu2ICovfVxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvdXJzZS1yZXNvdXJjZS1pdGVtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZXNvdXJjZS1oZWFkZXItc2ltcGxlXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj7mlZnlrabpmYTku7Y8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZWFjaGluZy1tYXRlcmlhbHNcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b24gY2xhc3NOYW1lPVwiYWRkLW1hdGVyaWFsLWJ0blwiIG9uQ2xpY2s9e3RyaWdnZXJUZWFjaGluZ01hdGVyaWFsVXBsb2FkfT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+Kzwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+5LiK5LygPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICB7Y291cnNlRGV0YWlsLnRlYWNoaW5nTWF0ZXJpYWxzICYmIGNvdXJzZURldGFpbC50ZWFjaGluZ01hdGVyaWFscy5sZW5ndGggPiAwID8gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICBjb3Vyc2VEZXRhaWwudGVhY2hpbmdNYXRlcmlhbHMubWFwKChtYXRlcmlhbCwgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGtleT17aW5kZXh9IGNsYXNzTmFtZT1cIm1hdGVyaWFsLWl0ZW1cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm1hdGVyaWFsLW5hbWVcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKG1hdGVyaWFsLnVybCkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgd2luZG93Lm9wZW4obWF0ZXJpYWwudXJsLCAnX2JsYW5rJyk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGN1cnNvcjogbWF0ZXJpYWwudXJsID8gJ3BvaW50ZXInIDogJ2RlZmF1bHQnLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiBtYXRlcmlhbC51cmwgPyAnIzE4OTBmZicgOiAnaW5oZXJpdCcsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGV4dERlY29yYXRpb246IG1hdGVyaWFsLnVybCA/ICd1bmRlcmxpbmUnIDogJ25vbmUnXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlPXttYXRlcmlhbC51cmwgPyAn54K55Ye75LiL6L296ZmE5Lu2JyA6IG1hdGVyaWFsLm5hbWV9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIPCfk44ge21hdGVyaWFsLm5hbWV9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInJlbW92ZS1tYXRlcmlhbC1idG5cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiByZW1vdmVUZWFjaGluZ01hdGVyaWFsKGluZGV4KX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU9XCLliKDpmaTpmYTku7ZcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICDDl1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICkpXG4gICAgICAgICAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImVtcHR5LW1hdGVyaWFscy1oaW50XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gc3R5bGU9e3sgY29sb3I6ICcjOTk5JywgZm9udFNpemU6ICcxNHB4JyB9fT7mmoLml6DmlZnlrabpmYTku7Y8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgey8qIOivvueoi+WGheWuueWMuuWfnyAqL31cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY291cnNlLWRldGFpbC1zZWN0aW9uXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic2VjdGlvbi1oZWFkZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8aDM+6K++56iL5YaF5a65PC9oMz5cbiAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJhZGQtY29udGVudC1zZWN0aW9uLWJ0blwiXG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXthZGRUZWFjaGluZ0luZm9JdGVtfVxuICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU9XCLmt7vliqDor77nqIvlhoXlrrlcIlxuICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICsg5re75Yqg6K++56iL5YaF5a65XG4gICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvdXJzZS1jb250ZW50LWFyZWFcIj5cbiAgICAgICAgICAgICAgICAgICAgICB7Y291cnNlRGV0YWlsLnRlYWNoaW5nSW5mbyAmJiBjb3Vyc2VEZXRhaWwudGVhY2hpbmdJbmZvLmxlbmd0aCA+IDAgPyAoXG4gICAgICAgICAgICAgICAgICAgICAgICBjb3Vyc2VEZXRhaWwudGVhY2hpbmdJbmZvLm1hcCgoaW5mbywgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBrZXk9e2luZGV4fSBjbGFzc05hbWU9XCJ0ZWFjaGluZy1pbmZvLWNhcmRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNhcmQtaGVhZGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJjYXJkLXRpdGxlXCI+6K++56iL5YaF5a65IHtpbmRleCArIDF9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJyZW1vdmUtY2FyZC1idG5cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiByZW1vdmVUZWFjaGluZ0luZm9JdGVtKGluZGV4KX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU9XCLliKDpmaTmraTlhoXlrrlcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICDDl1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjYXJkLWNvbnRlbnRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaW5wdXQtZ3JvdXBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGxhYmVsPuagh+mimDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17aW5mby50aXRsZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHVwZGF0ZVRlYWNoaW5nSW5mb1RpdGxlKGluZGV4LCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLor7fovpPlhaXmoIfpopjvvIzlpoLvvJrmlZnlrabnm67moIfjgIHmlZnlrabmlrnms5XnrYlcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRpdGxlLWlucHV0XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJpbnB1dC1ncm91cFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8bGFiZWw+5YaF5a65PC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHRleHRhcmVhXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2luZm8uY29udGVudH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHVwZGF0ZVRlYWNoaW5nSW5mb0NvbnRlbnQoaW5kZXgsIGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIuivt+i+k+WFpeWFt+S9k+WGheWuue+8jOWkmuS4quWGheWuuemhueWPr+eUqOaNouihjOWIhumalFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiY29udGVudC10ZXh0YXJlYVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcm93cz17NH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICkpXG4gICAgICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZW1wdHktY29udGVudC1oaW50XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxwPuaaguaXoOivvueoi+WGheWuue+8jOeCueWHu+WPs+S4iuinkuaMiemSrua3u+WKoDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgIHsvKiDph43mlrDkuIror74gKi99XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvdXJzZS1kZXRhaWwtc2VjdGlvblwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm9uZS1rZXktc2VjdGlvblwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwib25lLWtleS1pdGVtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj7lvIDlp4vkuIror748L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwic3dpdGNoXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJjaGVja2JveFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2hlY2tlZD17Y291cnNlRGV0YWlsLmlzT25lS2V5T3Blbn1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldENvdXJzZURldGFpbChwcmV2ID0+ICh7IC4uLnByZXYsIGlzT25lS2V5T3BlbjogZS50YXJnZXQuY2hlY2tlZCB9KSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInNsaWRlclwiPjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgICB7Y291cnNlRGV0YWlsLmlzT25lS2V5T3BlbiAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICA8PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm9uZS1rZXktaXRlbVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPuWIhumFjeenr+acqDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwic3dpdGNoXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImNoZWNrYm94XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2hlY2tlZD17Y291cnNlRGV0YWlsLmlzRGlzdHJpYnV0aW9uRW5hYmxlZH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRDb3Vyc2VEZXRhaWwocHJldiA9PiAoeyAuLi5wcmV2LCBpc0Rpc3RyaWJ1dGlvbkVuYWJsZWQ6IGUudGFyZ2V0LmNoZWNrZWQgfSkpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInNsaWRlclwiPjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtjb3Vyc2VEZXRhaWwuaXNEaXN0cmlidXRpb25FbmFibGVkICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmxvY2stdGVtcGxhdGUtc2VjdGlvblwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwic2VsZWN0LXRlbXBsYXRlLWJ0blwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlT3BlblRlbXBsYXRlUGlja2VyfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg6YCJ5oup56ev5pyo5qih5p2/XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNlbGVjdGVkLXRlbXBsYXRlLWRpc3BsYXlcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj57Y291cnNlRGV0YWlsLnNlbGVjdGVkVGVtcGxhdGUgfHwgJ+mAieS4reeahOaooeadv+WQjeWtlyd9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwib25lLWtleS1pdGVtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+5YiG6YWN6IO96YePPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJzd2l0Y2hcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwiY2hlY2tib3hcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjaGVja2VkPXtjb3Vyc2VEZXRhaWwuaXNEaXN0cmlidXRpb25XYXRlcn1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRDb3Vyc2VEZXRhaWwocHJldiA9PiAoeyAuLi5wcmV2LCBpc0Rpc3RyaWJ1dGlvbldhdGVyOiBlLnRhcmdldC5jaGVja2VkIH0pKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJzbGlkZXJcIj48L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge2NvdXJzZURldGFpbC5pc0Rpc3RyaWJ1dGlvbldhdGVyICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImVuZXJneS1pbnB1dC1zZWN0aW9uXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj7pnIDopoHog73ph4/vvJo8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Y291cnNlRGV0YWlsLnJlcXVpcmVkRW5lcmd5IHx8ICcnfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldENvdXJzZURldGFpbChwcmV2ID0+ICh7IC4uLnByZXYsIHJlcXVpcmVkRW5lcmd5OiBlLnRhcmdldC52YWx1ZSB9KSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwi6K+36L6T5YWl6ZyA6KaB55qE6IO96YeP5YC8XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZW5lcmd5LWlucHV0XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJvbmUta2V5LWl0ZW1cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj7liIbphY3ku7vliqE8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cInN3aXRjaFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJjaGVja2JveFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNoZWNrZWQ9e2NvdXJzZURldGFpbC5pc0Rpc3RyaWJ1dGlvbkxpbWl0fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldENvdXJzZURldGFpbChwcmV2ID0+ICh7IC4uLnByZXYsIGlzRGlzdHJpYnV0aW9uTGltaXQ6IGUudGFyZ2V0LmNoZWNrZWQgfSkpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInNsaWRlclwiPjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICAgICAgICB7Lyog5Lu75Yqh6YWN572u6KGo5Y2VICovfVxuICAgICAgICAgICAgICAgICAgICAgICAgICB7Y291cnNlRGV0YWlsLmlzRGlzdHJpYnV0aW9uTGltaXQgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGFzay1jb25maWctZm9ybVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgey8qIOS7u+WKoeWQjeensOWSjOaMgee7reWkqeaVsCAqL31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGFzay1jb25maWctcm93XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGFzay1jb25maWctZmllbGRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8bGFiZWw+5Lu75Yqh5ZCN56ewOjwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Y291cnNlRGV0YWlsLnRhc2tDb25maWcudGFza05hbWV9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldENvdXJzZURldGFpbChwcmV2ID0+ICh7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC4uLnByZXYsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRhc2tDb25maWc6IHsgLi4ucHJldi50YXNrQ29uZmlnLCB0YXNrTmFtZTogZS50YXJnZXQudmFsdWUgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSkpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLor7fovpPlhaXku7vliqHlkI3np7BcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRhc2stY29uZmlnLWZpZWxkXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGxhYmVsPuS7u+WKoeaMgee7reWkqeaVsDo8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cIm51bWJlclwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Y291cnNlRGV0YWlsLnRhc2tDb25maWcudGFza0R1cmF0aW9ufVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRDb3Vyc2VEZXRhaWwocHJldiA9PiAoe1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuLi5wcmV2LFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0YXNrQ29uZmlnOiB7IC4uLnByZXYudGFza0NvbmZpZywgdGFza0R1cmF0aW9uOiBlLnRhcmdldC52YWx1ZSB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIuivt+i+k+WFpeWkqeaVsFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgey8qIOS7u+WKoeaPj+i/sCAqL31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGFzay1jb25maWctZmllbGQgdGFzay1jb25maWctZnVsbFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8bGFiZWw+5Lu75Yqh5o+P6L+wOjwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDx0ZXh0YXJlYVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtjb3Vyc2VEZXRhaWwudGFza0NvbmZpZy50YXNrRGVzY3JpcHRpb259XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRDb3Vyc2VEZXRhaWwocHJldiA9PiAoe1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4ucHJldixcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRhc2tDb25maWc6IHsgLi4ucHJldi50YXNrQ29uZmlnLCB0YXNrRGVzY3JpcHRpb246IGUudGFyZ2V0LnZhbHVlIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLor7fovpPlhaXku7vliqHmj4/ov7BcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJvd3M9ezR9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgey8qIOS7u+WKoeiHquivhOmhuSAqL31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGFzay1jb25maWctZmllbGQgdGFzay1jb25maWctZnVsbFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8bGFiZWw+5Lu75Yqh6Ieq6K+E6aG5OiA8c3BhbiBjbGFzc05hbWU9XCJpdGVtLW51bWJlclwiPntjb3Vyc2VEZXRhaWwudGFza0NvbmZpZy5zZWxmQXNzZXNzbWVudEl0ZW1zLmxlbmd0aH08L3NwYW4+PC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2NvdXJzZURldGFpbC50YXNrQ29uZmlnLnNlbGZBc3Nlc3NtZW50SXRlbXMubWFwKChpdGVtLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYga2V5PXtpbmRleH0gY2xhc3NOYW1lPVwic2VsZi1hc3Nlc3NtZW50LWl0ZW1cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtpdGVtfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBuZXdJdGVtcyA9IFsuLi5jb3Vyc2VEZXRhaWwudGFza0NvbmZpZy5zZWxmQXNzZXNzbWVudEl0ZW1zXTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBuZXdJdGVtc1tpbmRleF0gPSBlLnRhcmdldC52YWx1ZTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRDb3Vyc2VEZXRhaWwocHJldiA9PiAoe1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4ucHJldixcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRhc2tDb25maWc6IHsgLi4ucHJldi50YXNrQ29uZmlnLCBzZWxmQXNzZXNzbWVudEl0ZW1zOiBuZXdJdGVtcyB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSkpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIuivt+i+k+WFpeiHquivhOmhueWGheWuuVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImFkZC1hc3Nlc3NtZW50LWJ0blwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0Q291cnNlRGV0YWlsKHByZXYgPT4gKHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC4uLnByZXYsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0YXNrQ29uZmlnOiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC4uLnByZXYudGFza0NvbmZpZyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2VsZkFzc2Vzc21lbnRJdGVtczogWy4uLnByZXYudGFza0NvbmZpZy5zZWxmQXNzZXNzbWVudEl0ZW1zLCAnJ11cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICArXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsvKiDku7vliqHlj4LogIPkvZzlk4EgKi99XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRhc2stY29uZmlnLWZpZWxkIHRhc2stY29uZmlnLWZ1bGxcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGxhYmVsPuS7u+WKoeWPguiAg+S9nOWTgTo8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlZmVyZW5jZS13b3Jrcy1zZWN0aW9uXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3b3Jrcy1zZWN0aW9uXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJoZWxwLXRleHRcIj7pgInmi6nkvZzlk4HkvZzkuLrku7vliqHlj4LogIPotYTmlpnvvIjlj6/lpJrpgInvvIk8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInJlbGF0aXZlIHdvcmtzLXNjcm9sbC13cmFwcGVyXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBtaW5IZWlnaHQ6ICcyMDBweCcsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY3Vyc29yOiAnZ3JhYicsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdXNlclNlbGVjdDogJ25vbmUnXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtsb2FkaW5nV29ya3MgPyAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJsb2FkaW5nLWNvbnRhaW5lclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJsb2FkaW5nLXNwaW5uZXJcIj48L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPuWKoOi9veS4rS4uLjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSA6IHdvcmtzLmxlbmd0aCA+IDAgPyAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwid29ya3MtaG9yaXpvbnRhbC1zY3JvbGxcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25XaGVlbD17aGFuZGxlV2hlZWx9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3dvcmtzLm1hcCgod29yaykgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAga2V5PXt3b3JrLmlkfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHdvcmstY2FyZCAke3NlbGVjdGVkV29ya0lkcy5pbmNsdWRlcyh3b3JrLmlkKSA/ICdzZWxlY3RlZCcgOiAnJ31gfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZVdvcmtTZWxlY3Qod29yay5pZCl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIndvcmstaW1hZ2VcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHt3b3JrLmNvdmVySW1hZ2UgfHwgd29yay5zY3JlZW5TaG90SW1hZ2UgPyAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxpbWdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzcmM9e3dvcmsuY292ZXJJbWFnZSB8fCB3b3JrLnNjcmVlblNob3RJbWFnZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBhbHQ9e3dvcmsudGl0bGV9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25FcnJvcj17KGUpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHRhcmdldCA9IGUuY3VycmVudFRhcmdldCBhcyBIVE1MSW1hZ2VFbGVtZW50O1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGFyZ2V0LnN0eWxlLmRpc3BsYXkgPSAnbm9uZSc7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBuZXh0RWxlbWVudCA9IHRhcmdldC5uZXh0RWxlbWVudFNpYmxpbmcgYXMgSFRNTEVsZW1lbnQ7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAobmV4dEVsZW1lbnQpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbmV4dEVsZW1lbnQuc3R5bGUuZGlzcGxheSA9ICdmbGV4JztcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSA6IG51bGx9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIndvcmstcGxhY2Vob2xkZXJcIiBzdHlsZT17eyBkaXNwbGF5OiB3b3JrLmNvdmVySW1hZ2UgfHwgd29yay5zY3JlZW5TaG90SW1hZ2UgPyAnbm9uZScgOiAnZmxleCcgfX0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIOS9nOWTgVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3b3JrLXRpdGxlXCI+e3dvcmsudGl0bGUgfHwgd29yay5uYW1lIHx8IHdvcmsud29ya05hbWUgfHwgJ+acquWRveWQjeS9nOWTgSd9PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3NlbGVjdGVkV29ya0lkcy5pbmNsdWRlcyh3b3JrLmlkKSAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNlbGVjdGVkLWluZGljYXRvclwiPuKckzwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJlbXB0eS1wbGFjZWhvbGRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJlbXB0eS10ZXh0XCI+5pqC5peg5L2c5ZOBPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgey8qIOS7u+WKoeWPguiAg+i1hOa6kCAqL31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGFzay1jb25maWctZmllbGQgdGFzay1jb25maWctZnVsbFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8bGFiZWw+5Lu75Yqh5Y+C6ICD6LWE5rqQOjwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVmZXJlbmNlLXJlc291cmNlcy1zZWN0aW9uXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWZlcmVuY2UtcmVzb3VyY2VzLWdyaWRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImJ1dHRvblwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInVwbG9hZC1yZXNvdXJjZS1idG5cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8g6Kem5Y+R5paH5Lu25LiK5LygXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgaW5wdXQgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCdpbnB1dCcpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlucHV0LnR5cGUgPSAnZmlsZSc7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaW5wdXQuYWNjZXB0ID0gJy5wZGYsLmRvYywuZG9jeCwucHB0LC5wcHR4LC54bHMsLnhsc3gsLnR4dCwuanBnLC5wbmcsLmdpZic7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaW5wdXQub25jaGFuZ2UgPSAoZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgZmlsZSA9IChlLnRhcmdldCBhcyBIVE1MSW5wdXRFbGVtZW50KS5maWxlcz8uWzBdO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGZpbGUpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0Q291cnNlRGV0YWlsKHByZXYgPT4gKHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuLi5wcmV2LFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRhc2tDb25maWc6IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC4uLnByZXYudGFza0NvbmZpZyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJlZmVyZW5jZVJlc291cmNlczogW1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuLi5wcmV2LnRhc2tDb25maWcucmVmZXJlbmNlUmVzb3VyY2VzLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7IHR5cGU6ICdmaWxlJywgbmFtZTogZmlsZS5uYW1lIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIF1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlucHV0LmNsaWNrKCk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxQbHVzIHNpemU9ezI0fSAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICDkuIrkvKBcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2NvdXJzZURldGFpbC50YXNrQ29uZmlnLnJlZmVyZW5jZVJlc291cmNlcy5tYXAoKHJlc291cmNlLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGtleT17aW5kZXh9IGNsYXNzTmFtZT1cInJlZmVyZW5jZS1yZXNvdXJjZS1pdGVtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+e3Jlc291cmNlLm5hbWV9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicmVtb3ZlLXJlc291cmNlLWJ0blwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IG5ld1Jlc291cmNlcyA9IGNvdXJzZURldGFpbC50YXNrQ29uZmlnLnJlZmVyZW5jZVJlc291cmNlcy5maWx0ZXIoKF8sIGkpID0+IGkgIT09IGluZGV4KTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0Q291cnNlRGV0YWlsKHByZXYgPT4gKHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuLi5wcmV2LFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRhc2tDb25maWc6IHsgLi4ucHJldi50YXNrQ29uZmlnLCByZWZlcmVuY2VSZXNvdXJjZXM6IG5ld1Jlc291cmNlcyB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgw5dcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIOW6lemDqOaMiemSriAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb3Vyc2UtbGlzdC1mb290ZXJcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvdXJzZS1saXN0LWZvb3Rlci1sZWZ0XCI+XG4gICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZVB1Ymxpc2h9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImNvdXJzZS1saXN0LWJ0biBjb3Vyc2UtbGlzdC1idG4tcHVibGlzaFwiXG4gICAgICAgICAgICAgIGRpc2FibGVkPXtjb3Vyc2VMaXN0Lmxlbmd0aCA9PT0gMCB8fCBpc1B1Ymxpc2hpbmdTZXJpZXMgfHwgc2VyaWVzU3RhdHVzID09PSAxfVxuICAgICAgICAgICAgICB0aXRsZT17XG4gICAgICAgICAgICAgICAgc2VyaWVzU3RhdHVzID09PSAxXG4gICAgICAgICAgICAgICAgICA/ICfns7vliJfor77nqIvlt7Llj5HluIMnXG4gICAgICAgICAgICAgICAgICA6IGNvdXJzZUxpc3QubGVuZ3RoID09PSAwXG4gICAgICAgICAgICAgICAgICAgID8gJ+WPkeW4g+Wksei0pe+8muivvueoi+ezu+WIl+S4reiHs+WwkemcgOimgeWMheWQq+S4gOS4quivvueoiydcbiAgICAgICAgICAgICAgICAgICAgOiBpc1B1Ymxpc2hpbmdTZXJpZXNcbiAgICAgICAgICAgICAgICAgICAgICA/ICfmraPlnKjlj5HluIPns7vliJfor77nqIsuLi4nXG4gICAgICAgICAgICAgICAgICAgICAgOiAn5Y+R5biD57O75YiX6K++56iLJ1xuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIHtzZXJpZXNTdGF0dXMgPT09IDFcbiAgICAgICAgICAgICAgICA/ICflt7Llj5HluIMnXG4gICAgICAgICAgICAgICAgOiBpc1B1Ymxpc2hpbmdTZXJpZXNcbiAgICAgICAgICAgICAgICAgID8gJ+ato+WcqOWPkeW4gy4uLidcbiAgICAgICAgICAgICAgICAgIDogJ+WPkeW4g+ezu+WIl+ivvueoiydcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb3Vyc2UtbGlzdC1mb290ZXItcmlnaHRcIj5cbiAgICAgICAgICAgIDxidXR0b24gb25DbGljaz17aGFuZGxlRXhpdEVkaXR9IGNsYXNzTmFtZT1cImNvdXJzZS1saXN0LWJ0biBjb3Vyc2UtbGlzdC1idG4tZXhpdFwiPlxuICAgICAgICAgICAgICDpgIDlh7rnvJbovpHmqKHlvI9cbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVQdWJsaXNoQ291cnNlfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJjb3Vyc2UtbGlzdC1idG4gY291cnNlLWxpc3QtYnRuLXB1Ymxpc2gtY291cnNlXCJcbiAgICAgICAgICAgICAgZGlzYWJsZWQ9eyFzZWxlY3RlZENvdXJzZUlkIHx8IGNvdXJzZUxpc3QuZmluZChjID0+IGMuaWQgPT09IHNlbGVjdGVkQ291cnNlSWQpPy5zdGF0dXMgPT09IDEgfHwgaXNQdWJsaXNoaW5nfVxuICAgICAgICAgICAgICB0aXRsZT17XG4gICAgICAgICAgICAgICAgIXNlbGVjdGVkQ291cnNlSWRcbiAgICAgICAgICAgICAgICAgID8gJ+ivt+WFiOmAieaLqeimgeWPkeW4g+eahOivvueoiydcbiAgICAgICAgICAgICAgICAgIDogY291cnNlTGlzdC5maW5kKGMgPT4gYy5pZCA9PT0gc2VsZWN0ZWRDb3Vyc2VJZCk/LnN0YXR1cyA9PT0gMVxuICAgICAgICAgICAgICAgICAgICA/ICfor6Xor77nqIvlt7Llj5HluIMnXG4gICAgICAgICAgICAgICAgICAgIDogaXNQdWJsaXNoaW5nXG4gICAgICAgICAgICAgICAgICAgICAgPyAn5q2j5Zyo5Y+R5biD6K++56iLLi4uJ1xuICAgICAgICAgICAgICAgICAgICAgIDogJ+WPkeW4g+mAieS4reeahOivvueoiydcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICB7aXNQdWJsaXNoaW5nID8gJ+ato+WcqOWPkeW4gy4uLicgOiAn5Y+R5biD6K++56iLJ31cbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVTYXZlfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJjb3Vyc2UtbGlzdC1idG4gY291cnNlLWxpc3QtYnRuLXNhdmVcIlxuICAgICAgICAgICAgICBkaXNhYmxlZD17dXBsb2FkaW5nRmlsZXMuc2l6ZSA+IDAgfHwgaXNDcmVhdGluZyB8fCBjb3Vyc2VMaXN0Lmxlbmd0aCA9PT0gMH1cbiAgICAgICAgICAgICAgdGl0bGU9e1xuICAgICAgICAgICAgICAgIGNvdXJzZUxpc3QubGVuZ3RoID09PSAwXG4gICAgICAgICAgICAgICAgICA/ICfor7flhYjmt7vliqDor77nqIvlhoXlrrknXG4gICAgICAgICAgICAgICAgICA6IHVwbG9hZGluZ0ZpbGVzLnNpemUgPiAwXG4gICAgICAgICAgICAgICAgICAgID8gJ+acieaWh+S7tuato+WcqOS4iuS8oOS4re+8jOivt+etieW+heS4iuS8oOWujOaIkCdcbiAgICAgICAgICAgICAgICAgICAgOiBpc0NyZWF0aW5nXG4gICAgICAgICAgICAgICAgICAgICAgPyAoY291cnNlTGlzdC5zb21lKGMgPT4gYy5pZCA+IDEwMDAwMDApID8gJ+ato+WcqOWIm+W7uuivvueoiy4uLicgOiAn5q2j5Zyo5L+d5a2Y6K++56iLLi4uJylcbiAgICAgICAgICAgICAgICAgICAgICA6IChjb3Vyc2VMaXN0LnNvbWUoYyA9PiBjLmlkID4gMTAwMDAwMCkgPyAn5Yib5bu66K++56iLJyA6ICfkv53lrZjor77nqIsnKVxuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIHt1cGxvYWRpbmdGaWxlcy5zaXplID4gMFxuICAgICAgICAgICAgICAgID8gJ+S4iuS8oOS4rS4uLidcbiAgICAgICAgICAgICAgICA6IGlzQ3JlYXRpbmdcbiAgICAgICAgICAgICAgICAgID8gKGNvdXJzZUxpc3Quc29tZShjID0+IGMuaWQgPiAxMDAwMDAwKSA/ICfmraPlnKjliJvlu7ouLi4nIDogJ+ato+WcqOS/neWtmC4uLicpXG4gICAgICAgICAgICAgICAgICA6IChjb3Vyc2VMaXN0LnNvbWUoYyA9PiBjLmlkID4gMTAwMDAwMCkgPyAn5Yib5bu66K++56iLJyA6ICfkv53lrZjor77nqIsnKVxuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIOWIoOmZpOehruiupOW8ueeqlyAqL31cbiAgICAgIHtkZWxldGVDb25maXJtVmlzaWJsZSAmJiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibW9kYWwtb3ZlcmxheVwiIG9uQ2xpY2s9e2NhbmNlbERlbGV0ZX0+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJkZWxldGUtY29uZmlybS1tb2RhbFwiIG9uQ2xpY2s9eyhlKSA9PiBlLnN0b3BQcm9wYWdhdGlvbigpfT5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZGVsZXRlLWNvbmZpcm0taGVhZGVyXCI+XG4gICAgICAgICAgICAgIDxoMz7noa7orqTliKDpmaQ8L2gzPlxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgb25DbGljaz17Y2FuY2VsRGVsZXRlfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImNsb3NlLWJ0blwiXG4gICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2lzRGVsZXRpbmd9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8WCBjbGFzc05hbWU9XCJ3LTQgaC00XCIgLz5cbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZGVsZXRlLWNvbmZpcm0tY29udGVudFwiPlxuICAgICAgICAgICAgICA8cD5cbiAgICAgICAgICAgICAgICB7aXNEZWxldGluZ1xuICAgICAgICAgICAgICAgICAgPyAn5q2j5Zyo5Yig6Zmk6K++56iL77yM6K+356iN5YCZLi4uJ1xuICAgICAgICAgICAgICAgICAgOiAn56Gu5a6a6KaB5Yig6Zmk6L+Z5Liq6K++56iL5ZCX77yf5Yig6Zmk5ZCO5peg5rOV5oGi5aSN44CCJ1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICB7aXNEZWxldGluZyAmJiAoXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJkZWxldGUtbG9hZGluZ1wiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJsb2FkaW5nLXNwaW5uZXJcIj48L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJkZWxldGUtY29uZmlybS1mb290ZXJcIj5cbiAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2NhbmNlbERlbGV0ZX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJjYW5jZWwtYnRuXCJcbiAgICAgICAgICAgICAgICBkaXNhYmxlZD17aXNEZWxldGluZ31cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIOWPlua2iFxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2NvbmZpcm1EZWxldGVDb3Vyc2V9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiY29uZmlybS1idG5cIlxuICAgICAgICAgICAgICAgIGRpc2FibGVkPXtpc0RlbGV0aW5nfVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAge2lzRGVsZXRpbmcgPyAn5q2j5Zyo5Yig6ZmkLi4uJyA6ICfnoa7orqTliKDpmaQnfVxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG5cbiAgICAgIHsvKiDmqKHmnb/pgInmi6nlvLnnqpcgKi99XG4gICAgICA8VGVtcGxhdGVQaWNrZXJNb2RhbFxuICAgICAgICBpc09wZW49e2lzVGVtcGxhdGVQaWNrZXJPcGVufVxuICAgICAgICBvbkNsb3NlPXsoKSA9PiBzZXRJc1RlbXBsYXRlUGlja2VyT3BlbihmYWxzZSl9XG4gICAgICAgIG9uVGVtcGxhdGVTZWxlY3Q9e2hhbmRsZVRlbXBsYXRlU2VsZWN0fVxuICAgICAgLz5cbiAgICA8L2Rpdj5cbiAgKTtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IENvdXJzZUxpc3RFZGl0TW9kYWw7XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZUNhbGxiYWNrIiwiWCIsIlNldHRpbmdzIiwiUGx1cyIsIkJvb2tPcGVuIiwiRmlsZVRleHQiLCJjb3Vyc2VNYW5hZ2VtZW50QXBpIiwiY291cnNlQXBpIiwidXBsb2FkQXBpIiwid29ya3NBcGkiLCJTZWxlY3QiLCJHZXROb3RpZmljYXRpb24iLCJUZW1wbGF0ZVBpY2tlck1vZGFsIiwiZmV0Y2hTZXJpZXNEZXRhaWwiLCJzZXJpZXNJZCIsImNvbnNvbGUiLCJsb2ciLCJyZXNwb25zZSIsImdldFNlcmllc0RldGFpbCIsImRhdGEiLCJmZXRjaENvdXJzZUxpc3QiLCJnZXRNYW5hZ2VtZW50U2VyaWVzQ291cnNlcyIsInBhZ2UiLCJwYWdlU2l6ZSIsImZldGNoQ291cnNlRGV0YWlsIiwiY291cnNlSWQiLCJnZXRDb3Vyc2VNYXJrZXRwbGFjZURldGFpbCIsImZldGNoQ291cnNlVGFncyIsInJlc3VsdCIsImdldENvdXJzZVRhZ3MiLCJzdGF0dXMiLCJlcnJvciIsIkNvdXJzZUxpc3RFZGl0TW9kYWwiLCJpc1Zpc2libGUiLCJvbkNsb3NlIiwib25TYXZlIiwic2VyaWVzVGl0bGUiLCJzZXJpZXNDb3ZlckltYWdlIiwiZ2V0U2VsZWN0ZWRDb3Vyc2UiLCJjb3Vyc2VEZXRhaWwiLCJjb3Vyc2VMaXN0Iiwibm90aWZpY2F0aW9uIiwic2V0Q291cnNlTGlzdCIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwicmlnaHRQYW5lbFR5cGUiLCJzZXRSaWdodFBhbmVsVHlwZSIsInNlbGVjdGVkQ291cnNlSWQiLCJzZXRTZWxlY3RlZENvdXJzZUlkIiwidXBsb2FkaW5nRmlsZXMiLCJzZXRVcGxvYWRpbmdGaWxlcyIsIlNldCIsImlzUHVibGlzaGluZyIsInNldElzUHVibGlzaGluZyIsImlzQ3JlYXRpbmciLCJzZXRJc0NyZWF0aW5nIiwiaXNQdWJsaXNoaW5nU2VyaWVzIiwic2V0SXNQdWJsaXNoaW5nU2VyaWVzIiwic2VyaWVzU3RhdHVzIiwic2V0U2VyaWVzU3RhdHVzIiwiZGVsZXRlQ29uZmlybVZpc2libGUiLCJzZXREZWxldGVDb25maXJtVmlzaWJsZSIsImNvdXJzZVRvRGVsZXRlIiwic2V0Q291cnNlVG9EZWxldGUiLCJpc0RlbGV0aW5nIiwic2V0SXNEZWxldGluZyIsImlzVGVtcGxhdGVQaWNrZXJPcGVuIiwic2V0SXNUZW1wbGF0ZVBpY2tlck9wZW4iLCJlZGl0aW5nVGl0bGUiLCJzZXRFZGl0aW5nVGl0bGUiLCJjb3Vyc2VHb2FscyIsInNldENvdXJzZUdvYWxzIiwiY291cnNlT2JqZWN0aXZlcyIsInNldENvdXJzZU9iamVjdGl2ZXMiLCJwcm9qZWN0TWVtYmVycyIsInNldFByb2plY3RNZW1iZXJzIiwid29ya3MiLCJzZXRXb3JrcyIsImxvYWRpbmdXb3JrcyIsInNldExvYWRpbmdXb3JrcyIsInNlbGVjdGVkV29ya0lkcyIsInNldFNlbGVjdGVkV29ya0lkcyIsImNvdXJzZVRhZ3MiLCJzZXRDb3Vyc2VUYWdzIiwic2VsZWN0ZWRUYWdzIiwic2V0U2VsZWN0ZWRUYWdzIiwidGFnc0xvYWRpbmciLCJzZXRUYWdzTG9hZGluZyIsInNldENvdXJzZURldGFpbCIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJjb3ZlckltYWdlIiwidmlkZW9VcmwiLCJ2aWRlb05hbWUiLCJpc1ZpZGVvRW5hYmxlZCIsImF0dGFjaG1lbnRVcmwiLCJhdHRhY2htZW50TmFtZSIsImlzQXR0YWNobWVudEVuYWJsZWQiLCJ0ZWFjaGluZ01hdGVyaWFscyIsInRlYWNoaW5nSW5mbyIsImNvbnRlbnRDb25maWciLCJoYXNWaWRlbyIsImhhc0RvY3VtZW50IiwiaGFzQXVkaW8iLCJ2aWRlbyIsInVybCIsIm5hbWUiLCJkb2N1bWVudCIsImF1ZGlvIiwiY291cnNlQ29udGVudCIsInRvcGljIiwiY29udGVudCIsImlzT25lS2V5T3BlbiIsImlzRGlzdHJpYnV0aW9uRW5hYmxlZCIsImRpc3RyaWJ1dGlvblJld2FyZCIsInNlbGVjdGVkVGVtcGxhdGUiLCJpc0Rpc3RyaWJ1dGlvbldhdGVyIiwicmVxdWlyZWRFbmVyZ3kiLCJlbmVyZ3lBbW91bnQiLCJpc0Rpc3RyaWJ1dGlvbkxpbWl0IiwiZGlzdHJpYnV0aW9uQ29uZGl0aW9ucyIsImludml0ZUNvdW50IiwidGFza0NvdW50IiwiZXhwZXJpZW5jZSIsImlzRGlzdHJpYnV0aW9uVGltZSIsImRpc3RyaWJ1dGlvblRpbWVDb25kaXRpb25zIiwic3RhcnRUaW1lIiwiZW5kVGltZSIsImRpc3RyaWJ1dGlvbk1hdGVyaWFscyIsInRhc2tDb25maWciLCJ0YXNrTmFtZSIsInRhc2tEdXJhdGlvbiIsInRhc2tEZXNjcmlwdGlvbiIsInNlbGZBc3Nlc3NtZW50SXRlbXMiLCJyZWZlcmVuY2VXb3JrcyIsInJlZmVyZW5jZVJlc291cmNlcyIsInRva2VuIiwibG9jYWxTdG9yYWdlIiwiZ2V0SXRlbSIsImxvYWRDb3Vyc2VMaXN0IiwibG9hZENvdXJzZVRhZ3MiLCJsb2FkU2VyaWVzRGV0YWlsIiwibG9hZFVzZXJXb3JrcyIsImNvZGUiLCJjb3Vyc2VzIiwibGlzdCIsIm1hcCIsImMiLCJpZCIsInR5cGUiLCJ0b3RhbCIsImxlbmd0aCIsImRyYWZ0IiwiZmlsdGVyIiwicHVibGlzaGVkIiwibWVzc2FnZSIsInNlcmllc0RhdGEiLCJ1c2VySWQiLCJnZXRUZWFjaGVyV29ya3MiLCJOdW1iZXIiLCJ3b3Jrc0xpc3QiLCJBcnJheSIsImlzQXJyYXkiLCJoYW5kbGVXb3JrU2VsZWN0Iiwid29ya0lkIiwiaW5jbHVkZXMiLCJwcmV2IiwiaGFuZGxlV2hlZWwiLCJlIiwiY29udGFpbmVyIiwiY3VycmVudFRhcmdldCIsInNjcm9sbFdpZHRoIiwiY2xpZW50V2lkdGgiLCJwcmV2ZW50RGVmYXVsdCIsInNjcm9sbExlZnQiLCJkZWx0YVkiLCJ0YWdzIiwiZm9yRWFjaCIsInRhZyIsImluZGV4IiwiaGFzVGFnIiwiaWRUeXBlIiwibmFtZVR5cGUiLCJuYW1lVmFsdWUiLCJuYW1lTm90RW1wdHkiLCJ0cmltIiwidmFsaWRUYWdzIiwiaXNWYWxpZCIsIndhcm4iLCJzdGF0dXNUZXh0IiwiYWRkTmV3Q291cnNlIiwibmV3Q291cnNlIiwiRGF0ZSIsIm5vdyIsIm9yZGVySW5kZXgiLCJzdGF0dXNMYWJlbCIsInZpZGVvRHVyYXRpb24iLCJ2aWRlb0R1cmF0aW9uTGFiZWwiLCJmaXJzdFRlYWNoaW5nVGl0bGUiLCJyZXNvdXJjZXNDb3VudCIsImNyZWF0ZWRBdCIsInRvSVNPU3RyaW5nIiwidXBkYXRlZEF0Iiwic2hvd0NvdXJzZVBhbmVsIiwic2hvd0RlbGV0ZUNvbmZpcm0iLCJjb25maXJtRGVsZXRlQ291cnNlIiwiZGVsZXRlQ291cnNlIiwiY291cnNlIiwic3VjY2VzcyIsImNhbmNlbERlbGV0ZSIsInVwZGF0ZUNvdXJzZVRpdGxlIiwibmV3VGl0bGUiLCJoYW5kbGVDb3ZlclVwbG9hZCIsImV2ZW50IiwiZmlsZSIsInRhcmdldCIsImZpbGVzIiwiYWxsb3dlZFR5cGVzIiwic2l6ZSIsImFkZCIsInByZXZpZXdVcmwiLCJVUkwiLCJjcmVhdGVPYmplY3RVUkwiLCJpbWFnZVVybCIsInVwbG9hZFRvT3NzIiwiYWxlcnQiLCJuZXdTZXQiLCJkZWxldGUiLCJoYW5kbGVWaWRlb1VwbG9hZCIsInRyaWdnZXJWaWRlb1VwbG9hZCIsImlucHV0IiwiY3JlYXRlRWxlbWVudCIsImFjY2VwdCIsIm9uY2hhbmdlIiwiY2xpY2siLCJoYW5kbGVBdHRhY2htZW50VXBsb2FkIiwiZG9jdW1lbnRVcmwiLCJ0cmlnZ2VyQXR0YWNobWVudFVwbG9hZCIsImhhbmRsZVRlYWNoaW5nTWF0ZXJpYWxVcGxvYWQiLCJtYXRlcmlhbFVybCIsIm5ld01hdGVyaWFsIiwiZnJvbSIsIml0ZW0iLCJzdGFydHNXaXRoIiwidHJpZ2dlclRlYWNoaW5nTWF0ZXJpYWxVcGxvYWQiLCJyZW1vdmVUZWFjaGluZ01hdGVyaWFsIiwiXyIsImkiLCJhZGRUZWFjaGluZ0luZm9JdGVtIiwicmVtb3ZlVGVhY2hpbmdJbmZvSXRlbSIsInVwZGF0ZVRlYWNoaW5nSW5mb1RpdGxlIiwibmV3VGVhY2hpbmdJbmZvIiwidXBkYXRlVGVhY2hpbmdJbmZvQ29udGVudCIsImNvbnZlcnRUZWFjaGluZ0luZm9Gb3JBUEkiLCJpbmZvIiwic3BsaXQiLCJsaW5lIiwiY2hlY2tGb3JCbG9iVXJscyIsImlzc3VlcyIsInB1c2giLCJhZGRpdGlvbmFsUmVzb3VyY2VzIiwicmVzb3VyY2UiLCJzYXZlQ291cnNlIiwiRXJyb3IiLCJibG9iSXNzdWVzIiwiam9pbiIsImNvdXJzZURhdGEiLCJjcmVhdGVDb3Vyc2UiLCJ1cGRhdGVDb3Vyc2UiLCJoYW5kbGVQdWJsaXNoQ291cnNlIiwic2VsZWN0ZWRDb3Vyc2UiLCJmaW5kIiwiY291cnNlSWRUb1B1Ymxpc2giLCJwdWJsaXNoQ291cnNlIiwiaGFuZGxlU2F2ZSIsInVwZGF0ZWRDb3Vyc2UiLCJtYXRlcmlhbCIsImhhbmRsZVB1Ymxpc2giLCJwdWJsaXNoQ291cnNlU2VyaWVzIiwicHVibGlzaERhdGEiLCJzdWNjZXNzTWVzc2FnZSIsInB1Ymxpc2hTdGF0cyIsInN0YXRzIiwidG90YWxDb3Vyc2VzIiwicHVibGlzaGVkQ291cnNlcyIsInZpZGVvQ291cnNlQ291bnQiLCJkb2N1bWVudENvdXJzZUNvdW50IiwidG90YWxSZXNvdXJjZXNDb3VudCIsInRvdGFsVmlkZW9EdXJhdGlvbiIsImR1cmF0aW9uTWludXRlcyIsIk1hdGgiLCJyb3VuZCIsImVycm9yTWVzc2FnZSIsImhhbmRsZUV4aXRFZGl0Iiwic2hvd1NldHRpbmdzUGFuZWwiLCJoYW5kbGVUZW1wbGF0ZVNlbGVjdCIsInRlbXBsYXRlIiwidGVtcGxhdGVOYW1lIiwiaGFuZGxlT3BlblRlbXBsYXRlUGlja2VyIiwicmVzIiwiY291cnNlRGV0YWlsRGF0YSIsIm1hcHBlZFRlYWNoaW5nSW5mbyIsImRpdiIsImNsYXNzTmFtZSIsImgyIiwiYnV0dG9uIiwib25DbGljayIsInAiLCJoMyIsInNwYW4iLCJzdG9wUHJvcGFnYXRpb24iLCJpbWciLCJzcmMiLCJhbHQiLCJsYWJlbCIsInZhbHVlIiwib25DaGFuZ2UiLCJwbGFjZWhvbGRlciIsIm1vZGUiLCJzdHlsZSIsIndpZHRoIiwib3B0aW9ucyIsImNvbG9yIiwiZm9udFNpemUiLCJtYXJnaW5Ub3AiLCJnZXRFbGVtZW50QnlJZCIsImRpc3BsYXkiLCJ0ZXh0YXJlYSIsInJvd3MiLCJjaGVja2VkIiwiY29udHJvbHMiLCJwb3N0ZXIiLCJzb3VyY2UiLCJ3aW5kb3ciLCJvcGVuIiwiY3Vyc29yIiwidGV4dERlY29yYXRpb24iLCJuZXdJdGVtcyIsIm1pbkhlaWdodCIsInVzZXJTZWxlY3QiLCJvbldoZWVsIiwid29yayIsInNjcmVlblNob3RJbWFnZSIsIm9uRXJyb3IiLCJuZXh0RWxlbWVudCIsIm5leHRFbGVtZW50U2libGluZyIsIndvcmtOYW1lIiwibmV3UmVzb3VyY2VzIiwiZGlzYWJsZWQiLCJzb21lIiwiaXNPcGVuIiwib25UZW1wbGF0ZVNlbGVjdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/workbench/components/CourseListEditModal.tsx\n"));

/***/ })

});