"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/course-plaza/page",{

/***/ "(app-pages-browser)/./app/(main)/course-plaza/page.tsx":
/*!******************************************!*\
  !*** ./app/(main)/course-plaza/page.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CoursePlaza; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/./node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _lib_api_course__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api/course */ \"(app-pages-browser)/./lib/api/course.ts\");\n/* harmony import */ var _components_CourseDetailView__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./components/CourseDetailView */ \"(app-pages-browser)/./app/(main)/course-plaza/components/CourseDetailView.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// 动态导入 AIBackground 组件，并禁用 SSR\nconst AIBackground = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_components_AIBackground_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/AIBackground */ \"(app-pages-browser)/./components/AIBackground.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\(main)\\\\course-plaza\\\\page.tsx -> \" + \"@/components/AIBackground\"\n        ]\n    },\n    ssr: false\n});\n_c = AIBackground;\nfunction CoursePlaza() {\n    _s();\n    const [seriesCourses, setSeriesCourses] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [communityCourses, setCommunityCourses] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true);\n    const [showAllOfficialCourses, setShowAllOfficialCourses] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [loadingMore, setLoadingMore] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [showAllCommunityCourses, setShowAllCommunityCourses] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [loadingMoreCommunity, setLoadingMoreCommunity] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [selectedCourse, setSelectedCourse] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [showCourseDetail, setShowCourseDetail] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [showOfficialSidebar, setShowOfficialSidebar] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [showCommunitySidebar, setShowCommunitySidebar] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    // 处理展示更多官方课程 - 打开侧边栏\n    const handleShowMoreOfficialCourses = ()=>{\n        setShowOfficialSidebar(true);\n    };\n    // 处理展示更多社区课程 - 打开侧边栏\n    const handleShowMoreCommunityCourses = ()=>{\n        setShowCommunitySidebar(true);\n    };\n    // 处理课程卡片点击\n    const handleCourseClick = (course)=>{\n        console.log(\"\\uD83C\\uDFAF 点击课程:\", course);\n        console.log(\"\\uD83D\\uDD0D 点击课程的seriesId:\", course.seriesId);\n        console.log(\"\\uD83D\\uDD0D 点击课程的完整数据:\", JSON.stringify(course, null, 2));\n        setSelectedCourse(course);\n        setShowCourseDetail(true);\n    };\n    // 返回课程列表\n    const handleBackToCourseList = ()=>{\n        setShowCourseDetail(false);\n        setSelectedCourse(null);\n    };\n    // 获取要显示的官方课程列表 - 显示所有课程\n    const getDisplayedOfficialCourses = ()=>{\n        return seriesCourses;\n    };\n    // 获取要显示的社区课程列表 - 显示所有课程\n    const getDisplayedCommunityCourses = ()=>{\n        return communityCourses;\n    };\n    // 获取系列课程数据\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        const fetchSeriesCourses = async ()=>{\n            try {\n                setLoading(true);\n                console.log(\"\\uD83D\\uDD04 开始获取课程市场系列课程列表...\");\n                console.log(\"\\uD83D\\uDD04 准备调用 courseApi.getMarketplaceSeries（获取课程市场系列课程）...\");\n                // 使用课程市场系列课程列表接口\n                const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_4__.courseApi.getMarketplaceSeries({\n                    page: 1,\n                    pageSize: 50 // 获取更多数据\n                });\n                if (res.code === 200 && res.data && res.data.list) {\n                    // 打印每个系列课程的详细信息\n                    res.data.list.forEach((item, index)=>{\n                        console.log(\"\\uD83D\\uDCCB 系列课程 \".concat(index + 1, \":\"), {\n                            id: item.id,\n                            title: item.title,\n                            category: item.category,\n                            categoryLabel: item.categoryLabel,\n                            status: item.status,\n                            statusLabel: item.statusLabel,\n                            totalCourses: item.totalCourses,\n                            totalStudents: item.totalStudents,\n                            coverImage: item.coverImage // 添加封面图片信息\n                        });\n                    });\n                    // 筛选 categoryLabel 为 \"官方\" 的官方课程，全部显示\n                    const officialCourses = res.data.list.filter((item)=>{\n                        console.log('\\uD83D\\uDD0D 检查系列课程 \"'.concat(item.title, '\": category=').concat(item.category, ', categoryLabel=\"').concat(item.categoryLabel, '\"'));\n                        return item.categoryLabel === \"官方\"; // 筛选官方课程 (categoryLabel = \"官方\")\n                    }).map((item)=>{\n                        var _item_totalStudents;\n                        console.log('\\uD83D\\uDCCB 官方系列课程 \"'.concat(item.title, '\": category=').concat(item.category, ', categoryLabel=\"').concat(item.categoryLabel, '\", coverImage=\"').concat(item.coverImage, '\"'));\n                        return {\n                            id: item.id,\n                            seriesId: item.id,\n                            title: item.title,\n                            lessons: \"共\".concat(item.totalCourses || 0, \"课时\"),\n                            views: ((_item_totalStudents = item.totalStudents) === null || _item_totalStudents === void 0 ? void 0 : _item_totalStudents.toString()) || \"0\",\n                            coverImage: item.coverImage && !item.coverImage.includes(\"example.com\") ? item.coverImage : null // 过滤示例URL\n                        };\n                    });\n                    // 筛选 categoryLabel 为 \"社区\" 的社区课程，全部显示\n                    const communityCourses = res.data.list.filter((item)=>{\n                        console.log('\\uD83D\\uDD0D 检查社区课程 \"'.concat(item.title, '\": category=').concat(item.category, ', categoryLabel=\"').concat(item.categoryLabel, '\"'));\n                        return item.categoryLabel === \"社区\"; // 筛选社区课程 (categoryLabel = \"社区\")\n                    }).map((item)=>{\n                        var _item_totalStudents;\n                        console.log('\\uD83D\\uDCCB 社区系列课程 \"'.concat(item.title, '\": category=').concat(item.category, ', categoryLabel=\"').concat(item.categoryLabel, '\", coverImage=\"').concat(item.coverImage, '\"'));\n                        return {\n                            id: item.id,\n                            seriesId: item.id,\n                            title: item.title,\n                            lessons: \"共\".concat(item.totalCourses || 0, \"课时\"),\n                            views: ((_item_totalStudents = item.totalStudents) === null || _item_totalStudents === void 0 ? void 0 : _item_totalStudents.toString()) || \"0\",\n                            coverImage: item.coverImage && !item.coverImage.includes(\"example.com\") ? item.coverImage : null // 过滤示例URL\n                        };\n                    });\n                    setSeriesCourses(officialCourses);\n                    setCommunityCourses(communityCourses);\n                } else {\n                    setSeriesCourses([]);\n                }\n            } catch (error) {\n                console.error(\"❌ 获取系列课程失败:\", error);\n                console.error(\"❌ 错误详情:\", error);\n                setSeriesCourses([]);\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchSeriesCourses();\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen\",\n        children: showCourseDetail && selectedCourse ? // 课程详情页面 - 带格子背景\n        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8 relative min-h-screen\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 z-[-1]\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AIBackground, {}, void 0, false, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                    lineNumber: 160,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto relative z-10\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CourseDetailView__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        course: selectedCourse,\n                        onBack: handleBackToCourseList\n                    }, void 0, false, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n            lineNumber: 158,\n            columnNumber: 9\n        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full h-64 relative\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        src: \"/images/mywork_background.svg\",\n                        alt: \"背景图片\",\n                        fill: true,\n                        className: \"object-cover\",\n                        priority: true\n                    }, void 0, false, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                    lineNumber: 174,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-8 relative min-h-screen\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 z-[-1]\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AIBackground, {}, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto relative z-10\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8\n                                    },\n                                    className: \"mb-12\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: \"官方课程\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 13\n                                                }, this),\n                                                (()=>{\n                                                    console.log(\"\\uD83D\\uDD0D 按钮显示条件检查:\", {\n                                                        loading,\n                                                        seriesCoursesLength: seriesCourses.length,\n                                                        shouldShowButton: !loading && seriesCourses.length > 6\n                                                    });\n                                                    return !loading && seriesCourses.length > 6;\n                                                })() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handleShowMoreOfficialCourses,\n                                                    disabled: loadingMore,\n                                                    className: \"group relative flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-blue-500 to-blue-600 text-white font-medium rounded-full shadow-lg hover:shadow-xl hover:from-blue-600 hover:to-blue-700 transform hover:scale-105 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none disabled:shadow-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 bg-gradient-to-r from-blue-400 to-blue-500 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                            lineNumber: 214,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative flex items-center gap-2\",\n                                                            children: loadingMore ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-5 h-5 animate-spin\",\n                                                                        fill: \"none\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                                className: \"opacity-25\",\n                                                                                cx: \"12\",\n                                                                                cy: \"12\",\n                                                                                r: \"10\",\n                                                                                stroke: \"currentColor\",\n                                                                                strokeWidth: \"4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                                lineNumber: 219,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                className: \"opacity-75\",\n                                                                                fill: \"currentColor\",\n                                                                                d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                                lineNumber: 220,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                        lineNumber: 218,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm\",\n                                                                        children: \"加载中...\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                        lineNumber: 222,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-semibold\",\n                                                                        children: \"展开全部\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                        lineNumber: 226,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-5 h-5 transition-all duration-300 \".concat(showAllOfficialCourses ? \"rotate-180 scale-110\" : \"group-hover:translate-y-0.5\"),\n                                                                        fill: \"none\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        stroke: \"currentColor\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\",\n                                                                            strokeWidth: 2.5,\n                                                                            d: \"M19 9l-7 7-7-7\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                            lineNumber: 233,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                        lineNumber: 227,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                            lineNumber: 215,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                    lineNumber: 209,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                            lineNumber: 199,\n                                            columnNumber: 11\n                                        }, this),\n                                        loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                            children: [\n                                                1,\n                                                2,\n                                                3\n                                            ].map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white rounded-lg border border-blue-200 shadow-sm overflow-hidden\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-36 bg-gradient-to-br from-blue-100 to-blue-200 animate-pulse flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-12 h-12 text-blue-600 opacity-50\",\n                                                                fill: \"none\",\n                                                                viewBox: \"0 0 48 48\",\n                                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M44 24C44 22.8954 43.1046 22 42 22C40.8954 22 40 22.8954 40 24H44ZM24 8C25.1046 8 26 7.10457 26 6C26 4.89543 25.1046 4 24 4V8ZM39 40H9V44H39V40ZM8 39V9H4V39H8ZM40 24V39H44V24H40ZM9 8H24V4H9V8ZM9 40C8.44772 40 8 39.5523 8 39H4C4 41.7614 6.23857 44 9 44V40ZM39 44C41.7614 44 44 41.7614 44 39H40C40 39.5523 39.5523 40 39 40V44ZM8 9C8 8.44772 8.44771 8 9 8V4C6.23858 4 4 6.23857 4 9H8Z\",\n                                                                        fill: \"currentColor\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                        lineNumber: 247,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M6 35L16.6931 25.198C17.4389 24.5143 18.5779 24.4953 19.3461 25.1538L32 36\",\n                                                                        stroke: \"currentColor\",\n                                                                        strokeWidth: \"4\",\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                        lineNumber: 248,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M28 31L32.7735 26.2265C33.4772 25.5228 34.5914 25.4436 35.3877 26.0408L42 31\",\n                                                                        stroke: \"currentColor\",\n                                                                        strokeWidth: \"4\",\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                        lineNumber: 249,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M37 18L37 6\",\n                                                                        stroke: \"currentColor\",\n                                                                        strokeWidth: \"4\",\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                        lineNumber: 250,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M32 11L37 6L42 11\",\n                                                                        stroke: \"currentColor\",\n                                                                        strokeWidth: \"4\",\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                        lineNumber: 251,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                lineNumber: 246,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                            lineNumber: 245,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-4 bg-gray-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-4 bg-gray-200 rounded animate-pulse mb-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                    lineNumber: 255,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"h-3 bg-gray-200 rounded animate-pulse w-16\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                            lineNumber: 257,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"h-3 bg-gray-200 rounded animate-pulse w-12\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                            lineNumber: 258,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                    lineNumber: 256,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                            lineNumber: 254,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, i, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 17\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 13\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                            layout: true,\n                                            transition: {\n                                                duration: 0.5,\n                                                ease: \"easeInOut\"\n                                            },\n                                            children: getDisplayedOfficialCourses().map((course, index)=>{\n                                                const isNewlyVisible = showAllOfficialCourses && index >= 6;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                                    className: \"bg-white rounded-lg border border-blue-200 shadow-sm overflow-hidden hover:shadow-md transition-shadow duration-200 cursor-pointer\",\n                                                    onClick: ()=>handleCourseClick(course),\n                                                    initial: {\n                                                        opacity: isNewlyVisible ? 0 : 1,\n                                                        y: isNewlyVisible ? 30 : 0,\n                                                        scale: isNewlyVisible ? 0.9 : 1\n                                                    },\n                                                    animate: {\n                                                        opacity: 1,\n                                                        y: 0,\n                                                        scale: 1\n                                                    },\n                                                    transition: {\n                                                        duration: isNewlyVisible ? 0.6 : 0.4,\n                                                        delay: isNewlyVisible ? (index - 6) * 0.15 + 0.2 : index * 0.08,\n                                                        ease: \"easeOut\",\n                                                        type: \"spring\",\n                                                        stiffness: 100,\n                                                        damping: 15\n                                                    },\n                                                    whileHover: {\n                                                        y: -8,\n                                                        scale: 1.03,\n                                                        boxShadow: \"0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)\",\n                                                        transition: {\n                                                            duration: 0.3,\n                                                            ease: \"easeOut\"\n                                                        }\n                                                    },\n                                                    layout: true,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-36 relative \".concat(!course.coverImage ? \"bg-gradient-to-br from-blue-100 to-blue-200\" : \"\"),\n                                                            children: course.coverImage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                src: course.coverImage,\n                                                                alt: course.title,\n                                                                fill: true,\n                                                                className: \"object-cover\",\n                                                                onLoad: ()=>{\n                                                                    console.log('✅ 封面图片加载成功: \"'.concat(course.title, '\" - ').concat(course.coverImage));\n                                                                },\n                                                                onError: (e)=>{\n                                                                    // 图片加载失败时隐藏图片，显示默认图标\n                                                                    console.log('❌ 封面图片加载失败: \"'.concat(course.title, '\" - ').concat(course.coverImage));\n                                                                    const target = e.target;\n                                                                    target.style.display = \"none\";\n                                                                    // 添加浅蓝色背景\n                                                                    const parent = target.parentElement;\n                                                                    if (parent) {\n                                                                        parent.className = parent.className + \" bg-gradient-to-br from-blue-100 to-blue-200\";\n                                                                    }\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                lineNumber: 302,\n                                                                columnNumber: 23\n                                                            }, this) : // 当没有封面图片时显示的图标\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-full h-full flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-12 h-12 text-blue-600 opacity-70\",\n                                                                    fill: \"none\",\n                                                                    viewBox: \"0 0 48 48\",\n                                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M44 24C44 22.8954 43.1046 22 42 22C40.8954 22 40 22.8954 40 24H44ZM24 8C25.1046 8 26 7.10457 26 6C26 4.89543 25.1046 4 24 4V8ZM39 40H9V44H39V40ZM8 39V9H4V39H8ZM40 24V39H44V24H40ZM9 8H24V4H9V8ZM9 40C8.44772 40 8 39.5523 8 39H4C4 41.7614 6.23857 44 9 44V40ZM39 44C41.7614 44 44 41.7614 44 39H40C40 39.5523 39.5523 40 39 40V44ZM8 9C8 8.44772 8.44771 8 9 8V4C6.23858 4 4 6.23857 4 9H8Z\",\n                                                                            fill: \"currentColor\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                            lineNumber: 326,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M6 35L16.6931 25.198C17.4389 24.5143 18.5779 24.4953 19.3461 25.1538L32 36\",\n                                                                            stroke: \"currentColor\",\n                                                                            strokeWidth: \"4\",\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                            lineNumber: 327,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M28 31L32.7735 26.2265C33.4772 25.5228 34.5914 25.4436 35.3877 26.0408L42 31\",\n                                                                            stroke: \"currentColor\",\n                                                                            strokeWidth: \"4\",\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                            lineNumber: 328,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M37 18L37 6\",\n                                                                            stroke: \"currentColor\",\n                                                                            strokeWidth: \"4\",\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                            lineNumber: 329,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M32 11L37 6L42 11\",\n                                                                            stroke: \"currentColor\",\n                                                                            strokeWidth: \"4\",\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                            lineNumber: 330,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                    lineNumber: 325,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                lineNumber: 324,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                            lineNumber: 300,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-4 bg-gray-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"font-medium text-gray-800 mb-3 text-base line-clamp-2\",\n                                                                    children: course.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                    lineNumber: 338,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between text-sm text-gray-600\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-500\",\n                                                                            children: course.lessons\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                            lineNumber: 340,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-1.5\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                    className: \"w-4 h-4 text-gray-400\",\n                                                                                    fill: \"currentColor\",\n                                                                                    viewBox: \"0 0 20 20\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                            d: \"M10 12a2 2 0 100-4 2 2 0 000 4z\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                                            lineNumber: 343,\n                                                                                            columnNumber: 27\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                            fillRule: \"evenodd\",\n                                                                                            d: \"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z\",\n                                                                                            clipRule: \"evenodd\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                                            lineNumber: 344,\n                                                                                            columnNumber: 27\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                                    lineNumber: 342,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-gray-600 font-medium\",\n                                                                                    children: course.views\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                                    lineNumber: 346,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                            lineNumber: 341,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                    lineNumber: 339,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                            lineNumber: 337,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, course.id, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                    lineNumber: 273,\n                                                    columnNumber: 19\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 9\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8,\n                                        delay: 0.2\n                                    },\n                                    className: \"mb-12\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: \"课程社区\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                    lineNumber: 365,\n                                                    columnNumber: 13\n                                                }, this),\n                                                (()=>{\n                                                    console.log(\"\\uD83D\\uDD0D 社区按钮显示条件检查:\", {\n                                                        loading,\n                                                        communityCoursesLength: communityCourses.length,\n                                                        shouldShowButton: !loading && communityCourses.length > 6\n                                                    });\n                                                    return !loading && communityCourses.length > 6;\n                                                })() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handleShowMoreCommunityCourses,\n                                                    disabled: loadingMoreCommunity,\n                                                    className: \"group relative flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-green-500 to-green-600 text-white font-medium rounded-full shadow-lg hover:shadow-xl hover:from-green-600 hover:to-green-700 transform hover:scale-105 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none disabled:shadow-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 bg-gradient-to-r from-green-400 to-green-500 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                            lineNumber: 379,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative flex items-center gap-2\",\n                                                            children: loadingMoreCommunity ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-5 h-5 animate-spin\",\n                                                                        fill: \"none\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                                className: \"opacity-25\",\n                                                                                cx: \"12\",\n                                                                                cy: \"12\",\n                                                                                r: \"10\",\n                                                                                stroke: \"currentColor\",\n                                                                                strokeWidth: \"4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                                lineNumber: 384,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                className: \"opacity-75\",\n                                                                                fill: \"currentColor\",\n                                                                                d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                                lineNumber: 385,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                        lineNumber: 383,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm\",\n                                                                        children: \"加载中...\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                        lineNumber: 387,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-semibold\",\n                                                                        children: showAllCommunityCourses ? \"收起\" : \"查看更多\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                        lineNumber: 391,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-5 h-5 transition-all duration-300 \".concat(showAllCommunityCourses ? \"rotate-180 scale-110\" : \"group-hover:translate-y-0.5\"),\n                                                                        fill: \"none\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        stroke: \"currentColor\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\",\n                                                                            strokeWidth: 2.5,\n                                                                            d: \"M19 9l-7 7-7-7\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                            lineNumber: 398,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                        lineNumber: 392,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                            lineNumber: 380,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                    lineNumber: 374,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                            lineNumber: 364,\n                                            columnNumber: 11\n                                        }, this),\n                                        loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                            children: [\n                                                1,\n                                                2,\n                                                3\n                                            ].map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white rounded-lg border border-blue-200 shadow-sm overflow-hidden animate-pulse\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-36 bg-gradient-to-br from-blue-100 to-blue-200 flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-12 h-12 text-blue-600 opacity-50\",\n                                                                fill: \"none\",\n                                                                viewBox: \"0 0 48 48\",\n                                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M44 24C44 22.8954 43.1046 22 42 22C40.8954 22 40 22.8954 40 24H44ZM24 8C25.1046 8 26 7.10457 26 6C26 4.89543 25.1046 4 24 4V8ZM39 40H9V44H39V40ZM8 39V9H4V39H8ZM40 24V39H44V24H40ZM9 8H24V4H9V8ZM9 40C8.44772 40 8 39.5523 8 39H4C4 41.7614 6.23857 44 9 44V40ZM39 44C41.7614 44 44 41.7614 44 39H40C40 39.5523 39.5523 40 39 40V44ZM8 9C8 8.44772 8.44771 8 9 8V4C6.23858 4 4 6.23857 4 9H8Z\",\n                                                                        fill: \"currentColor\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                        lineNumber: 412,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M6 35L16.6931 25.198C17.4389 24.5143 18.5779 24.4953 19.3461 25.1538L32 36\",\n                                                                        stroke: \"currentColor\",\n                                                                        strokeWidth: \"4\",\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                        lineNumber: 413,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M28 31L32.7735 26.2265C33.4772 25.5228 34.5914 25.4436 35.3877 26.0408L42 31\",\n                                                                        stroke: \"currentColor\",\n                                                                        strokeWidth: \"4\",\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                        lineNumber: 414,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M37 18L37 6\",\n                                                                        stroke: \"currentColor\",\n                                                                        strokeWidth: \"4\",\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                        lineNumber: 415,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M32 11L37 6L42 11\",\n                                                                        stroke: \"currentColor\",\n                                                                        strokeWidth: \"4\",\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                        lineNumber: 416,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                lineNumber: 411,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                            lineNumber: 410,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-4 bg-gray-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-4 bg-gray-200 rounded mb-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                    lineNumber: 420,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"h-3 bg-gray-200 rounded w-16\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                            lineNumber: 422,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"h-3 bg-gray-200 rounded w-8\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                            lineNumber: 423,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                    lineNumber: 421,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                            lineNumber: 419,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, i, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                    lineNumber: 409,\n                                                    columnNumber: 17\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                            lineNumber: 407,\n                                            columnNumber: 13\n                                        }, this) : communityCourses.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-12\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-400 text-lg mb-2\",\n                                                    children: \"暂无社区课程\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                    lineNumber: 431,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-500 text-sm\",\n                                                    children: \"社区课程正在建设中...\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                    lineNumber: 432,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                            lineNumber: 430,\n                                            columnNumber: 13\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                            layout: true,\n                                            transition: {\n                                                duration: 0.5,\n                                                ease: \"easeInOut\"\n                                            },\n                                            children: getDisplayedCommunityCourses().map((course, index)=>{\n                                                const isNewlyVisible = showAllCommunityCourses && index >= 6;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                                    className: \"bg-white rounded-lg border border-blue-200 shadow-sm overflow-hidden hover:shadow-md transition-shadow duration-200 cursor-pointer\",\n                                                    onClick: ()=>handleCourseClick(course),\n                                                    initial: {\n                                                        opacity: isNewlyVisible ? 0 : 1,\n                                                        y: isNewlyVisible ? 30 : 0,\n                                                        scale: isNewlyVisible ? 0.9 : 1\n                                                    },\n                                                    animate: {\n                                                        opacity: 1,\n                                                        y: 0,\n                                                        scale: 1\n                                                    },\n                                                    transition: {\n                                                        duration: isNewlyVisible ? 0.6 : 0.4,\n                                                        delay: isNewlyVisible ? (index - 6) * 0.15 + 0.2 : index * 0.08,\n                                                        ease: \"easeOut\",\n                                                        type: \"spring\",\n                                                        stiffness: 100,\n                                                        damping: 15\n                                                    },\n                                                    whileHover: {\n                                                        y: -8,\n                                                        scale: 1.03,\n                                                        boxShadow: \"0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)\",\n                                                        transition: {\n                                                            duration: 0.3,\n                                                            ease: \"easeOut\"\n                                                        }\n                                                    },\n                                                    layout: true,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-36 relative \".concat(!course.coverImage ? \"bg-gradient-to-br from-blue-100 to-blue-200\" : \"\"),\n                                                            children: course.coverImage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                src: course.coverImage,\n                                                                alt: course.title,\n                                                                fill: true,\n                                                                className: \"object-cover\",\n                                                                onLoad: ()=>{\n                                                                    console.log('✅ 封面图片加载成功: \"'.concat(course.title, '\" - ').concat(course.coverImage));\n                                                                },\n                                                                onError: (e)=>{\n                                                                    // 图片加载失败时隐藏图片，显示默认图标\n                                                                    console.log('❌ 封面图片加载失败: \"'.concat(course.title, '\" - ').concat(course.coverImage));\n                                                                    const target = e.target;\n                                                                    target.style.display = \"none\";\n                                                                    // 添加浅蓝色背景\n                                                                    const parent = target.parentElement;\n                                                                    if (parent) {\n                                                                        parent.className = parent.className + \" bg-gradient-to-br from-blue-100 to-blue-200\";\n                                                                    }\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                lineNumber: 472,\n                                                                columnNumber: 23\n                                                            }, this) : // 当没有封面图片时显示的图标\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-full h-full flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-12 h-12 text-blue-600 opacity-70\",\n                                                                    fill: \"none\",\n                                                                    viewBox: \"0 0 48 48\",\n                                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M44 24C44 22.8954 43.1046 22 42 22C40.8954 22 40 22.8954 40 24H44ZM24 8C25.1046 8 26 7.10457 26 6C26 4.89543 25.1046 4 24 4V8ZM39 40H9V44H39V40ZM8 39V9H4V39H8ZM40 24V39H44V24H40ZM9 8H24V4H9V8ZM9 40C8.44772 40 8 39.5523 8 39H4C4 41.7614 6.23857 44 9 44V40ZM39 44C41.7614 44 44 41.7614 44 39H40C40 39.5523 39.5523 40 39 40V44ZM8 9C8 8.44772 8.44771 8 9 8V4C6.23858 4 4 6.23857 4 9H8Z\",\n                                                                            fill: \"currentColor\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                            lineNumber: 496,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M6 35L16.6931 25.198C17.4389 24.5143 18.5779 24.4953 19.3461 25.1538L32 36\",\n                                                                            stroke: \"currentColor\",\n                                                                            strokeWidth: \"4\",\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                            lineNumber: 497,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M28 31L32.7735 26.2265C33.4772 25.5228 34.5914 25.4436 35.3877 26.0408L42 31\",\n                                                                            stroke: \"currentColor\",\n                                                                            strokeWidth: \"4\",\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                            lineNumber: 498,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M37 18L37 6\",\n                                                                            stroke: \"currentColor\",\n                                                                            strokeWidth: \"4\",\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                            lineNumber: 499,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M32 11L37 6L42 11\",\n                                                                            stroke: \"currentColor\",\n                                                                            strokeWidth: \"4\",\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                            lineNumber: 500,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                    lineNumber: 495,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                lineNumber: 494,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                            lineNumber: 470,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-4 bg-gray-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"font-medium text-gray-800 mb-3 text-base line-clamp-2\",\n                                                                    children: course.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                    lineNumber: 508,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between text-sm text-gray-600\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-500\",\n                                                                            children: course.lessons\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                            lineNumber: 510,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-1.5\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                    className: \"w-4 h-4 text-gray-400\",\n                                                                                    fill: \"currentColor\",\n                                                                                    viewBox: \"0 0 20 20\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                            d: \"M10 12a2 2 0 100-4 2 2 0 000 4z\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                                            lineNumber: 513,\n                                                                                            columnNumber: 27\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                            fillRule: \"evenodd\",\n                                                                                            d: \"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z\",\n                                                                                            clipRule: \"evenodd\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                                            lineNumber: 514,\n                                                                                            columnNumber: 27\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                                    lineNumber: 512,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-gray-600 font-medium\",\n                                                                                    children: course.views\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                                    lineNumber: 516,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                            lineNumber: 511,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                    lineNumber: 509,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                            lineNumber: 507,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, course.id, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                    lineNumber: 443,\n                                                    columnNumber: 19\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                            lineNumber: 435,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                    lineNumber: 358,\n                                    columnNumber: 9\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                            lineNumber: 191,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                    lineNumber: 185,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true)\n    }, void 0, false, {\n        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n        lineNumber: 155,\n        columnNumber: 5\n    }, this);\n}\n_s(CoursePlaza, \"gyljU7inwf6shDYB1IcTy/Ch8OM=\");\n_c1 = CoursePlaza;\nvar _c, _c1;\n$RefreshReg$(_c, \"AIBackground\");\n$RefreshReg$(_c1, \"CoursePlaza\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/(main)/course-plaza/page.tsx\n"));

/***/ })

});