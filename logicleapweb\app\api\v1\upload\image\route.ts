import { NextRequest, NextResponse } from 'next/server';
import { writeFile, mkdir } from 'fs/promises';
import { join } from 'path';
import { existsSync } from 'fs';

export async function POST(request: NextRequest) {
  try {
    console.log('📤 收到图片上传请求');
    
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const type = formData.get('type') as string || 'general';
    
    if (!file) {
      console.error('❌ 没有找到文件');
      return NextResponse.json(
        { code: 400, message: '没有找到文件', data: null },
        { status: 400 }
      );
    }
    
    console.log('📁 文件信息:', {
      name: file.name,
      size: file.size,
      type: file.type
    });
    
    // 验证文件类型
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      console.error('❌ 不支持的文件类型:', file.type);
      return NextResponse.json(
        { code: 400, message: '不支持的文件类型，请上传 JPG、PNG、GIF 或 WebP 格式的图片', data: null },
        { status: 400 }
      );
    }
    
    // 验证文件大小 (10MB)
    const maxSize = 10 * 1024 * 1024;
    if (file.size > maxSize) {
      console.error('❌ 文件太大:', file.size);
      return NextResponse.json(
        { code: 400, message: '文件大小不能超过 10MB', data: null },
        { status: 400 }
      );
    }
    
    // 生成唯一文件名
    const timestamp = Date.now();
    const randomStr = Math.random().toString(36).substring(2, 8);
    const fileExtension = file.name.split('.').pop() || 'jpg';
    const fileName = `${type}_${timestamp}_${randomStr}.${fileExtension}`;
    
    // 创建上传目录
    const uploadDir = join(process.cwd(), 'public', 'uploads', type);
    if (!existsSync(uploadDir)) {
      await mkdir(uploadDir, { recursive: true });
      console.log('📁 创建上传目录:', uploadDir);
    }
    
    // 保存文件
    const filePath = join(uploadDir, fileName);
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);
    
    await writeFile(filePath, buffer);
    console.log('✅ 文件保存成功:', filePath);
    
    // 生成访问URL
    const fileUrl = `/uploads/${type}/${fileName}`;
    const fullUrl = `${request.nextUrl.origin}${fileUrl}`;
    
    console.log('🔗 生成访问URL:', fullUrl);
    
    return NextResponse.json({
      code: 200,
      message: '图片上传成功',
      data: {
        url: fullUrl,
        fileName: fileName,
        originalName: file.name,
        size: file.size,
        type: file.type
      }
    });
    
  } catch (error) {
    console.error('❌ 图片上传失败:', error);
    return NextResponse.json(
      { 
        code: 500, 
        message: '图片上传失败: ' + (error instanceof Error ? error.message : '未知错误'), 
        data: null 
      },
      { status: 500 }
    );
  }
}

// 支持的HTTP方法
export const runtime = 'nodejs';
