import { QueryMonitorService } from '../service/query-monitor.service';
export interface QueryInfo {
    query: string;
    parameters?: any[];
    startTime: number;
    context?: string;
}
export declare class QueryInterceptor {
    private readonly queryMonitorService;
    private readonly logger;
    private activeQueries;
    constructor(queryMonitorService: QueryMonitorService);
    beforeQuery(query: string, parameters?: any[], context?: string): string;
    afterSlowQuery(queryId: string, error?: Error): void;
    private generateQueryId;
    getActiveQueryCount(): number;
    getActiveQueries(): QueryInfo[];
    cleanupTimeoutQueries(timeoutMs?: number): void;
}
