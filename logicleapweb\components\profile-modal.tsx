import Image from 'next/image'
import { UserCircle, Edit2, X, Key, Clipboard } from 'lucide-react'
import { UserInfo, UserState } from '../types/user'
import { useEffect, useState } from 'react'
import { userApi } from '../lib/api/user'
import { uploadApi } from '../lib/api/upload'
import { useRouter } from 'next/navigation'
import { useSelector, useDispatch } from 'react-redux'
import { RootState, setUser, clearUser } from '../lib/store'
import auth from '../lib/auth'
import { worksApi } from '../lib/api/works'
import { Modal, Input, Form, Tabs } from 'antd'
import request from '../lib/request'
import ModifyPhoneModal from './modify-phone-modal'
import SlideCaptcha from './slide-captcha'
import RedeemKeyModal from './redeem-key-modal'
import TeacherAuthModal from '../app/components/userAuth/teacherAuthModal'
import { studentApi } from '@/lib/api/student'
import { GetNotification } from 'logic-common/dist/components/Notification'
import { all } from 'axios'
import RoleSelectionModalForNewUser from '../app/components/userAuth/RoleSelectionModalForNewUser'

const notification = GetNotification();

interface ProfileModalProps {
  isOpen: boolean
  onClose: () => void
  points: number
  onUpdate?: () => void
}

// 密码强度等级
type PasswordStrength = 'weak' | 'medium' | 'strong';

// 密码强度提示组件
interface PasswordStrengthTipProps {
  password: string;
  showDialog: boolean;
}

const PasswordStrengthTip: React.FC<PasswordStrengthTipProps> = ({ password, showDialog }) => {
  // 评估密码强度
  const evaluatePasswordStrength = (pwd: string): PasswordStrength => {
    if (!pwd) return 'weak';
    let score = 0;
    if (pwd.length >= 8) score += 1;
    if (pwd.length >= 12) score += 1;
    if (/\d/.test(pwd)) score += 1;
    if (/[a-z]/.test(pwd)) score += 1;
    if (/[A-Z]/.test(pwd)) score += 1;
    if (/[^A-Za-z0-9]/.test(pwd)) score += 1;
    if (score <= 2) return 'weak';
    if (score <= 4) return 'medium';
    return 'strong';
  };

  const strength = evaluatePasswordStrength(password);

  // 获取密码强度对应的进度条百分比
  const getStrengthPercentage = (): number => {
    switch (strength) {
      case 'weak': return 33;
      case 'medium': return 66;
      case 'strong': return 100;
      default: return 0;
    }
  };

  // 获取密码强度对应的颜色
  const getStrengthColor = (): string => {
    switch (strength) {
      case 'weak': return '#ff4d4f';
      case 'medium': return '#faad14';
      case 'strong': return '#52c41a';
      default: return '#ff4d4f';
    }
  };

  // 获取密码强度对应的文字
  const getStrengthText = (): string => {
    switch (strength) {
      case 'weak': return '弱';
      case 'medium': return '中';
      case 'strong': return '强';
      default: return '弱';
    }
  };

  return (
    <div
      style={{
        opacity: (showDialog && password) ? 1 : 0,
        pointerEvents: (showDialog && password) ? 'auto' : 'none',
        width: '280px',
        backgroundColor: '#f9f0ff',
        left: "102%",
        top: "0",
        transform: "translateX(10px)",
        position: 'absolute' as const,
        borderRadius: '8px',
        padding: '16px',
        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.2)',
        zIndex: 1360,
        transition: 'opacity 0.3s',
      }}
    >
      <div style={{
        position: 'absolute',
        left: '-8px',
        top: '15px',
        width: '0px',
        height: '0px',
        borderTop: '8px solid transparent',
        borderBottom: '8px solid transparent',
        borderRight: '8px solid rgb(249, 240, 255)'
      }}></div>
      <div className="mb-3">
        <div style={
          {
            fontSize: '14px',
            fontWeight: 'bold',
            marginBottom: '12px',
            color: '#333',
          }
        }>密码强度提示</div>
        <div style={{
          marginBottom: '12px',
        }}>
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            fontSize: '12px',
            color: '#666',
            marginBottom: '4px',
          }}>
            <span >密码强度：</span>
            <span style={{
              color: getStrengthColor(),
              fontWeight: 'bold',
            }}>
              {getStrengthText()}
            </span>
          </div>
        </div>
        <div className="w-full bg-white rounded-full overflow-hidden" style={{ backgroundColor: 'rgba(255,255,255,0.6)' }}>
          <div
            style={{
              transition: 'all 0.3s',
              height: '6px',
              width: `${getStrengthPercentage()}%`,
              backgroundColor: getStrengthColor()
            }}
          ></div>
        </div>
      </div>

      <div style={{
        marginTop: '16px'
      }}>
        <div style={{
          fontSize: '12px',
          fontWeight: 'bold',
          marginBottom: '8px',
          color: '#333',
        }}>密码规则：</div>
        <ul style={{
          margin: 0,
          padding: '0 0 0 16px',
          fontSize: '12px',
          color: '#666',
        }}>
          <li style={{
            marginBottom: '4px',
          }}>密码可由数字、大写英文字母、小写英文字母、特殊字符组成</li>
          <li style={{
            marginBottom: '4px',
          }}>至少由数字、英文字母两种类型组成</li>
          <li style={{
            marginBottom: '4px',
          }}>长度要求8-16位字符</li>
        </ul>
      </div>
    </div>
  );
};

const ProfileModal = ({ isOpen, onClose, points, onUpdate }: ProfileModalProps) => {
  const dispatch = useDispatch()
  const [isEditing, setIsEditing] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const userInfo = useSelector((state: RootState) => state.user.userState)
  const [editForm, setEditForm] = useState({
    nickName: '',
    introduction: '',
    gender: 0,
    isEditingName: false,
    isEditingIntro: false
  })
  const [isUploadingAvatar, setIsUploadingAvatar] = useState(false)
  const router = useRouter()
  const userId = useSelector((state: RootState) => state.user.userState.userId)
  const [stats, setStats] = useState({
    totalWorks: 0,
    totalUsage: 0,
    totalViews: 0
  });
  const [isPasswordModalVisible, setIsPasswordModalVisible] = useState(false)
  const [form] = Form.useForm()
  const [verifyForm] = Form.useForm()
  const [activeTab, setActiveTab] = useState<string>('password')
  const [showCaptcha, setShowCaptcha] = useState(false)
  const [countdown, setCountdown] = useState(0)
  const [verifyCode, setVerifyCode] = useState('')
  const [newPwd, setNewPwd] = useState('')
  const [confirmPwd, setConfirmPwd] = useState('')
  const [studentInfo, setStudentInfo] = useState<{ studentNumber?: string }>({})
  const [showModifyPhone, setShowModifyPhone] = useState(false)
  const [isRedeemKeyModalOpen, setIsRedeemKeyModalOpen] = useState(false)
  // 新增密码强度相关状态
  const [passwordStrength, setPasswordStrength] = useState<PasswordStrength>('weak');
  const [showStrengthDialog, setShowStrengthDialog] = useState(false);
  // 教师认证模态框
  const [isTeacherAuthModalVisible, setIsTeacherAuthModalVisible] = useState(false);
  // 是否有待审核的教师认证
  const [hasPendingTeacherAuth, setHasPendingTeacherAuth] = useState(false);
  // 新增状态：角色选择弹框
  const [showRoleModal, setShowRoleModal] = useState(false)
  // 新增加载状态
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    if (userInfo) {
      setEditForm({
        nickName: userInfo.nickName || '',
        introduction: userInfo.introduction || '',
        gender: userInfo.gender || 0,
        isEditingName: false,
        isEditingIntro: false
      })
    }
  }, [userInfo])

  useEffect(() => {
    if (isOpen && userId) {
      // 打开模态框时设置加载状态为true
      setIsLoading(true)
      
      const fetchUserInfo = async () => {
        try {
          // console.log('哈哈', userId);
          const response = await userApi.getUserInfo(userId)

          if (response && response.code === 200 && response.data) {
            const updatedUserInfo: UserState = {
              userId: response.data.id,
              nickName: response.data.nickName,
              avatarUrl: response.data.avatarUrl,
              gender: response.data.gender,
              phone: response.data.phone,
              introduction: response.data.introduction,
              createTime: response.data.createTime,
              isLoggedIn: true,
              roleId: response.data.roleId,
              role: response.data.role,
              roles: response.data.roles
            };
            dispatch(setUser(updatedUserInfo));

            // 如果是学生角色，获取学生信息
            if (response.data.roleId === 1) {
              try {
                const studentResponse = await studentApi.getStudentInfo(userId)

                if (studentResponse.data.code === 200) {
                  setStudentInfo(studentResponse.data.data);
                }
              } catch (error) {
                console.error('获取学生信息失败:', error);
              }
            }

            setEditForm({
              nickName: response.data.nickName || '',
              introduction: response.data.introduction || '',
              gender: response.data.gender || 0,
              isEditingName: false,
              isEditingIntro: false
            })
          }
        } catch (error) {
          console.error('获取用户信息失败:', error)
        }
      }

      const fetchStats = async () => {
        try {
          const response = await worksApi.getStats(userId);
          if (response && response.data && response.data.code === 200 && response.data.data) {
            setStats({
              totalWorks: response.data.data.totalWorks || 0,
              totalUsage: response.data.data.totalUsage || 0,
              totalViews: response.data.data.totalViews || 0
            })
          }
        } catch (error) {
          console.error('获取用户统计数据失败:', error)
        }
      }

      const fetchStudentInfo = async () => {
        if (userInfo.roleId === 1) { // 只有当用户是学生时才获取学号信息
          try {
            const response = await studentApi.getStudentInfo(userId);
            if (response && response.data && response.data.code === 200 && response.data.data) {
              setStudentInfo({
                studentNumber: response.data.data.studentNumber
              });
            }
          } catch (error) {
            console.error('获取学生信息失败:', error);
          }
        }
      }

      // 检查是否有待审核的教师认证
      const checkTeacherAuthStatus = async () => {
        try {
          const response = await auth.getTeacherAuthStatus();
          // 如果有记录，检查是否是待审核状态
          if (response && response.code === 200 && response.data) {
            // result为0表示待审核，1表示通过，2表示拒绝
            setHasPendingTeacherAuth(response.data.result === 0);
          } else {
            // 没有记录或获取失败，则认为没有待审核记录
            setHasPendingTeacherAuth(false);
          }
        } catch (error) {
          console.error('获取教师认证状态失败:', error);
          setHasPendingTeacherAuth(false);
        }
      }

      // 使用Promise.all并行执行所有请求
      Promise.all([
        fetchUserInfo(), 
        fetchStats(), 
        fetchStudentInfo(), 
        checkTeacherAuthStatus()
      ])
      .catch(error => {
        console.error('加载数据失败:', error)
      })
      .finally(() => {
        // 所有请求完成后，无论成功失败都设置加载状态为false
        setIsLoading(false)
      })
    }
  }, [isOpen, userId, userInfo.roleId, dispatch])

  const handleSave = async () => {
    if (editForm.isEditingName || editForm.isEditingIntro) {
      setIsSaving(true)
      try {
        const response = await userApi.updateProfile(userId, {
          id: userId,
          nickName: editForm.nickName,
          introduction: editForm.introduction,
        })

        if (response.data.code === 200) {
          dispatch(setUser({
            ...userInfo,
            nickName: editForm.nickName,
            introduction: editForm.introduction,
          }))
          if (onUpdate) onUpdate()
        }
      } catch (error) {
        console.error('保存个人信息失败:', error)
      } finally {
        setIsSaving(false)
        setEditForm(prev => ({
          ...prev,
          isEditingName: false,
          isEditingIntro: false
        }))
      }
    }
  }

  const handleAvatarUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    console.log('上传oss');

    const file = e.target.files?.[0];
    if (!file) return;

    if (!file.type.startsWith('image/')) {
      notification.error('请上传图片文件');
      return;
    }

    if (file.size > 2 * 1024 * 1024) {
      notification.error('图片大小不能超过 2MB');
      return;
    }

    try {
      // 记录当前的头像URL，用于在更新成功后删除
      const oldAvatarUrl = userInfo?.avatarUrl;

      setIsUploadingAvatar(true);
      const loadingMsg = notification.loading('正在审核图片内容...');

      // 上传新头像（包含内容审核）
      const url = await uploadApi.uploadToOss(file);

      notification.success('图片审核通过，正在更新...');

      if (!url) {
        notification.error('图片审核未通过，请重新上传');
        if (loadingMsg) loadingMsg.close();
        return;
      }

      // 上传成功后更新用户信息
      const { data } = await userApi.updateAvatar(userId, {
        id: userId,
        avatarUrl: url
      });
      //11zww222222
      if (data.code === 200) {
        // 直接使用服务器返回的数据更新Redux，不再额外请求用户信息
        const userData = data.data;

        const updatedUserInfo: UserState = {
          userId: userData.id,
          nickName: userData.nickName || '',
          avatarUrl: userData.avatarUrl || '', // 这里已经是更新后的头像URL
          gender: userData.gender,
          phone: userData.phone || '',
          introduction: userData.introduction || '',
          isLoggedIn: true,
          createTime: userData.createTime,
          roleId: userData.roleId,
          role: userData.role,
          roles: userData.roles
        };

        dispatch(setUser(updatedUserInfo));
        // 同时更新localStorage中的用户信息，确保刷新页面后仍能保持
        localStorage.setItem('user', JSON.stringify(updatedUserInfo));

        setEditForm({
          nickName: userData.nickName || '',
          introduction: userData.introduction || '',
          gender: userData.gender || 0,
          isEditingName: false,
          isEditingIntro: false
        });

        // 头像更新成功后，删除旧的头像文件（如果存在且不是默认头像）
        if (oldAvatarUrl &&
          oldAvatarUrl.includes('oss-cn-guangzhou.aliyuncs.com') &&
          oldAvatarUrl !== 'https://logicleap.oss-cn-guangzhou.aliyuncs.com/base/c2b0f283138abaad873795e1dd8e18b_resized.png') {
          try {
            await uploadApi.deleteFromOss(oldAvatarUrl);
            console.log('旧头像已删除:', oldAvatarUrl);
          } catch (deleteError) {
            console.error('删除旧头像失败:', deleteError);
            // 不影响用户体验，所以不显示错误消息
          }
          notification.success('头像更新成功');
          onUpdate?.();
        }
      } else {
        // 如果更新失败，删除刚上传的图片，避免OSS中有无用文件
        try {
          await uploadApi.deleteFromOss(url);
          console.log('已删除未使用的头像:', url);
        } catch (deleteError) {
          console.error('删除未使用头像失败:', deleteError);
        }

        notification.error(data.message || '更新失败');
      }

      // 关闭加载提示
      if (loadingMsg) loadingMsg.close();
    } catch (error: any) {
      // 上传失败时不更新Redux
      notification.error(error.message || '头像上传失败');
    } finally {
      setIsUploadingAvatar(false);
    }
  };

  const handleUpdatePassword = async (values: { oldPassword: string; newPassword: string }) => {
    try {
      // 密码校验
      if (!values.oldPassword || !values.newPassword) {
        notification.error('请输入原密码和新密码');
        return;
      }

      // 密码强度校验
      if (values.newPassword.length < 6) {
        notification.error('新密码长度不能少于6位');
        return;
      }

      // 获取用户手机号
      const userPhone = userInfo?.phone;
      let response;

      // 根据是否有手机号选择不同的修改密码方式
      if (userPhone) {
        // 如果有手机号，使用手机号方式修改密码
        response = await userApi.resetPasswordByPhone(values.oldPassword, values.newPassword, userPhone);
      } else {
        // 如果没有手机号，使用用户ID方式修改密码
        response = await userApi.updatePassword(values.oldPassword, values.newPassword, userId);
      }

      if (response.code === 200) {
        notification.success('密码修改成功，请重新登录！');
        setIsPasswordModalVisible(false);
        form.resetFields();

        localStorage.removeItem('token');
        localStorage.removeItem('user');

        setTimeout(() => {
          window.location.reload();
        }, 200);
      } else {
        notification.error(response.data.message || '密码修改失败，请检查原密码是否正确');
      }
    } catch (error) {
      notification.error('密码修改失败，请检查原密码是否正确');
    }
  };

  // 添加日期格式化函数
  const formatDate = (dateString: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  // 处理发送验证码
  const handleSendCode = async () => {
    // 从表单或userInfo中获取手机号
    const phoneNumber = verifyForm.getFieldValue('phone') || userInfo?.phone;

    if (!phoneNumber) {
      notification.error('获取手机号失败或者没有绑定手机号，请刷新页面重试');
      return;
    }

    // 确保在打开验证码滑块前将手机号设置到表单中
    verifyForm.setFieldsValue({ phone: phoneNumber });

    setShowCaptcha(true);
  };

  // 人机验证处理成功回调
  const handleCaptchaSuccess = async () => {
    try {
      const { data: response } = await userApi.sendVerifyCode(userInfo?.phone || '');

      if (response.data && response.data.code === 200) {
        // 即使 code 为 200，也要检查 success 字段
        if (response.data.success === false) {
          // success 明确为 false 时表示业务失败
          notification.error(response.data.message || response.data.msg || '发送验证码失败');
        } else {
          // code 为 200 且 success 不为 false 时才是成功
          notification.success('验证码已发送');
          setShowCaptcha(false);
          setCountdown(60);
          const timer = setInterval(() => {
            setCountdown(prev => {
              if (prev <= 1) {
                clearInterval(timer);
                return 0;
              }
              return prev - 1;
            });
          }, 1000);
        }
      } else if (response.data && response.data.success === true) {
        // success 明确为 true 时也是成功
        notification.success('验证码已发送');
        setShowCaptcha(false);
        setCountdown(60);
        const timer = setInterval(() => {
          setCountdown(prev => {
            if (prev <= 1) {
              clearInterval(timer);
              return 0;
            }
            return prev - 1;
          });
        }, 1000);
      } else {
        notification.error(response.data?.message || response.data?.msg || '发送验证码失败');
      }
    } catch (error: any) {
      console.error('发送验证码失败:', error);
      if (error.response && error.response.data) {
        notification.error(error.response.data.msg || error.response.data.message || '发送验证码失败');
      } else {
        notification.error('发送验证码失败，请稍后再试');
      }
    } finally {
      setShowCaptcha(false);
    }
  };

  // 处理密码输入变化
  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newPassword = e.target.value;
    setNewPwd(newPassword);
    form.setFieldValue('newPassword', newPassword);
    setPasswordStrength(evaluatePasswordStrength(newPassword));
  };

  // 处理验证码表单密码输入变化
  const handleVerifyPasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newPassword = e.target.value;
    setNewPwd(newPassword);
    verifyForm.setFieldValue('newPassword', newPassword);
    setPasswordStrength(evaluatePasswordStrength(newPassword));
  };

  // 处理验证码重置密码
  const handleVerifySubmit = async () => {
    if (newPwd !== confirmPwd) {
      notification.error('两次输入的密码不一致');
      return;
    }

    try {
      // 从表单或userInfo中获取手机号
      const phoneNumber = verifyForm.getFieldValue('phone') || userInfo?.phone;

      if (!phoneNumber) {
        notification.error('未能获取手机号，请刷新页面重试');
        return;
      }

      const response = await auth.resetPasswordByCode({
        phone: phoneNumber,
        code: verifyCode,
        newPassword: newPwd
      });

      if (response.data.code === 200) {
        notification.success('密码重置成功，请重新登录！');
        setIsPasswordModalVisible(false);
        verifyForm.resetFields();

        localStorage.removeItem('token');
        localStorage.removeItem('user');

        setTimeout(() => {
          window.location.reload();
        }, 200);
      } else {
        notification.error(response.data.message || '密码重置失败');
      }
    } catch (error) {
      console.error('重置密码失败:', error);
      notification.error('密码重置失败');
    }
  };

  // 评估密码强度
  const evaluatePasswordStrength = (pwd: string): PasswordStrength => {
    if (!pwd) return 'weak';

    // 计算密码强度
    let score = 0;

    // 长度检查
    if (pwd.length >= 8) score += 1;
    if (pwd.length >= 12) score += 1;

    // 包含数字
    if (/\d/.test(pwd)) score += 1;

    // 包含小写字母
    if (/[a-z]/.test(pwd)) score += 1;

    // 包含大写字母
    if (/[A-Z]/.test(pwd)) score += 1;

    // 包含特殊字符
    if (/[^A-Za-z0-9]/.test(pwd)) score += 1;

    // 根据得分确定强度
    if (score <= 2) return 'weak';
    if (score <= 4) return 'medium';
    return 'strong';
  };

  // 获取密码强度对应的进度条百分比
  const getStrengthPercentage = (): number => {
    switch (passwordStrength) {
      case 'weak': return 33;
      case 'medium': return 66;
      case 'strong': return 100;
      default: return 0;
    }
  };

  // 获取密码强度对应的颜色
  const getStrengthColor = (): string => {
    switch (passwordStrength) {
      case 'weak': return '#ff4d4f';
      case 'medium': return '#faad14';
      case 'strong': return '#52c41a';
      default: return '#ff4d4f';
    }
  };

  // 获取密码强度对应的文字
  const getStrengthText = (): string => {
    switch (passwordStrength) {
      case 'weak': return '弱';
      case 'medium': return '中';
      case 'strong': return '强';
      default: return '弱';
    }
  };

  // 处理密码输入框获取焦点
  const handlePasswordFocus = () => {
    setShowStrengthDialog(true);
  };

  // 处理密码输入框失去焦点
  const handlePasswordBlur = () => {
    setShowStrengthDialog(false);
  };

  // 处理Tab切换
  const handleTabChange = (key: string) => {
    // 清空表单
    if (key === 'password') {
      verifyForm.resetFields();
    } else if (key === 'verify') {
      form.resetFields();
    }
    setActiveTab(key);
  };

  // 处理角色选择
  const handleRoleClick = () => {
    // 如果没有角色且不在加载状态，则打开角色选择弹框
    if ((!userInfo?.role?.name || userInfo?.role?.name === '暂无角色') && !isLoading) {
      setShowRoleModal(true);
    }
  };

  if (!isOpen) return null


  return (
    <div
      className="fixed inset-0 bg-black/80 flex items-center justify-center z-[100] overflow-hidden"
      onClick={(e) => {
        if (e.target === e.currentTarget) {
          onClose();
        }
      }}
      onWheel={(e) => e.stopPropagation()}
    >
      <div
        className="bg-white rounded-2xl w-[520px] max-h-[85vh] flex flex-col relative"
        onClick={(e) => e.stopPropagation()}
      >
        {/* 弹窗头部 */}
        <div className="flex items-center justify-between px-6 py-4 border-b border-gray-100">
          <h3 className="text-lg font-semibold text-gray-800">个人中心</h3>
          <button
            onClick={onClose}
            className="p-1 hover:bg-gray-100 rounded-full transition-colors"
            disabled={isLoading}
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* 加载指示器 */}
        {isLoading && (
          <div className="absolute inset-0 bg-white/80 flex items-center justify-center z-10 rounded-2xl">
            <div className="flex flex-col items-center">
              <div className="w-10 h-10 border-4 border-blue-200 border-t-blue-500 rounded-full animate-spin"></div>
              <p className="mt-3 text-gray-600">加载中...</p>
            </div>
          </div>
        )}

        {/* 滚动的内容域 */}
        <div
          className="flex-1 overflow-y-auto px-6 py-6"
          style={{ overscrollBehavior: 'contain' }}
        >
          {/* 用基本信息 */}
          <div className="flex items-center space-x-4">
            <div className="relative group">
              {userInfo?.avatarUrl ? (
                <img
                  src={userInfo.avatarUrl}
                  alt="用户头像"
                  width={80}
                  height={80}
                  className="rounded-full w-20 h-20 object-cover border-2 border-blue-100"
                  onError={(e) => {
                    const target = e.currentTarget as HTMLImageElement;
                    target.style.display = 'none';
                    const parent = target.parentElement;
                    if (parent) {
                      // 创建一个替代的头像显示
                      const fallbackDiv = document.createElement('div');
                      fallbackDiv.className = 'w-20 h-20 rounded-full bg-blue-50 flex items-center justify-center border-2 border-blue-100';
                      
                      // 创建用户图标
                      const iconDiv = document.createElement('div');
                      iconDiv.className = 'text-blue-500';
                      iconDiv.style.width = '48px';
                      iconDiv.style.height = '48px';
                      iconDiv.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="8" r="5"></circle><path d="M20 21a8 8 0 0 0-16 0"></path></svg>';
                      
                      fallbackDiv.appendChild(iconDiv);
                      parent.appendChild(fallbackDiv);
                    }
                  }}
                />
              ) : (
                <div className="w-20 h-20 rounded-full bg-blue-50 flex items-center justify-center border-2 border-blue-100">
                  <UserCircle className="w-12 h-12 text-blue-500" />
                </div>
              )}
              <label className={`absolute inset-0 bg-black/50 rounded-full opacity-0 group-hover:opacity-100 
                transition-opacity flex items-center justify-center text-white text-sm cursor-pointer
                ${isLoading ? 'pointer-events-none' : ''}`}>
                {isUploadingAvatar ? (
                  <span className="animate-pulse">上传中...</span>
                ) : (
                  <>
                    更换头像
                    <input
                      type="file"
                      className="hidden"
                      accept="image/*"
                      onChange={handleAvatarUpload}
                      disabled={isUploadingAvatar || isLoading}
                    />
                  </>
                )}
              </label>
            </div>
            <div className="flex-1">
              <div
                className={`group relative cursor-pointer ${isLoading ? 'pointer-events-none' : ''}`}
                onClick={() => !isLoading && setEditForm(prev => ({ ...prev, isEditingName: true }))}
              >
                {editForm.isEditingName ? (
                  <input
                    type="text"
                    value={editForm.nickName}
                    onChange={(e) => setEditForm(prev => ({ ...prev, nickName: e.target.value }))}
                    onBlur={handleSave}
                    onKeyDown={(e) => e.key === 'Enter' && handleSave()}
                    className="w-full px-3 py-2 border border-gray-200 rounded-lg text-gray-800 text-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                    placeholder="请输入昵称"
                    maxLength={10}
                    autoFocus
                    disabled={isLoading}
                  />
                ) : (
                  <div className="flex items-center justify-between">
                    <h4 className="text-xl font-medium text-gray-800 group-hover:bg-blue-50 px-2 py-1 rounded-lg transition-colors">
                      {userInfo?.nickName || '用户'}
                    </h4>
                  </div>
                )}
              </div>
              <p className="text-sm text-gray-500 mt-1">
                {userInfo?.phone || '未绑定手机号'}
              </p>
            </div>
          </div>

          {/* 简介部分 */}
          <div className="mt-6 p-4 bg-blue-50/50 rounded-xl">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-700">个人简介</span>
            </div>
            <div
              className={`group relative cursor-pointer ${isLoading ? 'pointer-events-none' : ''}`}
              onClick={() => !isLoading && setEditForm(prev => ({ ...prev, isEditingIntro: true }))}
            >
              {editForm.isEditingIntro ? (
                <textarea
                  value={editForm.introduction}
                  onChange={(e) => setEditForm(prev => ({ ...prev, introduction: e.target.value }))}
                  onBlur={handleSave}
                  className="w-full px-3 py-2 border border-gray-200 rounded-lg text-gray-600 text-sm min-h-[80px] focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                  placeholder="介绍一下自己吧..."
                  maxLength={100}
                  autoFocus
                  disabled={isLoading}
                />
              ) : (
                <p className="text-sm text-gray-600 leading-relaxed group-hover:bg-blue-50 p-2 rounded-lg transition-colors">
                  {editForm.introduction || '这个人很懒，还没有写简介~'}
                </p>
              )}
            </div>
          </div>

          {/* 性别选择 - 仅在编辑模式显示 */}
          {/* {isEditing && (
            <div className="mt-6 p-4 bg-gray-50 rounded-xl">
              <span className="text-sm font-medium text-gray-700 block mb-3">性别</span>
              <div className="flex space-x-4">
                {[
                  { value: 0, label: '保密' },
                  { value: 1, label: '男' },
                  { value: 2, label: '女' }
                ].map((option) => (
                  <label key={option.value} className="flex items-center space-x-2 cursor-pointer">
                    <input
                      type="radio"
                      checked={editForm.gender === option.value}
                      onChange={() => setEditForm(prev => ({ ...prev, gender: option.value }))}
                      className="form-radio text-blue-600"
                    />
                    <span className="text-sm text-gray-600">{option.label}</span>
                  </label>
                ))}
              </div>
            </div>
          )} */}

          {/* 用户数据统计 */}
          <div className="grid grid-cols-3 gap-4 mt-6">
            <div className="text-center p-4 bg-blue-50/50 rounded-xl transition-colors hover:bg-blue-50">
              <div className="text-xl font-semibold text-gray-800">{points}</div>
              <div className="text-sm text-gray-500 mt-1">能量值</div>
            </div>
            <div className="text-center p-4 bg-blue-50/50 rounded-xl transition-colors hover:bg-blue-50">
              <div className="text-xl font-semibold text-gray-800">{stats.totalWorks}</div>
              <div className="text-sm text-gray-500 mt-1">作品数</div>
            </div>
            <div 
              className={`text-center p-4 ${!userInfo?.role?.name || userInfo?.role?.name === '暂无角色' 
                ? 'bg-blue-100/50 rounded-xl transition-colors hover:bg-blue-100 cursor-pointer'
                : 'bg-blue-50/50 rounded-xl transition-colors hover:bg-blue-50'} 
                ${isLoading ? 'pointer-events-none opacity-75' : ''}`}
              onClick={handleRoleClick}
            >
              <div className="text-xl font-semibold text-gray-800">
                {isLoading ? '加载中...' : (userInfo?.role?.name || '暂无角色')}
              </div>
              <div className="text-sm text-gray-500 mt-1">
                {isLoading ? '请稍候' : (!userInfo?.role?.name || userInfo?.role?.name === '暂无角色'
                  ? '点击选择角色'
                  : '角色')}
              </div>
            </div>
          </div>

          {/* 账号信息 */}
          <div className="mt-8">
            <h5 className="text-sm font-medium text-gray-700 mb-4">账号信息</h5>
            <div className="space-y-2">
              {/* 兑换密钥 - 移到手机号前面 */}
              <div className={`flex items-center px-4 py-3.5 bg-yellow-50/80 rounded-xl transition-all hover:bg-yellow-50 border border-yellow-100 
                ${isLoading ? 'opacity-75' : ''}`}>
                <div className="flex-1">
                  <div className="text-sm text-yellow-700">兑换密钥</div>
                  <div className="mt-0.5 text-sm font-medium text-yellow-800">
                    输入兑换密钥获取能量
                  </div>
                </div>
                <button
                  onClick={() => !isLoading && setIsRedeemKeyModalOpen(true)}
                  className="flex items-center px-3 py-1.5 text-sm text-yellow-600 bg-yellow-100 hover:bg-yellow-200 rounded-lg transition-colors"
                  disabled={isLoading}
                >
                  <Key className="w-4 h-4 mr-1.5 text-yellow-500" />
                  兑换密钥
                </button>
              </div>

              {/* 教师认证按钮 - 仅当用户是普通用户(roleId === 3)时显示，且没有待审核的记录 */}
              {userInfo.roleId === 3 && !hasPendingTeacherAuth && (
                <div className={`flex items-center px-4 py-3.5 bg-blue-50/80 rounded-xl transition-all hover:bg-blue-50 border border-blue-100 
                  ${isLoading ? 'opacity-75' : ''}`}>
                  <div className="flex-1">
                    <div className="text-sm text-blue-700">教师认证</div>
                    <div className="mt-0.5 text-sm font-medium text-blue-800">
                      认证为教师身份，获取更多权限
                    </div>
                  </div>
                  <button
                    onClick={() => !isLoading && setIsTeacherAuthModalVisible(true)}
                    className="flex items-center px-3 py-1.5 text-sm text-blue-600 bg-blue-100 hover:bg-blue-200 rounded-lg transition-colors"
                    disabled={isLoading}
                  >
                    <Clipboard className="w-4 h-4 mr-1.5 text-blue-500" />
                    申请认证
                  </button>
                </div>
              )}

              {/* 显示已经提交认证的状态提示 */}
              {userInfo.roleId === 3 && hasPendingTeacherAuth && (
                <div className={`flex items-center px-4 py-3.5 bg-gray-50/80 rounded-xl transition-all hover:bg-gray-50 border border-gray-100 
                  ${isLoading ? 'opacity-75' : ''}`}>
                  <div className="flex-1">
                    <div className="text-sm text-gray-700">教师认证</div>
                    <div className="mt-0.5 text-sm font-medium text-gray-800">
                      您的教师认证申请正在审核中
                    </div>
                  </div>
                </div>
              )}

              {/* 手机号 - 移到兑换密钥后面 */}
              <div className={`flex items-center px-4 py-3.5 bg-blue-50/50 rounded-xl transition-all hover:bg-blue-50 
                ${isLoading ? 'opacity-75' : ''}`}>
                <div className="flex-1">
                  <div className="text-sm text-gray-500">手机号</div>
                  <div className="mt-0.5 text-sm font-medium text-gray-700">
                    {userInfo?.phone || '未绑定'}
                  </div>
                </div>
                <button
                  onClick={() => !isLoading && setShowModifyPhone(true)}
                  className="px-3 py-1.5 text-sm text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                  disabled={isLoading}
                >
                  {userInfo?.phone ? "修改" : "去绑定"}
                </button>
              </div>

              {/* 学号信息 - 仅对学生显示 */}
              {userInfo.roleId === 1 && (
                <div className={`flex items-center px-4 py-3.5 bg-blue-50/50 rounded-xl transition-all hover:bg-blue-50 
                  ${isLoading ? 'opacity-75' : ''}`}>
                  <div className="flex-1">
                    <div className="text-sm text-gray-500">学号</div>
                    <div className="mt-0.5 text-sm font-medium text-gray-700">
                      {studentInfo?.studentNumber || '未绑定'}
                    </div>
                  </div>
                </div>
              )}

              {/* 账号状态 */}
              <div className={`flex items-center px-4 py-3.5 bg-blue-50/50 rounded-xl transition-all hover:bg-blue-50 
                ${isLoading ? 'opacity-75' : ''}`}>
                <div className="flex-1">
                  <div className="text-sm text-gray-500">账号状态</div>
                  <div className="mt-0.5 text-sm font-medium text-emerald-600">正常</div>
                </div>
              </div>

              {/* 登录密码 */}
              <div className={`flex items-center justify-between px-4 py-3.5 bg-blue-50/50 rounded-xl transition-all hover:bg-blue-50 
                ${isLoading ? 'opacity-75' : ''}`}>
                <div className="flex-1">
                  <div className="text-sm text-gray-500">登录密码</div>
                  <div className="mt-0.5 text-sm font-medium text-gray-700">
                    ••••••••
                  </div>
                </div>
                <button
                  onClick={() => !isLoading && setIsPasswordModalVisible(true)}
                  className="text-sm text-blue-600 hover:text-blue-700 transition-colors"
                  disabled={isLoading}
                >
                  修改
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* 底部按钮 - 固定在底部 */}
        <div className="px-6 py-4 border-t border-gray-100 bg-white rounded-b-2xl">
          {/* 可以放其他操作按钮，如有需要 */}
        </div>
      </div>

      <Modal
        title="修改密码"
        open={isPasswordModalVisible && !isLoading}
        onCancel={() => {
          setIsPasswordModalVisible(false);
          form.resetFields();
          verifyForm.resetFields();
          setActiveTab('password');
        }}
        footer={null}
        width={420}
        className="password-modal"
        centered
        modalRender={(modal) => (
          <div onClick={(e) => e.stopPropagation()}>
            {modal}
          </div>
        )}
        styles={{
          mask: {
            backgroundColor: 'rgba(0, 0, 0, 0.65)',
          },
          header: {
            padding: '20px 24px',
            marginBottom: '8px',
            borderBottom: '1px solid #f0f0f0'
          },
          body: {
            padding: '0 24px 24px',
          },
          content: {
            padding: 0,
            borderRadius: '16px',
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
          }
        }}
      >
        <Tabs
          activeKey={activeTab}
          onChange={handleTabChange}
          className="mb-4"
          items={[
            {
              key: 'password',
              label: '密码验证',
              children: (
                <Form
                  form={form}
                  onFinish={handleUpdatePassword}
                  layout="vertical"
                  className="password-form"
                >
                  {/* 原密码输入框 */}
                  <Form.Item
                    name="oldPassword"
                    label={<span className="text-gray-700 font-medium">原密码</span>}
                    rules={[{ required: true, message: '请输入原密码' }]}
                  >
                    <Input.Password
                      placeholder="请输入原密码"
                      className="h-10 text-sm rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      iconRender={visible => (
                        <div className="flex items-center justify-center w-8 h-8 hover:bg-gray-100 rounded-full transition-colors">
                          {visible ? (
                            <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                              <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                              <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
                            </svg>
                          ) : (
                            <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z" clipRule="evenodd" />
                              <path d="M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z" />
                            </svg>
                          )}
                        </div>
                      )}
                    />
                  </Form.Item>

                  {/* 新密码输入框 */}
                  <Form.Item
                    name="newPassword"
                    label={<span className="text-gray-700 font-medium">新密码</span>}
                    rules={[
                      { required: true, message: '请输入新密码' },
                      { min: 6, message: '密码长度不能小于6位' }
                    ]}
                  >
                    <div className="relative">
                      <Input.Password
                        placeholder="请输入新密码"
                        className="h-10 text-sm rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        onChange={handlePasswordChange}
                        onFocus={handlePasswordFocus}
                        onBlur={handlePasswordBlur}
                        iconRender={visible => (
                          <div className="flex items-center justify-center w-8 h-8 hover:bg-gray-100 rounded-full transition-colors">
                            {visible ? (
                              <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                                <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                                <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
                              </svg>
                            ) : (
                              <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z" clipRule="evenodd" />
                                <path d="M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z" />
                              </svg>
                            )}
                          </div>
                        )}
                      />
                      <PasswordStrengthTip
                        password={form.getFieldValue('newPassword') || ''}
                        showDialog={showStrengthDialog}
                      />
                    </div>
                  </Form.Item>

                  {/* 确认新密码输入框 */}
                  <Form.Item
                    name="confirmPassword"
                    label={<span className="text-gray-700 font-medium">确认新密码</span>}
                    dependencies={['newPassword']}
                    rules={[
                      { required: true, message: '请确认新密码' },
                      ({ getFieldValue }) => ({
                        validator(_, value) {
                          if (!value || getFieldValue('newPassword') === value) {
                            return Promise.resolve()
                          }
                          return Promise.reject(new Error('两次输入的密码不一致'))
                        },
                      }),
                    ]}
                  >
                    <Input.Password
                      placeholder="请再次输入新密码"
                      className="h-10 text-sm rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      iconRender={visible => (
                        <div className="flex items-center justify-center w-8 h-8 hover:bg-gray-100 rounded-full transition-colors">
                          {visible ? (
                            <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                              <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                              <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
                            </svg>
                          ) : (
                            <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z" clipRule="evenodd" />
                              <path d="M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z" />
                            </svg>
                          )}
                        </div>
                      )}
                    />
                  </Form.Item>

                  {/* 底部按钮 */}
                  <Form.Item className="mb-0 mt-8">
                    <div className="flex gap-3 justify-end">
                      <button
                        type="button"
                        onClick={() => {
                          setIsPasswordModalVisible(false)
                          form.resetFields()
                        }}
                        className="px-5 py-2 border border-gray-200 text-gray-600 rounded-lg text-sm hover:bg-gray-50 transition-colors"
                      >
                        取消
                      </button>
                      <button
                        type="submit"
                        className="px-5 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm transition-colors shadow-sm"
                      >
                        确认修改
                      </button>
                    </div>
                  </Form.Item>
                </Form>
              )
            },
            {
              key: 'verify',
              label: '验证码验证',
              children: (
                <Form
                  form={verifyForm}
                  layout="vertical"
                  onFinish={handleVerifySubmit}
                >
                  <Form.Item
                    name="phone"
                    label={<span className="text-gray-700 font-medium">手机号</span>}
                    initialValue={userInfo?.phone || ''}
                  >
                    <Input
                      className="h-10 text-sm rounded-lg bg-gray-100"
                      disabled
                      suffix={
                        <div className="text-xs text-gray-500">
                          (账户绑定手机号)
                        </div>
                      }
                    />
                  </Form.Item>

                  <Form.Item
                    name="verifyCode"
                    label={<span className="text-gray-700 font-medium">验证码</span>}
                    rules={[{ required: true, message: '请输入验证码' }]}
                  >
                    <div className="flex gap-2">
                      <Input
                        placeholder="请输入验证码"
                        autoComplete="off"
                        className="h-10 text-sm rounded-lg"
                        onChange={e => {
                          setVerifyCode(e.target.value);
                          verifyForm.setFieldValue('verifyCode', e.target.value);
                        }}
                      />
                      <button
                        type="button"
                        onClick={handleSendCode}
                        disabled={countdown > 0 || !userInfo?.phone}
                        className="min-w-[120px] px-4 py-2 bg-blue-500 text-white rounded disabled:bg-gray-300 h-10"
                      >
                        {countdown > 0 ? `${countdown}s` : '获取验证码'}
                      </button>
                    </div>
                  </Form.Item>

                  <Form.Item
                    name="newPassword"
                    label={<span className="text-gray-700 font-medium">新密码</span>}
                    rules={[
                      { required: true, message: '请输入新密码' },
                      { min: 6, message: '密码长度不能小于6位' }
                    ]}
                  >
                    <div className="relative">
                      <Input.Password
                        placeholder="请输入新密码"
                        className="h-10 text-sm rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        onChange={handleVerifyPasswordChange}
                        onFocus={handlePasswordFocus}
                        onBlur={handlePasswordBlur}
                        iconRender={visible => (
                          <div className="flex items-center justify-center w-8 h-8 hover:bg-gray-100 rounded-full transition-colors">
                            {visible ? (
                              <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                                <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                                <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
                              </svg>
                            ) : (
                              <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z" clipRule="evenodd" />
                                <path d="M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z" />
                              </svg>
                            )}
                          </div>
                        )}
                      />
                      <PasswordStrengthTip
                        password={verifyForm.getFieldValue('newPassword') || ''}
                        showDialog={showStrengthDialog}
                      />
                    </div>
                  </Form.Item>

                  <Form.Item
                    name="confirmPassword"
                    label={<span className="text-gray-700 font-medium">确认密码</span>}

                    rules={[
                      { required: true, message: '请确认新密码' },
                      ({ getFieldValue }) => ({
                        validator(_, value) {
                          if (!value || getFieldValue('newPassword') === value) {
                            return Promise.resolve()
                          }
                          return Promise.reject(new Error('两次输入的密码不一致'))
                        },
                      }),
                    ]}
                  >
                    <Input.Password
                      placeholder="请再次输入新密码"
                      autoComplete="new-password"
                      className="h-10 text-sm rounded-lg"
                      onChange={e => {
                        setConfirmPwd(e.target.value);
                        verifyForm.setFieldValue('confirmPassword', e.target.value);
                      }}
                      iconRender={visible => (
                        <div className="flex items-center justify-center w-8 h-8 hover:bg-gray-100 rounded-full transition-colors">
                          {visible ? (
                            <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                              <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                              <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
                            </svg>
                          ) : (
                            <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z" clipRule="evenodd" />
                              <path d="M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z" />
                            </svg>
                          )}
                        </div>
                      )}
                    />
                  </Form.Item>

                  <Form.Item className="mb-0 mt-8">
                    <div className="flex gap-3 justify-end">
                      <button
                        type="button"
                        onClick={() => {
                          setIsPasswordModalVisible(false);
                          verifyForm.resetFields();
                        }}
                        className="px-5 py-2 border border-gray-200 text-gray-600 rounded-lg text-sm hover:bg-gray-50 transition-colors"
                      >
                        取消
                      </button>
                      <button
                        type="submit"
                        className="px-5 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm transition-colors shadow-sm"
                      >
                        确认修改
                      </button>
                    </div>
                  </Form.Item>
                </Form>
              )
            }
          ]}
        />
      </Modal>

      {/* 滑动验证组件 */}
      <SlideCaptcha
        isOpen={showCaptcha}
        onClose={() => setShowCaptcha(false)}
        onSuccess={handleCaptchaSuccess}
      />

      <ModifyPhoneModal
        isOpen={showModifyPhone}
        onClose={() => setShowModifyPhone(false)}
        currentPhone={userInfo?.phone || ''}
        onSuccess={() => {
          // 清除用户登录信息
          localStorage.removeItem('token');
          localStorage.removeItem('user');

          // 显示成功消息
          notification.success(userInfo?.phone ? '手机号修改成功，请重新登录' : '手机号绑定成功，请重新登录');

          // 延迟刷新页面
          setTimeout(() => {
            window.location.reload();
          }, 200);
        }}
      />

      {/* 密钥兑换模态框 */}
      {userId && (
        <RedeemKeyModal
          isOpen={isRedeemKeyModalOpen}
          onClose={() => setIsRedeemKeyModalOpen(false)}
          userId={userId}
        />
      )}

      {/* 教师认证模态框 */}
      {userId && (
        <TeacherAuthModal
          visible={isTeacherAuthModalVisible}
          handleCloseTeacherAuthModal={() => setIsTeacherAuthModalVisible(false)}
          onSuccess={() => {
            // 关闭教师认证模态框
            setIsTeacherAuthModalVisible(false);
            // 设置有待审核的认证
            setHasPendingTeacherAuth(true);
            // 刷新用户信息
            if (onUpdate) onUpdate();
            // 显示成功消息，内部组件以实现，不需弹框
            // GetNotification().success('教师认证申请已提交，请等待审核');
          }}
        />
      )}

      {/* 角色选择弹框 */}
      {showRoleModal && (
        <RoleSelectionModalForNewUser
          onSuccess={() => {
            setShowRoleModal(false);
            // 刷新用户信息，使用现有的用户信息刷新方法
            if (userId) {
              userApi.getUserInfo(userId).then(response => {
                if (response && response.code === 200 && response.data) {
                  const updatedUserInfo: UserState = {
                    userId: response.data.id,
                    nickName: response.data.nickName,
                    avatarUrl: response.data.avatarUrl,
                    gender: response.data.gender,
                    phone: response.data.phone,
                    introduction: response.data.introduction,
                    createTime: response.data.createTime,
                    isLoggedIn: true,
                    roleId: response.data.roleId,
                    role: response.data.role,
                    roles: response.data.roles
                  };
                  dispatch(setUser(updatedUserInfo));
                }
              }).catch(error => {
                console.error('获取用户信息失败:', error);
              });
            }
          }}
          handleCloseRsmfnModal={() => setShowRoleModal(false)}
        />
      )}
    </div>
  )
}

export default ProfileModal 