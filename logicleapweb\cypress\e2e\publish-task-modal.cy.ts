describe('发布任务弹窗表单验证和数据清空', () => {
  beforeEach(() => {
    // 访问工作台页面
    cy.visit('/workbench');
    
    // 模拟登录状态
    cy.window().then((win) => {
      win.localStorage.setItem('user', JSON.stringify({
        userId: 1,
        teacherId: 1,
        nickname: '测试老师'
      }));
    });
  });

  it('应该在没有输入任务名称和设置时间时禁用开始上课按钮', () => {
    // 打开发布任务弹窗（假设有触发按钮）
    cy.get('[data-testid="publish-task-btn"]').click();
    
    // 选择班级后进入任务发布页面
    cy.get('[data-testid="class-selection"]').first().click();
    cy.get('[data-testid="confirm-class-btn"]').click();
    
    // 验证初始状态下开始上课按钮是禁用的
    cy.get('.start-class-btn').should('have.class', 'disabled');
    cy.get('.start-class-btn').should('be.disabled');
    
    // 只输入任务名称，按钮仍应禁用
    cy.get('input[placeholder="任务名称"]').type('测试任务');
    cy.get('.start-class-btn').should('have.class', 'disabled');
    cy.get('.start-class-btn').should('be.disabled');
    
    // 设置开始时间，按钮仍应禁用
    cy.get('input[placeholder="点击设置开始时间"]').click();
    cy.get('.quick-time-btn').contains('现在').click();
    cy.get('.start-class-btn').should('have.class', 'disabled');
    cy.get('.start-class-btn').should('be.disabled');
    
    // 设置结束时间，按钮应该启用
    cy.get('input[placeholder="点击设置结束时间"]').click();
    cy.get('.quick-time-btn').contains('1小时').click();
    cy.get('.start-class-btn').should('have.class', 'enabled');
    cy.get('.start-class-btn').should('not.be.disabled');
  });

  it('应该在点击上一步时清空所有表单数据', () => {
    // 打开发布任务弹窗
    cy.get('[data-testid="publish-task-btn"]').click();
    cy.get('[data-testid="class-selection"]').first().click();
    cy.get('[data-testid="confirm-class-btn"]').click();
    
    // 填写表单数据
    cy.get('input[placeholder="任务名称"]').type('测试任务名称');
    cy.get('textarea[placeholder="任务描述"]').type('这是一个测试任务的描述');
    
    // 设置时间
    cy.get('input[placeholder="点击设置开始时间"]').click();
    cy.get('.quick-time-btn').contains('现在').click();
    cy.get('input[placeholder="点击设置结束时间"]').click();
    cy.get('.quick-time-btn').contains('1小时').click();
    
    // 验证表单已填写
    cy.get('input[placeholder="任务名称"]').should('have.value', '测试任务名称');
    cy.get('textarea[placeholder="任务描述"]').should('have.value', '这是一个测试任务的描述');
    cy.get('.start-class-btn').should('have.class', 'enabled');
    
    // 点击上一步
    cy.get('.prev-btn').click();
    
    // 重新打开弹窗验证数据已清空
    cy.get('[data-testid="publish-task-btn"]').click();
    cy.get('[data-testid="class-selection"]').first().click();
    cy.get('[data-testid="confirm-class-btn"]').click();
    
    // 验证表单已清空
    cy.get('input[placeholder="任务名称"]').should('have.value', '');
    cy.get('textarea[placeholder="任务描述"]').should('have.value', '');
    cy.get('input[placeholder="点击设置开始时间"]').should('not.have.class', 'has-selected-date');
    cy.get('input[placeholder="点击设置结束时间"]').should('not.have.class', 'has-selected-date');
    cy.get('.start-class-btn').should('have.class', 'disabled');
  });

  it('应该在关闭弹窗时清空所有表单数据', () => {
    // 打开发布任务弹窗
    cy.get('[data-testid="publish-task-btn"]').click();
    cy.get('[data-testid="class-selection"]').first().click();
    cy.get('[data-testid="confirm-class-btn"]').click();
    
    // 填写表单数据
    cy.get('input[placeholder="任务名称"]').type('测试任务名称');
    cy.get('textarea[placeholder="任务描述"]').type('这是一个测试任务的描述');
    
    // 关闭弹窗
    cy.get('.modal-close-btn-outside').click();
    
    // 重新打开弹窗验证数据已清空
    cy.get('[data-testid="publish-task-btn"]').click();
    cy.get('[data-testid="class-selection"]').first().click();
    cy.get('[data-testid="confirm-class-btn"]').click();
    
    // 验证表单已清空
    cy.get('input[placeholder="任务名称"]').should('have.value', '');
    cy.get('textarea[placeholder="任务描述"]').should('have.value', '');
    cy.get('.start-class-btn').should('have.class', 'disabled');
  });

  it('应该在切换标签页后保持表单数据', () => {
    // 打开发布任务弹窗
    cy.get('[data-testid="publish-task-btn"]').click();
    cy.get('[data-testid="class-selection"]').first().click();
    cy.get('[data-testid="confirm-class-btn"]').click();
    
    // 填写任务信息
    cy.get('input[placeholder="任务名称"]').type('测试任务');
    
    // 切换到资源与附件标签页
    cy.get('.tab-btn').contains('资源与附件').click();
    
    // 切换回任务信息标签页
    cy.get('.tab-btn').contains('任务信息').click();
    
    // 验证数据仍然存在
    cy.get('input[placeholder="任务名称"]').should('have.value', '测试任务');
  });
});
