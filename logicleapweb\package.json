{"name": "logicleapweb", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build --no-lint", "start": "next start -p 3000 -H 0.0.0.0", "cy": "cypress open", "cy:slow": "cross-env CYPRESS_delayBetweenCommands=200 cypress open", "lint": "next lint", "update-common": "npm install --save https://github.com/zhulezai/logic-common.git --legacy-peer-deps"}, "dependencies": {"@ant-design/charts": "^2.6.0", "@heroicons/react": "^2.2.0", "@icon-park/react": "^1.4.2", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tabs": "^1.1.1", "@reduxjs/toolkit": "^2.4.0", "@types/axios": "^0.14.4", "@types/qrcode": "^1.5.5", "@types/react-redux": "^7.1.34", "@types/react-slick": "^0.23.13", "@types/uuid": "^10.0.0", "@types/video.js": "^7.3.58", "@uiw/react-md-editor": "^4.0.5", "ali-oss": "^6.22.0", "antd": "^5.22.2", "axios": "^1.7.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cos-js-sdk-v5": "^1.8.6", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "file-saver": "^2.0.5", "framer-motion": "^11.15.0", "geist": "^1.3.1", "html2canvas": "^1.4.1", "html2pdf.js": "^0.10.3", "http-proxy-middleware": "^3.0.5", "jose": "^6.0.10", "jspdf": "^3.0.1", "jszip": "^3.10.1", "logic-common": "github:zhulezai/logic-common", "lucide-react": "^0.460.0", "markdown-it": "^14.1.0", "next": "14.2.16", "qrcode": "^1.5.4", "react": "^18", "react-dom": "^18", "react-icons": "^5.5.0", "react-markdown": "^9.0.3", "react-markdown-editor-lite": "^1.3.4", "react-masonry-css": "^1.0.16", "react-redux": "^9.1.2", "react-slick": "^0.30.3", "rehype-raw": "^7.0.0", "shadcn-ui": "^0.9.3", "slick-carousel": "^1.8.1", "socket.io-client": "^4.8.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "video.js": "^8.21.0", "wiz-editor": "^0.0.767", "wiz-editor-react": "^0.0.428", "xlsx": "^0.18.5"}, "devDependencies": {"@shadcn/ui": "^0.0.4", "@types/ali-oss": "^6.16.11", "@types/crypto-js": "^4.2.2", "@types/file-saver": "^2.0.7", "@types/lodash": "^4.17.14", "@types/markdown-it": "^14.1.2", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "cross-env": "^7.0.3", "cypress": "^14.5.0", "eslint": "^8", "eslint-config-next": "14.2.16", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}