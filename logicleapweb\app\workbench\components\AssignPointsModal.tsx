'use client'

import { useState, useEffect, useMemo } from 'react';
import { Modal, Form, Input, InputNumber, Button, Alert, Avatar, Tooltip } from 'antd';
import { InfoCircleOutlined, CheckOutlined, UserOutlined, ThunderboltOutlined, KeyOutlined, ClockCircleOutlined, WarningOutlined } from '@ant-design/icons';
import { pointsApi } from '@/lib/api/points';
import { packageApi } from '@/lib/api/package';
import dayjs from 'dayjs';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
dayjs.extend(isSameOrBefore);
import { useWindowSize } from '@/hooks/useWindowSize';
import { GetNotification } from 'logic-common/dist/components/Notification';

// 定义Student接口
interface Student {
  id: number;
  userId: number;
  nickName: string;
  studentNumber: string;
  avatarUrl?: string;
  totalPoints?: number;
  availablePoints?: number;
}

interface AssignPointsModalProps {
  visible: boolean;
  onCancel: () => void;
  onOk: (params: { availablePoints: number; studentExpiries?: { [id: number]: string | undefined }; remark?: string }) => Promise<void>;
  studentName: string;
  studentId: number;
  userId: number;
  student: Student | null;
  isBatch?: boolean;
  selectedStudents: number[];
  students: Student[];
  onSuccess?: () => void;
  refreshStudentList: () => Promise<void>;
  onGoToRedeemKey?: (studentIds: number[]) => void;
}

export const AssignPointsModal = ({
  visible,
  onCancel,
  onOk,
  studentName,
  studentId,
  userId,
  student,
  isBatch,
  selectedStudents,
  students,
  onSuccess,
  refreshStudentList,
  onGoToRedeemKey
}: AssignPointsModalProps) => {
  const { width: windowWidth } = useWindowSize();
  const [availablePoints, setAvailablePoints] = useState<number | null>(null);
  const [studentPoints, setStudentPoints] = useState<number>(0);
  const [isLoading, setIsLoading] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [studentPointsMap, setStudentPointsMap] = useState<Map<number, number>>(new Map());
  const notification = GetNotification();

  // 获取学生积分信息
  useEffect(() => {
    if (visible) {
      if (isBatch && selectedStudents.length > 0) {
        fetchBatchStudentPoints();
      } else if (student) {
        fetchSingleStudentPoints();
      }
    }
  }, [visible, isBatch, selectedStudents, student]);

  const fetchSingleStudentPoints = async () => {
    if (!student) return;
    try {
      const response = await pointsApi.getBatchStudentPoints([student.userId]);
      console.log('单个学生积分响应:', response);

      if (response.data.code === 200) {
        const userData = response.data.data[student.userId];
        console.log('学生数据:', userData);

        if (userData) {
          const totalPoints = userData.totalPoints || 0;
          const availablePoints = userData.availablePoints || 0;
          const remainingPoints = totalPoints - availablePoints;
          console.log('计算结果:', { totalPoints, availablePoints, remainingPoints });
          setStudentPoints(remainingPoints);
        } else {
          console.log('未找到学生数据，设置为0');
          setStudentPoints(0);
        }
      } else {
        console.log('API返回错误:', response.data);
        setStudentPoints(0);
        notification.error('获取学生可用能量失败');
      }
    } catch (error) {
      console.error('获取学生积分失败:', error);
      setStudentPoints(0);
      notification.error('获取学生可用能量失败');
    }
  };

  const fetchBatchStudentPoints = async () => {
    try {
      const response = await pointsApi.getBatchStudentPoints(selectedStudents);
      console.log('批量学生积分响应:', response);

      if (response.data.code === 200) {
        const pointsMap = new Map();
        const data = response.data.data;
        console.log('批量学生数据:', data);

        for (const userId in data) {
          const totalPoints = data[userId]?.totalPoints || 0;
          const availablePoints = data[userId]?.availablePoints || 0;
          const remainingPoints = totalPoints - availablePoints;
          console.log(`学生${userId}:`, { totalPoints, availablePoints, remainingPoints });
          pointsMap.set(Number(userId), remainingPoints);
        }

        setStudentPointsMap(pointsMap);

        // 如果是批量模式，设置最小剩余积分作为显示值
        if (pointsMap.size > 0) {
          const minPoints = Math.min(...Array.from(pointsMap.values()));
          console.log('最小剩余积分:', minPoints);
          setStudentPoints(minPoints);
        }
      } else {
        console.log('批量API返回错误:', response.data);
        selectedStudents.forEach(sid => setStudentPointsMap(prev => new Map(prev.set(sid, 0))));
        setStudentPoints(0);
        notification.error(response.data.message || '批量获取学生能量失败');
      }
    } catch (error) {
      console.error('批量获取学生积分失败:', error);
      setStudentPoints(0);
      notification.error('批量获取学生能量失败');
    }
  };

  const handleSubmit = async () => {
    if (availablePoints === null || availablePoints <= 0) {
      notification.error('请输入有效的分配能量数量');
      return;
    }

    if (!isBatch && availablePoints > studentPoints) {
      notification.error('分配能量不能超过学生剩余的能量');
      return;
    }
    if (isBatch && availablePoints > studentPoints) {
      notification.error(`分配能量不能超过所选学生中的最低剩余能量 (${studentPoints})`);
      return;
    }

    setIsLoading(true);

    try {
      let studentExpiries: { [studentId: number]: string | undefined } = {};
      const defaultExpireTime = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString();
      const targetStudentIds = isBatch ? selectedStudents : [userId];

      // 获取每个学生的套餐过期时间
      for (const sid of targetStudentIds) {
        try {
          const { packageApi } = await import('../../../lib/api/package');
          const packageResponse = await packageApi.getUserCurrentPackage(sid);
          if (packageResponse.data.code === 1000 && packageResponse.data.data?.expireTime) {
            studentExpiries[sid] = packageResponse.data.data.expireTime;
          } else {
            studentExpiries[sid] = defaultExpireTime;
          }
        } catch (error) {
          console.error(`获取学生 ${sid} 套餐失败:`, error);
          studentExpiries[sid] = defaultExpireTime;
        }
      }

      await onOk({
        availablePoints: availablePoints,
        studentExpiries: studentExpiries,
        remark: isBatch ? `批量分配能量 - ${availablePoints}` : `分配能量 - ${availablePoints}`
      });

      setShowSuccess(true);
      onSuccess?.();

      setTimeout(() => {
        setShowSuccess(false);
        setAvailablePoints(null);
        onCancel();
      }, 1500);

    } catch (error: any) {
      console.error('分配能量失败:', error);
      notification.error(error.response?.data?.message || '分配能量失败');
    } finally {
      setIsLoading(false);
    }
  };

  const modalWidth = useMemo(() => {
    if (windowWidth <= 768) return '95%';
    return 600;
  }, [windowWidth]);

  return (
    <Modal
      title={
        <div className="flex items-center justify-between text-lg font-medium">
          <div className="flex items-center gap-2 text-gradient bg-gradient-to-r from-yellow-500 to-orange-500 bg-clip-text text-transparent">
            <ThunderboltOutlined className="text-yellow-500" />
            <span>{isBatch ? '批量分配能量' : '分配能量'}</span>
          </div>
          <div className="px-3 py-1.5 rounded-full bg-blue-50 text-blue-700 text-sm font-medium shadow-sm">
            {isBatch
              ? `已选择 ${selectedStudents.length} 名学生`
              : student
                ? <div className="flex items-center gap-2">
                  <Avatar size={20} src={student.avatarUrl} icon={<UserOutlined />} />
                  <span>{student.nickName || `学生${student.studentNumber || student.userId}`}</span>
                </div>
                : ''
            }
          </div>
        </div>
      }
      open={visible}
      onCancel={() => {
        setAvailablePoints(null);
        onCancel();
      }}
      footer={null}
      width={modalWidth}
      centered
      destroyOnClose
      maskClosable={false}
      className="assign-points-modal"
    >
      <div className="space-y-6">
        {showSuccess ? (
          <div className="text-center py-8">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mb-4">
              <CheckOutlined className="text-2xl text-green-600" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">分配成功！</h3>
            <p className="text-gray-600">能量已成功分配给{isBatch ? `${selectedStudents.length}名学生` : (student?.nickName || `学生${student?.studentNumber || student?.userId}`)}</p>
          </div>
        ) : (
          <>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1.5">分配能量数量</label>
              <div className="relative">
                <InputNumber
                  value={availablePoints}
                  onChange={(value) => {
                    // 只允许正整数，不允许0和负数
                    if (value === null || value === undefined) {
                      setAvailablePoints(null);
                    } else if (Number(value) > 0) {
                      setAvailablePoints(Number(value));
                    }
                  }}
                  placeholder="请输入分配数量"
                  className={`w-full h-11 rounded-xl text-base focus:border-blue-500 focus:ring-1 focus:ring-blue-500 ${availablePoints && availablePoints > studentPoints ? 'text-red-500 border-red-300 focus:border-red-500 focus:ring-red-500' : ''}`}
                  min={1}
                  controls={false}
                  formatter={(value) => value === null ? '' : `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  parser={(value) => {
                    if (!value) return 0;
                    const parsed = Number(value.replace(/\D/g, ''));
                    return parsed > 0 ? parsed : 0;
                  }}
                  precision={0}
                />
                <div className="absolute right-3 top-1/2 -translate-y-1/2 text-sm text-gray-400">
                  / {studentPoints.toLocaleString()} 剩余
                </div>
              </div>
              {availablePoints && availablePoints > studentPoints && (
                <div className="text-xs text-red-500 mt-1 flex items-center gap-1">
                  <WarningOutlined />可分配积分不足，请前往兑换密钥兑换套餐
                </div>
              )}
            </div>

            <Alert
              icon={<InfoCircleOutlined />}
              message="温馨提示"
              description={
                <div className="space-y-1 text-sm">
                  <div className="flex items-center gap-2">
                    <CheckOutlined className="text-green-500 text-xs" />
                    <span>请留意学生的可用能量是否足够本次分配。</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <InfoCircleOutlined className="text-blue-500 text-xs" />
                    <span>分配的能量将从学生的套餐中扣除。</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <ClockCircleOutlined className="text-orange-500 text-xs" />
                    <span>能量的过期时间与学生当前套餐的过期时间保持一致。</span>
                  </div>
                </div>
              }
              type="info"
              showIcon
              className="bg-blue-50 border-blue-200"
            />

            <div className="flex justify-between items-center pt-4 border-t">
              <Button
                icon={<KeyOutlined />}
                onClick={() => {
                  const targetStudents = isBatch ? selectedStudents : (student ? [student.userId] : []);
                  onGoToRedeemKey?.(targetStudents);
                }}
                className="flex items-center gap-2 text-blue-600 hover:text-blue-700"
                type="link"
              >
                前往兑换密钥
              </Button>

              <div className="flex gap-3">
                <Button
                  onClick={() => {
                    setAvailablePoints(null);
                    onCancel();
                  }}
                  className="px-6 h-10 rounded-lg"
                >
                  取消
                </Button>
                <Button
                  type="primary"
                  onClick={handleSubmit}
                  loading={isLoading}
                  disabled={!availablePoints || availablePoints <= 0}
                  className="px-6 h-10 rounded-lg bg-blue-600 hover:bg-blue-700"
                >
                  确认分配
                </Button>
              </div>
            </div>
          </>
        )}
      </div>
    </Modal>
  );
};
