'use client'

import { useState } from 'react';
import { message } from 'antd';
import Image from 'next/image';
import userApi from '@/lib/api/user';
import { useDispatch } from 'react-redux';
import { setUser } from '@/lib/store';
import { COLORS } from './colors';

interface Role {
  userId: number;
  roleId: number;
  roleName: string;
  nickName: string;
  avatar?: string;
  schoolInfo?: {
    province: string;
    city: string;
    district: string;
    schoolName: string;
  };
  studentNumber?: string;
}

interface InlineChooseAccountProps {
  roleList: Role[];
  sessionId: string;
  onSuccess: (userId?: number) => void;
}

const InlineChooseAccount: React.FC<InlineChooseAccountProps> = ({
  roleList,
  sessionId,
  onSuccess
}) => {
  const [loading, setLoading] = useState(false);
  const [selectedRole, setSelectedRole] = useState<Role | null>(null);
  const dispatch = useDispatch();

  const handleSelectRole = async () => {
    if (!selectedRole) {
      message.error('请选择一个账号');
      return;
    }

    try {
      setLoading(true);
      
      // 选择角色
      const response = await userApi.selectIdentity(selectedRole.userId, sessionId);
      
      console.log("选择的角色为",response);
      
      if (response.code === 200) {
//  / 从响应体获取data.userinfo 然后存到本地存储
        if (!response.data?.userInfo) {
          throw new Error('获取用户信息失败');
        }
        
        const data = response.data;
        const userInfo = data.userInfo;
        
        // 保存token和用户信息
        localStorage.setItem('token', data.token);
        localStorage.setItem('refreshToken', data.refreshToken || '');
        localStorage.setItem('userId', userInfo.id.toString());
        
        // 构建更完整的用户数据对象
        const userData = {
          userId: userInfo.id,
          nickName: userInfo.nickName || '',
          avatarUrl: userInfo.avatar || '',
          gender: userInfo.gender || 0,
          phone: userInfo.phone || '',
          isLoggedIn: true,
          roleId: userInfo.roleId || 0,
          registerType: userInfo.register_type || '',
          needSetPwd:userInfo.needSetPwd
        };
        
        // 保存新的完整的用户信息
console.log("保存新的完整的用户信息",userData);
        localStorage.setItem('user', JSON.stringify(userData));
        // 更新Redux状态
        dispatch(setUser(userData));
        
        // 手机号验证码登录多账户可能出现一种情况，就是选择的账户没有设置密码，此时进行回传值决定是否设置密码弹框
        onSuccess(userInfo.id);
      } else {
        message.error(response.message || '角色选择失败');
      }
    } catch (error) {
      console.error('角色选择失败:', error);
      message.error('角色选择失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="w-full">
      <div className="text-center mb-6">
        <h2 className={`text-xl font-semibold ${COLORS.text.white}`}>请选择您要登录的账号</h2>
        <p className={`mt-2 text-sm ${COLORS.text.white}`}>您有多个角色账号，请选择一个进行登录</p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        {roleList.map((role) => (
          <div
            key={role.userId + '-' + role.roleId}
            className={`p-4 rounded-xl border cursor-pointer transition-all ${
              selectedRole?.userId === role.userId
                ? 'border-blue-500 bg-blue-500/10'
                : `${COLORS.border.white_dim} ${COLORS.bg.white_dim} ${COLORS.bg.white_dim_hover}`
            }`}
            onClick={() => setSelectedRole(role)}
          >
            <div className="flex items-center">
              <div className="w-12 h-12 rounded-full bg-white/10 flex items-center justify-center overflow-hidden">
                {role.avatar ? (
                  <Image
                    src={role.avatar}
                    alt={role.roleName}
                    width={48}
                    height={48}
                    className="object-cover"
                  />
                ) : (
                  <div className="w-12 h-12 rounded-full bg-blue-500/30 flex items-center justify-center text-white text-xl font-medium">
                    {role.nickName?.[0] || role.roleName?.[0] || '?'}
                  </div>
                )}
              </div>
              
              <div className="ml-3">
                <div className={`font-medium ${COLORS.text.primary}`}>{role.nickName || '未命名用户'}</div>
                <div className={`text-xs ${COLORS.text.primary}`}>
                  {role.roleName}
                  {role.schoolInfo?.schoolName && <span> · {role.schoolInfo.schoolName}</span>}
                  {role.schoolInfo?.province && <span> · {role.schoolInfo.province}{role.schoolInfo.city}</span>}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
      
      {/* 使用自定义按钮替代Ant Design Button */}
      <button
        onClick={handleSelectRole}
        disabled={!selectedRole || loading}
        className={`h-12 w-full ${COLORS.bg.accent} ${COLORS.text.white} rounded-xl ${COLORS.bg.accent_hover} border-0 flex items-center justify-center`}
      >
        {loading ? (
          <>
            <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            加载中...
          </>
        ) : (
          '确认选择'
        )}
      </button>
    </div>
  );
};

export default InlineChooseAccount; 