import React from 'react';
import { <PERSON>, Button, Tooltip, Popconfirm } from 'antd';
import { EditOutlined, DeleteOutlined, EyeOutlined } from '@ant-design/icons';
import { useRouter } from 'next/navigation';
import { activityApi } from '@/lib/api/activity';
import { GetNotification } from 'logic-common/dist/components/Notification';

interface Activity {
  id: number;
  name: string;
}

interface ActivityActionsProps {
  record: Activity;
  onDelete: () => void;
}

const ActivityActions: React.FC<ActivityActionsProps> = ({ record, onDelete }) => {
  const router = useRouter();

  const handleDelete = async () => {
    const notification = GetNotification();
    try {
      await activityApi.delete(record.id);
      notification.success('活动删除成功');
      onDelete(); // 调用父组件传入的刷新方法
    } catch (error) {
      console.error('删除活动失败:', error);
      notification.error('删除活动失败');
    }
  };

  return (
    <Space size="middle">
      <Tooltip title="查看">
        <Button
          type="text"
          icon={<EyeOutlined />}
          onClick={() => router.push(`/activity/${record.id}`)}
        />
      </Tooltip>
      <Tooltip title="编辑">
        <Button
          type="text"
          icon={<EditOutlined />}
          onClick={() => router.push(`/activity/edit/${record.id}`)}
        />
      </Tooltip>
      <Tooltip title="删除">
        <Popconfirm
          title="确定要删除这个活动吗?"
          onConfirm={handleDelete}
          okText="确定"
          cancelText="取消"
        >
          <Button
            type="text"
            danger
            icon={<DeleteOutlined />}
          />
        </Popconfirm>
      </Tooltip>
    </Space>
  );
};

export default ActivityActions; 