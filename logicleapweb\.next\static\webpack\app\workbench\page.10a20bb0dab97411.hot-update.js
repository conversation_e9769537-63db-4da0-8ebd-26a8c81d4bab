"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/workbench/page",{

/***/ "(app-pages-browser)/./app/workbench/components/LeftSidebar.tsx":
/*!**************************************************!*\
  !*** ./app/workbench/components/LeftSidebar.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftCircle,Book,Briefcase,ChevronDown,LayoutDashboard,ListTodo,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftCircle,Book,Briefcase,ChevronDown,LayoutDashboard,ListTodo,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftCircle,Book,Briefcase,ChevronDown,LayoutDashboard,ListTodo,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/list-todo.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftCircle,Book,Briefcase,ChevronDown,LayoutDashboard,ListTodo,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftCircle,Book,Briefcase,ChevronDown,LayoutDashboard,ListTodo,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftCircle,Book,Briefcase,ChevronDown,LayoutDashboard,ListTodo,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftCircle,Book,Briefcase,ChevronDown,LayoutDashboard,ListTodo,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftCircle,Book,Briefcase,ChevronDown,LayoutDashboard,ListTodo,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftCircle,Book,Briefcase,ChevronDown,LayoutDashboard,ListTodo,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-arrow-left.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _lib_api_school__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api/school */ \"(app-pages-browser)/./lib/api/school.ts\");\n/* harmony import */ var _lib_api_class__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api/class */ \"(app-pages-browser)/./lib/api/class.ts\");\n/* harmony import */ var _lib_utils_address__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils/address */ \"(app-pages-browser)/./lib/utils/address.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst LeftSidebar = (param)=>{\n    let { userInfo, onMenuItemClick, onSchoolSelect, onClassesUpdate } = param;\n    _s();\n    const [activeItem, setActiveItem] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"快速开始\");\n    const [isClassDropdownOpen, setIsClassDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [schools, setSchools] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedSchool, setSelectedSchool] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [schoolsLoading, setSchoolsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [schoolsError, setSchoolsError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 监听activeItem变化，当进入班级管理页面时自动打开下拉菜单\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"activeItem 状态变化:\", activeItem);\n        // 当切换到班级管理页面时，自动打开下拉菜单\n        if (activeItem === \"班级管理\") {\n            setIsClassDropdownOpen(true);\n        }\n    }, [\n        activeItem\n    ]);\n    // 下拉菜单的ref，用于检测点击外部区域\n    const dropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 标志位，防止导航点击和外部点击冲突\n    const isNavigatingRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // 点击外部区域关闭下拉菜单并切换到班级管理页面\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleClickOutside = (event)=>{\n            if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n                console.log(\"点击外部区域，isNavigating:\", isNavigatingRef.current);\n                // 如果正在导航，不处理外部点击\n                if (isNavigatingRef.current) {\n                    isNavigatingRef.current = false;\n                    return;\n                }\n                // 如果当前活跃项是班级管理，不关闭下拉菜单\n                if (activeItem === \"班级管理\") {\n                    return;\n                }\n                // 关闭下拉菜单\n                setIsClassDropdownOpen(false);\n                // 如果有选中的学校，切换到班级管理页面\n                if (selectedSchool) {\n                    setActiveItem(\"班级管理\");\n                    onMenuItemClick === null || onMenuItemClick === void 0 ? void 0 : onMenuItemClick(\"班级管理\");\n                }\n            }\n        };\n        if (isClassDropdownOpen) {\n            document.addEventListener(\"mousedown\", handleClickOutside);\n        }\n        return ()=>{\n            document.removeEventListener(\"mousedown\", handleClickOutside);\n        };\n    }, [\n        isClassDropdownOpen,\n        selectedSchool,\n        onMenuItemClick,\n        activeItem\n    ]);\n    // 监听自定义事件来关闭下拉菜单\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleCloseDropdown = ()=>{\n            setIsClassDropdownOpen(false);\n        };\n        document.addEventListener(\"closeDropdown\", handleCloseDropdown);\n        return ()=>{\n            document.removeEventListener(\"closeDropdown\", handleCloseDropdown);\n        };\n    }, []);\n    // 获取教师管理的学校列表\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchSchools();\n    }, []);\n    const navItems = [\n        {\n            icon: _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            name: \"快速开始\",\n            hasDivider: true\n        },\n        {\n            icon: _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            name: \"班级管理\",\n            hasDropdown: true\n        },\n        {\n            icon: _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            name: \"班级任务\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            name: \"班级项目\",\n            hasDivider: true\n        },\n        {\n            icon: BookOpen,\n            name: \"官方课程\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            name: \"课程管理\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            name: \"模板管理\"\n        }\n    ];\n    // 处理班级管理点击\n    const handleClassManagementClick = ()=>{\n        // 设置为活跃状态\n        setActiveItem(\"班级管理\");\n        // 如果没有选中学校且有可用学校，自动选择第一个学校\n        if (!selectedSchool && schools.length > 0) {\n            const firstSchool = schools[0];\n            setSelectedSchool(firstSchool);\n            onSchoolSelect === null || onSchoolSelect === void 0 ? void 0 : onSchoolSelect(firstSchool);\n            fetchClasses(firstSchool.id);\n            console.log(\"班级管理：自动选择第一个学校:\", firstSchool);\n        } else if (!selectedSchool && schools.length === 0 && !schoolsLoading) {\n            // 如果没有学校数据且不在加载中，重新获取学校列表\n            console.log(\"班级管理：没有学校数据，重新获取学校列表\");\n            fetchSchools();\n        }\n        // 如果当前已经是班级管理页面且下拉菜单已打开，则关闭；否则打开\n        if (activeItem === \"班级管理\" && isClassDropdownOpen) {\n            setIsClassDropdownOpen(false);\n        } else {\n            setIsClassDropdownOpen(true);\n        }\n        // 通知父组件\n        onMenuItemClick === null || onMenuItemClick === void 0 ? void 0 : onMenuItemClick(\"班级管理\");\n    };\n    // 处理学校选择\n    const handleSchoolSelect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((school)=>{\n        console.log(\"handleSchoolSelect 被调用，当前activeItem:\", activeItem);\n        // 不关闭下拉菜单，只更新选中状态\n        setSelectedSchool(school);\n        // 强制切换到班级管理页面（无论当前在什么页面）\n        setActiveItem(\"班级管理\");\n        onMenuItemClick === null || onMenuItemClick === void 0 ? void 0 : onMenuItemClick(\"班级管理\");\n        // 始终通知父组件学校选择变化（用于数据更新）\n        onSchoolSelect === null || onSchoolSelect === void 0 ? void 0 : onSchoolSelect(school);\n        // 获取该学校的班级列表\n        fetchClasses(school.id);\n    }, [\n        onMenuItemClick,\n        onSchoolSelect\n    ]);\n    // 处理返回主页\n    const handleBackToHome = ()=>{\n        console.log(\"点击返回主页按钮\");\n        // 获取当前域名和端口，然后跳转到home页面\n        const currentOrigin = window.location.origin;\n        const homeUrl = \"\".concat(currentOrigin, \"/home\");\n        console.log(\"当前域名:\", currentOrigin);\n        console.log(\"跳转到:\", homeUrl);\n        // 直接跳转到home页面\n        window.location.href = homeUrl;\n    };\n    // 获取学校列表\n    const fetchSchools = async ()=>{\n        setSchoolsLoading(true);\n        setSchoolsError(null);\n        try {\n            var _response_data, _response_data1, _response_data2, _response_data3;\n            const response = await (0,_lib_api_school__WEBPACK_IMPORTED_MODULE_3__.getTeacherSchools)();\n            console.log(\"获取学校列表API响应:\", response);\n            // 检查多种可能的响应格式\n            let schoolList = [];\n            if (((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.code) === 200 && ((_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.data)) {\n                schoolList = response.data.data;\n            } else if (((_response_data2 = response.data) === null || _response_data2 === void 0 ? void 0 : _response_data2.status) === 200 && ((_response_data3 = response.data) === null || _response_data3 === void 0 ? void 0 : _response_data3.data)) {\n                schoolList = response.data.data;\n            } else if (Array.isArray(response.data)) {\n                schoolList = response.data;\n            }\n            if (schoolList.length > 0) {\n                setSchools(schoolList);\n                const firstSchool = schoolList[0];\n                setSelectedSchool(firstSchool);\n                // 通知父组件学校选择变化\n                onSchoolSelect === null || onSchoolSelect === void 0 ? void 0 : onSchoolSelect(firstSchool);\n                // 获取第一个学校的班级列表\n                fetchClasses(firstSchool.id);\n                console.log(\"成功获取学校列表，数量:\", schoolList.length);\n                console.log(\"自动选择第一个学校:\", firstSchool);\n            } else {\n                setSchoolsError(\"暂无数据\");\n            }\n        } catch (error) {\n            console.error(\"获取学校列表失败:\", error);\n            setSchoolsError(\"请检查网络连接失败\");\n        } finally{\n            setSchoolsLoading(false);\n        }\n    };\n    // 获取指定学校的班级列表\n    const fetchClasses = async (schoolId)=>{\n        if (!(userInfo === null || userInfo === void 0 ? void 0 : userInfo.id)) {\n            console.log(\"用户未登录，无法获取班级列表\");\n            return;\n        }\n        // 通知父组件开始加载\n        onClassesUpdate && onClassesUpdate([], true, null);\n        try {\n            var _response_data;\n            console.log(\"获取班级列表:\", {\n                schoolId,\n                teacherId: userInfo.id\n            });\n            const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_4__.classApi.getTeacherClasses(schoolId, userInfo.id);\n            console.log(\"班级列表API响应:\", response);\n            if (((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.code) === 200) {\n                const classList = response.data.data || [];\n                // 通知父组件数据更新\n                onClassesUpdate && onClassesUpdate(classList, false, null);\n                console.log(\"成功获取班级列表，数量:\", classList.length);\n            } else {\n                const errorMsg = \"获取班级列表失败\";\n                // 通知父组件错误状态\n                onClassesUpdate && onClassesUpdate([], false, errorMsg);\n            }\n        } catch (error) {\n            console.error(\"获取班级列表失败:\", error);\n            const errorMsg = \"请检查网络连接\";\n            // 通知父组件错误状态\n            onClassesUpdate && onClassesUpdate([], false, errorMsg);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: \"left-sidebar\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"sidebar-header\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                        lineNumber: 276,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-lg font-bold\",\n                        children: \"教师空间\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                        lineNumber: 277,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                lineNumber: 275,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"teacher-info\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        src: userInfo.avatarUrl || \"/images/xiaoluo-default.webp\",\n                        alt: userInfo.nickName || \"小洛头像\",\n                        width: 40,\n                        height: 40,\n                        className: \"avatar\",\n                        style: {\n                            backgroundColor: \"white\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                        lineNumber: 281,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"teacher-details\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"teacher-name\",\n                                children: userInfo.nickName || \"未登录\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                lineNumber: 290,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"teacher-title\",\n                                children: \"教师\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                        lineNumber: 289,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                lineNumber: 280,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"sidebar-nav\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                    children: navItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), {\n                            children: [\n                                item.hasDropdown ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"nav-item-dropdown\",\n                                    ref: dropdownRef,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"nav-item \".concat(activeItem === item.name ? \"active\" : \"\", \" \").concat(isClassDropdownOpen ? \"dropdown-open\" : \"\"),\n                                            onClick: (e)=>{\n                                                e.preventDefault();\n                                                e.stopPropagation();\n                                                handleClassManagementClick();\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                    className: \"nav-icon\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                    lineNumber: 308,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: item.name\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                    lineNumber: 309,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"dropdown-arrow \".concat(isClassDropdownOpen ? \"rotated\" : \"\"),\n                                                    size: 16,\n                                                    style: {\n                                                        transform: isClassDropdownOpen ? \"rotate(180deg)\" : \"rotate(0deg)\",\n                                                        transition: \"transform 0.3s ease\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                    lineNumber: 310,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                            lineNumber: 300,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        isClassDropdownOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"dropdown-menu \".concat(schoolsLoading || schoolsError || schools.length === 0 ? \"empty\" : \"\"),\n                                            onClick: (e)=>{\n                                                e.preventDefault();\n                                                e.stopPropagation();\n                                            },\n                                            children: schoolsLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"dropdown-item disabled loading\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"loading-spinner\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                        lineNumber: 329,\n                                                        columnNumber: 27\n                                                    }, undefined),\n                                                    \"正在加载学校信息...\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                lineNumber: 328,\n                                                columnNumber: 25\n                                            }, undefined) : schoolsError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"dropdown-item disabled error\",\n                                                children: schoolsError\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                lineNumber: 333,\n                                                columnNumber: 25\n                                            }, undefined) : schools.length > 0 ? schools.map((school)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"dropdown-item \".concat((selectedSchool === null || selectedSchool === void 0 ? void 0 : selectedSchool.id) === school.id ? \"selected\" : \"\"),\n                                                    onClick: (e)=>{\n                                                        e.preventDefault();\n                                                        e.stopPropagation();\n                                                        handleSchoolSelect(school);\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"school-info\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"school-name\",\n                                                                children: school.schoolName\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                                lineNumber: 348,\n                                                                columnNumber: 31\n                                                            }, undefined),\n                                                            school.province && school.district && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"school-location\",\n                                                                children: (0,_lib_utils_address__WEBPACK_IMPORTED_MODULE_5__.formatSchoolAddress)(school.province, school.city, school.district)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                                lineNumber: 350,\n                                                                columnNumber: 33\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                        lineNumber: 347,\n                                                        columnNumber: 29\n                                                    }, undefined)\n                                                }, school.id, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 27\n                                                }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"dropdown-item disabled no-data\",\n                                                children: \"暂无数据\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                lineNumber: 358,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"nav-item \".concat(activeItem === item.name ? \"active\" : \"\"),\n                                    onClick: (e)=>{\n                                        e.preventDefault();\n                                        e.stopPropagation();\n                                        console.log(\"点击导航项:\", item.name);\n                                        console.log(\"当前下拉菜单状态:\", isClassDropdownOpen);\n                                        console.log(\"当前活跃项:\", activeItem);\n                                        // 设置导航标志，防止外部点击干扰\n                                        isNavigatingRef.current = true;\n                                        // 先关闭下拉菜单\n                                        setIsClassDropdownOpen(false);\n                                        // 然后更新活跃项\n                                        setActiveItem(item.name);\n                                        // 最后通知父组件\n                                        onMenuItemClick === null || onMenuItemClick === void 0 ? void 0 : onMenuItemClick(item.name);\n                                        console.log(\"完成设置 - 活跃项:\", item.name, \"下拉菜单已关闭\");\n                                        // 延迟重置标志位\n                                        setTimeout(()=>{\n                                            isNavigatingRef.current = false;\n                                        }, 100);\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                            className: \"nav-icon\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                            lineNumber: 395,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: item.name\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                            lineNumber: 396,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                    lineNumber: 366,\n                                    columnNumber: 17\n                                }, undefined),\n                                item.hasDivider && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"nav-divider\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                    lineNumber: 399,\n                                    columnNumber: 35\n                                }, undefined)\n                            ]\n                        }, item.name, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                            lineNumber: 297,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                    lineNumber: 295,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                lineNumber: 294,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"sidebar-footer\",\n                onClick: handleBackToHome,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        className: \"nav-icon\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                        lineNumber: 405,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"返回主页\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                        lineNumber: 406,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                lineNumber: 404,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n        lineNumber: 274,\n        columnNumber: 5\n    }, undefined);\n};\n_s(LeftSidebar, \"sRB/Ml9AeQvFB94OE2ocIHTANvQ=\");\n_c = LeftSidebar;\n/* harmony default export */ __webpack_exports__[\"default\"] = (LeftSidebar);\nvar _c;\n$RefreshReg$(_c, \"LeftSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/workbench/components/LeftSidebar.tsx\n"));

/***/ })

});