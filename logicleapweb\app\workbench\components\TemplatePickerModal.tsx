'use client';

import React, { useState, useEffect } from 'react';
import { X } from 'lucide-react';
import { getRoleTemplateList, getOfficialTemplates } from '@/lib/api/role';
import { useSelector } from 'react-redux';
import { RootState } from '@/lib/store';
import './TemplatePickerModal.css';

interface Template {
  id: number;
  templateName: string;
  templateDescription?: string;
  createTime: string;
  updateTime?: string;
  userId: number;
  roleId: number;
  status: number;
  category: 'my' | 'official';
  isOfficial?: boolean;
  usageCount?: number;
}

interface TemplatePickerModalProps {
  isOpen: boolean;
  onClose: () => void;
  onTemplateSelect: (template: Template) => void;
}

const TemplatePickerModal: React.FC<TemplatePickerModalProps> = ({
  isOpen,
  onClose,
  onTemplateSelect
}) => {
  const [activeTab, setActiveTab] = useState<'my' | 'official'>('my');
  const [templates, setTemplates] = useState<Template[]>([]);
  const [loading, setLoading] = useState(false);
  const [mounted, setMounted] = useState(false);
  const [showOfficialDetail, setShowOfficialDetail] = useState(false);
  const [detailTemplates, setDetailTemplates] = useState<Template[]>([]);

  const userId = useSelector((state: RootState) => state.user.userState.userId);

  // 防止水合错误
  useEffect(() => {
    setMounted(true);
  }, []);

  // 获取我的模板
  const fetchMyTemplates = async () => {
    if (!userId) return [];
    
    try {
      const response = await getRoleTemplateList(userId);
      if (response.data.code === 200) {
        return response.data.data.map((template: any) => ({
          ...template,
          category: 'my' as const,
          isOfficial: false
        }));
      }
    } catch (error) {
      console.error('获取我的模板失败:', error);
    }
    return [];
  };

  // 获取官方模板
  const fetchOfficialTemplates = async () => {
    try {
      const response = await getOfficialTemplates();
      if (response.data.code === 200) {
        return response.data.data.map((template: any) => ({
          ...template,
          category: 'official' as const,
          isOfficial: true
        }));
      }
    } catch (error) {
      console.error('获取官方模板失败:', error);
    }
    return [];
  };

  // 获取详细的官方模板列表（模拟数据）
  const fetchDetailOfficialTemplates = async () => {
    // 模拟详细的官方模板数据
    const detailTemplates: Template[] = [
      { id: 101, templateName: '全网模板', templateDescription: '适用于全网教学', createTime: '2025-01-01', updateTime: '2025-01-01', userId: 1, roleId: 1, status: 1, category: 'official' as const, isOfficial: true },
      { id: 102, templateName: 'ZWJ所有积木模板', templateDescription: 'ZWJ专用模板', createTime: '2025-01-02', updateTime: '2025-01-02', userId: 1, roleId: 1, status: 1, category: 'official' as const, isOfficial: true },
      { id: 103, templateName: '测试模板', templateDescription: '测试专用模板', createTime: '2025-01-03', updateTime: '2025-01-03', userId: 1, roleId: 1, status: 1, category: 'official' as const, isOfficial: true },
      { id: 104, templateName: '演示模板', templateDescription: '演示专用模板', createTime: '2025-01-04', updateTime: '2025-01-04', userId: 1, roleId: 1, status: 1, category: 'official' as const, isOfficial: true },
      { id: 105, templateName: 'microbit', templateDescription: 'microbit编程模板', createTime: '2025-01-05', updateTime: '2025-01-05', userId: 1, roleId: 1, status: 1, category: 'official' as const, isOfficial: true },
      { id: 106, templateName: '系统默认全校模板', templateDescription: '系统推荐模板', createTime: '2025-01-06', updateTime: '2025-01-06', userId: 1, roleId: 1, status: 1, category: 'official' as const, isOfficial: true },
      { id: 107, templateName: '全网大模板', templateDescription: '全网通用大模板', createTime: '2025-01-07', updateTime: '2025-01-07', userId: 1, roleId: 1, status: 1, category: 'official' as const, isOfficial: true },
      { id: 108, templateName: '基础新手模板plus', templateDescription: '新手入门增强版', createTime: '2025-01-08', updateTime: '2025-01-08', userId: 1, roleId: 1, status: 1, category: 'official' as const, isOfficial: true },
      { id: 109, templateName: '全网积木', templateDescription: '全网积木编程', createTime: '2025-01-09', updateTime: '2025-01-09', userId: 1, roleId: 1, status: 1, category: 'official' as const, isOfficial: true },
      { id: 110, templateName: '第十四课：AI绘画（四）', templateDescription: 'AI绘画第四课', createTime: '2025-01-10', updateTime: '2025-01-10', userId: 1, roleId: 1, status: 1, category: 'official' as const, isOfficial: true },
      { id: 111, templateName: '第十三课：AI绘画（三）', templateDescription: 'AI绘画第三课', createTime: '2025-01-11', updateTime: '2025-01-11', userId: 1, roleId: 1, status: 1, category: 'official' as const, isOfficial: true },
      { id: 112, templateName: '第十二课：AI绘画（二）', templateDescription: 'AI绘画第二课', createTime: '2025-01-12', updateTime: '2025-01-12', userId: 1, roleId: 1, status: 1, category: 'official' as const, isOfficial: true },
      { id: 113, templateName: '第一课：AI绘画（一）', templateDescription: 'AI绘画第一课', createTime: '2025-01-13', updateTime: '2025-01-13', userId: 1, roleId: 1, status: 1, category: 'official' as const, isOfficial: true },
      { id: 114, templateName: '第十课：视觉识别（二）', templateDescription: '视觉识别第二课', createTime: '2025-01-14', updateTime: '2025-01-14', userId: 1, roleId: 1, status: 1, category: 'official' as const, isOfficial: true },
      { id: 115, templateName: '第八课：语音合成', templateDescription: '语音合成课程', createTime: '2025-01-15', updateTime: '2025-01-15', userId: 1, roleId: 1, status: 1, category: 'official' as const, isOfficial: true },
      { id: 116, templateName: '第六课：语音识别（二）', templateDescription: '语音识别第二课', createTime: '2025-01-16', updateTime: '2025-01-16', userId: 1, roleId: 1, status: 1, category: 'official' as const, isOfficial: true },
      { id: 117, templateName: '第五课：语音识别（一）', templateDescription: '语音识别第一课', createTime: '2025-01-17', updateTime: '2025-01-17', userId: 1, roleId: 1, status: 1, category: 'official' as const, isOfficial: true },
      { id: 118, templateName: '第四课：流式大模型的应用实践人工智能', templateDescription: '流式大模型应用', createTime: '2025-01-18', updateTime: '2025-01-18', userId: 1, roleId: 1, status: 1, category: 'official' as const, isOfficial: true },
    ];
    setDetailTemplates(detailTemplates);
  };

  // 获取所有模板
  const fetchTemplates = async () => {
    if (!mounted || !userId) return;
    
    setLoading(true);
    try {
      const [myTemplates, officialTemplates] = await Promise.all([
        fetchMyTemplates(),
        fetchOfficialTemplates()
      ]);
      
      setTemplates([...myTemplates, ...officialTemplates]);
    } catch (error) {
      console.error('获取模板列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (isOpen) {
      fetchTemplates();
    }
  }, [isOpen, mounted, userId]);

  const handleTemplateClick = (template: Template) => {
    // 如果是课程模板（id为2），显示详细列表
    if (template.id === 2) {
      setShowOfficialDetail(true);
      fetchDetailOfficialTemplates();
      return;
    }

    onTemplateSelect(template);
    onClose();
  };

  const handleBackToMain = () => {
    setShowOfficialDetail(false);
    setDetailTemplates([]);
  };

  const filteredTemplates = templates.filter(template => template.category === activeTab);

  // 防止水合错误，在客户端挂载前不渲染
  if (!mounted || !isOpen) return null;

  return (
    <div className="modal-overlay">
      <div className="modal-content template-picker-modal">
        {/* 头部 */}
        <div className="modal-header">
          {showOfficialDetail && (
            <button className="back-btn" onClick={handleBackToMain}>
              ← 返回
            </button>
          )}
          <button className="close-btn" onClick={onClose}>
            <X size={24} />
          </button>
        </div>

        {!showOfficialDetail ? (
          <>
            {/* 标签页 */}
            <div className="template-tabs">
              <button
                className={`tab-button ${activeTab === 'my' ? 'active' : ''}`}
                onClick={() => setActiveTab('my')}
              >
                我的模板
              </button>
              <button
                className={`tab-button ${activeTab === 'official' ? 'active' : ''}`}
                onClick={() => setActiveTab('official')}
              >
                官方模板
              </button>
            </div>

            {/* 模板网格 */}
            <div className="template-picker-grid">
              {loading ? (
                <div className="loading-placeholder">
                  <div className="loading-spinner"></div>
                  <p>正在加载模板...</p>
                </div>
              ) : filteredTemplates.length === 0 ? (
                <div className="empty-placeholder">
                  <p>暂无{activeTab === 'my' ? '我的' : '官方'}模板</p>
                </div>
              ) : (
                filteredTemplates.map((template, index) => (
                  <div
                    key={template.id}
                    className="template-picker-card"
                    onClick={() => handleTemplateClick(template)}
                  >
                    {index === 0 && activeTab === 'my' ? (
                      <div className="template-card-content">
                        <div className="template-card-title">第一节课模板</div>
                      </div>
                    ) : (
                      <div className="template-card-content">
                        <div className="template-card-title">{template.templateName}</div>
                        {template.id === 2 && (
                          <div className="template-card-subtitle">点击查看所有官方模板</div>
                        )}
                      </div>
                    )}
                  </div>
                ))
              )}
            </div>
          </>
        ) : (
          <>
            {/* 详细官方模板页面 */}
            <div className="template-detail-header">
              <h2>官方模板列表</h2>
            </div>
            <div className="template-detail-grid">
              {detailTemplates.map((template) => (
                <div
                  key={template.id}
                  className="template-detail-card"
                  onClick={() => {
                    onTemplateSelect(template);
                    onClose();
                  }}
                >
                  <div className="template-detail-content">
                    <div className="template-detail-icon">
                      <span className="text-orange-500 text-xl">🧩</span>
                    </div>
                    <div className="template-detail-info">
                      <div className="template-detail-title">{template.templateName}</div>
                      <div className="template-detail-tags">
                        <span className="tag-official">官方</span>
                      </div>
                      <div className="template-detail-description">
                        {template.templateDescription}
                      </div>
                      <div className="template-detail-usage">
                        数字学生正在使用
                      </div>
                    </div>
                    <div className="template-detail-action">
                      <button className="use-template-btn">使用此模板</button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default TemplatePickerModal;
