"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/workbench/page",{

/***/ "(app-pages-browser)/./app/workbench/components/CourseListEditModal.tsx":
/*!**********************************************************!*\
  !*** ./app/workbench/components/CourseListEditModal.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FileText,Plus,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FileText,Plus,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FileText,Plus,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FileText,Plus,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FileText,Plus,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _lib_api_course_management__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api/course-management */ \"(app-pages-browser)/./lib/api/course-management.ts\");\n/* harmony import */ var _lib_api_course__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api/course */ \"(app-pages-browser)/./lib/api/course.ts\");\n/* harmony import */ var _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api/upload */ \"(app-pages-browser)/./lib/api/upload.ts\");\n/* harmony import */ var _barrel_optimize_names_Select_antd__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Select!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/select/index.js\");\n/* harmony import */ var logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\");\n/* harmony import */ var _CourseListEditModal_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./CourseListEditModal.css */ \"(app-pages-browser)/./app/workbench/components/CourseListEditModal.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n// 获取系列课程详情\nconst fetchSeriesDetail = async (seriesId)=>{\n    console.log(\"\\uD83D\\uDD0D 获取系列课程详情，seriesId:\", seriesId);\n    console.log(\"\\uD83D\\uDD17 调用API: GET /api/v1/course-management/series/{seriesId}\");\n    const response = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getSeriesDetail(seriesId);\n    console.log(\"\\uD83D\\uDCE1 系列详情API响应:\", response);\n    return response.data;\n};\n// API调用函数 - 获取系列下的所有课程（包含未发布和已发布）\nconst fetchCourseList = async (seriesId)=>{\n    console.log(\"\\uD83D\\uDD0D 获取系列课程列表，seriesId:\", seriesId);\n    console.log(\"\\uD83D\\uDD17 调用API: GET /api/v1/course-management/series/{seriesId}/courses\");\n    // 使用课程管理API获取所有状态的课程\n    const response = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getManagementSeriesCourses(seriesId, {\n        page: 1,\n        pageSize: 100\n    });\n    console.log(\"\\uD83D\\uDCE1 API响应:\", response);\n    return response.data;\n};\n// 获取课程详情\nconst fetchCourseDetail = async (seriesId, courseId)=>{\n    return await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getCourseMarketplaceDetail(seriesId, courseId);\n};\n// 获取课程标签\nconst fetchCourseTags = async ()=>{\n    try {\n        console.log(\"\\uD83D\\uDD0D 开始调用 courseApi.getCourseTags\");\n        const result = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getCourseTags({\n            page: 1,\n            pageSize: 100,\n            status: 1 // 只获取启用的标签\n        });\n        console.log(\"\\uD83D\\uDD0D courseApi.getCourseTags 返回结果:\", result);\n        return result;\n    } catch (error) {\n        console.error(\"\\uD83D\\uDD0D courseApi.getCourseTags 调用失败:\", error);\n        throw error;\n    }\n};\nconst CourseListEditModal = (param)=>{\n    let { isVisible, onClose, onSave, seriesTitle, seriesCoverImage, seriesId = 123 // 默认值，实际使用时应该传入真实的seriesId\n     } = param;\n    var _getSelectedCourse, _getSelectedCourse1, _getSelectedCourse2, _getSelectedCourse3, _courseDetail_contentConfig_video, _courseDetail_contentConfig, _courseDetail_contentConfig_document, _courseDetail_contentConfig1, _courseList_find, _courseList_find1;\n    _s();\n    const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_5__.GetNotification)();\n    const [courseList, setCourseList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [rightPanelType, setRightPanelType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"none\");\n    const [selectedCourseId, setSelectedCourseId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [uploadingFiles, setUploadingFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [isPublishing, setIsPublishing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isCreating, setIsCreating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPublishingSeries, setIsPublishingSeries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [seriesStatus, setSeriesStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0); // 0=草稿，1=已发布，2=已归档\n    // 删除确认弹窗状态\n    const [deleteConfirmVisible, setDeleteConfirmVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [courseToDelete, setCourseToDelete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isDeleting, setIsDeleting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingTitle, setEditingTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(seriesTitle);\n    const [courseGoals, setCourseGoals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [courseObjectives, setCourseObjectives] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [projectMembers, setProjectMembers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // 课程标签相关状态\n    const [courseTags, setCourseTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedTags, setSelectedTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [tagsLoading, setTagsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 课程详细编辑状态\n    const [courseDetail, setCourseDetail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: \"\",\n        description: \"\",\n        coverImage: \"\",\n        videoUrl: \"\",\n        videoName: \"\",\n        isVideoEnabled: false,\n        attachmentUrl: \"\",\n        attachmentName: \"\",\n        isAttachmentEnabled: false,\n        teachingMaterials: [],\n        // 支持teachingInfo结构\n        teachingInfo: [],\n        // 支持contentConfig结构\n        contentConfig: {\n            hasVideo: 0,\n            hasDocument: 0,\n            hasAudio: 0,\n            video: {\n                url: \"\",\n                name: \"\"\n            },\n            document: {\n                url: \"\",\n                name: \"\"\n            },\n            audio: {\n                url: \"\",\n                name: \"\"\n            }\n        },\n        courseContent: {\n            topic: \"\",\n            content: \"\"\n        },\n        isOneKeyOpen: false,\n        isDistributionEnabled: false,\n        distributionReward: \"\",\n        selectedTemplate: \"\",\n        isDistributionWater: false,\n        requiredEnergy: \"\",\n        energyAmount: \"\",\n        isDistributionLimit: false,\n        distributionConditions: {\n            inviteCount: \"\",\n            taskCount: \"\",\n            experience: \"\"\n        },\n        isDistributionTime: false,\n        distributionTimeConditions: {\n            startTime: \"\",\n            endTime: \"\"\n        },\n        distributionMaterials: [],\n        // 任务配置相关状态\n        taskConfig: {\n            taskName: \"\",\n            taskDuration: \"\",\n            taskDescription: \"\",\n            selfAssessmentItems: [\n                \"\"\n            ],\n            referenceWorks: [],\n            referenceResources: []\n        }\n    });\n    // 获取课程列表数据\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isVisible && seriesId) {\n            // 检查用户登录状态\n            const token = localStorage.getItem(\"token\");\n            console.log(\"\\uD83D\\uDD10 检查登录状态，token存在:\", !!token);\n            console.log(\"\\uD83D\\uDD0D seriesId:\", seriesId);\n            if (!token) {\n                console.error(\"❌ 用户未登录，无法获取课程列表\");\n                // 设置空列表，显示空状态\n                setCourseList([]);\n                setLoading(false);\n                return;\n            }\n            loadCourseList();\n            loadCourseTags();\n            loadSeriesDetail();\n        }\n    }, [\n        isVisible,\n        seriesId\n    ]);\n    const loadCourseList = async ()=>{\n        try {\n            setLoading(true);\n            console.log(\"\\uD83D\\uDD0D 开始加载课程列表，seriesId:\", seriesId);\n            const response = await fetchCourseList(seriesId);\n            console.log(\"\\uD83D\\uDCE1 API响应:\", response);\n            if (response.code === 200) {\n                console.log(\"✅ 课程列表数据:\", response.data);\n                const courses = response.data.list || [];\n                console.log(\"✅ 解析的课程数组:\", courses);\n                console.log(\"\\uD83D\\uDCCA 课程ID详情:\", courses.map((c)=>({\n                        id: c.id,\n                        type: typeof c.id,\n                        title: c.title,\n                        status: c.status\n                    })));\n                console.log(\"\\uD83D\\uDCCA 课程状态统计:\", {\n                    total: courses.length,\n                    draft: courses.filter((c)=>c.status === 0).length,\n                    published: courses.filter((c)=>c.status === 1).length\n                });\n                setCourseList(courses);\n            } else {\n                console.error(\"❌ API返回错误:\", response);\n                setCourseList([]);\n            }\n        } catch (error) {\n            var _error_response, _error_response1, _error_response2;\n            console.error(\"❌ 加载课程列表失败:\", error);\n            // 检查是否是认证错误\n            if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 401) {\n                console.error(\"\\uD83D\\uDD10 认证失败，用户未登录或token已过期\");\n            } else if (((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status) === 403) {\n                console.error(\"\\uD83D\\uDEAB 权限不足，无法访问该系列课程\");\n            } else if (((_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.status) === 404) {\n                console.error(\"\\uD83D\\uDCED 系列课程不存在，seriesId:\", seriesId);\n            } else {\n                console.error(\"\\uD83D\\uDD27 其他错误:\", error.message);\n            }\n            setCourseList([]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 加载系列课程详情\n    const loadSeriesDetail = async ()=>{\n        try {\n            console.log(\"\\uD83D\\uDD0D 开始加载系列课程详情，seriesId:\", seriesId);\n            const response = await fetchSeriesDetail(seriesId);\n            console.log(\"\\uD83D\\uDCE1 系列详情响应:\", response);\n            if (response.code === 200 && response.data) {\n                const seriesData = response.data;\n                console.log(\"✅ 系列课程详情:\", seriesData);\n                setSeriesStatus(seriesData.status || 0);\n                console.log(\"\\uD83D\\uDCCA 系列课程状态:\", seriesData.status, \"(0=草稿，1=已发布，2=已归档)\");\n            } else {\n                console.error(\"❌ 获取系列详情失败:\", response.message);\n            }\n        } catch (error) {\n            console.error(\"❌ 加载系列详情异常:\", error);\n        }\n    };\n    // 加载课程标签\n    const loadCourseTags = async ()=>{\n        try {\n            setTagsLoading(true);\n            console.log(\"\\uD83C\\uDFF7️ 开始加载课程标签\");\n            const response = await fetchCourseTags();\n            console.log(\"\\uD83D\\uDCE1 标签API完整响应:\", response);\n            // 检查响应结构\n            if (response && response.data) {\n                console.log(\"\\uD83D\\uDCCA 响应数据:\", response.data);\n                let tags = [];\n                // 处理标准的API响应格式 (response.data.list) - 根据实际API响应\n                if (response.data.list && Array.isArray(response.data.list)) {\n                    tags = response.data.list;\n                    console.log(\"✅ 从 data.list 解析到标签:\", tags.length, \"个\");\n                } else if (Array.isArray(response.data)) {\n                    tags = response.data;\n                    console.log(\"✅ 从 data 数组解析到标签:\", tags.length, \"个\");\n                } else if (response.data.data && response.data.data.list && Array.isArray(response.data.data.list)) {\n                    tags = response.data.data.list;\n                    console.log(\"✅ 从 data.data.list 解析到标签:\", tags.length, \"个\");\n                }\n                // 验证标签数据格式\n                console.log(\"\\uD83D\\uDD0D 原始标签数据:\", tags);\n                console.log(\"\\uD83D\\uDD0D 标签数据类型检查:\");\n                tags.forEach((tag, index)=>{\n                    var _tag_name;\n                    console.log(\"标签\".concat(index, \":\"), {\n                        tag,\n                        hasTag: !!tag,\n                        idType: typeof (tag === null || tag === void 0 ? void 0 : tag.id),\n                        nameType: typeof (tag === null || tag === void 0 ? void 0 : tag.name),\n                        nameValue: tag === null || tag === void 0 ? void 0 : tag.name,\n                        nameNotEmpty: (tag === null || tag === void 0 ? void 0 : (_tag_name = tag.name) === null || _tag_name === void 0 ? void 0 : _tag_name.trim()) !== \"\"\n                    });\n                });\n                const validTags = tags.filter((tag)=>{\n                    const isValid = tag && typeof tag.id === \"number\" && typeof tag.name === \"string\" && tag.name.trim() !== \"\";\n                    if (!isValid) {\n                        console.log(\"❌ 无效标签:\", tag, {\n                            hasTag: !!tag,\n                            idType: typeof (tag === null || tag === void 0 ? void 0 : tag.id),\n                            nameType: typeof (tag === null || tag === void 0 ? void 0 : tag.name),\n                            nameValue: tag === null || tag === void 0 ? void 0 : tag.name\n                        });\n                    }\n                    return isValid;\n                });\n                console.log(\"✅ 有效标签数量:\", validTags.length);\n                console.log(\"✅ 有效标签详情:\", validTags);\n                if (validTags.length > 0) {\n                    setCourseTags(validTags);\n                    console.log(\"✅ 成功设置真实标签数据\");\n                    return;\n                } else {\n                    console.warn(\"⚠️ 没有有效的标签数据\");\n                }\n            } else {\n                console.warn(\"⚠️ API响应格式不正确:\", response);\n            }\n            // 如果没有真实数据，设置空数组\n            console.log(\"\\uD83D\\uDCED 没有标签数据，设置空数组\");\n            setCourseTags([]);\n        } catch (error) {\n            var _error_response, _error_response1, _error_response2;\n            console.error(\"❌ 加载课程标签失败:\", error);\n            console.error(\"❌ 错误详情:\", {\n                message: error.message,\n                status: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status,\n                statusText: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.statusText,\n                data: (_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.data\n            });\n            // 发生错误时设置空数组\n            setCourseTags([]);\n        } finally{\n            setTagsLoading(false);\n        }\n    };\n    // 添加新课程\n    const addNewCourse = ()=>{\n        const newCourse = {\n            id: Date.now(),\n            seriesId: seriesId,\n            title: \"第\".concat(courseList.length + 1, \"课 - 新课时\"),\n            description: \"\",\n            coverImage: \"\",\n            orderIndex: courseList.length + 1,\n            status: 0,\n            statusLabel: \"草稿\",\n            hasVideo: 0,\n            hasDocument: 0,\n            hasAudio: 0,\n            videoDuration: 0,\n            videoDurationLabel: \"\",\n            videoName: \"\",\n            firstTeachingTitle: \"\",\n            resourcesCount: 0,\n            createdAt: new Date().toISOString(),\n            updatedAt: new Date().toISOString()\n        };\n        setCourseList([\n            ...courseList,\n            newCourse\n        ]);\n        // 自动选中新添加的课程\n        showCoursePanel(newCourse.id);\n    };\n    // 显示删除确认弹窗\n    const showDeleteConfirm = (id)=>{\n        setCourseToDelete(id);\n        setDeleteConfirmVisible(true);\n    };\n    // 确认删除课程\n    const confirmDeleteCourse = async ()=>{\n        if (!courseToDelete) return;\n        try {\n            setIsDeleting(true);\n            // 调用删除API\n            await _lib_api_course_management__WEBPACK_IMPORTED_MODULE_2__.courseManagementApi.deleteCourse(courseToDelete);\n            // 从列表中移除课程\n            setCourseList(courseList.filter((course)=>course.id !== courseToDelete));\n            // 如果删除的是当前选中的课程，清空右侧面板\n            if (selectedCourseId === courseToDelete) {\n                setRightPanelType(\"none\");\n                setSelectedCourseId(null);\n            }\n            // 关闭确认弹窗\n            setDeleteConfirmVisible(false);\n            setCourseToDelete(null);\n            // 显示成功提示\n            notification.success(\"课程已成功删除\");\n        } catch (error) {\n            console.error(\"删除课程失败:\", error);\n            showError(\"删除失败\", \"删除课程失败，请重试\");\n        } finally{\n            setIsDeleting(false);\n        }\n    };\n    // 取消删除\n    const cancelDelete = ()=>{\n        if (isDeleting) return; // 正在删除时不允许取消\n        setDeleteConfirmVisible(false);\n        setCourseToDelete(null);\n    };\n    // 更新课程标题\n    const updateCourseTitle = (id, newTitle)=>{\n        setCourseList(courseList.map((course)=>course.id === id ? {\n                ...course,\n                title: newTitle\n            } : course));\n    };\n    // 处理课程封面上传\n    const handleCoverUpload = async (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (file) {\n            // 检查文件类型\n            const allowedTypes = [\n                \"image/jpeg\",\n                \"image/jpg\",\n                \"image/png\",\n                \"image/gif\"\n            ];\n            if (!allowedTypes.includes(file.type)) {\n                showError(\"文件格式错误\", \"请选择 JPG、PNG 或 GIF 格式的图片文件\");\n                return;\n            }\n            // 检查文件大小 (10MB)\n            if (file.size > 10 * 1024 * 1024) {\n                showError(\"文件过大\", \"文件大小不能超过 10MB\");\n                return;\n            }\n            try {\n                console.log(\"\\uD83D\\uDCE4 开始上传课程封面:\", file.name);\n                // 添加到上传中的文件列表\n                setUploadingFiles((prev)=>new Set(prev).add(\"cover\"));\n                // 先显示预览图片\n                const previewUrl = URL.createObjectURL(file);\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        coverImage: previewUrl\n                    }));\n                // 上传到OSS\n                const imageUrl = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n                console.log(\"✅ 课程封面上传成功:\", imageUrl);\n                // 更新课程详情中的封面为真实URL\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        coverImage: imageUrl\n                    }));\n                // 同时更新课程列表中的封面\n                if (selectedCourseId) {\n                    setCourseList((prev)=>prev.map((course)=>course.id === selectedCourseId ? {\n                                ...course,\n                                coverImage: imageUrl\n                            } : course));\n                }\n                alert(\"课程封面上传成功！\");\n            } catch (error) {\n                console.error(\"❌ 课程封面上传失败:\", error);\n                showError(\"上传失败\", \"课程封面上传失败，请重试\");\n                // 上传失败时清除预览图片\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        coverImage: \"\"\n                    }));\n            } finally{\n                // 从上传中的文件列表移除\n                setUploadingFiles((prev)=>{\n                    const newSet = new Set(prev);\n                    newSet.delete(\"cover\");\n                    return newSet;\n                });\n            }\n        }\n    };\n    // 处理视频上传\n    const handleVideoUpload = async (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (file) {\n            // 检查文件类型\n            const allowedTypes = [\n                \"video/mp4\",\n                \"video/avi\",\n                \"video/mov\",\n                \"video/wmv\",\n                \"video/flv\"\n            ];\n            if (!allowedTypes.includes(file.type)) {\n                alert(\"请选择 MP4、AVI、MOV、WMV 或 FLV 格式的视频文件\");\n                return;\n            }\n            // 检查文件大小 (100MB)\n            if (file.size > 100 * 1024 * 1024) {\n                alert(\"视频文件大小不能超过 100MB\");\n                return;\n            }\n            try {\n                console.log(\"\\uD83D\\uDCE4 开始上传课程视频:\", file.name);\n                // 添加到上传中的文件列表\n                setUploadingFiles((prev)=>new Set(prev).add(\"video\"));\n                // 先显示预览视频\n                const previewUrl = URL.createObjectURL(file);\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        contentConfig: {\n                            ...prev.contentConfig,\n                            video: {\n                                url: previewUrl,\n                                name: file.name\n                            }\n                        }\n                    }));\n                // 上传到OSS\n                const videoUrl = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n                console.log(\"✅ 课程视频上传成功:\", videoUrl);\n                // 更新课程详情中的视频信息为真实URL\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        contentConfig: {\n                            ...prev.contentConfig,\n                            video: {\n                                url: videoUrl,\n                                name: file.name\n                            }\n                        }\n                    }));\n                // 同时更新课程列表中的视频信息\n                if (selectedCourseId) {\n                    setCourseList((prev)=>prev.map((course)=>course.id === selectedCourseId ? {\n                                ...course,\n                                contentConfig: {\n                                    ...course.contentConfig,\n                                    video: {\n                                        url: videoUrl,\n                                        name: file.name\n                                    }\n                                }\n                            } : course));\n                }\n                alert(\"课程视频上传成功！\");\n            } catch (error) {\n                console.error(\"❌ 课程视频上传失败:\", error);\n                alert(\"课程视频上传失败，请重试\");\n                // 上传失败时清除视频信息\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        contentConfig: {\n                            ...prev.contentConfig,\n                            video: {\n                                url: \"\",\n                                name: \"\"\n                            }\n                        }\n                    }));\n            } finally{\n                // 从上传中的文件列表移除\n                setUploadingFiles((prev)=>{\n                    const newSet = new Set(prev);\n                    newSet.delete(\"video\");\n                    return newSet;\n                });\n            }\n        }\n    };\n    // 触发视频文件选择\n    const triggerVideoUpload = ()=>{\n        const input = document.createElement(\"input\");\n        input.type = \"file\";\n        input.accept = \"video/mp4,video/avi,video/mov,video/wmv,video/flv\";\n        input.onchange = (e)=>handleVideoUpload(e);\n        input.click();\n    };\n    // 处理附件上传\n    const handleAttachmentUpload = async (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (file) {\n            // 检查文件类型\n            const allowedTypes = [\n                \"application/pdf\",\n                \"application/msword\",\n                \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\",\n                \"application/vnd.ms-excel\",\n                \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\",\n                \"application/vnd.ms-powerpoint\",\n                \"application/vnd.openxmlformats-officedocument.presentationml.presentation\",\n                \"text/plain\",\n                \"image/jpeg\",\n                \"image/png\",\n                \"image/gif\"\n            ];\n            if (!allowedTypes.includes(file.type)) {\n                alert(\"请选择支持的文件格式：PDF、DOC、DOCX、XLS、XLSX、PPT、PPTX、TXT、JPG、PNG、GIF\");\n                return;\n            }\n            // 检查文件大小 (10MB)\n            if (file.size > 10 * 1024 * 1024) {\n                alert(\"文件大小不能超过 10MB\");\n                return;\n            }\n            try {\n                console.log(\"\\uD83D\\uDCE4 开始上传课程附件:\", file.name);\n                // 添加到上传中的文件列表\n                setUploadingFiles((prev)=>new Set(prev).add(\"document\"));\n                // 上传到OSS\n                const documentUrl = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n                console.log(\"✅ 课程附件上传成功:\", documentUrl);\n                // 更新课程详情中的附件信息\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        contentConfig: {\n                            ...prev.contentConfig,\n                            hasDocument: 1,\n                            document: {\n                                url: documentUrl,\n                                name: file.name\n                            }\n                        }\n                    }));\n                alert(\"课程附件上传成功！\");\n            } catch (error) {\n                console.error(\"❌ 课程附件上传失败:\", error);\n                alert(\"课程附件上传失败，请重试\");\n            } finally{\n                // 从上传中的文件列表移除\n                setUploadingFiles((prev)=>{\n                    const newSet = new Set(prev);\n                    newSet.delete(\"document\");\n                    return newSet;\n                });\n            }\n        }\n    };\n    // 触发附件文件选择\n    const triggerAttachmentUpload = ()=>{\n        const input = document.createElement(\"input\");\n        input.type = \"file\";\n        input.accept = \".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.jpg,.png,.gif\";\n        input.onchange = (e)=>handleAttachmentUpload(e);\n        input.click();\n    };\n    // 处理教学附件上传\n    const handleTeachingMaterialUpload = async (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (file) {\n            // 检查文件类型\n            const allowedTypes = [\n                \"application/pdf\",\n                \"application/msword\",\n                \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\",\n                \"application/vnd.ms-excel\",\n                \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\",\n                \"application/vnd.ms-powerpoint\",\n                \"application/vnd.openxmlformats-officedocument.presentationml.presentation\",\n                \"text/plain\",\n                \"image/jpeg\",\n                \"image/png\",\n                \"image/gif\"\n            ];\n            if (!allowedTypes.includes(file.type)) {\n                alert(\"请选择支持的文件格式：PDF、DOC、DOCX、XLS、XLSX、PPT、PPTX、TXT、JPG、PNG、GIF\");\n                return;\n            }\n            // 检查文件大小 (10MB)\n            if (file.size > 10 * 1024 * 1024) {\n                alert(\"文件大小不能超过 10MB\");\n                return;\n            }\n            try {\n                console.log(\"\\uD83D\\uDCE4 开始上传教学材料:\", file.name);\n                // 添加到上传中的文件列表\n                setUploadingFiles((prev)=>new Set(prev).add(\"teaching-\".concat(Date.now())));\n                // 上传到OSS\n                const materialUrl = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n                console.log(\"✅ 教学材料上传成功:\", materialUrl);\n                // 添加到教学附件列表\n                const newMaterial = {\n                    type: file.type,\n                    name: file.name,\n                    url: materialUrl\n                };\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        teachingMaterials: [\n                            ...prev.teachingMaterials,\n                            newMaterial\n                        ]\n                    }));\n                alert(\"教学材料上传成功！\");\n            } catch (error) {\n                console.error(\"❌ 教学材料上传失败:\", error);\n                alert(\"教学材料上传失败，请重试\");\n            } finally{\n                // 从上传中的文件列表移除\n                setUploadingFiles((prev)=>{\n                    const newSet = new Set(prev);\n                    // 移除所有teaching-开头的项目\n                    Array.from(newSet).forEach((item)=>{\n                        if (item.startsWith(\"teaching-\")) {\n                            newSet.delete(item);\n                        }\n                    });\n                    return newSet;\n                });\n            }\n        }\n    };\n    // 触发教学附件文件选择\n    const triggerTeachingMaterialUpload = ()=>{\n        const input = document.createElement(\"input\");\n        input.type = \"file\";\n        input.accept = \".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.jpg,.png,.gif\";\n        input.onchange = (e)=>handleTeachingMaterialUpload(e);\n        input.click();\n    };\n    // 删除教学附件\n    const removeTeachingMaterial = (index)=>{\n        setCourseDetail((prev)=>({\n                ...prev,\n                teachingMaterials: prev.teachingMaterials.filter((_, i)=>i !== index)\n            }));\n    };\n    // 课程内容管理函数\n    const addTeachingInfoItem = ()=>{\n        setCourseDetail((prev)=>({\n                ...prev,\n                teachingInfo: [\n                    ...prev.teachingInfo,\n                    {\n                        title: \"\",\n                        content: \"\"\n                    }\n                ]\n            }));\n    };\n    const removeTeachingInfoItem = (index)=>{\n        setCourseDetail((prev)=>({\n                ...prev,\n                teachingInfo: prev.teachingInfo.filter((_, i)=>i !== index)\n            }));\n    };\n    const updateTeachingInfoTitle = (index, title)=>{\n        setCourseDetail((prev)=>{\n            const newTeachingInfo = [\n                ...prev.teachingInfo\n            ];\n            newTeachingInfo[index] = {\n                ...newTeachingInfo[index],\n                title\n            };\n            return {\n                ...prev,\n                teachingInfo: newTeachingInfo\n            };\n        });\n    };\n    const updateTeachingInfoContent = (index, content)=>{\n        setCourseDetail((prev)=>{\n            const newTeachingInfo = [\n                ...prev.teachingInfo\n            ];\n            newTeachingInfo[index] = {\n                ...newTeachingInfo[index],\n                content\n            };\n            return {\n                ...prev,\n                teachingInfo: newTeachingInfo\n            };\n        });\n    };\n    // 将UI格式的teachingInfo转换为API格式\n    const convertTeachingInfoForAPI = (teachingInfo)=>{\n        return teachingInfo.map((info)=>({\n                title: info.title,\n                content: info.content.split(\"\\n\").filter((line)=>line.trim()) // 按换行分割并过滤空行\n            }));\n    };\n    // 检查是否有未上传完成的文件（blob URL）\n    const checkForBlobUrls = (course)=>{\n        var _course_contentConfig_video, _course_contentConfig, _course_contentConfig_document, _course_contentConfig1;\n        const issues = [];\n        // 检查封面图片\n        if (course.coverImage && course.coverImage.startsWith(\"blob:\")) {\n            issues.push(\"课程封面图片\");\n        }\n        // 检查视频文件\n        if (((_course_contentConfig = course.contentConfig) === null || _course_contentConfig === void 0 ? void 0 : (_course_contentConfig_video = _course_contentConfig.video) === null || _course_contentConfig_video === void 0 ? void 0 : _course_contentConfig_video.url) && course.contentConfig.video.url.startsWith(\"blob:\")) {\n            issues.push(\"课程视频\");\n        }\n        // 检查文档附件\n        if (((_course_contentConfig1 = course.contentConfig) === null || _course_contentConfig1 === void 0 ? void 0 : (_course_contentConfig_document = _course_contentConfig1.document) === null || _course_contentConfig_document === void 0 ? void 0 : _course_contentConfig_document.url) && course.contentConfig.document.url.startsWith(\"blob:\")) {\n            issues.push(\"课程文档\");\n        }\n        // 检查教学材料\n        if (course.additionalResources) {\n            course.additionalResources.forEach((resource, index)=>{\n                if (resource.url && resource.url.startsWith(\"blob:\")) {\n                    issues.push(\"教学材料\".concat(index + 1));\n                }\n            });\n        }\n        return issues;\n    };\n    // 保存单个课程到后端\n    const saveCourse = async (course)=>{\n        try {\n            var _course_contentConfig_video, _course_contentConfig, _course_contentConfig_document, _course_contentConfig1;\n            // 验证必要的数据\n            if (!course.title || course.title.trim() === \"\") {\n                throw new Error(\"课程标题不能为空\");\n            }\n            // 检查是否有未上传完成的文件\n            const blobIssues = checkForBlobUrls(course);\n            if (blobIssues.length > 0) {\n                throw new Error(\"以下文件尚未上传完成，请等待上传完成后再保存：\".concat(blobIssues.join(\"、\")));\n            }\n            // 准备课程数据\n            const courseData = {\n                seriesId: seriesId,\n                title: course.title,\n                description: course.description || \"\",\n                coverImage: course.coverImage || \"\",\n                hasVideo: ((_course_contentConfig = course.contentConfig) === null || _course_contentConfig === void 0 ? void 0 : (_course_contentConfig_video = _course_contentConfig.video) === null || _course_contentConfig_video === void 0 ? void 0 : _course_contentConfig_video.url) ? 1 : 0,\n                hasDocument: ((_course_contentConfig1 = course.contentConfig) === null || _course_contentConfig1 === void 0 ? void 0 : (_course_contentConfig_document = _course_contentConfig1.document) === null || _course_contentConfig_document === void 0 ? void 0 : _course_contentConfig_document.url) ? 1 : 0,\n                hasAudio: 0,\n                videoDuration: 0,\n                contentConfig: course.contentConfig || {},\n                teachingInfo: convertTeachingInfoForAPI(course.teachingInfo || []),\n                additionalResources: course.additionalResources || [],\n                orderIndex: course.orderIndex || 1\n            };\n            console.log(\"\\uD83D\\uDCBE 准备保存课程数据:\", courseData);\n            let result;\n            // 判断是新课程还是更新课程\n            if (course.id && course.id > 1000000) {\n                // 新课程，使用创建API\n                console.log(\"\\uD83D\\uDCE4 创建新课程\");\n                const { data: response } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.createCourse(courseData);\n                result = response;\n                console.log(\"✅ 课程创建成功\");\n            } else {\n                // 现有课程，使用更新API\n                console.log(\"\\uD83D\\uDCE4 更新现有课程:\", course.id);\n                const { data: response } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.updateCourse(course.id, courseData);\n                result = response;\n                console.log(\"✅ 课程更新成功\");\n            }\n            return result;\n        } catch (error) {\n            console.error(\"❌ 保存课程失败:\", error);\n            throw error;\n        }\n    };\n    // 发布选中的课程\n    const handlePublishCourse = async ()=>{\n        if (!selectedCourseId) {\n            alert(\"请先选择要发布的课程\");\n            return;\n        }\n        const selectedCourse = courseList.find((course)=>course.id === selectedCourseId);\n        if (!selectedCourse) {\n            alert(\"未找到选中的课程\");\n            return;\n        }\n        // 检查课程是否已经发布\n        if (selectedCourse.status === 1) {\n            alert(\"该课程已经发布，无需重复发布\");\n            return;\n        }\n        try {\n            setIsPublishing(true);\n            console.log(\"\\uD83D\\uDCE4 开始发布课程\");\n            console.log(\"\\uD83D\\uDCE4 selectedCourseId:\", selectedCourseId, \"类型:\", typeof selectedCourseId);\n            console.log(\"\\uD83D\\uDCE4 课程信息:\", selectedCourse);\n            console.log(\"\\uD83D\\uDCE4 课程ID字段:\", selectedCourse.id, \"类型:\", typeof selectedCourse.id);\n            console.log(\"\\uD83D\\uDCE4 课程列表中的所有ID:\", courseList.map((c)=>({\n                    id: c.id,\n                    type: typeof c.id,\n                    title: c.title\n                })));\n            // 确保使用正确的课程ID\n            const courseIdToPublish = selectedCourse.id;\n            console.log(\"\\uD83D\\uDCE4 即将发布的课程ID:\", courseIdToPublish, \"类型:\", typeof courseIdToPublish);\n            const { data: response } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.publishCourse(courseIdToPublish);\n            if (response.code === 200) {\n                console.log(\"✅ 课程发布成功:\", response.data);\n                alert(\"课程发布成功！\");\n                // 刷新课程列表以更新状态\n                await loadCourseList();\n            } else {\n                console.error(\"❌ 发布课程失败:\", response.message);\n                alert(response.message || \"发布课程失败\");\n            }\n        } catch (error) {\n            var _error_response, _error_response_data, _error_response1;\n            console.error(\"❌ 发布课程失败:\", error);\n            console.error(\"❌ 错误详情:\", (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data);\n            // 处理具体的错误信息\n            if ((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : (_error_response_data = _error_response1.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) {\n                alert(error.response.data.message);\n            } else if (error.message) {\n                alert(error.message);\n            } else {\n                alert(\"发布课程失败，请重试\");\n            }\n        } finally{\n            setIsPublishing(false);\n        }\n    };\n    // 保存课程列表\n    const handleSave = async ()=>{\n        try {\n            setIsCreating(true);\n            // 检查是否有文件正在上传\n            if (uploadingFiles.size > 0) {\n                alert(\"有文件正在上传中，请等待上传完成后再保存\");\n                setIsCreating(false);\n                return;\n            }\n            console.log(\"\\uD83D\\uDCBE 开始保存课程列表\");\n            // 如果有选中的课程且在编辑状态，先保存当前课程\n            if (selectedCourseId && rightPanelType === \"course\") {\n                const selectedCourse = courseList.find((c)=>c.id === selectedCourseId);\n                if (selectedCourse) {\n                    var _courseDetail_teachingMaterials;\n                    // 更新课程数据\n                    const updatedCourse = {\n                        ...selectedCourse,\n                        title: courseDetail.title,\n                        description: courseDetail.description,\n                        coverImage: courseDetail.coverImage,\n                        hasVideo: courseDetail.isVideoEnabled ? 1 : 0,\n                        hasDocument: courseDetail.isAttachmentEnabled ? 1 : 0,\n                        contentConfig: courseDetail.contentConfig,\n                        teachingInfo: courseDetail.teachingInfo,\n                        additionalResources: ((_courseDetail_teachingMaterials = courseDetail.teachingMaterials) === null || _courseDetail_teachingMaterials === void 0 ? void 0 : _courseDetail_teachingMaterials.map((material)=>({\n                                title: material.name,\n                                url: material.url,\n                                description: material.name\n                            }))) || []\n                    };\n                    // 先更新课程列表中的数据\n                    setCourseList((prev)=>prev.map((course)=>course.id === selectedCourseId ? updatedCourse : course));\n                    // 然后保存到后端\n                    await saveCourse(updatedCourse);\n                }\n            }\n            // 保存系列课程信息\n            const data = {\n                title: editingTitle,\n                courseGoals,\n                courseObjectives,\n                courseList\n            };\n            onSave(data);\n            onClose();\n            console.log(\"✅ 课程列表保存完成\");\n        } catch (error) {\n            console.error(\"❌ 保存失败:\", error);\n            alert(\"保存失败: \".concat(error.message || \"请重试\"));\n        } finally{\n            setIsCreating(false);\n        }\n    };\n    // 发布系列课程\n    const handlePublish = async ()=>{\n        // 如果系列已发布，不执行任何操作\n        if (seriesStatus === 1) {\n            return;\n        }\n        try {\n            setIsPublishingSeries(true);\n            // 检查是否有课程\n            if (courseList.length === 0) {\n                alert(\"发布失败：课程系列中至少需要包含一个课程\");\n                return;\n            }\n            console.log(\"\\uD83D\\uDCE4 开始发布系列课程，系列ID:\", seriesId);\n            const { data: response } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.publishCourseSeries(seriesId);\n            if (response.code === 200) {\n                console.log(\"✅ 系列课程发布成功:\", response.data);\n                // 构建成功消息\n                const publishData = response.data;\n                let successMessage = '系列课程\"'.concat(publishData.title, '\"发布成功！');\n                // 如果有发布统计信息，添加到消息中\n                if (publishData.publishStats) {\n                    const stats = publishData.publishStats;\n                    successMessage += \"\\n\\n发布统计：\\n• 总课程数：\".concat(publishData.totalCourses, \"\\n• 已发布课程：\").concat(publishData.publishedCourses, \"\\n• 视频课程：\").concat(stats.videoCourseCount, \"个\\n• 文档课程：\").concat(stats.documentCourseCount, \"个\\n• 总资源数：\").concat(stats.totalResourcesCount, \"个\");\n                    if (stats.totalVideoDuration > 0) {\n                        const durationMinutes = Math.round(stats.totalVideoDuration / 60);\n                        successMessage += \"\\n• 视频总时长：\".concat(durationMinutes, \"分钟\");\n                    }\n                }\n                alert(successMessage);\n                // 更新系列状态为已发布\n                setSeriesStatus(1);\n                // 刷新课程列表以更新状态\n                await loadCourseList();\n                // 通知父组件刷新数据\n                onSave({\n                    type: \"publish_series\",\n                    seriesId: seriesId,\n                    message: \"系列课程发布成功\"\n                });\n            } else {\n                console.error(\"❌ 发布系列课程失败:\", response.message);\n                alert(response.message || \"发布系列课程失败\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"❌ 发布系列课程出错:\", error);\n            // 处理具体的错误信息\n            let errorMessage = \"发布系列课程失败\";\n            if ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) {\n                errorMessage = error.response.data.message;\n            } else if (error.message) {\n                errorMessage = error.message;\n            }\n            alert(errorMessage);\n        } finally{\n            setIsPublishingSeries(false);\n        }\n    };\n    // 退出编辑模式 - 保存数据并关闭\n    const handleExitEdit = ()=>{\n        handleSave();\n    };\n    // 显示设置面板\n    const showSettingsPanel = ()=>{\n        setRightPanelType(\"settings\");\n        setSelectedCourseId(null);\n    };\n    // 显示课程编辑面板\n    const showCoursePanel = async (courseId)=>{\n        console.log(\"\\uD83C\\uDFAF showCoursePanel 被调用\");\n        console.log(\"\\uD83C\\uDFAF 传入的courseId:\", courseId, \"类型:\", typeof courseId);\n        console.log(\"\\uD83C\\uDFAF 当前课程列表:\", courseList.map((c)=>({\n                id: c.id,\n                type: typeof c.id,\n                title: c.title\n            })));\n        setRightPanelType(\"course\");\n        setSelectedCourseId(courseId);\n        // 获取选中的课程并更新courseDetail状态\n        const selectedCourse = courseList.find((course)=>course.id === courseId);\n        console.log(\"\\uD83C\\uDFAF 找到的课程:\", selectedCourse);\n        if (selectedCourse) {\n            try {\n                console.log(\"\\uD83D\\uDD04 获取课程详情，seriesId:\", seriesId, \"courseId:\", courseId);\n                // 获取真实的课程详情数据\n                const { data: res } = await fetchCourseDetail(seriesId, courseId);\n                if (res.code === 200 && res.data) {\n                    var _courseDetailData_additionalResources, _courseDetailData_teachingInfo;\n                    const courseDetailData = res.data;\n                    console.log(\"✅ 获取到课程详情:\", courseDetailData);\n                    // 将真实的 additionalResources 映射到 teachingMaterials\n                    const teachingMaterials = ((_courseDetailData_additionalResources = courseDetailData.additionalResources) === null || _courseDetailData_additionalResources === void 0 ? void 0 : _courseDetailData_additionalResources.map((resource)=>({\n                            type: \"application/octet-stream\",\n                            name: resource.title || resource.name || \"附件\",\n                            url: resource.url\n                        }))) || [];\n                    // 将API返回的teachingInfo数组格式转换为新的格式\n                    const mappedTeachingInfo = ((_courseDetailData_teachingInfo = courseDetailData.teachingInfo) === null || _courseDetailData_teachingInfo === void 0 ? void 0 : _courseDetailData_teachingInfo.map((info)=>({\n                            title: info.title || \"\",\n                            content: Array.isArray(info.content) ? info.content.join(\"\\n\") : info.content || \"\"\n                        }))) || [];\n                    console.log(\"\\uD83D\\uDCCE 映射的教学附件:\", teachingMaterials);\n                    console.log(\"\\uD83D\\uDCDA 映射的教学信息:\", mappedTeachingInfo);\n                    setCourseDetail((prev)=>{\n                        var _courseDetailData_contentConfig_video, _courseDetailData_contentConfig, _courseDetailData_contentConfig_video1, _courseDetailData_contentConfig1, _courseDetailData_contentConfig_document, _courseDetailData_contentConfig2, _courseDetailData_contentConfig_document1, _courseDetailData_contentConfig3;\n                        return {\n                            ...prev,\n                            title: courseDetailData.title,\n                            description: courseDetailData.description,\n                            coverImage: courseDetailData.coverImage || \"\",\n                            isVideoEnabled: courseDetailData.hasVideo === 1,\n                            isAttachmentEnabled: courseDetailData.hasDocument === 1,\n                            contentConfig: courseDetailData.contentConfig || {},\n                            teachingInfo: mappedTeachingInfo,\n                            teachingMaterials: teachingMaterials,\n                            videoUrl: ((_courseDetailData_contentConfig = courseDetailData.contentConfig) === null || _courseDetailData_contentConfig === void 0 ? void 0 : (_courseDetailData_contentConfig_video = _courseDetailData_contentConfig.video) === null || _courseDetailData_contentConfig_video === void 0 ? void 0 : _courseDetailData_contentConfig_video.url) || \"\",\n                            videoName: ((_courseDetailData_contentConfig1 = courseDetailData.contentConfig) === null || _courseDetailData_contentConfig1 === void 0 ? void 0 : (_courseDetailData_contentConfig_video1 = _courseDetailData_contentConfig1.video) === null || _courseDetailData_contentConfig_video1 === void 0 ? void 0 : _courseDetailData_contentConfig_video1.name) || \"\",\n                            attachmentUrl: ((_courseDetailData_contentConfig2 = courseDetailData.contentConfig) === null || _courseDetailData_contentConfig2 === void 0 ? void 0 : (_courseDetailData_contentConfig_document = _courseDetailData_contentConfig2.document) === null || _courseDetailData_contentConfig_document === void 0 ? void 0 : _courseDetailData_contentConfig_document.url) || \"\",\n                            attachmentName: ((_courseDetailData_contentConfig3 = courseDetailData.contentConfig) === null || _courseDetailData_contentConfig3 === void 0 ? void 0 : (_courseDetailData_contentConfig_document1 = _courseDetailData_contentConfig3.document) === null || _courseDetailData_contentConfig_document1 === void 0 ? void 0 : _courseDetailData_contentConfig_document1.name) || \"\"\n                        };\n                    });\n                } else {\n                    console.error(\"❌ 获取课程详情失败:\", res.message);\n                    // 使用基础数据作为后备\n                    setCourseDetail((prev)=>({\n                            ...prev,\n                            title: selectedCourse.title,\n                            description: selectedCourse.description,\n                            coverImage: selectedCourse.coverImage || \"\",\n                            isVideoEnabled: selectedCourse.hasVideo === 1,\n                            isAttachmentEnabled: selectedCourse.hasDocument === 1,\n                            teachingMaterials: [] // 清空附件列表\n                        }));\n                }\n            } catch (error) {\n                console.error(\"❌ 获取课程详情异常:\", error);\n                // 使用基础数据作为后备\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        title: selectedCourse.title,\n                        description: selectedCourse.description,\n                        coverImage: selectedCourse.coverImage || \"\",\n                        isVideoEnabled: selectedCourse.hasVideo === 1,\n                        isAttachmentEnabled: selectedCourse.hasDocument === 1,\n                        teachingMaterials: [] // 清空附件列表\n                    }));\n            }\n        }\n    };\n    // 获取选中的课程\n    const getSelectedCourse = ()=>{\n        return courseList.find((course)=>course.id === selectedCourseId);\n    };\n    if (!isVisible) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"course-list-modal-overlay\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"course-list-modal\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"course-list-header\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"course-list-title-section\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"course-list-title\",\n                                        children: \"课程列表\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 1245,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"course-list-actions\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: showSettingsPanel,\n                                                className: \"course-list-settings-btn \".concat(rightPanelType === \"settings\" ? \"active\" : \"\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1251,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1247,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: addNewCourse,\n                                                className: \"course-list-add-btn\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1254,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1253,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 1246,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                lineNumber: 1244,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onClose,\n                                className: \"course-list-close-btn\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 1259,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                lineNumber: 1258,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                        lineNumber: 1243,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"course-list-content\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"course-list-sidebar\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"course-list-items\",\n                                    children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"course-list-loading\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"加载中...\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 1270,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 1269,\n                                        columnNumber: 17\n                                    }, undefined) : courseList.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"course-list-empty\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"course-list-empty-icon\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"w-12 h-12 text-gray-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1275,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1274,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"course-list-empty-title\",\n                                                children: \"暂无课时\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1277,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"course-list-empty-description\",\n                                                children: \"点击右上角的 + 按钮添加第一个课时\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1278,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: addNewCourse,\n                                                className: \"course-list-empty-btn\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1285,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    \"添加课时\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1281,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 1273,\n                                        columnNumber: 17\n                                    }, undefined) : courseList.map((course)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"course-list-item \".concat(selectedCourseId === course.id ? \"active\" : \"\"),\n                                            onClick: ()=>showCoursePanel(course.id),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-list-item-content\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"course-list-item-text\",\n                                                            children: course.title\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1297,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"course-status-badge \".concat(course.status === 1 ? \"published\" : \"draft\"),\n                                                            children: course.status === 1 ? \"已发布\" : \"未发布\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1298,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1296,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: (e)=>{\n                                                        e.stopPropagation();\n                                                        showDeleteConfirm(course.id);\n                                                    },\n                                                    className: \"course-list-item-delete\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"w-3 h-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1309,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1302,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, course.id, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 1291,\n                                            columnNumber: 19\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 1267,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                lineNumber: 1266,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"course-list-edit-area\",\n                                children: [\n                                    rightPanelType === \"none\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"course-edit-empty\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"course-edit-empty-icon\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"w-16 h-16 text-gray-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1322,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1321,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"course-edit-empty-title\",\n                                                children: \"无课程详情\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1324,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"course-edit-empty-description\",\n                                                children: \"点击左侧课程或设置按钮查看详情\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1325,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 1320,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    rightPanelType === \"settings\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"course-series-cover\",\n                                                children: seriesCoverImage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: seriesCoverImage,\n                                                    alt: \"系列课程封面\",\n                                                    className: \"course-series-cover-image\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1336,\n                                                    columnNumber: 21\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-series-cover-placeholder\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"系列课程封面\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1343,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1342,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1334,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"course-edit-form\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"course-edit-field\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"course-edit-label\",\n                                                                children: \"系列课程标题\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1352,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                value: editingTitle,\n                                                                onChange: (e)=>setEditingTitle(e.target.value),\n                                                                className: \"course-edit-input\",\n                                                                placeholder: \"请输入系列课程标题\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1353,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1351,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"course-edit-field\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"course-edit-label\",\n                                                                children: \"课程标签\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1364,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Select_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                mode: \"multiple\",\n                                                                style: {\n                                                                    width: \"100%\"\n                                                                },\n                                                                placeholder: \"请选择课程标签\",\n                                                                value: selectedTags,\n                                                                onChange: setSelectedTags,\n                                                                loading: tagsLoading,\n                                                                options: courseTags.map((tag)=>{\n                                                                    console.log(\"\\uD83C\\uDFF7️ 渲染标签选项:\", tag);\n                                                                    return {\n                                                                        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            style: {\n                                                                                color: tag.color\n                                                                            },\n                                                                            children: tag.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1376,\n                                                                            columnNumber: 29\n                                                                        }, void 0),\n                                                                        value: tag.id\n                                                                    };\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1365,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    fontSize: \"12px\",\n                                                                    color: \"#666\",\n                                                                    marginTop: \"4px\"\n                                                                },\n                                                                children: [\n                                                                    \"调试: 当前标签数量 \",\n                                                                    courseTags.length,\n                                                                    \", 加载状态: \",\n                                                                    tagsLoading ? \"是\" : \"否\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1385,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1363,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"course-edit-field\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"course-edit-label\",\n                                                                children: \"课程项目成员\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1392,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                value: projectMembers,\n                                                                onChange: (e)=>setProjectMembers(e.target.value),\n                                                                className: \"course-edit-input\",\n                                                                placeholder: \"请输入项目成员，如：张老师、李助教、王同学\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1393,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1391,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1349,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true),\n                                    rightPanelType === \"course\" && getSelectedCourse() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"course-detail-edit\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-detail-top\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"course-detail-cover\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"course-cover-upload-area\",\n                                                                    onClick: ()=>{\n                                                                        var _document_getElementById;\n                                                                        return (_document_getElementById = document.getElementById(\"cover-upload-input\")) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.click();\n                                                                    },\n                                                                    children: courseDetail.coverImage || ((_getSelectedCourse = getSelectedCourse()) === null || _getSelectedCourse === void 0 ? void 0 : _getSelectedCourse.coverImage) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                        src: courseDetail.coverImage || ((_getSelectedCourse1 = getSelectedCourse()) === null || _getSelectedCourse1 === void 0 ? void 0 : _getSelectedCourse1.coverImage),\n                                                                        alt: \"课程封面\",\n                                                                        className: \"course-cover-image\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1417,\n                                                                        columnNumber: 27\n                                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"course-cover-placeholder\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"点击上传课程封面\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1424,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1423,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1412,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    id: \"cover-upload-input\",\n                                                                    type: \"file\",\n                                                                    accept: \"image/jpeg,image/jpg,image/png,image/gif\",\n                                                                    onChange: handleCoverUpload,\n                                                                    style: {\n                                                                        display: \"none\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1428,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1411,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"course-detail-basic\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"course-detail-field\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            children: \"课程标题\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1438,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            value: courseDetail.title || ((_getSelectedCourse2 = getSelectedCourse()) === null || _getSelectedCourse2 === void 0 ? void 0 : _getSelectedCourse2.title) || \"\",\n                                                                            onChange: (e)=>{\n                                                                                setCourseDetail((prev)=>({\n                                                                                        ...prev,\n                                                                                        title: e.target.value\n                                                                                    }));\n                                                                                updateCourseTitle(selectedCourseId, e.target.value);\n                                                                            },\n                                                                            placeholder: \"请输入课程标题\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1439,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1437,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"course-detail-field\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            children: \"课程介绍\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1450,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                            value: courseDetail.description || ((_getSelectedCourse3 = getSelectedCourse()) === null || _getSelectedCourse3 === void 0 ? void 0 : _getSelectedCourse3.description) || \"\",\n                                                                            onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                        ...prev,\n                                                                                        description: e.target.value\n                                                                                    })),\n                                                                            placeholder: \"请输入课程介绍\",\n                                                                            rows: 3\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1451,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1449,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1436,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1410,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-detail-section\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            children: \"课程资源\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1463,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"course-resource-item\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"resource-header-right\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"课程视频\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1468,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"switch\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: \"checkbox\",\n                                                                                    checked: courseDetail.isVideoEnabled,\n                                                                                    onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                ...prev,\n                                                                                                isVideoEnabled: e.target.checked\n                                                                                            }))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1470,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"slider\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1475,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1469,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1467,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                courseDetail.isVideoEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"video-content-area\",\n                                                                    children: ((_courseDetail_contentConfig = courseDetail.contentConfig) === null || _courseDetail_contentConfig === void 0 ? void 0 : (_courseDetail_contentConfig_video = _courseDetail_contentConfig.video) === null || _courseDetail_contentConfig_video === void 0 ? void 0 : _courseDetail_contentConfig_video.url) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"video-info-section\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"video-preview\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                                                                    className: \"video-thumbnail\",\n                                                                                    controls: true,\n                                                                                    poster: courseDetail.coverImage,\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                                                                                            src: courseDetail.contentConfig.video.url,\n                                                                                            type: \"video/mp4\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1489,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined),\n                                                                                        \"您的浏览器不支持视频播放\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1484,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1483,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"video-name-centered\",\n                                                                                children: courseDetail.contentConfig.video.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1493,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"upload-btn-horizontal\",\n                                                                                onClick: triggerVideoUpload,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"重新上传\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1495,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1494,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1482,\n                                                                        columnNumber: 29\n                                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"video-upload-section\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"video-placeholder-centered\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"play-icon\",\n                                                                                    children: \"▶\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1501,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1500,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"upload-btn-horizontal\",\n                                                                                onClick: triggerVideoUpload,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"上传视频\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1504,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1503,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1499,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1479,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1466,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"course-resource-item\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"resource-header-right\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"课程附件\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1515,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"switch\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: \"checkbox\",\n                                                                                    checked: courseDetail.isAttachmentEnabled,\n                                                                                    onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                ...prev,\n                                                                                                isAttachmentEnabled: e.target.checked\n                                                                                            }))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1517,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"slider\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1522,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1516,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1514,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                courseDetail.isAttachmentEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"attachment-content-area\",\n                                                                    children: ((_courseDetail_contentConfig1 = courseDetail.contentConfig) === null || _courseDetail_contentConfig1 === void 0 ? void 0 : (_courseDetail_contentConfig_document = _courseDetail_contentConfig1.document) === null || _courseDetail_contentConfig_document === void 0 ? void 0 : _courseDetail_contentConfig_document.url) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"attachment-info-section\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"attachment-preview\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"document-icon\",\n                                                                                        children: \"\\uD83D\\uDCC4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1531,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"attachment-details\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"attachment-name\",\n                                                                                            children: courseDetail.contentConfig.document.name\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1533,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1532,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1530,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"upload-btn-horizontal\",\n                                                                                onClick: triggerAttachmentUpload,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"重新上传\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1537,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1536,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1529,\n                                                                        columnNumber: 29\n                                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"attachment-upload-section\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            className: \"upload-btn-horizontal\",\n                                                                            onClick: triggerAttachmentUpload,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"上传附件\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1543,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1542,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1541,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1526,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1513,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"course-resource-item\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"resource-header-simple\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"教学附件\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1554,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1553,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"teaching-materials\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            className: \"add-material-btn\",\n                                                                            onClick: triggerTeachingMaterialUpload,\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"+\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1558,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"上传\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1559,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1557,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        courseDetail.teachingMaterials && courseDetail.teachingMaterials.length > 0 ? courseDetail.teachingMaterials.map((material, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"material-item\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"material-name\",\n                                                                                        onClick: ()=>{\n                                                                                            if (material.url) {\n                                                                                                window.open(material.url, \"_blank\");\n                                                                                            }\n                                                                                        },\n                                                                                        style: {\n                                                                                            cursor: material.url ? \"pointer\" : \"default\",\n                                                                                            color: material.url ? \"#1890ff\" : \"inherit\",\n                                                                                            textDecoration: material.url ? \"underline\" : \"none\"\n                                                                                        },\n                                                                                        title: material.url ? \"点击下载附件\" : material.name,\n                                                                                        children: [\n                                                                                            \"\\uD83D\\uDCCE \",\n                                                                                            material.name\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1564,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        className: \"remove-material-btn\",\n                                                                                        onClick: ()=>removeTeachingMaterial(index),\n                                                                                        title: \"删除附件\",\n                                                                                        children: \"\\xd7\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1580,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, index, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1563,\n                                                                                columnNumber: 29\n                                                                            }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"empty-materials-hint\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                style: {\n                                                                                    color: \"#999\",\n                                                                                    fontSize: \"14px\"\n                                                                                },\n                                                                                children: \"暂无教学附件\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1591,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1590,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1556,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1552,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1462,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-detail-section\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"section-header\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    children: \"课程内容\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1601,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"add-content-section-btn\",\n                                                                    onClick: addTeachingInfoItem,\n                                                                    title: \"添加课程内容\",\n                                                                    children: \"+ 添加课程内容\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1602,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1600,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"course-content-area\",\n                                                            children: courseDetail.teachingInfo && courseDetail.teachingInfo.length > 0 ? courseDetail.teachingInfo.map((info, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"teaching-info-card\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"card-header\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"card-title\",\n                                                                                    children: [\n                                                                                        \"课程内容 \",\n                                                                                        index + 1\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1615,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    className: \"remove-card-btn\",\n                                                                                    onClick: ()=>removeTeachingInfoItem(index),\n                                                                                    title: \"删除此内容\",\n                                                                                    children: \"\\xd7\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1616,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1614,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"card-content\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"input-group\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                            children: \"标题\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1626,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                            type: \"text\",\n                                                                                            value: info.title,\n                                                                                            onChange: (e)=>updateTeachingInfoTitle(index, e.target.value),\n                                                                                            placeholder: \"请输入标题，如：教学目标、教学方法等\",\n                                                                                            className: \"title-input\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1627,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1625,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"input-group\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                            children: \"内容\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1636,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                                            value: info.content,\n                                                                                            onChange: (e)=>updateTeachingInfoContent(index, e.target.value),\n                                                                                            placeholder: \"请输入具体内容，多个内容项可用换行分隔\",\n                                                                                            className: \"content-textarea\",\n                                                                                            rows: 4\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1637,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1635,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1624,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, index, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1613,\n                                                                    columnNumber: 27\n                                                                }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"empty-content-hint\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: \"暂无课程内容，点击右上角按钮添加\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1650,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1649,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1610,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1599,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-detail-section\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"one-key-section\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"one-key-item\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"重新上课\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1660,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"switch\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"checkbox\",\n                                                                                checked: courseDetail.isOneKeyOpen,\n                                                                                onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                            ...prev,\n                                                                                            isOneKeyOpen: e.target.checked\n                                                                                        }))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1662,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"slider\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1667,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1661,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1659,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            courseDetail.isOneKeyOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"one-key-item\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"分配积木\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1674,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"switch\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                        type: \"checkbox\",\n                                                                                        checked: courseDetail.isDistributionEnabled,\n                                                                                        onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    isDistributionEnabled: e.target.checked\n                                                                                                }))\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1676,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"slider\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1681,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1675,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            courseDetail.isDistributionEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"block-template-section\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        className: \"select-template-btn\",\n                                                                                        children: \"选择积木模板\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1685,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"selected-template-display\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            children: courseDetail.selectedTemplate || \"选中的模板名字\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1689,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1688,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1684,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1673,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"one-key-item\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"分配能量\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1696,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"switch\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                        type: \"checkbox\",\n                                                                                        checked: courseDetail.isDistributionWater,\n                                                                                        onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    isDistributionWater: e.target.checked\n                                                                                                }))\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1698,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"slider\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1703,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1697,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1695,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    courseDetail.isDistributionWater && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"energy-input-section\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"需要能量：\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1709,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"text\",\n                                                                                value: courseDetail.requiredEnergy || \"\",\n                                                                                onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                            ...prev,\n                                                                                            requiredEnergy: e.target.value\n                                                                                        })),\n                                                                                placeholder: \"请输入需要的能量值\",\n                                                                                className: \"energy-input\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1710,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1708,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"one-key-item\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"分配任务\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1721,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"switch\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                        type: \"checkbox\",\n                                                                                        checked: courseDetail.isDistributionLimit,\n                                                                                        onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    isDistributionLimit: e.target.checked\n                                                                                                }))\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1723,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"slider\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1728,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1722,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1720,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    courseDetail.isDistributionLimit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"task-config-form\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"task-config-row\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"task-config-field\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                                children: \"任务名称:\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                lineNumber: 1738,\n                                                                                                columnNumber: 35\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                type: \"text\",\n                                                                                                value: courseDetail.taskConfig.taskName,\n                                                                                                onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                            ...prev,\n                                                                                                            taskConfig: {\n                                                                                                                ...prev.taskConfig,\n                                                                                                                taskName: e.target.value\n                                                                                                            }\n                                                                                                        })),\n                                                                                                placeholder: \"请输入任务名称\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                lineNumber: 1739,\n                                                                                                columnNumber: 35\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1737,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"task-config-field\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                                children: \"任务持续天数:\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                lineNumber: 1750,\n                                                                                                columnNumber: 35\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                type: \"number\",\n                                                                                                value: courseDetail.taskConfig.taskDuration,\n                                                                                                onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                            ...prev,\n                                                                                                            taskConfig: {\n                                                                                                                ...prev.taskConfig,\n                                                                                                                taskDuration: e.target.value\n                                                                                                            }\n                                                                                                        })),\n                                                                                                placeholder: \"请输入天数\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                lineNumber: 1751,\n                                                                                                columnNumber: 35\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1749,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1736,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"task-config-field task-config-full\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        children: \"任务描述:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1765,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                                        value: courseDetail.taskConfig.taskDescription,\n                                                                                        onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    taskConfig: {\n                                                                                                        ...prev.taskConfig,\n                                                                                                        taskDescription: e.target.value\n                                                                                                    }\n                                                                                                })),\n                                                                                        placeholder: \"请输入任务描述\",\n                                                                                        rows: 4\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1766,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1764,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"task-config-field task-config-full\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        children: [\n                                                                                            \"任务自评项: \",\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"item-number\",\n                                                                                                children: courseDetail.taskConfig.selfAssessmentItems.length\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                lineNumber: 1779,\n                                                                                                columnNumber: 47\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1779,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    courseDetail.taskConfig.selfAssessmentItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"self-assessment-item\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                type: \"text\",\n                                                                                                value: item,\n                                                                                                onChange: (e)=>{\n                                                                                                    const newItems = [\n                                                                                                        ...courseDetail.taskConfig.selfAssessmentItems\n                                                                                                    ];\n                                                                                                    newItems[index] = e.target.value;\n                                                                                                    setCourseDetail((prev)=>({\n                                                                                                            ...prev,\n                                                                                                            taskConfig: {\n                                                                                                                ...prev.taskConfig,\n                                                                                                                selfAssessmentItems: newItems\n                                                                                                            }\n                                                                                                        }));\n                                                                                                },\n                                                                                                placeholder: \"请输入自评项内容\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                lineNumber: 1782,\n                                                                                                columnNumber: 37\n                                                                                            }, undefined)\n                                                                                        }, index, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1781,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        type: \"button\",\n                                                                                        className: \"add-assessment-btn\",\n                                                                                        onClick: ()=>setCourseDetail((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    taskConfig: {\n                                                                                                        ...prev.taskConfig,\n                                                                                                        selfAssessmentItems: [\n                                                                                                            ...prev.taskConfig.selfAssessmentItems,\n                                                                                                            \"\"\n                                                                                                        ]\n                                                                                                    }\n                                                                                                })),\n                                                                                        children: \"+\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1797,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1778,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"task-config-field task-config-full\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        children: \"任务参考作品:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1814,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"reference-works-section\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                type: \"button\",\n                                                                                                className: \"select-works-btn\",\n                                                                                                children: \"选择作品\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                lineNumber: 1816,\n                                                                                                columnNumber: 35\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"reference-works-grid\",\n                                                                                                children: [\n                                                                                                    courseDetail.taskConfig.referenceWorks.map((work, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                            className: \"reference-work-item\",\n                                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                                children: work.name || \"作品\"\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                lineNumber: 1820,\n                                                                                                                columnNumber: 41\n                                                                                                            }, undefined)\n                                                                                                        }, index, false, {\n                                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                            lineNumber: 1819,\n                                                                                                            columnNumber: 39\n                                                                                                        }, undefined)),\n                                                                                                    Array.from({\n                                                                                                        length: Math.max(0, 3 - courseDetail.taskConfig.referenceWorks.length)\n                                                                                                    }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                            className: \"reference-work-item empty\"\n                                                                                                        }, \"empty-\".concat(index), false, {\n                                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                            lineNumber: 1825,\n                                                                                                            columnNumber: 39\n                                                                                                        }, undefined))\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                lineNumber: 1817,\n                                                                                                columnNumber: 35\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1815,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1813,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"task-config-field task-config-full\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        children: \"任务参考资源:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1833,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"reference-resources-section\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"reference-resources-grid\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                    type: \"button\",\n                                                                                                    className: \"upload-resource-btn\",\n                                                                                                    onClick: ()=>{\n                                                                                                        // 触发文件上传\n                                                                                                        const input = document.createElement(\"input\");\n                                                                                                        input.type = \"file\";\n                                                                                                        input.accept = \".pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx,.txt,.jpg,.png,.gif\";\n                                                                                                        input.onchange = (e)=>{\n                                                                                                            var _e_target_files;\n                                                                                                            const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n                                                                                                            if (file) {\n                                                                                                                setCourseDetail((prev)=>({\n                                                                                                                        ...prev,\n                                                                                                                        taskConfig: {\n                                                                                                                            ...prev.taskConfig,\n                                                                                                                            referenceResources: [\n                                                                                                                                ...prev.taskConfig.referenceResources,\n                                                                                                                                {\n                                                                                                                                    type: \"file\",\n                                                                                                                                    name: file.name\n                                                                                                                                }\n                                                                                                                            ]\n                                                                                                                        }\n                                                                                                                    }));\n                                                                                                            }\n                                                                                                        };\n                                                                                                        input.click();\n                                                                                                    },\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                                                            size: 24\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                            lineNumber: 1862,\n                                                                                                            columnNumber: 39\n                                                                                                        }, undefined),\n                                                                                                        \"上传\"\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                    lineNumber: 1836,\n                                                                                                    columnNumber: 37\n                                                                                                }, undefined),\n                                                                                                courseDetail.taskConfig.referenceResources.map((resource, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"reference-resource-item\",\n                                                                                                        children: [\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                                children: resource.name\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                lineNumber: 1867,\n                                                                                                                columnNumber: 41\n                                                                                                            }, undefined),\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                                type: \"button\",\n                                                                                                                className: \"remove-resource-btn\",\n                                                                                                                onClick: ()=>{\n                                                                                                                    const newResources = courseDetail.taskConfig.referenceResources.filter((_, i)=>i !== index);\n                                                                                                                    setCourseDetail((prev)=>({\n                                                                                                                            ...prev,\n                                                                                                                            taskConfig: {\n                                                                                                                                ...prev.taskConfig,\n                                                                                                                                referenceResources: newResources\n                                                                                                                            }\n                                                                                                                        }));\n                                                                                                                },\n                                                                                                                children: \"\\xd7\"\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                lineNumber: 1868,\n                                                                                                                columnNumber: 41\n                                                                                                            }, undefined)\n                                                                                                        ]\n                                                                                                    }, index, true, {\n                                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                        lineNumber: 1866,\n                                                                                                        columnNumber: 39\n                                                                                                    }, undefined))\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1835,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1834,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1832,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1734,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1658,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1657,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 1408,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                lineNumber: 1318,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                        lineNumber: 1264,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"course-list-footer\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"course-list-footer-left\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handlePublish,\n                                    className: \"course-list-btn course-list-btn-publish\",\n                                    disabled: courseList.length === 0 || isPublishingSeries || seriesStatus === 1,\n                                    title: seriesStatus === 1 ? \"系列课程已发布\" : courseList.length === 0 ? \"发布失败：课程系列中至少需要包含一个课程\" : isPublishingSeries ? \"正在发布系列课程...\" : \"发布系列课程\",\n                                    children: seriesStatus === 1 ? \"已发布\" : isPublishingSeries ? \"正在发布...\" : \"发布系列课程\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 1901,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                lineNumber: 1900,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"course-list-footer-right\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleExitEdit,\n                                        className: \"course-list-btn course-list-btn-exit\",\n                                        children: \"退出编辑模式\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 1924,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handlePublishCourse,\n                                        className: \"course-list-btn course-list-btn-publish-course\",\n                                        disabled: !selectedCourseId || ((_courseList_find = courseList.find((c)=>c.id === selectedCourseId)) === null || _courseList_find === void 0 ? void 0 : _courseList_find.status) === 1 || isPublishing,\n                                        title: !selectedCourseId ? \"请先选择要发布的课程\" : ((_courseList_find1 = courseList.find((c)=>c.id === selectedCourseId)) === null || _courseList_find1 === void 0 ? void 0 : _courseList_find1.status) === 1 ? \"该课程已发布\" : isPublishing ? \"正在发布课程...\" : \"发布选中的课程\",\n                                        children: isPublishing ? \"正在发布...\" : \"发布课程\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 1927,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleSave,\n                                        className: \"course-list-btn course-list-btn-save\",\n                                        disabled: uploadingFiles.size > 0 || isCreating || courseList.length === 0,\n                                        title: courseList.length === 0 ? \"请先添加课程内容\" : uploadingFiles.size > 0 ? \"有文件正在上传中，请等待上传完成\" : isCreating ? courseList.some((c)=>c.id > 1000000) ? \"正在创建课程...\" : \"正在保存课程...\" : courseList.some((c)=>c.id > 1000000) ? \"创建课程\" : \"保存课程\",\n                                        children: uploadingFiles.size > 0 ? \"上传中...\" : isCreating ? courseList.some((c)=>c.id > 1000000) ? \"正在创建...\" : \"正在保存...\" : courseList.some((c)=>c.id > 1000000) ? \"创建课程\" : \"保存课程\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 1943,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                lineNumber: 1923,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                        lineNumber: 1899,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                lineNumber: 1241,\n                columnNumber: 7\n            }, undefined),\n            deleteConfirmVisible && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"modal-overlay\",\n                onClick: cancelDelete,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"delete-confirm-modal\",\n                    onClick: (e)=>e.stopPropagation(),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"delete-confirm-header\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    children: \"确认删除\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 1973,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: cancelDelete,\n                                    className: \"close-btn\",\n                                    disabled: isDeleting,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 1979,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 1974,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                            lineNumber: 1972,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"delete-confirm-content\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: isDeleting ? \"正在删除课程，请稍候...\" : \"确定要删除这个课程吗？删除后无法恢复。\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 1983,\n                                    columnNumber: 15\n                                }, undefined),\n                                isDeleting && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"delete-loading\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"loading-spinner\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 1991,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 1990,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                            lineNumber: 1982,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"delete-confirm-footer\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: cancelDelete,\n                                    className: \"cancel-btn\",\n                                    disabled: isDeleting,\n                                    children: \"取消\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 1996,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: confirmDeleteCourse,\n                                    className: \"confirm-btn\",\n                                    disabled: isDeleting,\n                                    children: isDeleting ? \"正在删除...\" : \"确认删除\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 2003,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                            lineNumber: 1995,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                    lineNumber: 1971,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                lineNumber: 1970,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n        lineNumber: 1240,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CourseListEditModal, \"L0tFYCIcSAxHLrtHgY5Dz8pHNyM=\");\n_c = CourseListEditModal;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CourseListEditModal);\nvar _c;\n$RefreshReg$(_c, \"CourseListEditModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/workbench/components/CourseListEditModal.tsx\n"));

/***/ })

});