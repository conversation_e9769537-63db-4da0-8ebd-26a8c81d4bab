"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserClassService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const user_class_entity_1 = require("./entities/user_class.entity");
const crypto_1 = require("crypto");
let UserClassService = class UserClassService {
    userClassRepository;
    constructor(userClassRepository) {
        this.userClassRepository = userClassRepository;
    }
    async create(createUserClassDto) {
        if (!createUserClassDto.inviteCode) {
            createUserClassDto.inviteCode = this.generateInviteCode();
        }
        const entity = this.userClassRepository.create(createUserClassDto);
        return await this.userClassRepository.save(entity);
    }
    async findAll() {
        return await this.userClassRepository.find({
            order: {
                schoolId: 'ASC',
                grade: 'ASC',
                className: 'ASC'
            }
        });
    }
    async findOne(id) {
        return await this.userClassRepository.findOne({ where: { id } });
    }
    async findByIds(ids) {
        if (!ids || ids.length === 0) {
            return [];
        }
        return await this.userClassRepository.find({
            where: { id: (0, typeorm_2.In)(ids) }
        });
    }
    async findBySchoolId(schoolId) {
        return await this.userClassRepository.find({
            where: { schoolId },
            order: { grade: 'ASC', className: 'ASC' }
        });
    }
    async findByGrade(schoolId, grade) {
        return await this.userClassRepository.find({
            where: { schoolId, grade },
            order: { className: 'ASC' }
        });
    }
    async findByTeacherId(teacherId) {
        return await this.userClassRepository.find({
            where: { teacherId },
            order: { createTime: 'ASC' }
        });
    }
    async findByAssistantTeacherId(assistantTeacherId) {
        return await this.userClassRepository.find({
            where: { assistantTeacherId },
            order: { schoolId: 'ASC', grade: 'ASC', className: 'ASC' }
        });
    }
    async findByInviteCode(inviteCode) {
        return await this.userClassRepository.findOne({ where: { inviteCode } });
    }
    async update(id, updateUserClassDto) {
        return await this.userClassRepository.update(id, updateUserClassDto);
    }
    async regenerateInviteCode(id) {
        const inviteCode = this.generateInviteCode();
        await this.userClassRepository.update(id, { inviteCode });
        return { inviteCode };
    }
    async remove(id) {
        return await this.userClassRepository.delete(id);
    }
    generateInviteCode() {
        return (0, crypto_1.randomBytes)(4).toString('hex').substring(0, 8).toUpperCase();
    }
    async getTeacherClasses(classData) {
        const { schoolId, teacherId } = classData;
        return await this.userClassRepository.find({ where: { schoolId, teacherId } });
    }
};
exports.UserClassService = UserClassService;
exports.UserClassService = UserClassService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(user_class_entity_1.UserClass)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], UserClassService);
//# sourceMappingURL=user_class.service.js.map