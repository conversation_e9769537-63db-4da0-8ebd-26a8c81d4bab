# 数据库监控配置示例
# 复制此文件为 .env 或添加到现有的 .env 文件中

# 🔧 主开关：完全启用/禁用数据库监控
# 设置为 false 可以完全禁用监控，提升性能
ENABLE_DB_MONITORING=true

# 🔧 轻量级模式：启用后会减少监控开销
# 轻量级模式下会禁用调用栈捕获、参数记录等高开销功能
DB_MONITOR_LIGHTWEIGHT=false

# 🔧 慢查询日志开关
# 设置为 false 可以禁用慢查询日志记录
ENABLE_SLOW_QUERY_LOG=true

# 🔧 查询指标收集开关
# 设置为 false 可以禁用查询统计指标收集
ENABLE_QUERY_METRICS=true

# 🔧 调用栈捕获开关
# 调用栈捕获对性能影响较大，生产环境建议禁用
# 生产环境默认禁用，开发环境默认启用
ENABLE_STACK_TRACE=true

# 🔧 采样率配置 (1-100)
# 100 = 监控所有查询
# 50 = 监控50%的查询
# 10 = 监控10%的查询
# 1 = 监控1%的查询
DB_MONITOR_SAMPLING_RATE=100

# 🔧 异步处理慢查询记录
# 启用后慢查询记录会异步处理，减少对主流程的影响
ASYNC_SLOW_QUERY_PROCESSING=true

# 🔧 最大慢查询记录数
# 超过此数量会自动清理旧记录
MAX_SLOW_QUERY_RECORDS=100

# 🔧 查询超时清理时间（毫秒）
# 超过此时间的活跃查询会被清理，防止内存泄漏
QUERY_TIMEOUT_MS=300000

# ========================================
# 性能优化建议：
# ========================================

# 🚀 高性能生产环境配置：
# ENABLE_DB_MONITORING=true
# DB_MONITOR_LIGHTWEIGHT=true
# ENABLE_STACK_TRACE=false
# DB_MONITOR_SAMPLING_RATE=10
# ASYNC_SLOW_QUERY_PROCESSING=true

# 🔍 开发调试环境配置：
# ENABLE_DB_MONITORING=true
# DB_MONITOR_LIGHTWEIGHT=false
# ENABLE_STACK_TRACE=true
# DB_MONITOR_SAMPLING_RATE=100
# ASYNC_SLOW_QUERY_PROCESSING=false

# ⚡ 极致性能配置（完全禁用监控）：
# ENABLE_DB_MONITORING=false

# 📊 监控重点配置（只关注慢查询）：
# ENABLE_DB_MONITORING=true
# ENABLE_SLOW_QUERY_LOG=true
# ENABLE_QUERY_METRICS=false
# ENABLE_STACK_TRACE=false
# DB_MONITOR_SAMPLING_RATE=100
