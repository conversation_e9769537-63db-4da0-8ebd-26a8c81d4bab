import { DataSource } from 'typeorm';
export declare class DatabaseTestController {
    private readonly dataSource;
    constructor(dataSource: DataSource);
    testSlowQuery(delay?: number): Promise<{
        code: number;
        message: string;
        data: any;
    }>;
    testComplexQuery(): Promise<{
        code: number;
        message: string;
        data: any;
    }>;
    testBatchQueries(count?: number): Promise<{
        code: number;
        message: string;
        data: any;
    }>;
    getConnectionInfo(): Promise<{
        code: number;
        message: string;
        data: any;
    }>;
}
