'use client';

import React from 'react';
import { X } from 'lucide-react';

interface OneClickClassConfirmModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  isLoading: boolean;
  schoolName: string;
  className: string;
  courseName: string;
}

const OneClickClassConfirmModal: React.FC<OneClickClassConfirmModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  isLoading,
  schoolName,
  className,
  courseName
}) => {
  if (!isOpen) return null;

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div 
        className="modal-content one-click-confirm-modal" 
        onClick={(e) => e.stopPropagation()}
      >
        <div className="modal-header">
          <h3 className="modal-title">确认开始上课</h3>
          <button
            className="modal-close-btn"
            onClick={onClose}
            disabled={isLoading}
          >
            <X size={20} />
          </button>
        </div>
        
        <div className="modal-body">
          <div className="confirm-info">
            <div className="info-item">
              <span className="info-label">学校：</span>
              <span className="info-value">{schoolName}</span>
            </div>
            <div className="info-item">
              <span className="info-label">班级：</span>
              <span className="info-value">{className}</span>
            </div>
            <div className="info-item">
              <span className="info-label">课程：</span>
              <span className="info-value">{courseName}</span>
            </div>
          </div>
          
          <div className="confirm-description">
            即将为该班级开启课程，自动分配积分、应用模板、创建任务
          </div>
        </div>
        
        <div className="modal-footer">
          <button
            className="btn btn-cancel"
            onClick={onClose}
            disabled={isLoading}
          >
            取消
          </button>
          <button
            className="btn btn-confirm"
            onClick={onConfirm}
            disabled={isLoading}
          >
            {isLoading ? '正在开启...' : '确定'}
          </button>
        </div>
      </div>
      
      <style jsx>{`
        .modal-overlay {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-color: rgba(0, 0, 0, 0.5);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 1000;
        }

        .one-click-confirm-modal {
          background: white;
          border-radius: 16px;
          box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
          max-width: 420px;
          width: 90%;
          max-height: 90vh;
          overflow: hidden;
          animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
          from {
            opacity: 0;
            transform: translateY(-20px) scale(0.95);
          }
          to {
            opacity: 1;
            transform: translateY(0) scale(1);
          }
        }

        .modal-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 24px 24px 20px 24px;
          border-bottom: none;
        }

        .modal-title {
          margin: 0;
          font-size: 20px;
          font-weight: 600;
          color: #1a1a1a;
        }

        .modal-close-btn {
          background: none;
          border: none;
          cursor: pointer;
          padding: 4px;
          border-radius: 4px;
          color: #8c8c8c;
          transition: all 0.2s;
        }

        .modal-close-btn:hover:not(:disabled) {
          background-color: #f5f5f5;
          color: #262626;
        }

        .modal-close-btn:disabled {
          cursor: not-allowed;
          opacity: 0.5;
        }

        .modal-body {
          padding: 0 24px 24px 24px;
        }

        .confirm-info {
          margin-bottom: 24px;
        }

        .info-item {
          display: flex;
          align-items: center;
          margin-bottom: 16px;
          font-size: 16px;
        }

        .info-item:last-child {
          margin-bottom: 0;
        }

        .info-label {
          font-weight: 500;
          color: #666;
          min-width: 60px;
        }

        .info-value {
          color: #1a1a1a;
          font-weight: 500;
        }

        .confirm-description {
          background-color: #f8f9fa;
          border-radius: 8px;
          padding: 16px;
          font-size: 14px;
          color: #666;
          line-height: 1.5;
          text-align: center;
          border: none;
        }

        .modal-footer {
          display: flex;
          gap: 16px;
          justify-content: center;
          padding: 24px;
          border-top: none;
          background-color: white;
        }

        .btn {
          padding: 12px 32px;
          border-radius: 8px;
          font-size: 16px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s;
          border: none;
          min-width: 100px;
        }

        .btn:disabled {
          cursor: not-allowed;
          opacity: 0.6;
        }

        .btn-cancel {
          background-color: white;
          color: #666;
          border: 1px solid #d9d9d9;
        }

        .btn-cancel:hover:not(:disabled) {
          background-color: #f5f5f5;
          border-color: #b7b7b7;
        }

        .btn-confirm {
          background-color: #1890ff;
          color: white;
        }

        .btn-confirm:hover:not(:disabled) {
          background-color: #40a9ff;
        }

        .btn-confirm:disabled {
          background-color: #d9d9d9;
        }
      `}</style>
    </div>
  );
};

export default OneClickClassConfirmModal;
