import React, { useEffect, useState } from 'react';
import { Select, Spin } from 'antd';
import { documentApi } from '../lib/api/document';

interface DocumentSelectorProps {
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
  labelInValue?: boolean;
}

interface Document {
  id: string;
  title: string;
}

export const documentCache: Record<string, { title: string, id: string }> = {};

export async function getDocumentTitle(docId: string): Promise<string> {
  if (!docId) return '';
  
  if (documentCache[docId]) {
    return documentCache[docId].title;
  }
  
  try {
    const response = await documentApi.getById(Number(docId));
    if (response.data?.data?.title) {
      const title = response.data.data.title;
      documentCache[docId] = { title, id: docId };
      return title;
    }
  } catch (error) {
    console.error('获取文档标题失败:', error);
  }
  
  return docId;
}

const DocumentSelector: React.FC<DocumentSelectorProps> = ({ 
  placeholder, 
  value, 
  onChange,
  labelInValue = false 
}) => {
  const [documents, setDocuments] = useState<Document[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [selectedDocTitle, setSelectedDocTitle] = useState<string>('');
  const [searchText, setSearchText] = useState<string>('');
  const [initialized, setInitialized] = useState<boolean>(false);

  useEffect(() => {
    const fetchDocuments = async () => {
      setLoading(true);
      try {
        const response = await documentApi.getList();
        const docs = response.data?.data || [];
        
        docs.forEach((doc: Document) => {
          documentCache[doc.id] = { title: doc.title || '无标题', id: doc.id };
        });
        
        setDocuments(docs);
        
        if (value && docs.length > 0) {
          const selectedDoc = docs.find((doc: Document) => doc.id === value);
          if (selectedDoc) {
            setSelectedDocTitle(selectedDoc.title);
          }
        }
        
        setInitialized(true);
      } catch (error) {
        console.error('获取文档列表失败:', error);
        setDocuments([]);
        setInitialized(true);
      } finally {
        setLoading(false);
      }
    };
    fetchDocuments();
  }, []);

  useEffect(() => {
    const updateSelectedTitle = async () => {
      if (!value) {
        setSelectedDocTitle('');
        return;
      }
      
      if (documentCache[value]) {
        setSelectedDocTitle(documentCache[value].title);
        return;
      }
      
      const docInList = documents.find(doc => doc.id === value);
      if (docInList) {
        setSelectedDocTitle(docInList.title);
        return;
      }
      
      if (initialized && !loading) {
        try {
          setLoading(true);
          const title = await getDocumentTitle(value);
          setSelectedDocTitle(title);
        } catch (error) {
          console.error('获取文档标题失败:', error);
        } finally {
          setLoading(false);
        }
      }
    };
    
    updateSelectedTitle();
  }, [value, documents, initialized, loading]);

  const handleChange = (selectedValue: any, option: any) => {
    // 处理 labelInValue 的情况
    const actualValue = typeof selectedValue === 'object' ? selectedValue.value : selectedValue;

    if (onChange) {
      if (labelInValue) {
        onChange(selectedValue); // 返回完整对象
      } else {
        onChange(actualValue); // 只返回值
      }
    }

    const selectedDoc = documents.find(doc => doc.id === actualValue);
    if (selectedDoc) {
      setSelectedDocTitle(selectedDoc.title);
    }
  };

  // 根据 labelInValue 决定显示值的格式
  const displayValue = labelInValue
    ? (value && selectedDocTitle ? { value, label: selectedDocTitle || value } : undefined)
    : value;

  return (
    <Select
      placeholder={placeholder}
      labelInValue={labelInValue}
      value={displayValue}
      onChange={(selected: any) => handleChange(selected, selected)}
      loading={loading}
      showSearch
      optionFilterProp="label"
      onSearch={setSearchText}
      filterOption={(input, option) =>
        (option?.label?.toString().toLowerCase() || '').includes(input.toLowerCase())
      }
      options={(Array.isArray(documents) ? documents : []).map(doc => ({
        label: doc.title || '无标题',
        value: doc.id,
      }))}
      notFoundContent={loading ? <Spin size="small" /> : null}
      style={{ width: '100%' }}
    />
  );
};

export default DocumentSelector; 