/* 表单过渡容器样式 */
.form-transition-container {
  position: relative;
  width: 100%;
  max-height: 1000px;
}

/* 表单步骤基本样式 */
.form-step {
  position: absolute;
  width: 100%;
  transition-property: transform, opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); /* 使用更平滑的缓动函数 */
}

/* 初始渲染时禁用动画 */
.form-step.no-animation {
  transition: none !important;
  animation: none !important;
}

/* 当前活动表单步骤 */
.form-step.active {
  transform: translateX(0);
  opacity: 1;
  z-index: 10;
  visibility: visible;
}

/* 前一个表单步骤 - 滑向左侧 */
.form-step.prev {
  transform: translateX(-100%);
  opacity: 0;
  z-index: 5;
  visibility: hidden;
}

/* 下一个表单步骤 - 从右侧滑入 */
.form-step.next {
  transform: translateX(100%);
  opacity: 0;
  z-index: 5;
  visibility: hidden;
}

/* 水平滑动效果增强 */
.form-step.active:not(.no-animation) {
  animation: slide-in 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slide-in {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 从右侧滑入的特殊效果 */
.role-selection-enter {
  transform: translateX(100%);
  opacity: 0;
}

.role-selection-enter-active {
  transform: translateX(0);
  opacity: 1;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.role-selection-exit {
  transform: translateX(0);
  opacity: 1;
}

.role-selection-exit-active {
  transform: translateX(-100%);
  opacity: 0;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
} 