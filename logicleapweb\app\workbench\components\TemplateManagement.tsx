'use client';

import React, { useState, useEffect } from 'react';
import { Search, Upload, Plus } from 'lucide-react';
import { getRoleTemplateList, getOfficialTemplates, getTemplateInfo, deleteTemplate, addUserJoinRole, getUserCurrentTemplate, getStudentTemplates } from '@/lib/api/role';
import { roleTemplateFolderApi } from '@/lib/api/role-template-folder';
import { useSelector } from 'react-redux';
import { RootState } from '@/lib/store';
import { useTemplate } from '../contexts/TemplateContext';
import PermissionTemplateModal from './PermissionTemplateModal';
import { GetNotification } from 'logic-common/dist/components/Notification';
import './TemplateManagement.css';

interface Template {
  id: number;
  templateName: string;
  templateDescription?: string;
  createTime: string;
  updateTime?: string;
  userId: number;
  roleId: number;
  status: number;
  category: 'my' | 'official';
  isOfficial?: boolean;
  usageCount?: number;
}

interface School {
  id: number;
  schoolName: string;
  province?: string;
  city?: string;
  district?: string;
  createTime?: string;
  updateTime?: string;
}

interface TemplateManagementProps {
  selectedSchool?: School | null;
  userInfo?: {
    id: number;
    nickName: string;
    [key: string]: any;
  };
}

const TemplateManagement: React.FC<TemplateManagementProps> = ({
  selectedSchool,
  userInfo
}) => {
  const [activeTab, setActiveTab] = useState<'my' | 'official'>('official');
  const [searchQuery, setSearchQuery] = useState('');
  const [templates, setTemplates] = useState<Template[]>([]);
  const [loading, setLoading] = useState(false);
  const [mounted, setMounted] = useState(false);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [editingTemplateId, setEditingTemplateId] = useState<number | null>(null);
  const [currentTemplate, setCurrentTemplate] = useState<{ templateId: number } | null>(null);
  const [officialTemplateCount, setOfficialTemplateCount] = useState<number>(0);
  const [showOfficialDetail, setShowOfficialDetail] = useState(false);
  const [detailTemplates, setDetailTemplates] = useState<Template[]>([]);

  // 文件夹相关状态
  const [folders, setFolders] = useState<any[]>([]);
  const [currentView, setCurrentView] = useState<'folders' | 'templates'>('folders');
  const [currentFolder, setCurrentFolder] = useState<any>(null);

  const [loadingFolders, setLoadingFolders] = useState(false);
  const [loadingFolderTemplates, setLoadingFolderTemplates] = useState(false);

  const userId = useSelector((state: RootState) => state.user.userState.userId);
  const roleId = useSelector((state: RootState) => state.user.userState.roleId);

  // 使用模板上下文
  const { refreshCurrentTemplate, notifyGlobalTemplateChange } = useTemplate();

  // 获取通知组件
  const notification = GetNotification();

  // 防止水合错误
  useEffect(() => {
    setMounted(true);
  }, []);

  // 获取我的模板
  const fetchMyTemplates = async () => {
    if (!userId) return [];

    try {
      const response = await getRoleTemplateList(userId);
      if (response.data.code === 200) {
        return response.data.data.map((template: any) => ({
          ...template,
          category: 'my' as const,
          isOfficial: false
        }));
      }
    } catch (error) {
      console.error('获取我的模板失败:', error);
    }
    return [];
  };

  // 获取详细官方模板数量
  const fetchOfficialTemplateCount = async () => {
    // 模拟详细的官方模板数据（与TemplatePickerModal中的数据一致）
    const detailTemplates = [
      { id: 101, templateName: '全网模板' },
      { id: 102, templateName: 'ZWJ所有积木模板' },
      { id: 103, templateName: '测试模板' },
      { id: 104, templateName: '演示模板' },
      { id: 105, templateName: 'microbit' },
      { id: 106, templateName: '系统默认全校模板' },
      { id: 107, templateName: '全网大模板' },
      { id: 108, templateName: '基础新手模板plus' },
      { id: 109, templateName: '全网积木' },
      { id: 110, templateName: '第十四课：AI绘画（四）' },
      { id: 111, templateName: '第十三课：AI绘画（三）' },
      { id: 112, templateName: '第十二课：AI绘画（二）' },
      { id: 113, templateName: '第一课：AI绘画（一）' },
      { id: 114, templateName: '第十课：视觉识别（二）' },
      { id: 115, templateName: '第八课：语音合成' },
      { id: 116, templateName: '第六课：语音识别（二）' },
      { id: 117, templateName: '第五课：语音识别（一）' },
      { id: 118, templateName: '第四课：流式大模型的应用实践人工智能' },
    ];
    setOfficialTemplateCount(detailTemplates.length);
  };

  // 获取详细的官方模板列表用于显示
  const fetchDetailOfficialTemplates = async () => {
    // 模拟详细的官方模板数据
    const detailTemplates: Template[] = [
      { id: 101, templateName: '全网模板', templateDescription: '适用于全网教学', createTime: '2025-01-01', userId: 1, roleId: 1, status: 1, isOfficial: true, category: 'official' as const },
      { id: 102, templateName: 'ZWJ所有积木模板', templateDescription: 'ZWJ专用模板', createTime: '2025-01-02', userId: 1, roleId: 1, status: 1, isOfficial: true, category: 'official' as const },
      { id: 103, templateName: '测试模板', templateDescription: '测试专用模板', createTime: '2025-01-03', userId: 1, roleId: 1, status: 1, isOfficial: true, category: 'official' as const },
      { id: 104, templateName: '演示模板', templateDescription: '演示专用模板', createTime: '2025-01-04', userId: 1, roleId: 1, status: 1, isOfficial: true, category: 'official' as const },
      { id: 105, templateName: 'microbit', templateDescription: 'microbit编程模板', createTime: '2025-01-05', userId: 1, roleId: 1, status: 1, isOfficial: true, category: 'official' as const },
      { id: 106, templateName: '系统默认全校模板', templateDescription: '系统推荐模板', createTime: '2025-01-06', userId: 1, roleId: 1, status: 1, isOfficial: true, category: 'official' as const },
      { id: 107, templateName: '全网大模板', templateDescription: '全网通用大模板', createTime: '2025-01-07', userId: 1, roleId: 1, status: 1, isOfficial: true, category: 'official' as const },
      { id: 108, templateName: '基础新手模板plus', templateDescription: '新手入门增强版', createTime: '2025-01-08', userId: 1, roleId: 1, status: 1, isOfficial: true, category: 'official' as const },
      { id: 109, templateName: '全网积木', templateDescription: '全网积木编程', createTime: '2025-01-09', userId: 1, roleId: 1, status: 1, isOfficial: true, category: 'official' as const },
      { id: 110, templateName: '第十四课：AI绘画（四）', templateDescription: 'AI绘画第四课', createTime: '2025-01-10', userId: 1, roleId: 1, status: 1, isOfficial: true, category: 'official' as const },
      { id: 111, templateName: '第十三课：AI绘画（三）', templateDescription: 'AI绘画第三课', createTime: '2025-01-11', userId: 1, roleId: 1, status: 1, isOfficial: true, category: 'official' as const },
      { id: 112, templateName: '第十二课：AI绘画（二）', templateDescription: 'AI绘画第二课', createTime: '2025-01-12', userId: 1, roleId: 1, status: 1, isOfficial: true, category: 'official' as const },
      { id: 113, templateName: '第一课：AI绘画（一）', templateDescription: 'AI绘画第一课', createTime: '2025-01-13', userId: 1, roleId: 1, status: 1, isOfficial: true, category: 'official' as const },
      { id: 114, templateName: '第十课：视觉识别（二）', templateDescription: '视觉识别第二课', createTime: '2025-01-14', userId: 1, roleId: 1, status: 1, isOfficial: true, category: 'official' as const },
      { id: 115, templateName: '第八课：语音合成', templateDescription: '语音合成课程', createTime: '2025-01-15', userId: 1, roleId: 1, status: 1, isOfficial: true, category: 'official' as const },
      { id: 116, templateName: '第六课：语音识别（二）', templateDescription: '语音识别第二课', createTime: '2025-01-16', userId: 1, roleId: 1, status: 1, isOfficial: true, category: 'official' as const },
      { id: 117, templateName: '第五课：语音识别（一）', templateDescription: '语音识别第一课', createTime: '2025-01-17', userId: 1, roleId: 1, status: 1, isOfficial: true, category: 'official' as const },
      { id: 118, templateName: '第四课：流式大模型的应用实践人工智能', templateDescription: '流式大模型应用', createTime: '2025-01-18', userId: 1, roleId: 1, status: 1, isOfficial: true, category: 'official' as const },
    ];
    setDetailTemplates(detailTemplates);
  };

  // 处理模板点击事件
  const handleTemplateClick = (template: Template) => {
    if (template.isOfficial && template.id === 2) {
      // 课程模板，显示详细页面
      setShowOfficialDetail(true);
      fetchDetailOfficialTemplates();
    } else if (!template.isOfficial) {
      // 我的模板，进入编辑模式
      handleEditTemplate(template);
    }
  };

  // 返回主页面
  const handleBackToMain = () => {
    setShowOfficialDetail(false);
    setDetailTemplates([]);
  };

  // 返回文件夹列表
  const handleBackToFolders = () => {
    setCurrentView('folders');
    setCurrentFolder(null);
    setTemplates([]); // 清空模板列表数据
  };

  // 处理文件夹点击
  const handleFolderClick = async (folder: any) => {
    setCurrentFolder(folder);
    setCurrentView('templates');
    setTemplates([]); // 先清空之前的数据
    setLoadingFolderTemplates(true); // 开始加载时显示加载动画

    try {
      const folderRes = await roleTemplateFolderApi.getFolderTemplates(folder.id);
      if (folderRes.data.data.code === 200) {
        const templatePromises = folderRes.data.data.data.map((item: { id: number }) =>
          roleTemplateFolderApi.getTemplateInfo(item.id)
        );
        const templateResults = await Promise.all(templatePromises);
        const folderTemplates = templateResults
          .filter(res => res.data.code === 200)
          .map(res => {
            const templateData = res.data.data;
            return {
              id: templateData.id,
              templateName: templateData.templateName,
              templateDescription: templateData.templateDescription || '',
              createTime: templateData.createTime,
              userId: templateData.userId,
              roleId: templateData.roleId,
              status: templateData.status,
              category: 'official' as const,
              isOfficial: true,
              lastModified: templateData.updateTime || templateData.createTime
            };
          });

        setTemplates(folderTemplates);
      }
    } catch (error) {
      console.error('获取文件夹模板失败:', error);
    } finally {
      setLoadingFolderTemplates(false);
    }
  };

  // 获取文件夹列表
  const fetchFolders = async () => {
    setLoadingFolders(true);
    try {
      const response = await roleTemplateFolderApi.getFolderList();
      if (response.data.code === 200) {
        // 获取文件夹列表并计算每个文件夹的模板数量
        const foldersWithCount = await Promise.all(
          response.data.data.data.map(async (folder: any) => {
            try {
              const folderRes = await roleTemplateFolderApi.getFolderTemplates(folder.id);
              const templateCount = folderRes.data.data?.code === 200 ? folderRes.data.data.data.length : 0;
              return {
                ...folder,
                templateCount
              };
            } catch (error) {
              console.error(`获取文件夹 ${folder.id} 模板数量失败:`, error);
              return {
                ...folder,
                templateCount: 0
              };
            }
          })
        );
        setFolders(foldersWithCount);
      }
    } catch (error) {
      console.error('获取文件夹列表失败:', error);
    } finally {
      setLoadingFolders(false);
    }
  };



  // 获取官方模板
  const fetchOfficialTemplates = async () => {
    try {
      const response = await getOfficialTemplates();
      if (response.data.code === 200) {
        return response.data.data.map((template: any) => ({
          ...template,
          category: 'official' as const,
          isOfficial: true
        }));
      }
    } catch (error) {
      console.error('获取官方模板失败:', error);
    }
    return [];
  };

  // 获取当前使用的模板
  const fetchCurrentTemplate = async () => {
    if (!userId) return;

    try {
      const response = await getUserCurrentTemplate(userId);
      if (response.data.code === 200) {
        setCurrentTemplate(response.data.data);
      }
    } catch (error) {
      console.error('获取当前使用模板失败:', error);
    }
  };

  // 获取所有模板
  const fetchTemplates = async () => {
    if (!mounted || !userId) return;

    setLoading(true);
    try {
      const [myTemplates, officialTemplates] = await Promise.all([
        fetchMyTemplates(),
        fetchOfficialTemplates()
      ]);

      setTemplates([...myTemplates, ...officialTemplates]);
    } catch (error) {
      console.error('获取模板列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (mounted && userId) {
      if (activeTab === 'my') {
        fetchTemplates();
      } else {
        fetchFolders();
      }
      fetchCurrentTemplate();
      fetchOfficialTemplateCount();
    }
  }, [mounted, userId, activeTab]);

  // 处理创建模板成功
  const handleCreateTemplateSuccess = () => {
    fetchTemplates(); // 刷新模板列表
    setIsCreateModalOpen(false);
  };

  // 处理编辑模板
  const handleEditTemplate = async (template: Template) => {
    try {
      // 检查模板详情是否可以获取
      const response = await getTemplateInfo(template.id);
      if (response.data.code === 200) {
        setEditingTemplateId(template.id);
        setIsEditModalOpen(true);
      } else {
        console.error('获取模板详情失败:', response.data.message);
      }
    } catch (error) {
      console.error('获取模板详情失败:', error);
    }
  };

  // 处理编辑模板成功
  const handleEditTemplateSuccess = () => {
    fetchTemplates(); // 刷新模板列表
    setIsEditModalOpen(false);
    setEditingTemplateId(null);
  };

  // 处理使用模板
  const handleUseTemplate = async (templateId: number) => {
    console.log('=== 开始切换模板 ===');
    console.log('目标模板ID:', templateId);
    console.log('当前用户ID:', userId);
    console.log('当前角色ID:', roleId);

    try {
      const response = await addUserJoinRole({
        userId: userId,
        roleId: roleId || 2, // 默认为教师角色
        templateId: templateId
      });

      console.log('模板切换API响应:', response.data);

      if (response.data.code === 200) {
        notification.success('模板应用成功');

        // 刷新当前使用的模板状态
        fetchCurrentTemplate();

        // 通知全局模板变化，触发所有班级学生模板重置
        notifyGlobalTemplateChange();
      } else {
        console.error('模板应用失败:', response.data);
        notification.error(response.data.message || '模板应用失败');
      }
    } catch (error) {
      console.error('应用模板失败:', error);
      notification.error('应用模板失败');
    }
  };

  // 处理删除模板
  const handleDeleteTemplate = async (templateId: number) => {
    try {
      const response = await deleteTemplate(templateId);
      if (response.data.code === 200) {
        console.log('删除模板成功');
        fetchTemplates(); // 刷新模板列表
        setIsEditModalOpen(false);
        setEditingTemplateId(null);
      } else {
        console.error('删除模板失败:', response.data.message);
      }
    } catch (error) {
      console.error('删除模板失败:', error);
    }
  };

  const filteredTemplates = templates.filter(template =>
    template.category === activeTab &&
    (template.templateName.toLowerCase().includes(searchQuery.toLowerCase()) ||
     (template.templateDescription && template.templateDescription.toLowerCase().includes(searchQuery.toLowerCase())))
  );

  return (
    <div className="template-management">
     

      {/* 标签页切换 */}
      <div className="template-tabs">
        <div className={`template-tabs-group ${activeTab === 'my' ? 'official-active' : ''}`}>
          <button
            className="tab-btn"
            onClick={() => setActiveTab('official')}
          >
            官方模板
          </button>
          <button
            className={`tab-btn ${activeTab === 'my' ? 'active' : ''}`}
            onClick={() => setActiveTab('my')}
          >
            我的模板
          </button>
        </div>
        {activeTab === 'my' && (
          <button className="create-template-btn" onClick={() => setIsCreateModalOpen(true)}>
            <div className="create-template-icon">
              <Plus size={16} />
            </div>
            创建模板
          </button>
        )}
      </div>

      {/* 模板网格 */}
      {showOfficialDetail ? (
        <div>
          <div className="detail-header">
            <button className="back-btn" onClick={handleBackToMain}>
              ← 返回
            </button>
          </div>
          <div className="template-grid">
            {detailTemplates.map((template) => (
              <div
                key={template.id}
                className="template-card detail-template-card"
                onClick={() => handleUseTemplate(template.id)}
                style={{ cursor: 'pointer' }}
              >
                <div className="template-icon">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" fill="#FFB800" stroke="#FFB800" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </div>
                <div className="template-info">
                  <div className="template-title">
                    <div className="template-name-with-badges">
                      <span className="template-name">{template.templateName}</span>
                      <span className="template-type official">官方</span>
                    </div>
                    <div className="template-meta">
                      <span className="create-time">
                        创建于{new Date(template.createTime).toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                </div>
                <div className="template-actions">
                  <div className="usage-info">暂无学生使用</div>
                  <button
                    className="use-template-btn"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleUseTemplate(template.id);
                    }}
                  >
                    使用此模板
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      ) : (
        <div className={`template-grid ${activeTab === 'my' ? 'my-templates' : ''}`}>
          {activeTab === 'official' && currentView === 'folders' ? (
            // 文件夹视图
            loadingFolders ? (
              <div className="loading-placeholder">
                <div className="loading-spinner"></div>
                <p>正在加载文件夹...</p>
              </div>
            ) : (
              <div className="folder-grid">
                {/* 文件夹列表 */}
                {folders.map(folder => (
                  <div
                    key={folder.id}
                    className="folder-card"
                    onClick={() => handleFolderClick(folder)}
                  >
                    <div className="folder-card-content">
                      <div className="folder-card-left">
                        <div className="folder-icon">
                          <svg width="24" height="24" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M5 8C5 6.89543 5.89543 6 7 6H19L24 12H41C42.1046 12 43 12.8954 43 14V40C43 41.1046 42.1046 42 41 42H7C5.89543 42 5 41.1046 5 40V8Z" fill="#F97316" stroke="#F97316" strokeWidth="2" strokeLinejoin="round"/>
                          </svg>
                        </div>
                        <div className="folder-info">
                          <h3 className="folder-name">
                            {folder.folderName}
                            <span className="official-tag">官方</span>
                          </h3>
                          <p className="folder-count">
                            {folder.templateCount || 0} 个模板
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )
          ) : activeTab === 'official' && currentView === 'templates' ? (
            // 文件夹内模板视图
            <div>
              <div className="folder-header">
                <button className="back-btn" onClick={handleBackToFolders}>
                  ← 返回
                </button>
              </div>

              {loadingFolderTemplates ? (
                <div className="loading-placeholder">
                  <div className="loading-spinner"></div>
                  <p>正在加载模板...</p>
                </div>
              ) : templates.length === 0 ? (
                <div className="empty-placeholder">
                  <p>当前文件夹暂无模板</p>
                </div>
              ) : (
                <div className="template-list">
                  {templates.map((template) => (
                    <div
                      key={template.id}
                      className="template-card"
                      onClick={() => handleTemplateClick(template)}
                    >
                      <div className="template-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" fill="#FFB800" stroke="#FFB800" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                      </div>
                      <div className="template-info">
                        <div className="template-title">
                          <div className="template-name-with-badges">
                            <span className="template-name">{template.templateName}</span>
                            <span className="template-type official">官方</span>
                          </div>
                          <div className="template-meta">
                            <span className="create-time">
                              创建于{new Date(template.createTime).toLocaleDateString()}
                            </span>
                          </div>
                        </div>
                      </div>
                      <div className="template-actions">
                        <div className="usage-info">暂无学生使用</div>
                        <button
                          className={`use-template-btn ${currentTemplate?.templateId === template.id ? 'current' : ''}`}
                          onClick={(e) => {
                            e.stopPropagation();
                            handleUseTemplate(template.id);
                          }}
                        >
                          {currentTemplate?.templateId === template.id ? '当前使用' : '使用此模板'}
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          ) : (
            // 我的模板视图
            loading ? (
              <div className="loading-placeholder">
                <div className="loading-spinner"></div>
                <p>正在加载模板...</p>
              </div>
            ) : filteredTemplates.length === 0 ? (
              <div className="empty-placeholder">
                <p>暂无{activeTab === 'my' ? '我的' : '官方'}模板</p>
              </div>
            ) : (
            filteredTemplates.map((template) => (
            <div
              key={template.id}
              className={`template-card ${!template.isOfficial || template.id === 2 ? 'editable' : ''}`}
              onClick={() => handleTemplateClick(template)}
              style={{ cursor: !template.isOfficial || template.id === 2 ? 'pointer' : 'default' }}
            >
              <div className="template-icon">
                {template.isOfficial ? (
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" fill="#FFB800" stroke="#FFB800" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                ) : (
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect x="3" y="3" width="7" height="7" rx="1" fill="white"/>
                    <rect x="14" y="3" width="7" height="7" rx="1" fill="white"/>
                    <rect x="3" y="14" width="7" height="7" rx="1" fill="white"/>
                    <rect x="14" y="14" width="7" height="7" rx="1" fill="white"/>
                  </svg>
                )}
              </div>
              <div className="template-info">
                <div className="template-title">
                  <div className="template-name-with-badges">
                    <span className="template-name">{template.templateName}</span>
                    <span className={`template-type ${template.isOfficial ? 'official' : ''}`}>
                      {template.isOfficial ? '官方' : '自定义'}
                    </span>
                    {currentTemplate?.templateId === template.id && (
                      <span className="current-template-badge">当前使用</span>
                    )}
                  </div>
                  <div className="template-meta">
                    <span className="create-time">
                      {template.id === 2 ? `${officialTemplateCount} 个模板` : `创建于${new Date(template.createTime).toLocaleDateString()}`}
                    </span>
                  </div>
                </div>
                {!template.isOfficial && (
                  <div className="edit-hint">
                    <span>点击编辑模板</span>
                  </div>
                )}
              </div>
              <div className="template-actions">
                {/* 课程模板（id为2）不显示使用情况信息 */}
                {template.id !== 2 && (
                  <div className="usage-info">
                    {template.usageCount ? `${template.usageCount}名学生使用` : '暂无学生使用'}
                  </div>
                )}
                {/* 课程模板（id为2）不显示使用此模板按钮 */}
                {template.id !== 2 && (
                  <button
                    className={`use-template-btn ${currentTemplate?.templateId === template.id ? 'current' : ''}`}
                    onClick={(e) => {
                      e.stopPropagation(); // 阻止事件冒泡
                      if (currentTemplate?.templateId !== template.id) {
                        handleUseTemplate(template.id);
                      }
                    }}
                    disabled={currentTemplate?.templateId === template.id}
                  >
                    {currentTemplate?.templateId === template.id ? '当前使用' : '使用此模板'}
                  </button>
                )}
              </div>
            </div>
          ))
            )
          )}
        </div>
      )}

      {/* 创建模板弹窗 */}
      <PermissionTemplateModal
        visible={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSuccess={handleCreateTemplateSuccess}
        roleId={roleId || 2} // 默认为教师角色
        userId={userId || 0}
      />

      {/* 编辑模板弹窗 */}
      {isEditModalOpen && editingTemplateId && (
        <PermissionTemplateModal
          visible={isEditModalOpen}
          onClose={() => {
            setIsEditModalOpen(false);
            setEditingTemplateId(null);
          }}
          onSuccess={handleEditTemplateSuccess}
          onDeleteTemplate={handleDeleteTemplate}
          roleId={roleId || 2}
          userId={userId || 0}
          templateId={editingTemplateId}
        />
      )}
    </div>
  );
};

export default TemplateManagement;
