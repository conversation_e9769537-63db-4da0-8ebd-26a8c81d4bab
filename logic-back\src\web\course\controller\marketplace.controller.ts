import { Controller, Get, Query, UseGuards, Param, Post, Put, Delete, Body, UseFilters } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiParam } from '@nestjs/swagger';
import { MarketplaceService } from '../application/services/marketplace/marketplace.service';

import { GetSeriesListQueryDto, SeriesListResponseDto } from '../application/dto/marketplace/series-list.dto';
import { SeriesDetailResponseDto } from '../application/dto/marketplace/series-detail.dto';
import { CourseDetailResponseDto } from '../application/dto/marketplace/course-detail.dto';
import { GetTagsListQueryDto, TagsListResponseDto } from '../application/dto/marketplace/tags-list.dto';
import { CreateTagDto, UpdateTagDto, TagDetailResponseDto, DeleteTagResponseDto } from '../application/dto/marketplace/tag-management.dto';
import { HttpResponseResultService } from 'src/web/http_response_result/http_response_result.service';
import { NotLogin } from 'src/web/router_guard/not-login.decorator';


@ApiTags('课程广场')
@Controller('api/v1/course-marketplace')
@ApiBearerAuth("access-token")
export class MarketplaceController {
  constructor(
    private readonly marketplaceService: MarketplaceService,
    private readonly httpResponseResultService: HttpResponseResultService,
  ) {}

  @ApiOperation({ 
    summary: '查询系列课程列表',
    description: '获取课程广场的系列课程列表，支持分页、分类筛选和标签筛选'
  })
  @ApiResponse({
    status: 200,
    description: '查询成功',
    type: SeriesListResponseDto,
    examples: {
      success: {
        summary: '成功响应',
        value: {
          code: 200,
          message: 'success',
          data: {
            list: [
              {
                id: 1,
                title: 'Python编程入门系列',
                description: '适合零基础学员的Python课程',
                coverImage: 'https://example.com/cover1.jpg',
                category: 0,
                categoryLabel: '官方',
                status: 1,
                statusLabel: '已发布',
                projectMembers: '张老师、李助教',
                totalCourses: 5,
                totalStudents: 1200,
                contentSummary: {
                  hasVideo: true,
                  hasDocument: true,
                  hasAudio: false,
                  videoCourseCount: 4,
                  documentCourseCount: 5,
                  averageVideoDuration: 1800,
                  totalResourcesCount: 15
                },
                createdAt: '2024-01-15T10:30:00Z',
                tags: [
                  {
                    id: 1,
                    name: '编程',
                    color: '#007bff',
                    category: 1,
                    categoryLabel: '类型'
                  },
                  {
                    id: 2,
                    name: '入门',
                    color: '#28a745',
                    category: 0,
                    categoryLabel: '难度'
                  }
                ]
              }
            ],
            pagination: {
              page: 1,
              pageSize: 10,
              total: 25,
              totalPages: 3,
              hasNext: true,
              hasPrev: false
            },
            filterStats: {
              totalSeries: 25,
              officialCount: 18,
              communityCount: 7,
              videoSeriesCount: 20,
              documentSeriesCount: 22
            }
          }
        }
      }
    }
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
    examples: {
      badRequest: {
        summary: '参数错误',
        value: {
          code: 400,
          message: '页码必须大于0',
          data: null
        }
      }
    }
  })
  @ApiResponse({
    status: 500,
    description: '服务器内部错误',
    examples: {
      serverError: {
        summary: '服务器错误',
        value: {
          code: 500,
          message: '服务器内部错误，请稍后重试',
          data: null
        }
      }
    }
  })
  @Get('series')
  @NotLogin() // 课程广场是公开接口，不需要登录1
  // 1.获取系列列表，用户广场展示
  async getSeriesList(@Query() query: GetSeriesListQueryDto) {
    const result = await this.marketplaceService.getSeriesList(query);
    return this.httpResponseResultService.success(result, 'success', 200);
  }

  @ApiOperation({
    summary: '查询系列课程详情及子课程',
    description: '获取系列课程详情，包含该系列下所有已发布的子课程，默认返回第一个课程作为当前显示课程'
  })
  @ApiParam({ name: 'seriesId', description: '系列课程ID', type: Number })
  @ApiResponse({
    status: 200,
    description: '查询成功',
    type: SeriesDetailResponseDto,
    examples: {
      success: {
        summary: '成功响应',
        value: {
          code: 200,
          message: 'success',
          data: {
            id: 123,
            title: 'React前端开发系列',
            description: '从零开始学习React开发，包含基础概念、组件开发、状态管理等核心内容',
            coverImage: 'https://example.com/series-cover.jpg',
            category: 0,
            categoryLabel: '官方',
            status: 1,
            statusLabel: '已发布',
            projectMembers: '张老师、李助教、王同学',
            totalCourses: 3,
            totalStudents: 850,
            creatorId: 100,
            createdAt: '2024-01-15T10:30:00Z',
            updatedAt: '2024-01-22T09:15:00Z',
            tags: [
              {
                id: 1,
                name: '前端开发',
                color: '#007bff',
                category: 1,
                categoryLabel: '类型'
              }
            ],
            courses: [
              {
                id: 1,
                title: '第一课：React基础概念',
                description: '了解React的核心概念和基本用法',
                coverImage: 'https://example.com/course1-cover.jpg',
                orderIndex: 1,
                status: 1,
                statusLabel: '已发布',
                hasVideo: 1,
                hasDocument: 1,
                hasAudio: 0,
                videoDuration: 1800,
                videoDurationLabel: '30分钟',
                videoName: 'React基础概念讲解.mp4',
                firstTeachingTitle: '教学目标',
                resourcesCount: 2
              }
            ],
            defaultCourse: {
              id: 1,
              title: '第一课：React基础概念',
              contentConfig: {
                hasVideo: 1,
                hasDocument: 1,
                video: {
                  url: 'https://example.com/videos/react-basics.mp4',
                  name: 'React基础概念讲解.mp4'
                }
              },
              teachingInfo: [
                {
                  title: '教学目标',
                  content: ['理解React的核心概念']
                }
              ],
              additionalResources: [
                {
                  title: 'React官方文档',
                  url: 'https://react.dev/',
                  description: 'React官方学习资源'
                }
              ]
            },
            currentCourseId: 1
          }
        }
      }
    }
  })
  @ApiResponse({
    status: 404,
    description: '系列课程不存在',
    examples: {
      notFound: {
        summary: '系列不存在',
        value: {
          code: 404,
          message: '系列课程不存在',
          data: null
        }
      }
    }
  })
  @ApiResponse({
    status: 500,
    description: '服务器内部错误',
    examples: {
      serverError: {
        summary: '服务器错误',
        value: {
          code: 500,
          message: '服务器内部错误，请稍后重试',
          data: null
        }
      }
    }
  })
  @Get('series/:seriesId')
  @NotLogin() // 课程详情也是公开接口，不需要登录
  // 2.获取系列详情，包含子课程列表和默认课程详情
  async getSeriesDetail(@Param('seriesId') seriesId: number) {
    const result = await this.marketplaceService.getSeriesDetail(seriesId);
    return this.httpResponseResultService.success(result, 'success', 200);
  }

  @ApiOperation({
    summary: '切换到指定子课程',
    description: '获取系列中指定课程的详细内容'
  })
  @ApiParam({ name: 'seriesId', description: '系列课程ID', type: Number })
  @ApiParam({ name: 'courseId', description: '子课程ID', type: Number })
  @ApiResponse({
    status: 200,
    description: '查询成功',
    type: CourseDetailResponseDto,
    examples: {
      success: {
        summary: '成功响应',
        value: {
          code: 200,
          message: 'success',
          data: {
            id: 2,
            title: '第二课：组件与Props',
            description: '学习React组件的创建和属性传递',
            coverImage: 'https://example.com/course2-cover.jpg',
            hasVideo: 1,
            hasDocument: 1,
            hasAudio: 1,
            videoDuration: 2400,
            videoDurationLabel: '40分钟',
            videoName: 'React组件与Props.mp4',
            resourcesCount: 3,
            contentConfig: {
              hasVideo: 1,
              hasDocument: 1,
              hasAudio: 1,
              video: {
                url: 'https://example.com/videos/react-components.mp4',
                name: 'React组件与Props.mp4'
              },
              document: {
                url: 'https://example.com/documents/react-components-slides.pdf',
                name: 'React组件与Props课件.pdf'
              },
              audio: {
                url: 'https://example.com/audio/react-components.mp3',
                name: 'React组件与Props音频.mp3'
              }
            },
            teachingInfo: [
              {
                title: '教学目标',
                content: [
                  '理解React组件的概念和作用',
                  '掌握函数组件和类组件的创建方法'
                ]
              }
            ],
            additionalResources: [
              {
                title: 'React组件文档',
                url: 'https://react.dev/learn/your-first-component',
                description: 'React官方组件学习指南'
              }
            ],
            orderIndex: 2,
            status: 1,
            statusLabel: '已发布',
            currentCourseId: 2
          }
        }
      }
    }
  })
  @ApiResponse({
    status: 404,
    description: '系列或课程不存在',
    examples: {
      seriesNotFound: {
        summary: '系列不存在',
        value: {
          code: 404,
          message: '系列课程不存在，ID: 123',
          data: null
        }
      },
      courseNotFound: {
        summary: '课程不存在',
        value: {
          code: 404,
          message: '课程不存在或未发布，系列ID: 123，课程ID: 999',
          data: null
        }
      }
    }
  })
  @ApiResponse({
    status: 500,
    description: '服务器内部错误',
    examples: {
      serverError: {
        summary: '服务器错误',
        value: {
          code: 500,
          message: '服务器内部错误，请稍后重试',
          data: null
        }
      }
    }
  })
  @Get('series/:seriesId/courses/:courseId')
  @NotLogin() // 课程详情也是公开接口，不需要登录
  // 3.获取指定课程详情，用于课程切换
  async getCourseDetail(
    @Param('seriesId') seriesId: number,
    @Param('courseId') courseId: number
  ) {
    const result = await this.marketplaceService.getCourseDetail(seriesId, courseId);
    return this.httpResponseResultService.success(result, 'success', 200);
  }

  @ApiOperation({
    summary: '获取课程标签列表',
    description: '获取所有激活状态的课程标签，用于筛选'
  })
  @ApiResponse({
    status: 200,
    description: '查询成功',
    type: TagsListResponseDto,
    examples: {
      success: {
        summary: '成功响应',
        value: {
          code: 200,
          message: 'success',
          data: {
            list: [
              {
                id: 1,
                name: '编程',
                color: '#007bff',
                category: 1,
                categoryLabel: '类型',
                description: '编程相关课程',
                usageCount: 25,
                status: 1,
                statusLabel: '启用',
                createdAt: '2024-01-10T10:30:00Z'
              },
              {
                id: 2,
                name: '入门',
                color: '#28a745',
                category: 0,
                categoryLabel: '官方',
                description: '适合初学者的课程',
                usageCount: 18,
                status: 1,
                statusLabel: '启用',
                createdAt: '2024-01-10T10:30:00Z'
              }
            ],
            pagination: {
              page: 1,
              pageSize: 50,
              total: 12,
              totalPages: 1,
              hasNext: false,
              hasPrev: false
            }
          }
        }
      }
    }
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
    examples: {
      badRequest: {
        summary: '参数错误',
        value: {
          code: 400,
          message: '页码必须大于0',
          data: null
        }
      }
    }
  })
  @ApiResponse({
    status: 500,
    description: '服务器内部错误',
    examples: {
      serverError: {
        summary: '服务器错误',
        value: {
          code: 500,
          message: '服务器内部错误，请稍后重试',
          data: null
        }
      }
    }
  })
  @Get('tags')
  @NotLogin() // 标签列表是公开接口，不需要登录
  // 4.获取标签列表，用于筛选功能
  async getTagsList(@Query() query: GetTagsListQueryDto) {
    const result = await this.marketplaceService.getTagsList(query);
    return this.httpResponseResultService.success(result, 'success', 200);
  }

  @ApiOperation({
    summary: '创建标签',
    description: '创建新的课程标签'
  })
  @ApiResponse({
    status: 201,
    description: '创建成功',
    type: TagDetailResponseDto,
    examples: {
      success: {
        summary: '成功响应',
        value: {
          code: 200,
          message: '标签创建成功',
          data: {
            id: 1,
            name: '编程',
            color: '#007bff',
            category: 1,
            categoryLabel: '类型',
            description: '编程相关课程',
            usageCount: 0,
            status: 1,
            statusLabel: '启用',
            createdAt: '2024-01-10T10:30:00Z',
            updatedAt: '2024-01-10T10:30:00Z'
          }
        }
      }
    }
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误或标签名称已存在',
    examples: {
      nameExists: {
        summary: '标签名称已存在',
        value: {
          code: 400,
          message: '标签名称 "编程" 已存在',
          data: null
        }
      },
      validationError: {
        summary: '参数验证错误',
        value: {
          statusCode: 400,
          message: ['标签名称不能为空', '标签分类必须是 0=难度，1=类型，2=特色，3=其他 中的一个'],
          error: 'Bad Request'
        }
      }
    }
  })
  @Post('tags')
  // 5.创建标签，需要管理员权限
  async createTag(@Body() createTagDto: CreateTagDto) {
    const result = await this.marketplaceService.createTag(createTagDto);
    return this.httpResponseResultService.success(result, '标签创建成功', 200);
  }

  @ApiOperation({
    summary: '更新标签',
    description: '更新指定标签的信息'
  })
  @ApiParam({ name: 'id', description: '标签ID', type: Number })
  @ApiResponse({
    status: 200,
    description: '更新成功',
    type: TagDetailResponseDto
  })
  @ApiResponse({
    status: 404,
    description: '标签不存在',
    examples: {
      notFound: {
        summary: '标签不存在',
        value: {
          code: 404,
          message: '标签不存在，ID: 999',
          data: null
        }
      }
    }
  })
  @Put('tags/:id')
  // 6.更新标签，需要管理员权限
  async updateTag(@Param('id') id: number, @Body() updateTagDto: UpdateTagDto) {
    const result = await this.marketplaceService.updateTag(id, updateTagDto);
    return this.httpResponseResultService.success(result, '标签更新成功', 200);
  }

  @ApiOperation({
    summary: '删除标签',
    description: '删除指定的标签'
  })
  @ApiParam({ name: 'id', description: '标签ID', type: Number })
  @ApiResponse({
    status: 200,
    description: '删除成功',
    type: DeleteTagResponseDto,
    examples: {
      success: {
        summary: '成功响应',
        value: {
          code: 200,
          message: '标签删除成功',
          data: null
        }
      }
    }
  })
  @ApiResponse({
    status: 404,
    description: '标签不存在',
    examples: {
      notFound: {
        summary: '标签不存在',
        value: {
          code: 404,
          message: '标签不存在，ID: 999',
          data: null
        }
      }
    }
  })
  @ApiResponse({
    status: 400,
    description: '标签正在使用中，无法删除',
    examples: {
      inUse: {
        summary: '标签正在使用',
        value: {
          code: 400,
          message: '标签正在被 5 个系列课程使用，无法删除',
          data: null
        }
      }
    }
  })
  @Delete('tags/:id')
  // 7.删除标签，需要管理员权限
  async deleteTag(@Param('id') id: number) {
    await this.marketplaceService.deleteTag(id);
    return this.httpResponseResultService.success(null, '标签删除成功', 200);
  }

  @ApiOperation({
    summary: '获取标签详情',
    description: '获取指定标签的详细信息'
  })
  @ApiParam({ name: 'id', description: '标签ID', type: Number })
  @ApiResponse({
    status: 200,
    description: '查询成功',
    type: TagDetailResponseDto
  })
  @ApiResponse({
    status: 404,
    description: '标签不存在',
    examples: {
      notFound: {
        summary: '标签不存在',
        value: {
          code: 404,
          message: '标签不存在，ID: 999',
          data: null
        }
      }
    }
  })
  @Get('tags/:id')
  @NotLogin() // 标签详情是公开接口，不需要登录
  // 8.获取标签详情
  async getTagById(@Param('id') id: number) {
    const result = await this.marketplaceService.getTagById(id);
    return this.httpResponseResultService.success(result, 'success', 200);
  }
}
