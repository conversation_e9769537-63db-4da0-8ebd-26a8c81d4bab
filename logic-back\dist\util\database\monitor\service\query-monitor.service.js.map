{"version": 3, "file": "query-monitor.service.js", "sourceRoot": "", "sources": ["../../../../../src/util/database/monitor/service/query-monitor.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAAoD;AACpD,2CAA+C;AAoBxC,IAAM,mBAAmB,2BAAzB,MAAM,mBAAmB;IAgBX;IAfF,MAAM,GAAG,IAAI,eAAM,CAAC,qBAAmB,CAAC,IAAI,CAAC,CAAC;IACvD,kBAAkB,CAAS;IAC3B,sBAAsB,CAAU;IAChC,kBAAkB,CAAU;IAC5B,gBAAgB,CAAU;IAC1B,eAAe,CAAU;IACzB,YAAY,CAAS;IACrB,wBAAwB,CAAU;IAClC,wBAAwB,CAAU;IAClC,gBAAgB,GAAsB,EAAE,CAAC;IACzC,YAAY,CAAe;IAC3B,mBAAmB,CAAS;IAC5B,YAAY,GAAW,CAAC,CAAC;IAEjC,YACmB,aAA4B;QAA5B,kBAAa,GAAb,aAAa,CAAe;QAG7C,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAU,mCAAmC,EAAE,IAAI,CAAC,CAAC;QAC3G,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAU,0BAA0B,EAAE,KAAK,CAAC,CAAC;QAC1F,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,6BAA6B,EAAE,IAAI,CAAC,CAAC;QAC9F,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAU,iCAAiC,EAAE,IAAI,CAAC,CAAC;QACvG,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAU,6BAA6B,EAAE,IAAI,CAAC,CAAC;QAC/F,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAU,2BAA2B,EAAE,IAAI,CAAC,CAAC;QAC3F,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,uBAAuB,EAAE,GAAG,CAAC,CAAC;QACjF,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAU,mCAAmC,EAAE,IAAI,CAAC,CAAC;QAC3G,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,8BAA8B,EAAE,GAAG,CAAC,CAAC;QAG/F,IAAI,CAAC,YAAY,EAAE,CAAC;QAGpB,IAAI,IAAI,CAAC,wBAAwB,EAAE,CAAC;YAClC,MAAM,IAAI,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC;YACrD,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,GAAG,GAAG,CAAC,CAAC,CAAC,QAAQ,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC;YACjF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,eAAe,IAAI,cAAc,IAAI,CAAC,kBAAkB,OAAO,QAAQ,EAAE,CAAC,CAAC;QAC7F,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QAChC,CAAC;IACH,CAAC;IAKD,WAAW,CAAC,KAAa,EAAE,aAAqB,EAAE,UAAkB,EAAE,OAAgB;QAEpF,IAAI,CAAC,IAAI,CAAC,wBAAwB,EAAE,CAAC;YACnC,OAAO;QACT,CAAC;QAGD,IAAI,CAAC,YAAY,EAAE,CAAC;QACpB,IAAI,IAAI,CAAC,YAAY,GAAG,GAAG,EAAE,CAAC;YAC5B,MAAM,YAAY,GAAG,CAAC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,CAAC;YACrF,IAAI,CAAC,YAAY,EAAE,CAAC;gBAClB,OAAO;YACT,CAAC;QACH,CAAC;QAGD,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC5B,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;QACpC,CAAC;QAGD,IAAI,IAAI,CAAC,sBAAsB,EAAG,CAAC;YACjC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YAExB,IAAI,IAAI,CAAC,wBAAwB,EAAE,CAAC;gBAElC,YAAY,CAAC,KAAK,IAAI,EAAE;oBACtB,MAAM,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,aAAa,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;gBACxE,CAAC,CAAC,CAAC;YACL,CAAC;iBAAM,CAAC;gBAEN,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,aAAa,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;oBAC5E,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,YAAY,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;gBACjD,CAAC,CAAC,CAAC;YACL,CAAC;QACH,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,eAAe,CAAC,KAAa,EAAE,aAAqB,EAAE,UAAkB,EAAE,OAAgB;QACtG,MAAM,eAAe,GAAoB;YACvC,KAAK,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC;YAC9F,UAAU,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC;YAClF,aAAa;YACb,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,KAAK,EAAE,IAAI,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC,SAAS;YACvF,OAAO,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO;SACpD,CAAC;QAGF,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAG5C,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC5D,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QACjF,CAAC;QAGD,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,CAAC;QAGnC,IAAI,CAAC,qBAAqB,CAAC,eAAe,CAAC,CAAC;IAG9C,CAAC;IAOO,aAAa,CAAC,aAAqB;QACzC,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,CAAC;QAEjC,IAAI,aAAa,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC7C,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;QAClC,CAAC;QAGD,IAAI,IAAI,CAAC,YAAY,CAAC,YAAY,KAAK,CAAC,EAAE,CAAC;YACzC,IAAI,CAAC,YAAY,CAAC,oBAAoB,GAAG,aAAa,CAAC;YACvD,IAAI,CAAC,YAAY,CAAC,gBAAgB,GAAG,aAAa,CAAC;YACnD,IAAI,CAAC,YAAY,CAAC,gBAAgB,GAAG,aAAa,CAAC;QACrD,CAAC;aAAM,CAAC;YAEN,IAAI,CAAC,YAAY,CAAC,oBAAoB;gBACpC,CAAC,IAAI,CAAC,YAAY,CAAC,oBAAoB,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,YAAY,GAAG,CAAC,CAAC,GAAG,aAAa,CAAC;oBAC/F,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC;YAGjC,IAAI,CAAC,YAAY,CAAC,gBAAgB,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,gBAAgB,EAAE,aAAa,CAAC,CAAC;YACjG,IAAI,CAAC,YAAY,CAAC,gBAAgB,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,gBAAgB,EAAE,aAAa,CAAC,CAAC;QACnG,CAAC;IACH,CAAC;IAKO,YAAY,CAAC,MAAuB;QAC1C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,MAAM,CAAC,aAAa,OAAO,MAAM,CAAC,OAAO,IAAI,EAAE,EAAE,EAAE;YAC/E,KAAK,EAAE,MAAM,CAAC,KAAK;YACnB,aAAa,EAAE,MAAM,CAAC,aAAa;YACnC,UAAU,EAAE,MAAM,CAAC,UAAU;YAC7B,SAAS,EAAE,MAAM,CAAC,SAAS;YAC3B,OAAO,EAAE,MAAM,CAAC,OAAO;SACxB,CAAC,CAAC;IACL,CAAC;IAKO,qBAAqB,CAAC,MAAuB;QAEnD,IAAI,MAAM,CAAC,aAAa,IAAI,KAAK,EAAE,CAAC;YAClC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,eAAe,MAAM,CAAC,aAAa,KAAK,EAAE;gBAC1D,KAAK,EAAE,MAAM,CAAC,KAAK;gBACnB,aAAa,EAAE,MAAM,CAAC,aAAa;gBACnC,OAAO,EAAE,MAAM,CAAC,OAAO;aACxB,CAAC,CAAC;QACL,CAAC;aAAM,IAAI,MAAM,CAAC,aAAa,IAAI,IAAI,EAAE,CAAC;YACxC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,MAAM,CAAC,aAAa,KAAK,EAAE;gBACvD,KAAK,EAAE,MAAM,CAAC,KAAK;gBACnB,aAAa,EAAE,MAAM,CAAC,aAAa;gBACnC,OAAO,EAAE,MAAM,CAAC,OAAO;aACxB,CAAC,CAAC;QACL,CAAC;IAIH,CAAC;IAKO,aAAa,CAAC,KAAa;QACjC,IAAI,CAAC,KAAK;YAAE,OAAO,EAAE,CAAC;QAGtB,MAAM,SAAS,GAAG,IAAI,CAAC;QACvB,IAAI,KAAK,CAAC,MAAM,GAAG,SAAS,EAAE,CAAC;YAC7B,OAAO,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,GAAG,KAAK,CAAC;QAC/C,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAKO,wBAAwB,CAAC,KAAa;QAC5C,IAAI,CAAC,KAAK;YAAE,OAAO,EAAE,CAAC;QAGtB,MAAM,SAAS,GAAG,GAAG,CAAC;QACtB,IAAI,KAAK,CAAC,MAAM,GAAG,SAAS,EAAE,CAAC;YAC7B,OAAO,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,GAAG,KAAK,CAAC;QAC/C,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAKO,kBAAkB,CAAC,UAAkB;QAC3C,IAAI,CAAC,UAAU;YAAE,OAAO,EAAE,CAAC;QAE3B,OAAO,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;YAC5B,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC;gBACpD,OAAO,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC;YACzC,CAAC;YACD,OAAO,KAAK,CAAC;QACf,CAAC,CAAC,CAAC;IACL,CAAC;IAKO,YAAY;QAClB,MAAM,KAAK,GAAG,IAAI,KAAK,EAAE,CAAC,KAAK,CAAC;QAChC,IAAI,CAAC,KAAK;YAAE,OAAO,EAAE,CAAC;QAGtB,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC5C,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1B,CAAC;IAKD,mBAAmB,CAAC,KAAc;QAChC,MAAM,OAAO,GAAG,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,OAAO,EAAE,CAAC;QACrD,OAAO,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;IACnD,CAAC;IAQD,eAAe;QACb,OAAO,EAAE,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;IAClC,CAAC;IAKD,YAAY;QACV,IAAI,CAAC,YAAY,GAAG;YAClB,YAAY,EAAE,CAAC;YACf,WAAW,EAAE,CAAC;YACd,oBAAoB,EAAE,CAAC;YACvB,gBAAgB,EAAE,CAAC;YACnB,gBAAgB,EAAE,CAAC;YACnB,aAAa,EAAE,IAAI,IAAI,EAAE;SAC1B,CAAC;IACJ,CAAC;IAKD,qBAAqB;QACnB,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;QAC3B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;IAC9B,CAAC;IAKD,SAAS;QACP,OAAO;YACL,wBAAwB,EAAE,IAAI,CAAC,wBAAwB;YACvD,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,kBAAkB,EAAE,IAAI,CAAC,kBAAkB;YAC3C,sBAAsB,EAAE,IAAI,CAAC,sBAAsB;YACnD,kBAAkB,EAAE,IAAI,CAAC,kBAAkB;YAC3C,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;YACvC,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,wBAAwB,EAAE,IAAI,CAAC,wBAAwB;YACvD,mBAAmB,EAAE,IAAI,CAAC,mBAAmB;YAC7C,YAAY,EAAE,IAAI,CAAC,YAAY;SAChC,CAAC;IACJ,CAAC;IAKD,YAAY,CAAC,MAUX;QACA,IAAI,MAAM,CAAC,wBAAwB,KAAK,SAAS,EAAE,CAAC;YAClD,IAAI,CAAC,wBAAwB,GAAG,MAAM,CAAC,wBAAwB,CAAC;YAChE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,MAAM,CAAC,wBAAwB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;QAC5E,CAAC;QACD,IAAI,MAAM,CAAC,eAAe,KAAK,SAAS,EAAE,CAAC;YACzC,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC,eAAe,CAAC;YAC9C,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;QACnE,CAAC;QACD,IAAI,MAAM,CAAC,kBAAkB,KAAK,SAAS,EAAE,CAAC;YAC5C,IAAI,CAAC,kBAAkB,GAAG,MAAM,CAAC,kBAAkB,CAAC;QACtD,CAAC;QACD,IAAI,MAAM,CAAC,sBAAsB,KAAK,SAAS,EAAE,CAAC;YAChD,IAAI,CAAC,sBAAsB,GAAG,MAAM,CAAC,sBAAsB,CAAC;QAC9D,CAAC;QACD,IAAI,MAAM,CAAC,kBAAkB,KAAK,SAAS,EAAE,CAAC;YAC5C,IAAI,CAAC,kBAAkB,GAAG,MAAM,CAAC,kBAAkB,CAAC;QACtD,CAAC;QACD,IAAI,MAAM,CAAC,gBAAgB,KAAK,SAAS,EAAE,CAAC;YAC1C,IAAI,CAAC,gBAAgB,GAAG,MAAM,CAAC,gBAAgB,CAAC;QAClD,CAAC;QACD,IAAI,MAAM,CAAC,YAAY,KAAK,SAAS,EAAE,CAAC;YACtC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC;QACtE,CAAC;QACD,IAAI,MAAM,CAAC,wBAAwB,KAAK,SAAS,EAAE,CAAC;YAClD,IAAI,CAAC,wBAAwB,GAAG,MAAM,CAAC,wBAAwB,CAAC;QAClE,CAAC;QACD,IAAI,MAAM,CAAC,mBAAmB,KAAK,SAAS,EAAE,CAAC;YAC7C,IAAI,CAAC,mBAAmB,GAAG,MAAM,CAAC,mBAAmB,CAAC;QACxD,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;IACvC,CAAC;CACF,CAAA;AAlVY,kDAAmB;8BAAnB,mBAAmB;IAD/B,IAAA,mBAAU,GAAE;qCAiBuB,sBAAa;GAhBpC,mBAAmB,CAkV/B"}