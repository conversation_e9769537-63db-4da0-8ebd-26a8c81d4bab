'use client'

import dynamic from 'next/dynamic'
import { motion } from 'framer-motion'
import Image from 'next/image'
import { useState, useEffect } from 'react'
import { courseApi } from '@/lib/api/course'
import type { CourseSeriesDetail } from '@/lib/api/course'
import CourseDetailView, { type SeriesCourseData } from './components/CourseDetailView'

// 动态导入 AIBackground 组件，并禁用 SSR
const AIBackground = dynamic(() => import('@/components/AIBackground'), {
  ssr: false
})



export default function CoursePlaza() {
  const [seriesCourses, setSeriesCourses] = useState<SeriesCourseData[]>([]);
  const [communityCourses, setCommunityCourses] = useState<SeriesCourseData[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAllOfficialCourses, setShowAllOfficialCourses] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [showAllCommunityCourses, setShowAllCommunityCourses] = useState(false);
  const [loadingMoreCommunity, setLoadingMoreCommunity] = useState(false);
  const [selectedCourse, setSelectedCourse] = useState<SeriesCourseData | null>(null);
  const [showCourseDetail, setShowCourseDetail] = useState(false);

  // 处理展示更多官方课程
  const handleShowMoreOfficialCourses = async () => {
    if (showAllOfficialCourses) {
      // 如果已经展示全部，则收起 - 添加平滑收起动画
      setShowAllOfficialCourses(false);
      // 平滑滚动到官方课程标题
      setTimeout(() => {
        const headings = document.querySelectorAll('h2');
        const officialSection = Array.from(headings).find(h => h.textContent?.includes('官方课程'));
        if (officialSection) {
          officialSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
      }, 100);
      return;
    }

    setLoadingMore(true);
    try {
      // 优化的懒加载延迟，提供更好的用户体验
      await new Promise(resolve => setTimeout(resolve, 800));
      setShowAllOfficialCourses(true);
    } catch (error) {
      console.error('加载更多课程失败:', error);
    } finally {
      setLoadingMore(false);
    }
  };

  // 处理展示更多社区课程
  const handleShowMoreCommunityCourses = async () => {
    if (showAllCommunityCourses) {
      // 如果已经展示全部，则收起 - 添加平滑收起动画
      setShowAllCommunityCourses(false);
      // 平滑滚动到课程社区标题
      setTimeout(() => {
        const headings = document.querySelectorAll('h2');
        const communitySection = Array.from(headings).find(h => h.textContent?.includes('课程社区'));
        if (communitySection) {
          communitySection.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
      }, 100);
      return;
    }

    setLoadingMoreCommunity(true);
    try {
      // 优化的懒加载延迟，提供更好的用户体验
      await new Promise(resolve => setTimeout(resolve, 800));
      setShowAllCommunityCourses(true);
    } catch (error) {
      console.error('加载更多社区课程失败:', error);
    } finally {
      setLoadingMoreCommunity(false);
    }
  };

  // 处理课程卡片点击
  const handleCourseClick = (course: SeriesCourseData) => {
    console.log('🎯 点击课程:', course);
    console.log('🔍 点击课程的seriesId:', course.seriesId);
    console.log('🔍 点击课程的完整数据:', JSON.stringify(course, null, 2));
    setSelectedCourse(course);
    setShowCourseDetail(true);
  };

  // 返回课程列表
  const handleBackToCourseList = () => {
    setShowCourseDetail(false);
    setSelectedCourse(null);
  };

  // 获取要显示的官方课程列表
  const getDisplayedOfficialCourses = () => {
    if (showAllOfficialCourses) {
      return seriesCourses;
    }
    return seriesCourses.slice(0, 6);
  };

  // 获取要显示的社区课程列表
  const getDisplayedCommunityCourses = () => {
    if (showAllCommunityCourses) {
      return communityCourses;
    }
    return communityCourses.slice(0, 6);
  };

  // 获取系列课程数据
  useEffect(() => {

    const fetchSeriesCourses = async () => {
      try {
        setLoading(true);
        console.log('🔄 开始获取课程市场系列课程列表...');
        console.log('🔄 准备调用 courseApi.getMarketplaceSeries（获取课程市场系列课程）...');

        // 使用课程市场系列课程列表接口
        const { data: res } = await courseApi.getMarketplaceSeries({
          page: 1,
          pageSize: 50 // 获取更多数据
          // 不筛选状态和分类，显示所有系列课程
        });


        if (res.code === 200 && res.data && res.data.list) {
          // 打印每个系列课程的详细信息
          res.data.list.forEach((item: any, index: number) => {
            console.log(`📋 系列课程 ${index + 1}:`, {
              id: item.id,
              title: item.title,
              category: item.category,
              categoryLabel: item.categoryLabel,
              status: item.status,
              statusLabel: item.statusLabel,
              totalCourses: item.totalCourses,
              totalStudents: item.totalStudents,
              coverImage: item.coverImage // 添加封面图片信息
            });
          });

          // 筛选 categoryLabel 为 "官方" 的官方课程，全部显示
          const officialCourses = res.data.list
            .filter((item: any) => {
              console.log(`🔍 检查系列课程 "${item.title}": category=${item.category}, categoryLabel="${item.categoryLabel}"`);
              return item.categoryLabel === '官方'; // 筛选官方课程 (categoryLabel = "官方")
            })
            .map((item: any) => {
              console.log(`📋 官方系列课程 "${item.title}": category=${item.category}, categoryLabel="${item.categoryLabel}", coverImage="${item.coverImage}"`);
              return {
                id: item.id,
                seriesId: item.id, // 添加系列ID，用于获取系列下的课程列表
                title: item.title,
                lessons: `共${item.totalCourses || 0}课时`,
                views: item.totalStudents?.toString() || '0',
                coverImage: (item.coverImage && !item.coverImage.includes('example.com')) ? item.coverImage : null // 过滤示例URL
              };
            });

          // 筛选 categoryLabel 为 "社区" 的社区课程，全部显示
          const communityCourses = res.data.list
            .filter((item: any) => {
              console.log(`🔍 检查社区课程 "${item.title}": category=${item.category}, categoryLabel="${item.categoryLabel}"`);
              return item.categoryLabel === '社区'; // 筛选社区课程 (categoryLabel = "社区")
            })
            .map((item: any) => {
              console.log(`📋 社区系列课程 "${item.title}": category=${item.category}, categoryLabel="${item.categoryLabel}", coverImage="${item.coverImage}"`);
              return {
                id: item.id,
                seriesId: item.id, // 添加系列ID，用于获取系列下的课程列表
                title: item.title,
                lessons: `共${item.totalCourses || 0}课时`,
                views: item.totalStudents?.toString() || '0',
                coverImage: (item.coverImage && !item.coverImage.includes('example.com')) ? item.coverImage : null // 过滤示例URL
              };
            });


          setSeriesCourses(officialCourses);
          setCommunityCourses(communityCourses);
        } else {
          setSeriesCourses([]);
        }
      } catch (error) {
        console.error('❌ 获取系列课程失败:', error);
        console.error('❌ 错误详情:', error);
        setSeriesCourses([]);
      } finally {
        setLoading(false);
      }
    };

    fetchSeriesCourses();
  }, []);

  return (
    <div className="min-h-screen">
      {showCourseDetail && selectedCourse ? (
        // 课程详情页面 - 带格子背景
        <div className="container mx-auto px-4 py-8 relative min-h-screen">
          {/* 动态格子背景 */}
          <div className="absolute inset-0 z-[-1]">
            <AIBackground />
          </div>

          <div className="max-w-7xl mx-auto relative z-10">
            <CourseDetailView
              course={selectedCourse}
              onBack={handleBackToCourseList}
            />
          </div>
        </div>
      ) : (
        <>
          {/* 背景图片 - 只在课程列表页面显示 */}
          <div className="w-full h-64 relative">
            <Image
              src="/images/mywork_background.svg"
              alt="背景图片"
              fill
              className="object-cover"
              priority
            />
          </div>

          {/* 内容区域 */}
          <div className="container mx-auto px-4 py-8 relative min-h-screen">
            {/* 动态格子背景 */}
            <div className="absolute inset-0 z-[-1]">
              <AIBackground />
            </div>

            <div className="max-w-7xl mx-auto relative z-10">
        {/* 官方课程部分 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="mb-12"
        >
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-gray-900">官方课程</h2>
            {(() => {
              console.log('🔍 按钮显示条件检查:', {
                loading,
                seriesCoursesLength: seriesCourses.length,
                shouldShowButton: !loading && seriesCourses.length > 6
              });
              return !loading && seriesCourses.length > 6;
            })() && (
              <button
                onClick={handleShowMoreOfficialCourses}
                disabled={loadingMore}
                className="group relative flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-blue-500 to-blue-600 text-white font-medium rounded-full shadow-lg hover:shadow-xl hover:from-blue-600 hover:to-blue-700 transform hover:scale-105 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none disabled:shadow-lg"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-blue-400 to-blue-500 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div className="relative flex items-center gap-2">
                  {loadingMore ? (
                    <>
                      <svg className="w-5 h-5 animate-spin" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      <span className="text-sm">加载中...</span>
                    </>
                  ) : (
                    <>
                      <span className="text-sm font-semibold">{showAllOfficialCourses ? '收起' : '查看更多'}</span>
                      <svg
                        className={`w-5 h-5 transition-all duration-300 ${showAllOfficialCourses ? 'rotate-180 scale-110' : 'group-hover:translate-y-0.5'}`}
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M19 9l-7 7-7-7" />
                      </svg>
                    </>
                  )}
                </div>
              </button>
            )}
          </div>
          {loading ? (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {[1, 2, 3].map((i) => (
                <div key={i} className="bg-white rounded-lg border border-blue-200 shadow-sm overflow-hidden">
                  <div className="h-36 bg-gradient-to-br from-blue-100 to-blue-200 animate-pulse flex items-center justify-center">
                    <svg className="w-12 h-12 text-blue-600 opacity-50" fill="none" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
                      <path d="M44 24C44 22.8954 43.1046 22 42 22C40.8954 22 40 22.8954 40 24H44ZM24 8C25.1046 8 26 7.10457 26 6C26 4.89543 25.1046 4 24 4V8ZM39 40H9V44H39V40ZM8 39V9H4V39H8ZM40 24V39H44V24H40ZM9 8H24V4H9V8ZM9 40C8.44772 40 8 39.5523 8 39H4C4 41.7614 6.23857 44 9 44V40ZM39 44C41.7614 44 44 41.7614 44 39H40C40 39.5523 39.5523 40 39 40V44ZM8 9C8 8.44772 8.44771 8 9 8V4C6.23858 4 4 6.23857 4 9H8Z" fill="currentColor"/>
                      <path d="M6 35L16.6931 25.198C17.4389 24.5143 18.5779 24.4953 19.3461 25.1538L32 36" stroke="currentColor" strokeWidth="4" strokeLinecap="round" strokeLinejoin="round"/>
                      <path d="M28 31L32.7735 26.2265C33.4772 25.5228 34.5914 25.4436 35.3877 26.0408L42 31" stroke="currentColor" strokeWidth="4" strokeLinecap="round" strokeLinejoin="round"/>
                      <path d="M37 18L37 6" stroke="currentColor" strokeWidth="4" strokeLinecap="round" strokeLinejoin="round"/>
                      <path d="M32 11L37 6L42 11" stroke="currentColor" strokeWidth="4" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </div>
                  <div className="p-4 bg-gray-50">
                    <div className="h-4 bg-gray-200 rounded animate-pulse mb-3"></div>
                    <div className="flex justify-between">
                      <div className="h-3 bg-gray-200 rounded animate-pulse w-16"></div>
                      <div className="h-3 bg-gray-200 rounded animate-pulse w-12"></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <motion.div
              className="grid grid-cols-1 md:grid-cols-3 gap-6"
              layout
              transition={{ duration: 0.5, ease: "easeInOut" }}
            >
              {getDisplayedOfficialCourses().map((course, index) => {
                const isNewlyVisible = showAllOfficialCourses && index >= 6;
                return (
                  <motion.div
                    key={course.id}
                    className="bg-white rounded-lg border border-blue-200 shadow-sm overflow-hidden hover:shadow-md transition-shadow duration-200 cursor-pointer"
                    onClick={() => handleCourseClick(course)}
                    initial={{
                      opacity: isNewlyVisible ? 0 : 1,
                      y: isNewlyVisible ? 30 : 0,
                      scale: isNewlyVisible ? 0.9 : 1
                    }}
                    animate={{ opacity: 1, y: 0, scale: 1 }}
                    transition={{
                      duration: isNewlyVisible ? 0.6 : 0.4,
                      delay: isNewlyVisible ? (index - 6) * 0.15 + 0.2 : index * 0.08,
                      ease: "easeOut",
                      type: "spring",
                      stiffness: 100,
                      damping: 15
                    }}
                    whileHover={{
                      y: -8,
                      scale: 1.03,
                      boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
                      transition: { duration: 0.3, ease: "easeOut" }
                    }}
                    layout
                  >
                  {/* 课程封面图片 */}
                  <div className={`h-36 relative ${!course.coverImage ? 'bg-gradient-to-br from-blue-100 to-blue-200' : ''}`}>
                    {course.coverImage ? (
                      <Image
                        src={course.coverImage}
                        alt={course.title}
                        fill
                        className="object-cover"
                        onLoad={() => {
                          console.log(`✅ 封面图片加载成功: "${course.title}" - ${course.coverImage}`);
                        }}
                        onError={(e) => {
                          // 图片加载失败时隐藏图片，显示默认图标
                          console.log(`❌ 封面图片加载失败: "${course.title}" - ${course.coverImage}`);
                          const target = e.target as HTMLImageElement;
                          target.style.display = 'none';
                          // 添加浅蓝色背景
                          const parent = target.parentElement;
                          if (parent) {
                            parent.className = parent.className + ' bg-gradient-to-br from-blue-100 to-blue-200';
                          }
                        }}
                      />
                    ) : (
                      // 当没有封面图片时显示的图标
                      <div className="w-full h-full flex items-center justify-center">
                        <svg className="w-12 h-12 text-blue-600 opacity-70" fill="none" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
                          <path d="M44 24C44 22.8954 43.1046 22 42 22C40.8954 22 40 22.8954 40 24H44ZM24 8C25.1046 8 26 7.10457 26 6C26 4.89543 25.1046 4 24 4V8ZM39 40H9V44H39V40ZM8 39V9H4V39H8ZM40 24V39H44V24H40ZM9 8H24V4H9V8ZM9 40C8.44772 40 8 39.5523 8 39H4C4 41.7614 6.23857 44 9 44V40ZM39 44C41.7614 44 44 41.7614 44 39H40C40 39.5523 39.5523 40 39 40V44ZM8 9C8 8.44772 8.44771 8 9 8V4C6.23858 4 4 6.23857 4 9H8Z" fill="currentColor"/>
                          <path d="M6 35L16.6931 25.198C17.4389 24.5143 18.5779 24.4953 19.3461 25.1538L32 36" stroke="currentColor" strokeWidth="4" strokeLinecap="round" strokeLinejoin="round"/>
                          <path d="M28 31L32.7735 26.2265C33.4772 25.5228 34.5914 25.4436 35.3877 26.0408L42 31" stroke="currentColor" strokeWidth="4" strokeLinecap="round" strokeLinejoin="round"/>
                          <path d="M37 18L37 6" stroke="currentColor" strokeWidth="4" strokeLinecap="round" strokeLinejoin="round"/>
                          <path d="M32 11L37 6L42 11" stroke="currentColor" strokeWidth="4" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                      </div>
                    )}
                  </div>

                  {/* 课程信息 */}
                  <div className="p-4 bg-gray-50">
                    <h3 className="font-medium text-gray-800 mb-3 text-base line-clamp-2">{course.title}</h3>
                    <div className="flex items-center justify-between text-sm text-gray-600">
                      <span className="text-gray-500">{course.lessons}</span>
                      <div className="flex items-center gap-1.5">
                        <svg className="w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
                          <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd"/>
                        </svg>
                        <span className="text-gray-600 font-medium">{course.views}</span>
                      </div>
                    </div>
                  </div>
                </motion.div>
                );
              })}
            </motion.div>
          )}
        </motion.div>

        {/* 课程社区部分 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="mb-12"
        >
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-gray-900">课程社区</h2>
            {(() => {
              console.log('🔍 社区按钮显示条件检查:', {
                loading,
                communityCoursesLength: communityCourses.length,
                shouldShowButton: !loading && communityCourses.length > 6
              });
              return !loading && communityCourses.length > 6;
            })() && (
              <button
                onClick={handleShowMoreCommunityCourses}
                disabled={loadingMoreCommunity}
                className="group relative flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-green-500 to-green-600 text-white font-medium rounded-full shadow-lg hover:shadow-xl hover:from-green-600 hover:to-green-700 transform hover:scale-105 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none disabled:shadow-lg"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-green-400 to-green-500 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div className="relative flex items-center gap-2">
                  {loadingMoreCommunity ? (
                    <>
                      <svg className="w-5 h-5 animate-spin" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      <span className="text-sm">加载中...</span>
                    </>
                  ) : (
                    <>
                      <span className="text-sm font-semibold">{showAllCommunityCourses ? '收起' : '查看更多'}</span>
                      <svg
                        className={`w-5 h-5 transition-all duration-300 ${showAllCommunityCourses ? 'rotate-180 scale-110' : 'group-hover:translate-y-0.5'}`}
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M19 9l-7 7-7-7" />
                      </svg>
                    </>
                  )}
                </div>
              </button>
            )}
          </div>
          {loading ? (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {[1, 2, 3].map((i) => (
                <div key={i} className="bg-white rounded-lg border border-blue-200 shadow-sm overflow-hidden animate-pulse">
                  <div className="h-36 bg-gradient-to-br from-blue-100 to-blue-200 flex items-center justify-center">
                    <svg className="w-12 h-12 text-blue-600 opacity-50" fill="none" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
                      <path d="M44 24C44 22.8954 43.1046 22 42 22C40.8954 22 40 22.8954 40 24H44ZM24 8C25.1046 8 26 7.10457 26 6C26 4.89543 25.1046 4 24 4V8ZM39 40H9V44H39V40ZM8 39V9H4V39H8ZM40 24V39H44V24H40ZM9 8H24V4H9V8ZM9 40C8.44772 40 8 39.5523 8 39H4C4 41.7614 6.23857 44 9 44V40ZM39 44C41.7614 44 44 41.7614 44 39H40C40 39.5523 39.5523 40 39 40V44ZM8 9C8 8.44772 8.44771 8 9 8V4C6.23858 4 4 6.23857 4 9H8Z" fill="currentColor"/>
                      <path d="M6 35L16.6931 25.198C17.4389 24.5143 18.5779 24.4953 19.3461 25.1538L32 36" stroke="currentColor" strokeWidth="4" strokeLinecap="round" strokeLinejoin="round"/>
                      <path d="M28 31L32.7735 26.2265C33.4772 25.5228 34.5914 25.4436 35.3877 26.0408L42 31" stroke="currentColor" strokeWidth="4" strokeLinecap="round" strokeLinejoin="round"/>
                      <path d="M37 18L37 6" stroke="currentColor" strokeWidth="4" strokeLinecap="round" strokeLinejoin="round"/>
                      <path d="M32 11L37 6L42 11" stroke="currentColor" strokeWidth="4" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </div>
                  <div className="p-4 bg-gray-50">
                    <div className="h-4 bg-gray-200 rounded mb-3"></div>
                    <div className="flex justify-between">
                      <div className="h-3 bg-gray-200 rounded w-16"></div>
                      <div className="h-3 bg-gray-200 rounded w-8"></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : communityCourses.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-gray-400 text-lg mb-2">暂无社区课程</div>
              <div className="text-gray-500 text-sm">社区课程正在建设中...</div>
            </div>
          ) : (
            <motion.div
              className="grid grid-cols-1 md:grid-cols-3 gap-6"
              layout
              transition={{ duration: 0.5, ease: "easeInOut" }}
            >
              {getDisplayedCommunityCourses().map((course, index) => {
                const isNewlyVisible = showAllCommunityCourses && index >= 6;
                return (
                  <motion.div
                    key={course.id}
                    className="bg-white rounded-lg border border-blue-200 shadow-sm overflow-hidden hover:shadow-md transition-shadow duration-200 cursor-pointer"
                    onClick={() => handleCourseClick(course)}
                    initial={{
                      opacity: isNewlyVisible ? 0 : 1,
                      y: isNewlyVisible ? 30 : 0,
                      scale: isNewlyVisible ? 0.9 : 1
                    }}
                    animate={{ opacity: 1, y: 0, scale: 1 }}
                    transition={{
                      duration: isNewlyVisible ? 0.6 : 0.4,
                      delay: isNewlyVisible ? (index - 6) * 0.15 + 0.2 : index * 0.08,
                      ease: "easeOut",
                      type: "spring",
                      stiffness: 100,
                      damping: 15
                    }}
                    whileHover={{
                      y: -8,
                      scale: 1.03,
                      boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
                      transition: { duration: 0.3, ease: "easeOut" }
                    }}
                    layout
                  >
                  {/* 课程封面图片 */}
                  <div className={`h-36 relative ${!course.coverImage ? 'bg-gradient-to-br from-blue-100 to-blue-200' : ''}`}>
                    {course.coverImage ? (
                      <Image
                        src={course.coverImage}
                        alt={course.title}
                        fill
                        className="object-cover"
                        onLoad={() => {
                          console.log(`✅ 封面图片加载成功: "${course.title}" - ${course.coverImage}`);
                        }}
                        onError={(e) => {
                          // 图片加载失败时隐藏图片，显示默认图标
                          console.log(`❌ 封面图片加载失败: "${course.title}" - ${course.coverImage}`);
                          const target = e.target as HTMLImageElement;
                          target.style.display = 'none';
                          // 添加浅蓝色背景
                          const parent = target.parentElement;
                          if (parent) {
                            parent.className = parent.className + ' bg-gradient-to-br from-blue-100 to-blue-200';
                          }
                        }}
                      />
                    ) : (
                      // 当没有封面图片时显示的图标
                      <div className="w-full h-full flex items-center justify-center">
                        <svg className="w-12 h-12 text-blue-600 opacity-70" fill="none" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
                          <path d="M44 24C44 22.8954 43.1046 22 42 22C40.8954 22 40 22.8954 40 24H44ZM24 8C25.1046 8 26 7.10457 26 6C26 4.89543 25.1046 4 24 4V8ZM39 40H9V44H39V40ZM8 39V9H4V39H8ZM40 24V39H44V24H40ZM9 8H24V4H9V8ZM9 40C8.44772 40 8 39.5523 8 39H4C4 41.7614 6.23857 44 9 44V40ZM39 44C41.7614 44 44 41.7614 44 39H40C40 39.5523 39.5523 40 39 40V44ZM8 9C8 8.44772 8.44771 8 9 8V4C6.23858 4 4 6.23857 4 9H8Z" fill="currentColor"/>
                          <path d="M6 35L16.6931 25.198C17.4389 24.5143 18.5779 24.4953 19.3461 25.1538L32 36" stroke="currentColor" strokeWidth="4" strokeLinecap="round" strokeLinejoin="round"/>
                          <path d="M28 31L32.7735 26.2265C33.4772 25.5228 34.5914 25.4436 35.3877 26.0408L42 31" stroke="currentColor" strokeWidth="4" strokeLinecap="round" strokeLinejoin="round"/>
                          <path d="M37 18L37 6" stroke="currentColor" strokeWidth="4" strokeLinecap="round" strokeLinejoin="round"/>
                          <path d="M32 11L37 6L42 11" stroke="currentColor" strokeWidth="4" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                      </div>
                    )}
                  </div>

                  {/* 课程信息 */}
                  <div className="p-4 bg-gray-50">
                    <h3 className="font-medium text-gray-800 mb-3 text-base line-clamp-2">{course.title}</h3>
                    <div className="flex items-center justify-between text-sm text-gray-600">
                      <span className="text-gray-500">{course.lessons}</span>
                      <div className="flex items-center gap-1.5">
                        <svg className="w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
                          <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd"/>
                        </svg>
                        <span className="text-gray-600 font-medium">{course.views}</span>
                      </div>
                    </div>
                  </div>
                </motion.div>
                );
              })}
            </motion.div>
          )}
        </motion.div>
            </div>
          </div>
        </>
      )}
    </div>
  )
}
