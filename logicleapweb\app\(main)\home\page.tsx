'use client'

import { useState, useEffect, useRef, useMemo, useCallback } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { ArrowRight, Lightbulb, Book, Compass, Eye, ImageIcon, Code, Video, Music, MessageSquare, Heart, Share2, X, Clock } from 'lucide-react'
import { motion, useScroll, useTransform, AnimatePresence } from 'framer-motion'
import AIBackground from '@/components/AIBackground'
import Animated<PERSON>ogo from '@/components/AnimatedLogo'
import { useSelector } from 'react-redux'
import { RootState } from '@/lib/store'
import LoginDialog from '@/components/login-dialog'
import Slider from 'react-slick'
import type { Settings } from 'react-slick'
import 'slick-carousel/slick/slick.css'
import 'slick-carousel/slick/slick-theme.css'
import { worksApi } from '@/lib/api/works'
import { userApi } from '@/lib/api/user'
import { usePathname, useRouter } from 'next/navigation'
import { Spin } from 'antd'
import { createScratchUrl } from '@/lib/navigation'
import { carouselApi } from '@/lib/api/carousel'
import { CarouselItemDisplay } from '@/lib/api/carousel'
import { Tag } from 'antd';
import ReactMarkdown from 'react-markdown'
import rehypeRaw from 'rehype-raw'
import { TaskStatus } from '@/lib/api/task';
import task from '@/lib/api/task';
import { Modal } from 'antd';
import AnnouncementPop from './components/announcement-pop';
import MessageCenter from './components/message-center'
import HomeProvider from './HomeProvider'
import ReportDialog from './components/report-dialog'
import { viewWork } from '@/lib/utils/view-work-modal'
import { ImagePreviewModal } from './components/image-preview-modal';
import ShowcaseSidebar from './components/showcase-sidebar';
import ShowcaseContent from './components/showcase-content';
import { map } from 'rxjs/operators';
import { GetNotification } from 'logic-common/dist/components/Notification';
import {
  ShowType,
  GalleryImage,
  WorkItem,
  SearchImageResult,
  ImageData,
  SearchResult,
  DEFAULT_AVATAR,
  DEFAULT_IMAGE
} from './types';


// 修改 showType 的类型定义，包含所有可能的状态
// type ShowType = 
//   | 'scratch-work' 
//   | 'scratch-image' 
//   | 'node-work' 
//   | 'node-extension' 
//   | 'ai-work';

// 添加 WorkItem 接口定义
// interface WorkItem {

export default function Home() {
  // 将所有状态声明移到顶部
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<SearchResult | null>(null);
  const [isSearching, setIsSearching] = useState(false);

  const { scrollYProgress } = useScroll()
  const router = useRouter()
  const backgroundY = useTransform(scrollYProgress, [0, 1], ['0%', '20%'])
  const [displayText, setDisplayText] = useState("探索AI的无限可能")
  const [showCursor, setShowCursor] = useState(true)
  const [isTypingComplete, setIsTypingComplete] = useState(false)
  const [likedWorks, setLikedWorks] = useState<{ [key: number]: boolean }>({});
  const pathname = usePathname()
  // 添加打字机效果的useEffect
  useEffect(() => {
    const text = "探索AI的无限可能";
    let currentIndex = 0;
    setIsTypingComplete(false);

    const typingInterval = setInterval(() => {
      if (currentIndex <= text.length) {
        setDisplayText(text.slice(0, currentIndex));
        currentIndex++;
      } else {
        setIsTypingComplete(true);
        setShowCursor(false); // 文字完成后隐藏光标
        clearInterval(typingInterval);
      }
    }, 150);
    return () => clearInterval(typingInterval);
  }, []);

  const fadeInUpVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        ease: "easeOut"
      }
    }
  }

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3
      }
    }
  }

  const cardVariants = {
    hidden: { opacity: 0, scale: 0.8, y: 20 },
    visible: {
      opacity: 1,
      scale: 1,
      y: 0,
      transition: {
        duration: 0.5,
        ease: "easeOut"
      }
    }
  }

  const cardClassName = `
    bg-white/70 backdrop-blur-md
    border border-white/20
    shadow-[0_8px_30px_rgb(0,0,0,0.12)]
    hover:shadow-[0_8px_30px_rgb(71,102,194,0.2)]
    transition-all duration-300 
    rounded-2xl p-8
  `

  const specialCardClassName = `
    bg-gradient-to-br from-white/80 to-blue-50/80 backdrop-blur-md
    border border-white/20
    shadow-[0_8px_30px_rgb(0,0,0,0.12)]
    hover:shadow-[0_8px_30px_rgb(71,102,194,0.2)]
    transition-all duration-300 
    rounded-2xl p-8
  `

  const [showLoginDialog, setShowLoginDialog] = useState(false)
  const userState = useSelector((state: RootState) => state.user.userState)
  // 备选方案，若此时redux未加载则采用本地存储的userInfo
  const userId = userState.userId || (() => {
    const userData = localStorage.getItem('user');
    if (userData) {
      try {
        const parsedUser = JSON.parse(userData);
        return parsedUser.id || parsedUser.userId;
      } catch (e) {
        console.error('解析用户信息失败:', e);
      }
    }
    return null;
  })();

  useEffect(() => {
    // console.log('userState changed:', userState)
  }, [userState])

  const handleStartCreate = (e?: React.MouseEvent, toolName: string = "图形化编程") => {
    if (e) {
      e.preventDefault()
    }

    if (!userState.isLoggedIn) {
      const redirectUrl = pathname;
      router.push(`/login?redirect=${encodeURIComponent(redirectUrl)}`);
      return
    }

    if (toolName === "图形化编程") {
      // 获取完整的用户状态
      const userData = localStorage.getItem('user');
      const fullUserState = userData ? JSON.parse(userData) : null;

      // 使用 viewWork 打开新窗口
      viewWork({
        content: '', // 新建作品，content 为空
        workId: undefined, // 不设置 workId 表示新建
        userId: userState.userId, // 传入当前用户 ID
        isNew: true, // 标记为新建作品
        userState: fullUserState || userState // 传入完整的用户状态
      });
    } else {
      const notification = GetNotification();
      notification.info('正在开发中，敬请期待');
    }
  }

  // 1. 先定义状态
  const [carouselItems, setCarouselItems] = useState<CarouselItemDisplay[]>([])
  const [playingVideos, setPlayingVideos] = useState<{ [key: number]: boolean }>({})

  // 自定义箭头组件
  const NextArrow = (props: any) => {
    const { className, onClick } = props;
    return (
      <div className={`${className} slick-next`} onClick={onClick}>
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
          <path d="M9 18l6-6-6-6" />
        </svg>
      </div>
    );
  };

  const PrevArrow = (props: any) => {
    const { className, onClick } = props;
    return (
      <div className={`${className} slick-prev`} onClick={onClick}>
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
          <path d="M15 18l-6-6 6-6" />
        </svg>
      </div>
    );
  };

  // 2. 然后定义 sliderSettings
  const sliderSettings = useMemo(() => ({
    dots: true,
    infinite: carouselItems.length > 1,
    speed: 500,
    slidesToShow: 1,
    slidesToScroll: 1,
    autoplay: carouselItems.length > 1,
    autoplaySpeed: 3000,
    arrows: carouselItems.length > 1,
    nextArrow: <NextArrow />,
    prevArrow: <PrevArrow />,
  }), [carouselItems.length])

  // 3. 数据获取函数
  const fetchCarouselData = async () => {
    try {
      console.log('获取轮播图数据...');

      const { data: response } = await carouselApi.getActiveList()
      if (response.code === 200) {
        console.log(response.data);

        setCarouselItems(response.data.map(item => ({
          id: item.id,
          title: item.largeTitle,
          subtitle: item.mediumTitle,
          description: item.smallTitle,
          image: item.imageUrl,
          video: item.videoUrl,
          redirectUrl: item.redirectUrl,
          buttonText: item.buttonText,
          buttonStatus: item.buttonStatus,
          type: item.type,
          displayType: item.displayType
        })))
      }
    } catch (error) {
      console.error('获取轮播图数据失败:', error)
    }
  }

  // 4. 初始化数据
  useEffect(() => {

    fetchCarouselData()
  }, [])

  const [isLogoAnimationComplete, setIsLogoAnimationComplete] = useState(false)

  const introContent = {
    title: "创新AI教育台",
    description: "我们致力于为年提供最好的AI学习环境，通过实践和创新，培养未来科技人才。",
    features: [
      "图形化编程界面，零基础也能快速上手",
      "丰富的教学资源和项目案例",
      "专业的指导和活跃的学习社区"
    ]
  }
  const [galleryImages, setGalleryImages] = useState<any[]>([])
  const [activeTag, setActiveTag] = useState('热门')
  const [filterTitle, setFilterTitle] = useState('图片广场')

  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const loadingRef = useRef(null);

  // 修改监听标签变化的 useEffect
  useEffect(() => {
    // 只有在非搜索结果标签时才执行
    if (activeTag !== '搜索结果') {
      // 清空当前列表并重新加载
      setGalleryImages([]);
      setPage(1);
      setHasMore(true);

      // 根据当前选中的标签重新获取数据
      fetchGalleryImages(1);
    }
  }, [activeTag]); // 当 activeTag 改变时重新获取数据

  // 修改 fetchGalleryImages 函数
  const fetchGalleryImages = async (pageNum = 1) => {
    try {
      setIsLoading(true);
      const { data: response } = await worksApi.getAllWorks({
        type: 2,
        orderBy: activeTag === '热门' ? 'popular' :
          activeTag === '最新' ? 'newest' :
            'recommend',
        status: 1,
        page: pageNum,
        size: 10
      });

      if (response.code === 200) {
        const formattedImages = response.data.list.map((item: SearchImageResult) => {
          // 确保author字段正确处理
          let authorData = {
            nickName: '未知用户',
            avatarUrl: DEFAULT_AVATAR
          };

          // 尝试从不同来源获取作者信息
          if (item.author_nickName || item.author_avatarUrl) {
            authorData = {
              nickName: item.author_nickName || '未知用户',
              avatarUrl: item.author_avatarUrl || DEFAULT_AVATAR
            };
          } else if (item.author) {
            authorData = {
              nickName: item.author.nickName || '未知用户',
              avatarUrl: item.author.avatarUrl || DEFAULT_AVATAR
            };
          }

          return {
            id: item.id,
            imageUrl: item.backupImagePath || item.imageUrl || '/images/image-placeholder.jpg',
            src: item.backupImagePath || item.imageUrl || '/images/image-placeholder.jpg',
            title: item.prompt || item.title || '未命名图片',
            description: item.prompt || item.description || '暂无描述',
            prompt: item.prompt,
            views: item.viewCount || 0,
            likeCount: item.likeCount || 0,
            createTime: item.uploadTime || item.createTime,
            author: authorData
          };
        });

        if (pageNum === 1) {
          setGalleryImages(formattedImages);
        } else {
          setGalleryImages(prev => [...prev, ...formattedImages]);
        }

        setHasMore(formattedImages.length === 10);
        setPage(pageNum);
      }
    } catch (error) {
      console.error('获取图片列表失败:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchGalleryImages()
  }, [])
  // 加对展示区域位置的引用
  const showcaseRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const updateSidebarPosition = () => {
      if (showcaseRef.current) {
        const showcaseTop = showcaseRef.current.offsetTop;
        document.documentElement.style.setProperty('--showcase-start', `${showcaseTop}px`);
      }
    };

    updateSidebarPosition();
    window.addEventListener('resize', updateSidebarPosition);

    return () => window.removeEventListener('resize', updateSidebarPosition);
  }, []);



  // 添加 setShowMessage 状态
  const [showMessage, setShowMessage] = useState(false)

  const creationToolsRef = useRef<HTMLDivElement>(null)

  // 添加滚动函数
  const scrollToCreationTools = () => {
    creationToolsRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  // 添加路由参数处理
  useEffect(() => {
    // 给页面一点时间加载
    const timer = setTimeout(() => {
      const params = new URLSearchParams(window.location.search)
      const section = params.get('section')

      if (section === 'creation') {
        const creationToolsSection = document.querySelector('.creation-tools-section')
        if (creationToolsSection) {
          const navbarHeight = 56
          const elementPosition = creationToolsSection.getBoundingClientRect().top + window.scrollY
          const offsetPosition = elementPosition - navbarHeight - 20

          window.scrollTo({
            top: offsetPosition,
            behavior: 'smooth'
          })
        }
      } else if (section === 'gallery') {
        const gallerySection = document.querySelector('.showcase-content')
        if (gallerySection) {
          const navbarHeight = 56
          const elementPosition = gallerySection.getBoundingClientRect().top + window.scrollY
          const offsetPosition = elementPosition - navbarHeight - 20

          window.scrollTo({
            top: offsetPosition,
            behavior: 'smooth'
          })
        }
      }
    }, 100) // 100ms 延迟

    return () => clearTimeout(timer)
  }, []) // 仅在组件挂载时执行一次

  // 添加状态来控制当前展示类型
  const [showType, setShowType] = useState<ShowType>('scratch-work')
  const [worksList, setWorksList] = useState<WorkItem[]>([]);

  const fetchLikeStatus = async () => {
    if (!userState.isLoggedIn) return;

    try {
      // const [worksResponse, imagesResponse] = await Promise.all([
      //   worksApi.getLikeStatus(
      //     worksList.map(work => work.id),
      //     1
      //   ),
      //   worksApi.getLikeStatus(
      //     galleryImages.map(img => img.id),
      //     2
      //   )
      // ]);
      // console.log(worksResponse);
      // console.log(imagesResponse);
      // 合并作品和图片的点赞状态
      // if (worksResponse?.data?.code === 200 && imagesResponse?.data?.code === 200) {
      //   setLikedWorks(prev => ({
      //     ...prev,
      //     ...worksResponse.data,
      //     ...imagesResponse.data
      //   }));

      // }
      const response = await worksApi.getUserLikes(userId); // 请求 /user-work-like/user/{userId}
      const likedList = response.data.data; // 假设后端返回的是点赞记录数组
      const likedMap = Object.fromEntries(likedList.map((i: { targetId: number }) => [i.targetId, true]));

      // 合并作品和图片的点赞状态
      if (response?.data?.code === 200) {
        setLikedWorks(likedMap);


      }


    } catch (error) {
      console.error('获取点赞状态失败:', error);
    }
  };

  // 修改 useEffect
  useEffect(() => {
    if (worksList.length === 0 && galleryImages.length === 0) return;
    fetchLikeStatus();
  }, [worksList, galleryImages]); // 当作品列表或图片列表更新时重新获取点赞状态


  // 添加获取作品列表的函数
  const fetchWorks = async (pageNum = 1) => {
    console.log('fetchWorks');

    try {
      setIsLoading(true);
      const { data: response } = await worksApi.getAllWorks({
        type: 1,
        orderBy: filters.orderBy,
        status: 1,
        page: pageNum,
        size: 10
      });


      if (response.code === 200) {
        const formattedWorks = response.data.list.map((item: WorkItem) => {
          const formattedWork = {
            id: item.id,
            title: item.title || '未命名作品',
            description: item.description || '暂无描述',
            coverImage: item.coverImage || null,
            screenShotImage: item.screenShotImage,
            type: item.type,
            status: item.status,
            createTime: item.createTime,
            viewCount: item.viewCount || 0,
            likeCount: item.likeCount || 0,
            userId: item.userId,
            originalWorkId: item.originalWorkId,
            originalAuthorId: item.originalAuthorId,
            isDerivative: item.isDerivative,
            originalAuthor: item.originalAuthor ? {
              nickName: item.originalAuthor.nickName,
              avatarUrl: item.originalAuthor.avatarUrl
            } : undefined,
            originalWork: item.originalWork ? {
              title: item.originalWork.title,
              content: item.originalWork.content,
              coverImage: item.originalWork.coverImage,
              screenShotImage: item.originalWork.screenShotImage
            } : undefined,
            author: {
              nickName: item.author?.nickName || '匿名用户',
              avatarUrl: item.author?.avatarUrl || DEFAULT_AVATAR
            }
          };

          return formattedWork;
        });

        if (pageNum === 1) {
          setWorksList(formattedWorks);
        } else {
          setWorksList(prev => [...prev, ...formattedWorks]);
        }

        setHasMore(formattedWorks.length === 10);
        setPage(pageNum);
      }
    } catch (error) {
      console.error('获取作品列表失败:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // 修改 Intersection Observer 的实现
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && hasMore && !isLoading) {
          // 添加防抖，避免频繁触发
          clearTimeout(timeoutId);
          timeoutId = setTimeout(() => {
            if (showType === 'scratch-image') {
              fetchGalleryImages(page + 1);
            } else if (showType === 'scratch-work') {
              fetchWorks(page + 1);
            }
          }, 300);
        }
      },
      {
        root: null,
        rootMargin: '200px', // 增加预加载距离
        threshold: 0.1
      }
    );

    const currentRef = loadingRef.current;
    if (currentRef) {
      observer.observe(currentRef);
    }

    return () => {
      clearTimeout(timeoutId);
      if (currentRef) {
        observer.unobserve(currentRef);
      }
      observer.disconnect();
    };
  }, [hasMore, isLoading, page, showType, fetchGalleryImages, fetchWorks]);

  // 修改切换类型的处理函数
  const handleSidebarItemClick = (type: ShowType) => {
    if (type === 'node-work' || type === 'node-extension' || type === 'ai-work') {
      setShowMessage(true);
      return;
    }

    // 清空搜索相关状态
    setSearchQuery('');  // 清空搜索框
    setSearchResults(null);  // 清空搜索结果

    // 先清空当前数据和状态
    setWorksList([]);
    setGalleryImages([]);
    setShowType(type);
    setPage(1);
    setHasMore(true);
    setIsLoading(false);

    // 重置标签为热门
    setActiveTag('热门');

    // 重置滚动位置
    const showcaseContent = document.querySelector('.showcase-content');
    if (showcaseContent) {
      showcaseContent.scrollTop = 0;
    }

    // 然后加载新数据
    if (type === 'scratch-work') {
      setFilterTitle('作品广场');
      fetchWorks(1);
    } else if (type === 'scratch-image') {
      setFilterTitle('图片广场');
      fetchGalleryImages(1);
    }
  };

  // 添加 useEffect 监听 likedWorks 变化
  useEffect(() => {
    // console.log('likedWorks 更新了:', likedWorks);
  }, [likedWorks]);

  // 根据类型显示不同的标签
  const imageTags = [
    { id: 2, label: '热门', icon: '🔥' },
    { id: 1, label: '推荐', icon: '🏆' },
    { id: 3, label: '最新', icon: '⭐' },
    { id: 4, label: '搜索结果', icon: '🔍', hidden: true }  // 添加搜索标签，默认隐藏
  ]

  const workTags = [
    { id: 2, label: '热门', icon: '🔥' },
    { id: 1, label: '推荐', icon: '🏆' },
    { id: 3, label: '最新', icon: '⭐' },
    { id: 4, label: '搜索结果', icon: '🔍', hidden: true }  // 添加搜索标签，默认隐藏
  ]

  // 在 useEffect 中初始加载作品列表
  useEffect(() => {
    // 初始化时加载作品列表
    fetchWorks()
    // 设置初始标签和标签
    setFilterTitle('作品广场')
    setActiveTag('热门')
  }, []) // 仅在组件挂载时执行一次

  // 添加作品点击处理函数
  const handleWorkClick = async (work: WorkItem) => {
    console.log('work', work);
    
    // 先设置基本信息
    setSelectedWork(work);
    
    // 如果有原作品ID，尝试获取原作品信息
    if (work.originalWorkId) {
      try {
        const response = await worksApi.getDetail(work.originalWorkId);
        console.log('response', response);
        
        // 如果有原作者ID，尝试获取原作者信息
        let originalAuthorInfo = null;
        if (work.originalAuthorId) {
          try {
            const authorResponse = await userApi.getUserInfo(work.originalAuthorId);
            console.log('authorResponse', authorResponse);
            if (authorResponse?.code === 200) {
              originalAuthorInfo = authorResponse.data;
            }
          } catch (error) {
            console.error('获取原作者信息失败:', error);
          }
        }
        
        if (response?.data.code === 200 && response?.data.data) {
          // 更新原作品信息和原作者信息
          setSelectedWork(prev => {
            if (prev) {
              return {
                ...prev,
                originalWork: response.data.data,
                originalAuthor: originalAuthorInfo ? {
                  nickName: originalAuthorInfo.nickName || '未知用户',
                  avatarUrl: originalAuthorInfo.avatarUrl || DEFAULT_AVATAR
                } : prev.originalAuthor
              };
            }
            return prev;
          });
        }
      } catch (error) {
        console.error('获取原作品详情失败:', error);
      }
    }
  }

  // 修改 handleEnterWork 函数
  const handleEnterWork = async (work: WorkItem) => {
    const notification = GetNotification();
    
    if (!userState.isLoggedIn) {
      const redirectUrl = pathname;
      router.push(`/login?redirect=${encodeURIComponent(redirectUrl)}`);
      return
    }
    if (work.type === 1) {
      try {
        const response = await worksApi.getDetail(work.id)
        if (response?.data.code === 200) {
          const content = response?.data.data.content
          if (!content) {
            console.error('作品内容为空')
            notification.error('作品内容获取失败')
            return
          }

          // 使用 viewWork 组件打开作品
          viewWork({
            content: content,
            workId: work.id,
            userId: work.userId
          });
        }
      } catch (error) {
        console.error('获取作品详情失败:', error)
        notification.error('请先登录')
      }
    } else {
      router.push(`/work/${work.id}`)
    }
  }

  // 添加这个 useEffect 来处理页面刷新时滚动到顶部
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  // 首先添加新的状态来控制弹窗
  const [selectedWork, setSelectedWork] = useState<WorkItem | null>(null)

  // 视频播放控制函数
  const handleVideoPlay = (index: number) => {
    setPlayingVideos(prev => ({
      ...prev,
      [index]: true
    }));
  };

  const [showPolicy, setShowPolicy] = useState<'privacy' | 'service' | 'children' | null>(null);
  const [policyContent, setPolicyContent] = useState('');

  // 添加加载协议内容的函数
  const loadPolicyContent = async (type: 'privacy' | 'service' | 'children') => {
    try {
      const policyPaths = {
        privacy: '/policies/privacy-policy.md',
        service: '/policies/service-agreement.md',
        children: '/policies/children-privacy-policy.md'
      };

      const response = await fetch(policyPaths[type]);
      const content = await response.text();
      setPolicyContent(content);
      setShowPolicy(type);
    } catch (err) {
      console.error('Failed to load policy:', err);
    }
  };

  // 更新 PolicyDialog 组件
  const PolicyDialog = () => (
    <div className="fixed inset-0 bg-black/45 backdrop-blur-sm flex items-center justify-center z-[60]">
      <div className="bg-white rounded-[20px] w-[800px] max-h-[90vh] flex flex-col">
        <div className="px-8 py-6 border-b border-gray-200 flex justify-between items-center">
          <h3 className="text-lg font-medium text-[#1D2129]">
            {showPolicy === 'privacy' && '个人信息保护政策'}
            {showPolicy === 'service' && '平台服务协议'}
            {showPolicy === 'children' && '儿童个人信息保护规则及监护人须知'}
          </h3>
          <button
            onClick={() => setShowPolicy(null)}
            className="text-gray-400 hover:text-gray-600"
          >
            <X size={20} />
          </button>
        </div>

        <div className="flex-1 overflow-y-auto px-8 py-6">
          <div className="terms-content text-[14px] leading-relaxed text-[#1D2129]">
            {/* 生效日期 */}
            <div className="mb-6">
              <div className="font-normal text-[#1D2129]">生效日期</div>
              <div className="mt-2 text-[#1D2129]">【2025/1/20】</div>
            </div>

            {/* 签约提示 */}
            <div className="mb-6">
              <div className="font-medium text-[#1D2129]">【签约提示】</div>
              <div
                className="mt-2 text-[#1D2129] space-y-4"
                dangerouslySetInnerHTML={{
                  __html: formatContent(policyContent.split('【协议正文】')[0].replace('【签约提示】', ''))
                }}
              />
            </div>

            {/* 协议正文 */}
            <div>
              <div className="font-medium mb-4 text-[#1D2129]">【协议正文】</div>
              <div
                className="text-[#1D2129] space-y-4"
                dangerouslySetInnerHTML={{
                  __html: formatContent(policyContent.split('【协议正文】')[1] || '')
                }}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  useEffect(() => {
    const handleScroll = (e: Event) => {
      if (showPolicy) {
        e.preventDefault();
      }
    };

    if (showPolicy) {
      document.body.style.overflow = 'hidden';
      // 防止移动端滚动
      document.addEventListener('touchmove', handleScroll, { passive: false });
    }

    return () => {
      document.body.style.overflow = 'unset';
      document.removeEventListener('touchmove', handleScroll);
    };
  }, [showPolicy]);

  // 添加新的状态
  const [showWechat, setShowWechat] = useState(false);
  const [isJoinUsModalOpen, setIsJoinUsModalOpen] = useState(false); // <-- 1. 添加新状态 
  const [isCharacterHovered, setIsCharacterHovered] = useState(false); // <-- 1. 添加悬停状态 

  // 添加微信二维码弹窗组件
  const WechatDialog = () => (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-[60]">
      <div className="bg-white rounded-2xl shadow-2xl relative w-[400px] flex flex-col">
        <div className="p-6 border-b border-gray-100 sticky top-0 bg-white rounded-t-2xl z-10 flex items-center justify-between">
          <h2 className="text-xl font-bold">
            关注公众号
          </h2>
          <button
            onClick={() => setShowWechat(false)}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X size={20} />
          </button>
        </div>

        <div className="p-8 flex flex-col items-center">
          <div className="relative w-[200px] h-[200px] mb-4">
            <Image
              src="/images/WeChat.jpg"
              alt="微信公众号二维码"
              fill
              sizes="200px"
              className="object-contain"
            />
          </div>
          <p className="text-gray-600 text-center">
            请扫码关注公众号
          </p>
        </div>
      </div>
    </div>
  );

  // 添加滚动控制
  useEffect(() => {
    const handleScroll = (e: Event) => {
      if (showWechat) {
        e.preventDefault();
      }
    };

    if (showWechat) {
      document.body.style.overflow = 'hidden';
      document.addEventListener('touchmove', handleScroll, { passive: false });
    }

    return () => {
      document.body.style.overflow = 'unset';
      document.removeEventListener('touchmove', handleScroll);
    };
  }, [showWechat]);

  // 添加新的状态
  const [selectedImage, setSelectedImage] = useState<GalleryImage | null>(null);

  // 首先在组件顶部添加状态
  const [imageLoading, setImageLoading] = useState(true);

  // 修改设置选中图片的函数
  const handleImageClick = async (image: GalleryImage) => {
    setImageLoading(true); // 重置加载状态

    // 在设置 selectedImage 前，确保使用最新的点赞状态
    const updatedImage = {
      ...image,
      likeCount: image.likeCount,
      isLiked: likedWorks[image.id] || false // 确保点赞状态正确
    };

    setSelectedImage(updatedImage);

    // 增加浏览次数
    try {
      const { data: response } = await worksApi.updateImageViewCount(image.id);
      if (response.code === 200) {
        // 更新本地状态中的浏览次数
        setGalleryImages(prevImages =>
          prevImages.map(img =>
            img.id === image.id
              ? { ...img, views: (img.views || 0) + 1 }
              : img
          )
        );

        // 同时更新 selectedImage 的浏览次数
        setSelectedImage(prev =>
          prev && prev.id === image.id
            ? { ...prev, views: (prev.views || 0) + 1 }
            : prev
        );
      }
    } catch (error) {
      console.error('增加浏览次数失败:', error);
    }
  };

  // 添加排序状态
  const [sortType, setSortType] = useState('popular'); // 默认热门

  // 添加类型定义
  interface Filters {
    type?: number;
    status?: number;
    orderBy?: 'newest' | 'oldest' | 'popular' | 'recommend';  // 限制具体的字符串类型
  }

  // 在组件内添加状态
  const [filters, setFilters] = useState<Filters>({
    type: undefined,
    status: undefined,
    orderBy: 'popular' as const  // 使用 as const 确保类型正确
  });

  // 修改 handleSortChange 函数
  const handleSortChange = async (value: string) => {
    setSortType(value);

    // 使用新的 filters 值直接调用 fetchWorks
    const newFilters = {
      ...filters,
      orderBy: value as 'newest' | 'oldest' | 'popular' | 'recommend'
    };

    try {
      setIsLoading(true);
      const { data: response } = await worksApi.getAllWorks({
        type: 1,
        orderBy: newFilters.orderBy,
        status: 1,
        page: 1,
        size: 10
      });

      if (response.code === 200) {
        setWorksList(response.data.list || []);
        setHasMore(response.data.list?.length === 10);
        setPage(1);
      }
    } catch (error) {
      console.error('获取作品列表失败:', error);
    } finally {
      setIsLoading(false);
    }

    // 更新 filters 状态
    setFilters(newFilters);
  };

  // 修改 handleTagClick 函数
  const handleTagClick = (label: string) => {
    if (label === '搜索结果' && !searchResults) {
      return;
    }

    setActiveTag(label);

    // 重置滚动位置到顶部
    const showcaseContent = document.querySelector('.showcase-content');
    if (showcaseContent) {
      showcaseContent.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    }

    if (label !== '搜索结果') {
      setSearchQuery('');
      setSearchResults(null);
      setPage(1);
      setHasMore(true);

      // 清空当前数据
      if (showType === 'scratch-image') {
        setGalleryImages([]);
      } else {
        setWorksList([]);
      }

      const orderByValue = label === '最新' ? 'newest' :
        label === '热门' ? 'popular' :
          'recommend';
      handleSortChange(orderByValue);
    }
  };

  // 移除重复的 formatContent 函数定义，保留一个就好
  const formatContent = (content: string) => {
    return content
      // 处理标题
      .replace(/####\s+(.*)/g, '<h4 style="font-size: 16px; font-weight: 600; color: #1D2129; margin: 24px 0 16px;">$1</h4>')
      // 处理加粗和下划线组合
      .replace(/<strong><u>(.*?)<\/u><\/strong>/g, '<span style="font-weight: 600; text-decoration: underline;">$1</span>')
      // 处理加粗
      .replace(/<strong>(.*?)<\/strong>/g, '<span style="font-weight: 600; color: #1D2129;">$1</span>')
      // 处理下划线
      .replace(/<u>(.*?)<\/u>/g, '<span style="text-decoration: underline;">$1</span>')
      // 处理换行
      .replace(/\n/g, '<br/>') // 单个换行转为 <br/>
      // 处理段落
      .split('<br/><br/>').map(paragraph =>
        `<div style="margin-bottom: 16px;">${paragraph}</div>`
      ).join('');
  };

  // 修改搜索处理函数
  const handleSearch = async () => {
    const notification = GetNotification();
    
    if (!searchQuery.trim()) return;

    try {
      setIsLoading(true);
      setHasMore(false);

      // 先清空现有数据集合和搜索状态
      setGalleryImages([]);
      setWorksList([]);
      setSearchResults(null);

      // 设置标签，但不触发 useEffect
      setActiveTag('搜索结果');

      const { data: response } = await worksApi.getAllWorks({
        keyword: searchQuery,
        type: showType === 'scratch-image' ? 2 : 1,
        orderBy: 'newest',
        page: 1,
        size: 12
      });

      if (response.code === 200) {
        if (response.data.list.length > 0) {
          setSearchResults(response.data);

          if (showType === 'scratch-image') {
            const formattedImages = response.data.list.map((img: SearchImageResult) => {
              // 确保author字段正确处理
              let authorData = {
                nickName: '未知用户',
                avatarUrl: DEFAULT_AVATAR
              };

              // 尝试从不同来源获取作者信息
              if (img.author_nickName || img.author_avatarUrl) {
                authorData = {
                  nickName: img.author_nickName || '未知用户',
                  avatarUrl: img.author_avatarUrl || DEFAULT_AVATAR
                };
              } else if (img.author) {
                authorData = {
                  nickName: img.author.nickName || '未知用户',
                  avatarUrl: img.author.avatarUrl || DEFAULT_AVATAR
                };
              }

              return {
                ...img,
                imageUrl: img.backupImagePath || img.imageUrl || '/images/image-placeholder.jpg',
                src: img.backupImagePath || img.imageUrl || '/images/image-placeholder.jpg',
                title: img.prompt || img.title || '未命名图片',
                description: img.prompt || img.description || '暂无描述',
                prompt: img.prompt,
                views: img.viewCount || 0,
                likeCount: img.likeCount || 0,
                createTime: img.uploadTime || img.createTime,
                author: authorData
              };
            });
            setGalleryImages(formattedImages);
          } else {
            setWorksList(response.data.list);
          }
          setHasMore(response.data.list.length === 12);
          setPage(1);
        } else {
          notification.info('没有找到相关内容');
        }
      }
    } catch (error) {
      console.error('搜索失败:', error);
      notification.error('搜索失败，请重试');
    } finally {
      setIsLoading(false);
    }
  };

  // 添加两个不同的 ref 用于懒加载
  const searchLoadingRef = useRef(null);  // 搜索结果的懒加载 ref
  const normalLoadingRef = useRef(null);  // 普通列表的懒加载 ref

  // 修改懒加载的 useEffect
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && hasMore && !isLoading) {
          clearTimeout(timeoutId);
          timeoutId = setTimeout(async () => {
            try {
              setIsLoading(true);

              if (activeTag === '搜索结果') {
                // 搜索状态下使用搜索 API
                const { data: response } = await worksApi.getAllWorks({
                  keyword: searchQuery,
                  type: showType === 'scratch-image' ? 2 : 1,
                  orderBy: 'newest',
                  page: page + 1,
                  size: 12
                });

                if (response.code === 200) {
                  if (showType === 'scratch-image') {
                    const formattedImages = response.data.list.map((img: SearchImageResult) => ({
                      ...img,
                      imageUrl: img.backupImagePath || img.imageUrl || '/images/image-placeholder.jpg',
                      src: img.backupImagePath || img.imageUrl || '/images/image-placeholder.jpg',
                      title: img.prompt || img.title || '未命名图片',
                      description: img.prompt || img.description || '暂无描述',
                      prompt: img.prompt,
                      views: img.viewCount || 0,
                      likeCount: img.likeCount || 0,  // 添加这个字段
                      createTime: img.uploadTime || img.createTime, // 修改这里，使用 img 而不是 item
                      author: {
                        nickName: img.author_nickName || img.author?.nickName || '未知用户',
                        avatarUrl: img.author_avatarUrl || img.author?.avatarUrl || '/images/avatar-placeholder.png'
                      }
                    }));
                    setGalleryImages(prev => [...prev, ...formattedImages]);
                  } else {
                    setWorksList(prev => [...prev, ...response.data.list]);
                  }
                  setHasMore(response.data.list.length === 12);
                  setPage(page + 1);
                }
              } else {
                // 非搜索状态下使用普通加载
                if (showType === 'scratch-image') {
                  await fetchGalleryImages(page + 1);
                } else if (showType === 'scratch-work') {
                  await fetchWorks(page + 1);
                }
              }
            } finally {
              setIsLoading(false);
            }
          }, 300);
        }
      },
      {
        root: null,
        rootMargin: '100px',
        threshold: 0.1
      }
    );

    const currentRef = activeTag === '搜索结果' ? searchLoadingRef.current : normalLoadingRef.current;
    if (currentRef) {
      observer.observe(currentRef);
    }

    return () => {
      clearTimeout(timeoutId);
      if (currentRef) {
        observer.unobserve(currentRef);
      }
      observer.disconnect();
    };
  }, [hasMore, isLoading, page, showType, searchQuery, activeTag]);

  // 添加新的 useEffect 来监听 activeTag 变化
  useEffect(() => {
    // 重置滚动位置到顶部
    const showcaseContent = document.querySelector('.showcase-content');
    if (showcaseContent) {
      showcaseContent.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    }

    // 清空当前数据
    if (showType === 'scratch-image') {
      setGalleryImages([]);
    } else {
      setWorksList([]);
    }

    setPage(1);
    setHasMore(true);
  }, [activeTag]); // 监听 activeTag 变化

  // 添加任务相关状态
  const [tasks, setTasks] = useState<any[]>([]);

  // 检查作业是否需要修改并显示提示
  const checkTasksForRevision = (taskList: Array<{
    id: number;
    taskName: string;
    assignments?: Array<{
      studentId: number;
      taskStatus: number;
      submitTime: string;
      feedback?: string;
    }>;
  }>) => {
    const notification = GetNotification();
    // 从localStorage获取已经提示过的作业记录
    const notifiedRecords = JSON.parse(localStorage.getItem('notifiedRedoRecords') || '{}');

    // 查找需要修改且未提示过的作业
    const tasksNeedRevision = taskList.filter(task => {
      const assignment = task.assignments?.[0];
      if (!assignment || assignment.studentId !== userId || assignment.taskStatus !== TaskStatus.RE_DO) {
        return false;
      }

      // 使用作业ID和提交时间的组合作为唯一标识
      const recordKey = `${task.id}_${assignment.submitTime}`;
      return !notifiedRecords[recordKey];
    });

    if (tasksNeedRevision.length > 0) {
      // 显示提示
      Modal.warning({
        title: '作业需要修改',
        content: (
          <div>
            <p>以下作业被老师打回需要修改：</p>
            <ul className="mt-2">
              {tasksNeedRevision.map(task => (
                <li key={task.id} className="mb-2">
                  • {task.taskName}
                  {task.assignments?.[0]?.feedback && (
                    <div className="ml-4 mt-1 text-gray-500">
                      修改意见：{task.assignments[0].feedback}
                    </div>
                  )}
                </li>
              ))}
            </ul>
          </div>
        ),
        onOk() {
          // 记录新的提示记录
          const newNotifiedRecords = { ...notifiedRecords };
          tasksNeedRevision.forEach(task => {
            const assignment = task.assignments![0];
            const recordKey = `${task.id}_${assignment.submitTime}`;
            newNotifiedRecords[recordKey] = true;
          });
          localStorage.setItem('notifiedRedoRecords', JSON.stringify(newNotifiedRecords));
        }
      });
    }
  };

  // 获取任务列表
  const fetchTasks = async () => {
    if (!userId) return;

    try {
      const userData = localStorage.getItem('user');
      const user = userData ? JSON.parse(userData) : null;
      const roleId = user?.roleId || 1;

      const response = await task.getTaskList({
        studentId: userId,
        roleId,
        page: 1,
        size: 50
      });

      if (response.data.code === 200) {
        const taskList = response.data.data.list;
        setTasks(taskList);
        // 检查是否有需要修改的作业
        checkTasksForRevision(taskList);
      }
    } catch (error) {
      console.error('获取任务列表失败:', error);
    }
  };

  // 在用户登录状态改变时获取任务列表
  const fetchTasksRef = useRef(false);
  useEffect(() => {
    if (userState.isLoggedIn && userId && !fetchTasksRef.current) {
      fetchTasksRef.current = true;
      fetchTasks();
    }
  }, [userState.isLoggedIn, userId]);

  // 添加新的状态来跟踪点赞状态


  // 修改点赞处理函数
  const handleLike = async (e: React.MouseEvent, item: WorkItem | ImageData) => {
    const notification = GetNotification();
    
    e.stopPropagation();

    if (!userState.isLoggedIn) {
      const redirectUrl = pathname;
      router.push(`/login?redirect=${encodeURIComponent(redirectUrl)}`);
      return;
    }

    // 立即更新UI
    const isCurrentlyLiked = likedWorks[item.id];
    const currentLikeCount = item.likeCount || 0;
    const newLikeCount = currentLikeCount + (isCurrentlyLiked ? -1 : 1);

    setLikedWorks(prev => ({
      ...prev,
      [item.id]: !isCurrentlyLiked
    }));

    // 更新所有相关状态
    item.likeCount = newLikeCount;

    // 如果当前有选中的图片且ID匹配，也要更新选中图片的点赞状态
    if (selectedImage && selectedImage.id === item.id) {
      setSelectedImage({
        ...selectedImage,
        likeCount: newLikeCount,
        isLiked: !isCurrentlyLiked
      });
    }

    // 更新图片列表中的点赞状态
    if (item.type === 2) {
      setGalleryImages(prevImages =>
        prevImages.map(img =>
          img.id === item.id
            ? { ...img, likeCount: newLikeCount }
            : img
        )
      );
    } else {
      // 更新作品列表中的点赞状态
      setWorksList(prevWorks =>
        prevWorks.map(work =>
          work.id === item.id
            ? { ...work, likeCount: newLikeCount }
            : work
        )
      );
    }

    try {
      const response = await worksApi.toggleLike(item.id, item.type || 1);

      if (response?.data?.code === 200) {
        notification.success(isCurrentlyLiked ? '取消点赞成功' : '点赞成功');
      }
    } catch (error) {
      // 发生错误时恢复原状态
      setLikedWorks(prev => ({
        ...prev,
        [item.id]: isCurrentlyLiked
      }));

      const revertedLikeCount = newLikeCount + (isCurrentlyLiked ? 1 : -1); // 恢复原来的点赞数
      item.likeCount = revertedLikeCount;

      // 恢复选中图片的点赞状态
      if (selectedImage && selectedImage.id === item.id) {
        setSelectedImage({
          ...selectedImage,
          likeCount: revertedLikeCount,
          isLiked: isCurrentlyLiked
        });
      }

      // 恢复图片列表中的点赞状态
      if (item.type === 2) {
        setGalleryImages(prevImages =>
          prevImages.map(img =>
            img.id === item.id
              ? { ...img, likeCount: revertedLikeCount }
              : img
          )
        );
      } else {
        // 恢复作品列表中的点赞状态
        setWorksList(prevWorks =>
          prevWorks.map(work =>
            work.id === item.id
              ? { ...work, likeCount: revertedLikeCount }
              : work
          )
        );
      }

      console.error('点赞失败:', error);
      notification.error('点赞失败，请重试');
    }
  };

  // 添加点赞列表的状态
  const [likedList, setLikedList] = useState<any[]>([]);
  const [likedPage, setLikedPage] = useState(1);
  const [hasMoreLiked, setHasMoreLiked] = useState(true);
  const [isLoadingLiked, setIsLoadingLiked] = useState(false);

  // 添加获取点赞列表的函数
  const fetchLikedList = async (pageNum = 1) => {
    try {
      setIsLoadingLiked(true);
      const { data: response } = await worksApi.getLikedList({
        page: pageNum,
        size: 10
      });

      if (response.code === 200) {
        if (pageNum === 1) {
          setLikedList(response.data.list);
        } else {
          setLikedList(prev => [...prev, ...response.data.list]);
        }
        setHasMoreLiked(response.data.list.length === 10);
        setLikedPage(pageNum);
      }
    } catch (error) {
      console.error('获取点赞列表失败:', error);
    } finally {
      setIsLoadingLiked(false);
    }
  };

  // 添加加载更多的处理函数
  const handleLoadMoreLiked = () => {
    if (!isLoadingLiked && hasMoreLiked) {
      fetchLikedList(likedPage + 1);
    }
  };

  // 修改标签切换处理
  useEffect(() => {
    if (activeTag === '点赞') {
      fetchLikedList(1);
    }
  }, [activeTag]);

  // 添加滚动监听
  useEffect(() => {
    const handleScroll = () => {
      if (activeTag === '点赞' && !isLoadingLiked && hasMoreLiked) {
        const scrollHeight = document.documentElement.scrollHeight;
        const scrollTop = document.documentElement.scrollTop;
        const clientHeight = document.documentElement.clientHeight;

        if (scrollHeight - scrollTop - clientHeight < 100) {
          handleLoadMoreLiked();
        }
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [activeTag, isLoadingLiked, hasMoreLiked]);

  // ... 在组件内部添加状态
  const [showReportDialog, setShowReportDialog] = useState(false)
  const [reportTarget, setReportTarget] = useState<{ id: number; type: number } | null>(null)

  // 添加处理轮播图按钮点击的函数
  const handleCarouselButtonClick = (item: CarouselItemDisplay) => {
    // 如果是跳转到 scratch 编辑器
    if (item.redirectUrl === '/logicleap') {
      if (!userState.isLoggedIn) {
        const redirectUrl = pathname;
        router.push(`/login?redirect=${encodeURIComponent(redirectUrl)}`);
        return
      }

      // 获取完整的用户状态
      const userData = localStorage.getItem('user');
      const fullUserState = userData ? JSON.parse(userData) : null;

      // 使用 viewWork 打开新窗口
      viewWork({
        content: '',
        workId: undefined,
        userId: userState.userId,
        isNew: true,
        userState: fullUserState || userState
      });
    } else if (item.redirectUrl) {
      // 其他链接直接跳转
      router.push(item.redirectUrl);
    }
  }

  // 修复容器高度过高的问题
  useEffect(() => {
    // 添加CSS修复容器高度问题
    const style = document.createElement('style');
    style.innerHTML = `
      .min-h-\\[calc\\(80vh-6rem\\)\\] {
        min-height: calc(80vh - 6rem);
        max-height: calc(100vh - 6rem);
      }
      
      @media (max-width: 768px) {
        .min-h-\\[calc\\(80vh-6rem\\)\\] {
          min-height: auto;
          max-height: none;
        }
      }
      
      .section.min-h-\\[calc\\(80vh-6rem\\)\\] {
        display: flex;
        align-items: center;
      }
    `;
    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style);
    };
  }, []);

  // 2. 添加打开/关闭模态框的函数 
  const handleOpenJoinUsModal = () => {
    setIsJoinUsModalOpen(true);
  };

  const handleCloseJoinUsModal = () => {
    setIsJoinUsModalOpen(false);
  };

  // 自定义箭头组件(用于作品详情)
  const WorkNextArrow = (props: any) => {
    const { className, onClick } = props;
    return (
      <button className="work-slider-arrow next" aria-label="下一张" onClick={onClick}>
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2">
          <path d="M9 18l6-6-6-6" />
        </svg>
      </button>
    );
  };

  const WorkPrevArrow = (props: any) => {
    const { className, onClick } = props;
    return (
      <button className="work-slider-arrow prev" aria-label="上一张" onClick={onClick}>
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2">
          <path d="M15 18l-6-6 6-6" />
        </svg>
      </button>
    );
  };

  return (
    <div className="w-full min-h-screen bg-white relative">
      {/* 动态点点背景 */}
      <div className="absolute inset-0 z-0">
        <AIBackground />
      </div>

      {/* 修改顶部轮播和Logo部分的容器结构 */}
      <div className="max-w-[100rem] mx-auto relative z-10">
        <motion.section
          initial="hidden"
          animate="visible"
          variants={fadeInUpVariants}
          className="min-h-[h-auto] flex flex-wrap items-start justify-between px-4 lg:px-8 gap-8"
        >
          <motion.div
            aria-label="轮播图容器"
            variants={fadeInUpVariants}
            className="w-full lg:w-[50%] xl:w-[55%] flex-shrink-0 flex-grow-0"
          >
            <div className="mt-10 md:mt-20 mb-4">
              <motion.h1
                className="text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold text-[#4766C2] mb-4 leading-tight"
              >
                <motion.span
                  initial={{ opacity: 1 }}
                  animate={{ opacity: 1 }}
                  className="inline-block relative"
                  style={{ minWidth: '1em' }}
                >
                  {displayText}
                  {showCursor && (
                    <motion.span
                      initial={{ opacity: 0 }}
                      animate={{ opacity: isTypingComplete ? 1 : 0 }}
                      transition={{ duration: 0.3 }}
                      className="absolute right-0 top-0"
                    >
                      <span className="inline-block w-[2px] h-[1em] bg-[#4766C2] ml-1 animate-blink"></span>
                    </motion.span>
                  )}
                </motion.span>
              </motion.h1>
              <motion.p
                className="text-base md:text-lg lg:text-xl text-gray-600 max-w-[600px]"
                initial={{ opacity: 0 }}
                animate={{ opacity: isTypingComplete ? 1 : 0 }}
                transition={{ duration: 0.5 }}
              >
                开始创造属于你的人工智能程序
              </motion.p>
            </div>
            <div className="w-full">
              <Slider {...sliderSettings} className="carousel-container">
                {carouselItems.map((item, index) => (
                  <div key={`carousel-item-${index}-${Date.now()}`}>
                    <div className="relative h-[400px] rounded-2xl overflow-hidden">
                      {item.type === 1 ?
                        (
                          <div className="relative w-full h-full group">
                            {playingVideos[item.id] ? (
                              <div className="relative w-full h-full">
                                <video
                                  src={item.video}
                                  className="w-full h-full object-cover"
                                  autoPlay
                                  controls
                                  playsInline
                                  onError={(e) => {
                                    console.error('视频加载失败:', item.video, e);
                                    const target = e.target as HTMLVideoElement;
                                    target.style.display = 'none';
                                    setPlayingVideos(prev => ({
                                      ...prev,
                                      [item.id]: false
                                    }));
                                  }}
                                />
                              </div>
                            ) : (
                              <div className="relative w-full h-full">
                                <Image
                                  src={item.image || DEFAULT_IMAGE}
                                  alt={item.title || '轮播图片展示'}
                                  fill
                                  className="object-cover"
                                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 66vw, 50vw"
                                  priority
                                  onError={(e) => {
                                    const target = e.target as HTMLImageElement;
                                    target.src = DEFAULT_IMAGE;
                                  }}
                                />
                                <div
                                  className="absolute inset-0 flex items-center justify-center cursor-pointer z-10"
                                  onClick={() => handleVideoPlay(item.id)}
                                >
                                  <div className="w-16 h-16 bg-black/30 backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-black/40 transition-colors group-hover:scale-110 duration-300">
                                    <div className="w-0 h-0 border-t-[12px] border-t-transparent border-l-[20px] border-l-white border-b-[12px] border-b-transparent ml-1" />
                                  </div>
                                </div>
                                <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent flex flex-col justify-end p-8">
                                  <h2 className="text-3xl font-bold text-white mb-4">{item.title || '欢迎'}</h2>
                                  <p className="text-lg text-white/90 mb-8">{item.subtitle || ''}</p>
                                  {item.buttonStatus === 1 && (
                                    <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                                      <button
                                        onClick={() => handleCarouselButtonClick(item)}
                                        className="bg-white text-[#4766C2] px-8 py-3 rounded-full text-lg font-semibold 
                                        hover:bg-opacity-90 transition duration-300 inline-flex items-center
                                        shadow-lg"
                                      >
                                        {item.buttonText || "了解更多"}
                                        <ArrowRight className="inline-block ml-2" size={20} />
                                      </button>
                                    </motion.div>
                                  )}
                                </div>
                              </div>
                            )}
                          </div>
                        )
                        :
                        (
                          // 图片类型 - 根据 displayType 显示
                          item.displayType === 1 ? (
                            // 全显示模式
                            <div className="absolute inset-0">
                              <Image
                                src={item.image || DEFAULT_IMAGE}
                                alt={item.title || '轮播图片展示'}
                                fill
                                className="object-cover"
                                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 66vw, 50vw"
                                priority
                                onError={(e) => {
                                  const target = e.target as HTMLImageElement;
                                  target.src = DEFAULT_IMAGE;
                                }}
                              />
                              <div
                                aria-label='全显示模式的文字覆盖层'
                                className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent flex flex-col justify-end p-8">
                                <h2 className="text-3xl font-bold text-white mb-4">{item.title || '欢迎'}</h2>
                                <p className="text-lg text-white/90 mb-8">{item.subtitle || ''}</p>
                                {item.buttonStatus === 1 && (
                                  <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                                    <button
                                      onClick={() => handleCarouselButtonClick(item)}
                                      className="bg-white text-[#4766C2] px-8 py-3 rounded-full text-lg font-semibold 
                                      hover:bg-opacity-90 transition duration-300 inline-flex items-center
                                      shadow-lg"
                                    >
                                      {item.buttonText || "了解更多"}
                                      <ArrowRight className="inline-block ml-2" size={20} />
                                    </button>
                                  </motion.div>
                                )}
                              </div>
                            </div>
                          ) : (
                            // 半显示模式
                            <div className="absolute inset-0 flex">
                              <div className="w-1/2 relative">
                                <Image
                                  src={item.image || DEFAULT_IMAGE}
                                  alt={item.title || '轮播图片展示'}
                                  fill
                                  className="object-cover rounded-l-2xl"
                                  sizes="(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw"
                                  priority
                                  onError={(e) => {
                                    const target = e.target as HTMLImageElement;
                                    target.src = DEFAULT_IMAGE;
                                  }}
                                />
                              </div>
                              <div className="w-1/2 p-8 flex flex-col justify-center bg-gradient-to-r from-white/95 to-white/90 backdrop-blur-sm">
                                <h2 className="text-3xl font-bold text-[#4766C2] mb-4">{item.title || '欢迎'}</h2>
                                <p className="text-lg text-gray-600 mb-8 leading-relaxed">{item.subtitle || ''}</p>
                                {item.buttonStatus === 1 && (
                                  <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                                    <button
                                      onClick={() => handleCarouselButtonClick(item)}
                                      className="bg-[#4766C2] text-white px-8 py-3 rounded-full text-lg font-semibold 
                                      hover:bg-blue-700 transition duration-300 inline-flex items-center
                                      shadow-lg shadow-blue-500/30 hover:shadow-blue-500/40"
                                    >
                                      {item.buttonText || "了解更多"}
                                      <ArrowRight className="inline-block ml-2" size={20} />
                                    </button>
                                  </motion.div>
                                )}
                              </div>
                            </div>
                          )
                        )
                      }
                    </div>
                  </div>
                ))}
              </Slider>
            </div>
          </motion.div>
          <motion.div
            aria-label='Logo动画容器'
            className="w-full lg:w-[45%] xl:w-[40%] relative mt-8 md:mt-20 flex-shrink-0 flex-grow-0"
          >
            <motion.div
              initial={false}
              animate={{
                x: 0,
                y: 0,
                scale: 1,
              }}
              transition={{
                duration: 1,
                ease: "easeInOut"
              }}
              className="w-full h-[250px] sm:h-[300px] md:h-[400px] lg:h-[450px] xl:h-[500px] max-h-[40rem]"
              style={{
                position: 'relative',
                margin: '4rem auto 0 auto'
              }}
            >
              <AnimatedLogo onAnimationComplete={() => {
                // 保持回调不变
              }} />
            </motion.div>
          </motion.div>
        </motion.section>
      </div>



      {/* 创作工具区域 */}
      <div className="creation-tools-section" ref={creationToolsRef}>
        <motion.div
          className="section-header"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
        >
          <h2>创作工具</h2>
          <p>选择合适的工具，开启你的AI创作之旅</p>
        </motion.div>
        <motion.div
          className="tools-container"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={{
            visible: {
              transition: {
                staggerChildren: 0.2
              }
            }
          }}
        >
          <motion.div
            className="tool-card"
            variants={{
              hidden: { opacity: 0, y: 20 },
              visible: {
                opacity: 1,
                y: 0,
                transition: {
                  type: "spring",
                  stiffness: 100,
                  damping: 20
                }
              }
            }}
            whileHover={{ scale: 1.02 }}
          >
            <div className="tool-icon">
              <Code className="text-[#4766C2]" size={32} />
            </div>
            <h3>图形化编程</h3>
            <p>通过简单的图像化编程快速实现你的创意</p>
            <motion.button
              className="create-btn"
              onClick={(e) => handleStartCreate(e, "图形化编程")}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <span>开始创作</span>
            </motion.button>
          </motion.div>

          <motion.div
            className="tool-card"
            variants={{
              hidden: { opacity: 0, y: 20 },
              visible: {
                opacity: 1,
                y: 0,
                transition: {
                  type: "spring",
                  stiffness: 100,
                  damping: 20
                }
              }
            }}
            whileHover={{ scale: 1.02 }}
          >
            <div className="tool-icon">
              <ImageIcon className="text-[#4766C2]" size={32} />
            </div>
            <h3>节点式开发</h3>
            <p>使用节点式开发工具，进阶学习人工智能</p>
            <motion.button
              className="create-btn"
              onClick={(e) => handleStartCreate(e, "节点式开发")}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <span>开始创作</span>
            </motion.button>
          </motion.div>

          <motion.div
            className="tool-card"
            variants={{
              hidden: { opacity: 0, y: 20 },
              visible: {
                opacity: 1,
                y: 0,
                transition: {
                  type: "spring",
                  stiffness: 100,
                  damping: 20
                }
              }
            }}
            whileHover={{ scale: 1.02 }}
          >
            <div className="tool-icon">
              <MessageSquare className="text-[#4766C2]" size={32} />
            </div>
            <h3>AI IDE</h3>
            <p>使用AI IDE，开发AI应用</p>
            <motion.button
              className="create-btn"
              onClick={(e) => handleStartCreate(e, "AI IDE")}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <span>开始创作</span>
            </motion.button>
          </motion.div>
        </motion.div>
      </div>
      <div aria-label='创意展示区域 - 独立的全宽容器' className="w-full bg-white relative z-10">
        <motion.section
          ref={showcaseRef}
          initial="hidden"
          animate="visible"
          variants={fadeInUpVariants}
          className="mt-16"
        >
          <div className="showcase-container">
            {/* 使用ShowcaseSidebar组件替代原来的侧边栏 */}
            <ShowcaseSidebar
              showType={showType}
              handleSidebarItemClick={handleSidebarItemClick}
              handleStartCreate={handleStartCreate}
              loadPolicyContent={loadPolicyContent}
              setShowWechat={setShowWechat}
              onJoinUsClick={handleOpenJoinUsModal} // <-- 3. 传递 prop 
            />

            {/* 使用ShowcaseContent组件替代原来的主内容区 */}
            <ShowcaseContent
              showType={showType}
              filterTitle={filterTitle}
              activeTag={activeTag}
              searchQuery={searchQuery}
              setSearchQuery={setSearchQuery}
              handleSearch={handleSearch}
              isSearching={isSearching}
              imageTags={imageTags}
              workTags={workTags}
              handleTagClick={handleTagClick}
              galleryImages={galleryImages}
              worksList={worksList}
              handleImageClick={handleImageClick}
              handleWorkClick={handleWorkClick}
              likedWorks={likedWorks}
              setLikedWorks={setLikedWorks}
              setShowLoginDialog={setShowLoginDialog}
              userState={userState}
              worksApi={worksApi}
              setReportTarget={setReportTarget}
              setShowReportDialog={setShowReportDialog}
              DEFAULT_AVATAR={DEFAULT_AVATAR}
              isLoading={isLoading}
              hasMore={hasMore}
              searchLoadingRef={searchLoadingRef}
              normalLoadingRef={normalLoadingRef}
            />
          </div>
        </motion.section>
      </div>

      {showLoginDialog && (
        <LoginDialog
          isOpen={showLoginDialog}
          onClose={() => setShowLoginDialog(false)}
          onSuccess={() => {
            setShowLoginDialog(false)
          }}
        />
      )}

      {/* 移除原来的消息提示弹窗 */}
      {/* <AnimatePresence>
        {showMessage && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 flex items-center justify-center z-50"
          >
            <div className="bg-black/80 text-white px-8 py-4 rounded-lg text-center min-w-[200px]">
              正在开发中，敬请期待
            </div>
          </motion.div>
        )}
      </AnimatePresence> */}

      {/* 作品详情弹窗 */}
      {selectedWork && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/60 backdrop-blur-sm p-4">
          <div className="bg-white rounded-[2rem] w-[900px] max-h-[90vh] overflow-hidden shadow-2xl">
            <div className="flex h-full">
              {/* 左侧作品信息 */}
              <div className="w-2/3 p-8 overflow-y-auto">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-2xl font-bold text-gray-800">{selectedWork.title}</h2>
                  <button
                    onClick={() => setSelectedWork(null)}
                    className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                  >
                    <X size={24} className="text-gray-500" />
                  </button>
                </div>

                <div aria-label='作品图片轮播' className="relative work-slider-container mb-8">
                  <Slider
                    dots={true}
                    infinite={false}
                    speed={500}
                    slidesToShow={1}
                    slidesToScroll={1}
                    className="work-slider rounded-2xl overflow-hidden"
                    arrows={true}
                    nextArrow={<WorkNextArrow />}
                    prevArrow={<WorkPrevArrow />}
                  >
                    {/* 如果有封面图片就显示封面图片 */}
                    {selectedWork.coverImage && (
                      <div className="relative w-full aspect-video rounded-2xl overflow-hidden">
                        <Image
                          src={selectedWork.coverImage}
                          alt={selectedWork.title}
                          fill
                          loading="eager"
                          className="object-cover"
                          sizes="(max-width: 768px) 90vw, (max-width: 1200px) 66vw, 600px"
                          priority
                        />
                        <div className="absolute bottom-4 left-4 bg-black/50 backdrop-blur-sm text-white px-4 py-2 rounded-xl text-sm font-medium">
                          作品封面
                        </div>
                      </div>
                    )}

                    {/* 作品截图 */}
                    {selectedWork.screenShotImage && (
                      <div className="relative w-full aspect-video rounded-2xl overflow-hidden">
                        <Image
                          src={selectedWork.screenShotImage}
                          alt={`${selectedWork.title} 截图`}
                          fill
                          loading="eager"
                          className="object-contain bg-gray-50"
                          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                        />
                        <div className="absolute bottom-4 left-4 bg-black/50 backdrop-blur-sm text-white px-4 py-2 rounded-xl text-sm font-medium">
                          作品截图
                        </div>
                      </div>
                    )}
                  </Slider>
                </div>

                {/* 作品描述 */}
                <div className="mb-8">
                  <div className="flex items-center gap-2 mb-3 text-gray-500">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      <path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z" />
                    </svg>
                    <span className="text-sm font-medium">作品描述</span>
                  </div>
                  <div className="bg-gray-50/50 rounded-xl">
                    <p className="text-gray-600 p-4 text-sm leading-relaxed">
                      {selectedWork.description || '暂无描述'}
                    </p>
                  </div>
                </div>

                {/* 作品统计 */}
                <div className="flex items-center gap-6 text-gray-500">
                  <span className="flex items-center gap-2">
                    <Eye size={18} />
                    <span className="text-sm">{selectedWork.viewCount} 次浏览</span>
                  </span>
                  <div className="flex items-center gap-2">
                    <button
                      onClick={async (e) => {
                        e.stopPropagation();
                        const notification = GetNotification();

                        if (!userState.isLoggedIn) {
                          const redirectUrl = pathname;
                          router.push(`/login?redirect=${encodeURIComponent(redirectUrl)}`);
                          return;
                        }

                        // 获取当前点赞状态
                        const isCurrentlyLiked = likedWorks[selectedWork.id];

                        // 更新点赞状态
                        setLikedWorks(prev => ({
                          ...prev,
                          [selectedWork.id]: !isCurrentlyLiked
                        }));

                        // 更新显示的点赞数
                        const newLikeCount = (selectedWork.likeCount || 0) + (isCurrentlyLiked ? -1 : 1);
                        selectedWork.likeCount = newLikeCount;

                        try {
                          // 在后台发送请求
                          const response = await worksApi.toggleLike(selectedWork.id, 1);
                          if (response?.data?.code === 200) {
                            notification.success(isCurrentlyLiked ? '取消点赞成功' : '点赞成功');
                          }
                        } catch (error) {
                          // 如果失败，恢复状态
                          setLikedWorks(prev => ({
                            ...prev,
                            [selectedWork.id]: isCurrentlyLiked
                          }));
                          selectedWork.likeCount = newLikeCount - (isCurrentlyLiked ? -1 : 1);
                          notification.error('操作失败，请重试');
                        }
                      }}
                      className={`flex items-center gap-1 transition-colors ${likedWorks[selectedWork.id] ? 'text-red-500' : 'text-gray-500 hover:text-red-500'
                        }`}
                    >
                      {likedWorks[selectedWork.id] ? (
                        <Heart size={16} fill="currentColor" />
                      ) : (
                        <Heart size={16} />
                      )}
                      <span>{selectedWork.likeCount || 0} 点赞</span>
                    </button>
                  </div>

                </div>
              </div>

              {/* 右侧作者信息和操作按钮 */}
              <div className="w-1/3 border-l border-gray-100 p-8 bg-gray-50/50">
                {/* 作者信息 */}
                <div className="flex items-center gap-4 mb-8">
                  <div className="w-14 h-14 rounded-full overflow-hidden relative ring-2 ring-white shadow-lg">
                    <Image
                      src={selectedWork.author.avatarUrl}
                      alt={selectedWork.author.nickName}
                      fill
                      loading="eager"
                      className="object-cover"
                      sizes="56px"
                    />
                  </div>
                  <div className="flex-1 min-w-0">
                    <h3 className="font-semibold text-lg text-gray-800">{selectedWork.author.nickName}</h3>
                    <p className="text-sm text-gray-500">创作者</p>
                  </div>
                </div>

                {/* 操作按钮 */}
                <button
                  onClick={() => handleEnterWork(selectedWork)}
                  className="w-full bg-[#4766C2] text-white px-6 py-4 rounded-xl font-semibold
                    hover:bg-[#3d57a7] transition duration-300 mb-4 flex items-center justify-center gap-2
                    shadow-lg shadow-blue-500/20 hover:shadow-blue-500/30"
                >
                  <span>进入作品</span>
                  <ArrowRight size={20} />
                </button>

                {/* 原作品信息 */}
                {(selectedWork.originalWorkId || selectedWork.originalAuthorId) && (
                  <div className="mt-6">
                    <div className="text-sm font-medium text-gray-500 mb-2">原作品</div>

                    {selectedWork.originalWorkId && (
                      <div>
                        {selectedWork.originalWork ? (
                          ((selectedWork.originalWork as any)?.status === 1 || (selectedWork.originalWork as any)?.status === '1' || (selectedWork.originalWork as any)?.status === 2 || (selectedWork.originalWork as any)?.status === '2') ? (
                            <div className="bg-blue-50/20 rounded-xl overflow-hidden border border-blue-100/30">
                              {/* Original work image */}
                              <div className="relative w-full aspect-[3/2] bg-white">
                                <Image
                                  src={selectedWork.originalWork?.coverImage || selectedWork.originalWork?.screenShotImage || DEFAULT_IMAGE}
                                  alt={selectedWork.originalWork?.title || '原作品'}
                                  fill
                                  loading="eager"
                                  className="object-cover"
                                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                                  priority
                                />
                              </div>

                              {/* Original work info + Button */}
                              <div className="p-3">
                                <div className="flex items-center justify-between gap-4">
                                  <div className="min-w-0 flex-1">
                                    <div className="font-medium text-gray-800 mb-0.5 truncate">
                                      {selectedWork.originalWork?.title || '未知作品'}
                                    </div>

                                    {selectedWork.originalAuthorId && (
                                      <div className="flex items-center gap-1 min-w-0">
                                        <Image
                                          src={selectedWork.originalAuthor?.avatarUrl || DEFAULT_AVATAR}
                                          alt={selectedWork.originalAuthor?.nickName || '原作者'}
                                          width={14}
                                          height={14}
                                          className="rounded-full object-cover flex-shrink-0"
                                        />
                                        <span className="text-xs text-gray-500 truncate">
                                          {selectedWork.originalAuthor?.nickName || '未知用户'}
                                        </span>
                                      </div>
                                    )}
                                  </div>

                                  <button
                                    onClick={() => selectedWork.originalWorkId && handleEnterWork({
                                      ...selectedWork.originalWork!,
                                      id: selectedWork.originalWorkId,
                                      description: selectedWork.originalWork?.title || '',
                                      type: 1, // Assuming original work is type 1 (scratch-work)
                                      status: 1, // Assuming original status for entering is 1
                                      createTime: '', // Placeholder
                                      viewCount: 0, // Placeholder
                                      likeCount: 0, // Placeholder
                                      userId: selectedWork.originalAuthorId || 0,
                                      author: selectedWork.originalAuthor || {
                                        nickName: '未知用户',
                                        avatarUrl: DEFAULT_AVATAR
                                      }
                                    })}
                                    className="text-white text-xs inline-flex items-center gap-1
                                      bg-[#4766C2] hover:bg-[#3d57a7] px-3 py-1 rounded-full transition-colors flex-shrink-0"
                                  >
                                    查看原作品
                                    <ArrowRight size={12} />
                                  </button>
                                </div>
                              </div>
                            </div>
                          ) : (
                            <div className="bg-gray-50 p-3 rounded-xl">
                              <div className="flex items-center gap-2 text-gray-500">
                                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                  <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
                                  <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
                                </svg>
                                <span className="text-xs">原作品未公开</span>
                              </div>
                            </div>
                          )
                        ) : (
                          <div className="bg-gray-50 p-3 rounded-xl">
                            <div className="flex items-center gap-2 text-gray-500">
                              <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                <path d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                              </svg>
                              <span className="text-xs">该作品已被原作者删除</span>
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 添加弹窗 */}
      {showPolicy && <PolicyDialog />}

      {showWechat && <WechatDialog />}

      {/* 图片预览弹窗 */}
      <ImagePreviewModal
        selectedImage={selectedImage}
        onClose={() => setSelectedImage(null)}
        likedWorks={likedWorks}
        setLikedWorks={setLikedWorks}
        userState={userState}
        setShowLoginDialog={setShowLoginDialog}
      />

      {/* 公告弹窗 */}
      {<AnnouncementPop />}

      {/* 消息中心弹窗 */}
      <MessageCenter />

      {reportTarget && (
        <ReportDialog
          isOpen={showReportDialog}
          onClose={() => {
            setShowReportDialog(false)
            setReportTarget(null)
          }}
          targetId={reportTarget.id}
          targetType={reportTarget.type}
        />
      )}

      {/* 4. 添加新的加入我们模态框 */}
      <Modal
        title="扫码添加技术客服微信"
        open={isJoinUsModalOpen}
        onCancel={handleCloseJoinUsModal}
        footer={null} // 隐藏默认按钮 
        centered // 居中显示 
        width={400} // 设置合适的宽度 
      >
        <div className="flex flex-col items-center py-6"> {/* 使用 flex 居中 */}
          <div className="relative w-[250px] h-[250px] mb-4"> {/* 调整二维码大小 */}
            <Image
              src="/images/wxmingpian.png" // 确保路径正确 
              alt="添加技术客服微信二维码"
              fill
              className="object-contain" // 保持比例 
              sizes="250px"
            />
          </div>
          <p className="text-gray-600 text-center text-sm">
            微信扫码添加技术客服微信
          </p>
        </div>
      </Modal>

      {/* 添加悬浮角色 */}
      <Link
        href="https://p0ugaza03ho.feishu.cn/wiki/PlPRwbCL4ihmr4kFqbEcNB07nYg"
        target="_blank"
        rel="noopener noreferrer"
        className="hidden md:block fixed right-8 bottom-[15vh] z-40 group cursor-pointer"
      >
        <div
          onMouseEnter={() => setIsCharacterHovered(true)}
          onMouseLeave={() => setIsCharacterHovered(false)}
        >
          {/* 添加 filter 和 drop-shadow 类实现光晕 */}
          <div className="relative w-24 h-auto transition-transform duration-300 group-hover:scale-105 group-hover:drop-shadow-[0_0_12px_rgba(0,0,0,0.5)]">
            <Image
              src={isCharacterHovered ? "/images/float_rolr2.svg" : "/images/float_rolr1.svg"}
              alt="Floating Character"
              width={96}
              height={144}
              className="object-contain"
              priority
            />
          </div>
        </div>
      </Link>
    </div> // <-- 6. 确保结束标签存在以修复 linter error 
  )
}


