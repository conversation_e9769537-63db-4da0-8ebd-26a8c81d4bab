"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/workbench/page",{

/***/ "(app-pages-browser)/./app/workbench/components/CreateSeriesCourseModal.tsx":
/*!**************************************************************!*\
  !*** ./app/workbench/components/CreateSeriesCourseModal.tsx ***!
  \**************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _lib_api_course_management__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api/course-management */ \"(app-pages-browser)/./lib/api/course-management.ts\");\n/* harmony import */ var _ToastManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ToastManager */ \"(app-pages-browser)/./app/workbench/components/ToastManager.tsx\");\n/* harmony import */ var _CreateSeriesCourseModal_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./CreateSeriesCourseModal.css */ \"(app-pages-browser)/./app/workbench/components/CreateSeriesCourseModal.css\");\n/* harmony import */ var _CourseListEditModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./CourseListEditModal */ \"(app-pages-browser)/./app/workbench/components/CourseListEditModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// 图片压缩函数\nconst compressImage = function(file) {\n    let quality = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0.7, maxWidth = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 800, maxHeight = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 600;\n    return new Promise((resolve)=>{\n        const canvas = document.createElement(\"canvas\");\n        const ctx = canvas.getContext(\"2d\");\n        const img = new Image();\n        img.onload = ()=>{\n            // 计算压缩后的尺寸\n            let { width, height } = img;\n            if (width > maxWidth || height > maxHeight) {\n                const ratio = Math.min(maxWidth / width, maxHeight / height);\n                width *= ratio;\n                height *= ratio;\n            }\n            canvas.width = width;\n            canvas.height = height;\n            // 绘制压缩后的图片\n            ctx.drawImage(img, 0, 0, width, height);\n            // 转换为Blob\n            canvas.toBlob((blob)=>{\n                if (blob) {\n                    const compressedFile = new File([\n                        blob\n                    ], file.name, {\n                        type: file.type,\n                        lastModified: Date.now()\n                    });\n                    resolve(compressedFile);\n                } else {\n                    resolve(file); // 如果压缩失败，返回原文件\n                }\n            }, file.type, quality);\n        };\n        img.src = URL.createObjectURL(file);\n    });\n};\n// 上传图片到服务器获取真实URL\nconst uploadImageToServer = async (file)=>{\n    console.log(\"开始上传图片到服务器:\", file.name);\n    // 先压缩图片\n    const compressedFile = await compressImage(file, 0.8, 1200, 800);\n    console.log(\"图片压缩完成，原大小:\", file.size, \"压缩后:\", compressedFile.size);\n    // 创建FormData\n    const formData = new FormData();\n    formData.append(\"file\", compressedFile);\n    formData.append(\"type\", \"course-cover\"); // 标识这是课程封面图片\n    try {\n        var _result_data;\n        // 发送到图片上传API\n        const response = await fetch(\"/api/v1/upload/image\", {\n            method: \"POST\",\n            body: formData\n        });\n        if (!response.ok) {\n            throw new Error(\"上传失败: \".concat(response.status, \" \").concat(response.statusText));\n        }\n        const result = await response.json();\n        console.log(\"图片上传响应:\", result);\n        if (result.code === 200 && ((_result_data = result.data) === null || _result_data === void 0 ? void 0 : _result_data.url)) {\n            console.log(\"图片上传成功，URL:\", result.data.url);\n            return result.data.url; // 返回图片URL\n        } else {\n            throw new Error(result.message || \"上传失败\");\n        }\n    } catch (error) {\n        console.error(\"图片上传错误:\", error);\n        throw error;\n    }\n};\nconst CreateSeriesCourseModal = (param)=>{\n    let { isVisible, onClose, onSubmit } = param;\n    _s();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: \"\",\n        description: \"\"\n    });\n    const [coverImagePreview, setCoverImagePreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isCourseListModalVisible, setIsCourseListModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [createdSeriesData, setCreatedSeriesData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const handleInputChange = (e)=>{\n        const { name, value } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n    };\n    // 处理文件上传\n    const handleFileUpload = (e)=>{\n        var _e_target_files;\n        const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n        if (file) {\n            // 检查文件类型\n            const allowedTypes = [\n                \"image/jpeg\",\n                \"image/jpg\",\n                \"image/png\",\n                \"image/gif\"\n            ];\n            if (!allowedTypes.includes(file.type)) {\n                alert(\"请选择 JPG、PNG 或 GIF 格式的图片\");\n                return;\n            }\n            // 检查文件大小 (5MB，更严格的限制)\n            const maxSize = 5 * 1024 * 1024;\n            if (file.size > maxSize) {\n                alert(\"图片大小不能超过 5MB，请选择更小的图片或使用图片压缩工具\");\n                return;\n            }\n            // 更新表单数据\n            setFormData((prev)=>({\n                    ...prev,\n                    coverImage: file\n                }));\n            // 创建预览URL\n            const reader = new FileReader();\n            reader.onload = (e)=>{\n                var _e_target;\n                setCoverImagePreview((_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result);\n            };\n            reader.readAsDataURL(file);\n        }\n    };\n    // 移除封面图片\n    const removeCoverImage = ()=>{\n        setFormData((prev)=>({\n                ...prev,\n                coverImage: undefined\n            }));\n        setCoverImagePreview(null);\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!formData.title.trim()) {\n            alert(\"请输入系列课程标题\");\n            return;\n        }\n        // 检查是否上传了封面图片\n        if (!formData.coverImage) {\n            alert(\"请上传系列课程封面图片\");\n            return;\n        }\n        setIsSubmitting(true);\n        try {\n            console.log(\"开始构建请求数据...\");\n            // 根据后端错误信息，后端期望纯JSON格式\n            // coverImage必须是字符串，不能是文件对象\n            let coverImageUrl = \"\";\n            // 上传图片到服务器获取真实URL（必需）\n            console.log(\"开始上传图片文件:\", formData.coverImage.name, formData.coverImage.size);\n            try {\n                setIsUploading(true);\n                // 上传图片到服务器\n                coverImageUrl = await uploadImageToServer(formData.coverImage);\n                console.log(\"图片上传成功，获得URL:\", coverImageUrl);\n                // 验证返回的URL是否有效\n                if (!coverImageUrl || !coverImageUrl.startsWith(\"http\")) {\n                    throw new Error(\"图片上传返回的URL无效\");\n                }\n            } catch (error) {\n                console.error(\"图片上传失败:\", error);\n                alert(\"图片上传失败，请重试: \" + (error instanceof Error ? error.message : \"未知错误\"));\n                return; // 上传失败时直接返回，不创建课程\n            } finally{\n                setIsUploading(false);\n            }\n            // 构建JSON数据，包含必需字段\n            const requestData = {\n                title: formData.title,\n                category: 0,\n                coverImage: coverImageUrl // 必需字段：真实图片URL\n            };\n            // 可选字段：只有用户输入了description才添加\n            if (formData.description && formData.description.trim()) {\n                requestData.description = formData.description.trim();\n            }\n            console.log(\"添加真实图片URL到请求:\", coverImageUrl);\n            // 注意：不发送 projectMembers 和 tagIds 字段\n            // 这些字段可以在后续的编辑功能中添加\n            console.log(\"发送的数据:\", requestData);\n            console.log(\"构建的JSON数据:\", requestData);\n            // 发送JSON请求\n            const response = await _lib_api_course_management__WEBPACK_IMPORTED_MODULE_2__.courseManagementApi.createSeriesCourse(requestData);\n            if (response.code === 200) {\n                console.log(\"系列课程创建成功:\", response.data);\n                // 保存创建的系列课程数据\n                setCreatedSeriesData({\n                    ...response.data,\n                    coverImage: coverImageUrl,\n                    title: formData.title\n                });\n                // 显示课程列表编辑弹窗\n                setIsCourseListModalVisible(true);\n                // 通知父组件\n                onSubmit(response.data);\n            } else {\n                throw new Error(response.message || \"创建失败\");\n            }\n        } catch (error) {\n            console.error(\"创建系列课程失败:\", error);\n            // 显示更详细的错误信息\n            let errorMessage = \"创建系列课程失败，请重试\";\n            if (error.response) {\n                var _error_response_data;\n                console.error(\"错误响应:\", error.response.data);\n                console.error(\"错误状态:\", error.response.status);\n                errorMessage = \"请求失败 (\".concat(error.response.status, \"): \").concat(((_error_response_data = error.response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || error.response.statusText);\n            } else if (error.request) {\n                console.error(\"网络错误:\", error.request);\n                errorMessage = \"网络连接失败，请检查网络连接\";\n            } else {\n                console.error(\"请求配置错误:\", error.message);\n                errorMessage = error.message;\n            }\n            alert(errorMessage);\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const handleClose = ()=>{\n        setFormData({\n            title: \"\",\n            description: \"\"\n        });\n        setCoverImagePreview(null);\n        setIsSubmitting(false);\n        setIsUploading(false);\n        setIsCourseListModalVisible(false);\n        setCreatedSeriesData(null);\n        onClose();\n    };\n    // 处理课程列表编辑弹窗关闭\n    const handleCourseListModalClose = ()=>{\n        setIsCourseListModalVisible(false);\n        // 关闭课程列表编辑弹窗时，也关闭创建系列课程弹窗\n        handleClose();\n    };\n    // 处理课程列表保存\n    const handleCourseListSave = (data)=>{\n        console.log(\"保存课程列表数据:\", data);\n        // TODO: 调用API保存课程列表数据\n        alert(\"课程列表保存成功！\");\n    };\n    if (!isVisible) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ToastManager__WEBPACK_IMPORTED_MODULE_3__.ToastProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"series-modal-overlay\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"series-modal\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"series-modal-header\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"series-modal-title\",\n                                    children: \"创建系列课程\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CreateSeriesCourseModal.tsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleClose,\n                                    className: \"series-modal-close\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-6 h-6\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CreateSeriesCourseModal.tsx\",\n                                        lineNumber: 304,\n                                        columnNumber: 13\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CreateSeriesCourseModal.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CreateSeriesCourseModal.tsx\",\n                            lineNumber: 298,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"series-modal-content\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"series-cover-section\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"series-cover-container\",\n                                        children: coverImagePreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"series-cover-preview\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: coverImagePreview,\n                                                    alt: \"课程封面预览\",\n                                                    className: \"series-cover-image\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CreateSeriesCourseModal.tsx\",\n                                                    lineNumber: 315,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: removeCoverImage,\n                                                    className: \"series-cover-remove\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CreateSeriesCourseModal.tsx\",\n                                                        lineNumber: 325,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CreateSeriesCourseModal.tsx\",\n                                                    lineNumber: 320,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CreateSeriesCourseModal.tsx\",\n                                            lineNumber: 314,\n                                            columnNumber: 17\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"series-cover-placeholder\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"file\",\n                                                    accept: \"image/jpeg,image/jpg,image/png,image/gif\",\n                                                    onChange: handleFileUpload,\n                                                    className: \"series-cover-input\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CreateSeriesCourseModal.tsx\",\n                                                    lineNumber: 330,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-6 h-6 text-gray-400 mb-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CreateSeriesCourseModal.tsx\",\n                                                    lineNumber: 336,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"series-cover-text\",\n                                                    children: \"系列课程封面 *\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CreateSeriesCourseModal.tsx\",\n                                                    lineNumber: 337,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"series-cover-hint\",\n                                                    children: \"点击上传图片（必需）\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CreateSeriesCourseModal.tsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CreateSeriesCourseModal.tsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CreateSeriesCourseModal.tsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 13\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CreateSeriesCourseModal.tsx\",\n                                    lineNumber: 311,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"series-form-group\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        name: \"title\",\n                                        value: formData.title,\n                                        onChange: handleInputChange,\n                                        placeholder: \"系列课程标题\",\n                                        className: \"series-title-input\",\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CreateSeriesCourseModal.tsx\",\n                                        lineNumber: 346,\n                                        columnNumber: 13\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CreateSeriesCourseModal.tsx\",\n                                    lineNumber: 345,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"series-form-actions\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: isSubmitting,\n                                        className: \"series-create-btn \".concat(isSubmitting ? \"series-create-btn-loading\" : \"\"),\n                                        children: isSubmitting ? \"创建中...\" : \"创建系列课程\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CreateSeriesCourseModal.tsx\",\n                                        lineNumber: 359,\n                                        columnNumber: 13\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CreateSeriesCourseModal.tsx\",\n                                    lineNumber: 358,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CreateSeriesCourseModal.tsx\",\n                            lineNumber: 309,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CreateSeriesCourseModal.tsx\",\n                    lineNumber: 296,\n                    columnNumber: 7\n                }, undefined),\n                createdSeriesData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CourseListEditModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    isVisible: isCourseListModalVisible,\n                    onClose: handleCourseListModalClose,\n                    onSave: handleCourseListSave,\n                    seriesTitle: createdSeriesData.title,\n                    seriesCoverImage: createdSeriesData.coverImage,\n                    seriesId: createdSeriesData.id\n                }, void 0, false, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CreateSeriesCourseModal.tsx\",\n                    lineNumber: 372,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CreateSeriesCourseModal.tsx\",\n            lineNumber: 295,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CreateSeriesCourseModal.tsx\",\n        lineNumber: 294,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CreateSeriesCourseModal, \"lz6s6B5KdBPGF8nsXze0vZOkNKI=\");\n_c = CreateSeriesCourseModal;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CreateSeriesCourseModal);\nvar _c;\n$RefreshReg$(_c, \"CreateSeriesCourseModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/workbench/components/CreateSeriesCourseModal.tsx\n"));

/***/ })

});