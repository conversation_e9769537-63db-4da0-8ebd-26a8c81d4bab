import { PackageInfo } from '../../../lib/api/package-purchase';
import { RechargeOption, DetailedFeature } from '../types';

/**
 * 将后端套餐数据转换为前端充值选项格式
 */
export function transformPackageToRechargeOption(pkg: PackageInfo): RechargeOption {
  // 根据套餐价格和积分确定图标和样式，优先使用API返回的packageIcon
  const getPackageIcon = (points: number, price: number, apiIcon?: string) => {
    // 优先使用API返回的packageIcon
    if (apiIcon) {
      return apiIcon;
    }

    // 如果没有API图标，使用默认逻辑
    if (price <= 1) return "🧪"; // 测试套餐
    if (points >= 1000) return "👑"; // 高级套餐
    if (points >= 500) return "⭐"; // 中级套餐
    return "🎁"; // 基础套餐
  };

  const getGradient = (points: number, price: number) => {
    if (price <= 1) return "from-purple-400 to-purple-600"; // 测试套餐
    if (points >= 1000) return "from-yellow-400 to-yellow-600"; // 高级套餐
    if (points >= 500) return "from-red-400 to-red-600"; // 中级套餐
    return "from-blue-400 to-blue-600"; // 基础套餐
  };

  const getBgGradient = (points: number, price: number) => {
    if (price <= 1) return "from-purple-500 to-purple-700"; // 测试套餐
    if (points >= 1000) return "from-yellow-500 to-yellow-700"; // 高级套餐
    if (points >= 500) return "from-red-500 to-red-700"; // 中级套餐
    return "from-blue-500 to-blue-700"; // 基础套餐
  };

  // 生成特性列表 - 优先使用API返回的宣传话语
  let features: string[] = [];
  let detailedFeatures: DetailedFeature[] = [];

  // 检查是否有有效的宣传话语数据
  const hasValidPromotion = pkg.promotion &&
                           pkg.promotion.features &&
                           Array.isArray(pkg.promotion.features) &&
                           pkg.promotion.features.length > 0;

  if (hasValidPromotion && pkg.promotion) {
    console.log(`套餐 ${pkg.packageName} 使用自定义宣传话语，特性数量: ${pkg.promotion.features.length}`);

    // 使用API返回的宣传话语数据
    detailedFeatures = pkg.promotion.features.map((feature: any) => ({
      name: feature.name || '',
      value: feature.value || '',
      icon: feature.icon || '📋'
    })).filter((f: any) => f.name && f.value); // 过滤掉空的特性

    // 从详细特性中提取简单特性列表
    features = detailedFeatures.slice(0, 4).map(f => f.name);
  } else {
    console.log(`套餐 ${pkg.packageName} 没有自定义宣传话语，显示空特性列表`);

    // 没有自定义宣传话语时，显示空的特性列表
    features = [];
    detailedFeatures = [];
  }

  // 确定是否为热门套餐（基于折扣率或积分数量）
  const isPopular = pkg.discountRate <= 0.8 || pkg.points >= 500;

  // 处理原价逻辑：如果有折扣率但没有原价，根据折扣率计算原价
  let originalAmount = undefined;
  if (pkg.originalPrice && pkg.originalPrice > pkg.currentPrice) {
    originalAmount = pkg.originalPrice;
  } else if (pkg.discountRate && pkg.discountRate > 0 && pkg.discountRate < 1) {
    // 如果有折扣率但没有原价，根据折扣率计算原价
    originalAmount = Number((pkg.currentPrice / pkg.discountRate).toFixed(2));
  }

  return {
    id: pkg.packageId.toString(),
    title: pkg.packageName,
    subtitle: pkg.packageDescription || "性价比之选",
    amount: pkg.currentPrice,
    originalAmount,
    bonus: pkg.points,
    icon: (pkg.promotion?.packageIcon) || getPackageIcon(pkg.points, pkg.currentPrice, pkg.promotion?.packageIcon),
    features,
    detailedFeatures,
    popular: isPopular,
    gradient: getGradient(pkg.points, pkg.currentPrice),
    bgGradient: getBgGradient(pkg.points, pkg.currentPrice),
    // 保留原始API数据
    packageId: pkg.packageId,
    packageName: pkg.packageName,
    packageDescription: pkg.packageDescription,
    points: pkg.points,
    validityDays: pkg.validityDays,
    currentPrice: pkg.currentPrice,
    discountRate: pkg.discountRate,
    savings: pkg.savings,
    currency: pkg.currency
  };
}

/**
 * 批量转换套餐数据
 */
export function transformPackagesToRechargeOptions(packages: PackageInfo[]): RechargeOption[] {
  return packages.map(transformPackageToRechargeOption);
}

/**
 * 根据有效期天数生成标签
 */
export function getValidityLabel(validityDays: number): string {
  if (validityDays === 30) return '月卡';
  if (validityDays === 90) return '季卡';
  if (validityDays === 180) return '半年卡';
  if (validityDays === 365) return '年卡';

  // 不足一个月的显示天数
  if (validityDays < 30) {
    return `${validityDays}天`;
  }

  // 其他情况显示具体月数
  const months = Math.round(validityDays / 30);
  return `${months}个月`;
}

/**
 * 根据有效期天数获取标签的CSS类名
 */
export function getValidityTagClass(validityDays: number): string {
 return 'yearly';
}

/**
 * 生成模拟数据作为后备方案
 */
export function generateFallbackData(): RechargeOption[] {
  return [
    {
      id: "test",
      title: "测试充值",
      subtitle: "开发测试专用",
      amount: 0.01,
      bonus: 10,
      icon: "🧪",
      features: ["立即到账", "安全支付", "测试专用", "赠送10积分"],
      detailedFeatures: [
        { name: "充值金额", value: "¥0.01", icon: "💳" },
        { name: "赠送积分", value: "10积分", icon: "⚡" },
        { name: "到账时间", value: "即时到账", icon: "⏰" },
        { name: "有效期", value: "30天", icon: "📅" },
        { name: "安全保障", value: "银行级加密", icon: "🛡️" },
        { name: "客服支持", value: "24小时在线", icon: "🎧" },
      ],
      popular: true,
      gradient: "from-purple-400 to-purple-600",
      bgGradient: "from-purple-500 to-purple-700",
      packageId: 0,
      points: 10,
      validityDays: 30,
      currentPrice: 0.01,
      discountRate: 1.0
    },
    {
      id: "basic",
      title: "基础套餐",
      subtitle: "新手推荐",
      amount: 50,
      originalAmount: 60,
      bonus: 100,
      icon: "⚡",
      features: ["立即到账", "安全支付", "24小时客服", "赠送100积分"],
      detailedFeatures: [
        { name: "充值金额", value: "¥50", icon: "💳" },
        { name: "赠送积分", value: "100积分", icon: "⚡" },
        { name: "到账时间", value: "即时到账", icon: "⏰" },
        { name: "有效期", value: "90天", icon: "📅" },
        { name: "安全保障", value: "银行级加密", icon: "🛡️" },
        { name: "客服支持", value: "24小时在线", icon: "🎧" },
      ],
      popular: false,
      gradient: "from-blue-400 to-blue-600",
      bgGradient: "from-blue-500 to-blue-700",
      packageId: 1,
      points: 100,
      validityDays: 90,
      currentPrice: 50,
      discountRate: 0.83,
      savings: 10
    },
    {
      id: "premium",
      title: "超值套餐",
      subtitle: "最受欢迎",
      amount: 150,
      originalAmount: 200,
      bonus: 300,
      icon: "👑",
      features: ["立即到账", "安全支付", "24小时客服", "赠送300积分", "VIP标识"],
      detailedFeatures: [
        { name: "充值金额", value: "¥150", icon: "💳" },
        { name: "赠送积分", value: "300积分", icon: "⚡" },
        { name: "VIP特权", value: "专属标识", icon: "👑" },
        { name: "到账时间", value: "即时到账", icon: "⏰" },
        { name: "有效期", value: "180天", icon: "📅" },
        { name: "专属客服", value: "1对1服务", icon: "🎧" },
        { name: "安全保障", value: "银行级加密", icon: "🛡️" },
      ],
      popular: true,
      gradient: "from-red-400 to-red-600",
      bgGradient: "from-red-500 to-red-700",
      packageId: 2,
      points: 300,
      validityDays: 180,
      currentPrice: 150,
      discountRate: 0.75,
      savings: 50
    }
  ];
}
