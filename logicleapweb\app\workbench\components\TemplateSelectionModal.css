/* 模板选择弹窗样式 */
.template-selection-modal {
  max-width: 600px;
  min-width: 520px;
  height: 700px;
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 50%, #e6f3ff 100%);
  touch-action: manipulation; /* 允许基本的触摸操作，但禁用双击缩放等 */
  overscroll-behavior: contain; /* 阻止滚动链传播 */
}

/* 模板选择弹窗使用外部关闭按钮样式 */

.template-selection-modal .modal-content-body {
  padding: 0px 40px 80px 40px;
}

.template-selection-modal .section-title {
  font-size: 18px;
  color: #1e293b;
  /* margin-bottom: 32px; */
  font-weight: 600;
  /* text-align: center; */
  position: relative;
  flex-shrink: 0;
  margin-top: 15px;
}

.template-selection-modal .section-title::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, #3b82f6 0%, #60a5fa 100%);
  border-radius: 2px;
}

/* 分配选项样式 */
.distribution-options {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
  justify-content: center;
  align-items: flex-start;
}

.distribution-card {
  flex: 1;
  max-width: 140px;
  min-width: 100px;
  min-height: 80px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
  border: 2px solid transparent;
  border-radius: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: visible;
  z-index: 1;
  padding: 12px 8px;
  transform: translateZ(0);
  will-change: transform;
}

.distribution-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.02) 0%, rgba(29, 78, 216, 0.02) 100%);
  opacity: 0;
  transition: opacity 0.15s ease-out;
  z-index: -1;
  border-radius: 16px;
  transform: translateZ(0);
  will-change: opacity;
}

.distribution-card:hover::before {
  opacity: 1;
}

.distribution-card:hover {
  transform: translateY(-1px) translateZ(0);
}

.distribution-card.selected {
  border-color: #3b82f6;
  box-shadow: 0 12px 40px rgba(59, 130, 246, 0.25);
  z-index: 5;
}

.distribution-card.selected::before {
  opacity: 0.1;
}

/* 禁用状态样式 */
.distribution-card.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
  border-color: #e5e7eb !important;
  box-shadow: none !important;
}

.distribution-card.disabled::before {
  opacity: 0 !important;
}

.distribution-card.disabled .distribution-label {
  background: none !important;
  -webkit-background-clip: unset !important;
  -webkit-text-fill-color: unset !important;
  background-clip: unset !important;
  color: #6b7280 !important;
}

/* 能量输入框样式 */
.energy-input-container {
  margin-top: 8px;
  width: 100%;
  animation: slideDown 0.3s ease-out;
}

.energy-input {
  width: 100%;
  height: 28px;
  border: 1px solid #3b82f6;
  border-radius: 6px;
  padding: 4px 8px;
  font-size: 12px;
  text-align: center;
  background: rgba(255, 255, 255, 0.95);
  color: #1e293b;
  outline: none;
  transition: all 0.3s ease;
}

.energy-input:focus {
  border-color: #1d4ed8;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
  background: white;
}

.energy-input::placeholder {
  color: #94a3b8;
  font-size: 11px;
}

/* 当前可分配能量样式 */
.available-energy {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
  border: 1px solid #93c5fd;
  border-radius: 12px;
  padding: 12px 16px;
  margin-top: 16px;
  margin-bottom: 16px;
  text-align: center;
  font-size: 14px;
  font-weight: 600;
  color: #3b82f6;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.1);
  animation: fadeInUp 0.3s ease-out;
}

/* 最低可分配能量样式 */
.min-available-energy {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.1) 0%, rgba(16, 185, 129, 0.1) 100%);
  border: 1px solid #86efac;
  border-radius: 12px;
  padding: 12px 16px;
  margin-top: 16px;
  margin-bottom: 16px;
  text-align: center;
  font-size: 14px;
  font-weight: 600;
  color: #059669;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 12px rgba(34, 197, 94, 0.1);
  animation: fadeInUp 0.3s ease-out;
}

/* 加载文字样式 */
.loading-text {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  color: #6b7280;
}

.loading-spinner-small {
  width: 16px;
  height: 16px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* 动画效果 */
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.distribution-label {
  font-size: 14px;
  font-weight: 600;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: 0.3px;
  text-align: center;
  transition: all 0.3s ease;
  line-height: 1.2;
  word-break: keep-all;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.distribution-card.selected .distribution-label {
  background: linear-gradient(135deg, #1d4ed8 0%, #0f172a 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 模板选择区域样式 */
.template-selection-area {
  margin-bottom: 32px;
}

.template-placeholder {
  height: 200px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
  border: 2px dashed #93c5fd;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: #3b82f6;
  font-weight: 600;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  cursor: pointer;
}

.template-placeholder.selected {
  border: 2px solid #3b82f6;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(147, 197, 253, 0.1) 100%);
  box-shadow: 0 8px 32px rgba(59, 130, 246, 0.2);
}

.template-placeholder::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.98) 100%);
  z-index: -1;
}

.template-placeholder:hover {
  border-color: #1d4ed8;
  box-shadow: 0 8px 32px rgba(59, 130, 246, 0.25);
  color: #1d4ed8;
  transform: translateY(-2px);
}

.template-placeholder:hover::before {
  background: linear-gradient(135deg, rgba(255, 255, 255, 1) 0%, rgba(248, 250, 252, 1) 100%);
}

/* 取消模板按钮样式 */
.cancel-template-btn {
  position: absolute;
  top: 12px;
  right: 12px;
  width: 24px;
  height: 24px;
  background: rgba(239, 68, 68, 0.9);
  color: white;
  border: none;
  border-radius: 50%;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  z-index: 10;
  line-height: 1;
}

.cancel-template-btn:hover {
  background: rgba(220, 38, 38, 1);
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
}

/* 底部按钮样式 */
.template-selection-modal .modal-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  padding: 20px 24px;
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 50%, #e6f3ff 100%);
  border-top: 1px solid rgba(59, 130, 246, 0.1);
  border-radius: 0 0 24px 24px;
  flex-shrink: 0;
  gap: 12px;
}

.prev-btn, .next-btn {
  flex: 1;
  max-width: 120px;
  padding: 12px 24px;
  border-radius: 12px;
  font-size: 15px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  min-width: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

/* 发布中状态时保持按钮大小一致 */
.next-btn.publishing {
  max-width: 120px;
  min-width: 120px;
}

.prev-btn {
  background: rgba(255, 255, 255, 0.9);
  color: #3b82f6;
  border-color: #3b82f6;
  backdrop-filter: blur(10px);
}

.prev-btn:hover {
  background: #3b82f6;
  color: white;
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

.next-btn.enabled {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border-color: #1d4ed8;
}

.next-btn.enabled:hover {
  background: linear-gradient(135deg, #1d4ed8 0%, #0f172a 100%);
  box-shadow: 0 8px 25px rgba(29, 78, 216, 0.4);
  transform: translateY(-1px);
}

.next-btn.disabled {
  background: rgba(255, 255, 255, 0.6);
  color: #9ca3af;
  border-color: #e5e7eb;
  cursor: not-allowed;
}

/* 发布中状态样式 */
.publishing-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  width: 100%;
  font-size: 15px;
  font-weight: 600;
  white-space: nowrap;
  letter-spacing: 0.3px;
}

.spinner {
  width: 14px;
  height: 14px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  flex-shrink: 0;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 步骤指示器样式覆盖 */
.template-selection-modal .step.completed .step-number {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.template-selection-modal .step.completed .step-label {
  color: #1e293b;
  font-weight: 600;
}

/* 步骤指示器连接线已删除 */
/* 确保连接线不显示 */
.template-selection-modal .step-indicator::before {
  display: none !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .template-selection-modal {
    min-width: 320px;
    max-width: 95%;
    height: 90vh;
  }

  .template-selection-modal .modal-content-body {
    padding: 20px 24px 80px 24px;
  }

  .distribution-options {
    flex-direction: column;
    gap: 12px;
  }

  .distribution-card {
    max-width: none;
    height: 60px;
  }

  .template-placeholder {
    height: 150px;
    font-size: 14px;
  }

  .template-selection-modal .modal-footer {
    flex-direction: column;
    gap: 12px;
    padding: 20px 16px;
  }

  .prev-btn, .next-btn {
    max-width: none;
  }

  /* 响应式连接线样式已删除 */
}

/* 时间设置样式 */
/* .time-settings {
  margin-top: 24px;
} */

.time-row {
  display: flex;
  gap: 16px;
  align-items: flex-end;
}

.time-field-container {
  flex: 1;
  position: relative;
}

.time-field {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
}

/* 快捷时间选择器样式 */
.quick-time-selector {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 2px solid #e2e8f0;
  border-top: none;
  border-radius: 0 0 12px 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  padding: 8px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  animation: slideDown 0.2s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.quick-time-btn {
  flex: 1;
  min-width: 60px;
  padding: 8px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background: white;
  color: #374151;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
}

.quick-time-btn:hover {
  background: #f8fafc;
  border-color: #3b82f6;
  color: #3b82f6;
}

.quick-time-btn:active {
  background: #3b82f6;
  color: white;
  transform: scale(0.98);
}

/* 确保输入框容器有相对定位以支持图标的绝对定位 */
.time-field .form-input[type="datetime-local"] {
  position: relative;
}

.time-field .form-label {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
}

/* 日期时间输入框样式优化 */
.time-field .form-input[type="datetime-local"] {
  width: 100%;
  padding: 16px 50px 16px 20px; /* 右侧留出图标空间 */
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 14px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  outline: none;
  color: #374151;
  font-family: inherit;
  cursor: pointer;
}

.time-field .form-input[type="datetime-local"]:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  background: white;
}

.time-field .form-input[type="datetime-local"]:hover {
  border-color: #cbd5e1;
  background: white;
}

/* 文本类型时间输入框样式 */
.time-field .form-input[type="text"] {
  width: 100%;
  padding: 16px 20px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 14px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  outline: none;
  color: #374151;
  font-family: inherit;
  cursor: pointer;
}

.time-field .form-input[type="text"]:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  background: white;
}

.time-field .form-input[type="text"]:hover {
  border-color: #cbd5e1;
  background: white;
}

/* 完全隐藏默认的 yyyy/mm/dd 占位符 */
.time-field .form-input[type="datetime-local"]::-webkit-datetime-edit-text {
  color: transparent !important;
  width: 0 !important;
  display: none !important;
  opacity: 0 !important;
  visibility: hidden !important;
}

.time-field .form-input[type="datetime-local"]::-webkit-datetime-edit-month-field {
  color: transparent !important;
  width: 0 !important;
  display: none !important;
  opacity: 0 !important;
  visibility: hidden !important;
}

.time-field .form-input[type="datetime-local"]::-webkit-datetime-edit-day-field {
  color: transparent !important;
  width: 0 !important;
  display: none !important;
  opacity: 0 !important;
  visibility: hidden !important;
}

.time-field .form-input[type="datetime-local"]::-webkit-datetime-edit-year-field {
  color: transparent !important;
  width: 0 !important;
  display: none !important;
  opacity: 0 !important;
  visibility: hidden !important;
}

.time-field .form-input[type="datetime-local"]::-webkit-datetime-edit-hour-field {
  color: transparent !important;
  width: 0 !important;
  display: none !important;
  opacity: 0 !important;
  visibility: hidden !important;
}

.time-field .form-input[type="datetime-local"]::-webkit-datetime-edit-minute-field {
  color: transparent !important;
  width: 0 !important;
  display: none !important;
  opacity: 0 !important;
  visibility: hidden !important;
}

/* 隐藏分隔符和包装器 */
.time-field .form-input[type="datetime-local"]::-webkit-datetime-edit-fields-wrapper {
  padding: 0 !important;
  display: none !important;
  opacity: 0 !important;
  visibility: hidden !important;
}

/* 隐藏所有可能的日期时间编辑元素 */
.time-field .form-input[type="datetime-local"]::-webkit-datetime-edit {
  color: transparent !important;
  background: transparent !important;
  display: none !important;
  opacity: 0 !important;
  visibility: hidden !important;
}

/* 强制隐藏输入框内容 */
.time-field .form-input[type="datetime-local"] {
  color: transparent !important;
  text-shadow: none !important;
  -webkit-text-fill-color: transparent !important;
}

/* 针对Firefox的样式 */
.time-field .form-input[type="datetime-local"]::-moz-placeholder {
  color: transparent;
  opacity: 0;
}

.time-field .form-input[type="datetime-local"]::placeholder {
  color: transparent;
  opacity: 0;
}

/* 通用方法：当输入框为空时使用背景图片覆盖 */
.time-field .form-input[type="datetime-local"]:placeholder-shown {
  color: transparent;
}

/* 确保在所有状态下都隐藏默认占位符 */
.time-field .form-input[type="datetime-local"]:not(:focus):invalid {
  color: transparent;
}

/* 当输入框为空时完全隐藏内容 */
.time-field .form-input[type="datetime-local"]:invalid {
  color: transparent;
}

/* 当有值时显示正常内容 */
.time-field .form-input[type="datetime-local"]:valid {
  color: #374151;
}

.time-field .form-input[type="datetime-local"]:valid::-webkit-datetime-edit-text {
  color: #374151;
  width: auto;
}

.time-field .form-input[type="datetime-local"]:valid::-webkit-datetime-edit-month-field {
  color: #374151;
  width: auto;
}

.time-field .form-input[type="datetime-local"]:valid::-webkit-datetime-edit-day-field {
  color: #374151;
  width: auto;
}

.time-field .form-input[type="datetime-local"]:valid::-webkit-datetime-edit-year-field {
  color: #374151;
  width: auto;
}

.time-field .form-input[type="datetime-local"]:valid::-webkit-datetime-edit-hour-field {
  color: #374151;
  width: auto;
}

.time-field .form-input[type="datetime-local"]:valid::-webkit-datetime-edit-minute-field {
  color: #374151;
  width: auto;
}

/* 聚焦时也显示内容 */
.time-field .form-input[type="datetime-local"]:focus {
  color: #374151;
}

.time-field .form-input[type="datetime-local"]:focus::-webkit-datetime-edit-text,
.time-field .form-input[type="datetime-local"]:focus::-webkit-datetime-edit-month-field,
.time-field .form-input[type="datetime-local"]:focus::-webkit-datetime-edit-day-field,
.time-field .form-input[type="datetime-local"]:focus::-webkit-datetime-edit-year-field,
.time-field .form-input[type="datetime-local"]:focus::-webkit-datetime-edit-hour-field,
.time-field .form-input[type="datetime-local"]:focus::-webkit-datetime-edit-minute-field {
  color: #374151;
  width: auto;
}

/* 添加自定义文字显示 - 替换默认占位符 */
.time-field::before {
  content: attr(data-label);
  position: absolute;
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
  color: #6b7280;
  pointer-events: none;
  font-size: 14px;
  z-index: 10;
  line-height: 1;
  font-weight: 400;
  transition: color 0.2s ease;
  background: transparent;
}

/* 当输入框聚焦时改变文字颜色 */
.time-field:has(.form-input[type="datetime-local"]:focus)::before,
.time-field:has(.form-input[type="text"]:focus)::before {
  color: #3b82f6;
}

/* 当有值时显示不同的样式 */
.time-field:has(.form-input[type="datetime-local"]:valid)::before,
.time-field:has(.form-input[type="text"]:not(:placeholder-shown))::before {
  color: #374151;
  font-weight: 500;
}

/* 添加选中日期显示 */
.time-field::after {
  content: attr(data-selected-date);
  position: absolute;
  left: 20px; /* 与标签文字相同位置，最左边 */
  top: 50%;
  transform: translateY(-50%);
  color: #374151;
  pointer-events: none;
  font-size: 14px;
  z-index: 10;
  line-height: 1;
  font-weight: 500;
  opacity: 0;
  transition: opacity 0.2s ease;
  background: transparent;
}

/* 当有值时显示选中的日期 */
.time-field.has-selected-date::after {
  opacity: 1;
}

/* 当有选中日期时隐藏标签文字 */
.time-field.has-selected-date::before {
  opacity: 0;
}

/* 确保文字不会干扰点击 */
.time-field::before {
  pointer-events: none;
}

/* 增强点击区域 */
.time-field {
  cursor: pointer;
}

.time-field:hover .form-input[type="datetime-local"],
.time-field:hover .form-input[type="text"] {
  border-color: #cbd5e1;
}

/* 日期选择器图标样式 - 移动到最右边 */
.time-field .form-input[type="datetime-local"]::-webkit-calendar-picker-indicator {
  opacity: 0.7;
  cursor: pointer;
  padding: 6px;
  border-radius: 6px;
  transition: all 0.2s ease;
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  width: 24px;
  height: 24px;
  margin: 0;
  background-color: transparent;
  z-index: 15;
  border: none;
  outline: none;
}

.time-field .form-input[type="datetime-local"]::-webkit-calendar-picker-indicator:hover {
  opacity: 1;
  background: rgba(59, 130, 246, 0.1);
}

/* 加载动画样式 - 覆盖全局样式，确保居中显示 */
.template-selection-modal .loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #6b7280;
  text-align: center;
  width: 100%;
  height: 160px;
}

.template-selection-modal .loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e5e7eb;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 轮播组件样式 */
.work-image-container .ant-carousel {
  position: relative;
  height: 130px;
  border-radius: 8px;
  overflow: hidden;
}

.work-image-container .ant-carousel .slick-slide {
  height: 130px;
}

.work-image-container .ant-carousel .slick-slide > div {
  height: 100%;
}

.work-image-container .work-image {
  height: 130px;
  position: relative;
  border-radius: 8px;
  overflow: hidden;
}

/* 默认轮播箭头样式优化 */
.work-image-container .ant-carousel .slick-prev,
.work-image-container .ant-carousel .slick-next {
  z-index: 10;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.work-image-container:hover .ant-carousel .slick-prev,
.work-image-container:hover .ant-carousel .slick-next {
  opacity: 1;
}

.work-image-container .ant-carousel .slick-prev:before,
.work-image-container .ant-carousel .slick-next:before {
  font-size: 20px;
  color: rgba(255, 255, 255, 0.8);
}

.work-image-container .ant-carousel .slick-prev:hover:before,
.work-image-container .ant-carousel .slick-next:hover:before {
  color: white;
}

/* 自定义轮播点样式 */
.work-image-container .custom-dots {
  position: absolute;
  bottom: 8px;
  left: 50%;
  transform: translateX(-50%);
  display: flex !important;
  gap: 4px;
  z-index: 10;
}

.work-image-container .custom-dots li {
  width: 6px;
  height: 6px;
  margin: 0;
}

.work-image-container .custom-dots li button {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  border: none;
  padding: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.work-image-container .custom-dots li.slick-active button {
  background: white;
  transform: scale(1.2);
  box-shadow: 0 0 8px rgba(255, 255, 255, 0.6);
}

/* 当只有一张图片时隐藏轮播点 */
.work-image-container .ant-carousel .slick-dots li:only-child {
  display: none !important;
}

/* 当只有一个轮播点时隐藏整个点容器 */
.work-image-container .ant-carousel .slick-dots:has(li:only-child) {
  display: none !important;
}

/* 轮播过渡动画优化 */
.work-image-container .ant-carousel .slick-slide {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.work-image-container .ant-carousel .slick-track {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 防止轮播跳跃时的闪烁 */
.work-image-container .ant-carousel .slick-list {
  overflow: hidden;
}

.work-image-container .ant-carousel .slick-slide img {
  transition: opacity 0.2s ease-in-out;
}

/* 当轮播切换时保持图片稳定 */
.work-image-container .ant-carousel .slick-slide.slick-current img {
  opacity: 1;
}

.work-image-container .ant-carousel .slick-slide:not(.slick-current) img {
  opacity: 0.95;
}

/* 图片标签样式 */
.work-image-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
  color: white;
  padding: 8px 12px;
  font-size: 12px;
}

.work-image-label {
  font-size: 12px;
  color: white;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .time-row {
    flex-direction: column;
    gap: 12px;
  }

  .time-field .form-input[type="datetime-local"] {
    padding: 14px 50px 14px 16px; /* 移动端也保留图标空间 */
    font-size: 16px; /* 防止iOS缩放 */
  }

  .quick-time-selector {
    padding: 12px;
    gap: 10px;
  }

  .quick-time-btn {
    min-width: 70px;
    padding: 10px 14px;
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .template-selection-modal {
    min-width: 280px;
    max-width: 98%;
    height: 85vh;
  }

  .template-selection-modal .modal-content-body {
    padding: 16px 20px 80px 20px;
  }

  /* 小屏幕连接线样式已删除 */

  .distribution-card {
    height: 50px;
    font-size: 12px;
  }

  .template-placeholder {
    height: 120px;
    font-size: 12px;
  }

  .time-field .form-input[type="datetime-local"] {
    padding: 12px 45px 12px 14px;
    font-size: 16px;
  }

  .quick-time-selector {
    padding: 10px;
    gap: 8px;
  }

  .quick-time-btn {
    min-width: 60px;
    padding: 8px 12px;
    font-size: 12px;
  }
}
