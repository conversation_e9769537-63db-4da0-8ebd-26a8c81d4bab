'use client'

import { useState, useEffect, useCallback, useRef } from 'react';
import { paymentApi } from '../../../lib/api/payment';
import { message } from 'antd';

export interface PaymentPollingOptions {
  orderId: string;
  onSuccess?: (orderInfo: any) => void;
  onFailed?: (error: string) => void;
  onTimeout?: () => void;
  maxAttempts?: number;
  intervalMs?: number;
  timeoutMs?: number;
}

export interface UsePaymentPollingReturn {
  isPolling: boolean;
  attempts: number;
  timeRemaining: number;
  startPolling: (options: PaymentPollingOptions) => void;
  stopPolling: () => void;
  status: 'idle' | 'polling' | 'success' | 'failed' | 'timeout';
}

export const usePaymentPolling = (): UsePaymentPollingReturn => {
  const [isPolling, setIsPolling] = useState(false);
  const [attempts, setAttempts] = useState(0);
  const [timeRemaining, setTimeRemaining] = useState(0);
  const [status, setStatus] = useState<'idle' | 'polling' | 'success' | 'failed' | 'timeout'>('idle');
  
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const countdownRef = useRef<NodeJS.Timeout | null>(null);
  const optionsRef = useRef<PaymentPollingOptions | null>(null);

  const clearAllTimers = useCallback(() => {
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current);
      pollingIntervalRef.current = null;
    }
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
    if (countdownRef.current) {
      clearInterval(countdownRef.current);
      countdownRef.current = null;
    }
  }, []);

  const stopPolling = useCallback(() => {
    clearAllTimers();
    setIsPolling(false);
    setAttempts(0);
    setTimeRemaining(0);
    setStatus('idle');
    optionsRef.current = null;
  }, [clearAllTimers]);

  const checkPaymentStatus = useCallback(async (orderId: string) => {
    try {
      console.log(`正在查询支付状态，订单号: ${orderId}`);
      const result = await paymentApi.queryPayment(orderId);
      console.log('支付状态查询结果:', result);

      if (result.success) {
        const paymentStatus = result.status || 'pending';
        console.log(`支付状态: ${paymentStatus}`);

        switch (paymentStatus) {
          case 'success':
            console.log('支付成功，停止轮询');
            setStatus('success');
            // 先保存回调函数，再停止轮询
            const successCallback = optionsRef.current?.onSuccess;
            stopPolling();
            if (successCallback) {
              console.log('调用成功回调');
              successCallback(result);
            }
            message.success('支付成功！');
            return true;

          case 'failed':
            console.log('支付失败，停止轮询');
            setStatus('failed');
            // 先保存回调函数，再停止轮询
            const failedCallback = optionsRef.current?.onFailed;
            stopPolling();
            if (failedCallback) {
              failedCallback('支付失败');
            }
            message.error('支付失败，请重试');
            return true;

          case 'pending':
          default:
            console.log('支付状态为pending，继续轮询');
            // 继续轮询
            return false;
        }
      } else {
        console.warn('查询支付状态失败:', result.errorMessage);
        return false;
      }
    } catch (error) {
      console.error('查询支付状态异常:', error);
      return false;
    }
  }, [stopPolling]);

  const startPolling = useCallback((options: PaymentPollingOptions) => {
    // 停止之前的轮询
    stopPolling();
    
    const {
      orderId,
      maxAttempts = 180, // 默认最多尝试180次（15分钟，每5秒一次）
      intervalMs = 5000,  // 默认5秒间隔
      timeoutMs = 900000, // 默认15分钟超时
    } = options;
    
    optionsRef.current = options;
    setIsPolling(true);
    setAttempts(0);
    setTimeRemaining(Math.floor(timeoutMs / 1000));
    setStatus('polling');
    
    console.log(`开始轮询支付状态，订单号: ${orderId}`);
    
    // 立即检查一次
    checkPaymentStatus(orderId);
    
    // 设置轮询定时器
    pollingIntervalRef.current = setInterval(async () => {
      setAttempts(prev => {
        const newAttempts = prev + 1;
        
        // 检查是否超过最大尝试次数
        if (newAttempts >= maxAttempts) {
          setStatus('timeout');
          stopPolling();
          if (options.onTimeout) {
            options.onTimeout();
          }
          message.warning('支付超时，请检查支付状态或重新发起支付');
          return newAttempts;
        }
        
        // 检查支付状态
        checkPaymentStatus(orderId);
        
        return newAttempts;
      });
    }, intervalMs);
    
    // 设置总超时定时器
    timeoutRef.current = setTimeout(() => {
      setStatus('timeout');
      stopPolling();
      if (options.onTimeout) {
        options.onTimeout();
      }
      message.warning('支付超时，请检查支付状态或重新发起支付');
    }, timeoutMs);
    
    // 设置倒计时定时器
    countdownRef.current = setInterval(() => {
      setTimeRemaining(prev => {
        const newTime = prev - 1;
        if (newTime <= 0) {
          return 0;
        }
        return newTime;
      });
    }, 1000);
    
  }, [stopPolling, checkPaymentStatus]);

  // 组件卸载时清理定时器
  useEffect(() => {
    return () => {
      clearAllTimers();
    };
  }, [clearAllTimers]);

  return {
    isPolling,
    attempts,
    timeRemaining,
    startPolling,
    stopPolling,
    status,
  };
};
