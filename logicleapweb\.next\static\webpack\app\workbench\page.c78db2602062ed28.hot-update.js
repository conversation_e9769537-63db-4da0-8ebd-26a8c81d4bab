"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/workbench/page",{

/***/ "(app-pages-browser)/./app/workbench/components/CourseListEditModal.tsx":
/*!**********************************************************!*\
  !*** ./app/workbench/components/CourseListEditModal.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FileText,Plus,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FileText,Plus,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FileText,Plus,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FileText,Plus,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FileText,Plus,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _lib_api_course_management__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api/course-management */ \"(app-pages-browser)/./lib/api/course-management.ts\");\n/* harmony import */ var _lib_api_course__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api/course */ \"(app-pages-browser)/./lib/api/course.ts\");\n/* harmony import */ var _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api/upload */ \"(app-pages-browser)/./lib/api/upload.ts\");\n/* harmony import */ var _lib_api_works__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/api/works */ \"(app-pages-browser)/./lib/api/works.ts\");\n/* harmony import */ var _barrel_optimize_names_Select_antd__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Select!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/select/index.js\");\n/* harmony import */ var logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\");\n/* harmony import */ var _TemplatePickerModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./TemplatePickerModal */ \"(app-pages-browser)/./app/workbench/components/TemplatePickerModal.tsx\");\n/* harmony import */ var _CourseListEditModal_css__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./CourseListEditModal.css */ \"(app-pages-browser)/./app/workbench/components/CourseListEditModal.css\");\n/* harmony import */ var _NewPublishTaskModal_css__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./NewPublishTaskModal.css */ \"(app-pages-browser)/./app/workbench/components/NewPublishTaskModal.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n// 获取系列课程详情\nconst fetchSeriesDetail = async (seriesId)=>{\n    console.log(\"\\uD83D\\uDD0D 获取系列课程详情，seriesId:\", seriesId);\n    console.log(\"\\uD83D\\uDD17 调用API: GET /api/v1/course-management/series/{seriesId}\");\n    const response = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getSeriesDetail(seriesId);\n    console.log(\"\\uD83D\\uDCE1 系列详情API响应:\", response);\n    return response.data;\n};\n// API调用函数 - 获取系列下的所有课程（包含未发布和已发布）\nconst fetchCourseList = async (seriesId)=>{\n    console.log(\"\\uD83D\\uDD0D 获取系列课程列表，seriesId:\", seriesId);\n    console.log(\"\\uD83D\\uDD17 调用API: GET /api/v1/course-management/series/{seriesId}/courses\");\n    // 使用课程管理API获取所有状态的课程\n    const response = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getManagementSeriesCourses(seriesId, {\n        page: 1,\n        pageSize: 100\n    });\n    console.log(\"\\uD83D\\uDCE1 API响应:\", response);\n    return response.data;\n};\n// 获取课程详情\nconst fetchCourseDetail = async (seriesId, courseId)=>{\n    return await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getCourseMarketplaceDetail(seriesId, courseId);\n};\n// 获取课程标签\nconst fetchCourseTags = async ()=>{\n    try {\n        console.log(\"\\uD83D\\uDD0D 开始调用 courseApi.getCourseTags\");\n        const result = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getCourseTags({\n            page: 1,\n            pageSize: 100,\n            status: 1 // 只获取启用的标签\n        });\n        console.log(\"\\uD83D\\uDD0D courseApi.getCourseTags 返回结果:\", result);\n        return result;\n    } catch (error) {\n        console.error(\"\\uD83D\\uDD0D courseApi.getCourseTags 调用失败:\", error);\n        throw error;\n    }\n};\nconst CourseListEditModal = (param)=>{\n    let { isVisible, onClose, onSave, seriesTitle, seriesCoverImage, seriesId = 123 // 默认值，实际使用时应该传入真实的seriesId\n     } = param;\n    var _getSelectedCourse, _getSelectedCourse1, _getSelectedCourse2, _getSelectedCourse3, _courseDetail_contentConfig_video, _courseDetail_contentConfig, _courseDetail_contentConfig_document, _courseDetail_contentConfig1, _courseList_find, _courseList_find1;\n    _s();\n    const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_6__.GetNotification)();\n    const [courseList, setCourseList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [rightPanelType, setRightPanelType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"none\");\n    const [selectedCourseId, setSelectedCourseId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [uploadingFiles, setUploadingFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [isPublishing, setIsPublishing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isCreating, setIsCreating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPublishingSeries, setIsPublishingSeries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [seriesStatus, setSeriesStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0); // 0=草稿，1=已发布，2=已归档\n    // 删除确认弹窗状态\n    const [deleteConfirmVisible, setDeleteConfirmVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [courseToDelete, setCourseToDelete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isDeleting, setIsDeleting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 模板选择弹窗状态\n    const [isTemplatePickerOpen, setIsTemplatePickerOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingTitle, setEditingTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(seriesTitle);\n    const [courseGoals, setCourseGoals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [courseObjectives, setCourseObjectives] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [projectMembers, setProjectMembers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // 作品相关状态\n    const [works, setWorks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loadingWorks, setLoadingWorks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedWorkIds, setSelectedWorkIds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [worksPage, setWorksPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [hasMoreWorks, setHasMoreWorks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [loadingMoreWorks, setLoadingMoreWorks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 课程标签相关状态\n    const [courseTags, setCourseTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedTags, setSelectedTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [tagsLoading, setTagsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 课程详细编辑状态\n    const [courseDetail, setCourseDetail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: \"\",\n        description: \"\",\n        coverImage: \"\",\n        videoUrl: \"\",\n        videoName: \"\",\n        isVideoEnabled: false,\n        attachmentUrl: \"\",\n        attachmentName: \"\",\n        isAttachmentEnabled: false,\n        teachingMaterials: [],\n        // 支持teachingInfo结构\n        teachingInfo: [],\n        // 支持contentConfig结构\n        contentConfig: {\n            hasVideo: 0,\n            hasDocument: 0,\n            hasAudio: 0,\n            video: {\n                url: \"\",\n                name: \"\"\n            },\n            document: {\n                url: \"\",\n                name: \"\"\n            },\n            audio: {\n                url: \"\",\n                name: \"\"\n            }\n        },\n        courseContent: {\n            topic: \"\",\n            content: \"\"\n        },\n        isOneKeyOpen: false,\n        isDistributionEnabled: false,\n        distributionReward: \"\",\n        selectedTemplate: \"\",\n        isDistributionWater: false,\n        requiredEnergy: \"\",\n        energyAmount: \"\",\n        isDistributionLimit: false,\n        distributionConditions: {\n            inviteCount: \"\",\n            taskCount: \"\",\n            experience: \"\"\n        },\n        isDistributionTime: false,\n        distributionTimeConditions: {\n            startTime: \"\",\n            endTime: \"\"\n        },\n        distributionMaterials: [],\n        // 任务配置相关状态\n        taskConfig: {\n            taskName: \"\",\n            taskDuration: \"\",\n            taskDescription: \"\",\n            selfAssessmentItems: [\n                \"\"\n            ],\n            referenceWorks: [],\n            referenceResources: []\n        }\n    });\n    // 获取课程列表数据\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isVisible && seriesId) {\n            // 检查用户登录状态\n            const token = localStorage.getItem(\"token\");\n            console.log(\"\\uD83D\\uDD10 检查登录状态，token存在:\", !!token);\n            console.log(\"\\uD83D\\uDD0D seriesId:\", seriesId);\n            if (!token) {\n                console.error(\"❌ 用户未登录，无法获取课程列表\");\n                // 设置空列表，显示空状态\n                setCourseList([]);\n                setLoading(false);\n                return;\n            }\n            loadCourseList();\n            loadCourseTags();\n            loadSeriesDetail();\n            loadUserWorks(true); // 重置加载\n        }\n    }, [\n        isVisible,\n        seriesId\n    ]);\n    const loadCourseList = async ()=>{\n        try {\n            setLoading(true);\n            console.log(\"\\uD83D\\uDD0D 开始加载课程列表，seriesId:\", seriesId);\n            const response = await fetchCourseList(seriesId);\n            console.log(\"\\uD83D\\uDCE1 API响应:\", response);\n            if (response.code === 200) {\n                console.log(\"✅ 课程列表数据:\", response.data);\n                const courses = response.data.list || [];\n                console.log(\"✅ 解析的课程数组:\", courses);\n                console.log(\"\\uD83D\\uDCCA 课程ID详情:\", courses.map((c)=>({\n                        id: c.id,\n                        type: typeof c.id,\n                        title: c.title,\n                        status: c.status\n                    })));\n                console.log(\"\\uD83D\\uDCCA 课程状态统计:\", {\n                    total: courses.length,\n                    draft: courses.filter((c)=>c.status === 0).length,\n                    published: courses.filter((c)=>c.status === 1).length\n                });\n                setCourseList(courses);\n            } else {\n                console.error(\"❌ API返回错误:\", response);\n                setCourseList([]);\n            }\n        } catch (error) {\n            var _error_response, _error_response1, _error_response2;\n            console.error(\"❌ 加载课程列表失败:\", error);\n            // 检查是否是认证错误\n            if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 401) {\n                console.error(\"\\uD83D\\uDD10 认证失败，用户未登录或token已过期\");\n            } else if (((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status) === 403) {\n                console.error(\"\\uD83D\\uDEAB 权限不足，无法访问该系列课程\");\n            } else if (((_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.status) === 404) {\n                console.error(\"\\uD83D\\uDCED 系列课程不存在，seriesId:\", seriesId);\n            } else {\n                console.error(\"\\uD83D\\uDD27 其他错误:\", error.message);\n            }\n            setCourseList([]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 加载系列课程详情\n    const loadSeriesDetail = async ()=>{\n        try {\n            console.log(\"\\uD83D\\uDD0D 开始加载系列课程详情，seriesId:\", seriesId);\n            const response = await fetchSeriesDetail(seriesId);\n            console.log(\"\\uD83D\\uDCE1 系列详情响应:\", response);\n            if (response.code === 200 && response.data) {\n                const seriesData = response.data;\n                console.log(\"✅ 系列课程详情:\", seriesData);\n                setSeriesStatus(seriesData.status || 0);\n                console.log(\"\\uD83D\\uDCCA 系列课程状态:\", seriesData.status, \"(0=草稿，1=已发布，2=已归档)\");\n            } else {\n                console.error(\"❌ 获取系列详情失败:\", response.message);\n            }\n        } catch (error) {\n            console.error(\"❌ 加载系列详情异常:\", error);\n        }\n    };\n    // 加载用户作品数据（初始加载）\n    const loadUserWorks = async function() {\n        let reset = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        try {\n            var _response_data;\n            if (reset) {\n                setLoadingWorks(true);\n                setWorks([]);\n                setWorksPage(1);\n                setHasMoreWorks(true);\n            }\n            // 从localStorage获取用户ID\n            const userId = localStorage.getItem(\"userId\") || \"2896\"; // 默认使用2896\n            const currentPage = reset ? 1 : worksPage;\n            const response = await _lib_api_works__WEBPACK_IMPORTED_MODULE_5__.worksApi.getTeacherWorks(Number(userId), currentPage, 5); // 每次加载5个\n            // 检查多种可能的数据结构\n            let worksList = [];\n            if (((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.code) === 200) {\n                var _response_data_data, _response_data1, _response_data2;\n                // 情况1: response.data.data.list\n                if ((_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : (_response_data_data = _response_data1.data) === null || _response_data_data === void 0 ? void 0 : _response_data_data.list) {\n                    worksList = response.data.data.list;\n                } else if (Array.isArray((_response_data2 = response.data) === null || _response_data2 === void 0 ? void 0 : _response_data2.data)) {\n                    worksList = response.data.data;\n                } else if (Array.isArray(response.data)) {\n                    worksList = response.data;\n                }\n            } else if (Array.isArray(response)) {\n                worksList = response;\n            }\n            if (reset) {\n                setWorks(worksList);\n            } else {\n                setWorks((prev)=>[\n                        ...prev,\n                        ...worksList\n                    ]);\n            }\n            // 检查是否还有更多数据\n            setHasMoreWorks(worksList.length === 5);\n            if (!reset) {\n                setWorksPage((prev)=>prev + 1);\n            }\n        } catch (error) {\n            console.error(\"加载作品数据失败:\", error);\n            if (reset) {\n                setWorks([]);\n            }\n        } finally{\n            setLoadingWorks(false);\n        }\n    };\n    // 加载更多作品\n    const loadMoreWorks = async ()=>{\n        if (loadingMoreWorks || !hasMoreWorks) return;\n        try {\n            var _response_data;\n            setLoadingMoreWorks(true);\n            // 从localStorage获取用户ID\n            const userId = localStorage.getItem(\"userId\") || \"2896\"; // 默认使用2896\n            const response = await _lib_api_works__WEBPACK_IMPORTED_MODULE_5__.worksApi.getTeacherWorks(Number(userId), worksPage, 5);\n            // 检查多种可能的数据结构\n            let worksList = [];\n            if (((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.code) === 200) {\n                var _response_data_data, _response_data1, _response_data2;\n                // 情况1: response.data.data.list\n                if ((_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : (_response_data_data = _response_data1.data) === null || _response_data_data === void 0 ? void 0 : _response_data_data.list) {\n                    worksList = response.data.data.list;\n                } else if (Array.isArray((_response_data2 = response.data) === null || _response_data2 === void 0 ? void 0 : _response_data2.data)) {\n                    worksList = response.data.data;\n                } else if (Array.isArray(response.data)) {\n                    worksList = response.data;\n                }\n            } else if (Array.isArray(response)) {\n                worksList = response;\n            }\n            setWorks((prev)=>[\n                    ...prev,\n                    ...worksList\n                ]);\n            // 检查是否还有更多数据\n            setHasMoreWorks(worksList.length === 5);\n            setWorksPage((prev)=>prev + 1);\n        } catch (error) {\n            console.error(\"加载更多作品失败:\", error);\n        } finally{\n            setLoadingMoreWorks(false);\n        }\n    };\n    // 处理作品选择\n    const handleWorkSelect = (workId)=>{\n        if (selectedWorkIds.includes(workId)) {\n            // 取消选中\n            setSelectedWorkIds((prev)=>prev.filter((id)=>id !== workId));\n        } else {\n            // 选中\n            setSelectedWorkIds((prev)=>[\n                    ...prev,\n                    workId\n                ]);\n        }\n    };\n    // 加载课程标签\n    const loadCourseTags = async ()=>{\n        try {\n            setTagsLoading(true);\n            console.log(\"\\uD83C\\uDFF7️ 开始加载课程标签\");\n            const response = await fetchCourseTags();\n            console.log(\"\\uD83D\\uDCE1 标签API完整响应:\", response);\n            // 检查响应结构\n            if (response && response.data) {\n                console.log(\"\\uD83D\\uDCCA 响应数据:\", response.data);\n                let tags = [];\n                // 处理标准的API响应格式 (response.data.list) - 根据实际API响应\n                if (response.data.list && Array.isArray(response.data.list)) {\n                    tags = response.data.list;\n                    console.log(\"✅ 从 data.list 解析到标签:\", tags.length, \"个\");\n                } else if (Array.isArray(response.data)) {\n                    tags = response.data;\n                    console.log(\"✅ 从 data 数组解析到标签:\", tags.length, \"个\");\n                } else if (response.data.data && response.data.data.list && Array.isArray(response.data.data.list)) {\n                    tags = response.data.data.list;\n                    console.log(\"✅ 从 data.data.list 解析到标签:\", tags.length, \"个\");\n                }\n                // 验证标签数据格式\n                console.log(\"\\uD83D\\uDD0D 原始标签数据:\", tags);\n                console.log(\"\\uD83D\\uDD0D 标签数据类型检查:\");\n                tags.forEach((tag, index)=>{\n                    var _tag_name;\n                    console.log(\"标签\".concat(index, \":\"), {\n                        tag,\n                        hasTag: !!tag,\n                        idType: typeof (tag === null || tag === void 0 ? void 0 : tag.id),\n                        nameType: typeof (tag === null || tag === void 0 ? void 0 : tag.name),\n                        nameValue: tag === null || tag === void 0 ? void 0 : tag.name,\n                        nameNotEmpty: (tag === null || tag === void 0 ? void 0 : (_tag_name = tag.name) === null || _tag_name === void 0 ? void 0 : _tag_name.trim()) !== \"\"\n                    });\n                });\n                const validTags = tags.filter((tag)=>{\n                    const isValid = tag && typeof tag.id === \"number\" && typeof tag.name === \"string\" && tag.name.trim() !== \"\";\n                    if (!isValid) {\n                        console.log(\"❌ 无效标签:\", tag, {\n                            hasTag: !!tag,\n                            idType: typeof (tag === null || tag === void 0 ? void 0 : tag.id),\n                            nameType: typeof (tag === null || tag === void 0 ? void 0 : tag.name),\n                            nameValue: tag === null || tag === void 0 ? void 0 : tag.name\n                        });\n                    }\n                    return isValid;\n                });\n                console.log(\"✅ 有效标签数量:\", validTags.length);\n                console.log(\"✅ 有效标签详情:\", validTags);\n                if (validTags.length > 0) {\n                    setCourseTags(validTags);\n                    console.log(\"✅ 成功设置真实标签数据\");\n                    return;\n                } else {\n                    console.warn(\"⚠️ 没有有效的标签数据\");\n                }\n            } else {\n                console.warn(\"⚠️ API响应格式不正确:\", response);\n            }\n            // 如果没有真实数据，设置空数组\n            console.log(\"\\uD83D\\uDCED 没有标签数据，设置空数组\");\n            setCourseTags([]);\n        } catch (error) {\n            var _error_response, _error_response1, _error_response2;\n            console.error(\"❌ 加载课程标签失败:\", error);\n            console.error(\"❌ 错误详情:\", {\n                message: error.message,\n                status: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status,\n                statusText: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.statusText,\n                data: (_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.data\n            });\n            // 发生错误时设置空数组\n            setCourseTags([]);\n        } finally{\n            setTagsLoading(false);\n        }\n    };\n    // 添加新课程\n    const addNewCourse = ()=>{\n        const newCourse = {\n            id: Date.now(),\n            seriesId: seriesId,\n            title: \"第\".concat(courseList.length + 1, \"课 - 新课时\"),\n            description: \"\",\n            coverImage: \"\",\n            orderIndex: courseList.length + 1,\n            status: 0,\n            statusLabel: \"草稿\",\n            hasVideo: 0,\n            hasDocument: 0,\n            hasAudio: 0,\n            videoDuration: 0,\n            videoDurationLabel: \"\",\n            videoName: \"\",\n            firstTeachingTitle: \"\",\n            resourcesCount: 0,\n            createdAt: new Date().toISOString(),\n            updatedAt: new Date().toISOString()\n        };\n        setCourseList([\n            ...courseList,\n            newCourse\n        ]);\n        // 自动选中新添加的课程\n        showCoursePanel(newCourse.id);\n    };\n    // 显示删除确认弹窗\n    const showDeleteConfirm = (id)=>{\n        setCourseToDelete(id);\n        setDeleteConfirmVisible(true);\n    };\n    // 确认删除课程\n    const confirmDeleteCourse = async ()=>{\n        if (!courseToDelete) return;\n        try {\n            setIsDeleting(true);\n            // 调用删除API\n            await _lib_api_course_management__WEBPACK_IMPORTED_MODULE_2__.courseManagementApi.deleteCourse(courseToDelete);\n            // 从列表中移除课程\n            setCourseList(courseList.filter((course)=>course.id !== courseToDelete));\n            // 如果删除的是当前选中的课程，清空右侧面板\n            if (selectedCourseId === courseToDelete) {\n                setRightPanelType(\"none\");\n                setSelectedCourseId(null);\n            }\n            // 关闭确认弹窗\n            setDeleteConfirmVisible(false);\n            setCourseToDelete(null);\n            // 显示成功提示\n            notification.success(\"课程已成功删除\");\n        } catch (error) {\n            console.error(\"删除课程失败:\", error);\n            notification.error(\"删除课程失败，请重试\");\n        } finally{\n            setIsDeleting(false);\n        }\n    };\n    // 取消删除\n    const cancelDelete = ()=>{\n        if (isDeleting) return; // 正在删除时不允许取消\n        setDeleteConfirmVisible(false);\n        setCourseToDelete(null);\n    };\n    // 更新课程标题\n    const updateCourseTitle = (id, newTitle)=>{\n        setCourseList(courseList.map((course)=>course.id === id ? {\n                ...course,\n                title: newTitle\n            } : course));\n    };\n    // 处理课程封面上传\n    const handleCoverUpload = async (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (file) {\n            // 检查文件类型\n            const allowedTypes = [\n                \"image/jpeg\",\n                \"image/jpg\",\n                \"image/png\",\n                \"image/gif\"\n            ];\n            if (!allowedTypes.includes(file.type)) {\n                notification.error(\"请选择 JPG、PNG 或 GIF 格式的图片文件\");\n                return;\n            }\n            // 检查文件大小 (10MB)\n            if (file.size > 10 * 1024 * 1024) {\n                notification.error(\"文件大小不能超过 10MB\");\n                return;\n            }\n            try {\n                console.log(\"\\uD83D\\uDCE4 开始上传课程封面:\", file.name);\n                // 添加到上传中的文件列表\n                setUploadingFiles((prev)=>new Set(prev).add(\"cover\"));\n                // 先显示预览图片\n                const previewUrl = URL.createObjectURL(file);\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        coverImage: previewUrl\n                    }));\n                // 上传到OSS\n                const imageUrl = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n                console.log(\"✅ 课程封面上传成功:\", imageUrl);\n                // 更新课程详情中的封面为真实URL\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        coverImage: imageUrl\n                    }));\n                // 同时更新课程列表中的封面\n                if (selectedCourseId) {\n                    setCourseList((prev)=>prev.map((course)=>course.id === selectedCourseId ? {\n                                ...course,\n                                coverImage: imageUrl\n                            } : course));\n                }\n                alert(\"课程封面上传成功！\");\n            } catch (error) {\n                console.error(\"❌ 课程封面上传失败:\", error);\n                notification.error(\"课程封面上传失败，请重试\");\n                // 上传失败时清除预览图片\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        coverImage: \"\"\n                    }));\n            } finally{\n                // 从上传中的文件列表移除\n                setUploadingFiles((prev)=>{\n                    const newSet = new Set(prev);\n                    newSet.delete(\"cover\");\n                    return newSet;\n                });\n            }\n        }\n    };\n    // 处理视频上传\n    const handleVideoUpload = async (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (file) {\n            // 检查文件类型\n            const allowedTypes = [\n                \"video/mp4\",\n                \"video/avi\",\n                \"video/mov\",\n                \"video/wmv\",\n                \"video/flv\"\n            ];\n            if (!allowedTypes.includes(file.type)) {\n                alert(\"请选择 MP4、AVI、MOV、WMV 或 FLV 格式的视频文件\");\n                return;\n            }\n            // 检查文件大小 (100MB)\n            if (file.size > 100 * 1024 * 1024) {\n                alert(\"视频文件大小不能超过 100MB\");\n                return;\n            }\n            try {\n                console.log(\"\\uD83D\\uDCE4 开始上传课程视频:\", file.name);\n                // 添加到上传中的文件列表\n                setUploadingFiles((prev)=>new Set(prev).add(\"video\"));\n                // 先显示预览视频\n                const previewUrl = URL.createObjectURL(file);\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        contentConfig: {\n                            ...prev.contentConfig,\n                            video: {\n                                url: previewUrl,\n                                name: file.name\n                            }\n                        }\n                    }));\n                // 上传到OSS\n                const videoUrl = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n                console.log(\"✅ 课程视频上传成功:\", videoUrl);\n                // 更新课程详情中的视频信息为真实URL\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        contentConfig: {\n                            ...prev.contentConfig,\n                            video: {\n                                url: videoUrl,\n                                name: file.name\n                            }\n                        }\n                    }));\n                // 同时更新课程列表中的视频信息\n                if (selectedCourseId) {\n                    setCourseList((prev)=>prev.map((course)=>course.id === selectedCourseId ? {\n                                ...course,\n                                contentConfig: {\n                                    ...course.contentConfig,\n                                    video: {\n                                        url: videoUrl,\n                                        name: file.name\n                                    }\n                                }\n                            } : course));\n                }\n                alert(\"课程视频上传成功！\");\n            } catch (error) {\n                console.error(\"❌ 课程视频上传失败:\", error);\n                alert(\"课程视频上传失败，请重试\");\n                // 上传失败时清除视频信息\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        contentConfig: {\n                            ...prev.contentConfig,\n                            video: {\n                                url: \"\",\n                                name: \"\"\n                            }\n                        }\n                    }));\n            } finally{\n                // 从上传中的文件列表移除\n                setUploadingFiles((prev)=>{\n                    const newSet = new Set(prev);\n                    newSet.delete(\"video\");\n                    return newSet;\n                });\n            }\n        }\n    };\n    // 触发视频文件选择\n    const triggerVideoUpload = ()=>{\n        const input = document.createElement(\"input\");\n        input.type = \"file\";\n        input.accept = \"video/mp4,video/avi,video/mov,video/wmv,video/flv\";\n        input.onchange = (e)=>handleVideoUpload(e);\n        input.click();\n    };\n    // 处理附件上传\n    const handleAttachmentUpload = async (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (file) {\n            // 检查文件类型\n            const allowedTypes = [\n                \"application/pdf\",\n                \"application/msword\",\n                \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\",\n                \"application/vnd.ms-excel\",\n                \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\",\n                \"application/vnd.ms-powerpoint\",\n                \"application/vnd.openxmlformats-officedocument.presentationml.presentation\",\n                \"text/plain\",\n                \"image/jpeg\",\n                \"image/png\",\n                \"image/gif\"\n            ];\n            if (!allowedTypes.includes(file.type)) {\n                alert(\"请选择支持的文件格式：PDF、DOC、DOCX、XLS、XLSX、PPT、PPTX、TXT、JPG、PNG、GIF\");\n                return;\n            }\n            // 检查文件大小 (10MB)\n            if (file.size > 10 * 1024 * 1024) {\n                alert(\"文件大小不能超过 10MB\");\n                return;\n            }\n            try {\n                console.log(\"\\uD83D\\uDCE4 开始上传课程附件:\", file.name);\n                // 添加到上传中的文件列表\n                setUploadingFiles((prev)=>new Set(prev).add(\"document\"));\n                // 上传到OSS\n                const documentUrl = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n                console.log(\"✅ 课程附件上传成功:\", documentUrl);\n                // 更新课程详情中的附件信息\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        contentConfig: {\n                            ...prev.contentConfig,\n                            hasDocument: 1,\n                            document: {\n                                url: documentUrl,\n                                name: file.name\n                            }\n                        }\n                    }));\n                alert(\"课程附件上传成功！\");\n            } catch (error) {\n                console.error(\"❌ 课程附件上传失败:\", error);\n                alert(\"课程附件上传失败，请重试\");\n            } finally{\n                // 从上传中的文件列表移除\n                setUploadingFiles((prev)=>{\n                    const newSet = new Set(prev);\n                    newSet.delete(\"document\");\n                    return newSet;\n                });\n            }\n        }\n    };\n    // 触发附件文件选择\n    const triggerAttachmentUpload = ()=>{\n        const input = document.createElement(\"input\");\n        input.type = \"file\";\n        input.accept = \".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.jpg,.png,.gif\";\n        input.onchange = (e)=>handleAttachmentUpload(e);\n        input.click();\n    };\n    // 处理教学附件上传\n    const handleTeachingMaterialUpload = async (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (file) {\n            // 检查文件类型\n            const allowedTypes = [\n                \"application/pdf\",\n                \"application/msword\",\n                \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\",\n                \"application/vnd.ms-excel\",\n                \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\",\n                \"application/vnd.ms-powerpoint\",\n                \"application/vnd.openxmlformats-officedocument.presentationml.presentation\",\n                \"text/plain\",\n                \"image/jpeg\",\n                \"image/png\",\n                \"image/gif\"\n            ];\n            if (!allowedTypes.includes(file.type)) {\n                alert(\"请选择支持的文件格式：PDF、DOC、DOCX、XLS、XLSX、PPT、PPTX、TXT、JPG、PNG、GIF\");\n                return;\n            }\n            // 检查文件大小 (10MB)\n            if (file.size > 10 * 1024 * 1024) {\n                alert(\"文件大小不能超过 10MB\");\n                return;\n            }\n            try {\n                console.log(\"\\uD83D\\uDCE4 开始上传教学材料:\", file.name);\n                // 添加到上传中的文件列表\n                setUploadingFiles((prev)=>new Set(prev).add(\"teaching-\".concat(Date.now())));\n                // 上传到OSS\n                const materialUrl = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n                console.log(\"✅ 教学材料上传成功:\", materialUrl);\n                // 添加到教学附件列表\n                const newMaterial = {\n                    type: file.type,\n                    name: file.name,\n                    url: materialUrl\n                };\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        teachingMaterials: [\n                            ...prev.teachingMaterials,\n                            newMaterial\n                        ]\n                    }));\n                alert(\"教学材料上传成功！\");\n            } catch (error) {\n                console.error(\"❌ 教学材料上传失败:\", error);\n                alert(\"教学材料上传失败，请重试\");\n            } finally{\n                // 从上传中的文件列表移除\n                setUploadingFiles((prev)=>{\n                    const newSet = new Set(prev);\n                    // 移除所有teaching-开头的项目\n                    Array.from(newSet).forEach((item)=>{\n                        if (item.startsWith(\"teaching-\")) {\n                            newSet.delete(item);\n                        }\n                    });\n                    return newSet;\n                });\n            }\n        }\n    };\n    // 触发教学附件文件选择\n    const triggerTeachingMaterialUpload = ()=>{\n        const input = document.createElement(\"input\");\n        input.type = \"file\";\n        input.accept = \".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.jpg,.png,.gif\";\n        input.onchange = (e)=>handleTeachingMaterialUpload(e);\n        input.click();\n    };\n    // 删除教学附件\n    const removeTeachingMaterial = (index)=>{\n        setCourseDetail((prev)=>({\n                ...prev,\n                teachingMaterials: prev.teachingMaterials.filter((_, i)=>i !== index)\n            }));\n    };\n    // 课程内容管理函数\n    const addTeachingInfoItem = ()=>{\n        setCourseDetail((prev)=>({\n                ...prev,\n                teachingInfo: [\n                    ...prev.teachingInfo,\n                    {\n                        title: \"\",\n                        content: \"\"\n                    }\n                ]\n            }));\n    };\n    const removeTeachingInfoItem = (index)=>{\n        setCourseDetail((prev)=>({\n                ...prev,\n                teachingInfo: prev.teachingInfo.filter((_, i)=>i !== index)\n            }));\n    };\n    const updateTeachingInfoTitle = (index, title)=>{\n        setCourseDetail((prev)=>{\n            const newTeachingInfo = [\n                ...prev.teachingInfo\n            ];\n            newTeachingInfo[index] = {\n                ...newTeachingInfo[index],\n                title\n            };\n            return {\n                ...prev,\n                teachingInfo: newTeachingInfo\n            };\n        });\n    };\n    const updateTeachingInfoContent = (index, content)=>{\n        setCourseDetail((prev)=>{\n            const newTeachingInfo = [\n                ...prev.teachingInfo\n            ];\n            newTeachingInfo[index] = {\n                ...newTeachingInfo[index],\n                content\n            };\n            return {\n                ...prev,\n                teachingInfo: newTeachingInfo\n            };\n        });\n    };\n    // 将UI格式的teachingInfo转换为API格式\n    const convertTeachingInfoForAPI = (teachingInfo)=>{\n        return teachingInfo.map((info)=>({\n                title: info.title,\n                content: info.content.split(\"\\n\").filter((line)=>line.trim()) // 按换行分割并过滤空行\n            }));\n    };\n    // 检查是否有未上传完成的文件（blob URL）\n    const checkForBlobUrls = (course)=>{\n        var _course_contentConfig_video, _course_contentConfig, _course_contentConfig_document, _course_contentConfig1;\n        const issues = [];\n        // 检查封面图片\n        if (course.coverImage && course.coverImage.startsWith(\"blob:\")) {\n            issues.push(\"课程封面图片\");\n        }\n        // 检查视频文件\n        if (((_course_contentConfig = course.contentConfig) === null || _course_contentConfig === void 0 ? void 0 : (_course_contentConfig_video = _course_contentConfig.video) === null || _course_contentConfig_video === void 0 ? void 0 : _course_contentConfig_video.url) && course.contentConfig.video.url.startsWith(\"blob:\")) {\n            issues.push(\"课程视频\");\n        }\n        // 检查文档附件\n        if (((_course_contentConfig1 = course.contentConfig) === null || _course_contentConfig1 === void 0 ? void 0 : (_course_contentConfig_document = _course_contentConfig1.document) === null || _course_contentConfig_document === void 0 ? void 0 : _course_contentConfig_document.url) && course.contentConfig.document.url.startsWith(\"blob:\")) {\n            issues.push(\"课程文档\");\n        }\n        // 检查教学材料\n        if (course.additionalResources) {\n            course.additionalResources.forEach((resource, index)=>{\n                if (resource.url && resource.url.startsWith(\"blob:\")) {\n                    issues.push(\"教学材料\".concat(index + 1));\n                }\n            });\n        }\n        return issues;\n    };\n    // 保存单个课程到后端\n    const saveCourse = async (course)=>{\n        try {\n            var _course_contentConfig_video, _course_contentConfig, _course_contentConfig_document, _course_contentConfig1;\n            // 验证必要的数据\n            if (!course.title || course.title.trim() === \"\") {\n                throw new Error(\"课程标题不能为空\");\n            }\n            // 检查是否有未上传完成的文件\n            const blobIssues = checkForBlobUrls(course);\n            if (blobIssues.length > 0) {\n                throw new Error(\"以下文件尚未上传完成，请等待上传完成后再保存：\".concat(blobIssues.join(\"、\")));\n            }\n            // 准备课程数据\n            const courseData = {\n                seriesId: seriesId,\n                title: course.title,\n                description: course.description || \"\",\n                coverImage: course.coverImage || \"\",\n                hasVideo: ((_course_contentConfig = course.contentConfig) === null || _course_contentConfig === void 0 ? void 0 : (_course_contentConfig_video = _course_contentConfig.video) === null || _course_contentConfig_video === void 0 ? void 0 : _course_contentConfig_video.url) ? 1 : 0,\n                hasDocument: ((_course_contentConfig1 = course.contentConfig) === null || _course_contentConfig1 === void 0 ? void 0 : (_course_contentConfig_document = _course_contentConfig1.document) === null || _course_contentConfig_document === void 0 ? void 0 : _course_contentConfig_document.url) ? 1 : 0,\n                hasAudio: 0,\n                videoDuration: 0,\n                contentConfig: course.contentConfig || {},\n                teachingInfo: convertTeachingInfoForAPI(course.teachingInfo || []),\n                additionalResources: course.additionalResources || [],\n                orderIndex: course.orderIndex || 1\n            };\n            console.log(\"\\uD83D\\uDCBE 准备保存课程数据:\", courseData);\n            let result;\n            // 判断是新课程还是更新课程\n            if (course.id && course.id > 1000000) {\n                // 新课程，使用创建API\n                console.log(\"\\uD83D\\uDCE4 创建新课程\");\n                const { data: response } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.createCourse(courseData);\n                result = response;\n                console.log(\"✅ 课程创建成功\");\n            } else {\n                // 现有课程，使用更新API\n                console.log(\"\\uD83D\\uDCE4 更新现有课程:\", course.id);\n                const { data: response } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.updateCourse(course.id, courseData);\n                result = response;\n                console.log(\"✅ 课程更新成功\");\n            }\n            return result;\n        } catch (error) {\n            console.error(\"❌ 保存课程失败:\", error);\n            throw error;\n        }\n    };\n    // 发布选中的课程\n    const handlePublishCourse = async ()=>{\n        if (!selectedCourseId) {\n            alert(\"请先选择要发布的课程\");\n            return;\n        }\n        const selectedCourse = courseList.find((course)=>course.id === selectedCourseId);\n        if (!selectedCourse) {\n            alert(\"未找到选中的课程\");\n            return;\n        }\n        // 检查课程是否已经发布\n        if (selectedCourse.status === 1) {\n            alert(\"该课程已经发布，无需重复发布\");\n            return;\n        }\n        try {\n            setIsPublishing(true);\n            console.log(\"\\uD83D\\uDCE4 开始发布课程\");\n            console.log(\"\\uD83D\\uDCE4 selectedCourseId:\", selectedCourseId, \"类型:\", typeof selectedCourseId);\n            console.log(\"\\uD83D\\uDCE4 课程信息:\", selectedCourse);\n            console.log(\"\\uD83D\\uDCE4 课程ID字段:\", selectedCourse.id, \"类型:\", typeof selectedCourse.id);\n            console.log(\"\\uD83D\\uDCE4 课程列表中的所有ID:\", courseList.map((c)=>({\n                    id: c.id,\n                    type: typeof c.id,\n                    title: c.title\n                })));\n            // 确保使用正确的课程ID\n            const courseIdToPublish = selectedCourse.id;\n            console.log(\"\\uD83D\\uDCE4 即将发布的课程ID:\", courseIdToPublish, \"类型:\", typeof courseIdToPublish);\n            const { data: response } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.publishCourse(courseIdToPublish);\n            if (response.code === 200) {\n                console.log(\"✅ 课程发布成功:\", response.data);\n                alert(\"课程发布成功！\");\n                // 刷新课程列表以更新状态\n                await loadCourseList();\n            } else {\n                console.error(\"❌ 发布课程失败:\", response.message);\n                alert(response.message || \"发布课程失败\");\n            }\n        } catch (error) {\n            var _error_response, _error_response_data, _error_response1;\n            console.error(\"❌ 发布课程失败:\", error);\n            console.error(\"❌ 错误详情:\", (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data);\n            // 处理具体的错误信息\n            if ((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : (_error_response_data = _error_response1.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) {\n                alert(error.response.data.message);\n            } else if (error.message) {\n                alert(error.message);\n            } else {\n                alert(\"发布课程失败，请重试\");\n            }\n        } finally{\n            setIsPublishing(false);\n        }\n    };\n    // 保存课程列表\n    const handleSave = async ()=>{\n        try {\n            setIsCreating(true);\n            // 检查是否有文件正在上传\n            if (uploadingFiles.size > 0) {\n                alert(\"有文件正在上传中，请等待上传完成后再保存\");\n                setIsCreating(false);\n                return;\n            }\n            console.log(\"\\uD83D\\uDCBE 开始保存课程列表\");\n            // 如果有选中的课程且在编辑状态，先保存当前课程\n            if (selectedCourseId && rightPanelType === \"course\") {\n                const selectedCourse = courseList.find((c)=>c.id === selectedCourseId);\n                if (selectedCourse) {\n                    var _courseDetail_teachingMaterials;\n                    // 更新课程数据\n                    const updatedCourse = {\n                        ...selectedCourse,\n                        title: courseDetail.title,\n                        description: courseDetail.description,\n                        coverImage: courseDetail.coverImage,\n                        hasVideo: courseDetail.isVideoEnabled ? 1 : 0,\n                        hasDocument: courseDetail.isAttachmentEnabled ? 1 : 0,\n                        contentConfig: courseDetail.contentConfig,\n                        teachingInfo: courseDetail.teachingInfo,\n                        additionalResources: ((_courseDetail_teachingMaterials = courseDetail.teachingMaterials) === null || _courseDetail_teachingMaterials === void 0 ? void 0 : _courseDetail_teachingMaterials.map((material)=>({\n                                title: material.name,\n                                url: material.url,\n                                description: material.name\n                            }))) || []\n                    };\n                    // 先更新课程列表中的数据\n                    setCourseList((prev)=>prev.map((course)=>course.id === selectedCourseId ? updatedCourse : course));\n                    // 然后保存到后端\n                    await saveCourse(updatedCourse);\n                }\n            }\n            // 保存系列课程信息\n            const data = {\n                title: editingTitle,\n                courseGoals,\n                courseObjectives,\n                courseList\n            };\n            onSave(data);\n            onClose();\n            console.log(\"✅ 课程列表保存完成\");\n        } catch (error) {\n            console.error(\"❌ 保存失败:\", error);\n            alert(\"保存失败: \".concat(error.message || \"请重试\"));\n        } finally{\n            setIsCreating(false);\n        }\n    };\n    // 发布系列课程\n    const handlePublish = async ()=>{\n        // 如果系列已发布，不执行任何操作\n        if (seriesStatus === 1) {\n            return;\n        }\n        try {\n            setIsPublishingSeries(true);\n            // 检查是否有课程\n            if (courseList.length === 0) {\n                alert(\"发布失败：课程系列中至少需要包含一个课程\");\n                return;\n            }\n            console.log(\"\\uD83D\\uDCE4 开始发布系列课程，系列ID:\", seriesId);\n            const { data: response } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.publishCourseSeries(seriesId);\n            if (response.code === 200) {\n                console.log(\"✅ 系列课程发布成功:\", response.data);\n                // 构建成功消息\n                const publishData = response.data;\n                let successMessage = '系列课程\"'.concat(publishData.title, '\"发布成功！');\n                // 如果有发布统计信息，添加到消息中\n                if (publishData.publishStats) {\n                    const stats = publishData.publishStats;\n                    successMessage += \"\\n\\n发布统计：\\n• 总课程数：\".concat(publishData.totalCourses, \"\\n• 已发布课程：\").concat(publishData.publishedCourses, \"\\n• 视频课程：\").concat(stats.videoCourseCount, \"个\\n• 文档课程：\").concat(stats.documentCourseCount, \"个\\n• 总资源数：\").concat(stats.totalResourcesCount, \"个\");\n                    if (stats.totalVideoDuration > 0) {\n                        const durationMinutes = Math.round(stats.totalVideoDuration / 60);\n                        successMessage += \"\\n• 视频总时长：\".concat(durationMinutes, \"分钟\");\n                    }\n                }\n                alert(successMessage);\n                // 更新系列状态为已发布\n                setSeriesStatus(1);\n                // 刷新课程列表以更新状态\n                await loadCourseList();\n                // 通知父组件刷新数据\n                onSave({\n                    type: \"publish_series\",\n                    seriesId: seriesId,\n                    message: \"系列课程发布成功\"\n                });\n            } else {\n                console.error(\"❌ 发布系列课程失败:\", response.message);\n                alert(response.message || \"发布系列课程失败\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"❌ 发布系列课程出错:\", error);\n            // 处理具体的错误信息\n            let errorMessage = \"发布系列课程失败\";\n            if ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) {\n                errorMessage = error.response.data.message;\n            } else if (error.message) {\n                errorMessage = error.message;\n            }\n            alert(errorMessage);\n        } finally{\n            setIsPublishingSeries(false);\n        }\n    };\n    // 退出编辑模式 - 保存数据并关闭\n    const handleExitEdit = ()=>{\n        handleSave();\n    };\n    // 显示设置面板\n    const showSettingsPanel = ()=>{\n        setRightPanelType(\"settings\");\n        setSelectedCourseId(null);\n    };\n    // 处理模板选择\n    const handleTemplateSelect = (template)=>{\n        setCourseDetail((prev)=>({\n                ...prev,\n                selectedTemplate: template.templateName\n            }));\n        setIsTemplatePickerOpen(false);\n    };\n    // 打开模板选择弹窗\n    const handleOpenTemplatePicker = ()=>{\n        setIsTemplatePickerOpen(true);\n    };\n    // 显示课程编辑面板\n    const showCoursePanel = async (courseId)=>{\n        setRightPanelType(\"course\");\n        setSelectedCourseId(courseId);\n        // 如果作品数据还没有加载，重新加载\n        if (works.length === 0 && !loadingWorks) {\n            loadUserWorks(true);\n        }\n        // 获取选中的课程并更新courseDetail状态\n        const selectedCourse = courseList.find((course)=>course.id === courseId);\n        console.log(\"\\uD83C\\uDFAF 找到的课程:\", selectedCourse);\n        if (selectedCourse) {\n            try {\n                console.log(\"\\uD83D\\uDD04 获取课程详情，seriesId:\", seriesId, \"courseId:\", courseId);\n                // 获取真实的课程详情数据\n                const { data: res } = await fetchCourseDetail(seriesId, courseId);\n                if (res.code === 200 && res.data) {\n                    var _courseDetailData_additionalResources, _courseDetailData_teachingInfo;\n                    const courseDetailData = res.data;\n                    console.log(\"✅ 获取到课程详情:\", courseDetailData);\n                    // 将真实的 additionalResources 映射到 teachingMaterials\n                    const teachingMaterials = ((_courseDetailData_additionalResources = courseDetailData.additionalResources) === null || _courseDetailData_additionalResources === void 0 ? void 0 : _courseDetailData_additionalResources.map((resource)=>({\n                            type: \"application/octet-stream\",\n                            name: resource.title || resource.name || \"附件\",\n                            url: resource.url\n                        }))) || [];\n                    // 将API返回的teachingInfo数组格式转换为新的格式\n                    const mappedTeachingInfo = ((_courseDetailData_teachingInfo = courseDetailData.teachingInfo) === null || _courseDetailData_teachingInfo === void 0 ? void 0 : _courseDetailData_teachingInfo.map((info)=>({\n                            title: info.title || \"\",\n                            content: Array.isArray(info.content) ? info.content.join(\"\\n\") : info.content || \"\"\n                        }))) || [];\n                    console.log(\"\\uD83D\\uDCCE 映射的教学附件:\", teachingMaterials);\n                    console.log(\"\\uD83D\\uDCDA 映射的教学信息:\", mappedTeachingInfo);\n                    setCourseDetail((prev)=>{\n                        var _courseDetailData_contentConfig_video, _courseDetailData_contentConfig, _courseDetailData_contentConfig_video1, _courseDetailData_contentConfig1, _courseDetailData_contentConfig_document, _courseDetailData_contentConfig2, _courseDetailData_contentConfig_document1, _courseDetailData_contentConfig3;\n                        return {\n                            ...prev,\n                            title: courseDetailData.title,\n                            description: courseDetailData.description,\n                            coverImage: courseDetailData.coverImage || \"\",\n                            isVideoEnabled: courseDetailData.hasVideo === 1,\n                            isAttachmentEnabled: courseDetailData.hasDocument === 1,\n                            contentConfig: courseDetailData.contentConfig || {},\n                            teachingInfo: mappedTeachingInfo,\n                            teachingMaterials: teachingMaterials,\n                            videoUrl: ((_courseDetailData_contentConfig = courseDetailData.contentConfig) === null || _courseDetailData_contentConfig === void 0 ? void 0 : (_courseDetailData_contentConfig_video = _courseDetailData_contentConfig.video) === null || _courseDetailData_contentConfig_video === void 0 ? void 0 : _courseDetailData_contentConfig_video.url) || \"\",\n                            videoName: ((_courseDetailData_contentConfig1 = courseDetailData.contentConfig) === null || _courseDetailData_contentConfig1 === void 0 ? void 0 : (_courseDetailData_contentConfig_video1 = _courseDetailData_contentConfig1.video) === null || _courseDetailData_contentConfig_video1 === void 0 ? void 0 : _courseDetailData_contentConfig_video1.name) || \"\",\n                            attachmentUrl: ((_courseDetailData_contentConfig2 = courseDetailData.contentConfig) === null || _courseDetailData_contentConfig2 === void 0 ? void 0 : (_courseDetailData_contentConfig_document = _courseDetailData_contentConfig2.document) === null || _courseDetailData_contentConfig_document === void 0 ? void 0 : _courseDetailData_contentConfig_document.url) || \"\",\n                            attachmentName: ((_courseDetailData_contentConfig3 = courseDetailData.contentConfig) === null || _courseDetailData_contentConfig3 === void 0 ? void 0 : (_courseDetailData_contentConfig_document1 = _courseDetailData_contentConfig3.document) === null || _courseDetailData_contentConfig_document1 === void 0 ? void 0 : _courseDetailData_contentConfig_document1.name) || \"\"\n                        };\n                    });\n                } else {\n                    console.error(\"❌ 获取课程详情失败:\", res.message);\n                    // 使用基础数据作为后备\n                    setCourseDetail((prev)=>({\n                            ...prev,\n                            title: selectedCourse.title,\n                            description: selectedCourse.description,\n                            coverImage: selectedCourse.coverImage || \"\",\n                            isVideoEnabled: selectedCourse.hasVideo === 1,\n                            isAttachmentEnabled: selectedCourse.hasDocument === 1,\n                            teachingMaterials: [] // 清空附件列表\n                        }));\n                }\n            } catch (error) {\n                console.error(\"❌ 获取课程详情异常:\", error);\n                // 使用基础数据作为后备\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        title: selectedCourse.title,\n                        description: selectedCourse.description,\n                        coverImage: selectedCourse.coverImage || \"\",\n                        isVideoEnabled: selectedCourse.hasVideo === 1,\n                        isAttachmentEnabled: selectedCourse.hasDocument === 1,\n                        teachingMaterials: [] // 清空附件列表\n                    }));\n            }\n        }\n    };\n    // 获取选中的课程\n    const getSelectedCourse = ()=>{\n        return courseList.find((course)=>course.id === selectedCourseId);\n    };\n    if (!isVisible) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"course-list-modal-overlay\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"course-list-modal\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"course-list-header\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"course-list-title-section\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"course-list-title\",\n                                        children: \"课程列表\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 1392,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"course-list-actions\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: showSettingsPanel,\n                                                className: \"course-list-settings-btn \".concat(rightPanelType === \"settings\" ? \"active\" : \"\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1398,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1394,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: addNewCourse,\n                                                className: \"course-list-add-btn\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1401,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1400,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 1393,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                lineNumber: 1391,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onClose,\n                                className: \"course-list-close-btn\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 1406,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                lineNumber: 1405,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                        lineNumber: 1390,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"course-list-content\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"course-list-sidebar\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"course-list-items\",\n                                    children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"course-list-loading\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"加载中...\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 1417,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 1416,\n                                        columnNumber: 17\n                                    }, undefined) : courseList.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"course-list-empty\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"course-list-empty-icon\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"w-12 h-12 text-gray-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1422,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1421,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"course-list-empty-title\",\n                                                children: \"暂无课时\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1424,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"course-list-empty-description\",\n                                                children: \"点击右上角的 + 按钮添加第一个课时\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1425,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: addNewCourse,\n                                                className: \"course-list-empty-btn\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1432,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    \"添加课时\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1428,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 1420,\n                                        columnNumber: 17\n                                    }, undefined) : courseList.map((course)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"course-list-item \".concat(selectedCourseId === course.id ? \"active\" : \"\"),\n                                            onClick: ()=>showCoursePanel(course.id),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-list-item-content\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"course-list-item-text\",\n                                                            children: course.title\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1444,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"course-status-badge \".concat(course.status === 1 ? \"published\" : \"draft\"),\n                                                            children: course.status === 1 ? \"已发布\" : \"未发布\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1445,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1443,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: (e)=>{\n                                                        e.stopPropagation();\n                                                        showDeleteConfirm(course.id);\n                                                    },\n                                                    className: \"course-list-item-delete\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"w-3 h-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1456,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1449,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, course.id, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 1438,\n                                            columnNumber: 19\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 1414,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                lineNumber: 1413,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"course-list-edit-area\",\n                                children: [\n                                    rightPanelType === \"none\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"course-edit-empty\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"course-edit-empty-icon\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"w-16 h-16 text-gray-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1469,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1468,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"course-edit-empty-title\",\n                                                children: \"无课程详情\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1471,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"course-edit-empty-description\",\n                                                children: \"点击左侧课程或设置按钮查看详情\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1472,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 1467,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    rightPanelType === \"settings\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"course-series-cover\",\n                                                children: seriesCoverImage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: seriesCoverImage,\n                                                    alt: \"系列课程封面\",\n                                                    className: \"course-series-cover-image\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1483,\n                                                    columnNumber: 21\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-series-cover-placeholder\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"系列课程封面\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1490,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1489,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1481,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"course-edit-form\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"course-edit-field\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"course-edit-label\",\n                                                                children: \"系列课程标题\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1499,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                value: editingTitle,\n                                                                onChange: (e)=>setEditingTitle(e.target.value),\n                                                                className: \"course-edit-input\",\n                                                                placeholder: \"请输入系列课程标题\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1500,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1498,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"course-edit-field\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"course-edit-label\",\n                                                                children: \"课程标签\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1511,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Select_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                mode: \"multiple\",\n                                                                style: {\n                                                                    width: \"100%\"\n                                                                },\n                                                                placeholder: \"请选择课程标签\",\n                                                                value: selectedTags,\n                                                                onChange: setSelectedTags,\n                                                                loading: tagsLoading,\n                                                                options: courseTags.map((tag)=>{\n                                                                    console.log(\"\\uD83C\\uDFF7️ 渲染标签选项:\", tag);\n                                                                    return {\n                                                                        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            style: {\n                                                                                color: tag.color\n                                                                            },\n                                                                            children: tag.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1523,\n                                                                            columnNumber: 29\n                                                                        }, void 0),\n                                                                        value: tag.id\n                                                                    };\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1512,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    fontSize: \"12px\",\n                                                                    color: \"#666\",\n                                                                    marginTop: \"4px\"\n                                                                },\n                                                                children: [\n                                                                    \"调试: 当前标签数量 \",\n                                                                    courseTags.length,\n                                                                    \", 加载状态: \",\n                                                                    tagsLoading ? \"是\" : \"否\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1532,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1510,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"course-edit-field\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"course-edit-label\",\n                                                                children: \"课程项目成员\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1539,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                value: projectMembers,\n                                                                onChange: (e)=>setProjectMembers(e.target.value),\n                                                                className: \"course-edit-input\",\n                                                                placeholder: \"请输入项目成员，如：张老师、李助教、王同学\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1540,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1538,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1496,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true),\n                                    rightPanelType === \"course\" && getSelectedCourse() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"course-detail-edit\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-detail-top\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"course-detail-cover\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"course-cover-upload-area\",\n                                                                    onClick: ()=>{\n                                                                        var _document_getElementById;\n                                                                        return (_document_getElementById = document.getElementById(\"cover-upload-input\")) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.click();\n                                                                    },\n                                                                    children: courseDetail.coverImage || ((_getSelectedCourse = getSelectedCourse()) === null || _getSelectedCourse === void 0 ? void 0 : _getSelectedCourse.coverImage) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                        src: courseDetail.coverImage || ((_getSelectedCourse1 = getSelectedCourse()) === null || _getSelectedCourse1 === void 0 ? void 0 : _getSelectedCourse1.coverImage),\n                                                                        alt: \"课程封面\",\n                                                                        className: \"course-cover-image\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1564,\n                                                                        columnNumber: 27\n                                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"course-cover-placeholder\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"点击上传课程封面\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1571,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1570,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1559,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    id: \"cover-upload-input\",\n                                                                    type: \"file\",\n                                                                    accept: \"image/jpeg,image/jpg,image/png,image/gif\",\n                                                                    onChange: handleCoverUpload,\n                                                                    style: {\n                                                                        display: \"none\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1575,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"one-click-class-btn\",\n                                                                    onClick: ()=>{\n                                                                        // TODO: 实现一键上课功能\n                                                                        console.log(\"一键上课按钮被点击\");\n                                                                        notification.info(\"一键上课功能开发中...\");\n                                                                    },\n                                                                    children: \"一键上课\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1583,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1558,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"course-detail-basic\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"course-detail-field\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            children: \"课程标题\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1596,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            value: courseDetail.title || ((_getSelectedCourse2 = getSelectedCourse()) === null || _getSelectedCourse2 === void 0 ? void 0 : _getSelectedCourse2.title) || \"\",\n                                                                            onChange: (e)=>{\n                                                                                setCourseDetail((prev)=>({\n                                                                                        ...prev,\n                                                                                        title: e.target.value\n                                                                                    }));\n                                                                                updateCourseTitle(selectedCourseId, e.target.value);\n                                                                            },\n                                                                            placeholder: \"请输入课程标题\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1597,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1595,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"course-detail-field\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            children: \"课程介绍\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1608,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                            value: courseDetail.description || ((_getSelectedCourse3 = getSelectedCourse()) === null || _getSelectedCourse3 === void 0 ? void 0 : _getSelectedCourse3.description) || \"\",\n                                                                            onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                        ...prev,\n                                                                                        description: e.target.value\n                                                                                    })),\n                                                                            placeholder: \"请输入课程介绍\",\n                                                                            rows: 3\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1609,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1607,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1594,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1557,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-detail-section\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            children: \"课程资源\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1621,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"course-resource-item\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"resource-header-right\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"课程视频\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1626,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"switch\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: \"checkbox\",\n                                                                                    checked: courseDetail.isVideoEnabled,\n                                                                                    onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                ...prev,\n                                                                                                isVideoEnabled: e.target.checked\n                                                                                            }))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1628,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"slider\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1633,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1627,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1625,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                courseDetail.isVideoEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"video-content-area\",\n                                                                    children: ((_courseDetail_contentConfig = courseDetail.contentConfig) === null || _courseDetail_contentConfig === void 0 ? void 0 : (_courseDetail_contentConfig_video = _courseDetail_contentConfig.video) === null || _courseDetail_contentConfig_video === void 0 ? void 0 : _courseDetail_contentConfig_video.url) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"video-info-section\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"video-preview\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                                                                    className: \"video-thumbnail\",\n                                                                                    controls: true,\n                                                                                    poster: courseDetail.coverImage,\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                                                                                            src: courseDetail.contentConfig.video.url,\n                                                                                            type: \"video/mp4\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1647,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined),\n                                                                                        \"您的浏览器不支持视频播放\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1642,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1641,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"video-name-centered\",\n                                                                                children: courseDetail.contentConfig.video.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1651,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"upload-btn-horizontal\",\n                                                                                onClick: triggerVideoUpload,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"重新上传\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1653,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1652,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1640,\n                                                                        columnNumber: 29\n                                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"video-upload-section\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"video-placeholder-centered\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"play-icon\",\n                                                                                    children: \"▶\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1659,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1658,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"upload-btn-horizontal\",\n                                                                                onClick: triggerVideoUpload,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"上传视频\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1662,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1661,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1657,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1637,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1624,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"course-resource-item\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"resource-header-right\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"课程附件\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1673,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"switch\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: \"checkbox\",\n                                                                                    checked: courseDetail.isAttachmentEnabled,\n                                                                                    onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                ...prev,\n                                                                                                isAttachmentEnabled: e.target.checked\n                                                                                            }))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1675,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"slider\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1680,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1674,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1672,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                courseDetail.isAttachmentEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"attachment-content-area\",\n                                                                    children: ((_courseDetail_contentConfig1 = courseDetail.contentConfig) === null || _courseDetail_contentConfig1 === void 0 ? void 0 : (_courseDetail_contentConfig_document = _courseDetail_contentConfig1.document) === null || _courseDetail_contentConfig_document === void 0 ? void 0 : _courseDetail_contentConfig_document.url) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"attachment-info-section\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"attachment-preview\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"document-icon\",\n                                                                                        children: \"\\uD83D\\uDCC4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1689,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"attachment-details\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"attachment-name\",\n                                                                                            children: courseDetail.contentConfig.document.name\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1691,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1690,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1688,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"upload-btn-horizontal\",\n                                                                                onClick: triggerAttachmentUpload,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"重新上传\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1695,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1694,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1687,\n                                                                        columnNumber: 29\n                                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"attachment-upload-section\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            className: \"upload-btn-horizontal\",\n                                                                            onClick: triggerAttachmentUpload,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"上传附件\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1701,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1700,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1699,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1684,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1671,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"course-resource-item\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"resource-header-simple\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"教学附件\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1712,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1711,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"teaching-materials\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            className: \"add-material-btn\",\n                                                                            onClick: triggerTeachingMaterialUpload,\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"+\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1716,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"上传\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1717,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1715,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        courseDetail.teachingMaterials && courseDetail.teachingMaterials.length > 0 ? courseDetail.teachingMaterials.map((material, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"material-item\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"material-name\",\n                                                                                        onClick: ()=>{\n                                                                                            if (material.url) {\n                                                                                                window.open(material.url, \"_blank\");\n                                                                                            }\n                                                                                        },\n                                                                                        style: {\n                                                                                            cursor: material.url ? \"pointer\" : \"default\",\n                                                                                            color: material.url ? \"#1890ff\" : \"inherit\",\n                                                                                            textDecoration: material.url ? \"underline\" : \"none\"\n                                                                                        },\n                                                                                        title: material.url ? \"点击下载附件\" : material.name,\n                                                                                        children: [\n                                                                                            \"\\uD83D\\uDCCE \",\n                                                                                            material.name\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1722,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        className: \"remove-material-btn\",\n                                                                                        onClick: ()=>removeTeachingMaterial(index),\n                                                                                        title: \"删除附件\",\n                                                                                        children: \"\\xd7\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1738,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, index, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1721,\n                                                                                columnNumber: 29\n                                                                            }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"empty-materials-hint\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                style: {\n                                                                                    color: \"#999\",\n                                                                                    fontSize: \"14px\"\n                                                                                },\n                                                                                children: \"暂无教学附件\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1749,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1748,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1714,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1710,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1620,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-detail-section\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"section-header\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    children: \"课程内容\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1759,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"add-content-section-btn\",\n                                                                    onClick: addTeachingInfoItem,\n                                                                    title: \"添加课程内容\",\n                                                                    children: \"+ 添加课程内容\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1760,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1758,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"course-content-area\",\n                                                            children: courseDetail.teachingInfo && courseDetail.teachingInfo.length > 0 ? courseDetail.teachingInfo.map((info, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"teaching-info-card\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"card-header\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"card-title\",\n                                                                                    children: [\n                                                                                        \"课程内容 \",\n                                                                                        index + 1\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1773,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    className: \"remove-card-btn\",\n                                                                                    onClick: ()=>removeTeachingInfoItem(index),\n                                                                                    title: \"删除此内容\",\n                                                                                    children: \"\\xd7\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1774,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1772,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"card-content\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"input-group\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                            children: \"标题\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1784,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                            type: \"text\",\n                                                                                            value: info.title,\n                                                                                            onChange: (e)=>updateTeachingInfoTitle(index, e.target.value),\n                                                                                            placeholder: \"请输入标题，如：教学目标、教学方法等\",\n                                                                                            className: \"title-input\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1785,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1783,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"input-group\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                            children: \"内容\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1794,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                                            value: info.content,\n                                                                                            onChange: (e)=>updateTeachingInfoContent(index, e.target.value),\n                                                                                            placeholder: \"请输入具体内容，多个内容项可用换行分隔\",\n                                                                                            className: \"content-textarea\",\n                                                                                            rows: 4\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1795,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1793,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1782,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, index, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1771,\n                                                                    columnNumber: 27\n                                                                }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"empty-content-hint\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: \"暂无课程内容，点击右上角按钮添加\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1808,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1807,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1768,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1757,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-detail-section\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"one-key-section\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"one-key-item\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"重新上课\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1818,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"switch\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"checkbox\",\n                                                                                checked: courseDetail.isOneKeyOpen,\n                                                                                onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                            ...prev,\n                                                                                            isOneKeyOpen: e.target.checked\n                                                                                        }))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1820,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"slider\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1825,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1819,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1817,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            courseDetail.isOneKeyOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"one-key-item\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"分配积木\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1832,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"switch\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                        type: \"checkbox\",\n                                                                                        checked: courseDetail.isDistributionEnabled,\n                                                                                        onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    isDistributionEnabled: e.target.checked\n                                                                                                }))\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1834,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"slider\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1839,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1833,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            courseDetail.isDistributionEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"block-template-section\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        className: \"select-template-btn\",\n                                                                                        onClick: handleOpenTemplatePicker,\n                                                                                        children: \"选择积木模板\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1843,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"selected-template-display\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            children: courseDetail.selectedTemplate || \"选中的模板名字\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1850,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1849,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1842,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1831,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"one-key-item\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"分配能量\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1857,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"switch\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                        type: \"checkbox\",\n                                                                                        checked: courseDetail.isDistributionWater,\n                                                                                        onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    isDistributionWater: e.target.checked\n                                                                                                }))\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1859,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"slider\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1864,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1858,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1856,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    courseDetail.isDistributionWater && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"energy-input-section\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"需要能量：\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1870,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"text\",\n                                                                                value: courseDetail.requiredEnergy || \"\",\n                                                                                onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                            ...prev,\n                                                                                            requiredEnergy: e.target.value\n                                                                                        })),\n                                                                                placeholder: \"请输入需要的能量值\",\n                                                                                className: \"energy-input\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1871,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1869,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"one-key-item\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"分配任务\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1882,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"switch\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                        type: \"checkbox\",\n                                                                                        checked: courseDetail.isDistributionLimit,\n                                                                                        onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    isDistributionLimit: e.target.checked\n                                                                                                }))\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1884,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"slider\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1889,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1883,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1881,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    courseDetail.isDistributionLimit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"task-config-form\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"task-config-row\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"task-config-field\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                                children: \"任务名称:\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                lineNumber: 1899,\n                                                                                                columnNumber: 35\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                type: \"text\",\n                                                                                                value: courseDetail.taskConfig.taskName,\n                                                                                                onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                            ...prev,\n                                                                                                            taskConfig: {\n                                                                                                                ...prev.taskConfig,\n                                                                                                                taskName: e.target.value\n                                                                                                            }\n                                                                                                        })),\n                                                                                                placeholder: \"请输入任务名称\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                lineNumber: 1900,\n                                                                                                columnNumber: 35\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1898,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"task-config-field\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                                children: \"任务持续天数:\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                lineNumber: 1911,\n                                                                                                columnNumber: 35\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                type: \"number\",\n                                                                                                value: courseDetail.taskConfig.taskDuration,\n                                                                                                onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                            ...prev,\n                                                                                                            taskConfig: {\n                                                                                                                ...prev.taskConfig,\n                                                                                                                taskDuration: e.target.value\n                                                                                                            }\n                                                                                                        })),\n                                                                                                placeholder: \"请输入天数\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                lineNumber: 1912,\n                                                                                                columnNumber: 35\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1910,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1897,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"task-config-field task-config-full\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        children: \"任务描述:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1926,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                                        value: courseDetail.taskConfig.taskDescription,\n                                                                                        onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    taskConfig: {\n                                                                                                        ...prev.taskConfig,\n                                                                                                        taskDescription: e.target.value\n                                                                                                    }\n                                                                                                })),\n                                                                                        placeholder: \"请输入任务描述\",\n                                                                                        rows: 4\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1927,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1925,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"task-config-field task-config-full\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        children: [\n                                                                                            \"任务自评项: \",\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"item-number\",\n                                                                                                children: courseDetail.taskConfig.selfAssessmentItems.length\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                lineNumber: 1940,\n                                                                                                columnNumber: 47\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1940,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    courseDetail.taskConfig.selfAssessmentItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"self-assessment-item\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                type: \"text\",\n                                                                                                value: item,\n                                                                                                onChange: (e)=>{\n                                                                                                    const newItems = [\n                                                                                                        ...courseDetail.taskConfig.selfAssessmentItems\n                                                                                                    ];\n                                                                                                    newItems[index] = e.target.value;\n                                                                                                    setCourseDetail((prev)=>({\n                                                                                                            ...prev,\n                                                                                                            taskConfig: {\n                                                                                                                ...prev.taskConfig,\n                                                                                                                selfAssessmentItems: newItems\n                                                                                                            }\n                                                                                                        }));\n                                                                                                },\n                                                                                                placeholder: \"请输入自评项内容\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                lineNumber: 1943,\n                                                                                                columnNumber: 37\n                                                                                            }, undefined)\n                                                                                        }, index, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1942,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        type: \"button\",\n                                                                                        className: \"add-assessment-btn\",\n                                                                                        onClick: ()=>setCourseDetail((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    taskConfig: {\n                                                                                                        ...prev.taskConfig,\n                                                                                                        selfAssessmentItems: [\n                                                                                                            ...prev.taskConfig.selfAssessmentItems,\n                                                                                                            \"\"\n                                                                                                        ]\n                                                                                                    }\n                                                                                                })),\n                                                                                        children: \"+\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1958,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1939,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"task-config-field task-config-full\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        children: \"任务参考作品:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1975,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"reference-works-section\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"works-section\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                    className: \"help-text\",\n                                                                                                    children: \"选择作品作为任务参考资料（可多选）\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                    lineNumber: 1978,\n                                                                                                    columnNumber: 37\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"relative works-scroll-wrapper\",\n                                                                                                    style: {\n                                                                                                        minHeight: \"200px\",\n                                                                                                        cursor: \"grab\",\n                                                                                                        userSelect: \"none\"\n                                                                                                    },\n                                                                                                    children: loadingWorks ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"loading-container\",\n                                                                                                        children: [\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                className: \"loading-spinner\"\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                lineNumber: 1989,\n                                                                                                                columnNumber: 43\n                                                                                                            }, undefined),\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                                children: \"加载中...\"\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                lineNumber: 1990,\n                                                                                                                columnNumber: 43\n                                                                                                            }, undefined)\n                                                                                                        ]\n                                                                                                    }, void 0, true, {\n                                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                        lineNumber: 1988,\n                                                                                                        columnNumber: 41\n                                                                                                    }, undefined) : works.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                                        children: [\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                className: \"works-horizontal-scroll\",\n                                                                                                                children: works.map((work)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                        className: \"work-card \".concat(selectedWorkIds.includes(work.id) ? \"selected\" : \"\"),\n                                                                                                                        onClick: ()=>handleWorkSelect(work.id),\n                                                                                                                        children: [\n                                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                                className: \"work-image\",\n                                                                                                                                children: [\n                                                                                                                                    work.coverImage || work.screenShotImage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                                                                        src: work.coverImage || work.screenShotImage,\n                                                                                                                                        alt: work.title,\n                                                                                                                                        onError: (e)=>{\n                                                                                                                                            const target = e.currentTarget;\n                                                                                                                                            target.style.display = \"none\";\n                                                                                                                                            const nextElement = target.nextElementSibling;\n                                                                                                                                            if (nextElement) {\n                                                                                                                                                nextElement.style.display = \"flex\";\n                                                                                                                                            }\n                                                                                                                                        }\n                                                                                                                                    }, void 0, false, {\n                                                                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                                        lineNumber: 2003,\n                                                                                                                                        columnNumber: 53\n                                                                                                                                    }, undefined) : null,\n                                                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                                        className: \"work-placeholder\",\n                                                                                                                                        style: {\n                                                                                                                                            display: work.coverImage || work.screenShotImage ? \"none\" : \"flex\"\n                                                                                                                                        },\n                                                                                                                                        children: \"作品\"\n                                                                                                                                    }, void 0, false, {\n                                                                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                                        lineNumber: 2016,\n                                                                                                                                        columnNumber: 51\n                                                                                                                                    }, undefined)\n                                                                                                                                ]\n                                                                                                                            }, void 0, true, {\n                                                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                                lineNumber: 2001,\n                                                                                                                                columnNumber: 49\n                                                                                                                            }, undefined),\n                                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                                className: \"work-title\",\n                                                                                                                                children: work.title || work.name || work.workName || \"未命名作品\"\n                                                                                                                            }, void 0, false, {\n                                                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                                lineNumber: 2020,\n                                                                                                                                columnNumber: 49\n                                                                                                                            }, undefined),\n                                                                                                                            selectedWorkIds.includes(work.id) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                                className: \"selected-indicator\",\n                                                                                                                                children: \"✓\"\n                                                                                                                            }, void 0, false, {\n                                                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                                lineNumber: 2022,\n                                                                                                                                columnNumber: 51\n                                                                                                                            }, undefined)\n                                                                                                                        ]\n                                                                                                                    }, work.id, true, {\n                                                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                        lineNumber: 1996,\n                                                                                                                        columnNumber: 47\n                                                                                                                    }, undefined))\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                lineNumber: 1994,\n                                                                                                                columnNumber: 43\n                                                                                                            }, undefined),\n                                                                                                            hasMoreWorks && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                className: \"load-more-container\",\n                                                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                                    className: \"load-more-btn\",\n                                                                                                                    onClick: loadMoreWorks,\n                                                                                                                    disabled: loadingMoreWorks,\n                                                                                                                    children: loadingMoreWorks ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                                                        children: [\n                                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                                className: \"loading-spinner small\"\n                                                                                                                            }, void 0, false, {\n                                                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                                lineNumber: 2038,\n                                                                                                                                columnNumber: 53\n                                                                                                                            }, undefined),\n                                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                                                children: \"加载中...\"\n                                                                                                                            }, void 0, false, {\n                                                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                                lineNumber: 2039,\n                                                                                                                                columnNumber: 53\n                                                                                                                            }, undefined)\n                                                                                                                        ]\n                                                                                                                    }, void 0, true) : \"加载更多\"\n                                                                                                                }, void 0, false, {\n                                                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                    lineNumber: 2031,\n                                                                                                                    columnNumber: 47\n                                                                                                                }, undefined)\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                lineNumber: 2030,\n                                                                                                                columnNumber: 45\n                                                                                                            }, undefined)\n                                                                                                        ]\n                                                                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"empty-placeholder\",\n                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                            className: \"empty-text\",\n                                                                                                            children: \"暂无作品\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                            lineNumber: 2050,\n                                                                                                            columnNumber: 43\n                                                                                                        }, undefined)\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                        lineNumber: 2049,\n                                                                                                        columnNumber: 41\n                                                                                                    }, undefined)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                    lineNumber: 1979,\n                                                                                                    columnNumber: 37\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1977,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1976,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1974,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"task-config-field task-config-full\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        children: \"任务参考资源:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 2060,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"reference-resources-section\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"reference-resources-grid\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                    type: \"button\",\n                                                                                                    className: \"upload-resource-btn\",\n                                                                                                    onClick: ()=>{\n                                                                                                        // 触发文件上传\n                                                                                                        const input = document.createElement(\"input\");\n                                                                                                        input.type = \"file\";\n                                                                                                        input.accept = \".pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx,.txt,.jpg,.png,.gif\";\n                                                                                                        input.onchange = (e)=>{\n                                                                                                            var _e_target_files;\n                                                                                                            const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n                                                                                                            if (file) {\n                                                                                                                setCourseDetail((prev)=>({\n                                                                                                                        ...prev,\n                                                                                                                        taskConfig: {\n                                                                                                                            ...prev.taskConfig,\n                                                                                                                            referenceResources: [\n                                                                                                                                ...prev.taskConfig.referenceResources,\n                                                                                                                                {\n                                                                                                                                    type: \"file\",\n                                                                                                                                    name: file.name\n                                                                                                                                }\n                                                                                                                            ]\n                                                                                                                        }\n                                                                                                                    }));\n                                                                                                            }\n                                                                                                        };\n                                                                                                        input.click();\n                                                                                                    },\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                                            size: 24\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                            lineNumber: 2089,\n                                                                                                            columnNumber: 39\n                                                                                                        }, undefined),\n                                                                                                        \"上传\"\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                    lineNumber: 2063,\n                                                                                                    columnNumber: 37\n                                                                                                }, undefined),\n                                                                                                courseDetail.taskConfig.referenceResources.map((resource, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"reference-resource-item\",\n                                                                                                        children: [\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                                children: resource.name\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                lineNumber: 2094,\n                                                                                                                columnNumber: 41\n                                                                                                            }, undefined),\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                                type: \"button\",\n                                                                                                                className: \"remove-resource-btn\",\n                                                                                                                onClick: ()=>{\n                                                                                                                    const newResources = courseDetail.taskConfig.referenceResources.filter((_, i)=>i !== index);\n                                                                                                                    setCourseDetail((prev)=>({\n                                                                                                                            ...prev,\n                                                                                                                            taskConfig: {\n                                                                                                                                ...prev.taskConfig,\n                                                                                                                                referenceResources: newResources\n                                                                                                                            }\n                                                                                                                        }));\n                                                                                                                },\n                                                                                                                children: \"\\xd7\"\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                lineNumber: 2095,\n                                                                                                                columnNumber: 41\n                                                                                                            }, undefined)\n                                                                                                        ]\n                                                                                                    }, index, true, {\n                                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                        lineNumber: 2093,\n                                                                                                        columnNumber: 39\n                                                                                                    }, undefined))\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 2062,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 2061,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 2059,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1895,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1816,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1815,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 1555,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                lineNumber: 1465,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                        lineNumber: 1411,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"course-list-footer\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"course-list-footer-left\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handlePublish,\n                                    className: \"course-list-btn course-list-btn-publish\",\n                                    disabled: courseList.length === 0 || isPublishingSeries || seriesStatus === 1,\n                                    title: seriesStatus === 1 ? \"系列课程已发布\" : courseList.length === 0 ? \"发布失败：课程系列中至少需要包含一个课程\" : isPublishingSeries ? \"正在发布系列课程...\" : \"发布系列课程\",\n                                    children: seriesStatus === 1 ? \"已发布\" : isPublishingSeries ? \"正在发布...\" : \"发布系列课程\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 2128,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                lineNumber: 2127,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"course-list-footer-right\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleExitEdit,\n                                        className: \"course-list-btn course-list-btn-exit\",\n                                        children: \"退出编辑模式\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 2151,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handlePublishCourse,\n                                        className: \"course-list-btn course-list-btn-publish-course\",\n                                        disabled: !selectedCourseId || ((_courseList_find = courseList.find((c)=>c.id === selectedCourseId)) === null || _courseList_find === void 0 ? void 0 : _courseList_find.status) === 1 || isPublishing,\n                                        title: !selectedCourseId ? \"请先选择要发布的课程\" : ((_courseList_find1 = courseList.find((c)=>c.id === selectedCourseId)) === null || _courseList_find1 === void 0 ? void 0 : _courseList_find1.status) === 1 ? \"该课程已发布\" : isPublishing ? \"正在发布课程...\" : \"发布选中的课程\",\n                                        children: isPublishing ? \"正在发布...\" : \"发布课程\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 2154,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleSave,\n                                        className: \"course-list-btn course-list-btn-save\",\n                                        disabled: uploadingFiles.size > 0 || isCreating || courseList.length === 0,\n                                        title: courseList.length === 0 ? \"请先添加课程内容\" : uploadingFiles.size > 0 ? \"有文件正在上传中，请等待上传完成\" : isCreating ? courseList.some((c)=>c.id > 1000000) ? \"正在创建课程...\" : \"正在保存课程...\" : courseList.some((c)=>c.id > 1000000) ? \"创建课程\" : \"保存课程\",\n                                        children: uploadingFiles.size > 0 ? \"上传中...\" : isCreating ? courseList.some((c)=>c.id > 1000000) ? \"正在创建...\" : \"正在保存...\" : courseList.some((c)=>c.id > 1000000) ? \"创建课程\" : \"保存课程\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 2170,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                lineNumber: 2150,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                        lineNumber: 2126,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                lineNumber: 1388,\n                columnNumber: 7\n            }, undefined),\n            deleteConfirmVisible && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"modal-overlay\",\n                onClick: cancelDelete,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"delete-confirm-modal\",\n                    onClick: (e)=>e.stopPropagation(),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"delete-confirm-header\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    children: \"确认删除\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 2200,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: cancelDelete,\n                                    className: \"close-btn\",\n                                    disabled: isDeleting,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 2206,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 2201,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                            lineNumber: 2199,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"delete-confirm-content\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: isDeleting ? \"正在删除课程，请稍候...\" : \"确定要删除这个课程吗？删除后无法恢复。\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 2210,\n                                    columnNumber: 15\n                                }, undefined),\n                                isDeleting && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"delete-loading\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"loading-spinner\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 2218,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 2217,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                            lineNumber: 2209,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"delete-confirm-footer\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: cancelDelete,\n                                    className: \"cancel-btn\",\n                                    disabled: isDeleting,\n                                    children: \"取消\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 2223,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: confirmDeleteCourse,\n                                    className: \"confirm-btn\",\n                                    disabled: isDeleting,\n                                    children: isDeleting ? \"正在删除...\" : \"确认删除\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 2230,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                            lineNumber: 2222,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                    lineNumber: 2198,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                lineNumber: 2197,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TemplatePickerModal__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                isOpen: isTemplatePickerOpen,\n                onClose: ()=>setIsTemplatePickerOpen(false),\n                onTemplateSelect: handleTemplateSelect\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                lineNumber: 2243,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n        lineNumber: 1387,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CourseListEditModal, \"KEW5Vi2F/lNLILkQPYfx5TLVpM4=\");\n_c = CourseListEditModal;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CourseListEditModal);\nvar _c;\n$RefreshReg$(_c, \"CourseListEditModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC93b3JrYmVuY2gvY29tcG9uZW50cy9Db3Vyc2VMaXN0RWRpdE1vZGFsLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUVtRDtBQUNrQjtBQUNIO0FBQ3JCO0FBQ0E7QUFDRjtBQUNiO0FBQzhDO0FBQ3BCO0FBQ3JCO0FBQ0E7QUFpRW5DLFdBQVc7QUFDWCxNQUFNZSxvQkFBb0IsT0FBT0M7SUFDL0JDLFFBQVFDLEdBQUcsQ0FBQyxtQ0FBeUJGO0lBQ3JDQyxRQUFRQyxHQUFHLENBQUM7SUFFWixNQUFNQyxXQUFXLE1BQU1WLHNEQUFTQSxDQUFDVyxlQUFlLENBQUNKO0lBQ2pEQyxRQUFRQyxHQUFHLENBQUMsMkJBQWlCQztJQUM3QixPQUFPQSxTQUFTRSxJQUFJO0FBQ3RCO0FBRUEsa0NBQWtDO0FBQ2xDLE1BQU1DLGtCQUFrQixPQUFPTjtJQUM3QkMsUUFBUUMsR0FBRyxDQUFDLG1DQUF5QkY7SUFDckNDLFFBQVFDLEdBQUcsQ0FBQztJQUVaLHFCQUFxQjtJQUNyQixNQUFNQyxXQUFXLE1BQU1WLHNEQUFTQSxDQUFDYywwQkFBMEIsQ0FBQ1AsVUFBVTtRQUNwRVEsTUFBTTtRQUNOQyxVQUFVO0lBRVo7SUFFQVIsUUFBUUMsR0FBRyxDQUFDLHVCQUFhQztJQUN6QixPQUFPQSxTQUFTRSxJQUFJO0FBQ3RCO0FBRUEsU0FBUztBQUNULE1BQU1LLG9CQUFvQixPQUFPVixVQUFrQlc7SUFDakQsT0FBTyxNQUFNbEIsc0RBQVNBLENBQUNtQiwwQkFBMEIsQ0FBQ1osVUFBVVc7QUFDOUQ7QUFFQSxTQUFTO0FBQ1QsTUFBTUUsa0JBQWtCO0lBQ3RCLElBQUk7UUFDRlosUUFBUUMsR0FBRyxDQUFDO1FBQ1osTUFBTVksU0FBUyxNQUFNckIsc0RBQVNBLENBQUNzQixhQUFhLENBQUM7WUFDM0NQLE1BQU07WUFDTkMsVUFBVTtZQUNWTyxRQUFRLEVBQUUsV0FBVztRQUN2QjtRQUNBZixRQUFRQyxHQUFHLENBQUMsOENBQW9DWTtRQUNoRCxPQUFPQTtJQUNULEVBQUUsT0FBT0csT0FBTztRQUNkaEIsUUFBUWdCLEtBQUssQ0FBQyw4Q0FBb0NBO1FBQ2xELE1BQU1BO0lBQ1I7QUFDRjtBQUVBLE1BQU1DLHNCQUEwRDtRQUFDLEVBQy9EQyxTQUFTLEVBQ1RDLE9BQU8sRUFDUEMsTUFBTSxFQUNOQyxXQUFXLEVBQ1hDLGdCQUFnQixFQUNoQnZCLFdBQVcsSUFBSSwyQkFBMkI7SUFBNUIsRUFDZjtRQXM1Q21Ed0Isb0JBRVFBLHFCQWtDTEEscUJBV01BLHFCQTZCbENDLG1DQUFBQSw2QkErQ0FBLHNDQUFBQSw4QkF1ZGtCQyxrQkFJekJBOztJQTMrRGxCLE1BQU1DLGVBQWU5QiwwRkFBZUE7SUFFcEMsTUFBTSxDQUFDNkIsWUFBWUUsY0FBYyxHQUFHM0MsK0NBQVFBLENBQWUsRUFBRTtJQUM3RCxNQUFNLENBQUM0QyxTQUFTQyxXQUFXLEdBQUc3QywrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUM4QyxnQkFBZ0JDLGtCQUFrQixHQUFHL0MsK0NBQVFBLENBQWlDO0lBQ3JGLE1BQU0sQ0FBQ2dELGtCQUFrQkMsb0JBQW9CLEdBQUdqRCwrQ0FBUUEsQ0FBZ0I7SUFDeEUsTUFBTSxDQUFDa0QsZ0JBQWdCQyxrQkFBa0IsR0FBR25ELCtDQUFRQSxDQUFjLElBQUlvRDtJQUN0RSxNQUFNLENBQUNDLGNBQWNDLGdCQUFnQixHQUFHdEQsK0NBQVFBLENBQUM7SUFDakQsTUFBTSxDQUFDdUQsWUFBWUMsY0FBYyxHQUFHeEQsK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDeUQsb0JBQW9CQyxzQkFBc0IsR0FBRzFELCtDQUFRQSxDQUFDO0lBQzdELE1BQU0sQ0FBQzJELGNBQWNDLGdCQUFnQixHQUFHNUQsK0NBQVFBLENBQVMsSUFBSSxtQkFBbUI7SUFFaEYsV0FBVztJQUNYLE1BQU0sQ0FBQzZELHNCQUFzQkMsd0JBQXdCLEdBQUc5RCwrQ0FBUUEsQ0FBQztJQUNqRSxNQUFNLENBQUMrRCxnQkFBZ0JDLGtCQUFrQixHQUFHaEUsK0NBQVFBLENBQWdCO0lBQ3BFLE1BQU0sQ0FBQ2lFLFlBQVlDLGNBQWMsR0FBR2xFLCtDQUFRQSxDQUFDO0lBRTdDLFdBQVc7SUFDWCxNQUFNLENBQUNtRSxzQkFBc0JDLHdCQUF3QixHQUFHcEUsK0NBQVFBLENBQUM7SUFFakUsTUFBTSxDQUFDcUUsY0FBY0MsZ0JBQWdCLEdBQUd0RSwrQ0FBUUEsQ0FBQ3FDO0lBQ2pELE1BQU0sQ0FBQ2tDLGFBQWFDLGVBQWUsR0FBR3hFLCtDQUFRQSxDQUFDO0lBQy9DLE1BQU0sQ0FBQ3lFLGtCQUFrQkMsb0JBQW9CLEdBQUcxRSwrQ0FBUUEsQ0FBQztJQUN6RCxNQUFNLENBQUMyRSxnQkFBZ0JDLGtCQUFrQixHQUFHNUUsK0NBQVFBLENBQUM7SUFFckQsU0FBUztJQUNULE1BQU0sQ0FBQzZFLE9BQU9DLFNBQVMsR0FBRzlFLCtDQUFRQSxDQUFRLEVBQUU7SUFDNUMsTUFBTSxDQUFDK0UsY0FBY0MsZ0JBQWdCLEdBQUdoRiwrQ0FBUUEsQ0FBQztJQUNqRCxNQUFNLENBQUNpRixpQkFBaUJDLG1CQUFtQixHQUFHbEYsK0NBQVFBLENBQVcsRUFBRTtJQUNuRSxNQUFNLENBQUNtRixXQUFXQyxhQUFhLEdBQUdwRiwrQ0FBUUEsQ0FBQztJQUMzQyxNQUFNLENBQUNxRixjQUFjQyxnQkFBZ0IsR0FBR3RGLCtDQUFRQSxDQUFDO0lBQ2pELE1BQU0sQ0FBQ3VGLGtCQUFrQkMsb0JBQW9CLEdBQUd4RiwrQ0FBUUEsQ0FBQztJQUV6RCxXQUFXO0lBQ1gsTUFBTSxDQUFDeUYsWUFBWUMsY0FBYyxHQUFHMUYsK0NBQVFBLENBQWMsRUFBRTtJQUM1RCxNQUFNLENBQUMyRixjQUFjQyxnQkFBZ0IsR0FBRzVGLCtDQUFRQSxDQUFXLEVBQUU7SUFDN0QsTUFBTSxDQUFDNkYsYUFBYUMsZUFBZSxHQUFHOUYsK0NBQVFBLENBQUM7SUFFL0MsV0FBVztJQUNYLE1BQU0sQ0FBQ3dDLGNBQWN1RCxnQkFBZ0IsR0FBRy9GLCtDQUFRQSxDQUFDO1FBQy9DZ0csT0FBTztRQUNQQyxhQUFhO1FBQ2JDLFlBQVk7UUFDWkMsVUFBVTtRQUNWQyxXQUFXO1FBQ1hDLGdCQUFnQjtRQUNoQkMsZUFBZTtRQUNmQyxnQkFBZ0I7UUFDaEJDLHFCQUFxQjtRQUNyQkMsbUJBQW1CLEVBQUU7UUFDckIsbUJBQW1CO1FBQ25CQyxjQUFjLEVBQUU7UUFDaEIsb0JBQW9CO1FBQ3BCQyxlQUFlO1lBQ2JDLFVBQVU7WUFDVkMsYUFBYTtZQUNiQyxVQUFVO1lBQ1ZDLE9BQU87Z0JBQUVDLEtBQUs7Z0JBQUlDLE1BQU07WUFBRztZQUMzQkMsVUFBVTtnQkFBRUYsS0FBSztnQkFBSUMsTUFBTTtZQUFHO1lBQzlCRSxPQUFPO2dCQUFFSCxLQUFLO2dCQUFJQyxNQUFNO1lBQUc7UUFDN0I7UUFDQUcsZUFBZTtZQUNiQyxPQUFPO1lBQ1BDLFNBQVM7UUFDWDtRQUNBQyxjQUFjO1FBQ2RDLHVCQUF1QjtRQUN2QkMsb0JBQW9CO1FBQ3BCQyxrQkFBa0I7UUFDbEJDLHFCQUFxQjtRQUNyQkMsZ0JBQWdCO1FBQ2hCQyxjQUFjO1FBQ2RDLHFCQUFxQjtRQUNyQkMsd0JBQXdCO1lBQ3RCQyxhQUFhO1lBQ2JDLFdBQVc7WUFDWEMsWUFBWTtRQUNkO1FBQ0FDLG9CQUFvQjtRQUNwQkMsNEJBQTRCO1lBQzFCQyxXQUFXO1lBQ1hDLFNBQVM7UUFDWDtRQUNBQyx1QkFBdUIsRUFBRTtRQUN6QixXQUFXO1FBQ1hDLFlBQVk7WUFDVkMsVUFBVTtZQUNWQyxjQUFjO1lBQ2RDLGlCQUFpQjtZQUNqQkMscUJBQXFCO2dCQUFDO2FBQUc7WUFDekJDLGdCQUFnQixFQUFFO1lBQ2xCQyxvQkFBb0IsRUFBRTtRQUN4QjtJQUNGO0lBRUEsV0FBVztJQUNYN0ksZ0RBQVNBLENBQUM7UUFDUixJQUFJaUMsYUFBYW5CLFVBQVU7WUFDekIsV0FBVztZQUNYLE1BQU1nSSxRQUFRQyxhQUFhQyxPQUFPLENBQUM7WUFDbkNqSSxRQUFRQyxHQUFHLENBQUMsZ0NBQXNCLENBQUMsQ0FBQzhIO1lBQ3BDL0gsUUFBUUMsR0FBRyxDQUFDLDBCQUFnQkY7WUFFNUIsSUFBSSxDQUFDZ0ksT0FBTztnQkFDVi9ILFFBQVFnQixLQUFLLENBQUM7Z0JBQ2QsY0FBYztnQkFDZFcsY0FBYyxFQUFFO2dCQUNoQkUsV0FBVztnQkFDWDtZQUNGO1lBRUFxRztZQUNBQztZQUNBQztZQUNBQyxjQUFjLE9BQU8sT0FBTztRQUM5QjtJQUNGLEdBQUc7UUFBQ25IO1FBQVduQjtLQUFTO0lBRXhCLE1BQU1tSSxpQkFBaUI7UUFDckIsSUFBSTtZQUNGckcsV0FBVztZQUNYN0IsUUFBUUMsR0FBRyxDQUFDLG1DQUF5QkY7WUFFckMsTUFBTUcsV0FBVyxNQUFNRyxnQkFBZ0JOO1lBQ3ZDQyxRQUFRQyxHQUFHLENBQUMsdUJBQWFDO1lBRXpCLElBQUlBLFNBQVNvSSxJQUFJLEtBQUssS0FBSztnQkFDekJ0SSxRQUFRQyxHQUFHLENBQUMsYUFBYUMsU0FBU0UsSUFBSTtnQkFDdEMsTUFBTW1JLFVBQVVySSxTQUFTRSxJQUFJLENBQUNvSSxJQUFJLElBQUksRUFBRTtnQkFDeEN4SSxRQUFRQyxHQUFHLENBQUMsY0FBY3NJO2dCQUMxQnZJLFFBQVFDLEdBQUcsQ0FBQyx3QkFBY3NJLFFBQVFFLEdBQUcsQ0FBQyxDQUFDQyxJQUFZO3dCQUNqREMsSUFBSUQsRUFBRUMsRUFBRTt3QkFDUkMsTUFBTSxPQUFPRixFQUFFQyxFQUFFO3dCQUNqQjNELE9BQU8wRCxFQUFFMUQsS0FBSzt3QkFDZGpFLFFBQVEySCxFQUFFM0gsTUFBTTtvQkFDbEI7Z0JBQ0FmLFFBQVFDLEdBQUcsQ0FBQyx3QkFBYztvQkFDeEI0SSxPQUFPTixRQUFRTyxNQUFNO29CQUNyQkMsT0FBT1IsUUFBUVMsTUFBTSxDQUFDLENBQUNOLElBQVdBLEVBQUUzSCxNQUFNLEtBQUssR0FBRytILE1BQU07b0JBQ3hERyxXQUFXVixRQUFRUyxNQUFNLENBQUMsQ0FBQ04sSUFBV0EsRUFBRTNILE1BQU0sS0FBSyxHQUFHK0gsTUFBTTtnQkFDOUQ7Z0JBQ0FuSCxjQUFjNEc7WUFDaEIsT0FBTztnQkFDTHZJLFFBQVFnQixLQUFLLENBQUMsY0FBY2Q7Z0JBQzVCeUIsY0FBYyxFQUFFO1lBQ2xCO1FBQ0YsRUFBRSxPQUFPWCxPQUFZO2dCQUlmQSxpQkFFT0Esa0JBRUFBO1lBUFhoQixRQUFRZ0IsS0FBSyxDQUFDLGVBQWVBO1lBRTdCLFlBQVk7WUFDWixJQUFJQSxFQUFBQSxrQkFBQUEsTUFBTWQsUUFBUSxjQUFkYyxzQ0FBQUEsZ0JBQWdCRCxNQUFNLE1BQUssS0FBSztnQkFDbENmLFFBQVFnQixLQUFLLENBQUM7WUFDaEIsT0FBTyxJQUFJQSxFQUFBQSxtQkFBQUEsTUFBTWQsUUFBUSxjQUFkYyx1Q0FBQUEsaUJBQWdCRCxNQUFNLE1BQUssS0FBSztnQkFDekNmLFFBQVFnQixLQUFLLENBQUM7WUFDaEIsT0FBTyxJQUFJQSxFQUFBQSxtQkFBQUEsTUFBTWQsUUFBUSxjQUFkYyx1Q0FBQUEsaUJBQWdCRCxNQUFNLE1BQUssS0FBSztnQkFDekNmLFFBQVFnQixLQUFLLENBQUMsa0NBQXdCakI7WUFDeEMsT0FBTztnQkFDTEMsUUFBUWdCLEtBQUssQ0FBQyxzQkFBWUEsTUFBTWtJLE9BQU87WUFDekM7WUFFQXZILGNBQWMsRUFBRTtRQUNsQixTQUFVO1lBQ1JFLFdBQVc7UUFDYjtJQUNGO0lBRUEsV0FBVztJQUNYLE1BQU11RyxtQkFBbUI7UUFDdkIsSUFBSTtZQUNGcEksUUFBUUMsR0FBRyxDQUFDLHFDQUEyQkY7WUFFdkMsTUFBTUcsV0FBVyxNQUFNSixrQkFBa0JDO1lBQ3pDQyxRQUFRQyxHQUFHLENBQUMsd0JBQWNDO1lBRTFCLElBQUlBLFNBQVNvSSxJQUFJLEtBQUssT0FBT3BJLFNBQVNFLElBQUksRUFBRTtnQkFDMUMsTUFBTStJLGFBQWFqSixTQUFTRSxJQUFJO2dCQUNoQ0osUUFBUUMsR0FBRyxDQUFDLGFBQWFrSjtnQkFDekJ2RyxnQkFBZ0J1RyxXQUFXcEksTUFBTSxJQUFJO2dCQUNyQ2YsUUFBUUMsR0FBRyxDQUFDLHdCQUFja0osV0FBV3BJLE1BQU0sRUFBRTtZQUMvQyxPQUFPO2dCQUNMZixRQUFRZ0IsS0FBSyxDQUFDLGVBQWVkLFNBQVNnSixPQUFPO1lBQy9DO1FBQ0YsRUFBRSxPQUFPbEksT0FBWTtZQUNuQmhCLFFBQVFnQixLQUFLLENBQUMsZUFBZUE7UUFDL0I7SUFDRjtJQUVBLGlCQUFpQjtJQUNqQixNQUFNcUgsZ0JBQWdCO1lBQU9lLHlFQUFRO1FBQ25DLElBQUk7Z0JBaUJFbEo7WUFoQkosSUFBSWtKLE9BQU87Z0JBQ1RwRixnQkFBZ0I7Z0JBQ2hCRixTQUFTLEVBQUU7Z0JBQ1hNLGFBQWE7Z0JBQ2JFLGdCQUFnQjtZQUNsQjtZQUVBLHNCQUFzQjtZQUN0QixNQUFNK0UsU0FBU3JCLGFBQWFDLE9BQU8sQ0FBQyxhQUFhLFFBQVEsV0FBVztZQUVwRSxNQUFNcUIsY0FBY0YsUUFBUSxJQUFJakY7WUFDaEMsTUFBTWpFLFdBQVcsTUFBTVIsb0RBQVFBLENBQUM2SixlQUFlLENBQUNDLE9BQU9ILFNBQVNDLGFBQWEsSUFBSSxTQUFTO1lBRTFGLGNBQWM7WUFDZCxJQUFJRyxZQUFtQixFQUFFO1lBRXpCLElBQUl2SixFQUFBQSxpQkFBQUEsU0FBU0UsSUFBSSxjQUFiRixxQ0FBQUEsZUFBZW9JLElBQUksTUFBSyxLQUFLO29CQUUzQnBJLHFCQUFBQSxpQkFJbUJBO2dCQUx2QiwrQkFBK0I7Z0JBQy9CLEtBQUlBLGtCQUFBQSxTQUFTRSxJQUFJLGNBQWJGLHVDQUFBQSxzQkFBQUEsZ0JBQWVFLElBQUksY0FBbkJGLDBDQUFBQSxvQkFBcUJzSSxJQUFJLEVBQUU7b0JBQzdCaUIsWUFBWXZKLFNBQVNFLElBQUksQ0FBQ0EsSUFBSSxDQUFDb0ksSUFBSTtnQkFDckMsT0FFSyxJQUFJa0IsTUFBTUMsT0FBTyxFQUFDekosa0JBQUFBLFNBQVNFLElBQUksY0FBYkYsc0NBQUFBLGdCQUFlRSxJQUFJLEdBQUc7b0JBQzNDcUosWUFBWXZKLFNBQVNFLElBQUksQ0FBQ0EsSUFBSTtnQkFDaEMsT0FFSyxJQUFJc0osTUFBTUMsT0FBTyxDQUFDekosU0FBU0UsSUFBSSxHQUFHO29CQUNyQ3FKLFlBQVl2SixTQUFTRSxJQUFJO2dCQUMzQjtZQUNGLE9BRUssSUFBSXNKLE1BQU1DLE9BQU8sQ0FBQ3pKLFdBQVc7Z0JBQ2hDdUosWUFBWXZKO1lBQ2Q7WUFFQSxJQUFJa0osT0FBTztnQkFDVHRGLFNBQVMyRjtZQUNYLE9BQU87Z0JBQ0wzRixTQUFTOEYsQ0FBQUEsT0FBUTsyQkFBSUE7MkJBQVNIO3FCQUFVO1lBQzFDO1lBRUEsYUFBYTtZQUNibkYsZ0JBQWdCbUYsVUFBVVgsTUFBTSxLQUFLO1lBRXJDLElBQUksQ0FBQ00sT0FBTztnQkFDVmhGLGFBQWF3RixDQUFBQSxPQUFRQSxPQUFPO1lBQzlCO1FBQ0YsRUFBRSxPQUFPNUksT0FBTztZQUNkaEIsUUFBUWdCLEtBQUssQ0FBQyxhQUFhQTtZQUMzQixJQUFJb0ksT0FBTztnQkFDVHRGLFNBQVMsRUFBRTtZQUNiO1FBQ0YsU0FBVTtZQUNSRSxnQkFBZ0I7UUFDbEI7SUFDRjtJQUVBLFNBQVM7SUFDVCxNQUFNNkYsZ0JBQWdCO1FBQ3BCLElBQUl0RixvQkFBb0IsQ0FBQ0YsY0FBYztRQUV2QyxJQUFJO2dCQVdFbkU7WUFWSnNFLG9CQUFvQjtZQUVwQixzQkFBc0I7WUFDdEIsTUFBTTZFLFNBQVNyQixhQUFhQyxPQUFPLENBQUMsYUFBYSxRQUFRLFdBQVc7WUFFcEUsTUFBTS9ILFdBQVcsTUFBTVIsb0RBQVFBLENBQUM2SixlQUFlLENBQUNDLE9BQU9ILFNBQVNsRixXQUFXO1lBRTNFLGNBQWM7WUFDZCxJQUFJc0YsWUFBbUIsRUFBRTtZQUV6QixJQUFJdkosRUFBQUEsaUJBQUFBLFNBQVNFLElBQUksY0FBYkYscUNBQUFBLGVBQWVvSSxJQUFJLE1BQUssS0FBSztvQkFFM0JwSSxxQkFBQUEsaUJBSW1CQTtnQkFMdkIsK0JBQStCO2dCQUMvQixLQUFJQSxrQkFBQUEsU0FBU0UsSUFBSSxjQUFiRix1Q0FBQUEsc0JBQUFBLGdCQUFlRSxJQUFJLGNBQW5CRiwwQ0FBQUEsb0JBQXFCc0ksSUFBSSxFQUFFO29CQUM3QmlCLFlBQVl2SixTQUFTRSxJQUFJLENBQUNBLElBQUksQ0FBQ29JLElBQUk7Z0JBQ3JDLE9BRUssSUFBSWtCLE1BQU1DLE9BQU8sRUFBQ3pKLGtCQUFBQSxTQUFTRSxJQUFJLGNBQWJGLHNDQUFBQSxnQkFBZUUsSUFBSSxHQUFHO29CQUMzQ3FKLFlBQVl2SixTQUFTRSxJQUFJLENBQUNBLElBQUk7Z0JBQ2hDLE9BRUssSUFBSXNKLE1BQU1DLE9BQU8sQ0FBQ3pKLFNBQVNFLElBQUksR0FBRztvQkFDckNxSixZQUFZdkosU0FBU0UsSUFBSTtnQkFDM0I7WUFDRixPQUVLLElBQUlzSixNQUFNQyxPQUFPLENBQUN6SixXQUFXO2dCQUNoQ3VKLFlBQVl2SjtZQUNkO1lBRUE0RCxTQUFTOEYsQ0FBQUEsT0FBUTt1QkFBSUE7dUJBQVNIO2lCQUFVO1lBRXhDLGFBQWE7WUFDYm5GLGdCQUFnQm1GLFVBQVVYLE1BQU0sS0FBSztZQUNyQzFFLGFBQWF3RixDQUFBQSxPQUFRQSxPQUFPO1FBQzlCLEVBQUUsT0FBTzVJLE9BQU87WUFDZGhCLFFBQVFnQixLQUFLLENBQUMsYUFBYUE7UUFDN0IsU0FBVTtZQUNSd0Qsb0JBQW9CO1FBQ3RCO0lBQ0Y7SUFFQSxTQUFTO0lBQ1QsTUFBTXNGLG1CQUFtQixDQUFDQztRQUN4QixJQUFJOUYsZ0JBQWdCK0YsUUFBUSxDQUFDRCxTQUFTO1lBQ3BDLE9BQU87WUFDUDdGLG1CQUFtQjBGLENBQUFBLE9BQVFBLEtBQUtaLE1BQU0sQ0FBQ0wsQ0FBQUEsS0FBTUEsT0FBT29CO1FBQ3RELE9BQU87WUFDTCxLQUFLO1lBQ0w3RixtQkFBbUIwRixDQUFBQSxPQUFRO3VCQUFJQTtvQkFBTUc7aUJBQU87UUFDOUM7SUFDRjtJQUVBLFNBQVM7SUFDVCxNQUFNNUIsaUJBQWlCO1FBQ3JCLElBQUk7WUFDRnJELGVBQWU7WUFDZjlFLFFBQVFDLEdBQUcsQ0FBQztZQUlaLE1BQU1DLFdBQVcsTUFBTVU7WUFDdkJaLFFBQVFDLEdBQUcsQ0FBQywyQkFBaUJDO1lBRTdCLFNBQVM7WUFDVCxJQUFJQSxZQUFZQSxTQUFTRSxJQUFJLEVBQUU7Z0JBQzdCSixRQUFRQyxHQUFHLENBQUMsc0JBQVlDLFNBQVNFLElBQUk7Z0JBRXJDLElBQUk2SixPQUFvQixFQUFFO2dCQUUxQixnREFBZ0Q7Z0JBQ2hELElBQUkvSixTQUFTRSxJQUFJLENBQUNvSSxJQUFJLElBQUlrQixNQUFNQyxPQUFPLENBQUN6SixTQUFTRSxJQUFJLENBQUNvSSxJQUFJLEdBQUc7b0JBQzNEeUIsT0FBTy9KLFNBQVNFLElBQUksQ0FBQ29JLElBQUk7b0JBQ3pCeEksUUFBUUMsR0FBRyxDQUFDLHdCQUF3QmdLLEtBQUtuQixNQUFNLEVBQUU7Z0JBQ25ELE9BRUssSUFBSVksTUFBTUMsT0FBTyxDQUFDekosU0FBU0UsSUFBSSxHQUFHO29CQUNyQzZKLE9BQU8vSixTQUFTRSxJQUFJO29CQUNwQkosUUFBUUMsR0FBRyxDQUFDLHFCQUFxQmdLLEtBQUtuQixNQUFNLEVBQUU7Z0JBQ2hELE9BRUssSUFBSTVJLFNBQVNFLElBQUksQ0FBQ0EsSUFBSSxJQUFJRixTQUFTRSxJQUFJLENBQUNBLElBQUksQ0FBQ29JLElBQUksSUFBSWtCLE1BQU1DLE9BQU8sQ0FBQ3pKLFNBQVNFLElBQUksQ0FBQ0EsSUFBSSxDQUFDb0ksSUFBSSxHQUFHO29CQUNoR3lCLE9BQU8vSixTQUFTRSxJQUFJLENBQUNBLElBQUksQ0FBQ29JLElBQUk7b0JBQzlCeEksUUFBUUMsR0FBRyxDQUFDLDZCQUE2QmdLLEtBQUtuQixNQUFNLEVBQUU7Z0JBQ3hEO2dCQUVBLFdBQVc7Z0JBQ1g5SSxRQUFRQyxHQUFHLENBQUMsd0JBQWNnSztnQkFDMUJqSyxRQUFRQyxHQUFHLENBQUM7Z0JBQ1pnSyxLQUFLQyxPQUFPLENBQUMsQ0FBQ0MsS0FBS0M7d0JBT0REO29CQU5oQm5LLFFBQVFDLEdBQUcsQ0FBQyxLQUFXLE9BQU5tSyxPQUFNLE1BQUk7d0JBQ3pCRDt3QkFDQUUsUUFBUSxDQUFDLENBQUNGO3dCQUNWRyxRQUFRLFFBQU9ILGdCQUFBQSwwQkFBQUEsSUFBS3hCLEVBQUU7d0JBQ3RCNEIsVUFBVSxRQUFPSixnQkFBQUEsMEJBQUFBLElBQUtsRSxJQUFJO3dCQUMxQnVFLFNBQVMsRUFBRUwsZ0JBQUFBLDBCQUFBQSxJQUFLbEUsSUFBSTt3QkFDcEJ3RSxjQUFjTixDQUFBQSxnQkFBQUEsMkJBQUFBLFlBQUFBLElBQUtsRSxJQUFJLGNBQVRrRSxnQ0FBQUEsVUFBV08sSUFBSSxRQUFPO29CQUN0QztnQkFDRjtnQkFFQSxNQUFNQyxZQUFZVixLQUFLakIsTUFBTSxDQUFDbUIsQ0FBQUE7b0JBQzVCLE1BQU1TLFVBQVVULE9BQ2QsT0FBT0EsSUFBSXhCLEVBQUUsS0FBSyxZQUNsQixPQUFPd0IsSUFBSWxFLElBQUksS0FBSyxZQUNwQmtFLElBQUlsRSxJQUFJLENBQUN5RSxJQUFJLE9BQU87b0JBRXRCLElBQUksQ0FBQ0UsU0FBUzt3QkFDWjVLLFFBQVFDLEdBQUcsQ0FBQyxXQUFXa0ssS0FBSzs0QkFDMUJFLFFBQVEsQ0FBQyxDQUFDRjs0QkFDVkcsUUFBUSxRQUFPSCxnQkFBQUEsMEJBQUFBLElBQUt4QixFQUFFOzRCQUN0QjRCLFVBQVUsUUFBT0osZ0JBQUFBLDBCQUFBQSxJQUFLbEUsSUFBSTs0QkFDMUJ1RSxTQUFTLEVBQUVMLGdCQUFBQSwwQkFBQUEsSUFBS2xFLElBQUk7d0JBQ3RCO29CQUNGO29CQUVBLE9BQU8yRTtnQkFDVDtnQkFFQTVLLFFBQVFDLEdBQUcsQ0FBQyxhQUFhMEssVUFBVTdCLE1BQU07Z0JBQ3pDOUksUUFBUUMsR0FBRyxDQUFDLGFBQWEwSztnQkFFekIsSUFBSUEsVUFBVTdCLE1BQU0sR0FBRyxHQUFHO29CQUN4QnBFLGNBQWNpRztvQkFDZDNLLFFBQVFDLEdBQUcsQ0FBQztvQkFDWjtnQkFDRixPQUFPO29CQUNMRCxRQUFRNkssSUFBSSxDQUFDO2dCQUNmO1lBQ0YsT0FBTztnQkFDTDdLLFFBQVE2SyxJQUFJLENBQUMsa0JBQWtCM0s7WUFDakM7WUFFQSxpQkFBaUI7WUFDakJGLFFBQVFDLEdBQUcsQ0FBQztZQUNaeUUsY0FBYyxFQUFFO1FBRWxCLEVBQUUsT0FBTzFELE9BQVk7Z0JBSVRBLGlCQUNJQSxrQkFDTkE7WUFMUmhCLFFBQVFnQixLQUFLLENBQUMsZUFBZUE7WUFDN0JoQixRQUFRZ0IsS0FBSyxDQUFDLFdBQVc7Z0JBQ3ZCa0ksU0FBU2xJLE1BQU1rSSxPQUFPO2dCQUN0Qm5JLE1BQU0sR0FBRUMsa0JBQUFBLE1BQU1kLFFBQVEsY0FBZGMsc0NBQUFBLGdCQUFnQkQsTUFBTTtnQkFDOUIrSixVQUFVLEdBQUU5SixtQkFBQUEsTUFBTWQsUUFBUSxjQUFkYyx1Q0FBQUEsaUJBQWdCOEosVUFBVTtnQkFDdEMxSyxJQUFJLEdBQUVZLG1CQUFBQSxNQUFNZCxRQUFRLGNBQWRjLHVDQUFBQSxpQkFBZ0JaLElBQUk7WUFDNUI7WUFFQSxhQUFhO1lBQ2JzRSxjQUFjLEVBQUU7UUFDbEIsU0FBVTtZQUNSSSxlQUFlO1FBQ2pCO0lBQ0Y7SUFFQSxRQUFRO0lBQ1IsTUFBTWlHLGVBQWU7UUFDbkIsTUFBTUMsWUFBd0I7WUFDNUJyQyxJQUFJc0MsS0FBS0MsR0FBRztZQUNabkwsVUFBVUE7WUFDVmlGLE9BQU8sSUFBMEIsT0FBdEJ2RCxXQUFXcUgsTUFBTSxHQUFHLEdBQUU7WUFDakM3RCxhQUFhO1lBQ2JDLFlBQVk7WUFDWmlHLFlBQVkxSixXQUFXcUgsTUFBTSxHQUFHO1lBQ2hDL0gsUUFBUTtZQUNScUssYUFBYTtZQUNieEYsVUFBVTtZQUNWQyxhQUFhO1lBQ2JDLFVBQVU7WUFDVnVGLGVBQWU7WUFDZkMsb0JBQW9CO1lBQ3BCbEcsV0FBVztZQUNYbUcsb0JBQW9CO1lBQ3BCQyxnQkFBZ0I7WUFDaEJDLFdBQVcsSUFBSVIsT0FBT1MsV0FBVztZQUNqQ0MsV0FBVyxJQUFJVixPQUFPUyxXQUFXO1FBQ25DO1FBQ0EvSixjQUFjO2VBQUlGO1lBQVl1SjtTQUFVO1FBQ3hDLGFBQWE7UUFDYlksZ0JBQWdCWixVQUFVckMsRUFBRTtJQUM5QjtJQUVBLFdBQVc7SUFDWCxNQUFNa0Qsb0JBQW9CLENBQUNsRDtRQUN6QjNGLGtCQUFrQjJGO1FBQ2xCN0Ysd0JBQXdCO0lBQzFCO0lBRUEsU0FBUztJQUNULE1BQU1nSixzQkFBc0I7UUFDMUIsSUFBSSxDQUFDL0ksZ0JBQWdCO1FBRXJCLElBQUk7WUFDRkcsY0FBYztZQUVkLFVBQVU7WUFDVixNQUFNM0QsMkVBQW1CQSxDQUFDd00sWUFBWSxDQUFDaEo7WUFFdkMsV0FBVztZQUNYcEIsY0FBY0YsV0FBV3VILE1BQU0sQ0FBQ2dELENBQUFBLFNBQVVBLE9BQU9yRCxFQUFFLEtBQUs1RjtZQUV4RCx1QkFBdUI7WUFDdkIsSUFBSWYscUJBQXFCZSxnQkFBZ0I7Z0JBQ3ZDaEIsa0JBQWtCO2dCQUNsQkUsb0JBQW9CO1lBQ3RCO1lBRUEsU0FBUztZQUNUYSx3QkFBd0I7WUFDeEJFLGtCQUFrQjtZQUVsQixTQUFTO1lBQ1R0QixhQUFhdUssT0FBTyxDQUFDO1FBQ3ZCLEVBQUUsT0FBT2pMLE9BQU87WUFDZGhCLFFBQVFnQixLQUFLLENBQUMsV0FBV0E7WUFDekJVLGFBQWFWLEtBQUssQ0FBQztRQUNyQixTQUFVO1lBQ1JrQyxjQUFjO1FBQ2hCO0lBQ0Y7SUFFQSxPQUFPO0lBQ1AsTUFBTWdKLGVBQWU7UUFDbkIsSUFBSWpKLFlBQVksUUFBUSxhQUFhO1FBQ3JDSCx3QkFBd0I7UUFDeEJFLGtCQUFrQjtJQUNwQjtJQUVBLFNBQVM7SUFDVCxNQUFNbUosb0JBQW9CLENBQUN4RCxJQUFZeUQ7UUFDckN6SyxjQUFjRixXQUFXZ0gsR0FBRyxDQUFDdUQsQ0FBQUEsU0FDM0JBLE9BQU9yRCxFQUFFLEtBQUtBLEtBQUs7Z0JBQUUsR0FBR3FELE1BQU07Z0JBQUVoSCxPQUFPb0g7WUFBUyxJQUFJSjtJQUV4RDtJQUVBLFdBQVc7SUFDWCxNQUFNSyxvQkFBb0IsT0FBT0M7WUFDbEJBO1FBQWIsTUFBTUMsUUFBT0Qsc0JBQUFBLE1BQU1FLE1BQU0sQ0FBQ0MsS0FBSyxjQUFsQkgsMENBQUFBLG1CQUFvQixDQUFDLEVBQUU7UUFDcEMsSUFBSUMsTUFBTTtZQUNSLFNBQVM7WUFDVCxNQUFNRyxlQUFlO2dCQUFDO2dCQUFjO2dCQUFhO2dCQUFhO2FBQVk7WUFDMUUsSUFBSSxDQUFDQSxhQUFhMUMsUUFBUSxDQUFDdUMsS0FBSzNELElBQUksR0FBRztnQkFDckNsSCxhQUFhVixLQUFLLENBQUM7Z0JBQ25CO1lBQ0Y7WUFFQSxnQkFBZ0I7WUFDaEIsSUFBSXVMLEtBQUtJLElBQUksR0FBRyxLQUFLLE9BQU8sTUFBTTtnQkFDaENqTCxhQUFhVixLQUFLLENBQUM7Z0JBQ25CO1lBQ0Y7WUFFQSxJQUFJO2dCQUNGaEIsUUFBUUMsR0FBRyxDQUFDLDBCQUFnQnNNLEtBQUt0RyxJQUFJO2dCQUVyQyxjQUFjO2dCQUNkOUQsa0JBQWtCeUgsQ0FBQUEsT0FBUSxJQUFJeEgsSUFBSXdILE1BQU1nRCxHQUFHLENBQUM7Z0JBRTVDLFVBQVU7Z0JBQ1YsTUFBTUMsYUFBYUMsSUFBSUMsZUFBZSxDQUFDUjtnQkFDdkN4SCxnQkFBZ0I2RSxDQUFBQSxPQUFTO3dCQUFFLEdBQUdBLElBQUk7d0JBQUUxRSxZQUFZMkg7b0JBQVc7Z0JBRTNELFNBQVM7Z0JBQ1QsTUFBTUcsV0FBVyxNQUFNdk4sc0RBQVNBLENBQUN3TixXQUFXLENBQUNWO2dCQUM3Q3ZNLFFBQVFDLEdBQUcsQ0FBQyxlQUFlK007Z0JBRTNCLG1CQUFtQjtnQkFDbkJqSSxnQkFBZ0I2RSxDQUFBQSxPQUFTO3dCQUFFLEdBQUdBLElBQUk7d0JBQUUxRSxZQUFZOEg7b0JBQVM7Z0JBRXpELGVBQWU7Z0JBQ2YsSUFBSWhMLGtCQUFrQjtvQkFDcEJMLGNBQWNpSSxDQUFBQSxPQUFRQSxLQUFLbkIsR0FBRyxDQUFDdUQsQ0FBQUEsU0FDN0JBLE9BQU9yRCxFQUFFLEtBQUszRyxtQkFBbUI7Z0NBQUUsR0FBR2dLLE1BQU07Z0NBQUU5RyxZQUFZOEg7NEJBQVMsSUFBSWhCO2dCQUUzRTtnQkFFQWtCLE1BQU07WUFDUixFQUFFLE9BQU9sTSxPQUFPO2dCQUNkaEIsUUFBUWdCLEtBQUssQ0FBQyxlQUFlQTtnQkFDN0JVLGFBQWFWLEtBQUssQ0FBQztnQkFFbkIsY0FBYztnQkFDZCtELGdCQUFnQjZFLENBQUFBLE9BQVM7d0JBQUUsR0FBR0EsSUFBSTt3QkFBRTFFLFlBQVk7b0JBQUc7WUFDckQsU0FBVTtnQkFDUixjQUFjO2dCQUNkL0Msa0JBQWtCeUgsQ0FBQUE7b0JBQ2hCLE1BQU11RCxTQUFTLElBQUkvSyxJQUFJd0g7b0JBQ3ZCdUQsT0FBT0MsTUFBTSxDQUFDO29CQUNkLE9BQU9EO2dCQUNUO1lBQ0Y7UUFDRjtJQUNGO0lBRUEsU0FBUztJQUNULE1BQU1FLG9CQUFvQixPQUFPZjtZQUNsQkE7UUFBYixNQUFNQyxRQUFPRCxzQkFBQUEsTUFBTUUsTUFBTSxDQUFDQyxLQUFLLGNBQWxCSCwwQ0FBQUEsbUJBQW9CLENBQUMsRUFBRTtRQUNwQyxJQUFJQyxNQUFNO1lBQ1IsU0FBUztZQUNULE1BQU1HLGVBQWU7Z0JBQUM7Z0JBQWE7Z0JBQWE7Z0JBQWE7Z0JBQWE7YUFBWTtZQUN0RixJQUFJLENBQUNBLGFBQWExQyxRQUFRLENBQUN1QyxLQUFLM0QsSUFBSSxHQUFHO2dCQUNyQ3NFLE1BQU07Z0JBQ047WUFDRjtZQUVBLGlCQUFpQjtZQUNqQixJQUFJWCxLQUFLSSxJQUFJLEdBQUcsTUFBTSxPQUFPLE1BQU07Z0JBQ2pDTyxNQUFNO2dCQUNOO1lBQ0Y7WUFFQSxJQUFJO2dCQUNGbE4sUUFBUUMsR0FBRyxDQUFDLDBCQUFnQnNNLEtBQUt0RyxJQUFJO2dCQUVyQyxjQUFjO2dCQUNkOUQsa0JBQWtCeUgsQ0FBQUEsT0FBUSxJQUFJeEgsSUFBSXdILE1BQU1nRCxHQUFHLENBQUM7Z0JBRTVDLFVBQVU7Z0JBQ1YsTUFBTUMsYUFBYUMsSUFBSUMsZUFBZSxDQUFDUjtnQkFDdkN4SCxnQkFBZ0I2RSxDQUFBQSxPQUFTO3dCQUN2QixHQUFHQSxJQUFJO3dCQUNQakUsZUFBZTs0QkFDYixHQUFHaUUsS0FBS2pFLGFBQWE7NEJBQ3JCSSxPQUFPO2dDQUNMQyxLQUFLNkc7Z0NBQ0w1RyxNQUFNc0csS0FBS3RHLElBQUk7NEJBQ2pCO3dCQUNGO29CQUNGO2dCQUVBLFNBQVM7Z0JBQ1QsTUFBTWQsV0FBVyxNQUFNMUYsc0RBQVNBLENBQUN3TixXQUFXLENBQUNWO2dCQUM3Q3ZNLFFBQVFDLEdBQUcsQ0FBQyxlQUFla0Y7Z0JBRTNCLHFCQUFxQjtnQkFDckJKLGdCQUFnQjZFLENBQUFBLE9BQVM7d0JBQ3ZCLEdBQUdBLElBQUk7d0JBQ1BqRSxlQUFlOzRCQUNiLEdBQUdpRSxLQUFLakUsYUFBYTs0QkFDckJJLE9BQU87Z0NBQ0xDLEtBQUtiO2dDQUNMYyxNQUFNc0csS0FBS3RHLElBQUk7NEJBQ2pCO3dCQUNGO29CQUNGO2dCQUVBLGlCQUFpQjtnQkFDakIsSUFBSWpFLGtCQUFrQjtvQkFDcEJMLGNBQWNpSSxDQUFBQSxPQUFRQSxLQUFLbkIsR0FBRyxDQUFDdUQsQ0FBQUEsU0FDN0JBLE9BQU9yRCxFQUFFLEtBQUszRyxtQkFBbUI7Z0NBQy9CLEdBQUdnSyxNQUFNO2dDQUNUckcsZUFBZTtvQ0FDYixHQUFHcUcsT0FBT3JHLGFBQWE7b0NBQ3ZCSSxPQUFPO3dDQUNMQyxLQUFLYjt3Q0FDTGMsTUFBTXNHLEtBQUt0RyxJQUFJO29DQUNqQjtnQ0FDRjs0QkFDRixJQUFJK0Y7Z0JBRVI7Z0JBRUFrQixNQUFNO1lBQ1IsRUFBRSxPQUFPbE0sT0FBTztnQkFDZGhCLFFBQVFnQixLQUFLLENBQUMsZUFBZUE7Z0JBQzdCa00sTUFBTTtnQkFFTixjQUFjO2dCQUNkbkksZ0JBQWdCNkUsQ0FBQUEsT0FBUzt3QkFDdkIsR0FBR0EsSUFBSTt3QkFDUGpFLGVBQWU7NEJBQ2IsR0FBR2lFLEtBQUtqRSxhQUFhOzRCQUNyQkksT0FBTztnQ0FDTEMsS0FBSztnQ0FDTEMsTUFBTTs0QkFDUjt3QkFDRjtvQkFDRjtZQUNGLFNBQVU7Z0JBQ1IsY0FBYztnQkFDZDlELGtCQUFrQnlILENBQUFBO29CQUNoQixNQUFNdUQsU0FBUyxJQUFJL0ssSUFBSXdIO29CQUN2QnVELE9BQU9DLE1BQU0sQ0FBQztvQkFDZCxPQUFPRDtnQkFDVDtZQUNGO1FBQ0Y7SUFDRjtJQUVBLFdBQVc7SUFDWCxNQUFNRyxxQkFBcUI7UUFDekIsTUFBTUMsUUFBUXJILFNBQVNzSCxhQUFhLENBQUM7UUFDckNELE1BQU0zRSxJQUFJLEdBQUc7UUFDYjJFLE1BQU1FLE1BQU0sR0FBRztRQUNmRixNQUFNRyxRQUFRLEdBQUcsQ0FBQ0MsSUFBTU4sa0JBQWtCTTtRQUMxQ0osTUFBTUssS0FBSztJQUNiO0lBRUEsU0FBUztJQUNULE1BQU1DLHlCQUF5QixPQUFPdkI7WUFDdkJBO1FBQWIsTUFBTUMsUUFBT0Qsc0JBQUFBLE1BQU1FLE1BQU0sQ0FBQ0MsS0FBSyxjQUFsQkgsMENBQUFBLG1CQUFvQixDQUFDLEVBQUU7UUFDcEMsSUFBSUMsTUFBTTtZQUNSLFNBQVM7WUFDVCxNQUFNRyxlQUFlO2dCQUNuQjtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTthQUNEO1lBRUQsSUFBSSxDQUFDQSxhQUFhMUMsUUFBUSxDQUFDdUMsS0FBSzNELElBQUksR0FBRztnQkFDckNzRSxNQUFNO2dCQUNOO1lBQ0Y7WUFFQSxnQkFBZ0I7WUFDaEIsSUFBSVgsS0FBS0ksSUFBSSxHQUFHLEtBQUssT0FBTyxNQUFNO2dCQUNoQ08sTUFBTTtnQkFDTjtZQUNGO1lBRUEsSUFBSTtnQkFDRmxOLFFBQVFDLEdBQUcsQ0FBQywwQkFBZ0JzTSxLQUFLdEcsSUFBSTtnQkFFckMsY0FBYztnQkFDZDlELGtCQUFrQnlILENBQUFBLE9BQVEsSUFBSXhILElBQUl3SCxNQUFNZ0QsR0FBRyxDQUFDO2dCQUU1QyxTQUFTO2dCQUNULE1BQU1rQixjQUFjLE1BQU1yTyxzREFBU0EsQ0FBQ3dOLFdBQVcsQ0FBQ1Y7Z0JBQ2hEdk0sUUFBUUMsR0FBRyxDQUFDLGVBQWU2TjtnQkFFM0IsZUFBZTtnQkFDZi9JLGdCQUFnQjZFLENBQUFBLE9BQVM7d0JBQ3ZCLEdBQUdBLElBQUk7d0JBQ1BqRSxlQUFlOzRCQUNiLEdBQUdpRSxLQUFLakUsYUFBYTs0QkFDckJFLGFBQWE7NEJBQ2JLLFVBQVU7Z0NBQ1JGLEtBQUs4SDtnQ0FDTDdILE1BQU1zRyxLQUFLdEcsSUFBSTs0QkFDakI7d0JBQ0Y7b0JBQ0Y7Z0JBRUFpSCxNQUFNO1lBQ1IsRUFBRSxPQUFPbE0sT0FBTztnQkFDZGhCLFFBQVFnQixLQUFLLENBQUMsZUFBZUE7Z0JBQzdCa00sTUFBTTtZQUNSLFNBQVU7Z0JBQ1IsY0FBYztnQkFDZC9LLGtCQUFrQnlILENBQUFBO29CQUNoQixNQUFNdUQsU0FBUyxJQUFJL0ssSUFBSXdIO29CQUN2QnVELE9BQU9DLE1BQU0sQ0FBQztvQkFDZCxPQUFPRDtnQkFDVDtZQUNGO1FBQ0Y7SUFDRjtJQUVBLFdBQVc7SUFDWCxNQUFNWSwwQkFBMEI7UUFDOUIsTUFBTVIsUUFBUXJILFNBQVNzSCxhQUFhLENBQUM7UUFDckNELE1BQU0zRSxJQUFJLEdBQUc7UUFDYjJFLE1BQU1FLE1BQU0sR0FBRztRQUNmRixNQUFNRyxRQUFRLEdBQUcsQ0FBQ0MsSUFBTUUsdUJBQXVCRjtRQUMvQ0osTUFBTUssS0FBSztJQUNiO0lBRUEsV0FBVztJQUNYLE1BQU1JLCtCQUErQixPQUFPMUI7WUFDN0JBO1FBQWIsTUFBTUMsUUFBT0Qsc0JBQUFBLE1BQU1FLE1BQU0sQ0FBQ0MsS0FBSyxjQUFsQkgsMENBQUFBLG1CQUFvQixDQUFDLEVBQUU7UUFDcEMsSUFBSUMsTUFBTTtZQUNSLFNBQVM7WUFDVCxNQUFNRyxlQUFlO2dCQUNuQjtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTtnQkFDQTthQUNEO1lBRUQsSUFBSSxDQUFDQSxhQUFhMUMsUUFBUSxDQUFDdUMsS0FBSzNELElBQUksR0FBRztnQkFDckNzRSxNQUFNO2dCQUNOO1lBQ0Y7WUFFQSxnQkFBZ0I7WUFDaEIsSUFBSVgsS0FBS0ksSUFBSSxHQUFHLEtBQUssT0FBTyxNQUFNO2dCQUNoQ08sTUFBTTtnQkFDTjtZQUNGO1lBRUEsSUFBSTtnQkFDRmxOLFFBQVFDLEdBQUcsQ0FBQywwQkFBZ0JzTSxLQUFLdEcsSUFBSTtnQkFFckMsY0FBYztnQkFDZDlELGtCQUFrQnlILENBQUFBLE9BQVEsSUFBSXhILElBQUl3SCxNQUFNZ0QsR0FBRyxDQUFDLFlBQXVCLE9BQVgzQixLQUFLQyxHQUFHO2dCQUVoRSxTQUFTO2dCQUNULE1BQU0rQyxjQUFjLE1BQU14TyxzREFBU0EsQ0FBQ3dOLFdBQVcsQ0FBQ1Y7Z0JBQ2hEdk0sUUFBUUMsR0FBRyxDQUFDLGVBQWVnTztnQkFFM0IsWUFBWTtnQkFDWixNQUFNQyxjQUFjO29CQUNsQnRGLE1BQU0yRCxLQUFLM0QsSUFBSTtvQkFDZjNDLE1BQU1zRyxLQUFLdEcsSUFBSTtvQkFDZkQsS0FBS2lJO2dCQUNQO2dCQUVBbEosZ0JBQWdCNkUsQ0FBQUEsT0FBUzt3QkFDdkIsR0FBR0EsSUFBSTt3QkFDUG5FLG1CQUFtQjsrQkFBSW1FLEtBQUtuRSxpQkFBaUI7NEJBQUV5STt5QkFBWTtvQkFDN0Q7Z0JBRUFoQixNQUFNO1lBQ1IsRUFBRSxPQUFPbE0sT0FBTztnQkFDZGhCLFFBQVFnQixLQUFLLENBQUMsZUFBZUE7Z0JBQzdCa00sTUFBTTtZQUNSLFNBQVU7Z0JBQ1IsY0FBYztnQkFDZC9LLGtCQUFrQnlILENBQUFBO29CQUNoQixNQUFNdUQsU0FBUyxJQUFJL0ssSUFBSXdIO29CQUN2QixxQkFBcUI7b0JBQ3JCRixNQUFNeUUsSUFBSSxDQUFDaEIsUUFBUWpELE9BQU8sQ0FBQ2tFLENBQUFBO3dCQUN6QixJQUFJQSxLQUFLQyxVQUFVLENBQUMsY0FBYzs0QkFDaENsQixPQUFPQyxNQUFNLENBQUNnQjt3QkFDaEI7b0JBQ0Y7b0JBQ0EsT0FBT2pCO2dCQUNUO1lBQ0Y7UUFDRjtJQUNGO0lBRUEsYUFBYTtJQUNiLE1BQU1tQixnQ0FBZ0M7UUFDcEMsTUFBTWYsUUFBUXJILFNBQVNzSCxhQUFhLENBQUM7UUFDckNELE1BQU0zRSxJQUFJLEdBQUc7UUFDYjJFLE1BQU1FLE1BQU0sR0FBRztRQUNmRixNQUFNRyxRQUFRLEdBQUcsQ0FBQ0MsSUFBTUssNkJBQTZCTDtRQUNyREosTUFBTUssS0FBSztJQUNiO0lBRUEsU0FBUztJQUNULE1BQU1XLHlCQUF5QixDQUFDbkU7UUFDOUJyRixnQkFBZ0I2RSxDQUFBQSxPQUFTO2dCQUN2QixHQUFHQSxJQUFJO2dCQUNQbkUsbUJBQW1CbUUsS0FBS25FLGlCQUFpQixDQUFDdUQsTUFBTSxDQUFDLENBQUN3RixHQUFHQyxJQUFNQSxNQUFNckU7WUFDbkU7SUFDRjtJQUVBLFdBQVc7SUFDWCxNQUFNc0Usc0JBQXNCO1FBQzFCM0osZ0JBQWdCNkUsQ0FBQUEsT0FBUztnQkFDdkIsR0FBR0EsSUFBSTtnQkFDUGxFLGNBQWM7dUJBQUlrRSxLQUFLbEUsWUFBWTtvQkFBRTt3QkFBRVYsT0FBTzt3QkFBSXNCLFNBQVM7b0JBQUc7aUJBQUU7WUFDbEU7SUFDRjtJQUVBLE1BQU1xSSx5QkFBeUIsQ0FBQ3ZFO1FBQzlCckYsZ0JBQWdCNkUsQ0FBQUEsT0FBUztnQkFDdkIsR0FBR0EsSUFBSTtnQkFDUGxFLGNBQWNrRSxLQUFLbEUsWUFBWSxDQUFDc0QsTUFBTSxDQUFDLENBQUN3RixHQUFHQyxJQUFNQSxNQUFNckU7WUFDekQ7SUFDRjtJQUVBLE1BQU13RSwwQkFBMEIsQ0FBQ3hFLE9BQWVwRjtRQUM5Q0QsZ0JBQWdCNkUsQ0FBQUE7WUFDZCxNQUFNaUYsa0JBQWtCO21CQUFJakYsS0FBS2xFLFlBQVk7YUFBQztZQUM5Q21KLGVBQWUsQ0FBQ3pFLE1BQU0sR0FBRztnQkFBRSxHQUFHeUUsZUFBZSxDQUFDekUsTUFBTTtnQkFBRXBGO1lBQU07WUFDNUQsT0FBTztnQkFBRSxHQUFHNEUsSUFBSTtnQkFBRWxFLGNBQWNtSjtZQUFnQjtRQUNsRDtJQUNGO0lBRUEsTUFBTUMsNEJBQTRCLENBQUMxRSxPQUFlOUQ7UUFDaER2QixnQkFBZ0I2RSxDQUFBQTtZQUNkLE1BQU1pRixrQkFBa0I7bUJBQUlqRixLQUFLbEUsWUFBWTthQUFDO1lBQzlDbUosZUFBZSxDQUFDekUsTUFBTSxHQUFHO2dCQUFFLEdBQUd5RSxlQUFlLENBQUN6RSxNQUFNO2dCQUFFOUQ7WUFBUTtZQUM5RCxPQUFPO2dCQUFFLEdBQUdzRCxJQUFJO2dCQUFFbEUsY0FBY21KO1lBQWdCO1FBQ2xEO0lBQ0Y7SUFFQSw2QkFBNkI7SUFDN0IsTUFBTUUsNEJBQTRCLENBQUNySjtRQUNqQyxPQUFPQSxhQUFhK0MsR0FBRyxDQUFDdUcsQ0FBQUEsT0FBUztnQkFDL0JoSyxPQUFPZ0ssS0FBS2hLLEtBQUs7Z0JBQ2pCc0IsU0FBUzBJLEtBQUsxSSxPQUFPLENBQUMySSxLQUFLLENBQUMsTUFBTWpHLE1BQU0sQ0FBQ2tHLENBQUFBLE9BQVFBLEtBQUt4RSxJQUFJLElBQUksYUFBYTtZQUM3RTtJQUNGO0lBRUEsMEJBQTBCO0lBQzFCLE1BQU15RSxtQkFBbUIsQ0FBQ25EO1lBU3BCQSw2QkFBQUEsdUJBS0FBLGdDQUFBQTtRQWJKLE1BQU1vRCxTQUFTLEVBQUU7UUFFakIsU0FBUztRQUNULElBQUlwRCxPQUFPOUcsVUFBVSxJQUFJOEcsT0FBTzlHLFVBQVUsQ0FBQ21KLFVBQVUsQ0FBQyxVQUFVO1lBQzlEZSxPQUFPQyxJQUFJLENBQUM7UUFDZDtRQUVBLFNBQVM7UUFDVCxJQUFJckQsRUFBQUEsd0JBQUFBLE9BQU9yRyxhQUFhLGNBQXBCcUcsNkNBQUFBLDhCQUFBQSxzQkFBc0JqRyxLQUFLLGNBQTNCaUcsa0RBQUFBLDRCQUE2QmhHLEdBQUcsS0FBSWdHLE9BQU9yRyxhQUFhLENBQUNJLEtBQUssQ0FBQ0MsR0FBRyxDQUFDcUksVUFBVSxDQUFDLFVBQVU7WUFDMUZlLE9BQU9DLElBQUksQ0FBQztRQUNkO1FBRUEsU0FBUztRQUNULElBQUlyRCxFQUFBQSx5QkFBQUEsT0FBT3JHLGFBQWEsY0FBcEJxRyw4Q0FBQUEsaUNBQUFBLHVCQUFzQjlGLFFBQVEsY0FBOUI4RixxREFBQUEsK0JBQWdDaEcsR0FBRyxLQUFJZ0csT0FBT3JHLGFBQWEsQ0FBQ08sUUFBUSxDQUFDRixHQUFHLENBQUNxSSxVQUFVLENBQUMsVUFBVTtZQUNoR2UsT0FBT0MsSUFBSSxDQUFDO1FBQ2Q7UUFFQSxTQUFTO1FBQ1QsSUFBSXJELE9BQU9zRCxtQkFBbUIsRUFBRTtZQUM5QnRELE9BQU9zRCxtQkFBbUIsQ0FBQ3BGLE9BQU8sQ0FBQyxDQUFDcUYsVUFBVW5GO2dCQUM1QyxJQUFJbUYsU0FBU3ZKLEdBQUcsSUFBSXVKLFNBQVN2SixHQUFHLENBQUNxSSxVQUFVLENBQUMsVUFBVTtvQkFDcERlLE9BQU9DLElBQUksQ0FBQyxPQUFpQixPQUFWakYsUUFBUTtnQkFDN0I7WUFDRjtRQUNGO1FBRUEsT0FBT2dGO0lBQ1Q7SUFFQSxZQUFZO0lBQ1osTUFBTUksYUFBYSxPQUFPeEQ7UUFDeEIsSUFBSTtnQkFrQlVBLDZCQUFBQSx1QkFDR0EsZ0NBQUFBO1lBbEJmLFVBQVU7WUFDVixJQUFJLENBQUNBLE9BQU9oSCxLQUFLLElBQUlnSCxPQUFPaEgsS0FBSyxDQUFDMEYsSUFBSSxPQUFPLElBQUk7Z0JBQy9DLE1BQU0sSUFBSStFLE1BQU07WUFDbEI7WUFFQSxnQkFBZ0I7WUFDaEIsTUFBTUMsYUFBYVAsaUJBQWlCbkQ7WUFDcEMsSUFBSTBELFdBQVc1RyxNQUFNLEdBQUcsR0FBRztnQkFDekIsTUFBTSxJQUFJMkcsTUFBTSwwQkFBK0MsT0FBckJDLFdBQVdDLElBQUksQ0FBQztZQUM1RDtZQUVBLFNBQVM7WUFDVCxNQUFNQyxhQUFhO2dCQUNqQjdQLFVBQVVBO2dCQUNWaUYsT0FBT2dILE9BQU9oSCxLQUFLO2dCQUNuQkMsYUFBYStHLE9BQU8vRyxXQUFXLElBQUk7Z0JBQ25DQyxZQUFZOEcsT0FBTzlHLFVBQVUsSUFBSTtnQkFDakNVLFVBQVVvRyxFQUFBQSx3QkFBQUEsT0FBT3JHLGFBQWEsY0FBcEJxRyw2Q0FBQUEsOEJBQUFBLHNCQUFzQmpHLEtBQUssY0FBM0JpRyxrREFBQUEsNEJBQTZCaEcsR0FBRyxJQUFHLElBQUk7Z0JBQ2pESCxhQUFhbUcsRUFBQUEseUJBQUFBLE9BQU9yRyxhQUFhLGNBQXBCcUcsOENBQUFBLGlDQUFBQSx1QkFBc0I5RixRQUFRLGNBQTlCOEYscURBQUFBLCtCQUFnQ2hHLEdBQUcsSUFBRyxJQUFJO2dCQUN2REYsVUFBVTtnQkFDVnVGLGVBQWU7Z0JBQ2YxRixlQUFlcUcsT0FBT3JHLGFBQWEsSUFBSSxDQUFDO2dCQUN4Q0QsY0FBY3FKLDBCQUEwQi9DLE9BQU90RyxZQUFZLElBQUksRUFBRTtnQkFDakU0SixxQkFBcUJ0RCxPQUFPc0QsbUJBQW1CLElBQUksRUFBRTtnQkFDckRuRSxZQUFZYSxPQUFPYixVQUFVLElBQUk7WUFDbkM7WUFFQW5MLFFBQVFDLEdBQUcsQ0FBQywwQkFBZ0IyUDtZQUU1QixJQUFJL087WUFFSixlQUFlO1lBQ2YsSUFBSW1MLE9BQU9yRCxFQUFFLElBQUlxRCxPQUFPckQsRUFBRSxHQUFHLFNBQVM7Z0JBQ3BDLGNBQWM7Z0JBQ2QzSSxRQUFRQyxHQUFHLENBQUM7Z0JBQ1osTUFBTSxFQUFFRyxNQUFNRixRQUFRLEVBQUUsR0FBRyxNQUFNVixzREFBU0EsQ0FBQ3FRLFlBQVksQ0FBQ0Q7Z0JBQ3hEL08sU0FBU1g7Z0JBQ1RGLFFBQVFDLEdBQUcsQ0FBQztZQUNkLE9BQU87Z0JBQ0wsZUFBZTtnQkFDZkQsUUFBUUMsR0FBRyxDQUFDLHdCQUFjK0wsT0FBT3JELEVBQUU7Z0JBQ25DLE1BQU0sRUFBRXZJLE1BQU1GLFFBQVEsRUFBRSxHQUFHLE1BQU1WLHNEQUFTQSxDQUFDc1EsWUFBWSxDQUFDOUQsT0FBT3JELEVBQUUsRUFBRWlIO2dCQUNuRS9PLFNBQVNYO2dCQUNURixRQUFRQyxHQUFHLENBQUM7WUFDZDtZQUVBLE9BQU9ZO1FBQ1QsRUFBRSxPQUFPRyxPQUFPO1lBQ2RoQixRQUFRZ0IsS0FBSyxDQUFDLGFBQWFBO1lBQzNCLE1BQU1BO1FBQ1I7SUFDRjtJQUVBLFVBQVU7SUFDVixNQUFNK08sc0JBQXNCO1FBQzFCLElBQUksQ0FBQy9OLGtCQUFrQjtZQUNyQmtMLE1BQU07WUFDTjtRQUNGO1FBRUEsTUFBTThDLGlCQUFpQnZPLFdBQVd3TyxJQUFJLENBQUNqRSxDQUFBQSxTQUFVQSxPQUFPckQsRUFBRSxLQUFLM0c7UUFDL0QsSUFBSSxDQUFDZ08sZ0JBQWdCO1lBQ25COUMsTUFBTTtZQUNOO1FBQ0Y7UUFFQSxhQUFhO1FBQ2IsSUFBSThDLGVBQWVqUCxNQUFNLEtBQUssR0FBRztZQUMvQm1NLE1BQU07WUFDTjtRQUNGO1FBRUEsSUFBSTtZQUNGNUssZ0JBQWdCO1lBQ2hCdEMsUUFBUUMsR0FBRyxDQUFDO1lBQ1pELFFBQVFDLEdBQUcsQ0FBQyxrQ0FBd0IrQixrQkFBa0IsT0FBTyxPQUFPQTtZQUNwRWhDLFFBQVFDLEdBQUcsQ0FBQyxzQkFBWStQO1lBQ3hCaFEsUUFBUUMsR0FBRyxDQUFDLHdCQUFjK1AsZUFBZXJILEVBQUUsRUFBRSxPQUFPLE9BQU9xSCxlQUFlckgsRUFBRTtZQUM1RTNJLFFBQVFDLEdBQUcsQ0FBQyw0QkFBa0J3QixXQUFXZ0gsR0FBRyxDQUFDQyxDQUFBQSxJQUFNO29CQUFFQyxJQUFJRCxFQUFFQyxFQUFFO29CQUFFQyxNQUFNLE9BQU9GLEVBQUVDLEVBQUU7b0JBQUUzRCxPQUFPMEQsRUFBRTFELEtBQUs7Z0JBQUM7WUFFakcsY0FBYztZQUNkLE1BQU1rTCxvQkFBb0JGLGVBQWVySCxFQUFFO1lBQzNDM0ksUUFBUUMsR0FBRyxDQUFDLDJCQUFpQmlRLG1CQUFtQixPQUFPLE9BQU9BO1lBRTlELE1BQU0sRUFBRTlQLE1BQU1GLFFBQVEsRUFBRSxHQUFHLE1BQU1WLHNEQUFTQSxDQUFDMlEsYUFBYSxDQUFDRDtZQUV6RCxJQUFJaFEsU0FBU29JLElBQUksS0FBSyxLQUFLO2dCQUN6QnRJLFFBQVFDLEdBQUcsQ0FBQyxhQUFhQyxTQUFTRSxJQUFJO2dCQUN0QzhNLE1BQU07Z0JBRU4sY0FBYztnQkFDZCxNQUFNaEY7WUFDUixPQUFPO2dCQUNMbEksUUFBUWdCLEtBQUssQ0FBQyxhQUFhZCxTQUFTZ0osT0FBTztnQkFDM0NnRSxNQUFNaE4sU0FBU2dKLE9BQU8sSUFBSTtZQUM1QjtRQUNGLEVBQUUsT0FBT2xJLE9BQVk7Z0JBRU1BLGlCQUdyQkEsc0JBQUFBO1lBSkpoQixRQUFRZ0IsS0FBSyxDQUFDLGFBQWFBO1lBQzNCaEIsUUFBUWdCLEtBQUssQ0FBQyxZQUFXQSxrQkFBQUEsTUFBTWQsUUFBUSxjQUFkYyxzQ0FBQUEsZ0JBQWdCWixJQUFJO1lBRTdDLFlBQVk7WUFDWixLQUFJWSxtQkFBQUEsTUFBTWQsUUFBUSxjQUFkYyx3Q0FBQUEsdUJBQUFBLGlCQUFnQlosSUFBSSxjQUFwQlksMkNBQUFBLHFCQUFzQmtJLE9BQU8sRUFBRTtnQkFDakNnRSxNQUFNbE0sTUFBTWQsUUFBUSxDQUFDRSxJQUFJLENBQUM4SSxPQUFPO1lBQ25DLE9BQU8sSUFBSWxJLE1BQU1rSSxPQUFPLEVBQUU7Z0JBQ3hCZ0UsTUFBTWxNLE1BQU1rSSxPQUFPO1lBQ3JCLE9BQU87Z0JBQ0xnRSxNQUFNO1lBQ1I7UUFDRixTQUFVO1lBQ1I1SyxnQkFBZ0I7UUFDbEI7SUFDRjtJQUVBLFNBQVM7SUFDVCxNQUFNOE4sYUFBYTtRQUNqQixJQUFJO1lBQ0Y1TixjQUFjO1lBQ2QsY0FBYztZQUNkLElBQUlOLGVBQWV5SyxJQUFJLEdBQUcsR0FBRztnQkFDM0JPLE1BQU07Z0JBQ04xSyxjQUFjO2dCQUNkO1lBQ0Y7WUFFQXhDLFFBQVFDLEdBQUcsQ0FBQztZQUVaLHlCQUF5QjtZQUN6QixJQUFJK0Isb0JBQW9CRixtQkFBbUIsVUFBVTtnQkFDbkQsTUFBTWtPLGlCQUFpQnZPLFdBQVd3TyxJQUFJLENBQUN2SCxDQUFBQSxJQUFLQSxFQUFFQyxFQUFFLEtBQUszRztnQkFDckQsSUFBSWdPLGdCQUFnQjt3QkFXS3hPO29CQVZ2QixTQUFTO29CQUNULE1BQU02TyxnQkFBZ0I7d0JBQ3BCLEdBQUdMLGNBQWM7d0JBQ2pCaEwsT0FBT3hELGFBQWF3RCxLQUFLO3dCQUN6QkMsYUFBYXpELGFBQWF5RCxXQUFXO3dCQUNyQ0MsWUFBWTFELGFBQWEwRCxVQUFVO3dCQUNuQ1UsVUFBVXBFLGFBQWE2RCxjQUFjLEdBQUcsSUFBSTt3QkFDNUNRLGFBQWFyRSxhQUFhZ0UsbUJBQW1CLEdBQUcsSUFBSTt3QkFDcERHLGVBQWVuRSxhQUFhbUUsYUFBYTt3QkFDekNELGNBQWNsRSxhQUFha0UsWUFBWTt3QkFDdkM0SixxQkFBcUI5TixFQUFBQSxrQ0FBQUEsYUFBYWlFLGlCQUFpQixjQUE5QmpFLHNEQUFBQSxnQ0FBZ0NpSCxHQUFHLENBQUM2SCxDQUFBQSxXQUFhO2dDQUNwRXRMLE9BQU9zTCxTQUFTckssSUFBSTtnQ0FDcEJELEtBQUtzSyxTQUFTdEssR0FBRztnQ0FDakJmLGFBQWFxTCxTQUFTckssSUFBSTs0QkFDNUIsUUFBTyxFQUFFO29CQUNYO29CQUVBLGNBQWM7b0JBQ2R0RSxjQUFjaUksQ0FBQUEsT0FBUUEsS0FBS25CLEdBQUcsQ0FBQ3VELENBQUFBLFNBQzdCQSxPQUFPckQsRUFBRSxLQUFLM0csbUJBQW1CcU8sZ0JBQWdCckU7b0JBR25ELFVBQVU7b0JBQ1YsTUFBTXdELFdBQVdhO2dCQUNuQjtZQUNGO1lBRUEsV0FBVztZQUNYLE1BQU1qUSxPQUFPO2dCQUNYNEUsT0FBTzNCO2dCQUNQRTtnQkFDQUU7Z0JBQ0FoQztZQUNGO1lBRUFMLE9BQU9oQjtZQUNQZTtZQUVBbkIsUUFBUUMsR0FBRyxDQUFDO1FBQ2QsRUFBRSxPQUFPZSxPQUFZO1lBQ25CaEIsUUFBUWdCLEtBQUssQ0FBQyxXQUFXQTtZQUN6QmtNLE1BQU0sU0FBZ0MsT0FBdkJsTSxNQUFNa0ksT0FBTyxJQUFJO1FBQ2xDLFNBQVU7WUFDUjFHLGNBQWM7UUFDaEI7SUFDRjtJQUVBLFNBQVM7SUFDVCxNQUFNK04sZ0JBQWdCO1FBQ3BCLGtCQUFrQjtRQUNsQixJQUFJNU4saUJBQWlCLEdBQUc7WUFDdEI7UUFDRjtRQUVBLElBQUk7WUFDRkQsc0JBQXNCO1lBRXRCLFVBQVU7WUFDVixJQUFJakIsV0FBV3FILE1BQU0sS0FBSyxHQUFHO2dCQUMzQm9FLE1BQU07Z0JBQ047WUFDRjtZQUVBbE4sUUFBUUMsR0FBRyxDQUFDLCtCQUFxQkY7WUFFakMsTUFBTSxFQUFFSyxNQUFNRixRQUFRLEVBQUUsR0FBRyxNQUFNVixzREFBU0EsQ0FBQ2dSLG1CQUFtQixDQUFDelE7WUFFL0QsSUFBSUcsU0FBU29JLElBQUksS0FBSyxLQUFLO2dCQUN6QnRJLFFBQVFDLEdBQUcsQ0FBQyxlQUFlQyxTQUFTRSxJQUFJO2dCQUV4QyxTQUFTO2dCQUNULE1BQU1xUSxjQUFjdlEsU0FBU0UsSUFBSTtnQkFDakMsSUFBSXNRLGlCQUFpQixRQUEwQixPQUFsQkQsWUFBWXpMLEtBQUssRUFBQztnQkFFL0MsbUJBQW1CO2dCQUNuQixJQUFJeUwsWUFBWUUsWUFBWSxFQUFFO29CQUM1QixNQUFNQyxRQUFRSCxZQUFZRSxZQUFZO29CQUN0Q0Qsa0JBQWtCLHFCQUEwREQsT0FBckNBLFlBQVlJLFlBQVksRUFBQyxjQUFvREQsT0FBeENILFlBQVlLLGdCQUFnQixFQUFDLGFBQThDRixPQUFuQ0EsTUFBTUcsZ0JBQWdCLEVBQUMsY0FBa0RILE9BQXRDQSxNQUFNSSxtQkFBbUIsRUFBQyxjQUFzQyxPQUExQkosTUFBTUssbUJBQW1CLEVBQUM7b0JBRXZOLElBQUlMLE1BQU1NLGtCQUFrQixHQUFHLEdBQUc7d0JBQ2hDLE1BQU1DLGtCQUFrQkMsS0FBS0MsS0FBSyxDQUFDVCxNQUFNTSxrQkFBa0IsR0FBRzt3QkFDOURSLGtCQUFrQixhQUE2QixPQUFoQlMsaUJBQWdCO29CQUNqRDtnQkFDRjtnQkFFQWpFLE1BQU13RDtnQkFFTixhQUFhO2dCQUNiOU4sZ0JBQWdCO2dCQUVoQixjQUFjO2dCQUNkLE1BQU1zRjtnQkFFTixZQUFZO2dCQUNaOUcsT0FBTztvQkFDTHdILE1BQU07b0JBQ043SSxVQUFVQTtvQkFDVm1KLFNBQVM7Z0JBQ1g7WUFDRixPQUFPO2dCQUNMbEosUUFBUWdCLEtBQUssQ0FBQyxlQUFlZCxTQUFTZ0osT0FBTztnQkFDN0NnRSxNQUFNaE4sU0FBU2dKLE9BQU8sSUFBSTtZQUM1QjtRQUNGLEVBQUUsT0FBT2xJLE9BQVk7Z0JBS2ZBLHNCQUFBQTtZQUpKaEIsUUFBUWdCLEtBQUssQ0FBQyxlQUFlQTtZQUU3QixZQUFZO1lBQ1osSUFBSXNRLGVBQWU7WUFDbkIsS0FBSXRRLGtCQUFBQSxNQUFNZCxRQUFRLGNBQWRjLHVDQUFBQSx1QkFBQUEsZ0JBQWdCWixJQUFJLGNBQXBCWSwyQ0FBQUEscUJBQXNCa0ksT0FBTyxFQUFFO2dCQUNqQ29JLGVBQWV0USxNQUFNZCxRQUFRLENBQUNFLElBQUksQ0FBQzhJLE9BQU87WUFDNUMsT0FBTyxJQUFJbEksTUFBTWtJLE9BQU8sRUFBRTtnQkFDeEJvSSxlQUFldFEsTUFBTWtJLE9BQU87WUFDOUI7WUFFQWdFLE1BQU1vRTtRQUNSLFNBQVU7WUFDUjVPLHNCQUFzQjtRQUN4QjtJQUNGO0lBRUEsbUJBQW1CO0lBQ25CLE1BQU02TyxpQkFBaUI7UUFDckJuQjtJQUNGO0lBRUEsU0FBUztJQUNULE1BQU1vQixvQkFBb0I7UUFDeEJ6UCxrQkFBa0I7UUFDbEJFLG9CQUFvQjtJQUN0QjtJQUVBLFNBQVM7SUFDVCxNQUFNd1AsdUJBQXVCLENBQUNDO1FBQzVCM00sZ0JBQWdCNkUsQ0FBQUEsT0FBUztnQkFDdkIsR0FBR0EsSUFBSTtnQkFDUGxELGtCQUFrQmdMLFNBQVNDLFlBQVk7WUFDekM7UUFDQXZPLHdCQUF3QjtJQUMxQjtJQUVBLFdBQVc7SUFDWCxNQUFNd08sMkJBQTJCO1FBQy9CeE8sd0JBQXdCO0lBQzFCO0lBRUEsV0FBVztJQUNYLE1BQU13SSxrQkFBa0IsT0FBT2xMO1FBQzdCcUIsa0JBQWtCO1FBQ2xCRSxvQkFBb0J2QjtRQUVwQixtQkFBbUI7UUFDbkIsSUFBSW1ELE1BQU1pRixNQUFNLEtBQUssS0FBSyxDQUFDL0UsY0FBYztZQUN2Q3NFLGNBQWM7UUFDaEI7UUFFQSwyQkFBMkI7UUFDM0IsTUFBTTJILGlCQUFpQnZPLFdBQVd3TyxJQUFJLENBQUNqRSxDQUFBQSxTQUFVQSxPQUFPckQsRUFBRSxLQUFLakk7UUFDL0RWLFFBQVFDLEdBQUcsQ0FBQyx1QkFBYStQO1FBRXpCLElBQUlBLGdCQUFnQjtZQUNsQixJQUFJO2dCQUNGaFEsUUFBUUMsR0FBRyxDQUFDLGlDQUF1QkYsVUFBVSxhQUFhVztnQkFFMUQsY0FBYztnQkFDZCxNQUFNLEVBQUVOLE1BQU15UixHQUFHLEVBQUUsR0FBRyxNQUFNcFIsa0JBQWtCVixVQUFVVztnQkFFeEQsSUFBSW1SLElBQUl2SixJQUFJLEtBQUssT0FBT3VKLElBQUl6UixJQUFJLEVBQUU7d0JBS04wUix1Q0FPQ0E7b0JBWDNCLE1BQU1BLG1CQUFtQkQsSUFBSXpSLElBQUk7b0JBQ2pDSixRQUFRQyxHQUFHLENBQUMsY0FBYzZSO29CQUUxQixpREFBaUQ7b0JBQ2pELE1BQU1yTSxvQkFBb0JxTSxFQUFBQSx3Q0FBQUEsaUJBQWlCeEMsbUJBQW1CLGNBQXBDd0MsNERBQUFBLHNDQUFzQ3JKLEdBQUcsQ0FBQyxDQUFDOEcsV0FBbUI7NEJBQ3RGM0csTUFBTTs0QkFDTjNDLE1BQU1zSixTQUFTdkssS0FBSyxJQUFJdUssU0FBU3RKLElBQUksSUFBSTs0QkFDekNELEtBQUt1SixTQUFTdkosR0FBRzt3QkFDbkIsUUFBTyxFQUFFO29CQUVULGlDQUFpQztvQkFDakMsTUFBTStMLHFCQUFxQkQsRUFBQUEsaUNBQUFBLGlCQUFpQnBNLFlBQVksY0FBN0JvTSxxREFBQUEsK0JBQStCckosR0FBRyxDQUFDLENBQUN1RyxPQUFlOzRCQUM1RWhLLE9BQU9nSyxLQUFLaEssS0FBSyxJQUFJOzRCQUNyQnNCLFNBQVNvRCxNQUFNQyxPQUFPLENBQUNxRixLQUFLMUksT0FBTyxJQUFJMEksS0FBSzFJLE9BQU8sQ0FBQ3FKLElBQUksQ0FBQyxRQUFTWCxLQUFLMUksT0FBTyxJQUFJO3dCQUNwRixRQUFPLEVBQUU7b0JBRVR0RyxRQUFRQyxHQUFHLENBQUMseUJBQWV3RjtvQkFDM0J6RixRQUFRQyxHQUFHLENBQUMseUJBQWU4UjtvQkFFM0JoTixnQkFBZ0I2RSxDQUFBQTs0QkFVSmtJLHVDQUFBQSxpQ0FDQ0Esd0NBQUFBLGtDQUNJQSwwQ0FBQUEsa0NBQ0NBLDJDQUFBQTsrQkFiTzs0QkFDdkIsR0FBR2xJLElBQUk7NEJBQ1A1RSxPQUFPOE0saUJBQWlCOU0sS0FBSzs0QkFDN0JDLGFBQWE2TSxpQkFBaUI3TSxXQUFXOzRCQUN6Q0MsWUFBWTRNLGlCQUFpQjVNLFVBQVUsSUFBSTs0QkFDM0NHLGdCQUFnQnlNLGlCQUFpQmxNLFFBQVEsS0FBSzs0QkFDOUNKLHFCQUFxQnNNLGlCQUFpQmpNLFdBQVcsS0FBSzs0QkFDdERGLGVBQWVtTSxpQkFBaUJuTSxhQUFhLElBQUksQ0FBQzs0QkFDbERELGNBQWNxTTs0QkFDZHRNLG1CQUFtQkE7NEJBQ25CTixVQUFVMk0sRUFBQUEsa0NBQUFBLGlCQUFpQm5NLGFBQWEsY0FBOUJtTSx1REFBQUEsd0NBQUFBLGdDQUFnQy9MLEtBQUssY0FBckMrTCw0REFBQUEsc0NBQXVDOUwsR0FBRyxLQUFJOzRCQUN4RFosV0FBVzBNLEVBQUFBLG1DQUFBQSxpQkFBaUJuTSxhQUFhLGNBQTlCbU0sd0RBQUFBLHlDQUFBQSxpQ0FBZ0MvTCxLQUFLLGNBQXJDK0wsNkRBQUFBLHVDQUF1QzdMLElBQUksS0FBSTs0QkFDMURYLGVBQWV3TSxFQUFBQSxtQ0FBQUEsaUJBQWlCbk0sYUFBYSxjQUE5Qm1NLHdEQUFBQSwyQ0FBQUEsaUNBQWdDNUwsUUFBUSxjQUF4QzRMLCtEQUFBQSx5Q0FBMEM5TCxHQUFHLEtBQUk7NEJBQ2hFVCxnQkFBZ0J1TSxFQUFBQSxtQ0FBQUEsaUJBQWlCbk0sYUFBYSxjQUE5Qm1NLHdEQUFBQSw0Q0FBQUEsaUNBQWdDNUwsUUFBUSxjQUF4QzRMLGdFQUFBQSwwQ0FBMEM3TCxJQUFJLEtBQUk7d0JBQ3BFOztnQkFDRixPQUFPO29CQUNMakcsUUFBUWdCLEtBQUssQ0FBQyxlQUFlNlEsSUFBSTNJLE9BQU87b0JBQ3hDLGFBQWE7b0JBQ2JuRSxnQkFBZ0I2RSxDQUFBQSxPQUFTOzRCQUN2QixHQUFHQSxJQUFJOzRCQUNQNUUsT0FBT2dMLGVBQWVoTCxLQUFLOzRCQUMzQkMsYUFBYStLLGVBQWUvSyxXQUFXOzRCQUN2Q0MsWUFBWThLLGVBQWU5SyxVQUFVLElBQUk7NEJBQ3pDRyxnQkFBZ0IySyxlQUFlcEssUUFBUSxLQUFLOzRCQUM1Q0oscUJBQXFCd0ssZUFBZW5LLFdBQVcsS0FBSzs0QkFDcERKLG1CQUFtQixFQUFFLENBQUMsU0FBUzt3QkFDakM7Z0JBQ0Y7WUFDRixFQUFFLE9BQU96RSxPQUFPO2dCQUNkaEIsUUFBUWdCLEtBQUssQ0FBQyxlQUFlQTtnQkFDN0IsYUFBYTtnQkFDYitELGdCQUFnQjZFLENBQUFBLE9BQVM7d0JBQ3ZCLEdBQUdBLElBQUk7d0JBQ1A1RSxPQUFPZ0wsZUFBZWhMLEtBQUs7d0JBQzNCQyxhQUFhK0ssZUFBZS9LLFdBQVc7d0JBQ3ZDQyxZQUFZOEssZUFBZTlLLFVBQVUsSUFBSTt3QkFDekNHLGdCQUFnQjJLLGVBQWVwSyxRQUFRLEtBQUs7d0JBQzVDSixxQkFBcUJ3SyxlQUFlbkssV0FBVyxLQUFLO3dCQUNwREosbUJBQW1CLEVBQUUsQ0FBQyxTQUFTO29CQUNqQztZQUNGO1FBQ0Y7SUFDRjtJQUVBLFVBQVU7SUFDVixNQUFNbEUsb0JBQW9CO1FBQ3hCLE9BQU9FLFdBQVd3TyxJQUFJLENBQUNqRSxDQUFBQSxTQUFVQSxPQUFPckQsRUFBRSxLQUFLM0c7SUFDakQ7SUFFQSxJQUFJLENBQUNkLFdBQVcsT0FBTztJQUV2QixxQkFDRSw4REFBQzhRO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDRDtnQkFBSUMsV0FBVTs7a0NBRWIsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDQzt3Q0FBR0QsV0FBVTtrREFBb0I7Ozs7OztrREFDbEMsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0U7Z0RBQ0NDLFNBQVNaO2dEQUNUUyxXQUFXLDRCQUEwRSxPQUE5Q25RLG1CQUFtQixhQUFhLFdBQVc7MERBRWxGLDRFQUFDM0MsOEdBQVFBO29EQUFDOFMsV0FBVTs7Ozs7Ozs7Ozs7MERBRXRCLDhEQUFDRTtnREFBT0MsU0FBU3JIO2dEQUFja0gsV0FBVTswREFDdkMsNEVBQUM3Uyw4R0FBSUE7b0RBQUM2UyxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FJdEIsOERBQUNFO2dDQUFPQyxTQUFTalI7Z0NBQVM4USxXQUFVOzBDQUNsQyw0RUFBQy9TLDhHQUFDQTtvQ0FBQytTLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQUtqQiw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUViLDhEQUFDRDtnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQ0Q7b0NBQUlDLFdBQVU7OENBQ1pyUSx3QkFDQyw4REFBQ29RO3dDQUFJQyxXQUFVO2tEQUNiLDRFQUFDSTtzREFBRTs7Ozs7Ozs7OztvREFFSDVRLFdBQVdxSCxNQUFNLEtBQUssa0JBQ3hCLDhEQUFDa0o7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDRDtnREFBSUMsV0FBVTswREFDYiw0RUFBQzVTLDhHQUFRQTtvREFBQzRTLFdBQVU7Ozs7Ozs7Ozs7OzBEQUV0Qiw4REFBQ0s7Z0RBQUdMLFdBQVU7MERBQTBCOzs7Ozs7MERBQ3hDLDhEQUFDSTtnREFBRUosV0FBVTswREFBZ0M7Ozs7OzswREFHN0MsOERBQUNFO2dEQUNDQyxTQUFTckg7Z0RBQ1RrSCxXQUFVOztrRUFFViw4REFBQzdTLDhHQUFJQTt3REFBQzZTLFdBQVU7Ozs7OztvREFBWTs7Ozs7Ozs7Ozs7O29EQUtoQ3hRLFdBQVdnSCxHQUFHLENBQUMsQ0FBQ3VELHVCQUNkLDhEQUFDZ0c7NENBRUNDLFdBQVcsb0JBQW1FLE9BQS9DalEscUJBQXFCZ0ssT0FBT3JELEVBQUUsR0FBRyxXQUFXOzRDQUMzRXlKLFNBQVMsSUFBTXhHLGdCQUFnQkksT0FBT3JELEVBQUU7OzhEQUV4Qyw4REFBQ3FKO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ007NERBQUtOLFdBQVU7c0VBQXlCakcsT0FBT2hILEtBQUs7Ozs7OztzRUFDckQsOERBQUN1Tjs0REFBS04sV0FBVyx1QkFBbUUsT0FBNUNqRyxPQUFPakwsTUFBTSxLQUFLLElBQUksY0FBYztzRUFDekVpTCxPQUFPakwsTUFBTSxLQUFLLElBQUksUUFBUTs7Ozs7Ozs7Ozs7OzhEQUduQyw4REFBQ29SO29EQUNDQyxTQUFTLENBQUN6RTt3REFDUkEsRUFBRTZFLGVBQWU7d0RBQ2pCM0csa0JBQWtCRyxPQUFPckQsRUFBRTtvREFDN0I7b0RBQ0FzSixXQUFVOzhEQUVWLDRFQUFDL1MsOEdBQUNBO3dEQUFDK1MsV0FBVTs7Ozs7Ozs7Ozs7OzJDQWpCVmpHLE9BQU9yRCxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7MENBMEJ4Qiw4REFBQ3FKO2dDQUFJQyxXQUFVOztvQ0FDWm5RLG1CQUFtQix3QkFDbEIsOERBQUNrUTt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEO2dEQUFJQyxXQUFVOzBEQUNiLDRFQUFDM1MsOEdBQVFBO29EQUFDMlMsV0FBVTs7Ozs7Ozs7Ozs7MERBRXRCLDhEQUFDSztnREFBR0wsV0FBVTswREFBMEI7Ozs7OzswREFDeEMsOERBQUNJO2dEQUFFSixXQUFVOzBEQUFnQzs7Ozs7Ozs7Ozs7O29DQU1oRG5RLG1CQUFtQiw0QkFDbEI7OzBEQUVFLDhEQUFDa1E7Z0RBQUlDLFdBQVU7MERBQ1ozUSxpQ0FDQyw4REFBQ21SO29EQUNDQyxLQUFLcFI7b0RBQ0xxUixLQUFJO29EQUNKVixXQUFVOzs7Ozs4RUFHWiw4REFBQ0Q7b0RBQUlDLFdBQVU7OERBQ2IsNEVBQUNNO2tFQUFLOzs7Ozs7Ozs7Ozs7Ozs7OzBEQU1aLDhEQUFDUDtnREFBSUMsV0FBVTs7a0VBRWIsOERBQUNEO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ1c7Z0VBQU1YLFdBQVU7MEVBQW9COzs7Ozs7MEVBQ3JDLDhEQUFDMUU7Z0VBQ0MzRSxNQUFLO2dFQUNMaUssT0FBT3hQO2dFQUNQeVAsVUFBVSxDQUFDbkYsSUFBTXJLLGdCQUFnQnFLLEVBQUVuQixNQUFNLENBQUNxRyxLQUFLO2dFQUMvQ1osV0FBVTtnRUFDVmMsYUFBWTs7Ozs7Ozs7Ozs7O2tFQUtoQiw4REFBQ2Y7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDVztnRUFBTVgsV0FBVTswRUFBb0I7Ozs7OzswRUFDckMsOERBQUN0UywyRUFBTUE7Z0VBQ0xxVCxNQUFLO2dFQUNMQyxPQUFPO29FQUFFQyxPQUFPO2dFQUFPO2dFQUN2QkgsYUFBWTtnRUFDWkYsT0FBT2xPO2dFQUNQbU8sVUFBVWxPO2dFQUNWaEQsU0FBU2lEO2dFQUNUc08sU0FBUzFPLFdBQVdnRSxHQUFHLENBQUMwQixDQUFBQTtvRUFDdEJuSyxRQUFRQyxHQUFHLENBQUMseUJBQWVrSztvRUFDM0IsT0FBTzt3RUFDTHlJLHFCQUNFLDhEQUFDTDs0RUFBS1UsT0FBTztnRkFBRUcsT0FBT2pKLElBQUlpSixLQUFLOzRFQUFDO3NGQUM3QmpKLElBQUlsRSxJQUFJOzs7Ozs7d0VBR2I0TSxPQUFPMUksSUFBSXhCLEVBQUU7b0VBQ2Y7Z0VBQ0Y7Ozs7OzswRUFHRiw4REFBQ3FKO2dFQUFJaUIsT0FBTztvRUFBRUksVUFBVTtvRUFBUUQsT0FBTztvRUFBUUUsV0FBVztnRUFBTTs7b0VBQUc7b0VBQ3JEN08sV0FBV3FFLE1BQU07b0VBQUM7b0VBQVNqRSxjQUFjLE1BQU07Ozs7Ozs7Ozs7Ozs7a0VBSy9ELDhEQUFDbU47d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDVztnRUFBTVgsV0FBVTswRUFBb0I7Ozs7OzswRUFDckMsOERBQUMxRTtnRUFDQzNFLE1BQUs7Z0VBQ0xpSyxPQUFPbFA7Z0VBQ1BtUCxVQUFVLENBQUNuRixJQUFNL0osa0JBQWtCK0osRUFBRW5CLE1BQU0sQ0FBQ3FHLEtBQUs7Z0VBQ2pEWixXQUFVO2dFQUNWYyxhQUFZOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OztvQ0FPckJqUixtQkFBbUIsWUFBWVAscUNBQzlCO2tEQUVFLDRFQUFDeVE7NENBQUlDLFdBQVU7OzhEQUViLDhEQUFDRDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNEOzREQUFJQyxXQUFVOzs4RUFDYiw4REFBQ0Q7b0VBQ0NDLFdBQVU7b0VBQ1ZHLFNBQVM7NEVBQU1sTTtnRkFBQUEsMkJBQUFBLFNBQVNxTixjQUFjLENBQUMsbUNBQXhCck4sK0NBQUFBLHlCQUErQzBILEtBQUs7OzhFQUVsRXBNLGFBQWEwRCxVQUFVLE1BQUkzRCxxQkFBQUEsaUNBQUFBLHlDQUFBQSxtQkFBcUIyRCxVQUFVLGtCQUN6RCw4REFBQ3VOO3dFQUNDQyxLQUFLbFIsYUFBYTBELFVBQVUsTUFBSTNELHNCQUFBQSxpQ0FBQUEsMENBQUFBLG9CQUFxQjJELFVBQVU7d0VBQy9EeU4sS0FBSTt3RUFDSlYsV0FBVTs7Ozs7a0dBR1osOERBQUNEO3dFQUFJQyxXQUFVO2tGQUNiLDRFQUFDTTtzRkFBSzs7Ozs7Ozs7Ozs7Ozs7Ozs4RUFJWiw4REFBQ2hGO29FQUNDNUUsSUFBRztvRUFDSEMsTUFBSztvRUFDTDZFLFFBQU87b0VBQ1BxRixVQUFVekc7b0VBQ1Y0RyxPQUFPO3dFQUFFTyxTQUFTO29FQUFPOzs7Ozs7OEVBRzNCLDhEQUFDckI7b0VBQ0NGLFdBQVU7b0VBQ1ZHLFNBQVM7d0VBQ1AsaUJBQWlCO3dFQUNqQnBTLFFBQVFDLEdBQUcsQ0FBQzt3RUFDWnlCLGFBQWFzTixJQUFJLENBQUM7b0VBQ3BCOzhFQUNEOzs7Ozs7Ozs7Ozs7c0VBSUgsOERBQUNnRDs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUNEO29FQUFJQyxXQUFVOztzRkFDYiw4REFBQ1c7c0ZBQU07Ozs7OztzRkFDUCw4REFBQ3JGOzRFQUNDM0UsTUFBSzs0RUFDTGlLLE9BQU9yUixhQUFhd0QsS0FBSyxNQUFJekQsc0JBQUFBLGlDQUFBQSwwQ0FBQUEsb0JBQXFCeUQsS0FBSyxLQUFJOzRFQUMzRDhOLFVBQVUsQ0FBQ25GO2dGQUNUNUksZ0JBQWdCNkUsQ0FBQUEsT0FBUzt3RkFBRSxHQUFHQSxJQUFJO3dGQUFFNUUsT0FBTzJJLEVBQUVuQixNQUFNLENBQUNxRyxLQUFLO29GQUFDO2dGQUMxRDFHLGtCQUFrQm5LLGtCQUFtQjJMLEVBQUVuQixNQUFNLENBQUNxRyxLQUFLOzRFQUNyRDs0RUFDQUUsYUFBWTs7Ozs7Ozs7Ozs7OzhFQUdoQiw4REFBQ2Y7b0VBQUlDLFdBQVU7O3NGQUNiLDhEQUFDVztzRkFBTTs7Ozs7O3NGQUNQLDhEQUFDYTs0RUFDQ1osT0FBT3JSLGFBQWF5RCxXQUFXLE1BQUkxRCxzQkFBQUEsaUNBQUFBLDBDQUFBQSxvQkFBcUIwRCxXQUFXLEtBQUk7NEVBQ3ZFNk4sVUFBVSxDQUFDbkYsSUFBTTVJLGdCQUFnQjZFLENBQUFBLE9BQVM7d0ZBQUUsR0FBR0EsSUFBSTt3RkFBRTNFLGFBQWEwSSxFQUFFbkIsTUFBTSxDQUFDcUcsS0FBSztvRkFBQzs0RUFDakZFLGFBQVk7NEVBQ1pXLE1BQU07Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4REFPZCw4REFBQzFCO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ0s7c0VBQUc7Ozs7OztzRUFHSiw4REFBQ047NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDRDtvRUFBSUMsV0FBVTs7c0ZBQ2IsOERBQUNNO3NGQUFLOzs7Ozs7c0ZBQ04sOERBQUNLOzRFQUFNWCxXQUFVOzs4RkFDZiw4REFBQzFFO29GQUNDM0UsTUFBSztvRkFDTCtLLFNBQVNuUyxhQUFhNkQsY0FBYztvRkFDcEN5TixVQUFVLENBQUNuRixJQUFNNUksZ0JBQWdCNkUsQ0FBQUEsT0FBUztnR0FBRSxHQUFHQSxJQUFJO2dHQUFFdkUsZ0JBQWdCc0ksRUFBRW5CLE1BQU0sQ0FBQ21ILE9BQU87NEZBQUM7Ozs7Ozs4RkFFeEYsOERBQUNwQjtvRkFBS04sV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7O2dFQUduQnpRLGFBQWE2RCxjQUFjLGtCQUMxQiw4REFBQzJNO29FQUFJQyxXQUFVOzhFQUVaelEsRUFBQUEsOEJBQUFBLGFBQWFtRSxhQUFhLGNBQTFCbkUsbURBQUFBLG9DQUFBQSw0QkFBNEJ1RSxLQUFLLGNBQWpDdkUsd0RBQUFBLGtDQUFtQ3dFLEdBQUcsa0JBQ3JDLDhEQUFDZ007d0VBQUlDLFdBQVU7OzBGQUNiLDhEQUFDRDtnRkFBSUMsV0FBVTswRkFDYiw0RUFBQ2xNO29GQUNDa00sV0FBVTtvRkFDVjJCLFFBQVE7b0ZBQ1JDLFFBQVFyUyxhQUFhMEQsVUFBVTs7c0dBRS9CLDhEQUFDNE87NEZBQU9wQixLQUFLbFIsYUFBYW1FLGFBQWEsQ0FBQ0ksS0FBSyxDQUFDQyxHQUFHOzRGQUFFNEMsTUFBSzs7Ozs7O3dGQUFjOzs7Ozs7Ozs7Ozs7MEZBSTFFLDhEQUFDb0o7Z0ZBQUlDLFdBQVU7MEZBQXVCelEsYUFBYW1FLGFBQWEsQ0FBQ0ksS0FBSyxDQUFDRSxJQUFJOzs7Ozs7MEZBQzNFLDhEQUFDa007Z0ZBQU9GLFdBQVU7Z0ZBQXdCRyxTQUFTOUU7MEZBQ2pELDRFQUFDaUY7OEZBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7a0dBSVYsOERBQUNQO3dFQUFJQyxXQUFVOzswRkFDYiw4REFBQ0Q7Z0ZBQUlDLFdBQVU7MEZBQ2IsNEVBQUNEO29GQUFJQyxXQUFVOzhGQUFZOzs7Ozs7Ozs7OzswRkFFN0IsOERBQUNFO2dGQUFPRixXQUFVO2dGQUF3QkcsU0FBUzlFOzBGQUNqRCw0RUFBQ2lGOzhGQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NFQVNsQiw4REFBQ1A7NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDRDtvRUFBSUMsV0FBVTs7c0ZBQ2IsOERBQUNNO3NGQUFLOzs7Ozs7c0ZBQ04sOERBQUNLOzRFQUFNWCxXQUFVOzs4RkFDZiw4REFBQzFFO29GQUNDM0UsTUFBSztvRkFDTCtLLFNBQVNuUyxhQUFhZ0UsbUJBQW1CO29GQUN6Q3NOLFVBQVUsQ0FBQ25GLElBQU01SSxnQkFBZ0I2RSxDQUFBQSxPQUFTO2dHQUFFLEdBQUdBLElBQUk7Z0dBQUVwRSxxQkFBcUJtSSxFQUFFbkIsTUFBTSxDQUFDbUgsT0FBTzs0RkFBQzs7Ozs7OzhGQUU3Riw4REFBQ3BCO29GQUFLTixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Z0VBR25CelEsYUFBYWdFLG1CQUFtQixrQkFDL0IsOERBQUN3TTtvRUFBSUMsV0FBVTs4RUFFWnpRLEVBQUFBLCtCQUFBQSxhQUFhbUUsYUFBYSxjQUExQm5FLG9EQUFBQSx1Q0FBQUEsNkJBQTRCMEUsUUFBUSxjQUFwQzFFLDJEQUFBQSxxQ0FBc0N3RSxHQUFHLGtCQUN4Qyw4REFBQ2dNO3dFQUFJQyxXQUFVOzswRkFDYiw4REFBQ0Q7Z0ZBQUlDLFdBQVU7O2tHQUNiLDhEQUFDRDt3RkFBSUMsV0FBVTtrR0FBZ0I7Ozs7OztrR0FDL0IsOERBQUNEO3dGQUFJQyxXQUFVO2tHQUNiLDRFQUFDRDs0RkFBSUMsV0FBVTtzR0FBbUJ6USxhQUFhbUUsYUFBYSxDQUFDTyxRQUFRLENBQUNELElBQUk7Ozs7Ozs7Ozs7Ozs7Ozs7OzBGQUc5RSw4REFBQ2tNO2dGQUFPRixXQUFVO2dGQUF3QkcsU0FBU3JFOzBGQUNqRCw0RUFBQ3dFOzhGQUFLOzs7Ozs7Ozs7Ozs7Ozs7O2tHQUlWLDhEQUFDUDt3RUFBSUMsV0FBVTtrRkFDYiw0RUFBQ0U7NEVBQU9GLFdBQVU7NEVBQXdCRyxTQUFTckU7c0ZBQ2pELDRFQUFDd0U7MEZBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzRUFTbEIsOERBQUNQOzREQUFJQyxXQUFVOzs4RUFDYiw4REFBQ0Q7b0VBQUlDLFdBQVU7OEVBQ2IsNEVBQUNNO2tGQUFLOzs7Ozs7Ozs7Ozs4RUFFUiw4REFBQ1A7b0VBQUlDLFdBQVU7O3NGQUNiLDhEQUFDRTs0RUFBT0YsV0FBVTs0RUFBbUJHLFNBQVM5RDs7OEZBQzVDLDhEQUFDaUU7OEZBQUs7Ozs7Ozs4RkFDTiw4REFBQ0E7OEZBQUs7Ozs7Ozs7Ozs7Ozt3RUFFUC9RLGFBQWFpRSxpQkFBaUIsSUFBSWpFLGFBQWFpRSxpQkFBaUIsQ0FBQ3FELE1BQU0sR0FBRyxJQUN6RXRILGFBQWFpRSxpQkFBaUIsQ0FBQ2dELEdBQUcsQ0FBQyxDQUFDNkgsVUFBVWxHLHNCQUM1Qyw4REFBQzRIO2dGQUFnQkMsV0FBVTs7a0dBQ3pCLDhEQUFDTTt3RkFDQ04sV0FBVTt3RkFDVkcsU0FBUzs0RkFDUCxJQUFJOUIsU0FBU3RLLEdBQUcsRUFBRTtnR0FDaEIrTixPQUFPQyxJQUFJLENBQUMxRCxTQUFTdEssR0FBRyxFQUFFOzRGQUM1Qjt3RkFDRjt3RkFDQWlOLE9BQU87NEZBQ0xnQixRQUFRM0QsU0FBU3RLLEdBQUcsR0FBRyxZQUFZOzRGQUNuQ29OLE9BQU85QyxTQUFTdEssR0FBRyxHQUFHLFlBQVk7NEZBQ2xDa08sZ0JBQWdCNUQsU0FBU3RLLEdBQUcsR0FBRyxjQUFjO3dGQUMvQzt3RkFDQWhCLE9BQU9zTCxTQUFTdEssR0FBRyxHQUFHLFdBQVdzSyxTQUFTckssSUFBSTs7NEZBQy9DOzRGQUNLcUssU0FBU3JLLElBQUk7Ozs7Ozs7a0dBRW5CLDhEQUFDa007d0ZBQ0NGLFdBQVU7d0ZBQ1ZHLFNBQVMsSUFBTTdELHVCQUF1Qm5FO3dGQUN0Q3BGLE9BQU07a0dBQ1A7Ozs7Ozs7K0VBckJPb0Y7Ozs7MkdBMkJaLDhEQUFDNEg7NEVBQUlDLFdBQVU7c0ZBQ2IsNEVBQUNNO2dGQUFLVSxPQUFPO29GQUFFRyxPQUFPO29GQUFRQyxVQUFVO2dGQUFPOzBGQUFHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4REFRNUQsOERBQUNyQjtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNEOzREQUFJQyxXQUFVOzs4RUFDYiw4REFBQ0s7OEVBQUc7Ozs7Ozs4RUFDSiw4REFBQ0g7b0VBQ0NGLFdBQVU7b0VBQ1ZHLFNBQVMxRDtvRUFDVDFKLE9BQU07OEVBQ1A7Ozs7Ozs7Ozs7OztzRUFJSCw4REFBQ2dOOzREQUFJQyxXQUFVO3NFQUNaelEsYUFBYWtFLFlBQVksSUFBSWxFLGFBQWFrRSxZQUFZLENBQUNvRCxNQUFNLEdBQUcsSUFDL0R0SCxhQUFha0UsWUFBWSxDQUFDK0MsR0FBRyxDQUFDLENBQUN1RyxNQUFNNUUsc0JBQ25DLDhEQUFDNEg7b0VBQWdCQyxXQUFVOztzRkFDekIsOERBQUNEOzRFQUFJQyxXQUFVOzs4RkFDYiw4REFBQ007b0ZBQUtOLFdBQVU7O3dGQUFhO3dGQUFNN0gsUUFBUTs7Ozs7Ozs4RkFDM0MsOERBQUMrSDtvRkFDQ0YsV0FBVTtvRkFDVkcsU0FBUyxJQUFNekQsdUJBQXVCdkU7b0ZBQ3RDcEYsT0FBTTs4RkFDUDs7Ozs7Ozs7Ozs7O3NGQUlILDhEQUFDZ047NEVBQUlDLFdBQVU7OzhGQUNiLDhEQUFDRDtvRkFBSUMsV0FBVTs7c0dBQ2IsOERBQUNXO3NHQUFNOzs7Ozs7c0dBQ1AsOERBQUNyRjs0RkFDQzNFLE1BQUs7NEZBQ0xpSyxPQUFPN0QsS0FBS2hLLEtBQUs7NEZBQ2pCOE4sVUFBVSxDQUFDbkYsSUFBTWlCLHdCQUF3QnhFLE9BQU91RCxFQUFFbkIsTUFBTSxDQUFDcUcsS0FBSzs0RkFDOURFLGFBQVk7NEZBQ1pkLFdBQVU7Ozs7Ozs7Ozs7Ozs4RkFHZCw4REFBQ0Q7b0ZBQUlDLFdBQVU7O3NHQUNiLDhEQUFDVztzR0FBTTs7Ozs7O3NHQUNQLDhEQUFDYTs0RkFDQ1osT0FBTzdELEtBQUsxSSxPQUFPOzRGQUNuQndNLFVBQVUsQ0FBQ25GLElBQU1tQiwwQkFBMEIxRSxPQUFPdUQsRUFBRW5CLE1BQU0sQ0FBQ3FHLEtBQUs7NEZBQ2hFRSxhQUFZOzRGQUNaZCxXQUFVOzRGQUNWeUIsTUFBTTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzttRUE3Qkp0Sjs7OzsrRkFvQ1osOERBQUM0SDtnRUFBSUMsV0FBVTswRUFDYiw0RUFBQ0k7OEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OERBT1gsOERBQUNMO29EQUFJQyxXQUFVOzhEQUNiLDRFQUFDRDt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUNEO2dFQUFJQyxXQUFVOztrRkFDYiw4REFBQ007a0ZBQUs7Ozs7OztrRkFDTiw4REFBQ0s7d0VBQU1YLFdBQVU7OzBGQUNmLDhEQUFDMUU7Z0ZBQ0MzRSxNQUFLO2dGQUNMK0ssU0FBU25TLGFBQWErRSxZQUFZO2dGQUNsQ3VNLFVBQVUsQ0FBQ25GLElBQU01SSxnQkFBZ0I2RSxDQUFBQSxPQUFTOzRGQUFFLEdBQUdBLElBQUk7NEZBQUVyRCxjQUFjb0gsRUFBRW5CLE1BQU0sQ0FBQ21ILE9BQU87d0ZBQUM7Ozs7OzswRkFFdEYsOERBQUNwQjtnRkFBS04sV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzREQUluQnpRLGFBQWErRSxZQUFZLGtCQUN4Qjs7a0ZBQ0UsOERBQUN5TDt3RUFBSUMsV0FBVTs7MEZBQ2IsOERBQUNNOzBGQUFLOzs7Ozs7MEZBQ04sOERBQUNLO2dGQUFNWCxXQUFVOztrR0FDZiw4REFBQzFFO3dGQUNDM0UsTUFBSzt3RkFDTCtLLFNBQVNuUyxhQUFhZ0YscUJBQXFCO3dGQUMzQ3NNLFVBQVUsQ0FBQ25GLElBQU01SSxnQkFBZ0I2RSxDQUFBQSxPQUFTO29HQUFFLEdBQUdBLElBQUk7b0dBQUVwRCx1QkFBdUJtSCxFQUFFbkIsTUFBTSxDQUFDbUgsT0FBTztnR0FBQzs7Ozs7O2tHQUUvRiw4REFBQ3BCO3dGQUFLTixXQUFVOzs7Ozs7Ozs7Ozs7NEVBRWpCelEsYUFBYWdGLHFCQUFxQixrQkFDakMsOERBQUN3TDtnRkFBSUMsV0FBVTs7a0dBQ2IsOERBQUNFO3dGQUNDRixXQUFVO3dGQUNWRyxTQUFTUjtrR0FDVjs7Ozs7O2tHQUdELDhEQUFDSTt3RkFBSUMsV0FBVTtrR0FDYiw0RUFBQ007c0dBQU0vUSxhQUFha0YsZ0JBQWdCLElBQUk7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tGQU1oRCw4REFBQ3NMO3dFQUFJQyxXQUFVOzswRkFDYiw4REFBQ007MEZBQUs7Ozs7OzswRkFDTiw4REFBQ0s7Z0ZBQU1YLFdBQVU7O2tHQUNmLDhEQUFDMUU7d0ZBQ0MzRSxNQUFLO3dGQUNMK0ssU0FBU25TLGFBQWFtRixtQkFBbUI7d0ZBQ3pDbU0sVUFBVSxDQUFDbkYsSUFBTTVJLGdCQUFnQjZFLENBQUFBLE9BQVM7b0dBQUUsR0FBR0EsSUFBSTtvR0FBRWpELHFCQUFxQmdILEVBQUVuQixNQUFNLENBQUNtSCxPQUFPO2dHQUFDOzs7Ozs7a0dBRTdGLDhEQUFDcEI7d0ZBQUtOLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7OztvRUFJbkJ6USxhQUFhbUYsbUJBQW1CLGtCQUMvQiw4REFBQ3FMO3dFQUFJQyxXQUFVOzswRkFDYiw4REFBQ007MEZBQUs7Ozs7OzswRkFDTiw4REFBQ2hGO2dGQUNDM0UsTUFBSztnRkFDTGlLLE9BQU9yUixhQUFhb0YsY0FBYyxJQUFJO2dGQUN0Q2tNLFVBQVUsQ0FBQ25GLElBQU01SSxnQkFBZ0I2RSxDQUFBQSxPQUFTOzRGQUFFLEdBQUdBLElBQUk7NEZBQUVoRCxnQkFBZ0IrRyxFQUFFbkIsTUFBTSxDQUFDcUcsS0FBSzt3RkFBQztnRkFDcEZFLGFBQVk7Z0ZBQ1pkLFdBQVU7Ozs7Ozs7Ozs7OztrRkFLaEIsOERBQUNEO3dFQUFJQyxXQUFVOzswRkFDYiw4REFBQ007MEZBQUs7Ozs7OzswRkFDTiw4REFBQ0s7Z0ZBQU1YLFdBQVU7O2tHQUNmLDhEQUFDMUU7d0ZBQ0MzRSxNQUFLO3dGQUNMK0ssU0FBU25TLGFBQWFzRixtQkFBbUI7d0ZBQ3pDZ00sVUFBVSxDQUFDbkYsSUFBTTVJLGdCQUFnQjZFLENBQUFBLE9BQVM7b0dBQUUsR0FBR0EsSUFBSTtvR0FBRTlDLHFCQUFxQjZHLEVBQUVuQixNQUFNLENBQUNtSCxPQUFPO2dHQUFDOzs7Ozs7a0dBRTdGLDhEQUFDcEI7d0ZBQUtOLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7OztvRUFLbkJ6USxhQUFhc0YsbUJBQW1CLGtCQUMvQiw4REFBQ2tMO3dFQUFJQyxXQUFVOzswRkFFYiw4REFBQ0Q7Z0ZBQUlDLFdBQVU7O2tHQUNiLDhEQUFDRDt3RkFBSUMsV0FBVTs7MEdBQ2IsOERBQUNXOzBHQUFNOzs7Ozs7MEdBQ1AsOERBQUNyRjtnR0FDQzNFLE1BQUs7Z0dBQ0xpSyxPQUFPclIsYUFBYWdHLFVBQVUsQ0FBQ0MsUUFBUTtnR0FDdkNxTCxVQUFVLENBQUNuRixJQUFNNUksZ0JBQWdCNkUsQ0FBQUEsT0FBUzs0R0FDeEMsR0FBR0EsSUFBSTs0R0FDUHBDLFlBQVk7Z0hBQUUsR0FBR29DLEtBQUtwQyxVQUFVO2dIQUFFQyxVQUFVa0csRUFBRW5CLE1BQU0sQ0FBQ3FHLEtBQUs7NEdBQUM7d0dBQzdEO2dHQUNBRSxhQUFZOzs7Ozs7Ozs7Ozs7a0dBR2hCLDhEQUFDZjt3RkFBSUMsV0FBVTs7MEdBQ2IsOERBQUNXOzBHQUFNOzs7Ozs7MEdBQ1AsOERBQUNyRjtnR0FDQzNFLE1BQUs7Z0dBQ0xpSyxPQUFPclIsYUFBYWdHLFVBQVUsQ0FBQ0UsWUFBWTtnR0FDM0NvTCxVQUFVLENBQUNuRixJQUFNNUksZ0JBQWdCNkUsQ0FBQUEsT0FBUzs0R0FDeEMsR0FBR0EsSUFBSTs0R0FDUHBDLFlBQVk7Z0hBQUUsR0FBR29DLEtBQUtwQyxVQUFVO2dIQUFFRSxjQUFjaUcsRUFBRW5CLE1BQU0sQ0FBQ3FHLEtBQUs7NEdBQUM7d0dBQ2pFO2dHQUNBRSxhQUFZOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MEZBTWxCLDhEQUFDZjtnRkFBSUMsV0FBVTs7a0dBQ2IsOERBQUNXO2tHQUFNOzs7Ozs7a0dBQ1AsOERBQUNhO3dGQUNDWixPQUFPclIsYUFBYWdHLFVBQVUsQ0FBQ0csZUFBZTt3RkFDOUNtTCxVQUFVLENBQUNuRixJQUFNNUksZ0JBQWdCNkUsQ0FBQUEsT0FBUztvR0FDeEMsR0FBR0EsSUFBSTtvR0FDUHBDLFlBQVk7d0dBQUUsR0FBR29DLEtBQUtwQyxVQUFVO3dHQUFFRyxpQkFBaUJnRyxFQUFFbkIsTUFBTSxDQUFDcUcsS0FBSztvR0FBQztnR0FDcEU7d0ZBQ0FFLGFBQVk7d0ZBQ1pXLE1BQU07Ozs7Ozs7Ozs7OzswRkFLViw4REFBQzFCO2dGQUFJQyxXQUFVOztrR0FDYiw4REFBQ1c7OzRGQUFNOzBHQUFPLDhEQUFDTDtnR0FBS04sV0FBVTswR0FBZXpRLGFBQWFnRyxVQUFVLENBQUNJLG1CQUFtQixDQUFDa0IsTUFBTTs7Ozs7Ozs7Ozs7O29GQUM5RnRILGFBQWFnRyxVQUFVLENBQUNJLG1CQUFtQixDQUFDYSxHQUFHLENBQUMsQ0FBQzJGLE1BQU1oRSxzQkFDdEQsOERBQUM0SDs0RkFBZ0JDLFdBQVU7c0dBQ3pCLDRFQUFDMUU7Z0dBQ0MzRSxNQUFLO2dHQUNMaUssT0FBT3pFO2dHQUNQMEUsVUFBVSxDQUFDbkY7b0dBQ1QsTUFBTXdHLFdBQVc7MkdBQUkzUyxhQUFhZ0csVUFBVSxDQUFDSSxtQkFBbUI7cUdBQUM7b0dBQ2pFdU0sUUFBUSxDQUFDL0osTUFBTSxHQUFHdUQsRUFBRW5CLE1BQU0sQ0FBQ3FHLEtBQUs7b0dBQ2hDOU4sZ0JBQWdCNkUsQ0FBQUEsT0FBUzs0R0FDdkIsR0FBR0EsSUFBSTs0R0FDUHBDLFlBQVk7Z0hBQUUsR0FBR29DLEtBQUtwQyxVQUFVO2dIQUFFSSxxQkFBcUJ1TTs0R0FBUzt3R0FDbEU7Z0dBQ0Y7Z0dBQ0FwQixhQUFZOzs7Ozs7MkZBWk4zSTs7Ozs7a0dBZ0JaLDhEQUFDK0g7d0ZBQ0N2SixNQUFLO3dGQUNMcUosV0FBVTt3RkFDVkcsU0FBUyxJQUFNck4sZ0JBQWdCNkUsQ0FBQUEsT0FBUztvR0FDdEMsR0FBR0EsSUFBSTtvR0FDUHBDLFlBQVk7d0dBQ1YsR0FBR29DLEtBQUtwQyxVQUFVO3dHQUNsQkkscUJBQXFCOytHQUFJZ0MsS0FBS3BDLFVBQVUsQ0FBQ0ksbUJBQW1COzRHQUFFO3lHQUFHO29HQUNuRTtnR0FDRjtrR0FDRDs7Ozs7Ozs7Ozs7OzBGQU1ILDhEQUFDb0s7Z0ZBQUlDLFdBQVU7O2tHQUNiLDhEQUFDVztrR0FBTTs7Ozs7O2tHQUNQLDhEQUFDWjt3RkFBSUMsV0FBVTtrR0FDYiw0RUFBQ0Q7NEZBQUlDLFdBQVU7OzhHQUNiLDhEQUFDSTtvR0FBRUosV0FBVTs4R0FBWTs7Ozs7OzhHQUN6Qiw4REFBQ0Q7b0dBQ0NDLFdBQVU7b0dBQ1ZnQixPQUFPO3dHQUNMbUIsV0FBVzt3R0FDWEgsUUFBUTt3R0FDUkksWUFBWTtvR0FDZDs4R0FFQ3RRLDZCQUNDLDhEQUFDaU87d0dBQUlDLFdBQVU7OzBIQUNiLDhEQUFDRDtnSEFBSUMsV0FBVTs7Ozs7OzBIQUNmLDhEQUFDTTswSEFBSzs7Ozs7Ozs7Ozs7b0hBRU4xTyxNQUFNaUYsTUFBTSxHQUFHLGtCQUNqQjs7MEhBQ0UsOERBQUNrSjtnSEFBSUMsV0FBVTswSEFDWnBPLE1BQU00RSxHQUFHLENBQUMsQ0FBQzZMLHFCQUNWLDhEQUFDdEM7d0hBRUNDLFdBQVcsYUFBaUUsT0FBcERoTyxnQkFBZ0IrRixRQUFRLENBQUNzSyxLQUFLM0wsRUFBRSxJQUFJLGFBQWE7d0hBQ3pFeUosU0FBUyxJQUFNdEksaUJBQWlCd0ssS0FBSzNMLEVBQUU7OzBJQUV2Qyw4REFBQ3FKO2dJQUFJQyxXQUFVOztvSUFDWnFDLEtBQUtwUCxVQUFVLElBQUlvUCxLQUFLQyxlQUFlLGlCQUN0Qyw4REFBQzlCO3dJQUNDQyxLQUFLNEIsS0FBS3BQLFVBQVUsSUFBSW9QLEtBQUtDLGVBQWU7d0lBQzVDNUIsS0FBSzJCLEtBQUt0UCxLQUFLO3dJQUNmd1AsU0FBUyxDQUFDN0c7NElBQ1IsTUFBTW5CLFNBQVNtQixFQUFFOEcsYUFBYTs0SUFDOUJqSSxPQUFPeUcsS0FBSyxDQUFDTyxPQUFPLEdBQUc7NElBQ3ZCLE1BQU1rQixjQUFjbEksT0FBT21JLGtCQUFrQjs0SUFDN0MsSUFBSUQsYUFBYTtnSkFDZkEsWUFBWXpCLEtBQUssQ0FBQ08sT0FBTyxHQUFHOzRJQUM5Qjt3SUFDRjs7Ozs7b0pBRUE7a0pBQ0osOERBQUN4Qjt3SUFBSUMsV0FBVTt3SUFBbUJnQixPQUFPOzRJQUFFTyxTQUFTYyxLQUFLcFAsVUFBVSxJQUFJb1AsS0FBS0MsZUFBZSxHQUFHLFNBQVM7d0lBQU87a0pBQUc7Ozs7Ozs7Ozs7OzswSUFJbkgsOERBQUN2QztnSUFBSUMsV0FBVTswSUFBY3FDLEtBQUt0UCxLQUFLLElBQUlzUCxLQUFLck8sSUFBSSxJQUFJcU8sS0FBS00sUUFBUSxJQUFJOzs7Ozs7NEhBQ3hFM1EsZ0JBQWdCK0YsUUFBUSxDQUFDc0ssS0FBSzNMLEVBQUUsbUJBQy9CLDhEQUFDcUo7Z0lBQUlDLFdBQVU7MElBQXFCOzs7Ozs7O3VIQXpCakNxQyxLQUFLM0wsRUFBRTs7Ozs7Ozs7Ozs0R0FnQ2pCdEUsOEJBQ0MsOERBQUMyTjtnSEFBSUMsV0FBVTswSEFDYiw0RUFBQ0U7b0hBQ0NGLFdBQVU7b0hBQ1ZHLFNBQVN2STtvSEFDVGdMLFVBQVV0UTs4SEFFVEEsaUNBQ0M7OzBJQUNFLDhEQUFDeU47Z0lBQUlDLFdBQVU7Ozs7OzswSUFDZiw4REFBQ007MElBQUs7Ozs7Ozs7dUlBR1I7Ozs7Ozs7Ozs7OztxSUFPViw4REFBQ1A7d0dBQUlDLFdBQVU7a0hBQ2IsNEVBQUNEOzRHQUFJQyxXQUFVO3NIQUFhOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEZBU3hDLDhEQUFDRDtnRkFBSUMsV0FBVTs7a0dBQ2IsOERBQUNXO2tHQUFNOzs7Ozs7a0dBQ1AsOERBQUNaO3dGQUFJQyxXQUFVO2tHQUNiLDRFQUFDRDs0RkFBSUMsV0FBVTs7OEdBQ2IsOERBQUNFO29HQUNDdkosTUFBSztvR0FDTHFKLFdBQVU7b0dBQ1ZHLFNBQVM7d0dBQ1AsU0FBUzt3R0FDVCxNQUFNN0UsUUFBUXJILFNBQVNzSCxhQUFhLENBQUM7d0dBQ3JDRCxNQUFNM0UsSUFBSSxHQUFHO3dHQUNiMkUsTUFBTUUsTUFBTSxHQUFHO3dHQUNmRixNQUFNRyxRQUFRLEdBQUcsQ0FBQ0M7Z0hBQ0g7NEdBQWIsTUFBTXBCLFFBQU8sb0JBQUdDLE1BQU0sQ0FBc0JDLEtBQUssY0FBcEMscURBQXNDLENBQUMsRUFBRTs0R0FDdEQsSUFBSUYsTUFBTTtnSEFDUnhILGdCQUFnQjZFLENBQUFBLE9BQVM7d0hBQ3ZCLEdBQUdBLElBQUk7d0hBQ1BwQyxZQUFZOzRIQUNWLEdBQUdvQyxLQUFLcEMsVUFBVTs0SEFDbEJNLG9CQUFvQjttSUFDZjhCLEtBQUtwQyxVQUFVLENBQUNNLGtCQUFrQjtnSUFDckM7b0lBQUVjLE1BQU07b0lBQVEzQyxNQUFNc0csS0FBS3RHLElBQUk7Z0lBQUM7NkhBQ2pDO3dIQUNIO29IQUNGOzRHQUNGO3dHQUNGO3dHQUNBc0gsTUFBTUssS0FBSztvR0FDYjs7c0hBRUEsOERBQUN4Tyw4R0FBSUE7NEdBQUN1TixNQUFNOzs7Ozs7d0dBQU07Ozs7Ozs7Z0dBR25CbkwsYUFBYWdHLFVBQVUsQ0FBQ00sa0JBQWtCLENBQUNXLEdBQUcsQ0FBQyxDQUFDOEcsVUFBVW5GLHNCQUN6RCw4REFBQzRIO3dHQUFnQkMsV0FBVTs7MEhBQ3pCLDhEQUFDTTswSEFBTWhELFNBQVN0SixJQUFJOzs7Ozs7MEhBQ3BCLDhEQUFDa007Z0hBQ0N2SixNQUFLO2dIQUNMcUosV0FBVTtnSEFDVkcsU0FBUztvSEFDUCxNQUFNMEMsZUFBZXRULGFBQWFnRyxVQUFVLENBQUNNLGtCQUFrQixDQUFDa0IsTUFBTSxDQUFDLENBQUN3RixHQUFHQyxJQUFNQSxNQUFNckU7b0hBQ3ZGckYsZ0JBQWdCNkUsQ0FBQUEsT0FBUzs0SEFDdkIsR0FBR0EsSUFBSTs0SEFDUHBDLFlBQVk7Z0lBQUUsR0FBR29DLEtBQUtwQyxVQUFVO2dJQUFFTSxvQkFBb0JnTjs0SEFBYTt3SEFDckU7Z0hBQ0Y7MEhBQ0Q7Ozs7Ozs7dUdBWk8xSzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQWlDeEMsOERBQUM0SDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNEO2dDQUFJQyxXQUFVOzBDQUNiLDRFQUFDRTtvQ0FDQ0MsU0FBUzdCO29DQUNUMEIsV0FBVTtvQ0FDVjRDLFVBQVVwVCxXQUFXcUgsTUFBTSxLQUFLLEtBQUtyRyxzQkFBc0JFLGlCQUFpQjtvQ0FDNUVxQyxPQUNFckMsaUJBQWlCLElBQ2IsWUFDQWxCLFdBQVdxSCxNQUFNLEtBQUssSUFDcEIseUJBQ0FyRyxxQkFDRSxnQkFDQTs4Q0FHVEUsaUJBQWlCLElBQ2QsUUFDQUYscUJBQ0UsWUFDQTs7Ozs7Ozs7Ozs7MENBSVYsOERBQUN1UDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNFO3dDQUFPQyxTQUFTYjt3Q0FBZ0JVLFdBQVU7a0RBQXVDOzs7Ozs7a0RBR2xGLDhEQUFDRTt3Q0FDQ0MsU0FBU3JDO3dDQUNUa0MsV0FBVTt3Q0FDVjRDLFVBQVUsQ0FBQzdTLG9CQUFvQlAsRUFBQUEsbUJBQUFBLFdBQVd3TyxJQUFJLENBQUN2SCxDQUFBQSxJQUFLQSxFQUFFQyxFQUFFLEtBQUszRywrQkFBOUJQLHVDQUFBQSxpQkFBaURWLE1BQU0sTUFBSyxLQUFLc0I7d0NBQ2hHMkMsT0FDRSxDQUFDaEQsbUJBQ0csZUFDQVAsRUFBQUEsb0JBQUFBLFdBQVd3TyxJQUFJLENBQUN2SCxDQUFBQSxJQUFLQSxFQUFFQyxFQUFFLEtBQUszRywrQkFBOUJQLHdDQUFBQSxrQkFBaURWLE1BQU0sTUFBSyxJQUMxRCxXQUNBc0IsZUFDRSxjQUNBO2tEQUdUQSxlQUFlLFlBQVk7Ozs7OztrREFFOUIsOERBQUM4UDt3Q0FDQ0MsU0FBU2hDO3dDQUNUNkIsV0FBVTt3Q0FDVjRDLFVBQVUzUyxlQUFleUssSUFBSSxHQUFHLEtBQUtwSyxjQUFjZCxXQUFXcUgsTUFBTSxLQUFLO3dDQUN6RTlELE9BQ0V2RCxXQUFXcUgsTUFBTSxLQUFLLElBQ2xCLGFBQ0E1RyxlQUFleUssSUFBSSxHQUFHLElBQ3BCLHFCQUNBcEssYUFDR2QsV0FBV3NULElBQUksQ0FBQ3JNLENBQUFBLElBQUtBLEVBQUVDLEVBQUUsR0FBRyxXQUFXLGNBQWMsY0FDckRsSCxXQUFXc1QsSUFBSSxDQUFDck0sQ0FBQUEsSUFBS0EsRUFBRUMsRUFBRSxHQUFHLFdBQVcsU0FBUztrREFHMUR6RyxlQUFleUssSUFBSSxHQUFHLElBQ25CLFdBQ0FwSyxhQUNHZCxXQUFXc1QsSUFBSSxDQUFDck0sQ0FBQUEsSUFBS0EsRUFBRUMsRUFBRSxHQUFHLFdBQVcsWUFBWSxZQUNuRGxILFdBQVdzVCxJQUFJLENBQUNyTSxDQUFBQSxJQUFLQSxFQUFFQyxFQUFFLEdBQUcsV0FBVyxTQUFTOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7WUFROUQ5RixzQ0FDQyw4REFBQ21QO2dCQUFJQyxXQUFVO2dCQUFnQkcsU0FBU2xHOzBCQUN0Qyw0RUFBQzhGO29CQUFJQyxXQUFVO29CQUF1QkcsU0FBUyxDQUFDekUsSUFBTUEsRUFBRTZFLGVBQWU7O3NDQUNyRSw4REFBQ1I7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDSzs4Q0FBRzs7Ozs7OzhDQUNKLDhEQUFDSDtvQ0FDQ0MsU0FBU2xHO29DQUNUK0YsV0FBVTtvQ0FDVjRDLFVBQVU1Ujs4Q0FFViw0RUFBQy9ELDhHQUFDQTt3Q0FBQytTLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQUdqQiw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDSTs4Q0FDRXBQLGFBQ0csa0JBQ0E7Ozs7OztnQ0FHTEEsNEJBQ0MsOERBQUMrTztvQ0FBSUMsV0FBVTs4Q0FDYiw0RUFBQ0Q7d0NBQUlDLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQUlyQiw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDRTtvQ0FDQ0MsU0FBU2xHO29DQUNUK0YsV0FBVTtvQ0FDVjRDLFVBQVU1Ujs4Q0FDWDs7Ozs7OzhDQUdELDhEQUFDa1A7b0NBQ0NDLFNBQVN0RztvQ0FDVG1HLFdBQVU7b0NBQ1Y0QyxVQUFVNVI7OENBRVRBLGFBQWEsWUFBWTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBUXBDLDhEQUFDcEQsNERBQW1CQTtnQkFDbEJtVixRQUFRN1I7Z0JBQ1JoQyxTQUFTLElBQU1pQyx3QkFBd0I7Z0JBQ3ZDNlIsa0JBQWtCeEQ7Ozs7Ozs7Ozs7OztBQUkxQjtHQTVrRU14UTtLQUFBQTtBQThrRU4sK0RBQWVBLG1CQUFtQkEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9hcHAvd29ya2JlbmNoL2NvbXBvbmVudHMvQ291cnNlTGlzdEVkaXRNb2RhbC50c3g/MmQ4ZiJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgWCwgU2V0dGluZ3MsIFBsdXMsIEJvb2tPcGVuLCBGaWxlVGV4dCB9IGZyb20gJ2x1Y2lkZS1yZWFjdCc7XG5pbXBvcnQgeyBjb3Vyc2VNYW5hZ2VtZW50QXBpIH0gZnJvbSAnQC9saWIvYXBpL2NvdXJzZS1tYW5hZ2VtZW50JztcbmltcG9ydCB7IGNvdXJzZUFwaSB9IGZyb20gJ0AvbGliL2FwaS9jb3Vyc2UnO1xuaW1wb3J0IHsgdXBsb2FkQXBpIH0gZnJvbSAnQC9saWIvYXBpL3VwbG9hZCc7XG5pbXBvcnQgeyB3b3Jrc0FwaSB9IGZyb20gJ0AvbGliL2FwaS93b3Jrcyc7XG5pbXBvcnQgeyBTZWxlY3QgfSBmcm9tICdhbnRkJztcbmltcG9ydCB7IEdldE5vdGlmaWNhdGlvbiB9IGZyb20gJ2xvZ2ljLWNvbW1vbi9kaXN0L2NvbXBvbmVudHMvTm90aWZpY2F0aW9uJztcbmltcG9ydCBUZW1wbGF0ZVBpY2tlck1vZGFsIGZyb20gJy4vVGVtcGxhdGVQaWNrZXJNb2RhbCc7XG5pbXBvcnQgJy4vQ291cnNlTGlzdEVkaXRNb2RhbC5jc3MnO1xuaW1wb3J0ICcuL05ld1B1Ymxpc2hUYXNrTW9kYWwuY3NzJztcblxuaW50ZXJmYWNlIENvdXJzZUl0ZW0ge1xuICBpZDogbnVtYmVyO1xuICBzZXJpZXNJZDogbnVtYmVyO1xuICB0aXRsZTogc3RyaW5nO1xuICBkZXNjcmlwdGlvbjogc3RyaW5nO1xuICBjb3ZlckltYWdlOiBzdHJpbmc7XG4gIG9yZGVySW5kZXg6IG51bWJlcjtcbiAgc3RhdHVzOiBudW1iZXI7XG4gIHN0YXR1c0xhYmVsOiBzdHJpbmc7XG4gIGhhc1ZpZGVvOiBudW1iZXI7XG4gIGhhc0RvY3VtZW50OiBudW1iZXI7XG4gIGhhc0F1ZGlvOiBudW1iZXI7XG4gIHZpZGVvRHVyYXRpb246IG51bWJlcjtcbiAgdmlkZW9EdXJhdGlvbkxhYmVsOiBzdHJpbmc7XG4gIHZpZGVvTmFtZTogc3RyaW5nO1xuICBmaXJzdFRlYWNoaW5nVGl0bGU6IHN0cmluZztcbiAgcmVzb3VyY2VzQ291bnQ6IG51bWJlcjtcbiAgY3JlYXRlZEF0OiBzdHJpbmc7XG4gIHVwZGF0ZWRBdDogc3RyaW5nO1xuICAvLyDmlrDlop7lrZfmrrXnlKjkuo7lrZjlgqjlrozmlbTnmoTor77nqIvor6bmg4VcbiAgY29udGVudENvbmZpZz86IGFueTtcbiAgdGVhY2hpbmdJbmZvPzogYW55W107XG4gIGFkZGl0aW9uYWxSZXNvdXJjZXM/OiBBcnJheTx7XG4gICAgdGl0bGU6IHN0cmluZztcbiAgICB1cmw6IHN0cmluZztcbiAgICBkZXNjcmlwdGlvbjogc3RyaW5nO1xuICB9Pjtcbn1cblxuaW50ZXJmYWNlIENvdXJzZVRhZyB7XG4gIGlkOiBudW1iZXI7XG4gIG5hbWU6IHN0cmluZztcbiAgY29sb3I6IHN0cmluZztcbiAgY2F0ZWdvcnk6IG51bWJlcjtcbiAgZGVzY3JpcHRpb246IHN0cmluZztcbiAgc3RhdHVzOiBudW1iZXI7XG59XG5cbmludGVyZmFjZSBBcGlSZXNwb25zZSB7XG4gIGNvZGU6IG51bWJlcjtcbiAgbWVzc2FnZTogc3RyaW5nO1xuICBkYXRhOiB7XG4gICAgbGlzdDogQ291cnNlSXRlbVtdO1xuICAgIHBhZ2luYXRpb246IHtcbiAgICAgIHBhZ2U6IG51bWJlcjtcbiAgICAgIHBhZ2VTaXplOiBudW1iZXI7XG4gICAgICB0b3RhbDogbnVtYmVyO1xuICAgICAgdG90YWxQYWdlczogbnVtYmVyO1xuICAgICAgaGFzTmV4dDogYm9vbGVhbjtcbiAgICAgIGhhc1ByZXY6IGJvb2xlYW47XG4gICAgfTtcbiAgfTtcbn1cblxuaW50ZXJmYWNlIENvdXJzZUxpc3RFZGl0TW9kYWxQcm9wcyB7XG4gIGlzVmlzaWJsZTogYm9vbGVhbjtcbiAgb25DbG9zZTogKCkgPT4gdm9pZDtcbiAgb25TYXZlOiAoZGF0YTogYW55KSA9PiB2b2lkO1xuICBzZXJpZXNUaXRsZTogc3RyaW5nO1xuICBzZXJpZXNDb3ZlckltYWdlPzogc3RyaW5nO1xuICBzZXJpZXNJZDogbnVtYmVyO1xufVxuXG4vLyDojrflj5bns7vliJfor77nqIvor6bmg4VcbmNvbnN0IGZldGNoU2VyaWVzRGV0YWlsID0gYXN5bmMgKHNlcmllc0lkOiBudW1iZXIpOiBQcm9taXNlPGFueT4gPT4ge1xuICBjb25zb2xlLmxvZygn8J+UjSDojrflj5bns7vliJfor77nqIvor6bmg4XvvIxzZXJpZXNJZDonLCBzZXJpZXNJZCk7XG4gIGNvbnNvbGUubG9nKCfwn5SXIOiwg+eUqEFQSTogR0VUIC9hcGkvdjEvY291cnNlLW1hbmFnZW1lbnQvc2VyaWVzL3tzZXJpZXNJZH0nKTtcblxuICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGNvdXJzZUFwaS5nZXRTZXJpZXNEZXRhaWwoc2VyaWVzSWQpO1xuICBjb25zb2xlLmxvZygn8J+ToSDns7vliJfor6bmg4VBUEnlk43lupQ6JywgcmVzcG9uc2UpO1xuICByZXR1cm4gcmVzcG9uc2UuZGF0YTtcbn07XG5cbi8vIEFQSeiwg+eUqOWHveaVsCAtIOiOt+WPluezu+WIl+S4i+eahOaJgOacieivvueoi++8iOWMheWQq+acquWPkeW4g+WSjOW3suWPkeW4g++8iVxuY29uc3QgZmV0Y2hDb3Vyc2VMaXN0ID0gYXN5bmMgKHNlcmllc0lkOiBudW1iZXIpOiBQcm9taXNlPEFwaVJlc3BvbnNlPiA9PiB7XG4gIGNvbnNvbGUubG9nKCfwn5SNIOiOt+WPluezu+WIl+ivvueoi+WIl+ihqO+8jHNlcmllc0lkOicsIHNlcmllc0lkKTtcbiAgY29uc29sZS5sb2coJ/CflJcg6LCD55SoQVBJOiBHRVQgL2FwaS92MS9jb3Vyc2UtbWFuYWdlbWVudC9zZXJpZXMve3Nlcmllc0lkfS9jb3Vyc2VzJyk7XG5cbiAgLy8g5L2/55So6K++56iL566h55CGQVBJ6I635Y+W5omA5pyJ54q25oCB55qE6K++56iLXG4gIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgY291cnNlQXBpLmdldE1hbmFnZW1lbnRTZXJpZXNDb3Vyc2VzKHNlcmllc0lkLCB7XG4gICAgcGFnZTogMSxcbiAgICBwYWdlU2l6ZTogMTAwLFxuICAgIC8vIOS4jeS8oHN0YXR1c+WPguaVsO+8jOiOt+WPluaJgOacieeKtuaAgeeahOivvueoi++8iHN0YXR1cz0w5pyq5Y+R5biD77yMc3RhdHVzPTHlt7Llj5HluIPvvIlcbiAgfSk7XG5cbiAgY29uc29sZS5sb2coJ/Cfk6EgQVBJ5ZON5bqUOicsIHJlc3BvbnNlKTtcbiAgcmV0dXJuIHJlc3BvbnNlLmRhdGE7XG59O1xuXG4vLyDojrflj5bor77nqIvor6bmg4VcbmNvbnN0IGZldGNoQ291cnNlRGV0YWlsID0gYXN5bmMgKHNlcmllc0lkOiBudW1iZXIsIGNvdXJzZUlkOiBudW1iZXIpID0+IHtcbiAgcmV0dXJuIGF3YWl0IGNvdXJzZUFwaS5nZXRDb3Vyc2VNYXJrZXRwbGFjZURldGFpbChzZXJpZXNJZCwgY291cnNlSWQpO1xufTtcblxuLy8g6I635Y+W6K++56iL5qCH562+XG5jb25zdCBmZXRjaENvdXJzZVRhZ3MgPSBhc3luYyAoKTogUHJvbWlzZTxhbnk+ID0+IHtcbiAgdHJ5IHtcbiAgICBjb25zb2xlLmxvZygn8J+UjSDlvIDlp4vosIPnlKggY291cnNlQXBpLmdldENvdXJzZVRhZ3MnKTtcbiAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBjb3Vyc2VBcGkuZ2V0Q291cnNlVGFncyh7XG4gICAgICBwYWdlOiAxLFxuICAgICAgcGFnZVNpemU6IDEwMCxcbiAgICAgIHN0YXR1czogMSAvLyDlj6rojrflj5blkK/nlKjnmoTmoIfnrb5cbiAgICB9KTtcbiAgICBjb25zb2xlLmxvZygn8J+UjSBjb3Vyc2VBcGkuZ2V0Q291cnNlVGFncyDov5Tlm57nu5Pmnpw6JywgcmVzdWx0KTtcbiAgICByZXR1cm4gcmVzdWx0O1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ/CflI0gY291cnNlQXBpLmdldENvdXJzZVRhZ3Mg6LCD55So5aSx6LSlOicsIGVycm9yKTtcbiAgICB0aHJvdyBlcnJvcjtcbiAgfVxufTtcblxuY29uc3QgQ291cnNlTGlzdEVkaXRNb2RhbDogUmVhY3QuRkM8Q291cnNlTGlzdEVkaXRNb2RhbFByb3BzPiA9ICh7XG4gIGlzVmlzaWJsZSxcbiAgb25DbG9zZSxcbiAgb25TYXZlLFxuICBzZXJpZXNUaXRsZSxcbiAgc2VyaWVzQ292ZXJJbWFnZSxcbiAgc2VyaWVzSWQgPSAxMjMgLy8g6buY6K6k5YC877yM5a6e6ZmF5L2/55So5pe25bqU6K+l5Lyg5YWl55yf5a6e55qEc2VyaWVzSWRcbn0pID0+IHtcbiAgY29uc3Qgbm90aWZpY2F0aW9uID0gR2V0Tm90aWZpY2F0aW9uKCk7XG5cbiAgY29uc3QgW2NvdXJzZUxpc3QsIHNldENvdXJzZUxpc3RdID0gdXNlU3RhdGU8Q291cnNlSXRlbVtdPihbXSk7XG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW3JpZ2h0UGFuZWxUeXBlLCBzZXRSaWdodFBhbmVsVHlwZV0gPSB1c2VTdGF0ZTwnbm9uZScgfCAnc2V0dGluZ3MnIHwgJ2NvdXJzZSc+KCdub25lJyk7XG4gIGNvbnN0IFtzZWxlY3RlZENvdXJzZUlkLCBzZXRTZWxlY3RlZENvdXJzZUlkXSA9IHVzZVN0YXRlPG51bWJlciB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbdXBsb2FkaW5nRmlsZXMsIHNldFVwbG9hZGluZ0ZpbGVzXSA9IHVzZVN0YXRlPFNldDxzdHJpbmc+PihuZXcgU2V0KCkpO1xuICBjb25zdCBbaXNQdWJsaXNoaW5nLCBzZXRJc1B1Ymxpc2hpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbaXNDcmVhdGluZywgc2V0SXNDcmVhdGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtpc1B1Ymxpc2hpbmdTZXJpZXMsIHNldElzUHVibGlzaGluZ1Nlcmllc10gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtzZXJpZXNTdGF0dXMsIHNldFNlcmllc1N0YXR1c10gPSB1c2VTdGF0ZTxudW1iZXI+KDApOyAvLyAwPeiNieeov++8jDE95bey5Y+R5biD77yMMj3lt7LlvZLmoaNcblxuICAvLyDliKDpmaTnoa7orqTlvLnnqpfnirbmgIFcbiAgY29uc3QgW2RlbGV0ZUNvbmZpcm1WaXNpYmxlLCBzZXREZWxldGVDb25maXJtVmlzaWJsZV0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtjb3Vyc2VUb0RlbGV0ZSwgc2V0Q291cnNlVG9EZWxldGVdID0gdXNlU3RhdGU8bnVtYmVyIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtpc0RlbGV0aW5nLCBzZXRJc0RlbGV0aW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcblxuICAvLyDmqKHmnb/pgInmi6nlvLnnqpfnirbmgIFcbiAgY29uc3QgW2lzVGVtcGxhdGVQaWNrZXJPcGVuLCBzZXRJc1RlbXBsYXRlUGlja2VyT3Blbl0gPSB1c2VTdGF0ZShmYWxzZSk7XG5cbiAgY29uc3QgW2VkaXRpbmdUaXRsZSwgc2V0RWRpdGluZ1RpdGxlXSA9IHVzZVN0YXRlKHNlcmllc1RpdGxlKTtcbiAgY29uc3QgW2NvdXJzZUdvYWxzLCBzZXRDb3Vyc2VHb2Fsc10gPSB1c2VTdGF0ZSgnJyk7XG4gIGNvbnN0IFtjb3Vyc2VPYmplY3RpdmVzLCBzZXRDb3Vyc2VPYmplY3RpdmVzXSA9IHVzZVN0YXRlKCcnKTtcbiAgY29uc3QgW3Byb2plY3RNZW1iZXJzLCBzZXRQcm9qZWN0TWVtYmVyc10gPSB1c2VTdGF0ZSgnJyk7XG5cbiAgLy8g5L2c5ZOB55u45YWz54q25oCBXG4gIGNvbnN0IFt3b3Jrcywgc2V0V29ya3NdID0gdXNlU3RhdGU8YW55W10+KFtdKTtcbiAgY29uc3QgW2xvYWRpbmdXb3Jrcywgc2V0TG9hZGluZ1dvcmtzXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW3NlbGVjdGVkV29ya0lkcywgc2V0U2VsZWN0ZWRXb3JrSWRzXSA9IHVzZVN0YXRlPG51bWJlcltdPihbXSk7XG4gIGNvbnN0IFt3b3Jrc1BhZ2UsIHNldFdvcmtzUGFnZV0gPSB1c2VTdGF0ZSgxKTtcbiAgY29uc3QgW2hhc01vcmVXb3Jrcywgc2V0SGFzTW9yZVdvcmtzXSA9IHVzZVN0YXRlKHRydWUpO1xuICBjb25zdCBbbG9hZGluZ01vcmVXb3Jrcywgc2V0TG9hZGluZ01vcmVXb3Jrc10gPSB1c2VTdGF0ZShmYWxzZSk7XG5cbiAgLy8g6K++56iL5qCH562+55u45YWz54q25oCBXG4gIGNvbnN0IFtjb3Vyc2VUYWdzLCBzZXRDb3Vyc2VUYWdzXSA9IHVzZVN0YXRlPENvdXJzZVRhZ1tdPihbXSk7XG4gIGNvbnN0IFtzZWxlY3RlZFRhZ3MsIHNldFNlbGVjdGVkVGFnc10gPSB1c2VTdGF0ZTxudW1iZXJbXT4oW10pO1xuICBjb25zdCBbdGFnc0xvYWRpbmcsIHNldFRhZ3NMb2FkaW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcblxuICAvLyDor77nqIvor6bnu4bnvJbovpHnirbmgIFcbiAgY29uc3QgW2NvdXJzZURldGFpbCwgc2V0Q291cnNlRGV0YWlsXSA9IHVzZVN0YXRlKHtcbiAgICB0aXRsZTogJycsXG4gICAgZGVzY3JpcHRpb246ICcnLFxuICAgIGNvdmVySW1hZ2U6ICcnLFxuICAgIHZpZGVvVXJsOiAnJyxcbiAgICB2aWRlb05hbWU6ICcnLFxuICAgIGlzVmlkZW9FbmFibGVkOiBmYWxzZSxcbiAgICBhdHRhY2htZW50VXJsOiAnJyxcbiAgICBhdHRhY2htZW50TmFtZTogJycsXG4gICAgaXNBdHRhY2htZW50RW5hYmxlZDogZmFsc2UsXG4gICAgdGVhY2hpbmdNYXRlcmlhbHM6IFtdIGFzIHsgdHlwZTogc3RyaW5nOyBuYW1lOiBzdHJpbmc7IHVybDogc3RyaW5nIH1bXSxcbiAgICAvLyDmlK/mjIF0ZWFjaGluZ0luZm/nu5PmnoRcbiAgICB0ZWFjaGluZ0luZm86IFtdIGFzIHsgdGl0bGU6IHN0cmluZzsgY29udGVudDogc3RyaW5nIH1bXSxcbiAgICAvLyDmlK/mjIFjb250ZW50Q29uZmln57uT5p6EXG4gICAgY29udGVudENvbmZpZzoge1xuICAgICAgaGFzVmlkZW86IDAsXG4gICAgICBoYXNEb2N1bWVudDogMCxcbiAgICAgIGhhc0F1ZGlvOiAwLFxuICAgICAgdmlkZW86IHsgdXJsOiAnJywgbmFtZTogJycgfSxcbiAgICAgIGRvY3VtZW50OiB7IHVybDogJycsIG5hbWU6ICcnIH0sXG4gICAgICBhdWRpbzogeyB1cmw6ICcnLCBuYW1lOiAnJyB9XG4gICAgfSxcbiAgICBjb3Vyc2VDb250ZW50OiB7XG4gICAgICB0b3BpYzogJycsXG4gICAgICBjb250ZW50OiAnJ1xuICAgIH0sXG4gICAgaXNPbmVLZXlPcGVuOiBmYWxzZSxcbiAgICBpc0Rpc3RyaWJ1dGlvbkVuYWJsZWQ6IGZhbHNlLFxuICAgIGRpc3RyaWJ1dGlvblJld2FyZDogJycsXG4gICAgc2VsZWN0ZWRUZW1wbGF0ZTogJycsXG4gICAgaXNEaXN0cmlidXRpb25XYXRlcjogZmFsc2UsXG4gICAgcmVxdWlyZWRFbmVyZ3k6ICcnLFxuICAgIGVuZXJneUFtb3VudDogJycsXG4gICAgaXNEaXN0cmlidXRpb25MaW1pdDogZmFsc2UsXG4gICAgZGlzdHJpYnV0aW9uQ29uZGl0aW9uczoge1xuICAgICAgaW52aXRlQ291bnQ6ICcnLFxuICAgICAgdGFza0NvdW50OiAnJyxcbiAgICAgIGV4cGVyaWVuY2U6ICcnXG4gICAgfSxcbiAgICBpc0Rpc3RyaWJ1dGlvblRpbWU6IGZhbHNlLFxuICAgIGRpc3RyaWJ1dGlvblRpbWVDb25kaXRpb25zOiB7XG4gICAgICBzdGFydFRpbWU6ICcnLFxuICAgICAgZW5kVGltZTogJydcbiAgICB9LFxuICAgIGRpc3RyaWJ1dGlvbk1hdGVyaWFsczogW10gYXMgeyB0eXBlOiBzdHJpbmc7IG5hbWU6IHN0cmluZyB9W10sXG4gICAgLy8g5Lu75Yqh6YWN572u55u45YWz54q25oCBXG4gICAgdGFza0NvbmZpZzoge1xuICAgICAgdGFza05hbWU6ICcnLFxuICAgICAgdGFza0R1cmF0aW9uOiAnJyxcbiAgICAgIHRhc2tEZXNjcmlwdGlvbjogJycsXG4gICAgICBzZWxmQXNzZXNzbWVudEl0ZW1zOiBbJyddLFxuICAgICAgcmVmZXJlbmNlV29ya3M6IFtdIGFzIHsgdHlwZTogc3RyaW5nOyBuYW1lOiBzdHJpbmcgfVtdLFxuICAgICAgcmVmZXJlbmNlUmVzb3VyY2VzOiBbXSBhcyB7IHR5cGU6IHN0cmluZzsgbmFtZTogc3RyaW5nOyB1cmw/OiBzdHJpbmcgfVtdXG4gICAgfVxuICB9KTtcblxuICAvLyDojrflj5bor77nqIvliJfooajmlbDmja5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoaXNWaXNpYmxlICYmIHNlcmllc0lkKSB7XG4gICAgICAvLyDmo4Dmn6XnlKjmiLfnmbvlvZXnirbmgIFcbiAgICAgIGNvbnN0IHRva2VuID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3Rva2VuJyk7XG4gICAgICBjb25zb2xlLmxvZygn8J+UkCDmo4Dmn6XnmbvlvZXnirbmgIHvvIx0b2tlbuWtmOWcqDonLCAhIXRva2VuKTtcbiAgICAgIGNvbnNvbGUubG9nKCfwn5SNIHNlcmllc0lkOicsIHNlcmllc0lkKTtcblxuICAgICAgaWYgKCF0b2tlbikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCfinYwg55So5oi35pyq55m75b2V77yM5peg5rOV6I635Y+W6K++56iL5YiX6KGoJyk7XG4gICAgICAgIC8vIOiuvue9ruepuuWIl+ihqO+8jOaYvuekuuepuueKtuaAgVxuICAgICAgICBzZXRDb3Vyc2VMaXN0KFtdKTtcbiAgICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cblxuICAgICAgbG9hZENvdXJzZUxpc3QoKTtcbiAgICAgIGxvYWRDb3Vyc2VUYWdzKCk7XG4gICAgICBsb2FkU2VyaWVzRGV0YWlsKCk7XG4gICAgICBsb2FkVXNlcldvcmtzKHRydWUpOyAvLyDph43nva7liqDovb1cbiAgICB9XG4gIH0sIFtpc1Zpc2libGUsIHNlcmllc0lkXSk7XG5cbiAgY29uc3QgbG9hZENvdXJzZUxpc3QgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIHNldExvYWRpbmcodHJ1ZSk7XG4gICAgICBjb25zb2xlLmxvZygn8J+UjSDlvIDlp4vliqDovb3or77nqIvliJfooajvvIxzZXJpZXNJZDonLCBzZXJpZXNJZCk7XG5cbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2hDb3Vyc2VMaXN0KHNlcmllc0lkKTtcbiAgICAgIGNvbnNvbGUubG9nKCfwn5OhIEFQSeWTjeW6lDonLCByZXNwb25zZSk7XG5cbiAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDApIHtcbiAgICAgICAgY29uc29sZS5sb2coJ+KchSDor77nqIvliJfooajmlbDmja46JywgcmVzcG9uc2UuZGF0YSk7XG4gICAgICAgIGNvbnN0IGNvdXJzZXMgPSByZXNwb25zZS5kYXRhLmxpc3QgfHwgW107XG4gICAgICAgIGNvbnNvbGUubG9nKCfinIUg6Kej5p6Q55qE6K++56iL5pWw57uEOicsIGNvdXJzZXMpO1xuICAgICAgICBjb25zb2xlLmxvZygn8J+TiiDor77nqItJROivpuaDhTonLCBjb3Vyc2VzLm1hcCgoYzogYW55KSA9PiAoe1xuICAgICAgICAgIGlkOiBjLmlkLFxuICAgICAgICAgIHR5cGU6IHR5cGVvZiBjLmlkLFxuICAgICAgICAgIHRpdGxlOiBjLnRpdGxlLFxuICAgICAgICAgIHN0YXR1czogYy5zdGF0dXNcbiAgICAgICAgfSkpKTtcbiAgICAgICAgY29uc29sZS5sb2coJ/Cfk4og6K++56iL54q25oCB57uf6K6hOicsIHtcbiAgICAgICAgICB0b3RhbDogY291cnNlcy5sZW5ndGgsXG4gICAgICAgICAgZHJhZnQ6IGNvdXJzZXMuZmlsdGVyKChjOiBhbnkpID0+IGMuc3RhdHVzID09PSAwKS5sZW5ndGgsXG4gICAgICAgICAgcHVibGlzaGVkOiBjb3Vyc2VzLmZpbHRlcigoYzogYW55KSA9PiBjLnN0YXR1cyA9PT0gMSkubGVuZ3RoXG4gICAgICAgIH0pO1xuICAgICAgICBzZXRDb3Vyc2VMaXN0KGNvdXJzZXMpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgY29uc29sZS5lcnJvcign4p2MIEFQSei/lOWbnumUmeivrzonLCByZXNwb25zZSk7XG4gICAgICAgIHNldENvdXJzZUxpc3QoW10pO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDliqDovb3or77nqIvliJfooajlpLHotKU6JywgZXJyb3IpO1xuXG4gICAgICAvLyDmo4Dmn6XmmK/lkKbmmK/orqTor4HplJnor69cbiAgICAgIGlmIChlcnJvci5yZXNwb25zZT8uc3RhdHVzID09PSA0MDEpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcign8J+UkCDorqTor4HlpLHotKXvvIznlKjmiLfmnKrnmbvlvZXmiJZ0b2tlbuW3sui/h+acnycpO1xuICAgICAgfSBlbHNlIGlmIChlcnJvci5yZXNwb25zZT8uc3RhdHVzID09PSA0MDMpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcign8J+aqyDmnYPpmZDkuI3otrPvvIzml6Dms5Xorr/pl67or6Xns7vliJfor77nqIsnKTtcbiAgICAgIH0gZWxzZSBpZiAoZXJyb3IucmVzcG9uc2U/LnN0YXR1cyA9PT0gNDA0KSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ/Cfk60g57O75YiX6K++56iL5LiN5a2Y5Zyo77yMc2VyaWVzSWQ6Jywgc2VyaWVzSWQpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgY29uc29sZS5lcnJvcign8J+UpyDlhbbku5bplJnor686JywgZXJyb3IubWVzc2FnZSk7XG4gICAgICB9XG5cbiAgICAgIHNldENvdXJzZUxpc3QoW10pO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgLy8g5Yqg6L2957O75YiX6K++56iL6K+m5oOFXG4gIGNvbnN0IGxvYWRTZXJpZXNEZXRhaWwgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnNvbGUubG9nKCfwn5SNIOW8gOWni+WKoOi9veezu+WIl+ivvueoi+ivpuaDhe+8jHNlcmllc0lkOicsIHNlcmllc0lkKTtcblxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaFNlcmllc0RldGFpbChzZXJpZXNJZCk7XG4gICAgICBjb25zb2xlLmxvZygn8J+ToSDns7vliJfor6bmg4Xlk43lupQ6JywgcmVzcG9uc2UpO1xuXG4gICAgICBpZiAocmVzcG9uc2UuY29kZSA9PT0gMjAwICYmIHJlc3BvbnNlLmRhdGEpIHtcbiAgICAgICAgY29uc3Qgc2VyaWVzRGF0YSA9IHJlc3BvbnNlLmRhdGE7XG4gICAgICAgIGNvbnNvbGUubG9nKCfinIUg57O75YiX6K++56iL6K+m5oOFOicsIHNlcmllc0RhdGEpO1xuICAgICAgICBzZXRTZXJpZXNTdGF0dXMoc2VyaWVzRGF0YS5zdGF0dXMgfHwgMCk7XG4gICAgICAgIGNvbnNvbGUubG9nKCfwn5OKIOezu+WIl+ivvueoi+eKtuaAgTonLCBzZXJpZXNEYXRhLnN0YXR1cywgJygwPeiNieeov++8jDE95bey5Y+R5biD77yMMj3lt7LlvZLmoaMpJyk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBjb25zb2xlLmVycm9yKCfinYwg6I635Y+W57O75YiX6K+m5oOF5aSx6LSlOicsIHJlc3BvbnNlLm1lc3NhZ2UpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDliqDovb3ns7vliJfor6bmg4XlvILluLg6JywgZXJyb3IpO1xuICAgIH1cbiAgfTtcblxuICAvLyDliqDovb3nlKjmiLfkvZzlk4HmlbDmja7vvIjliJ3lp4vliqDovb3vvIlcbiAgY29uc3QgbG9hZFVzZXJXb3JrcyA9IGFzeW5jIChyZXNldCA9IGZhbHNlKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGlmIChyZXNldCkge1xuICAgICAgICBzZXRMb2FkaW5nV29ya3ModHJ1ZSk7XG4gICAgICAgIHNldFdvcmtzKFtdKTtcbiAgICAgICAgc2V0V29ya3NQYWdlKDEpO1xuICAgICAgICBzZXRIYXNNb3JlV29ya3ModHJ1ZSk7XG4gICAgICB9XG5cbiAgICAgIC8vIOS7jmxvY2FsU3RvcmFnZeiOt+WPlueUqOaIt0lEXG4gICAgICBjb25zdCB1c2VySWQgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgndXNlcklkJykgfHwgJzI4OTYnOyAvLyDpu5jorqTkvb/nlKgyODk2XG5cbiAgICAgIGNvbnN0IGN1cnJlbnRQYWdlID0gcmVzZXQgPyAxIDogd29ya3NQYWdlO1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB3b3Jrc0FwaS5nZXRUZWFjaGVyV29ya3MoTnVtYmVyKHVzZXJJZCksIGN1cnJlbnRQYWdlLCA1KTsgLy8g5q+P5qyh5Yqg6L29NeS4qlxuXG4gICAgICAvLyDmo4Dmn6XlpJrnp43lj6/og73nmoTmlbDmja7nu5PmnoRcbiAgICAgIGxldCB3b3Jrc0xpc3Q6IGFueVtdID0gW107XG5cbiAgICAgIGlmIChyZXNwb25zZS5kYXRhPy5jb2RlID09PSAyMDApIHtcbiAgICAgICAgLy8g5oOF5Ya1MTogcmVzcG9uc2UuZGF0YS5kYXRhLmxpc3RcbiAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGE/LmRhdGE/Lmxpc3QpIHtcbiAgICAgICAgICB3b3Jrc0xpc3QgPSByZXNwb25zZS5kYXRhLmRhdGEubGlzdDtcbiAgICAgICAgfVxuICAgICAgICAvLyDmg4XlhrUyOiByZXNwb25zZS5kYXRhLmRhdGEgKOebtOaOpeaYr+aVsOe7hClcbiAgICAgICAgZWxzZSBpZiAoQXJyYXkuaXNBcnJheShyZXNwb25zZS5kYXRhPy5kYXRhKSkge1xuICAgICAgICAgIHdvcmtzTGlzdCA9IHJlc3BvbnNlLmRhdGEuZGF0YTtcbiAgICAgICAgfVxuICAgICAgICAvLyDmg4XlhrUzOiByZXNwb25zZS5kYXRhICjnm7TmjqXmmK/mlbDnu4QpXG4gICAgICAgIGVsc2UgaWYgKEFycmF5LmlzQXJyYXkocmVzcG9uc2UuZGF0YSkpIHtcbiAgICAgICAgICB3b3Jrc0xpc3QgPSByZXNwb25zZS5kYXRhO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgICAvLyDmg4XlhrU0OiDnm7TmjqXmo4Dmn6VyZXNwb25zZeaYr+WQpuaYr+aVsOe7hFxuICAgICAgZWxzZSBpZiAoQXJyYXkuaXNBcnJheShyZXNwb25zZSkpIHtcbiAgICAgICAgd29ya3NMaXN0ID0gcmVzcG9uc2U7XG4gICAgICB9XG5cbiAgICAgIGlmIChyZXNldCkge1xuICAgICAgICBzZXRXb3Jrcyh3b3Jrc0xpc3QpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgc2V0V29ya3MocHJldiA9PiBbLi4ucHJldiwgLi4ud29ya3NMaXN0XSk7XG4gICAgICB9XG5cbiAgICAgIC8vIOajgOafpeaYr+WQpui/mOacieabtOWkmuaVsOaNrlxuICAgICAgc2V0SGFzTW9yZVdvcmtzKHdvcmtzTGlzdC5sZW5ndGggPT09IDUpO1xuXG4gICAgICBpZiAoIXJlc2V0KSB7XG4gICAgICAgIHNldFdvcmtzUGFnZShwcmV2ID0+IHByZXYgKyAxKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcign5Yqg6L295L2c5ZOB5pWw5o2u5aSx6LSlOicsIGVycm9yKTtcbiAgICAgIGlmIChyZXNldCkge1xuICAgICAgICBzZXRXb3JrcyhbXSk7XG4gICAgICB9XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldExvYWRpbmdXb3JrcyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIC8vIOWKoOi9veabtOWkmuS9nOWTgVxuICBjb25zdCBsb2FkTW9yZVdvcmtzID0gYXN5bmMgKCkgPT4ge1xuICAgIGlmIChsb2FkaW5nTW9yZVdvcmtzIHx8ICFoYXNNb3JlV29ya3MpIHJldHVybjtcblxuICAgIHRyeSB7XG4gICAgICBzZXRMb2FkaW5nTW9yZVdvcmtzKHRydWUpO1xuXG4gICAgICAvLyDku45sb2NhbFN0b3JhZ2Xojrflj5bnlKjmiLdJRFxuICAgICAgY29uc3QgdXNlcklkID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3VzZXJJZCcpIHx8ICcyODk2JzsgLy8g6buY6K6k5L2/55SoMjg5NlxuXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHdvcmtzQXBpLmdldFRlYWNoZXJXb3JrcyhOdW1iZXIodXNlcklkKSwgd29ya3NQYWdlLCA1KTtcblxuICAgICAgLy8g5qOA5p+l5aSa56eN5Y+v6IO955qE5pWw5o2u57uT5p6EXG4gICAgICBsZXQgd29ya3NMaXN0OiBhbnlbXSA9IFtdO1xuXG4gICAgICBpZiAocmVzcG9uc2UuZGF0YT8uY29kZSA9PT0gMjAwKSB7XG4gICAgICAgIC8vIOaDheWGtTE6IHJlc3BvbnNlLmRhdGEuZGF0YS5saXN0XG4gICAgICAgIGlmIChyZXNwb25zZS5kYXRhPy5kYXRhPy5saXN0KSB7XG4gICAgICAgICAgd29ya3NMaXN0ID0gcmVzcG9uc2UuZGF0YS5kYXRhLmxpc3Q7XG4gICAgICAgIH1cbiAgICAgICAgLy8g5oOF5Ya1MjogcmVzcG9uc2UuZGF0YS5kYXRhICjnm7TmjqXmmK/mlbDnu4QpXG4gICAgICAgIGVsc2UgaWYgKEFycmF5LmlzQXJyYXkocmVzcG9uc2UuZGF0YT8uZGF0YSkpIHtcbiAgICAgICAgICB3b3Jrc0xpc3QgPSByZXNwb25zZS5kYXRhLmRhdGE7XG4gICAgICAgIH1cbiAgICAgICAgLy8g5oOF5Ya1MzogcmVzcG9uc2UuZGF0YSAo55u05o6l5piv5pWw57uEKVxuICAgICAgICBlbHNlIGlmIChBcnJheS5pc0FycmF5KHJlc3BvbnNlLmRhdGEpKSB7XG4gICAgICAgICAgd29ya3NMaXN0ID0gcmVzcG9uc2UuZGF0YTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgICAgLy8g5oOF5Ya1NDog55u05o6l5qOA5p+lcmVzcG9uc2XmmK/lkKbmmK/mlbDnu4RcbiAgICAgIGVsc2UgaWYgKEFycmF5LmlzQXJyYXkocmVzcG9uc2UpKSB7XG4gICAgICAgIHdvcmtzTGlzdCA9IHJlc3BvbnNlO1xuICAgICAgfVxuXG4gICAgICBzZXRXb3JrcyhwcmV2ID0+IFsuLi5wcmV2LCAuLi53b3Jrc0xpc3RdKTtcblxuICAgICAgLy8g5qOA5p+l5piv5ZCm6L+Y5pyJ5pu05aSa5pWw5o2uXG4gICAgICBzZXRIYXNNb3JlV29ya3Mod29ya3NMaXN0Lmxlbmd0aCA9PT0gNSk7XG4gICAgICBzZXRXb3Jrc1BhZ2UocHJldiA9PiBwcmV2ICsgMSk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+WKoOi9veabtOWkmuS9nOWTgeWksei0pTonLCBlcnJvcik7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldExvYWRpbmdNb3JlV29ya3MoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICAvLyDlpITnkIbkvZzlk4HpgInmi6lcbiAgY29uc3QgaGFuZGxlV29ya1NlbGVjdCA9ICh3b3JrSWQ6IG51bWJlcikgPT4ge1xuICAgIGlmIChzZWxlY3RlZFdvcmtJZHMuaW5jbHVkZXMod29ya0lkKSkge1xuICAgICAgLy8g5Y+W5raI6YCJ5LitXG4gICAgICBzZXRTZWxlY3RlZFdvcmtJZHMocHJldiA9PiBwcmV2LmZpbHRlcihpZCA9PiBpZCAhPT0gd29ya0lkKSk7XG4gICAgfSBlbHNlIHtcbiAgICAgIC8vIOmAieS4rVxuICAgICAgc2V0U2VsZWN0ZWRXb3JrSWRzKHByZXYgPT4gWy4uLnByZXYsIHdvcmtJZF0pO1xuICAgIH1cbiAgfTtcblxuICAvLyDliqDovb3or77nqIvmoIfnrb5cbiAgY29uc3QgbG9hZENvdXJzZVRhZ3MgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIHNldFRhZ3NMb2FkaW5nKHRydWUpO1xuICAgICAgY29uc29sZS5sb2coJ/Cfj7fvuI8g5byA5aeL5Yqg6L296K++56iL5qCH562+Jyk7XG5cblxuXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoQ291cnNlVGFncygpO1xuICAgICAgY29uc29sZS5sb2coJ/Cfk6Eg5qCH562+QVBJ5a6M5pW05ZON5bqUOicsIHJlc3BvbnNlKTtcblxuICAgICAgLy8g5qOA5p+l5ZON5bqU57uT5p6EXG4gICAgICBpZiAocmVzcG9uc2UgJiYgcmVzcG9uc2UuZGF0YSkge1xuICAgICAgICBjb25zb2xlLmxvZygn8J+TiiDlk43lupTmlbDmja46JywgcmVzcG9uc2UuZGF0YSk7XG5cbiAgICAgICAgbGV0IHRhZ3M6IENvdXJzZVRhZ1tdID0gW107XG5cbiAgICAgICAgLy8g5aSE55CG5qCH5YeG55qEQVBJ5ZON5bqU5qC85byPIChyZXNwb25zZS5kYXRhLmxpc3QpIC0g5qC55o2u5a6e6ZmFQVBJ5ZON5bqUXG4gICAgICAgIGlmIChyZXNwb25zZS5kYXRhLmxpc3QgJiYgQXJyYXkuaXNBcnJheShyZXNwb25zZS5kYXRhLmxpc3QpKSB7XG4gICAgICAgICAgdGFncyA9IHJlc3BvbnNlLmRhdGEubGlzdDtcbiAgICAgICAgICBjb25zb2xlLmxvZygn4pyFIOS7jiBkYXRhLmxpc3Qg6Kej5p6Q5Yiw5qCH562+OicsIHRhZ3MubGVuZ3RoLCAn5LiqJyk7XG4gICAgICAgIH1cbiAgICAgICAgLy8g5aSE55CG55u05o6l5pWw57uE5qC85byPIChyZXNwb25zZS5kYXRhKVxuICAgICAgICBlbHNlIGlmIChBcnJheS5pc0FycmF5KHJlc3BvbnNlLmRhdGEpKSB7XG4gICAgICAgICAgdGFncyA9IHJlc3BvbnNlLmRhdGE7XG4gICAgICAgICAgY29uc29sZS5sb2coJ+KchSDku44gZGF0YSDmlbDnu4Top6PmnpDliLDmoIfnrb46JywgdGFncy5sZW5ndGgsICfkuKonKTtcbiAgICAgICAgfVxuICAgICAgICAvLyDlpITnkIbltYzlpZfnmoRBUEnlk43lupTmoLzlvI8gKHJlc3BvbnNlLmRhdGEuZGF0YS5saXN0KSAtIOWkh+eUqOagvOW8j1xuICAgICAgICBlbHNlIGlmIChyZXNwb25zZS5kYXRhLmRhdGEgJiYgcmVzcG9uc2UuZGF0YS5kYXRhLmxpc3QgJiYgQXJyYXkuaXNBcnJheShyZXNwb25zZS5kYXRhLmRhdGEubGlzdCkpIHtcbiAgICAgICAgICB0YWdzID0gcmVzcG9uc2UuZGF0YS5kYXRhLmxpc3Q7XG4gICAgICAgICAgY29uc29sZS5sb2coJ+KchSDku44gZGF0YS5kYXRhLmxpc3Qg6Kej5p6Q5Yiw5qCH562+OicsIHRhZ3MubGVuZ3RoLCAn5LiqJyk7XG4gICAgICAgIH1cblxuICAgICAgICAvLyDpqozor4HmoIfnrb7mlbDmja7moLzlvI9cbiAgICAgICAgY29uc29sZS5sb2coJ/CflI0g5Y6f5aeL5qCH562+5pWw5o2uOicsIHRhZ3MpO1xuICAgICAgICBjb25zb2xlLmxvZygn8J+UjSDmoIfnrb7mlbDmja7nsbvlnovmo4Dmn6U6Jyk7XG4gICAgICAgIHRhZ3MuZm9yRWFjaCgodGFnLCBpbmRleCkgPT4ge1xuICAgICAgICAgIGNvbnNvbGUubG9nKGDmoIfnrb4ke2luZGV4fTpgLCB7XG4gICAgICAgICAgICB0YWcsXG4gICAgICAgICAgICBoYXNUYWc6ICEhdGFnLFxuICAgICAgICAgICAgaWRUeXBlOiB0eXBlb2YgdGFnPy5pZCxcbiAgICAgICAgICAgIG5hbWVUeXBlOiB0eXBlb2YgdGFnPy5uYW1lLFxuICAgICAgICAgICAgbmFtZVZhbHVlOiB0YWc/Lm5hbWUsXG4gICAgICAgICAgICBuYW1lTm90RW1wdHk6IHRhZz8ubmFtZT8udHJpbSgpICE9PSAnJ1xuICAgICAgICAgIH0pO1xuICAgICAgICB9KTtcblxuICAgICAgICBjb25zdCB2YWxpZFRhZ3MgPSB0YWdzLmZpbHRlcih0YWcgPT4ge1xuICAgICAgICAgIGNvbnN0IGlzVmFsaWQgPSB0YWcgJiZcbiAgICAgICAgICAgIHR5cGVvZiB0YWcuaWQgPT09ICdudW1iZXInICYmXG4gICAgICAgICAgICB0eXBlb2YgdGFnLm5hbWUgPT09ICdzdHJpbmcnICYmXG4gICAgICAgICAgICB0YWcubmFtZS50cmltKCkgIT09ICcnO1xuXG4gICAgICAgICAgaWYgKCFpc1ZhbGlkKSB7XG4gICAgICAgICAgICBjb25zb2xlLmxvZygn4p2MIOaXoOaViOagh+etvjonLCB0YWcsIHtcbiAgICAgICAgICAgICAgaGFzVGFnOiAhIXRhZyxcbiAgICAgICAgICAgICAgaWRUeXBlOiB0eXBlb2YgdGFnPy5pZCxcbiAgICAgICAgICAgICAgbmFtZVR5cGU6IHR5cGVvZiB0YWc/Lm5hbWUsXG4gICAgICAgICAgICAgIG5hbWVWYWx1ZTogdGFnPy5uYW1lXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICB9XG5cbiAgICAgICAgICByZXR1cm4gaXNWYWxpZDtcbiAgICAgICAgfSk7XG5cbiAgICAgICAgY29uc29sZS5sb2coJ+KchSDmnInmlYjmoIfnrb7mlbDph486JywgdmFsaWRUYWdzLmxlbmd0aCk7XG4gICAgICAgIGNvbnNvbGUubG9nKCfinIUg5pyJ5pWI5qCH562+6K+m5oOFOicsIHZhbGlkVGFncyk7XG5cbiAgICAgICAgaWYgKHZhbGlkVGFncy5sZW5ndGggPiAwKSB7XG4gICAgICAgICAgc2V0Q291cnNlVGFncyh2YWxpZFRhZ3MpO1xuICAgICAgICAgIGNvbnNvbGUubG9nKCfinIUg5oiQ5Yqf6K6+572u55yf5a6e5qCH562+5pWw5o2uJyk7XG4gICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIGNvbnNvbGUud2Fybign4pqg77iPIOayoeacieacieaViOeahOagh+etvuaVsOaNricpO1xuICAgICAgICB9XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBjb25zb2xlLndhcm4oJ+KaoO+4jyBBUEnlk43lupTmoLzlvI/kuI3mraPnoa46JywgcmVzcG9uc2UpO1xuICAgICAgfVxuXG4gICAgICAvLyDlpoLmnpzmsqHmnInnnJ/lrp7mlbDmja7vvIzorr7nva7nqbrmlbDnu4RcbiAgICAgIGNvbnNvbGUubG9nKCfwn5OtIOayoeacieagh+etvuaVsOaNru+8jOiuvue9ruepuuaVsOe7hCcpO1xuICAgICAgc2V0Q291cnNlVGFncyhbXSk7XG5cbiAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfinYwg5Yqg6L296K++56iL5qCH562+5aSx6LSlOicsIGVycm9yKTtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDplJnor6/or6bmg4U6Jywge1xuICAgICAgICBtZXNzYWdlOiBlcnJvci5tZXNzYWdlLFxuICAgICAgICBzdGF0dXM6IGVycm9yLnJlc3BvbnNlPy5zdGF0dXMsXG4gICAgICAgIHN0YXR1c1RleHQ6IGVycm9yLnJlc3BvbnNlPy5zdGF0dXNUZXh0LFxuICAgICAgICBkYXRhOiBlcnJvci5yZXNwb25zZT8uZGF0YVxuICAgICAgfSk7XG5cbiAgICAgIC8vIOWPkeeUn+mUmeivr+aXtuiuvue9ruepuuaVsOe7hFxuICAgICAgc2V0Q291cnNlVGFncyhbXSk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldFRhZ3NMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgLy8g5re75Yqg5paw6K++56iLXG4gIGNvbnN0IGFkZE5ld0NvdXJzZSA9ICgpID0+IHtcbiAgICBjb25zdCBuZXdDb3Vyc2U6IENvdXJzZUl0ZW0gPSB7XG4gICAgICBpZDogRGF0ZS5ub3coKSxcbiAgICAgIHNlcmllc0lkOiBzZXJpZXNJZCxcbiAgICAgIHRpdGxlOiBg56ysJHtjb3Vyc2VMaXN0Lmxlbmd0aCArIDF96K++IC0g5paw6K++5pe2YCxcbiAgICAgIGRlc2NyaXB0aW9uOiAnJyxcbiAgICAgIGNvdmVySW1hZ2U6ICcnLFxuICAgICAgb3JkZXJJbmRleDogY291cnNlTGlzdC5sZW5ndGggKyAxLFxuICAgICAgc3RhdHVzOiAwLFxuICAgICAgc3RhdHVzTGFiZWw6ICfojYnnqL8nLFxuICAgICAgaGFzVmlkZW86IDAsXG4gICAgICBoYXNEb2N1bWVudDogMCxcbiAgICAgIGhhc0F1ZGlvOiAwLFxuICAgICAgdmlkZW9EdXJhdGlvbjogMCxcbiAgICAgIHZpZGVvRHVyYXRpb25MYWJlbDogJycsXG4gICAgICB2aWRlb05hbWU6ICcnLFxuICAgICAgZmlyc3RUZWFjaGluZ1RpdGxlOiAnJyxcbiAgICAgIHJlc291cmNlc0NvdW50OiAwLFxuICAgICAgY3JlYXRlZEF0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgICB1cGRhdGVkQXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgIH07XG4gICAgc2V0Q291cnNlTGlzdChbLi4uY291cnNlTGlzdCwgbmV3Q291cnNlXSk7XG4gICAgLy8g6Ieq5Yqo6YCJ5Lit5paw5re75Yqg55qE6K++56iLXG4gICAgc2hvd0NvdXJzZVBhbmVsKG5ld0NvdXJzZS5pZCk7XG4gIH07XG5cbiAgLy8g5pi+56S65Yig6Zmk56Gu6K6k5by556qXXG4gIGNvbnN0IHNob3dEZWxldGVDb25maXJtID0gKGlkOiBudW1iZXIpID0+IHtcbiAgICBzZXRDb3Vyc2VUb0RlbGV0ZShpZCk7XG4gICAgc2V0RGVsZXRlQ29uZmlybVZpc2libGUodHJ1ZSk7XG4gIH07XG5cbiAgLy8g56Gu6K6k5Yig6Zmk6K++56iLXG4gIGNvbnN0IGNvbmZpcm1EZWxldGVDb3Vyc2UgPSBhc3luYyAoKSA9PiB7XG4gICAgaWYgKCFjb3Vyc2VUb0RlbGV0ZSkgcmV0dXJuO1xuXG4gICAgdHJ5IHtcbiAgICAgIHNldElzRGVsZXRpbmcodHJ1ZSk7XG5cbiAgICAgIC8vIOiwg+eUqOWIoOmZpEFQSVxuICAgICAgYXdhaXQgY291cnNlTWFuYWdlbWVudEFwaS5kZWxldGVDb3Vyc2UoY291cnNlVG9EZWxldGUpO1xuXG4gICAgICAvLyDku47liJfooajkuK3np7vpmaTor77nqItcbiAgICAgIHNldENvdXJzZUxpc3QoY291cnNlTGlzdC5maWx0ZXIoY291cnNlID0+IGNvdXJzZS5pZCAhPT0gY291cnNlVG9EZWxldGUpKTtcblxuICAgICAgLy8g5aaC5p6c5Yig6Zmk55qE5piv5b2T5YmN6YCJ5Lit55qE6K++56iL77yM5riF56m65Y+z5L6n6Z2i5p2/XG4gICAgICBpZiAoc2VsZWN0ZWRDb3Vyc2VJZCA9PT0gY291cnNlVG9EZWxldGUpIHtcbiAgICAgICAgc2V0UmlnaHRQYW5lbFR5cGUoJ25vbmUnKTtcbiAgICAgICAgc2V0U2VsZWN0ZWRDb3Vyc2VJZChudWxsKTtcbiAgICAgIH1cblxuICAgICAgLy8g5YWz6Zet56Gu6K6k5by556qXXG4gICAgICBzZXREZWxldGVDb25maXJtVmlzaWJsZShmYWxzZSk7XG4gICAgICBzZXRDb3Vyc2VUb0RlbGV0ZShudWxsKTtcblxuICAgICAgLy8g5pi+56S65oiQ5Yqf5o+Q56S6XG4gICAgICBub3RpZmljYXRpb24uc3VjY2Vzcygn6K++56iL5bey5oiQ5Yqf5Yig6ZmkJyk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+WIoOmZpOivvueoi+Wksei0pTonLCBlcnJvcik7XG4gICAgICBub3RpZmljYXRpb24uZXJyb3IoJ+WIoOmZpOivvueoi+Wksei0pe+8jOivt+mHjeivlScpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRJc0RlbGV0aW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgLy8g5Y+W5raI5Yig6ZmkXG4gIGNvbnN0IGNhbmNlbERlbGV0ZSA9ICgpID0+IHtcbiAgICBpZiAoaXNEZWxldGluZykgcmV0dXJuOyAvLyDmraPlnKjliKDpmaTml7bkuI3lhYHorrjlj5bmtohcbiAgICBzZXREZWxldGVDb25maXJtVmlzaWJsZShmYWxzZSk7XG4gICAgc2V0Q291cnNlVG9EZWxldGUobnVsbCk7XG4gIH07XG5cbiAgLy8g5pu05paw6K++56iL5qCH6aKYXG4gIGNvbnN0IHVwZGF0ZUNvdXJzZVRpdGxlID0gKGlkOiBudW1iZXIsIG5ld1RpdGxlOiBzdHJpbmcpID0+IHtcbiAgICBzZXRDb3Vyc2VMaXN0KGNvdXJzZUxpc3QubWFwKGNvdXJzZSA9PlxuICAgICAgY291cnNlLmlkID09PSBpZCA/IHsgLi4uY291cnNlLCB0aXRsZTogbmV3VGl0bGUgfSA6IGNvdXJzZVxuICAgICkpO1xuICB9O1xuXG4gIC8vIOWkhOeQhuivvueoi+WwgemdouS4iuS8oFxuICBjb25zdCBoYW5kbGVDb3ZlclVwbG9hZCA9IGFzeW5jIChldmVudDogUmVhY3QuQ2hhbmdlRXZlbnQ8SFRNTElucHV0RWxlbWVudD4pID0+IHtcbiAgICBjb25zdCBmaWxlID0gZXZlbnQudGFyZ2V0LmZpbGVzPy5bMF07XG4gICAgaWYgKGZpbGUpIHtcbiAgICAgIC8vIOajgOafpeaWh+S7tuexu+Wei1xuICAgICAgY29uc3QgYWxsb3dlZFR5cGVzID0gWydpbWFnZS9qcGVnJywgJ2ltYWdlL2pwZycsICdpbWFnZS9wbmcnLCAnaW1hZ2UvZ2lmJ107XG4gICAgICBpZiAoIWFsbG93ZWRUeXBlcy5pbmNsdWRlcyhmaWxlLnR5cGUpKSB7XG4gICAgICAgIG5vdGlmaWNhdGlvbi5lcnJvcign6K+36YCJ5oupIEpQR+OAgVBORyDmiJYgR0lGIOagvOW8j+eahOWbvueJh+aWh+S7ticpO1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG5cbiAgICAgIC8vIOajgOafpeaWh+S7tuWkp+WwjyAoMTBNQilcbiAgICAgIGlmIChmaWxlLnNpemUgPiAxMCAqIDEwMjQgKiAxMDI0KSB7XG4gICAgICAgIG5vdGlmaWNhdGlvbi5lcnJvcign5paH5Lu25aSn5bCP5LiN6IO96LaF6L+HIDEwTUInKTtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuXG4gICAgICB0cnkge1xuICAgICAgICBjb25zb2xlLmxvZygn8J+TpCDlvIDlp4vkuIrkvKDor77nqIvlsIHpnaI6JywgZmlsZS5uYW1lKTtcblxuICAgICAgICAvLyDmt7vliqDliLDkuIrkvKDkuK3nmoTmlofku7bliJfooahcbiAgICAgICAgc2V0VXBsb2FkaW5nRmlsZXMocHJldiA9PiBuZXcgU2V0KHByZXYpLmFkZCgnY292ZXInKSk7XG5cbiAgICAgICAgLy8g5YWI5pi+56S66aKE6KeI5Zu+54mHXG4gICAgICAgIGNvbnN0IHByZXZpZXdVcmwgPSBVUkwuY3JlYXRlT2JqZWN0VVJMKGZpbGUpO1xuICAgICAgICBzZXRDb3Vyc2VEZXRhaWwocHJldiA9PiAoeyAuLi5wcmV2LCBjb3ZlckltYWdlOiBwcmV2aWV3VXJsIH0pKTtcblxuICAgICAgICAvLyDkuIrkvKDliLBPU1NcbiAgICAgICAgY29uc3QgaW1hZ2VVcmwgPSBhd2FpdCB1cGxvYWRBcGkudXBsb2FkVG9Pc3MoZmlsZSk7XG4gICAgICAgIGNvbnNvbGUubG9nKCfinIUg6K++56iL5bCB6Z2i5LiK5Lyg5oiQ5YqfOicsIGltYWdlVXJsKTtcblxuICAgICAgICAvLyDmm7TmlrDor77nqIvor6bmg4XkuK3nmoTlsIHpnaLkuLrnnJ/lrp5VUkxcbiAgICAgICAgc2V0Q291cnNlRGV0YWlsKHByZXYgPT4gKHsgLi4ucHJldiwgY292ZXJJbWFnZTogaW1hZ2VVcmwgfSkpO1xuXG4gICAgICAgIC8vIOWQjOaXtuabtOaWsOivvueoi+WIl+ihqOS4reeahOWwgemdolxuICAgICAgICBpZiAoc2VsZWN0ZWRDb3Vyc2VJZCkge1xuICAgICAgICAgIHNldENvdXJzZUxpc3QocHJldiA9PiBwcmV2Lm1hcChjb3Vyc2UgPT5cbiAgICAgICAgICAgIGNvdXJzZS5pZCA9PT0gc2VsZWN0ZWRDb3Vyc2VJZCA/IHsgLi4uY291cnNlLCBjb3ZlckltYWdlOiBpbWFnZVVybCB9IDogY291cnNlXG4gICAgICAgICAgKSk7XG4gICAgICAgIH1cblxuICAgICAgICBhbGVydCgn6K++56iL5bCB6Z2i5LiK5Lyg5oiQ5Yqf77yBJyk7XG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCfinYwg6K++56iL5bCB6Z2i5LiK5Lyg5aSx6LSlOicsIGVycm9yKTtcbiAgICAgICAgbm90aWZpY2F0aW9uLmVycm9yKCfor77nqIvlsIHpnaLkuIrkvKDlpLHotKXvvIzor7fph43or5UnKTtcblxuICAgICAgICAvLyDkuIrkvKDlpLHotKXml7bmuIXpmaTpooTop4jlm77niYdcbiAgICAgICAgc2V0Q291cnNlRGV0YWlsKHByZXYgPT4gKHsgLi4ucHJldiwgY292ZXJJbWFnZTogJycgfSkpO1xuICAgICAgfSBmaW5hbGx5IHtcbiAgICAgICAgLy8g5LuO5LiK5Lyg5Lit55qE5paH5Lu25YiX6KGo56e76ZmkXG4gICAgICAgIHNldFVwbG9hZGluZ0ZpbGVzKHByZXYgPT4ge1xuICAgICAgICAgIGNvbnN0IG5ld1NldCA9IG5ldyBTZXQocHJldik7XG4gICAgICAgICAgbmV3U2V0LmRlbGV0ZSgnY292ZXInKTtcbiAgICAgICAgICByZXR1cm4gbmV3U2V0O1xuICAgICAgICB9KTtcbiAgICAgIH1cbiAgICB9XG4gIH07XG5cbiAgLy8g5aSE55CG6KeG6aKR5LiK5LygXG4gIGNvbnN0IGhhbmRsZVZpZGVvVXBsb2FkID0gYXN5bmMgKGV2ZW50OiBSZWFjdC5DaGFuZ2VFdmVudDxIVE1MSW5wdXRFbGVtZW50PikgPT4ge1xuICAgIGNvbnN0IGZpbGUgPSBldmVudC50YXJnZXQuZmlsZXM/LlswXTtcbiAgICBpZiAoZmlsZSkge1xuICAgICAgLy8g5qOA5p+l5paH5Lu257G75Z6LXG4gICAgICBjb25zdCBhbGxvd2VkVHlwZXMgPSBbJ3ZpZGVvL21wNCcsICd2aWRlby9hdmknLCAndmlkZW8vbW92JywgJ3ZpZGVvL3dtdicsICd2aWRlby9mbHYnXTtcbiAgICAgIGlmICghYWxsb3dlZFR5cGVzLmluY2x1ZGVzKGZpbGUudHlwZSkpIHtcbiAgICAgICAgYWxlcnQoJ+ivt+mAieaLqSBNUDTjgIFBVknjgIFNT1bjgIFXTVYg5oiWIEZMViDmoLzlvI/nmoTop4bpopHmlofku7YnKTtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuXG4gICAgICAvLyDmo4Dmn6Xmlofku7blpKflsI8gKDEwME1CKVxuICAgICAgaWYgKGZpbGUuc2l6ZSA+IDEwMCAqIDEwMjQgKiAxMDI0KSB7XG4gICAgICAgIGFsZXJ0KCfop4bpopHmlofku7blpKflsI/kuI3og73otoXov4cgMTAwTUInKTtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuXG4gICAgICB0cnkge1xuICAgICAgICBjb25zb2xlLmxvZygn8J+TpCDlvIDlp4vkuIrkvKDor77nqIvop4bpopE6JywgZmlsZS5uYW1lKTtcblxuICAgICAgICAvLyDmt7vliqDliLDkuIrkvKDkuK3nmoTmlofku7bliJfooahcbiAgICAgICAgc2V0VXBsb2FkaW5nRmlsZXMocHJldiA9PiBuZXcgU2V0KHByZXYpLmFkZCgndmlkZW8nKSk7XG5cbiAgICAgICAgLy8g5YWI5pi+56S66aKE6KeI6KeG6aKRXG4gICAgICAgIGNvbnN0IHByZXZpZXdVcmwgPSBVUkwuY3JlYXRlT2JqZWN0VVJMKGZpbGUpO1xuICAgICAgICBzZXRDb3Vyc2VEZXRhaWwocHJldiA9PiAoe1xuICAgICAgICAgIC4uLnByZXYsXG4gICAgICAgICAgY29udGVudENvbmZpZzoge1xuICAgICAgICAgICAgLi4ucHJldi5jb250ZW50Q29uZmlnLFxuICAgICAgICAgICAgdmlkZW86IHtcbiAgICAgICAgICAgICAgdXJsOiBwcmV2aWV3VXJsLFxuICAgICAgICAgICAgICBuYW1lOiBmaWxlLm5hbWVcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG4gICAgICAgIH0pKTtcblxuICAgICAgICAvLyDkuIrkvKDliLBPU1NcbiAgICAgICAgY29uc3QgdmlkZW9VcmwgPSBhd2FpdCB1cGxvYWRBcGkudXBsb2FkVG9Pc3MoZmlsZSk7XG4gICAgICAgIGNvbnNvbGUubG9nKCfinIUg6K++56iL6KeG6aKR5LiK5Lyg5oiQ5YqfOicsIHZpZGVvVXJsKTtcblxuICAgICAgICAvLyDmm7TmlrDor77nqIvor6bmg4XkuK3nmoTop4bpopHkv6Hmga/kuLrnnJ/lrp5VUkxcbiAgICAgICAgc2V0Q291cnNlRGV0YWlsKHByZXYgPT4gKHtcbiAgICAgICAgICAuLi5wcmV2LFxuICAgICAgICAgIGNvbnRlbnRDb25maWc6IHtcbiAgICAgICAgICAgIC4uLnByZXYuY29udGVudENvbmZpZyxcbiAgICAgICAgICAgIHZpZGVvOiB7XG4gICAgICAgICAgICAgIHVybDogdmlkZW9VcmwsXG4gICAgICAgICAgICAgIG5hbWU6IGZpbGUubmFtZVxuICAgICAgICAgICAgfVxuICAgICAgICAgIH1cbiAgICAgICAgfSkpO1xuXG4gICAgICAgIC8vIOWQjOaXtuabtOaWsOivvueoi+WIl+ihqOS4reeahOinhumikeS/oeaBr1xuICAgICAgICBpZiAoc2VsZWN0ZWRDb3Vyc2VJZCkge1xuICAgICAgICAgIHNldENvdXJzZUxpc3QocHJldiA9PiBwcmV2Lm1hcChjb3Vyc2UgPT5cbiAgICAgICAgICAgIGNvdXJzZS5pZCA9PT0gc2VsZWN0ZWRDb3Vyc2VJZCA/IHtcbiAgICAgICAgICAgICAgLi4uY291cnNlLFxuICAgICAgICAgICAgICBjb250ZW50Q29uZmlnOiB7XG4gICAgICAgICAgICAgICAgLi4uY291cnNlLmNvbnRlbnRDb25maWcsXG4gICAgICAgICAgICAgICAgdmlkZW86IHtcbiAgICAgICAgICAgICAgICAgIHVybDogdmlkZW9VcmwsXG4gICAgICAgICAgICAgICAgICBuYW1lOiBmaWxlLm5hbWVcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0gOiBjb3Vyc2VcbiAgICAgICAgICApKTtcbiAgICAgICAgfVxuXG4gICAgICAgIGFsZXJ0KCfor77nqIvop4bpopHkuIrkvKDmiJDlip/vvIEnKTtcbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDor77nqIvop4bpopHkuIrkvKDlpLHotKU6JywgZXJyb3IpO1xuICAgICAgICBhbGVydCgn6K++56iL6KeG6aKR5LiK5Lyg5aSx6LSl77yM6K+36YeN6K+VJyk7XG5cbiAgICAgICAgLy8g5LiK5Lyg5aSx6LSl5pe25riF6Zmk6KeG6aKR5L+h5oGvXG4gICAgICAgIHNldENvdXJzZURldGFpbChwcmV2ID0+ICh7XG4gICAgICAgICAgLi4ucHJldixcbiAgICAgICAgICBjb250ZW50Q29uZmlnOiB7XG4gICAgICAgICAgICAuLi5wcmV2LmNvbnRlbnRDb25maWcsXG4gICAgICAgICAgICB2aWRlbzoge1xuICAgICAgICAgICAgICB1cmw6ICcnLFxuICAgICAgICAgICAgICBuYW1lOiAnJ1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH1cbiAgICAgICAgfSkpO1xuICAgICAgfSBmaW5hbGx5IHtcbiAgICAgICAgLy8g5LuO5LiK5Lyg5Lit55qE5paH5Lu25YiX6KGo56e76ZmkXG4gICAgICAgIHNldFVwbG9hZGluZ0ZpbGVzKHByZXYgPT4ge1xuICAgICAgICAgIGNvbnN0IG5ld1NldCA9IG5ldyBTZXQocHJldik7XG4gICAgICAgICAgbmV3U2V0LmRlbGV0ZSgndmlkZW8nKTtcbiAgICAgICAgICByZXR1cm4gbmV3U2V0O1xuICAgICAgICB9KTtcbiAgICAgIH1cbiAgICB9XG4gIH07XG5cbiAgLy8g6Kem5Y+R6KeG6aKR5paH5Lu26YCJ5oupXG4gIGNvbnN0IHRyaWdnZXJWaWRlb1VwbG9hZCA9ICgpID0+IHtcbiAgICBjb25zdCBpbnB1dCA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ2lucHV0Jyk7XG4gICAgaW5wdXQudHlwZSA9ICdmaWxlJztcbiAgICBpbnB1dC5hY2NlcHQgPSAndmlkZW8vbXA0LHZpZGVvL2F2aSx2aWRlby9tb3YsdmlkZW8vd212LHZpZGVvL2Zsdic7XG4gICAgaW5wdXQub25jaGFuZ2UgPSAoZSkgPT4gaGFuZGxlVmlkZW9VcGxvYWQoZSBhcyBhbnkpO1xuICAgIGlucHV0LmNsaWNrKCk7XG4gIH07XG5cbiAgLy8g5aSE55CG6ZmE5Lu25LiK5LygXG4gIGNvbnN0IGhhbmRsZUF0dGFjaG1lbnRVcGxvYWQgPSBhc3luYyAoZXZlbnQ6IFJlYWN0LkNoYW5nZUV2ZW50PEhUTUxJbnB1dEVsZW1lbnQ+KSA9PiB7XG4gICAgY29uc3QgZmlsZSA9IGV2ZW50LnRhcmdldC5maWxlcz8uWzBdO1xuICAgIGlmIChmaWxlKSB7XG4gICAgICAvLyDmo4Dmn6Xmlofku7bnsbvlnotcbiAgICAgIGNvbnN0IGFsbG93ZWRUeXBlcyA9IFtcbiAgICAgICAgJ2FwcGxpY2F0aW9uL3BkZicsXG4gICAgICAgICdhcHBsaWNhdGlvbi9tc3dvcmQnLFxuICAgICAgICAnYXBwbGljYXRpb24vdm5kLm9wZW54bWxmb3JtYXRzLW9mZmljZWRvY3VtZW50LndvcmRwcm9jZXNzaW5nbWwuZG9jdW1lbnQnLFxuICAgICAgICAnYXBwbGljYXRpb24vdm5kLm1zLWV4Y2VsJyxcbiAgICAgICAgJ2FwcGxpY2F0aW9uL3ZuZC5vcGVueG1sZm9ybWF0cy1vZmZpY2Vkb2N1bWVudC5zcHJlYWRzaGVldG1sLnNoZWV0JyxcbiAgICAgICAgJ2FwcGxpY2F0aW9uL3ZuZC5tcy1wb3dlcnBvaW50JyxcbiAgICAgICAgJ2FwcGxpY2F0aW9uL3ZuZC5vcGVueG1sZm9ybWF0cy1vZmZpY2Vkb2N1bWVudC5wcmVzZW50YXRpb25tbC5wcmVzZW50YXRpb24nLFxuICAgICAgICAndGV4dC9wbGFpbicsXG4gICAgICAgICdpbWFnZS9qcGVnJyxcbiAgICAgICAgJ2ltYWdlL3BuZycsXG4gICAgICAgICdpbWFnZS9naWYnXG4gICAgICBdO1xuXG4gICAgICBpZiAoIWFsbG93ZWRUeXBlcy5pbmNsdWRlcyhmaWxlLnR5cGUpKSB7XG4gICAgICAgIGFsZXJ0KCfor7fpgInmi6nmlK/mjIHnmoTmlofku7bmoLzlvI/vvJpQREbjgIFET0PjgIFET0NY44CBWExT44CBWExTWOOAgVBQVOOAgVBQVFjjgIFUWFTjgIFKUEfjgIFQTkfjgIFHSUYnKTtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuXG4gICAgICAvLyDmo4Dmn6Xmlofku7blpKflsI8gKDEwTUIpXG4gICAgICBpZiAoZmlsZS5zaXplID4gMTAgKiAxMDI0ICogMTAyNCkge1xuICAgICAgICBhbGVydCgn5paH5Lu25aSn5bCP5LiN6IO96LaF6L+HIDEwTUInKTtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuXG4gICAgICB0cnkge1xuICAgICAgICBjb25zb2xlLmxvZygn8J+TpCDlvIDlp4vkuIrkvKDor77nqIvpmYTku7Y6JywgZmlsZS5uYW1lKTtcblxuICAgICAgICAvLyDmt7vliqDliLDkuIrkvKDkuK3nmoTmlofku7bliJfooahcbiAgICAgICAgc2V0VXBsb2FkaW5nRmlsZXMocHJldiA9PiBuZXcgU2V0KHByZXYpLmFkZCgnZG9jdW1lbnQnKSk7XG5cbiAgICAgICAgLy8g5LiK5Lyg5YiwT1NTXG4gICAgICAgIGNvbnN0IGRvY3VtZW50VXJsID0gYXdhaXQgdXBsb2FkQXBpLnVwbG9hZFRvT3NzKGZpbGUpO1xuICAgICAgICBjb25zb2xlLmxvZygn4pyFIOivvueoi+mZhOS7tuS4iuS8oOaIkOWKnzonLCBkb2N1bWVudFVybCk7XG5cbiAgICAgICAgLy8g5pu05paw6K++56iL6K+m5oOF5Lit55qE6ZmE5Lu25L+h5oGvXG4gICAgICAgIHNldENvdXJzZURldGFpbChwcmV2ID0+ICh7XG4gICAgICAgICAgLi4ucHJldixcbiAgICAgICAgICBjb250ZW50Q29uZmlnOiB7XG4gICAgICAgICAgICAuLi5wcmV2LmNvbnRlbnRDb25maWcsXG4gICAgICAgICAgICBoYXNEb2N1bWVudDogMSxcbiAgICAgICAgICAgIGRvY3VtZW50OiB7XG4gICAgICAgICAgICAgIHVybDogZG9jdW1lbnRVcmwsXG4gICAgICAgICAgICAgIG5hbWU6IGZpbGUubmFtZVxuICAgICAgICAgICAgfVxuICAgICAgICAgIH1cbiAgICAgICAgfSkpO1xuXG4gICAgICAgIGFsZXJ0KCfor77nqIvpmYTku7bkuIrkvKDmiJDlip/vvIEnKTtcbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDor77nqIvpmYTku7bkuIrkvKDlpLHotKU6JywgZXJyb3IpO1xuICAgICAgICBhbGVydCgn6K++56iL6ZmE5Lu25LiK5Lyg5aSx6LSl77yM6K+36YeN6K+VJyk7XG4gICAgICB9IGZpbmFsbHkge1xuICAgICAgICAvLyDku47kuIrkvKDkuK3nmoTmlofku7bliJfooajnp7vpmaRcbiAgICAgICAgc2V0VXBsb2FkaW5nRmlsZXMocHJldiA9PiB7XG4gICAgICAgICAgY29uc3QgbmV3U2V0ID0gbmV3IFNldChwcmV2KTtcbiAgICAgICAgICBuZXdTZXQuZGVsZXRlKCdkb2N1bWVudCcpO1xuICAgICAgICAgIHJldHVybiBuZXdTZXQ7XG4gICAgICAgIH0pO1xuICAgICAgfVxuICAgIH1cbiAgfTtcblxuICAvLyDop6blj5HpmYTku7bmlofku7bpgInmi6lcbiAgY29uc3QgdHJpZ2dlckF0dGFjaG1lbnRVcGxvYWQgPSAoKSA9PiB7XG4gICAgY29uc3QgaW5wdXQgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCdpbnB1dCcpO1xuICAgIGlucHV0LnR5cGUgPSAnZmlsZSc7XG4gICAgaW5wdXQuYWNjZXB0ID0gJy5wZGYsLmRvYywuZG9jeCwueGxzLC54bHN4LC5wcHQsLnBwdHgsLnR4dCwuanBnLC5wbmcsLmdpZic7XG4gICAgaW5wdXQub25jaGFuZ2UgPSAoZSkgPT4gaGFuZGxlQXR0YWNobWVudFVwbG9hZChlIGFzIGFueSk7XG4gICAgaW5wdXQuY2xpY2soKTtcbiAgfTtcblxuICAvLyDlpITnkIbmlZnlrabpmYTku7bkuIrkvKBcbiAgY29uc3QgaGFuZGxlVGVhY2hpbmdNYXRlcmlhbFVwbG9hZCA9IGFzeW5jIChldmVudDogUmVhY3QuQ2hhbmdlRXZlbnQ8SFRNTElucHV0RWxlbWVudD4pID0+IHtcbiAgICBjb25zdCBmaWxlID0gZXZlbnQudGFyZ2V0LmZpbGVzPy5bMF07XG4gICAgaWYgKGZpbGUpIHtcbiAgICAgIC8vIOajgOafpeaWh+S7tuexu+Wei1xuICAgICAgY29uc3QgYWxsb3dlZFR5cGVzID0gW1xuICAgICAgICAnYXBwbGljYXRpb24vcGRmJyxcbiAgICAgICAgJ2FwcGxpY2F0aW9uL21zd29yZCcsXG4gICAgICAgICdhcHBsaWNhdGlvbi92bmQub3BlbnhtbGZvcm1hdHMtb2ZmaWNlZG9jdW1lbnQud29yZHByb2Nlc3NpbmdtbC5kb2N1bWVudCcsXG4gICAgICAgICdhcHBsaWNhdGlvbi92bmQubXMtZXhjZWwnLFxuICAgICAgICAnYXBwbGljYXRpb24vdm5kLm9wZW54bWxmb3JtYXRzLW9mZmljZWRvY3VtZW50LnNwcmVhZHNoZWV0bWwuc2hlZXQnLFxuICAgICAgICAnYXBwbGljYXRpb24vdm5kLm1zLXBvd2VycG9pbnQnLFxuICAgICAgICAnYXBwbGljYXRpb24vdm5kLm9wZW54bWxmb3JtYXRzLW9mZmljZWRvY3VtZW50LnByZXNlbnRhdGlvbm1sLnByZXNlbnRhdGlvbicsXG4gICAgICAgICd0ZXh0L3BsYWluJyxcbiAgICAgICAgJ2ltYWdlL2pwZWcnLFxuICAgICAgICAnaW1hZ2UvcG5nJyxcbiAgICAgICAgJ2ltYWdlL2dpZidcbiAgICAgIF07XG5cbiAgICAgIGlmICghYWxsb3dlZFR5cGVzLmluY2x1ZGVzKGZpbGUudHlwZSkpIHtcbiAgICAgICAgYWxlcnQoJ+ivt+mAieaLqeaUr+aMgeeahOaWh+S7tuagvOW8j++8mlBERuOAgURPQ+OAgURPQ1jjgIFYTFPjgIFYTFNY44CBUFBU44CBUFBUWOOAgVRYVOOAgUpQR+OAgVBOR+OAgUdJRicpO1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG5cbiAgICAgIC8vIOajgOafpeaWh+S7tuWkp+WwjyAoMTBNQilcbiAgICAgIGlmIChmaWxlLnNpemUgPiAxMCAqIDEwMjQgKiAxMDI0KSB7XG4gICAgICAgIGFsZXJ0KCfmlofku7blpKflsI/kuI3og73otoXov4cgMTBNQicpO1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG5cbiAgICAgIHRyeSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCfwn5OkIOW8gOWni+S4iuS8oOaVmeWtpuadkOaWmTonLCBmaWxlLm5hbWUpO1xuXG4gICAgICAgIC8vIOa3u+WKoOWIsOS4iuS8oOS4reeahOaWh+S7tuWIl+ihqFxuICAgICAgICBzZXRVcGxvYWRpbmdGaWxlcyhwcmV2ID0+IG5ldyBTZXQocHJldikuYWRkKGB0ZWFjaGluZy0ke0RhdGUubm93KCl9YCkpO1xuXG4gICAgICAgIC8vIOS4iuS8oOWIsE9TU1xuICAgICAgICBjb25zdCBtYXRlcmlhbFVybCA9IGF3YWl0IHVwbG9hZEFwaS51cGxvYWRUb09zcyhmaWxlKTtcbiAgICAgICAgY29uc29sZS5sb2coJ+KchSDmlZnlrabmnZDmlpnkuIrkvKDmiJDlip86JywgbWF0ZXJpYWxVcmwpO1xuXG4gICAgICAgIC8vIOa3u+WKoOWIsOaVmeWtpumZhOS7tuWIl+ihqFxuICAgICAgICBjb25zdCBuZXdNYXRlcmlhbCA9IHtcbiAgICAgICAgICB0eXBlOiBmaWxlLnR5cGUsXG4gICAgICAgICAgbmFtZTogZmlsZS5uYW1lLFxuICAgICAgICAgIHVybDogbWF0ZXJpYWxVcmxcbiAgICAgICAgfTtcblxuICAgICAgICBzZXRDb3Vyc2VEZXRhaWwocHJldiA9PiAoe1xuICAgICAgICAgIC4uLnByZXYsXG4gICAgICAgICAgdGVhY2hpbmdNYXRlcmlhbHM6IFsuLi5wcmV2LnRlYWNoaW5nTWF0ZXJpYWxzLCBuZXdNYXRlcmlhbF1cbiAgICAgICAgfSkpO1xuXG4gICAgICAgIGFsZXJ0KCfmlZnlrabmnZDmlpnkuIrkvKDmiJDlip/vvIEnKTtcbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDmlZnlrabmnZDmlpnkuIrkvKDlpLHotKU6JywgZXJyb3IpO1xuICAgICAgICBhbGVydCgn5pWZ5a2m5p2Q5paZ5LiK5Lyg5aSx6LSl77yM6K+36YeN6K+VJyk7XG4gICAgICB9IGZpbmFsbHkge1xuICAgICAgICAvLyDku47kuIrkvKDkuK3nmoTmlofku7bliJfooajnp7vpmaRcbiAgICAgICAgc2V0VXBsb2FkaW5nRmlsZXMocHJldiA9PiB7XG4gICAgICAgICAgY29uc3QgbmV3U2V0ID0gbmV3IFNldChwcmV2KTtcbiAgICAgICAgICAvLyDnp7vpmaTmiYDmnIl0ZWFjaGluZy3lvIDlpLTnmoTpobnnm65cbiAgICAgICAgICBBcnJheS5mcm9tKG5ld1NldCkuZm9yRWFjaChpdGVtID0+IHtcbiAgICAgICAgICAgIGlmIChpdGVtLnN0YXJ0c1dpdGgoJ3RlYWNoaW5nLScpKSB7XG4gICAgICAgICAgICAgIG5ld1NldC5kZWxldGUoaXRlbSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfSk7XG4gICAgICAgICAgcmV0dXJuIG5ld1NldDtcbiAgICAgICAgfSk7XG4gICAgICB9XG4gICAgfVxuICB9O1xuXG4gIC8vIOinpuWPkeaVmeWtpumZhOS7tuaWh+S7tumAieaLqVxuICBjb25zdCB0cmlnZ2VyVGVhY2hpbmdNYXRlcmlhbFVwbG9hZCA9ICgpID0+IHtcbiAgICBjb25zdCBpbnB1dCA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ2lucHV0Jyk7XG4gICAgaW5wdXQudHlwZSA9ICdmaWxlJztcbiAgICBpbnB1dC5hY2NlcHQgPSAnLnBkZiwuZG9jLC5kb2N4LC54bHMsLnhsc3gsLnBwdCwucHB0eCwudHh0LC5qcGcsLnBuZywuZ2lmJztcbiAgICBpbnB1dC5vbmNoYW5nZSA9IChlKSA9PiBoYW5kbGVUZWFjaGluZ01hdGVyaWFsVXBsb2FkKGUgYXMgYW55KTtcbiAgICBpbnB1dC5jbGljaygpO1xuICB9O1xuXG4gIC8vIOWIoOmZpOaVmeWtpumZhOS7tlxuICBjb25zdCByZW1vdmVUZWFjaGluZ01hdGVyaWFsID0gKGluZGV4OiBudW1iZXIpID0+IHtcbiAgICBzZXRDb3Vyc2VEZXRhaWwocHJldiA9PiAoe1xuICAgICAgLi4ucHJldixcbiAgICAgIHRlYWNoaW5nTWF0ZXJpYWxzOiBwcmV2LnRlYWNoaW5nTWF0ZXJpYWxzLmZpbHRlcigoXywgaSkgPT4gaSAhPT0gaW5kZXgpXG4gICAgfSkpO1xuICB9O1xuXG4gIC8vIOivvueoi+WGheWuueeuoeeQhuWHveaVsFxuICBjb25zdCBhZGRUZWFjaGluZ0luZm9JdGVtID0gKCkgPT4ge1xuICAgIHNldENvdXJzZURldGFpbChwcmV2ID0+ICh7XG4gICAgICAuLi5wcmV2LFxuICAgICAgdGVhY2hpbmdJbmZvOiBbLi4ucHJldi50ZWFjaGluZ0luZm8sIHsgdGl0bGU6ICcnLCBjb250ZW50OiAnJyB9XVxuICAgIH0pKTtcbiAgfTtcblxuICBjb25zdCByZW1vdmVUZWFjaGluZ0luZm9JdGVtID0gKGluZGV4OiBudW1iZXIpID0+IHtcbiAgICBzZXRDb3Vyc2VEZXRhaWwocHJldiA9PiAoe1xuICAgICAgLi4ucHJldixcbiAgICAgIHRlYWNoaW5nSW5mbzogcHJldi50ZWFjaGluZ0luZm8uZmlsdGVyKChfLCBpKSA9PiBpICE9PSBpbmRleClcbiAgICB9KSk7XG4gIH07XG5cbiAgY29uc3QgdXBkYXRlVGVhY2hpbmdJbmZvVGl0bGUgPSAoaW5kZXg6IG51bWJlciwgdGl0bGU6IHN0cmluZykgPT4ge1xuICAgIHNldENvdXJzZURldGFpbChwcmV2ID0+IHtcbiAgICAgIGNvbnN0IG5ld1RlYWNoaW5nSW5mbyA9IFsuLi5wcmV2LnRlYWNoaW5nSW5mb107XG4gICAgICBuZXdUZWFjaGluZ0luZm9baW5kZXhdID0geyAuLi5uZXdUZWFjaGluZ0luZm9baW5kZXhdLCB0aXRsZSB9O1xuICAgICAgcmV0dXJuIHsgLi4ucHJldiwgdGVhY2hpbmdJbmZvOiBuZXdUZWFjaGluZ0luZm8gfTtcbiAgICB9KTtcbiAgfTtcblxuICBjb25zdCB1cGRhdGVUZWFjaGluZ0luZm9Db250ZW50ID0gKGluZGV4OiBudW1iZXIsIGNvbnRlbnQ6IHN0cmluZykgPT4ge1xuICAgIHNldENvdXJzZURldGFpbChwcmV2ID0+IHtcbiAgICAgIGNvbnN0IG5ld1RlYWNoaW5nSW5mbyA9IFsuLi5wcmV2LnRlYWNoaW5nSW5mb107XG4gICAgICBuZXdUZWFjaGluZ0luZm9baW5kZXhdID0geyAuLi5uZXdUZWFjaGluZ0luZm9baW5kZXhdLCBjb250ZW50IH07XG4gICAgICByZXR1cm4geyAuLi5wcmV2LCB0ZWFjaGluZ0luZm86IG5ld1RlYWNoaW5nSW5mbyB9O1xuICAgIH0pO1xuICB9O1xuXG4gIC8vIOWwhlVJ5qC85byP55qEdGVhY2hpbmdJbmZv6L2s5o2i5Li6QVBJ5qC85byPXG4gIGNvbnN0IGNvbnZlcnRUZWFjaGluZ0luZm9Gb3JBUEkgPSAodGVhY2hpbmdJbmZvOiB7IHRpdGxlOiBzdHJpbmc7IGNvbnRlbnQ6IHN0cmluZyB9W10pID0+IHtcbiAgICByZXR1cm4gdGVhY2hpbmdJbmZvLm1hcChpbmZvID0+ICh7XG4gICAgICB0aXRsZTogaW5mby50aXRsZSxcbiAgICAgIGNvbnRlbnQ6IGluZm8uY29udGVudC5zcGxpdCgnXFxuJykuZmlsdGVyKGxpbmUgPT4gbGluZS50cmltKCkpIC8vIOaMieaNouihjOWIhuWJsuW5tui/h+a7pOepuuihjFxuICAgIH0pKTtcbiAgfTtcblxuICAvLyDmo4Dmn6XmmK/lkKbmnInmnKrkuIrkvKDlrozmiJDnmoTmlofku7bvvIhibG9iIFVSTO+8iVxuICBjb25zdCBjaGVja0ZvckJsb2JVcmxzID0gKGNvdXJzZTogQ291cnNlSXRlbSkgPT4ge1xuICAgIGNvbnN0IGlzc3VlcyA9IFtdO1xuXG4gICAgLy8g5qOA5p+l5bCB6Z2i5Zu+54mHXG4gICAgaWYgKGNvdXJzZS5jb3ZlckltYWdlICYmIGNvdXJzZS5jb3ZlckltYWdlLnN0YXJ0c1dpdGgoJ2Jsb2I6JykpIHtcbiAgICAgIGlzc3Vlcy5wdXNoKCfor77nqIvlsIHpnaLlm77niYcnKTtcbiAgICB9XG5cbiAgICAvLyDmo4Dmn6Xop4bpopHmlofku7ZcbiAgICBpZiAoY291cnNlLmNvbnRlbnRDb25maWc/LnZpZGVvPy51cmwgJiYgY291cnNlLmNvbnRlbnRDb25maWcudmlkZW8udXJsLnN0YXJ0c1dpdGgoJ2Jsb2I6JykpIHtcbiAgICAgIGlzc3Vlcy5wdXNoKCfor77nqIvop4bpopEnKTtcbiAgICB9XG5cbiAgICAvLyDmo4Dmn6XmlofmoaPpmYTku7ZcbiAgICBpZiAoY291cnNlLmNvbnRlbnRDb25maWc/LmRvY3VtZW50Py51cmwgJiYgY291cnNlLmNvbnRlbnRDb25maWcuZG9jdW1lbnQudXJsLnN0YXJ0c1dpdGgoJ2Jsb2I6JykpIHtcbiAgICAgIGlzc3Vlcy5wdXNoKCfor77nqIvmlofmoaMnKTtcbiAgICB9XG5cbiAgICAvLyDmo4Dmn6XmlZnlrabmnZDmlplcbiAgICBpZiAoY291cnNlLmFkZGl0aW9uYWxSZXNvdXJjZXMpIHtcbiAgICAgIGNvdXJzZS5hZGRpdGlvbmFsUmVzb3VyY2VzLmZvckVhY2goKHJlc291cmNlLCBpbmRleCkgPT4ge1xuICAgICAgICBpZiAocmVzb3VyY2UudXJsICYmIHJlc291cmNlLnVybC5zdGFydHNXaXRoKCdibG9iOicpKSB7XG4gICAgICAgICAgaXNzdWVzLnB1c2goYOaVmeWtpuadkOaWmSR7aW5kZXggKyAxfWApO1xuICAgICAgICB9XG4gICAgICB9KTtcbiAgICB9XG5cbiAgICByZXR1cm4gaXNzdWVzO1xuICB9O1xuXG4gIC8vIOS/neWtmOWNleS4quivvueoi+WIsOWQjuerr1xuICBjb25zdCBzYXZlQ291cnNlID0gYXN5bmMgKGNvdXJzZTogQ291cnNlSXRlbSkgPT4ge1xuICAgIHRyeSB7XG4gICAgICAvLyDpqozor4Hlv4XopoHnmoTmlbDmja5cbiAgICAgIGlmICghY291cnNlLnRpdGxlIHx8IGNvdXJzZS50aXRsZS50cmltKCkgPT09ICcnKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcign6K++56iL5qCH6aKY5LiN6IO95Li656m6Jyk7XG4gICAgICB9XG5cbiAgICAgIC8vIOajgOafpeaYr+WQpuacieacquS4iuS8oOWujOaIkOeahOaWh+S7tlxuICAgICAgY29uc3QgYmxvYklzc3VlcyA9IGNoZWNrRm9yQmxvYlVybHMoY291cnNlKTtcbiAgICAgIGlmIChibG9iSXNzdWVzLmxlbmd0aCA+IDApIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGDku6XkuIvmlofku7blsJrmnKrkuIrkvKDlrozmiJDvvIzor7fnrYnlvoXkuIrkvKDlrozmiJDlkI7lho3kv53lrZjvvJoke2Jsb2JJc3N1ZXMuam9pbign44CBJyl9YCk7XG4gICAgICB9XG5cbiAgICAgIC8vIOWHhuWkh+ivvueoi+aVsOaNrlxuICAgICAgY29uc3QgY291cnNlRGF0YSA9IHtcbiAgICAgICAgc2VyaWVzSWQ6IHNlcmllc0lkLFxuICAgICAgICB0aXRsZTogY291cnNlLnRpdGxlLFxuICAgICAgICBkZXNjcmlwdGlvbjogY291cnNlLmRlc2NyaXB0aW9uIHx8ICcnLFxuICAgICAgICBjb3ZlckltYWdlOiBjb3Vyc2UuY292ZXJJbWFnZSB8fCAnJyxcbiAgICAgICAgaGFzVmlkZW86IGNvdXJzZS5jb250ZW50Q29uZmlnPy52aWRlbz8udXJsID8gMSA6IDAsXG4gICAgICAgIGhhc0RvY3VtZW50OiBjb3Vyc2UuY29udGVudENvbmZpZz8uZG9jdW1lbnQ/LnVybCA/IDEgOiAwLFxuICAgICAgICBoYXNBdWRpbzogMCxcbiAgICAgICAgdmlkZW9EdXJhdGlvbjogMCxcbiAgICAgICAgY29udGVudENvbmZpZzogY291cnNlLmNvbnRlbnRDb25maWcgfHwge30sXG4gICAgICAgIHRlYWNoaW5nSW5mbzogY29udmVydFRlYWNoaW5nSW5mb0ZvckFQSShjb3Vyc2UudGVhY2hpbmdJbmZvIHx8IFtdKSxcbiAgICAgICAgYWRkaXRpb25hbFJlc291cmNlczogY291cnNlLmFkZGl0aW9uYWxSZXNvdXJjZXMgfHwgW10sXG4gICAgICAgIG9yZGVySW5kZXg6IGNvdXJzZS5vcmRlckluZGV4IHx8IDFcbiAgICAgIH07XG5cbiAgICAgIGNvbnNvbGUubG9nKCfwn5K+IOWHhuWkh+S/neWtmOivvueoi+aVsOaNrjonLCBjb3Vyc2VEYXRhKTtcblxuICAgICAgbGV0IHJlc3VsdDogYW55O1xuXG4gICAgICAvLyDliKTmlq3mmK/mlrDor77nqIvov5jmmK/mm7TmlrDor77nqItcbiAgICAgIGlmIChjb3Vyc2UuaWQgJiYgY291cnNlLmlkID4gMTAwMDAwMCkge1xuICAgICAgICAvLyDmlrDor77nqIvvvIzkvb/nlKjliJvlu7pBUElcbiAgICAgICAgY29uc29sZS5sb2coJ/Cfk6Qg5Yib5bu65paw6K++56iLJyk7XG4gICAgICAgIGNvbnN0IHsgZGF0YTogcmVzcG9uc2UgfSA9IGF3YWl0IGNvdXJzZUFwaS5jcmVhdGVDb3Vyc2UoY291cnNlRGF0YSk7XG4gICAgICAgIHJlc3VsdCA9IHJlc3BvbnNlO1xuICAgICAgICBjb25zb2xlLmxvZygn4pyFIOivvueoi+WIm+W7uuaIkOWKnycpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgLy8g546w5pyJ6K++56iL77yM5L2/55So5pu05pawQVBJXG4gICAgICAgIGNvbnNvbGUubG9nKCfwn5OkIOabtOaWsOeOsOacieivvueoizonLCBjb3Vyc2UuaWQpO1xuICAgICAgICBjb25zdCB7IGRhdGE6IHJlc3BvbnNlIH0gPSBhd2FpdCBjb3Vyc2VBcGkudXBkYXRlQ291cnNlKGNvdXJzZS5pZCwgY291cnNlRGF0YSk7XG4gICAgICAgIHJlc3VsdCA9IHJlc3BvbnNlO1xuICAgICAgICBjb25zb2xlLmxvZygn4pyFIOivvueoi+abtOaWsOaIkOWKnycpO1xuICAgICAgfVxuXG4gICAgICByZXR1cm4gcmVzdWx0O1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfinYwg5L+d5a2Y6K++56iL5aSx6LSlOicsIGVycm9yKTtcbiAgICAgIHRocm93IGVycm9yO1xuICAgIH1cbiAgfTtcblxuICAvLyDlj5HluIPpgInkuK3nmoTor77nqItcbiAgY29uc3QgaGFuZGxlUHVibGlzaENvdXJzZSA9IGFzeW5jICgpID0+IHtcbiAgICBpZiAoIXNlbGVjdGVkQ291cnNlSWQpIHtcbiAgICAgIGFsZXJ0KCfor7flhYjpgInmi6nopoHlj5HluIPnmoTor77nqIsnKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICBjb25zdCBzZWxlY3RlZENvdXJzZSA9IGNvdXJzZUxpc3QuZmluZChjb3Vyc2UgPT4gY291cnNlLmlkID09PSBzZWxlY3RlZENvdXJzZUlkKTtcbiAgICBpZiAoIXNlbGVjdGVkQ291cnNlKSB7XG4gICAgICBhbGVydCgn5pyq5om+5Yiw6YCJ5Lit55qE6K++56iLJyk7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgLy8g5qOA5p+l6K++56iL5piv5ZCm5bey57uP5Y+R5biDXG4gICAgaWYgKHNlbGVjdGVkQ291cnNlLnN0YXR1cyA9PT0gMSkge1xuICAgICAgYWxlcnQoJ+ivpeivvueoi+W3sue7j+WPkeW4g++8jOaXoOmcgOmHjeWkjeWPkeW4gycpO1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIHRyeSB7XG4gICAgICBzZXRJc1B1Ymxpc2hpbmcodHJ1ZSk7XG4gICAgICBjb25zb2xlLmxvZygn8J+TpCDlvIDlp4vlj5HluIPor77nqIsnKTtcbiAgICAgIGNvbnNvbGUubG9nKCfwn5OkIHNlbGVjdGVkQ291cnNlSWQ6Jywgc2VsZWN0ZWRDb3Vyc2VJZCwgJ+exu+WeizonLCB0eXBlb2Ygc2VsZWN0ZWRDb3Vyc2VJZCk7XG4gICAgICBjb25zb2xlLmxvZygn8J+TpCDor77nqIvkv6Hmga86Jywgc2VsZWN0ZWRDb3Vyc2UpO1xuICAgICAgY29uc29sZS5sb2coJ/Cfk6Qg6K++56iLSUTlrZfmrrU6Jywgc2VsZWN0ZWRDb3Vyc2UuaWQsICfnsbvlnos6JywgdHlwZW9mIHNlbGVjdGVkQ291cnNlLmlkKTtcbiAgICAgIGNvbnNvbGUubG9nKCfwn5OkIOivvueoi+WIl+ihqOS4reeahOaJgOaciUlEOicsIGNvdXJzZUxpc3QubWFwKGMgPT4gKHsgaWQ6IGMuaWQsIHR5cGU6IHR5cGVvZiBjLmlkLCB0aXRsZTogYy50aXRsZSB9KSkpO1xuXG4gICAgICAvLyDnoa7kv53kvb/nlKjmraPnoa7nmoTor77nqItJRFxuICAgICAgY29uc3QgY291cnNlSWRUb1B1Ymxpc2ggPSBzZWxlY3RlZENvdXJzZS5pZDtcbiAgICAgIGNvbnNvbGUubG9nKCfwn5OkIOWNs+WwhuWPkeW4g+eahOivvueoi0lEOicsIGNvdXJzZUlkVG9QdWJsaXNoLCAn57G75Z6LOicsIHR5cGVvZiBjb3Vyc2VJZFRvUHVibGlzaCk7XG5cbiAgICAgIGNvbnN0IHsgZGF0YTogcmVzcG9uc2UgfSA9IGF3YWl0IGNvdXJzZUFwaS5wdWJsaXNoQ291cnNlKGNvdXJzZUlkVG9QdWJsaXNoKTtcblxuICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkge1xuICAgICAgICBjb25zb2xlLmxvZygn4pyFIOivvueoi+WPkeW4g+aIkOWKnzonLCByZXNwb25zZS5kYXRhKTtcbiAgICAgICAgYWxlcnQoJ+ivvueoi+WPkeW4g+aIkOWKn++8gScpO1xuXG4gICAgICAgIC8vIOWIt+aWsOivvueoi+WIl+ihqOS7peabtOaWsOeKtuaAgVxuICAgICAgICBhd2FpdCBsb2FkQ291cnNlTGlzdCgpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgY29uc29sZS5lcnJvcign4p2MIOWPkeW4g+ivvueoi+Wksei0pTonLCByZXNwb25zZS5tZXNzYWdlKTtcbiAgICAgICAgYWxlcnQocmVzcG9uc2UubWVzc2FnZSB8fCAn5Y+R5biD6K++56iL5aSx6LSlJyk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgY29uc29sZS5lcnJvcign4p2MIOWPkeW4g+ivvueoi+Wksei0pTonLCBlcnJvcik7XG4gICAgICBjb25zb2xlLmVycm9yKCfinYwg6ZSZ6K+v6K+m5oOFOicsIGVycm9yLnJlc3BvbnNlPy5kYXRhKTtcblxuICAgICAgLy8g5aSE55CG5YW35L2T55qE6ZSZ6K+v5L+h5oGvXG4gICAgICBpZiAoZXJyb3IucmVzcG9uc2U/LmRhdGE/Lm1lc3NhZ2UpIHtcbiAgICAgICAgYWxlcnQoZXJyb3IucmVzcG9uc2UuZGF0YS5tZXNzYWdlKTtcbiAgICAgIH0gZWxzZSBpZiAoZXJyb3IubWVzc2FnZSkge1xuICAgICAgICBhbGVydChlcnJvci5tZXNzYWdlKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGFsZXJ0KCflj5HluIPor77nqIvlpLHotKXvvIzor7fph43or5UnKTtcbiAgICAgIH1cbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0SXNQdWJsaXNoaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgLy8g5L+d5a2Y6K++56iL5YiX6KGoXG4gIGNvbnN0IGhhbmRsZVNhdmUgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIHNldElzQ3JlYXRpbmcodHJ1ZSk7XG4gICAgICAvLyDmo4Dmn6XmmK/lkKbmnInmlofku7bmraPlnKjkuIrkvKBcbiAgICAgIGlmICh1cGxvYWRpbmdGaWxlcy5zaXplID4gMCkge1xuICAgICAgICBhbGVydCgn5pyJ5paH5Lu25q2j5Zyo5LiK5Lyg5Lit77yM6K+3562J5b6F5LiK5Lyg5a6M5oiQ5ZCO5YaN5L+d5a2YJyk7XG4gICAgICAgIHNldElzQ3JlYXRpbmcoZmFsc2UpO1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG5cbiAgICAgIGNvbnNvbGUubG9nKCfwn5K+IOW8gOWni+S/neWtmOivvueoi+WIl+ihqCcpO1xuXG4gICAgICAvLyDlpoLmnpzmnInpgInkuK3nmoTor77nqIvkuJTlnKjnvJbovpHnirbmgIHvvIzlhYjkv53lrZjlvZPliY3or77nqItcbiAgICAgIGlmIChzZWxlY3RlZENvdXJzZUlkICYmIHJpZ2h0UGFuZWxUeXBlID09PSAnY291cnNlJykge1xuICAgICAgICBjb25zdCBzZWxlY3RlZENvdXJzZSA9IGNvdXJzZUxpc3QuZmluZChjID0+IGMuaWQgPT09IHNlbGVjdGVkQ291cnNlSWQpO1xuICAgICAgICBpZiAoc2VsZWN0ZWRDb3Vyc2UpIHtcbiAgICAgICAgICAvLyDmm7TmlrDor77nqIvmlbDmja5cbiAgICAgICAgICBjb25zdCB1cGRhdGVkQ291cnNlID0ge1xuICAgICAgICAgICAgLi4uc2VsZWN0ZWRDb3Vyc2UsXG4gICAgICAgICAgICB0aXRsZTogY291cnNlRGV0YWlsLnRpdGxlLFxuICAgICAgICAgICAgZGVzY3JpcHRpb246IGNvdXJzZURldGFpbC5kZXNjcmlwdGlvbixcbiAgICAgICAgICAgIGNvdmVySW1hZ2U6IGNvdXJzZURldGFpbC5jb3ZlckltYWdlLFxuICAgICAgICAgICAgaGFzVmlkZW86IGNvdXJzZURldGFpbC5pc1ZpZGVvRW5hYmxlZCA/IDEgOiAwLFxuICAgICAgICAgICAgaGFzRG9jdW1lbnQ6IGNvdXJzZURldGFpbC5pc0F0dGFjaG1lbnRFbmFibGVkID8gMSA6IDAsXG4gICAgICAgICAgICBjb250ZW50Q29uZmlnOiBjb3Vyc2VEZXRhaWwuY29udGVudENvbmZpZyxcbiAgICAgICAgICAgIHRlYWNoaW5nSW5mbzogY291cnNlRGV0YWlsLnRlYWNoaW5nSW5mbyxcbiAgICAgICAgICAgIGFkZGl0aW9uYWxSZXNvdXJjZXM6IGNvdXJzZURldGFpbC50ZWFjaGluZ01hdGVyaWFscz8ubWFwKG1hdGVyaWFsID0+ICh7XG4gICAgICAgICAgICAgIHRpdGxlOiBtYXRlcmlhbC5uYW1lLFxuICAgICAgICAgICAgICB1cmw6IG1hdGVyaWFsLnVybCxcbiAgICAgICAgICAgICAgZGVzY3JpcHRpb246IG1hdGVyaWFsLm5hbWVcbiAgICAgICAgICAgIH0pKSB8fCBbXVxuICAgICAgICAgIH07XG5cbiAgICAgICAgICAvLyDlhYjmm7TmlrDor77nqIvliJfooajkuK3nmoTmlbDmja5cbiAgICAgICAgICBzZXRDb3Vyc2VMaXN0KHByZXYgPT4gcHJldi5tYXAoY291cnNlID0+XG4gICAgICAgICAgICBjb3Vyc2UuaWQgPT09IHNlbGVjdGVkQ291cnNlSWQgPyB1cGRhdGVkQ291cnNlIDogY291cnNlXG4gICAgICAgICAgKSk7XG5cbiAgICAgICAgICAvLyDnhLblkI7kv53lrZjliLDlkI7nq69cbiAgICAgICAgICBhd2FpdCBzYXZlQ291cnNlKHVwZGF0ZWRDb3Vyc2UpO1xuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIC8vIOS/neWtmOezu+WIl+ivvueoi+S/oeaBr1xuICAgICAgY29uc3QgZGF0YSA9IHtcbiAgICAgICAgdGl0bGU6IGVkaXRpbmdUaXRsZSxcbiAgICAgICAgY291cnNlR29hbHMsXG4gICAgICAgIGNvdXJzZU9iamVjdGl2ZXMsXG4gICAgICAgIGNvdXJzZUxpc3RcbiAgICAgIH07XG5cbiAgICAgIG9uU2F2ZShkYXRhKTtcbiAgICAgIG9uQ2xvc2UoKTtcblxuICAgICAgY29uc29sZS5sb2coJ+KchSDor77nqIvliJfooajkv53lrZjlrozmiJAnKTtcbiAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfinYwg5L+d5a2Y5aSx6LSlOicsIGVycm9yKTtcbiAgICAgIGFsZXJ0KGDkv53lrZjlpLHotKU6ICR7ZXJyb3IubWVzc2FnZSB8fCAn6K+36YeN6K+VJ31gKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0SXNDcmVhdGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIC8vIOWPkeW4g+ezu+WIl+ivvueoi1xuICBjb25zdCBoYW5kbGVQdWJsaXNoID0gYXN5bmMgKCkgPT4ge1xuICAgIC8vIOWmguaenOezu+WIl+W3suWPkeW4g++8jOS4jeaJp+ihjOS7u+S9leaTjeS9nFxuICAgIGlmIChzZXJpZXNTdGF0dXMgPT09IDEpIHtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICB0cnkge1xuICAgICAgc2V0SXNQdWJsaXNoaW5nU2VyaWVzKHRydWUpO1xuXG4gICAgICAvLyDmo4Dmn6XmmK/lkKbmnInor77nqItcbiAgICAgIGlmIChjb3Vyc2VMaXN0Lmxlbmd0aCA9PT0gMCkge1xuICAgICAgICBhbGVydCgn5Y+R5biD5aSx6LSl77ya6K++56iL57O75YiX5Lit6Iez5bCR6ZyA6KaB5YyF5ZCr5LiA5Liq6K++56iLJyk7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cblxuICAgICAgY29uc29sZS5sb2coJ/Cfk6Qg5byA5aeL5Y+R5biD57O75YiX6K++56iL77yM57O75YiXSUQ6Jywgc2VyaWVzSWQpO1xuXG4gICAgICBjb25zdCB7IGRhdGE6IHJlc3BvbnNlIH0gPSBhd2FpdCBjb3Vyc2VBcGkucHVibGlzaENvdXJzZVNlcmllcyhzZXJpZXNJZCk7XG5cbiAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDApIHtcbiAgICAgICAgY29uc29sZS5sb2coJ+KchSDns7vliJfor77nqIvlj5HluIPmiJDlip86JywgcmVzcG9uc2UuZGF0YSk7XG5cbiAgICAgICAgLy8g5p6E5bu65oiQ5Yqf5raI5oGvXG4gICAgICAgIGNvbnN0IHB1Ymxpc2hEYXRhID0gcmVzcG9uc2UuZGF0YTtcbiAgICAgICAgbGV0IHN1Y2Nlc3NNZXNzYWdlID0gYOezu+WIl+ivvueoi1wiJHtwdWJsaXNoRGF0YS50aXRsZX1cIuWPkeW4g+aIkOWKn++8gWA7XG5cbiAgICAgICAgLy8g5aaC5p6c5pyJ5Y+R5biD57uf6K6h5L+h5oGv77yM5re75Yqg5Yiw5raI5oGv5LitXG4gICAgICAgIGlmIChwdWJsaXNoRGF0YS5wdWJsaXNoU3RhdHMpIHtcbiAgICAgICAgICBjb25zdCBzdGF0cyA9IHB1Ymxpc2hEYXRhLnB1Ymxpc2hTdGF0cztcbiAgICAgICAgICBzdWNjZXNzTWVzc2FnZSArPSBgXFxuXFxu5Y+R5biD57uf6K6h77yaXFxu4oCiIOaAu+ivvueoi+aVsO+8miR7cHVibGlzaERhdGEudG90YWxDb3Vyc2VzfVxcbuKAoiDlt7Llj5HluIPor77nqIvvvJoke3B1Ymxpc2hEYXRhLnB1Ymxpc2hlZENvdXJzZXN9XFxu4oCiIOinhumikeivvueoi++8miR7c3RhdHMudmlkZW9Db3Vyc2VDb3VudH3kuKpcXG7igKIg5paH5qGj6K++56iL77yaJHtzdGF0cy5kb2N1bWVudENvdXJzZUNvdW50feS4qlxcbuKAoiDmgLvotYTmupDmlbDvvJoke3N0YXRzLnRvdGFsUmVzb3VyY2VzQ291bnR95LiqYDtcblxuICAgICAgICAgIGlmIChzdGF0cy50b3RhbFZpZGVvRHVyYXRpb24gPiAwKSB7XG4gICAgICAgICAgICBjb25zdCBkdXJhdGlvbk1pbnV0ZXMgPSBNYXRoLnJvdW5kKHN0YXRzLnRvdGFsVmlkZW9EdXJhdGlvbiAvIDYwKTtcbiAgICAgICAgICAgIHN1Y2Nlc3NNZXNzYWdlICs9IGBcXG7igKIg6KeG6aKR5oC75pe26ZW/77yaJHtkdXJhdGlvbk1pbnV0ZXN95YiG6ZKfYDtcbiAgICAgICAgICB9XG4gICAgICAgIH1cblxuICAgICAgICBhbGVydChzdWNjZXNzTWVzc2FnZSk7XG5cbiAgICAgICAgLy8g5pu05paw57O75YiX54q25oCB5Li65bey5Y+R5biDXG4gICAgICAgIHNldFNlcmllc1N0YXR1cygxKTtcblxuICAgICAgICAvLyDliLfmlrDor77nqIvliJfooajku6Xmm7TmlrDnirbmgIFcbiAgICAgICAgYXdhaXQgbG9hZENvdXJzZUxpc3QoKTtcblxuICAgICAgICAvLyDpgJrnn6XniLbnu4Tku7bliLfmlrDmlbDmja5cbiAgICAgICAgb25TYXZlKHtcbiAgICAgICAgICB0eXBlOiAncHVibGlzaF9zZXJpZXMnLFxuICAgICAgICAgIHNlcmllc0lkOiBzZXJpZXNJZCxcbiAgICAgICAgICBtZXNzYWdlOiAn57O75YiX6K++56iL5Y+R5biD5oiQ5YqfJ1xuICAgICAgICB9KTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDlj5HluIPns7vliJfor77nqIvlpLHotKU6JywgcmVzcG9uc2UubWVzc2FnZSk7XG4gICAgICAgIGFsZXJ0KHJlc3BvbnNlLm1lc3NhZ2UgfHwgJ+WPkeW4g+ezu+WIl+ivvueoi+Wksei0pScpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDlj5HluIPns7vliJfor77nqIvlh7rplJk6JywgZXJyb3IpO1xuXG4gICAgICAvLyDlpITnkIblhbfkvZPnmoTplJnor6/kv6Hmga9cbiAgICAgIGxldCBlcnJvck1lc3NhZ2UgPSAn5Y+R5biD57O75YiX6K++56iL5aSx6LSlJztcbiAgICAgIGlmIChlcnJvci5yZXNwb25zZT8uZGF0YT8ubWVzc2FnZSkge1xuICAgICAgICBlcnJvck1lc3NhZ2UgPSBlcnJvci5yZXNwb25zZS5kYXRhLm1lc3NhZ2U7XG4gICAgICB9IGVsc2UgaWYgKGVycm9yLm1lc3NhZ2UpIHtcbiAgICAgICAgZXJyb3JNZXNzYWdlID0gZXJyb3IubWVzc2FnZTtcbiAgICAgIH1cblxuICAgICAgYWxlcnQoZXJyb3JNZXNzYWdlKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0SXNQdWJsaXNoaW5nU2VyaWVzKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgLy8g6YCA5Ye657yW6L6R5qih5byPIC0g5L+d5a2Y5pWw5o2u5bm25YWz6ZetXG4gIGNvbnN0IGhhbmRsZUV4aXRFZGl0ID0gKCkgPT4ge1xuICAgIGhhbmRsZVNhdmUoKTtcbiAgfTtcblxuICAvLyDmmL7npLrorr7nva7pnaLmnb9cbiAgY29uc3Qgc2hvd1NldHRpbmdzUGFuZWwgPSAoKSA9PiB7XG4gICAgc2V0UmlnaHRQYW5lbFR5cGUoJ3NldHRpbmdzJyk7XG4gICAgc2V0U2VsZWN0ZWRDb3Vyc2VJZChudWxsKTtcbiAgfTtcblxuICAvLyDlpITnkIbmqKHmnb/pgInmi6lcbiAgY29uc3QgaGFuZGxlVGVtcGxhdGVTZWxlY3QgPSAodGVtcGxhdGU6IGFueSkgPT4ge1xuICAgIHNldENvdXJzZURldGFpbChwcmV2ID0+ICh7XG4gICAgICAuLi5wcmV2LFxuICAgICAgc2VsZWN0ZWRUZW1wbGF0ZTogdGVtcGxhdGUudGVtcGxhdGVOYW1lXG4gICAgfSkpO1xuICAgIHNldElzVGVtcGxhdGVQaWNrZXJPcGVuKGZhbHNlKTtcbiAgfTtcblxuICAvLyDmiZPlvIDmqKHmnb/pgInmi6nlvLnnqpdcbiAgY29uc3QgaGFuZGxlT3BlblRlbXBsYXRlUGlja2VyID0gKCkgPT4ge1xuICAgIHNldElzVGVtcGxhdGVQaWNrZXJPcGVuKHRydWUpO1xuICB9O1xuXG4gIC8vIOaYvuekuuivvueoi+e8lui+kemdouadv1xuICBjb25zdCBzaG93Q291cnNlUGFuZWwgPSBhc3luYyAoY291cnNlSWQ6IG51bWJlcikgPT4ge1xuICAgIHNldFJpZ2h0UGFuZWxUeXBlKCdjb3Vyc2UnKTtcbiAgICBzZXRTZWxlY3RlZENvdXJzZUlkKGNvdXJzZUlkKTtcblxuICAgIC8vIOWmguaenOS9nOWTgeaVsOaNrui/mOayoeacieWKoOi9ve+8jOmHjeaWsOWKoOi9vVxuICAgIGlmICh3b3Jrcy5sZW5ndGggPT09IDAgJiYgIWxvYWRpbmdXb3Jrcykge1xuICAgICAgbG9hZFVzZXJXb3Jrcyh0cnVlKTtcbiAgICB9XG5cbiAgICAvLyDojrflj5bpgInkuK3nmoTor77nqIvlubbmm7TmlrBjb3Vyc2VEZXRhaWznirbmgIFcbiAgICBjb25zdCBzZWxlY3RlZENvdXJzZSA9IGNvdXJzZUxpc3QuZmluZChjb3Vyc2UgPT4gY291cnNlLmlkID09PSBjb3Vyc2VJZCk7XG4gICAgY29uc29sZS5sb2coJ/Cfjq8g5om+5Yiw55qE6K++56iLOicsIHNlbGVjdGVkQ291cnNlKTtcblxuICAgIGlmIChzZWxlY3RlZENvdXJzZSkge1xuICAgICAgdHJ5IHtcbiAgICAgICAgY29uc29sZS5sb2coJ/CflIQg6I635Y+W6K++56iL6K+m5oOF77yMc2VyaWVzSWQ6Jywgc2VyaWVzSWQsICdjb3Vyc2VJZDonLCBjb3Vyc2VJZCk7XG5cbiAgICAgICAgLy8g6I635Y+W55yf5a6e55qE6K++56iL6K+m5oOF5pWw5o2uXG4gICAgICAgIGNvbnN0IHsgZGF0YTogcmVzIH0gPSBhd2FpdCBmZXRjaENvdXJzZURldGFpbChzZXJpZXNJZCwgY291cnNlSWQpO1xuXG4gICAgICAgIGlmIChyZXMuY29kZSA9PT0gMjAwICYmIHJlcy5kYXRhKSB7XG4gICAgICAgICAgY29uc3QgY291cnNlRGV0YWlsRGF0YSA9IHJlcy5kYXRhO1xuICAgICAgICAgIGNvbnNvbGUubG9nKCfinIUg6I635Y+W5Yiw6K++56iL6K+m5oOFOicsIGNvdXJzZURldGFpbERhdGEpO1xuXG4gICAgICAgICAgLy8g5bCG55yf5a6e55qEIGFkZGl0aW9uYWxSZXNvdXJjZXMg5pig5bCE5YiwIHRlYWNoaW5nTWF0ZXJpYWxzXG4gICAgICAgICAgY29uc3QgdGVhY2hpbmdNYXRlcmlhbHMgPSBjb3Vyc2VEZXRhaWxEYXRhLmFkZGl0aW9uYWxSZXNvdXJjZXM/Lm1hcCgocmVzb3VyY2U6IGFueSkgPT4gKHtcbiAgICAgICAgICAgIHR5cGU6ICdhcHBsaWNhdGlvbi9vY3RldC1zdHJlYW0nLCAvLyDpu5jorqTnsbvlnotcbiAgICAgICAgICAgIG5hbWU6IHJlc291cmNlLnRpdGxlIHx8IHJlc291cmNlLm5hbWUgfHwgJ+mZhOS7ticsXG4gICAgICAgICAgICB1cmw6IHJlc291cmNlLnVybFxuICAgICAgICAgIH0pKSB8fCBbXTtcblxuICAgICAgICAgIC8vIOWwhkFQSei/lOWbnueahHRlYWNoaW5nSW5mb+aVsOe7hOagvOW8j+i9rOaNouS4uuaWsOeahOagvOW8j1xuICAgICAgICAgIGNvbnN0IG1hcHBlZFRlYWNoaW5nSW5mbyA9IGNvdXJzZURldGFpbERhdGEudGVhY2hpbmdJbmZvPy5tYXAoKGluZm86IGFueSkgPT4gKHtcbiAgICAgICAgICAgIHRpdGxlOiBpbmZvLnRpdGxlIHx8ICcnLFxuICAgICAgICAgICAgY29udGVudDogQXJyYXkuaXNBcnJheShpbmZvLmNvbnRlbnQpID8gaW5mby5jb250ZW50LmpvaW4oJ1xcbicpIDogKGluZm8uY29udGVudCB8fCAnJylcbiAgICAgICAgICB9KSkgfHwgW107XG5cbiAgICAgICAgICBjb25zb2xlLmxvZygn8J+TjiDmmKDlsITnmoTmlZnlrabpmYTku7Y6JywgdGVhY2hpbmdNYXRlcmlhbHMpO1xuICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5OaIOaYoOWwhOeahOaVmeWtpuS/oeaBrzonLCBtYXBwZWRUZWFjaGluZ0luZm8pO1xuXG4gICAgICAgICAgc2V0Q291cnNlRGV0YWlsKHByZXYgPT4gKHtcbiAgICAgICAgICAgIC4uLnByZXYsXG4gICAgICAgICAgICB0aXRsZTogY291cnNlRGV0YWlsRGF0YS50aXRsZSxcbiAgICAgICAgICAgIGRlc2NyaXB0aW9uOiBjb3Vyc2VEZXRhaWxEYXRhLmRlc2NyaXB0aW9uLFxuICAgICAgICAgICAgY292ZXJJbWFnZTogY291cnNlRGV0YWlsRGF0YS5jb3ZlckltYWdlIHx8ICcnLFxuICAgICAgICAgICAgaXNWaWRlb0VuYWJsZWQ6IGNvdXJzZURldGFpbERhdGEuaGFzVmlkZW8gPT09IDEsXG4gICAgICAgICAgICBpc0F0dGFjaG1lbnRFbmFibGVkOiBjb3Vyc2VEZXRhaWxEYXRhLmhhc0RvY3VtZW50ID09PSAxLFxuICAgICAgICAgICAgY29udGVudENvbmZpZzogY291cnNlRGV0YWlsRGF0YS5jb250ZW50Q29uZmlnIHx8IHt9LFxuICAgICAgICAgICAgdGVhY2hpbmdJbmZvOiBtYXBwZWRUZWFjaGluZ0luZm8sIC8vIOS9v+eUqOaYoOWwhOWQjueahOaVmeWtpuS/oeaBr1xuICAgICAgICAgICAgdGVhY2hpbmdNYXRlcmlhbHM6IHRlYWNoaW5nTWF0ZXJpYWxzLCAvLyDkvb/nlKjnnJ/lrp7nmoTpmYTku7bmlbDmja5cbiAgICAgICAgICAgIHZpZGVvVXJsOiBjb3Vyc2VEZXRhaWxEYXRhLmNvbnRlbnRDb25maWc/LnZpZGVvPy51cmwgfHwgJycsXG4gICAgICAgICAgICB2aWRlb05hbWU6IGNvdXJzZURldGFpbERhdGEuY29udGVudENvbmZpZz8udmlkZW8/Lm5hbWUgfHwgJycsXG4gICAgICAgICAgICBhdHRhY2htZW50VXJsOiBjb3Vyc2VEZXRhaWxEYXRhLmNvbnRlbnRDb25maWc/LmRvY3VtZW50Py51cmwgfHwgJycsXG4gICAgICAgICAgICBhdHRhY2htZW50TmFtZTogY291cnNlRGV0YWlsRGF0YS5jb250ZW50Q29uZmlnPy5kb2N1bWVudD8ubmFtZSB8fCAnJ1xuICAgICAgICAgIH0pKTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICBjb25zb2xlLmVycm9yKCfinYwg6I635Y+W6K++56iL6K+m5oOF5aSx6LSlOicsIHJlcy5tZXNzYWdlKTtcbiAgICAgICAgICAvLyDkvb/nlKjln7rnoYDmlbDmja7kvZzkuLrlkI7lpIdcbiAgICAgICAgICBzZXRDb3Vyc2VEZXRhaWwocHJldiA9PiAoe1xuICAgICAgICAgICAgLi4ucHJldixcbiAgICAgICAgICAgIHRpdGxlOiBzZWxlY3RlZENvdXJzZS50aXRsZSxcbiAgICAgICAgICAgIGRlc2NyaXB0aW9uOiBzZWxlY3RlZENvdXJzZS5kZXNjcmlwdGlvbixcbiAgICAgICAgICAgIGNvdmVySW1hZ2U6IHNlbGVjdGVkQ291cnNlLmNvdmVySW1hZ2UgfHwgJycsXG4gICAgICAgICAgICBpc1ZpZGVvRW5hYmxlZDogc2VsZWN0ZWRDb3Vyc2UuaGFzVmlkZW8gPT09IDEsXG4gICAgICAgICAgICBpc0F0dGFjaG1lbnRFbmFibGVkOiBzZWxlY3RlZENvdXJzZS5oYXNEb2N1bWVudCA9PT0gMSxcbiAgICAgICAgICAgIHRlYWNoaW5nTWF0ZXJpYWxzOiBbXSAvLyDmuIXnqbrpmYTku7bliJfooahcbiAgICAgICAgICB9KSk7XG4gICAgICAgIH1cbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDojrflj5bor77nqIvor6bmg4XlvILluLg6JywgZXJyb3IpO1xuICAgICAgICAvLyDkvb/nlKjln7rnoYDmlbDmja7kvZzkuLrlkI7lpIdcbiAgICAgICAgc2V0Q291cnNlRGV0YWlsKHByZXYgPT4gKHtcbiAgICAgICAgICAuLi5wcmV2LFxuICAgICAgICAgIHRpdGxlOiBzZWxlY3RlZENvdXJzZS50aXRsZSxcbiAgICAgICAgICBkZXNjcmlwdGlvbjogc2VsZWN0ZWRDb3Vyc2UuZGVzY3JpcHRpb24sXG4gICAgICAgICAgY292ZXJJbWFnZTogc2VsZWN0ZWRDb3Vyc2UuY292ZXJJbWFnZSB8fCAnJyxcbiAgICAgICAgICBpc1ZpZGVvRW5hYmxlZDogc2VsZWN0ZWRDb3Vyc2UuaGFzVmlkZW8gPT09IDEsXG4gICAgICAgICAgaXNBdHRhY2htZW50RW5hYmxlZDogc2VsZWN0ZWRDb3Vyc2UuaGFzRG9jdW1lbnQgPT09IDEsXG4gICAgICAgICAgdGVhY2hpbmdNYXRlcmlhbHM6IFtdIC8vIOa4heepuumZhOS7tuWIl+ihqFxuICAgICAgICB9KSk7XG4gICAgICB9XG4gICAgfVxuICB9O1xuXG4gIC8vIOiOt+WPlumAieS4reeahOivvueoi1xuICBjb25zdCBnZXRTZWxlY3RlZENvdXJzZSA9ICgpID0+IHtcbiAgICByZXR1cm4gY291cnNlTGlzdC5maW5kKGNvdXJzZSA9PiBjb3Vyc2UuaWQgPT09IHNlbGVjdGVkQ291cnNlSWQpO1xuICB9O1xuXG4gIGlmICghaXNWaXNpYmxlKSByZXR1cm4gbnVsbDtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiY291cnNlLWxpc3QtbW9kYWwtb3ZlcmxheVwiPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb3Vyc2UtbGlzdC1tb2RhbFwiPlxuICAgICAgICB7Lyog5aS06YOoICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvdXJzZS1saXN0LWhlYWRlclwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY291cnNlLWxpc3QtdGl0bGUtc2VjdGlvblwiPlxuICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cImNvdXJzZS1saXN0LXRpdGxlXCI+6K++56iL5YiX6KGoPC9oMj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY291cnNlLWxpc3QtYWN0aW9uc1wiPlxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgb25DbGljaz17c2hvd1NldHRpbmdzUGFuZWx9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgY291cnNlLWxpc3Qtc2V0dGluZ3MtYnRuICR7cmlnaHRQYW5lbFR5cGUgPT09ICdzZXR0aW5ncycgPyAnYWN0aXZlJyA6ICcnfWB9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8U2V0dGluZ3MgY2xhc3NOYW1lPVwidy00IGgtNFwiIC8+XG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICA8YnV0dG9uIG9uQ2xpY2s9e2FkZE5ld0NvdXJzZX0gY2xhc3NOYW1lPVwiY291cnNlLWxpc3QtYWRkLWJ0blwiPlxuICAgICAgICAgICAgICAgIDxQbHVzIGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPlxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDxidXR0b24gb25DbGljaz17b25DbG9zZX0gY2xhc3NOYW1lPVwiY291cnNlLWxpc3QtY2xvc2UtYnRuXCI+XG4gICAgICAgICAgICA8WCBjbGFzc05hbWU9XCJ3LTYgaC02XCIgLz5cbiAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIOS4u+imgeWGheWuuSAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb3Vyc2UtbGlzdC1jb250ZW50XCI+XG4gICAgICAgICAgey8qIOW3puS+p+ivvueoi+WIl+ihqCAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvdXJzZS1saXN0LXNpZGViYXJcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY291cnNlLWxpc3QtaXRlbXNcIj5cbiAgICAgICAgICAgICAge2xvYWRpbmcgPyAoXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb3Vyc2UtbGlzdC1sb2FkaW5nXCI+XG4gICAgICAgICAgICAgICAgICA8cD7liqDovb3kuK0uLi48L3A+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICkgOiBjb3Vyc2VMaXN0Lmxlbmd0aCA9PT0gMCA/IChcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvdXJzZS1saXN0LWVtcHR5XCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvdXJzZS1saXN0LWVtcHR5LWljb25cIj5cbiAgICAgICAgICAgICAgICAgICAgPEJvb2tPcGVuIGNsYXNzTmFtZT1cInctMTIgaC0xMiB0ZXh0LWdyYXktMzAwXCIgLz5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cImNvdXJzZS1saXN0LWVtcHR5LXRpdGxlXCI+5pqC5peg6K++5pe2PC9oMz5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cImNvdXJzZS1saXN0LWVtcHR5LWRlc2NyaXB0aW9uXCI+XG4gICAgICAgICAgICAgICAgICAgIOeCueWHu+WPs+S4iuinkueahCArIOaMiemSrua3u+WKoOesrOS4gOS4quivvuaXtlxuICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXthZGROZXdDb3Vyc2V9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImNvdXJzZS1saXN0LWVtcHR5LWJ0blwiXG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIDxQbHVzIGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPlxuICAgICAgICAgICAgICAgICAgICDmt7vliqDor77ml7ZcbiAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgIGNvdXJzZUxpc3QubWFwKChjb3Vyc2UpID0+IChcbiAgICAgICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICAgICAga2V5PXtjb3Vyc2UuaWR9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGNvdXJzZS1saXN0LWl0ZW0gJHtzZWxlY3RlZENvdXJzZUlkID09PSBjb3Vyc2UuaWQgPyAnYWN0aXZlJyA6ICcnfWB9XG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNob3dDb3Vyc2VQYW5lbChjb3Vyc2UuaWQpfVxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvdXJzZS1saXN0LWl0ZW0tY29udGVudFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImNvdXJzZS1saXN0LWl0ZW0tdGV4dFwiPntjb3Vyc2UudGl0bGV9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17YGNvdXJzZS1zdGF0dXMtYmFkZ2UgJHtjb3Vyc2Uuc3RhdHVzID09PSAxID8gJ3B1Ymxpc2hlZCcgOiAnZHJhZnQnfWB9PlxuICAgICAgICAgICAgICAgICAgICAgICAge2NvdXJzZS5zdGF0dXMgPT09IDEgPyAn5bey5Y+R5biDJyA6ICfmnKrlj5HluIMnfVxuICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgZS5zdG9wUHJvcGFnYXRpb24oKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIHNob3dEZWxldGVDb25maXJtKGNvdXJzZS5pZCk7XG4gICAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJjb3Vyc2UtbGlzdC1pdGVtLWRlbGV0ZVwiXG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICA8WCBjbGFzc05hbWU9XCJ3LTMgaC0zXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICApKVxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7Lyog5Y+z5L6n57yW6L6R5Yy65Z+fICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY291cnNlLWxpc3QtZWRpdC1hcmVhXCI+XG4gICAgICAgICAgICB7cmlnaHRQYW5lbFR5cGUgPT09ICdub25lJyAmJiAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY291cnNlLWVkaXQtZW1wdHlcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvdXJzZS1lZGl0LWVtcHR5LWljb25cIj5cbiAgICAgICAgICAgICAgICAgIDxGaWxlVGV4dCBjbGFzc05hbWU9XCJ3LTE2IGgtMTYgdGV4dC1ncmF5LTMwMFwiIC8+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cImNvdXJzZS1lZGl0LWVtcHR5LXRpdGxlXCI+5peg6K++56iL6K+m5oOFPC9oMz5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJjb3Vyc2UtZWRpdC1lbXB0eS1kZXNjcmlwdGlvblwiPlxuICAgICAgICAgICAgICAgICAg54K55Ye75bem5L6n6K++56iL5oiW6K6+572u5oyJ6ZKu5p+l55yL6K+m5oOFXG4gICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgIHtyaWdodFBhbmVsVHlwZSA9PT0gJ3NldHRpbmdzJyAmJiAoXG4gICAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgICAgey8qIOezu+WIl+ivvueoi+WwgemdoiAqL31cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvdXJzZS1zZXJpZXMtY292ZXJcIj5cbiAgICAgICAgICAgICAgICAgIHtzZXJpZXNDb3ZlckltYWdlID8gKFxuICAgICAgICAgICAgICAgICAgICA8aW1nXG4gICAgICAgICAgICAgICAgICAgICAgc3JjPXtzZXJpZXNDb3ZlckltYWdlfVxuICAgICAgICAgICAgICAgICAgICAgIGFsdD1cIuezu+WIl+ivvueoi+WwgemdolwiXG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiY291cnNlLXNlcmllcy1jb3Zlci1pbWFnZVwiXG4gICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvdXJzZS1zZXJpZXMtY292ZXItcGxhY2Vob2xkZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj7ns7vliJfor77nqIvlsIHpnaI8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgIHsvKiDns7vliJforr7nva7ooajljZUgKi99XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb3Vyc2UtZWRpdC1mb3JtXCI+XG4gICAgICAgICAgICAgICAgICB7Lyog57O75YiX6K++56iL5qCH6aKYICovfVxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb3Vyc2UtZWRpdC1maWVsZFwiPlxuICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiY291cnNlLWVkaXQtbGFiZWxcIj7ns7vliJfor77nqIvmoIfpopg8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2VkaXRpbmdUaXRsZX1cbiAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldEVkaXRpbmdUaXRsZShlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiY291cnNlLWVkaXQtaW5wdXRcIlxuICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwi6K+36L6T5YWl57O75YiX6K++56iL5qCH6aKYXCJcbiAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICB7Lyog6K++56iL5qCH562+ICovfVxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb3Vyc2UtZWRpdC1maWVsZFwiPlxuICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiY291cnNlLWVkaXQtbGFiZWxcIj7or77nqIvmoIfnrb48L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICA8U2VsZWN0XG4gICAgICAgICAgICAgICAgICAgICAgbW9kZT1cIm11bHRpcGxlXCJcbiAgICAgICAgICAgICAgICAgICAgICBzdHlsZT17eyB3aWR0aDogJzEwMCUnIH19XG4gICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLor7fpgInmi6nor77nqIvmoIfnrb5cIlxuICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtzZWxlY3RlZFRhZ3N9XG4gICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9e3NldFNlbGVjdGVkVGFnc31cbiAgICAgICAgICAgICAgICAgICAgICBsb2FkaW5nPXt0YWdzTG9hZGluZ31cbiAgICAgICAgICAgICAgICAgICAgICBvcHRpb25zPXtjb3Vyc2VUYWdzLm1hcCh0YWcgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ/Cfj7fvuI8g5riy5p+T5qCH562+6YCJ6aG5OicsIHRhZyk7XG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICBsYWJlbDogKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIHN0eWxlPXt7IGNvbG9yOiB0YWcuY29sb3IgfX0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7dGFnLm5hbWV9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICApLFxuICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZTogdGFnLmlkXG4gICAgICAgICAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgICAgICAgICAgIH0pfVxuICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICB7Lyog6LCD6K+V5L+h5oGvICovfVxuICAgICAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGZvbnRTaXplOiAnMTJweCcsIGNvbG9yOiAnIzY2NicsIG1hcmdpblRvcDogJzRweCcgfX0+XG4gICAgICAgICAgICAgICAgICAgICAg6LCD6K+VOiDlvZPliY3moIfnrb7mlbDph48ge2NvdXJzZVRhZ3MubGVuZ3RofSwg5Yqg6L2954q25oCBOiB7dGFnc0xvYWRpbmcgPyAn5pivJyA6ICflkKYnfVxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICB7Lyog6K++56iL6aG555uu5oiQ5ZGYICovfVxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb3Vyc2UtZWRpdC1maWVsZFwiPlxuICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiY291cnNlLWVkaXQtbGFiZWxcIj7or77nqIvpobnnm67miJDlkZg8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3Byb2plY3RNZW1iZXJzfVxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0UHJvamVjdE1lbWJlcnMoZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImNvdXJzZS1lZGl0LWlucHV0XCJcbiAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIuivt+i+k+WFpemhueebruaIkOWRmO+8jOWmgu+8muW8oOiAgeW4iOOAgeadjuWKqeaVmeOAgeeOi+WQjOWtplwiXG4gICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC8+XG4gICAgICAgICAgICApfVxuXG4gICAgICAgICAgICB7cmlnaHRQYW5lbFR5cGUgPT09ICdjb3Vyc2UnICYmIGdldFNlbGVjdGVkQ291cnNlKCkgJiYgKFxuICAgICAgICAgICAgICA8PlxuICAgICAgICAgICAgICAgIHsvKiDor77nqIvor6bnu4bnvJbovpHnlYzpnaIgKi99XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb3Vyc2UtZGV0YWlsLWVkaXRcIj5cbiAgICAgICAgICAgICAgICAgIHsvKiDpobbpg6jljLrln5/vvJror77nqIvlsIHpnaLlkozln7rmnKzkv6Hmga8gKi99XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvdXJzZS1kZXRhaWwtdG9wXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY291cnNlLWRldGFpbC1jb3ZlclwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImNvdXJzZS1jb3Zlci11cGxvYWQtYXJlYVwiXG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBkb2N1bWVudC5nZXRFbGVtZW50QnlJZCgnY292ZXItdXBsb2FkLWlucHV0Jyk/LmNsaWNrKCl9XG4gICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAge2NvdXJzZURldGFpbC5jb3ZlckltYWdlIHx8IGdldFNlbGVjdGVkQ291cnNlKCk/LmNvdmVySW1hZ2UgPyAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxpbWdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzcmM9e2NvdXJzZURldGFpbC5jb3ZlckltYWdlIHx8IGdldFNlbGVjdGVkQ291cnNlKCk/LmNvdmVySW1hZ2V9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYWx0PVwi6K++56iL5bCB6Z2iXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJjb3Vyc2UtY292ZXItaW1hZ2VcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb3Vyc2UtY292ZXItcGxhY2Vob2xkZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj7ngrnlh7vkuIrkvKDor77nqIvlsIHpnaI8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgIGlkPVwiY292ZXItdXBsb2FkLWlucHV0XCJcbiAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJmaWxlXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIGFjY2VwdD1cImltYWdlL2pwZWcsaW1hZ2UvanBnLGltYWdlL3BuZyxpbWFnZS9naWZcIlxuICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZUNvdmVyVXBsb2FkfVxuICAgICAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3sgZGlzcGxheTogJ25vbmUnIH19XG4gICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICB7Lyog5LiA6ZSu5LiK6K++5oyJ6ZKuICovfVxuICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm9uZS1jbGljay1jbGFzcy1idG5cIlxuICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAvLyBUT0RPOiDlrp7njrDkuIDplK7kuIror77lip/og71cbiAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ+S4gOmUruS4iuivvuaMiemSruiiq+eCueWHuycpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICBub3RpZmljYXRpb24uaW5mbygn5LiA6ZSu5LiK6K++5Yqf6IO95byA5Y+R5LitLi4uJyk7XG4gICAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgIOS4gOmUruS4iuivvlxuICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb3Vyc2UtZGV0YWlsLWJhc2ljXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb3Vyc2UtZGV0YWlsLWZpZWxkXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8bGFiZWw+6K++56iL5qCH6aKYPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtjb3Vyc2VEZXRhaWwudGl0bGUgfHwgZ2V0U2VsZWN0ZWRDb3Vyc2UoKT8udGl0bGUgfHwgJyd9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNldENvdXJzZURldGFpbChwcmV2ID0+ICh7IC4uLnByZXYsIHRpdGxlOiBlLnRhcmdldC52YWx1ZSB9KSk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdXBkYXRlQ291cnNlVGl0bGUoc2VsZWN0ZWRDb3Vyc2VJZCEsIGUudGFyZ2V0LnZhbHVlKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLor7fovpPlhaXor77nqIvmoIfpophcIlxuICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvdXJzZS1kZXRhaWwtZmllbGRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbD7or77nqIvku4vnu408L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHRleHRhcmVhXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtjb3Vyc2VEZXRhaWwuZGVzY3JpcHRpb24gfHwgZ2V0U2VsZWN0ZWRDb3Vyc2UoKT8uZGVzY3JpcHRpb24gfHwgJyd9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0Q291cnNlRGV0YWlsKHByZXYgPT4gKHsgLi4ucHJldiwgZGVzY3JpcHRpb246IGUudGFyZ2V0LnZhbHVlIH0pKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLor7fovpPlhaXor77nqIvku4vnu41cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICByb3dzPXszfVxuICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgey8qIOivvueoi+i1hOa6kOWMuuWfnyAqL31cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY291cnNlLWRldGFpbC1zZWN0aW9uXCI+XG4gICAgICAgICAgICAgICAgICAgIDxoMz7or77nqIvotYTmupA8L2gzPlxuXG4gICAgICAgICAgICAgICAgICAgIHsvKiDor77nqIvop4bpopEgKi99XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY291cnNlLXJlc291cmNlLWl0ZW1cIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlc291cmNlLWhlYWRlci1yaWdodFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+6K++56iL6KeG6aKRPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cInN3aXRjaFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwiY2hlY2tib3hcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNoZWNrZWQ9e2NvdXJzZURldGFpbC5pc1ZpZGVvRW5hYmxlZH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldENvdXJzZURldGFpbChwcmV2ID0+ICh7IC4uLnByZXYsIGlzVmlkZW9FbmFibGVkOiBlLnRhcmdldC5jaGVja2VkIH0pKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwic2xpZGVyXCI+PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICB7Y291cnNlRGV0YWlsLmlzVmlkZW9FbmFibGVkICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidmlkZW8tY29udGVudC1hcmVhXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHsvKiDmmL7npLrnnJ/lrp7op4bpopHkv6Hmga8gKi99XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtjb3Vyc2VEZXRhaWwuY29udGVudENvbmZpZz8udmlkZW8/LnVybCA/IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInZpZGVvLWluZm8tc2VjdGlvblwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ2aWRlby1wcmV2aWV3XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDx2aWRlb1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInZpZGVvLXRodW1ibmFpbFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29udHJvbHNcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwb3N0ZXI9e2NvdXJzZURldGFpbC5jb3ZlckltYWdlfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNvdXJjZSBzcmM9e2NvdXJzZURldGFpbC5jb250ZW50Q29uZmlnLnZpZGVvLnVybH0gdHlwZT1cInZpZGVvL21wNFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg5oKo55qE5rWP6KeI5Zmo5LiN5pSv5oyB6KeG6aKR5pKt5pS+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvdmlkZW8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidmlkZW8tbmFtZS1jZW50ZXJlZFwiPntjb3Vyc2VEZXRhaWwuY29udGVudENvbmZpZy52aWRlby5uYW1lfTwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9XCJ1cGxvYWQtYnRuLWhvcml6b250YWxcIiBvbkNsaWNrPXt0cmlnZ2VyVmlkZW9VcGxvYWR9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj7ph43mlrDkuIrkvKA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInZpZGVvLXVwbG9hZC1zZWN0aW9uXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInZpZGVvLXBsYWNlaG9sZGVyLWNlbnRlcmVkXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicGxheS1pY29uXCI+4pa2PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b24gY2xhc3NOYW1lPVwidXBsb2FkLWJ0bi1ob3Jpem9udGFsXCIgb25DbGljaz17dHJpZ2dlclZpZGVvVXBsb2FkfT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+5LiK5Lyg6KeG6aKRPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICB7Lyog6K++56iL6ZmE5Lu2ICovfVxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvdXJzZS1yZXNvdXJjZS1pdGVtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZXNvdXJjZS1oZWFkZXItcmlnaHRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPuivvueoi+mZhOS7tjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJzd2l0Y2hcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImNoZWNrYm94XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjaGVja2VkPXtjb3Vyc2VEZXRhaWwuaXNBdHRhY2htZW50RW5hYmxlZH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldENvdXJzZURldGFpbChwcmV2ID0+ICh7IC4uLnByZXYsIGlzQXR0YWNobWVudEVuYWJsZWQ6IGUudGFyZ2V0LmNoZWNrZWQgfSkpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJzbGlkZXJcIj48L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIHtjb3Vyc2VEZXRhaWwuaXNBdHRhY2htZW50RW5hYmxlZCAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImF0dGFjaG1lbnQtY29udGVudC1hcmVhXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHsvKiDmmL7npLrnnJ/lrp7pmYTku7bkv6Hmga8gKi99XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtjb3Vyc2VEZXRhaWwuY29udGVudENvbmZpZz8uZG9jdW1lbnQ/LnVybCA/IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImF0dGFjaG1lbnQtaW5mby1zZWN0aW9uXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImF0dGFjaG1lbnQtcHJldmlld1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImRvY3VtZW50LWljb25cIj7wn5OEPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYXR0YWNobWVudC1kZXRhaWxzXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhdHRhY2htZW50LW5hbWVcIj57Y291cnNlRGV0YWlsLmNvbnRlbnRDb25maWcuZG9jdW1lbnQubmFtZX08L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b24gY2xhc3NOYW1lPVwidXBsb2FkLWJ0bi1ob3Jpem9udGFsXCIgb25DbGljaz17dHJpZ2dlckF0dGFjaG1lbnRVcGxvYWR9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj7ph43mlrDkuIrkvKA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImF0dGFjaG1lbnQtdXBsb2FkLXNlY3Rpb25cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b24gY2xhc3NOYW1lPVwidXBsb2FkLWJ0bi1ob3Jpem9udGFsXCIgb25DbGljaz17dHJpZ2dlckF0dGFjaG1lbnRVcGxvYWR9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj7kuIrkvKDpmYTku7Y8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgIHsvKiDmlZnlrabpmYTku7YgKi99XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY291cnNlLXJlc291cmNlLWl0ZW1cIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlc291cmNlLWhlYWRlci1zaW1wbGVcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPuaVmeWtpumZhOS7tjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRlYWNoaW5nLW1hdGVyaWFsc1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9XCJhZGQtbWF0ZXJpYWwtYnRuXCIgb25DbGljaz17dHJpZ2dlclRlYWNoaW5nTWF0ZXJpYWxVcGxvYWR9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj4rPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj7kuIrkvKA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtjb3Vyc2VEZXRhaWwudGVhY2hpbmdNYXRlcmlhbHMgJiYgY291cnNlRGV0YWlsLnRlYWNoaW5nTWF0ZXJpYWxzLmxlbmd0aCA+IDAgPyAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNvdXJzZURldGFpbC50ZWFjaGluZ01hdGVyaWFscy5tYXAoKG1hdGVyaWFsLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYga2V5PXtpbmRleH0gY2xhc3NOYW1lPVwibWF0ZXJpYWwtaXRlbVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwibWF0ZXJpYWwtbmFtZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAobWF0ZXJpYWwudXJsKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB3aW5kb3cub3BlbihtYXRlcmlhbC51cmwsICdfYmxhbmsnKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY3Vyc29yOiBtYXRlcmlhbC51cmwgPyAncG9pbnRlcicgOiAnZGVmYXVsdCcsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I6IG1hdGVyaWFsLnVybCA/ICcjMTg5MGZmJyA6ICdpbmhlcml0JyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0ZXh0RGVjb3JhdGlvbjogbWF0ZXJpYWwudXJsID8gJ3VuZGVybGluZScgOiAnbm9uZSdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU9e21hdGVyaWFsLnVybCA/ICfngrnlh7vkuIvovb3pmYTku7YnIDogbWF0ZXJpYWwubmFtZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg8J+TjiB7bWF0ZXJpYWwubmFtZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicmVtb3ZlLW1hdGVyaWFsLWJ0blwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHJlbW92ZVRlYWNoaW5nTWF0ZXJpYWwoaW5kZXgpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aXRsZT1cIuWIoOmZpOmZhOS7tlwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIMOXXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgKSlcbiAgICAgICAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZW1wdHktbWF0ZXJpYWxzLWhpbnRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBzdHlsZT17eyBjb2xvcjogJyM5OTknLCBmb250U2l6ZTogJzE0cHgnIH19PuaaguaXoOaVmeWtpumZhOS7tjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICB7Lyog6K++56iL5YaF5a655Yy65Z+fICovfVxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb3Vyc2UtZGV0YWlsLXNlY3Rpb25cIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzZWN0aW9uLWhlYWRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxoMz7or77nqIvlhoXlrrk8L2gzPlxuICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImFkZC1jb250ZW50LXNlY3Rpb24tYnRuXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2FkZFRlYWNoaW5nSW5mb0l0ZW19XG4gICAgICAgICAgICAgICAgICAgICAgICB0aXRsZT1cIua3u+WKoOivvueoi+WGheWuuVwiXG4gICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgKyDmt7vliqDor77nqIvlhoXlrrlcbiAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY291cnNlLWNvbnRlbnQtYXJlYVwiPlxuICAgICAgICAgICAgICAgICAgICAgIHtjb3Vyc2VEZXRhaWwudGVhY2hpbmdJbmZvICYmIGNvdXJzZURldGFpbC50ZWFjaGluZ0luZm8ubGVuZ3RoID4gMCA/IChcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvdXJzZURldGFpbC50ZWFjaGluZ0luZm8ubWFwKChpbmZvLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGtleT17aW5kZXh9IGNsYXNzTmFtZT1cInRlYWNoaW5nLWluZm8tY2FyZFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY2FyZC1oZWFkZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImNhcmQtdGl0bGVcIj7or77nqIvlhoXlrrkge2luZGV4ICsgMX08L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInJlbW92ZS1jYXJkLWJ0blwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHJlbW92ZVRlYWNoaW5nSW5mb0l0ZW0oaW5kZXgpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aXRsZT1cIuWIoOmZpOatpOWGheWuuVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIMOXXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNhcmQtY29udGVudFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJpbnB1dC1ncm91cFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8bGFiZWw+5qCH6aKYPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtpbmZvLnRpdGxlfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gdXBkYXRlVGVhY2hpbmdJbmZvVGl0bGUoaW5kZXgsIGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIuivt+i+k+WFpeagh+mimO+8jOWmgu+8muaVmeWtpuebruagh+OAgeaVmeWtpuaWueazleetiVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGl0bGUtaW5wdXRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImlucHV0LWdyb3VwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbD7lhoXlrrk8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8dGV4dGFyZWFcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17aW5mby5jb250ZW50fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gdXBkYXRlVGVhY2hpbmdJbmZvQ29udGVudChpbmRleCwgZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwi6K+36L6T5YWl5YW35L2T5YaF5a6577yM5aSa5Liq5YaF5a656aG55Y+v55So5o2i6KGM5YiG6ZqUXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJjb250ZW50LXRleHRhcmVhXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByb3dzPXs0fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgKSlcbiAgICAgICAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJlbXB0eS1jb250ZW50LWhpbnRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHA+5pqC5peg6K++56iL5YaF5a6577yM54K55Ye75Y+z5LiK6KeS5oyJ6ZKu5re75YqgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgey8qIOmHjeaWsOS4iuivviAqL31cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY291cnNlLWRldGFpbC1zZWN0aW9uXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwib25lLWtleS1zZWN0aW9uXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJvbmUta2V5LWl0ZW1cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPumHjeaWsOS4iuivvjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJzd2l0Y2hcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImNoZWNrYm94XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjaGVja2VkPXtjb3Vyc2VEZXRhaWwuaXNPbmVLZXlPcGVufVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0Q291cnNlRGV0YWlsKHByZXYgPT4gKHsgLi4ucHJldiwgaXNPbmVLZXlPcGVuOiBlLnRhcmdldC5jaGVja2VkIH0pKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwic2xpZGVyXCI+PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICAgIHtjb3Vyc2VEZXRhaWwuaXNPbmVLZXlPcGVuICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwib25lLWtleS1pdGVtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+5YiG6YWN56ev5pyoPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJzd2l0Y2hcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwiY2hlY2tib3hcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjaGVja2VkPXtjb3Vyc2VEZXRhaWwuaXNEaXN0cmlidXRpb25FbmFibGVkfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldENvdXJzZURldGFpbChwcmV2ID0+ICh7IC4uLnByZXYsIGlzRGlzdHJpYnV0aW9uRW5hYmxlZDogZS50YXJnZXQuY2hlY2tlZCB9KSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwic2xpZGVyXCI+PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge2NvdXJzZURldGFpbC5pc0Rpc3RyaWJ1dGlvbkVuYWJsZWQgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJibG9jay10ZW1wbGF0ZS1zZWN0aW9uXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJzZWxlY3QtdGVtcGxhdGUtYnRuXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVPcGVuVGVtcGxhdGVQaWNrZXJ9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICDpgInmi6nnp6/mnKjmqKHmnb9cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic2VsZWN0ZWQtdGVtcGxhdGUtZGlzcGxheVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPntjb3Vyc2VEZXRhaWwuc2VsZWN0ZWRUZW1wbGF0ZSB8fCAn6YCJ5Lit55qE5qih5p2/5ZCN5a2XJ308L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJvbmUta2V5LWl0ZW1cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj7liIbphY3og73ph488L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cInN3aXRjaFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJjaGVja2JveFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNoZWNrZWQ9e2NvdXJzZURldGFpbC5pc0Rpc3RyaWJ1dGlvbldhdGVyfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldENvdXJzZURldGFpbChwcmV2ID0+ICh7IC4uLnByZXYsIGlzRGlzdHJpYnV0aW9uV2F0ZXI6IGUudGFyZ2V0LmNoZWNrZWQgfSkpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInNsaWRlclwiPjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICAgICAgICB7Y291cnNlRGV0YWlsLmlzRGlzdHJpYnV0aW9uV2F0ZXIgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZW5lcmd5LWlucHV0LXNlY3Rpb25cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPumcgOimgeiDvemHj++8mjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtjb3Vyc2VEZXRhaWwucmVxdWlyZWRFbmVyZ3kgfHwgJyd9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0Q291cnNlRGV0YWlsKHByZXYgPT4gKHsgLi4ucHJldiwgcmVxdWlyZWRFbmVyZ3k6IGUudGFyZ2V0LnZhbHVlIH0pKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLor7fovpPlhaXpnIDopoHnmoTog73ph4/lgLxcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJlbmVyZ3ktaW5wdXRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm9uZS1rZXktaXRlbVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPuWIhumFjeS7u+WKoTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwic3dpdGNoXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImNoZWNrYm94XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2hlY2tlZD17Y291cnNlRGV0YWlsLmlzRGlzdHJpYnV0aW9uTGltaXR9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0Q291cnNlRGV0YWlsKHByZXYgPT4gKHsgLi4ucHJldiwgaXNEaXN0cmlidXRpb25MaW1pdDogZS50YXJnZXQuY2hlY2tlZCB9KSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwic2xpZGVyXCI+PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHsvKiDku7vliqHphY3nva7ooajljZUgKi99XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtjb3Vyc2VEZXRhaWwuaXNEaXN0cmlidXRpb25MaW1pdCAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0YXNrLWNvbmZpZy1mb3JtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Lyog5Lu75Yqh5ZCN56ew5ZKM5oyB57ut5aSp5pWwICovfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0YXNrLWNvbmZpZy1yb3dcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0YXNrLWNvbmZpZy1maWVsZFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbD7ku7vliqHlkI3np7A6PC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtjb3Vyc2VEZXRhaWwudGFza0NvbmZpZy50YXNrTmFtZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0Q291cnNlRGV0YWlsKHByZXYgPT4gKHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4ucHJldixcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGFza0NvbmZpZzogeyAuLi5wcmV2LnRhc2tDb25maWcsIHRhc2tOYW1lOiBlLnRhcmdldC52YWx1ZSB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIuivt+i+k+WFpeS7u+WKoeWQjeensFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGFzay1jb25maWctZmllbGRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8bGFiZWw+5Lu75Yqh5oyB57ut5aSp5pWwOjwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwibnVtYmVyXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtjb3Vyc2VEZXRhaWwudGFza0NvbmZpZy50YXNrRHVyYXRpb259XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldENvdXJzZURldGFpbChwcmV2ID0+ICh7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC4uLnByZXYsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRhc2tDb25maWc6IHsgLi4ucHJldi50YXNrQ29uZmlnLCB0YXNrRHVyYXRpb246IGUudGFyZ2V0LnZhbHVlIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwi6K+36L6T5YWl5aSp5pWwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Lyog5Lu75Yqh5o+P6L+wICovfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0YXNrLWNvbmZpZy1maWVsZCB0YXNrLWNvbmZpZy1mdWxsXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbD7ku7vliqHmj4/ov7A6PC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHRleHRhcmVhXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2NvdXJzZURldGFpbC50YXNrQ29uZmlnLnRhc2tEZXNjcmlwdGlvbn1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldENvdXJzZURldGFpbChwcmV2ID0+ICh7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuLi5wcmV2LFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGFza0NvbmZpZzogeyAuLi5wcmV2LnRhc2tDb25maWcsIHRhc2tEZXNjcmlwdGlvbjogZS50YXJnZXQudmFsdWUgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIuivt+i+k+WFpeS7u+WKoeaPj+i/sFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcm93cz17NH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Lyog5Lu75Yqh6Ieq6K+E6aG5ICovfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0YXNrLWNvbmZpZy1maWVsZCB0YXNrLWNvbmZpZy1mdWxsXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbD7ku7vliqHoh6ror4Tpobk6IDxzcGFuIGNsYXNzTmFtZT1cIml0ZW0tbnVtYmVyXCI+e2NvdXJzZURldGFpbC50YXNrQ29uZmlnLnNlbGZBc3Nlc3NtZW50SXRlbXMubGVuZ3RofTwvc3Bhbj48L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Y291cnNlRGV0YWlsLnRhc2tDb25maWcuc2VsZkFzc2Vzc21lbnRJdGVtcy5tYXAoKGl0ZW0sIGluZGV4KSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBrZXk9e2luZGV4fSBjbGFzc05hbWU9XCJzZWxmLWFzc2Vzc21lbnQtaXRlbVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2l0ZW19XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IG5ld0l0ZW1zID0gWy4uLmNvdXJzZURldGFpbC50YXNrQ29uZmlnLnNlbGZBc3Nlc3NtZW50SXRlbXNdO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG5ld0l0ZW1zW2luZGV4XSA9IGUudGFyZ2V0LnZhbHVlO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNldENvdXJzZURldGFpbChwcmV2ID0+ICh7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuLi5wcmV2LFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGFza0NvbmZpZzogeyAuLi5wcmV2LnRhc2tDb25maWcsIHNlbGZBc3Nlc3NtZW50SXRlbXM6IG5ld0l0ZW1zIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KSk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwi6K+36L6T5YWl6Ieq6K+E6aG55YaF5a65XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImJ1dHRvblwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYWRkLWFzc2Vzc21lbnQtYnRuXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRDb3Vyc2VEZXRhaWwocHJldiA9PiAoe1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4ucHJldixcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRhc2tDb25maWc6IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4ucHJldi50YXNrQ29uZmlnLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZWxmQXNzZXNzbWVudEl0ZW1zOiBbLi4ucHJldi50YXNrQ29uZmlnLnNlbGZBc3Nlc3NtZW50SXRlbXMsICcnXVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgey8qIOS7u+WKoeWPguiAg+S9nOWTgSAqL31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGFzay1jb25maWctZmllbGQgdGFzay1jb25maWctZnVsbFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8bGFiZWw+5Lu75Yqh5Y+C6ICD5L2c5ZOBOjwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVmZXJlbmNlLXdvcmtzLXNlY3Rpb25cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIndvcmtzLXNlY3Rpb25cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cImhlbHAtdGV4dFwiPumAieaLqeS9nOWTgeS9nOS4uuS7u+WKoeWPguiAg+i1hOaWme+8iOWPr+WkmumAie+8iTwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicmVsYXRpdmUgd29ya3Mtc2Nyb2xsLXdyYXBwZXJcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1pbkhlaWdodDogJzIwMHB4JyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjdXJzb3I6ICdncmFiJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB1c2VyU2VsZWN0OiAnbm9uZSdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2xvYWRpbmdXb3JrcyA/IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImxvYWRpbmctY29udGFpbmVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImxvYWRpbmctc3Bpbm5lclwiPjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+5Yqg6L295LitLi4uPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApIDogd29ya3MubGVuZ3RoID4gMCA/IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3b3Jrcy1ob3Jpem9udGFsLXNjcm9sbFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7d29ya3MubWFwKCh3b3JrKSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAga2V5PXt3b3JrLmlkfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgd29yay1jYXJkICR7c2VsZWN0ZWRXb3JrSWRzLmluY2x1ZGVzKHdvcmsuaWQpID8gJ3NlbGVjdGVkJyA6ICcnfWB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVXb3JrU2VsZWN0KHdvcmsuaWQpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwid29yay1pbWFnZVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7d29yay5jb3ZlckltYWdlIHx8IHdvcmsuc2NyZWVuU2hvdEltYWdlID8gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxpbWdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNyYz17d29yay5jb3ZlckltYWdlIHx8IHdvcmsuc2NyZWVuU2hvdEltYWdlfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYWx0PXt3b3JrLnRpdGxlfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25FcnJvcj17KGUpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgdGFyZ2V0ID0gZS5jdXJyZW50VGFyZ2V0IGFzIEhUTUxJbWFnZUVsZW1lbnQ7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRhcmdldC5zdHlsZS5kaXNwbGF5ID0gJ25vbmUnO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBuZXh0RWxlbWVudCA9IHRhcmdldC5uZXh0RWxlbWVudFNpYmxpbmcgYXMgSFRNTEVsZW1lbnQ7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmIChuZXh0RWxlbWVudCkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG5leHRFbGVtZW50LnN0eWxlLmRpc3BsYXkgPSAnZmxleCc7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSA6IG51bGx9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwid29yay1wbGFjZWhvbGRlclwiIHN0eWxlPXt7IGRpc3BsYXk6IHdvcmsuY292ZXJJbWFnZSB8fCB3b3JrLnNjcmVlblNob3RJbWFnZSA/ICdub25lJyA6ICdmbGV4JyB9fT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICDkvZzlk4FcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwid29yay10aXRsZVwiPnt3b3JrLnRpdGxlIHx8IHdvcmsubmFtZSB8fCB3b3JrLndvcmtOYW1lIHx8ICfmnKrlkb3lkI3kvZzlk4EnfTwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3NlbGVjdGVkV29ya0lkcy5pbmNsdWRlcyh3b3JrLmlkKSAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic2VsZWN0ZWQtaW5kaWNhdG9yXCI+4pyTPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Lyog5Yqg6L295pu05aSa5oyJ6ZKuICovfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2hhc01vcmVXb3JrcyAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibG9hZC1tb3JlLWNvbnRhaW5lclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImxvYWQtbW9yZS1idG5cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17bG9hZE1vcmVXb3Jrc31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtsb2FkaW5nTW9yZVdvcmtzfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtsb2FkaW5nTW9yZVdvcmtzID8gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibG9hZGluZy1zcGlubmVyIHNtYWxsXCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+5Yqg6L295LitLi4uPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Lz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICfliqDovb3mm7TlpJonXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJlbXB0eS1wbGFjZWhvbGRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJlbXB0eS10ZXh0XCI+5pqC5peg5L2c5ZOBPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgey8qIOS7u+WKoeWPguiAg+i1hOa6kCAqL31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGFzay1jb25maWctZmllbGQgdGFzay1jb25maWctZnVsbFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8bGFiZWw+5Lu75Yqh5Y+C6ICD6LWE5rqQOjwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVmZXJlbmNlLXJlc291cmNlcy1zZWN0aW9uXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWZlcmVuY2UtcmVzb3VyY2VzLWdyaWRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImJ1dHRvblwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInVwbG9hZC1yZXNvdXJjZS1idG5cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8g6Kem5Y+R5paH5Lu25LiK5LygXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgaW5wdXQgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCdpbnB1dCcpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlucHV0LnR5cGUgPSAnZmlsZSc7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaW5wdXQuYWNjZXB0ID0gJy5wZGYsLmRvYywuZG9jeCwucHB0LC5wcHR4LC54bHMsLnhsc3gsLnR4dCwuanBnLC5wbmcsLmdpZic7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaW5wdXQub25jaGFuZ2UgPSAoZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgZmlsZSA9IChlLnRhcmdldCBhcyBIVE1MSW5wdXRFbGVtZW50KS5maWxlcz8uWzBdO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGZpbGUpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0Q291cnNlRGV0YWlsKHByZXYgPT4gKHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuLi5wcmV2LFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRhc2tDb25maWc6IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC4uLnByZXYudGFza0NvbmZpZyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJlZmVyZW5jZVJlc291cmNlczogW1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuLi5wcmV2LnRhc2tDb25maWcucmVmZXJlbmNlUmVzb3VyY2VzLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7IHR5cGU6ICdmaWxlJywgbmFtZTogZmlsZS5uYW1lIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIF1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlucHV0LmNsaWNrKCk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxQbHVzIHNpemU9ezI0fSAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICDkuIrkvKBcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2NvdXJzZURldGFpbC50YXNrQ29uZmlnLnJlZmVyZW5jZVJlc291cmNlcy5tYXAoKHJlc291cmNlLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGtleT17aW5kZXh9IGNsYXNzTmFtZT1cInJlZmVyZW5jZS1yZXNvdXJjZS1pdGVtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+e3Jlc291cmNlLm5hbWV9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicmVtb3ZlLXJlc291cmNlLWJ0blwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IG5ld1Jlc291cmNlcyA9IGNvdXJzZURldGFpbC50YXNrQ29uZmlnLnJlZmVyZW5jZVJlc291cmNlcy5maWx0ZXIoKF8sIGkpID0+IGkgIT09IGluZGV4KTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0Q291cnNlRGV0YWlsKHByZXYgPT4gKHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuLi5wcmV2LFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRhc2tDb25maWc6IHsgLi4ucHJldi50YXNrQ29uZmlnLCByZWZlcmVuY2VSZXNvdXJjZXM6IG5ld1Jlc291cmNlcyB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgw5dcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIOW6lemDqOaMiemSriAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb3Vyc2UtbGlzdC1mb290ZXJcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvdXJzZS1saXN0LWZvb3Rlci1sZWZ0XCI+XG4gICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZVB1Ymxpc2h9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImNvdXJzZS1saXN0LWJ0biBjb3Vyc2UtbGlzdC1idG4tcHVibGlzaFwiXG4gICAgICAgICAgICAgIGRpc2FibGVkPXtjb3Vyc2VMaXN0Lmxlbmd0aCA9PT0gMCB8fCBpc1B1Ymxpc2hpbmdTZXJpZXMgfHwgc2VyaWVzU3RhdHVzID09PSAxfVxuICAgICAgICAgICAgICB0aXRsZT17XG4gICAgICAgICAgICAgICAgc2VyaWVzU3RhdHVzID09PSAxXG4gICAgICAgICAgICAgICAgICA/ICfns7vliJfor77nqIvlt7Llj5HluIMnXG4gICAgICAgICAgICAgICAgICA6IGNvdXJzZUxpc3QubGVuZ3RoID09PSAwXG4gICAgICAgICAgICAgICAgICAgID8gJ+WPkeW4g+Wksei0pe+8muivvueoi+ezu+WIl+S4reiHs+WwkemcgOimgeWMheWQq+S4gOS4quivvueoiydcbiAgICAgICAgICAgICAgICAgICAgOiBpc1B1Ymxpc2hpbmdTZXJpZXNcbiAgICAgICAgICAgICAgICAgICAgICA/ICfmraPlnKjlj5HluIPns7vliJfor77nqIsuLi4nXG4gICAgICAgICAgICAgICAgICAgICAgOiAn5Y+R5biD57O75YiX6K++56iLJ1xuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIHtzZXJpZXNTdGF0dXMgPT09IDFcbiAgICAgICAgICAgICAgICA/ICflt7Llj5HluIMnXG4gICAgICAgICAgICAgICAgOiBpc1B1Ymxpc2hpbmdTZXJpZXNcbiAgICAgICAgICAgICAgICAgID8gJ+ato+WcqOWPkeW4gy4uLidcbiAgICAgICAgICAgICAgICAgIDogJ+WPkeW4g+ezu+WIl+ivvueoiydcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb3Vyc2UtbGlzdC1mb290ZXItcmlnaHRcIj5cbiAgICAgICAgICAgIDxidXR0b24gb25DbGljaz17aGFuZGxlRXhpdEVkaXR9IGNsYXNzTmFtZT1cImNvdXJzZS1saXN0LWJ0biBjb3Vyc2UtbGlzdC1idG4tZXhpdFwiPlxuICAgICAgICAgICAgICDpgIDlh7rnvJbovpHmqKHlvI9cbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVQdWJsaXNoQ291cnNlfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJjb3Vyc2UtbGlzdC1idG4gY291cnNlLWxpc3QtYnRuLXB1Ymxpc2gtY291cnNlXCJcbiAgICAgICAgICAgICAgZGlzYWJsZWQ9eyFzZWxlY3RlZENvdXJzZUlkIHx8IGNvdXJzZUxpc3QuZmluZChjID0+IGMuaWQgPT09IHNlbGVjdGVkQ291cnNlSWQpPy5zdGF0dXMgPT09IDEgfHwgaXNQdWJsaXNoaW5nfVxuICAgICAgICAgICAgICB0aXRsZT17XG4gICAgICAgICAgICAgICAgIXNlbGVjdGVkQ291cnNlSWRcbiAgICAgICAgICAgICAgICAgID8gJ+ivt+WFiOmAieaLqeimgeWPkeW4g+eahOivvueoiydcbiAgICAgICAgICAgICAgICAgIDogY291cnNlTGlzdC5maW5kKGMgPT4gYy5pZCA9PT0gc2VsZWN0ZWRDb3Vyc2VJZCk/LnN0YXR1cyA9PT0gMVxuICAgICAgICAgICAgICAgICAgICA/ICfor6Xor77nqIvlt7Llj5HluIMnXG4gICAgICAgICAgICAgICAgICAgIDogaXNQdWJsaXNoaW5nXG4gICAgICAgICAgICAgICAgICAgICAgPyAn5q2j5Zyo5Y+R5biD6K++56iLLi4uJ1xuICAgICAgICAgICAgICAgICAgICAgIDogJ+WPkeW4g+mAieS4reeahOivvueoiydcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICB7aXNQdWJsaXNoaW5nID8gJ+ato+WcqOWPkeW4gy4uLicgOiAn5Y+R5biD6K++56iLJ31cbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVTYXZlfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJjb3Vyc2UtbGlzdC1idG4gY291cnNlLWxpc3QtYnRuLXNhdmVcIlxuICAgICAgICAgICAgICBkaXNhYmxlZD17dXBsb2FkaW5nRmlsZXMuc2l6ZSA+IDAgfHwgaXNDcmVhdGluZyB8fCBjb3Vyc2VMaXN0Lmxlbmd0aCA9PT0gMH1cbiAgICAgICAgICAgICAgdGl0bGU9e1xuICAgICAgICAgICAgICAgIGNvdXJzZUxpc3QubGVuZ3RoID09PSAwXG4gICAgICAgICAgICAgICAgICA/ICfor7flhYjmt7vliqDor77nqIvlhoXlrrknXG4gICAgICAgICAgICAgICAgICA6IHVwbG9hZGluZ0ZpbGVzLnNpemUgPiAwXG4gICAgICAgICAgICAgICAgICAgID8gJ+acieaWh+S7tuato+WcqOS4iuS8oOS4re+8jOivt+etieW+heS4iuS8oOWujOaIkCdcbiAgICAgICAgICAgICAgICAgICAgOiBpc0NyZWF0aW5nXG4gICAgICAgICAgICAgICAgICAgICAgPyAoY291cnNlTGlzdC5zb21lKGMgPT4gYy5pZCA+IDEwMDAwMDApID8gJ+ato+WcqOWIm+W7uuivvueoiy4uLicgOiAn5q2j5Zyo5L+d5a2Y6K++56iLLi4uJylcbiAgICAgICAgICAgICAgICAgICAgICA6IChjb3Vyc2VMaXN0LnNvbWUoYyA9PiBjLmlkID4gMTAwMDAwMCkgPyAn5Yib5bu66K++56iLJyA6ICfkv53lrZjor77nqIsnKVxuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIHt1cGxvYWRpbmdGaWxlcy5zaXplID4gMFxuICAgICAgICAgICAgICAgID8gJ+S4iuS8oOS4rS4uLidcbiAgICAgICAgICAgICAgICA6IGlzQ3JlYXRpbmdcbiAgICAgICAgICAgICAgICAgID8gKGNvdXJzZUxpc3Quc29tZShjID0+IGMuaWQgPiAxMDAwMDAwKSA/ICfmraPlnKjliJvlu7ouLi4nIDogJ+ato+WcqOS/neWtmC4uLicpXG4gICAgICAgICAgICAgICAgICA6IChjb3Vyc2VMaXN0LnNvbWUoYyA9PiBjLmlkID4gMTAwMDAwMCkgPyAn5Yib5bu66K++56iLJyA6ICfkv53lrZjor77nqIsnKVxuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIOWIoOmZpOehruiupOW8ueeqlyAqL31cbiAgICAgIHtkZWxldGVDb25maXJtVmlzaWJsZSAmJiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibW9kYWwtb3ZlcmxheVwiIG9uQ2xpY2s9e2NhbmNlbERlbGV0ZX0+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJkZWxldGUtY29uZmlybS1tb2RhbFwiIG9uQ2xpY2s9eyhlKSA9PiBlLnN0b3BQcm9wYWdhdGlvbigpfT5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZGVsZXRlLWNvbmZpcm0taGVhZGVyXCI+XG4gICAgICAgICAgICAgIDxoMz7noa7orqTliKDpmaQ8L2gzPlxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgb25DbGljaz17Y2FuY2VsRGVsZXRlfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImNsb3NlLWJ0blwiXG4gICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2lzRGVsZXRpbmd9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8WCBjbGFzc05hbWU9XCJ3LTQgaC00XCIgLz5cbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZGVsZXRlLWNvbmZpcm0tY29udGVudFwiPlxuICAgICAgICAgICAgICA8cD5cbiAgICAgICAgICAgICAgICB7aXNEZWxldGluZ1xuICAgICAgICAgICAgICAgICAgPyAn5q2j5Zyo5Yig6Zmk6K++56iL77yM6K+356iN5YCZLi4uJ1xuICAgICAgICAgICAgICAgICAgOiAn56Gu5a6a6KaB5Yig6Zmk6L+Z5Liq6K++56iL5ZCX77yf5Yig6Zmk5ZCO5peg5rOV5oGi5aSN44CCJ1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICB7aXNEZWxldGluZyAmJiAoXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJkZWxldGUtbG9hZGluZ1wiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJsb2FkaW5nLXNwaW5uZXJcIj48L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJkZWxldGUtY29uZmlybS1mb290ZXJcIj5cbiAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2NhbmNlbERlbGV0ZX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJjYW5jZWwtYnRuXCJcbiAgICAgICAgICAgICAgICBkaXNhYmxlZD17aXNEZWxldGluZ31cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIOWPlua2iFxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2NvbmZpcm1EZWxldGVDb3Vyc2V9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiY29uZmlybS1idG5cIlxuICAgICAgICAgICAgICAgIGRpc2FibGVkPXtpc0RlbGV0aW5nfVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAge2lzRGVsZXRpbmcgPyAn5q2j5Zyo5Yig6ZmkLi4uJyA6ICfnoa7orqTliKDpmaQnfVxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG5cbiAgICAgIHsvKiDmqKHmnb/pgInmi6nlvLnnqpcgKi99XG4gICAgICA8VGVtcGxhdGVQaWNrZXJNb2RhbFxuICAgICAgICBpc09wZW49e2lzVGVtcGxhdGVQaWNrZXJPcGVufVxuICAgICAgICBvbkNsb3NlPXsoKSA9PiBzZXRJc1RlbXBsYXRlUGlja2VyT3BlbihmYWxzZSl9XG4gICAgICAgIG9uVGVtcGxhdGVTZWxlY3Q9e2hhbmRsZVRlbXBsYXRlU2VsZWN0fVxuICAgICAgLz5cbiAgICA8L2Rpdj5cbiAgKTtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IENvdXJzZUxpc3RFZGl0TW9kYWw7XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsIlgiLCJTZXR0aW5ncyIsIlBsdXMiLCJCb29rT3BlbiIsIkZpbGVUZXh0IiwiY291cnNlTWFuYWdlbWVudEFwaSIsImNvdXJzZUFwaSIsInVwbG9hZEFwaSIsIndvcmtzQXBpIiwiU2VsZWN0IiwiR2V0Tm90aWZpY2F0aW9uIiwiVGVtcGxhdGVQaWNrZXJNb2RhbCIsImZldGNoU2VyaWVzRGV0YWlsIiwic2VyaWVzSWQiLCJjb25zb2xlIiwibG9nIiwicmVzcG9uc2UiLCJnZXRTZXJpZXNEZXRhaWwiLCJkYXRhIiwiZmV0Y2hDb3Vyc2VMaXN0IiwiZ2V0TWFuYWdlbWVudFNlcmllc0NvdXJzZXMiLCJwYWdlIiwicGFnZVNpemUiLCJmZXRjaENvdXJzZURldGFpbCIsImNvdXJzZUlkIiwiZ2V0Q291cnNlTWFya2V0cGxhY2VEZXRhaWwiLCJmZXRjaENvdXJzZVRhZ3MiLCJyZXN1bHQiLCJnZXRDb3Vyc2VUYWdzIiwic3RhdHVzIiwiZXJyb3IiLCJDb3Vyc2VMaXN0RWRpdE1vZGFsIiwiaXNWaXNpYmxlIiwib25DbG9zZSIsIm9uU2F2ZSIsInNlcmllc1RpdGxlIiwic2VyaWVzQ292ZXJJbWFnZSIsImdldFNlbGVjdGVkQ291cnNlIiwiY291cnNlRGV0YWlsIiwiY291cnNlTGlzdCIsIm5vdGlmaWNhdGlvbiIsInNldENvdXJzZUxpc3QiLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsInJpZ2h0UGFuZWxUeXBlIiwic2V0UmlnaHRQYW5lbFR5cGUiLCJzZWxlY3RlZENvdXJzZUlkIiwic2V0U2VsZWN0ZWRDb3Vyc2VJZCIsInVwbG9hZGluZ0ZpbGVzIiwic2V0VXBsb2FkaW5nRmlsZXMiLCJTZXQiLCJpc1B1Ymxpc2hpbmciLCJzZXRJc1B1Ymxpc2hpbmciLCJpc0NyZWF0aW5nIiwic2V0SXNDcmVhdGluZyIsImlzUHVibGlzaGluZ1NlcmllcyIsInNldElzUHVibGlzaGluZ1NlcmllcyIsInNlcmllc1N0YXR1cyIsInNldFNlcmllc1N0YXR1cyIsImRlbGV0ZUNvbmZpcm1WaXNpYmxlIiwic2V0RGVsZXRlQ29uZmlybVZpc2libGUiLCJjb3Vyc2VUb0RlbGV0ZSIsInNldENvdXJzZVRvRGVsZXRlIiwiaXNEZWxldGluZyIsInNldElzRGVsZXRpbmciLCJpc1RlbXBsYXRlUGlja2VyT3BlbiIsInNldElzVGVtcGxhdGVQaWNrZXJPcGVuIiwiZWRpdGluZ1RpdGxlIiwic2V0RWRpdGluZ1RpdGxlIiwiY291cnNlR29hbHMiLCJzZXRDb3Vyc2VHb2FscyIsImNvdXJzZU9iamVjdGl2ZXMiLCJzZXRDb3Vyc2VPYmplY3RpdmVzIiwicHJvamVjdE1lbWJlcnMiLCJzZXRQcm9qZWN0TWVtYmVycyIsIndvcmtzIiwic2V0V29ya3MiLCJsb2FkaW5nV29ya3MiLCJzZXRMb2FkaW5nV29ya3MiLCJzZWxlY3RlZFdvcmtJZHMiLCJzZXRTZWxlY3RlZFdvcmtJZHMiLCJ3b3Jrc1BhZ2UiLCJzZXRXb3Jrc1BhZ2UiLCJoYXNNb3JlV29ya3MiLCJzZXRIYXNNb3JlV29ya3MiLCJsb2FkaW5nTW9yZVdvcmtzIiwic2V0TG9hZGluZ01vcmVXb3JrcyIsImNvdXJzZVRhZ3MiLCJzZXRDb3Vyc2VUYWdzIiwic2VsZWN0ZWRUYWdzIiwic2V0U2VsZWN0ZWRUYWdzIiwidGFnc0xvYWRpbmciLCJzZXRUYWdzTG9hZGluZyIsInNldENvdXJzZURldGFpbCIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJjb3ZlckltYWdlIiwidmlkZW9VcmwiLCJ2aWRlb05hbWUiLCJpc1ZpZGVvRW5hYmxlZCIsImF0dGFjaG1lbnRVcmwiLCJhdHRhY2htZW50TmFtZSIsImlzQXR0YWNobWVudEVuYWJsZWQiLCJ0ZWFjaGluZ01hdGVyaWFscyIsInRlYWNoaW5nSW5mbyIsImNvbnRlbnRDb25maWciLCJoYXNWaWRlbyIsImhhc0RvY3VtZW50IiwiaGFzQXVkaW8iLCJ2aWRlbyIsInVybCIsIm5hbWUiLCJkb2N1bWVudCIsImF1ZGlvIiwiY291cnNlQ29udGVudCIsInRvcGljIiwiY29udGVudCIsImlzT25lS2V5T3BlbiIsImlzRGlzdHJpYnV0aW9uRW5hYmxlZCIsImRpc3RyaWJ1dGlvblJld2FyZCIsInNlbGVjdGVkVGVtcGxhdGUiLCJpc0Rpc3RyaWJ1dGlvbldhdGVyIiwicmVxdWlyZWRFbmVyZ3kiLCJlbmVyZ3lBbW91bnQiLCJpc0Rpc3RyaWJ1dGlvbkxpbWl0IiwiZGlzdHJpYnV0aW9uQ29uZGl0aW9ucyIsImludml0ZUNvdW50IiwidGFza0NvdW50IiwiZXhwZXJpZW5jZSIsImlzRGlzdHJpYnV0aW9uVGltZSIsImRpc3RyaWJ1dGlvblRpbWVDb25kaXRpb25zIiwic3RhcnRUaW1lIiwiZW5kVGltZSIsImRpc3RyaWJ1dGlvbk1hdGVyaWFscyIsInRhc2tDb25maWciLCJ0YXNrTmFtZSIsInRhc2tEdXJhdGlvbiIsInRhc2tEZXNjcmlwdGlvbiIsInNlbGZBc3Nlc3NtZW50SXRlbXMiLCJyZWZlcmVuY2VXb3JrcyIsInJlZmVyZW5jZVJlc291cmNlcyIsInRva2VuIiwibG9jYWxTdG9yYWdlIiwiZ2V0SXRlbSIsImxvYWRDb3Vyc2VMaXN0IiwibG9hZENvdXJzZVRhZ3MiLCJsb2FkU2VyaWVzRGV0YWlsIiwibG9hZFVzZXJXb3JrcyIsImNvZGUiLCJjb3Vyc2VzIiwibGlzdCIsIm1hcCIsImMiLCJpZCIsInR5cGUiLCJ0b3RhbCIsImxlbmd0aCIsImRyYWZ0IiwiZmlsdGVyIiwicHVibGlzaGVkIiwibWVzc2FnZSIsInNlcmllc0RhdGEiLCJyZXNldCIsInVzZXJJZCIsImN1cnJlbnRQYWdlIiwiZ2V0VGVhY2hlcldvcmtzIiwiTnVtYmVyIiwid29ya3NMaXN0IiwiQXJyYXkiLCJpc0FycmF5IiwicHJldiIsImxvYWRNb3JlV29ya3MiLCJoYW5kbGVXb3JrU2VsZWN0Iiwid29ya0lkIiwiaW5jbHVkZXMiLCJ0YWdzIiwiZm9yRWFjaCIsInRhZyIsImluZGV4IiwiaGFzVGFnIiwiaWRUeXBlIiwibmFtZVR5cGUiLCJuYW1lVmFsdWUiLCJuYW1lTm90RW1wdHkiLCJ0cmltIiwidmFsaWRUYWdzIiwiaXNWYWxpZCIsIndhcm4iLCJzdGF0dXNUZXh0IiwiYWRkTmV3Q291cnNlIiwibmV3Q291cnNlIiwiRGF0ZSIsIm5vdyIsIm9yZGVySW5kZXgiLCJzdGF0dXNMYWJlbCIsInZpZGVvRHVyYXRpb24iLCJ2aWRlb0R1cmF0aW9uTGFiZWwiLCJmaXJzdFRlYWNoaW5nVGl0bGUiLCJyZXNvdXJjZXNDb3VudCIsImNyZWF0ZWRBdCIsInRvSVNPU3RyaW5nIiwidXBkYXRlZEF0Iiwic2hvd0NvdXJzZVBhbmVsIiwic2hvd0RlbGV0ZUNvbmZpcm0iLCJjb25maXJtRGVsZXRlQ291cnNlIiwiZGVsZXRlQ291cnNlIiwiY291cnNlIiwic3VjY2VzcyIsImNhbmNlbERlbGV0ZSIsInVwZGF0ZUNvdXJzZVRpdGxlIiwibmV3VGl0bGUiLCJoYW5kbGVDb3ZlclVwbG9hZCIsImV2ZW50IiwiZmlsZSIsInRhcmdldCIsImZpbGVzIiwiYWxsb3dlZFR5cGVzIiwic2l6ZSIsImFkZCIsInByZXZpZXdVcmwiLCJVUkwiLCJjcmVhdGVPYmplY3RVUkwiLCJpbWFnZVVybCIsInVwbG9hZFRvT3NzIiwiYWxlcnQiLCJuZXdTZXQiLCJkZWxldGUiLCJoYW5kbGVWaWRlb1VwbG9hZCIsInRyaWdnZXJWaWRlb1VwbG9hZCIsImlucHV0IiwiY3JlYXRlRWxlbWVudCIsImFjY2VwdCIsIm9uY2hhbmdlIiwiZSIsImNsaWNrIiwiaGFuZGxlQXR0YWNobWVudFVwbG9hZCIsImRvY3VtZW50VXJsIiwidHJpZ2dlckF0dGFjaG1lbnRVcGxvYWQiLCJoYW5kbGVUZWFjaGluZ01hdGVyaWFsVXBsb2FkIiwibWF0ZXJpYWxVcmwiLCJuZXdNYXRlcmlhbCIsImZyb20iLCJpdGVtIiwic3RhcnRzV2l0aCIsInRyaWdnZXJUZWFjaGluZ01hdGVyaWFsVXBsb2FkIiwicmVtb3ZlVGVhY2hpbmdNYXRlcmlhbCIsIl8iLCJpIiwiYWRkVGVhY2hpbmdJbmZvSXRlbSIsInJlbW92ZVRlYWNoaW5nSW5mb0l0ZW0iLCJ1cGRhdGVUZWFjaGluZ0luZm9UaXRsZSIsIm5ld1RlYWNoaW5nSW5mbyIsInVwZGF0ZVRlYWNoaW5nSW5mb0NvbnRlbnQiLCJjb252ZXJ0VGVhY2hpbmdJbmZvRm9yQVBJIiwiaW5mbyIsInNwbGl0IiwibGluZSIsImNoZWNrRm9yQmxvYlVybHMiLCJpc3N1ZXMiLCJwdXNoIiwiYWRkaXRpb25hbFJlc291cmNlcyIsInJlc291cmNlIiwic2F2ZUNvdXJzZSIsIkVycm9yIiwiYmxvYklzc3VlcyIsImpvaW4iLCJjb3Vyc2VEYXRhIiwiY3JlYXRlQ291cnNlIiwidXBkYXRlQ291cnNlIiwiaGFuZGxlUHVibGlzaENvdXJzZSIsInNlbGVjdGVkQ291cnNlIiwiZmluZCIsImNvdXJzZUlkVG9QdWJsaXNoIiwicHVibGlzaENvdXJzZSIsImhhbmRsZVNhdmUiLCJ1cGRhdGVkQ291cnNlIiwibWF0ZXJpYWwiLCJoYW5kbGVQdWJsaXNoIiwicHVibGlzaENvdXJzZVNlcmllcyIsInB1Ymxpc2hEYXRhIiwic3VjY2Vzc01lc3NhZ2UiLCJwdWJsaXNoU3RhdHMiLCJzdGF0cyIsInRvdGFsQ291cnNlcyIsInB1Ymxpc2hlZENvdXJzZXMiLCJ2aWRlb0NvdXJzZUNvdW50IiwiZG9jdW1lbnRDb3Vyc2VDb3VudCIsInRvdGFsUmVzb3VyY2VzQ291bnQiLCJ0b3RhbFZpZGVvRHVyYXRpb24iLCJkdXJhdGlvbk1pbnV0ZXMiLCJNYXRoIiwicm91bmQiLCJlcnJvck1lc3NhZ2UiLCJoYW5kbGVFeGl0RWRpdCIsInNob3dTZXR0aW5nc1BhbmVsIiwiaGFuZGxlVGVtcGxhdGVTZWxlY3QiLCJ0ZW1wbGF0ZSIsInRlbXBsYXRlTmFtZSIsImhhbmRsZU9wZW5UZW1wbGF0ZVBpY2tlciIsInJlcyIsImNvdXJzZURldGFpbERhdGEiLCJtYXBwZWRUZWFjaGluZ0luZm8iLCJkaXYiLCJjbGFzc05hbWUiLCJoMiIsImJ1dHRvbiIsIm9uQ2xpY2siLCJwIiwiaDMiLCJzcGFuIiwic3RvcFByb3BhZ2F0aW9uIiwiaW1nIiwic3JjIiwiYWx0IiwibGFiZWwiLCJ2YWx1ZSIsIm9uQ2hhbmdlIiwicGxhY2Vob2xkZXIiLCJtb2RlIiwic3R5bGUiLCJ3aWR0aCIsIm9wdGlvbnMiLCJjb2xvciIsImZvbnRTaXplIiwibWFyZ2luVG9wIiwiZ2V0RWxlbWVudEJ5SWQiLCJkaXNwbGF5IiwidGV4dGFyZWEiLCJyb3dzIiwiY2hlY2tlZCIsImNvbnRyb2xzIiwicG9zdGVyIiwic291cmNlIiwid2luZG93Iiwib3BlbiIsImN1cnNvciIsInRleHREZWNvcmF0aW9uIiwibmV3SXRlbXMiLCJtaW5IZWlnaHQiLCJ1c2VyU2VsZWN0Iiwid29yayIsInNjcmVlblNob3RJbWFnZSIsIm9uRXJyb3IiLCJjdXJyZW50VGFyZ2V0IiwibmV4dEVsZW1lbnQiLCJuZXh0RWxlbWVudFNpYmxpbmciLCJ3b3JrTmFtZSIsImRpc2FibGVkIiwibmV3UmVzb3VyY2VzIiwic29tZSIsImlzT3BlbiIsIm9uVGVtcGxhdGVTZWxlY3QiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/workbench/components/CourseListEditModal.tsx\n"));

/***/ })

});