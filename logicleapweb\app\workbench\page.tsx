'use client';

import React, { useEffect, useState } from 'react';
import LeftSidebar from './components/LeftSidebar';
import MainContent from './components/MainContent';
import RightSidebar from './components/RightSidebar';
import { TemplateProvider, useTemplate } from './contexts/TemplateContext';
import './styles/workbench.css';
import userApi, { UserInfo } from '@/lib/api/user';
import { pointsApi } from '@/lib/api/points';
import teacherApi from '@/lib/api/teacher';
import { useSelector } from 'react-redux';
import { RootState } from '@/lib/store';
import { useUserInfo } from '@/hooks/useUserInfo';


interface School {
  id: number;
  schoolName: string;
  province: string;
  city: string;
  district: string;
}

interface ClassInfo {
  id: number;
  schoolId: number;
  grade: string;
  className: string;
  teacherId: number;
  assistantTeacherId: number;
  inviteCode: string;
  createTime: string;
  updateTime: string;
  studentCount: number;
  isAssistant?: boolean;
}


const WorkbenchPage = () => {
    const [stats, setStats] = useState({ studentCount: 0, classCount: 0, courseCount: 0 });
    const [points, setPoints] = useState(0);
    const [activeView, setActiveView] = useState('快速开始');
    const [mounted, setMounted] = useState(false);
    const [selectedSchool, setSelectedSchool] = useState<School | null>(null);

    // 直接从Redux获取用户状态，并使用useUserInfo钩子
    const userState = useSelector((state: RootState) => state.user.userState);
    const userId = userState.userId;

    // 使用useUserInfo hook获取refreshUserInfo函数
    const { refreshUserInfo } = useUserInfo();

    // 构建userInfo对象
    const userInfo: UserInfo = {
        id: userState.userId || 0,
        gender: userState.gender || 0,
        phone: userState.phone || '',
        nickName: userState.nickName || '',
        avatarUrl: userState.avatarUrl || '',
        introduction: userState.introduction || '',
        createTime: userState.createTime || '',
        roleId: userState.roleId || 0,
        role: userState.role || null,
        roles: userState.roles || []
    };

    console.log('WorkbenchPage - userId:', userId);
    console.log('WorkbenchPage - userInfo:', userInfo);

    // 班级数据状态
    const [classes, setClasses] = useState<ClassInfo[]>([]);
    const [classesLoading, setClassesLoading] = useState(false);
    const [classesError, setClassesError] = useState<string | null>(null);

    // 防止水合错误
    useEffect(() => {
        setMounted(true);
    }, []);

    useEffect(() => {
        const fetchData = async () => {
            if (!userId || !mounted) return;

            // 1. 刷新用户信息（包括头像）
            try {
                await refreshUserInfo();
            } catch (error) {
                console.error("Failed to refresh user info:", error);
            }

            // 2. 获取教师统计数据
            try {
                const statsRes = await teacherApi.getTeacherStat(userId);
                if (statsRes.code === 200) {
                    setStats(prevStats => ({ ...prevStats, ...statsRes.data }));
                }
            } catch (error) {
                console.error("Failed to fetch teacher stats:", error);
                // 使用占位数据
                setStats(prevStats => ({ ...prevStats, studentCount: 258, classCount: 3, courseCount: 50 }));
            }

            // 3. 获取能量点数
            try {
                const pointsRes = await pointsApi.getTotal();
                if (pointsRes.data.code === 200) {
                    const pointsData = pointsRes.data.data;
                    const totalPoints = typeof pointsData === 'object' && pointsData !== null && 'total' in pointsData
                        ? Number((pointsData as { total: number }).total)
                        : Number(pointsData);
                    setPoints(totalPoints || 0);
                }
            } catch (error) {
                console.error("Failed to fetch points:", error);
                setPoints(9999);
            }
        };

        fetchData();
    }, [userId, mounted]);

    const handleMenuItemClick = (itemName: string) => {
        setActiveView(itemName);
    };

    const handleSchoolSelect = (school: School) => {
        setSelectedSchool(school);
        console.log('主页面接收到选中学校:', school);
    };

    // 处理学校变化（从MainContent传回的回调）
    const handleSchoolChange = (school: School) => {
        console.log('主页面接收到学校变化回调:', school);
        // 这里可以添加额外的学校变化处理逻辑
    };

    // 处理班级数据更新
    const handleClassesUpdate = (classesData: ClassInfo[], loading: boolean, error: string | null) => {
        setClasses(classesData);
        setClassesLoading(loading);
        setClassesError(error);
        console.log('主页面接收到班级数据:', { classesData, loading, error });
    };

    // 防止水合错误，在客户端挂载前不渲染
    if (!mounted) {
        return (
            <div className="workbench-container">
                <div className="loading-placeholder">
                    <div className="loading-spinner"></div>
                    <p>正在加载...</p>
                </div>
            </div>
        );
    }

    return (
        <TemplateProvider userId={userId || null}>
            <div className="workbench-container">
                <LeftSidebar
                    userInfo={userInfo}
                    onMenuItemClick={handleMenuItemClick}
                    onSchoolSelect={handleSchoolSelect}
                    onClassesUpdate={handleClassesUpdate}
                />
                <div className="main-content-area">
                    <MainContent
                        activeView={activeView}
                        selectedSchool={selectedSchool}
                        userInfo={userInfo}
                        classes={classes}
                        classesLoading={classesLoading}
                        classesError={classesError}
                        onClassesUpdate={(updatedClasses) => handleClassesUpdate(updatedClasses, false, null)}
                        onSchoolChange={handleSchoolChange}
                    />
                </div>
                <RightSidebar userInfo={userInfo} stats={stats} points={points} />
            </div>
        </TemplateProvider>
    );
};

export default WorkbenchPage; 