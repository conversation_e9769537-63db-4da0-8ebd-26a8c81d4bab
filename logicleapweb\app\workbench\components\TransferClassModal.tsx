import React, { useState } from 'react';
import { Modal, Form, Radio, Input, Button, Divider } from 'antd';
import { UserOutlined } from '@ant-design/icons';

interface SearchedTeacher {
  id: number;
  nickName: string;
  phone: string;
  avatarUrl?: string;
}

interface TransferClassModalProps {
  visible: boolean;
  onCancel: () => void;
  onOk: (values: { transferType: 'search' | 'assistant' | 'remove'; phone?: string }) => void;
  onSearchTeacher: (phone: string) => Promise<void>;
  searchedTeacher: SearchedTeacher | null;
  hasAssistantTeacher: boolean;
  onRemoveAssistant?: () => void;
  loading?: boolean;
}

export const TransferClassModal: React.FC<TransferClassModalProps> = ({
  visible,
  onCancel,
  onOk,
  onSearchTeacher,
  searchedTeacher,
  hasAssistantTeacher,
  onRemoveAssistant,
  loading = false,
}) => {
  const [form] = Form.useForm();
  const [searchLoading, setSearchLoading] = useState(false);

  const handleSearchTeacher = async (phone: string) => {
    if (!phone.trim()) {
      return;
    }
    
    setSearchLoading(true);
    try {
      await onSearchTeacher(phone);
    } finally {
      setSearchLoading(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  return (
    <Modal
      title="转让班级管理"
      open={visible}
      onCancel={handleCancel}
      footer={null}
      centered
      width={500}
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={(values) => {
          if (values.transferType === 'remove') {
            onRemoveAssistant?.();
            handleCancel();
          } else {
            onOk(values);
          }
        }}
      >
        <Form.Item
          name="transferType"
          label="管理选项"
          rules={[{ required: true, message: '请选择操作类型' }]}
        >
          <Radio.Group>
            <Radio value="search">转让给其他教师</Radio>
            <Radio value="assistant" disabled={!hasAssistantTeacher}>
              转让给协助教师
              {!hasAssistantTeacher && 
                <span className="text-gray-400 ml-2">(暂无协助教师)</span>
              }
            </Radio>
            <Divider className="my-2" />
            <Radio value="remove" disabled={!hasAssistantTeacher}>
              移出协助教师
              {!hasAssistantTeacher && 
                <span className="text-gray-400 ml-2">(暂无协助教师)</span>
              }
            </Radio>
          </Radio.Group>
        </Form.Item>

        <Form.Item
          noStyle
          shouldUpdate={(prevValues, currentValues) => 
            prevValues.transferType !== currentValues.transferType
          }
        >
          {({ getFieldValue }) => 
            getFieldValue('transferType') === 'search' && (
              <Form.Item
                label="教师手机号"
                name="phone"
                rules={[{ required: true, message: '请输入教师手机号' }]}
              >
                <Input.Search
                  placeholder="请输入教师手机号"
                  onSearch={handleSearchTeacher}
                  enterButton="搜索"
                  loading={searchLoading}
                />
              </Form.Item>
            )
          }
        </Form.Item>

        {/* 搜索结果显示 */}
        {searchedTeacher && (
          <div className="mb-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white">
                {searchedTeacher.avatarUrl ? (
                  <img 
                    src={searchedTeacher.avatarUrl} 
                    alt={searchedTeacher.nickName}
                    className="w-full h-full rounded-full object-cover"
                  />
                ) : (
                  <UserOutlined />
                )}
              </div>
              <div>
                <div className="font-medium text-gray-800">{searchedTeacher.nickName}</div>
                <div className="text-sm text-gray-500">{searchedTeacher.phone}</div>
              </div>
            </div>
          </div>
        )}

        <Form.Item className="mb-0 text-right">
          <Button type="default" className="mr-2" onClick={handleCancel}>
            取消
          </Button>
          <Button 
            type="primary" 
            htmlType="submit"
            loading={loading}
          >
            确定
          </Button>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default TransferClassModal;
