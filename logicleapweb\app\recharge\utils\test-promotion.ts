// 测试宣传话语数据转换的示例文件
import { PackageInfo } from '../../../lib/api/package-purchase';
import { transformPackageToRechargeOption } from './dataTransform';

// 模拟带有宣传话语的套餐数据
const mockPackageWithPromotion: PackageInfo = {
  packageId: 1,
  packageName: "首充6元大礼包",
  packageDescription: "新用户专享优惠",
  points: 100,
  validityDays: 30,
  originalPrice: 15.00,
  currentPrice: 0.01,
  discountRate: 0.67,
  savings: 14.99,
  currency: "CNY",
  promotion: {
    features: [
      { name: "充值金额", value: "¥0.01", icon: "💳" },
      { name: "赠送积分", value: "100积分", icon: "⚡" },
      { name: "到账时间", value: "即时到账", icon: "⏰" },
      { name: "有效期", value: "30天", icon: "📅" },
      { name: "安全保障", value: "银行级加密", icon: "🛡️" },
      { name: "客服支持", value: "24小时在线", icon: "🎧" },
      { name: "节省金额", value: "¥14.99", icon: "💰" }
    ]
  }
};

// 模拟没有宣传话语的套餐数据
const mockPackageWithoutPromotion: PackageInfo = {
  packageId: 2,
  packageName: "标准套餐",
  packageDescription: "经典选择",
  points: 500,
  validityDays: 60,
  originalPrice: 50.00,
  currentPrice: 40.00,
  discountRate: 0.8,
  savings: 10.00,
  currency: "CNY"
};

// 测试函数
export function testPromotionDataTransform() {
  console.log('=== 测试带有宣传话语的套餐数据转换 ===');
  const resultWithPromotion = transformPackageToRechargeOption(mockPackageWithPromotion);
  console.log('转换结果:', JSON.stringify(resultWithPromotion, null, 2));
  
  console.log('\n=== 测试没有宣传话语的套餐数据转换 ===');
  const resultWithoutPromotion = transformPackageToRechargeOption(mockPackageWithoutPromotion);
  console.log('转换结果:', JSON.stringify(resultWithoutPromotion, null, 2));
  
  return {
    withPromotion: resultWithPromotion,
    withoutPromotion: resultWithoutPromotion
  };
}

// 如果在浏览器环境中运行，可以在控制台调用这个函数进行测试
if (typeof window !== 'undefined') {
  (window as any).testPromotionDataTransform = testPromotionDataTransform;
}
