"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var TeachingService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.TeachingService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const course_teaching_record_entity_1 = require("../../../domain/entities/teaching/course-teaching-record.entity");
const course_entity_1 = require("../../../domain/entities/management/course.entity");
const course_series_entity_1 = require("../../../domain/entities/management/course-series.entity");
const course_settings_entity_1 = require("../../../domain/entities/management/course-settings.entity");
const task_template_entity_1 = require("../../../domain/entities/teaching/task-template.entity");
const calculation_utils_1 = require("../../../utils/management/calculation.utils");
const lock_manager_1 = require("../../../../../payment/lock/lock.manager");
const teaching_exceptions_1 = require("../../../domain/exceptions/teaching/teaching.exceptions");
const task_self_assessment_item_entity_1 = require("../../../../../util/database/mysql/task_self_assessment_item/entities/task_self_assessment_item.entity");
const teacher_task_entity_1 = require("../../../../../util/database/mysql/teacher_task/entities/teacher_task.entity");
const teacher_task_assignment_entity_1 = require("../../../../../util/database/mysql/teacher_task_assignment/entities/teacher_task_assignment.entity");
const user_class_service_1 = require("../../../../../util/database/mysql/user_class/user_class.service");
const user_student_service_1 = require("../../../../../util/database/mysql/user_student/user_student.service");
const user_info_service_1 = require("../../../../../util/database/mysql/user_info/user_info.service");
const web_point_permission_service_1 = require("../../../../web_point_permission/web_point_permission.service");
let TeachingService = TeachingService_1 = class TeachingService {
    teachingRecordRepository;
    courseRepository;
    courseSeriesRepository;
    courseSettingsRepository;
    taskTemplateRepository;
    teacherTaskRepository;
    teacherTaskAssignmentRepository;
    taskSelfAssessmentItemRepository;
    dataSource;
    lockManager;
    baseUserClassService;
    userStudentService;
    userInfoService;
    webPointPermissionService;
    logger = new common_1.Logger(TeachingService_1.name);
    TEST_ROLLBACK_STEP = '999';
    Allerror = false;
    constructor(teachingRecordRepository, courseRepository, courseSeriesRepository, courseSettingsRepository, taskTemplateRepository, teacherTaskRepository, teacherTaskAssignmentRepository, taskSelfAssessmentItemRepository, dataSource, lockManager, baseUserClassService, userStudentService, userInfoService, webPointPermissionService) {
        this.teachingRecordRepository = teachingRecordRepository;
        this.courseRepository = courseRepository;
        this.courseSeriesRepository = courseSeriesRepository;
        this.courseSettingsRepository = courseSettingsRepository;
        this.taskTemplateRepository = taskTemplateRepository;
        this.teacherTaskRepository = teacherTaskRepository;
        this.teacherTaskAssignmentRepository = teacherTaskAssignmentRepository;
        this.taskSelfAssessmentItemRepository = taskSelfAssessmentItemRepository;
        this.dataSource = dataSource;
        this.lockManager = lockManager;
        this.baseUserClassService = baseUserClassService;
        this.userStudentService = userStudentService;
        this.userInfoService = userInfoService;
        this.webPointPermissionService = webPointPermissionService;
    }
    getTestRollbackStep() {
        return this.TEST_ROLLBACK_STEP;
    }
    async handleExecutionError(error, dto, teacherId, lockAcquireTime, totalExecutionTime) {
        try {
            if (error.message?.startsWith('无法获取锁:')) {
                console.log("检测到分布式锁获取失败，转换为并发冲突异常");
                try {
                    const failedRecord = await this.createFailedTeachingRecord(dto, teacherId, 0, '分布式锁获取失败，系统繁忙', totalExecutionTime);
                    console.log("锁获取失败记录创建成功，ID:", failedRecord.id);
                    return new teaching_exceptions_1.DuplicateOperationException(dto.courseId, dto.classId, teacherId, -1, new Date().toISOString());
                }
                catch (recordError) {
                    if (recordError instanceof teaching_exceptions_1.DuplicateOperationException) {
                        console.log("创建失败记录时检测到重复操作，直接返回重复操作异常");
                        return recordError;
                    }
                    return new teaching_exceptions_1.DuplicateOperationException(dto.courseId, dto.classId, teacherId, -1, new Date().toISOString());
                }
            }
            if (error instanceof teaching_exceptions_1.PartialFailureException) {
                console.log("============= 事务回滚，但保存部分失败的详细信息 =============");
                const responseData = error.getResponse();
                const partialData = responseData.data;
                partialData.lockAcquireTime = lockAcquireTime;
                partialData.totalExecutionTime = totalExecutionTime;
                const partialFailureRecord = await this.createPartialFailureRecord(dto, teacherId, lockAcquireTime, partialData, totalExecutionTime);
                partialData.teachingRecordId = partialFailureRecord.id;
                console.log("部分失败记录创建成功");
                return null;
            }
            console.log("提交事务失败，一键上课失败，事务进行回滚，更新教学记录为失败状态....");
            try {
                const failedRecord = await this.createFailedTeachingRecord(dto, teacherId, 0, error.message || '一键上课执行失败', totalExecutionTime);
                console.log("失败记录创建成功，ID:", failedRecord.id);
                return null;
            }
            catch (recordError) {
                if (recordError instanceof teaching_exceptions_1.DuplicateOperationException) {
                    console.log("创建失败记录时检测到重复操作，返回重复操作异常");
                    return recordError;
                }
                this.logger.error(`处理失败记录时出错: ${recordError.message}`);
                return null;
            }
        }
        catch (recordError) {
            this.logger.error(`处理失败记录时出错: ${recordError.message}`);
            return null;
        }
    }
    async oneClickStart(dto, teacherId) {
        if (this.getTestRollbackStep() === '1') {
            this.logger.warn('🧪 测试回滚：模拟锁获取失败');
            throw new teaching_exceptions_1.DuplicateOperationException(dto.courseId, dto.classId, teacherId, -1, new Date().toISOString());
        }
        const lockKey = `course-teaching:${dto.courseId}:${dto.classId}`;
        const startTime = Date.now();
        let lockAcquireTime = 0;
        console.log("1.获取分布式锁，防止重复调用接口");
        try {
            return await this.lockManager.withDistributedLock(lockKey, async () => {
                lockAcquireTime = Date.now() - startTime;
                console.log(`获取锁成功，耗时: ${lockAcquireTime}ms, 开始执行业务逻辑`);
                return this.executeOneClickStartWithTransaction(dto, teacherId, lockAcquireTime, startTime);
            }, 60000);
        }
        catch (error) {
            const totalExecutionTime = Date.now() - startTime;
            const finalError = await this.handleExecutionError(error, dto, teacherId, lockAcquireTime, totalExecutionTime);
            throw finalError || error;
        }
    }
    async validateTeachingPermissions(dto, teacherId, manager) {
        if (this.getTestRollbackStep() === '2') {
            this.logger.warn('🧪 测试回滚：权限验证阶段异常');
            throw new Error('🧪 测试事务回滚：权限验证阶段异常');
        }
        const course = await manager.findOne(course_entity_1.Course, {
            where: { id: dto.courseId },
            select: ['id', 'status', 'creatorId', 'title']
        });
        if (!course) {
            throw new teaching_exceptions_1.CourseNotFoundOrNotPublishedException(dto.courseId);
        }
        const hasPermission = this.checkCoursePermission(course, teacherId);
        if (!hasPermission) {
            throw new teaching_exceptions_1.InsufficientTeacherPermissionException(teacherId, dto.classId, dto.courseId, course.status, course.creatorId);
        }
    }
    checkCoursePermission(course, teacherId) {
        if (course.status === 1) {
            this.logger.debug(`课程已发布，允许所有教师操作: courseId=${course.id}, teacherId=${teacherId}`);
            return true;
        }
        if (course.creatorId === teacherId) {
            this.logger.debug(`课程未发布，但当前用户是创建者，允许操作: courseId=${course.id}, teacherId=${teacherId}, creatorId=${course.creatorId}`);
            return true;
        }
        this.logger.warn(`课程未发布且当前用户不是创建者，拒绝操作: courseId=${course.id}, teacherId=${teacherId}, creatorId=${course.creatorId}, status=${course.status}`);
        return false;
    }
    async getCourseInfoWithValidation(courseId, manager) {
        console.log("进入获取课程信息接口调用");
        const course = await manager
            .createQueryBuilder(course_entity_1.Course, 'course')
            .leftJoinAndSelect('course.series', 'series')
            .where('course.id = :courseId', { courseId })
            .getOne();
        if (!course) {
            console.log("课程不存在");
            throw new teaching_exceptions_1.CourseNotFoundOrNotPublishedException(courseId);
        }
        console.log("课程信息获取成功");
        return {
            id: course.id,
            title: course.title,
            seriesName: course.series?.title || '未知系列',
            status: course.status
        };
    }
    async getClassInfoWithValidation(classId) {
        try {
            const classInfo = await this.getClassInfo(classId);
            console.log("班级信息获取成功");
            return classInfo;
        }
        catch (error) {
            this.logger.error(`获取班级信息失败: ${error.message}`);
            throw new common_1.BadRequestException('获取班级信息失败，请检查班级是否存在');
        }
    }
    async getClassStudentsWithValidation(classId) {
        const students = await this.getClassStudents(classId);
        if (!students || students.length === 0) {
            throw new teaching_exceptions_1.EmptyClassException(classId, 0);
        }
        console.log("班级学生获取成功");
        return students;
    }
    async getCourseSettingsWithValidation(courseId, manager) {
        const courseSettings = await manager.findOne(course_settings_entity_1.CourseSettings, {
            where: { courseId },
        });
        console.log("课程设置获取成功");
        return courseSettings;
    }
    async getTaskTemplatesForCourse(courseId, manager) {
        console.log("任务模板获取成功");
        return await manager.find(task_template_entity_1.TaskTemplate, {
            where: { courseId },
            order: { id: 'ASC' },
        });
    }
    validateCourseSettings(courseId, courseSettings, taskTemplates) {
        if (this.getTestRollbackStep() === '4') {
            this.logger.warn('🧪 测试回滚：课程设置验证阶段异常');
            throw new Error('🧪 测试事务回滚：课程设置验证阶段异常');
        }
        const missingSettings = [];
        if (courseSettings?.autoCreateTasks && (!taskTemplates || taskTemplates.length === 0)) {
            missingSettings.push('taskTemplates');
        }
        if (courseSettings?.templateId && courseSettings.templateId <= 0) {
            missingSettings.push('templateId');
        }
        if (missingSettings.length > 0) {
            throw new teaching_exceptions_1.IncompleteSettingsException(courseId, missingSettings);
        }
    }
    async createFailedTeachingRecord(dto, teacherId, lockAcquireTime, errorMessage, totalExecutionTime) {
        try {
            const record = this.teachingRecordRepository.create({
                courseId: dto.courseId,
                classId: dto.classId,
                teacherId,
                status: course_teaching_record_entity_1.TeachingStatus.FAILED,
                lockAcquireTime,
                errorMessage,
                totalExecutionTime,
            });
            return await this.teachingRecordRepository.save(record);
        }
        catch (error) {
            if (error.code === 'ER_DUP_ENTRY' ||
                error.message?.includes('Duplicate entry') ||
                error.message?.includes('idx_course_class_teacher_date')) {
                console.log("检测到失败记录创建时的唯一约束冲突，转换为重复操作异常");
                throw new teaching_exceptions_1.DuplicateOperationException(dto.courseId, dto.classId, teacherId, -1, new Date().toISOString());
            }
            throw error;
        }
    }
    async createPartialFailureRecord(dto, teacherId, lockAcquireTime, partialData, totalExecutionTime) {
        console.log("======开始创建部分失败记录=====");
        try {
            console.log(`创建新的部分失败记录：课程=${dto.courseId}, 班级=${dto.classId}, 教师=${teacherId}`);
            const record = this.teachingRecordRepository.create({
                courseId: dto.courseId,
                classId: dto.classId,
                teacherId,
                status: course_teaching_record_entity_1.TeachingStatus.FAILED,
                lockAcquireTime,
                pointsAllocated: partialData.pointsAllocated || 0,
                tasksCreated: partialData.tasksCreated || 0,
                templateApplied: partialData.templateApplied ? course_teaching_record_entity_1.TemplateAppliedStatus.YES : course_teaching_record_entity_1.TemplateAppliedStatus.NO,
                executionDetails: partialData,
                totalExecutionTime,
                errorMessage: `部分操作失败: ${partialData.details?.failedOperations?.map(op => op.operation).join(', ')} 操作失败，共影响${partialData.details?.failedOperations?.reduce((total, op) => total + op.affectedStudents, 0) || 0}名学生`,
            });
            console.log("新记录创建完成，准备保存到数据库");
            console.log("记录信息:", {
                courseId: record.courseId,
                classId: record.classId,
                teacherId: record.teacherId,
                status: record.status,
                errorMessage: record.errorMessage
            });
            const savedRecord = await this.teachingRecordRepository.save(record);
            console.log("新记录保存成功，ID:", savedRecord.id);
            return savedRecord;
        }
        catch (error) {
            console.error("保存部分失败记录时出错:", error);
            throw error;
        }
    }
    async createOrReuseTeachingRecord(dto, teacherId, lockAcquireTime, manager) {
        if (this.getTestRollbackStep() === '5') {
            this.logger.warn('🧪 测试回滚：创建教学记录阶段异常');
            throw new Error('🧪 测试事务回滚：创建教学记录阶段异常');
        }
        const today = new Date().toISOString().split('T')[0];
        const existingFailedRecord = await manager
            .createQueryBuilder(course_teaching_record_entity_1.CourseTeachingRecord, 'record')
            .where('record.courseId = :courseId', { courseId: dto.courseId })
            .andWhere('record.classId = :classId', { classId: dto.classId })
            .andWhere('record.teacherId = :teacherId', { teacherId })
            .andWhere('record.status = :status', { status: course_teaching_record_entity_1.TeachingStatus.FAILED })
            .andWhere('DATE(record.createdAt) = :today', { today })
            .getOne();
        if (existingFailedRecord) {
            this.logger.log(`复用失败记录: 记录ID=${existingFailedRecord.id}, 重置为进行中状态`);
            console.log("更新失败记录为进行中状态");
            await manager.update(course_teaching_record_entity_1.CourseTeachingRecord, existingFailedRecord.id, {
                status: course_teaching_record_entity_1.TeachingStatus.IN_PROGRESS,
                lockAcquireTime,
                errorMessage: null,
                totalExecutionTime: 0,
                pointsAllocated: 0,
                tasksCreated: 0,
                templateApplied: course_teaching_record_entity_1.TemplateAppliedStatus.NO,
                executionDetails: null,
            });
            console.log("失败记录更新成功");
            return await manager.findOne(course_teaching_record_entity_1.CourseTeachingRecord, { where: { id: existingFailedRecord.id } });
        }
        else {
            this.logger.log('没有找到失败记录，创建新的教学记录');
            console.log("创建新的教学记录");
            const record = manager.create(course_teaching_record_entity_1.CourseTeachingRecord, {
                courseId: dto.courseId,
                classId: dto.classId,
                teacherId,
                status: course_teaching_record_entity_1.TeachingStatus.IN_PROGRESS,
                lockAcquireTime,
            });
            return await manager.save(record);
        }
    }
    async createTeachingRecordInTransaction(dto, teacherId, lockAcquireTime, manager) {
        const record = manager.create(course_teaching_record_entity_1.CourseTeachingRecord, {
            courseId: dto.courseId,
            classId: dto.classId,
            teacherId,
            status: course_teaching_record_entity_1.TeachingStatus.IN_PROGRESS,
            lockAcquireTime,
        });
        return await manager.save(record);
    }
    async getClassInfo(classId) {
        try {
            const classInfo = await this.baseUserClassService.findOne(+classId);
            this.logger.log(`获取班级信息成功: ${JSON.stringify(classInfo)}`);
            if (!classInfo) {
                throw new common_1.BadRequestException(`班级ID为${classId}的记录不存在`);
            }
            return {
                id: classInfo.id,
                name: classInfo.className,
                schoolId: classInfo.schoolId,
                grade: classInfo.grade,
                teacherId: classInfo.teacherId,
                assistantTeacherId: classInfo.assistantTeacherId,
                inviteCode: classInfo.inviteCode,
            };
        }
        catch (error) {
            this.logger.error(`获取班级信息失败: ${error.message}`);
            throw new common_1.BadRequestException('获取班级信息失败，请检查班级是否存在');
        }
    }
    async getClassStudents(classId) {
        try {
            const students = await this.userStudentService.findByClass(+classId);
            return students.map(student => ({
                id: student.userId,
                name: `学生${student.userId}`,
                studentId: student.id,
                classId: student.classId,
                schoolId: student.schoolId,
                studentNumber: student.studentNumber,
            }));
        }
        catch (error) {
            this.logger.error(`获取班级学生列表失败: ${error.message}`);
            throw new common_1.BadRequestException('获取班级学生列表失败');
        }
    }
    async executeTeachingFlow(params) {
        const { dto, teacherId, courseInfo, classInfo, students, courseSettings, taskTemplates, manager, testRollbackStep } = params;
        const result = {
            pointsAllocated: 0,
            tasksCreated: 0,
            templateApplied: false,
            createdTasks: [],
            failedOperations: [],
            warningMessages: [],
            details: {
                courseName: courseInfo.title,
                seriesName: courseInfo.seriesName,
                className: classInfo.name,
                studentCount: students.length,
                pointsPerStudent: 0,
                templateName: '',
            }
        };
        if (courseSettings?.requiredPoints > 0) {
            try {
                const pointsResult = await this.allocatePoints(dto.classId, students, courseSettings.requiredPoints, teacherId, manager);
                result.pointsAllocated = pointsResult.totalPoints;
                result.details.pointsPerStudent = courseSettings.requiredPoints;
                console.log("分配积分权限成功，pointsResult：", pointsResult);
                if (pointsResult.insufficientStudents && pointsResult.insufficientStudents.length > 0) {
                    result.failedOperations.push({
                        operation: 'allocatePoints',
                        error: '部分学生积分不足，无法完成积分分配',
                        affectedStudents: pointsResult.insufficientStudents.length
                    });
                    pointsResult.insufficientStudents.forEach(student => {
                        result.warningMessages.push(`学生：${student.studentName}积分不足，当前${student.currentPoints}分，需要${student.requiredPoints}分，已跳过积分分配`);
                    });
                    result.warningMessages.push(`共${pointsResult.insufficientStudents.length}名学生的积分分配失败，请稍后手动处理`);
                }
            }
            catch (error) {
                this.logger.error(`积分分配失败: ${error.message}`);
                result.failedOperations.push({
                    operation: 'allocatePoints',
                    error: error.message || '积分分配过程中发生未知错误',
                    affectedStudents: students.length
                });
                result.warningMessages.push(`积分分配失败，请稍后手动处理`);
            }
        }
        if (courseSettings?.templateId) {
            try {
                if (testRollbackStep === '7') {
                    this.logger.warn('🧪 测试部分失败：权限模板应用阶段异常');
                    throw new teaching_exceptions_1.PartialFailureException({
                        templateApplied: false,
                        pointsAllocated: true,
                        tasksCreated: false,
                        details: {
                            successfulOperations: [
                                { operation: '积分分配', affectedStudents: students.length }
                            ],
                            failedOperations: [
                                { operation: '权限模板应用', affectedStudents: students.length, error: '模板服务暂时不可用' }
                            ]
                        }
                    });
                }
                await this.applyPermissionTemplate(students, courseSettings.templateId, manager);
                result.templateApplied = true;
                result.details.templateName = `模板${courseSettings.templateId}`;
                console.log("应用权限模板成功");
            }
            catch (error) {
                this.logger.error(`权限模板应用失败: ${error.message}`);
                const failedStudents = error.failedStudents || students.slice(0, 3);
                result.failedOperations.push({
                    operation: 'applyTemplate',
                    error: error.message || '模板应用过程中发生未知错误',
                    affectedStudents: failedStudents.length
                });
                failedStudents.forEach(student => {
                    result.warningMessages.push(`学生：${student.name || student.id}的权限模板应用失败：${error.message}`);
                });
                result.warningMessages.push(`共${failedStudents.length}名学生的模板应用失败，请稍后手动处理`);
            }
        }
        if (taskTemplates.length > 0 && courseSettings?.autoCreateTasks) {
            try {
                if (testRollbackStep === '8') {
                    this.logger.warn('🧪 测试部分失败：任务创建阶段异常');
                    throw new teaching_exceptions_1.PartialFailureException({
                        templateApplied: true,
                        pointsAllocated: true,
                        tasksCreated: false,
                        details: {
                            successfulOperations: [
                                { operation: '积分分配', affectedStudents: students.length },
                                { operation: '权限模板应用', affectedStudents: students.length }
                            ],
                            failedOperations: [
                                { operation: '任务创建', affectedStudents: students.length, error: '系统繁忙' }
                            ]
                        }
                    });
                }
                const tasksResult = await this.createTasks(dto.classId, students, taskTemplates, teacherId, manager);
                result.tasksCreated = tasksResult.length;
                result.createdTasks = tasksResult;
                console.log("创建任务成功");
            }
            catch (error) {
                this.logger.error(`任务创建失败: ${error.message}`);
                const failedStudents = error.failedStudents || students.slice(-2);
                result.failedOperations.push({
                    operation: 'createTasks',
                    error: error.message || '任务创建过程中发生未知错误',
                    affectedStudents: failedStudents.length
                });
                failedStudents.forEach((student) => {
                    result.warningMessages.push(`学生：${student.name || student.id}的任务创建失败：${error.message}`);
                });
                result.warningMessages.push(`共${failedStudents.length}名学生的任务创建失败，请稍后手动处理`);
            }
        }
        if (result.failedOperations.length > 0) {
            this.logger.warn(`检测到部分失败，失败操作数: ${result.failedOperations.length}`);
            const partialFailureData = {
                success: true,
                teachingRecordId: null,
                pointsAllocated: result.pointsAllocated,
                tasksCreated: result.tasksCreated,
                templateApplied: result.templateApplied,
                executionTime: new Date().toISOString(),
                lockAcquireTime: 0,
                totalExecutionTime: 0,
                details: {
                    ...result.details,
                    createdTasks: result.createdTasks,
                    failedOperations: result.failedOperations,
                    warningMessages: result.warningMessages
                }
            };
            throw new teaching_exceptions_1.PartialFailureException(partialFailureData);
        }
        return result;
    }
    async allocatePoints(classId, students, pointsPerStudent, teacherId, manager) {
        const startTime = Date.now();
        this.logger.log(`步骤1: 开始积分分配: 班级ID=${classId}, 教师ID=${teacherId}, 学生数=${students.length}, 每人积分=${pointsPerStudent}`);
        console.log("开始分配积分");
        const results = [];
        let successCount = 0;
        let failedCount = 0;
        const studentIds = students.map(s => s.id);
        this.logger.debug(`提取学生ID列表: [${studentIds.join(', ')}]`);
        this.logger.debug('步骤1: 开始批量检查学生是否存在');
        const existingStudents = await manager.query(`SELECT id FROM user_info WHERE id IN (${studentIds.map(() => '?').join(',')})`, studentIds);
        const existingStudentIds = new Set(existingStudents.map(s => s.id));
        this.logger.debug(`学生存在性检查完成: 查询到${existingStudents.length}个有效学生`);
        for (const studentId of studentIds) {
            if (!existingStudentIds.has(studentId)) {
                this.logger.error(`学生验证失败: 学生ID ${studentId} 不存在于user_info表`);
                throw new Error(`学生ID ${studentId} 不存在`);
            }
        }
        this.logger.debug('步骤1完成: 所有学生都存在于系统中');
        this.logger.debug('步骤2: 开始批量获取学生总的特殊套餐积分信息');
        const studentPointsResult = await manager.query(`SELECT 
        userId,
        COALESCE(SUM(points), 0) AS total
       FROM user_package
       WHERE status != 0 
         AND assignType = 2 
         AND userId IN (${studentIds.map(() => '?').join(',')})
         AND userId IS NOT NULL
       GROUP BY userId`, studentIds);
        const studentPointsMap = new Map();
        this.logger.debug(`学生积分查询结果: 查询到${studentPointsResult.length}个学生的积分记录`);
        studentPointsResult.forEach(row => {
            studentPointsMap.set(row.userId, Number(row.total));
            this.logger.debug(`学生ID ${row.userId} 当前总的特殊套餐积分: ${row.total}`);
        });
        this.logger.debug('步骤2完成: 学生总的特殊套餐积分信息获取完毕');
        this.logger.debug('步骤3: 找该学生所有已分配的权限记录');
        const existingPermissions = await manager.query(`SELECT
        id,
        studentUserId,
        teacherUserId,
        availablePoints,
        expireTime,
        status,
        createTime,
        remark
       FROM user_points_permission
       WHERE studentUserId IN (${studentIds.map(() => '?').join(',')})
         AND status = 1
         AND (expireTime IS NULL OR expireTime > NOW())
       ORDER BY studentUserId, createTime DESC`, [...studentIds]);
        const teacherPermissionsMap = new Map();
        this.logger.debug(`权限记录查询结果: 查询到${existingPermissions.length}个现有权限记录`);
        const studentPermissionsMap = new Map();
        existingPermissions.forEach(perm => {
            const studentId = perm.studentUserId;
            if (!studentPermissionsMap.has(studentId)) {
                studentPermissionsMap.set(studentId, []);
            }
            studentPermissionsMap.get(studentId).push(perm);
        });
        const validStudents = [];
        const insufficientStudents = [];
        for (const student of students) {
            const studentId = student.id;
            const studentTotalPoints = studentPointsMap.get(studentId) || 0;
            const openTest = false;
            if ((this.getTestRollbackStep() === '6' || openTest || this.Allerror) && students.indexOf(student) < 3) {
                console.log(`正在模拟学生ID ${studentId} 积分不足`);
                insufficientStudents.push({
                    studentId,
                    studentName: student.name || `学生${studentId}`,
                    currentPoints: 0,
                    requiredPoints: pointsPerStudent,
                    errorType: 'test_simulation',
                    errorMessage: '测试模拟积分不足'
                });
                continue;
            }
            const permissions = studentPermissionsMap.get(studentId) || [];
            const totalAllocatedPoints = permissions.reduce((sum, permission) => {
                if (permission.status === 1 && (!permission.expireTime || new Date(permission.expireTime) > new Date())) {
                    const newSum = Number(sum) + Number(permission.availablePoints || 0);
                    this.logger.debug(`学生ID ${studentId} 累加积分: ${sum} + ${permission.availablePoints} = ${newSum}`);
                    return newSum;
                }
                return Number(sum);
            }, 0);
            this.logger.debug(`学生ID ${studentId} 总已分配积分: ${totalAllocatedPoints}, 本次分配积分: ${pointsPerStudent}，能够分配的总特殊套餐积分为${studentTotalPoints}`);
            const totalAfterAllocation = Number(totalAllocatedPoints) + Number(pointsPerStudent);
            this.logger.debug(`学生ID ${studentId} 分配后总积分: ${totalAfterAllocation}, 特殊套餐总额度: ${studentTotalPoints}, 剩余可分配: ${studentTotalPoints - totalAfterAllocation}`);
            if (totalAfterAllocation > studentTotalPoints) {
                this.logger.warn(`学生ID ${studentId} 分配积分超过限制，学生特殊套餐总积分${Number(studentTotalPoints)}，` +
                    `已分配${Number(totalAllocatedPoints)}，本次分配${Number(pointsPerStudent)}，` +
                    `分配后总积分将达到${Number(totalAfterAllocation)}`);
                insufficientStudents.push({
                    studentId,
                    studentName: student.name || `学生${studentId}`,
                    currentPoints: studentTotalPoints,
                    requiredPoints: pointsPerStudent,
                    errorType: 'allocation_limit_exceeded',
                    errorMessage: `分配积分超过限制，已分配${totalAllocatedPoints}，本次分配${pointsPerStudent}，总额度${studentTotalPoints}`
                });
                continue;
            }
            validStudents.push(student);
            const teacherPermissions = permissions.filter(p => p.teacherUserId === teacherId);
            const existingTeacherPermission = teacherPermissions.length > 0 ? teacherPermissions[0] : null;
            teacherPermissionsMap.set(studentId, {
                id: existingTeacherPermission ? existingTeacherPermission.id : null,
                availablePoints: totalAllocatedPoints,
                teacherAvailablePoints: existingTeacherPermission ? Number(existingTeacherPermission.availablePoints || 0) : 0,
                records: permissions,
                teacherRecords: teacherPermissions
            });
        }
        this.logger.debug(`步骤3完成: 积分检查完毕，有效学生${validStudents.length}个，积分不足学生${insufficientStudents.length}个`);
        if (validStudents.length === 0) {
            this.logger.warn('所有学生积分都不足，无法进行积分分配');
            return {
                totalPoints: 0,
                successCount: 0,
                failCount: students.length,
                results: students.map(student => ({
                    studentId: student.id,
                    status: 'failed',
                    error: '积分不足'
                })),
                insufficientStudents
            };
        }
        this.logger.debug('步骤4: 开始构建积分流水记录');
        const pointsInsertValues = validStudents.map(student => {
            const studentId = student.id;
            const studentTotalPoints = studentPointsMap.get(studentId) || 0;
            const newTotalPoints = studentTotalPoints - pointsPerStudent;
            this.logger.debug(`构建学生ID ${studentId} 积分流水: 扣除${pointsPerStudent}分, 余额${newTotalPoints}分`);
            return `(${studentId}, ${-pointsPerStudent}, ${newTotalPoints}, 2, 2, '老师${teacherId}一键上课自动分配积分 - 班级ID: ${classId}', '${teacherId}', NOW(), NOW())`;
        }).join(', ');
        this.logger.debug('步骤5: 开始批量插入积分流水记录');
        await manager.query(`INSERT INTO user_points (userId, pointsValue, totalPoints, type, source, remark, operator, createTime, updateTime)
       VALUES ${pointsInsertValues}`);
        this.logger.debug(`步骤5完成: 成功插入${validStudents.length}条积分流水记录`);
        this.logger.debug('步骤6: 开始处理权限记录');
        const updatePromises = [];
        const newPermissions = [];
        for (const student of validStudents) {
            const studentId = student.id;
            const existingPerm = teacherPermissionsMap.get(studentId);
            if (existingPerm && existingPerm.id) {
                const newAvailablePoints = existingPerm.teacherAvailablePoints + pointsPerStudent;
                this.logger.debug(`学生ID ${studentId} 更新教师权限: ${existingPerm.teacherAvailablePoints} + ${pointsPerStudent} = ${newAvailablePoints}`);
                updatePromises.push(manager.query('UPDATE user_points_permission SET availablePoints = ?, updateTime = NOW() WHERE id = ?', [newAvailablePoints, existingPerm.id]));
            }
            else {
                this.logger.debug(`学生ID ${studentId} 创建新权限记录: ${pointsPerStudent}分`);
                newPermissions.push(`(${studentId}, ${teacherId}, ${pointsPerStudent}, 1, '一键上课自动分配积分 - 班级ID: ${classId}', NOW(), NOW())`);
            }
            results.push({ studentId, status: 'success' });
            successCount++;
        }
        for (const insufficientStudent of insufficientStudents) {
            results.push({
                studentId: insufficientStudent.studentId,
                status: 'failed',
                error: `积分不足，当前${insufficientStudent.currentPoints}分，需要${insufficientStudent.requiredPoints}分`
            });
            failedCount++;
        }
        if (updatePromises.length > 0) {
            this.logger.debug(`执行批量更新: ${updatePromises.length}个权限记录`);
            await Promise.all(updatePromises);
            this.logger.debug('批量更新权限记录完成');
        }
        if (newPermissions.length > 0) {
            this.logger.debug(`执行批量插入: ${newPermissions.length}个新权限记录`);
            await manager.query(`INSERT INTO user_points_permission (studentUserId, teacherUserId, availablePoints, status, remark, createTime, updateTime)
         VALUES ${newPermissions.join(', ')}`);
            this.logger.debug('批量插入新权限记录完成');
        }
        this.logger.debug('步骤6完成: 权限记录处理完毕');
        const totalPoints = validStudents.length * pointsPerStudent;
        const executionTime = Date.now() - startTime;
        this.logger.log(`步骤7: 积分分配操作完成统计`);
        this.logger.log(`✅ 积分分配成功完成:`);
        this.logger.log(`   - 班级ID: ${classId}`);
        this.logger.log(`   - 教师ID: ${teacherId}`);
        this.logger.log(`   - 总分配积分: ${totalPoints}分`);
        this.logger.log(`   - 每人积分: ${pointsPerStudent}分`);
        this.logger.log(`   - 成功人数: ${successCount}人`);
        this.logger.log(`   - 失败人数: ${failedCount}人`);
        this.logger.log(`   - 执行耗时: ${executionTime}ms`);
        this.logger.log(`   - 平均每人耗时: ${Math.round(executionTime / students.length)}ms`);
        return {
            executionTime,
            totalPoints,
            successCount,
            failedCount,
            results,
            insufficientStudents
        };
    }
    async applyPermissionTemplate(students, templateId, manager) {
        try {
            let successCount = 0;
            let failCount = 0;
            const results = [];
            const roleId = 1;
            const role = await manager.query('SELECT id, status FROM user_role WHERE id = ? AND status = 1', [roleId]);
            this.logger.debug(`验证角色是否存在user_role表且启用: 角色ID=${roleId}, 结果=${role.length > 0 ? '存在' : '不存在'}`);
            if (!role.length) {
                this.logger.error(`学生角色不存在或已被禁用: 角色ID=${roleId}`);
                throw new Error('学生角色不存在或已被禁用');
            }
            const failedStudents = [];
            for (let i = 0; i < students.length; i++) {
                const student = students[i];
                try {
                    const userId = student.id;
                    this.logger.debug(`开始处理学生ID ${userId} 的权限模板应用: 待分配的模板ID=${templateId}`);
                    const openTest = false;
                    if ((this.getTestRollbackStep() === '7' || openTest || this.Allerror) && i < 3) {
                        console.log("正在模拟前3个学生权限模板应用失败");
                        failedStudents.push(student);
                        throw new Error(`模板服务暂时不可用`);
                    }
                    const existingRelation = await manager.query('SELECT id, templateId FROM user_join_role WHERE userId = ? AND roleId = ?', [userId, roleId]);
                    if (existingRelation.length > 0) {
                        this.logger.debug(`学生ID ${userId} 已存在user_join_role记录: ID=${existingRelation[0].id}, 当前模板ID=${existingRelation[0].templateId}`);
                        await manager.query('UPDATE user_join_role SET templateId = ?, originalTemplateId = ?, updateTime = NOW() WHERE userId = ? AND roleId = ?', [templateId, templateId, userId, roleId]);
                    }
                    else {
                        this.logger.debug(`学生ID ${userId} 不存在user_join_role记录`);
                        await manager.query(`INSERT INTO user_join_role (userId, roleId, templateId, originalTemplateId, createTime, updateTime)
               VALUES (?, ?, ?, ?, NOW(), NOW())`, [userId, roleId, templateId, templateId]);
                    }
                    results.push({
                        userId,
                        roleId,
                        success: true,
                        message: '权限模板应用成功'
                    });
                    successCount++;
                }
                catch (error) {
                    this.logger.error(`学生ID ${student.id} 权限模板应用失败: ${error.message}`);
                    results.push({
                        userId: student.id,
                        roleId,
                        success: false,
                        message: error.message
                    });
                    failCount++;
                }
            }
            this.logger.log(`事务中权限模板应用完成: 模板ID=${templateId}, 学生数=${students.length}, 成功${successCount}人, 失败${failCount}人`);
            if (failedStudents.length > 0) {
                this.logger.warn(`权限模板应用部分失败，${failedStudents.length}名学生失败`);
                throw new teaching_exceptions_1.PartialFailureException({
                    templateApplied: false,
                    pointsAllocated: true,
                    tasksCreated: false,
                    details: {
                        successfulOperations: [
                            { operation: '积分分配', affectedStudents: students.length }
                        ],
                        failedOperations: [
                            {
                                operation: '权限模板应用',
                                affectedStudents: failedStudents.length,
                                error: `${failedStudents.length}名学生权限模板应用失败`,
                                failedStudents: failedStudents.map(s => ({ id: s.id, name: s.name || `学生${s.id}` }))
                            }
                        ]
                    }
                });
            }
            return {
                successCount,
                failCount,
                results
            };
        }
        catch (error) {
            this.logger.error(`权限模板应用失败: ${error.message}`);
            throw error;
        }
    }
    async createTasks(classId, students, taskTemplates, teacherId, manager) {
        const createdTasks = [];
        for (const template of taskTemplates) {
            try {
                const startDate = new Date();
                const endDate = new Date(Date.now() + template.durationDays * 24 * 60 * 60 * 1000);
                const validStudentIds = students.map(s => s.id).filter(id => id);
                const taskResult = await manager.query(`INSERT INTO teacher_task (
            taskName, taskDescription, teacherId, classId, taskType, priority,
            startDate, endDate, taskContent, allowLateSubmission, attachments,
            workIdsStr, status, isPublic, createTime, updateTime
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`, [
                    template.taskName,
                    template.taskDescription,
                    teacherId,
                    classId,
                    teacher_task_entity_1.TaskType.GRAPHIC,
                    teacher_task_entity_1.Priority.IMPORTANT,
                    startDate,
                    endDate,
                    template.taskDescription || '',
                    0,
                    JSON.stringify(template.attachments || []),
                    template.workIdsStr || null,
                    teacher_task_entity_1.TaskStatus.NOT_STARTED,
                    0
                ]);
                this.logger.debug(`任务创建成功: 任务名=${template.taskName}, 任务ID=${taskResult.insertId}`);
                const taskId = taskResult.insertId;
                if (validStudentIds.length > 0) {
                    let successfulStudentIds = validStudentIds;
                    const failedStudentIds = [];
                    const openTest = false;
                    if (this.getTestRollbackStep() === '8' || openTest || this.Allerror) {
                        successfulStudentIds = validStudentIds.slice(0, -2);
                        failedStudentIds.push(...validStudentIds.slice(-2));
                        if (failedStudentIds.length > 0) {
                            const failedStudents = students.filter(s => failedStudentIds.includes(s.id));
                            this.logger.warn(`任务创建部分失败，${failedStudents.length}名学生失败`);
                            throw new teaching_exceptions_1.PartialFailureException({
                                templateApplied: true,
                                pointsAllocated: true,
                                tasksCreated: false,
                                details: {
                                    successfulOperations: [
                                        { operation: '积分分配', affectedStudents: students.length },
                                        { operation: '权限模板应用', affectedStudents: students.length }
                                    ],
                                    failedOperations: [
                                        {
                                            operation: '任务创建',
                                            affectedStudents: failedStudents.length,
                                            error: '系统繁忙，任务创建失败',
                                            failedStudents: failedStudents.map(s => ({ id: s.id, name: s.name || `学生${s.id}` }))
                                        }
                                    ]
                                }
                            });
                        }
                    }
                    if (successfulStudentIds.length > 0) {
                        const assignmentValues = successfulStudentIds.map(studentId => `(${taskId}, ${studentId}, 0, NOW(), NOW())`).join(', ');
                        await manager.query(`INSERT INTO teacher_task_assignment (taskId, studentId, taskStatus, createTime, updateTime)
               VALUES ${assignmentValues}`);
                    }
                }
                if (template.selfAssessmentItems && Array.isArray(template.selfAssessmentItems)) {
                    for (let index = 0; index < template.selfAssessmentItems.length; index++) {
                        const item = template.selfAssessmentItems[index];
                        const content = item.content || item;
                        const sequence = item.sequence || index + 1;
                        await manager.query(`INSERT INTO task_self_assessment_item (task_id, content, sequence, score_sum, rated_count)
               VALUES (?, ?, ?, ?, ?)`, [taskId, content, sequence, 0, 0]);
                    }
                }
                createdTasks.push({
                    taskId,
                    taskName: template.taskName,
                    startDate: startDate.toISOString(),
                    endDate: endDate.toISOString(),
                    assignedStudents: validStudentIds.length,
                });
                this.logger.log(`事务中成功创建任务: ${template.taskName}, ID=${taskId}`);
                this.logger.log(`   - 任务开始时间: ${startDate.toISOString()}`);
                this.logger.log(`   - 任务结束时间: ${endDate.toISOString()}`);
                this.logger.log(`   - 任务分配学生数: ${validStudentIds.length}`);
            }
            catch (error) {
                this.logger.error(`任务创建失败: ${template.taskName}, ${error.message}`);
                throw new Error(`任务创建失败: ${template.taskName}`);
            }
        }
        return createdTasks;
    }
    async updateTeachingRecordSuccess(record, executionResult, totalExecutionTime, manager) {
        if (this.getTestRollbackStep() === '9') {
            this.logger.warn('🧪 测试部分失败：更新记录阶段异常');
            throw new teaching_exceptions_1.PartialFailureException({
                templateApplied: true,
                pointsAllocated: true,
                tasksCreated: true,
                recordUpdated: false,
                details: {
                    successfulOperations: [
                        { operation: '积分分配', affectedStudents: 3 },
                        { operation: '权限模板应用', affectedStudents: 3 },
                        { operation: '任务创建', affectedStudents: 3 }
                    ],
                    failedOperations: [
                        { operation: '记录更新', affectedStudents: 0, error: '数据库更新失败' }
                    ]
                }
            });
        }
        const updateData = {
            status: course_teaching_record_entity_1.TeachingStatus.SUCCESS,
            pointsAllocated: executionResult.pointsAllocated,
            tasksCreated: executionResult.tasksCreated,
            templateApplied: executionResult.templateApplied ? course_teaching_record_entity_1.TemplateAppliedStatus.YES : course_teaching_record_entity_1.TemplateAppliedStatus.NO,
            executionDetails: executionResult,
            totalExecutionTime,
        };
        await manager.update(course_teaching_record_entity_1.CourseTeachingRecord, record.id, updateData);
    }
    async updateTeachingRecordFailed(record, errorMessage, totalExecutionTime, manager) {
        const updateData = {
            status: course_teaching_record_entity_1.TeachingStatus.FAILED,
            errorMessage,
            totalExecutionTime,
        };
        await manager.update(course_teaching_record_entity_1.CourseTeachingRecord, record.id, updateData);
    }
    buildSuccessResponse(record, executionResult, lockAcquireTime, totalExecutionTime) {
        return {
            success: true,
            teachingRecordId: record.id,
            pointsAllocated: executionResult.pointsAllocated,
            tasksCreated: executionResult.tasksCreated,
            templateApplied: executionResult.templateApplied,
            executionTime: new Date().toISOString(),
            lockAcquireTime,
            totalExecutionTime,
            details: {
                ...executionResult.details,
                createdTasks: executionResult.createdTasks,
                failedOperations: executionResult.failedOperations,
                warningMessages: executionResult.warningMessages,
            },
        };
    }
    async getCourseSettings(courseId) {
        console.log("进入获取课程设置信息接口调用");
        const course = await this.courseRepository
            .createQueryBuilder('course')
            .leftJoinAndSelect('course.series', 'series')
            .where('course.id = :courseId', { courseId })
            .getOne();
        console.log("获取课程基本信息:", course);
        if (!course) {
            throw new common_1.BadRequestException('课程不存在');
        }
        const courseSettings = await this.courseSettingsRepository.findOne({
            where: { courseId },
        });
        const taskTemplates = await this.taskTemplateRepository.find({
            where: { courseId },
            order: { id: 'ASC' },
        });
        return this.buildCourseSettingsResponse(course, courseSettings, taskTemplates);
    }
    buildCourseSettingsResponse(course, courseSettings, taskTemplates) {
        const contentInfo = {
            hasVideo: course.hasVideo,
            hasDocument: course.hasDocument,
            hasAudio: course.hasAudio,
            videoDuration: course.videoDuration,
            videoDurationLabel: this.formatVideoDuration(course.videoDuration),
            videoName: this.extractVideoName(course.contentConfig),
            resourcesCount: this.calculateResourcesCount(course),
        };
        const settings = {
            templateId: courseSettings?.templateId || 0,
            templateName: courseSettings?.templateId ? `模板${courseSettings.templateId}` : '无模板',
            requiredPoints: courseSettings?.requiredPoints || 0,
            autoCreateTasks: courseSettings?.autoCreateTasks || 0,
            autoCreateTasksLabel: courseSettings?.autoCreateTasks ? '是' : '否',
        };
        const taskTemplateInfos = taskTemplates.map(template => ({
            id: template.id,
            taskName: template.taskName,
            taskDescription: template.taskDescription,
            durationDays: template.durationDays,
            status: 1,
            statusLabel: '启用',
            attachmentsCount: this.getAttachmentsCount(template.attachments),
            assessmentItemsCount: this.getAssessmentItemsCount(template.selfAssessmentItems),
            firstAttachmentType: this.getFirstAttachmentType(template.attachments),
        }));
        const preview = {
            willAllocatePoints: (courseSettings?.requiredPoints || 0) > 0,
            pointsPerStudent: courseSettings?.requiredPoints || 0,
            willApplyTemplate: !!courseSettings?.templateId,
            willCreateTasks: !!(courseSettings?.autoCreateTasks && taskTemplates.length > 0),
            tasksCount: courseSettings?.autoCreateTasks ? taskTemplates.length : 0,
        };
        return {
            courseId: course.id,
            courseName: course.title,
            seriesName: course.series?.title || '未知系列',
            contentInfo,
            settings,
            taskTemplates: taskTemplateInfos,
            preview,
        };
    }
    formatVideoDuration(seconds) {
        return calculation_utils_1.CalculationUtils.formatVideoDuration(seconds);
    }
    extractVideoName(contentConfig) {
        if (!contentConfig || !contentConfig.video)
            return '';
        return contentConfig.video.name || contentConfig.video.url?.split('/').pop() || '';
    }
    calculateResourcesCount(course) {
        let count = 0;
        if (course.hasVideo)
            count++;
        if (course.hasDocument)
            count++;
        if (course.hasAudio)
            count++;
        if (course.additionalResources && Array.isArray(course.additionalResources)) {
            count += course.additionalResources.length;
        }
        return count;
    }
    getAttachmentsCount(attachments) {
        if (!attachments)
            return 0;
        if (Array.isArray(attachments))
            return attachments.length;
        return 0;
    }
    getAssessmentItemsCount(assessmentItems) {
        if (!assessmentItems)
            return 0;
        if (Array.isArray(assessmentItems))
            return assessmentItems.length;
        return 0;
    }
    getFirstAttachmentType(attachments) {
        if (!attachments || !Array.isArray(attachments) || attachments.length === 0) {
            return '';
        }
        const firstAttachment = attachments[0];
        return firstAttachment.type || 'file';
    }
    async getTeachingRecords(query) {
        const { page = 1, pageSize = 10 } = query;
        const queryBuilder = this.teachingRecordRepository
            .createQueryBuilder('record')
            .leftJoinAndSelect('record.course', 'course')
            .leftJoinAndSelect('course.series', 'series');
        this.applyFilters(queryBuilder, query);
        queryBuilder.orderBy('record.createdAt', 'DESC');
        const total = await queryBuilder.getCount();
        const paginationInfo = calculation_utils_1.CalculationUtils.calculatePaginationInfo(page, pageSize, total);
        queryBuilder
            .skip(paginationInfo.skip)
            .take(pageSize);
        const records = await queryBuilder.getMany();
        const list = await this.buildTeachingRecordList(records);
        return {
            list,
            pagination: {
                page: paginationInfo.page,
                pageSize: paginationInfo.pageSize,
                total: paginationInfo.total,
                totalPages: paginationInfo.totalPages,
                hasNext: paginationInfo.hasNext,
                hasPrev: paginationInfo.hasPrev,
            },
        };
    }
    applyFilters(queryBuilder, query) {
        if (query.teacherId) {
            queryBuilder.andWhere('record.teacherId = :teacherId', { teacherId: query.teacherId });
        }
        if (query.courseId) {
            queryBuilder.andWhere('record.courseId = :courseId', { courseId: query.courseId });
        }
        if (query.classId) {
            queryBuilder.andWhere('record.classId = :classId', { classId: query.classId });
        }
        if (query.status !== undefined && query.status !== null) {
            queryBuilder.andWhere('record.status = :status', { status: query.status });
        }
        if (query.startDate) {
            queryBuilder.andWhere('DATE(record.createdAt) >= :startDate', { startDate: query.startDate });
        }
        if (query.endDate) {
            queryBuilder.andWhere('DATE(record.createdAt) <= :endDate', { endDate: query.endDate });
        }
    }
    async buildTeachingRecordList(records) {
        if (records.length === 0) {
            return [];
        }
        const classIds = [...new Set(records.map(r => r.classId))];
        const teacherIds = [...new Set(records.map(r => r.teacherId))];
        const [classInfoMap, teacherInfoMap] = await Promise.all([
            this.batchGetClassInfo(classIds),
            this.batchGetTeacherInfo(teacherIds)
        ]);
        return records.map(record => {
            const classInfo = classInfoMap.get(record.classId);
            const teacherInfo = teacherInfoMap.get(record.teacherId);
            return {
                id: record.id,
                courseId: record.courseId,
                courseName: record.course?.title || '未知课程',
                seriesName: record.course?.series?.title || '未知系列',
                classId: record.classId,
                className: classInfo?.name || `班级${record.classId}`,
                teacherId: record.teacherId,
                teacherName: teacherInfo?.name || `教师${record.teacherId}`,
                status: record.status,
                statusLabel: course_teaching_record_entity_1.TeachingStatusUtils.getStatusLabel(record.status),
                pointsAllocated: record.pointsAllocated,
                tasksCreated: record.tasksCreated,
                templateApplied: record.templateApplied,
                lockAcquireTime: record.lockAcquireTime,
                totalExecutionTime: record.totalExecutionTime,
                errorMessage: record.errorMessage,
                createdAt: record.createdAt.toISOString(),
                updatedAt: record.updatedAt.toISOString(),
            };
        });
    }
    async batchGetClassInfo(classIds) {
        const classInfoMap = new Map();
        if (classIds.length === 0) {
            return classInfoMap;
        }
        try {
            const classes = await this.baseUserClassService.findByIds(classIds);
            classes.forEach(classInfo => {
                classInfoMap.set(classInfo.id, {
                    id: classInfo.id,
                    name: classInfo.className,
                });
            });
            classIds.forEach(classId => {
                if (!classInfoMap.has(classId)) {
                    classInfoMap.set(classId, {
                        id: classId,
                        name: `班级${classId}`,
                    });
                    this.logger.warn(`班级信息不存在: classId=${classId}`);
                }
            });
        }
        catch (error) {
            this.logger.error(`批量获取班级信息失败: ${error.message}`);
            classIds.forEach(classId => {
                classInfoMap.set(classId, {
                    id: classId,
                    name: `班级${classId}`,
                });
            });
        }
        return classInfoMap;
    }
    async getClassInfoForRecord(classId) {
        try {
            const classInfo = await this.baseUserClassService.findOne(+classId);
            if (!classInfo) {
                this.logger.warn(`班级信息不存在: classId=${classId}`);
                return {
                    id: classId,
                    name: `班级${classId}`,
                };
            }
            return {
                id: classInfo.id,
                name: classInfo.className,
            };
        }
        catch (error) {
            this.logger.warn(`获取班级信息失败: classId=${classId}, ${error.message}`);
            return {
                id: classId,
                name: `班级${classId}`,
            };
        }
    }
    async batchGetTeacherInfo(teacherIds) {
        const teacherInfoMap = new Map();
        if (teacherIds.length === 0) {
            return teacherInfoMap;
        }
        try {
            const teachers = await this.userInfoService.findByIds(teacherIds);
            teachers.forEach(teacher => {
                teacherInfoMap.set(teacher.id, {
                    id: teacher.id,
                    name: teacher.nickName || `教师${teacher.id}`,
                });
            });
            teacherIds.forEach(teacherId => {
                if (!teacherInfoMap.has(teacherId)) {
                    teacherInfoMap.set(teacherId, {
                        id: teacherId,
                        name: `教师${teacherId}`,
                    });
                    this.logger.warn(`教师信息不存在: teacherId=${teacherId}`);
                }
            });
        }
        catch (error) {
            this.logger.error(`批量获取教师信息失败: ${error.message}`);
            teacherIds.forEach(teacherId => {
                teacherInfoMap.set(teacherId, {
                    id: teacherId,
                    name: `教师${teacherId}`,
                });
            });
        }
        return teacherInfoMap;
    }
    async getTeacherInfoForRecord(teacherId) {
        try {
            return {
                id: teacherId,
                name: `教师${teacherId}`,
            };
        }
        catch (error) {
            this.logger.warn(`获取教师信息失败: teacherId=${teacherId}, ${error.message}`);
            return {
                id: teacherId,
                name: `教师${teacherId}`,
            };
        }
    }
    async gatherTeachingContext(dto, teacherId, manager) {
        if (this.getTestRollbackStep() === '3') {
            this.logger.warn('🧪 测试回滚：获取课程信息阶段异常');
            throw new Error('🧪 测试事务回滚：获取课程信息阶段异常');
        }
        const [courseInfo, classInfo, students, courseSettings, taskTemplates] = await Promise.all([
            this.getCourseInfoWithValidation(dto.courseId, manager),
            this.getClassInfoWithValidation(dto.classId),
            this.getClassStudentsWithValidation(dto.classId),
            this.getCourseSettingsWithValidation(dto.courseId, manager),
            this.getTaskTemplatesForCourse(dto.courseId, manager)
        ]);
        return {
            courseInfo,
            classInfo,
            students,
            courseSettings,
            taskTemplates,
        };
    }
    async executeOneClickStartWithTransaction(dto, teacherId, lockAcquireTime, startTime) {
        return this.dataSource.transaction(async (manager) => {
            console.log("2.开启事物....");
            let teachingRecord = null;
            try {
                await this.validateTeachingPermissions(dto, teacherId, manager);
                const { courseInfo, classInfo, students, courseSettings, taskTemplates } = await this.gatherTeachingContext(dto, teacherId, manager);
                this.validateCourseSettings(dto.courseId, courseSettings, taskTemplates);
                teachingRecord = await this.createTeachingRecordInTransaction(dto, teacherId, lockAcquireTime, manager);
                const executionResult = await this.executeTeachingFlow({
                    dto,
                    teacherId,
                    courseInfo,
                    classInfo,
                    students,
                    courseSettings,
                    taskTemplates,
                    manager,
                    testRollbackStep: this.getTestRollbackStep() || undefined
                });
                const totalExecutionTime = Date.now() - startTime;
                await this.updateTeachingRecordSuccess(teachingRecord, executionResult, totalExecutionTime, manager);
                console.log("11.提交事物....");
                console.log("12.释放分布式锁....");
                return this.buildSuccessResponse(teachingRecord, executionResult, lockAcquireTime, totalExecutionTime);
            }
            catch (error) {
                console.log("提交事物失败，一键上课失败，事务进行回滚，更新教学记录为失败状态....");
                console.log("失败对象error", error);
                if (error instanceof teaching_exceptions_1.ConcurrencyConflictException ||
                    error instanceof teaching_exceptions_1.DuplicateOperationException ||
                    error instanceof teaching_exceptions_1.CourseNotFoundOrNotPublishedException ||
                    error instanceof teaching_exceptions_1.InsufficientTeacherPermissionException ||
                    error instanceof teaching_exceptions_1.EmptyClassException ||
                    error instanceof teaching_exceptions_1.IncompleteSettingsException ||
                    error instanceof teaching_exceptions_1.PartialFailureException ||
                    error instanceof teaching_exceptions_1.InsufficientStudentPointsException) {
                    throw error;
                }
                throw new common_1.InternalServerErrorException('一键上课执行失败，请稍后重试');
            }
        });
    }
};
exports.TeachingService = TeachingService;
exports.TeachingService = TeachingService = TeachingService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(course_teaching_record_entity_1.CourseTeachingRecord)),
    __param(1, (0, typeorm_1.InjectRepository)(course_entity_1.Course)),
    __param(2, (0, typeorm_1.InjectRepository)(course_series_entity_1.CourseSeries)),
    __param(3, (0, typeorm_1.InjectRepository)(course_settings_entity_1.CourseSettings)),
    __param(4, (0, typeorm_1.InjectRepository)(task_template_entity_1.TaskTemplate)),
    __param(5, (0, typeorm_1.InjectRepository)(teacher_task_entity_1.TeacherTask)),
    __param(6, (0, typeorm_1.InjectRepository)(teacher_task_assignment_entity_1.TeacherTaskAssignment)),
    __param(7, (0, typeorm_1.InjectRepository)(task_self_assessment_item_entity_1.TaskSelfAssessmentItem)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.DataSource,
        lock_manager_1.LockManager,
        user_class_service_1.UserClassService,
        user_student_service_1.UserStudentService,
        user_info_service_1.UserInfoService,
        web_point_permission_service_1.WebPointPermissionService])
], TeachingService);
//# sourceMappingURL=teaching.service.js.map