"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/course-plaza/page",{

/***/ "(app-pages-browser)/./app/(main)/course-plaza/page.tsx":
/*!******************************************!*\
  !*** ./app/(main)/course-plaza/page.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CoursePlaza; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/./node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _lib_api_course__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api/course */ \"(app-pages-browser)/./lib/api/course.ts\");\n/* harmony import */ var _components_CourseDetailView__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./components/CourseDetailView */ \"(app-pages-browser)/./app/(main)/course-plaza/components/CourseDetailView.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// 动态导入 AIBackground 组件，并禁用 SSR\nconst AIBackground = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_components_AIBackground_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/AIBackground */ \"(app-pages-browser)/./components/AIBackground.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\(main)\\\\course-plaza\\\\page.tsx -> \" + \"@/components/AIBackground\"\n        ]\n    },\n    ssr: false\n});\n_c = AIBackground;\nfunction CoursePlaza() {\n    _s();\n    const [seriesCourses, setSeriesCourses] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [communityCourses, setCommunityCourses] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true);\n    const [showAllOfficialCourses, setShowAllOfficialCourses] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [loadingMore, setLoadingMore] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [showAllCommunityCourses, setShowAllCommunityCourses] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [loadingMoreCommunity, setLoadingMoreCommunity] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [selectedCourse, setSelectedCourse] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [showCourseDetail, setShowCourseDetail] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [showOfficialSidebar, setShowOfficialSidebar] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [showCommunitySidebar, setShowCommunitySidebar] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    // 处理展示更多官方课程 - 打开侧边栏\n    const handleShowMoreOfficialCourses = ()=>{\n        setShowOfficialSidebar(true);\n    };\n    // 处理展示更多社区课程 - 打开侧边栏\n    const handleShowMoreCommunityCourses = ()=>{\n        setShowCommunitySidebar(true);\n    };\n    // 处理课程卡片点击\n    const handleCourseClick = (course)=>{\n        console.log(\"\\uD83C\\uDFAF 点击课程:\", course);\n        console.log(\"\\uD83D\\uDD0D 点击课程的seriesId:\", course.seriesId);\n        console.log(\"\\uD83D\\uDD0D 点击课程的完整数据:\", JSON.stringify(course, null, 2));\n        setSelectedCourse(course);\n        setShowCourseDetail(true);\n    };\n    // 返回课程列表\n    const handleBackToCourseList = ()=>{\n        setShowCourseDetail(false);\n        setSelectedCourse(null);\n    };\n    // 获取要显示的官方课程列表\n    const getDisplayedOfficialCourses = ()=>{\n        if (showAllOfficialCourses) {\n            return seriesCourses;\n        }\n        return seriesCourses.slice(0, 6);\n    };\n    // 获取要显示的社区课程列表\n    const getDisplayedCommunityCourses = ()=>{\n        if (showAllCommunityCourses) {\n            return communityCourses;\n        }\n        return communityCourses.slice(0, 6);\n    };\n    // 获取系列课程数据\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        const fetchSeriesCourses = async ()=>{\n            try {\n                setLoading(true);\n                console.log(\"\\uD83D\\uDD04 开始获取课程市场系列课程列表...\");\n                console.log(\"\\uD83D\\uDD04 准备调用 courseApi.getMarketplaceSeries（获取课程市场系列课程）...\");\n                // 使用课程市场系列课程列表接口\n                const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_4__.courseApi.getMarketplaceSeries({\n                    page: 1,\n                    pageSize: 50 // 获取更多数据\n                });\n                if (res.code === 200 && res.data && res.data.list) {\n                    // 打印每个系列课程的详细信息\n                    res.data.list.forEach((item, index)=>{\n                        console.log(\"\\uD83D\\uDCCB 系列课程 \".concat(index + 1, \":\"), {\n                            id: item.id,\n                            title: item.title,\n                            category: item.category,\n                            categoryLabel: item.categoryLabel,\n                            status: item.status,\n                            statusLabel: item.statusLabel,\n                            totalCourses: item.totalCourses,\n                            totalStudents: item.totalStudents,\n                            coverImage: item.coverImage // 添加封面图片信息\n                        });\n                    });\n                    // 筛选 categoryLabel 为 \"官方\" 的官方课程，全部显示\n                    const officialCourses = res.data.list.filter((item)=>{\n                        console.log('\\uD83D\\uDD0D 检查系列课程 \"'.concat(item.title, '\": category=').concat(item.category, ', categoryLabel=\"').concat(item.categoryLabel, '\"'));\n                        return item.categoryLabel === \"官方\"; // 筛选官方课程 (categoryLabel = \"官方\")\n                    }).map((item)=>{\n                        var _item_totalStudents;\n                        console.log('\\uD83D\\uDCCB 官方系列课程 \"'.concat(item.title, '\": category=').concat(item.category, ', categoryLabel=\"').concat(item.categoryLabel, '\", coverImage=\"').concat(item.coverImage, '\"'));\n                        return {\n                            id: item.id,\n                            seriesId: item.id,\n                            title: item.title,\n                            lessons: \"共\".concat(item.totalCourses || 0, \"课时\"),\n                            views: ((_item_totalStudents = item.totalStudents) === null || _item_totalStudents === void 0 ? void 0 : _item_totalStudents.toString()) || \"0\",\n                            coverImage: item.coverImage && !item.coverImage.includes(\"example.com\") ? item.coverImage : null // 过滤示例URL\n                        };\n                    });\n                    // 筛选 categoryLabel 为 \"社区\" 的社区课程，全部显示\n                    const communityCourses = res.data.list.filter((item)=>{\n                        console.log('\\uD83D\\uDD0D 检查社区课程 \"'.concat(item.title, '\": category=').concat(item.category, ', categoryLabel=\"').concat(item.categoryLabel, '\"'));\n                        return item.categoryLabel === \"社区\"; // 筛选社区课程 (categoryLabel = \"社区\")\n                    }).map((item)=>{\n                        var _item_totalStudents;\n                        console.log('\\uD83D\\uDCCB 社区系列课程 \"'.concat(item.title, '\": category=').concat(item.category, ', categoryLabel=\"').concat(item.categoryLabel, '\", coverImage=\"').concat(item.coverImage, '\"'));\n                        return {\n                            id: item.id,\n                            seriesId: item.id,\n                            title: item.title,\n                            lessons: \"共\".concat(item.totalCourses || 0, \"课时\"),\n                            views: ((_item_totalStudents = item.totalStudents) === null || _item_totalStudents === void 0 ? void 0 : _item_totalStudents.toString()) || \"0\",\n                            coverImage: item.coverImage && !item.coverImage.includes(\"example.com\") ? item.coverImage : null // 过滤示例URL\n                        };\n                    });\n                    setSeriesCourses(officialCourses);\n                    setCommunityCourses(communityCourses);\n                } else {\n                    setSeriesCourses([]);\n                }\n            } catch (error) {\n                console.error(\"❌ 获取系列课程失败:\", error);\n                console.error(\"❌ 错误详情:\", error);\n                setSeriesCourses([]);\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchSeriesCourses();\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen\",\n        children: showCourseDetail && selectedCourse ? // 课程详情页面 - 带格子背景\n        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8 relative min-h-screen\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 z-[-1]\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AIBackground, {}, void 0, false, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                    lineNumber: 166,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto relative z-10\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CourseDetailView__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        course: selectedCourse,\n                        onBack: handleBackToCourseList\n                    }, void 0, false, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                    lineNumber: 170,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n            lineNumber: 164,\n            columnNumber: 9\n        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full h-64 relative\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        src: \"/images/mywork_background.svg\",\n                        alt: \"背景图片\",\n                        fill: true,\n                        className: \"object-cover\",\n                        priority: true\n                    }, void 0, false, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                    lineNumber: 180,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-8 relative min-h-screen\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 z-[-1]\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AIBackground, {}, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                            lineNumber: 193,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto relative z-10\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8\n                                    },\n                                    className: \"mb-12\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: \"官方课程\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                    lineNumber: 206,\n                                                    columnNumber: 13\n                                                }, this),\n                                                (()=>{\n                                                    console.log(\"\\uD83D\\uDD0D 按钮显示条件检查:\", {\n                                                        loading,\n                                                        seriesCoursesLength: seriesCourses.length,\n                                                        shouldShowButton: !loading && seriesCourses.length > 6\n                                                    });\n                                                    return !loading && seriesCourses.length > 6;\n                                                })() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handleShowMoreOfficialCourses,\n                                                    disabled: loadingMore,\n                                                    className: \"group relative flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-blue-500 to-blue-600 text-white font-medium rounded-full shadow-lg hover:shadow-xl hover:from-blue-600 hover:to-blue-700 transform hover:scale-105 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none disabled:shadow-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 bg-gradient-to-r from-blue-400 to-blue-500 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                            lineNumber: 220,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative flex items-center gap-2\",\n                                                            children: loadingMore ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-5 h-5 animate-spin\",\n                                                                        fill: \"none\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                                className: \"opacity-25\",\n                                                                                cx: \"12\",\n                                                                                cy: \"12\",\n                                                                                r: \"10\",\n                                                                                stroke: \"currentColor\",\n                                                                                strokeWidth: \"4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                                lineNumber: 225,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                className: \"opacity-75\",\n                                                                                fill: \"currentColor\",\n                                                                                d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                                lineNumber: 226,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                        lineNumber: 224,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm\",\n                                                                        children: \"加载中...\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                        lineNumber: 228,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-semibold\",\n                                                                        children: showAllOfficialCourses ? \"收起\" : \"查看更多\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                        lineNumber: 232,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-5 h-5 transition-all duration-300 \".concat(showAllOfficialCourses ? \"rotate-180 scale-110\" : \"group-hover:translate-y-0.5\"),\n                                                                        fill: \"none\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        stroke: \"currentColor\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\",\n                                                                            strokeWidth: 2.5,\n                                                                            d: \"M19 9l-7 7-7-7\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                            lineNumber: 239,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                        lineNumber: 233,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                            lineNumber: 221,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 11\n                                        }, this),\n                                        loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                            children: [\n                                                1,\n                                                2,\n                                                3\n                                            ].map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white rounded-lg border border-blue-200 shadow-sm overflow-hidden\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-36 bg-gradient-to-br from-blue-100 to-blue-200 animate-pulse flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-12 h-12 text-blue-600 opacity-50\",\n                                                                fill: \"none\",\n                                                                viewBox: \"0 0 48 48\",\n                                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M44 24C44 22.8954 43.1046 22 42 22C40.8954 22 40 22.8954 40 24H44ZM24 8C25.1046 8 26 7.10457 26 6C26 4.89543 25.1046 4 24 4V8ZM39 40H9V44H39V40ZM8 39V9H4V39H8ZM40 24V39H44V24H40ZM9 8H24V4H9V8ZM9 40C8.44772 40 8 39.5523 8 39H4C4 41.7614 6.23857 44 9 44V40ZM39 44C41.7614 44 44 41.7614 44 39H40C40 39.5523 39.5523 40 39 40V44ZM8 9C8 8.44772 8.44771 8 9 8V4C6.23858 4 4 6.23857 4 9H8Z\",\n                                                                        fill: \"currentColor\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                        lineNumber: 253,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M6 35L16.6931 25.198C17.4389 24.5143 18.5779 24.4953 19.3461 25.1538L32 36\",\n                                                                        stroke: \"currentColor\",\n                                                                        strokeWidth: \"4\",\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                        lineNumber: 254,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M28 31L32.7735 26.2265C33.4772 25.5228 34.5914 25.4436 35.3877 26.0408L42 31\",\n                                                                        stroke: \"currentColor\",\n                                                                        strokeWidth: \"4\",\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                        lineNumber: 255,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M37 18L37 6\",\n                                                                        stroke: \"currentColor\",\n                                                                        strokeWidth: \"4\",\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                        lineNumber: 256,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M32 11L37 6L42 11\",\n                                                                        stroke: \"currentColor\",\n                                                                        strokeWidth: \"4\",\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                        lineNumber: 257,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                lineNumber: 252,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                            lineNumber: 251,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-4 bg-gray-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-4 bg-gray-200 rounded animate-pulse mb-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                    lineNumber: 261,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"h-3 bg-gray-200 rounded animate-pulse w-16\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                            lineNumber: 263,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"h-3 bg-gray-200 rounded animate-pulse w-12\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                            lineNumber: 264,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                    lineNumber: 262,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                            lineNumber: 260,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, i, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 17\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 13\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                            layout: true,\n                                            transition: {\n                                                duration: 0.5,\n                                                ease: \"easeInOut\"\n                                            },\n                                            children: getDisplayedOfficialCourses().map((course, index)=>{\n                                                const isNewlyVisible = showAllOfficialCourses && index >= 6;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                                    className: \"bg-white rounded-lg border border-blue-200 shadow-sm overflow-hidden hover:shadow-md transition-shadow duration-200 cursor-pointer\",\n                                                    onClick: ()=>handleCourseClick(course),\n                                                    initial: {\n                                                        opacity: isNewlyVisible ? 0 : 1,\n                                                        y: isNewlyVisible ? 30 : 0,\n                                                        scale: isNewlyVisible ? 0.9 : 1\n                                                    },\n                                                    animate: {\n                                                        opacity: 1,\n                                                        y: 0,\n                                                        scale: 1\n                                                    },\n                                                    transition: {\n                                                        duration: isNewlyVisible ? 0.6 : 0.4,\n                                                        delay: isNewlyVisible ? (index - 6) * 0.15 + 0.2 : index * 0.08,\n                                                        ease: \"easeOut\",\n                                                        type: \"spring\",\n                                                        stiffness: 100,\n                                                        damping: 15\n                                                    },\n                                                    whileHover: {\n                                                        y: -8,\n                                                        scale: 1.03,\n                                                        boxShadow: \"0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)\",\n                                                        transition: {\n                                                            duration: 0.3,\n                                                            ease: \"easeOut\"\n                                                        }\n                                                    },\n                                                    layout: true,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-36 relative \".concat(!course.coverImage ? \"bg-gradient-to-br from-blue-100 to-blue-200\" : \"\"),\n                                                            children: course.coverImage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                src: course.coverImage,\n                                                                alt: course.title,\n                                                                fill: true,\n                                                                className: \"object-cover\",\n                                                                onLoad: ()=>{\n                                                                    console.log('✅ 封面图片加载成功: \"'.concat(course.title, '\" - ').concat(course.coverImage));\n                                                                },\n                                                                onError: (e)=>{\n                                                                    // 图片加载失败时隐藏图片，显示默认图标\n                                                                    console.log('❌ 封面图片加载失败: \"'.concat(course.title, '\" - ').concat(course.coverImage));\n                                                                    const target = e.target;\n                                                                    target.style.display = \"none\";\n                                                                    // 添加浅蓝色背景\n                                                                    const parent = target.parentElement;\n                                                                    if (parent) {\n                                                                        parent.className = parent.className + \" bg-gradient-to-br from-blue-100 to-blue-200\";\n                                                                    }\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                lineNumber: 308,\n                                                                columnNumber: 23\n                                                            }, this) : // 当没有封面图片时显示的图标\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-full h-full flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-12 h-12 text-blue-600 opacity-70\",\n                                                                    fill: \"none\",\n                                                                    viewBox: \"0 0 48 48\",\n                                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M44 24C44 22.8954 43.1046 22 42 22C40.8954 22 40 22.8954 40 24H44ZM24 8C25.1046 8 26 7.10457 26 6C26 4.89543 25.1046 4 24 4V8ZM39 40H9V44H39V40ZM8 39V9H4V39H8ZM40 24V39H44V24H40ZM9 8H24V4H9V8ZM9 40C8.44772 40 8 39.5523 8 39H4C4 41.7614 6.23857 44 9 44V40ZM39 44C41.7614 44 44 41.7614 44 39H40C40 39.5523 39.5523 40 39 40V44ZM8 9C8 8.44772 8.44771 8 9 8V4C6.23858 4 4 6.23857 4 9H8Z\",\n                                                                            fill: \"currentColor\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                            lineNumber: 332,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M6 35L16.6931 25.198C17.4389 24.5143 18.5779 24.4953 19.3461 25.1538L32 36\",\n                                                                            stroke: \"currentColor\",\n                                                                            strokeWidth: \"4\",\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                            lineNumber: 333,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M28 31L32.7735 26.2265C33.4772 25.5228 34.5914 25.4436 35.3877 26.0408L42 31\",\n                                                                            stroke: \"currentColor\",\n                                                                            strokeWidth: \"4\",\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                            lineNumber: 334,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M37 18L37 6\",\n                                                                            stroke: \"currentColor\",\n                                                                            strokeWidth: \"4\",\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                            lineNumber: 335,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M32 11L37 6L42 11\",\n                                                                            stroke: \"currentColor\",\n                                                                            strokeWidth: \"4\",\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                            lineNumber: 336,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                    lineNumber: 331,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                lineNumber: 330,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                            lineNumber: 306,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-4 bg-gray-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"font-medium text-gray-800 mb-3 text-base line-clamp-2\",\n                                                                    children: course.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                    lineNumber: 344,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between text-sm text-gray-600\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-500\",\n                                                                            children: course.lessons\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                            lineNumber: 346,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-1.5\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                    className: \"w-4 h-4 text-gray-400\",\n                                                                                    fill: \"currentColor\",\n                                                                                    viewBox: \"0 0 20 20\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                            d: \"M10 12a2 2 0 100-4 2 2 0 000 4z\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                                            lineNumber: 349,\n                                                                                            columnNumber: 27\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                            fillRule: \"evenodd\",\n                                                                                            d: \"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z\",\n                                                                                            clipRule: \"evenodd\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                                            lineNumber: 350,\n                                                                                            columnNumber: 27\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                                    lineNumber: 348,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-gray-600 font-medium\",\n                                                                                    children: course.views\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                                    lineNumber: 352,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                            lineNumber: 347,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                    lineNumber: 345,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                            lineNumber: 343,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, course.id, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                    lineNumber: 279,\n                                                    columnNumber: 19\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 9\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8,\n                                        delay: 0.2\n                                    },\n                                    className: \"mb-12\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: \"课程社区\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                    lineNumber: 371,\n                                                    columnNumber: 13\n                                                }, this),\n                                                (()=>{\n                                                    console.log(\"\\uD83D\\uDD0D 社区按钮显示条件检查:\", {\n                                                        loading,\n                                                        communityCoursesLength: communityCourses.length,\n                                                        shouldShowButton: !loading && communityCourses.length > 6\n                                                    });\n                                                    return !loading && communityCourses.length > 6;\n                                                })() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handleShowMoreCommunityCourses,\n                                                    disabled: loadingMoreCommunity,\n                                                    className: \"group relative flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-green-500 to-green-600 text-white font-medium rounded-full shadow-lg hover:shadow-xl hover:from-green-600 hover:to-green-700 transform hover:scale-105 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none disabled:shadow-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 bg-gradient-to-r from-green-400 to-green-500 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                            lineNumber: 385,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative flex items-center gap-2\",\n                                                            children: loadingMoreCommunity ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-5 h-5 animate-spin\",\n                                                                        fill: \"none\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                                className: \"opacity-25\",\n                                                                                cx: \"12\",\n                                                                                cy: \"12\",\n                                                                                r: \"10\",\n                                                                                stroke: \"currentColor\",\n                                                                                strokeWidth: \"4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                                lineNumber: 390,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                className: \"opacity-75\",\n                                                                                fill: \"currentColor\",\n                                                                                d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                                lineNumber: 391,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                        lineNumber: 389,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm\",\n                                                                        children: \"加载中...\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                        lineNumber: 393,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-semibold\",\n                                                                        children: showAllCommunityCourses ? \"收起\" : \"查看更多\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                        lineNumber: 397,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-5 h-5 transition-all duration-300 \".concat(showAllCommunityCourses ? \"rotate-180 scale-110\" : \"group-hover:translate-y-0.5\"),\n                                                                        fill: \"none\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        stroke: \"currentColor\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\",\n                                                                            strokeWidth: 2.5,\n                                                                            d: \"M19 9l-7 7-7-7\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                            lineNumber: 404,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                        lineNumber: 398,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                            lineNumber: 386,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                    lineNumber: 380,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                            lineNumber: 370,\n                                            columnNumber: 11\n                                        }, this),\n                                        loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                            children: [\n                                                1,\n                                                2,\n                                                3\n                                            ].map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white rounded-lg border border-blue-200 shadow-sm overflow-hidden animate-pulse\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-36 bg-gradient-to-br from-blue-100 to-blue-200 flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-12 h-12 text-blue-600 opacity-50\",\n                                                                fill: \"none\",\n                                                                viewBox: \"0 0 48 48\",\n                                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M44 24C44 22.8954 43.1046 22 42 22C40.8954 22 40 22.8954 40 24H44ZM24 8C25.1046 8 26 7.10457 26 6C26 4.89543 25.1046 4 24 4V8ZM39 40H9V44H39V40ZM8 39V9H4V39H8ZM40 24V39H44V24H40ZM9 8H24V4H9V8ZM9 40C8.44772 40 8 39.5523 8 39H4C4 41.7614 6.23857 44 9 44V40ZM39 44C41.7614 44 44 41.7614 44 39H40C40 39.5523 39.5523 40 39 40V44ZM8 9C8 8.44772 8.44771 8 9 8V4C6.23858 4 4 6.23857 4 9H8Z\",\n                                                                        fill: \"currentColor\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                        lineNumber: 418,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M6 35L16.6931 25.198C17.4389 24.5143 18.5779 24.4953 19.3461 25.1538L32 36\",\n                                                                        stroke: \"currentColor\",\n                                                                        strokeWidth: \"4\",\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                        lineNumber: 419,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M28 31L32.7735 26.2265C33.4772 25.5228 34.5914 25.4436 35.3877 26.0408L42 31\",\n                                                                        stroke: \"currentColor\",\n                                                                        strokeWidth: \"4\",\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                        lineNumber: 420,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M37 18L37 6\",\n                                                                        stroke: \"currentColor\",\n                                                                        strokeWidth: \"4\",\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                        lineNumber: 421,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M32 11L37 6L42 11\",\n                                                                        stroke: \"currentColor\",\n                                                                        strokeWidth: \"4\",\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                        lineNumber: 422,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                lineNumber: 417,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                            lineNumber: 416,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-4 bg-gray-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-4 bg-gray-200 rounded mb-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                    lineNumber: 426,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"h-3 bg-gray-200 rounded w-16\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                            lineNumber: 428,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"h-3 bg-gray-200 rounded w-8\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                            lineNumber: 429,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                    lineNumber: 427,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                            lineNumber: 425,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, i, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                    lineNumber: 415,\n                                                    columnNumber: 17\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                            lineNumber: 413,\n                                            columnNumber: 13\n                                        }, this) : communityCourses.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-12\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-400 text-lg mb-2\",\n                                                    children: \"暂无社区课程\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                    lineNumber: 437,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-500 text-sm\",\n                                                    children: \"社区课程正在建设中...\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                    lineNumber: 438,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                            lineNumber: 436,\n                                            columnNumber: 13\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                            layout: true,\n                                            transition: {\n                                                duration: 0.5,\n                                                ease: \"easeInOut\"\n                                            },\n                                            children: getDisplayedCommunityCourses().map((course, index)=>{\n                                                const isNewlyVisible = showAllCommunityCourses && index >= 6;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                                    className: \"bg-white rounded-lg border border-blue-200 shadow-sm overflow-hidden hover:shadow-md transition-shadow duration-200 cursor-pointer\",\n                                                    onClick: ()=>handleCourseClick(course),\n                                                    initial: {\n                                                        opacity: isNewlyVisible ? 0 : 1,\n                                                        y: isNewlyVisible ? 30 : 0,\n                                                        scale: isNewlyVisible ? 0.9 : 1\n                                                    },\n                                                    animate: {\n                                                        opacity: 1,\n                                                        y: 0,\n                                                        scale: 1\n                                                    },\n                                                    transition: {\n                                                        duration: isNewlyVisible ? 0.6 : 0.4,\n                                                        delay: isNewlyVisible ? (index - 6) * 0.15 + 0.2 : index * 0.08,\n                                                        ease: \"easeOut\",\n                                                        type: \"spring\",\n                                                        stiffness: 100,\n                                                        damping: 15\n                                                    },\n                                                    whileHover: {\n                                                        y: -8,\n                                                        scale: 1.03,\n                                                        boxShadow: \"0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)\",\n                                                        transition: {\n                                                            duration: 0.3,\n                                                            ease: \"easeOut\"\n                                                        }\n                                                    },\n                                                    layout: true,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-36 relative \".concat(!course.coverImage ? \"bg-gradient-to-br from-blue-100 to-blue-200\" : \"\"),\n                                                            children: course.coverImage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                src: course.coverImage,\n                                                                alt: course.title,\n                                                                fill: true,\n                                                                className: \"object-cover\",\n                                                                onLoad: ()=>{\n                                                                    console.log('✅ 封面图片加载成功: \"'.concat(course.title, '\" - ').concat(course.coverImage));\n                                                                },\n                                                                onError: (e)=>{\n                                                                    // 图片加载失败时隐藏图片，显示默认图标\n                                                                    console.log('❌ 封面图片加载失败: \"'.concat(course.title, '\" - ').concat(course.coverImage));\n                                                                    const target = e.target;\n                                                                    target.style.display = \"none\";\n                                                                    // 添加浅蓝色背景\n                                                                    const parent = target.parentElement;\n                                                                    if (parent) {\n                                                                        parent.className = parent.className + \" bg-gradient-to-br from-blue-100 to-blue-200\";\n                                                                    }\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                lineNumber: 478,\n                                                                columnNumber: 23\n                                                            }, this) : // 当没有封面图片时显示的图标\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-full h-full flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-12 h-12 text-blue-600 opacity-70\",\n                                                                    fill: \"none\",\n                                                                    viewBox: \"0 0 48 48\",\n                                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M44 24C44 22.8954 43.1046 22 42 22C40.8954 22 40 22.8954 40 24H44ZM24 8C25.1046 8 26 7.10457 26 6C26 4.89543 25.1046 4 24 4V8ZM39 40H9V44H39V40ZM8 39V9H4V39H8ZM40 24V39H44V24H40ZM9 8H24V4H9V8ZM9 40C8.44772 40 8 39.5523 8 39H4C4 41.7614 6.23857 44 9 44V40ZM39 44C41.7614 44 44 41.7614 44 39H40C40 39.5523 39.5523 40 39 40V44ZM8 9C8 8.44772 8.44771 8 9 8V4C6.23858 4 4 6.23857 4 9H8Z\",\n                                                                            fill: \"currentColor\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                            lineNumber: 502,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M6 35L16.6931 25.198C17.4389 24.5143 18.5779 24.4953 19.3461 25.1538L32 36\",\n                                                                            stroke: \"currentColor\",\n                                                                            strokeWidth: \"4\",\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                            lineNumber: 503,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M28 31L32.7735 26.2265C33.4772 25.5228 34.5914 25.4436 35.3877 26.0408L42 31\",\n                                                                            stroke: \"currentColor\",\n                                                                            strokeWidth: \"4\",\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                            lineNumber: 504,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M37 18L37 6\",\n                                                                            stroke: \"currentColor\",\n                                                                            strokeWidth: \"4\",\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                            lineNumber: 505,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M32 11L37 6L42 11\",\n                                                                            stroke: \"currentColor\",\n                                                                            strokeWidth: \"4\",\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                            lineNumber: 506,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                    lineNumber: 501,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                lineNumber: 500,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                            lineNumber: 476,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-4 bg-gray-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"font-medium text-gray-800 mb-3 text-base line-clamp-2\",\n                                                                    children: course.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                    lineNumber: 514,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between text-sm text-gray-600\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-500\",\n                                                                            children: course.lessons\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                            lineNumber: 516,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-1.5\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                    className: \"w-4 h-4 text-gray-400\",\n                                                                                    fill: \"currentColor\",\n                                                                                    viewBox: \"0 0 20 20\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                            d: \"M10 12a2 2 0 100-4 2 2 0 000 4z\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                                            lineNumber: 519,\n                                                                                            columnNumber: 27\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                            fillRule: \"evenodd\",\n                                                                                            d: \"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z\",\n                                                                                            clipRule: \"evenodd\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                                            lineNumber: 520,\n                                                                                            columnNumber: 27\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                                    lineNumber: 518,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-gray-600 font-medium\",\n                                                                                    children: course.views\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                                    lineNumber: 522,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                            lineNumber: 517,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                    lineNumber: 515,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                            lineNumber: 513,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, course.id, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                    lineNumber: 449,\n                                                    columnNumber: 19\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                            lineNumber: 441,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                    lineNumber: 364,\n                                    columnNumber: 9\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                            lineNumber: 197,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                    lineNumber: 191,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true)\n    }, void 0, false, {\n        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n        lineNumber: 161,\n        columnNumber: 5\n    }, this);\n}\n_s(CoursePlaza, \"gyljU7inwf6shDYB1IcTy/Ch8OM=\");\n_c1 = CoursePlaza;\nvar _c, _c1;\n$RefreshReg$(_c, \"AIBackground\");\n$RefreshReg$(_c1, \"CoursePlaza\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/(main)/course-plaza/page.tsx\n"));

/***/ })

});