describe('The Home Page', () => {
    it('successfully loads', () => {
      cy.viewport(1920, 1080)
      cy.visit('/home') // change URL to match your dev URL
      /* ==== Generated with Cypress Studio ==== */
      
      /* ==== End Cypress Studio ==== */
      /* ==== Generated with Cypress Studio ==== */
      cy.get('.bg-white\\/10 > .lucide').click();
      cy.get('.border-b > :nth-child(2)').click();
      cy.get('#phone-password').clear();
      cy.get('#phone-password').type('15217186585');
      cy.slowDown(500)
      cy.get('#password').clear();
      cy.get('#password').type('123456');
      cy.slowDown(500)
      cy.get('#terms').check();
      cy.get('.pt-2 > .w-full').click();
      cy.get('.grid > :nth-child(1) > :nth-child(1)').click();
      cy.slowDown(500)
      cy.get('.slide-content > div.w-full').click();
      cy.slowDown(500)
      cy.get('.slide-content > div.w-full > .w-full').click();
      /* ==== End Cypress Studio ==== */
    })
  })

  