"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/workbench/page",{

/***/ "(app-pages-browser)/./app/workbench/components/LeftSidebar.tsx":
/*!**************************************************!*\
  !*** ./app/workbench/components/LeftSidebar.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowLeftCircle_Book_BookOpen_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftCircle,Book,BookOpen,Briefcase,ChevronDown,LayoutDashboard,ListTodo,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftCircle_Book_BookOpen_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftCircle,Book,BookOpen,Briefcase,ChevronDown,LayoutDashboard,ListTodo,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftCircle_Book_BookOpen_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftCircle,Book,BookOpen,Briefcase,ChevronDown,LayoutDashboard,ListTodo,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/list-todo.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftCircle_Book_BookOpen_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftCircle,Book,BookOpen,Briefcase,ChevronDown,LayoutDashboard,ListTodo,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftCircle_Book_BookOpen_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftCircle,Book,BookOpen,Briefcase,ChevronDown,LayoutDashboard,ListTodo,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftCircle_Book_BookOpen_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftCircle,Book,BookOpen,Briefcase,ChevronDown,LayoutDashboard,ListTodo,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftCircle_Book_BookOpen_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftCircle,Book,BookOpen,Briefcase,ChevronDown,LayoutDashboard,ListTodo,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftCircle_Book_BookOpen_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftCircle,Book,BookOpen,Briefcase,ChevronDown,LayoutDashboard,ListTodo,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftCircle_Book_BookOpen_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftCircle,Book,BookOpen,Briefcase,ChevronDown,LayoutDashboard,ListTodo,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftCircle_Book_BookOpen_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftCircle,Book,BookOpen,Briefcase,ChevronDown,LayoutDashboard,ListTodo,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-arrow-left.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _lib_api_school__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api/school */ \"(app-pages-browser)/./lib/api/school.ts\");\n/* harmony import */ var _lib_api_class__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api/class */ \"(app-pages-browser)/./lib/api/class.ts\");\n/* harmony import */ var _lib_utils_address__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils/address */ \"(app-pages-browser)/./lib/utils/address.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst LeftSidebar = (param)=>{\n    let { userInfo, onMenuItemClick, onSchoolSelect, onClassesUpdate } = param;\n    _s();\n    const [activeItem, setActiveItem] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"快速开始\");\n    const [isClassDropdownOpen, setIsClassDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [schools, setSchools] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedSchool, setSelectedSchool] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [schoolsLoading, setSchoolsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [schoolsError, setSchoolsError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 监听activeItem变化，当进入班级管理页面时自动打开下拉菜单\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"activeItem 状态变化:\", activeItem);\n        // 当切换到班级管理页面时，自动打开下拉菜单\n        if (activeItem === \"班级管理\") {\n            setIsClassDropdownOpen(true);\n        }\n    }, [\n        activeItem\n    ]);\n    // 下拉菜单的ref，用于检测点击外部区域\n    const dropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 标志位，防止导航点击和外部点击冲突\n    const isNavigatingRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // 点击外部区域关闭下拉菜单并切换到班级管理页面\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleClickOutside = (event)=>{\n            if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n                console.log(\"点击外部区域，isNavigating:\", isNavigatingRef.current);\n                // 如果正在导航，不处理外部点击\n                if (isNavigatingRef.current) {\n                    isNavigatingRef.current = false;\n                    return;\n                }\n                // 如果当前活跃项是班级管理，不关闭下拉菜单\n                if (activeItem === \"班级管理\") {\n                    return;\n                }\n                // 关闭下拉菜单\n                setIsClassDropdownOpen(false);\n                // 如果有选中的学校，切换到班级管理页面\n                if (selectedSchool) {\n                    setActiveItem(\"班级管理\");\n                    onMenuItemClick === null || onMenuItemClick === void 0 ? void 0 : onMenuItemClick(\"班级管理\");\n                }\n            }\n        };\n        if (isClassDropdownOpen) {\n            document.addEventListener(\"mousedown\", handleClickOutside);\n        }\n        return ()=>{\n            document.removeEventListener(\"mousedown\", handleClickOutside);\n        };\n    }, [\n        isClassDropdownOpen,\n        selectedSchool,\n        onMenuItemClick,\n        activeItem\n    ]);\n    // 监听自定义事件来关闭下拉菜单\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleCloseDropdown = ()=>{\n            setIsClassDropdownOpen(false);\n        };\n        document.addEventListener(\"closeDropdown\", handleCloseDropdown);\n        return ()=>{\n            document.removeEventListener(\"closeDropdown\", handleCloseDropdown);\n        };\n    }, []);\n    // 获取教师管理的学校列表\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchSchools();\n    }, []);\n    const navItems = [\n        {\n            icon: _barrel_optimize_names_ArrowLeftCircle_Book_BookOpen_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            name: \"快速开始\",\n            hasDivider: true\n        },\n        {\n            icon: _barrel_optimize_names_ArrowLeftCircle_Book_BookOpen_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            name: \"班级管理\",\n            hasDropdown: true\n        },\n        {\n            icon: _barrel_optimize_names_ArrowLeftCircle_Book_BookOpen_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            name: \"班级任务\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowLeftCircle_Book_BookOpen_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            name: \"班级项目\",\n            hasDivider: true\n        },\n        {\n            icon: _barrel_optimize_names_ArrowLeftCircle_Book_BookOpen_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            name: \"官方课程\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowLeftCircle_Book_BookOpen_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            name: \"课程管理\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowLeftCircle_Book_BookOpen_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            name: \"模板管理\"\n        }\n    ];\n    // 处理班级管理点击\n    const handleClassManagementClick = ()=>{\n        // 设置为活跃状态\n        setActiveItem(\"班级管理\");\n        // 如果没有选中学校且有可用学校，自动选择第一个学校\n        if (!selectedSchool && schools.length > 0) {\n            const firstSchool = schools[0];\n            setSelectedSchool(firstSchool);\n            onSchoolSelect === null || onSchoolSelect === void 0 ? void 0 : onSchoolSelect(firstSchool);\n            fetchClasses(firstSchool.id);\n            console.log(\"班级管理：自动选择第一个学校:\", firstSchool);\n        } else if (!selectedSchool && schools.length === 0 && !schoolsLoading) {\n            // 如果没有学校数据且不在加载中，重新获取学校列表\n            console.log(\"班级管理：没有学校数据，重新获取学校列表\");\n            fetchSchools();\n        }\n        // 如果当前已经是班级管理页面且下拉菜单已打开，则关闭；否则打开\n        if (activeItem === \"班级管理\" && isClassDropdownOpen) {\n            setIsClassDropdownOpen(false);\n        } else {\n            setIsClassDropdownOpen(true);\n        }\n        // 通知父组件\n        onMenuItemClick === null || onMenuItemClick === void 0 ? void 0 : onMenuItemClick(\"班级管理\");\n    };\n    // 处理学校选择\n    const handleSchoolSelect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((school)=>{\n        console.log(\"handleSchoolSelect 被调用，当前activeItem:\", activeItem);\n        // 不关闭下拉菜单，只更新选中状态\n        setSelectedSchool(school);\n        // 强制切换到班级管理页面（无论当前在什么页面）\n        setActiveItem(\"班级管理\");\n        onMenuItemClick === null || onMenuItemClick === void 0 ? void 0 : onMenuItemClick(\"班级管理\");\n        // 始终通知父组件学校选择变化（用于数据更新）\n        onSchoolSelect === null || onSchoolSelect === void 0 ? void 0 : onSchoolSelect(school);\n        // 获取该学校的班级列表\n        fetchClasses(school.id);\n    }, [\n        onMenuItemClick,\n        onSchoolSelect\n    ]);\n    // 处理返回主页\n    const handleBackToHome = ()=>{\n        console.log(\"点击返回主页按钮\");\n        // 获取当前域名和端口，然后跳转到home页面\n        const currentOrigin = window.location.origin;\n        const homeUrl = \"\".concat(currentOrigin, \"/home\");\n        console.log(\"当前域名:\", currentOrigin);\n        console.log(\"跳转到:\", homeUrl);\n        // 直接跳转到home页面\n        window.location.href = homeUrl;\n    };\n    // 获取学校列表\n    const fetchSchools = async ()=>{\n        setSchoolsLoading(true);\n        setSchoolsError(null);\n        try {\n            var _response_data, _response_data1, _response_data2, _response_data3;\n            const response = await (0,_lib_api_school__WEBPACK_IMPORTED_MODULE_3__.getTeacherSchools)();\n            console.log(\"获取学校列表API响应:\", response);\n            // 检查多种可能的响应格式\n            let schoolList = [];\n            if (((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.code) === 200 && ((_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.data)) {\n                schoolList = response.data.data;\n            } else if (((_response_data2 = response.data) === null || _response_data2 === void 0 ? void 0 : _response_data2.status) === 200 && ((_response_data3 = response.data) === null || _response_data3 === void 0 ? void 0 : _response_data3.data)) {\n                schoolList = response.data.data;\n            } else if (Array.isArray(response.data)) {\n                schoolList = response.data;\n            }\n            if (schoolList.length > 0) {\n                setSchools(schoolList);\n                const firstSchool = schoolList[0];\n                setSelectedSchool(firstSchool);\n                // 通知父组件学校选择变化\n                onSchoolSelect === null || onSchoolSelect === void 0 ? void 0 : onSchoolSelect(firstSchool);\n                // 获取第一个学校的班级列表\n                fetchClasses(firstSchool.id);\n                console.log(\"成功获取学校列表，数量:\", schoolList.length);\n                console.log(\"自动选择第一个学校:\", firstSchool);\n            } else {\n                setSchoolsError(\"暂无数据\");\n            }\n        } catch (error) {\n            console.error(\"获取学校列表失败:\", error);\n            setSchoolsError(\"请检查网络连接失败\");\n        } finally{\n            setSchoolsLoading(false);\n        }\n    };\n    // 获取指定学校的班级列表\n    const fetchClasses = async (schoolId)=>{\n        if (!(userInfo === null || userInfo === void 0 ? void 0 : userInfo.id)) {\n            console.log(\"用户未登录，无法获取班级列表\");\n            return;\n        }\n        // 通知父组件开始加载\n        onClassesUpdate && onClassesUpdate([], true, null);\n        try {\n            var _response_data;\n            console.log(\"获取班级列表:\", {\n                schoolId,\n                teacherId: userInfo.id\n            });\n            const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_4__.classApi.getTeacherClasses(schoolId, userInfo.id);\n            console.log(\"班级列表API响应:\", response);\n            if (((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.code) === 200) {\n                const classList = response.data.data || [];\n                // 通知父组件数据更新\n                onClassesUpdate && onClassesUpdate(classList, false, null);\n                console.log(\"成功获取班级列表，数量:\", classList.length);\n            } else {\n                const errorMsg = \"获取班级列表失败\";\n                // 通知父组件错误状态\n                onClassesUpdate && onClassesUpdate([], false, errorMsg);\n            }\n        } catch (error) {\n            console.error(\"获取班级列表失败:\", error);\n            const errorMsg = \"请检查网络连接\";\n            // 通知父组件错误状态\n            onClassesUpdate && onClassesUpdate([], false, errorMsg);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: \"left-sidebar\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"sidebar-header\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftCircle_Book_BookOpen_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                        lineNumber: 276,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-lg font-bold\",\n                        children: \"教师空间\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                        lineNumber: 277,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                lineNumber: 275,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"teacher-info\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        src: userInfo.avatarUrl || \"/images/xiaoluo-default.webp\",\n                        alt: userInfo.nickName || \"小洛头像\",\n                        width: 40,\n                        height: 40,\n                        className: \"avatar\",\n                        style: {\n                            backgroundColor: \"white\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                        lineNumber: 281,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"teacher-details\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"teacher-name\",\n                                children: userInfo.nickName || \"未登录\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                lineNumber: 290,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"teacher-title\",\n                                children: \"教师\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                        lineNumber: 289,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                lineNumber: 280,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"sidebar-nav\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                    children: navItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), {\n                            children: [\n                                item.hasDropdown ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"nav-item-dropdown\",\n                                    ref: dropdownRef,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"nav-item \".concat(activeItem === item.name ? \"active\" : \"\", \" \").concat(isClassDropdownOpen ? \"dropdown-open\" : \"\"),\n                                            onClick: (e)=>{\n                                                e.preventDefault();\n                                                e.stopPropagation();\n                                                handleClassManagementClick();\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                    className: \"nav-icon\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                    lineNumber: 308,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: item.name\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                    lineNumber: 309,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftCircle_Book_BookOpen_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"dropdown-arrow \".concat(isClassDropdownOpen ? \"rotated\" : \"\"),\n                                                    size: 16,\n                                                    style: {\n                                                        transform: isClassDropdownOpen ? \"rotate(180deg)\" : \"rotate(0deg)\",\n                                                        transition: \"transform 0.3s ease\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                    lineNumber: 310,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                            lineNumber: 300,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        isClassDropdownOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"dropdown-menu \".concat(schoolsLoading || schoolsError || schools.length === 0 ? \"empty\" : \"\"),\n                                            onClick: (e)=>{\n                                                e.preventDefault();\n                                                e.stopPropagation();\n                                            },\n                                            children: schoolsLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"dropdown-item disabled loading\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"loading-spinner\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                        lineNumber: 329,\n                                                        columnNumber: 27\n                                                    }, undefined),\n                                                    \"正在加载学校信息...\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                lineNumber: 328,\n                                                columnNumber: 25\n                                            }, undefined) : schoolsError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"dropdown-item disabled error\",\n                                                children: schoolsError\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                lineNumber: 333,\n                                                columnNumber: 25\n                                            }, undefined) : schools.length > 0 ? schools.map((school)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"dropdown-item \".concat((selectedSchool === null || selectedSchool === void 0 ? void 0 : selectedSchool.id) === school.id ? \"selected\" : \"\"),\n                                                    onClick: (e)=>{\n                                                        e.preventDefault();\n                                                        e.stopPropagation();\n                                                        handleSchoolSelect(school);\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"school-info\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"school-name\",\n                                                                children: school.schoolName\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                                lineNumber: 348,\n                                                                columnNumber: 31\n                                                            }, undefined),\n                                                            school.province && school.district && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"school-location\",\n                                                                children: (0,_lib_utils_address__WEBPACK_IMPORTED_MODULE_5__.formatSchoolAddress)(school.province, school.city, school.district)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                                lineNumber: 350,\n                                                                columnNumber: 33\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                        lineNumber: 347,\n                                                        columnNumber: 29\n                                                    }, undefined)\n                                                }, school.id, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 27\n                                                }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"dropdown-item disabled no-data\",\n                                                children: \"暂无数据\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                lineNumber: 358,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"nav-item \".concat(activeItem === item.name ? \"active\" : \"\"),\n                                    onClick: (e)=>{\n                                        e.preventDefault();\n                                        e.stopPropagation();\n                                        console.log(\"点击导航项:\", item.name);\n                                        console.log(\"当前下拉菜单状态:\", isClassDropdownOpen);\n                                        console.log(\"当前活跃项:\", activeItem);\n                                        // 设置导航标志，防止外部点击干扰\n                                        isNavigatingRef.current = true;\n                                        // 先关闭下拉菜单\n                                        setIsClassDropdownOpen(false);\n                                        // 然后更新活跃项\n                                        setActiveItem(item.name);\n                                        // 最后通知父组件\n                                        onMenuItemClick === null || onMenuItemClick === void 0 ? void 0 : onMenuItemClick(item.name);\n                                        console.log(\"完成设置 - 活跃项:\", item.name, \"下拉菜单已关闭\");\n                                        // 延迟重置标志位\n                                        setTimeout(()=>{\n                                            isNavigatingRef.current = false;\n                                        }, 100);\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                            className: \"nav-icon\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                            lineNumber: 395,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: item.name\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                            lineNumber: 396,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                    lineNumber: 366,\n                                    columnNumber: 17\n                                }, undefined),\n                                item.hasDivider && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"nav-divider\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                    lineNumber: 399,\n                                    columnNumber: 35\n                                }, undefined)\n                            ]\n                        }, item.name, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                            lineNumber: 297,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                    lineNumber: 295,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                lineNumber: 294,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"sidebar-footer\",\n                onClick: handleBackToHome,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftCircle_Book_BookOpen_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                        className: \"nav-icon\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                        lineNumber: 405,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"返回主页\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                        lineNumber: 406,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                lineNumber: 404,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n        lineNumber: 274,\n        columnNumber: 5\n    }, undefined);\n};\n_s(LeftSidebar, \"sRB/Ml9AeQvFB94OE2ocIHTANvQ=\");\n_c = LeftSidebar;\n/* harmony default export */ __webpack_exports__[\"default\"] = (LeftSidebar);\nvar _c;\n$RefreshReg$(_c, \"LeftSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC93b3JrYmVuY2gvY29tcG9uZW50cy9MZWZ0U2lkZWJhci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFd0U7QUFDbUU7QUFDNUc7QUFFc0I7QUFDVjtBQUNlO0FBaUMxRCxNQUFNbUIsY0FBYztRQUFDLEVBQUVDLFFBQVEsRUFBRUMsZUFBZSxFQUFFQyxjQUFjLEVBQUVDLGVBQWUsRUFBb0I7O0lBQ25HLE1BQU0sQ0FBQ0MsWUFBWUMsY0FBYyxHQUFHeEIsK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDeUIscUJBQXFCQyx1QkFBdUIsR0FBRzFCLCtDQUFRQSxDQUFDO0lBRy9ELE1BQU0sQ0FBQzJCLFNBQVNDLFdBQVcsR0FBRzVCLCtDQUFRQSxDQUFXLEVBQUU7SUFDbkQsTUFBTSxDQUFDNkIsZ0JBQWdCQyxrQkFBa0IsR0FBRzlCLCtDQUFRQSxDQUFnQjtJQUNwRSxNQUFNLENBQUMrQixnQkFBZ0JDLGtCQUFrQixHQUFHaEMsK0NBQVFBLENBQUM7SUFDckQsTUFBTSxDQUFDaUMsY0FBY0MsZ0JBQWdCLEdBQUdsQywrQ0FBUUEsQ0FBZ0I7SUFFaEUsb0NBQW9DO0lBQ3BDQyxnREFBU0EsQ0FBQztRQUNSa0MsUUFBUUMsR0FBRyxDQUFDLG9CQUFvQmI7UUFFaEMsdUJBQXVCO1FBQ3ZCLElBQUlBLGVBQWUsUUFBUTtZQUN6QkcsdUJBQXVCO1FBQ3pCO0lBQ0YsR0FBRztRQUFDSDtLQUFXO0lBRWYsc0JBQXNCO0lBQ3RCLE1BQU1jLGNBQWNuQyw2Q0FBTUEsQ0FBZ0I7SUFFMUMsb0JBQW9CO0lBQ3BCLE1BQU1vQyxrQkFBa0JwQyw2Q0FBTUEsQ0FBQztJQUUvQix5QkFBeUI7SUFDekJELGdEQUFTQSxDQUFDO1FBQ1IsTUFBTXNDLHFCQUFxQixDQUFDQztZQUMxQixJQUFJSCxZQUFZSSxPQUFPLElBQUksQ0FBQ0osWUFBWUksT0FBTyxDQUFDQyxRQUFRLENBQUNGLE1BQU1HLE1BQU0sR0FBVztnQkFDOUVSLFFBQVFDLEdBQUcsQ0FBQyx3QkFBd0JFLGdCQUFnQkcsT0FBTztnQkFFM0QsaUJBQWlCO2dCQUNqQixJQUFJSCxnQkFBZ0JHLE9BQU8sRUFBRTtvQkFDM0JILGdCQUFnQkcsT0FBTyxHQUFHO29CQUMxQjtnQkFDRjtnQkFFQSx1QkFBdUI7Z0JBQ3ZCLElBQUlsQixlQUFlLFFBQVE7b0JBQ3pCO2dCQUNGO2dCQUVBLFNBQVM7Z0JBQ1RHLHVCQUF1QjtnQkFFdkIscUJBQXFCO2dCQUNyQixJQUFJRyxnQkFBZ0I7b0JBQ2xCTCxjQUFjO29CQUNkSiw0QkFBQUEsc0NBQUFBLGdCQUFrQjtnQkFDcEI7WUFDRjtRQUNGO1FBRUEsSUFBSUsscUJBQXFCO1lBQ3ZCbUIsU0FBU0MsZ0JBQWdCLENBQUMsYUFBYU47UUFDekM7UUFFQSxPQUFPO1lBQ0xLLFNBQVNFLG1CQUFtQixDQUFDLGFBQWFQO1FBQzVDO0lBQ0YsR0FBRztRQUFDZDtRQUFxQkk7UUFBZ0JUO1FBQWlCRztLQUFXO0lBRXJFLGlCQUFpQjtJQUNqQnRCLGdEQUFTQSxDQUFDO1FBQ1IsTUFBTThDLHNCQUFzQjtZQUMxQnJCLHVCQUF1QjtRQUN6QjtRQUVBa0IsU0FBU0MsZ0JBQWdCLENBQUMsaUJBQWlCRTtRQUUzQyxPQUFPO1lBQ0xILFNBQVNFLG1CQUFtQixDQUFDLGlCQUFpQkM7UUFDaEQ7SUFDRixHQUFHLEVBQUU7SUFFTCxjQUFjO0lBQ2Q5QyxnREFBU0EsQ0FBQztRQUNSK0M7SUFDRixHQUFHLEVBQUU7SUFNTCxNQUFNQyxXQUFXO1FBQ2Y7WUFBRUMsTUFBTTlDLDhLQUFlQTtZQUFFK0MsTUFBTTtZQUFRQyxZQUFZO1FBQUs7UUFDeEQ7WUFBRUYsTUFBTTdDLDhLQUFLQTtZQUFFOEMsTUFBTTtZQUFRRSxhQUFhO1FBQUs7UUFDL0M7WUFBRUgsTUFBTTVDLDhLQUFRQTtZQUFFNkMsTUFBTTtRQUFPO1FBQy9CO1lBQUVELE1BQU0zQyw4S0FBU0E7WUFBRTRDLE1BQU07WUFBUUMsWUFBWTtRQUFLO1FBQ2xEO1lBQUVGLE1BQU16QywrS0FBUUE7WUFBRTBDLE1BQU07UUFBTztRQUMvQjtZQUFFRCxNQUFNMUMsK0tBQUlBO1lBQUUyQyxNQUFNO1FBQU87UUFDM0I7WUFBRUQsTUFBTXhDLCtLQUFRQTtZQUFFeUMsTUFBTTtRQUFPO0tBQ2hDO0lBRUQsV0FBVztJQUNYLE1BQU1HLDZCQUE2QjtRQUNqQyxVQUFVO1FBQ1Y5QixjQUFjO1FBRWQsMkJBQTJCO1FBQzNCLElBQUksQ0FBQ0ssa0JBQWtCRixRQUFRNEIsTUFBTSxHQUFHLEdBQUc7WUFDekMsTUFBTUMsY0FBYzdCLE9BQU8sQ0FBQyxFQUFFO1lBQzlCRyxrQkFBa0IwQjtZQUNsQm5DLDJCQUFBQSxxQ0FBQUEsZUFBaUJtQztZQUNqQkMsYUFBYUQsWUFBWUUsRUFBRTtZQUMzQnZCLFFBQVFDLEdBQUcsQ0FBQyxtQkFBbUJvQjtRQUNqQyxPQUFPLElBQUksQ0FBQzNCLGtCQUFrQkYsUUFBUTRCLE1BQU0sS0FBSyxLQUFLLENBQUN4QixnQkFBZ0I7WUFDckUsMEJBQTBCO1lBQzFCSSxRQUFRQyxHQUFHLENBQUM7WUFDWlk7UUFDRjtRQUVBLGlDQUFpQztRQUNqQyxJQUFJekIsZUFBZSxVQUFVRSxxQkFBcUI7WUFDaERDLHVCQUF1QjtRQUN6QixPQUFPO1lBQ0xBLHVCQUF1QjtRQUN6QjtRQUNBLFFBQVE7UUFDUk4sNEJBQUFBLHNDQUFBQSxnQkFBa0I7SUFDcEI7SUFFQSxTQUFTO0lBQ1QsTUFBTXVDLHFCQUFxQnhELGtEQUFXQSxDQUFDLENBQUN5RDtRQUN0Q3pCLFFBQVFDLEdBQUcsQ0FBQyx3Q0FBd0NiO1FBRXBELGtCQUFrQjtRQUNsQk8sa0JBQWtCOEI7UUFFbEIseUJBQXlCO1FBQ3pCcEMsY0FBYztRQUNkSiw0QkFBQUEsc0NBQUFBLGdCQUFrQjtRQUVsQix3QkFBd0I7UUFDeEJDLDJCQUFBQSxxQ0FBQUEsZUFBaUJ1QztRQUVqQixhQUFhO1FBQ2JILGFBQWFHLE9BQU9GLEVBQUU7SUFDeEIsR0FBRztRQUFDdEM7UUFBaUJDO0tBQWU7SUFFcEMsU0FBUztJQUNULE1BQU13QyxtQkFBbUI7UUFDdkIxQixRQUFRQyxHQUFHLENBQUM7UUFFWix3QkFBd0I7UUFDeEIsTUFBTTBCLGdCQUFnQkMsT0FBT0MsUUFBUSxDQUFDQyxNQUFNO1FBQzVDLE1BQU1DLFVBQVUsR0FBaUIsT0FBZEosZUFBYztRQUNqQzNCLFFBQVFDLEdBQUcsQ0FBQyxTQUFTMEI7UUFDckIzQixRQUFRQyxHQUFHLENBQUMsUUFBUThCO1FBRXBCLGNBQWM7UUFDZEgsT0FBT0MsUUFBUSxDQUFDRyxJQUFJLEdBQUdEO0lBQ3pCO0lBRUEsU0FBUztJQUNULE1BQU1sQixlQUFlO1FBQ25CaEIsa0JBQWtCO1FBQ2xCRSxnQkFBZ0I7UUFFaEIsSUFBSTtnQkFNRWtDLGdCQUErQkEsaUJBRXhCQSxpQkFBaUNBO1lBUDVDLE1BQU1BLFdBQVcsTUFBTXJELGtFQUFpQkE7WUFDeENvQixRQUFRQyxHQUFHLENBQUMsZ0JBQWdCZ0M7WUFFNUIsY0FBYztZQUNkLElBQUlDLGFBQWEsRUFBRTtZQUNuQixJQUFJRCxFQUFBQSxpQkFBQUEsU0FBU0UsSUFBSSxjQUFiRixxQ0FBQUEsZUFBZUcsSUFBSSxNQUFLLFNBQU9ILGtCQUFBQSxTQUFTRSxJQUFJLGNBQWJGLHNDQUFBQSxnQkFBZUUsSUFBSSxHQUFFO2dCQUN0REQsYUFBYUQsU0FBU0UsSUFBSSxDQUFDQSxJQUFJO1lBQ2pDLE9BQU8sSUFBSUYsRUFBQUEsa0JBQUFBLFNBQVNFLElBQUksY0FBYkYsc0NBQUFBLGdCQUFlSSxNQUFNLE1BQUssU0FBT0osa0JBQUFBLFNBQVNFLElBQUksY0FBYkYsc0NBQUFBLGdCQUFlRSxJQUFJLEdBQUU7Z0JBQy9ERCxhQUFhRCxTQUFTRSxJQUFJLENBQUNBLElBQUk7WUFDakMsT0FBTyxJQUFJRyxNQUFNQyxPQUFPLENBQUNOLFNBQVNFLElBQUksR0FBRztnQkFDdkNELGFBQWFELFNBQVNFLElBQUk7WUFDNUI7WUFFQSxJQUFJRCxXQUFXZCxNQUFNLEdBQUcsR0FBRztnQkFDekIzQixXQUFXeUM7Z0JBQ1gsTUFBTWIsY0FBY2EsVUFBVSxDQUFDLEVBQUU7Z0JBQ2pDdkMsa0JBQWtCMEI7Z0JBRWxCLGNBQWM7Z0JBQ2RuQywyQkFBQUEscUNBQUFBLGVBQWlCbUM7Z0JBRWpCLGVBQWU7Z0JBQ2ZDLGFBQWFELFlBQVlFLEVBQUU7Z0JBRTNCdkIsUUFBUUMsR0FBRyxDQUFDLGdCQUFnQmlDLFdBQVdkLE1BQU07Z0JBQzdDcEIsUUFBUUMsR0FBRyxDQUFDLGNBQWNvQjtZQUM1QixPQUFPO2dCQUNMdEIsZ0JBQWdCO1lBQ2xCO1FBQ0YsRUFBRSxPQUFPeUMsT0FBTztZQUNkeEMsUUFBUXdDLEtBQUssQ0FBQyxhQUFhQTtZQUMzQnpDLGdCQUFnQjtRQUNsQixTQUFVO1lBQ1JGLGtCQUFrQjtRQUNwQjtJQUNGO0lBRUEsY0FBYztJQUNkLE1BQU15QixlQUFlLE9BQU9tQjtRQUMxQixJQUFJLEVBQUN6RCxxQkFBQUEsK0JBQUFBLFNBQVV1QyxFQUFFLEdBQUU7WUFDakJ2QixRQUFRQyxHQUFHLENBQUM7WUFDWjtRQUNGO1FBRUEsWUFBWTtRQUNaZCxtQkFBbUJBLGdCQUFnQixFQUFFLEVBQUUsTUFBTTtRQUU3QyxJQUFJO2dCQUtFOEM7WUFKSmpDLFFBQVFDLEdBQUcsQ0FBQyxXQUFXO2dCQUFFd0M7Z0JBQVVDLFdBQVcxRCxTQUFTdUMsRUFBRTtZQUFDO1lBQzFELE1BQU1VLFdBQVcsTUFBTXBELG9EQUFRQSxDQUFDOEQsaUJBQWlCLENBQUNGLFVBQVV6RCxTQUFTdUMsRUFBRTtZQUN2RXZCLFFBQVFDLEdBQUcsQ0FBQyxjQUFjZ0M7WUFFMUIsSUFBSUEsRUFBQUEsaUJBQUFBLFNBQVNFLElBQUksY0FBYkYscUNBQUFBLGVBQWVHLElBQUksTUFBSyxLQUFLO2dCQUMvQixNQUFNUSxZQUFZWCxTQUFTRSxJQUFJLENBQUNBLElBQUksSUFBSSxFQUFFO2dCQUMxQyxZQUFZO2dCQUNaaEQsbUJBQW1CQSxnQkFBZ0J5RCxXQUFXLE9BQU87Z0JBQ3JENUMsUUFBUUMsR0FBRyxDQUFDLGdCQUFnQjJDLFVBQVV4QixNQUFNO1lBQzlDLE9BQU87Z0JBQ0wsTUFBTXlCLFdBQVc7Z0JBQ2pCLFlBQVk7Z0JBQ1oxRCxtQkFBbUJBLGdCQUFnQixFQUFFLEVBQUUsT0FBTzBEO1lBQ2hEO1FBQ0YsRUFBRSxPQUFPTCxPQUFPO1lBQ2R4QyxRQUFRd0MsS0FBSyxDQUFDLGFBQWFBO1lBQzNCLE1BQU1LLFdBQVc7WUFDakIsWUFBWTtZQUNaMUQsbUJBQW1CQSxnQkFBZ0IsRUFBRSxFQUFFLE9BQU8wRDtRQUNoRDtJQUNGO0lBRUEscUJBQ0UsOERBQUNDO1FBQU1DLFdBQVU7OzBCQUNmLDhEQUFDQztnQkFBSUQsV0FBVTs7a0NBQ2IsOERBQUN0RSwrS0FBTUE7Ozs7O2tDQUNQLDhEQUFDd0U7d0JBQUtGLFdBQVU7a0NBQW9COzs7Ozs7Ozs7Ozs7MEJBR3RDLDhEQUFDQztnQkFBSUQsV0FBVTs7a0NBQ2IsOERBQUNwRSxrREFBS0E7d0JBQ0p1RSxLQUFLbEUsU0FBU21FLFNBQVMsSUFBSTt3QkFDM0JDLEtBQUtwRSxTQUFTcUUsUUFBUSxJQUFJO3dCQUMxQkMsT0FBTzt3QkFDUEMsUUFBUTt3QkFDUlIsV0FBVTt3QkFDVlMsT0FBTzs0QkFBRUMsaUJBQWlCO3dCQUFROzs7Ozs7a0NBRXBDLDhEQUFDVDt3QkFBSUQsV0FBVTs7MENBQ2IsOERBQUNXO2dDQUFFWCxXQUFVOzBDQUFnQi9ELFNBQVNxRSxRQUFRLElBQUk7Ozs7OzswQ0FDbEQsOERBQUNLO2dDQUFFWCxXQUFVOzBDQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQUdqQyw4REFBQ1k7Z0JBQUlaLFdBQVU7MEJBQ2IsNEVBQUNhOzhCQUNFOUMsU0FBUytDLEdBQUcsQ0FBQyxDQUFDQyxxQkFDYiw4REFBQ2xHLHVEQUFjOztnQ0FDWmtHLEtBQUs1QyxXQUFXLGlCQUNmLDhEQUFDOEM7b0NBQUdqQixXQUFVO29DQUFvQmtCLEtBQUsvRDs7c0RBQ3JDLDhEQUFDOEM7NENBQ0NELFdBQVcsWUFBd0R6RCxPQUE1Q0YsZUFBZTBFLEtBQUs5QyxJQUFJLEdBQUcsV0FBVyxJQUFHLEtBQThDLE9BQTNDMUIsc0JBQXNCLGtCQUFrQjs0Q0FDM0c0RSxTQUFTLENBQUNDO2dEQUNSQSxFQUFFQyxjQUFjO2dEQUNoQkQsRUFBRUUsZUFBZTtnREFDakJsRDs0Q0FDRjs7OERBRUEsOERBQUMyQyxLQUFLL0MsSUFBSTtvREFBQ2dDLFdBQVU7Ozs7Ozs4REFDckIsOERBQUNFOzhEQUFNYSxLQUFLOUMsSUFBSTs7Ozs7OzhEQUNoQiw4REFBQ3RDLCtLQUFXQTtvREFDVnFFLFdBQVcsa0JBQXVELE9BQXJDekQsc0JBQXNCLFlBQVk7b0RBQy9EZ0YsTUFBTTtvREFDTmQsT0FBTzt3REFDTGUsV0FBV2pGLHNCQUFzQixtQkFBbUI7d0RBQ3BEa0YsWUFBWTtvREFDZDs7Ozs7Ozs7Ozs7O3dDQUdIbEYscUNBQ0MsOERBQUMwRDs0Q0FDQ0QsV0FBVyxpQkFBeUYsT0FBeEUsa0JBQW1CakQsZ0JBQWdCTixRQUFRNEIsTUFBTSxLQUFLLElBQUssVUFBVTs0Q0FDakc4QyxTQUFTLENBQUNDO2dEQUNSQSxFQUFFQyxjQUFjO2dEQUNoQkQsRUFBRUUsZUFBZTs0Q0FDbkI7c0RBRUN6RSwrQkFDQyw4REFBQ29EO2dEQUFJRCxXQUFVOztrRUFDYiw4REFBQ0M7d0RBQUlELFdBQVU7Ozs7OztvREFBd0I7Ozs7Ozs0REFHdkNqRCw2QkFDRiw4REFBQ2tEO2dEQUFJRCxXQUFVOzBEQUNaakQ7Ozs7OzREQUVETixRQUFRNEIsTUFBTSxHQUFHLElBQ25CNUIsUUFBUXFFLEdBQUcsQ0FBQyxDQUFDcEMsdUJBQ1gsOERBQUN1QjtvREFFQ0QsV0FBVyxpQkFBb0UsT0FBbkRyRCxDQUFBQSwyQkFBQUEscUNBQUFBLGVBQWdCNkIsRUFBRSxNQUFLRSxPQUFPRixFQUFFLEdBQUcsYUFBYTtvREFDNUUyQyxTQUFTLENBQUNDO3dEQUNSQSxFQUFFQyxjQUFjO3dEQUNoQkQsRUFBRUUsZUFBZTt3REFDakI3QyxtQkFBbUJDO29EQUNyQjs4REFFQSw0RUFBQ3VCO3dEQUFJRCxXQUFVOzswRUFDYiw4REFBQ0M7Z0VBQUlELFdBQVU7MEVBQWV0QixPQUFPZ0QsVUFBVTs7Ozs7OzREQUM5Q2hELE9BQU9pRCxRQUFRLElBQUlqRCxPQUFPa0QsUUFBUSxrQkFDakMsOERBQUMzQjtnRUFBSUQsV0FBVTswRUFDWmpFLHVFQUFtQkEsQ0FBQzJDLE9BQU9pRCxRQUFRLEVBQUVqRCxPQUFPbUQsSUFBSSxFQUFFbkQsT0FBT2tELFFBQVE7Ozs7Ozs7Ozs7OzttREFabkVsRCxPQUFPRixFQUFFOzs7OytFQW1CbEIsOERBQUN5QjtnREFBSUQsV0FBVTswREFBaUM7Ozs7Ozs7Ozs7Ozs7Ozs7OERBUXhELDhEQUFDaUI7b0NBQ0NqQixXQUFXLFlBQXFELE9BQXpDM0QsZUFBZTBFLEtBQUs5QyxJQUFJLEdBQUcsV0FBVztvQ0FDN0RrRCxTQUFTLENBQUNDO3dDQUNSQSxFQUFFQyxjQUFjO3dDQUNoQkQsRUFBRUUsZUFBZTt3Q0FDakJyRSxRQUFRQyxHQUFHLENBQUMsVUFBVTZELEtBQUs5QyxJQUFJO3dDQUMvQmhCLFFBQVFDLEdBQUcsQ0FBQyxhQUFhWDt3Q0FDekJVLFFBQVFDLEdBQUcsQ0FBQyxVQUFVYjt3Q0FFdEIsa0JBQWtCO3dDQUNsQmUsZ0JBQWdCRyxPQUFPLEdBQUc7d0NBRTFCLFVBQVU7d0NBQ1ZmLHVCQUF1Qjt3Q0FFdkIsVUFBVTt3Q0FDVkYsY0FBY3lFLEtBQUs5QyxJQUFJO3dDQUV2QixVQUFVO3dDQUNWL0IsNEJBQUFBLHNDQUFBQSxnQkFBa0I2RSxLQUFLOUMsSUFBSTt3Q0FFM0JoQixRQUFRQyxHQUFHLENBQUMsZUFBZTZELEtBQUs5QyxJQUFJLEVBQUU7d0NBRXRDLFVBQVU7d0NBQ1Y2RCxXQUFXOzRDQUNUMUUsZ0JBQWdCRyxPQUFPLEdBQUc7d0NBQzVCLEdBQUc7b0NBQ0w7O3NEQUVBLDhEQUFDd0QsS0FBSy9DLElBQUk7NENBQUNnQyxXQUFVOzs7Ozs7c0RBQ3JCLDhEQUFDRTtzREFBTWEsS0FBSzlDLElBQUk7Ozs7Ozs7Ozs7OztnQ0FHbkI4QyxLQUFLN0MsVUFBVSxrQkFBSSw4REFBQytCO29DQUFJRCxXQUFVOzs7Ozs7OzJCQXRHaEJlLEtBQUs5QyxJQUFJOzs7Ozs7Ozs7Ozs7Ozs7MEJBMkdwQyw4REFBQ2dDO2dCQUFJRCxXQUFVO2dCQUFpQm1CLFNBQVN4Qzs7a0NBQ3ZDLDhEQUFDbEQsK0tBQWVBO3dCQUFDdUUsV0FBVTs7Ozs7O2tDQUMzQiw4REFBQ0U7a0NBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUlkO0dBaFhNbEU7S0FBQUE7QUFrWE4sK0RBQWVBLFdBQVdBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vYXBwL3dvcmtiZW5jaC9jb21wb25lbnRzL0xlZnRTaWRlYmFyLnRzeD9jNDI4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QsIHVzZVJlZiwgdXNlQ2FsbGJhY2sgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBMYXlvdXREYXNoYm9hcmQsIFVzZXJzLCBMaXN0VG9kbywgQnJpZWZjYXNlLCBCb29rLCBCb29rT3BlbiwgU2V0dGluZ3MsIEFycm93TGVmdENpcmNsZSwgU2hpZWxkLCBDaGV2cm9uRG93biB9IGZyb20gJ2x1Y2lkZS1yZWFjdCc7XG5pbXBvcnQgSW1hZ2UgZnJvbSAnbmV4dC9pbWFnZSc7XG5pbXBvcnQgeyBVc2VySW5mbyB9IGZyb20gJ0AvbGliL2FwaS91c2VyJztcbmltcG9ydCB7IGdldFRlYWNoZXJTY2hvb2xzIH0gZnJvbSAnQC9saWIvYXBpL3NjaG9vbCc7XG5pbXBvcnQgeyBjbGFzc0FwaSB9IGZyb20gJ0AvbGliL2FwaS9jbGFzcyc7XG5pbXBvcnQgeyBmb3JtYXRTY2hvb2xBZGRyZXNzIH0gZnJvbSAnQC9saWIvdXRpbHMvYWRkcmVzcyc7XG5cbmludGVyZmFjZSBTY2hvb2wge1xuICBpZDogbnVtYmVyO1xuICBzY2hvb2xOYW1lOiBzdHJpbmc7XG4gIHByb3ZpbmNlPzogc3RyaW5nO1xuICBjaXR5Pzogc3RyaW5nO1xuICBkaXN0cmljdD86IHN0cmluZztcbiAgY3JlYXRlVGltZT86IHN0cmluZztcbiAgdXBkYXRlVGltZT86IHN0cmluZztcbn1cblxuaW50ZXJmYWNlIENsYXNzSW5mbyB7XG4gIGlkOiBudW1iZXI7XG4gIHNjaG9vbElkOiBudW1iZXI7XG4gIGdyYWRlOiBzdHJpbmc7XG4gIGNsYXNzTmFtZTogc3RyaW5nO1xuICB0ZWFjaGVySWQ6IG51bWJlcjtcbiAgYXNzaXN0YW50VGVhY2hlcklkOiBudW1iZXI7XG4gIGludml0ZUNvZGU6IHN0cmluZztcbiAgY3JlYXRlVGltZTogc3RyaW5nO1xuICB1cGRhdGVUaW1lOiBzdHJpbmc7XG4gIHN0dWRlbnRDb3VudDogbnVtYmVyO1xuICBpc0Fzc2lzdGFudD86IGJvb2xlYW47XG59XG5cbmludGVyZmFjZSBMZWZ0U2lkZWJhclByb3BzIHtcbiAgdXNlckluZm86IFBhcnRpYWw8VXNlckluZm8+O1xuICBvbk1lbnVJdGVtQ2xpY2s/OiAoaXRlbU5hbWU6IHN0cmluZykgPT4gdm9pZDtcbiAgb25TY2hvb2xTZWxlY3Q/OiAoc2Nob29sOiBTY2hvb2wpID0+IHZvaWQ7XG4gIG9uQ2xhc3Nlc1VwZGF0ZT86IChjbGFzc2VzOiBDbGFzc0luZm9bXSwgbG9hZGluZzogYm9vbGVhbiwgZXJyb3I6IHN0cmluZyB8IG51bGwpID0+IHZvaWQ7XG59XG5cbmNvbnN0IExlZnRTaWRlYmFyID0gKHsgdXNlckluZm8sIG9uTWVudUl0ZW1DbGljaywgb25TY2hvb2xTZWxlY3QsIG9uQ2xhc3Nlc1VwZGF0ZSB9OiBMZWZ0U2lkZWJhclByb3BzKSA9PiB7XG4gIGNvbnN0IFthY3RpdmVJdGVtLCBzZXRBY3RpdmVJdGVtXSA9IHVzZVN0YXRlKCflv6vpgJ/lvIDlp4snKTtcbiAgY29uc3QgW2lzQ2xhc3NEcm9wZG93bk9wZW4sIHNldElzQ2xhc3NEcm9wZG93bk9wZW5dID0gdXNlU3RhdGUoZmFsc2UpO1xuXG5cbiAgY29uc3QgW3NjaG9vbHMsIHNldFNjaG9vbHNdID0gdXNlU3RhdGU8U2Nob29sW10+KFtdKTtcbiAgY29uc3QgW3NlbGVjdGVkU2Nob29sLCBzZXRTZWxlY3RlZFNjaG9vbF0gPSB1c2VTdGF0ZTxTY2hvb2wgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW3NjaG9vbHNMb2FkaW5nLCBzZXRTY2hvb2xzTG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtzY2hvb2xzRXJyb3IsIHNldFNjaG9vbHNFcnJvcl0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKTtcblxuICAvLyDnm5HlkKxhY3RpdmVJdGVt5Y+Y5YyW77yM5b2T6L+b5YWl54+t57qn566h55CG6aG16Z2i5pe26Ieq5Yqo5omT5byA5LiL5ouJ6I+c5Y2VXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc29sZS5sb2coJ2FjdGl2ZUl0ZW0g54q25oCB5Y+Y5YyWOicsIGFjdGl2ZUl0ZW0pO1xuXG4gICAgLy8g5b2T5YiH5o2i5Yiw54+t57qn566h55CG6aG16Z2i5pe277yM6Ieq5Yqo5omT5byA5LiL5ouJ6I+c5Y2VXG4gICAgaWYgKGFjdGl2ZUl0ZW0gPT09ICfnj63nuqfnrqHnkIYnKSB7XG4gICAgICBzZXRJc0NsYXNzRHJvcGRvd25PcGVuKHRydWUpO1xuICAgIH1cbiAgfSwgW2FjdGl2ZUl0ZW1dKTtcblxuICAvLyDkuIvmi4noj5zljZXnmoRyZWbvvIznlKjkuo7mo4DmtYvngrnlh7vlpJbpg6jljLrln59cbiAgY29uc3QgZHJvcGRvd25SZWYgPSB1c2VSZWY8SFRNTExJRWxlbWVudD4obnVsbCk7XG5cbiAgLy8g5qCH5b+X5L2N77yM6Ziy5q2i5a+86Iiq54K55Ye75ZKM5aSW6YOo54K55Ye75Yay56qBXG4gIGNvbnN0IGlzTmF2aWdhdGluZ1JlZiA9IHVzZVJlZihmYWxzZSk7XG5cbiAgLy8g54K55Ye75aSW6YOo5Yy65Z+f5YWz6Zet5LiL5ouJ6I+c5Y2V5bm25YiH5o2i5Yiw54+t57qn566h55CG6aG16Z2iXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgaGFuZGxlQ2xpY2tPdXRzaWRlID0gKGV2ZW50OiBNb3VzZUV2ZW50KSA9PiB7XG4gICAgICBpZiAoZHJvcGRvd25SZWYuY3VycmVudCAmJiAhZHJvcGRvd25SZWYuY3VycmVudC5jb250YWlucyhldmVudC50YXJnZXQgYXMgTm9kZSkpIHtcbiAgICAgICAgY29uc29sZS5sb2coJ+eCueWHu+WklumDqOWMuuWfn++8jGlzTmF2aWdhdGluZzonLCBpc05hdmlnYXRpbmdSZWYuY3VycmVudCk7XG5cbiAgICAgICAgLy8g5aaC5p6c5q2j5Zyo5a+86Iiq77yM5LiN5aSE55CG5aSW6YOo54K55Ye7XG4gICAgICAgIGlmIChpc05hdmlnYXRpbmdSZWYuY3VycmVudCkge1xuICAgICAgICAgIGlzTmF2aWdhdGluZ1JlZi5jdXJyZW50ID0gZmFsc2U7XG4gICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8g5aaC5p6c5b2T5YmN5rS76LeD6aG55piv54+t57qn566h55CG77yM5LiN5YWz6Zet5LiL5ouJ6I+c5Y2VXG4gICAgICAgIGlmIChhY3RpdmVJdGVtID09PSAn54+t57qn566h55CGJykge1xuICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIOWFs+mXreS4i+aLieiPnOWNlVxuICAgICAgICBzZXRJc0NsYXNzRHJvcGRvd25PcGVuKGZhbHNlKTtcblxuICAgICAgICAvLyDlpoLmnpzmnInpgInkuK3nmoTlrabmoKHvvIzliIfmjaLliLDnj63nuqfnrqHnkIbpobXpnaJcbiAgICAgICAgaWYgKHNlbGVjdGVkU2Nob29sKSB7XG4gICAgICAgICAgc2V0QWN0aXZlSXRlbSgn54+t57qn566h55CGJyk7XG4gICAgICAgICAgb25NZW51SXRlbUNsaWNrPy4oJ+ePree6p+euoeeQhicpO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfTtcblxuICAgIGlmIChpc0NsYXNzRHJvcGRvd25PcGVuKSB7XG4gICAgICBkb2N1bWVudC5hZGRFdmVudExpc3RlbmVyKCdtb3VzZWRvd24nLCBoYW5kbGVDbGlja091dHNpZGUpO1xuICAgIH1cblxuICAgIHJldHVybiAoKSA9PiB7XG4gICAgICBkb2N1bWVudC5yZW1vdmVFdmVudExpc3RlbmVyKCdtb3VzZWRvd24nLCBoYW5kbGVDbGlja091dHNpZGUpO1xuICAgIH07XG4gIH0sIFtpc0NsYXNzRHJvcGRvd25PcGVuLCBzZWxlY3RlZFNjaG9vbCwgb25NZW51SXRlbUNsaWNrLCBhY3RpdmVJdGVtXSk7XG5cbiAgLy8g55uR5ZCs6Ieq5a6a5LmJ5LqL5Lu25p2l5YWz6Zet5LiL5ouJ6I+c5Y2VXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgaGFuZGxlQ2xvc2VEcm9wZG93biA9ICgpID0+IHtcbiAgICAgIHNldElzQ2xhc3NEcm9wZG93bk9wZW4oZmFsc2UpO1xuICAgIH07XG5cbiAgICBkb2N1bWVudC5hZGRFdmVudExpc3RlbmVyKCdjbG9zZURyb3Bkb3duJywgaGFuZGxlQ2xvc2VEcm9wZG93bik7XG5cbiAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgZG9jdW1lbnQucmVtb3ZlRXZlbnRMaXN0ZW5lcignY2xvc2VEcm9wZG93bicsIGhhbmRsZUNsb3NlRHJvcGRvd24pO1xuICAgIH07XG4gIH0sIFtdKTtcblxuICAvLyDojrflj5bmlZnluIjnrqHnkIbnmoTlrabmoKHliJfooahcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBmZXRjaFNjaG9vbHMoKTtcbiAgfSwgW10pO1xuXG5cblxuXG5cbiAgY29uc3QgbmF2SXRlbXMgPSBbXG4gICAgeyBpY29uOiBMYXlvdXREYXNoYm9hcmQsIG5hbWU6ICflv6vpgJ/lvIDlp4snLCBoYXNEaXZpZGVyOiB0cnVlIH0sXG4gICAgeyBpY29uOiBVc2VycywgbmFtZTogJ+ePree6p+euoeeQhicsIGhhc0Ryb3Bkb3duOiB0cnVlIH0sXG4gICAgeyBpY29uOiBMaXN0VG9kbywgbmFtZTogJ+ePree6p+S7u+WKoScgfSxcbiAgICB7IGljb246IEJyaWVmY2FzZSwgbmFtZTogJ+ePree6p+mhueebricsIGhhc0RpdmlkZXI6IHRydWUgfSxcbiAgICB7IGljb246IEJvb2tPcGVuLCBuYW1lOiAn5a6Y5pa56K++56iLJyB9LFxuICAgIHsgaWNvbjogQm9vaywgbmFtZTogJ+ivvueoi+euoeeQhicgfSxcbiAgICB7IGljb246IFNldHRpbmdzLCBuYW1lOiAn5qih5p2/566h55CGJyB9LFxuICBdO1xuXG4gIC8vIOWkhOeQhuePree6p+euoeeQhueCueWHu1xuICBjb25zdCBoYW5kbGVDbGFzc01hbmFnZW1lbnRDbGljayA9ICgpID0+IHtcbiAgICAvLyDorr7nva7kuLrmtLvot4PnirbmgIFcbiAgICBzZXRBY3RpdmVJdGVtKCfnj63nuqfnrqHnkIYnKTtcblxuICAgIC8vIOWmguaenOayoeaciemAieS4reWtpuagoeS4lOacieWPr+eUqOWtpuagoe+8jOiHquWKqOmAieaLqeesrOS4gOS4quWtpuagoVxuICAgIGlmICghc2VsZWN0ZWRTY2hvb2wgJiYgc2Nob29scy5sZW5ndGggPiAwKSB7XG4gICAgICBjb25zdCBmaXJzdFNjaG9vbCA9IHNjaG9vbHNbMF07XG4gICAgICBzZXRTZWxlY3RlZFNjaG9vbChmaXJzdFNjaG9vbCk7XG4gICAgICBvblNjaG9vbFNlbGVjdD8uKGZpcnN0U2Nob29sKTtcbiAgICAgIGZldGNoQ2xhc3NlcyhmaXJzdFNjaG9vbC5pZCk7XG4gICAgICBjb25zb2xlLmxvZygn54+t57qn566h55CG77ya6Ieq5Yqo6YCJ5oup56ys5LiA5Liq5a2m5qChOicsIGZpcnN0U2Nob29sKTtcbiAgICB9IGVsc2UgaWYgKCFzZWxlY3RlZFNjaG9vbCAmJiBzY2hvb2xzLmxlbmd0aCA9PT0gMCAmJiAhc2Nob29sc0xvYWRpbmcpIHtcbiAgICAgIC8vIOWmguaenOayoeacieWtpuagoeaVsOaNruS4lOS4jeWcqOWKoOi9veS4re+8jOmHjeaWsOiOt+WPluWtpuagoeWIl+ihqFxuICAgICAgY29uc29sZS5sb2coJ+ePree6p+euoeeQhu+8muayoeacieWtpuagoeaVsOaNru+8jOmHjeaWsOiOt+WPluWtpuagoeWIl+ihqCcpO1xuICAgICAgZmV0Y2hTY2hvb2xzKCk7XG4gICAgfVxuXG4gICAgLy8g5aaC5p6c5b2T5YmN5bey57uP5piv54+t57qn566h55CG6aG16Z2i5LiU5LiL5ouJ6I+c5Y2V5bey5omT5byA77yM5YiZ5YWz6Zet77yb5ZCm5YiZ5omT5byAXG4gICAgaWYgKGFjdGl2ZUl0ZW0gPT09ICfnj63nuqfnrqHnkIYnICYmIGlzQ2xhc3NEcm9wZG93bk9wZW4pIHtcbiAgICAgIHNldElzQ2xhc3NEcm9wZG93bk9wZW4oZmFsc2UpO1xuICAgIH0gZWxzZSB7XG4gICAgICBzZXRJc0NsYXNzRHJvcGRvd25PcGVuKHRydWUpO1xuICAgIH1cbiAgICAvLyDpgJrnn6XniLbnu4Tku7ZcbiAgICBvbk1lbnVJdGVtQ2xpY2s/Lign54+t57qn566h55CGJyk7XG4gIH07XG5cbiAgLy8g5aSE55CG5a2m5qCh6YCJ5oupXG4gIGNvbnN0IGhhbmRsZVNjaG9vbFNlbGVjdCA9IHVzZUNhbGxiYWNrKChzY2hvb2w6IFNjaG9vbCkgPT4ge1xuICAgIGNvbnNvbGUubG9nKCdoYW5kbGVTY2hvb2xTZWxlY3Qg6KKr6LCD55So77yM5b2T5YmNYWN0aXZlSXRlbTonLCBhY3RpdmVJdGVtKTtcblxuICAgIC8vIOS4jeWFs+mXreS4i+aLieiPnOWNle+8jOWPquabtOaWsOmAieS4reeKtuaAgVxuICAgIHNldFNlbGVjdGVkU2Nob29sKHNjaG9vbCk7XG5cbiAgICAvLyDlvLrliLbliIfmjaLliLDnj63nuqfnrqHnkIbpobXpnaLvvIjml6DorrrlvZPliY3lnKjku4DkuYjpobXpnaLvvIlcbiAgICBzZXRBY3RpdmVJdGVtKCfnj63nuqfnrqHnkIYnKTtcbiAgICBvbk1lbnVJdGVtQ2xpY2s/Lign54+t57qn566h55CGJyk7XG5cbiAgICAvLyDlp4vnu4jpgJrnn6XniLbnu4Tku7blrabmoKHpgInmi6nlj5jljJbvvIjnlKjkuo7mlbDmja7mm7TmlrDvvIlcbiAgICBvblNjaG9vbFNlbGVjdD8uKHNjaG9vbCk7XG5cbiAgICAvLyDojrflj5bor6XlrabmoKHnmoTnj63nuqfliJfooahcbiAgICBmZXRjaENsYXNzZXMoc2Nob29sLmlkKTtcbiAgfSwgW29uTWVudUl0ZW1DbGljaywgb25TY2hvb2xTZWxlY3RdKTtcblxuICAvLyDlpITnkIbov5Tlm57kuLvpobVcbiAgY29uc3QgaGFuZGxlQmFja1RvSG9tZSA9ICgpID0+IHtcbiAgICBjb25zb2xlLmxvZygn54K55Ye76L+U5Zue5Li76aG15oyJ6ZKuJyk7XG5cbiAgICAvLyDojrflj5blvZPliY3ln5/lkI3lkoznq6/lj6PvvIznhLblkI7ot7PovazliLBob21l6aG16Z2iXG4gICAgY29uc3QgY3VycmVudE9yaWdpbiA9IHdpbmRvdy5sb2NhdGlvbi5vcmlnaW47XG4gICAgY29uc3QgaG9tZVVybCA9IGAke2N1cnJlbnRPcmlnaW59L2hvbWVgO1xuICAgIGNvbnNvbGUubG9nKCflvZPliY3ln5/lkI06JywgY3VycmVudE9yaWdpbik7XG4gICAgY29uc29sZS5sb2coJ+i3s+i9rOWIsDonLCBob21lVXJsKTtcblxuICAgIC8vIOebtOaOpei3s+i9rOWIsGhvbWXpobXpnaJcbiAgICB3aW5kb3cubG9jYXRpb24uaHJlZiA9IGhvbWVVcmw7XG4gIH07XG5cbiAgLy8g6I635Y+W5a2m5qCh5YiX6KGoXG4gIGNvbnN0IGZldGNoU2Nob29scyA9IGFzeW5jICgpID0+IHtcbiAgICBzZXRTY2hvb2xzTG9hZGluZyh0cnVlKTtcbiAgICBzZXRTY2hvb2xzRXJyb3IobnVsbCk7XG5cbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBnZXRUZWFjaGVyU2Nob29scygpO1xuICAgICAgY29uc29sZS5sb2coJ+iOt+WPluWtpuagoeWIl+ihqEFQSeWTjeW6lDonLCByZXNwb25zZSk7XG5cbiAgICAgIC8vIOajgOafpeWkmuenjeWPr+iDveeahOWTjeW6lOagvOW8j1xuICAgICAgbGV0IHNjaG9vbExpc3QgPSBbXTtcbiAgICAgIGlmIChyZXNwb25zZS5kYXRhPy5jb2RlID09PSAyMDAgJiYgcmVzcG9uc2UuZGF0YT8uZGF0YSkge1xuICAgICAgICBzY2hvb2xMaXN0ID0gcmVzcG9uc2UuZGF0YS5kYXRhO1xuICAgICAgfSBlbHNlIGlmIChyZXNwb25zZS5kYXRhPy5zdGF0dXMgPT09IDIwMCAmJiByZXNwb25zZS5kYXRhPy5kYXRhKSB7XG4gICAgICAgIHNjaG9vbExpc3QgPSByZXNwb25zZS5kYXRhLmRhdGE7XG4gICAgICB9IGVsc2UgaWYgKEFycmF5LmlzQXJyYXkocmVzcG9uc2UuZGF0YSkpIHtcbiAgICAgICAgc2Nob29sTGlzdCA9IHJlc3BvbnNlLmRhdGE7XG4gICAgICB9XG5cbiAgICAgIGlmIChzY2hvb2xMaXN0Lmxlbmd0aCA+IDApIHtcbiAgICAgICAgc2V0U2Nob29scyhzY2hvb2xMaXN0KTtcbiAgICAgICAgY29uc3QgZmlyc3RTY2hvb2wgPSBzY2hvb2xMaXN0WzBdO1xuICAgICAgICBzZXRTZWxlY3RlZFNjaG9vbChmaXJzdFNjaG9vbCk7XG5cbiAgICAgICAgLy8g6YCa55+l54i257uE5Lu25a2m5qCh6YCJ5oup5Y+Y5YyWXG4gICAgICAgIG9uU2Nob29sU2VsZWN0Py4oZmlyc3RTY2hvb2wpO1xuXG4gICAgICAgIC8vIOiOt+WPluesrOS4gOS4quWtpuagoeeahOePree6p+WIl+ihqFxuICAgICAgICBmZXRjaENsYXNzZXMoZmlyc3RTY2hvb2wuaWQpO1xuXG4gICAgICAgIGNvbnNvbGUubG9nKCfmiJDlip/ojrflj5blrabmoKHliJfooajvvIzmlbDph486Jywgc2Nob29sTGlzdC5sZW5ndGgpO1xuICAgICAgICBjb25zb2xlLmxvZygn6Ieq5Yqo6YCJ5oup56ys5LiA5Liq5a2m5qChOicsIGZpcnN0U2Nob29sKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHNldFNjaG9vbHNFcnJvcign5pqC5peg5pWw5o2uJyk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPluWtpuagoeWIl+ihqOWksei0pTonLCBlcnJvcik7XG4gICAgICBzZXRTY2hvb2xzRXJyb3IoJ+ivt+ajgOafpee9kee7nOi/nuaOpeWksei0pScpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRTY2hvb2xzTG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIC8vIOiOt+WPluaMh+WumuWtpuagoeeahOePree6p+WIl+ihqFxuICBjb25zdCBmZXRjaENsYXNzZXMgPSBhc3luYyAoc2Nob29sSWQ6IG51bWJlcikgPT4ge1xuICAgIGlmICghdXNlckluZm8/LmlkKSB7XG4gICAgICBjb25zb2xlLmxvZygn55So5oi35pyq55m75b2V77yM5peg5rOV6I635Y+W54+t57qn5YiX6KGoJyk7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgLy8g6YCa55+l54i257uE5Lu25byA5aeL5Yqg6L29XG4gICAgb25DbGFzc2VzVXBkYXRlICYmIG9uQ2xhc3Nlc1VwZGF0ZShbXSwgdHJ1ZSwgbnVsbCk7XG5cbiAgICB0cnkge1xuICAgICAgY29uc29sZS5sb2coJ+iOt+WPluePree6p+WIl+ihqDonLCB7IHNjaG9vbElkLCB0ZWFjaGVySWQ6IHVzZXJJbmZvLmlkIH0pO1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBjbGFzc0FwaS5nZXRUZWFjaGVyQ2xhc3NlcyhzY2hvb2xJZCwgdXNlckluZm8uaWQpO1xuICAgICAgY29uc29sZS5sb2coJ+ePree6p+WIl+ihqEFQSeWTjeW6lDonLCByZXNwb25zZSk7XG5cbiAgICAgIGlmIChyZXNwb25zZS5kYXRhPy5jb2RlID09PSAyMDApIHtcbiAgICAgICAgY29uc3QgY2xhc3NMaXN0ID0gcmVzcG9uc2UuZGF0YS5kYXRhIHx8IFtdO1xuICAgICAgICAvLyDpgJrnn6XniLbnu4Tku7bmlbDmja7mm7TmlrBcbiAgICAgICAgb25DbGFzc2VzVXBkYXRlICYmIG9uQ2xhc3Nlc1VwZGF0ZShjbGFzc0xpc3QsIGZhbHNlLCBudWxsKTtcbiAgICAgICAgY29uc29sZS5sb2coJ+aIkOWKn+iOt+WPluePree6p+WIl+ihqO+8jOaVsOmHjzonLCBjbGFzc0xpc3QubGVuZ3RoKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGNvbnN0IGVycm9yTXNnID0gJ+iOt+WPluePree6p+WIl+ihqOWksei0pSc7XG4gICAgICAgIC8vIOmAmuefpeeItue7hOS7tumUmeivr+eKtuaAgVxuICAgICAgICBvbkNsYXNzZXNVcGRhdGUgJiYgb25DbGFzc2VzVXBkYXRlKFtdLCBmYWxzZSwgZXJyb3JNc2cpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfojrflj5bnj63nuqfliJfooajlpLHotKU6JywgZXJyb3IpO1xuICAgICAgY29uc3QgZXJyb3JNc2cgPSAn6K+35qOA5p+l572R57uc6L+e5o6lJztcbiAgICAgIC8vIOmAmuefpeeItue7hOS7tumUmeivr+eKtuaAgVxuICAgICAgb25DbGFzc2VzVXBkYXRlICYmIG9uQ2xhc3Nlc1VwZGF0ZShbXSwgZmFsc2UsIGVycm9yTXNnKTtcbiAgICB9XG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8YXNpZGUgY2xhc3NOYW1lPVwibGVmdC1zaWRlYmFyXCI+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInNpZGViYXItaGVhZGVyXCI+XG4gICAgICAgIDxTaGllbGQgLz5cbiAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LWJvbGRcIj7mlZnluIjnqbrpl7Q8L3NwYW4+XG4gICAgICA8L2Rpdj5cblxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZWFjaGVyLWluZm9cIj5cbiAgICAgICAgPEltYWdlXG4gICAgICAgICAgc3JjPXt1c2VySW5mby5hdmF0YXJVcmwgfHwgXCIvaW1hZ2VzL3hpYW9sdW8tZGVmYXVsdC53ZWJwXCJ9XG4gICAgICAgICAgYWx0PXt1c2VySW5mby5uaWNrTmFtZSB8fCBcIuWwj+a0m+WktOWDj1wifVxuICAgICAgICAgIHdpZHRoPXs0MH1cbiAgICAgICAgICBoZWlnaHQ9ezQwfVxuICAgICAgICAgIGNsYXNzTmFtZT1cImF2YXRhclwiXG4gICAgICAgICAgc3R5bGU9e3sgYmFja2dyb3VuZENvbG9yOiAnd2hpdGUnIH19XG4gICAgICAgIC8+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGVhY2hlci1kZXRhaWxzXCI+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGVhY2hlci1uYW1lXCI+e3VzZXJJbmZvLm5pY2tOYW1lIHx8ICfmnKrnmbvlvZUnfTwvcD5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZWFjaGVyLXRpdGxlXCI+5pWZ5biIPC9wPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgICAgPG5hdiBjbGFzc05hbWU9XCJzaWRlYmFyLW5hdlwiPlxuICAgICAgICA8dWw+XG4gICAgICAgICAge25hdkl0ZW1zLm1hcCgoaXRlbSkgPT4gKFxuICAgICAgICAgICAgPFJlYWN0LkZyYWdtZW50IGtleT17aXRlbS5uYW1lfT5cbiAgICAgICAgICAgICAge2l0ZW0uaGFzRHJvcGRvd24gPyAoXG4gICAgICAgICAgICAgICAgPGxpIGNsYXNzTmFtZT1cIm5hdi1pdGVtLWRyb3Bkb3duXCIgcmVmPXtkcm9wZG93blJlZn0+XG4gICAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YG5hdi1pdGVtICR7YWN0aXZlSXRlbSA9PT0gaXRlbS5uYW1lID8gJ2FjdGl2ZScgOiAnJ30gJHtpc0NsYXNzRHJvcGRvd25PcGVuID8gJ2Ryb3Bkb3duLW9wZW4nIDogJyd9YH1cbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KGUpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICBlLnByZXZlbnREZWZhdWx0KCk7XG4gICAgICAgICAgICAgICAgICAgICAgZS5zdG9wUHJvcGFnYXRpb24oKTtcbiAgICAgICAgICAgICAgICAgICAgICBoYW5kbGVDbGFzc01hbmFnZW1lbnRDbGljaygpO1xuICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICA8aXRlbS5pY29uIGNsYXNzTmFtZT1cIm5hdi1pY29uXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4+e2l0ZW0ubmFtZX08L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDxDaGV2cm9uRG93blxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGRyb3Bkb3duLWFycm93ICR7aXNDbGFzc0Ryb3Bkb3duT3BlbiA/ICdyb3RhdGVkJyA6ICcnfWB9XG4gICAgICAgICAgICAgICAgICAgICAgc2l6ZT17MTZ9XG4gICAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgICAgICAgIHRyYW5zZm9ybTogaXNDbGFzc0Ryb3Bkb3duT3BlbiA/ICdyb3RhdGUoMTgwZGVnKScgOiAncm90YXRlKDBkZWcpJyxcbiAgICAgICAgICAgICAgICAgICAgICAgIHRyYW5zaXRpb246ICd0cmFuc2Zvcm0gMC4zcyBlYXNlJ1xuICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIHtpc0NsYXNzRHJvcGRvd25PcGVuICYmIChcbiAgICAgICAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGRyb3Bkb3duLW1lbnUgJHsoc2Nob29sc0xvYWRpbmcgfHwgc2Nob29sc0Vycm9yIHx8IHNjaG9vbHMubGVuZ3RoID09PSAwKSA/ICdlbXB0eScgOiAnJ31gfVxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eyhlKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICBlLnByZXZlbnREZWZhdWx0KCk7XG4gICAgICAgICAgICAgICAgICAgICAgICBlLnN0b3BQcm9wYWdhdGlvbigpO1xuICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICB7c2Nob29sc0xvYWRpbmcgPyAoXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImRyb3Bkb3duLWl0ZW0gZGlzYWJsZWQgbG9hZGluZ1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImxvYWRpbmctc3Bpbm5lclwiPjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICDmraPlnKjliqDovb3lrabmoKHkv6Hmga8uLi5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICkgOiBzY2hvb2xzRXJyb3IgPyAoXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImRyb3Bkb3duLWl0ZW0gZGlzYWJsZWQgZXJyb3JcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge3NjaG9vbHNFcnJvcn1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICkgOiBzY2hvb2xzLmxlbmd0aCA+IDAgPyAoXG4gICAgICAgICAgICAgICAgICAgICAgICBzY2hvb2xzLm1hcCgoc2Nob29sKSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBrZXk9e3NjaG9vbC5pZH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2Bkcm9wZG93bi1pdGVtICR7c2VsZWN0ZWRTY2hvb2w/LmlkID09PSBzY2hvb2wuaWQgPyAnc2VsZWN0ZWQnIDogJyd9YH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZS5wcmV2ZW50RGVmYXVsdCgpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZS5zdG9wUHJvcGFnYXRpb24oKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhhbmRsZVNjaG9vbFNlbGVjdChzY2hvb2wpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNjaG9vbC1pbmZvXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNjaG9vbC1uYW1lXCI+e3NjaG9vbC5zY2hvb2xOYW1lfTwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3NjaG9vbC5wcm92aW5jZSAmJiBzY2hvb2wuZGlzdHJpY3QgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNjaG9vbC1sb2NhdGlvblwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtmb3JtYXRTY2hvb2xBZGRyZXNzKHNjaG9vbC5wcm92aW5jZSwgc2Nob29sLmNpdHksIHNjaG9vbC5kaXN0cmljdCl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICApKVxuICAgICAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImRyb3Bkb3duLWl0ZW0gZGlzYWJsZWQgbm8tZGF0YVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICDmmoLml6DmlbDmja5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICA8L2xpPlxuICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgIDxsaVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgbmF2LWl0ZW0gJHthY3RpdmVJdGVtID09PSBpdGVtLm5hbWUgPyAnYWN0aXZlJyA6ICcnfWB9XG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICBlLnByZXZlbnREZWZhdWx0KCk7XG4gICAgICAgICAgICAgICAgICAgIGUuc3RvcFByb3BhZ2F0aW9uKCk7XG4gICAgICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCfngrnlh7vlr7zoiKrpobk6JywgaXRlbS5uYW1lKTtcbiAgICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ+W9k+WJjeS4i+aLieiPnOWNleeKtuaAgTonLCBpc0NsYXNzRHJvcGRvd25PcGVuKTtcbiAgICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ+W9k+WJjea0u+i3g+mhuTonLCBhY3RpdmVJdGVtKTtcblxuICAgICAgICAgICAgICAgICAgICAvLyDorr7nva7lr7zoiKrmoIflv5fvvIzpmLLmraLlpJbpg6jngrnlh7vlubLmibBcbiAgICAgICAgICAgICAgICAgICAgaXNOYXZpZ2F0aW5nUmVmLmN1cnJlbnQgPSB0cnVlO1xuXG4gICAgICAgICAgICAgICAgICAgIC8vIOWFiOWFs+mXreS4i+aLieiPnOWNlVxuICAgICAgICAgICAgICAgICAgICBzZXRJc0NsYXNzRHJvcGRvd25PcGVuKGZhbHNlKTtcblxuICAgICAgICAgICAgICAgICAgICAvLyDnhLblkI7mm7TmlrDmtLvot4PpoblcbiAgICAgICAgICAgICAgICAgICAgc2V0QWN0aXZlSXRlbShpdGVtLm5hbWUpO1xuXG4gICAgICAgICAgICAgICAgICAgIC8vIOacgOWQjumAmuefpeeItue7hOS7tlxuICAgICAgICAgICAgICAgICAgICBvbk1lbnVJdGVtQ2xpY2s/LihpdGVtLm5hbWUpO1xuXG4gICAgICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCflrozmiJDorr7nva4gLSDmtLvot4Ppobk6JywgaXRlbS5uYW1lLCAn5LiL5ouJ6I+c5Y2V5bey5YWz6ZetJyk7XG5cbiAgICAgICAgICAgICAgICAgICAgLy8g5bu26L+f6YeN572u5qCH5b+X5L2NXG4gICAgICAgICAgICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgIGlzTmF2aWdhdGluZ1JlZi5jdXJyZW50ID0gZmFsc2U7XG4gICAgICAgICAgICAgICAgICAgIH0sIDEwMCk7XG4gICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxpdGVtLmljb24gY2xhc3NOYW1lPVwibmF2LWljb25cIiAvPlxuICAgICAgICAgICAgICAgICAgPHNwYW4+e2l0ZW0ubmFtZX08L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9saT5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAge2l0ZW0uaGFzRGl2aWRlciAmJiA8ZGl2IGNsYXNzTmFtZT1cIm5hdi1kaXZpZGVyXCI+PC9kaXY+fVxuICAgICAgICAgICAgPC9SZWFjdC5GcmFnbWVudD5cbiAgICAgICAgICApKX1cbiAgICAgICAgPC91bD5cbiAgICAgIDwvbmF2PlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJzaWRlYmFyLWZvb3RlclwiIG9uQ2xpY2s9e2hhbmRsZUJhY2tUb0hvbWV9PlxuICAgICAgICA8QXJyb3dMZWZ0Q2lyY2xlIGNsYXNzTmFtZT1cIm5hdi1pY29uXCIgLz5cbiAgICAgICAgPHNwYW4+6L+U5Zue5Li76aG1PC9zcGFuPlxuICAgICAgPC9kaXY+XG4gICAgPC9hc2lkZT5cbiAgKTtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IExlZnRTaWRlYmFyOyAiXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZVJlZiIsInVzZUNhbGxiYWNrIiwiTGF5b3V0RGFzaGJvYXJkIiwiVXNlcnMiLCJMaXN0VG9kbyIsIkJyaWVmY2FzZSIsIkJvb2siLCJCb29rT3BlbiIsIlNldHRpbmdzIiwiQXJyb3dMZWZ0Q2lyY2xlIiwiU2hpZWxkIiwiQ2hldnJvbkRvd24iLCJJbWFnZSIsImdldFRlYWNoZXJTY2hvb2xzIiwiY2xhc3NBcGkiLCJmb3JtYXRTY2hvb2xBZGRyZXNzIiwiTGVmdFNpZGViYXIiLCJ1c2VySW5mbyIsIm9uTWVudUl0ZW1DbGljayIsIm9uU2Nob29sU2VsZWN0Iiwib25DbGFzc2VzVXBkYXRlIiwiYWN0aXZlSXRlbSIsInNldEFjdGl2ZUl0ZW0iLCJpc0NsYXNzRHJvcGRvd25PcGVuIiwic2V0SXNDbGFzc0Ryb3Bkb3duT3BlbiIsInNjaG9vbHMiLCJzZXRTY2hvb2xzIiwic2VsZWN0ZWRTY2hvb2wiLCJzZXRTZWxlY3RlZFNjaG9vbCIsInNjaG9vbHNMb2FkaW5nIiwic2V0U2Nob29sc0xvYWRpbmciLCJzY2hvb2xzRXJyb3IiLCJzZXRTY2hvb2xzRXJyb3IiLCJjb25zb2xlIiwibG9nIiwiZHJvcGRvd25SZWYiLCJpc05hdmlnYXRpbmdSZWYiLCJoYW5kbGVDbGlja091dHNpZGUiLCJldmVudCIsImN1cnJlbnQiLCJjb250YWlucyIsInRhcmdldCIsImRvY3VtZW50IiwiYWRkRXZlbnRMaXN0ZW5lciIsInJlbW92ZUV2ZW50TGlzdGVuZXIiLCJoYW5kbGVDbG9zZURyb3Bkb3duIiwiZmV0Y2hTY2hvb2xzIiwibmF2SXRlbXMiLCJpY29uIiwibmFtZSIsImhhc0RpdmlkZXIiLCJoYXNEcm9wZG93biIsImhhbmRsZUNsYXNzTWFuYWdlbWVudENsaWNrIiwibGVuZ3RoIiwiZmlyc3RTY2hvb2wiLCJmZXRjaENsYXNzZXMiLCJpZCIsImhhbmRsZVNjaG9vbFNlbGVjdCIsInNjaG9vbCIsImhhbmRsZUJhY2tUb0hvbWUiLCJjdXJyZW50T3JpZ2luIiwid2luZG93IiwibG9jYXRpb24iLCJvcmlnaW4iLCJob21lVXJsIiwiaHJlZiIsInJlc3BvbnNlIiwic2Nob29sTGlzdCIsImRhdGEiLCJjb2RlIiwic3RhdHVzIiwiQXJyYXkiLCJpc0FycmF5IiwiZXJyb3IiLCJzY2hvb2xJZCIsInRlYWNoZXJJZCIsImdldFRlYWNoZXJDbGFzc2VzIiwiY2xhc3NMaXN0IiwiZXJyb3JNc2ciLCJhc2lkZSIsImNsYXNzTmFtZSIsImRpdiIsInNwYW4iLCJzcmMiLCJhdmF0YXJVcmwiLCJhbHQiLCJuaWNrTmFtZSIsIndpZHRoIiwiaGVpZ2h0Iiwic3R5bGUiLCJiYWNrZ3JvdW5kQ29sb3IiLCJwIiwibmF2IiwidWwiLCJtYXAiLCJpdGVtIiwiRnJhZ21lbnQiLCJsaSIsInJlZiIsIm9uQ2xpY2siLCJlIiwicHJldmVudERlZmF1bHQiLCJzdG9wUHJvcGFnYXRpb24iLCJzaXplIiwidHJhbnNmb3JtIiwidHJhbnNpdGlvbiIsInNjaG9vbE5hbWUiLCJwcm92aW5jZSIsImRpc3RyaWN0IiwiY2l0eSIsInNldFRpbWVvdXQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/workbench/components/LeftSidebar.tsx\n"));

/***/ })

});