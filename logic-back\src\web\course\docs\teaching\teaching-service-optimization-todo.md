# Teaching Service 优化 TODO 清单

## 🎯 总体目标
将 `teaching.service.ts` 中的 `oneClickStart` 方法从 200+ 行重构为更清晰、可维护的代码结构。

## 📋 优化任务清单

### 🔥 高优先级 (P0) - 立即执行

#### 1. 方法拆分重构
- [x] **提取数据收集方法**
  
  ```typescript
  // 目标：将 Promise.all 数据获取逻辑提取为独立方法
  private async gatherTeachingContext(dto: OneClickStartDto, manager: EntityManager)
  ```
  - 估时：30分钟
  - 影响：提高代码可读性，便于单元测试
  
- [ ] **提取核心事务逻辑**
  ```typescript
  // 目标：将事务内的业务逻辑提取为独立方法
  private async executeOneClickStartWithTransaction(dto, teacherId, startTime)
  ```
  - 估时：45分钟
  - 影响：减少嵌套层级，提高可维护性

- [ ] **统一错误处理**
  ```typescript
  // 目标：将重复的错误处理逻辑合并
  private async handleOneClickStartError(error, dto, teacherId, startTime)
  private async handlePartialFailure(error, dto, teacherId, startTime)
  private async handleCompleteFailure(error, dto, teacherId, startTime)
  ```
  - 估时：60分钟
  - 影响：减少代码重复，统一错误处理策略

#### 2. 测试代码分离
- [ ] **移除硬编码测试逻辑**
  - 将 `testRollbackStep` 相关代码移除
  - 改用配置文件或环境变量控制测试行为
  - 估时：20分钟
  - 影响：清理生产代码，提高代码质量

### ⚡ 中优先级 (P1) - 本周完成

#### 3. 性能优化
- [ ] **优化数据库查询**
  - 检查是否有 N+1 查询问题
  - 优化 Promise.all 中的并发查询
  - 添加查询性能监控
  - 估时：90分钟

- [ ] **缓存机制**
  - 对课程设置、模板等相对静态数据添加缓存
  - 实现缓存失效策略
  - 估时：120分钟

#### 4. 日志和监控优化
- [ ] **结构化日志**
  ```typescript
  // 替换 console.log 为结构化日志
  this.logger.info('开始一键上课', { 
    courseId: dto.courseId, 
    classId: dto.classId, 
    teacherId 
  });
  ```
  - 估时：45分钟

- [ ] **性能指标收集**
  - 添加关键步骤的耗时统计
  - 实现性能告警机制
  - 估时：60分钟

### 🔧 低优先级 (P2) - 下个迭代

#### 5. 架构优化
- [ ] **引入状态机模式**
  - 将一键上课流程改为状态机驱动
  - 便于流程可视化和状态跟踪
  - 估时：4小时

- [ ] **事件驱动架构**
  - 将同步操作改为事件发布
  - 实现异步处理提高响应速度
  - 估时：6小时

#### 6. 业务逻辑优化
- [ ] **幂等性增强**
  - 完善重复操作检测机制
  - 实现更精确的幂等性控制
  - 估时：2小时

- [ ] **回滚机制优化**
  - 实现更细粒度的回滚策略
  - 支持部分回滚和补偿操作
  - 估时：3小时

## 📁 重构后的文件结构建议

```
teaching.service.ts
├── oneClickStart()                    # 入口方法 (20行)
├── executeOneClickStartWithTransaction() # 核心事务逻辑 (30行)
├── gatherTeachingContext()           # 数据收集 (15行)
├── validateTeachingFlow()            # 业务验证 (20行)
├── executeTeachingOperations()       # 执行教学操作 (25行)
├── handleOneClickStartError()        # 错误处理 (30行)
├── handlePartialFailure()            # 部分失败处理 (20行)
├── handleCompleteFailure()           # 完全失败处理 (15行)
└── buildSuccessResponse()            # 响应构建 (10行)
```

## 🧪 测试策略

### 单元测试
- [ ] **核心方法测试覆盖**
  - `gatherTeachingContext` 方法测试
  - `executeOneClickStartWithTransaction` 方法测试
  - 各种错误处理方法测试
  - 目标覆盖率：90%+

### 集成测试
- [ ] **并发场景测试**
  - 模拟高并发一键上课请求
  - 验证分布式锁和事务的正确性
  - 测试各种异常场景的处理

### 性能测试
- [ ] **压力测试**
  - 模拟 100+ 并发请求
  - 监控响应时间和资源使用
  - 验证优化效果

## 📊 成功指标

### 代码质量指标
- [ ] 方法平均行数 < 30行
- [ ] 圈复杂度 < 10
- [ ] 代码重复率 < 5%
- [ ] 测试覆盖率 > 90%

### 性能指标
- [ ] 平均响应时间 < 500ms
- [ ] 99分位响应时间 < 2s
- [ ] 并发处理能力 > 100 QPS
- [ ] 错误率 < 0.1%

## 🚀 实施计划

### 第一周 (P0任务)
- **周一-周二**: 方法拆分重构
- **周三**: 测试代码分离
- **周四-周五**: 单元测试编写和验证

### 第二周 (P1任务)
- **周一-周二**: 性能优化
- **周三-周四**: 日志和监控优化
- **周五**: 集成测试和性能测试

### 第三周 (P2任务)
- **周一-周三**: 架构优化
- **周四-周五**: 业务逻辑优化和最终验证

## 📝 注意事项

### 风险控制
- [ ] **渐进式重构**: 每次只重构一个方法，确保功能不受影响
- [ ] **充分测试**: 每个重构步骤都要有对应的测试验证
- [ ] **回滚准备**: 保留原始代码备份，出问题时可快速回滚

### 团队协作
- [ ] **代码审查**: 每个重构 PR 都需要 Code Review
- [ ] **文档更新**: 及时更新相关技术文档
- [ ] **知识分享**: 重构完成后进行团队分享

---

**创建时间**: 2025-07-31  
**负责人**: [待分配]  
**预计完成时间**: 3周  
**优先级**: P0 -> P1 -> P2
