/** @jsxImportSource react */
'use client'

import { Modal, Form, Input, Table, Switch, Button, message, List, Typography } from 'antd'
import { useState, useEffect, useRef } from 'react'
import {
  getExtensions,
  getBlocksByExtension
} from '@/lib/api/permission'
import {
  createTemplate,
  getRoleTemplateList,
  getTemplateInfo,
  updateTemplate,
} from '@/lib/api/role'
import { MinusOutlined, PlusOutlined, ExclamationCircleFilled, CheckOutlined, CloseOutlined, CloseCircleOutlined } from '@ant-design/icons'
import { GetNotification } from 'logic-common/dist/components/Notification'

const notification = GetNotification();

interface PermissionTemplateModalProps {
  roleId: number
  userId: number
  templateId?: number
  sourceTemplateId?: number | null
  visible: boolean
  onClose: () => void
  onSuccess?: () => void
  isOfficial?: boolean
  isCreatingFromTemplate?: boolean
  onDeleteTemplate?: (templateId: number) => void
  onUseTemplate?: (templateId: number) => Promise<void>
}

interface ExtensionPermission {
  id?: number;
  extensionId: string;
  isEnabled: boolean;
}

interface BlockPermission {
  id?: number;
  extensionId: string;
  blockId: string;
  isEnabled: boolean;
}

// 定义主题色常量，便于统一管理
const THEME = {
  primary: '#3864FF', // 更亮的蓝色
  success: '#1FC38E', // 翡翠绿
  danger: '#FF5252',  // 鲜红色
  warning: '#FFAD33', // 暖橙色
  background: '#F9FAFC', // 淡灰背景
  border: '#E8ECF4',  // 淡蓝灰边框
  text: {
    primary: '#1D2129',
    secondary: '#5F6A78',
    light: '#8F99A8'
  }
}

const PermissionTemplateModal: React.FC<PermissionTemplateModalProps> = ({
  roleId,
  userId,
  templateId,
  sourceTemplateId,
  visible,
  onClose,
  onSuccess,
  isOfficial,
  isCreatingFromTemplate,
  onDeleteTemplate,
  onUseTemplate
}) => {

  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [usingTemplate, setUsingTemplate] = useState(false)
  const [deleteConfirmVisible, setDeleteConfirmVisible] = useState(false)
  const [descriptionExpanded, setDescriptionExpanded] = useState(false)
  const [extensions, setExtensions] = useState<any[]>([])
  const [permissions, setPermissions] = useState<{
    extensions: ExtensionPermission[];
    blocks: BlockPermission[];
  }>({
    extensions: [],
    blocks: []
  })

  // 使用ref来保存初始状态，用于检测变化
  const initialStateRef = useRef<{
    formValues: any,
    permissions: {
      extensions: ExtensionPermission[];
      blocks: BlockPermission[];
    }
  }>({
    formValues: {},
    permissions: {
      extensions: [],
      blocks: []
    }
  });

  // 检测是否有更改
  const [hasChanges, setHasChanges] = useState(false);

  // 检查模板是否被修改
  const checkForChanges = () => {
    // 检查表单值是否变化
    const currentFormValues = form.getFieldsValue();
    const initialFormValues = initialStateRef.current.formValues;

    // 创建模式下，如果用户填写了任何内容就认为有变化
    if (!templateId || isCreatingFromTemplate) {
      // 检查是否填写了模板名称
      if (currentFormValues.templateName && currentFormValues.templateName.trim() !== '') {
        return true;
      }

      // 检查是否填写了模板描述
      if (currentFormValues.templateDescription && currentFormValues.templateDescription.trim() !== '') {
        return true;
      }

      // 检查是否修改了权限
      if (permissions.extensions.some(ext => ext.isEnabled)) {
        return true;
      }

      return false;
    }

    // 编辑模式下的检查逻辑
    const formChanged =
      currentFormValues.templateName !== initialFormValues.templateName ||
      currentFormValues.templateDescription !== initialFormValues.templateDescription;

    // 检查权限是否变化
    const initialPermissions = initialStateRef.current.permissions;

    // 检查扩展权限变化
    let extensionsChanged = permissions.extensions.length !== initialPermissions.extensions.length;

    if (!extensionsChanged) {
      for (const ext of permissions.extensions) {
        const initialExt = initialPermissions.extensions.find(e => e.extensionId === ext.extensionId);
        if (!initialExt || initialExt.isEnabled !== ext.isEnabled) {
          extensionsChanged = true;
          break;
        }
      }
    }

    // 检查积木块权限变化
    let blocksChanged = permissions.blocks.length !== initialPermissions.blocks.length;

    if (!blocksChanged) {
      for (const block of permissions.blocks) {
        const initialBlock = initialPermissions.blocks.find(
          b => b.extensionId === block.extensionId && b.blockId === block.blockId
        );
        if (!initialBlock || initialBlock.isEnabled !== block.isEnabled) {
          blocksChanged = true;
          break;
        }
      }
    }

    return formChanged || extensionsChanged || blocksChanged;
  };

  // 获取所有数据
  const fetchData = async () => {
    setLoading(true);
    try {
      // console.log('开始获取数据...');
      const [extensionsRes, templateRes] = await Promise.all([
        getExtensions(),
        isCreatingFromTemplate && templateId ? getTemplateInfo(templateId) :
          sourceTemplateId ? getTemplateInfo(sourceTemplateId) :
            templateId ? getTemplateInfo(templateId) :
              Promise.resolve(null)
      ]);
      // console.log('获取到扩展和模板数据:', extensionsRes?.data?.data?.length, templateRes?.data?.code);
      // 过滤掉 URL 相关的扩展
      const extensionsList = (extensionsRes.data?.data || []).filter((ext: any) =>
        !ext.extensionId.includes('logicleapUrl') &&
        !ext.extensionId.includes('url') &&
        !ext.extensionId.includes('URL')
      );
      // console.log('过滤后的扩展列表数量:', extensionsList.length);
      // console.log('扩展列表第一项结构:', extensionsList[0] ? Object.keys(extensionsList[0]) : '无扩展');
      // console.log('是否包含blocks属性:', extensionsList[0]?.blocks ? '是' : '否');

      let templateData = null;
      if (templateRes?.data?.code === 200) {
        templateData = templateRes.data.data;
        // 过滤掉模板中的 URL 相关权限
        if (templateData.permissions) {
          templateData.permissions.extensions = templateData.permissions.extensions.filter(
            (ext: any) => !ext.extensionId.includes('logicleapUrl') &&
              !ext.extensionId.includes('url') &&
              !ext.extensionId.includes('URL')
          );
          templateData.permissions.blocks = templateData.permissions.blocks.filter(
            (block: any) => !block.extensionId.includes('logicleapUrl') &&
              !block.extensionId.includes('url') &&
              !block.extensionId.includes('URL')
          );
        }

        // 设置表单数据
        const formValues = {
          templateName: isCreatingFromTemplate ? `基于 ${templateData.templateName} 的模板` :
            templateId ? templateData.templateName :
              sourceTemplateId ? `基于 ${templateData.templateName} 的模板` : '',
          templateDescription: isCreatingFromTemplate ? '' : templateData.templateDescription,
          isOfficial: isOfficial
        };

        form.setFieldsValue(formValues);

        // 保存初始表单值用于比较
        initialStateRef.current.formValues = { ...formValues };
      }
      // console.log('获取模板详情响应6:', templateData);

      // 初始化权限
      const templatePermissions = templateData?.permissions || { extensions: [], blocks: [] };
      // console.log('获取模板详情响应7:', templatePermissions);
      const initialPermissions = {
        extensions: templatePermissions.extensions?.map((ext: any) => ({
          extensionId: ext.extensionId,
          isEnabled: ext.isEnabled,
          id: ext.id
        })) || [],
        blocks: templatePermissions.blocks?.map((block: any) => ({
          extensionId: block.extensionId,
          blockId: block.blockId,
          isEnabled: block.isEnabled,
          id: block.id
        })) || []
      };
      // console.log('获取模板详情响应8:', initialPermissions);
      setPermissions(initialPermissions);

      // 保存初始权限用于比较
      initialStateRef.current.permissions = JSON.parse(JSON.stringify(initialPermissions));

      // 合并扩展数据和权限数据
      const mergedExtensions = extensionsList.map((ext: any) => {
        const extensionPermission = templatePermissions.extensions?.find(
          (p: any) => p.extensionId === ext.extensionId
        );

        // 获取与当前扩展相关的所有积木块权限
        const extensionBlocks = templatePermissions.blocks?.filter(
          (b: any) => b.extensionId === ext.extensionId
        ) || [];

        // console.log('扩展ID:', ext.extensionId, '相关积木块数量:', extensionBlocks.length);

        return {
          ...ext,
          isEnabled: extensionPermission ? extensionPermission.isEnabled : false,
          blocks: [], // 先设置为空数组，后面会异步加载
          blockPermissions: extensionBlocks // 保存积木块权限引用
        };
      });

      setExtensions(mergedExtensions);
      setHasChanges(false);

      // 异步加载每个扩展的积木块数据
      Promise.all(
        mergedExtensions.map(async (ext: any) => {
          try {
            const res = await getBlocksByExtension(ext.extensionId);
            const blockData = res.data?.data || [];

            // 将积木块数据与权限合并
            const blocksWithPermissions = blockData.map((block: any) => {
              const blockPermission = ext.blockPermissions.find(
                (p: any) => p.blockId === block.blockId
              );

              // console.log('积木块数据:', block.blockId, '权限状态:', blockPermission?.isEnabled);

              return {
                ...block,
                isEnabled: blockPermission ? blockPermission.isEnabled : false
              };
            });

            return {
              ...ext,
              blocks: blocksWithPermissions
            };
          } catch (error) {
            console.error('获取积木块数据失败:', ext.extensionId, error);
            return ext;
          }
        })
      ).then(updatedExtensions => {
        // console.log('更新后的扩展数据(含积木块):', updatedExtensions.length);
        setExtensions(updatedExtensions);
      });
    } catch (error) {
      console.error('获取数据失败:', error);
      notification.error('获取数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 确保表单始终有默认值
  useEffect(() => {
    // 获取当前值
    const currentValues = form.getFieldsValue();
    const hasName = currentValues.templateName && currentValues.templateName.trim() !== '';

    // 如果模板名称为空，设置一个默认值
    if (!hasName) {
      form.setFieldsValue({
        templateName: '新建权限模板',
        ...currentValues
      });
      console.log("已设置默认模板名称");
    }
  }, [visible]);

  // 现有的useEffect保持不变
  useEffect(() => {
    if (visible) {
      // 重置表单
      form.resetFields();
      // 确保描述展开以便验证
      setDescriptionExpanded(true);
      fetchData();
    }
  }, [visible, templateId, isOfficial]);

  // 在表单值变化时检测更改
  useEffect(() => {
    // 不仅检查编辑模式，也检查创建模式下的变更
    const currentHasChanges = checkForChanges();
    setHasChanges(currentHasChanges);
  }, [permissions, form.getFieldsValue()]);

  // 处理扩展权限变更
  const handleExtensionChange = (extensionId: string, isEnabled: boolean) => {
    // 更新UI状态
    const updatedExtensions = extensions.map(ext =>
      ext.extensionId === extensionId ? {
        ...ext,
        isEnabled,
        blocks: ext.blocks.map((block: any) => ({
          ...block,
          isEnabled // 同步更新所有积木块状态
        }))
      } : ext
    );
    setExtensions(updatedExtensions);

    // 更新权限数据
    setPermissions(prev => {
      // 1. 更新扩展权限
      let newExtensions = [...prev.extensions];
      const existingExtIndex = newExtensions.findIndex(e => e.extensionId === extensionId);

      if (existingExtIndex >= 0) {
        // 更新现有扩展权限
        newExtensions[existingExtIndex] = {
          ...newExtensions[existingExtIndex],
          isEnabled
        };
      } else {
        // 添加新扩展权限
        newExtensions.push({
          extensionId,
          isEnabled
        });
      }

      // 2. 更新该扩展下所有积木块的权限
      const extBlocks = updatedExtensions.find(e => e.extensionId === extensionId)?.blocks || [];
      let newBlocks = [...prev.blocks];

      // 首先移除此扩展的所有现有积木块权限
      newBlocks = newBlocks.filter(b => b.extensionId !== extensionId);

      // 然后添加更新后的积木块权限
      extBlocks.forEach((block: any) => {
        newBlocks.push({
          extensionId,
          blockId: block.blockId,
          isEnabled // 使用扩展的启用状态
        });
      });

      // console.log(`已更新扩展${extensionId}及其${extBlocks.length}个积木块的权限，状态:${isEnabled}`);

      return {
        extensions: newExtensions,
        blocks: newBlocks
      };
    });
  };

  // 处理积木块权限变更
  const handleBlockChange = (extensionId: string, blockId: string, isEnabled: boolean) => {
    // 更新UI状态
    const updatedExtensions = extensions.map(ext => {
      if (ext.extensionId === extensionId) {
        // 更新积木块状态
        const updatedBlocks = ext.blocks.map((block: any) =>
          block.blockId === blockId ? { ...block, isEnabled } : block
        );

        // 计算扩展的启用状态（只要有一个积木块启用，扩展就启用）
        const anyBlockEnabled = updatedBlocks.some((block: any) => block.isEnabled);

        return {
          ...ext,
          blocks: updatedBlocks,
          isEnabled: anyBlockEnabled
        };
      }
      return ext;
    });
    setExtensions(updatedExtensions);

    // 更新权限数据
    setPermissions(prev => {
      // 1. 更新积木块权限
      let newBlocks = [...prev.blocks];
      const existingBlockIndex = newBlocks.findIndex(
        b => b.extensionId === extensionId && b.blockId === blockId
      );

      if (existingBlockIndex >= 0) {
        // 更新现有权限
        newBlocks[existingBlockIndex] = {
          ...newBlocks[existingBlockIndex],
          isEnabled
        };
      } else {
        // 添加新权限
        newBlocks.push({
          extensionId,
          blockId,
          isEnabled
        });
      }

      // 2. 更新扩展权限状态
      const extensionBlocks = newBlocks.filter(b => b.extensionId === extensionId);
      const anyBlockEnabled = extensionBlocks.some(b => b.isEnabled);

      let newExtensions = [...prev.extensions];
      const existingExtIndex = newExtensions.findIndex(e => e.extensionId === extensionId);

      if (existingExtIndex >= 0) {
        // 更新现有扩展权限
        newExtensions[existingExtIndex] = {
          ...newExtensions[existingExtIndex],
          isEnabled: anyBlockEnabled
        };
      } else {
        // 添加新扩展权限
        newExtensions.push({
          extensionId,
          isEnabled: anyBlockEnabled
        });
      }

      return {
        extensions: newExtensions,
        blocks: newBlocks
      };
    });
  };

  // 添加全选处理函数
  const handleSelectAll = (checked: boolean) => {
    const updatedExtensions = extensions.map(ext => ({
      ...ext,
      isEnabled: checked,
      blocks: ext.blocks.map((block: any) => ({
        ...block,
        isEnabled: checked
      }))
    }));
    setExtensions(updatedExtensions);

    // 更新所有权限
    setPermissions({
      extensions: updatedExtensions.map(ext => ({
        extensionId: ext.extensionId,
        isEnabled: checked
      })),
      blocks: updatedExtensions.flatMap(ext =>
        ext.blocks.map((block: any) => ({
          extensionId: ext.extensionId,
          blockId: block.blockId,
          isEnabled: checked
        }))
      )
    });
  };

  // 添加扩展下的全选处理函数
  const handleSelectAllBlocks = (extensionId: string, checked: boolean) => {
    const updatedExtensions = extensions.map(ext =>
      ext.extensionId === extensionId ? {
        ...ext,
        isEnabled: checked, // 更新扩展状态
        blocks: ext.blocks.map((block: any) => ({
          ...block,
          isEnabled: checked
        }))
      } : ext
    );
    setExtensions(updatedExtensions);

    // 更新权限
    setPermissions(prev => {
      const newBlocks = prev.blocks.filter(b => b.extensionId !== extensionId);
      const extensionBlocks = updatedExtensions
        .find(e => e.extensionId === extensionId)
        ?.blocks.map((block: any) => ({
          extensionId,
          blockId: block.blockId,
          isEnabled: checked
        })) || [];

      // 更新扩展权限
      const existingExt = prev.extensions.find(e => e.extensionId === extensionId);
      const newExtensions = [
        ...prev.extensions.filter(e => e.extensionId !== extensionId),
        existingExt ?
          { ...existingExt, isEnabled: checked } :
          { extensionId, isEnabled: checked }
      ];

      return {
        extensions: newExtensions,
        blocks: [...newBlocks, ...extensionBlocks]
      };
    });
  };

  // 提交表单
  const handleSubmit = async () => {
    try {
      // 检查表单是否被正确初始化
      if (!form) {
        console.error("表单对象未初始化");
        notification.error('表单对象未初始化，请刷新页面重试');
        return;
      }

      // 记录表单当前值用于调试
      const currentFormValues = form.getFieldsValue();
      console.log("当前表单值:", currentFormValues);

      // 检查必填字段
      if (!currentFormValues.templateName) {
        notification.error('请填写模板名称');
        return;
      }

      let values;
      try {
        values = await form.validateFields();
        console.log("表单验证通过:", values);
      } catch (validationError: any) {
        console.error("表单验证错误:", validationError);
        // 显示详细的验证错误信息
        if (validationError.errorFields) {
          const errorMsgs = validationError.errorFields.map((field: any) => field.errors.join(', '));
          notification.error(`表单验证失败: ${errorMsgs.join('; ')}`);
        } else {
          notification.error('表单验证失败，请检查必填项');
        }
        return;
      }

      setLoading(true);

      const params = {
        ...values,
        roleId,
        userId,
        permissions,
        isOfficial: roleId === 4 ? values.isOfficial : false // 只有 roleId = 4 时才能设置官方模板
      };

      if (templateId && !isCreatingFromTemplate) {
        // 检查是否有权限编辑
        if (values.isOfficial && roleId !== 4) {
          notification.error('您没有权限编辑官方模板');
          return;
        }

        await updateTemplate({
          id: templateId,
          templateName: values.templateName,
          templateDescription: values.templateDescription,
          permissions: {
            extensions: permissions.extensions.map(ext => ({
              extensionId: ext.extensionId,
              isEnabled: ext.isEnabled
            })),
            blocks: permissions.blocks.map(block => ({
              extensionId: block.extensionId,
              blockId: block.blockId,
              isEnabled: block.isEnabled
            }))
          },
          isOfficial: params.isOfficial
        });
        notification.success('更新模板成功');

        // 更新初始状态，使其与当前状态一致
        initialStateRef.current = {
          formValues: { ...values },
          permissions: JSON.parse(JSON.stringify(permissions))
        };

        setHasChanges(false);
      } else {
        // 创建新模板
        await createTemplate(params);
        notification.success('创建模板成功');
      }

      onSuccess?.();

      // 如果是在点击"使用此模板"按钮后的保存，则在保存后应用模板
      if (usingTemplate && templateId && onUseTemplate) {
        await onUseTemplate(templateId);
      } else {
        onClose();
      }

      setUsingTemplate(false);
    } catch (error) {
      console.error('模板操作失败:', error);
      notification.error('操作失败');
      setUsingTemplate(false);
    } finally {
      setLoading(false);
    }
  };

  // 使用模板
  const handleUseTemplate = async () => {
    try {
      setLoading(true);

      // 无论是否有更改，都先尝试保存模板
      if (hasChanges) {
        // 检查表单是否被正确初始化
        if (!form) {
          console.error("表单对象未初始化");
          notification.error('表单对象未初始化，请刷新页面重试');
          setLoading(false);
          return;
        }

        // 记录表单当前值用于调试
        const currentFormValues = form.getFieldsValue();
        console.log("使用模板时的表单值:", currentFormValues);

        // 检查必填字段
        if (!currentFormValues.templateName) {
          notification.error('请填写模板名称');
          setLoading(false);
          return;
        }

        let values;
        try {
          values = await form.validateFields();
          console.log("使用模板-表单验证通过:", values);
        } catch (validationError: any) {
          console.error("使用模板-表单验证错误:", validationError);
          // 显示详细的验证错误信息
          if (validationError.errorFields) {
            const errorMsgs = validationError.errorFields.map((field: any) => field.errors.join(', '));
            notification.error(`表单验证失败: ${errorMsgs.join('; ')}`);
          } else {
            notification.error('表单验证失败，请检查必填项');
          }
          setLoading(false);
          return;
        }

        const params = {
          ...values,
          roleId,
          userId,
          permissions,
          isOfficial: roleId === 4 ? values.isOfficial : false
        };

        if (templateId && !isCreatingFromTemplate) {
          // 检查是否有权限编辑
          if (values.isOfficial && roleId !== 4) {
            notification.error('您没有权限编辑官方模板');
            setLoading(false);
            return;
          }

          await updateTemplate({
            id: templateId,
            templateName: values.templateName,
            templateDescription: values.templateDescription,
            permissions: {
              extensions: permissions.extensions.map(ext => ({
                extensionId: ext.extensionId,
                isEnabled: ext.isEnabled
              })),
              blocks: permissions.blocks.map(block => ({
                extensionId: block.extensionId,
                blockId: block.blockId,
                isEnabled: block.isEnabled
              }))
            },
            isOfficial: params.isOfficial
          });

          notification.success('更新模板成功');

          // 更新初始状态，使其与当前状态一致
          initialStateRef.current = {
            formValues: { ...values },
            permissions: JSON.parse(JSON.stringify(permissions))
          };

          setHasChanges(false);
        }
      }

      // 保存完成后，使用模板
      if (templateId && onUseTemplate) {
        await onUseTemplate(templateId);

        // 延迟关闭弹窗，给后端足够时间更新状态
        setTimeout(() => {
          onClose();
        }, 500);
      }

      onSuccess?.();
    } catch (error) {
      console.error('使用模板失败:', error);
      notification.error('操作失败');
    } finally {
      setLoading(false);
    }
  };

  // 处理删除模板的确认
  const handleConfirmDelete = () => {
    if (templateId && onDeleteTemplate) {
      onDeleteTemplate(templateId);
      setDeleteConfirmVisible(false);
    }
  };

  const columns = [
    {
      title: (
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          padding: '8px 0'
        }}>
          <Typography.Text strong style={{ fontSize: '15px' }}>扩展名称</Typography.Text>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            background: `${THEME.primary}10`,
            padding: '4px 12px',
            borderRadius: '6px',
          }}>
            <Typography.Text style={{
              fontSize: '14px',
              color: THEME.primary,
              margin: 0
            }}>全选</Typography.Text>
            <Switch
              checked={extensions.every(ext => ext.isEnabled)}
              onChange={handleSelectAll}
              size="small"
              style={{ backgroundColor: extensions.every(ext => ext.isEnabled) ? THEME.primary : undefined }}
            />
          </div>
        </div>
      ),
      dataIndex: 'extensionName',
      key: 'extensionName',
      width: '40%',
      render: (text: string, record: any) => (
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '8px'
        }}>
          <Switch
            checked={record.isEnabled}
            onChange={(checked) => handleExtensionChange(record.extensionId, checked)}
            style={{ backgroundColor: record.isEnabled ? THEME.primary : undefined }}
          />
          <Typography.Text
            style={{
              color: record.isEnabled ? THEME.text.primary : THEME.text.light,
              fontWeight: 500,
              transition: 'color 0.3s'
            }}
          >
            {text}
          </Typography.Text>
        </div>
      )
    },
    {
      title: <Typography.Text strong style={{ fontSize: '15px' }}>描述</Typography.Text>,
      dataIndex: 'description',
      key: 'description',
      width: '60%',
      ellipsis: true,
      render: (text: string, record: any) => (
        <Typography.Text
          style={{
            color: record.isEnabled ? THEME.text.secondary : THEME.text.light,
            transition: 'color 0.3s'
          }}
        >
          {text}
        </Typography.Text>
      )
    }
  ]

  return (
    <>
      <style jsx global>{`
        .close-icon-hover:hover {
          color: ${THEME.primary} !important;
        }
        .modal-close-btn-hover:hover {
          background-color: #f5f5f5;
        }
        .modal-close-btn-hover:hover svg {
          color: ${THEME.primary} !important;
        }
        .use-template-btn:hover {
          background-color: ${THEME.primary} !important;
          opacity: 0.85;
        }
        .permission-template-modal .ant-modal-body {
          padding-right: 8px !important;
        }
        .permission-template-modal .ant-modal-body::-webkit-scrollbar {
          width: 6px;
        }
        .permission-template-modal .ant-modal-body::-webkit-scrollbar-thumb {
          background-color: #E5E6EB;
          border-radius: 3px;
        }
        .permission-template-modal .ant-form-item {
          margin-bottom: 16px;
        }
        .permission-template-modal .ant-table {
          margin-bottom: 0;
        }
        @media screen and (max-height: 800px) {
          .permission-template-modal .ant-modal-body {
            max-height: calc(100vh - 220px);
          }
          .permission-template-modal .ant-form-item {
            margin-bottom: 12px;
          }
        }
      `}</style>
      <Modal
        title={
          <Typography.Title level={4} style={{
            margin: 0,
            padding: '0 0 16px 0',
            borderBottom: `1px solid ${THEME.border}`,
            color: THEME.text.primary,
            fontSize: '20px'
          }}>
            {templateId ? "编辑权限模板" : "创建权限模板"}
          </Typography.Title>
        }
        open={visible}
        onCancel={() => {
          // 如果正在加载（执行保存、使用、删除操作），不允许关闭
          if (loading) {
            return;
          }

          // 如果有未保存的修改，显示确认弹窗
          if (hasChanges) {
            Modal.confirm({
              title: null,
              icon: null,
              content: (
                <div style={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  padding: '32px 24px',
                  position: 'relative' // 为关闭图标添加相对定位
                }}>
                  <div
                    style={{
                      position: 'absolute',
                      top: '10px',
                      right: '10px',
                      width: '32px',
                      height: '32px',
                      borderRadius: '50%',
                      border: '1px solid #e8e8e8',
                      display: 'flex',
                      justifyContent: 'center',
                      alignItems: 'center',
                      cursor: 'pointer',
                      transition: 'all 0.3s'
                    }}
                    className="modal-close-btn-hover"
                    onClick={(e) => {
                      e.stopPropagation();
                      Modal.destroyAll(); // 只关闭确认弹窗
                    }}
                  >
                    <CloseOutlined
                      style={{
                        fontSize: '14px',
                        color: '#999'
                      }}
                    />
                  </div>
                  <div style={{
                    backgroundColor: `${THEME.primary}10`,
                    borderRadius: '50%',
                    padding: '20px',
                    marginBottom: '24px'
                  }}>
                    <ExclamationCircleFilled style={{
                      fontSize: '64px',
                      color: THEME.primary
                    }} />
                  </div>
                  <Typography.Text style={{
                    fontSize: '16px',
                    fontWeight: 500,
                    marginBottom: '8px',
                    color: THEME.text.primary
                  }}>
                    模板编辑操作未保存，是否保存模板
                  </Typography.Text>
                </div>
              ),
              centered: true,
              okText: '确定',
              cancelText: '取消',
              okButtonProps: {
                style: {
                  borderRadius: '20px',
                  backgroundColor: THEME.primary,
                  borderColor: THEME.primary,
                  height: '40px',
                  width: '108px',
                  fontSize: '14px',
                  boxShadow: '0 2px 8px rgba(56, 100, 255, 0.3)'
                }
              },
              cancelButtonProps: {
                style: {
                  borderRadius: '20px',
                  backgroundColor: '#f5a4a4',
                  borderColor: '#f5a4a4',
                  color: '#ffffff',
                  height: '40px',
                  width: '108px',
                  fontSize: '14px'
                }
              },
              width: 400,
              styles: {
                content: {
                  borderRadius: '16px',
                  overflow: 'hidden',
                  boxShadow: '0 4px 24px rgba(0, 0, 0, 0.1)'
                },
                body: {
                  padding: 0
                },
                footer: {
                  padding: '0 24px 24px',
                  borderTop: 'none',
                  display: 'flex',
                  justifyContent: 'center',
                  gap: '16px'
                },
                mask: {
                  backdropFilter: 'blur(2px)',
                  backgroundColor: 'rgba(0, 0, 0, 0.4)'
                }
              },
              // 自定义按钮渲染
              footer: (_, { OkBtn, CancelBtn }) => (
                <div style={{
                  display: 'flex',
                  justifyContent: 'center',
                  gap: '16px',
                  padding: '0 24px 24px'
                }}>
                  <CancelBtn />
                  <OkBtn />
                </div>
              ),
              onOk: async () => {
                await handleSubmit();
              },
              onCancel: () => {
                onClose(); // 取消按钮关闭整个模板弹窗
              }
            });
          } else {
            // 如果没有修改，直接关闭
            onClose();
          }
        }}
        width={800}
        maskClosable={!loading} // 在loading状态下禁止点击遮罩关闭
        closeIcon={loading ? null : undefined} // 在loading状态下不显示关闭图标
        footer={
          <div style={{ display: 'flex', justifyContent: 'space-between', width: '100%' }}>
            <div>
              {templateId && !isCreatingFromTemplate && onDeleteTemplate && !isOfficial && (
                <Button
                  key="delete"
                  onClick={() => setDeleteConfirmVisible(true)}
                  disabled={loading}
                  style={{
                    border: `1px solid ${THEME.danger}`,
                    color: THEME.danger,
                    padding: '6px 20px',
                    height: '40px',
                    borderRadius: '20px',
                    fontWeight: 500,
                    transition: 'all 0.3s'
                  }}
                >
                  删除模板
                </Button>
              )}
            </div>
            <div style={{ display: 'flex', gap: '16px' }}>
              {templateId && !isCreatingFromTemplate ? (
                <>
                  <Button
                    key="save"
                    type="primary"
                    loading={loading}
                    disabled={loading}
                    onClick={handleSubmit}
                    style={{
                      backgroundColor: THEME.primary,
                      borderColor: THEME.primary,
                      padding: '6px 20px',
                      height: '40px',
                      borderRadius: '20px',
                      fontWeight: 500,
                      boxShadow: '0 2px 8px rgba(56, 100, 255, 0.3)'
                    }}
                  >
                    保存设置
                  </Button>
                  {onUseTemplate && (
                    <Button
                      key="use"
                      type="primary"
                      loading={loading}
                      disabled={loading}
                      onClick={handleUseTemplate}
                      style={{
                        backgroundColor: THEME.primary,
                        borderColor: THEME.primary,
                        padding: '6px 20px',
                        height: '40px',
                        borderRadius: '20px',
                        fontWeight: 500,
                        boxShadow: '0 2px 8px rgba(56, 100, 255, 0.3)',
                        transition: 'all 0.3s'
                      }}
                      className="use-template-btn"
                    >
                      使用此模板
                    </Button>
                  )}
                </>
              ) : (
                <Button
                  key="submit"
                  type="primary"
                  loading={loading}
                  disabled={loading}
                  onClick={handleSubmit}
                  style={{
                    backgroundColor: THEME.primary,
                    borderColor: THEME.primary,
                    padding: '6px 24px',
                    height: '40px',
                    borderRadius: '20px',
                    fontWeight: 500,
                    boxShadow: '0 2px 8px rgba(56, 100, 255, 0.3)'
                  }}
                >
                  确定
                </Button>
              )}
            </div>
          </div>
        }
        styles={{
          body: {
            maxHeight: 'calc(100vh - 180px)',
            overflow: 'auto',
            padding: '0 24px',
            paddingTop: '8px',
            paddingBottom: '16px',
            backgroundColor: THEME.background
          },
          content: {
            padding: '0',
            borderRadius: '16px',
            maxHeight: '95vh',
            margin: '10px 0',
            boxShadow: '0 10px 30px rgba(0, 0, 0, 0.1)',
            overflow: 'hidden',
            position: 'relative',
            display: 'flex',
            flexDirection: 'column'
          },
          header: {
            padding: '16px 24px 8px',
            borderBottom: 'none',
            marginBottom: '0',
            backgroundColor: 'white',
            flex: 'none'
          },
          footer: {
            padding: '12px 24px',
            borderTop: `1px solid ${THEME.border}`,
            marginTop: '0',
            backgroundColor: 'white',
            flex: 'none'
          },
          mask: {
            backdropFilter: 'blur(2px)',
            backgroundColor: 'rgba(0, 0, 0, 0.4)'
          }
        }}
        className="permission-template-modal"
        centered
      >
        <Form
          form={form}
          layout="vertical"
          style={{
            marginTop: '8px',
            marginBottom: '24px',
            backgroundColor: 'white',
            padding: '16px 20px',
            borderRadius: '12px',
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.05)'
          }}
        >
          <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
            {/* 模板名称部分 */}
            <div style={{
              display: 'flex',
              alignItems: 'center'
            }}>
              <div style={{
                width: '80px',
                flexShrink: 0
              }}>
                <Typography.Text strong style={{
                  fontSize: '15px',
                  color: THEME.text.primary
                }}>
                  模板名称
                </Typography.Text>
              </div>
              <Form.Item
                name="templateName"
                rules={[{ required: true, message: '请输入模板名称' }]}
                style={{
                  margin: 0,
                  flex: 1
                }}
              >
                <Input
                  placeholder="请输入模板名称"
                  style={{
                    height: '44px',
                    borderRadius: '8px',
                    border: `1px solid ${THEME.border}`,
                    transition: 'all 0.3s',
                    fontSize: '14px',
                    padding: '0 16px'
                  }}
                />
              </Form.Item>
            </div>

            {/* 模板描述部分 */}
            <div style={{
              display: 'flex',
              alignItems: 'flex-start'
            }}>
              <div style={{
                width: '80px',
                flexShrink: 0,
                paddingTop: '12px'
              }}>
                <Typography.Text strong style={{
                  fontSize: '15px',
                  color: THEME.text.primary
                }}>
                  模板描述
                </Typography.Text>
              </div>
              <div style={{
                flex: 1,
                backgroundColor: '#F9FAFC',
                borderRadius: '8px',
                padding: '12px 16px'
              }}>
                <div
                  onClick={() => setDescriptionExpanded(!descriptionExpanded)}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'flex-end',
                    cursor: 'pointer',
                    userSelect: 'none'
                  }}
                >
                  {descriptionExpanded ? (
                    <MinusOutlined style={{ color: THEME.text.secondary }} />
                  ) : (
                    <PlusOutlined style={{ color: THEME.text.secondary }} />
                  )}
                </div>

                <Form.Item
                  name="templateDescription"
                  style={{
                    marginBottom: 0,
                    height: 'auto',
                    overflow: 'visible',
                    transition: 'all 0.3s ease',
                    opacity: descriptionExpanded ? 1 : 0,
                    marginTop: descriptionExpanded ? '12px' : '0'
                  }}
                >
                  <Input.TextArea
                    placeholder="请输入模板描述"
                    style={{
                      borderRadius: '8px',
                      border: `1px solid ${THEME.border}`,
                      minHeight: '100px',
                      padding: '12px 16px',
                      fontSize: '14px',
                      transition: 'all 0.3s',
                      backgroundColor: 'white',
                      display: descriptionExpanded ? 'block' : 'none'
                    }}
                  />
                </Form.Item>
              </div>
            </div>

            {/* 官方模板选项 */}
            {roleId === 4 && (
              <div style={{
                display: 'flex',
                alignItems: 'center'
              }}>
                <div style={{
                  width: '80px',
                  flexShrink: 0
                }}>
                  <Typography.Text strong style={{
                    fontSize: '15px',
                    color: THEME.text.primary
                  }}>
                    官方模板
                  </Typography.Text>
                </div>
                <Form.Item
                  name="isOfficial"
                  valuePropName="checked"
                  style={{
                    margin: 0
                  }}
                >
                  <Switch
                    checkedChildren={<CheckOutlined />}
                    unCheckedChildren={<CloseOutlined />}
                    style={{ backgroundColor: form.getFieldValue('isOfficial') ? THEME.primary : undefined }}
                  />
                </Form.Item>
              </div>
            )}
          </div>
        </Form>

        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.05)',
          overflow: 'hidden',
          marginBottom: '16px'
        }}>
          <Typography.Title level={5} style={{
            margin: 0,
            padding: '16px 20px',
            borderBottom: `1px solid ${THEME.border}`,
            fontSize: '16px'
          }}>
            权限设置
          </Typography.Title>

          <Table
            columns={columns}
            dataSource={extensions}
            rowKey="extensionId"
            loading={loading}
            pagination={false}
            expandable={{
              expandedRowRender: (record) => (
                <div style={{ padding: '0 48px 24px' }}>
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    marginBottom: '16px',
                    padding: '8px 0'
                  }}>
                    <Typography.Text style={{
                      fontSize: '15px',
                      color: THEME.primary,
                      fontWeight: 500
                    }}>
                      积木块列表
                    </Typography.Text>
                  </div>
                  <div style={{
                    display: 'grid',
                    gridTemplateColumns: 'repeat(2, 1fr)',
                    gap: '12px'
                  }}>
                    {(record.blocks || []).map((block: any) => (
                      <div
                        key={block.blockId}
                        style={{
                          padding: '16px',
                          borderRadius: '8px',
                          background: 'white',
                          border: `1px solid ${THEME.border}`,
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignItems: 'center',
                          boxShadow: '0 2px 6px rgba(0, 0, 0, 0.03)',
                          transition: 'all 0.3s',
                          transform: block.isEnabled ? 'scale(1)' : 'scale(0.98)',
                          opacity: block.isEnabled ? 1 : 0.7
                        }}
                      >
                        <div style={{ flex: 1 }}>
                          <Typography.Text style={{
                            fontSize: '14px',
                            fontWeight: 500,
                            color: block.isEnabled ? THEME.text.primary : THEME.text.light,
                            display: 'block',
                            marginBottom: '4px'
                          }}>
                            {block.blockName}
                          </Typography.Text>
                          <Typography.Text style={{
                            fontSize: '13px',
                            color: block.isEnabled ? THEME.text.secondary : THEME.text.light,
                            display: 'block'
                          }}>
                            {block.name}
                          </Typography.Text>
                        </div>
                        <Switch
                          checked={block.isEnabled}
                          onChange={(checked) =>
                            handleBlockChange(record.extensionId, block.blockId, checked)
                          }
                          style={{
                            backgroundColor: block.isEnabled ? THEME.primary : undefined,
                            marginLeft: '16px'
                          }}
                        />
                      </div>
                    ))}
                  </div>
                </div>
              ),
              expandIcon: ({ expanded, onExpand, record }) => (
                <div
                  onClick={e => onExpand(record, e)}
                  style={{
                    cursor: 'pointer',
                    padding: '6px',
                    marginRight: '8px',
                    color: THEME.text.secondary,
                    borderRadius: '50%',
                    transition: 'all 0.3s',
                    backgroundColor: expanded ? `${THEME.primary}10` : 'transparent'
                  }}
                >
                  {expanded ? (
                    <MinusOutlined style={{ fontSize: '12px', color: expanded ? THEME.primary : undefined }} />
                  ) : (
                    <PlusOutlined style={{ fontSize: '12px' }} />
                  )}
                </div>
              )
            }}
            style={{
              borderRadius: '0',
              overflow: 'hidden',
              border: 'none'
            }}
            size="small"
          />
        </div>
      </Modal>

      {/* 删除模板确认弹窗 */}
      <Modal
        title={null}
        open={deleteConfirmVisible}
        onCancel={() => !loading && setDeleteConfirmVisible(false)}
        footer={null}
        width={400}
        centered
        maskClosable={!loading} // 禁止点击遮罩关闭
        closeIcon={loading ? null : undefined} // 在loading状态下不显示关闭图标
        styles={{
          content: {
            borderRadius: '16px',
            overflow: 'hidden',
            boxShadow: '0 10px 30px rgba(0, 0, 0, 0.15)'
          },
          mask: {
            backdropFilter: 'blur(2px)',
            backgroundColor: 'rgba(0, 0, 0, 0.4)'
          }
        }}
      >
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          padding: '32px 24px'
        }}>
          <div style={{
            backgroundColor: `${THEME.primary}10`,
            borderRadius: '50%',
            padding: '24px',
            marginBottom: '24px'
          }}>
            <ExclamationCircleFilled style={{
              fontSize: '64px',
              color: THEME.primary
            }} />
          </div>

          <Typography.Title level={4} style={{
            textAlign: 'center',
            marginTop: 0,
            marginBottom: '32px',
            fontSize: '18px',
            fontWeight: 500,
            color: THEME.text.primary
          }}>
            是否删除模板，删除之后无法找回
          </Typography.Title>

          <div style={{
            display: 'flex',
            justifyContent: 'center',
            gap: '16px',
            width: '100%'
          }}>
            <Button
              onClick={() => setDeleteConfirmVisible(false)}
              style={{
                borderRadius: '20px',
                backgroundColor: '#f5a4a4',
                borderColor: '#f5a4a4',
                color: '#ffffff',
                height: '40px',
                width: '120px',
                fontSize: '14px',
                fontWeight: 500
              }}
            >
              取消
            </Button>
            <Button
              type="primary"
              onClick={handleConfirmDelete}
              style={{
                borderRadius: '20px',
                backgroundColor: THEME.primary,
                borderColor: THEME.primary,
                height: '40px',
                width: '120px',
                fontSize: '14px',
                fontWeight: 500,
                boxShadow: '0 2px 8px rgba(56, 100, 255, 0.3)'
              }}
            >
              确定
            </Button>
          </div>
        </div>
      </Modal>
    </>
  );
}

export default PermissionTemplateModal
