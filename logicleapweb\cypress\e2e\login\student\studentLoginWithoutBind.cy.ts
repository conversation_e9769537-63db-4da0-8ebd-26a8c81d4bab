describe('The Record Page', () => {
  it('successfully loads', () => {
    cy.viewport(1920, 1080)
    cy.visit('/home')
    /* ==== Generated with Cypress Studio ==== */


    /* ==== End Cypress Studio ==== */
    /* ==== Generated with Cypress Studio ==== */
    cy.get('.bg-white\\/10').click();
    cy.get('.border-b > :nth-child(3)').click();
    cy.get('#rc_select_7').clear();
    cy.get('#rc_select_7').type('洛基');
    cy.get('.ant-select-item-option-active > .ant-select-item-option-content').click();
    cy.get('#studentNumber').clear();
    cy.get('#studentNumber').type('zwwbind1');
    cy.get('#studentPassword').clear();
    cy.get('#studentPassword').type('123456');
    cy.get('#terms').check();
    cy.get('.pt-2 > .w-full').click();
    cy.get('.space-x-4 > .ant-btn-default > span').click();
    cy.get('.border-gray-100\\/80 > button.flex').click();
    cy.get('.p-1').click();
    cy.get('[href="/my-works"]').click();
    cy.get('#rc-tabs-1-tab-images').click();
    cy.get('#rc-tabs-1-tab-likes').click();
    cy.get('.gap-4 > .ant-btn').click();
    /* ==== End Cypress Studio ==== */
  })
})

