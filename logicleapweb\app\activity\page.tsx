'use client'; // 添加 use client 指令，因为使用了 hooks

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useSelector } from 'react-redux';
import { RootState } from '@/lib/store';
import AddActivityModal from './components/AddActivityModal';
import UpdateActivityModal from './components/UpdateActivityModal';
import ActivityHeader from './components/ActivityHeader';
import ActivityList from './components/ActivityList';
import { GetNotification } from 'logic-common/dist/components/Notification';

export default function ActivityPage() {
  const [addModalVisible, setAddModalVisible] = useState(false);
  const [updateModalVisible, setUpdateModalVisible] = useState(false);
  const [selectedActivityId, setSelectedActivityId] = useState<number | undefined>(undefined);
  const [refreshKey, setRefreshKey] = useState(0); // 用于强制刷新列表
  const [isMounted, setIsMounted] = useState(false);
  const router = useRouter();
  const isLoggedIn = useSelector((state: RootState) => state.user.userState.isLoggedIn);
  const roleId = useSelector((state: RootState) => state.user.userState.roleId);

  // 避免水合错误
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // 检查用户权限
  useEffect(() => {
    if (isMounted && (!isLoggedIn || (roleId !== 4 && roleId !== 5))) {
      const notification = GetNotification();
      notification.error('您没有权限访问此页面');
      router.replace('/home');
    }
  }, [isMounted, isLoggedIn, roleId, router]);

  const handlePublishClick = () => {
    setAddModalVisible(true);
  };

  const handleAddSuccess = () => {
    const notification = GetNotification();
    notification.success('活动创建成功');
    setRefreshKey(prev => prev + 1); // 触发列表刷新
  };

  const handleEditClick = (activityId: number) => {
    setSelectedActivityId(activityId);
    setUpdateModalVisible(true);
  };

  const handleUpdateSuccess = () => {
    const notification = GetNotification();
    notification.success('活动更新成功');
    setRefreshKey(prev => prev + 1); // 触发列表刷新
  };

  // 避免水合错误，在客户端挂载前显示加载状态
  if (!isMounted) {
    return <div></div>;
  }

  // 如果用户没有权限，显示空的div
  if (!isLoggedIn || (roleId !== 4 && roleId !== 5)) {
    return <div></div>;
  }

  return (
    <div className="p-6">
      <ActivityHeader 
        title="活动管理" 
        onPublish={handlePublishClick} 
      />

      <ActivityList 
        key={refreshKey} // 使用key触发组件重新渲染
        showOnlyActive={true} 
        onEdit={handleEditClick}
      />

      <AddActivityModal
        visible={addModalVisible}
        onCancel={() => setAddModalVisible(false)}
        onSuccess={handleAddSuccess}
      />

      <UpdateActivityModal
        visible={updateModalVisible}
        onCancel={() => {
          setUpdateModalVisible(false);
          setSelectedActivityId(undefined);
        }}
        onSuccess={handleUpdateSuccess}
        activityId={selectedActivityId}
      />
    </div>
  );
}
