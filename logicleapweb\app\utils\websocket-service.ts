import { TabUtils } from './tab-utils';
import { WS_URL } from '@/config/config';

// WebSocket连接状态枚举
export enum WebSocketState {
  CONNECTING = 0,
  CONNECTED = 1,
  AUTHENTICATED = 2,
  CLOSED = 3,
  ERROR = 4
}

// 事件类型定义
export type WebSocketEvent =
  | { type: 'authSuccess', data: any }
  | { type: 'authError', data: { message: string } }
  | { type: 'pointsUpdate', data: { points: number | string, change: number | string, sourceTabId?: string } }
  | { type: 'assignPoints', data: { points: number | string, change: number | string, sourceTabId?: string } }
  | { type: 'packageAssigned', data: { packageInfo: { packageId: number; packageName: string; points: number; validityDays: number; showPoints: boolean; remark?: string; } } }
  | { type: 'paymentSuccess', data: { type: string; title: string; message: string; data: any; timestamp: string } }
  | { type: 'message', data: any }
  | { type: 'stateChange', state: WebSocketState }
  | { type: 'error', error: any };

// 事件侦听器类型
type EventListener = (event: WebSocketEvent) => void;

/**
 * WebSocket服务 - 单例模式
 * 管理WebSocket连接、重连、心跳和消息处理
 */
class WebSocketService {
  private static instance: WebSocketService;
  private ws: WebSocket | null = null;
  private state: WebSocketState = WebSocketState.CLOSED;
  private listeners: EventListener[] = [];
  private reconnectAttempts = 0;
  private reconnectTimer: NodeJS.Timeout | null = null;
  private pingInterval: NodeJS.Timeout | null = null;
  private lastPongTime: number | null = null;
  private connecting = false;
  private authenticated = false;
  private lastConnectTime = 0;
  private serverUrl: string;
  private pageId!: string;
  private userId: string | null = null;

  private constructor() {
    this.serverUrl = WS_URL;
  }

  /**
   * 获取WebSocketService实例
   */
  public static getInstance(): WebSocketService {
    if (!WebSocketService.instance) {
      WebSocketService.instance = new WebSocketService();
    }
    return WebSocketService.instance;
  }

  /**
   * 初始化WebSocket连接
   * @param pagePrefix 页面前缀，用于生成唯一的标签页ID
   */
  public init(pagePrefix: string): void {
    this.pageId = TabUtils.getTabId(pagePrefix);
    this.connect();

    // 在页面卸载前清理资源
    window.addEventListener('beforeunload', () => {
      this.disconnect();
    });
  }

  /**
   * 添加事件监听器
   * @param listener 事件监听器函数
   * @returns 用于移除监听器的函数
   */
  public addEventListener(listener: EventListener): () => void {
    this.listeners.push(listener);
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }

  /**
   * 触发事件
   * @param event 事件对象
   */
  private dispatchEvent(event: WebSocketEvent): void {
    this.listeners.forEach(listener => {
      try {
        listener(event);
      } catch (error) {
        console.error('处理WebSocket事件出错:', error);
      }
    });
  }

  /**
   * 更新连接状态
   * @param state 新状态
   */
  private setState(state: WebSocketState): void {
    if (this.state !== state) {
      this.state = state;
      this.dispatchEvent({ type: 'stateChange', state });
    }
  }

  /**
   * 建立WebSocket连接
   */
  private connect(): void {
    // 检查连接状态
    if (this.connecting ||
      (this.ws && (this.ws.readyState === WebSocket.OPEN || this.ws.readyState === WebSocket.CONNECTING))) {
      return;
    }

    // 检查重连频率
    const now = Date.now();
    if (now - this.lastConnectTime < 2000) {
      // 至少间隔10秒
      if (this.reconnectTimer) {
        clearTimeout(this.reconnectTimer);
      }

      this.reconnectTimer = setTimeout(() => {
        this.connect();
      }, 2000);
      return;
    }

    this.lastConnectTime = now;
    this.connecting = true;
    this.setState(WebSocketState.CONNECTING);

    try {
      this.cleanupExistingConnection();

      // 创建新的WebSocket连接
      this.ws = new WebSocket(this.serverUrl);

      // 设置连接超时
      const connectionTimeout = setTimeout(() => {
        if (!this.authenticated && this.ws && this.ws.readyState !== WebSocket.OPEN) {
          this.disconnect();
        }
      }, 2000);

      // 连接打开时
      this.ws.onopen = () => {
        this.setState(WebSocketState.CONNECTED);
        this.reconnectAttempts = 0;
        this.authenticate();
        clearTimeout(connectionTimeout);
      };

      // 接收消息
      this.ws.onmessage = this.handleMessage.bind(this);

      // 连接关闭
      this.ws.onclose = (event) => {
        this.connecting = false;
        this.setState(WebSocketState.CLOSED);

        // 检查是否是认证成功后立即关闭
        if (this.authenticated && this.lastPongTime && (now - this.lastPongTime < 5000)) {
          this.authenticated = false;
          return;
        }

        // 如果已认证但连接关闭，尝试重连
        if (this.authenticated) {
          this.scheduleReconnect();
        }

        this.authenticated = false;
      };

      // 连接错误
      this.ws.onerror = (error) => {
        this.setState(WebSocketState.ERROR);
        this.dispatchEvent({ type: 'error', error });
      };

    } catch (error) {
      this.connecting = false;
      this.setState(WebSocketState.ERROR);
      this.dispatchEvent({ type: 'error', error });
    }
  }

  /**
   * 清理现有连接
   */
  private cleanupExistingConnection(): void {
    if (this.ws) {
      this.ws.onclose = null;
      this.ws.onerror = null;
      this.ws.onmessage = null;
      this.ws.onopen = null;

      if (this.ws.readyState === WebSocket.OPEN || this.ws.readyState === WebSocket.CONNECTING) {
        try {
          this.ws.close();
        } catch (e) {
          // 忽略关闭错误
        }
      }

      this.ws = null;
    }

    // 清理定时器
    if (this.pingInterval) {
      clearInterval(this.pingInterval);
      this.pingInterval = null;
    }

    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
  }

  /**
   * 处理接收到的消息
   */
  private handleMessage(event: MessageEvent): void {
    try {
      const data = JSON.parse(event.data);

      switch (data.type) {
        case 'authSuccess':
          this.authenticated = true;
          this.connecting = false;
          this.userId = data.data.userId;
          this.dispatchEvent({ type: 'authSuccess', data: data.data });
          this.setupHeartbeat();
          break;

        case 'authError':
          this.connecting = false;
          this.authenticated = false;
          this.dispatchEvent({ type: 'authError', data: data.data });
          break;

        case 'pong':
          this.lastPongTime = Date.now();
          break;

        case 'heartbeat':
          // 服务器发起的心跳，回复一个ping
          if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            try {
              this.ws.send(JSON.stringify({ event: 'ping' }));
              this.lastPongTime = Date.now();
            } catch (e) {
              // 忽略错误
            }
          }
          break;

        case 'pointsUpdate':
          this.dispatchEvent({
            type: 'pointsUpdate',
            data: {
              points: data.data.points,
              change: data.data.change,
              sourceTabId: data.data.sourceTabId
            }
          });
          break;
        case 'assignPoints':
          this.dispatchEvent({
            type: 'assignPoints',
            data: {
              points: data.data.points,
              change: data.data.change,
              sourceTabId: data.data.sourceTabId
            }
          });
          break;

        case 'packageAssigned':
          this.dispatchEvent({
            type: 'packageAssigned',
            data: {
              packageInfo: data.data.packageInfo
            }
          });
          break;

        case 'payment_success_notification':
          this.dispatchEvent({
            type: 'paymentSuccess',
            data: data
          });
          break;

        default:
          this.dispatchEvent({ type: 'message', data });
      }
    } catch (error) {
      this.dispatchEvent({ type: 'error', error });
    }
  }

  /**
   * 发送认证消息
   */
  private authenticate(): void {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN || this.authenticated) {
      return;
    }

    const token = localStorage.getItem('token');
    if (token) {
      try {
        const tokenPayload = JSON.parse(atob(token.split('.')[1]));

        // 发送认证信息
        const message = {
          event: 'auth',
          data: {
            token: token.replace('Bearer ', ''),
            userId: tokenPayload.id,
            tabId: this.pageId
          }
        };

        this.ws.send(JSON.stringify(message));
      } catch (error) {
        this.connecting = false;
        this.dispatchEvent({ type: 'error', error });
      }
    } else {
      this.connecting = false;
    }
  }

  /**
   * 设置心跳检测
   */
  private setupHeartbeat(): void {
    if (this.pingInterval) {
      clearInterval(this.pingInterval);
    }

    this.lastPongTime = Date.now();

    // 每60秒发送一次心跳
    this.pingInterval = setInterval(() => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        // 检查上次pong时间，如果超过120秒没收到pong，认为连接已断开
        const now = Date.now();
        if (this.lastPongTime && now - this.lastPongTime > 120000) {
          this.reconnect();
          return;
        }

        try {
          this.ws.send(JSON.stringify({ event: 'ping' }));
        } catch (e) {
          this.reconnect();
        }
      } else if (this.ws &&
        (this.ws.readyState === WebSocket.CLOSED || this.ws.readyState === WebSocket.CLOSING)) {
        this.reconnect();
      }
    }, 60000);
  }

  /**
   * 安排重连
   */
  private scheduleReconnect(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
    }

    if (this.reconnectAttempts >= 10) {
      return;
    }

    this.reconnectAttempts++;

    // 指数退避重连策略
    const delay = Math.min(60000, 2000 * Math.pow(1.5, this.reconnectAttempts));

    this.reconnectTimer = setTimeout(() => {
      this.connect();
    }, delay);
  }

  /**
   * 立即重连
   */
  public reconnect(): void {
    this.disconnect();
    this.connect();
  }

  /**
   * 断开连接
   */
  public disconnect(): void {
    this.authenticated = false;
    this.connecting = false;
    this.cleanupExistingConnection();
    this.setState(WebSocketState.CLOSED);
  }

  /**
   * 获取当前WebSocket状态
   */
  public getState(): WebSocketState {
    return this.state;
  }

  /**
   * 获取当前标签页ID
   */
  public getTabId(): string {
    return this.pageId;
  }

  /**
   * 获取当前用户ID
   */
  public getUserId(): string | null {
    return this.userId;
  }

  /**
   * 发送消息
   * @param message 要发送的消息对象
   * @returns 是否发送成功
   */
  public sendMessage(message: any): boolean {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      return false;
    }

    try {
      this.ws.send(JSON.stringify(message));
      return true;
    } catch (error) {
      this.dispatchEvent({ type: 'error', error });
      return false;
    }
  }
}

export default WebSocketService.getInstance(); 