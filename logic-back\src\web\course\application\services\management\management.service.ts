import { Injectable, NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like, In, DataSource } from 'typeorm';
import { CourseSeries } from '../../../domain/entities/management/course-series.entity';
import { Course } from '../../../domain/entities/management/course.entity';
import { TaskTemplate } from '../../../domain/entities/teaching/task-template.entity';
import { CourseSettings } from '../../../domain/entities/management/course-settings.entity';
import { CourseSeriesTag } from '../../../domain/entities/marketplace/course-series-tag.entity';
import { CourseTag } from '../../../domain/entities/marketplace/course-tag.entity';
import { StatusUtils } from '../../../utils/management/status.utils';
import { CalculationUtils } from '../../../utils/management/calculation.utils';
import { ValidationUtils } from '../../../utils/management/validation.utils';

@Injectable()
export class ManagementService {
  constructor(
    @InjectRepository(CourseSeries)
    private readonly courseSeriesRepository: Repository<CourseSeries>,
    @InjectRepository(Course)
    private readonly courseRepository: Repository<Course>,
    @InjectRepository(TaskTemplate)
    private readonly taskTemplateRepository: Repository<TaskTemplate>,
    @InjectRepository(CourseSettings)
    private readonly courseSettingsRepository: Repository<CourseSettings>,
    @InjectRepository(CourseSeriesTag)
    private readonly courseSeriesTagRepository: Repository<CourseSeriesTag>,
    @InjectRepository(CourseTag)
    private readonly courseTagRepository: Repository<CourseTag>,
    private readonly dataSource: DataSource
  ) { }

  /**
   * 获取当前用户创建的系列课程列表
   * 优化版：减少数据库查询次数，提高并发性能
   */
  async getMyCourseSeries(
    userId: number,
    page: number,
    pageSize: number,
    status?: number,
    keyword?: string
  ) {
    // 1.构建查询条件
    const skip = (page - 1) * pageSize;
    const where: any = { creatorId: userId };

    if (status !== undefined) {
      where.status = status;
    }

    if (keyword) {
      where.title = Like(`%${keyword}%`);
    }

    // 2.执行查询
    const [items, total] = await this.courseSeriesRepository.findAndCount({
      where,
      skip,
      take: pageSize,
      order: { createdAt: 'DESC' }
    });

    // 如果没有查询到数据，直接返回
    if (items.length === 0) {
      return {
        items: [],
        page,
        pageSize,
        total,
        totalPages: Math.ceil(total / pageSize)
      };
    }

    // 3.获取所有系列ID
    const seriesIds = items.map(item => item.id);

    // 4.一次性查询所有系列的课程统计信息
    const courseStats = await this.courseRepository.createQueryBuilder('course')
      .select('course.series_id', 'seriesId')
      .addSelect('COUNT(*)', 'totalCourses')
      .addSelect('SUM(CASE WHEN course.has_video = 1 THEN 1 ELSE 0 END)', 'videoCourses')
      .addSelect('SUM(CASE WHEN course.has_document = 1 THEN 1 ELSE 0 END)', 'documentCourses')
      .addSelect('SUM(COALESCE(course.resources_count, 0))', 'resourcesCount')
      .addSelect('SUM(CASE WHEN course.status = 1 THEN 1 ELSE 0 END)', 'publishedCourses')
      .where('course.series_id IN (:...seriesIds)', { seriesIds })
      .groupBy('course.series_id')
      .getRawMany();

    // 5.转换为Map便于快速查找
    const statsMap = new Map(courseStats.map(stat => [Number(stat.seriesId), stat]));

    // 6.为每个系列添加统计信息
    for (const item of items) {
      const stats = statsMap.get(item.id) || {
        totalCourses: 0, videoCourses: 0, documentCourses: 0,
        resourcesCount: 0, publishedCourses: 0
      };

      const totalCourses = Number(stats.totalCourses) || 0;
      const publishedCourses = Number(stats.publishedCourses) || 0;
      const completionRate = totalCourses > 0 ? publishedCourses / totalCourses : 0;

      item['_contentSummary'] = {
        videoCourseCount: Number(stats.videoCourses) || 0,
        documentCourseCount: Number(stats.documentCourses) || 0,
        totalResourcesCount: Number(stats.resourcesCount) || 0,
        completionRate: parseFloat(completionRate.toFixed(2))
      };
    }

    // 7.返回分页及其系列下的课程统计信息
    return {
      items,
      page,
      pageSize,
      total,
      totalPages: Math.ceil(total / pageSize)
    };
  }

  /**
   * 根据ID获取课程系列
   */
  async findCourseSeriesById(id: number): Promise<CourseSeries> {
    const series = await this.courseSeriesRepository.findOne({ where: { id } });

    if (!series) {
      throw new NotFoundException(`系列ID为${id}的课程系列不存在`);
    }

    return series;
  }

  /**
   * 创建系列课程
   */
  async createCourseSeries(courseSeriesData: any, userId: number): Promise<CourseSeries> {
    // 使用事务确保数据一致性
    return await this.dataSource.transaction(async manager => {
      try {
        // 1.数据验证
        if (!courseSeriesData.title || courseSeriesData.title.trim() === '') {
          throw new BadRequestException('系列名称不能为空');
        }

        // 2.如果提供了标签ID，验证标签是否存在数据库里
        if (courseSeriesData.tagIds && courseSeriesData.tagIds.length > 0) {
          const existingTags = await manager.find(CourseTag, {
            where: { id: In(courseSeriesData.tagIds) }
          });

          if (existingTags.length !== courseSeriesData.tagIds.length) {
            throw new BadRequestException('部分标签不存在');
          }
        }

        // 3.创建新系列
        const newSeries = manager.create(CourseSeries, {
          title: courseSeriesData.title,
          description: courseSeriesData.description,
          coverImage: courseSeriesData.coverImage,
          category: courseSeriesData.category || 0,
          status: 0, // 默认为草稿状态
          projectMembers: courseSeriesData.projectMembers,
          creatorId: userId,
          totalCourses: 0, // 初始化为0
          totalStudents: 0 // 初始化为0
        });

        // 4.保存系列到数据库
        const savedSeries = await manager.save(CourseSeries, newSeries);

        // 5.如果提供了标签ID，创建关联关系
        if (courseSeriesData.tagIds && courseSeriesData.tagIds.length > 0) {
          const seriesTagRelations = courseSeriesData.tagIds.map(tagId => ({
            seriesId: savedSeries.id,
            tagId: tagId
          }));

          await manager.save(CourseSeriesTag, seriesTagRelations);
        }

        return savedSeries;
      } catch (error) {
        if (error instanceof BadRequestException) {
          throw error;
        }
        throw new BadRequestException(`创建系列课程失败: ${error.message}`);
      }
    });
  }

  /**
   * 更新课程系列
   */
  async updateCourseSeries(id: number, updateData: any, userId?: number): Promise<CourseSeries> {


    return await this.dataSource.transaction(async manager => {
      // 🔒 提前获取排他锁，防止标签关联的删除-重建过程中出现并发问题
      //     不加排他锁的时间线：
      // T1: A删除系列1的所有标签 (删除tag1, tag2)
      // T2: B删除系列1的所有标签 (此时已经没有了，删除0条)
      // T3: A插入新标签 (插入tag3, tag4)
      // T4: B插入新标签 (插入tag5, tag6)

      // ❌ 最终结果：系列1有4个标签 [tag3, tag4, tag5, tag6]
      // ✅ 预期结果：应该只有 [tag5, tag6] (最后操作的用户)
      // 使用事务确保数据一致性
      await manager.query(
        'SELECT id FROM course_series WHERE id = ? FOR UPDATE',
        [id]
      );

      // 查找现有系列
      const series = await manager.findOne(CourseSeries, { where: { id } });
      if (!series) {
        throw new NotFoundException(`系列ID为${id}的课程系列不存在`);
      }

      // 验证权限（如果提供了userId）
      if (userId && series.creatorId !== userId) {
        throw new ForbiddenException('无权限更新此课程系列');
      }

      // 提取标签ID并从更新数据中移除
      const tagIds = updateData.tagIds;
      delete updateData.tagIds;

      // 防止更新只读字段
      delete updateData.id;
      delete updateData.createdAt;
      delete updateData.updatedAt;
      delete updateData.creatorId; // 创建者不允许修改

      // 如果提供了标签ID，验证标签是否存在
      if (tagIds && tagIds.length > 0) {
        const existingTags = await manager.find(CourseTag, {
          where: { id: In(tagIds) }
        });

        if (existingTags.length !== tagIds.length) {
          throw new BadRequestException('部分标签不存在');
        }
      }

      // 应用更新
      Object.assign(series, updateData);

      // 保存系列更新
      const updatedSeries = await manager.save(CourseSeries, series);

      // 处理标签关联更新
      if (tagIds !== undefined) {
        // 删除现有的标签关联
        await manager.delete(CourseSeriesTag, { seriesId: id });

        // 如果提供了新的标签ID，创建新的关联关系
        if (tagIds && tagIds.length > 0) {
          const seriesTagRelations = tagIds.map((tagId: number) => ({
            seriesId: id,
            tagId: tagId
          }));

          await manager.save(CourseSeriesTag, seriesTagRelations);
        }
      }

      return updatedSeries;
    });
  }

  async removeCourseSeries(id: number, userId?: number): Promise<{ success: boolean; message: string }> {
    return await this.dataSource.transaction(async manager => {
      // 🔒 锁定系列记录，防止删除过程中被其他操作干扰
      await manager.query('SELECT id FROM course_series WHERE id = ? FOR UPDATE', [id]);
      
      const series = await manager.findOne(CourseSeries, { where: { id } });
      if (!series) {
        throw new NotFoundException(`系列ID为${id}的课程系列不存在`);
      }
      
      if (userId && series.creatorId !== userId) {
        throw new ForbiddenException('无权限删除此课程系列');
      }
      
      await manager.remove(CourseSeries, series);
      
      return {
        success: true,
        message: `ID为${id}的课程系列已成功删除`
      };
    });
  }

  /**
   * 发布课程系列11
   */
  async publishCourseSeries(seriesId: number, userId: number) {
    // 🚀 优化策略：先做预检查，减少不必要的锁等待和连接占用

    // 1. 预检查（无锁，快速失败）
    const series = await this.dataSource.getRepository(CourseSeries).findOne({
      where: { id: seriesId }
    });

    if (!series) {
      throw new NotFoundException(`系列ID为${seriesId}的课程系列不存在`);
    }

    // 如果已发布，返回信息提示请勿重复发布系列
    if (series.status === 1) {
      throw new BadRequestException('该系列已发布，请勿重复发布');
    }

    // 验证权限，只有创造者才有权限发布
    if (series.creatorId !== userId) {
      throw new ForbiddenException('无权限发布此课程系列');
    }

    // 检查发布条件（例如：至少需要一个课程）
    const courseCount = await this.dataSource.getRepository(Course).count({
      where: { seriesId }
    });
    if (courseCount <= 0) {
      throw new BadRequestException('发布失败：课程系列中至少需要包含一个课程');
    }

    // 2. 短事务更新（只在最后更新时使用锁，大幅减少锁持有时间）
    const updatedSeries = await this.dataSource.transaction(async manager => {
      // 🔒 使用 TypeORM 的悲观锁，比原生 SQL 更高效
      const lockedSeries = await manager.findOne(CourseSeries, {
        where: { id: seriesId },
        lock: { mode: 'pessimistic_write' }
      });

      if (!lockedSeries) {
        throw new NotFoundException(`系列ID为${seriesId}的课程系列不存在`);
      }

      // 再次检查状态（防止在预检查后被其他请求修改）
      if (lockedSeries.status === 1) {
        throw new BadRequestException('该系列已发布，请勿重复发布');
      }

      // 快速更新状态
      lockedSeries.status = 1;
      return await manager.save(CourseSeries, lockedSeries);
    });

    // 3. 获取发布统计信息（在事务外执行，不占用锁）
    const publishStats = await this.getCourseSeriesPublishStats(seriesId);

    // 4. 返回结果给控制器
    return {
      success: true,
      message: '课程系列发布成功',
      data: updatedSeries,
      publishStats
    };
  }

  /**
   * 获取课程系列的发布统计信息
   * 包含
   * 1.课程数量
   * 2.已发布课程数量
   * 3.视频课程数量
   * 4.文档课程数量
   * 5.总视频时长
   * 6.总资源数量
   */
  private async getCourseSeriesPublishStats(seriesId: number) {
    // 查询系列下的课程统计信息
    const courseStats = await this.courseRepository.createQueryBuilder('course')
      .select('COUNT(*)', 'totalCourses')
      .addSelect('SUM(CASE WHEN course.status = 1 THEN 1 ELSE 0 END)', 'publishedCourses')
      .addSelect('SUM(CASE WHEN course.has_video = 1 THEN 1 ELSE 0 END)', 'videoCourseCount')
      .addSelect('SUM(CASE WHEN course.has_document = 1 THEN 1 ELSE 0 END)', 'documentCourseCount')
      .addSelect('SUM(course.video_duration)', 'totalVideoDuration')
      .addSelect('SUM(COALESCE(JSON_LENGTH(course.additional_resources), 0))', 'totalResourcesCount')
      .where('course.series_id = :seriesId', { seriesId })
      .getRawOne();

    return {
      videoCourseCount: Number(courseStats.videoCourseCount) || 0,
      documentCourseCount: Number(courseStats.documentCourseCount) || 0,
      totalVideoDuration: Number(courseStats.totalVideoDuration) || 0,
      totalResourcesCount: Number(courseStats.totalResourcesCount) || 0,
      publishedCourses: Number(courseStats.publishedCourses) || 0,
      totalCourses: Number(courseStats.totalCourses) || 0
    };
  }

  /**
   * 获取系列下的课程列表
   */
  async getSeriesCourses(
    seriesId: number,
    status?: number,
    page: number = 1,
    pageSize: number = 20,
    userId?: number
  ) {
    // 检查系列是否存在
    const series = await this.findCourseSeriesById(seriesId);

    // 如果提供了userId，检查权限
    if (userId && series.creatorId !== userId) {
      throw new ForbiddenException('无权限查看此系列课程');
    }

    // 构建查询条件
    const where: any = { seriesId };

    if (status !== undefined) {
      where.status = status;
    }

    // 计算总数
    const total = await this.courseRepository.count({ where });

    // 分页查询
    const skip = (page - 1) * pageSize;
    const courses = await this.courseRepository.find({
      where,
      order: { orderIndex: 'ASC' },
      skip,
      take: pageSize
    });

    return {
      seriesId,
      courses,
      total,
      page,
      pageSize
    };
  }

  /**
   * 创建课程
   */
  async createCourse(courseData: any, userId: number) {
    try {
      // 1.数据验证
      if (!courseData.title || courseData.title.trim() === '') {
        throw new BadRequestException('课程标题不能为空');
      }

      if (!courseData.seriesId) {
        throw new BadRequestException('必须指定所属系列ID');
      }

      // 2.检查系列是否存在
      await this.findCourseSeriesById(courseData.seriesId);

      // 3.获取课程序号
      // 获取课程的最大排序索引，为新添加的课程分配下一个排序号
      const maxOrderIndex = await this.courseRepository
        .createQueryBuilder('course')
        .select('MAX(course.order_index)', 'maxOrder')
        .where('course.series_id = :seriesId', { seriesId: courseData.seriesId })
        .getRawOne();

      const nextOrderIndex = (maxOrderIndex?.maxOrder || 0) + 1;

      // 4.使用事务处理多表操作
      return await this.courseRepository.manager.transaction(async manager => {
        // 4.0增加提前获取排他锁，防止后续出现死锁
        await manager.query(
          'SELECT id FROM course_series WHERE id = ? FOR UPDATE',
          [courseData.seriesId]
        );


        // 4.1 创建新课程。create触发时间初始化， TypeORM 这是一个新实体，需要设置创建时间
        const newCourse = this.courseRepository.create({
          seriesId: courseData.seriesId,
          title: courseData.title,
          description: courseData.description,
          coverImage: courseData.coverImage,
          // 直接使用请求体中的字段，不再需要JSON解析
          hasVideo: courseData.hasVideo || 0,
          hasDocument: courseData.hasDocument || 0,
          hasAudio: courseData.hasAudio || 0,
          videoDuration: courseData.videoDuration || 0,
          contentConfig: courseData.contentConfig || {},
          teachingInfo: courseData.teachingInfo || [],
          additionalResources: courseData.additionalResources || [],
          // 双重保障，前端没传则后端查出来
          orderIndex: courseData.orderIndex || nextOrderIndex,
          status: 0, // 默认为草稿状态
          creatorId: userId
        });

        // 4.2 保存课程到数据库
        const savedCourse = await manager.save(Course, newCourse);

        // 4.3 更新系列课程数量
        await this.updateSeriesCourseCountWithManager(courseData.seriesId, manager);

        return savedCourse;
      });
    } catch (error) {
      if (error instanceof BadRequestException || error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(`创建课程失败: ${error.message}`);
    }
  }


  /**
   * 更新系列的课程数量（支持事务管理器）
   */
  private async updateSeriesCourseCountWithManager(seriesId: number, manager: any) {
    const courseCount = await manager.count(Course, { where: { seriesId } });
    await manager.update(CourseSeries, seriesId, { totalCourses: courseCount });
  }

  /**
   * 设置课程配置
   */
  async setCourseSettings(courseId: number, settingsData: any, userId: number) {
    // 1.查找课程
    const course = await this.courseRepository.findOne({ where: { id: courseId } });

    if (!course) {
      throw new NotFoundException(`课程ID为${courseId}的课程不存在`);
    }

    // 2.查找系列
    const series = await this.findCourseSeriesById(course.seriesId);

    // 3.验证权限，只有系列创建者才能设置课程配置
    if (series.creatorId !== userId) {
      throw new ForbiddenException('无权限设置此课程配置');
    }

    // 4.使用事务处理并发问题
    return await this.courseSettingsRepository.manager.transaction(async manager => {
      try {
        // 🔑 关键：使用 FOR UPDATE 锁定课程记录，防止并发创建重复配置
        await manager.query( 
          'SELECT id FROM courses WHERE id = ? FOR UPDATE',
          [courseId]
        );

        // 5.在事务内查找现有设置
        let settings = await manager.findOne(CourseSettings, { where: { courseId } });

        // 6.不存在就创建新设置，有就更新
        if (!settings) {
          settings = manager.create(CourseSettings, {
            courseId,
            templateId: settingsData.templateId || null,
            requiredPoints: settingsData.requiredPoints || 0,
            autoCreateTasks: settingsData.autoCreateTasks !== undefined ? settingsData.autoCreateTasks : 0
          });
        } else {
          // 更新现有设置
          if (settingsData.templateId !== undefined) {
            settings.templateId = settingsData.templateId;
          }

          if (settingsData.requiredPoints !== undefined) {
            settings.requiredPoints = settingsData.requiredPoints;
          }

          if (settingsData.autoCreateTasks !== undefined) {
            settings.autoCreateTasks = settingsData.autoCreateTasks;
          }
        }

        // 7.保存到数据库
        await manager.save(CourseSettings, settings);

        return {
          success: true,
          message: '课程配置设置成功',
          settings
        };
      } catch (error) {
        // 捕获外键约束错误
        if (error.message && error.message.includes('foreign key constraint fails')) {
          throw new BadRequestException('设置失败：指定的模板ID不存在，请输入有效的模板ID');
        }
        throw error;
      }
    });
  }

  /**
   * 添加任务模板
   */
  async addTaskTemplate(courseId: number, templateData: any, userId: number) {
    // 1.查找课程
    const course = await this.courseRepository.findOne({ where: { id: courseId } });

    if (!course) {
      throw new NotFoundException(`课程ID为${courseId}的课程不存在`);
    }

    // 2.查找系列
    const series = await this.findCourseSeriesById(course.seriesId);

    // 3.验证权限
    if (series.creatorId !== userId) {
      throw new ForbiddenException('无权限为此课程添加任务模板');
    }

    // 4.数据验证
    if (!templateData.taskName || templateData.taskName.trim() === '') {
      throw new BadRequestException('任务名称不能为空');
    }

    // 5.创建任务模板 - 不再手动设置生成列的值
    const newTemplate = this.taskTemplateRepository.create({
      courseId, // 使用路径参数中的courseId
      taskName: templateData.taskName,
      taskDescription: templateData.taskDescription,
      durationDays: templateData.durationDays || 7,
      attachments: templateData.attachments || [],
      workIdsStr: templateData.workIdsStr || '',
      selfAssessmentItems: templateData.selfAssessmentItems || [],
      status: 1 // 默认启用
    });

    // 保存到数据库
    return await this.taskTemplateRepository.save(newTemplate);
  }

  /**
   * 根据ID获取课程详情
   */
  async findCourseById(id: number, userId?: number): Promise<Course> {
    const course = await this.courseRepository.findOne({ where: { id } });

    if (!course) {
      throw new NotFoundException(`课程ID为${id}的课程不存在`);
    }

    // 如果提供了userId，检查权限
    if (userId) {
      const series = await this.findCourseSeriesById(course.seriesId);
      if (series.creatorId !== userId) {
        throw new ForbiddenException('无权限查看此课程');
      }
    }

    return course;
  }

  /**
   * 更新课程
   */
  async updateCourse(id: number, updateData: any, userId: number): Promise<Course> {
    // 查找现有课程
    const course = await this.findCourseById(id);

    // 查找系列并验证权限
    const series = await this.findCourseSeriesById(course.seriesId);
    if (series.creatorId !== userId) {
      throw new ForbiddenException('无权限更新此课程');
    }

    // 防止更新只读字段
    delete updateData.id;
    delete updateData.createdAt;
    delete updateData.updatedAt;
    delete updateData.creatorId;
    delete updateData.seriesId; // 不允许修改所属系列

    // 应用更新
    Object.assign(course, updateData);

    // 保存更新
    return await this.courseRepository.save(course);
  }

  /**
   * 删除课程
   */
  async removeCourse(id: number, userId: number): Promise<{ success: boolean; message: string }> {
    // 查找现有课程
    const course = await this.findCourseById(id);

    // 查找系列并验证权限
    const series = await this.findCourseSeriesById(course.seriesId);
    if (series.creatorId !== userId) {
      throw new ForbiddenException('无权限删除此课程');
    }

    // 使用事务处理删除操作
    return await this.courseRepository.manager.transaction(async manager => {
      // 🔑 新增：提前获取排他锁，防止死锁
      await manager.query(
        'SELECT id FROM course_series WHERE id = ? FOR UPDATE',
        [course.seriesId]
      );

      // 删除课程
      await manager.remove(Course, course);

      // 更新系列课程数量
      await this.updateSeriesCourseCountWithManager(course.seriesId, manager);

      return {
        success: true,
        message: `ID为${id}的课程已成功删除`
      };
    });
  }

  /**
   * 调整课程排序
   */
  async updateCourseOrders(
    seriesId: number,
    courseOrders: Array<{ courseId: number; orderIndex: number }>,
    userId: number
  ): Promise<{ success: boolean; message: string }> {
    // 使用事务和排他锁确保排序调整的原子性
    return await this.dataSource.transaction(async manager => {
      // 🔒 提前获取排他锁，防止多个用户同时调整同一系列的课程排序
      // 不加锁会出现以下的覆盖行为
      // 初始状态：
      // 课程A: orderIndex = 1
      // 课程B: orderIndex = 2
      // 课程C: orderIndex = 3

      // 用户X：想调整为 A=3, B=1, C=2
      // 用户Y：想调整为 A=2, B=3, C=1

      // 时间线：
      // T1: X更新课程A: 1 → 3
      // T2: Y更新课程A: 3 → 2  (覆盖X的更新)
      // T3: X更新课程B: 2 → 1
      // T4: Y更新课程B: 1 → 3  (覆盖X的更新)
      // T5: X更新课程C: 3 → 2
      // T6: Y更新课程C: 2 → 1  (覆盖X的更新)

      // ❌ 中间状态可能出现：A=2, B=1, C=2 (两个课程都是orderIndex=2)
      await manager.query(
        'SELECT id FROM course_series WHERE id = ? FOR UPDATE',
        [seriesId]
      );

      // 1.验证系列存在并检查权限
      const series = await manager.findOne(CourseSeries, { where: { id: seriesId } });
      if (!series) {
        throw new NotFoundException(`系列ID为${seriesId}的课程系列不存在`);
      }
      if (series.creatorId !== userId) {
        throw new ForbiddenException('无权限调整此系列课程排序');
      }

      // 2.验证所有课程ID是否属于该系列
      const courseIds = courseOrders.map(item => item.courseId);
      const courses = await manager.find(Course, {
        where: { id: In(courseIds), seriesId }
      });

      if (courses.length !== courseIds.length) {
        throw new BadRequestException('部分课程ID不存在或不属于该系列');
      }

      // 3.批量更新排序（现在是原子操作）
      for (const order of courseOrders) {
        await manager.update(Course, { id: order.courseId }, { orderIndex: order.orderIndex });
      }

      return {
        success: true,
        message: `系列${seriesId}的课程排序已成功更新`
      };
    });
  }

  /**
   * 获取我的系列课程列表（带格式化）
   * 在原有方法基础上添加数据转换和响应格式化
   */
  async getMyCourseSeries_Formatted(
    userId: number,
    page?: number,
    pageSize?: number,
    status?: number,
    keyword?: string
  ) {
    // 验证和标准化分页参数
    const { page: validatedPage, pageSize: validatedPageSize } = ValidationUtils.validateAndNormalizePagination(page, pageSize);
    // 1.调用原有方法获取数据
    const result = await this.getMyCourseSeries(userId, validatedPage, validatedPageSize, status, keyword);

    // 2.转换为API响应格式
    const list = result.items.map(series => {
      // 直接使用服务层计算的内容摘要
      const contentSummary = series['_contentSummary'];

      // 删除service内部加工的字段
      delete series['_contentSummary'];

      // 转换分类标签和状态标签
      const categoryLabel = StatusUtils.getCategoryLabel(series.category);
      const statusLabel = StatusUtils.getCourseStatusLabel(series.status);

      return {
        ...series,
        categoryLabel,
        statusLabel,
        contentSummary
      };
    });

    // 3.构建完整响应数据
    return {
      list,
      pagination: {
        page: result.page,
        pageSize: result.pageSize,
        total: result.total,
        totalPages: result.totalPages,
        hasNext: result.page < result.totalPages,
        hasPrev: result.page > 1
      }
    };
  }

  /**
   * 获取系列下课程列表（带格式化）
   * 在原有方法基础上添加数据转换和响应格式化
   */
  async getSeriesCourses_Formatted(
    seriesId: number,
    status: number | undefined,
    page?: number,
    pageSize?: number,
    userId?: number
  ) {
    // 验证和标准化分页参数
    const { page: validatedPage, pageSize: validatedPageSize } = ValidationUtils.validateAndNormalizePagination(page, pageSize || 20);
    // 1.调用原有方法获取数据
    const result = await this.getSeriesCourses(seriesId, status, validatedPage, validatedPageSize, userId);

    // 2.格式化响应
    const list = result.courses.map(course => {
      const statusLabel = StatusUtils.getCourseStatusLabel(course.status);
      const videoDurationLabel = CalculationUtils.formatVideoDuration(course.videoDuration);
      return {
        ...course,
        statusLabel,
        videoDurationLabel
      };
    });

    // 3.构建完整响应数据
    return {
      list,
      pagination: {
        page: result.page,
        pageSize: result.pageSize,
        total: result.total,
        totalPages: Math.ceil(result.total / result.pageSize),
        hasNext: result.page < Math.ceil(result.total / result.pageSize),
        hasPrev: result.page > 1
      }
    };
  }

  /**
   * 发布课程系列（带格式化）
   * 在原有方法基础上添加响应数据格式化
   */
  async publishCourseSeries_Formatted(seriesId: number, userId: number) {
    // 1.调用原有方法获取数据
    const result = await this.publishCourseSeries(seriesId, userId);

    // 2.构建状态标签
    const statusLabel = StatusUtils.getCourseStatusLabel(result.data.status);

    // 3.格式化响应数据
    return {
      id: result.data.id,
      title: result.data.title,
      status: result.data.status,
      statusLabel,
      publishedAt: new Date().toISOString(), // 使用当前时间作为发布时间
      totalCourses: result.publishStats.totalCourses,
      publishedCourses: result.publishStats.publishedCourses,
      publishStats: {
        videoCourseCount: result.publishStats.videoCourseCount,
        documentCourseCount: result.publishStats.documentCourseCount,
        totalVideoDuration: result.publishStats.totalVideoDuration,
        totalResourcesCount: result.publishStats.totalResourcesCount
      }
    };
  }
}
