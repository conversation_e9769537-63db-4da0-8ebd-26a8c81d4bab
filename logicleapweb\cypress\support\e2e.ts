// ***********************************************************
// This example support/e2e.ts is processed and
// loaded automatically before your test files.
//
// This is a great place to put global configuration and
// behavior that modifies Cy<PERSON>.
//
// You can change the location of this file or turn off
// automatically serving support files with the
// 'supportFile' configuration option.
//
// You can read more here:
// https://on.cypress.io/configuration
// ***********************************************************

// Import commands.js using ES2015 syntax:
import './commands'

// 避免刷屏问题：不再使用全局命令钩子
// 用户可以在测试中手动使用 cy.slowDown() 命令来添加延迟

// 如果需要全局减速，可以修改 Cypress 配置
const delayBetweenCommands = Cypress.env('delayBetweenCommands');
if (delayBetweenCommands && delayBetweenCommands > 0) {
  // 设置更慢的输入速度
  Cypress.config('keystrokeDelay', delayBetweenCommands);
  
  // 设置更长的默认命令超时时间
  Cypress.config('defaultCommandTimeout', 10000);
}

// Alternatively you can use CommonJS syntax:
// require('./commands')