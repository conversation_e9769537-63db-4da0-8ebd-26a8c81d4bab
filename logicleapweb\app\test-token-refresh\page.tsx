'use client';

import React, { useState, useEffect } from 'react';
import { Button, Card, Typography, Space, message, Divider } from 'antd';
import request from '../../lib/request';

const { Title, Text, Paragraph } = Typography;

export default function TestTokenRefreshPage() {
  const [tokenInfo, setTokenInfo] = useState<{
    token: string | null;
    refreshToken: string | null;
  }>({
    token: null,
    refreshToken: null
  });
  
  const [testResults, setTestResults] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);

  // 获取当前token信息
  const getTokenInfo = () => {
    const token = localStorage.getItem('token');
    const refreshToken = localStorage.getItem('refreshToken');
    setTokenInfo({ token, refreshToken });

    // 在控制台输出完整的token信息用于调试
    console.log('当前Token信息:', {
      token: token ? `${token.substring(0, 20)}...` : '无',
      refreshToken: refreshToken ? `${refreshToken.substring(0, 20)}...` : '无',
      tokenLength: token?.length || 0,
      refreshTokenLength: refreshToken?.length || 0
    });
  };

  useEffect(() => {
    getTokenInfo();
  }, []);

  // 添加测试结果
  const addTestResult = (result: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${result}`]);
  };

  // 测试正常API调用
  const testNormalApi = async () => {
    setLoading(true);
    try {
      console.log('📤 发起受保护API请求');
      addTestResult('📤 发起受保护API请求...');

      const response = await request.get('/api/router-guard/protected');

      console.log('📥 API响应:', response.data);
      addTestResult('✅ 正常API调用成功');
      addTestResult(`   响应: ${response.data.data.message}`);
      addTestResult(`   用户: ${response.data.data.user.nickName}`);
      message.success('API调用成功');
    } catch (error: any) {
      console.error('❌ API调用失败:', error);
      addTestResult(`❌ 正常API调用失败: ${error.message}`);
      addTestResult(`   错误详情: ${JSON.stringify(error.response?.data || error)}`);
      message.error('API调用失败');
    } finally {
      setLoading(false);
    }
  };

  // 手动刷新token
  const manualRefreshToken = async () => {
    setLoading(true);
    try {
      const refreshToken = localStorage.getItem('refreshToken');
      console.log('🔄 准备手动刷新token，refreshToken:', refreshToken ? `${refreshToken.substring(0, 20)}...` : '无');

      if (!refreshToken) {
        throw new Error('没有refreshToken');
      }

      addTestResult('🔄 开始手动刷新token...');

      const response = await request.post('/api/router-guard/refresh-token', {
        refreshToken: refreshToken
      });

      console.log('📥 刷新token响应:', response);

      if (response.data.code === 200) {
        const { token, refreshToken: newRefreshToken } = response.data.data;
        localStorage.setItem('token', token);
        localStorage.setItem('refreshToken', newRefreshToken);
        getTokenInfo();
        addTestResult('✅ 手动刷新token成功');
        message.success('Token刷新成功');
      } else {
        console.error('❌ 刷新失败，响应:', response.data);
        throw new Error(response.data.message || response.data.msg || 'Token刷新失败');
      }
    } catch (error: any) {
      console.error('❌ 手动刷新token异常:', error);
      addTestResult(`❌ 手动刷新token失败: ${error.message}`);
      addTestResult(`   错误详情: ${JSON.stringify(error.response?.data || error)}`);
      message.error('Token刷新失败');
    } finally {
      setLoading(false);
    }
  };

  // 模拟token过期（清除token但保留refreshToken）
  const simulateTokenExpired = () => {
    localStorage.removeItem('token');
    getTokenInfo();
    addTestResult('🔄 已清除token，模拟token过期');
    message.info('已清除token，下次API调用将触发自动刷新');
  };

  // 测试401错误格式
  const testErrorFormat = async () => {
    setLoading(true);
    try {
      // 先清除token
      localStorage.removeItem('token');
      addTestResult('🔍 测试401错误格式，已清除token');

      // 直接调用API，不使用无感刷新逻辑
      const response = await fetch('/api/router-guard/protected', {
        headers: {
          'Authorization': 'invalid-token'
        }
      });

      const data = await response.json();
      console.log('📥 401错误响应格式:', {
        status: response.status,
        data: data
      });

      addTestResult(`📥 401错误响应: status=${response.status}`);
      addTestResult(`   数据格式: ${JSON.stringify(data)}`);

    } catch (error: any) {
      console.error('❌ 测试错误格式失败:', error);
      addTestResult(`❌ 测试失败: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // 检查当前用户信息
  const checkUserInfo = () => {
    const user = localStorage.getItem('user');
    const token = localStorage.getItem('token');
    const refreshToken = localStorage.getItem('refreshToken');

    console.log('📋 当前存储信息:', {
      user: user ? JSON.parse(user) : null,
      token: token ? `${token.substring(0, 20)}...` : null,
      refreshToken: refreshToken ? `${refreshToken.substring(0, 20)}...` : null
    });

    addTestResult('📋 用户信息检查完成，详情请查看控制台');

    if (!user) {
      addTestResult('⚠️ 没有用户信息，请先登录');
      message.warning('请先登录系统');
      return;
    }

    if (!token) {
      addTestResult('⚠️ 没有access token');
    }

    if (!refreshToken) {
      addTestResult('⚠️ 没有refresh token');
    }

    if (token && refreshToken) {
      addTestResult('✅ token信息完整');
    }
  };

  // 清除所有token
  const clearAllTokens = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('refreshToken');
    localStorage.removeItem('user');
    getTokenInfo();
    setTestResults([]);
    addTestResult('🗑️ 已清除所有token和用户信息');
    message.info('已清除所有token');
  };

  // 测试无感刷新（先清除token，然后调用API）
  const testAutoRefresh = async () => {
    setLoading(true);
    try {
      // 先清除token
      localStorage.removeItem('token');
      getTokenInfo();
      addTestResult('🔄 已清除token，准备测试自动刷新');
      console.log('🔄 开始无感刷新测试，已清除token');

      // 等待一秒让用户看到token被清除
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 调用需要认证的API，应该会触发自动刷新
      console.log('📤 发起API请求，应该触发自动刷新');
      addTestResult('📤 发起API请求，应该触发自动刷新...');

      const response = await request.get('/api/router-guard/protected');

      console.log('📥 API响应成功:', response.data);

      // 刷新token信息显示
      getTokenInfo();
      addTestResult('✅ 无感刷新测试成功！API调用成功，token已自动刷新');
      addTestResult(`   API响应: ${response.data.data.message}`);
      addTestResult(`   用户信息: ${response.data.data.user.nickName}`);
      message.success('无感刷新测试成功！');
    } catch (error: any) {
      console.error('❌ 无感刷新测试失败:', error);
      addTestResult(`❌ 无感刷新测试失败: ${error.message}`);
      addTestResult(`   错误详情: ${JSON.stringify(error.response?.data || error)}`);
      message.error('无感刷新测试失败');
    } finally {
      setLoading(false);
    }
  };

  // 测试主动刷新（请求拦截器中的主动刷新逻辑）
  const testProactiveRefresh = async () => {
    setLoading(true);
    try {
      const refreshToken = localStorage.getItem('refreshToken');
      if (!refreshToken) {
        addTestResult('❌ 没有refreshToken，无法测试主动刷新');
        message.error('请先登录获取refreshToken');
        return;
      }

      // 清除token，保留refreshToken
      localStorage.removeItem('token');
      getTokenInfo();
      addTestResult('🔄 已清除token，保留refreshToken');
      addTestResult('🎯 测试请求拦截器中的主动刷新逻辑...');
      console.log('🎯 开始测试主动刷新：token不存在但refreshToken存在');

      // 等待一秒让用户看到token被清除
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 发起API请求，请求拦截器应该检测到没有token但有refreshToken，主动刷新
      console.log('📤 发起API请求，请求拦截器应该主动刷新token');
      addTestResult('📤 发起API请求，请求拦截器应该主动刷新token...');

      const response = await request.get('/api/router-guard/protected');

      console.log('📥 API响应成功:', response.data);

      // 刷新token信息显示
      getTokenInfo();
      addTestResult('✅ 主动刷新测试成功！请求拦截器成功刷新token');
      addTestResult(`   API响应: ${response.data.data.message}`);
      addTestResult(`   用户信息: ${response.data.data.user.nickName}`);

      // 检查token是否已经被设置
      const newToken = localStorage.getItem('token');
      if (newToken) {
        addTestResult('✅ 确认：新token已保存到localStorage');
      } else {
        addTestResult('⚠️ 警告：token未保存到localStorage');
      }

      message.success('主动刷新测试成功！');
    } catch (error: any) {
      console.error('❌ 主动刷新测试失败:', error);
      addTestResult(`❌ 主动刷新测试失败: ${error.message}`);

      // 详细的错误分析
      if (error.response) {
        addTestResult(`   HTTP状态: ${error.response.status}`);
        addTestResult(`   响应数据: ${JSON.stringify(error.response.data)}`);
      } else if (error.message === 'Token刷新失败，请重新登录') {
        addTestResult('   原因: 请求拦截器中refreshToken刷新失败');
      } else {
        addTestResult(`   网络错误: ${error.message}`);
      }

      message.error('主动刷新测试失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ padding: '24px', maxWidth: '800px', margin: '0 auto' }}>
      <Title level={2}>Token无感刷新测试页面</Title>
      
      <Card title="当前Token状态" style={{ marginBottom: '16px' }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <Text strong>Access Token: </Text>
            <Text code style={{ wordBreak: 'break-all' }}>
              {tokenInfo.token ? `${tokenInfo.token.substring(0, 50)}...` : '无'}
            </Text>
          </div>
          <div>
            <Text strong>Refresh Token: </Text>
            <Text code style={{ wordBreak: 'break-all' }}>
              {tokenInfo.refreshToken ? `${tokenInfo.refreshToken.substring(0, 50)}...` : '无'}
            </Text>
          </div>
          <Button onClick={getTokenInfo}>刷新Token信息</Button>
        </Space>
      </Card>

      <Card title="测试功能" style={{ marginBottom: '16px' }}>
        <Space wrap>
          <Button
            onClick={checkUserInfo}
          >
            检查用户信息
          </Button>
          <Button
            type="primary"
            onClick={testNormalApi}
            loading={loading}
          >
            测试正常API调用
          </Button>
          <Button
            onClick={manualRefreshToken}
            loading={loading}
          >
            手动刷新Token
          </Button>
          <Button
            onClick={simulateTokenExpired}
            type="dashed"
          >
            模拟Token过期
          </Button>
          <Button
            onClick={testErrorFormat}
            loading={loading}
          >
            测试401错误格式
          </Button>
          <Button
            onClick={testAutoRefresh}
            loading={loading}
            type="primary"
            ghost
          >
            测试无感刷新
          </Button>
          <Button
            onClick={testProactiveRefresh}
            loading={loading}
            type="primary"
            style={{ backgroundColor: '#52c41a', borderColor: '#52c41a' }}
          >
            测试主动刷新
          </Button>
          <Button
            onClick={clearAllTokens}
            danger
          >
            清除所有Token
          </Button>
        </Space>
      </Card>

      <Card title="测试结果" style={{ marginBottom: '16px' }}>
        <div style={{ maxHeight: '300px', overflowY: 'auto' }}>
          {testResults.length === 0 ? (
            <Text type="secondary">暂无测试结果</Text>
          ) : (
            testResults.map((result, index) => (
              <div key={index} style={{ marginBottom: '8px' }}>
                <Text code>{result}</Text>
              </div>
            ))
          )}
        </div>
      </Card>

      <Card title="使用说明">
        <Paragraph>
          <Title level={4}>测试步骤：</Title>
          <ol>
            <li><strong>登录系统</strong>：确保您已经登录并获得了token和refreshToken</li>
            <li><strong>测试正常API</strong>：点击"测试正常API调用"确认当前token有效</li>
            <li><strong>测试主动刷新</strong>：点击"测试主动刷新"，测试请求拦截器中的主动刷新逻辑</li>
            <li><strong>测试无感刷新</strong>：点击"测试无感刷新"，测试响应拦截器中的无感刷新逻辑</li>
            <li><strong>手动刷新</strong>：点击"手动刷新Token"测试手动刷新功能</li>
          </ol>
          
          <Divider />
          
          <Title level={4}>功能说明：</Title>
          <ul>
            <li><strong>主动刷新</strong>：请求拦截器检测到没有token但有refreshToken时，主动刷新token</li>
            <li><strong>无感刷新</strong>：响应拦截器检测到401错误时，自动使用refreshToken获取新的token</li>
            <li><strong>请求队列</strong>：刷新期间的其他请求会被暂存，刷新完成后自动重试</li>
            <li><strong>失败处理</strong>：如果refreshToken也过期，系统会自动清除所有认证信息</li>
            <li><strong>双重保障</strong>：主动刷新 + 无感刷新，确保用户体验流畅</li>
          </ul>
        </Paragraph>
      </Card>
    </div>
  );
}
