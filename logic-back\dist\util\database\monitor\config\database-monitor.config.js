"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.databaseMonitorConfig = void 0;
exports.databaseMonitorConfig = {
    database: {
        enableDatabaseMonitoring: process.env.ENABLE_DB_MONITORING !== 'false',
        lightweightMode: process.env.DB_MONITOR_LIGHTWEIGHT === 'true',
        enableSlowQueryLogging: process.env.ENABLE_SLOW_QUERY_LOG !== 'false',
        enableQueryMetrics: process.env.ENABLE_QUERY_METRICS !== 'false',
        enableStackTrace: process.env.ENABLE_STACK_TRACE !== 'false' && process.env.NODE_ENV !== 'production',
        maxSlowQueryRecords: parseInt(process.env.MAX_SLOW_QUERY_RECORDS || '100'),
        queryTimeoutMs: parseInt(process.env.QUERY_TIMEOUT_MS || '300000'),
        asyncSlowQueryProcessing: process.env.ASYNC_SLOW_QUERY_PROCESSING !== 'false',
        alerts: {
            slowQueryAlertThreshold: 5000,
            criticalSlowQueryThreshold: 10000,
            enableEmailAlert: false,
            enableDingTalkAlert: false,
            email: {
                recipients: ['<EMAIL>'],
                subject: '数据库慢查询告警'
            },
            dingTalk: {
                webhook: process.env.DINGTALK_WEBHOOK || '',
                secret: process.env.DINGTALK_SECRET || ''
            }
        }
    }
};
//# sourceMappingURL=database-monitor.config.js.map