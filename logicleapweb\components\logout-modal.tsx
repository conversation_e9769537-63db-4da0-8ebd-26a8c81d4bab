import { Modal} from 'antd';
import { GetNotification } from 'logic-common/dist/components/Notification';
import auth from '@/lib/auth';
import { useDispatch } from 'react-redux';
import { clearUser } from '@/lib/store';
import { LogOut } from 'lucide-react';
import { motion } from 'framer-motion';

interface LogoutModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export const LogoutModal = ({ isOpen, onClose }: LogoutModalProps) => {
  const dispatch = useDispatch();

  const handleLogout = () => {
    auth.logout();
    dispatch(clearUser());
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    localStorage.removeItem('userId');
    localStorage.removeItem('refreshToken');
    onClose();
    GetNotification().success('退出登录成功');
  };

  return (
    <Modal
      title={null}
      open={isOpen}
      onCancel={onClose}
      onOk={handleLogout}
      footer={null}
      width={400}
      className="rounded-2xl overflow-hidden"
      styles={{
        mask: {
          backdropFilter: 'blur(8px)',
          background: 'rgba(0, 0, 0, 0.1)'
        }
      }}
      centered
      closeIcon={null}
      closable={false}
    >
      <div className="p-6">
        <motion.div 
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ type: "spring", duration: 0.5 }}
          className="flex flex-col items-center"
        >
          <div className="w-20 h-20 bg-blue-50 rounded-full flex items-center justify-center mb-5 shadow-inner">
            <LogOut className="w-10 h-10 text-blue-600" />
          </div>
          <h3 className="text-2xl font-semibold mb-2 text-gray-800">退出登录</h3>
          <p className="text-gray-500 text-center mb-6">确定要退出登录吗？您的数据将会保持安全。</p>
          
          <div className="flex gap-5 w-full px-2">
            <button
              onClick={onClose}
              className="flex-1 h-11 border border-gray-200 rounded-xl font-medium text-gray-600 hover:bg-gray-50 hover:border-gray-300 transition-all duration-200"
            >
              取消
            </button>
            <button
              onClick={handleLogout}
              className="flex-1 h-11 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl font-medium hover:from-blue-600 hover:to-blue-700 transition-all duration-200 shadow-md hover:shadow-lg hover:-translate-y-0.5"
            >
              确定退出
            </button>
          </div>
        </motion.div>
      </div>
    </Modal>
  );
}; 