/* 新发布任务模态框样式 */
.new-publish-task-modal {
  max-width: 600px;
  min-width: 520px;
  height: 700px;
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 50%, #e6f3ff 100%);
}

/* 新发布任务弹窗使用外部关闭按钮样式 */

.new-publish-task-modal .modal-content-body {
  padding: 0px 40px 40px 40px;
  flex: 1;
  overflow-y: auto;
}

/* 标签页切换器 */
.tab-switcher {
  display: flex;
  justify-content: center;
  margin: 20px 0px 0px 0px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  padding: 4px;
  margin-left: 40px;
  margin-right: 40px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.tab-btn {
  flex: 1;
  padding: 12px 24px;
  border: none;
  background: transparent;
  color: #64748b;
  font-size: 14px;
  font-weight: 600;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.tab-btn.active {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.tab-btn:hover:not(.active) {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

/* 任务信息标签页 */
.task-info-tab {
  display: flex;
  flex-direction: column;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-label {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
}

.form-input {
  width: 100%;
  padding: 16px 20px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 14px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  outline: none;
}

.form-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  background: white;
}

.form-input::placeholder {
  color: #9ca3af;
}

.form-textarea {
  width: 100%;
  padding: 16px 20px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 14px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  outline: none;
  resize: vertical;
  min-height: 55px; /* 从80px减小到55px */
  font-family: inherit;
}

.form-textarea:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  background: white;
}

.form-textarea::placeholder {
  color: #9ca3af;
}

/* 自评项部分 */
.self-assessment-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.self-assessment-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.self-assessment-item .form-input {
  flex: 1;
}

.remove-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: #ef4444;
  color: white;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  font-weight: bold;
  transition: all 0.3s ease;
}

.remove-btn:hover {
  background: #dc2626;
  transform: scale(1.1);
}

.add-btn {
  padding: 12px 20px;
  border: 2px dashed #3b82f6;
  background: transparent;
  color: #3b82f6;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.add-btn:hover {
  background: rgba(59, 130, 246, 0.1);
  border-color: #1d4ed8;
  color: #1d4ed8;
}

/* 资源与附件标签页 */
.resources-tab {
  display: flex;
  flex-direction: column;
  gap: 32px;
  padding:20px 0px 80px 0px
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 16px;
}

/* 作品选择部分 */
.works-section {
  display: flex;
  flex-direction: column;
}

.works-section h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
}

.help-text {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 16px;
}

/* 作品滚动包装器 */
.works-scroll-wrapper {
  position: relative;
  width: 100%;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.8);
  border: 2px solid #e2e8f0;
  backdrop-filter: blur(10px);
  touch-action: pan-x;
  overscroll-behavior: contain;
}

.works-scroll-wrapper:hover {
  background-color: rgba(59, 130, 246, 0.02);
}

/* 水平滚动作品容器 */
.works-horizontal-scroll {
  overflow-x: auto;
  overflow-y: hidden;
  padding: 20px;
  padding-bottom: 16px;
  scrollbar-width: thin;
  scrollbar-color: rgba(59, 130, 246, 0.3) transparent;
  display: flex;
  gap: 16px;
  min-height: 180px;
  overscroll-behavior-x: contain;
  overscroll-behavior-y: none;
  scroll-behavior: smooth;
  /* 确保鼠标滚轮可以控制滚动 */
  cursor: grab;
}

.works-horizontal-scroll:active {
  cursor: grabbing;
}

.works-horizontal-scroll::-webkit-scrollbar {
  height: 6px;
}

.works-horizontal-scroll::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.works-horizontal-scroll::-webkit-scrollbar-thumb {
  background: rgba(59, 130, 246, 0.3);
  border-radius: 3px;
}

.works-horizontal-scroll::-webkit-scrollbar-thumb:hover {
  background: rgba(59, 130, 246, 0.5);
}

.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  color: #6b7280;
  font-size: 14px;
  height: 160px;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.empty-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 160px;
  color: #9ca3af;
  font-size: 16px;
  font-weight: 500;
}

.work-card {
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  padding: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  text-align: center;
  display: flex;
  flex-direction: column;
  min-width: 130px;
  max-width: 130px;
  height: 130px;
  flex-shrink: 0;
  gap: 8px;
}

.work-card:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
  transform: translateY(-2px);
}

.work-card.selected {
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.05);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

.work-image {
  width: 100%;
  height: 70px;
  border-radius: 6px;
  overflow: hidden;
  position: relative;
  flex-shrink: 0;
}

.work-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.work-placeholder {
  width: 100%;
  height: 100%;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #9ca3af;
  font-size: 12px;
}

.work-title {
  font-size: 11px;
  color: #1f2937;
  font-weight: 600;
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-top: 6px;
  text-align: center;
  padding: 0 2px;
  flex-shrink: 0;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.selected-indicator {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 20px;
  height: 20px;
  background: #3b82f6;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
}

/* 附件上传部分 */
.attachments-section {
  display: flex;
  flex-direction: column;
}

.attachments-section h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
}

.upload-area {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
}

.upload-btn {
  width: 60px;
  height: 60px;
  border: 2px dashed #3b82f6;
  border-radius: 8px;
  background: transparent;
  color: #3b82f6;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  font-size: 24px;
  font-weight: 300;
  line-height: 1;
}

.upload-btn:hover {
  background: rgba(59, 130, 246, 0.1);
  border-color: #1d4ed8;
  color: #1d4ed8;
}

.file-format-info {
  font-size: 12px;
  color: #6b7280;
}

.attachments-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.attachment-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid #e2e8f0;
  border-radius: 6px;
}

.file-name {
  font-size: 14px;
  color: #374151;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.remove-attachment-btn {
  width: 24px;
  height: 24px;
  border: none;
  background: #ef4444;
  color: white;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
  transition: all 0.3s ease;
}

.remove-attachment-btn:hover {
  background: #dc2626;
  transform: scale(1.1);
}

/* 底部按钮 */
.new-publish-task-modal .modal-footer {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  /* padding: 20px 40px 40px 40px; */
  gap: 16px;
}

.start-class-btn {
  padding: 16px 48px;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: not-allowed;
  transition: all 0.3s ease;
  min-width: 160px;
  margin-left: auto;
  /* 默认为禁用状态样式 */
  background: #f3f4f6;
  color: #d1d5db;
  box-shadow: none;
  opacity: 0.6;
  border: 1px solid #e5e7eb;
}

.start-class-btn.enabled {
  background-color: #3b82f6;
  color: white;
  box-shadow: 0 2px 6px rgba(59, 130, 246, 0.3);
  cursor: pointer;
  opacity: 1;
  border: none;
}

.start-class-btn.enabled:hover {
  background-color: #2563eb;
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.4);
  transform: translateY(-1px);
}

.start-class-btn.disabled {
  background: #f3f4f6;
  color: #d1d5db;
  cursor: not-allowed;
  box-shadow: none;
  opacity: 0.6;
  border: 1px solid #e5e7eb;
}

.start-class-btn.disabled:hover {
  background: #f3f4f6;
  color: #d1d5db;
  cursor: not-allowed;
  box-shadow: none;
  opacity: 0.6;
  transform: none;
}

/* 上一步按钮样式 */
.prev-btn {
  padding: 16px 32px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
  color: #64748b;
  min-width: 120px;
}

.prev-btn:hover {
  border-color: #3b82f6;
  color: #3b82f6;
  background: rgba(59, 130, 246, 0.05);
  transform: translateY(-1px);
}

/* 动画 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .new-publish-task-modal {
    min-width: 320px;
    max-width: 95%;
    height: 90vh;
  }

  .new-publish-task-modal .modal-content-body {
    padding: 20px 24px 32px 24px;
  }

  .tab-switcher {
    margin-left: 24px;
    margin-right: 24px;
  }

  .works-grid {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 12px;
  }

  .new-publish-task-modal .modal-footer {
    padding: 0 24px 32px 24px;
  }

  /* 移动端分页器调整 */
  .works-content {
    gap: 12px; /* 移动端减少间距 */
  }

  .pagination-btn {
    width: 44px;
    height: 44px;
  }

  .pagination-btn svg {
    width: 20px;
    height: 20px;
  }

  .pagination-info {
    flex-direction: column;
    gap: 4px;
    text-align: center;
  }

  .works-grid-wrapper {
    min-height: 150px;
  }
}

/* 针对新HTML结构的作品卡片样式 - 信息与图片在同一容器 */
.work-image-container {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 8px;
  overflow: hidden;
  background: #f3f4f6;
}

.work-image {
  position: relative;
  /* width: 100%;
  height: 100%;  */
  border-radius: 8px;
  overflow: hidden;
  background: #f3f4f6;
}

.work-cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.work-image-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
  color: white;
  padding: 8px 12px;
  font-size: 12px;
}

.work-image-label {
  font-size: 12px;
  color: white;
  font-weight: 500;
}

.work-info {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(to top, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.8));
  backdrop-filter: blur(4px);
  padding: 12px;
  border-radius: 0 0 8px 8px;
}

.work-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.work-title {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
  margin-right: 8px;
}

.work-status {
  font-size: 11px;
  padding: 3px 8px;
  border-radius: 12px;
  flex-shrink: 0;
  font-weight: 500;
  backdrop-filter: blur(4px);
}

.work-status.unpublished {
  background: rgba(254, 243, 199, 0.9);
  color: #d97706;
}

.work-status.published {
  background: rgba(220, 252, 231, 0.9);
  color: #16a34a;
}

.work-description {
  font-size: 12px;
  color: #6b7280;
  margin: 0 0 4px 0;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.work-time {
  font-size: 11px;
  color: #9ca3af;
  margin-bottom: 0;
  line-height: 1.3;
}

.work-checkbox {
  position: absolute;
  bottom: 8px;
  right: 8px;
  z-index: 10;
}

.work-checkbox input[type="checkbox"] {
  width: 18px;
  height: 18px;
  cursor: pointer;
  accent-color: #3b82f6;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 3px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 分页器样式 - 外侧布局 */
.works-content {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 16px;
}

.works-grid-wrapper {
  flex: 1;
  min-height: 200px;
}

.pagination-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  border: none;
  border-radius: 50%;
  background: #ff6b35;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 10;
  box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
  backdrop-filter: blur(8px);
  flex-shrink: 0;
}

/* 左右按钮在flex布局中无需特殊定位 */

.pagination-btn:hover:not(.disabled) {
  background: #e55a2b;
  color: white;
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(229, 90, 43, 0.4);
}

.pagination-btn:active:not(.disabled) {
  transform: scale(1.05);
}

.pagination-btn.disabled {
  opacity: 0.4;
  cursor: not-allowed;
  background: #ffb3a0;
  color: rgba(255, 255, 255, 0.7);
  box-shadow: 0 2px 8px rgba(255, 107, 53, 0.2);
  transform: scale(0.9);
}

.pagination-btn svg {
  width: 24px;
  height: 24px;
  transition: transform 0.2s ease;
}

.next-btn:hover:not(.disabled) svg {
  transform: translateX(3px);
}

.prev-btn:hover:not(.disabled) svg {
  transform: translateX(-3px);
}

/* 按钮出现动画 */
.pagination-btn {
  animation: fadeInScale 0.3s ease-out;
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 当没有作品时隐藏按钮 */
.works-content:has(.empty-placeholder) .pagination-btn {
  display: none;
}

.pagination-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background: #f8fafc;
  border-radius: 8px;
  font-size: 12px;
  color: #64748b;
  border: 1px solid #e2e8f0;
}

.page-info {
  font-weight: 500;
  color: #374151;
}

.total-info {
  color: #6b7280;
}
