// 班级信息接口
export interface ClassInfo {
  id: number;
  schoolId: number;
  grade: string;
  className: string;
  teacherId: number;
  assistantTeacherId: number;
  studentCount: number;
  inviteCode: string;
  createTime: string;
  updateTime: string;
  province: string;
  city: string;
  district: string;
  isAssistant?: boolean;
}

// 学校信息接口
export interface School {
  id: number;
  schoolName: string;
  province?: string;
  city?: string;
  district?: string;
  createTime?: string;
  updateTime?: string;
}

// 学生信息接口
export interface Student {
  userId: number;
  schoolId: number;
  classId: number;
  studentNumber: string;
  nickName: string;
  avatarUrl: string; // 修改为必需属性以兼容teacher-space
  phone?: string;
  availablePoints: number; // 修改为必需属性以兼容teacher-space
  totalPoints: number; // 修改为必需属性以兼容teacher-space
  id: number; // 兼容teacher-space的Student类型，设为必需
  currentTemplate?: {
    templateId: number;
    templateName: string;
    isOfficial: boolean;
  };
}

// 模板信息接口
export interface Template {
  templateId: number;
  templateName: string;
  templateDescription?: string;
  isOfficial: boolean;
  userId?: number;
  createTime?: string;
  updateTime?: string;
}

// 用户角色接口
export interface UserRole {
  userId: number;
  roleId: number;
}

// 班级详情组件属性
export interface ClassDetailProps {
  classInfo: ClassInfo;
  selectedSchool: School | null;
  onBack: () => void;
  onClassInfoUpdate?: (updatedClassInfo: ClassInfo) => void;
  onClassDeleted?: (deletedClassId: number) => void;
}

// 学生列表组件属性
export interface StudentListProps {
  students: Student[];
  loading: boolean;
  error: string | null;
  selectedStudent: Student | null;
  selectedStudentIds: number[];
  currentTemplate: Template | null;
  renderVersion: number;
  onStudentClick: (student: Student) => void;
  onStudentSelect: (studentId: number) => void;
  onRetry: () => void;
  onIndividualAssignBlocks: (studentId: number) => void;
  onAssignPoints: (studentId: number) => void;
}

// 学生详情面板属性
export interface StudentDetailPanelProps {
  selectedStudent: Student | null;
  currentTemplate: Template | null;
  onAssignBlocks: () => void;
  onAssignPoints: () => void;
  onUseKeyPackage: () => void;
  onRefreshTemplate: () => void;
}

// 班级操作栏属性
export interface ClassActionsProps {
  classInfo: ClassInfo;
  selectedStudentIds: number[];
  onAddStudent: () => void;
  onSelectAll: () => void;
  onBatchAction: (action: string) => void;
  onSettingsMenuItemClick: (action: string) => void;
}
