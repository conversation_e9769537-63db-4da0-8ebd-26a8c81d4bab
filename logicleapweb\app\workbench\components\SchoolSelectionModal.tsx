'use client';

import React, { useState, useEffect } from 'react';
import { X, Search, MapPin } from 'lucide-react';
import { schoolApi } from '@/lib/api/school';
import { useSelector } from 'react-redux';
import { RootState } from '@/lib/store';
import { formatSchoolAddress } from '@/lib/utils/address';
import './SchoolSelectionModal.css';

interface SchoolSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  actionType: string;
  onSchoolSelect: (school: any) => void;
}

const SchoolSelectionModal: React.FC<SchoolSelectionModalProps> = ({ isOpen, onClose, actionType, onSchoolSelect }) => {
  const [schools, setSchools] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [mounted, setMounted] = useState(false);
  const userId = useSelector((state: RootState) => state.user.userState.userId);

  // 防止水合错误
  useEffect(() => {
    setMounted(true);
  }, []);

  // 获取用户关联的学校列表
  const fetchUserSchools = async () => {
    if (!userId) {
      setError('用户未登录');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const response = await schoolApi.getUserSchools();

      if (response.data.code === 200) {
        const schoolsData = response.data.data || [];
        setSchools(schoolsData);
      } else {
        setError(response.data.message || '获取学校列表失败');
      }
    } catch (err) {
      console.error('获取学校列表失败:', err);
      setError('获取学校列表失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 当弹窗打开时获取学校数据
  useEffect(() => {
    if (isOpen && userId && mounted) {
      fetchUserSchools();
    }
  }, [isOpen, userId, mounted]);

  const handleSchoolSelect = (school: any) => {
    onSchoolSelect(school);
  };

  // 防止水合错误，在客户端挂载前不渲染
  if (!mounted || !isOpen) return null;

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="modal-wrapper">
        <button className="modal-close-btn-outside" onClick={onClose}>
          <X size={20} />
        </button>
        <div className="modal-content" onClick={(e) => e.stopPropagation()}>

        <div className="step-indicator">
          <div className="step active">
            <div className="step-number">1</div>
            <div className="step-label">选择班级</div>
          </div>
          {actionType === '发布任务' && (
            <div className="step">
              <div className="step-number">2</div>
              <div className="step-label">发布任务</div>
            </div>
          )}
          {(actionType === '分配积木' || actionType === '分配能量') && (
            <div className="step">
              <div className="step-number">2</div>
              <div className="step-label">能量和模板</div>
            </div>
          )}
          {(actionType === '快速上课' || actionType === '一键上课') && (
            <>
              <div className="step">
                <div className="step-number">2</div>
                <div className="step-label">能量和模板</div>
              </div>
              <div className="step">
                <div className="step-number">3</div>
                <div className="step-label">发布任务</div>
              </div>
            </>
          )}
        </div>

        <div className="modal-content-body">
          <h3 className="section-title">选择上课的学校</h3>

          {loading && (
            <div className="loading-container">
              <div className="loading-spinner"></div>
              <p>正在加载学校列表...</p>
            </div>
          )}

          {error && (
            <div className="error-container">
              <p className="error-message">{error}</p>
              <button className="retry-btn" onClick={fetchUserSchools}>
                重试
              </button>
            </div>
          )}

          {!loading && !error && schools.length === 0 && (
            <div className="empty-container">
              <p>您还没有关联任何学校</p>
              <p className="empty-hint">请先在个人设置中绑定学校</p>
            </div>
          )}

          {!loading && !error && schools.length > 0 && (
            <div className="schools-grid">
              {schools.map((school) => (
                <div
                  key={school.id}
                  className="school-card"
                  onClick={() => handleSchoolSelect(school)}
                >
                  <div className="school-card-name">{school.schoolName}</div>
                  <div className="school-card-location">
                    <MapPin size={14} />
                    {formatSchoolAddress(school.province, school.city, school.district)}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
      </div>
    </div>
  );
};

export default SchoolSelectionModal;
