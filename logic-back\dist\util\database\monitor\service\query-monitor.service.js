"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var QueryMonitorService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.QueryMonitorService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
let QueryMonitorService = QueryMonitorService_1 = class QueryMonitorService {
    configService;
    logger = new common_1.Logger(QueryMonitorService_1.name);
    slowQueryThreshold;
    enableSlowQueryLogging;
    enableQueryMetrics;
    enableStackTrace;
    lightweightMode;
    samplingRate;
    asyncSlowQueryProcessing;
    enableDatabaseMonitoring;
    slowQueryRecords = [];
    queryMetrics;
    maxSlowQueryRecords;
    queryCounter = 0;
    constructor(configService) {
        this.configService = configService;
        this.enableDatabaseMonitoring = this.configService.get('database.enableDatabaseMonitoring', true);
        this.lightweightMode = this.configService.get('database.lightweightMode', false);
        this.slowQueryThreshold = this.configService.get('database.slowQueryThreshold', 1000);
        this.enableSlowQueryLogging = this.configService.get('database.enableSlowQueryLogging', true);
        this.enableQueryMetrics = this.configService.get('database.enableQueryMetrics', true);
        this.enableStackTrace = this.configService.get('database.enableStackTrace', true);
        this.samplingRate = this.configService.get('database.samplingRate', 100);
        this.asyncSlowQueryProcessing = this.configService.get('database.asyncSlowQueryProcessing', true);
        this.maxSlowQueryRecords = this.configService.get('database.maxSlowQueryRecords', 100);
        this.resetMetrics();
        if (this.enableDatabaseMonitoring) {
            const mode = this.lightweightMode ? '轻量级模式' : '完整模式';
            const sampling = this.samplingRate < 100 ? `采样率: ${this.samplingRate}%` : '全量监控';
            this.logger.log(`数据库查询监控已启动 [${mode}] - 慢查询阈值: ${this.slowQueryThreshold}ms, ${sampling}`);
        }
        else {
            this.logger.log('数据库查询监控已禁用');
        }
    }
    recordQuery(query, executionTime, parameters, context) {
        if (!this.enableDatabaseMonitoring) {
            return;
        }
        this.queryCounter++;
        if (this.samplingRate < 100) {
            const shouldSample = (this.queryCounter % Math.floor(100 / this.samplingRate)) === 0;
            if (!shouldSample) {
                return;
            }
        }
        if (this.enableQueryMetrics) {
            this.updateMetrics(executionTime);
        }
        if (this.enableSlowQueryLogging) {
            console.log("当前查询为慢查询");
            if (this.asyncSlowQueryProcessing) {
                setImmediate(async () => {
                    await this.recordSlowQuery(query, executionTime, parameters, context);
                });
            }
            else {
                this.recordSlowQuery(query, executionTime, parameters, context).catch(error => {
                    this.logger.error(`记录慢查询失败: ${error.message}`);
                });
            }
        }
    }
    async recordSlowQuery(query, executionTime, parameters, context) {
        const slowQueryRecord = {
            query: this.lightweightMode ? this.sanitizeQueryLightweight(query) : this.sanitizeQuery(query),
            parameters: this.lightweightMode ? undefined : this.sanitizeParameters(parameters),
            executionTime,
            timestamp: new Date(),
            stack: this.enableStackTrace && !this.lightweightMode ? this.captureStack() : undefined,
            context: this.lightweightMode ? undefined : context
        };
        this.slowQueryRecords.push(slowQueryRecord);
        if (this.slowQueryRecords.length > this.maxSlowQueryRecords) {
            this.slowQueryRecords = this.slowQueryRecords.slice(-this.maxSlowQueryRecords);
        }
        this.logSlowQuery(slowQueryRecord);
        this.triggerSlowQueryAlert(slowQueryRecord);
    }
    updateMetrics(executionTime) {
        this.queryMetrics.totalQueries++;
        if (executionTime >= this.slowQueryThreshold) {
            this.queryMetrics.slowQueries++;
        }
        if (this.queryMetrics.totalQueries === 1) {
            this.queryMetrics.averageExecutionTime = executionTime;
            this.queryMetrics.maxExecutionTime = executionTime;
            this.queryMetrics.minExecutionTime = executionTime;
        }
        else {
            this.queryMetrics.averageExecutionTime =
                (this.queryMetrics.averageExecutionTime * (this.queryMetrics.totalQueries - 1) + executionTime) /
                    this.queryMetrics.totalQueries;
            this.queryMetrics.maxExecutionTime = Math.max(this.queryMetrics.maxExecutionTime, executionTime);
            this.queryMetrics.minExecutionTime = Math.min(this.queryMetrics.minExecutionTime, executionTime);
        }
    }
    logSlowQuery(record) {
        this.logger.warn(`🐌 慢查询检测 [${record.executionTime}ms] ${record.context || ''}`, {
            query: record.query,
            executionTime: record.executionTime,
            parameters: record.parameters,
            timestamp: record.timestamp,
            context: record.context
        });
    }
    triggerSlowQueryAlert(record) {
        if (record.executionTime >= 10000) {
            this.logger.error(`🚨 严重慢查询告警 [${record.executionTime}ms]`, {
                query: record.query,
                executionTime: record.executionTime,
                context: record.context
            });
        }
        else if (record.executionTime >= 5000) {
            this.logger.warn(`⚠️ 慢查询告警 [${record.executionTime}ms]`, {
                query: record.query,
                executionTime: record.executionTime,
                context: record.context
            });
        }
    }
    sanitizeQuery(query) {
        if (!query)
            return '';
        const maxLength = 1000;
        if (query.length > maxLength) {
            return query.substring(0, maxLength) + '...';
        }
        return query;
    }
    sanitizeQueryLightweight(query) {
        if (!query)
            return '';
        const maxLength = 200;
        if (query.length > maxLength) {
            return query.substring(0, maxLength) + '...';
        }
        return query;
    }
    sanitizeParameters(parameters) {
        if (!parameters)
            return [];
        return parameters.map(param => {
            if (typeof param === 'string' && param.length > 100) {
                return param.substring(0, 100) + '...';
            }
            return param;
        });
    }
    captureStack() {
        const stack = new Error().stack;
        if (!stack)
            return '';
        const lines = stack.split('\n').slice(3, 8);
        return lines.join('\n');
    }
    getSlowQueryRecords(limit) {
        const records = [...this.slowQueryRecords].reverse();
        return limit ? records.slice(0, limit) : records;
    }
    getQueryMetrics() {
        return { ...this.queryMetrics };
    }
    resetMetrics() {
        this.queryMetrics = {
            totalQueries: 0,
            slowQueries: 0,
            averageExecutionTime: 0,
            maxExecutionTime: 0,
            minExecutionTime: 0,
            lastResetTime: new Date()
        };
    }
    clearSlowQueryRecords() {
        this.slowQueryRecords = [];
        this.logger.log('慢查询记录已清空');
    }
    getConfig() {
        return {
            enableDatabaseMonitoring: this.enableDatabaseMonitoring,
            lightweightMode: this.lightweightMode,
            slowQueryThreshold: this.slowQueryThreshold,
            enableSlowQueryLogging: this.enableSlowQueryLogging,
            enableQueryMetrics: this.enableQueryMetrics,
            enableStackTrace: this.enableStackTrace,
            samplingRate: this.samplingRate,
            asyncSlowQueryProcessing: this.asyncSlowQueryProcessing,
            maxSlowQueryRecords: this.maxSlowQueryRecords,
            queryCounter: this.queryCounter
        };
    }
    updateConfig(config) {
        if (config.enableDatabaseMonitoring !== undefined) {
            this.enableDatabaseMonitoring = config.enableDatabaseMonitoring;
            this.logger.log(`数据库监控已${config.enableDatabaseMonitoring ? '启用' : '禁用'}`);
        }
        if (config.lightweightMode !== undefined) {
            this.lightweightMode = config.lightweightMode;
            this.logger.log(`轻量级模式已${config.lightweightMode ? '启用' : '禁用'}`);
        }
        if (config.slowQueryThreshold !== undefined) {
            this.slowQueryThreshold = config.slowQueryThreshold;
        }
        if (config.enableSlowQueryLogging !== undefined) {
            this.enableSlowQueryLogging = config.enableSlowQueryLogging;
        }
        if (config.enableQueryMetrics !== undefined) {
            this.enableQueryMetrics = config.enableQueryMetrics;
        }
        if (config.enableStackTrace !== undefined) {
            this.enableStackTrace = config.enableStackTrace;
        }
        if (config.samplingRate !== undefined) {
            this.samplingRate = Math.max(1, Math.min(100, config.samplingRate));
        }
        if (config.asyncSlowQueryProcessing !== undefined) {
            this.asyncSlowQueryProcessing = config.asyncSlowQueryProcessing;
        }
        if (config.maxSlowQueryRecords !== undefined) {
            this.maxSlowQueryRecords = config.maxSlowQueryRecords;
        }
        this.logger.log('查询监控配置已更新', config);
    }
};
exports.QueryMonitorService = QueryMonitorService;
exports.QueryMonitorService = QueryMonitorService = QueryMonitorService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], QueryMonitorService);
//# sourceMappingURL=query-monitor.service.js.map