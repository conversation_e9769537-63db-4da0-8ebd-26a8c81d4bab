"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/workbench/page",{

/***/ "(app-pages-browser)/./app/workbench/components/CourseListEditModal.tsx":
/*!**********************************************************!*\
  !*** ./app/workbench/components/CourseListEditModal.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FileText,Plus,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FileText,Plus,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FileText,Plus,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FileText,Plus,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FileText,Plus,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _lib_api_course__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api/course */ \"(app-pages-browser)/./lib/api/course.ts\");\n/* harmony import */ var _lib_api_upload__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api/upload */ \"(app-pages-browser)/./lib/api/upload.ts\");\n/* harmony import */ var _barrel_optimize_names_Select_antd__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Select!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/select/index.js\");\n/* harmony import */ var _CourseListEditModal_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./CourseListEditModal.css */ \"(app-pages-browser)/./app/workbench/components/CourseListEditModal.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// API调用函数 - 获取系列下的所有课程（包含未发布和已发布）\nconst fetchCourseList = async (seriesId)=>{\n    console.log(\"\\uD83D\\uDD0D 获取系列课程列表，seriesId:\", seriesId);\n    console.log(\"\\uD83D\\uDD17 调用API: GET /api/v1/course-management/series/{seriesId}/courses\");\n    // 使用课程管理API获取所有状态的课程\n    const response = await _lib_api_course__WEBPACK_IMPORTED_MODULE_2__.courseApi.getManagementSeriesCourses(seriesId, {\n        page: 1,\n        pageSize: 100\n    });\n    console.log(\"\\uD83D\\uDCE1 API响应:\", response);\n    return response.data;\n};\n// 获取课程详情\nconst fetchCourseDetail = async (seriesId, courseId)=>{\n    return await _lib_api_course__WEBPACK_IMPORTED_MODULE_2__.courseApi.getCourseMarketplaceDetail(seriesId, courseId);\n};\n// 获取课程标签\nconst fetchCourseTags = async ()=>{\n    try {\n        console.log(\"\\uD83D\\uDD0D 开始调用 courseApi.getCourseTags\");\n        const result = await _lib_api_course__WEBPACK_IMPORTED_MODULE_2__.courseApi.getCourseTags({\n            page: 1,\n            pageSize: 100,\n            status: 1 // 只获取启用的标签\n        });\n        console.log(\"\\uD83D\\uDD0D courseApi.getCourseTags 返回结果:\", result);\n        return result;\n    } catch (error) {\n        console.error(\"\\uD83D\\uDD0D courseApi.getCourseTags 调用失败:\", error);\n        throw error;\n    }\n};\nconst CourseListEditModal = (param)=>{\n    let { isVisible, onClose, onSave, seriesTitle, seriesCoverImage, seriesId = 123 // 默认值，实际使用时应该传入真实的seriesId\n     } = param;\n    var _getSelectedCourse, _getSelectedCourse1, _getSelectedCourse2, _getSelectedCourse3, _courseDetail_contentConfig_video, _courseDetail_contentConfig, _courseDetail_contentConfig_document, _courseDetail_contentConfig1, _courseList_find, _courseList_find1;\n    _s();\n    const [courseList, setCourseList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [rightPanelType, setRightPanelType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"none\");\n    const [selectedCourseId, setSelectedCourseId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [uploadingFiles, setUploadingFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [isPublishing, setIsPublishing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isCreating, setIsCreating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingTitle, setEditingTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(seriesTitle);\n    const [courseGoals, setCourseGoals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [courseObjectives, setCourseObjectives] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [projectMembers, setProjectMembers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // 课程标签相关状态\n    const [courseTags, setCourseTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedTags, setSelectedTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [tagsLoading, setTagsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 课程详细编辑状态\n    const [courseDetail, setCourseDetail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: \"\",\n        description: \"\",\n        coverImage: \"\",\n        videoUrl: \"\",\n        videoName: \"\",\n        isVideoEnabled: false,\n        attachmentUrl: \"\",\n        attachmentName: \"\",\n        isAttachmentEnabled: false,\n        teachingMaterials: [],\n        // 支持teachingInfo结构\n        teachingInfo: [],\n        // 支持contentConfig结构\n        contentConfig: {\n            hasVideo: 0,\n            hasDocument: 0,\n            hasAudio: 0,\n            video: {\n                url: \"\",\n                name: \"\"\n            },\n            document: {\n                url: \"\",\n                name: \"\"\n            },\n            audio: {\n                url: \"\",\n                name: \"\"\n            }\n        },\n        courseContent: {\n            topic: \"\",\n            content: \"\"\n        },\n        isOneKeyOpen: false,\n        isDistributionEnabled: false,\n        distributionReward: \"\",\n        selectedTemplate: \"\",\n        isDistributionWater: false,\n        requiredEnergy: \"\",\n        energyAmount: \"\",\n        isDistributionLimit: false,\n        distributionConditions: {\n            inviteCount: \"\",\n            taskCount: \"\",\n            experience: \"\"\n        },\n        isDistributionTime: false,\n        distributionTimeConditions: {\n            startTime: \"\",\n            endTime: \"\"\n        },\n        distributionMaterials: [],\n        // 任务配置相关状态\n        taskConfig: {\n            taskName: \"\",\n            taskDuration: \"\",\n            taskDescription: \"\",\n            selfAssessmentItems: [\n                \"\"\n            ],\n            referenceWorks: [],\n            referenceResources: []\n        }\n    });\n    // 获取课程列表数据\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isVisible && seriesId) {\n            // 检查用户登录状态\n            const token = localStorage.getItem(\"token\");\n            console.log(\"\\uD83D\\uDD10 检查登录状态，token存在:\", !!token);\n            console.log(\"\\uD83D\\uDD0D seriesId:\", seriesId);\n            if (!token) {\n                console.error(\"❌ 用户未登录，无法获取课程列表\");\n                // 设置空列表，显示空状态\n                setCourseList([]);\n                setLoading(false);\n                return;\n            }\n            loadCourseList();\n            loadCourseTags();\n        }\n    }, [\n        isVisible,\n        seriesId\n    ]);\n    const loadCourseList = async ()=>{\n        try {\n            setLoading(true);\n            console.log(\"\\uD83D\\uDD0D 开始加载课程列表，seriesId:\", seriesId);\n            const response = await fetchCourseList(seriesId);\n            console.log(\"\\uD83D\\uDCE1 API响应:\", response);\n            if (response.code === 200) {\n                console.log(\"✅ 课程列表数据:\", response.data);\n                const courses = response.data.list || [];\n                console.log(\"✅ 解析的课程数组:\", courses);\n                console.log(\"\\uD83D\\uDCCA 课程ID详情:\", courses.map((c)=>({\n                        id: c.id,\n                        type: typeof c.id,\n                        title: c.title,\n                        status: c.status\n                    })));\n                console.log(\"\\uD83D\\uDCCA 课程状态统计:\", {\n                    total: courses.length,\n                    draft: courses.filter((c)=>c.status === 0).length,\n                    published: courses.filter((c)=>c.status === 1).length\n                });\n                setCourseList(courses);\n            } else {\n                console.error(\"❌ API返回错误:\", response);\n                setCourseList([]);\n            }\n        } catch (error) {\n            var _error_response, _error_response1, _error_response2;\n            console.error(\"❌ 加载课程列表失败:\", error);\n            // 检查是否是认证错误\n            if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 401) {\n                console.error(\"\\uD83D\\uDD10 认证失败，用户未登录或token已过期\");\n            } else if (((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status) === 403) {\n                console.error(\"\\uD83D\\uDEAB 权限不足，无法访问该系列课程\");\n            } else if (((_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.status) === 404) {\n                console.error(\"\\uD83D\\uDCED 系列课程不存在，seriesId:\", seriesId);\n            } else {\n                console.error(\"\\uD83D\\uDD27 其他错误:\", error.message);\n            }\n            setCourseList([]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 加载课程标签\n    const loadCourseTags = async ()=>{\n        try {\n            setTagsLoading(true);\n            console.log(\"\\uD83C\\uDFF7️ 开始加载课程标签\");\n            const response = await fetchCourseTags();\n            console.log(\"\\uD83D\\uDCE1 标签API完整响应:\", response);\n            // 检查响应结构\n            if (response && response.data) {\n                console.log(\"\\uD83D\\uDCCA 响应数据:\", response.data);\n                let tags = [];\n                // 处理标准的API响应格式 (response.data.list) - 根据实际API响应\n                if (response.data.list && Array.isArray(response.data.list)) {\n                    tags = response.data.list;\n                    console.log(\"✅ 从 data.list 解析到标签:\", tags.length, \"个\");\n                } else if (Array.isArray(response.data)) {\n                    tags = response.data;\n                    console.log(\"✅ 从 data 数组解析到标签:\", tags.length, \"个\");\n                } else if (response.data.data && response.data.data.list && Array.isArray(response.data.data.list)) {\n                    tags = response.data.data.list;\n                    console.log(\"✅ 从 data.data.list 解析到标签:\", tags.length, \"个\");\n                }\n                // 验证标签数据格式\n                console.log(\"\\uD83D\\uDD0D 原始标签数据:\", tags);\n                console.log(\"\\uD83D\\uDD0D 标签数据类型检查:\");\n                tags.forEach((tag, index)=>{\n                    var _tag_name;\n                    console.log(\"标签\".concat(index, \":\"), {\n                        tag,\n                        hasTag: !!tag,\n                        idType: typeof (tag === null || tag === void 0 ? void 0 : tag.id),\n                        nameType: typeof (tag === null || tag === void 0 ? void 0 : tag.name),\n                        nameValue: tag === null || tag === void 0 ? void 0 : tag.name,\n                        nameNotEmpty: (tag === null || tag === void 0 ? void 0 : (_tag_name = tag.name) === null || _tag_name === void 0 ? void 0 : _tag_name.trim()) !== \"\"\n                    });\n                });\n                const validTags = tags.filter((tag)=>{\n                    const isValid = tag && typeof tag.id === \"number\" && typeof tag.name === \"string\" && tag.name.trim() !== \"\";\n                    if (!isValid) {\n                        console.log(\"❌ 无效标签:\", tag, {\n                            hasTag: !!tag,\n                            idType: typeof (tag === null || tag === void 0 ? void 0 : tag.id),\n                            nameType: typeof (tag === null || tag === void 0 ? void 0 : tag.name),\n                            nameValue: tag === null || tag === void 0 ? void 0 : tag.name\n                        });\n                    }\n                    return isValid;\n                });\n                console.log(\"✅ 有效标签数量:\", validTags.length);\n                console.log(\"✅ 有效标签详情:\", validTags);\n                if (validTags.length > 0) {\n                    setCourseTags(validTags);\n                    console.log(\"✅ 成功设置真实标签数据\");\n                    return;\n                } else {\n                    console.warn(\"⚠️ 没有有效的标签数据\");\n                }\n            } else {\n                console.warn(\"⚠️ API响应格式不正确:\", response);\n            }\n            // 如果没有真实数据，设置空数组\n            console.log(\"\\uD83D\\uDCED 没有标签数据，设置空数组\");\n            setCourseTags([]);\n        } catch (error) {\n            var _error_response, _error_response1, _error_response2;\n            console.error(\"❌ 加载课程标签失败:\", error);\n            console.error(\"❌ 错误详情:\", {\n                message: error.message,\n                status: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status,\n                statusText: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.statusText,\n                data: (_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.data\n            });\n            // 发生错误时设置空数组\n            setCourseTags([]);\n        } finally{\n            setTagsLoading(false);\n        }\n    };\n    // 添加新课程\n    const addNewCourse = ()=>{\n        const newCourse = {\n            id: Date.now(),\n            seriesId: seriesId,\n            title: \"第\".concat(courseList.length + 1, \"课 - 新课时\"),\n            description: \"\",\n            coverImage: \"\",\n            orderIndex: courseList.length + 1,\n            status: 0,\n            statusLabel: \"草稿\",\n            hasVideo: 0,\n            hasDocument: 0,\n            hasAudio: 0,\n            videoDuration: 0,\n            videoDurationLabel: \"\",\n            videoName: \"\",\n            firstTeachingTitle: \"\",\n            resourcesCount: 0,\n            createdAt: new Date().toISOString(),\n            updatedAt: new Date().toISOString()\n        };\n        setCourseList([\n            ...courseList,\n            newCourse\n        ]);\n        // 自动选中新添加的课程\n        showCoursePanel(newCourse.id);\n    };\n    // 删除课程\n    const deleteCourse = (id)=>{\n        setCourseList(courseList.filter((course)=>course.id !== id));\n    };\n    // 更新课程标题\n    const updateCourseTitle = (id, newTitle)=>{\n        setCourseList(courseList.map((course)=>course.id === id ? {\n                ...course,\n                title: newTitle\n            } : course));\n    };\n    // 处理课程封面上传\n    const handleCoverUpload = async (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (file) {\n            // 检查文件类型\n            const allowedTypes = [\n                \"image/jpeg\",\n                \"image/jpg\",\n                \"image/png\",\n                \"image/gif\"\n            ];\n            if (!allowedTypes.includes(file.type)) {\n                alert(\"请选择 JPG、PNG 或 GIF 格式的图片文件\");\n                return;\n            }\n            // 检查文件大小 (10MB)\n            if (file.size > 10 * 1024 * 1024) {\n                alert(\"文件大小不能超过 10MB\");\n                return;\n            }\n            try {\n                console.log(\"\\uD83D\\uDCE4 开始上传课程封面:\", file.name);\n                // 添加到上传中的文件列表\n                setUploadingFiles((prev)=>new Set(prev).add(\"cover\"));\n                // 先显示预览图片\n                const previewUrl = URL.createObjectURL(file);\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        coverImage: previewUrl\n                    }));\n                // 上传到OSS\n                const imageUrl = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_3__.uploadApi.uploadToOss(file);\n                console.log(\"✅ 课程封面上传成功:\", imageUrl);\n                // 更新课程详情中的封面为真实URL\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        coverImage: imageUrl\n                    }));\n                // 同时更新课程列表中的封面\n                if (selectedCourseId) {\n                    setCourseList((prev)=>prev.map((course)=>course.id === selectedCourseId ? {\n                                ...course,\n                                coverImage: imageUrl\n                            } : course));\n                }\n                alert(\"课程封面上传成功！\");\n            } catch (error) {\n                console.error(\"❌ 课程封面上传失败:\", error);\n                alert(\"课程封面上传失败，请重试\");\n                // 上传失败时清除预览图片\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        coverImage: \"\"\n                    }));\n            } finally{\n                // 从上传中的文件列表移除\n                setUploadingFiles((prev)=>{\n                    const newSet = new Set(prev);\n                    newSet.delete(\"cover\");\n                    return newSet;\n                });\n            }\n        }\n    };\n    // 处理视频上传\n    const handleVideoUpload = async (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (file) {\n            // 检查文件类型\n            const allowedTypes = [\n                \"video/mp4\",\n                \"video/avi\",\n                \"video/mov\",\n                \"video/wmv\",\n                \"video/flv\"\n            ];\n            if (!allowedTypes.includes(file.type)) {\n                alert(\"请选择 MP4、AVI、MOV、WMV 或 FLV 格式的视频文件\");\n                return;\n            }\n            // 检查文件大小 (100MB)\n            if (file.size > 100 * 1024 * 1024) {\n                alert(\"视频文件大小不能超过 100MB\");\n                return;\n            }\n            try {\n                console.log(\"\\uD83D\\uDCE4 开始上传课程视频:\", file.name);\n                // 添加到上传中的文件列表\n                setUploadingFiles((prev)=>new Set(prev).add(\"video\"));\n                // 先显示预览视频\n                const previewUrl = URL.createObjectURL(file);\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        contentConfig: {\n                            ...prev.contentConfig,\n                            video: {\n                                url: previewUrl,\n                                name: file.name\n                            }\n                        }\n                    }));\n                // 上传到OSS\n                const videoUrl = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_3__.uploadApi.uploadToOss(file);\n                console.log(\"✅ 课程视频上传成功:\", videoUrl);\n                // 更新课程详情中的视频信息为真实URL\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        contentConfig: {\n                            ...prev.contentConfig,\n                            video: {\n                                url: videoUrl,\n                                name: file.name\n                            }\n                        }\n                    }));\n                // 同时更新课程列表中的视频信息\n                if (selectedCourseId) {\n                    setCourseList((prev)=>prev.map((course)=>course.id === selectedCourseId ? {\n                                ...course,\n                                contentConfig: {\n                                    ...course.contentConfig,\n                                    video: {\n                                        url: videoUrl,\n                                        name: file.name\n                                    }\n                                }\n                            } : course));\n                }\n                alert(\"课程视频上传成功！\");\n            } catch (error) {\n                console.error(\"❌ 课程视频上传失败:\", error);\n                alert(\"课程视频上传失败，请重试\");\n                // 上传失败时清除视频信息\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        contentConfig: {\n                            ...prev.contentConfig,\n                            video: {\n                                url: \"\",\n                                name: \"\"\n                            }\n                        }\n                    }));\n            } finally{\n                // 从上传中的文件列表移除\n                setUploadingFiles((prev)=>{\n                    const newSet = new Set(prev);\n                    newSet.delete(\"video\");\n                    return newSet;\n                });\n            }\n        }\n    };\n    // 触发视频文件选择\n    const triggerVideoUpload = ()=>{\n        const input = document.createElement(\"input\");\n        input.type = \"file\";\n        input.accept = \"video/mp4,video/avi,video/mov,video/wmv,video/flv\";\n        input.onchange = (e)=>handleVideoUpload(e);\n        input.click();\n    };\n    // 处理附件上传\n    const handleAttachmentUpload = async (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (file) {\n            // 检查文件类型\n            const allowedTypes = [\n                \"application/pdf\",\n                \"application/msword\",\n                \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\",\n                \"application/vnd.ms-excel\",\n                \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\",\n                \"application/vnd.ms-powerpoint\",\n                \"application/vnd.openxmlformats-officedocument.presentationml.presentation\",\n                \"text/plain\",\n                \"image/jpeg\",\n                \"image/png\",\n                \"image/gif\"\n            ];\n            if (!allowedTypes.includes(file.type)) {\n                alert(\"请选择支持的文件格式：PDF、DOC、DOCX、XLS、XLSX、PPT、PPTX、TXT、JPG、PNG、GIF\");\n                return;\n            }\n            // 检查文件大小 (10MB)\n            if (file.size > 10 * 1024 * 1024) {\n                alert(\"文件大小不能超过 10MB\");\n                return;\n            }\n            try {\n                console.log(\"\\uD83D\\uDCE4 开始上传课程附件:\", file.name);\n                // 添加到上传中的文件列表\n                setUploadingFiles((prev)=>new Set(prev).add(\"document\"));\n                // 上传到OSS\n                const documentUrl = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_3__.uploadApi.uploadToOss(file);\n                console.log(\"✅ 课程附件上传成功:\", documentUrl);\n                // 更新课程详情中的附件信息\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        contentConfig: {\n                            ...prev.contentConfig,\n                            hasDocument: 1,\n                            document: {\n                                url: documentUrl,\n                                name: file.name\n                            }\n                        }\n                    }));\n                alert(\"课程附件上传成功！\");\n            } catch (error) {\n                console.error(\"❌ 课程附件上传失败:\", error);\n                alert(\"课程附件上传失败，请重试\");\n            } finally{\n                // 从上传中的文件列表移除\n                setUploadingFiles((prev)=>{\n                    const newSet = new Set(prev);\n                    newSet.delete(\"document\");\n                    return newSet;\n                });\n            }\n        }\n    };\n    // 触发附件文件选择\n    const triggerAttachmentUpload = ()=>{\n        const input = document.createElement(\"input\");\n        input.type = \"file\";\n        input.accept = \".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.jpg,.png,.gif\";\n        input.onchange = (e)=>handleAttachmentUpload(e);\n        input.click();\n    };\n    // 处理教学附件上传\n    const handleTeachingMaterialUpload = async (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (file) {\n            // 检查文件类型\n            const allowedTypes = [\n                \"application/pdf\",\n                \"application/msword\",\n                \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\",\n                \"application/vnd.ms-excel\",\n                \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\",\n                \"application/vnd.ms-powerpoint\",\n                \"application/vnd.openxmlformats-officedocument.presentationml.presentation\",\n                \"text/plain\",\n                \"image/jpeg\",\n                \"image/png\",\n                \"image/gif\"\n            ];\n            if (!allowedTypes.includes(file.type)) {\n                alert(\"请选择支持的文件格式：PDF、DOC、DOCX、XLS、XLSX、PPT、PPTX、TXT、JPG、PNG、GIF\");\n                return;\n            }\n            // 检查文件大小 (10MB)\n            if (file.size > 10 * 1024 * 1024) {\n                alert(\"文件大小不能超过 10MB\");\n                return;\n            }\n            try {\n                console.log(\"\\uD83D\\uDCE4 开始上传教学材料:\", file.name);\n                // 添加到上传中的文件列表\n                setUploadingFiles((prev)=>new Set(prev).add(\"teaching-\".concat(Date.now())));\n                // 上传到OSS\n                const materialUrl = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_3__.uploadApi.uploadToOss(file);\n                console.log(\"✅ 教学材料上传成功:\", materialUrl);\n                // 添加到教学附件列表\n                const newMaterial = {\n                    type: file.type,\n                    name: file.name,\n                    url: materialUrl\n                };\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        teachingMaterials: [\n                            ...prev.teachingMaterials,\n                            newMaterial\n                        ]\n                    }));\n                alert(\"教学材料上传成功！\");\n            } catch (error) {\n                console.error(\"❌ 教学材料上传失败:\", error);\n                alert(\"教学材料上传失败，请重试\");\n            } finally{\n                // 从上传中的文件列表移除\n                setUploadingFiles((prev)=>{\n                    const newSet = new Set(prev);\n                    // 移除所有teaching-开头的项目\n                    Array.from(newSet).forEach((item)=>{\n                        if (item.startsWith(\"teaching-\")) {\n                            newSet.delete(item);\n                        }\n                    });\n                    return newSet;\n                });\n            }\n        }\n    };\n    // 触发教学附件文件选择\n    const triggerTeachingMaterialUpload = ()=>{\n        const input = document.createElement(\"input\");\n        input.type = \"file\";\n        input.accept = \".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.jpg,.png,.gif\";\n        input.onchange = (e)=>handleTeachingMaterialUpload(e);\n        input.click();\n    };\n    // 删除教学附件\n    const removeTeachingMaterial = (index)=>{\n        setCourseDetail((prev)=>({\n                ...prev,\n                teachingMaterials: prev.teachingMaterials.filter((_, i)=>i !== index)\n            }));\n    };\n    // 课程内容管理函数\n    const addTeachingInfoItem = ()=>{\n        setCourseDetail((prev)=>({\n                ...prev,\n                teachingInfo: [\n                    ...prev.teachingInfo,\n                    {\n                        title: \"\",\n                        content: \"\"\n                    }\n                ]\n            }));\n    };\n    const removeTeachingInfoItem = (index)=>{\n        setCourseDetail((prev)=>({\n                ...prev,\n                teachingInfo: prev.teachingInfo.filter((_, i)=>i !== index)\n            }));\n    };\n    const updateTeachingInfoTitle = (index, title)=>{\n        setCourseDetail((prev)=>{\n            const newTeachingInfo = [\n                ...prev.teachingInfo\n            ];\n            newTeachingInfo[index] = {\n                ...newTeachingInfo[index],\n                title\n            };\n            return {\n                ...prev,\n                teachingInfo: newTeachingInfo\n            };\n        });\n    };\n    const updateTeachingInfoContent = (index, content)=>{\n        setCourseDetail((prev)=>{\n            const newTeachingInfo = [\n                ...prev.teachingInfo\n            ];\n            newTeachingInfo[index] = {\n                ...newTeachingInfo[index],\n                content\n            };\n            return {\n                ...prev,\n                teachingInfo: newTeachingInfo\n            };\n        });\n    };\n    // 将UI格式的teachingInfo转换为API格式\n    const convertTeachingInfoForAPI = (teachingInfo)=>{\n        return teachingInfo.map((info)=>({\n                title: info.title,\n                content: info.content.split(\"\\n\").filter((line)=>line.trim()) // 按换行分割并过滤空行\n            }));\n    };\n    // 检查是否有未上传完成的文件（blob URL）\n    const checkForBlobUrls = (course)=>{\n        var _course_contentConfig_video, _course_contentConfig, _course_contentConfig_document, _course_contentConfig1;\n        const issues = [];\n        // 检查封面图片\n        if (course.coverImage && course.coverImage.startsWith(\"blob:\")) {\n            issues.push(\"课程封面图片\");\n        }\n        // 检查视频文件\n        if (((_course_contentConfig = course.contentConfig) === null || _course_contentConfig === void 0 ? void 0 : (_course_contentConfig_video = _course_contentConfig.video) === null || _course_contentConfig_video === void 0 ? void 0 : _course_contentConfig_video.url) && course.contentConfig.video.url.startsWith(\"blob:\")) {\n            issues.push(\"课程视频\");\n        }\n        // 检查文档附件\n        if (((_course_contentConfig1 = course.contentConfig) === null || _course_contentConfig1 === void 0 ? void 0 : (_course_contentConfig_document = _course_contentConfig1.document) === null || _course_contentConfig_document === void 0 ? void 0 : _course_contentConfig_document.url) && course.contentConfig.document.url.startsWith(\"blob:\")) {\n            issues.push(\"课程文档\");\n        }\n        // 检查教学材料\n        if (course.additionalResources) {\n            course.additionalResources.forEach((resource, index)=>{\n                if (resource.url && resource.url.startsWith(\"blob:\")) {\n                    issues.push(\"教学材料\".concat(index + 1));\n                }\n            });\n        }\n        return issues;\n    };\n    // 保存单个课程到后端\n    const saveCourse = async (course)=>{\n        try {\n            var _course_contentConfig_video, _course_contentConfig, _course_contentConfig_document, _course_contentConfig1;\n            // 验证必要的数据\n            if (!course.title || course.title.trim() === \"\") {\n                throw new Error(\"课程标题不能为空\");\n            }\n            // 检查是否有未上传完成的文件\n            const blobIssues = checkForBlobUrls(course);\n            if (blobIssues.length > 0) {\n                throw new Error(\"以下文件尚未上传完成，请等待上传完成后再保存：\".concat(blobIssues.join(\"、\")));\n            }\n            // 准备课程数据\n            const courseData = {\n                seriesId: seriesId,\n                title: course.title,\n                description: course.description || \"\",\n                coverImage: course.coverImage || \"\",\n                hasVideo: ((_course_contentConfig = course.contentConfig) === null || _course_contentConfig === void 0 ? void 0 : (_course_contentConfig_video = _course_contentConfig.video) === null || _course_contentConfig_video === void 0 ? void 0 : _course_contentConfig_video.url) ? 1 : 0,\n                hasDocument: ((_course_contentConfig1 = course.contentConfig) === null || _course_contentConfig1 === void 0 ? void 0 : (_course_contentConfig_document = _course_contentConfig1.document) === null || _course_contentConfig_document === void 0 ? void 0 : _course_contentConfig_document.url) ? 1 : 0,\n                hasAudio: 0,\n                videoDuration: 0,\n                contentConfig: course.contentConfig || {},\n                teachingInfo: convertTeachingInfoForAPI(course.teachingInfo || []),\n                additionalResources: course.additionalResources || [],\n                orderIndex: course.orderIndex || 1\n            };\n            console.log(\"\\uD83D\\uDCBE 准备保存课程数据:\", courseData);\n            let result;\n            // 判断是新课程还是更新课程\n            if (course.id && course.id > 1000000) {\n                // 新课程，使用创建API\n                console.log(\"\\uD83D\\uDCE4 创建新课程\");\n                const { data: response } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_2__.courseApi.createCourse(courseData);\n                result = response;\n                console.log(\"✅ 课程创建成功\");\n            } else {\n                // 现有课程，使用更新API\n                console.log(\"\\uD83D\\uDCE4 更新现有课程:\", course.id);\n                const { data: response } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_2__.courseApi.updateCourse(course.id, courseData);\n                result = response;\n                console.log(\"✅ 课程更新成功\");\n            }\n            return result;\n        } catch (error) {\n            console.error(\"❌ 保存课程失败:\", error);\n            throw error;\n        }\n    };\n    // 发布选中的课程\n    const handlePublishCourse = async ()=>{\n        if (!selectedCourseId) {\n            alert(\"请先选择要发布的课程\");\n            return;\n        }\n        const selectedCourse = courseList.find((course)=>course.id === selectedCourseId);\n        if (!selectedCourse) {\n            alert(\"未找到选中的课程\");\n            return;\n        }\n        // 检查课程是否已经发布\n        if (selectedCourse.status === 1) {\n            alert(\"该课程已经发布，无需重复发布\");\n            return;\n        }\n        try {\n            setIsPublishing(true);\n            console.log(\"\\uD83D\\uDCE4 开始发布课程\");\n            console.log(\"\\uD83D\\uDCE4 selectedCourseId:\", selectedCourseId, \"类型:\", typeof selectedCourseId);\n            console.log(\"\\uD83D\\uDCE4 课程信息:\", selectedCourse);\n            console.log(\"\\uD83D\\uDCE4 课程ID字段:\", selectedCourse.id, \"类型:\", typeof selectedCourse.id);\n            console.log(\"\\uD83D\\uDCE4 课程列表中的所有ID:\", courseList.map((c)=>({\n                    id: c.id,\n                    type: typeof c.id,\n                    title: c.title\n                })));\n            // 确保使用正确的课程ID\n            const courseIdToPublish = selectedCourse.id;\n            console.log(\"\\uD83D\\uDCE4 即将发布的课程ID:\", courseIdToPublish, \"类型:\", typeof courseIdToPublish);\n            const { data: response } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_2__.courseApi.publishCourse(courseIdToPublish);\n            if (response.code === 200) {\n                console.log(\"✅ 课程发布成功:\", response.data);\n                alert(\"课程发布成功！\");\n                // 刷新课程列表以更新状态\n                await loadCourseList();\n            } else {\n                console.error(\"❌ 发布课程失败:\", response.message);\n                alert(response.message || \"发布课程失败\");\n            }\n        } catch (error) {\n            var _error_response, _error_response_data, _error_response1;\n            console.error(\"❌ 发布课程失败:\", error);\n            console.error(\"❌ 错误详情:\", (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data);\n            // 处理具体的错误信息\n            if ((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : (_error_response_data = _error_response1.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) {\n                alert(error.response.data.message);\n            } else if (error.message) {\n                alert(error.message);\n            } else {\n                alert(\"发布课程失败，请重试\");\n            }\n        } finally{\n            setIsPublishing(false);\n        }\n    };\n    // 保存课程列表\n    const handleSave = async ()=>{\n        try {\n            setIsCreating(true);\n            // 检查是否有文件正在上传\n            if (uploadingFiles.size > 0) {\n                alert(\"有文件正在上传中，请等待上传完成后再保存\");\n                setIsCreating(false);\n                return;\n            }\n            console.log(\"\\uD83D\\uDCBE 开始保存课程列表\");\n            // 如果有选中的课程且在编辑状态，先保存当前课程\n            if (selectedCourseId && rightPanelType === \"course\") {\n                const selectedCourse = courseList.find((c)=>c.id === selectedCourseId);\n                if (selectedCourse) {\n                    var _courseDetail_teachingMaterials;\n                    // 更新课程数据\n                    const updatedCourse = {\n                        ...selectedCourse,\n                        title: courseDetail.title,\n                        description: courseDetail.description,\n                        coverImage: courseDetail.coverImage,\n                        hasVideo: courseDetail.isVideoEnabled ? 1 : 0,\n                        hasDocument: courseDetail.isAttachmentEnabled ? 1 : 0,\n                        contentConfig: courseDetail.contentConfig,\n                        teachingInfo: courseDetail.teachingInfo,\n                        additionalResources: ((_courseDetail_teachingMaterials = courseDetail.teachingMaterials) === null || _courseDetail_teachingMaterials === void 0 ? void 0 : _courseDetail_teachingMaterials.map((material)=>({\n                                title: material.name,\n                                url: material.url,\n                                description: material.name\n                            }))) || []\n                    };\n                    // 先更新课程列表中的数据\n                    setCourseList((prev)=>prev.map((course)=>course.id === selectedCourseId ? updatedCourse : course));\n                    // 然后保存到后端\n                    await saveCourse(updatedCourse);\n                }\n            }\n            // 保存系列课程信息\n            const data = {\n                title: editingTitle,\n                courseGoals,\n                courseObjectives,\n                courseList\n            };\n            onSave(data);\n            onClose();\n            console.log(\"✅ 课程列表保存完成\");\n        } catch (error) {\n            console.error(\"❌ 保存失败:\", error);\n            alert(\"保存失败: \".concat(error.message || \"请重试\"));\n        } finally{\n            setIsCreating(false);\n        }\n    };\n    // 发布系列课程\n    const handlePublish = ()=>{\n        // TODO: 实现发布逻辑\n        alert(\"发布系列课程功能待实现\");\n    };\n    // 退出编辑模式 - 保存数据并关闭\n    const handleExitEdit = ()=>{\n        handleSave();\n    };\n    // 显示设置面板\n    const showSettingsPanel = ()=>{\n        setRightPanelType(\"settings\");\n        setSelectedCourseId(null);\n    };\n    // 显示课程编辑面板\n    const showCoursePanel = async (courseId)=>{\n        console.log(\"\\uD83C\\uDFAF showCoursePanel 被调用\");\n        console.log(\"\\uD83C\\uDFAF 传入的courseId:\", courseId, \"类型:\", typeof courseId);\n        console.log(\"\\uD83C\\uDFAF 当前课程列表:\", courseList.map((c)=>({\n                id: c.id,\n                type: typeof c.id,\n                title: c.title\n            })));\n        setRightPanelType(\"course\");\n        setSelectedCourseId(courseId);\n        // 获取选中的课程并更新courseDetail状态\n        const selectedCourse = courseList.find((course)=>course.id === courseId);\n        console.log(\"\\uD83C\\uDFAF 找到的课程:\", selectedCourse);\n        if (selectedCourse) {\n            try {\n                console.log(\"\\uD83D\\uDD04 获取课程详情，seriesId:\", seriesId, \"courseId:\", courseId);\n                // 获取真实的课程详情数据\n                const { data: res } = await fetchCourseDetail(seriesId, courseId);\n                if (res.code === 200 && res.data) {\n                    var _courseDetailData_additionalResources, _courseDetailData_teachingInfo;\n                    const courseDetailData = res.data;\n                    console.log(\"✅ 获取到课程详情:\", courseDetailData);\n                    // 将真实的 additionalResources 映射到 teachingMaterials\n                    const teachingMaterials = ((_courseDetailData_additionalResources = courseDetailData.additionalResources) === null || _courseDetailData_additionalResources === void 0 ? void 0 : _courseDetailData_additionalResources.map((resource)=>({\n                            type: \"application/octet-stream\",\n                            name: resource.title || resource.name || \"附件\",\n                            url: resource.url\n                        }))) || [];\n                    // 将API返回的teachingInfo数组格式转换为新的格式\n                    const mappedTeachingInfo = ((_courseDetailData_teachingInfo = courseDetailData.teachingInfo) === null || _courseDetailData_teachingInfo === void 0 ? void 0 : _courseDetailData_teachingInfo.map((info)=>({\n                            title: info.title || \"\",\n                            content: Array.isArray(info.content) ? info.content.join(\"\\n\") : info.content || \"\"\n                        }))) || [];\n                    console.log(\"\\uD83D\\uDCCE 映射的教学附件:\", teachingMaterials);\n                    console.log(\"\\uD83D\\uDCDA 映射的教学信息:\", mappedTeachingInfo);\n                    setCourseDetail((prev)=>{\n                        var _courseDetailData_contentConfig_video, _courseDetailData_contentConfig, _courseDetailData_contentConfig_video1, _courseDetailData_contentConfig1, _courseDetailData_contentConfig_document, _courseDetailData_contentConfig2, _courseDetailData_contentConfig_document1, _courseDetailData_contentConfig3;\n                        return {\n                            ...prev,\n                            title: courseDetailData.title,\n                            description: courseDetailData.description,\n                            coverImage: courseDetailData.coverImage || \"\",\n                            isVideoEnabled: courseDetailData.hasVideo === 1,\n                            isAttachmentEnabled: courseDetailData.hasDocument === 1,\n                            contentConfig: courseDetailData.contentConfig || {},\n                            teachingInfo: mappedTeachingInfo,\n                            teachingMaterials: teachingMaterials,\n                            videoUrl: ((_courseDetailData_contentConfig = courseDetailData.contentConfig) === null || _courseDetailData_contentConfig === void 0 ? void 0 : (_courseDetailData_contentConfig_video = _courseDetailData_contentConfig.video) === null || _courseDetailData_contentConfig_video === void 0 ? void 0 : _courseDetailData_contentConfig_video.url) || \"\",\n                            videoName: ((_courseDetailData_contentConfig1 = courseDetailData.contentConfig) === null || _courseDetailData_contentConfig1 === void 0 ? void 0 : (_courseDetailData_contentConfig_video1 = _courseDetailData_contentConfig1.video) === null || _courseDetailData_contentConfig_video1 === void 0 ? void 0 : _courseDetailData_contentConfig_video1.name) || \"\",\n                            attachmentUrl: ((_courseDetailData_contentConfig2 = courseDetailData.contentConfig) === null || _courseDetailData_contentConfig2 === void 0 ? void 0 : (_courseDetailData_contentConfig_document = _courseDetailData_contentConfig2.document) === null || _courseDetailData_contentConfig_document === void 0 ? void 0 : _courseDetailData_contentConfig_document.url) || \"\",\n                            attachmentName: ((_courseDetailData_contentConfig3 = courseDetailData.contentConfig) === null || _courseDetailData_contentConfig3 === void 0 ? void 0 : (_courseDetailData_contentConfig_document1 = _courseDetailData_contentConfig3.document) === null || _courseDetailData_contentConfig_document1 === void 0 ? void 0 : _courseDetailData_contentConfig_document1.name) || \"\"\n                        };\n                    });\n                } else {\n                    console.error(\"❌ 获取课程详情失败:\", res.message);\n                    // 使用基础数据作为后备\n                    setCourseDetail((prev)=>({\n                            ...prev,\n                            title: selectedCourse.title,\n                            description: selectedCourse.description,\n                            coverImage: selectedCourse.coverImage || \"\",\n                            isVideoEnabled: selectedCourse.hasVideo === 1,\n                            isAttachmentEnabled: selectedCourse.hasDocument === 1,\n                            teachingMaterials: [] // 清空附件列表\n                        }));\n                }\n            } catch (error) {\n                console.error(\"❌ 获取课程详情异常:\", error);\n                // 使用基础数据作为后备\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        title: selectedCourse.title,\n                        description: selectedCourse.description,\n                        coverImage: selectedCourse.coverImage || \"\",\n                        isVideoEnabled: selectedCourse.hasVideo === 1,\n                        isAttachmentEnabled: selectedCourse.hasDocument === 1,\n                        teachingMaterials: [] // 清空附件列表\n                    }));\n            }\n        }\n    };\n    // 获取选中的课程\n    const getSelectedCourse = ()=>{\n        return courseList.find((course)=>course.id === selectedCourseId);\n    };\n    if (!isVisible) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"course-list-modal-overlay\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"course-list-modal\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"course-list-header\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"course-list-title-section\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"course-list-title\",\n                                    children: \"课程列表\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 1095,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"course-list-actions\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: showSettingsPanel,\n                                            className: \"course-list-settings-btn \".concat(rightPanelType === \"settings\" ? \"active\" : \"\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1101,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 1097,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: addNewCourse,\n                                            className: \"course-list-add-btn\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1104,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 1103,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 1096,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                            lineNumber: 1094,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"course-list-close-btn\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"w-6 h-6\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                lineNumber: 1109,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                            lineNumber: 1108,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                    lineNumber: 1093,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"course-list-content\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"course-list-sidebar\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"course-list-items\",\n                                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"course-list-loading\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"加载中...\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 1120,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 1119,\n                                    columnNumber: 17\n                                }, undefined) : courseList.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"course-list-empty\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"course-list-empty-icon\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-12 h-12 text-gray-300\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1125,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 1124,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"course-list-empty-title\",\n                                            children: \"暂无课时\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 1127,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"course-list-empty-description\",\n                                            children: \"点击右上角的 + 按钮添加第一个课时\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 1128,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: addNewCourse,\n                                            className: \"course-list-empty-btn\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1135,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                \"添加课时\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 1131,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 1123,\n                                    columnNumber: 17\n                                }, undefined) : courseList.map((course)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"course-list-item \".concat(selectedCourseId === course.id ? \"active\" : \"\"),\n                                        onClick: ()=>showCoursePanel(course.id),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"course-list-item-content\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"course-list-item-text\",\n                                                        children: course.title\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1147,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"course-status-badge \".concat(course.status === 1 ? \"published\" : \"draft\"),\n                                                        children: course.status === 1 ? \"已发布\" : \"未发布\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1148,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1146,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                    deleteCourse(course.id);\n                                                },\n                                                className: \"course-list-item-delete\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-3 h-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1159,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1152,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, course.id, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 1141,\n                                        columnNumber: 19\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                lineNumber: 1117,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                            lineNumber: 1116,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"course-list-edit-area\",\n                            children: [\n                                rightPanelType === \"none\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"course-edit-empty\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"course-edit-empty-icon\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-16 h-16 text-gray-300\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1172,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 1171,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"course-edit-empty-title\",\n                                            children: \"无课程详情\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 1174,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"course-edit-empty-description\",\n                                            children: \"点击左侧课程或设置按钮查看详情\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 1175,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 1170,\n                                    columnNumber: 15\n                                }, undefined),\n                                rightPanelType === \"settings\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"course-series-cover\",\n                                            children: seriesCoverImage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: seriesCoverImage,\n                                                alt: \"系列课程封面\",\n                                                className: \"course-series-cover-image\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1186,\n                                                columnNumber: 21\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"course-series-cover-placeholder\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"系列课程封面\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1193,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1192,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 1184,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"course-edit-form\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-edit-field\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"course-edit-label\",\n                                                            children: \"系列课程标题\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1202,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: editingTitle,\n                                                            onChange: (e)=>setEditingTitle(e.target.value),\n                                                            className: \"course-edit-input\",\n                                                            placeholder: \"请输入系列课程标题\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1203,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1201,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-edit-field\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"course-edit-label\",\n                                                            children: \"课程标签\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1214,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Select_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            mode: \"multiple\",\n                                                            style: {\n                                                                width: \"100%\"\n                                                            },\n                                                            placeholder: \"请选择课程标签\",\n                                                            value: selectedTags,\n                                                            onChange: setSelectedTags,\n                                                            loading: tagsLoading,\n                                                            options: courseTags.map((tag)=>{\n                                                                console.log(\"\\uD83C\\uDFF7️ 渲染标签选项:\", tag);\n                                                                return {\n                                                                    label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            color: tag.color\n                                                                        },\n                                                                        children: tag.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1226,\n                                                                        columnNumber: 29\n                                                                    }, void 0),\n                                                                    value: tag.id\n                                                                };\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1215,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                fontSize: \"12px\",\n                                                                color: \"#666\",\n                                                                marginTop: \"4px\"\n                                                            },\n                                                            children: [\n                                                                \"调试: 当前标签数量 \",\n                                                                courseTags.length,\n                                                                \", 加载状态: \",\n                                                                tagsLoading ? \"是\" : \"否\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1235,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1213,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-edit-field\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"course-edit-label\",\n                                                            children: \"课程项目成员\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1242,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: projectMembers,\n                                                            onChange: (e)=>setProjectMembers(e.target.value),\n                                                            className: \"course-edit-input\",\n                                                            placeholder: \"请输入项目成员，如：张老师、李助教、王同学\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1243,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1241,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 1199,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true),\n                                rightPanelType === \"course\" && getSelectedCourse() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"course-detail-edit\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"course-detail-top\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"course-detail-cover\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"course-cover-upload-area\",\n                                                                onClick: ()=>{\n                                                                    var _document_getElementById;\n                                                                    return (_document_getElementById = document.getElementById(\"cover-upload-input\")) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.click();\n                                                                },\n                                                                children: courseDetail.coverImage || ((_getSelectedCourse = getSelectedCourse()) === null || _getSelectedCourse === void 0 ? void 0 : _getSelectedCourse.coverImage) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                    src: courseDetail.coverImage || ((_getSelectedCourse1 = getSelectedCourse()) === null || _getSelectedCourse1 === void 0 ? void 0 : _getSelectedCourse1.coverImage),\n                                                                    alt: \"课程封面\",\n                                                                    className: \"course-cover-image\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1267,\n                                                                    columnNumber: 27\n                                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"course-cover-placeholder\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"点击上传课程封面\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1274,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1273,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1262,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                id: \"cover-upload-input\",\n                                                                type: \"file\",\n                                                                accept: \"image/jpeg,image/jpg,image/png,image/gif\",\n                                                                onChange: handleCoverUpload,\n                                                                style: {\n                                                                    display: \"none\"\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1278,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1261,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"course-detail-basic\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"course-detail-field\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        children: \"课程标题\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1288,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        value: courseDetail.title || ((_getSelectedCourse2 = getSelectedCourse()) === null || _getSelectedCourse2 === void 0 ? void 0 : _getSelectedCourse2.title) || \"\",\n                                                                        onChange: (e)=>{\n                                                                            setCourseDetail((prev)=>({\n                                                                                    ...prev,\n                                                                                    title: e.target.value\n                                                                                }));\n                                                                            updateCourseTitle(selectedCourseId, e.target.value);\n                                                                        },\n                                                                        placeholder: \"请输入课程标题\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1289,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1287,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"course-detail-field\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        children: \"课程介绍\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1300,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                        value: courseDetail.description || ((_getSelectedCourse3 = getSelectedCourse()) === null || _getSelectedCourse3 === void 0 ? void 0 : _getSelectedCourse3.description) || \"\",\n                                                                        onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                    ...prev,\n                                                                                    description: e.target.value\n                                                                                })),\n                                                                        placeholder: \"请输入课程介绍\",\n                                                                        rows: 3\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1301,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1299,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1286,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1260,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"course-detail-section\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        children: \"课程资源\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1313,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"course-resource-item\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"resource-header-right\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"课程视频\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1318,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"switch\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"checkbox\",\n                                                                                checked: courseDetail.isVideoEnabled,\n                                                                                onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                            ...prev,\n                                                                                            isVideoEnabled: e.target.checked\n                                                                                        }))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1320,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"slider\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1325,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1319,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1317,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            courseDetail.isVideoEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"video-content-area\",\n                                                                children: ((_courseDetail_contentConfig = courseDetail.contentConfig) === null || _courseDetail_contentConfig === void 0 ? void 0 : (_courseDetail_contentConfig_video = _courseDetail_contentConfig.video) === null || _courseDetail_contentConfig_video === void 0 ? void 0 : _courseDetail_contentConfig_video.url) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"video-info-section\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"video-preview\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                                                                className: \"video-thumbnail\",\n                                                                                controls: true,\n                                                                                poster: courseDetail.coverImage,\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                                                                                        src: courseDetail.contentConfig.video.url,\n                                                                                        type: \"video/mp4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1339,\n                                                                                        columnNumber: 35\n                                                                                    }, undefined),\n                                                                                    \"您的浏览器不支持视频播放\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1334,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1333,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"video-name-centered\",\n                                                                            children: courseDetail.contentConfig.video.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1343,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            className: \"upload-btn-horizontal\",\n                                                                            onClick: triggerVideoUpload,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"重新上传\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1345,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1344,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1332,\n                                                                    columnNumber: 29\n                                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"video-upload-section\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"video-placeholder-centered\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"play-icon\",\n                                                                                children: \"▶\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1351,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1350,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            className: \"upload-btn-horizontal\",\n                                                                            onClick: triggerVideoUpload,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"上传视频\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1354,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1353,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1349,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1329,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1316,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"course-resource-item\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"resource-header-right\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"课程附件\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1365,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"switch\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"checkbox\",\n                                                                                checked: courseDetail.isAttachmentEnabled,\n                                                                                onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                            ...prev,\n                                                                                            isAttachmentEnabled: e.target.checked\n                                                                                        }))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1367,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"slider\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1372,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1366,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1364,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            courseDetail.isAttachmentEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"attachment-content-area\",\n                                                                children: ((_courseDetail_contentConfig1 = courseDetail.contentConfig) === null || _courseDetail_contentConfig1 === void 0 ? void 0 : (_courseDetail_contentConfig_document = _courseDetail_contentConfig1.document) === null || _courseDetail_contentConfig_document === void 0 ? void 0 : _courseDetail_contentConfig_document.url) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"attachment-info-section\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"attachment-preview\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"document-icon\",\n                                                                                    children: \"\\uD83D\\uDCC4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1381,\n                                                                                    columnNumber: 33\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"attachment-details\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"attachment-name\",\n                                                                                        children: courseDetail.contentConfig.document.name\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1383,\n                                                                                        columnNumber: 35\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1382,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1380,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            className: \"upload-btn-horizontal\",\n                                                                            onClick: triggerAttachmentUpload,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"重新上传\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1387,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1386,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1379,\n                                                                    columnNumber: 29\n                                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"attachment-upload-section\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"upload-btn-horizontal\",\n                                                                        onClick: triggerAttachmentUpload,\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"上传附件\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1393,\n                                                                            columnNumber: 33\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1392,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1391,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1376,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1363,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"course-resource-item\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"resource-header-simple\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"教学附件\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1404,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1403,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"teaching-materials\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"add-material-btn\",\n                                                                        onClick: triggerTeachingMaterialUpload,\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"+\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1408,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"上传\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1409,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1407,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    courseDetail.teachingMaterials && courseDetail.teachingMaterials.length > 0 ? courseDetail.teachingMaterials.map((material, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"material-item\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"material-name\",\n                                                                                    onClick: ()=>{\n                                                                                        if (material.url) {\n                                                                                            window.open(material.url, \"_blank\");\n                                                                                        }\n                                                                                    },\n                                                                                    style: {\n                                                                                        cursor: material.url ? \"pointer\" : \"default\",\n                                                                                        color: material.url ? \"#1890ff\" : \"inherit\",\n                                                                                        textDecoration: material.url ? \"underline\" : \"none\"\n                                                                                    },\n                                                                                    title: material.url ? \"点击下载附件\" : material.name,\n                                                                                    children: [\n                                                                                        \"\\uD83D\\uDCCE \",\n                                                                                        material.name\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1414,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    className: \"remove-material-btn\",\n                                                                                    onClick: ()=>removeTeachingMaterial(index),\n                                                                                    title: \"删除附件\",\n                                                                                    children: \"\\xd7\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1430,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, index, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1413,\n                                                                            columnNumber: 29\n                                                                        }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"empty-materials-hint\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            style: {\n                                                                                color: \"#999\",\n                                                                                fontSize: \"14px\"\n                                                                            },\n                                                                            children: \"暂无教学附件\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1441,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1440,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1406,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1402,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1312,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"course-detail-section\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"section-header\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                children: \"课程内容\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1451,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"add-content-section-btn\",\n                                                                onClick: addTeachingInfoItem,\n                                                                title: \"添加课程内容\",\n                                                                children: \"+ 添加课程内容\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1452,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1450,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"course-content-area\",\n                                                        children: courseDetail.teachingInfo && courseDetail.teachingInfo.length > 0 ? courseDetail.teachingInfo.map((info, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"teaching-info-card\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"card-header\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"card-title\",\n                                                                                children: [\n                                                                                    \"课程内容 \",\n                                                                                    index + 1\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1465,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"remove-card-btn\",\n                                                                                onClick: ()=>removeTeachingInfoItem(index),\n                                                                                title: \"删除此内容\",\n                                                                                children: \"\\xd7\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1466,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1464,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"card-content\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"input-group\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        children: \"标题\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1476,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                        type: \"text\",\n                                                                                        value: info.title,\n                                                                                        onChange: (e)=>updateTeachingInfoTitle(index, e.target.value),\n                                                                                        placeholder: \"请输入标题，如：教学目标、教学方法等\",\n                                                                                        className: \"title-input\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1477,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1475,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"input-group\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        children: \"内容\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1486,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                                        value: info.content,\n                                                                                        onChange: (e)=>updateTeachingInfoContent(index, e.target.value),\n                                                                                        placeholder: \"请输入具体内容，多个内容项可用换行分隔\",\n                                                                                        className: \"content-textarea\",\n                                                                                        rows: 4\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1487,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1485,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1474,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1463,\n                                                                columnNumber: 27\n                                                            }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"empty-content-hint\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"暂无课程内容，点击右上角按钮添加\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1500,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1499,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1460,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1449,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"course-detail-section\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"one-key-section\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"one-key-item\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"重新上课\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1510,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"switch\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"checkbox\",\n                                                                            checked: courseDetail.isOneKeyOpen,\n                                                                            onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                        ...prev,\n                                                                                        isOneKeyOpen: e.target.checked\n                                                                                    }))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1512,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"slider\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1517,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1511,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1509,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        courseDetail.isOneKeyOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"one-key-item\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"分配积木\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1524,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"switch\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: \"checkbox\",\n                                                                                    checked: courseDetail.isDistributionEnabled,\n                                                                                    onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                ...prev,\n                                                                                                isDistributionEnabled: e.target.checked\n                                                                                            }))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1526,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"slider\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1531,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1525,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        courseDetail.isDistributionEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"block-template-section\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    className: \"select-template-btn\",\n                                                                                    children: \"选择积木模板\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1535,\n                                                                                    columnNumber: 33\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"selected-template-display\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: courseDetail.selectedTemplate || \"选中的模板名字\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1539,\n                                                                                        columnNumber: 35\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1538,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1534,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1523,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"one-key-item\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"分配能量\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1546,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"switch\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: \"checkbox\",\n                                                                                    checked: courseDetail.isDistributionWater,\n                                                                                    onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                ...prev,\n                                                                                                isDistributionWater: e.target.checked\n                                                                                            }))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1548,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"slider\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1553,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1547,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1545,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                courseDetail.isDistributionWater && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"energy-input-section\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"需要能量：\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1559,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            value: courseDetail.requiredEnergy || \"\",\n                                                                            onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                        ...prev,\n                                                                                        requiredEnergy: e.target.value\n                                                                                    })),\n                                                                            placeholder: \"请输入需要的能量值\",\n                                                                            className: \"energy-input\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1560,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1558,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"one-key-item\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"分配任务\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1571,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"switch\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: \"checkbox\",\n                                                                                    checked: courseDetail.isDistributionLimit,\n                                                                                    onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                ...prev,\n                                                                                                isDistributionLimit: e.target.checked\n                                                                                            }))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1573,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"slider\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1578,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1572,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1570,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                courseDetail.isDistributionLimit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"task-config-form\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"task-config-row\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"task-config-field\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                            children: \"任务名称:\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1588,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                            type: \"text\",\n                                                                                            value: courseDetail.taskConfig.taskName,\n                                                                                            onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        taskConfig: {\n                                                                                                            ...prev.taskConfig,\n                                                                                                            taskName: e.target.value\n                                                                                                        }\n                                                                                                    })),\n                                                                                            placeholder: \"请输入任务名称\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1589,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1587,\n                                                                                    columnNumber: 33\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"task-config-field\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                            children: \"任务持续天数:\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1600,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                            type: \"number\",\n                                                                                            value: courseDetail.taskConfig.taskDuration,\n                                                                                            onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        taskConfig: {\n                                                                                                            ...prev.taskConfig,\n                                                                                                            taskDuration: e.target.value\n                                                                                                        }\n                                                                                                    })),\n                                                                                            placeholder: \"请输入天数\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1601,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1599,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1586,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"task-config-field task-config-full\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                    children: \"任务描述:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1615,\n                                                                                    columnNumber: 33\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                                    value: courseDetail.taskConfig.taskDescription,\n                                                                                    onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                ...prev,\n                                                                                                taskConfig: {\n                                                                                                    ...prev.taskConfig,\n                                                                                                    taskDescription: e.target.value\n                                                                                                }\n                                                                                            })),\n                                                                                    placeholder: \"请输入任务描述\",\n                                                                                    rows: 4\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1616,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1614,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"task-config-field task-config-full\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                    children: [\n                                                                                        \"任务自评项: \",\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"item-number\",\n                                                                                            children: courseDetail.taskConfig.selfAssessmentItems.length\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1629,\n                                                                                            columnNumber: 47\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1629,\n                                                                                    columnNumber: 33\n                                                                                }, undefined),\n                                                                                courseDetail.taskConfig.selfAssessmentItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"self-assessment-item\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                            type: \"text\",\n                                                                                            value: item,\n                                                                                            onChange: (e)=>{\n                                                                                                const newItems = [\n                                                                                                    ...courseDetail.taskConfig.selfAssessmentItems\n                                                                                                ];\n                                                                                                newItems[index] = e.target.value;\n                                                                                                setCourseDetail((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        taskConfig: {\n                                                                                                            ...prev.taskConfig,\n                                                                                                            selfAssessmentItems: newItems\n                                                                                                        }\n                                                                                                    }));\n                                                                                            },\n                                                                                            placeholder: \"请输入自评项内容\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1632,\n                                                                                            columnNumber: 37\n                                                                                        }, undefined)\n                                                                                    }, index, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1631,\n                                                                                        columnNumber: 35\n                                                                                    }, undefined)),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    type: \"button\",\n                                                                                    className: \"add-assessment-btn\",\n                                                                                    onClick: ()=>setCourseDetail((prev)=>({\n                                                                                                ...prev,\n                                                                                                taskConfig: {\n                                                                                                    ...prev.taskConfig,\n                                                                                                    selfAssessmentItems: [\n                                                                                                        ...prev.taskConfig.selfAssessmentItems,\n                                                                                                        \"\"\n                                                                                                    ]\n                                                                                                }\n                                                                                            })),\n                                                                                    children: \"+\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1647,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1628,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"task-config-field task-config-full\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                    children: \"任务参考作品:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1664,\n                                                                                    columnNumber: 33\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"reference-works-section\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                            type: \"button\",\n                                                                                            className: \"select-works-btn\",\n                                                                                            children: \"选择作品\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1666,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"reference-works-grid\",\n                                                                                            children: [\n                                                                                                courseDetail.taskConfig.referenceWorks.map((work, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"reference-work-item\",\n                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                            children: work.name || \"作品\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                            lineNumber: 1670,\n                                                                                                            columnNumber: 41\n                                                                                                        }, undefined)\n                                                                                                    }, index, false, {\n                                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                        lineNumber: 1669,\n                                                                                                        columnNumber: 39\n                                                                                                    }, undefined)),\n                                                                                                Array.from({\n                                                                                                    length: Math.max(0, 3 - courseDetail.taskConfig.referenceWorks.length)\n                                                                                                }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"reference-work-item empty\"\n                                                                                                    }, \"empty-\".concat(index), false, {\n                                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                        lineNumber: 1675,\n                                                                                                        columnNumber: 39\n                                                                                                    }, undefined))\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1667,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1665,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1663,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"task-config-field task-config-full\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                    children: \"任务参考资源:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1683,\n                                                                                    columnNumber: 33\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"reference-resources-section\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"reference-resources-grid\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                type: \"button\",\n                                                                                                className: \"upload-resource-btn\",\n                                                                                                onClick: ()=>{\n                                                                                                    // 触发文件上传\n                                                                                                    const input = document.createElement(\"input\");\n                                                                                                    input.type = \"file\";\n                                                                                                    input.accept = \".pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx,.txt,.jpg,.png,.gif\";\n                                                                                                    input.onchange = (e)=>{\n                                                                                                        var _e_target_files;\n                                                                                                        const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n                                                                                                        if (file) {\n                                                                                                            setCourseDetail((prev)=>({\n                                                                                                                    ...prev,\n                                                                                                                    taskConfig: {\n                                                                                                                        ...prev.taskConfig,\n                                                                                                                        referenceResources: [\n                                                                                                                            ...prev.taskConfig.referenceResources,\n                                                                                                                            {\n                                                                                                                                type: \"file\",\n                                                                                                                                name: file.name\n                                                                                                                            }\n                                                                                                                        ]\n                                                                                                                    }\n                                                                                                                }));\n                                                                                                        }\n                                                                                                    };\n                                                                                                    input.click();\n                                                                                                },\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                                                        size: 24\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                        lineNumber: 1712,\n                                                                                                        columnNumber: 39\n                                                                                                    }, undefined),\n                                                                                                    \"上传\"\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                lineNumber: 1686,\n                                                                                                columnNumber: 37\n                                                                                            }, undefined),\n                                                                                            courseDetail.taskConfig.referenceResources.map((resource, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"reference-resource-item\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                            children: resource.name\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                            lineNumber: 1717,\n                                                                                                            columnNumber: 41\n                                                                                                        }, undefined),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                            type: \"button\",\n                                                                                                            className: \"remove-resource-btn\",\n                                                                                                            onClick: ()=>{\n                                                                                                                const newResources = courseDetail.taskConfig.referenceResources.filter((_, i)=>i !== index);\n                                                                                                                setCourseDetail((prev)=>({\n                                                                                                                        ...prev,\n                                                                                                                        taskConfig: {\n                                                                                                                            ...prev.taskConfig,\n                                                                                                                            referenceResources: newResources\n                                                                                                                        }\n                                                                                                                    }));\n                                                                                                            },\n                                                                                                            children: \"\\xd7\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                            lineNumber: 1718,\n                                                                                                            columnNumber: 41\n                                                                                                        }, undefined)\n                                                                                                    ]\n                                                                                                }, index, true, {\n                                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                    lineNumber: 1716,\n                                                                                                    columnNumber: 39\n                                                                                                }, undefined))\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1685,\n                                                                                        columnNumber: 35\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1684,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1682,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1584,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1508,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1507,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 1258,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                            lineNumber: 1168,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                    lineNumber: 1114,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"course-list-footer\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"course-list-footer-left\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handlePublish,\n                                className: \"course-list-btn course-list-btn-publish\",\n                                children: \"发布系列课程\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                lineNumber: 1751,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                            lineNumber: 1750,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"course-list-footer-right\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleExitEdit,\n                                    className: \"course-list-btn course-list-btn-exit\",\n                                    children: \"退出编辑模式\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 1756,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handlePublishCourse,\n                                    className: \"course-list-btn course-list-btn-publish-course\",\n                                    disabled: !selectedCourseId || ((_courseList_find = courseList.find((c)=>c.id === selectedCourseId)) === null || _courseList_find === void 0 ? void 0 : _courseList_find.status) === 1 || isPublishing,\n                                    title: !selectedCourseId ? \"请先选择要发布的课程\" : ((_courseList_find1 = courseList.find((c)=>c.id === selectedCourseId)) === null || _courseList_find1 === void 0 ? void 0 : _courseList_find1.status) === 1 ? \"该课程已发布\" : isPublishing ? \"正在发布课程...\" : \"发布选中的课程\",\n                                    children: isPublishing ? \"正在发布...\" : \"发布课程\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 1759,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleSave,\n                                    className: \"course-list-btn course-list-btn-save\",\n                                    disabled: uploadingFiles.size > 0 || isCreating,\n                                    title: uploadingFiles.size > 0 ? \"有文件正在上传中，请等待上传完成\" : isCreating ? \"正在创建课程...\" : \"创建课程\",\n                                    children: uploadingFiles.size > 0 ? \"上传中...\" : isCreating ? \"正在创建...\" : \"创建课程\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 1775,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                            lineNumber: 1755,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                    lineNumber: 1749,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n            lineNumber: 1091,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n        lineNumber: 1090,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CourseListEditModal, \"ztY0lOQ//CLZRzgF3Lia9nILQxg=\");\n_c = CourseListEditModal;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CourseListEditModal);\nvar _c;\n$RefreshReg$(_c, \"CourseListEditModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/workbench/components/CourseListEditModal.tsx\n"));

/***/ })

});