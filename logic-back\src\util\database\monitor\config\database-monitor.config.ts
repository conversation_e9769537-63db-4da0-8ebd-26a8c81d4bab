export const databaseMonitorConfig = {
  database: {
    // 🔧 主开关：完全启用/禁用慢查询监控
    enableDatabaseMonitoring: process.env.ENABLE_DB_MONITORING !== 'false', // 默认启用

    // 🔧 轻量级模式：减少监控开销
    lightweightMode: process.env.DB_MONITOR_LIGHTWEIGHT === 'true', // 默认关闭

    // 启用慢查询日志记录
    enableSlowQueryLogging: process.env.ENABLE_SLOW_QUERY_LOG !== 'false', // 默认启用

    // 启用慢查询指标统计
    enableQueryMetrics: process.env.ENABLE_QUERY_METRICS !== 'false', // 默认启用

    // 启用调用栈捕获（性能影响较大，生产环境建议禁用）
    enableStackTrace: process.env.ENABLE_STACK_TRACE !== 'false' && process.env.NODE_ENV !== 'production',

    // 最大慢查询记录数
    maxSlowQueryRecords: parseInt(process.env.MAX_SLOW_QUERY_RECORDS || '100'),

    // 查询超时清理时间（毫秒）- 防止内存泄漏
    queryTimeoutMs: parseInt(process.env.QUERY_TIMEOUT_MS || '300000'), // 5分钟

    // 🔧 异步处理慢查询记录（减少对主流程的影响）
    asyncSlowQueryProcessing: process.env.ASYNC_SLOW_QUERY_PROCESSING !== 'false', // 默认启用

    // 告警配置
    alerts: {
      // 慢查询告警阈值
      slowQueryAlertThreshold: 5000, // 5秒
      
      // 严重慢查询告警阈值
      criticalSlowQueryThreshold: 10000, // 10秒
      
      // 启用邮件告警
      enableEmailAlert: false,
      
      // 启用钉钉告警
      enableDingTalkAlert: false,
      
      // 告警邮件配置
      email: {
        recipients: ['<EMAIL>'],
        subject: '数据库慢查询告警'
      },
      
      // 钉钉告警配置
      dingTalk: {
        webhook: process.env.DINGTALK_WEBHOOK || '',
        secret: process.env.DINGTALK_SECRET || ''
      }
    }
  }
};
