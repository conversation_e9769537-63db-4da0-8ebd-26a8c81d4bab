"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var QueryInterceptor_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.QueryInterceptor = void 0;
const common_1 = require("@nestjs/common");
const query_monitor_service_1 = require("../service/query-monitor.service");
let QueryInterceptor = QueryInterceptor_1 = class QueryInterceptor {
    queryMonitorService;
    logger = new common_1.Logger(QueryInterceptor_1.name);
    activeQueries = new Map();
    constructor(queryMonitorService) {
        this.queryMonitorService = queryMonitorService;
    }
    beforeQuery(query, parameters, context) {
        const queryId = this.generateQueryId();
        const queryInfo = {
            query,
            parameters,
            startTime: Date.now(),
            context
        };
        this.activeQueries.set(queryId, queryInfo);
        return queryId;
    }
    afterSlowQuery(queryId, error) {
        const queryInfo = this.activeQueries.get(queryId);
        if (!queryInfo) {
            return;
        }
        const executionTime = Date.now() - queryInfo.startTime;
        console.log("慢查询执行结束,总耗时为", executionTime);
        this.queryMonitorService.recordQuery(queryInfo.query, executionTime, queryInfo.parameters, queryInfo.context);
        if (error) {
            this.logger.error(`查询执行失败 [${executionTime}ms]: ${error.message}`, {
                query: queryInfo.query,
                parameters: queryInfo.parameters,
                executionTime,
                error: error.message,
                context: queryInfo.context
            });
        }
        this.activeQueries.delete(queryId);
    }
    generateQueryId() {
        return `query_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    getActiveQueryCount() {
        return this.activeQueries.size;
    }
    getActiveQueries() {
        return Array.from(this.activeQueries.values()).map(info => ({
            ...info,
            runningTime: Date.now() - info.startTime
        }));
    }
    cleanupTimeoutQueries(timeoutMs = 300000) {
        const now = Date.now();
        const timeoutQueries = [];
        for (const [queryId, queryInfo] of this.activeQueries.entries()) {
            if (now - queryInfo.startTime > timeoutMs) {
                timeoutQueries.push(queryId);
                this.logger.warn(`检测到超时查询 [${now - queryInfo.startTime}ms]`, {
                    query: queryInfo.query,
                    parameters: queryInfo.parameters,
                    context: queryInfo.context
                });
            }
        }
        timeoutQueries.forEach(queryId => {
            this.activeQueries.delete(queryId);
        });
        if (timeoutQueries.length > 0) {
            this.logger.warn(`清理了 ${timeoutQueries.length} 个超时查询`);
        }
    }
};
exports.QueryInterceptor = QueryInterceptor;
exports.QueryInterceptor = QueryInterceptor = QueryInterceptor_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [query_monitor_service_1.QueryMonitorService])
], QueryInterceptor);
//# sourceMappingURL=query-interceptor.js.map