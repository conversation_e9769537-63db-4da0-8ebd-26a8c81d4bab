import request from '../../request'

// 活动报名相关的类型定义
export interface SubmitRegistrationData {
  activityId: number
  agreementAccepted: boolean
  parentConsentAccepted: boolean
  parentSignaturePath: string
  signatureTime?: string
  signatureIp?: string
  remark?: string
}

export interface UploadSignatureData {
  signatureData: string
  activityId: number
}

export interface AddWorksData {
  works: {
    workId: number
    isAwarded?: boolean
    category?: string
    sort?: number
  }[]
}

export const activityRegistrationApi = {
  baseUrl: '/api/v1/activity',

  // 上传签名图片
  uploadSignature: (data: UploadSignatureData) => {
    return request.post(activityRegistrationApi.baseUrl + '/uploadSignature', data)
  },

  // 提交活动报名
  submitRegistration: (data: SubmitRegistrationData) => {
    return request.post(activityRegistrationApi.baseUrl + '/submitRegistration', data)
  },

  // 检查用户是否已报名某活动
  checkRegistration: (activityId: number) => {
    return request.get(`${activityRegistrationApi.baseUrl}/checkRegistration/${activityId}`)
  },

  // 取消活动报名
  cancelRegistration: (activityId: number) => {
    return request.patch(`${activityRegistrationApi.baseUrl}/cancelRegistration/${activityId}`)
  },

  // 获取用户的报名记录
  getUserRegistrations: (params?: {
    page?: number
    size?: number
  }) => {
    return request.get(activityRegistrationApi.baseUrl + '/myRegistrations', { params })
  },

  // 获取活动的报名统计信息
  getRegistrationStatistics: (activityId: number) => {
    return request.get(`${activityRegistrationApi.baseUrl}/registrationStatistics/${activityId}`)
  },

  // 向活动添加作品
  addWorksToActivity: (activityId: number, data: AddWorksData) => {
    return request.post(`${activityRegistrationApi.baseUrl}/addWorks/${activityId}`, data)
  },

  // 设置活动中的获奖作品
  setAwardedWorks: (activityId: number, data: { workIds: number[] }) => {
    return request.post(`${activityRegistrationApi.baseUrl}/setAwardedWorks/${activityId}`, data)
  }
}
