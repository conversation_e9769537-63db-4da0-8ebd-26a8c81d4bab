import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { CreateUserClassDto } from './dto/create-user_class.dto';
import { UpdateUserClassDto } from './dto/update-user_class.dto';
import { UserClass } from './entities/user_class.entity';
import { randomBytes } from 'crypto';

@Injectable()
export class UserClassService {
  constructor(
    @InjectRepository(UserClass)
    private readonly userClassRepository: Repository<UserClass>,
  ) { }

  /**
   * 创建班级信息
   */
  async create(createUserClassDto: CreateUserClassDto) {
    // 如果没有提供邀请码，自动生成一个8位邀请码
    if (!createUserClassDto.inviteCode) {
      createUserClassDto.inviteCode = this.generateInviteCode();
    }

    const entity = this.userClassRepository.create(createUserClassDto);
    return await this.userClassRepository.save(entity);
  }

  /**
   * 查询所有班级信息
   */
  async findAll() {
    return await this.userClassRepository.find({
      order: {
        schoolId: 'ASC',
        grade: 'ASC',
        className: 'ASC'
      }
    });
  }

  /**
   * 根据ID查询班级信息
   */
  async findOne(id: number) {
    return await this.userClassRepository.findOne({ where: { id } });
  }

  /**
   * 根据ID数组批量查询班级信息
   */
  async findByIds(ids: number[]) {
    if (!ids || ids.length === 0) {
      return [];
    }
    return await this.userClassRepository.find({
      where: { id: In(ids) }
    });
  }

  /**
   * 根据学校ID查询班级信息
   */
  async findBySchoolId(schoolId: number) {
    return await this.userClassRepository.find({
      where: { schoolId },
      order: { grade: 'ASC', className: 'ASC' }
    });
  }

  /**
   * 根据年级查询班级信息
   */
  async findByGrade(schoolId: number, grade: string) {
    return await this.userClassRepository.find({
      where: { schoolId, grade },
      order: { className: 'ASC' }
    });
  }

  /**
   * 根据管理老师ID查询班级信息
   */
  async findByTeacherId(teacherId: number) {
    return await this.userClassRepository.find({
      where: { teacherId },
      // order: { schoolId: 'ASC', grade: 'ASC', className: 'ASC' }
      order: { createTime: 'ASC' }
    });
  }

  /**
   * 根据协助老师ID查询班级信息
   */
  async findByAssistantTeacherId(assistantTeacherId: number) {
    return await this.userClassRepository.find({
      where: { assistantTeacherId },
      order: { schoolId: 'ASC', grade: 'ASC', className: 'ASC' }
    });
  }

  /**
   * 根据邀请码查询班级信息
   */
  async findByInviteCode(inviteCode: string) {
    return await this.userClassRepository.findOne({ where: { inviteCode } });
  }

  /**
   * 更新班级信息
   */
  async update(id: number, updateUserClassDto: UpdateUserClassDto) {
    return await this.userClassRepository.update(id, updateUserClassDto);
  }

  /**
   * 重新生成邀请码
   */
  async regenerateInviteCode(id: number) {
    const inviteCode = this.generateInviteCode();
    await this.userClassRepository.update(id, { inviteCode });
    return { inviteCode };
  }

  /**
   * 删除班级信息
   */
  async remove(id: number) {
    return await this.userClassRepository.delete(id);
  }

  /**
   * 生成8位随机邀请码
   */
  private generateInviteCode(): string {
    // 生成6个随机字节并转换为16进制字符串
    return randomBytes(4).toString('hex').substring(0, 8).toUpperCase();
  }

  /**
   * 获取老师在指定学校管理的班级
   */
  async getTeacherClasses(classData: any) {
    const { schoolId, teacherId } = classData;
    return await this.userClassRepository.find({ where: { schoolId, teacherId } });
  }
}
