import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, Modal } from 'antd';
import ActivityReviewComponent from './ActivityReviewComponent';
import CarouselReviewComponent from './CarouselReviewComponent';
import AnnouncementReviewComponent from './AnnouncementReviewComponent';
import WorkReviewComponent from './WorkReviewComponent';
import ParticipationReviewComponent from './ParticipationReviewComponent';
import TeacherReviewComponent from './TeacherReviewComponent';

const ReviewManagementTabs: React.FC = () => {
  const [visible, setVisible] = useState(false);
  const [activeTabKey, setActiveTabKey] = useState('activity');

  const handleOpen = () => {
    setVisible(true);
  };

  const handleClose = () => {
    setVisible(false);
  };

  const items = [
    {
      key: 'activity',
      label: '活动审核',
      children: <ActivityReviewComponent />
    },
    {
      key: 'carousel',
      label: '轮播图审核',
      children: <CarouselReviewComponent />
    },
    {
      key: 'announcement',
      label: '公告审核',
      children: <AnnouncementReviewComponent />
    },
    {
      key: 'work',
      label: '作品审核',
      children: <WorkReviewComponent />
    },
    {
      key: 'participation',
      label: '参赛作品审核',
      children: <ParticipationReviewComponent />
    },
    {
      key: 'teacher',
      label: '教师认证审核',
      children: <TeacherReviewComponent />
    },
    // 后续可以添加更多审核组件
    // { key: 'registration', label: '报名审核', children: <RegistrationReviewComponent /> },
  ];



  return (
    <>
      <Button type="primary" onClick={handleOpen}>
        审核管理
      </Button>

      <Modal
        title="内容审核管理"
        open={visible}
        onCancel={handleClose}
        footer={null}
        width={1200}
        className="review-modal"
        style={{ top: 20 }}
        styles={{ body: { padding: 0 } }}
      >
        <Tabs
          activeKey={activeTabKey}
          onChange={setActiveTabKey}
          items={items}
          tabPosition="left"
          style={{ minHeight: '600px' }}
        />
      </Modal>
    </>
  );
};

export default ReviewManagementTabs; 