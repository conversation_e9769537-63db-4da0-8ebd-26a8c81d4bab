'use client'

import React from 'react';
import { Upload, Button, Image } from 'antd';
import { UploadOutlined, DownloadOutlined, EyeOutlined } from '@ant-design/icons';
import type { UploadFile } from 'antd';
import styles from './EventsTaskModal.module.css';

interface RegistrationFormProps {
  activityData: any;
  registrationFormFileList: UploadFile[];
  setRegistrationFormFileList: (files: UploadFile[]) => void;
  uploadApi: any;
  validationErrors?: Record<string, boolean>;
  setValidationErrors?: (errors: Record<string, boolean> | ((prev: Record<string, boolean>) => Record<string, boolean>)) => void;
}

const RegistrationForm: React.FC<RegistrationFormProps> = ({
  activityData,
  registrationFormFileList,
  setRegistrationFormFileList,
  uploadApi,
  validationErrors,
  setValidationErrors,
}) => {
  // 判断文件是否为图片
  const isImageFile = (file: UploadFile) => {
    if (file.type) {
      return file.type.startsWith('image/');
    }
    // 如果没有type，通过文件名判断
    const fileName = file.name || '';
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
    return imageExtensions.some(ext => fileName.toLowerCase().endsWith(ext));
  };



  if (!activityData?.registrationForm) {
    return null;
  }

  return (
    <div className={styles.registrationFormSection}>
        {(() => {
          // 解析动态配置数据
          const registrationConfigs = [];

          if (activityData.registrationForm.includes('|')) {
            // 新格式：多个配置项用 | 分隔
            const configItems = activityData.registrationForm.split('|');
            configItems.forEach((item: string, index: number) => {
              const [name, fileUrl, exampleUrl] = item.split(',').map((s: string) => s.trim());
              if (name && fileUrl && exampleUrl) {
                registrationConfigs.push({ name, fileUrl, exampleUrl, index });
              }
            });
          } else {
            // 处理单项格式（可能是旧格式或新格式单项）
            const parts = activityData.registrationForm.split(',').map((s: string) => s.trim());

            if (parts.length === 2) {
              // 旧格式：文件URL,示例图URL
              const [formUrl, exampleUrl] = parts;
              if (formUrl && exampleUrl) {
                registrationConfigs.push({
                  name: '报名表',
                  fileUrl: formUrl,
                  exampleUrl: exampleUrl,
                  index: 0
                });
              }
            } else if (parts.length === 3) {
              // 新格式单项：名称,文件URL,示例图URL
              const [name, fileUrl, exampleUrl] = parts;
              if (name && fileUrl && exampleUrl) {
                registrationConfigs.push({
                  name,
                  fileUrl,
                  exampleUrl,
                  index: 0
                });
              }
            }
          }

          return registrationConfigs.map((config) => (
            <div key={config.index} className="border border-gray-200 rounded-xl p-6" style={{ marginBottom: '16px', marginTop: '16px' }}>
              <h4 className="font-medium text-gray-900">
                <span style={{ color: '#ff4d4f', marginRight: '4px' }}>*</span>
                {config.name}
              </h4>

              {/* 示例图和下载按钮 */}
              <div className="bg-gray-50 rounded-xl p-4">
                <div className="flex gap-6 items-start">
                  {/* 示例图缩略图 */}
                  {config.exampleUrl && (
                    <div className="flex flex-col gap-3">
                      <div className="text-sm font-medium text-gray-700">示例图：</div>
                      <div className="relative">
                        <Image
                          src={config.exampleUrl}
                          alt={`${config.name}示例图`}
                          width={140}
                          height={140}
                          className="rounded-lg border-2 border-white shadow-md hover:shadow-lg transition-all duration-200"
                          style={{ objectFit: 'cover' }}
                          preview={{
                            mask: (
                              <div className="flex flex-col items-center justify-center text-white text-sm">
                                <div>🔍</div>
                                <div>点击预览</div>
                              </div>
                            )
                          }}
                          onError={() => {
                            console.error('示例图加载失败:', config.exampleUrl);
                          }}
                        />
                      </div>
                    </div>
                  )}

                  {/* 下载按钮区域 */}
                  <div className="flex flex-col gap-3 flex-1 justify-center ">
                    <div className="text-sm font-medium text-gray-700">文件下载：</div>
                    <div className="flex flex-col gap-2">
                      <Button
                        type="primary"
                        icon={<DownloadOutlined />}
                        size="large"
                        onClick={() => {
                          if (config.fileUrl) {
                            window.open(config.fileUrl, '_blank');
                          }
                        }}
                        className="w-fit shadow-sm hover:shadow-md transition-shadow"
                      >
                        下载{config.name}
                      </Button>
                      <div className="text-xs text-gray-500 mt-1">
                        请下载{config.name}，参考示例图填写后上传
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* 上传已填写的文件 */}
              <div>
                <label className="block text-sm font-medium mb-2">上传已填写的{config.name}</label>
                <div style={{
                  border: validationErrors?.registrationForm ? '1px solid #ff4d4f' : '1px solid #d9d9d9',
                  borderRadius: '6px',
                  padding: '4px'
                }}>
                  <Upload
                    fileList={registrationFormFileList}
                    onChange={(info) => {
                      // 处理文件上传状态变化
                      const updatedFileList = info.fileList.map(file => {
                        if (file.response && file.status === 'done') {
                          // 如果上传成功，设置文件的URL
                          return {
                            ...file,
                            url: file.response
                          };
                        }
                        return file;
                      });
                      setRegistrationFormFileList(updatedFileList);

                      // 清除验证错误
                      if (validationErrors?.registrationForm && setValidationErrors) {
                        setValidationErrors(prev => ({ ...prev, registrationForm: false }));
                      }
                    }}
                    customRequest={async (options) => {
                      const { file, onSuccess, onError } = options;
                      try {
                        const response = await uploadApi.uploadToOss(file as File);
                        onSuccess?.(response);
                      } catch (error) {
                        onError?.(error as Error);
                      }
                    }}
                    accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                    maxCount={1}
                    showUploadList={{
                      showPreviewIcon: true,
                      showRemoveIcon: true,
                      showDownloadIcon: false,
                    }}
                    itemRender={(originNode, file) => {
                      // 如果是图片文件且已上传成功，显示缩略图
                      if (isImageFile(file) && (file.url || file.response) && file.status === 'done') {
                        const imageUrl = file.url || file.response;
                        return (
                          <div className="flex items-center gap-3 p-2 border border-gray-200 rounded-lg bg-gray-50">
                            {/* 缩略图 */}
                            <div className="relative">
                              <Image
                                src={imageUrl}
                                alt={file.name}
                                width={64}
                                height={64}
                                className="object-cover rounded border"
                                style={{ width: '64px', height: '64px' }}
                                preview={{
                                  mask: (
                                    <div className="flex items-center justify-center">
                                      <EyeOutlined className="text-white" />
                                    </div>
                                  ),
                                }}
                              />
                            </div>

                            {/* 文件信息 */}
                            <div className="flex-1 min-w-0">
                              <div className="text-sm font-medium text-gray-900 truncate">
                                {file.name}
                              </div>
                              <div className="text-xs text-gray-500">
                                图片文件 • 点击预览
                              </div>
                            </div>

                            {/* 删除按钮 */}
                            <Button
                              type="text"
                              size="small"
                              danger
                              onClick={() => {
                                setRegistrationFormFileList([]);
                              }}
                            >
                              删除
                            </Button>
                          </div>
                        );
                      }

                      // 非图片文件或未上传完成，使用默认显示
                      return originNode;
                    }}
                  >
                    <Button icon={<UploadOutlined />}>选择文件</Button>
                  </Upload>
                </div>
                <div className="text-xs text-gray-500 mt-1">
                  请下载{config.name}，参考示例图填写后上传
                </div>
              </div>
            </div>
          ));
        })()}
      </div>
  );
};

export default RegistrationForm;
