/* 系列课程创建模态框样式 */
.series-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.series-modal {
  background: white;
  border-radius: 16px;
  box-shadow: 0 24px 48px -12px rgba(0, 0, 0, 0.18);
  width: 100%;
  max-width: 480px;
  margin: 1rem;
  max-height: 90vh;
  overflow: hidden;
}

.series-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.75rem 2rem 1rem 2rem;
  border-bottom: 1px solid #f1f5f9;
}

.series-modal-title {
  font-size: 1.125rem;
  font-weight: 500;
  color: #1e293b;
  margin: 0;
}

.series-modal-close {
  background: none;
  border: none;
  color: #94a3b8;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.5rem;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.series-modal-close:hover {
  color: #64748b;
  background-color: #f8fafc;
}

.series-modal-content {
  padding: 2rem 2rem 2.5rem 2rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.75rem;
}

/* 系列课程封面区域 */
.series-cover-section {
  width: 100%;
  display: flex;
  justify-content: center;
}

.series-cover-container {
  position: relative;
}

.series-cover-placeholder {
  width: 320px;
  height: 200px;
  border: 2px dashed #cbd5e1;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f8fafc;
  transition: all 0.2s;
  cursor: pointer;
  position: relative;
}

.series-cover-placeholder:hover {
  border-color: #3b82f6;
  background-color: #eff6ff;
}

.series-cover-input {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  cursor: pointer;
}

.series-cover-text {
  color: #475569;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.series-cover-hint {
  color: #94a3b8;
  font-size: 0.75rem;
}

/* 封面预览样式 */
.series-cover-preview {
  position: relative;
  width: 320px;
  height: 200px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px -4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

.series-cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.series-cover-remove {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
}

.series-cover-remove:hover {
  background: rgba(0, 0, 0, 0.9);
  transform: scale(1.1);
}

/* 表单组 */
.series-form-group {
  width: 100%;
  max-width: 380px;
}

.series-title-input {
  width: 100%;
  padding: 0.875rem 1.25rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 0.875rem;
  color: #1e293b;
  background-color: white;
  transition: all 0.2s;
  text-align: center;
}

.series-title-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.08);
}

.series-title-input::placeholder {
  color: #94a3b8;
  text-align: center;
}

/* 表单操作按钮 */
.series-form-actions {
  width: 100%;
  max-width: 380px;
  display: flex;
  justify-content: center;
}

.series-create-btn {
  padding: 0.75rem 2rem;
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  min-width: 140px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.series-create-btn:hover {
  background-color: #2563eb;
  box-shadow: 0 4px 8px -2px rgba(59, 130, 246, 0.3);
  transform: translateY(-1px);
}

.series-create-btn:active {
  background-color: #1d4ed8;
  transform: translateY(0);
}

.series-create-btn:disabled,
.series-create-btn-loading {
  background-color: #94a3b8 !important;
  cursor: not-allowed !important;
  transform: none !important;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;
}

.series-create-btn:disabled:hover,
.series-create-btn-loading:hover {
  background-color: #94a3b8 !important;
  transform: none !important;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .series-modal {
    margin: 0.75rem;
    max-width: calc(100vw - 1.5rem);
  }

  .series-modal-content {
    padding: 1.5rem 1.5rem 2rem 1.5rem;
    gap: 1.5rem;
  }

  .series-cover-placeholder,
  .series-cover-preview {
    width: 280px;
    height: 175px;
  }

  .series-form-group,
  .series-form-actions {
    max-width: 100%;
  }

  .series-modal-header {
    padding: 1.5rem 1.5rem 0.75rem 1.5rem;
  }
}
