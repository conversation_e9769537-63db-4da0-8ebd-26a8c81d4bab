import React, { useState, useEffect } from 'react';
import { Modal, Form, Input, Button, Tabs, DatePicker, Upload, Spin, notification } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import type { UploadFile } from 'antd/es/upload/interface';
import dayjs from 'dayjs';
import taskApi from '../../../../lib/api/task';
import { Task } from '../../types';

interface EditTaskModalProps {
  visible: boolean;
  task: Task | null;
  onClose: () => void;
  onSuccess: () => void;
  currentClassId?: number;
}

export const EditTaskModal: React.FC<EditTaskModalProps> = ({
  visible,
  task,
  onClose,
  onSuccess,
  currentClassId
}) => {
  const [form] = Form.useForm();
  const [editFileList, setEditFileList] = useState<UploadFile[]>([]);
  const [editUploading, setEditUploading] = useState(false);
  const [selectedWorkIds, setSelectedWorkIds] = useState<number[]>([]);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);

  // 获取文件类型
  const getFileTypeFromUrl = (url: string): string => {
    const extension = url.split('.').pop()?.toLowerCase();
    const imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
    const documentTypes = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt'];
    
    if (imageTypes.includes(extension || '')) {
      return 'image';
    } else if (documentTypes.includes(extension || '')) {
      return 'document';
    }
    return 'unknown';
  };

  // 处理文件上传
  const handleEditUpload = async (file: File): Promise<string> => {
    const formData = new FormData();
    formData.append('file', file);
    
    try {
      setEditUploading(true);
      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      });
      
      if (!response.ok) {
        throw new Error('上传失败');
      }
      
      const result = await response.json();
      return result.url;
    } catch (error) {
      console.error('上传失败:', error);
      notification.error('文件上传失败');
      throw error;
    } finally {
      setEditUploading(false);
    }
  };

  // 移除文件
  const handleEditRemoveFile = (file: UploadFile) => {
    setEditFileList(prev => prev.filter(item => item.uid !== file.uid));
  };

  // 更新任务
  const updateTaskWithParams = async (values: any) => {
    if (!task) {
      notification.error('任务信息不存在，无法更新');
      return;
    }

    setSubmitting(true);

    try {
      // 验证时间逻辑
      if (values.startDate && values.endDate) {
        if (values.startDate.isAfter(values.endDate)) {
          notification.error('开始时间不能晚于结束时间');
          return;
        }
      }

      const params = {
        taskId: task.id,
        taskName: values.taskName?.trim(),
        taskDescription: values.taskDescription?.trim() || '',
        taskType: task.taskType || 1, // 默认任务类型
        priority: task.priority || 1, // 默认优先级
        startDate: values.startDate ? new Date(values.startDate.format('YYYY-MM-DD HH:mm:ss')) : new Date(task.startDate),
        endDate: values.endDate ? new Date(values.endDate.format('YYYY-MM-DD HH:mm:ss')) : new Date(task.endDate),
        taskContent: values.taskDescription?.trim() || task.taskContent || '',
        attachments: editFileList.map(file => file.url || file.response?.url).filter(Boolean),
        workIds: selectedWorkIds.length > 0 ? selectedWorkIds : [],
        selfAssessmentItems: values.selfAssessmentItems ?
          values.selfAssessmentItems.filter((item: string) => item?.trim()) : [],
        classId: currentClassId || task.classId,
        studentIds: task.studentIds || [],
        templateId: task.templateId,
        allowLateSubmission: task.allowLateSubmission || true,
      };

      console.log('更新任务参数:', params);

      const response = await taskApi.updateTask(params);

      if (response.data.code === 200) {
        notification.success({
          message: '更新任务成功',
          description: `任务"${values.taskName}"已成功更新`,
          duration: 3
        });
        handleClose();
        onSuccess();
      } else {
        const errorMsg = response.data.msg || response.data.message || '更新任务失败';
        notification.error({
          message: '更新失败',
          description: errorMsg,
          duration: 4
        });
      }
    } catch (error: any) {
      console.error('更新任务失败:', error);

      let errorMessage = '更新任务失败';
      let errorDescription = '请检查网络连接或稍后重试';

      if (error.response?.data) {
        errorMessage = error.response.data.message || error.response.data.msg || errorMessage;
        if (error.response.status === 403) {
          errorDescription = '您没有权限编辑此任务';
        } else if (error.response.status === 404) {
          errorDescription = '任务不存在或已被删除';
        } else if (error.response.status >= 500) {
          errorDescription = '服务器内部错误，请稍后重试';
        }
      } else if (error.message) {
        errorDescription = error.message;
      }

      notification.error({
        message: errorMessage,
        description: errorDescription,
        duration: 5
      });
    } finally {
      setSubmitting(false);
    }
  };

  // 关闭弹窗
  const handleClose = () => {
    onClose();
    form.resetFields();
    setEditFileList([]);
    setSelectedWorkIds([]);
  };

  // 当task变化时，设置表单值
  useEffect(() => {
    if (visible && task) {
      // 如果有附件，转换为上传组件需要的格式
      if (task.attachments && task.attachments.length > 0) {
        const fileList: UploadFile[] = task.attachments.map((attachment, index) => {
          const url = typeof attachment === 'string' ? attachment : attachment.url;
          const name = url.split('/').pop() || 'file';
          return {
            uid: `${index}`,
            name: name,
            status: 'done',
            url: url,
            type: getFileTypeFromUrl(url),
            size: 0,
          };
        });
        setEditFileList(fileList);
      } else {
        setEditFileList([]);
      }
      
      // 处理参考作品ID
      if (task.workIdsStr) {
        const workIds = task.workIdsStr.split(',').map(id => parseInt(id));
        setSelectedWorkIds(workIds);
      } else {
        setSelectedWorkIds([]);
      }

      // 设置表单默认值
      form.setFieldsValue({
        ...task,
        startDate: task ? dayjs(task.startDate) : undefined,
        endDate: task ? dayjs(task.endDate) : undefined,
      });

      // 查询任务的自评项
      if (task.id) {
        taskApi.getSelfAssessmentItemsByTaskId(task.id).then((response: any) => {
          if (response.data.code === 200 && response.data.data.length > 0) {
            // 设置自评项
            const selfAssessmentItems = response.data.data.map((item: any) => item.content);
            form.setFieldsValue({ selfAssessmentItems });
          }
        }).catch((error) => {
          console.error('获取自评项失败:', error);
        });
      }
    }
  }, [visible, task, form]);

  return (
    <Modal
      title={
        <div className="flex items-center gap-2">
          <span>编辑任务</span>
          {submitting && <Spin size="small" />}
        </div>
      }
      open={visible}
      onCancel={submitting ? undefined : handleClose}
      footer={null}
      width="95vw"
      style={{
        maxWidth: '800px',
        margin: '10vh auto',
        padding: 0,
        top: 0
      }}
      centered
      styles={{
        body: {
          maxHeight: 'calc(80vh - 100px)',
          overflowY: 'auto',
          padding: '24px'
        }
      }}
      maskClosable={!submitting}
      keyboard={!submitting}
      destroyOnClose={true}
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={async (values) => {
          // 检查是否有文件正在上传
          if (editUploading) {
            notification.warning({
              message: '请等待文件上传完成',
              description: '有文件正在上传中，请稍等片刻再提交',
              duration: 3
            });
            return;
          }

          await updateTaskWithParams(values);
        }}
        onFinishFailed={(errorInfo) => {
          console.log('表单验证失败:', errorInfo);
          notification.error({
            message: '表单验证失败',
            description: '请检查并修正表单中的错误信息',
            duration: 3
          });
        }}
      >
        <Tabs
          items={[
            {
              key: 'basic',
              label: '基本信息',
              children: (
                <>
                  <Form.Item
                    label="任务标题"
                    name="taskName"
                    rules={[
                      { required: true, message: '请输入任务标题' },
                      { min: 2, message: '任务标题至少2个字符' },
                      { max: 50, message: '任务标题不能超过50个字符' },
                      { whitespace: true, message: '任务标题不能为空格' }
                    ]}
                  >
                    <Input
                      placeholder="请输入任务标题"
                      showCount
                      maxLength={50}
                      disabled={submitting}
                    />
                  </Form.Item>

                  <Form.Item
                    label="任务描述"
                    name="taskDescription"
                    rules={[
                      { max: 500, message: '任务描述不能超过500个字符' }
                    ]}
                  >
                    <Input.TextArea
                      rows={4}
                      placeholder="请输入任务描述（选填）"
                      showCount
                      maxLength={500}
                      disabled={submitting}
                    />
                  </Form.Item>

                  <div className="grid grid-cols-2 gap-4">
                    <Form.Item
                      label="开始时间"
                      name="startDate"
                      rules={[
                        { required: true, message: '请选择开始时间' },
                        ({ getFieldValue }) => ({
                          validator(_, value) {
                            const endDate = getFieldValue('endDate');
                            if (value && endDate && value.isAfter(endDate)) {
                              return Promise.reject(new Error('开始时间不能晚于结束时间'));
                            }
                            return Promise.resolve();
                          },
                        }),
                      ]}
                    >
                      <DatePicker
                        showTime
                        format="YYYY-MM-DD HH:mm:ss"
                        placeholder="选择开始时间"
                        className="w-full"
                        disabled={submitting}
                        disabledDate={(current) => current && current < dayjs().startOf('day')}
                      />
                    </Form.Item>

                    <Form.Item
                      label="结束时间"
                      name="endDate"
                      rules={[
                        { required: true, message: '请选择结束时间' },
                        ({ getFieldValue }) => ({
                          validator(_, value) {
                            const startDate = getFieldValue('startDate');
                            if (value && startDate && value.isBefore(startDate)) {
                              return Promise.reject(new Error('结束时间不能早于开始时间'));
                            }
                            return Promise.resolve();
                          },
                        }),
                      ]}
                    >
                      <DatePicker
                        showTime
                        format="YYYY-MM-DD HH:mm:ss"
                        placeholder="选择结束时间"
                        className="w-full"
                        disabled={submitting}
                        disabledDate={(current) => current && current < dayjs().startOf('day')}
                      />
                    </Form.Item>
                  </div>

                  <Form.Item label="自评项（可选）" name="selfAssessmentItems">
                    <Form.List name="selfAssessmentItems">
                      {(fields, { add, remove }) => (
                        <>
                          {fields.map(({ key, name, ...restField }) => (
                            <div key={key} className="flex items-center space-x-2 mb-2">
                              <Form.Item
                                {...restField}
                                name={[name]}
                                className="flex-1 mb-0"
                                rules={[
                                  { min: 2, message: '自评项内容至少2个字符' },
                                  { max: 100, message: '自评项内容不能超过100个字符' },
                                  { whitespace: true, message: '自评项内容不能为空格' }
                                ]}
                              >
                                <Input
                                  placeholder="请输入自评项内容"
                                  showCount
                                  maxLength={100}
                                  disabled={submitting}
                                />
                              </Form.Item>
                              <Button
                                type="link"
                                onClick={() => remove(name)}
                                className="text-red-500"
                                disabled={submitting}
                              >
                                删除
                              </Button>
                            </div>
                          ))}
                          <Button
                            type="dashed"
                            onClick={() => add()}
                            className="w-full"
                            disabled={submitting || fields.length >= 10}
                          >
                            {fields.length >= 10 ? '最多添加10个自评项' : '添加自评项'}
                          </Button>
                        </>
                      )}
                    </Form.List>
                  </Form.Item>

                  <Form.Item label="上传资源">
                    <Upload
                      listType="picture-card"
                      fileList={editFileList}
                      customRequest={async ({ file, onSuccess, onError }) => {
                        try {
                          // 文件大小验证
                          if ((file as File).size > 10 * 1024 * 1024) {
                            notification.error('文件大小不能超过10MB');
                            onError?.(new Error('文件大小超限'));
                            return;
                          }

                          const url = await handleEditUpload(file as File);
                          const uploadFile: UploadFile = {
                            uid: Date.now().toString(),
                            name: (file as File).name,
                            status: 'done',
                            url: url,
                            type: getFileTypeFromUrl(url),
                            size: (file as File).size,
                          };
                          setEditFileList(prev => [...prev, uploadFile]);
                          onSuccess?.(url);
                        } catch (error) {
                          onError?.(error as Error);
                        }
                      }}
                      onRemove={handleEditRemoveFile}
                      accept=".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt"
                      multiple
                      disabled={submitting || editFileList.length >= 5}
                      beforeUpload={(file) => {
                        if (editFileList.length >= 5) {
                          notification.warning('最多只能上传5个文件');
                          return false;
                        }
                        return true;
                      }}
                    >
                      {(editUploading || submitting) ? (
                        <div className="flex flex-col items-center">
                          <Spin size="small" />
                          <div className="mt-1">{editUploading ? '上传中...' : '处理中...'}</div>
                        </div>
                      ) : editFileList.length >= 5 ? (
                        <div className="flex flex-col items-center text-gray-400">
                          <div>已达上限</div>
                          <div className="text-xs">最多5个文件</div>
                        </div>
                      ) : (
                        <div className="flex flex-col items-center">
                          <PlusOutlined />
                          <div className="mt-2">上传</div>
                        </div>
                      )}
                    </Upload>
                    <div className="text-gray-400 text-xs mt-1">
                      支持 jpg、png、gif、pdf、doc、docx、xls、xlsx、ppt、pptx、txt 格式
                      <br />单个文件不超过 10MB，最多上传 5 个文件
                    </div>
                  </Form.Item>
                </>
              )
            }
          ]}
        />

        <div className="flex justify-end mt-6 space-x-2 pt-4 border-t border-gray-100">
          <Button
            onClick={handleClose}
            disabled={submitting}
            size="large"
          >
            取消
          </Button>
          <Button
            type="primary"
            htmlType="submit"
            loading={submitting}
            disabled={editUploading}
            size="large"
          >
            {submitting ? '更新中...' : '更新任务'}
          </Button>
        </div>
      </Form>
    </Modal>
  );
};
