2025-08-05 10:39:50.712 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":2536,"hostname":"QQY","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (D:\\logicleap\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (D:\\logicleap\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (D:\\logicleap\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (D:\\logicleap\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (D:\\logicleap\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (D:\\logicleap\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (D:\\logicleap\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (D:\\logicleap\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (D:\\logicleap\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-08-05 10:39:50.814 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":2536,"hostname":"QQY","stack":[null]}
2025-08-05 10:39:50.815 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":2536,"hostname":"QQY","stack":[null]}
2025-08-05 10:39:50.815 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":2536,"hostname":"QQY","stack":[null]}
2025-08-05 10:39:50.816 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":2536,"hostname":"QQY","stack":[null]}
2025-08-05 10:39:50.817 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":2536,"hostname":"QQY","stack":[null]}
2025-08-05 10:39:50.818 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":2536,"hostname":"QQY","stack":[null]}
2025-08-05 10:39:50.818 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":2536,"hostname":"QQY","stack":[null]}
2025-08-05 10:39:50.818 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":2536,"hostname":"QQY","stack":[null]}
2025-08-05 10:39:50.819 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":2536,"hostname":"QQY","stack":[null]}
2025-08-05 10:39:50.819 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":2536,"hostname":"QQY","stack":[null]}
2025-08-05 10:39:50.820 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":2536,"hostname":"QQY","stack":[null]}
2025-08-05 10:39:50.820 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":2536,"hostname":"QQY","stack":[null]}
2025-08-05 10:39:50.820 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":2536,"hostname":"QQY","stack":[null]}
2025-08-05 10:39:50.821 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":2536,"hostname":"QQY","stack":[null]}
2025-08-05 10:39:50.821 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":2536,"hostname":"QQY","stack":[null]}
2025-08-05 10:39:50.821 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":2536,"hostname":"QQY","stack":[null]}
2025-08-05 10:39:50.822 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":2536,"hostname":"QQY","stack":[null]}
2025-08-05 10:39:50.823 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":2536,"hostname":"QQY","stack":[null]}
2025-08-05 10:39:50.823 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":2536,"hostname":"QQY","stack":[null]}
2025-08-05 10:39:50.824 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":2536,"hostname":"QQY","stack":[null]}
2025-08-05 10:39:51.343 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":2536,"hostname":"QQY","stack":[null]}
2025-08-05 10:40:31.098 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":2536,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 10:40:31.099 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":2536,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T02:40:31.099Z"}
2025-08-05 10:40:31.345 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":2536,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 10:40:31.346 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":2536,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T02:40:31.345Z"}
2025-08-05 10:40:32.531 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":2536,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 10:40:32.532 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":2536,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T02:40:32.531Z"}
2025-08-05 10:41:09.944 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":2536,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 10:41:09.945 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":2536,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T02:41:09.944Z"}
2025-08-05 10:41:32.575 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":2536,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 10:41:32.576 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":2536,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T02:41:32.576Z"}
2025-08-05 10:42:32.589 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":2536,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 10:42:32.589 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":2536,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T02:42:32.589Z"}
2025-08-05 10:42:38.062 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":2536,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 10:42:38.063 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":2536,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T02:42:38.062Z"}
2025-08-05 10:42:38.091 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":2536,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 10:42:38.091 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":2536,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T02:42:38.091Z"}
2025-08-05 10:42:39.536 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":2536,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 10:42:39.537 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":2536,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T02:42:39.537Z"}
2025-08-05 10:43:40.249 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":2536,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 10:43:40.249 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":2536,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T02:43:40.249Z"}
2025-08-05 10:44:41.245 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":2536,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 10:44:41.245 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":2536,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T02:44:41.245Z"}
2025-08-05 11:07:08.074 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (D:\\logicleap\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (D:\\logicleap\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (D:\\logicleap\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (D:\\logicleap\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (D:\\logicleap\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (D:\\logicleap\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (D:\\logicleap\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (D:\\logicleap\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (D:\\logicleap\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-08-05 11:07:08.237 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY","stack":[null]}
2025-08-05 11:07:08.237 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY","stack":[null]}
2025-08-05 11:07:08.238 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY","stack":[null]}
2025-08-05 11:07:08.241 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY","stack":[null]}
2025-08-05 11:07:08.241 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY","stack":[null]}
2025-08-05 11:07:08.242 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY","stack":[null]}
2025-08-05 11:07:08.242 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY","stack":[null]}
2025-08-05 11:07:08.243 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY","stack":[null]}
2025-08-05 11:07:08.243 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY","stack":[null]}
2025-08-05 11:07:08.244 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY","stack":[null]}
2025-08-05 11:07:08.244 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY","stack":[null]}
2025-08-05 11:07:08.245 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY","stack":[null]}
2025-08-05 11:07:08.245 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY","stack":[null]}
2025-08-05 11:07:08.246 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY","stack":[null]}
2025-08-05 11:07:08.249 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY","stack":[null]}
2025-08-05 11:07:08.249 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY","stack":[null]}
2025-08-05 11:07:08.250 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY","stack":[null]}
2025-08-05 11:07:08.257 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY","stack":[null]}
2025-08-05 11:07:08.257 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY","stack":[null]}
2025-08-05 11:07:08.258 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY","stack":[null]}
2025-08-05 11:07:20.456 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 11:07:20.456 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T03:07:20.456Z"}
2025-08-05 11:08:20.473 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 11:08:20.473 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T03:08:20.473Z"}
2025-08-05 11:09:21.247 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 11:09:21.247 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T03:09:21.247Z"}
2025-08-05 11:10:22.243 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 11:10:22.244 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T03:10:22.243Z"}
2025-08-05 11:11:23.249 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 11:11:23.249 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T03:11:23.249Z"}
2025-08-05 11:12:24.259 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 11:12:24.260 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T03:12:24.259Z"}
2025-08-05 11:13:25.234 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 11:13:25.234 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T03:13:25.234Z"}
2025-08-05 11:14:26.243 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 11:14:26.243 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T03:14:26.243Z"}
2025-08-05 11:15:27.241 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 11:15:27.242 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T03:15:27.242Z"}
2025-08-05 11:16:28.233 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 11:16:28.233 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T03:16:28.233Z"}
2025-08-05 11:17:29.250 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 11:17:29.251 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T03:17:29.250Z"}
2025-08-05 11:18:30.239 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 11:18:30.240 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T03:18:30.239Z"}
2025-08-05 11:19:31.240 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 11:19:31.241 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T03:19:31.241Z"}
2025-08-05 11:20:32.238 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 11:20:32.239 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T03:20:32.239Z"}
2025-08-05 11:21:33.234 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 11:21:33.234 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T03:21:33.234Z"}
2025-08-05 11:22:34.241 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 11:22:34.241 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T03:22:34.241Z"}
2025-08-05 11:23:35.240 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 11:23:35.241 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T03:23:35.241Z"}
2025-08-05 11:24:36.244 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 11:24:36.244 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T03:24:36.244Z"}
2025-08-05 11:25:37.235 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 11:25:37.235 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T03:25:37.235Z"}
2025-08-05 11:26:38.247 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 11:26:38.248 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T03:26:38.247Z"}
2025-08-05 11:27:39.237 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 11:27:39.237 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T03:27:39.237Z"}
2025-08-05 11:28:40.237 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 11:28:40.237 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T03:28:40.237Z"}
2025-08-05 11:29:41.233 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 11:29:41.233 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T03:29:41.233Z"}
2025-08-05 11:30:42.246 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 11:30:42.246 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T03:30:42.246Z"}
2025-08-05 11:31:43.239 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 11:31:43.239 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T03:31:43.239Z"}
2025-08-05 11:32:44.245 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 11:32:44.246 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T03:32:44.245Z"}
2025-08-05 11:33:45.245 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 11:33:45.245 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T03:33:45.245Z"}
2025-08-05 11:34:46.248 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 11:34:46.249 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T03:34:46.249Z"}
2025-08-05 11:35:47.235 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 11:35:47.235 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T03:35:47.235Z"}
2025-08-05 11:36:48.264 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 11:36:48.265 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T03:36:48.265Z"}
2025-08-05 11:37:49.244 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 11:37:49.244 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T03:37:49.244Z"}
2025-08-05 11:38:50.233 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 11:38:50.233 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T03:38:50.233Z"}
2025-08-05 11:39:51.236 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 11:39:51.236 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T03:39:51.236Z"}
2025-08-05 11:40:52.237 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 11:40:52.237 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T03:40:52.237Z"}
2025-08-05 11:41:53.254 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 11:41:53.254 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T03:41:53.254Z"}
2025-08-05 11:42:54.237 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 11:42:54.238 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T03:42:54.238Z"}
2025-08-05 11:43:55.244 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 11:43:55.245 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T03:43:55.244Z"}
2025-08-05 11:44:56.241 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 11:44:56.241 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T03:44:56.241Z"}
2025-08-05 11:45:57.238 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 11:45:57.239 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T03:45:57.238Z"}
2025-08-05 11:46:58.262 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 11:46:58.263 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T03:46:58.262Z"}
2025-08-05 11:47:59.253 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 11:47:59.254 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T03:47:59.254Z"}
2025-08-05 11:49:00.245 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 11:49:00.245 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T03:49:00.245Z"}
2025-08-05 11:50:01.241 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 11:50:01.242 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T03:50:01.241Z"}
2025-08-05 11:51:02.242 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 11:51:02.242 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T03:51:02.242Z"}
2025-08-05 11:52:03.238 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 11:52:03.238 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T03:52:03.238Z"}
2025-08-05 11:53:04.241 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 11:53:04.241 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T03:53:04.241Z"}
2025-08-05 11:54:05.243 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 11:54:05.244 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T03:54:05.243Z"}
2025-08-05 11:55:06.237 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 11:55:06.237 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T03:55:06.237Z"}
2025-08-05 11:56:07.267 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 11:56:07.268 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T03:56:07.268Z"}
2025-08-05 11:57:08.242 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 11:57:08.242 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T03:57:08.242Z"}
2025-08-05 11:58:09.247 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 11:58:09.247 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T03:58:09.247Z"}
2025-08-05 11:59:10.245 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 11:59:10.245 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T03:59:10.245Z"}
2025-08-05 12:00:11.243 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 12:00:11.243 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T04:00:11.243Z"}
2025-08-05 12:01:12.237 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 12:01:12.237 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T04:01:12.237Z"}
2025-08-05 12:02:13.237 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 12:02:13.238 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T04:02:13.237Z"}
2025-08-05 12:03:14.245 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 12:03:14.245 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T04:03:14.245Z"}
2025-08-05 12:04:15.246 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 12:04:15.247 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T04:04:15.246Z"}
2025-08-05 12:05:16.243 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 12:05:16.244 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T04:05:16.244Z"}
2025-08-05 12:06:17.237 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 12:06:17.237 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T04:06:17.237Z"}
2025-08-05 12:07:18.231 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 12:07:18.232 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T04:07:18.231Z"}
2025-08-05 12:08:19.240 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 12:08:19.240 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T04:08:19.240Z"}
2025-08-05 12:09:20.240 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 12:09:20.240 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T04:09:20.240Z"}
2025-08-05 12:10:21.238 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 12:10:21.239 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T04:10:21.239Z"}
2025-08-05 12:11:22.237 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 12:11:22.237 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T04:11:22.237Z"}
2025-08-05 12:12:23.235 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 12:12:23.235 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T04:12:23.235Z"}
2025-08-05 12:13:24.239 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 12:13:24.240 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T04:13:24.240Z"}
2025-08-05 12:14:25.243 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 12:14:25.243 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T04:14:25.243Z"}
2025-08-05 12:15:26.243 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 12:15:26.243 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T04:15:26.243Z"}
2025-08-05 12:16:27.239 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 12:16:27.240 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T04:16:27.240Z"}
2025-08-05 12:17:28.245 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 12:17:28.245 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T04:17:28.245Z"}
2025-08-05 12:18:29.233 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 12:18:29.233 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T04:18:29.233Z"}
2025-08-05 12:19:30.249 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 12:19:30.249 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T04:19:30.249Z"}
2025-08-05 12:20:31.246 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 12:20:31.246 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T04:20:31.246Z"}
2025-08-05 12:21:32.247 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 12:21:32.248 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T04:21:32.247Z"}
2025-08-05 12:22:33.241 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 12:22:33.241 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T04:22:33.241Z"}
2025-08-05 12:23:34.232 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 12:23:34.232 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T04:23:34.232Z"}
2025-08-05 12:24:35.236 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 12:24:35.237 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T04:24:35.236Z"}
2025-08-05 12:25:36.244 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 12:25:36.244 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T04:25:36.244Z"}
2025-08-05 12:26:37.237 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 12:26:37.237 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T04:26:37.237Z"}
2025-08-05 12:27:38.250 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 12:27:38.250 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T04:27:38.250Z"}
2025-08-05 12:28:39.251 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 12:28:39.251 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T04:28:39.251Z"}
2025-08-05 12:29:40.242 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 12:29:40.242 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T04:29:40.242Z"}
2025-08-05 12:30:41.246 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 12:30:41.246 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T04:30:41.246Z"}
2025-08-05 12:31:42.248 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 12:31:42.249 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T04:31:42.248Z"}
2025-08-05 12:32:43.254 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 12:32:43.254 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T04:32:43.254Z"}
2025-08-05 12:33:44.250 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 12:33:44.250 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T04:33:44.250Z"}
2025-08-05 12:34:45.244 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 12:34:45.244 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T04:34:45.244Z"}
2025-08-05 12:35:46.234 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 12:35:46.234 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T04:35:46.234Z"}
2025-08-05 12:36:47.248 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 12:36:47.248 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T04:36:47.248Z"}
2025-08-05 12:37:48.245 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 12:37:48.245 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T04:37:48.245Z"}
2025-08-05 12:38:49.247 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 12:38:49.247 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T04:38:49.247Z"}
2025-08-05 12:39:50.236 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 12:39:50.236 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T04:39:50.236Z"}
2025-08-05 12:40:51.241 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 12:40:51.241 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T04:40:51.241Z"}
2025-08-05 12:41:52.245 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 12:41:52.246 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T04:41:52.246Z"}
2025-08-05 12:42:53.236 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 12:42:53.236 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T04:42:53.236Z"}
2025-08-05 12:43:54.245 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 12:43:54.245 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T04:43:54.245Z"}
2025-08-05 12:44:55.246 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 12:44:55.246 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T04:44:55.246Z"}
2025-08-05 12:45:56.237 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 12:45:56.237 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T04:45:56.237Z"}
2025-08-05 12:46:57.236 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 12:46:57.237 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T04:46:57.237Z"}
2025-08-05 12:47:58.233 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 12:47:58.233 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T04:47:58.233Z"}
2025-08-05 12:48:59.234 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 12:48:59.234 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T04:48:59.234Z"}
2025-08-05 12:50:00.235 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 12:50:00.235 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T04:50:00.235Z"}
2025-08-05 12:51:01.244 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 12:51:01.245 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T04:51:01.245Z"}
2025-08-05 12:52:02.239 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 12:52:02.239 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T04:52:02.239Z"}
2025-08-05 12:53:03.242 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 12:53:03.242 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T04:53:03.242Z"}
2025-08-05 12:54:04.236 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 12:54:04.237 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T04:54:04.236Z"}
2025-08-05 12:55:05.240 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 12:55:05.240 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T04:55:05.240Z"}
2025-08-05 12:56:06.240 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 12:56:06.240 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T04:56:06.240Z"}
2025-08-05 12:57:07.234 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 12:57:07.234 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T04:57:07.234Z"}
2025-08-05 12:58:08.239 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 12:58:08.239 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T04:58:08.239Z"}
2025-08-05 12:59:09.245 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 12:59:09.245 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T04:59:09.245Z"}
2025-08-05 13:00:10.239 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 13:00:10.239 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T05:00:10.239Z"}
2025-08-05 13:01:11.247 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 13:01:11.247 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T05:01:11.247Z"}
2025-08-05 13:02:12.232 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 13:02:12.234 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T05:02:12.233Z"}
2025-08-05 13:03:13.243 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 13:03:13.243 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T05:03:13.243Z"}
2025-08-05 13:04:14.238 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 13:04:14.238 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T05:04:14.238Z"}
2025-08-05 13:05:15.290 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 13:05:15.290 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T05:05:15.290Z"}
2025-08-05 13:06:16.249 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 13:06:16.249 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T05:06:16.249Z"}
2025-08-05 13:07:17.243 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 13:07:17.244 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T05:07:17.243Z"}
2025-08-05 13:08:18.241 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 13:08:18.241 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T05:08:18.241Z"}
2025-08-05 13:09:19.239 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 13:09:19.240 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T05:09:19.239Z"}
2025-08-05 13:10:20.237 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 13:10:20.237 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T05:10:20.237Z"}
2025-08-05 13:11:21.231 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 13:11:21.232 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T05:11:21.232Z"}
2025-08-05 13:12:22.242 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 13:12:22.242 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T05:12:22.242Z"}
2025-08-05 13:13:23.241 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 13:13:23.241 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T05:13:23.241Z"}
2025-08-05 13:14:24.243 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 13:14:24.243 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T05:14:24.243Z"}
2025-08-05 13:15:25.236 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 13:15:25.236 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T05:15:25.236Z"}
2025-08-05 13:16:26.241 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 13:16:26.241 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T05:16:26.241Z"}
2025-08-05 13:17:27.251 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 13:17:27.251 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T05:17:27.251Z"}
2025-08-05 13:18:28.233 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 13:18:28.233 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T05:18:28.233Z"}
2025-08-05 13:19:29.240 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 13:19:29.241 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T05:19:29.240Z"}
2025-08-05 13:20:30.251 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 13:20:30.252 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T05:20:30.251Z"}
2025-08-05 13:21:31.245 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 13:21:31.245 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T05:21:31.245Z"}
2025-08-05 13:22:32.237 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 13:22:32.237 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T05:22:32.237Z"}
2025-08-05 13:23:33.236 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 13:23:33.236 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T05:23:33.236Z"}
2025-08-05 13:24:34.234 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 13:24:34.234 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T05:24:34.234Z"}
2025-08-05 13:25:35.233 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 13:25:35.234 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T05:25:35.234Z"}
2025-08-05 13:26:36.244 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 13:26:36.244 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T05:26:36.244Z"}
2025-08-05 13:27:37.234 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 13:27:37.234 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T05:27:37.234Z"}
2025-08-05 13:28:38.245 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 13:28:38.246 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T05:28:38.246Z"}
2025-08-05 13:29:39.235 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 13:29:39.236 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T05:29:39.236Z"}
2025-08-05 13:30:40.237 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 13:30:40.237 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T05:30:40.237Z"}
2025-08-05 13:31:41.234 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 13:31:41.234 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T05:31:41.234Z"}
2025-08-05 13:32:42.246 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 13:32:42.246 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T05:32:42.246Z"}
2025-08-05 13:33:43.245 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 13:33:43.245 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T05:33:43.245Z"}
2025-08-05 13:34:44.235 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 13:34:44.235 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T05:34:44.235Z"}
2025-08-05 13:35:45.243 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 13:35:45.243 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T05:35:45.243Z"}
2025-08-05 13:36:46.238 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 13:36:46.238 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T05:36:46.238Z"}
2025-08-05 13:37:47.239 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 13:37:47.239 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T05:37:47.239Z"}
2025-08-05 13:38:48.257 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 13:38:48.257 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T05:38:48.257Z"}
2025-08-05 13:39:49.240 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 13:39:49.240 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T05:39:49.240Z"}
2025-08-05 13:40:50.238 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 13:40:50.239 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T05:40:50.238Z"}
2025-08-05 13:41:51.237 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 13:41:51.237 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T05:41:51.237Z"}
2025-08-05 13:42:52.246 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 13:42:52.246 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T05:42:52.246Z"}
2025-08-05 13:43:53.232 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 13:43:53.232 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T05:43:53.232Z"}
2025-08-05 13:44:54.238 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 13:44:54.238 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T05:44:54.238Z"}
2025-08-05 13:45:55.249 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 13:45:55.249 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T05:45:55.249Z"}
2025-08-05 13:46:56.286 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 13:46:56.286 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T05:46:56.286Z"}
2025-08-05 13:47:57.247 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 13:47:57.247 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T05:47:57.247Z"}
2025-08-05 13:48:58.238 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 13:48:58.238 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T05:48:58.238Z"}
2025-08-05 13:49:59.241 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 13:49:59.242 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T05:49:59.241Z"}
2025-08-05 13:51:00.249 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 13:51:00.252 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T05:51:00.249Z"}
2025-08-05 13:52:01.237 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 13:52:01.237 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T05:52:01.237Z"}
2025-08-05 13:53:02.242 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 13:53:02.242 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T05:53:02.242Z"}
2025-08-05 13:54:03.232 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 13:54:03.232 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T05:54:03.232Z"}
2025-08-05 13:55:04.242 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 13:55:04.242 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T05:55:04.242Z"}
2025-08-05 13:56:05.248 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 13:56:05.248 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T05:56:05.248Z"}
2025-08-05 13:57:06.249 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 13:57:06.249 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T05:57:06.249Z"}
2025-08-05 13:58:07.245 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 13:58:07.246 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T05:58:07.245Z"}
2025-08-05 13:59:08.245 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 13:59:08.245 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T05:59:08.245Z"}
2025-08-05 14:00:09.256 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 14:00:09.256 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T06:00:09.256Z"}
2025-08-05 14:01:10.238 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 14:01:10.238 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T06:01:10.238Z"}
2025-08-05 14:02:11.234 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 14:02:11.234 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T06:02:11.234Z"}
2025-08-05 14:03:12.234 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 14:03:12.234 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T06:03:12.234Z"}
2025-08-05 14:04:13.255 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 14:04:13.255 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T06:04:13.255Z"}
2025-08-05 14:05:14.243 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 14:05:14.243 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T06:05:14.243Z"}
2025-08-05 14:06:15.232 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 14:06:15.232 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T06:06:15.232Z"}
2025-08-05 14:07:16.246 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 14:07:16.246 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T06:07:16.246Z"}
2025-08-05 14:08:17.244 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 14:08:17.244 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T06:08:17.244Z"}
2025-08-05 14:09:18.239 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 14:09:18.239 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T06:09:18.239Z"}
2025-08-05 14:10:19.235 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 14:10:19.235 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T06:10:19.235Z"}
2025-08-05 14:11:20.234 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 14:11:20.235 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T06:11:20.234Z"}
2025-08-05 14:12:21.242 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 14:12:21.244 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T06:12:21.243Z"}
2025-08-05 14:13:22.246 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 14:13:22.246 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T06:13:22.246Z"}
2025-08-05 14:14:23.236 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 14:14:23.236 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T06:14:23.236Z"}
2025-08-05 14:15:24.240 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 14:15:24.241 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T06:15:24.241Z"}
2025-08-05 14:16:25.237 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 14:16:25.237 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":5620,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T06:16:25.237Z"}
2025-08-05 14:18:24.982 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":30160,"hostname":"QQY","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (D:\\logicleap\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (D:\\logicleap\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (D:\\logicleap\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (D:\\logicleap\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (D:\\logicleap\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (D:\\logicleap\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (D:\\logicleap\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (D:\\logicleap\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (D:\\logicleap\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-08-05 14:18:25.008 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":30160,"hostname":"QQY","stack":[null]}
2025-08-05 14:18:25.009 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":30160,"hostname":"QQY","stack":[null]}
2025-08-05 14:18:25.009 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":30160,"hostname":"QQY","stack":[null]}
2025-08-05 14:18:25.009 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":30160,"hostname":"QQY","stack":[null]}
2025-08-05 14:18:25.009 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":30160,"hostname":"QQY","stack":[null]}
2025-08-05 14:18:25.009 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":30160,"hostname":"QQY","stack":[null]}
2025-08-05 14:18:25.009 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":30160,"hostname":"QQY","stack":[null]}
2025-08-05 14:18:25.010 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":30160,"hostname":"QQY","stack":[null]}
2025-08-05 14:18:25.010 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":30160,"hostname":"QQY","stack":[null]}
2025-08-05 14:18:25.010 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":30160,"hostname":"QQY","stack":[null]}
2025-08-05 14:18:25.010 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":30160,"hostname":"QQY","stack":[null]}
2025-08-05 14:18:25.010 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":30160,"hostname":"QQY","stack":[null]}
2025-08-05 14:18:25.010 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":30160,"hostname":"QQY","stack":[null]}
2025-08-05 14:18:25.011 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":30160,"hostname":"QQY","stack":[null]}
2025-08-05 14:18:25.011 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":30160,"hostname":"QQY","stack":[null]}
2025-08-05 14:18:25.011 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":30160,"hostname":"QQY","stack":[null]}
2025-08-05 14:18:25.011 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":30160,"hostname":"QQY","stack":[null]}
2025-08-05 14:18:25.011 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":30160,"hostname":"QQY","stack":[null]}
2025-08-05 14:18:25.012 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":30160,"hostname":"QQY","stack":[null]}
2025-08-05 14:18:25.012 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":30160,"hostname":"QQY","stack":[null]}
2025-08-05 14:18:29.252 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":30160,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 14:18:29.253 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":30160,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T06:18:29.253Z"}
2025-08-05 14:19:30.252 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":30160,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 14:19:30.252 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":30160,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T06:19:30.252Z"}
2025-08-05 14:20:31.244 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":30160,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 14:20:31.244 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":30160,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T06:20:31.244Z"}
2025-08-05 14:21:32.239 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":30160,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 14:21:32.239 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":30160,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T06:21:32.239Z"}
2025-08-05 14:22:33.249 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":30160,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 14:22:33.251 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":30160,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T06:22:33.250Z"}
2025-08-05 14:23:34.249 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":30160,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 14:23:34.250 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":30160,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T06:23:34.250Z"}
2025-08-05 14:24:35.242 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":30160,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 14:24:35.243 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":30160,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T06:24:35.243Z"}
2025-08-05 14:24:56.070 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":31380,"hostname":"QQY","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (D:\\logicleap\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (D:\\logicleap\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (D:\\logicleap\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (D:\\logicleap\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (D:\\logicleap\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (D:\\logicleap\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (D:\\logicleap\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (D:\\logicleap\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (D:\\logicleap\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-08-05 14:24:56.100 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":31380,"hostname":"QQY","stack":[null]}
2025-08-05 14:24:56.100 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":31380,"hostname":"QQY","stack":[null]}
2025-08-05 14:24:56.100 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":31380,"hostname":"QQY","stack":[null]}
2025-08-05 14:24:56.100 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":31380,"hostname":"QQY","stack":[null]}
2025-08-05 14:24:56.100 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":31380,"hostname":"QQY","stack":[null]}
2025-08-05 14:24:56.101 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":31380,"hostname":"QQY","stack":[null]}
2025-08-05 14:24:56.101 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":31380,"hostname":"QQY","stack":[null]}
2025-08-05 14:24:56.101 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":31380,"hostname":"QQY","stack":[null]}
2025-08-05 14:24:56.101 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":31380,"hostname":"QQY","stack":[null]}
2025-08-05 14:24:56.101 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":31380,"hostname":"QQY","stack":[null]}
2025-08-05 14:24:56.102 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":31380,"hostname":"QQY","stack":[null]}
2025-08-05 14:24:56.102 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":31380,"hostname":"QQY","stack":[null]}
2025-08-05 14:24:56.102 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":31380,"hostname":"QQY","stack":[null]}
2025-08-05 14:24:56.102 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":31380,"hostname":"QQY","stack":[null]}
2025-08-05 14:24:56.102 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":31380,"hostname":"QQY","stack":[null]}
2025-08-05 14:24:56.102 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":31380,"hostname":"QQY","stack":[null]}
2025-08-05 14:24:56.102 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":31380,"hostname":"QQY","stack":[null]}
2025-08-05 14:24:56.102 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":31380,"hostname":"QQY","stack":[null]}
2025-08-05 14:24:56.102 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":31380,"hostname":"QQY","stack":[null]}
2025-08-05 14:24:56.102 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":31380,"hostname":"QQY","stack":[null]}
2025-08-05 14:25:36.296 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":31380,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-05 14:25:36.296 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":31380,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-05T06:25:36.296Z"}
2025-08-05 14:25:51.075 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":16644,"hostname":"QQY","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (D:\\logicleap\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (D:\\logicleap\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (D:\\logicleap\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (D:\\logicleap\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (D:\\logicleap\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (D:\\logicleap\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (D:\\logicleap\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (D:\\logicleap\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (D:\\logicleap\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-08-05 14:25:51.122 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":16644,"hostname":"QQY","stack":[null]}
2025-08-05 14:25:51.122 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":16644,"hostname":"QQY","stack":[null]}
2025-08-05 14:25:51.123 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":16644,"hostname":"QQY","stack":[null]}
2025-08-05 14:25:51.123 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":16644,"hostname":"QQY","stack":[null]}
2025-08-05 14:25:51.123 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":16644,"hostname":"QQY","stack":[null]}
2025-08-05 14:25:51.123 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":16644,"hostname":"QQY","stack":[null]}
2025-08-05 14:25:51.124 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":16644,"hostname":"QQY","stack":[null]}
2025-08-05 14:25:51.124 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":16644,"hostname":"QQY","stack":[null]}
2025-08-05 14:25:51.124 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":16644,"hostname":"QQY","stack":[null]}
2025-08-05 14:25:51.125 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":16644,"hostname":"QQY","stack":[null]}
2025-08-05 14:25:51.125 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":16644,"hostname":"QQY","stack":[null]}
2025-08-05 14:25:51.126 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":16644,"hostname":"QQY","stack":[null]}
2025-08-05 14:25:51.126 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":16644,"hostname":"QQY","stack":[null]}
2025-08-05 14:25:51.126 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":16644,"hostname":"QQY","stack":[null]}
2025-08-05 14:25:51.126 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":16644,"hostname":"QQY","stack":[null]}
2025-08-05 14:25:51.126 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":16644,"hostname":"QQY","stack":[null]}
2025-08-05 14:25:51.126 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":16644,"hostname":"QQY","stack":[null]}
2025-08-05 14:25:51.126 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":16644,"hostname":"QQY","stack":[null]}
2025-08-05 14:25:51.127 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":16644,"hostname":"QQY","stack":[null]}
2025-08-05 14:25:51.127 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":16644,"hostname":"QQY","stack":[null]}
