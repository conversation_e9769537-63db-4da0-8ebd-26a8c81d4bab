<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>删除确认弹窗测试</title>
    <style>
        /* 删除确认弹窗样式 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
        }

        .delete-confirm-modal {
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            width: 400px;
            max-width: 90vw;
        }

        .delete-confirm-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 20px 24px 16px;
            border-bottom: 1px solid #f0f0f0;
        }

        .delete-confirm-header h3 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }

        .delete-confirm-header .close-btn {
            background: none;
            border: none;
            cursor: pointer;
            padding: 4px;
            color: #999;
            transition: color 0.2s;
        }

        .delete-confirm-header .close-btn:hover {
            color: #666;
        }

        .delete-confirm-content {
            padding: 20px 24px;
        }

        .delete-confirm-content p {
            margin: 0;
            color: #666;
            line-height: 1.5;
        }

        .delete-confirm-footer {
            display: flex;
            justify-content: flex-end;
            gap: 12px;
            padding: 16px 24px 20px;
            border-top: 1px solid #f0f0f0;
        }

        .delete-confirm-footer .cancel-btn {
            padding: 8px 16px;
            border: 1px solid #d9d9d9;
            background: white;
            color: #666;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .delete-confirm-footer .cancel-btn:hover {
            border-color: #40a9ff;
            color: #40a9ff;
        }

        .delete-confirm-footer .confirm-btn {
            padding: 8px 16px;
            border: 1px solid #ff4d4f;
            background: #ff4d4f;
            color: white;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .delete-confirm-footer .confirm-btn:hover {
            background: #ff7875;
            border-color: #ff7875;
        }

        /* 禁用状态样式 */
        .delete-confirm-header .close-btn:disabled,
        .delete-confirm-footer .cancel-btn:disabled,
        .delete-confirm-footer .confirm-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            pointer-events: none;
        }

        /* 删除加载动画 */
        .delete-loading {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 16px;
        }

        .loading-spinner {
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #ff4d4f;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 测试按钮样式 */
        .test-btn {
            padding: 12px 24px;
            background: #1890ff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 20px;
        }

        .test-btn:hover {
            background: #40a9ff;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
    </style>
</head>
<body>
    <h1>删除确认弹窗测试</h1>
    <button class="test-btn" onclick="showDeleteModal()">测试删除弹窗</button>

    <!-- 删除确认弹窗 -->
    <div id="deleteModal" class="modal-overlay" style="display: none;">
        <div class="delete-confirm-modal">
            <div class="delete-confirm-header">
                <h3>确认删除</h3>
                <button class="close-btn" onclick="cancelDelete()">
                    ✕
                </button>
            </div>
            <div class="delete-confirm-content">
                <p id="deleteMessage">确定要删除这个课程吗？删除后无法恢复。</p>
                <div id="deleteLoading" class="delete-loading" style="display: none;">
                    <div class="loading-spinner"></div>
                </div>
            </div>
            <div class="delete-confirm-footer">
                <button class="cancel-btn" onclick="cancelDelete()">
                    取消
                </button>
                <button id="confirmBtn" class="confirm-btn" onclick="confirmDelete()">
                    确认删除
                </button>
            </div>
        </div>
    </div>

    <script>
        let isDeleting = false;

        function showDeleteModal() {
            document.getElementById('deleteModal').style.display = 'flex';
        }

        function cancelDelete() {
            if (isDeleting) return;
            document.getElementById('deleteModal').style.display = 'none';
            resetModal();
        }

        function confirmDelete() {
            if (isDeleting) return;
            
            isDeleting = true;
            
            // 更新UI状态
            document.getElementById('deleteMessage').textContent = '正在删除课程，请稍候...';
            document.getElementById('deleteLoading').style.display = 'flex';
            document.getElementById('confirmBtn').textContent = '正在删除...';
            
            // 禁用所有按钮
            const buttons = document.querySelectorAll('.delete-confirm-modal button');
            buttons.forEach(btn => btn.disabled = true);
            
            // 模拟删除操作
            setTimeout(() => {
                // 删除成功
                document.getElementById('deleteModal').style.display = 'none';
                alert('删除成功！课程已成功删除');
                resetModal();
            }, 2000);
        }

        function resetModal() {
            isDeleting = false;
            document.getElementById('deleteMessage').textContent = '确定要删除这个课程吗？删除后无法恢复。';
            document.getElementById('deleteLoading').style.display = 'none';
            document.getElementById('confirmBtn').textContent = '确认删除';
            
            // 启用所有按钮
            const buttons = document.querySelectorAll('.delete-confirm-modal button');
            buttons.forEach(btn => btn.disabled = false);
        }
    </script>
</body>
</html>
