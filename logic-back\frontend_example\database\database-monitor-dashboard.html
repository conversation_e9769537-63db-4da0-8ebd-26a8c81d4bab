<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库监控管理面板</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        .dashboard {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        .card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid #1677ff;
        }
        .card h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #f0f0f0;
            padding-bottom: 10px;
        }
        .status-card {
            text-align: center;
            padding: 30px;
        }
        .status-indicator {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            margin: 0 auto 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2em;
            color: white;
            font-weight: bold;
        }
        .status-enabled {
            background: linear-gradient(135deg, #52c41a, #389e0d);
        }
        .status-disabled {
            background: linear-gradient(135deg, #ff4d4f, #cf1322);
        }
        .status-lightweight {
            background: linear-gradient(135deg, #faad14, #d48806);
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .metric-item {
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        .metric-value {
            font-size: 1.8em;
            font-weight: bold;
            color: #1677ff;
            margin-bottom: 5px;
        }
        .metric-label {
            font-size: 0.9em;
            color: #666;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input, select, textarea {
            width: 100%;
            padding: 12px 16px;
            box-sizing: border-box;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
            background-color: #fff;
        }
        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #1677ff;
            box-shadow: 0 0 0 3px rgba(22, 119, 255, 0.1);
            background-color: #fafbfc;
        }
        input:hover, select:hover, textarea:hover {
            border-color: #b8c5d1;
        }

        /* 数字输入框特殊样式 */
        input[type="number"] {
            text-align: center;
            font-weight: 500;
            color: #1677ff;
        }
        button {
            background-color: #1677ff;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        button:hover {
            background-color: #0e5edb;
            transform: translateY(-1px);
        }
        button.danger {
            background-color: #ff4d4f;
        }
        button.danger:hover {
            background-color: #cf1322;
        }
        button.success {
            background-color: #52c41a;
        }
        button.success:hover {
            background-color: #389e0d;
        }
        button.warning {
            background-color: #faad14;
        }
        button.warning:hover {
            background-color: #d48806;
        }
        button:disabled {
            background-color: #d9d9d9;
            cursor: not-allowed;
            transform: none;
        }
        .log-container {
            background: #1e1e1e;
            color: #f0f0f0;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 15px;
            border: 1px solid #333;
        }
        .log-entry {
            margin-bottom: 8px;
            padding: 5px;
            border-radius: 3px;
        }
        .log-info {
            color: #52c41a;
        }
        .log-warning {
            color: #faad14;
        }
        .log-error {
            color: #ff4d4f;
        }
        .config-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        /* 美化开关按钮 */
        .switch-container {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 0;
        }

        .switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 24px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        input:checked + .slider {
            background-color: #1677ff;
        }

        input:focus + .slider {
            box-shadow: 0 0 1px #1677ff;
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }

        .switch-label {
            font-weight: 500;
            color: #333;
            flex: 1;
        }

        /* 美化滑块 */
        .range-container {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        input[type="range"] {
            flex: 1;
            height: 6px;
            border-radius: 3px;
            background: #ddd;
            outline: none;
            -webkit-appearance: none;
            appearance: none;
        }

        input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #1677ff;
            cursor: pointer;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        input[type="range"]::-moz-range-thumb {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #1677ff;
            cursor: pointer;
            border: none;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .range-display {
            background: #1677ff;
            color: white;
            padding: 4px 12px;
            border-radius: 12px;
            font-weight: bold;
            font-size: 12px;
            min-width: 50px;
            text-align: center;
        }
        .quick-actions {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 20px;
        }
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }
        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 6px;
            color: white;
            font-weight: 500;
            z-index: 1000;
            transform: translateX(400px);
            transition: transform 0.3s ease;
        }
        .toast.show {
            transform: translateX(0);
        }
        .toast.success {
            background-color: #52c41a;
        }
        .toast.error {
            background-color: #ff4d4f;
        }
        .toast.warning {
            background-color: #faad14;
        }
        .slow-query-item {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 10px;
        }
        .slow-query-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .execution-time {
            background: #ff4d4f;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: bold;
            font-size: 12px;
        }
        .query-text {
            background: #1e1e1e;
            color: #f0f0f0;
            padding: 10px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            word-break: break-all;
            margin-top: 10px;
        }
        @media (max-width: 768px) {
            .dashboard {
                grid-template-columns: 1fr;
            }
            .config-section {
                grid-template-columns: 1fr;
            }
            .quick-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔍 数据库监控管理面板</h1>
        <p>实时监控数据库性能，智能管理慢查询</p>
    </div>

    <div class="dashboard">
        <!-- 监控状态卡片 -->
        <div class="card status-card">
            <h3>监控状态</h3>
            <div id="statusIndicator" class="status-indicator status-disabled">
                ❌
            </div>
            <div id="statusText">监控已禁用</div>
            <div style="margin-top: 15px;">
                <button id="toggleMonitoring" class="success">启用监控</button>
                <button id="toggleLightweight" class="warning">轻量级模式</button>
            </div>
        </div>

        <!-- 性能指标卡片 -->
        <div class="card">
            <h3>性能指标</h3>
            <div class="metrics-grid">
                <div class="metric-item">
                    <div id="totalQueries" class="metric-value">0</div>
                    <div class="metric-label">总查询数</div>
                </div>
                <div class="metric-item">
                    <div id="slowQueries" class="metric-value">0</div>
                    <div class="metric-label">慢查询数</div>
                </div>
                <div class="metric-item">
                    <div id="avgTime" class="metric-value">0ms</div>
                    <div class="metric-label">平均耗时</div>
                </div>
                <div class="metric-item">
                    <div id="activeQueries" class="metric-value">0</div>
                    <div class="metric-label">活跃查询</div>
                </div>
            </div>
            <button onclick="refreshMetrics()" style="margin-top: 15px;">🔄 刷新指标</button>
        </div>
    </div>

    <!-- 快捷操作 -->
    <div class="card">
        <h3>快捷操作</h3>
        <div class="quick-actions">
            <button onclick="testSlowQuery()" class="warning">🐌 测试慢查询</button>
            <button onclick="testBatchQueries()" class="">📊 测试批量查询</button>
            <button onclick="clearSlowQueries()" class="danger">🗑️ 清空慢查询</button>
            <button onclick="resetMetrics()" class="danger">🔄 重置指标</button>
            <button onclick="exportConfig()" class="">📤 导出配置</button>
            <button onclick="loadPresetConfig('production')" class="success">🚀 生产环境配置</button>
            <button onclick="loadPresetConfig('development')" class="">🔧 开发环境配置</button>
        </div>
    </div>

    <!-- 配置管理 -->
    <div class="card">
        <h3>配置管理</h3>
        <div class="config-section">
            <div class="form-group">
                <label for="slowQueryThreshold">慢查询阈值 (毫秒)</label>
                <input type="number" id="slowQueryThreshold" value="1000" min="100" max="10000">
            </div>
            <div class="form-group">
                <label for="samplingRate">采样率</label>
                <div class="range-container">
                    <input type="range" id="samplingRate" min="1" max="100" value="100" oninput="updateSamplingDisplay()">
                    <span id="samplingDisplay" class="range-display">100%</span>
                </div>
            </div>
            <div class="form-group">
                <label for="maxSlowQueryRecords">最大慢查询记录数</label>
                <input type="number" id="maxSlowQueryRecords" value="100" min="10" max="1000">
            </div>
            <div class="form-group">
                <div class="switch-container">
                    <span class="switch-label">启用慢查询日志</span>
                    <label class="switch">
                        <input type="checkbox" id="enableSlowQueryLogging" checked>
                        <span class="slider"></span>
                    </label>
                </div>
            </div>
            <div class="form-group">
                <div class="switch-container">
                    <span class="switch-label">启用查询指标</span>
                    <label class="switch">
                        <input type="checkbox" id="enableQueryMetrics" checked>
                        <span class="slider"></span>
                    </label>
                </div>
            </div>
            <div class="form-group">
                <div class="switch-container">
                    <span class="switch-label">启用调用栈捕获</span>
                    <label class="switch">
                        <input type="checkbox" id="enableStackTrace" checked>
                        <span class="slider"></span>
                    </label>
                </div>
            </div>
            <div class="form-group">
                <div class="switch-container">
                    <span class="switch-label">异步处理慢查询</span>
                    <label class="switch">
                        <input type="checkbox" id="asyncSlowQueryProcessing" checked>
                        <span class="slider"></span>
                    </label>
                </div>
            </div>
        </div>
        <button onclick="updateConfig()" class="success">💾 保存配置</button>
        <button onclick="loadConfig()" class="">🔄 重新加载配置</button>
    </div>

    <!-- 慢查询记录 -->
    <div class="card">
        <h3>慢查询记录</h3>
        <div style="margin-bottom: 15px;">
            <button onclick="loadSlowQueries()" class="">🔄 刷新记录</button>
            <button onclick="loadSlowQueries(10)" class="">📋 最近10条</button>
            <button onclick="loadSlowQueries(50)" class="">📋 最近50条</button>
        </div>
        <div id="slowQueriesContainer">
            <p style="text-align: center; color: #666;">点击"刷新记录"加载慢查询数据</p>
        </div>
    </div>

    <!-- 操作日志 -->
    <div class="card">
        <h3>操作日志</h3>
        <button onclick="clearLogs()" class="danger" style="margin-bottom: 10px;">清空日志</button>
        <div id="logContainer" class="log-container">
            <div class="log-entry log-info">[INFO] 数据库监控管理面板已加载</div>
        </div>
    </div>

    <!-- Toast 通知 -->
    <div id="toast" class="toast"></div>

    <script>
        // 配置
        const API_BASE = 'http://localhost:8003/api/v1';
        let currentConfig = {};
        let isMonitoringEnabled = false;
        let isLightweightMode = false;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadConfig();
            refreshMetrics();
            log('数据库监控管理面板初始化完成', 'info');
        });

        // 日志记录
        function log(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.innerHTML = `[${timestamp}] [${type.toUpperCase()}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // 清空日志
        function clearLogs() {
            document.getElementById('logContainer').innerHTML = '';
            log('日志已清空', 'info');
        }

        // 显示Toast通知
        function showToast(message, type = 'success') {
            const toast = document.getElementById('toast');
            toast.textContent = message;
            toast.className = `toast ${type}`;
            toast.classList.add('show');
            setTimeout(() => {
                toast.classList.remove('show');
            }, 3000);
        }

        // API请求封装
        async function apiRequest(endpoint, options = {}) {
            try {
                const response = await fetch(`${API_BASE}${endpoint}`, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                return data.data || data;
            } catch (error) {
                log(`API请求失败: ${error.message}`, 'error');
                showToast(`请求失败: ${error.message}`, 'error');
                throw error;
            }
        }

        // 加载配置
        async function loadConfig() {
            try {
                const result = await apiRequest('/database-monitor/config');
                currentConfig = result.data || result;
                updateConfigUI();
                updateStatusUI();
                log('配置加载成功', 'info');
            } catch (error) {
                log('配置加载失败', 'error');
            }
        }

        // 更新配置UI
        function updateConfigUI() {
            document.getElementById('slowQueryThreshold').value = currentConfig.slowQueryThreshold || 1000;
            document.getElementById('samplingRate').value = currentConfig.samplingRate || 100;
            document.getElementById('maxSlowQueryRecords').value = currentConfig.maxSlowQueryRecords || 100;
            document.getElementById('enableSlowQueryLogging').checked = currentConfig.enableSlowQueryLogging !== false;
            document.getElementById('enableQueryMetrics').checked = currentConfig.enableQueryMetrics !== false;
            document.getElementById('enableStackTrace').checked = currentConfig.enableStackTrace !== false;
            document.getElementById('asyncSlowQueryProcessing').checked = currentConfig.asyncSlowQueryProcessing !== false;

            isMonitoringEnabled = currentConfig.enableDatabaseMonitoring !== false;
            isLightweightMode = currentConfig.lightweightMode === true;

            updateSamplingDisplay();
        }

        // 更新状态UI
        function updateStatusUI() {
            const statusIndicator = document.getElementById('statusIndicator');
            const statusText = document.getElementById('statusText');
            const toggleButton = document.getElementById('toggleMonitoring');
            const lightweightButton = document.getElementById('toggleLightweight');

            if (isMonitoringEnabled) {
                if (isLightweightMode) {
                    statusIndicator.className = 'status-indicator status-lightweight';
                    statusIndicator.textContent = '⚡';
                    statusText.textContent = '轻量级模式';
                } else {
                    statusIndicator.className = 'status-indicator status-enabled';
                    statusIndicator.textContent = '✅';
                    statusText.textContent = '监控已启用';
                }
                toggleButton.textContent = '禁用监控';
                toggleButton.className = 'danger';
            } else {
                statusIndicator.className = 'status-indicator status-disabled';
                statusIndicator.textContent = '❌';
                statusText.textContent = '监控已禁用';
                toggleButton.textContent = '启用监控';
                toggleButton.className = 'success';
            }

            lightweightButton.textContent = isLightweightMode ? '标准模式' : '轻量级模式';
            lightweightButton.className = isLightweightMode ? '' : 'warning';
        }

        // 更新采样率显示
        function updateSamplingDisplay() {
            const samplingRate = document.getElementById('samplingRate').value;
            document.getElementById('samplingDisplay').textContent = `${samplingRate}%`;
        }

        // 切换监控状态
        async function toggleMonitoring() {
            try {
                const endpoint = isMonitoringEnabled ? '/database-monitor/disable' : '/database-monitor/enable';
                await apiRequest(endpoint, { method: 'POST' });
                isMonitoringEnabled = !isMonitoringEnabled;
                updateStatusUI();
                log(`监控已${isMonitoringEnabled ? '启用' : '禁用'}`, 'info');
                showToast(`监控已${isMonitoringEnabled ? '启用' : '禁用'}`);
            } catch (error) {
                log('切换监控状态失败', 'error');
            }
        }

        // 切换轻量级模式
        async function toggleLightweight() {
            try {
                await apiRequest('/database-monitor/lightweight-mode', {
                    method: 'POST',
                    body: JSON.stringify({ enabled: !isLightweightMode })
                });
                isLightweightMode = !isLightweightMode;
                updateStatusUI();
                log(`轻量级模式已${isLightweightMode ? '启用' : '禁用'}`, 'info');
                showToast(`轻量级模式已${isLightweightMode ? '启用' : '禁用'}`);
            } catch (error) {
                log('切换轻量级模式失败', 'error');
            }
        }

        // 绑定事件
        document.getElementById('toggleMonitoring').onclick = toggleMonitoring;
        document.getElementById('toggleLightweight').onclick = toggleLightweight;

        // 刷新性能指标
        async function refreshMetrics() {
            try {
                const result = await apiRequest('/database-monitor/metrics');
                const metrics = result.data || result;

                document.getElementById('totalQueries').textContent = metrics.totalQueries || 0;
                document.getElementById('slowQueries').textContent = metrics.slowQueries || 0;
                document.getElementById('avgTime').textContent = `${Math.round(metrics.averageExecutionTime || 0)}ms`;
                document.getElementById('activeQueries').textContent = metrics.activeQueries || 0;

                log('性能指标已刷新', 'info');
            } catch (error) {
                log('刷新性能指标失败', 'error');
            }
        }

        // 更新配置
        async function updateConfig() {
            try {
                const config = {
                    slowQueryThreshold: parseInt(document.getElementById('slowQueryThreshold').value),
                    samplingRate: parseInt(document.getElementById('samplingRate').value),
                    maxSlowQueryRecords: parseInt(document.getElementById('maxSlowQueryRecords').value),
                    enableSlowQueryLogging: document.getElementById('enableSlowQueryLogging').checked,
                    enableQueryMetrics: document.getElementById('enableQueryMetrics').checked,
                    enableStackTrace: document.getElementById('enableStackTrace').checked,
                    asyncSlowQueryProcessing: document.getElementById('asyncSlowQueryProcessing').checked
                };

                await apiRequest('/database-monitor/config', {
                    method: 'POST',
                    body: JSON.stringify(config)
                });

                log('配置更新成功', 'info');
                showToast('配置更新成功');
                loadConfig(); // 重新加载配置
            } catch (error) {
                log('配置更新失败', 'error');
            }
        }

        // 测试慢查询
        async function testSlowQuery() {
            try {
                log('开始测试慢查询...', 'info');
                const result = await apiRequest('/database-test/slow-query?delay=3');
                log(`慢查询测试完成，耗时: ${result.data.executionTime}ms`, 'info');
                showToast('慢查询测试完成');
                setTimeout(() => refreshMetrics(), 1000); // 延迟刷新指标
            } catch (error) {
                log('慢查询测试失败', 'error');
            }
        }

        // 测试批量查询
        async function testBatchQueries() {
            try {
                log('开始测试批量查询...', 'info');
                const result = await apiRequest('/database-test/batch-queries?count=5');
                log(`批量查询测试完成，总耗时: ${result.data.totalExecutionTime}ms`, 'info');
                showToast('批量查询测试完成');
                setTimeout(() => refreshMetrics(), 1000);
            } catch (error) {
                log('批量查询测试失败', 'error');
            }
        }

        // 清空慢查询记录
        async function clearSlowQueries() {
            try {
                await apiRequest('/database-monitor/clear-slow-queries', { method: 'POST' });
                log('慢查询记录已清空', 'info');
                showToast('慢查询记录已清空');
                loadSlowQueries(); // 刷新显示
            } catch (error) {
                log('清空慢查询记录失败', 'error');
            }
        }

        // 重置指标
        async function resetMetrics() {
            try {
                await apiRequest('/database-monitor/reset-metrics', { method: 'POST' });
                log('性能指标已重置', 'info');
                showToast('性能指标已重置');
                refreshMetrics();
            } catch (error) {
                log('重置指标失败', 'error');
            }
        }

        // 加载慢查询记录
        async function loadSlowQueries(limit) {
            try {
                const endpoint = limit ? `/database-monitor/slow-queries?limit=${limit}` : '/database-monitor/slow-queries';
                const result = await apiRequest(endpoint);
                const queries = result.data || result;

                const container = document.getElementById('slowQueriesContainer');

                if (!queries || queries.length === 0) {
                    container.innerHTML = '<p style="text-align: center; color: #666;">暂无慢查询记录</p>';
                    return;
                }

                let html = '';
                queries.forEach(query => {
                    const timestamp = new Date(query.timestamp).toLocaleString();
                    html += `
                        <div class="slow-query-item">
                            <div class="slow-query-header">
                                <span>${timestamp}</span>
                                <span class="execution-time">${query.executionTime}ms</span>
                            </div>
                            <div class="query-text">${query.query}</div>
                            ${query.context ? `<div style="margin-top: 10px; color: #666; font-size: 12px;">上下文: ${query.context}</div>` : ''}
                        </div>
                    `;
                });

                container.innerHTML = html;
                log(`加载了 ${queries.length} 条慢查询记录`, 'info');
            } catch (error) {
                log('加载慢查询记录失败', 'error');
            }
        }

        // 导出配置
        function exportConfig() {
            const config = {
                slowQueryThreshold: parseInt(document.getElementById('slowQueryThreshold').value),
                samplingRate: parseInt(document.getElementById('samplingRate').value),
                maxSlowQueryRecords: parseInt(document.getElementById('maxSlowQueryRecords').value),
                enableSlowQueryLogging: document.getElementById('enableSlowQueryLogging').checked,
                enableQueryMetrics: document.getElementById('enableQueryMetrics').checked,
                enableStackTrace: document.getElementById('enableStackTrace').checked,
                asyncSlowQueryProcessing: document.getElementById('asyncSlowQueryProcessing').checked,
                enableDatabaseMonitoring: isMonitoringEnabled,
                lightweightMode: isLightweightMode
            };

            const dataStr = JSON.stringify(config, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = 'database-monitor-config.json';
            link.click();
            URL.revokeObjectURL(url);

            log('配置已导出', 'info');
            showToast('配置已导出');
        }

        // 加载预设配置
        async function loadPresetConfig(preset) {
            let config = {};

            if (preset === 'production') {
                config = {
                    enableDatabaseMonitoring: true,
                    lightweightMode: true,
                    slowQueryThreshold: 2000,
                    samplingRate: 10,
                    enableStackTrace: false,
                    asyncSlowQueryProcessing: true
                };
                log('已加载生产环境配置', 'info');
            } else if (preset === 'development') {
                config = {
                    enableDatabaseMonitoring: true,
                    lightweightMode: false,
                    slowQueryThreshold: 1000,
                    samplingRate: 100,
                    enableStackTrace: true,
                    asyncSlowQueryProcessing: false
                };
                log('已加载开发环境配置', 'info');
            }

            try {
                await apiRequest('/database-monitor/config', {
                    method: 'POST',
                    body: JSON.stringify(config)
                });

                showToast(`${preset === 'production' ? '生产' : '开发'}环境配置已应用`);
                loadConfig(); // 重新加载配置
            } catch (error) {
                log('应用预设配置失败', 'error');
            }
        }

        // 定时刷新指标
        setInterval(() => {
            if (isMonitoringEnabled) {
                refreshMetrics();
            }
        }, 30000); // 每30秒刷新一次
    </script>
</body>
</html>
