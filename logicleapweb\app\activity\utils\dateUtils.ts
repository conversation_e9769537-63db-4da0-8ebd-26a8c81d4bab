/**
 * 格式化日期为YYYY-MM-DD格式
 * @param dateString 日期字符串
 * @returns 格式化后的日期字符串
 */
export function formatDate(dateString: string): string {
  const date = new Date(dateString);

  // 检查日期是否有效
  if (isNaN(date.getTime())) {
    return dateString; // 如果无效，返回原始字符串
  }

  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');

  return `${year}-${month}-${day}`;
}

/**
 * 格式化日期时间为YYYY-MM-DD HH:MM格式
 * @param dateString 日期字符串
 * @returns 格式化后的日期时间字符串
 */
export function formatDateTime(dateString: string): string {
  const date = new Date(dateString);

  // 检查日期是否有效
  if (isNaN(date.getTime())) {
    return dateString; // 如果无效，返回原始字符串
  }

  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}`;
}

/**
 * 计算活动剩余时间
 * @param endDate 结束日期
 * @returns 剩余时间字符串
 */
export function getTimeRemaining(endDate: string): string {
  // 在服务端渲染时，返回默认状态，避免水合错误
  if (typeof window === 'undefined') {
    return '计算中...';
  }

  const end = new Date(endDate).getTime();
  const now = new Date().getTime();

  // 如果已经结束，返回已结束
  if (now >= end) {
    return '已结束';
  }

  const diff = end - now;

  // 计算剩余天数、小时和分钟 - 修复计算错误（应该是1000而不是200）
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

  if (days > 0) {
    return `还剩 ${days} 天 ${hours} 小时`;
  } else if (hours > 0) {
    return `还剩 ${hours} 小时 ${minutes} 分钟`;
  } else {
    return `还剩 ${minutes} 分钟`;
  }
}