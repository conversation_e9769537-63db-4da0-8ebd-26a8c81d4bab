import { Controller, Get, Post, Body, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery, ApiBody } from '@nestjs/swagger';
import { NotLogin } from 'src/web/router_guard/not-login.decorator';
import { QueryInterceptor } from '../interceptor/query-interceptor';
import { QueryMonitorService, SlowQueryRecord, QueryMetrics } from '../service/query-monitor.service';

@ApiTags('数据库监控')
@Controller('api/v1/database-monitor')
@NotLogin() // 数据库监控接口不需要登录验证
export class DatabaseMonitorController {
  constructor(
    private readonly queryMonitorService: QueryMonitorService,
    private readonly queryInterceptor: QueryInterceptor,
  ) {}

  @Get('slow-queries')
  @ApiOperation({ summary: '获取慢查询记录' })
  @ApiQuery({ name: 'limit', required: false, description: '返回记录数量限制' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getSlowQueries(@Query('limit') limit?: number): Promise<{
    code: number;
    message: string;
    data: SlowQueryRecord[] | null;
  }> {
    try {
      const records = this.queryMonitorService.getSlowQueryRecords(limit);
      return {
        code: 200,
        message: '获取慢查询记录成功',
        data: records
      };
    } catch (error) {
      return {
        code: 500,
        message: `获取慢查询记录失败: ${error.message}`,
        data: null
      };
    }
  }

  @Get('metrics')
  @ApiOperation({ summary: '获取查询性能指标' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getQueryMetrics(): Promise<{
    code: number;
    message: string;
    data: (QueryMetrics & { activeQueries: number }) | null;
  }> {
    try {
      const metrics = this.queryMonitorService.getQueryMetrics();
      const activeQueries = this.queryInterceptor.getActiveQueryCount();

      return {
        code: 200,
        message: '获取查询指标成功',
        data: {
          ...metrics,
          activeQueries
        }
      };
    } catch (error) {
      return {
        code: 500,
        message: `获取查询指标失败: ${error.message}`,
        data: null
      };
    }
  }

  @Get('active-queries')
  @ApiOperation({ summary: '获取当前活跃查询' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getActiveQueries(): Promise<{
    code: number;
    message: string;
    data: any[];
  }> {
    const activeQueries = this.queryInterceptor.getActiveQueries();
    return {
      code: 200,
      message: '获取活跃查询成功',
      data: activeQueries
    };
  }

  @Get('config')
  @ApiOperation({ summary: '获取监控配置' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getConfig(): Promise<{
    code: number;
    message: string;
    data: any;
  }> {
    try {
      const config = this.queryMonitorService.getConfig();
      return {
        code: 200,
        message: '获取配置成功',
        data: config
      };
    } catch (error) {
      return {
        code: 500,
        message: `获取配置失败: ${error.message}`,
        data: null
      };
    }
  }

  @Post('config')
  @ApiOperation({ summary: '更新监控配置' })
  @ApiBody({
    description: '监控配置',
    schema: {
      type: 'object',
      properties: {
        enableDatabaseMonitoring: { type: 'boolean', description: '启用数据库监控' },
        lightweightMode: { type: 'boolean', description: '轻量级模式' },
        slowQueryThreshold: { type: 'number', description: '慢查询阈值(ms)' },
        enableSlowQueryLogging: { type: 'boolean', description: '启用慢查询日志' },
        enableQueryMetrics: { type: 'boolean', description: '启用查询指标' },
        enableStackTrace: { type: 'boolean', description: '启用调用栈捕获' },
        samplingRate: { type: 'number', description: '采样率(1-100)' },
        asyncSlowQueryProcessing: { type: 'boolean', description: '异步处理慢查询' },
        maxSlowQueryRecords: { type: 'number', description: '最大慢查询记录数' }
      }
    }
  })
  @ApiResponse({ status: 200, description: '更新成功' })
  async updateConfig(@Body() config: {
    enableDatabaseMonitoring?: boolean;
    lightweightMode?: boolean;
    slowQueryThreshold?: number;
    enableSlowQueryLogging?: boolean;
    enableQueryMetrics?: boolean;
    enableStackTrace?: boolean;
    samplingRate?: number;
    asyncSlowQueryProcessing?: boolean;
    maxSlowQueryRecords?: number;
  }): Promise<{
    code: number;
    message: string;
    data: any;
  }> {
    try {
      this.queryMonitorService.updateConfig(config);
      const newConfig = this.queryMonitorService.getConfig();

      return {
        code: 200,
        message: '配置更新成功',
        data: newConfig
      };
    } catch (error) {
      return {
        code: 500,
        message: `配置更新失败: ${error.message}`,
        data: null
      };
    }
  }

  @Post('reset-metrics')
  @ApiOperation({ summary: '重置查询指标' })
  @ApiResponse({ status: 200, description: '重置成功' })
  async resetMetrics(): Promise<{
    code: number;
    message: string;
    data: null;
  }> {
    this.queryMonitorService.resetMetrics();
    return {
      code: 200,
      message: '查询指标已重置',
      data: null
    };
  }

  @Post('clear-slow-queries')
  @ApiOperation({ summary: '清空慢查询记录' })
  @ApiResponse({ status: 200, description: '清空成功' })
  async clearSlowQueries(): Promise<{
    code: number;
    message: string;
    data: null;
  }> {
    this.queryMonitorService.clearSlowQueryRecords();
    return {
      code: 200,
      message: '慢查询记录已清空',
      data: null
    };
  }

  @Post('cleanup-timeout-queries')
  @ApiOperation({ summary: '清理超时查询' })
  @ApiBody({
    description: '超时时间配置',
    schema: {
      type: 'object',
      properties: {
        timeoutMs: { type: 'number', description: '超时时间(ms)', default: 300000 }
      }
    }
  })
  @ApiResponse({ status: 200, description: '清理成功' })
  async cleanupTimeoutQueries(@Body() body: { timeoutMs?: number }): Promise<{
    code: number;
    message: string;
    data: null;
  }> {
    
    this.queryInterceptor.cleanupTimeoutQueries(body.timeoutMs);
    return {
      code: 200,
      message: '超时查询清理完成',
      data: null
    };
  }

  @Post('enable')
  @ApiOperation({ summary: '启用数据库监控' })
  @ApiResponse({ status: 200, description: '启用成功' })
  async enableMonitoring(): Promise<{
    code: number;
    message: string;
    data: any;
  }> {
    try {
      this.queryMonitorService.updateConfig({ enableDatabaseMonitoring: true });
      return {
        code: 200,
        message: '数据库监控已启用',
        data: this.queryMonitorService.getConfig()
      };
    } catch (error) {
      return {
        code: 500,
        message: `启用监控失败: ${error.message}`,
        data: null
      };
    }
  }

  @Post('disable')
  @ApiOperation({ summary: '禁用数据库监控' })
  @ApiResponse({ status: 200, description: '禁用成功' })
  async disableMonitoring(): Promise<{
    code: number;
    message: string;
    data: any;
  }> {
    try {
      this.queryMonitorService.updateConfig({ enableDatabaseMonitoring: false });
      return {
        code: 200,
        message: '数据库监控已禁用',
        data: this.queryMonitorService.getConfig()
      };
    } catch (error) {
      return {
        code: 500,
        message: `禁用监控失败: ${error.message}`,
        data: null
      };
    }
  }

  @Post('lightweight-mode')
  @ApiOperation({ summary: '切换轻量级模式' })
  @ApiBody({
    description: '轻量级模式配置',
    schema: {
      type: 'object',
      properties: {
        enabled: { type: 'boolean', description: '是否启用轻量级模式' }
      }
    }
  })
  @ApiResponse({ status: 200, description: '切换成功' })
  async toggleLightweightMode(@Body() body: { enabled: boolean }): Promise<{
    code: number;
    message: string;
    data: any;
  }> {
    try {
      this.queryMonitorService.updateConfig({ lightweightMode: body.enabled });
      return {
        code: 200,
        message: `轻量级模式已${body.enabled ? '启用' : '禁用'}`,
        data: this.queryMonitorService.getConfig()
      };
    } catch (error) {
      return {
        code: 500,
        message: `切换轻量级模式失败: ${error.message}`,
        data: null
      };
    }
  }

  @Get('health')
  @ApiOperation({ summary: '数据库监控健康检查' })
  @ApiResponse({ status: 200, description: '健康检查成功' })
  async healthCheck(): Promise<{
    code: number;
    message: string;
    data: {
      status: string;
      timestamp: string;
      metrics: QueryMetrics | null;
      activeQueries: number;
      config: any;
    };
  }> {
    try {
      const metrics = this.queryMonitorService.getQueryMetrics();
      const activeQueries = this.queryInterceptor.getActiveQueryCount();
      const config = this.queryMonitorService.getConfig();

      return {
        code: 200,
        message: '数据库监控运行正常',
        data: {
          status: config.enableDatabaseMonitoring ? 'enabled' : 'disabled',
          timestamp: new Date().toISOString(),
          metrics,
          activeQueries,
          config
        }
      };
    } catch (error) {
      return {
        code: 500,
        message: `健康检查失败: ${error.message}`,
        data: {
          status: 'error',
          timestamp: new Date().toISOString(),
          metrics: null,
          activeQueries: 0,
          config: null
        }
      };
    }
  }
}
