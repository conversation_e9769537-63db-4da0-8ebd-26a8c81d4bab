/** @jsxImportSource react */
'use client'

import { useState } from 'react'
import { Modal, Input, Button, Form, message } from 'antd'
import { EyeInvisibleOutlined, EyeOutlined, LockOutlined } from '@ant-design/icons'
import userApi from '@/lib/api/user'
import { COLORS } from './colors'

interface InitialPasswordModalProps {
  isOpen: boolean
  onClose: () => void
  onSuccess: () => void
  userId: number
}

export default function InitialPasswordModal({ isOpen, onClose, onSuccess, userId }: InitialPasswordModalProps) {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)

  // 处理密码设置
  const handleSetPassword = async (values: { password: string; confirmPassword: string }) => {
    if (values.password !== values.confirmPassword) {
      message.error('两次输入的密码不一致')
      return
    }

    setLoading(true)
    try {
      const response = await userApi.initPwd({
        userId,
        password: values.password
      })

      if (response.data.code === 200) {
        message.success('密码设置成功')
        onSuccess()
      } else {
        message.error(response.data.message || '密码设置失败')
      }
    } catch (error) {
      console.error('密码设置失败:', error)
      message.error('密码设置失败，请稍后重试')
    } finally {
      setLoading(false)
    }
  }

  // 处理取消操作
  const handleCancel = async () => {
    // 如果用户点击右上角关闭按钮或取消，则自动设置默认密码为123456
    setLoading(true)
    try {
      const response = await userApi.initPwd({
        userId,
        password: '123456'
      })

      if (response.data.code === 200) {
        message.success('已为您设置默认密码: 123456')
      } else {
        message.error(response.data.message || '默认密码设置失败')
      }
    } catch (error) {
      console.error('默认密码设置失败:', error)
      message.error('默认密码设置失败，请稍后重试')
    } finally {
      setLoading(false)
      onClose()
    }
  }

  return (
    <Modal
      title="设置密码"
      open={isOpen}
      onCancel={handleCancel}
      footer={null}
      maskClosable={false}
      centered
      closeIcon={true}
    >
      <div className="py-4">
        <p className={`${COLORS.text.secondary}`}>
          请为您的账号设置一个密码，方便下次登录
        </p>
        <p className={`${COLORS.text.secondary} mb-6`}>
          若点击右上角关闭按钮或者取消按钮自动设置初始密码
        </p>

        <Form
          form={form}
          layout="vertical"
          onFinish={handleSetPassword}
        >
          <Form.Item
            name="password"
            label={<>
              <span className={COLORS.text.error}>*</span> 密码
            </>}
            rules={[
              { required: true, message: '请输入密码' },
              { min: 6, message: '密码长度不能少于6位' }
            ]}
          >
            <Input.Password
              placeholder="请输入密码"
              iconRender={visible => (visible ? <EyeOutlined /> : <EyeInvisibleOutlined />)}
            />
          </Form.Item>

          <Form.Item
            name="confirmPassword"
            label={<>
              <span className={COLORS.text.error}>*</span> 确认密码
            </>}
            rules={[
              { required: true, message: '请再次输入密码' },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue('password') === value) {
                    return Promise.resolve()
                  }
                  return Promise.reject(new Error('两次输入的密码不一致'))
                }
              })
            ]}
          >
            <Input.Password
              placeholder="请再次输入密码"
              iconRender={visible => (visible ? <EyeOutlined /> : <EyeInvisibleOutlined />)}
            />
          </Form.Item>

          <div className="flex gap-4 mt-6">
            <Button
              onClick={handleCancel}
              block
              className="h-10"
            >
              取消
            </Button>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              block
              className="h-10"
            >
              确认
            </Button>
          </div>
        </Form>
      </div>
    </Modal>
  )
} 