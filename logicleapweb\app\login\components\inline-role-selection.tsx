'use client'

import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON> } from 'antd';
import { UserOutlined, BookOutlined, TeamOutlined } from '@ant-design/icons';
import userApi from '@/lib/api/user';
import { useDispatch } from 'react-redux';
import { setUser } from '@/lib/store';
import { GetNotification } from 'logic-common/dist/components/Notification';
import { COLORS } from './colors';

interface InlineRoleSelectionProps {
  userId: number;
  onSuccess: (selectedRole?: {roleId: number, roleType: string, needsAuthentication?: boolean}) => void;
}

// 角色Id的map映射
const roleIdMap = {
  'student': 1, // 学生角色Id
  'teacher': 2, // 教师角色Id
  'normal': 3, // 普通用户角色Id
};

// 定义角色key类型
type RoleType = keyof typeof roleIdMap;

const InlineRoleSelection: React.FC<InlineRoleSelectionProps> = ({ userId, onSuccess }) => {
  const [selectedRole, setSelectedRole] = useState<RoleType | ''>('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const dispatch = useDispatch();
  const nt = GetNotification();

  // 角色卡片配置
  const roleCards = [
    {
      type: 'normal',
      title: '普通用户',
      icon: <UserOutlined style={{ fontSize: '36px' }} />,
      description: '体验基础功能'
    },
    {
      type: 'student',
      title: '学生',
      icon: <BookOutlined style={{ fontSize: '36px' }} />,
      description: '学习与成长'
    },
    {
      type: 'teacher',
      title: '教师',
      icon: <TeamOutlined style={{ fontSize: '36px' }} />,
      description: '教学与管理'
    }
  ];

  const handleSubmit = async () => {
    if (selectedRole) {
      console.log("zww：用户选择的角色为",selectedRole);
      
      setIsSubmitting(true);
      try {
        // 1.根据key映射对应的roleID
        const selectedRoleId = roleIdMap[selectedRole];
        console.log("zww：用户选择的角色映射为",selectedRoleId);
        
        // 2.检查是否为教师角色，如果是教师角色，可能需要先进行认证
        if (selectedRole === 'teacher') {
          console.log("zww：用户选择了老师的角色！即将弹出教师认证弹窗");
          
          // 3.通知父组件用户选择了教师角色，让父组件决定下一步的弹窗显示，全部在LoginForm里弹窗
          onSuccess({
            roleId: selectedRoleId,
            roleType: selectedRole,
            needsAuthentication: true // 标记该角色需要认证，尚未分配，这个标志位告诉父组件需要弹出inline-auth弹窗
          });
          setIsSubmitting(false);
          return;
        }
        console.log("zww：用户选择了非老师的角色！直接分配角色！");
        // 1.非教师角色，直接分配
        const res = await userApi.assignRole(userId, selectedRoleId);
        
        if (res.data.code === 200) {
          nt.success('身份绑定成功！');
          
          // 2.更新本地存储中的用户信息
          const userJson = localStorage.getItem('user');
          if (userJson) {
            const userData = JSON.parse(userJson);
            // 3.更新本地存储的角色id为选中的角色
            userData.roleId = selectedRoleId;
            localStorage.setItem('user', JSON.stringify(userData));

            // 4.更新Redux状态
            dispatch(setUser({
              ...userData,
              roleId: selectedRoleId
            }));
          }
          
          
          // 回调函数 - 传递选中的角色信息
          onSuccess({
            roleId: selectedRoleId,
            roleType: selectedRole,
            needsAuthentication: false, // 角色不需要认证，已经分配完成
          });
        } else {
          console.error("角色分配API响应错误:", res.data);
          nt.error(res.data.message || '身份绑定失败，请稍后重试');
        }
      } catch (error) {
        console.error('角色选择失败:', error);
        nt.error('身份绑定失败，请稍后重试');
      } finally {
        setIsSubmitting(false);
      }
    } else {
      nt.warning('请选择一个身份');
    }
  };

  return (
    <div className="w-full">
      <div className="text-center mb-6">
        <h2 className={`text-xl font-semibold ${COLORS.text.white}`}>请选择您的身份</h2>
        <p className={`mt-2 text-sm ${COLORS.text.white}`}>选择一个适合您的身份，以获得相应的功能和权限</p>
      </div>

      {/* 角色卡片wrapper */}
      <div className="flex  justify-center  gap-6 px-6 pb-6">
        {roleCards.map((card) => (
          <div
            key={card.type}
            className={`flex-1 p-6 rounded-xl cursor-pointer transition-all duration-300 transform hover:scale-105 ${
              selectedRole === card.type
                ? `bg-blue-600 ${COLORS.text.white} shadow-lg`
                : `${COLORS.bg.primary} text-gray-200 ${COLORS.bg.primary}`
            }`}
            onClick={() => {
              setSelectedRole(selectedRole === card.type ? '' : card.type as RoleType);
            }}
          >
            <div className="flex flex-col items-center text-center">
              <div className={`mb-4 ${selectedRole === card.type ? 'text-white' : 'text-blue-400'}`}>
                {card.icon}
              </div>
              <h3 className={`text-lg font-medium mb-2 ${selectedRole === card.type? COLORS.text.blue_light : COLORS.text.primary} text-black`}>{card.title}</h3>
              <p className={`text-sm ${selectedRole === card.type ? COLORS.text.blue_light : COLORS.text.primary}`}>
                {card.description}
              </p>
            </div>
          </div>
        ))}
      </div>

      {/* 确定按钮区域 */}
      <div className="flex justify-center py-6 mt-4">
        <button
          onClick={handleSubmit}
          disabled={!selectedRole || isSubmitting}
          className={`h-12 w-full max-w-md ${COLORS.bg.accent} ${COLORS.text.white} rounded-xl ${COLORS.bg.accent_hover} disabled:bg-gray-600 disabled:cursor-not-allowed flex items-center justify-center`}
        >
          {isSubmitting ? (
            <>
              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              提交中...
            </>
          ) : (
            '确认选择'
          )}
        </button>
      </div>
    </div>
  );
};

export default InlineRoleSelection; 