// import { request } from './common';
import request from "../request";

// 课程接口类型定义
export interface Course {
  id: number;
  name: string;
  description: string;
  category: string;
  status: 'active' | 'inactive';
  createdAt: string;
  updatedAt: string;
}



export interface UpdateCourseRequest {
  name?: string;
  description?: string;
  category?: string;
  status?: 'active' | 'inactive';
}

export interface CreateCourseSeriesRequest {
  title: string;
  description: string;
  coverImage: string;
  category: number; // 0=社区, 1=官方
  projectMembers: string;
  tagIds: number[] ;

}

export interface CreateCourseSeriesResponse {
  id: number;
  title: string;
  description: string;
  coverImage: string;
  category: number;
  status: number;
  projectMembers: string;
  totalCourses: number;
  totalStudents: number;
  creatorId: number;
  createdAt: string;
  updatedAt: string;
}

// 系列课程列表项
export interface CourseSeriesListItem {
  id: number;
  title: string;
  description: string;
  coverImage: string;
  category: number; // 0=社区, 1=官方
  categoryLabel: string; // "社区" 或 "官方"
  status: number; // 0=草稿, 1=已发布, 2=已归档
  statusLabel: string; // "草稿", "已发布", "已归档"
  projectMembers: string;
  totalCourses: number;
  totalStudents: number;
  contentSummary: {
    videoCourseCount: number;
    documentCourseCount: number;
    totalResourcesCount: number;
    completionRate: number;
  };
  createdAt: string;
  updatedAt: string;
}

// 分页信息
export interface PaginationInfo {
  page: number;
  pageSize: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// 系列课程列表响应
export interface CourseSeriesListResponse {
  code: number;
  msg: string;
  data: {
    list: CourseSeriesListItem[];
    pagination: PaginationInfo;
  };
}

// 系列课程列表项
export interface CourseSeriesListItem {
  id: number;
  title: string;
  description: string;
  coverImage: string;
  category: number;
  categoryLabel: string;
  status: number;
  statusLabel: string;
  projectMembers: string;
  totalCourses: number;
  totalStudents: number;
  contentSummary: {
    videoCourseCount: number;
    documentCourseCount: number;
    totalResourcesCount: number;
    completionRate: number;
  };
  createdAt: string;
  updatedAt: string;
}

// 系列课程列表响应
export interface CourseSeriesListResponse {
  code: number;
  msg: string;
  data: {
    list: CourseSeriesListItem[];
    pagination: {
      page: number;
      pageSize: number;
      total: number;
      totalPages: number;
      hasNext: boolean;
      hasPrev: boolean;
    };
  };
}

// 系列课程详情
export interface CourseSeriesDetail {
  id: number;
  title: string;
  description: string;
  coverImage: string;
  category: number;
  status: number;
  projectMembers: string;
  totalCourses: number;
  totalStudents: number;
  creatorId: number;
  createdAt: string;
  updatedAt: string;
}

// 课程内容配置
export interface CourseContentConfig {
  hasVideo: number;
  hasDocument: number;
  hasAudio: number;
  video?: {
    url: string;
    name: string;
  };
  document?: {
    url: string;
    name: string;
  };
  audio?: {
    url: string;
    name: string;
  };
}

// 教学信息
export interface TeachingInfo {
  title: string;
  content: string[];
}

// 附加资源
export interface AdditionalResource {
  title: string;
  url: string;
  description: string;
}



export interface CourseListResponse {
  code: number;
  msg: string;
  data: {
    list: CourseItem[];
    pagination: PaginationInfo;
  };
}

// 课程列表项
export interface CourseItem {
  id: number;
  seriesId: number;
  title: string;
  description: string;
  coverImage: string;
  orderIndex: number;
  status: number;
  statusLabel: string;
  hasVideo: number;
  hasDocument: number;
  hasAudio: number;
  videoDuration: number;
  videoDurationLabel: string;
  videoName: string;
  firstTeachingTitle: string;
  resourcesCount: number;
  createdAt: string;
  updatedAt: string;
}

// 课程内容配置
export interface CourseContentConfig {
  hasVideo: number;
  hasDocument: number;
  hasAudio: number;
  video?: {
    url: string;
    name: string;
  };
  document?: {
    url: string;
    name: string;
  };
  audio?: {
    url: string;
    name: string;
  };
}

// 教学信息项
export interface TeachingInfoItem {
  title: string;
  content: string[];
}

// 附加资源项
export interface AdditionalResourceItem {
  title: string;
  url: string;
  description: string;
}

// 创建课程请求
export interface CreateCourseRequest {
  seriesId: number;
  title: string;
  description: string;
  coverImage: string;
  hasVideo: number;
  hasDocument: number;
  hasAudio: number;
  videoDuration: number;
  contentConfig: CourseContentConfig;
  teachingInfo: TeachingInfoItem[];
  additionalResources: AdditionalResourceItem[];
  orderIndex: number;
}

export interface CourseResponse {
  code: number;
  msg: string;
  data: Course;
}

export interface CourseQueryParams {
  page?: number;
  pageSize?: number;
  keyword?: string;
  category?: string;
  status?: 'active' | 'inactive';
}

export interface Teacher {
  id: number;
  name: string;
  email: string;
  subject: string;
  school: string;
  avatar?: string;
  phone?: string;
}

export interface CourseTag {
  id: number;
  name: string;
  color: string;
}

export interface CourseSeries {
  id: number;
  title: string;
  description: string;
  coverImage: string;
  category: string;
  teacherIds: number[];
  tagIds: number[];
  createdAt: string;
  updatedAt: string;
}

// 课程API
export const courseApi = {
  baseUrl: '/api/v1/course-management',
  getMyCourseSeriesList: (params?: {
    page?: number;      // 页码，默认1
    pageSize?: number;  // 每页数量，默认10
    status?: number;    // 状态筛选：0=草稿，1=已发布，2=已归档
    keyword?: string;   // 搜索关键词
  }) => {
    // 设置默认参数
    const requestParams = {
      page: 1,
      pageSize: 10,
      ...params
    };

    console.log('📤 获取我的课程系列列表:', requestParams);

    return request.get(`${courseApi.baseUrl}/my-series`, {
      params: requestParams
    });
  },

  // 获取系列下的课程列表 - 使用课程市场API
  getSeriesCourseList: (seriesId: number, params?: {
    page?: number;
    pageSize?: number;
    status?: number;
  }) => {
    console.log('🌐 courseApi.getSeriesCourseList 调用（课程市场API）');
    console.log('📤 系列ID:', seriesId, '参数:', params);
    console.log('🔗 接口地址: GET /api/v1/course-marketplace/series/{seriesId}/courses');

    return request.get(`/api/v1/course-marketplace/series/${seriesId}/courses`, {
      params
    });
  },

  // 获取课程列表 - 暂时不需要，已删除
  // getCourseList: (params?: {
  //   page?: number;
  //   pageSize?: number;
  //   keyword?: string;
  //   category?: string;
  //   status?: string;
  // }) => {
  //   return request.get('/api/course/list', {
  //     params: {
  //       page: 1,
  //       pageSize: 10,
  //       ...params
  //     }
  //   });
  // },

  // 获取单个课程详情 - 暂时不需要，已删除
  // getCourseById: (id: number) => {
  //   return request.get(`/api/course/${id}`);
  // },

  // 获取系列下的课程列表
  getSeriesCourses: (seriesId: number, params?: {
    page?: number;
    pageSize?: number;
    status?: number;
  }) => {
    console.log('🌐 courseApi.getSeriesCourses 调用（课程市场API）');
    console.log('📤 系列ID:', seriesId, '参数:', params);
    console.log('🔗 接口地址: GET /api/v1/course-marketplace/series/{seriesId}/courses');

    return request.get(`/api/v1/course-marketplace/series/${seriesId}/courses`, {
      params
    });
  },

  // 获取系列下的课程列表 - 使用课程管理API
  getManagementSeriesCourses: (seriesId: number, params?: {
    page?: number;
    pageSize?: number;
    status?: number;
  }) => {
    console.log('🌐 courseApi.getManagementSeriesCourses 调用（课程管理API）');
    console.log('📤 系列ID:', seriesId, '参数:', params);
    console.log('🔗 接口地址: GET /api/v1/course-management/series/{seriesId}/courses');

    return request.get(`${courseApi.baseUrl}/series/${seriesId}/courses`, {
      params
    });
  },

  // 创建课程
  createCourse: (data: any) => {
    console.log('📤 发送课程创建请求到:', `${courseApi.baseUrl}/courses`);
    console.log('📤 请求数据:', data);

    // 为课程创建请求设置更长的超时时间，因为可能包含大文件
    return request.post(`${courseApi.baseUrl}/courses`, data, {
      timeout: 60000, // 60秒超时
      headers: {
        'Content-Type': 'application/json',
      }
    });
  },

  // 设置课程配置
  setCourseSettings: (courseId: number, settingsData: any) => {
    console.log('📤 发送课程设置请求到:', `${courseApi.baseUrl}/courses/${courseId}/settings`);
    console.log('📤 请求数据:', settingsData);

    return request.post(`${courseApi.baseUrl}/courses/${courseId}/settings`, settingsData, {
      headers: {
        'Content-Type': 'application/json',
      }
    });
  },

  // 获取课程详情
  getCourseDetail: (id: number) => {
    return request.get(`${courseApi.baseUrl}/courses/${id}`);
  },

  // 获取我的系列课程列表
  // 接口地址: GET /api/v1/course-management/my-series
  // 接口描述: 获取当前用户创建的系列课程列表，支持分页和筛选
  getMySeries: (params?: {
    page?: number;      // 页码，默认1
    pageSize?: number;  // 每页数量，默认10
    status?: number;    // 状态筛选：0=草稿，1=已发布，2=已归档
    keyword?: string;   // 搜索关键词
  }) => {
    console.log('🌐 courseApi.getMySeries 调用');
    console.log('📤 请求参数:', params);
    console.log('🔗 接口地址: GET /api/v1/course-management/my-series');
    console.log('📋 支持参数: page, pageSize, status(0=草稿,1=已发布,2=已归档), keyword');

    // 设置默认参数
    const requestParams = {
      page: 1,
      pageSize: 10,
      ...params
    };

    console.log('📤 最终请求参数:', requestParams);

    return request.get(`${courseApi.baseUrl}/my-series`, {
      params: requestParams
    });
  },

  // 获取系列课程详情
  getSeriesDetail: (id: number) => {
    console.log('📤 发送系列详情请求到:', `${courseApi.baseUrl}/series/${id}`);
    console.log('📤 系列ID:', id);
    return request.get(`${courseApi.baseUrl}/series/${id}`);
  },

  // 获取课程市场的系列详情
  getMarketplaceSeriesDetail: (seriesId: number) => {
    console.log('📤 发送课程市场系列详情请求到:', `/api/v1/course-marketplace/series/${seriesId}`);
    console.log('📤 系列ID:', seriesId);
    return request.get(`/api/v1/course-marketplace/series/${seriesId}`);
  },

  // 获取课程市场的课程详情
  getCourseMarketplaceDetail: (seriesId: number, courseId: number) => {
    console.log('📤 发送课程市场详情请求到:', `/api/v1/course-marketplace/series/${seriesId}/courses/${courseId}`);
    console.log('📤 系列ID:', seriesId, '课程ID:', courseId);
    return request.get(`/api/v1/course-marketplace/series/${seriesId}/courses/${courseId}`);
  },

  // 创建系列课程
  createCourseSeries: (data: any) => {
    return request.post(`${courseApi.baseUrl}/series`, data);
  },

  // 更新系列课程
  updateCourseSeries: (id: number, data: any) => {
    return request.put(`${courseApi.baseUrl}/series/${id}`, data);
  },

  // 删除系列课程
  deleteCourseSeries: (id: number) => {
    return request.delete(`${courseApi.baseUrl}/series/${id}`);
  },

  // 发布系列课程
  publishCourseSeries: (seriesId: number) => {
    return request.post(`${courseApi.baseUrl}/series/${seriesId}/publish`);
  },

  // 发布课程
  publishCourse: (courseId: number) => {
    console.log('📤 发送发布课程请求到:', `${courseApi.baseUrl}/courses/${courseId}/publish`);
    console.log('📤 课程ID:', courseId);
    return request.post(`${courseApi.baseUrl}/courses/${courseId}/publish`);
  },

  // 更新课程
  updateCourse: (id: number, data: any) => {
    return request.put(`${courseApi.baseUrl}/courses/${id}`, data);
  },

  // 删除课程
  deleteCourse: (id: number) => {
    return request.delete(`${courseApi.baseUrl}/courses/${id}`);
  },

  // 调整课程排序
  updateCourseOrders: (seriesId: number, courseOrders: { courseId: number; orderIndex: number }[]) => {
    return request.put(`${courseApi.baseUrl}/series/${seriesId}/course-orders`, { courseOrders });
  },

  // 批量删除课程 - 暂时不需要，已删除
  // batchDeleteCourses: (ids: number[]) => {
  //   return request.post('/api/course/batch-delete', { ids });
  // },

  // 更新课程状态 - 暂时不需要，已删除
  // updateCourseStatus: (id: number, status: 'active' | 'inactive') => {
  //   return request.patch(`/api/course/${id}/status`, { status });
  // },

  // 获取课程分类列表 - 暂时不需要，已删除
  // getCourseCategories: () => {
  //   return request.get('/api/course/categories');
  // },

  // 搜索课程 - 暂时不需要，已删除
  // searchCourses: (keyword: string, params?: any) => {
  //   return request.get('/api/course/search', {
  //     params: {
  //       keyword,
  //       page: 1,
  //       pageSize: 10,
  //       ...params
  //     }
  //   });
  // },

  // 获取教师列表
  getTeachers: () => {
    return request.get(`${courseApi.baseUrl}/teachers`);
  },

  // 获取课程标签列表 - 使用课程市场API
  getCourseTags: (params?: {
    page?: number;
    pageSize?: number;
    category?: number; // 0=难度, 1=类型, 2=特色, 3=其他
    status?: number;   // 0=禁用, 1=启用
    keyword?: string;
  }) => {
    console.log('🏷️ 获取课程标签列表，参数:', params);
    return request.get('/api/v1/course-marketplace/tags', {
      params: {
        page: 1,
        pageSize: 100, // 获取更多标签用于选择
        status: 1,     // 只获取启用的标签
        ...params
      }
    });
  },

  // 创建课程标签
  createCourseTag: (data: {
    name: string;
    color?: string;
    category: number;
    description?: string;
    status?: number;
  }) => {
    console.log('🏷️ 创建课程标签，数据:', data);
    return request.post('/api/v1/course-marketplace/tags', data);
  },

  // 更新课程标签
  updateCourseTag: (id: number, data: {
    name?: string;
    color?: string;
    category?: number;
    description?: string;
    status?: number;
  }) => {
    console.log('🏷️ 更新课程标签，ID:', id, '数据:', data);
    return request.put(`/api/v1/course-marketplace/tags/${id}`, data);
  },

  // 删除课程标签
  deleteCourseTag: (id: number) => {
    console.log('🏷️ 删除课程标签，ID:', id);
    return request.delete(`/api/v1/course-marketplace/tags/${id}`);
  },

  // 获取单个标签详情
  getCourseTagById: (id: number) => {
    console.log('🏷️ 获取标签详情，ID:', id);
    return request.get(`/api/v1/course-marketplace/tags/${id}`);
  },

  // 获取课程市场系列课程列表
  getMarketplaceSeries: (params?: {
    page?: number;      // 页码，默认1
    pageSize?: number;  // 每页数量，默认10
    category?: number;  // 分类筛选：0=官方，1=社区（注：前端使用categoryLabel字段筛选）
    status?: number;    // 状态筛选：0=草稿，1=已发布，2=已归档
    keyword?: string;   // 搜索关键词
    hasVideo?: boolean; // 是否包含视频
    hasDocument?: boolean; // 是否包含文档
    hasAudio?: boolean; // 是否包含音频
  }) => {
    console.log('🌐 courseApi.getMarketplaceSeries 调用');
    console.log('📤 请求参数:', params);
    console.log('🔗 接口地址: GET /api/v1/course-marketplace/series');
    console.log('📋 注意：前端使用categoryLabel字段("官方"/"社区")进行筛选');

    return request.get('/api/v1/course-marketplace/series', {
      params: {
        page: 1,
        pageSize: 50,
        ...params
      }
    });
  },

  // 获取课程系列列表
  getCourseSeries: (params?: {
    page?: number;
    pageSize?: number;
    category?: number; // 0=社区, 1=官方
    status?: number;   // 0=草稿, 1=已发布
    keyword?: string;
  }) => {
    console.log('🔄 开始获取系列课程列表，参数:', params);

    return request.get(`${courseApi.baseUrl}/series`, {
      params: {
        page: 1,
        pageSize: 10,
        ...params
      }
    });
  },

  // 根据手机号查询教师
  searchTeacherByPhone: (phone: string) => {
    console.log('发起手机号查询请求:', phone);
    return request.get(`${courseApi.baseUrl}/teachers/search-by-phone`, {
      params: { phone }
    });
  }
};
