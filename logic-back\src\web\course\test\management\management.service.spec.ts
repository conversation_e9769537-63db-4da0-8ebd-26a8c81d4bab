import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken, getDataSourceToken } from '@nestjs/typeorm';
import { Repository, Like, DataSource } from 'typeorm';
import { NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { TaskTemplate } from '../../domain/entities/teaching/task-template.entity';
import { CourseSeriesTag } from '../../domain/entities/marketplace/course-series-tag.entity';
import { CourseTag } from '../../domain/entities/marketplace/course-tag.entity';
import { ManagementService } from '../../application/services/management/management.service';
import { CourseSeries } from '../../domain/entities/management/course-series.entity';
import { CourseSettings } from '../../domain/entities/management/course-settings.entity';
import { Course } from '../../domain/entities/management/course.entity';

// Mock Repository 类型
type MockRepository = {
  findOne?: jest.Mock;
  find?: jest.Mock;
  findAndCount?: jest.Mock;
  save?: jest.Mock;
  create?: jest.Mock;
  remove?: jest.Mock;
  update?: jest.Mock;
  count?: jest.Mock;
  createQueryBuilder?: jest.Mock;
};

// 创建 Mock Repository 的工厂函数
const createMockRepository = (): MockRepository => ({
  findOne: jest.fn(),
  find: jest.fn(),
  findAndCount: jest.fn(),
  save: jest.fn(),
  create: jest.fn(),
  remove: jest.fn(),
  update: jest.fn(),
  count: jest.fn(),
  createQueryBuilder: jest.fn(),
});

describe('ManagementService', () => {
  let service: ManagementService;
  let courseSeriesRepository: MockRepository;
  let courseRepository: MockRepository;
  let taskTemplateRepository: MockRepository;
  let courseSettingsRepository: MockRepository;
  let courseSeriesTagRepository: MockRepository;
  let courseTagRepository: MockRepository;
  let dataSource: any;

  // 测试数据
  const mockCourseSeries: Partial<CourseSeries> = {
    id: 1,
    title: '测试系列课程',
    description: '这是一个测试系列课程',
    coverImage: 'https://example.com/cover.jpg',
    category: 0,
    status: 0,
    projectMembers: '张三,李四',
    totalCourses: 0,
    totalStudents: 0,
    creatorId: 123,
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01'),
  };

  const mockCourse: Partial<Course> = {
    id: 1,
    seriesId: 1,
    title: '测试课程',
    description: '这是一个测试课程',
    coverImage: 'https://example.com/course-cover.jpg',
    hasVideo: 1,
    hasDocument: 0,
    hasAudio: 0,
    videoDuration: 3600,
    contentConfig: {},
    teachingInfo: [],
    additionalResources: [],
    orderIndex: 1,
    status: 0,
    creatorId: 123,
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01'),
  };

  const mockTaskTemplate: Partial<TaskTemplate> = {
    id: 1,
    courseId: 1,
    taskName: '测试任务模板',
    taskDescription: '这是一个测试任务模板',
    durationDays: 7,
    attachments: [],
    workIdsStr: '',
    selfAssessmentItems: [],
    status: 1,
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01'),
  };

  const mockCourseSettings: Partial<CourseSettings> = {
    id: 1,
    courseId: 1,
    templateId: 1,
    requiredPoints: 100,
    autoCreateTasks: 1,
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01'),
  };

  beforeEach(async () => {
    // Mock DataSource with transaction method
    const mockDataSource = {
      transaction: jest.fn().mockImplementation(async (callback) => {
        const mockManager = {
          find: jest.fn(),
          create: jest.fn(),
          save: jest.fn(),
          delete: jest.fn(),
        };
        return await callback(mockManager);
      }),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ManagementService,
        {
          provide: getRepositoryToken(CourseSeries),
          useValue: createMockRepository(),
        },
        {
          provide: getRepositoryToken(Course),
          useValue: createMockRepository(),
        },
        {
          provide: getRepositoryToken(TaskTemplate),
          useValue: createMockRepository(),
        },
        {
          provide: getRepositoryToken(CourseSettings),
          useValue: createMockRepository(),
        },
        {
          provide: getRepositoryToken(CourseSeriesTag),
          useValue: createMockRepository(),
        },
        {
          provide: getRepositoryToken(CourseTag),
          useValue: createMockRepository(),
        },
        {
          provide: DataSource,
          useValue: mockDataSource,
        },
      ],
    }).compile();

    service = module.get<ManagementService>(ManagementService);
    courseSeriesRepository = module.get<MockRepository>(
      getRepositoryToken(CourseSeries),
    );
    courseRepository = module.get<MockRepository>(
      getRepositoryToken(Course),
    );
    taskTemplateRepository = module.get<MockRepository>(
      getRepositoryToken(TaskTemplate),
    );
    courseSettingsRepository = module.get<MockRepository>(
      getRepositoryToken(CourseSettings),
    );
    courseSeriesTagRepository = module.get<MockRepository>(
      getRepositoryToken(CourseSeriesTag),
    );
    courseTagRepository = module.get<MockRepository>(
      getRepositoryToken(CourseTag),
    );
    dataSource = module.get<any>(DataSource);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getMyCourseSeries', () => {
    it('应该返回用户的系列课程列表', async () => {
      const mockQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        getRawOne: jest.fn().mockResolvedValue({
          totalCourses: '2',
          videoCourses: '1',
          documentCourses: '1',
          resourcesCount: '3',
        }),
      };

      courseSeriesRepository.findAndCount!.mockResolvedValue([
        [mockCourseSeries],
        1,
      ]);
      courseRepository.createQueryBuilder!.mockReturnValue(mockQueryBuilder);
      courseRepository.count!.mockResolvedValue(2);

      const result = await service.getMyCourseSeries(123, 1, 10);

      expect(result).toEqual({
        items: expect.arrayContaining([
          expect.objectContaining({
            ...mockCourseSeries,
            _contentSummary: {
              videoCourseCount: 1,
              documentCourseCount: 1,
              totalResourcesCount: 3,
              completionRate: 1,
            },
          }),
        ]),
        page: 1,
        pageSize: 10,
        total: 1,
        totalPages: 1,
      });

      expect(courseSeriesRepository.findAndCount).toHaveBeenCalledWith({
        where: { creatorId: 123 },
        skip: 0,
        take: 10,
        order: { createdAt: 'DESC' },
      });
    });

    it('应该支持状态过滤', async () => {
      const mockQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        getRawOne: jest.fn().mockResolvedValue({
          totalCourses: '0',
          videoCourses: '0',
          documentCourses: '0',
          resourcesCount: '0',
        }),
      };

      courseSeriesRepository.findAndCount!.mockResolvedValue([[], 0]);
      courseRepository.createQueryBuilder!.mockReturnValue(mockQueryBuilder);
      courseRepository.count!.mockResolvedValue(0);

      await service.getMyCourseSeries(123, 1, 10, 1);

      expect(courseSeriesRepository.findAndCount).toHaveBeenCalledWith({
        where: { creatorId: 123, status: 1 },
        skip: 0,
        take: 10,
        order: { createdAt: 'DESC' },
      });
    });

    it('应该支持关键词搜索', async () => {
      const mockQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        getRawOne: jest.fn().mockResolvedValue({
          totalCourses: '0',
          videoCourses: '0',
          documentCourses: '0',
          resourcesCount: '0',
        }),
      };

      courseSeriesRepository.findAndCount!.mockResolvedValue([[], 0]);
      courseRepository.createQueryBuilder!.mockReturnValue(mockQueryBuilder);
      courseRepository.count!.mockResolvedValue(0);

      await service.getMyCourseSeries(123, 1, 10, undefined, '测试');

      expect(courseSeriesRepository.findAndCount).toHaveBeenCalledWith({
        where: { creatorId: 123, title: Like('%测试%') },
        skip: 0,
        take: 10,
        order: { createdAt: 'DESC' },
      });
    });
  });

  describe('findCourseSeriesById', () => {
    it('应该返回指定ID的课程系列', async () => {
      courseSeriesRepository.findOne!.mockResolvedValue(mockCourseSeries);

      const result = await service.findCourseSeriesById(1);

      expect(result).toEqual(mockCourseSeries);
      expect(courseSeriesRepository.findOne).toHaveBeenCalledWith({
        where: { id: 1 },
      });
    });

    it('当系列不存在时应该抛出NotFoundException', async () => {
      courseSeriesRepository.findOne!.mockResolvedValue(null);

      await expect(service.findCourseSeriesById(999)).rejects.toThrow(
        NotFoundException,
      );
      await expect(service.findCourseSeriesById(999)).rejects.toThrow(
        '系列ID为999的课程系列不存在',
      );
    });
  });

  describe('createCourseSeries', () => {
    it('应该成功创建课程系列', async () => {
      const createData = {
        title: '新系列课程',
        description: '新系列描述',
        coverImage: 'https://example.com/new-cover.jpg',
        category: 0,
        projectMembers: '张三',
        creatorId: 123,
      };

      courseSeriesRepository.create!.mockReturnValue(mockCourseSeries);
      courseSeriesRepository.save!.mockResolvedValue(mockCourseSeries);

      const result = await service.createCourseSeries(createData,2751);

      expect(result).toEqual(mockCourseSeries);
      expect(courseSeriesRepository.create).toHaveBeenCalledWith({
        title: createData.title,
        description: createData.description,
        coverImage: createData.coverImage,
        category: 0,
        status: 0,
        projectMembers: createData.projectMembers,
        creatorId: createData.creatorId,
        totalCourses: 0,
        totalStudents: 0,
      });
      expect(courseSeriesRepository.save).toHaveBeenCalledWith(mockCourseSeries);
    });

    it('当标题为空时应该抛出BadRequestException', async () => {
      const createData = {
        title: '',
        creatorId: 123,
      };

      await expect(service.createCourseSeries(createData,2751)).rejects.toThrow(
        BadRequestException,
      );
      await expect(service.createCourseSeries(createData,2751)).rejects.toThrow(
        '系列名称不能为空',
      );
    });

    it('当标题只有空格时应该抛出BadRequestException', async () => {
      const createData = {
        title: '   ',
        creatorId: 123,
      };

      await expect(service.createCourseSeries(createData,2751)).rejects.toThrow(
        BadRequestException,
      );
    });

    it('当数据库操作失败时应该抛出BadRequestException', async () => {
      const createData = {
        title: '测试系列',
        creatorId: 123,
      };

      courseSeriesRepository.create!.mockReturnValue(mockCourseSeries);
      courseSeriesRepository.save!.mockRejectedValue(
        new Error('数据库连接失败'),
      );

      await expect(service.createCourseSeries(createData,2751)).rejects.toThrow(
        BadRequestException,
      );
      await expect(service.createCourseSeries(createData,2751)).rejects.toThrow(
        '创建系列课程失败: 数据库连接失败',
      );
    });
  });

  describe('updateCourseSeries', () => {
    it('应该成功更新课程系列', async () => {
      const updateData = {
        title: '更新后的标题',
        description: '更新后的描述',
      };

      courseSeriesRepository.findOne!.mockResolvedValue(mockCourseSeries);
      courseSeriesRepository.save!.mockResolvedValue({
        ...mockCourseSeries,
        ...updateData,
      });

      const result = await service.updateCourseSeries(1, updateData, 123);

      expect(result).toEqual({
        ...mockCourseSeries,
        ...updateData,
      });
      expect(courseSeriesRepository.save).toHaveBeenCalled();
    });

    it('当用户无权限时应该抛出ForbiddenException', async () => {
      const updateData = { title: '更新标题' };

      courseSeriesRepository.findOne!.mockResolvedValue(mockCourseSeries);

      await expect(
        service.updateCourseSeries(1, updateData, 456),
      ).rejects.toThrow(ForbiddenException);
      await expect(
        service.updateCourseSeries(1, updateData, 456),
      ).rejects.toThrow('无权限更新此课程系列');
    });

    it('应该过滤掉只读字段', async () => {
      const updateData = {
        id: 999,
        createdAt: new Date(),
        updatedAt: new Date(),
        creatorId: 456,
        title: '新标题',
      };

      courseSeriesRepository.findOne!.mockResolvedValue(mockCourseSeries);
      courseSeriesRepository.save!.mockResolvedValue(mockCourseSeries);

      await service.updateCourseSeries(1, updateData, 123);

      // 验证只读字段被删除
      expect(courseSeriesRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          title: '新标题',
        }),
      );
      expect(courseSeriesRepository.save).toHaveBeenCalledWith(
        expect.not.objectContaining({
          id: 999,
          creatorId: 456,
        }),
      );
    });
  });

  describe('removeCourseSeries', () => {
    it('应该成功删除课程系列', async () => {
      courseSeriesRepository.findOne!.mockResolvedValue(mockCourseSeries);
      courseSeriesRepository.remove!.mockResolvedValue(mockCourseSeries);

      const result = await service.removeCourseSeries(1, 123);

      expect(result).toEqual({
        success: true,
        message: 'ID为1的课程系列已成功删除',
      });
      expect(courseSeriesRepository.remove).toHaveBeenCalledWith(
        mockCourseSeries,
      );
    });

    it('当用户无权限时应该抛出ForbiddenException', async () => {
      courseSeriesRepository.findOne!.mockResolvedValue(mockCourseSeries);

      await expect(service.removeCourseSeries(1, 456)).rejects.toThrow(
        ForbiddenException,
      );
      await expect(service.removeCourseSeries(1, 456)).rejects.toThrow(
        '无权限删除此课程系列',
      );
    });
  });

  describe('publishCourseSeries', () => {
    it('应该成功发布课程系列', async () => {
      const mockQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        getRawOne: jest.fn().mockResolvedValue({
          videoCourseCount: '2',
          documentCourseCount: '1',
          totalVideoDuration: '7200',
          totalResourcesCount: '5',
          publishedCourses: '2',
          totalCourses: '3',
        }),
      };

      courseSeriesRepository.findOne!.mockResolvedValue(mockCourseSeries);
      courseRepository.count!.mockResolvedValue(3);
      courseRepository.createQueryBuilder!.mockReturnValue(mockQueryBuilder);
      courseSeriesRepository.save!.mockResolvedValue({
        ...mockCourseSeries,
        status: 1,
      });

      const result = await service.publishCourseSeries(1, 123);

      expect(result).toEqual({
        success: true,
        message: '课程系列发布成功',
        data: { ...mockCourseSeries, status: 1 },
        publishStats: {
          videoCourseCount: 2,
          documentCourseCount: 1,
          totalVideoDuration: 7200,
          totalResourcesCount: 5,
          publishedCourses: 2,
          totalCourses: 3,
        },
      });
    });

    it('当用户无权限时应该抛出ForbiddenException', async () => {
      courseSeriesRepository.findOne!.mockResolvedValue(mockCourseSeries);

      await expect(service.publishCourseSeries(1, 456)).rejects.toThrow(
        ForbiddenException,
      );
      await expect(service.publishCourseSeries(1, 456)).rejects.toThrow(
        '无权限发布此课程系列',
      );
    });

    it('当系列中没有课程时应该抛出BadRequestException', async () => {
      courseSeriesRepository.findOne!.mockResolvedValue(mockCourseSeries);
      courseRepository.count!.mockResolvedValue(0);

      await expect(service.publishCourseSeries(1, 123)).rejects.toThrow(
        BadRequestException,
      );
      await expect(service.publishCourseSeries(1, 123)).rejects.toThrow(
        '发布失败：课程系列中至少需要包含一个课程',
      );
    });
  });

  describe('getSeriesCourses', () => {
    it('应该返回系列下的课程列表', async () => {
      courseSeriesRepository.findOne!.mockResolvedValue(mockCourseSeries);
      courseRepository.count!.mockResolvedValue(1);
      courseRepository.find!.mockResolvedValue([mockCourse]);

      const result = await service.getSeriesCourses(1, undefined, 1, 20, 123);

      expect(result).toEqual({
        seriesId: 1,
        courses: [mockCourse],
        total: 1,
        page: 1,
        pageSize: 20,
      });

      expect(courseRepository.find).toHaveBeenCalledWith({
        where: { seriesId: 1 },
        order: { orderIndex: 'ASC' },
        skip: 0,
        take: 20,
      });
    });

    it('应该支持状态过滤', async () => {
      courseSeriesRepository.findOne!.mockResolvedValue(mockCourseSeries);
      courseRepository.count!.mockResolvedValue(0);
      courseRepository.find!.mockResolvedValue([]);

      await service.getSeriesCourses(1, 1, 1, 20, 123);

      expect(courseRepository.count).toHaveBeenCalledWith({
        where: { seriesId: 1, status: 1 },
      });
      expect(courseRepository.find).toHaveBeenCalledWith({
        where: { seriesId: 1, status: 1 },
        order: { orderIndex: 'ASC' },
        skip: 0,
        take: 20,
      });
    });

    it('当用户无权限时应该抛出ForbiddenException', async () => {
      courseSeriesRepository.findOne!.mockResolvedValue(mockCourseSeries);

      await expect(
        service.getSeriesCourses(1, undefined, 1, 20, 456),
      ).rejects.toThrow(ForbiddenException);
      await expect(
        service.getSeriesCourses(1, undefined, 1, 20, 456),
      ).rejects.toThrow('无权限查看此系列课程');
    });
  });

  describe('createCourse', () => {
    it('应该成功创建课程', async () => {
      const createData = {
        seriesId: 1,
        title: '新课程',
        description: '新课程描述',
        coverImage: 'https://example.com/course.jpg',
        hasVideo: 1,
        hasDocument: 0,
        hasAudio: 0,
        videoDuration: 1800,
        contentConfig: { type: 'video' },
        teachingInfo: [{ title: '教学要点1' }],
        additionalResources: [{ name: '资源1' }],
        creatorId: 123,
      };

      const mockQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        getRawOne: jest.fn().mockResolvedValue({ maxOrder: 2 }),
      };

      courseSeriesRepository.findOne!.mockResolvedValue(mockCourseSeries);
      courseRepository.createQueryBuilder!.mockReturnValue(mockQueryBuilder);
      courseRepository.create!.mockReturnValue(mockCourse);
      courseRepository.save!.mockResolvedValue(mockCourse);
      courseRepository.count!.mockResolvedValue(1);
      courseSeriesRepository.update!.mockResolvedValue({ affected: 1 });

      const result = await service.createCourse(createData,2751);

      expect(result).toEqual(mockCourse);
      expect(courseRepository.create).toHaveBeenCalledWith({
        seriesId: 1,
        title: '新课程',
        description: '新课程描述',
        coverImage: 'https://example.com/course.jpg',
        hasVideo: 1,
        hasDocument: 0,
        hasAudio: 0,
        videoDuration: 1800,
        contentConfig: { type: 'video' },
        teachingInfo: [{ title: '教学要点1' }],
        additionalResources: [{ name: '资源1' }],
        orderIndex: 3,
        status: 0,
        creatorId: 123,
      });
    });

    it('当课程标题为空时应该抛出BadRequestException', async () => {
      const createData = {
        seriesId: 1,
        title: '',
        creatorId: 123,
      };

      await expect(service.createCourse(createData,2751)).rejects.toThrow(
        BadRequestException,
      );
      await expect(service.createCourse(createData,2751)).rejects.toThrow(
        '课程标题不能为空',
      );
    });

    it('当未指定系列ID时应该抛出BadRequestException', async () => {
      const createData = {
        title: '测试课程',
        creatorId: 123
      };

      await expect(service.createCourse(createData,2751)).rejects.toThrow(
        BadRequestException,
      );
      await expect(service.createCourse(createData,2751)).rejects.toThrow(
        '必须指定所属系列ID',
      );
    });

    it('当系列不存在时应该抛出NotFoundException', async () => {
      const createData = {
        seriesId: 999,
        title: '测试课程',
        creatorId: 123,
      };

      courseSeriesRepository.findOne!.mockResolvedValue(null);

      await expect(service.createCourse(createData,2751)).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe('setCourseSettings', () => {
    it('应该成功创建新的课程设置', async () => {
      const settingsData = {
        templateId: 1,
        requiredPoints: 100,
        autoCreateTasks: 1,
      };

      courseRepository.findOne!.mockResolvedValue(mockCourse);
      courseSeriesRepository.findOne!.mockResolvedValue(mockCourseSeries);
      courseSettingsRepository.findOne!.mockResolvedValue(null);
      courseSettingsRepository.create!.mockReturnValue(mockCourseSettings);
      courseSettingsRepository.save!.mockResolvedValue(mockCourseSettings);

      const result = await service.setCourseSettings(1, settingsData, 123);

      expect(result).toEqual({
        success: true,
        message: '课程配置设置成功',
        settings: mockCourseSettings,
      });

      expect(courseSettingsRepository.create).toHaveBeenCalledWith({
        courseId: 1,
        templateId: 1,
        requiredPoints: 100,
        autoCreateTasks: 1,
      });
    });

    it('应该成功更新现有的课程设置', async () => {
      const settingsData = {
        templateId: 2,
        requiredPoints: 200,
      };

      const existingSettings = { ...mockCourseSettings };

      courseRepository.findOne!.mockResolvedValue(mockCourse);
      courseSeriesRepository.findOne!.mockResolvedValue(mockCourseSeries);
      courseSettingsRepository.findOne!.mockResolvedValue(existingSettings);
      courseSettingsRepository.save!.mockResolvedValue({
        ...existingSettings,
        ...settingsData,
      });

      const result = await service.setCourseSettings(1, settingsData, 123);

      expect(result.success).toBe(true);
      expect(courseSettingsRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          templateId: 2,
          requiredPoints: 200,
        }),
      );
    });

    it('当课程不存在时应该抛出NotFoundException', async () => {
      courseRepository.findOne!.mockResolvedValue(null);

      await expect(
        service.setCourseSettings(999, {}, 123),
      ).rejects.toThrow(NotFoundException);
      await expect(
        service.setCourseSettings(999, {}, 123),
      ).rejects.toThrow('课程ID为999的课程不存在');
    });

    it('当用户无权限时应该抛出ForbiddenException', async () => {
      courseRepository.findOne!.mockResolvedValue(mockCourse);
      courseSeriesRepository.findOne!.mockResolvedValue(mockCourseSeries);

      await expect(
        service.setCourseSettings(1, {}, 456),
      ).rejects.toThrow(ForbiddenException);
      await expect(
        service.setCourseSettings(1, {}, 456),
      ).rejects.toThrow('无权限设置此课程配置');
    });

    it('当模板ID不存在时应该抛出BadRequestException', async () => {
      const settingsData = { templateId: 999 };

      courseRepository.findOne!.mockResolvedValue(mockCourse);
      courseSeriesRepository.findOne!.mockResolvedValue(mockCourseSeries);
      courseSettingsRepository.findOne!.mockResolvedValue(null);
      courseSettingsRepository.create!.mockReturnValue(mockCourseSettings);
      courseSettingsRepository.save!.mockRejectedValue(
        new Error('foreign key constraint fails'),
      );

      await expect(
        service.setCourseSettings(1, settingsData, 123),
      ).rejects.toThrow(BadRequestException);
      await expect(
        service.setCourseSettings(1, settingsData, 123),
      ).rejects.toThrow('设置失败：指定的模板ID不存在，请输入有效的模板ID');
    });
  });

  describe('addTaskTemplate', () => {
    it('应该成功添加任务模板', async () => {
      const templateData = {
        taskName: '新任务模板',
        taskDescription: '任务模板描述',
        durationDays: 14,
        attachments: [{ name: '附件1', url: 'https://example.com/file1.pdf' }],
        workIdsStr: '1,2,3',
        selfAssessmentItems: [
          { content: '自评项1', sequence: 1 },
          { content: '自评项2', sequence: 2 },
        ],
      };

      courseRepository.findOne!.mockResolvedValue(mockCourse);
      courseSeriesRepository.findOne!.mockResolvedValue(mockCourseSeries);
      taskTemplateRepository.create!.mockReturnValue(mockTaskTemplate);
      taskTemplateRepository.save!.mockResolvedValue(mockTaskTemplate);

      const result = await service.addTaskTemplate(1, templateData, 123);

      expect(result).toEqual(mockTaskTemplate);
      expect(taskTemplateRepository.create).toHaveBeenCalledWith({
        courseId: 1,
        taskName: '新任务模板',
        taskDescription: '任务模板描述',
        durationDays: 14,
        attachments: [{ name: '附件1', url: 'https://example.com/file1.pdf' }],
        workIdsStr: '1,2,3',
        selfAssessmentItems: [
          { content: '自评项1', sequence: 1 },
          { content: '自评项2', sequence: 2 },
        ],
        status: 1,
      });
      expect(taskTemplateRepository.save).toHaveBeenCalledWith(mockTaskTemplate);
    });

    it('应该使用默认值创建任务模板', async () => {
      const templateData = {
        taskName: '简单任务模板',
      };

      courseRepository.findOne!.mockResolvedValue(mockCourse);
      courseSeriesRepository.findOne!.mockResolvedValue(mockCourseSeries);
      taskTemplateRepository.create!.mockReturnValue(mockTaskTemplate);
      taskTemplateRepository.save!.mockResolvedValue(mockTaskTemplate);

      await service.addTaskTemplate(1, templateData, 123);

      expect(taskTemplateRepository.create).toHaveBeenCalledWith({
        courseId: 1,
        taskName: '简单任务模板',
        taskDescription: undefined,
        durationDays: 7,
        attachments: [],
        workIdsStr: '',
        selfAssessmentItems: [],
        status: 1,
      });
    });

    it('当课程不存在时应该抛出NotFoundException', async () => {
      courseRepository.findOne!.mockResolvedValue(null);

      await expect(
        service.addTaskTemplate(999, { taskName: '测试' }, 123),
      ).rejects.toThrow(NotFoundException);
      await expect(
        service.addTaskTemplate(999, { taskName: '测试' }, 123),
      ).rejects.toThrow('课程ID为999的课程不存在');
    });

    it('当用户无权限时应该抛出ForbiddenException', async () => {
      courseRepository.findOne!.mockResolvedValue(mockCourse);
      courseSeriesRepository.findOne!.mockResolvedValue(mockCourseSeries);

      await expect(
        service.addTaskTemplate(1, { taskName: '测试' }, 456),
      ).rejects.toThrow(ForbiddenException);
      await expect(
        service.addTaskTemplate(1, { taskName: '测试' }, 456),
      ).rejects.toThrow('无权限为此课程添加任务模板');
    });

    it('当任务名称为空时应该抛出BadRequestException', async () => {
      courseRepository.findOne!.mockResolvedValue(mockCourse);
      courseSeriesRepository.findOne!.mockResolvedValue(mockCourseSeries);

      await expect(
        service.addTaskTemplate(1, { taskName: '' }, 123),
      ).rejects.toThrow(BadRequestException);
      await expect(
        service.addTaskTemplate(1, { taskName: '' }, 123),
      ).rejects.toThrow('任务名称不能为空');
    });

    it('当任务名称只有空格时应该抛出BadRequestException', async () => {
      courseRepository.findOne!.mockResolvedValue(mockCourse);
      courseSeriesRepository.findOne!.mockResolvedValue(mockCourseSeries);

      await expect(
        service.addTaskTemplate(1, { taskName: '   ' }, 123),
      ).rejects.toThrow(BadRequestException);
      await expect(
        service.addTaskTemplate(1, { taskName: '   ' }, 123),
      ).rejects.toThrow('任务名称不能为空');
    });
  });

  // 测试私有方法 getCourseSeriesPublishStats
  describe('getCourseSeriesPublishStats (private method)', () => {
    it('应该通过 publishCourseSeries 间接测试统计功能', async () => {
      const mockQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        getRawOne: jest.fn().mockResolvedValue({
          videoCourseCount: '3',
          documentCourseCount: '2',
          totalVideoDuration: '10800',
          totalResourcesCount: '8',
          publishedCourses: '4',
          totalCourses: '5',
        }),
      };

      courseSeriesRepository.findOne!.mockResolvedValue(mockCourseSeries);
      courseRepository.count!.mockResolvedValue(5);
      courseRepository.createQueryBuilder!.mockReturnValue(mockQueryBuilder);
      courseSeriesRepository.save!.mockResolvedValue({
        ...mockCourseSeries,
        status: 1,
      });

      const result = await service.publishCourseSeries(1, 123);

      expect(result.publishStats).toEqual({
        videoCourseCount: 3,
        documentCourseCount: 2,
        totalVideoDuration: 10800,
        totalResourcesCount: 8,
        publishedCourses: 4,
        totalCourses: 5,
      });

      // 验证查询构建器的调用
      expect(mockQueryBuilder.select).toHaveBeenCalledWith('COUNT(*)', 'totalCourses');
      expect(mockQueryBuilder.addSelect).toHaveBeenCalledWith(
        'SUM(CASE WHEN course.status = 1 THEN 1 ELSE 0 END)',
        'publishedCourses',
      );
      expect(mockQueryBuilder.where).toHaveBeenCalledWith(
        'course.series_id = :seriesId',
        { seriesId: 1 },
      );
    });
  });

  // 测试私有方法 updateSeriesCourseCount
  describe('updateSeriesCourseCount (private method)', () => {
    it('应该通过 createCourse 间接测试课程数量更新功能', async () => {
      const createData = {
        seriesId: 1,
        title: '测试课程',
        creatorId: 123,
      };

      const mockQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        getRawOne: jest.fn().mockResolvedValue({ maxOrder: 0 }),
      };

      courseSeriesRepository.findOne!.mockResolvedValue(mockCourseSeries);
      courseRepository.createQueryBuilder!.mockReturnValue(mockQueryBuilder);
      courseRepository.create!.mockReturnValue(mockCourse);
      courseRepository.save!.mockResolvedValue(mockCourse);
      courseRepository.count!.mockResolvedValue(2);
      courseSeriesRepository.update!.mockResolvedValue({ affected: 1 });

      await service.createCourse(createData,2751);

      // 验证课程数量更新被调用
      expect(courseRepository.count).toHaveBeenCalledWith({
        where: { seriesId: 1 },
      });
      expect(courseSeriesRepository.update).toHaveBeenCalledWith(1, {
        totalCourses: 2,
      });
    });
  });

  // 边界情况和错误处理测试
  describe('边界情况和错误处理', () => {
    it('getMyCourseSeries - 当查询统计信息失败时应该使用默认值', async () => {
      const mockQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        getRawOne: jest.fn().mockResolvedValue(null),
      };

      courseSeriesRepository.findAndCount!.mockResolvedValue([
        [mockCourseSeries],
        1,
      ]);
      courseRepository.createQueryBuilder!.mockReturnValue(mockQueryBuilder);
      courseRepository.count!.mockResolvedValue(0);

      const result = await service.getMyCourseSeries(123, 1, 10);

      expect((result.items[0] as any)._contentSummary).toEqual({
        videoCourseCount: 0,
        documentCourseCount: 0,
        totalResourcesCount: 0,
        completionRate: 0,
      });
    });

    it('getMyCourseSeries - 分页计算应该正确', async () => {
      const mockQueryBuilder = {
        select: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        getRawOne: jest.fn().mockResolvedValue({
          totalCourses: '0',
          videoCourses: '0',
          documentCourses: '0',
          resourcesCount: '0',
        }),
      };

      courseSeriesRepository.findAndCount!.mockResolvedValue([[], 25]);
      courseRepository.createQueryBuilder!.mockReturnValue(mockQueryBuilder);
      courseRepository.count!.mockResolvedValue(0);

      const result = await service.getMyCourseSeries(123, 3, 10);

      expect(result.page).toBe(3);
      expect(result.pageSize).toBe(10);
      expect(result.total).toBe(25);
      expect(result.totalPages).toBe(3);
      expect(courseSeriesRepository.findAndCount).toHaveBeenCalledWith({
        where: { creatorId: 123 },
        skip: 20, // (3-1) * 10
        take: 10,
        order: { createdAt: 'DESC' },
      });
    });
  });
});
