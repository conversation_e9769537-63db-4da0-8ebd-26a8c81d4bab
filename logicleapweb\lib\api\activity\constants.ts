// 活动状态常量
export const ActivityStatus = {
  DRAFT: 0,     // 草稿
  PUBLISHED: 1, // 已发布
  ENDED: 2,     // 已结束
  CANCELLED: 3, // 已取消
  REVIEWING: 4  // 审核中
} as const;

// 活动类型常量
export const ActivityType = {
  WORK: 1,      // 作品活动
  IMAGE: 2,     // 图片活动
  OTHER: 3      // 其他活动
} as const;

// 内容类型常量（活动作品关联）
export const ContentType = {
  WORK: 1,     // 作品
  IMAGE: 2,    // 图片
  OTHER: 3     // 其他内容
} as const;

// 报名状态常量
export const SubmissionStatus = {
  CANCELLED: 0,    // 已取消
  SUBMITTED: 1,    // 已报名(旧版)
  APPROVED: 2,     // 已审核通过
  REJECTED: 3,     // 已拒绝
  REVIEWING: 4,    // 评审中
  AWARDED: 5,      // 已获奖
  PENDING: 6       // 审核中(新版)
} as const;

// 审核结果常量
export const AuditResult = {
  PENDING: 0,   // 待审核
  APPROVED: 1,  // 通过
  REJECTED: 2   // 拒绝
} as const;

// 任务状态常量
export const TaskStatus = {
  PENDING: 0,         // 待开始
  IN_PROGRESS: 1,     // 进行中
  SUBMITTED: 2,       // 已提交
  REVIEWED: 3,        // 已审核
  REJECTED: 4         // 审核不通过
} as const;

// 状态标签映射
export const StatusLabels = {
  [ActivityStatus.DRAFT]: '草稿',
  [ActivityStatus.PUBLISHED]: '已发布',
  [ActivityStatus.ENDED]: '已结束',
  [ActivityStatus.CANCELLED]: '已取消',
  [ActivityStatus.REVIEWING]: '审核中'
} as const;

export const SubmissionStatusLabels = {
  [SubmissionStatus.CANCELLED]: '已取消',
  [SubmissionStatus.SUBMITTED]: '已报名',
  [SubmissionStatus.APPROVED]: '已通过',
  [SubmissionStatus.REJECTED]: '已拒绝',
  [SubmissionStatus.REVIEWING]: '评审中',
  [SubmissionStatus.AWARDED]: '已获奖',
  [SubmissionStatus.PENDING]: '审核中'
} as const;

export const AuditResultLabels = {
  [AuditResult.PENDING]: '待审核',
  [AuditResult.APPROVED]: '通过',
  [AuditResult.REJECTED]: '拒绝'
} as const;

export const TaskStatusLabels = {
  [TaskStatus.PENDING]: '待开始',
  [TaskStatus.IN_PROGRESS]: '进行中',
  [TaskStatus.SUBMITTED]: '已提交',
  [TaskStatus.REVIEWED]: '已审核',
  [TaskStatus.REJECTED]: '审核不通过'
} as const;

// 状态颜色映射
export const StatusColors = {
  [ActivityStatus.DRAFT]: 'gray',
  [ActivityStatus.PUBLISHED]: 'blue',
  [ActivityStatus.ENDED]: 'green',
  [ActivityStatus.CANCELLED]: 'red',
  [ActivityStatus.REVIEWING]: 'orange'
} as const;

export const SubmissionStatusColors = {
  [SubmissionStatus.CANCELLED]: 'red',
  [SubmissionStatus.SUBMITTED]: 'blue',
  [SubmissionStatus.APPROVED]: 'green',
  [SubmissionStatus.REJECTED]: 'red',
  [SubmissionStatus.REVIEWING]: 'orange',
  [SubmissionStatus.AWARDED]: 'gold',
  [SubmissionStatus.PENDING]: 'orange'
} as const;

export const TaskStatusColors = {
  [TaskStatus.PENDING]: 'gray',
  [TaskStatus.IN_PROGRESS]: 'blue',
  [TaskStatus.SUBMITTED]: 'gold',
  [TaskStatus.REVIEWED]: 'green',
  [TaskStatus.REJECTED]: 'red'
} as const;
