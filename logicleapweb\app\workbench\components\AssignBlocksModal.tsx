'use client'

import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, But<PERSON>, Dropdown, Tag, Checkbox, Avatar, Divider } from 'antd';
import { BlockOutlined, EditOutlined, DeleteOutlined, EllipsisOutlined, EyeOutlined, AppstoreOutlined, <PERSON>LeftOutlined, FolderOutlined, StarOutlined, PlusOutlined, UserOutlined, CheckOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import { PermissionTemplateDisplay } from '../types/index';
import { useState, useEffect, useMemo } from 'react';
import PermissionModal from '@/components/permission-modal';
import PermissionTemplateModal from '@/components/permission-template-modal';
import { roleTemplateFolderApi } from '../../../lib/api/role-template-folder';
import { getOfficialTemplates, getTemplateInfo, batchAddUserJoinRole } from '../../../lib/api/role';
import { GetNotification } from 'logic-common/dist/components/Notification';

interface Student {
  userId: number;
  schoolId: number;
  classId: number;
  studentNumber: string;
  nickName: string;
  avatarUrl?: string;
  id: number;
  currentTemplate?: {
    templateId: number;
    templateName: string;
  };
}

interface AssignBlocksModalProps {
  visible: boolean;
  onCancel: () => void;
  isClassCardAssign: boolean;
  loadingTemplates: boolean;
  templates: PermissionTemplateDisplay[];
  studentTemplateUsage: Record<number, number>;
  teacherTemplate?: { templateId: number };
  onSelectTemplate: (templateId: number, selectedStudents?: number[]) => void;
  onTemplateUsageClick: (e: React.MouseEvent, template: PermissionTemplateDisplay) => void;
  userId?: number;
  onRefreshTemplates?: () => void;
  // 新增的props用于学生选择
  students: Student[];
  selectedStudentIds: number[];
  userRoles?: Array<{ userId: number; roleId: number }>;
  onSuccess?: () => void;
  onUpdateStudentTemplate?: (studentIds: number[], templateInfo: { templateId: number; templateName: string; isOfficial?: boolean }) => void;
}

export const AssignBlocksModal: React.FC<AssignBlocksModalProps> = ({
  visible,
  onCancel,
  isClassCardAssign,
  loadingTemplates,
  templates,
  studentTemplateUsage,
  teacherTemplate,
  onSelectTemplate,
  onTemplateUsageClick,
  userId = 0,
  onRefreshTemplates,
  students,
  selectedStudentIds,
  userRoles,
  onSuccess,
  onUpdateStudentTemplate,
}) => {
  const [activeTab, setActiveTab] = useState('my');
  const [isPermissionModalVisible, setIsPermissionModalVisible] = useState(false);
  const [isCreateTemplateModalVisible, setIsCreateTemplateModalVisible] = useState(false);
  const [selectedTemplateId, setSelectedTemplateId] = useState<number | null>(null);
  const [currentView, setCurrentView] = useState<'folders' | 'templates' | 'students'>('students');
  const [currentFolder, setCurrentFolder] = useState<any>(null);
  const [folders, setFolders] = useState<any[]>([]);
  const [loadingFolders, setLoadingFolders] = useState(false);
  const [folderTemplates, setFolderTemplates] = useState<PermissionTemplateDisplay[]>([]);
  const [loadingFolderTemplates, setLoadingFolderTemplates] = useState(false);
  const [specialTemplate, setSpecialTemplate] = useState<PermissionTemplateDisplay | null>(null);
  const [loadingSpecialTemplate, setLoadingSpecialTemplate] = useState(false);
  
  // 学生选择相关状态
  const [selectedStudentsForAssign, setSelectedStudentsForAssign] = useState<number[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<PermissionTemplateDisplay | null>(null);
  const [isAssigning, setIsAssigning] = useState(false);
  
  const notification = GetNotification();

  // 初始化选中的学生
  useEffect(() => {
    if (visible) {
      if (isClassCardAssign && selectedStudentIds.length === 1) {
        // 单个学生分配模式
        setSelectedStudentsForAssign(selectedStudentIds);
        setCurrentView('templates');
      } else if (selectedStudentIds.length > 0) {
        // 批量分配模式
        setSelectedStudentsForAssign(selectedStudentIds);
        setCurrentView('students');
      } else {
        // 默认显示学生选择界面
        setSelectedStudentsForAssign([]);
        setCurrentView('students');
      }
    }
  }, [visible, isClassCardAssign, selectedStudentIds]);

  // 处理学生选择
  const handleStudentSelect = (studentId: number) => {
    setSelectedStudentsForAssign(prev => {
      if (prev.includes(studentId)) {
        return prev.filter(id => id !== studentId);
      } else {
        return [...prev, studentId];
      }
    });
  };

  // 全选/取消全选
  const handleSelectAll = () => {
    if (selectedStudentsForAssign.length === students.length) {
      setSelectedStudentsForAssign([]);
    } else {
      setSelectedStudentsForAssign(students.map(s => s.userId));
    }
  };

  // 继续到模板选择
  const handleContinueToTemplates = () => {
    if (selectedStudentsForAssign.length === 0) {
      notification.warning('请至少选择一名学生');
      return;
    }
    setCurrentView('templates');
    setActiveTab('my');
  };

  // 返回学生选择
  const handleBackToStudents = () => {
    setCurrentView('students');
    setSelectedTemplate(null);
  };

  // 处理模板选择
  const handleTemplateSelect = (template: PermissionTemplateDisplay) => {
    setSelectedTemplate(template);
  };

  // 确认分配模板
  const handleConfirmAssign = async () => {
    console.log('=== 开始分配模板 ===');
    console.log('selectedTemplate:', selectedTemplate);
    console.log('selectedStudentsForAssign:', selectedStudentsForAssign);
    console.log('userRoles:', userRoles);

    if (!selectedTemplate || selectedStudentsForAssign.length === 0) {
      notification.error('请选择模板和学生');
      return;
    }

    setIsAssigning(true);
    notification.info('正在分配模板...');

    try {
      // 准备用户数据
      const usersData = selectedStudentsForAssign
        .map(userId => {
          const userInfo = userRoles?.find(u => u.userId === userId);
          console.log(`用户${userId}的角色信息:`, userInfo);
          if (!userInfo?.roleId) {
            console.warn(`用户${userId}没有找到角色信息`);
            return null;
          }
          return {
            userId: userId,
            roleId: userInfo.roleId,
            templateId: selectedTemplate.id,
            originalTemplateId: selectedTemplate.id
          };
        })
        .filter((item): item is { userId: number; roleId: number; templateId: number; originalTemplateId: number } => item !== null);

      console.log('过滤后的用户数据:', usersData);

      if (usersData.length === 0) {
        notification.error('无有效用户可分配');
        console.error('没有有效的用户数据可分配');
        return;
      }

      console.log('准备分配模板:', {
        templateId: selectedTemplate.id,
        selectedStudentsForAssign,
        usersData
      });

      // 调用批量分配API
      console.log('即将调用API，参数:', { users: usersData });
      const response = await batchAddUserJoinRole({ users: usersData });

      console.log('分配模板API响应:', response);
      console.log('分配模板API响应数据:', response.data);
      console.log('API响应中的data字段:', response.data.data);

      if (response.data.code === 200) {
        // 检查API响应的实际数据结构
        const responseData = response.data.data;
        const successCount = responseData?.successCount;
        const failCount = responseData?.failCount;
        const results = responseData?.results;

        console.log('分配结果:', { successCount, failCount, results });
        console.log('responseData完整内容:', responseData);

        // 根据API响应判断是否成功
        if (response.data.code === 200 && response.data.msg === '操作成功') {
          // 如果有具体的成功数量，使用它；否则使用选中的学生数量
          const actualSuccessCount = successCount || selectedStudentsForAssign.length;
          const actualFailCount = failCount || 0;

          notification.success(`成功为 ${actualSuccessCount} 名学生分配模板`);

          // 更新成功分配的学生的当前模板信息
          if (onUpdateStudentTemplate) {
            let successfulStudentIds: number[] = [];

            if (results && Array.isArray(results)) {
              // 如果API返回了详细结果，使用结果中的成功学生ID
              successfulStudentIds = results
                .filter((result: any) => result.success)
                .map((result: any) => result.userId);
            } else {
              // 如果API没有返回详细结果，假设所有学生都成功分配
              successfulStudentIds = selectedStudentsForAssign;
            }

            console.log('成功分配的学生ID:', successfulStudentIds);

            if (successfulStudentIds.length > 0) {
              onUpdateStudentTemplate(successfulStudentIds, {
                templateId: selectedTemplate.id,
                templateName: selectedTemplate.templateName,
                isOfficial: selectedTemplate.isOfficial
              });
            }
          }

          onSuccess?.();

          // 延迟关闭弹窗，确保用户能看到成功提示
          setTimeout(() => {
            onCancel();
          }, 1500);

          if (actualFailCount > 0) {
            notification.warning(`${actualFailCount} 名学生分配失败`);
          }
        } else {
          // 如果API返回的不是成功状态
          notification.error('分配模板失败：' + (response.data.msg || '未知错误'));
        }
      } else {
        console.log('API返回错误:', response.data);
        notification.error(response.data.message || '分配模板失败');
      }
    } catch (error: any) {
      console.error('=== 分配模板失败 ===');
      console.error('错误对象:', error);
      console.error('错误响应:', error.response);
      console.error('错误消息:', error.message);

      if (error.response) {
        console.error('HTTP状态码:', error.response.status);
        console.error('响应数据:', error.response.data);
        notification.error(error.response.data?.message || `请求失败 (${error.response.status})`);
      } else if (error.request) {
        console.error('网络请求失败:', error.request);
        notification.error('网络请求失败，请检查网络连接');
      } else {
        console.error('其他错误:', error.message);
        notification.error(error.message || '分配模板失败');
      }
    } finally {
      setIsAssigning(false);
    }
  };

  // 处理模板点击，显示模板详情
  const handleTemplateClick = (template: PermissionTemplateDisplay) => {
    setSelectedTemplateId(template.id);
    setIsPermissionModalVisible(true);
  };

  // 处理创建模板按钮点击
  const handleCreateTemplate = () => {
    setIsCreateTemplateModalVisible(true);
  };

  // 处理模板创建成功
  const handleCreateTemplateSuccess = () => {
    setIsCreateTemplateModalVisible(false);
    if (onRefreshTemplates) {
      onRefreshTemplates();
    }
    setActiveTab('my');
    if (activeTab === 'official' && currentView === 'folders' && currentFolder) {
      handleFolderClick(currentFolder);
    } else if (activeTab === 'official' && currentView === 'folders') {
      fetchFolders();
      fetchSpecialTemplate();
    }
  };

  // 获取特殊模板（userId为-1的模板）
  const fetchSpecialTemplate = async () => {
    if (activeTab !== 'official') return;

    setLoadingSpecialTemplate(true);
    try {
      const response = await getOfficialTemplates();
      if (response.data.code === 200) {
        const specialTemplateData = response.data.data.find((template: any) => template.userId === -1);

        if (specialTemplateData) {
          const templateInfoResponse = await getTemplateInfo(specialTemplateData.id);
          if (templateInfoResponse.data.code === 200) {
            const templateData = templateInfoResponse.data.data;
            setSpecialTemplate({
              id: templateData.id,
              templateName: templateData.templateName,
              templateDescription: templateData.templateDescription || '',
              createTime: templateData.createTime,
              isDefault: false,
              isOfficial: true,
              lastModified: templateData.updateTime || templateData.createTime
            });
          }
        }
      }
    } catch (error) {
      console.error('获取特殊模板失败:', error);
    } finally {
      setLoadingSpecialTemplate(false);
    }
  };

  // 获取文件夹列表
  const fetchFolders = async () => {
    if (activeTab !== 'official') return;
    
    setLoadingFolders(true);
    try {
      const res = await roleTemplateFolderApi.getFolderList();
      if (res.data.code === 200) {
        const foldersWithCount = await Promise.all(
          res.data.data.data.map(async (folder: any) => {
            const templatesRes = await roleTemplateFolderApi.getFolderTemplates(folder.id);
            return {
              ...folder,
              templateCount: templatesRes.data.code === 200 ? templatesRes.data.data.data.length : 0
            };
          })
        );
        setFolders(foldersWithCount);
      }
    } catch (error) {
      console.error('获取文件夹列表失败:', error);
    } finally {
      setLoadingFolders(false);
    }
  };

  // 处理文件夹点击
  const handleFolderClick = async (folder: any) => {
    setCurrentFolder(folder);
    setCurrentView('templates');
    setFolderTemplates([]);
    setLoadingFolderTemplates(true);

    try {
      const folderRes = await roleTemplateFolderApi.getFolderTemplates(folder.id);
      if (folderRes.data.code === 200) {
        const templatePromises = folderRes.data.data.data.map((item: { id: number }) =>
          roleTemplateFolderApi.getTemplateInfo(item.id)
        );
        const templateResults = await Promise.all(templatePromises);
        const templates = templateResults
          .filter(res => res.data.code === 200)
          .map(res => {
            const templateData = res.data.data;
            return {
              id: templateData.id,
              templateName: templateData.templateName,
              templateDescription: templateData.templateDescription || '',
              createTime: templateData.createTime,
              isDefault: false,
              isOfficial: true,
              lastModified: templateData.updateTime || templateData.createTime,
              roleId: templateData.roleId,
              userId: templateData.userId,
              status: templateData.status
            };
          });

        setFolderTemplates(templates);
      }
    } catch (error) {
      console.error('获取文件夹模板失败:', error);
    } finally {
      setLoadingFolderTemplates(false);
    }
  };

  // 返回文件夹列表
  const handleBackToFolders = () => {
    setCurrentView('folders');
    setCurrentFolder(null);
    setFolderTemplates([]);
  };

  // 当切换到官方模板标签时加载文件夹和特殊模板
  useEffect(() => {
    if (activeTab === 'official' && currentView === 'folders') {
      fetchFolders();
      fetchSpecialTemplate();
    }
  }, [activeTab, currentView]);

  // 监听模态框显示状态
  useEffect(() => {
    if (visible && activeTab === 'official' && currentView === 'folders') {
      fetchFolders();
      fetchSpecialTemplate();
    }
  }, [visible]);

  const modalWidth = useMemo(() => {
    return 900;
  }, []);

  return (
    <Modal
      title={
        <div className="flex items-center gap-4 pr-8">
          <span>分配积木</span>
          {currentView === 'templates' && (
            <div className="flex items-center gap-2 flex-1">
              <Button
                type={activeTab === 'my' ? 'primary' : 'default'}
                onClick={() => {
                  setActiveTab('my');
                  setCurrentView('templates');
                }}
                className="rounded-full min-w-[80px] whitespace-nowrap"
                size="small"
              >
                我的模板
              </Button>
              <Button
                type={activeTab === 'official' ? 'primary' : 'default'}
                onClick={() => {
                  setActiveTab('official');
                  setCurrentView('folders');
                }}
                className="rounded-full min-w-[80px] whitespace-nowrap"
                size="small"
              >
                官方模板
              </Button>
            </div>
          )}
          {currentView === 'templates' && (
            <Button
              type="primary"
              icon={<PlusOutlined />}
              size="small"
              onClick={handleCreateTemplate}
              className="rounded-full flex items-center"
            >
              创建模板
            </Button>
          )}
        </div>
      }
      open={visible}
      onCancel={() => {
        setSelectedStudentsForAssign([]);
        setSelectedTemplate(null);
        setCurrentView('students');
        onCancel();
      }}
      footer={null}
      width={modalWidth}
      centered
      modalRender={(modal) => (
        <div style={{
          margin: '20px 0',
          display: 'flex',
          flexDirection: 'column',
          height: 'fit-content',
          maxHeight: '90vh'
        }}>
          {modal}
        </div>
      )}
      style={{
        maxWidth: '90vw',
        top: 0,
        paddingBottom: 0
      }}
      styles={{
        body: {
          padding: '24px',
          maxHeight: 'calc(90vh - 110px)',
          overflow: 'auto'
        }
      }}
      className="custom-scrollbar-modal"
    >
      <style jsx global>{`
        .custom-scrollbar-modal .ant-modal-body {
          scrollbar-width: thin;
          scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
        }
        .custom-scrollbar-modal .ant-modal-body::-webkit-scrollbar {
          width: 6px;
        }
        .custom-scrollbar-modal .ant-modal-body::-webkit-scrollbar-thumb {
          background-color: rgba(0, 0, 0, 0.2);
          border-radius: 3px;
          transition: background-color 0.3s;
        }
        .custom-scrollbar-modal .ant-modal-body::-webkit-scrollbar-thumb:hover {
          background-color: rgba(0, 0, 0, 0.3);
        }
        .custom-scrollbar-modal .ant-modal-body::-webkit-scrollbar-track {
          background-color: transparent;
        }
      `}</style>
      
      {/* 学生选择界面 */}
      {currentView === 'students' && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium">选择要分配模板的学生</h3>
            <div className="flex items-center gap-2">
              <Button
                type={selectedStudentsForAssign.length === students.length ? 'primary' : 'default'}
                size="small"
                onClick={handleSelectAll}
              >
                {selectedStudentsForAssign.length === students.length ? '取消全选' : '全选'}
              </Button>
              <span className="text-sm text-gray-500">
                已选择 {selectedStudentsForAssign.length} / {students.length} 名学生
              </span>
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 max-h-96 overflow-y-auto">
            {students.map((student) => (
              <div
                key={student.userId}
                className={`
                  relative p-4 rounded-xl cursor-pointer border transition-all duration-200
                  ${selectedStudentsForAssign.includes(student.userId)
                    ? 'border-blue-500 bg-blue-50 shadow-md'
                    : 'border-gray-200 bg-white hover:border-blue-300 hover:shadow-sm'
                  }
                `}
                onClick={() => handleStudentSelect(student.userId)}
              >
                <div className="flex items-center gap-3">
                  <Avatar
                    size={40}
                    src={student.avatarUrl}
                    icon={<UserOutlined />}
                    className="flex-shrink-0"
                  />
                  <div className="flex-1 min-w-0">
                    <div className="font-medium text-gray-900 truncate">
                      {student.nickName || `学生${student.studentNumber || student.userId}`}
                    </div>
                    <div className="text-sm text-gray-500 truncate">
                      学号: {student.studentNumber}
                    </div>
                    {student.currentTemplate && (
                      <div className="text-xs text-blue-600 truncate">
                        当前: {student.currentTemplate.templateName}
                      </div>
                    )}
                  </div>
                  {selectedStudentsForAssign.includes(student.userId) && (
                    <CheckOutlined className="text-blue-500 text-lg" />
                  )}
                </div>
              </div>
            ))}
          </div>

          <div className="flex justify-end pt-4 border-t">
            <Button
              type="primary"
              onClick={handleContinueToTemplates}
              disabled={selectedStudentsForAssign.length === 0}
              className="px-6"
            >
              下一步：选择模板
            </Button>
          </div>
        </div>
      )}

      {/* 模板选择界面 */}
      {currentView === 'templates' && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Button
                icon={<ArrowLeftOutlined />}
                onClick={handleBackToStudents}
                size="small"
              >
                返回
              </Button>
              <h3 className="text-lg font-medium">
                为 {selectedStudentsForAssign.length} 名学生选择模板
              </h3>
            </div>
            {selectedTemplate && (
              <Button
                type="primary"
                onClick={() => {
                  console.log('确认分配按钮被点击');
                  handleConfirmAssign();
                }}
                loading={isAssigning}
                className="px-6"
              >
                确认分配
              </Button>
            )}
          </div>

          {selectedTemplate && (
            <Alert
              message={`已选择模板: ${selectedTemplate.templateName}`}
              type="success"
              showIcon
              action={
                <Button size="small" onClick={() => setSelectedTemplate(null)}>
                  重新选择
                </Button>
              }
            />
          )}

          <div className="space-y-3">
            {activeTab === 'official' && currentView === 'folders' ? (
              // 官方模板文件夹视图
              loadingFolders || loadingSpecialTemplate ? (
                <div className="flex justify-center py-8">
                  <Spin />
                </div>
              ) : (
                <div className="grid grid-cols-1 gap-3">
                  {/* 特殊模板（userId为-1的模板）置顶显示 */}
                  {specialTemplate && (
                    <div
                      key={`special-${specialTemplate.id}`}
                      className={`bg-white border p-4 rounded-xl cursor-pointer hover:shadow-md transition-all group relative z-10 ${
                        selectedTemplate?.id === specialTemplate.id ? 'border-blue-500 bg-blue-50' : 'border-gray-100'
                      }`}
                      onClick={() => handleTemplateSelect(specialTemplate)}
                    >
                      <div className="flex items-center justify-between gap-4">
                        <div className="flex items-center gap-3 flex-1 min-w-0">
                          <div className="w-10 h-10 rounded-lg bg-amber-50 group-hover:bg-amber-100 flex-shrink-0 flex items-center justify-center transition-colors">
                            <StarOutlined className="text-amber-500 text-lg" />
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2">
                              <span className="text-sm font-medium text-gray-800 group-hover:text-blue-600 transition-colors truncate max-w-[calc(100%-70px)]">
                                {specialTemplate.templateName}
                              </span>
                              <Tag color="gold" className="flex-shrink-0 rounded-full text-xs">
                                推荐
                              </Tag>
                              {selectedTemplate?.id === specialTemplate.id && (
                                <Tag color="blue" className="flex-shrink-0 rounded-full text-xs">
                                  已选择
                                </Tag>
                              )}
                            </div>
                            <p className="text-xs text-gray-500 mt-1">
                              推荐使用的模板
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2 flex-shrink-0">
                          <Button
                            type="default"
                            size="small"
                            className="flex-shrink-0"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleTemplateClick(specialTemplate);
                            }}
                          >
                            查看详情
                          </Button>
                        </div>
                      </div>
                      <div className="mt-3 text-xs text-gray-500">
                        {studentTemplateUsage[specialTemplate.id] ? (
                          <span
                            className="cursor-pointer hover:text-blue-500 transition-colors"
                            onClick={(e) => {
                              e.stopPropagation();
                              onTemplateUsageClick(e, specialTemplate);
                            }}
                          >
                            {studentTemplateUsage[specialTemplate.id]} 名学生正在使用
                          </span>
                        ) : (
                          <span>暂无学生使用</span>
                        )}
                      </div>
                    </div>
                  )}

                  {/* 文件夹列表 */}
                  {folders.map(folder => (
                    <div
                      key={folder.id}
                      className="bg-white border border-gray-100 p-4 rounded-xl cursor-pointer hover:shadow-md transition-all group relative z-10"
                      onClick={() => handleFolderClick(folder)}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="w-10 h-10 rounded-lg bg-blue-50 flex items-center justify-center group-hover:bg-blue-100 transition-colors">
                            <FolderOutlined className="text-blue-500 text-lg" />
                          </div>
                          <div>
                            <h3 className="text-sm font-medium text-gray-800 group-hover:text-blue-600 transition-colors">
                              {folder.folderName}
                              <Tag color="gold" className="ml-2 rounded-full text-xs">官方</Tag>
                            </h3>
                            <p className="text-xs text-gray-400 mt-0.5">
                              {folder.templateCount || 0} 个模板
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )
            ) : activeTab === 'official' && currentView === 'templates' ? (
              // 文件夹内模板列表视图
              <>
                <div className="flex items-center mb-4">
                  <Button
                    icon={<ArrowLeftOutlined />}
                    onClick={handleBackToFolders}
                    size="small"
                    className="mr-2"
                  >
                    返回
                  </Button>
                  <span className="text-base font-medium">
                    {currentFolder?.folderName}
                  </span>
                </div>
                {loadingFolderTemplates ? (
                  <div className="flex justify-center py-8">
                    <Spin />
                  </div>
                ) : (
                  folderTemplates.map(template => (
                    <div
                      key={template.id}
                      className={`bg-white border p-4 rounded-xl cursor-pointer hover:shadow-md transition-all group relative z-10 ${
                        selectedTemplate?.id === template.id ? 'border-blue-500 bg-blue-50' : 'border-gray-100'
                      }`}
                      onClick={() => handleTemplateSelect(template)}
                    >
                      <div className="flex items-center justify-between gap-4">
                        <div className="flex items-center gap-3 flex-1 min-w-0">
                          <div className="w-10 h-10 rounded-lg bg-amber-50 group-hover:bg-amber-100 flex-shrink-0 flex items-center justify-center transition-colors">
                            <BlockOutlined className="text-amber-500 text-lg" />
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2">
                              <span className="text-sm font-medium text-gray-800 group-hover:text-blue-600 transition-colors truncate max-w-[calc(100%-70px)]">
                                {template.templateName}
                              </span>
                              <Tag color="gold" className="flex-shrink-0 rounded-full text-xs">
                                官方
                              </Tag>
                              {selectedTemplate?.id === template.id && (
                                <Tag color="blue" className="flex-shrink-0 rounded-full text-xs">
                                  已选择
                                </Tag>
                              )}
                            </div>
                            <p className="text-xs text-gray-500 mt-1">
                              创建于 {dayjs(template.createTime).format('YYYY-MM-DD')}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2 flex-shrink-0">
                          <Button
                            type="default"
                            size="small"
                            className="flex-shrink-0"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleTemplateClick(template);
                            }}
                          >
                            查看详情
                          </Button>
                        </div>
                      </div>
                      <div className="mt-3 text-xs text-gray-500">
                        {studentTemplateUsage[template.id] ? (
                          <span
                            className="cursor-pointer hover:text-blue-500 transition-colors"
                            onClick={(e) => {
                              e.stopPropagation();
                              onTemplateUsageClick(e, template);
                            }}
                          >
                            {studentTemplateUsage[template.id]} 名学生正在使用
                          </span>
                        ) : (
                          <span>暂无学生使用</span>
                        )}
                      </div>
                    </div>
                  ))
                )}
              </>
            ) : (
              // 我的模板视图
              loadingTemplates ? (
                <div className="flex justify-center py-8">
                  <Spin />
                </div>
              ) : templates.filter(t => !t.isOfficial).map(template => (
                <div
                  key={template.id}
                  className={`bg-white border p-4 rounded-xl cursor-pointer hover:shadow-md transition-all group relative z-10 ${
                    selectedTemplate?.id === template.id ? 'border-blue-500 bg-blue-50' : 'border-gray-100'
                  }`}
                  onClick={() => handleTemplateSelect(template)}
                >
                  <div className="flex items-center justify-between gap-4">
                    <div className="flex items-center gap-3 flex-1 min-w-0">
                      <div className="w-10 h-10 rounded-lg bg-blue-50 group-hover:bg-blue-100 flex-shrink-0 flex items-center justify-center transition-colors">
                        <AppstoreOutlined className="text-blue-500 text-lg" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2">
                          <span className="text-sm font-medium text-gray-800 group-hover:text-blue-600 transition-colors truncate max-w-[calc(100%-70px)]">
                            {template.templateName}
                          </span>
                          <Tag color="blue" className="flex-shrink-0 rounded-full text-xs">
                            自定义
                          </Tag>
                          {selectedTemplate?.id === template.id && (
                            <Tag color="blue" className="flex-shrink-0 rounded-full text-xs">
                              已选择
                            </Tag>
                          )}
                        </div>
                        <p className="text-xs text-gray-500 mt-1">
                          创建于 {dayjs(template.createTime).format('YYYY-MM-DD')}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2 flex-shrink-0">
                      <Button
                        type="default"
                        size="small"
                        className="flex-shrink-0"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleTemplateClick(template);
                        }}
                      >
                        查看详情
                      </Button>
                    </div>
                  </div>
                  <div className="mt-3 text-xs text-gray-500">
                    {studentTemplateUsage[template.id] ? (
                      <span
                        className="cursor-pointer hover:text-blue-500 transition-colors"
                        onClick={(e) => {
                          e.stopPropagation();
                          onTemplateUsageClick(e, template);
                        }}
                      >
                        {studentTemplateUsage[template.id]} 名学生正在使用
                      </span>
                    ) : (
                      <span>暂无学生使用</span>
                    )}
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      )}

      {/* 添加权限查看弹窗 */}
      {isPermissionModalVisible && selectedTemplateId && (
        <PermissionModal
          userId={userId}
          visible={isPermissionModalVisible}
          onClose={() => {
            setIsPermissionModalVisible(false);
            setSelectedTemplateId(null);
          }}
          templateId={selectedTemplateId}
          readOnly={true}
          hideMoreOptions={true}
          onUseTemplate={(templateId) => {
            const template = templates.find(t => t.id === templateId) ||
                           folderTemplates.find(t => t.id === templateId) ||
                           (specialTemplate?.id === templateId ? specialTemplate : null);
            if (template) {
              handleTemplateSelect(template);
            }
            setIsPermissionModalVisible(false);
            setSelectedTemplateId(null);
          }}
        />
      )}

      {/* 添加创建模板弹窗 */}
      {isCreateTemplateModalVisible && (
        <PermissionTemplateModal
          visible={isCreateTemplateModalVisible}
          onClose={() => {
            setIsCreateTemplateModalVisible(false);
          }}
          onSuccess={handleCreateTemplateSuccess}
          roleId={2} // 老师角色ID
          userId={userId}
        />
      )}
    </Modal>
  );
};
