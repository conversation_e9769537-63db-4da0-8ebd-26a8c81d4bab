'use client';

import React, { createContext, useContext, useState, useEffect, useCallback, ReactNode } from 'react';
import { getUserCurrentTemplate } from '@/lib/api/role';

interface CurrentTemplate {
  templateId: number;
  templateName: string;
  isOfficial: boolean;
}

interface TemplateContextType {
  currentTemplate: CurrentTemplate | null;
  refreshCurrentTemplate: () => Promise<void>;
  isLoading: boolean;
  // 全局模板变化通知
  globalTemplateChangeVersion: number;
  notifyGlobalTemplateChange: () => void;
  // 任务发布成功通知
  taskPublishVersion: number;
  notifyTaskPublished: () => void;
}

const TemplateContext = createContext<TemplateContextType | undefined>(undefined);

interface TemplateProviderProps {
  children: ReactNode;
  userId: number | null;
}

export const TemplateProvider: React.FC<TemplateProviderProps> = ({ children, userId }) => {
  const [currentTemplate, setCurrentTemplate] = useState<CurrentTemplate | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [globalTemplateChangeVersion, setGlobalTemplateChangeVersion] = useState(0);
  const [taskPublishVersion, setTaskPublishVersion] = useState(0);

  // 获取当前使用的模板
  const fetchCurrentTemplate = useCallback(async () => {
    if (!userId) {
      setCurrentTemplate(null);
      return;
    }

    setIsLoading(true);
    try {
      const response = await getUserCurrentTemplate(userId);
      if (response.data.code === 200) {
        const templateData = response.data.data;
        setCurrentTemplate(templateData);
      } else {
        setCurrentTemplate(null);
      }
    } catch (error) {
      console.error('TemplateContext - 获取当前使用模板失败:', error);
      setCurrentTemplate(null);
    } finally {
      setIsLoading(false);
    }
  }, [userId]);

  // 刷新当前模板
  const refreshCurrentTemplate = useCallback(async () => {
    await fetchCurrentTemplate();
  }, [fetchCurrentTemplate]);

  // 通知全局模板变化
  const notifyGlobalTemplateChange = () => {
    const newVersion = globalTemplateChangeVersion + 1;
    setGlobalTemplateChangeVersion(newVersion);
  };

  // 通知任务发布成功
  const notifyTaskPublished = () => {
    const newVersion = taskPublishVersion + 1;
    setTaskPublishVersion(newVersion);
  };

  // 当userId变化时重新获取模板
  useEffect(() => {
    fetchCurrentTemplate();
  }, [fetchCurrentTemplate]);

  const value: TemplateContextType = {
    currentTemplate,
    refreshCurrentTemplate,
    isLoading,
    globalTemplateChangeVersion,
    notifyGlobalTemplateChange,
    taskPublishVersion,
    notifyTaskPublished
  };

  return (
    <TemplateContext.Provider value={value}>
      {children}
    </TemplateContext.Provider>
  );
};

// 自定义Hook来使用模板上下文
export const useTemplate = (): TemplateContextType => {
  const context = useContext(TemplateContext);
  if (context === undefined) {
    throw new Error('useTemplate must be used within a TemplateProvider');
  }
  return context;
};

export default TemplateContext;
