'use client'

import { motion } from 'framer-motion'
import Image from 'next/image'
import { useState, useEffect, useRef } from 'react'
import { courseApi } from '@/lib/api/course'
import { Margin } from '@icon-park/react'

// 系列课程数据接口
export interface SeriesCourseData {
  id: number;
  title: string;
  lessons: string;
  views: string;
  coverImage?: string;
  seriesId?: number; // 添加系列ID
}

// 课程列表项接口
interface CourseItem {
  id: number;
  title: string;
  orderIndex: number;
  status: number;
}

// 系列课程详情接口
interface SeriesDetail {
  id: number;
  title: string;
  description: string;
  coverImage: string;
  category: number;
  categoryLabel: string;
  status: number;
  projectMembers: string;
  totalCourses: number;
  totalStudents: number;
  creatorId: number;
  createdAt: string;
  updatedAt: string;
  tags?: Array<{
    id: number;
    name: string;
    color: string;
    category: number;
    categoryLabel: string;
  }>;
}

// 课程详情接口
interface CourseDetail {
  id: number;
  title: string;
  description: string;
  coverImage: string;
  hasVideo: number;
  hasDocument: number;
  hasAudio: number;
  videoDuration: number;
  videoDurationLabel: string;
  videoName: string;
  resourcesCount: number;
  contentConfig: {
    hasVideo: number;
    hasDocument: number;
    hasAudio: number;
    video?: {
      url: string;
      name: string;
    };
    document?: {
      url: string;
      name: string;
    };
    audio?: {
      url: string;
      name: string;
    };
  };
  teachingInfo: Array<{
    title: string;
    content: string[];
  }>;
  additionalResources: Array<{
    title: string;
    url: string;
    description: string;
  }>;
  orderIndex: number;
  status: number;
  statusLabel: string;
  currentCourseId: number;
}

// 课程详情视图组件
interface CourseDetailViewProps {
  course: SeriesCourseData;
  onBack: () => void;
}

export default function CourseDetailView({ course, onBack }: CourseDetailViewProps) {
  const [seriesCourses, setSeriesCourses] = useState<CourseItem[]>([]);
  const [seriesDetail, setSeriesDetail] = useState<SeriesDetail | null>(null);
  const [courseDetail, setCourseDetail] = useState<CourseDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [showVideoPlayer, setShowVideoPlayer] = useState(false);
  const [videoLoaded, setVideoLoaded] = useState(false);

  // 格式化视频时长
  const formatDuration = (seconds: number): string => {
    if (!seconds || seconds <= 0) return '';

    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;

    if (hours > 0) {
      return `${hours}小时${minutes}分钟`;
    } else if (minutes > 0) {
      return `${minutes}分钟${remainingSeconds > 0 ? remainingSeconds + '秒' : ''}`;
    } else {
      return `${remainingSeconds}秒`;
    }
  };

  // 添加自定义滚动条样式
  const customScrollbarStyle = `
    .custom-scrollbar::-webkit-scrollbar {
      width: 6px;
    }
    .custom-scrollbar::-webkit-scrollbar-track {
      background: #f1f5f9;
      border-radius: 3px;
    }
    .custom-scrollbar::-webkit-scrollbar-thumb {
      background: linear-gradient(to bottom, #3b82f6, #2563eb);
      border-radius: 3px;
    }
    .custom-scrollbar::-webkit-scrollbar-thumb:hover {
      background: linear-gradient(to bottom, #2563eb, #1d4ed8);
    }
  `;

  // 使用useRef跟踪请求状态，避免React严格模式重复请求
  const requestedSeriesIdRef = useRef<number | null>(null);
  const isRequestingRef = useRef<boolean>(false);

  // 添加调试信息
  console.log('📋 CourseDetailView 接收到的课程数据:', course);
  console.log('🔍 seriesId 值:', course.seriesId);
  console.log('🔍 seriesId 类型:', typeof course.seriesId);

  // 调试：打印传入的课程数据
  console.log('🎯 CourseDetailView 接收到的课程数据:', course);

  // 防止浏览器自动下载
  useEffect(() => {
    const preventAutoDownload = (e: Event) => {
      const target = e.target as HTMLElement;
      if (target && target.tagName === 'A' && target.getAttribute('href')?.includes('example.com')) {
        e.preventDefault();
        console.log('🚫 阻止示例文件自动下载');
      }
    };

    document.addEventListener('click', preventAutoDownload, true);
    return () => {
      document.removeEventListener('click', preventAutoDownload, true);
    };
  }, []);

  // 获取系列课程列表
  useEffect(() => {
    const fetchSeriesCourses = async () => {
      // 使用 seriesId 或者 id 作为系列ID
      const seriesId = course.seriesId || course.id;

      if (!seriesId) {
        console.warn('⚠️ 课程没有seriesId和id，无法获取系列课程列表');
        setLoading(false);
        return;
      }

      // 防重复请求
      if (requestedSeriesIdRef.current === seriesId || isRequestingRef.current) {
        console.log('🚫 防重复请求：系列课程列表已请求过，seriesId:', seriesId);
        return;
      }

      try {
        setLoading(true);
        requestedSeriesIdRef.current = seriesId;
        isRequestingRef.current = true;
        console.log('🔄 获取系列课程列表，使用ID:', seriesId);

        // 首先获取系列详情
        const { data: seriesRes } = await courseApi.getMarketplaceSeriesDetail(seriesId);

        if (seriesRes.code === 200 && seriesRes.data) {
          const seriesData = seriesRes.data;

          // 设置系列详情
          setSeriesDetail({
            id: seriesData.id,
            title: seriesData.title,
            description: seriesData.description,
            coverImage: (seriesData.coverImage && !seriesData.coverImage.includes('example.com')) ? seriesData.coverImage : '',
            category: seriesData.category,
            categoryLabel: seriesData.categoryLabel || (seriesData.category === 0 ? '官方' : '社区'),
            status: seriesData.status,
            projectMembers: seriesData.projectMembers,
            totalCourses: seriesData.totalCourses,
            totalStudents: seriesData.totalStudents,
            creatorId: seriesData.creatorId || 0,
            createdAt: seriesData.createdAt,
            updatedAt: seriesData.updatedAt || seriesData.createdAt,
            tags: seriesData.tags || []
          });
        }

        // 使用课程管理API获取系列下的课程列表
        console.log('🔄 使用课程管理API获取系列课程列表...');
        const { data: coursesRes } = await courseApi.getManagementSeriesCourses(seriesId, {
          page: 1,
          pageSize: 50,
          status: 1  // 只获取已发布的课程
        });

        if (coursesRes.code === 200 && coursesRes.data?.list) {
          const courses = coursesRes.data.list.map((item: any) => ({
            id: item.id,
            title: item.title,
            orderIndex: item.orderIndex || 0,
            status: item.status
          }));

          // 按orderIndex排序
          courses.sort((a: CourseItem, b: CourseItem) => a.orderIndex - b.orderIndex);
          console.log('📚 从课程管理API获取到课程列表:', courses);
          setSeriesCourses(courses);
        } else {
          console.log('⚠️ 课程管理API未返回课程列表');
          setSeriesCourses([]);
        }

        // 如果有课程列表，设置默认课程详情（选择第一个课程）
        if (coursesRes.code === 200 && coursesRes.data?.list && coursesRes.data.list.length > 0) {
          const firstCourse = coursesRes.data.list[0];
          console.log('🎬 设置默认课程（第一个课程）:', firstCourse);

          // 获取第一个课程的详细信息
          try {
            const { data: courseDetailRes } = await courseApi.getCourseDetail(firstCourse.id);
            if (courseDetailRes.code === 200 && courseDetailRes.data) {
              const courseData = courseDetailRes.data;
              setCourseDetail({
                id: courseData.id,
                title: courseData.title,
                description: courseData.description || seriesRes.data?.description || '',
                coverImage: (courseData.coverImage && !courseData.coverImage.includes('example.com')) ? courseData.coverImage : (seriesRes.data?.coverImage || ''),
                hasVideo: courseData.hasVideo || 0,
                hasDocument: courseData.hasDocument || 0,
                hasAudio: courseData.hasAudio || 0,
                videoDuration: courseData.videoDuration || 0,
                videoDurationLabel: courseData.videoDurationLabel || '',
                videoName: courseData.videoName || '',
                resourcesCount: courseData.additionalResources?.length || 0,
                contentConfig: courseData.contentConfig || {},
                teachingInfo: courseData.teachingInfo || [],
                additionalResources: courseData.additionalResources || [],
                orderIndex: courseData.orderIndex || 1,
                status: courseData.status || 1,
                statusLabel: courseData.statusLabel || '已发布',
                currentCourseId: courseData.id
              });
            }
          } catch (courseError) {
            console.error('❌ 获取课程详情失败:', courseError);
          }
        }
      } catch (error) {
        console.error('❌ 获取系列课程列表异常:', error);
        setSeriesCourses([]);
      } finally {
        setLoading(false);
        isRequestingRef.current = false;
      }
    };

    fetchSeriesCourses();
  }, [course.seriesId, course.id]);

  // 处理课程点击事件
  const handleCourseClick = async (courseItem: CourseItem) => {
    const seriesId = course.seriesId || course.id;
    const courseId = courseItem.id;

    console.log('🎯 点击课程，准备获取详情 - seriesId:', seriesId, 'courseId:', courseId);

    // 先清空课程详情，避免旧数据触发下载
    setCourseDetail(null);

    try {
      const { data: res } = await courseApi.getCourseMarketplaceDetail(seriesId, courseId);

      if (res.code === 200 && res.data) {
        console.log('📚 获取到课程详情:', res.data);
        setCourseDetail(res.data);
      } else {
        console.error('❌ 获取课程详情失败:', res);
        // 如果API失败，使用本地数据（添加示例视频时长和PDF文档）
        const sampleVideoDuration = courseItem.id === 1 ? 1800 : courseItem.id === 2 ? 2400 : 3600; // 30分钟、40分钟、60分钟
        const hasVideoSample = courseItem.id <= 2 ? 1 : 0; // 前两个课程有视频
        const hasDocumentSample = courseItem.id <= 3 ? 1 : 0; // 前三个课程有文档

        setCourseDetail({
          id: courseItem.id,
          title: courseItem.title,
          description: '',
          coverImage: course.coverImage || '',
          hasVideo: hasVideoSample,
          hasDocument: hasDocumentSample,
          hasAudio: 0,
          videoDuration: hasVideoSample ? sampleVideoDuration : 0,
          videoDurationLabel: '',
          videoName: hasVideoSample ? `${courseItem.title}教学视频` : '',
          resourcesCount: 0,
          contentConfig: {
            hasVideo: hasVideoSample,
            hasDocument: hasDocumentSample,
            hasAudio: 0,
            video: hasVideoSample ? {
              url: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
              name: `${courseItem.title}教学视频.mp4`
            } : {
              url: '',
              name: ''
            },
            document: hasDocumentSample ? {
              url: 'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf',
              name: `${courseItem.title}教学课件.pdf`
            } : {
              url: '',
              name: ''
            },
            audio: {
              url: '',
              name: ''
            }
          },
          teachingInfo: [],
          additionalResources: [],
          orderIndex: courseItem.orderIndex || 1,
          status: courseItem.status || 1,
          statusLabel: courseItem.status === 1 ? '已发布' : '草稿',
          currentCourseId: courseItem.id
        });
      }
    } catch (error) {
      console.error('❌ 获取课程详情异常:', error);
      // 如果API失败，使用本地数据（添加示例视频时长和PDF文档）
      const sampleVideoDuration = courseItem.id === 1 ? 1800 : courseItem.id === 2 ? 2400 : 3600; // 30分钟、40分钟、60分钟
      const hasVideoSample = courseItem.id <= 2 ? 1 : 0; // 前两个课程有视频
      const hasDocumentSample = courseItem.id <= 3 ? 1 : 0; // 前三个课程有文档

      setCourseDetail({
        id: courseItem.id,
        title: courseItem.title,
        description: '',
        coverImage: course.coverImage || '',
        hasVideo: hasVideoSample,
        hasDocument: hasDocumentSample,
        hasAudio: 0,
        videoDuration: hasVideoSample ? sampleVideoDuration : 0,
        videoDurationLabel: '',
        videoName: hasVideoSample ? `${courseItem.title}教学视频` : '',
        resourcesCount: 0,
        contentConfig: {
          hasVideo: hasVideoSample,
          hasDocument: hasDocumentSample,
          hasAudio: 0,
          video: hasVideoSample ? {
            url: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
            name: `${courseItem.title}教学视频.mp4`
          } : {
            url: '',
            name: ''
          },
          document: hasDocumentSample ? {
            url: 'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf',
            name: `${courseItem.title}教学课件.pdf`
          } : {
            url: '',
            name: ''
          },
          audio: {
            url: '',
            name: ''
          }
        },
        teachingInfo: [],
        additionalResources: [],
        orderIndex: courseItem.orderIndex || 1,
        status: courseItem.status || 1,
        statusLabel: courseItem.status === 1 ? '已发布' : '草稿',
        currentCourseId: courseItem.id
      });
    }
  };

  // 注释：现在数据直接从API获取，不再需要设置默认数据

  return (
    <motion.div
      initial={{ opacity: 0, x: 50 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -50 }}
      transition={{ duration: 0.5 }}
      className="min-h-screen"
    >
      {/* 自定义滚动条样式 */}
      <style dangerouslySetInnerHTML={{ __html: customScrollbarStyle }} />

      {/* 返回按钮 */}
      <div className="mb-6">
        <button
          onClick={onBack}
          className="flex items-center gap-2 px-4 py-2 text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-colors duration-200"
        >
          <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
          <span>返回课程列表</span>
        </button>
      </div>

      {/* 课程详情头部区域 - 保留左右布局，优化样式 */}
      <div className="bg-gradient-to-br from-white via-blue-50/30 to-indigo-50/50 rounded-2xl border border-blue-200/60 shadow-lg backdrop-blur-sm overflow-hidden mb-8">
        <div className="flex items-center justify-between p-6 bg-white/70 backdrop-blur-md border-b border-blue-100/50">
          <h1 className="text-2xl font-bold text-gray-900 tracking-tight">
            {courseDetail?.title || seriesDetail?.title || '课程详情'}
          </h1>
          <span className={`px-5 py-2.5 text-sm font-semibold rounded-xl shadow-sm transition-all duration-200 ${
            seriesDetail?.category === 0
              ? 'bg-gradient-to-r from-emerald-500 to-emerald-600 text-white hover:from-emerald-600 hover:to-emerald-700'
              : 'bg-gradient-to-r from-blue-500 to-blue-600 text-white hover:from-blue-600 hover:to-blue-700'
          }`}>
            {seriesDetail?.category === 0 ? '官方' : '社区'}
          </span>
        </div>

        <div className="flex gap-6 p-6">
          {/* 图片区域 - 优化样式，优先显示课程封面 */}
          <div className="relative group">
            <div className="w-40 h-28 bg-gradient-to-br from-orange-100 via-orange-50 to-amber-50 rounded-xl shadow-md overflow-hidden border-2 border-white/80 transition-all duration-300 group-hover:shadow-lg group-hover:scale-105">
              {((courseDetail?.coverImage && !courseDetail.coverImage.includes('example.com')) ||
                (seriesDetail?.coverImage && !seriesDetail.coverImage.includes('example.com'))) ? (
                <Image
                  src={courseDetail?.coverImage || seriesDetail?.coverImage || ''}
                  alt={courseDetail?.title || seriesDetail?.title || '课程封面'}
                  width={160}
                  height={112}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full flex flex-col items-center justify-center text-orange-400">
                  <svg className="w-8 h-8 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                  <span className="text-xs font-medium">课程封面</span>
                </div>
              )}
            </div>
            {/* 装饰性光晕 */}
            <div className="absolute -inset-1 bg-gradient-to-r from-orange-200/20 to-amber-200/20 rounded-xl blur-sm -z-10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          </div>

          {/* 内容区域 - 优化样式，优先显示课程描述 */}
          <div className="flex-1 space-y-4">
            <div className="bg-gradient-to-r from-purple-50 to-indigo-50 rounded-xl p-5 border border-purple-100/50 shadow-sm">
              <p className="text-gray-700 leading-relaxed">
                {courseDetail?.description || seriesDetail?.description || '暂无课程描述'}
              </p>
            </div>

            {/* 系列课程标签 */}
            {seriesDetail?.tags && seriesDetail.tags.length > 0 && (
              <div className="flex flex-wrap gap-2">
                {seriesDetail.tags.map((tag) => (
                  <span
                    key={tag.id}
                    className="inline-flex items-center px-3 py-1 text-xs font-medium rounded-full border"
                    style={{
                      backgroundColor: `${tag.color}15`,
                      borderColor: `${tag.color}40`,
                      color: tag.color
                    }}
                  >
                    {tag.name}
                  </span>
                ))}
              </div>
            )}

            <div className="flex items-center gap-2 text-sm text-gray-600">
              <svg className="w-4 h-4 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
              <span className="font-medium">项目成员：</span>
              <span>{seriesDetail?.projectMembers || '暂无信息'}</span>
            </div>
          </div>
        </div>
      </div>

      {/* 其余内容 - 左右分区布局 */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 左侧主要内容区域 */}
        <div className="lg:col-span-2 space-y-6">

          {/* 教学视频 */}
          <div className="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
            <div className="bg-gradient-to-r from-blue-600 to-purple-600 px-6 py-4">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-bold text-white flex items-center gap-2">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                  </svg>
                  教学视频
                </h2>
                {(courseDetail?.videoDurationLabel || courseDetail?.videoDuration) && (
                  <span className="bg-white/20 backdrop-blur-sm text-white px-3 py-1 rounded-full text-sm font-medium">
                    时长：{courseDetail.videoDurationLabel || formatDuration(courseDetail.videoDuration || 0)}
                  </span>
                )}
              </div>
            </div>

            <div className="p-6">
              {courseDetail?.hasVideo ? (
                <div className="relative group">
                  {!showVideoPlayer ? (
                    // 视频预览封面
                    <div
                      className="relative bg-gradient-to-br from-gray-900 to-gray-700 rounded-xl h-80 flex items-center justify-center cursor-pointer overflow-hidden transition-all duration-300 hover:shadow-2xl"
                      onClick={() => setShowVideoPlayer(true)}
                    >
                      {/* 背景图片 */}
                      {courseDetail.coverImage && !courseDetail.coverImage.includes('example.com') && (
                        <div
                          className="absolute inset-0 bg-cover bg-center opacity-60"
                          style={{ backgroundImage: `url(${courseDetail.coverImage})` }}
                        />
                      )}

                      {/* 渐变遮罩 */}
                      <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent" />

                      {/* 播放按钮 */}
                      <div className="relative z-10 text-center">
                        <div className="w-20 h-20 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4 transition-all duration-300 group-hover:bg-white/30 group-hover:scale-110">
                          <svg className="w-10 h-10 text-white ml-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                          </svg>
                        </div>
                        <h3 className="text-white text-xl font-bold mb-2">
                          {courseDetail.videoName || '教学视频'}
                        </h3>
                        <p className="text-white/80 text-sm">点击播放视频</p>
                      </div>

                      {/* 装饰元素 */}
                      <div className="absolute top-4 right-4 bg-red-500 text-white px-2 py-1 rounded text-xs font-bold">
                        HD
                      </div>
                    </div>
                  ) : (
                    // 实际视频播放器
                    <div className="relative bg-black rounded-xl overflow-hidden">
                      {courseDetail.contentConfig?.video?.url ? (
                        <video
                          className="w-full h-80 object-contain"
                          controls
                          autoPlay
                          poster={(courseDetail.coverImage && !courseDetail.coverImage.includes('example.com')) ? courseDetail.coverImage : undefined}
                          onLoadStart={() => setVideoLoaded(true)}
                        >
                          <source src={courseDetail.contentConfig.video.url} type="video/mp4" />
                          您的浏览器不支持视频播放
                        </video>
                      ) : (
                        <div className="h-80 flex items-center justify-center bg-gray-100">
                          <div className="text-center text-gray-500">
                            <svg className="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z" />
                            </svg>
                            <p className="font-medium">视频加载失败</p>
                            <p className="text-sm mt-1">请检查网络连接或稍后重试</p>
                          </div>
                        </div>
                      )}

                      {/* 关闭按钮 */}
                      <button
                        onClick={() => setShowVideoPlayer(false)}
                        className="absolute top-4 right-4 bg-black/50 hover:bg-black/70 text-white rounded-full p-2 transition-colors duration-200"
                      >
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </button>
                    </div>
                  )}
                </div>
              ) : (
                <div className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl h-80 flex items-center justify-center border-2 border-dashed border-gray-300">
                  <div className="text-center text-gray-500">
                    <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
                      <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M15.75 10.5l4.72-4.72a.75.75 0 011.28.53v11.38a.75.75 0 01-1.28.53l-4.72-4.72M4.5 18.75h9a2.25 2.25 0 002.25-2.25v-9a2.25 2.25 0 00-2.25-2.25h-9A2.25 2.25 0 002.25 7.5v9a2.25 2.25 0 002.25 2.25z" />
                      </svg>
                    </div>
                    <p className="text-lg font-semibold text-gray-600 mb-2">暂无教学视频</p>
                    <p className="text-sm text-gray-500">该课程暂未提供视频内容</p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* 教学课件 */}
          <div className="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
            <div className="bg-gradient-to-r from-indigo-500 to-purple-600 px-6 py-4">
              <h2 className="text-xl font-bold text-white flex items-center gap-3">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                教学课件
              </h2>
            </div>

            <div className="p-6">
              {courseDetail?.hasDocument ? (
                <div className="relative bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl overflow-hidden border border-gray-200 shadow-inner">
                  {courseDetail.contentConfig?.document?.url && courseDetail.contentConfig.document.url.trim() !== '' ? (
                    <div className="relative">
                      {/* PDF预览iframe */}
                      <iframe
                        src={`${courseDetail.contentConfig.document.url}#toolbar=1&navpanes=1&scrollbar=1&page=1&view=FitH`}
                        className="w-full h-[600px] rounded-xl border-0"
                        title="教学课件预览"
                        allow="fullscreen"
                        loading="lazy"
                        style={{
                          background: 'white',
                          boxShadow: 'inset 0 0 10px rgba(0,0,0,0.1)'
                        }}
                        onLoad={() => console.log('PDF课件加载完成')}
                        onError={(e) => {
                          console.error('PDF课件加载失败');
                          // 阻止错误传播，避免触发下载
                          e.preventDefault();
                        }}
                      />

                      {/* 课件信息覆盖层 */}
                      <div className="absolute top-4 left-4 bg-white/95 backdrop-blur-sm rounded-lg px-3 py-2 shadow-lg border border-white/20">
                        <div className="flex items-center gap-2">
                          <svg className="w-4 h-4 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                          </svg>
                          <span className="text-sm font-medium text-gray-700">
                            {courseDetail.contentConfig.document.name || '教学课件.pdf'}
                          </span>
                        </div>
                      </div>

                      {/* 操作按钮组 */}
                      <div className="absolute top-4 right-4 flex gap-2">
                        {/* 新窗口打开 */}
                        <button
                          onClick={() => {
                            if (courseDetail?.contentConfig?.document?.url) {
                              window.open(courseDetail.contentConfig.document.url, '_blank');
                            }
                          }}
                          className="bg-white/95 hover:bg-white backdrop-blur-sm rounded-lg p-2 shadow-lg border border-white/20 transition-all duration-200 hover:scale-105"
                          title="在新窗口中打开"
                        >
                          <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                          </svg>
                        </button>

                        {/* 下载按钮 */}
                        <a
                          href={courseDetail?.contentConfig?.document?.url || '#'}
                          download={courseDetail?.contentConfig?.document?.name || '教学课件.pdf'}
                          className="bg-white/95 hover:bg-white backdrop-blur-sm rounded-lg p-2 shadow-lg border border-white/20 transition-all duration-200 hover:scale-105"
                          title="下载课件"
                        >
                          <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                          </svg>
                        </a>

                        {/* 全屏按钮 */}
                        <button
                          onClick={() => {
                            const iframe = document.querySelector('iframe[title="教学课件预览"]') as HTMLIFrameElement;
                            if (iframe && iframe.requestFullscreen) {
                              iframe.requestFullscreen();
                            }
                          }}
                          className="bg-white/95 hover:bg-white backdrop-blur-sm rounded-lg p-2 shadow-lg border border-white/20 transition-all duration-200 hover:scale-105"
                          title="全屏查看"
                        >
                          <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
                          </svg>
                        </button>
                      </div>

                      {/* PDF加载提示 */}
                      <div className="absolute bottom-4 left-4 bg-blue-500/90 text-white text-xs px-3 py-1 rounded-full backdrop-blur-sm">
                        PDF预览模式
                      </div>
                    </div>
                  ) : (
                    <div className="h-96 flex items-center justify-center">
                      <div className="text-center">
                        <div className="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4">
                          <svg className="w-8 h-8 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                          </svg>
                        </div>
                        <p className="text-lg font-semibold text-gray-700 mb-2">
                          {courseDetail.contentConfig?.document?.name || '教学课件'}
                        </p>
                        <p className="text-sm text-gray-500">课件加载中...</p>
                        <div className="mt-4">
                          <div className="w-32 h-2 bg-gray-200 rounded-full mx-auto overflow-hidden">
                            <div className="h-full bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full animate-pulse"></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <div className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl h-96 flex items-center justify-center border-2 border-dashed border-gray-300">
                  <div className="text-center">
                    <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
                      <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                    </div>
                    <p className="text-lg font-semibold text-gray-600 mb-2">暂无教学课件</p>
                    <p className="text-sm text-gray-500">该课程暂未提供课件内容</p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* 教学信息 - 只在有课程时显示 */}
          {seriesCourses.length > 0 && (
            courseDetail?.teachingInfo && courseDetail.teachingInfo.length > 0 ? (
            courseDetail.teachingInfo.map((info, index) => {
              // 根据标题确定图标和颜色主题
              const getThemeConfig = (title: string) => {
                if (title.includes('目标')) {
                  return {
                    icon: (
                      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    ),
                    gradient: 'from-emerald-500 to-green-600',
                    bgColor: 'from-emerald-50 to-green-50',
                    borderColor: 'border-emerald-200',
                    textColor: 'text-emerald-700',
                    bulletColor: 'bg-emerald-500'
                  };
                } else if (title.includes('准备')) {
                  return {
                    icon: (
                      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                      </svg>
                    ),
                    gradient: 'from-blue-500 to-indigo-600',
                    bgColor: 'from-blue-50 to-indigo-50',
                    borderColor: 'border-blue-200',
                    textColor: 'text-blue-700',
                    bulletColor: 'bg-blue-500'
                  };
                } else if (title.includes('重难点')) {
                  return {
                    icon: (
                      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    ),
                    gradient: 'from-orange-500 to-red-600',
                    bgColor: 'from-orange-50 to-red-50',
                    borderColor: 'border-orange-200',
                    textColor: 'text-orange-700',
                    bulletColor: 'bg-orange-500'
                  };
                } else {
                  return {
                    icon: (
                      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                      </svg>
                    ),
                    gradient: 'from-purple-500 to-pink-600',
                    bgColor: 'from-purple-50 to-pink-50',
                    borderColor: 'border-purple-200',
                    textColor: 'text-purple-700',
                    bulletColor: 'bg-purple-500'
                  };
                }
              };

              const theme = getThemeConfig(info.title);

              return (
                <div key={index} className="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
                  {/* 标题栏 */}
                  <div className={`bg-gradient-to-r ${theme.gradient} px-6 py-4`}>
                    <h2 className="text-xl font-bold text-white flex items-center gap-3">
                      {theme.icon}
                      {info.title}
                    </h2>
                  </div>

                  {/* 内容区域 */}
                  <div className="p-6">
                    <div className={`bg-gradient-to-br ${theme.bgColor} rounded-xl p-6 border ${theme.borderColor}`}>
                      <ul className="space-y-4">
                        {info.content.map((item, itemIndex) => (
                          <li key={itemIndex} className="flex items-start gap-4 group">
                            <div className={`w-8 h-8 ${theme.bulletColor} rounded-full flex items-center justify-center flex-shrink-0 shadow-md group-hover:scale-110 transition-transform duration-200`}>
                              <span className="text-white font-bold text-sm">{itemIndex + 1}</span>
                            </div>
                            <div className="flex-1">
                              <p className={`${theme.textColor} font-medium leading-relaxed`}>
                                {item}
                              </p>
                            </div>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              );
            })
          ) : (
            <>
              {/* 默认教学目标 */}
              <div className="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
                <div className="bg-gradient-to-r from-emerald-500 to-green-600 px-6 py-4">
                  <h2 className="text-xl font-bold text-white flex items-center gap-3">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    教学目标
                  </h2>
                </div>
                <div className="p-6">
                  <div className="bg-gradient-to-br from-emerald-50 to-green-50 rounded-xl p-6 border border-emerald-200">
                    <ul className="space-y-4">
                      <li className="flex items-start gap-4 group">
                        <div className="w-8 h-8 bg-emerald-500 rounded-full flex items-center justify-center flex-shrink-0 shadow-md">
                          <span className="text-white font-bold text-sm">1</span>
                        </div>
                        <p className="text-emerald-700 font-medium leading-relaxed">
                          掌握课程核心概念和基本原理
                        </p>
                      </li>
                      <li className="flex items-start gap-4 group">
                        <div className="w-8 h-8 bg-emerald-500 rounded-full flex items-center justify-center flex-shrink-0 shadow-md">
                          <span className="text-white font-bold text-sm">2</span>
                        </div>
                        <p className="text-emerald-700 font-medium leading-relaxed">
                          培养实际应用和问题解决能力
                        </p>
                      </li>
                      <li className="flex items-start gap-4 group">
                        <div className="w-8 h-8 bg-emerald-500 rounded-full flex items-center justify-center flex-shrink-0 shadow-md">
                          <span className="text-white font-bold text-sm">3</span>
                        </div>
                        <p className="text-emerald-700 font-medium leading-relaxed">
                          建立系统性的知识框架和思维模式
                        </p>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* 默认教学准备 */}
              <div className="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
                <div className="bg-gradient-to-r from-blue-500 to-indigo-600 px-6 py-4">
                  <h2 className="text-xl font-bold text-white flex items-center gap-3">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                    </svg>
                    教学准备
                  </h2>
                </div>
                <div className="p-6">
                  <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-200">
                    <ul className="space-y-4">
                      <li className="flex items-start gap-4">
                        <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0 shadow-md">
                          <span className="text-white font-bold text-sm">1</span>
                        </div>
                        <p className="text-blue-700 font-medium leading-relaxed">
                          准备相关教学材料和演示工具
                        </p>
                      </li>
                      <li className="flex items-start gap-4">
                        <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0 shadow-md">
                          <span className="text-white font-bold text-sm">2</span>
                        </div>
                        <p className="text-blue-700 font-medium leading-relaxed">
                          设计互动环节和实践练习
                        </p>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* 默认教学重难点 */}
              <div className="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
                <div className="bg-gradient-to-r from-orange-500 to-red-600 px-6 py-4">
                  <h2 className="text-xl font-bold text-white flex items-center gap-3">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    教学重难点
                  </h2>
                </div>
                <div className="p-6">
                  <div className="bg-gradient-to-br from-orange-50 to-red-50 rounded-xl p-6 border border-orange-200">
                    <ul className="space-y-4">
                      <li className="flex items-start gap-4">
                        <div className="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center flex-shrink-0 shadow-md">
                          <span className="text-white font-bold text-sm">重</span>
                        </div>
                        <p className="text-orange-700 font-medium leading-relaxed">
                          核心概念的理解和应用
                        </p>
                      </li>
                      <li className="flex items-start gap-4">
                        <div className="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center flex-shrink-0 shadow-md">
                          <span className="text-white font-bold text-sm">难</span>
                        </div>
                        <p className="text-red-700 font-medium leading-relaxed">
                          复杂问题的分析和解决方法
                        </p>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </>
          )
          )}
        </div>

        {/* 右侧边栏 */}
        <div className="space-y-6">
          {/* 课程附件 */}
          <div className="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
            <div className="bg-gradient-to-r from-teal-500 to-cyan-600 px-6 py-4">
              <h3 className="text-xl font-bold text-white flex items-center gap-3">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
                </svg>
                课程附件
              </h3>
            </div>

            <div className="p-6">
              {courseDetail?.additionalResources && courseDetail.additionalResources.length > 0 ? (
                <>
                  {/* 固定高度容器，最多显示3个附件的高度 */}
                  <div className="h-[216px] overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 hover:scrollbar-thumb-gray-400">
                    <div className="space-y-2 pr-2">
                      {courseDetail.additionalResources.map((resource, index) => {
                      // 根据文件扩展名确定文件类型和样式
                      const getFileTypeConfig = (title: string) => {
                        const extension = title.split('.').pop()?.toLowerCase();

                        if (extension === 'pptx' || extension === 'ppt') {
                          return {
                            icon: (
                              <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0a2 2 0 00-2 2v14a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2" />
                              </svg>
                            ),
                            gradient: 'from-orange-500 to-red-600',
                            bgColor: 'from-orange-50 to-red-50',
                            borderColor: 'border-orange-200',
                            hoverColor: 'group-hover:text-orange-700',
                            buttonBg: 'bg-orange-100 hover:bg-orange-200 text-orange-600',
                            fileType: 'PowerPoint 演示文稿'
                          };
                        } else if (extension === 'pdf') {
                          return {
                            icon: (
                              <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                              </svg>
                            ),
                            gradient: 'from-red-500 to-pink-600',
                            bgColor: 'from-red-50 to-pink-50',
                            borderColor: 'border-red-200',
                            hoverColor: 'group-hover:text-red-700',
                            buttonBg: 'bg-red-100 hover:bg-red-200 text-red-600',
                            fileType: 'PDF 文档'
                          };
                        } else if (extension === 'docx' || extension === 'doc') {
                          return {
                            icon: (
                              <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                              </svg>
                            ),
                            gradient: 'from-blue-500 to-indigo-600',
                            bgColor: 'from-blue-50 to-indigo-50',
                            borderColor: 'border-blue-200',
                            hoverColor: 'group-hover:text-blue-700',
                            buttonBg: 'bg-blue-100 hover:bg-blue-200 text-blue-600',
                            fileType: 'Word 文档'
                          };
                        } else {
                          return {
                            icon: (
                              <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
                              </svg>
                            ),
                            gradient: 'from-emerald-500 to-teal-600',
                            bgColor: 'from-emerald-50 to-teal-50',
                            borderColor: 'border-emerald-200',
                            hoverColor: 'group-hover:text-emerald-700',
                            buttonBg: 'bg-emerald-100 hover:bg-emerald-200 text-emerald-600',
                            fileType: '附件文档'
                          };
                        }
                      };

                      const config = getFileTypeConfig(resource.title);

                      return (
                        <div key={index} className={`group relative bg-gradient-to-r ${config.bgColor} border ${config.borderColor} rounded-lg p-3 hover:shadow-md transition-all duration-300 cursor-pointer`}>
                          <div className="flex items-center gap-3">
                            <div className={`w-10 h-10 bg-gradient-to-br ${config.gradient} rounded-lg flex items-center justify-center shadow-md group-hover:scale-110 transition-transform duration-200`}>
                              {config.icon}
                            </div>
                            <div className="flex-1">
                              <h4 className={`text-sm font-semibold text-gray-900 ${config.hoverColor} transition-colors`}>
                                {resource.title}
                              </h4>
                              <p className="text-xs text-gray-500 mt-0.5">
                                {config.fileType} {resource.description && `• ${resource.description}`}
                              </p>
                            </div>
                            <div className="flex items-center gap-1">
                              <button className="p-1.5 bg-white/80 hover:bg-white rounded-md shadow-sm transition-colors duration-200">
                                <svg className="w-3.5 h-3.5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                </svg>
                              </button>
                              <a
                                href={resource.url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className={`p-1.5 ${config.buttonBg} rounded-md transition-colors duration-200`}
                              >
                                <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                              </a>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                    </div>
                  </div>

                  {/* 底部统计信息 */}
                  <div className="mt-4 pt-3 border-t border-gray-200">
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <span>共 {courseDetail.additionalResources.length} 个附件</span>
                      <span>点击下载按钮获取文件</span>
                    </div>
                  </div>
                </>
              ) : (
                /* 空状态 */
                <div className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl h-32 flex items-center justify-center border-2 border-dashed border-gray-300">
                  <div className="text-center">
                    <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-3">
                      <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
                      </svg>
                    </div>
                    <p className="text-sm font-semibold text-gray-600 mb-1">暂无课程附件</p>
                    <p className="text-xs text-gray-500">该课程暂未提供附件下载</p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* 系列课程列表 */}
          <div className="bg-gradient-to-br from-white via-blue-50/20 to-indigo-50/30 rounded-xl border border-blue-200/60 shadow-lg backdrop-blur-sm overflow-hidden">
            {/* 标题栏 */}
            <div className="bg-gradient-to-r from-blue-50/80 to-indigo-50/80 backdrop-blur-md border-b border-blue-100/50 px-6 py-4">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-lg flex items-center justify-center shadow-sm">
                  <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                  </svg>
                </div>
                <h3 className="text-lg font-bold text-gray-900 tracking-tight">
                  系列课程列表
                </h3>
                <span className="inline-flex items-center gap-1 px-3 py-1 bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-700 text-sm font-semibold rounded-full border border-blue-200/50 shadow-sm">
                  <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0a2 2 0 00-2 2v14a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2" />
                  </svg>
                  共{seriesCourses.length}课
                </span>
              </div>
            </div>

            {/* 内容区域 */}
            <div className="p-6">
              {loading ? (
                <div className="flex items-center justify-center py-12">
                  <div className="relative">
                    <div className="animate-spin rounded-full h-10 w-10 border-3 border-blue-200"></div>
                    <div className="animate-spin rounded-full h-10 w-10 border-3 border-blue-600 border-t-transparent absolute top-0"></div>
                  </div>
                  <span className="ml-3 text-gray-600 font-medium">加载课程列表...</span>
                </div>
              ) : seriesCourses.length > 0 ? (
                <div className="space-y-3 h-40 overflow-y-auto custom-scrollbar">
                  {seriesCourses.map((courseItem, index) => (
                    <div
                      key={courseItem.id}
                      className="group relative bg-gradient-to-r from-gray-50/50 to-blue-50/30 hover:from-blue-50 hover:to-indigo-50 rounded-xl px-4 py-1 border border-gray-200/50 hover:border-blue-300/50 transition-all duration-300 hover:shadow-md cursor-pointer"
                      onClick={() => handleCourseClick(courseItem)}
                    >
                      {/* 课程序号装饰 */}
                      <div className="absolute left-4 top-1.5 w-6 h-6 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center text-white text-xs font-bold shadow-sm">
                        {index + 1}
                      </div>

                      <div className="flex justify-between items-center ml-10">
                        <div className="flex-1 flex items-center gap-2">
                          <h4 className="text-gray-800 font-medium group-hover:text-gray-900 transition-colors" style={{marginBottom: 0}}>
                            {courseItem.title}
                          </h4>
                          <span className="text-xs text-gray-500">
                            第{index + 1}课
                          </span>
                        </div>

                        <div className="flex items-center gap-2">
                          <span className={`inline-flex items-center gap-1 px-3 py-1.5 text-xs font-semibold rounded-full transition-all duration-200 ${
                            courseItem.status === 1
                              ? 'bg-gradient-to-r from-emerald-100 to-green-100 text-emerald-700 border border-emerald-200/50 shadow-sm'
                              : 'bg-gradient-to-r from-gray-100 to-slate-100 text-gray-600 border border-gray-200/50'
                          }`}>
                            <div className={`w-2 h-2 rounded-full ${
                              courseItem.status === 1 ? 'bg-emerald-500' : 'bg-gray-400'
                            }`}></div>
                            {courseItem.status === 1 ? '已发布' : '草稿'}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-16">
                  <div className="w-16 h-16 bg-gradient-to-r from-gray-100 to-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                  <p className="text-gray-500 font-medium">暂无课程</p>
                  <p className="text-gray-400 text-sm mt-1">该系列还没有添加课程</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
}
