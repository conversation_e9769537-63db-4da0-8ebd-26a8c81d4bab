{"version": 3, "file": "query-interceptor.js", "sourceRoot": "", "sources": ["../../../../../src/util/database/monitor/interceptor/query-interceptor.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAAoD;AACpD,4EAAuE;AAUhE,IAAM,gBAAgB,wBAAtB,MAAM,gBAAgB;IAIE;IAHZ,MAAM,GAAG,IAAI,eAAM,CAAC,kBAAgB,CAAC,IAAI,CAAC,CAAC;IACpD,aAAa,GAAG,IAAI,GAAG,EAAqB,CAAC;IAErD,YAA6B,mBAAwC;QAAxC,wBAAmB,GAAnB,mBAAmB,CAAqB;IAAG,CAAC;IAKzE,WAAW,CAAC,KAAa,EAAE,UAAkB,EAAE,OAAgB;QAE7D,MAAM,OAAO,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QACvC,MAAM,SAAS,GAAc;YAC3B,KAAK;YACL,UAAU;YACV,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,OAAO;SACR,CAAC;QAEF,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;QAC3C,OAAO,OAAO,CAAC;IACjB,CAAC;IAKD,cAAc,CAAC,OAAe,EAAE,KAAa;QAC3C,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAClD,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,OAAO;QACT,CAAC;QACD,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,SAAS,CAAC;QACvD,OAAO,CAAC,GAAG,CAAC,cAAc,EAAC,aAAa,CAAC,CAAC;QAE1C,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAClC,SAAS,CAAC,KAAK,EACf,aAAa,EACb,SAAS,CAAC,UAAU,EACpB,SAAS,CAAC,OAAO,CAClB,CAAC;QAGF,IAAI,KAAK,EAAE,CAAC;YACV,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,aAAa,QAAQ,KAAK,CAAC,OAAO,EAAE,EAAE;gBACjE,KAAK,EAAE,SAAS,CAAC,KAAK;gBACtB,UAAU,EAAE,SAAS,CAAC,UAAU;gBAChC,aAAa;gBACb,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,OAAO,EAAE,SAAS,CAAC,OAAO;aAC3B,CAAC,CAAC;QACL,CAAC;QAGD,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IACrC,CAAC;IAKO,eAAe;QACrB,OAAO,SAAS,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IAC1E,CAAC;IAKD,mBAAmB;QACjB,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;IACjC,CAAC;IAKD,gBAAgB;QACd,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC1D,GAAG,IAAI;YACP,WAAW,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,SAAS;SACzC,CAAC,CAAC,CAAC;IACN,CAAC;IAKD,qBAAqB,CAAC,YAAoB,MAAM;QAC9C,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,cAAc,GAAa,EAAE,CAAC;QAEpC,KAAK,MAAM,CAAC,OAAO,EAAE,SAAS,CAAC,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,EAAE,CAAC;YAChE,IAAI,GAAG,GAAG,SAAS,CAAC,SAAS,GAAG,SAAS,EAAE,CAAC;gBAC1C,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBAG7B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,GAAG,GAAG,SAAS,CAAC,SAAS,KAAK,EAAE;oBAC3D,KAAK,EAAE,SAAS,CAAC,KAAK;oBACtB,UAAU,EAAE,SAAS,CAAC,UAAU;oBAChC,OAAO,EAAE,SAAS,CAAC,OAAO;iBAC3B,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAGD,cAAc,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YAC/B,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC9B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,cAAc,CAAC,MAAM,QAAQ,CAAC,CAAC;QACzD,CAAC;IACH,CAAC;CACF,CAAA;AA7GY,4CAAgB;2BAAhB,gBAAgB;IAD5B,IAAA,mBAAU,GAAE;qCAKuC,2CAAmB;GAJ1D,gBAAgB,CA6G5B"}