'use client'

import React from 'react';
import { Modal } from 'antd';
import { RechargeOption } from '../types';

interface SuccessModalProps {
  visible: boolean;
  onClose: () => void;
  plan: RechargeOption | null;
  orderInfo?: any;
}

const SuccessModal: React.FC<SuccessModalProps> = ({
  visible,
  onClose,
  plan,
  orderInfo
}) => {
  const handleClose = () => {
    onClose();
  };

  return (
    <Modal
      open={visible}
      onCancel={handleClose}
      footer={null}
      width={400}
      centered
      closable={false}
      className="success-modal"
    >
      <div className="success-content" style={{
        textAlign: 'center',
        padding: '20px 0'
      }}>
        {/* 成功图标 */}
        <div className="success-icon" style={{
          fontSize: '64px',
          marginBottom: '20px'
        }}>
          🎉
        </div>

        {/* 恭喜标题 */}
        <h2 style={{
          fontSize: '24px',
          fontWeight: 'bold',
          color: '#52c41a',
          marginBottom: '16px'
        }}>
          支付成功！
        </h2>

        {/* 获得内容 */}
        <div className="reward-info" style={{
          backgroundColor: '#f6ffed',
          border: '1px solid #b7eb8f',
          borderRadius: '8px',
          padding: '20px',
          marginBottom: '20px'
        }}>
          <div style={{
            fontSize: '18px',
            fontWeight: 'bold',
            color: '#389e0d',
            marginBottom: '8px'
          }}>
            🎁 恭喜获得
          </div>
          
          {plan && (
            <>
              <div style={{
                fontSize: '32px',
                fontWeight: 'bold',
                color: '#52c41a',
                marginBottom: '8px'
              }}>
                +¥{plan.amount % 1 === 0 ? Math.floor(plan.amount) : plan.amount} 余额
              </div>

              <div style={{
                fontSize: '14px',
                color: '#666',
                marginBottom: '12px'
              }}>
                {plan.title} - ¥{plan.amount % 1 === 0 ? Math.floor(plan.amount) : plan.amount}
              </div>

              {plan.bonus && plan.bonus > 0 && (
                <div style={{
                  fontSize: '14px',
                  color: '#fa8c16',
                  backgroundColor: '#fff7e6',
                  border: '1px solid #ffd591',
                  borderRadius: '4px',
                  padding: '4px 8px',
                  display: 'inline-block'
                }}>
                  🎁 额外赠送 +{Math.floor(plan.bonus)} 积分
                </div>
              )}
            </>
          )}
        </div>

        {/* 订单信息 */}
        {orderInfo && (
          <div className="order-info" style={{
            fontSize: '12px',
            color: '#999',
            marginBottom: '20px',
            padding: '12px',
            backgroundColor: '#fafafa',
            borderRadius: '6px'
          }}>
            <div>订单号: {orderInfo.orderNo}</div>
            {orderInfo.paymentTime && (
              <div>支付时间: {new Date(orderInfo.paymentTime).toLocaleString()}</div>
            )}
          </div>
        )}

        {/* 关闭按钮 */}
        <button
          onClick={handleClose}
          style={{
            backgroundColor: '#52c41a',
            color: 'white',
            border: 'none',
            borderRadius: '6px',
            padding: '12px 32px',
            fontSize: '16px',
            fontWeight: 'bold',
            cursor: 'pointer',
            transition: 'all 0.3s'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = '#389e0d';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = '#52c41a';
          }}
        >
          太棒了！
        </button>

        {/* 装饰性元素 */}
        <div style={{
          position: 'absolute',
          top: '10px',
          left: '10px',
          fontSize: '20px',
          opacity: 0.3
        }}>
          ✨
        </div>
        <div style={{
          position: 'absolute',
          top: '20px',
          right: '20px',
          fontSize: '16px',
          opacity: 0.3
        }}>
          🎊
        </div>
        <div style={{
          position: 'absolute',
          bottom: '20px',
          left: '20px',
          fontSize: '18px',
          opacity: 0.3
        }}>
          🌟
        </div>
        <div style={{
          position: 'absolute',
          bottom: '30px',
          right: '15px',
          fontSize: '14px',
          opacity: 0.3
        }}>
          💫
        </div>
      </div>
    </Modal>
  );
};

export default SuccessModal;
