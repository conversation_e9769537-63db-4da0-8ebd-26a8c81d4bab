"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseTestController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const typeorm_1 = require("typeorm");
const not_login_decorator_1 = require("../../../../web/router_guard/not-login.decorator");
let DatabaseTestController = class DatabaseTestController {
    dataSource;
    constructor(dataSource) {
        this.dataSource = dataSource;
    }
    async testSlowQuery(delay = 2) {
        const startTime = Date.now();
        try {
            const result = await this.dataSource.query(`SELECT SLEEP(${delay}) as sleep_result`);
            const executionTime = Date.now() - startTime;
            return {
                code: 200,
                message: '慢查询测试完成',
                data: {
                    delay: delay,
                    executionTime: executionTime,
                    result: result
                }
            };
        }
        catch (error) {
            const executionTime = Date.now() - startTime;
            return {
                code: 500,
                message: '慢查询测试失败',
                data: {
                    delay: delay,
                    executionTime: executionTime,
                    error: error.message
                }
            };
        }
    }
    async testComplexQuery() {
        const startTime = Date.now();
        try {
            const result = await this.dataSource.query(`
        SELECT COUNT(*) as count 
        FROM (
          SELECT a.id as id1, b.id as id2 
          FROM user_students a 
          CROSS JOIN user_students b 
          LIMIT 10000
        ) as temp
      `);
            const executionTime = Date.now() - startTime;
            return {
                code: 200,
                message: '复杂查询测试完成',
                data: {
                    executionTime: executionTime,
                    result: result
                }
            };
        }
        catch (error) {
            const executionTime = Date.now() - startTime;
            return {
                code: 500,
                message: '复杂查询测试失败',
                data: {
                    executionTime: executionTime,
                    error: error.message
                }
            };
        }
    }
    async testBatchQueries(count = 10) {
        const startTime = Date.now();
        const results = [];
        try {
            for (let i = 0; i < count; i++) {
                const queryStart = Date.now();
                const result = await this.dataSource.query(`SELECT 1 as test_result`);
                const queryTime = Date.now() - queryStart;
                results.push({
                    queryNumber: i,
                    executionTime: queryTime,
                    result: result[0]
                });
                if (Math.random() > 0.7) {
                    await new Promise(resolve => setTimeout(resolve, Math.random() * 1000));
                }
            }
            const totalExecutionTime = Date.now() - startTime;
            return {
                code: 200,
                message: '批量查询测试完成',
                data: {
                    totalQueries: count,
                    totalExecutionTime: totalExecutionTime,
                    averageExecutionTime: totalExecutionTime / count,
                    results: results
                }
            };
        }
        catch (error) {
            const totalExecutionTime = Date.now() - startTime;
            return {
                code: 500,
                message: '批量查询测试失败',
                data: {
                    totalQueries: count,
                    totalExecutionTime: totalExecutionTime,
                    completedQueries: results.length,
                    error: error.message,
                    results: results
                }
            };
        }
    }
    async getConnectionInfo() {
        try {
            const connectionInfo = await this.dataSource.query(`
        SELECT 
          CONNECTION_ID() as connection_id,
          USER() as current_user,
          DATABASE() as current_database,
          VERSION() as mysql_version,
          @@max_connections as max_connections,
          @@thread_cache_size as thread_cache_size,
          @@query_cache_size as query_cache_size
      `);
            const processlist = await this.dataSource.query(`
        SELECT COUNT(*) as active_connections 
        FROM INFORMATION_SCHEMA.PROCESSLIST 
        WHERE COMMAND != 'Sleep'
      `);
            return {
                code: 200,
                message: '获取数据库连接信息成功',
                data: {
                    connectionInfo: connectionInfo[0],
                    activeConnections: processlist[0].active_connections,
                    isConnected: this.dataSource.isInitialized
                }
            };
        }
        catch (error) {
            return {
                code: 500,
                message: '获取数据库连接信息失败',
                data: {
                    error: error.message,
                    isConnected: this.dataSource.isInitialized
                }
            };
        }
    }
};
exports.DatabaseTestController = DatabaseTestController;
__decorate([
    (0, common_1.Get)('slow-query'),
    (0, swagger_1.ApiOperation)({ summary: '测试慢查询' }),
    (0, swagger_1.ApiQuery)({ name: 'delay', required: false, description: '延迟时间(秒)', type: Number }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '测试成功' }),
    __param(0, (0, common_1.Query)('delay')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], DatabaseTestController.prototype, "testSlowQuery", null);
__decorate([
    (0, common_1.Get)('complex-query'),
    (0, swagger_1.ApiOperation)({ summary: '测试复杂查询' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '测试成功' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], DatabaseTestController.prototype, "testComplexQuery", null);
__decorate([
    (0, common_1.Get)('batch-queries'),
    (0, swagger_1.ApiOperation)({ summary: '测试批量查询' }),
    (0, swagger_1.ApiQuery)({ name: 'count', required: false, description: '查询次数', type: Number }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '测试成功' }),
    __param(0, (0, common_1.Query)('count')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], DatabaseTestController.prototype, "testBatchQueries", null);
__decorate([
    (0, common_1.Get)('connection-info'),
    (0, swagger_1.ApiOperation)({ summary: '获取数据库连接信息' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], DatabaseTestController.prototype, "getConnectionInfo", null);
exports.DatabaseTestController = DatabaseTestController = __decorate([
    (0, swagger_1.ApiTags)('数据库测试'),
    (0, common_1.Controller)('api/v1/database-test'),
    (0, not_login_decorator_1.NotLogin)(),
    __metadata("design:paramtypes", [typeorm_1.DataSource])
], DatabaseTestController);
//# sourceMappingURL=database-test.controller.js.map