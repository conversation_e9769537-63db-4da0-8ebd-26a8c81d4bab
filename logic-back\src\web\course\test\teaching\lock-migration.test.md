# Teaching 模块锁管理器迁移测试

## 迁移概述

本次迁移将 teaching 模块的锁管理从自定义的内存锁实现迁移到 payment 模块的 Redis 分布式锁实现。

## 迁移内容

### 1. 删除的文件
- `logic-back/src/web/course/utils/teaching/lock-manager.ts` - 旧的内存锁实现
- `logic-back/src/web/course/utils/teaching/lock-adapter.ts` - 临时适配器（已删除）

### 2. 修改的文件
- `logic-back/src/web/course/course.module.ts` - 更新依赖注入
- `logic-back/src/web/course/application/services/teaching/teaching.service.ts` - 更新锁管理器引用
- `logic-back/src/web/course/docs/teaching/teaching模块功能总结.md` - 更新文档

### 3. 新的依赖关系
```typescript
// 新的导入
import { LockManager } from '../../../../../payment/lock/lock.manager';

// 构造函数注入
constructor(
  // ... 其他依赖
  private readonly lockManager: LockManager, // 使用 payment 的 LockManager
  // ... 其他依赖
) {}
```

## 功能对比

### 旧实现（内存锁）
- 基于 Map 的内存锁
- 单机有效
- 手动清理过期锁
- 简单的重试机制

### 新实现（Redis 分布式锁）
- 基于 Redis 的分布式锁
- 多实例共享
- Redis 自动过期
- 更强大的重试和错误处理

## 接口兼容性

### withDistributedLock 方法
```typescript
// 旧接口
async withDistributedLock<T>(
  lockKey: string,
  operation: () => Promise<T>,
  ttl: number = 60000,
  maxRetries: number = 3,
  retryDelay: number = 1000
): Promise<T>

// 新接口
async withDistributedLock<T>(
  key: string,
  callback: () => Promise<T>,
  ttl: number = 10000
): Promise<T>
```



## 测试建议

### 1. 单元测试
- 测试锁获取成功的情况
- 测试锁获取失败的异常处理
- 测试锁超时机制

### 2. 集成测试
- 测试一键上课功能的并发控制
- 测试多实例环境下的锁互斥
- 测试 Redis 连接异常的处理

### 3. 性能测试
- 对比内存锁和 Redis 锁的性能差异
- 测试高并发场景下的锁竞争

## 部署注意事项

### 1. Redis 依赖
- 确保 Redis 服务正常运行
- 检查 Redis 连接配置
- 监控 Redis 连接状态

### 2. 配置检查
- 验证 PaymentConfigService 配置
- 检查锁超时时间设置
- 确认重试机制参数

### 3. 监控指标
- 锁获取成功率
- 锁获取耗时
- 锁竞争频率
- Redis 连接状态

## 回滚方案

如果发现问题，可以通过以下步骤回滚：

1. 恢复旧的 lock-manager.ts 文件
2. 更新 course.module.ts 的依赖注入
3. 修改 teaching.service.ts 的导入
4. 重新编译和部署

## 验证清单

- [ ] 编译无错误
- [ ] 依赖注入正确
- [ ] 异常处理适配
- [ ] Redis 连接正常
- [ ] 一键上课功能正常
- [ ] 并发控制有效
- [ ] 性能指标正常
- [ ] 日志输出正确
