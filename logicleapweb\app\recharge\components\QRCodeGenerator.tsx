'use client'

import React, { useEffect, useRef, useState } from 'react';
import QRCode from 'qrcode';

interface QRCodeGeneratorProps {
  value: string;
  size?: number;
  className?: string;
  paymentMethod?: 'wechat' | 'alipay' | 'unionpay' | string;
}

const QRCodeGenerator: React.FC<QRCodeGeneratorProps> = ({
  value,
  size,
  className = '',
  paymentMethod = 'wechat'
}) => {
  // 根据支付方式设置默认大小
  const getDefaultSize = () => {
    if (paymentMethod === 'alipay') {
      return 260; // 支付宝二维码更大
    }
    return 200; // 微信支付默认大小
  };

  const finalSize = size || getDefaultSize();

  // 根据支付方式获取提示文本
  const getPaymentText = () => {
    switch (paymentMethod) {
      case 'alipay':
        return '请使用支付宝扫码支付';
      case 'unionpay':
        return '请使用银联扫码支付';
      case 'wechat':
      default:
        return '请使用微信扫码支付';
    }
  };

  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!value || !canvasRef.current) return;

    const generateQRCode = async () => {
      setLoading(true);
      setError(null);

      try {
        const canvas = canvasRef.current;
        if (!canvas) return;

        // 使用专业的 qrcode 库生成二维码
        // 根据支付方式和URL长度调整参数
        const isLongUrl = value.length > 300;
        const qrOptions = {
          width: finalSize,
          margin: paymentMethod === 'alipay' && isLongUrl ? 1 : 2, // 支付宝长URL使用更小边距
          color: {
            dark: '#000000',
            light: '#FFFFFF'
          },
          errorCorrectionLevel: (paymentMethod === 'alipay' && isLongUrl ? 'L' : 'M') as 'L' | 'M' | 'Q' | 'H'
        };

        QRCode.toCanvas(canvas, value, qrOptions);

        setLoading(false);
      } catch (err) {
        console.error('生成二维码失败:', err);
        setError('生成二维码失败');
        setLoading(false);
      }
    };

    generateQRCode();
  }, [value, finalSize, paymentMethod]);

  if (error) {
    return (
      <div className={`qr-code-container ${className}`} style={{ textAlign: 'center' }}>
        <div style={{
          width: finalSize,
          height: finalSize,
          border: '1px solid #e0e0e0',
          borderRadius: '8px',
          backgroundColor: '#f9f9f9',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center'
        }}>
          <div style={{ fontSize: '24px', marginBottom: '8px' }}>❌</div>
          <div style={{ fontSize: '12px', color: '#666' }}>二维码生成失败</div>
        </div>
      </div>
    );
  }

  return (
    <div className={`qr-code-container ${className}`} style={{ textAlign: 'center' }}>
      {loading && (
        <div style={{
          position: 'absolute',
          width: finalSize,
          height: finalSize,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: 'rgba(255, 255, 255, 0.8)',
          borderRadius: '8px',
          zIndex: 1
        }}>
          <div style={{ fontSize: '12px', color: '#666' }}>生成中...</div>
        </div>
      )}
      <canvas
        ref={canvasRef}
        width={finalSize}
        height={finalSize}
        style={{
          border: '1px solid #e0e0e0',
          borderRadius: '8px',
          backgroundColor: '#ffffff',
          display: loading ? 'none' : 'block'
        }}
      />
      <div style={{
        marginTop: '8px',
        fontSize: '12px',
        color: '#666',
        textAlign: 'center',
        maxWidth: finalSize + 'px',
        wordBreak: 'break-all'
      }}>
        {getPaymentText()}
      </div>
    </div>
  );
};

export default QRCodeGenerator;
