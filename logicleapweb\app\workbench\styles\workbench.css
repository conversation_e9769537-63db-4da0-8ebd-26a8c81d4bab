/* 全局输入框样式重置 */
.search-bar input,
.search-bar input:focus,
.search-bar input:active,
.search-bar input:focus-visible {
    border: none !important;
    outline: none !important;
    box-shadow: none !important;
    -webkit-box-shadow: none !important;
    -moz-box-shadow: none !important;
    outline-offset: 0 !important;
    outline-width: 0 !important;
    outline-style: none !important;
    outline-color: transparent !important;
}

/* 整体布局 */
.workbench-container {
    display: flex;
    min-height: 100vh;
    height: 100vh;
    background-color: #f7f8fa;
    color: #333;
    overflow: hidden;
    position: relative;
    max-width: 100vw;
}

.left-sidebar {
    width: 240px;
    min-width: 240px;
    max-width: 240px;
    background-color: #fff;
    border-right: 1px solid #eef0f2;
    display: flex;
    flex-direction: column;
    padding: 20px 12px;
    overflow-y: auto;
    flex-shrink: 0;
}

.main-content-area {
    flex: 1;
    min-width: 0;
    padding: 24px 32px;
    overflow-y: auto;
    overflow-x: hidden;
    position: relative;
    background-image: linear-gradient(rgba(0,0,0,0.02) 1px, transparent 1px), linear-gradient(90deg, rgba(0,0,0,0.02) 1px, transparent 1px);
    background-size: 20px 20px;
}

.right-sidebar {
    width: 320px;
    background-color: #f7f8fa;
    border-left: 1px solid #eef0f2;
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

/* 左侧导航栏 */
.left-sidebar .sidebar-header {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 0 12px;
    margin-bottom: 24px;
    font-size: 16px;
    font-weight: 600;
}

.left-sidebar .teacher-info {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 24px;
    padding: 0 12px;
}
.left-sidebar .teacher-info .avatar {
    border-radius: 50%;
}
.left-sidebar .teacher-name {
    font-weight: 600;
}
.left-sidebar .teacher-title {
    font-size: 12px;
    color: #909399;
}
.left-sidebar .sidebar-nav ul {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: 4px;
}
.left-sidebar .nav-item {
    display: flex;
    align-items: center;
    padding: 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.2s, color 0.2s;
    position: relative;
    color: #606266;
}
.left-sidebar .nav-item:hover {
    background-color: #f5f7fa;
}
.left-sidebar .nav-item.active {
    background-color: #f0f4ff;
    color: #4a6fff;
    font-weight: 600;
}

.left-sidebar .nav-item.active .nav-icon {
    color: #4a6fff;
}
.left-sidebar .nav-icon {
    margin-right: 12px;
    width: 20px;
    height: 20px;
}
.left-sidebar .sidebar-footer {
    margin-top: auto;
    display: flex;
    align-items: center;
    padding: 12px;
    cursor: pointer;
    color: #606266;
    border-radius: 8px;
    transition: all 0.2s ease;
    border: 1px solid transparent;
}
.left-sidebar .sidebar-footer:hover {
    background-color: #f0f4ff;
    color: #4a6fff;
    border-color: #e4e7ed;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(74, 111, 255, 0.15);
}
.left-sidebar .sidebar-footer:active {
    transform: translateY(0);
    box-shadow: 0 1px 4px rgba(74, 111, 255, 0.1);
}

/* Add animation to nav-item transitions */
.left-sidebar .nav-item::before {
    content: '';
    position: absolute;
    left: -12px;
    top: 50%;
    transform: translateY(-50%) scaleY(0);
    height: 60%;
    width: 4px;
    background-color: #4a6fff;
    border-radius: 0 4px 4px 0;
    transition: transform 0.3s ease, height 0.3s ease;
}

.left-sidebar .nav-item.active::before {
    transform: translateY(-50%) scaleY(1);
}

/* 导航分隔线 */
.left-sidebar .nav-divider {
    height: 1px;
    background-color: #eef0f2;
    margin: 8px 0;
    width: 100%;
}

/* 下拉菜单样式 */
.left-sidebar .nav-item-dropdown {
    position: relative;
}

.left-sidebar .nav-item.dropdown-open {
    background-color: #f0f4ff;
    color: #4a6fff;
}

/* 只有当菜单项既是active又是dropdown-open时才显示蓝色竖条 */
.left-sidebar .nav-item.active.dropdown-open::before {
    transform: translateY(-50%) scaleY(1);
}

.left-sidebar .dropdown-arrow {
    margin-left: auto;
    transition: transform 0.3s ease;
    color: #909399;
    flex-shrink: 0;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.left-sidebar .dropdown-arrow.rotated {
    transform: rotate(180deg) !important;
    color: #4a6fff;
}

.left-sidebar .nav-item.dropdown-open .dropdown-arrow {
    color: #4a6fff;
}

.left-sidebar .dropdown-menu {
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    margin-top: 4px;
    margin-left: 0;
    padding: 6px 0;
    border: 1px solid #e5e7eb;
    animation: dropdownSlide 0.2s ease-out;
    width: 100%;
    max-height: 280px;
    overflow-y: auto;
    position: relative;
    z-index: 10;
}

/* 优化滚动条样式 */
.left-sidebar .dropdown-menu::-webkit-scrollbar {
    width: 4px;
}

.left-sidebar .dropdown-menu::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 2px;
}

.left-sidebar .dropdown-menu::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 2px;
    transition: background 0.2s ease;
}

.left-sidebar .dropdown-menu::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
}

@keyframes dropdownSlide {
    from {
        opacity: 0;
        transform: translateY(-8px) scale(0.98);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.left-sidebar .dropdown-item {
    padding: 12px 16px;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #374151;
    font-size: 14px;
    font-weight: 500;
    background: transparent;
    border: none;
    text-align: left;
    width: calc(100% - 12px);
    margin: 2px 6px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    min-height: 40px;
}

.left-sidebar .dropdown-item:hover:not(.disabled) {
    background-color: #f3f4f6;
    color: #1f2937;
    transform: translateY(-1px);
}

.left-sidebar .dropdown-item.selected {
    background-color: #dbeafe;
    color: #1d4ed8;
    font-weight: 600;
    position: relative;
    border: 1px solid #93c5fd;
}

.left-sidebar .dropdown-item.selected::after {
    display: none;
}

.left-sidebar .dropdown-item .school-info {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: 2px;
}

.left-sidebar .dropdown-item .school-name {
    font-weight: 500;
    color: inherit;
    font-size: 14px;
    line-height: 1.3;
}

.left-sidebar .dropdown-item .school-location {
    display: none; /* 隐藏地理位置信息，保持简洁 */
}

.left-sidebar .dropdown-item.disabled {
    color: #9ca3af;
    cursor: not-allowed;
    background: #f9fafb;
    text-align: center;
    font-style: normal;
    position: relative;
    padding: 16px;
    border: 1px dashed #d1d5db;
    margin: 6px;
    border-radius: 6px;
    font-size: 13px;
    min-height: 60px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.left-sidebar .dropdown-item.disabled::before {
    content: '🏫';
    display: block;
    font-size: 28px;
    margin-bottom: 8px;
    opacity: 0.6;
    filter: grayscale(0.3);
}

.left-sidebar .dropdown-item.disabled::after {
    content: '请联系管理员添加学校信息';
    display: block;
    font-size: 11px;
    color: #6b7280;
    margin-top: 4px;
    font-style: normal;
}

.left-sidebar .dropdown-menu.empty {
    min-height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8faff;
    border: none;
}

/* 加载状态样式 */
.left-sidebar .dropdown-item.disabled.loading {
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%) !important;
    border-color: #7dd3fc !important;
    color: #0369a1 !important;
    cursor: not-allowed !important;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 20px 16px;
}

.left-sidebar .loading-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid #bae6fd;
    border-top: 2px solid #0369a1;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 错误状态样式 */
.left-sidebar .dropdown-item.disabled.error {
    background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%) !important;
    border-color: #fca5a5 !important;
    color: #dc2626 !important;
    cursor: not-allowed !important;
}

.left-sidebar .dropdown-item.disabled.error::before {
    content: '⚠️';
    display: block;
    font-size: 20px;
    margin-bottom: 4px;
}

/* 无数据状态样式 */
.left-sidebar .dropdown-item.disabled.no-data {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%) !important;
    border-color: #cbd5e1 !important;
    color: #64748b !important;
    cursor: not-allowed !important;
}

.left-sidebar .dropdown-item.disabled.no-data::before {
    content: '📂';
    display: block;
    font-size: 20px;
    margin-bottom: 4px;
    opacity: 0.6;
}

/* 确保禁用状态的菜单项不会有悬停效果 */
.left-sidebar .dropdown-item.disabled:hover {
    cursor: not-allowed !important;
    background: inherit !important;
    color: inherit !important;
    transform: none !important;
}

.left-sidebar .dropdown-item.disabled.loading:hover {
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%) !important;
    color: #0369a1 !important;
    cursor: not-allowed !important;
}

.left-sidebar .dropdown-item.disabled.error:hover {
    background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%) !important;
    color: #dc2626 !important;
    cursor: not-allowed !important;
}

.left-sidebar .dropdown-item.disabled.no-data:hover {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%) !important;
    color: #64748b !important;
    cursor: not-allowed !important;
}

/* 主内容区 */
.main-content {
    flex-direction: column;
    gap: 32px;
    width: 100%;
    min-width: 0;
    position: relative;
}

.main-content .main-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;
}
.main-content .search-bar {
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    padding: 12px 20px;
    border-radius: 24px;
    width: 360px;
    border: 2px solid transparent;
    background-clip: padding-box;
    box-shadow:
        0 4px 12px rgba(0, 0, 0, 0.05),
        0 0 0 1px rgba(59, 130, 246, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.main-content .search-bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(147, 197, 253, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.main-content .search-bar:hover {
    box-shadow:
        0 8px 24px rgba(0, 0, 0, 0.08),
        0 0 0 1px rgba(59, 130, 246, 0.2);
    transform: translateY(-1px);
}

.main-content .search-bar:hover::before {
    opacity: 1;
}

.main-content .search-bar:focus-within {
    box-shadow:
        0 8px 24px rgba(59, 130, 246, 0.15),
        0 0 0 2px rgba(59, 130, 246, 0.3);
    transform: translateY(-2px);
}

.main-content .search-bar:focus-within::before {
    opacity: 1;
}

.main-content .search-icon {
    color: #6b7280;
    margin-right: 12px;
    transition: all 0.3s ease;
    z-index: 1;
    position: relative;
}

.main-content .search-bar:hover .search-icon {
    color: #3b82f6;
    transform: scale(1.1);
}

.main-content .search-bar:focus-within .search-icon {
    color: #2563eb;
    transform: scale(1.1);
}

.main-content .search-bar input {
    border: none !important;
    outline: none !important;
    background: transparent;
    width: 100%;
    font-size: 14px;
    color: #374151;
    font-weight: 400;
    z-index: 1;
    position: relative;
    box-shadow: none !important;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    -webkit-box-shadow: none !important;
    -moz-box-shadow: none !important;
}

.main-content .search-bar input:focus,
.main-content .search-bar input:active,
.main-content .search-bar input:focus-visible {
    border: none !important;
    outline: none !important;
    box-shadow: none !important;
    -webkit-box-shadow: none !important;
    -moz-box-shadow: none !important;
    outline-offset: 0 !important;
    outline-width: 0 !important;
    outline-style: none !important;
    outline-color: transparent !important;
}

.main-content .search-bar input::placeholder {
    color: #9ca3af;
    font-weight: 400;
    transition: color 0.3s ease;
}

.main-content .search-bar:focus-within input::placeholder {
    color: #6b7280;
}

.main-content .start-class-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    background-color: #3b82f6;
    color: white;
    border: none;
    border-radius: 30px;
    padding: 12px 24px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    box-shadow: 0 2px 6px rgba(59, 130, 246, 0.3);
    white-space: nowrap;
}

.main-content .start-class-btn:hover {
    background-color: #2563eb;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(59, 130, 246, 0.4);
}

.main-content .start-class-btn .start-class-icon {
    width: 20px;
    height: 20px;
    fill: white;
    flex-shrink: 0;
}

.main-content .start-class-btn span {
    flex-shrink: 0;
}

.section-title {
    font-size: 20px;
    font-weight: 700;
    /* margin-bottom: 20px; */
    color: #303133;
    text-align: left;
    padding-left: 0;
    border-bottom: none;
    text-decoration: none;
    position: relative;
}

.section-title::before,
.section-title::after {
    display: none;
}

/* 快速操作 */
.quick-actions .section-title {
    font-size: 20px;
    font-weight: 700;
    margin-bottom: 20px;
    color: #303133;
    text-align: left;
    padding-left: 0;
    border-bottom: none;
    text-decoration: none;
}

.quick-actions .quick-actions-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.quick-actions {
    width: 100%;
    min-width: 0;
    margin-top: 25px;
}

.quick-actions .actions-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 24px;
    margin-bottom: 16px;
    width: 100%;
    min-width: 0;
}
.action-card {
    padding: 24px;
    border-radius: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: transform 0.2s, box-shadow 0.2s;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(0,0,0,0.05);
    min-width: 0;
    width: 100%;
}
.action-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0,0,0,0.08);
}
.action-card:active {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.06);
}
.action-card.green { background: linear-gradient(135deg, #f0fff0, #d4f4d6); }
.action-card.purple { background: linear-gradient(135deg, #f5f0ff, #e2d9ff); }
.action-card.orange { background: linear-gradient(135deg, #fff5e6, #ffe2d1); }
.action-card h3 { font-size: 16px; font-weight: 600; margin-bottom: 8px; }
.action-card p { font-size: 13px; color: #606266; }
.action-card .card-icon {
    background-color: rgba(0, 0, 0, 0.04);
    border-radius: 50%;
    padding: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 进行中的任务 */
.ongoing-tasks .section-title {
    font-size: 20px;
    font-weight: 700;
    margin-bottom: 20px;
    color: #303133;
    text-align: left;
    padding-left: 0;
    border-bottom: none;
    text-decoration: none;
}

.ongoing-tasks {
    width: 100%;
    min-width: 0;
}

.ongoing-tasks .filter-tabs-container {
    background: #f0f8ff;
    border-radius: 12px;
    padding: 12px 20px;
    border: 1px solid #e6f3ff;
    margin-bottom: 20px;
    width: 100%;
    min-width: 1000px;
    overflow: visible;
}

.ongoing-tasks .filter-tabs {
    position: relative;
    height: 40px;
    padding: 0 20px;
    width: 100%;
    min-width: 1000px;
}

.ongoing-tasks .filter-tab {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    font-size: 14px;
    color: #606266;
    cursor: pointer;
    padding: 8px 0;
    border-bottom: 2px solid transparent;
    transition: all 0.2s;
    text-align: center;
    position: absolute;
}

.ongoing-tasks .filter-tab:nth-child(1) {
    left: 76px;
    width: 60px;
}

.ongoing-tasks .filter-tab:nth-child(2) {
    left: 220px;
    width: 120px;
}

.ongoing-tasks .filter-tab:nth-child(3) {
    left: 450px;
    width: 80px;
}

.ongoing-tasks .filter-tab:nth-child(4) {
    left: 580px;
    width: 60px;
}

.ongoing-tasks .filter-tab:nth-child(5) {
    left: 690px;
    width: 80px;
}

.ongoing-tasks .filter-tab:nth-child(6) {
    left: 780px;
    width: 100px;
}

.ongoing-tasks .filter-tab:hover {
    color: #0080ff;
    border-bottom-color: #0080ff;
}

.ongoing-tasks .tasks-grid {
    display: flex;
    flex-direction: column;
    gap: 16px;
    width: 100%;
    min-width: 0;
}

.ongoing-tasks .task-card {
    background: white;
    border-radius: 16px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border: 1px solid #f0f0f0;
    transition: all 0.2s;
    width: 100%;
    min-width: 0;
    overflow: hidden;
}

.ongoing-tasks .task-card:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.ongoing-tasks .task-card-content {
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    width: 100%;
    padding: 0 20px;
}

.ongoing-tasks .task-left {
    display: flex;
    align-items: center;
    gap: 20px;
    flex: 1;
    min-width: 0;
    justify-content: center;
    padding-left: 30px;
}

.ongoing-tasks .progress-circle-modern {
    width: 52px;
    height: 52px;
    border-radius: 50%;
    background: conic-gradient(#0080ff 0deg 288deg, #f0f0f0 288deg 360deg);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    flex-shrink: 0;
}

.ongoing-tasks .progress-circle-modern::before {
    content: '';
    position: absolute;
    width: 38px;
    height: 38px;
    background: white;
    border-radius: 50%;
}

.ongoing-tasks .progress-text {
    position: relative;
    z-index: 2;
    font-weight: 600;
    font-size: 12px;
    color: #0080ff;
}

.ongoing-tasks .task-details {
    flex: 1;
    text-align: center;
}

.ongoing-tasks .task-title {
    font-size: 16px;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
    line-height: 1.5;
    letter-spacing: -0.01em;
}

.ongoing-tasks .task-description {
    font-size: 13px;
    color: #6b7280;
    line-height: 1.4;
    margin-top: 4px;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.ongoing-tasks .task-right {
    display: flex;
    align-items: center;
    flex: 1;
    justify-content: space-evenly;
    min-width: 0;
}

.ongoing-tasks .task-stats {
    display: flex;
    align-items: center;
    justify-content: space-evenly;
    width: 100%;
    gap: 0;
}

.ongoing-tasks .stat-item {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    font-size: 14px;
    color: #4b5563;
    flex: 1;
    text-align: center;
}

.ongoing-tasks .stat-value {
    font-weight: 600;
    color: #1f2937;
    font-size: 15px;
}

.ongoing-tasks .star-icon {
    color: #fbbf24;
}

.ongoing-tasks .class-info {
    flex: 1;
    display: flex;
    justify-content: center;
}

.ongoing-tasks .class-badge {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 10px 16px;
    text-align: center;
    min-width: 100px;
    max-width: 120px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.ongoing-tasks .class-name {
    font-size: 13px;
    font-weight: 500;
    color: #303133;
    margin-bottom: 2px;
}

.ongoing-tasks .subclass-name {
    font-size: 12px;
    color: #909399;
}

/* 右侧边栏 */
.right-sidebar-header {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 16px;
}
.right-sidebar .header-icon {
    cursor: pointer;
    color: #606266;
}
.right-sidebar .user-dropdown {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
}
.right-sidebar .avatar {
    border-radius: 50%;
}
.right-sidebar-card {
    background-color: #fff;
    border-radius: 16px;
    padding: 20px;
    border: 1px solid #eef0f2;
}
.user-profile-card {
    background-color: #fff;
    border-radius: 16px;
    padding: 20px;
    text-align: center;
    border: 1px solid #eef0f2;
}
.user-profile-card .profile-avatar-container {
    margin: -65px auto 10px;
    width: 88px;
    height: 88px;
    border-radius: 50%;
    position: relative;
}
.user-profile-card .profile-avatar-container::before {
    content: '';
    position: absolute;
    inset: -6px;
    background: conic-gradient(from 90deg, #3ddc84, #a767ff, #ff8c5a, #3ddc84);
    border-radius: 50%;
    z-index: 0;
    filter: blur(4px);
    opacity: 0.8;
}
.user-profile-card .profile-avatar {
    position: relative;
    z-index: 1;
    border-radius: 50%;
    border: 4px solid #fff;
}
.user-profile-card .profile-name {
    font-size: 18px;
    font-weight: 600;
}
.user-profile-card .stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
    margin: 20px 0;
}
.user-profile-card .stat-value {
    font-size: 20px;
    font-weight: bold;
}
.user-profile-card .stat-label {
    font-size: 13px;
    color: #909399;
}
.user-profile-card .energy-balance {
    margin-top: 20px;
}
.user-profile-card .energy-value {
    font-size: 22px;
    font-weight: bold;
    color: #4a6fff;
    margin: 8px 0;
}
.user-profile-card .recharge-btn {
    width: 100%;
    background-color: #4a6fff;
    color: #fff;
    border: none;
    padding: 12px;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.2s;
}
.user-profile-card .recharge-btn:hover {
    background-color: #5a7fff;
}

.my-courses-card {
    background-color: #fff;
    border-radius: 16px;
    padding: 20px;
    border: 1px solid #eef0f2;
}
.my-courses-card .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}
.my-courses-card h4 { font-weight: 600; font-size: 16px; }
.my-courses-card .courses-list {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: 16px;
}
.my-courses-card .course-item {
    display: flex;
    align-items: center;
    gap: 12px;
}
.my-courses-card .course-icon-placeholder {
    width: 48px;
    height: 48px;
    background-color: #f0f4ff;
    border-radius: 8px;
    background-image: url("data:image/svg+xml,%3csvg width='100%25' height='100%25' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100%25' height='100%25' fill='none' rx='8' ry='8' stroke='%23D4DFFF' stroke-width='2' stroke-dasharray='6%2c 6' stroke-dashoffset='0' stroke-linecap='square'/%3e%3c/svg%3e");
}
.my-courses-card .course-name {
    font-weight: 500;
}
.my-courses-card .course-series {
    font-size: 13px;
    color: #909399;
}
.my-courses-card .draft-btn {
    margin-left: auto;
    background: #f5f7fa;
    border: 1px solid #eef0f2;
    color: #606266;
    border-radius: 12px;
    padding: 4px 10px;
    font-size: 12px;
    cursor: pointer;
}

.current-template-card {
    background-color: #fff;
    border-radius: 16px;
    padding: 20px;
    border: 1px solid #eef0f2;
    font-size: 14px;
}
.current-template-card .template-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 8px;
    font-weight: 500;
}
.current-template-card .template-info > svg {
    color: #909399;
}

/* 响应式设计 */
@media (max-width: 1400px) {
    .right-sidebar {
        display: none;
    }
}

@media (max-width: 1200px) {
    .main-content-area {
        padding: 20px 24px;
    }

    .quick-actions .actions-container {
        grid-template-columns: repeat(3, 1fr);
        gap: 20px;
    }

    .ongoing-tasks .filter-tabs-container {
        min-width: 900px;
    }

    .ongoing-tasks .filter-tabs {
        min-width: 900px;
    }

    .ongoing-tasks .filter-tab:nth-child(1) {
        left: 65px;
        width: 55px;
    }

    .ongoing-tasks .filter-tab:nth-child(2) {
        left: 185px;
        width: 110px;
    }

    .ongoing-tasks .filter-tab:nth-child(3) {
        left: 385px;
        width: 75px;
    }

    .ongoing-tasks .filter-tab:nth-child(4) {
        left: 495px;
        width: 55px;
    }

    .ongoing-tasks .filter-tab:nth-child(5) {
        left: 590px;
        width: 75px;
    }

    .ongoing-tasks .filter-tab:nth-child(6) {
        left: 670px;
        width: 95px;
    }
}

@media (max-width: 992px) {
    .quick-actions .actions-container {
        grid-template-columns: repeat(2, 1fr);
        gap: 16px;
    }

    .main-content-area {
        padding: 16px 20px;
    }

    .main-content .search-bar {
        width: 320px;
        padding: 10px 18px;
    }
}

@media (max-width: 768px) {
    .workbench-container {
        flex-direction: column;
        height: auto;
        min-height: 100vh;
    }

    .left-sidebar {
        width: 100%;
        min-width: 100%;
        max-width: 100%;
        border-right: none;
        border-bottom: 1px solid #eef0f2;
        padding: 12px;
        order: -1;
        flex-shrink: 0;
    }

    .main-content-area {
        padding: 16px;
        flex: 1;
    }

    .main-content .main-header {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
    }

    .main-content .search-bar {
        width: 100%;
        max-width: none;
        padding: 10px 16px;
        border-radius: 20px;
    }

    .quick-actions .actions-container {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .action-card {
        padding: 20px;
    }

    .ongoing-tasks .filter-tabs {
        padding: 0 10px;
    }

    .ongoing-tasks .filter-tab:nth-child(1) {
        left: 50px;
        width: 45px;
    }

    .ongoing-tasks .filter-tab:nth-child(2) {
        left: 140px;
        width: 100px;
    }

    .ongoing-tasks .filter-tab:nth-child(3) {
        left: 290px;
        width: 70px;
    }

    .ongoing-tasks .filter-tab:nth-child(4) {
        left: 395px;
        width: 45px;
    }

    .ongoing-tasks .filter-tab:nth-child(5) {
        left: 475px;
        width: 70px;
    }

    .ongoing-tasks .filter-tab:nth-child(6) {
        left: 550px;
        width: 85px;
    }
}

@media (max-width: 480px) {
    .main-content-area {
        padding: 12px;
    }

    .main-content {
        gap: 24px;
    }

    .section-title {
        font-size: 18px;
        margin-bottom: 16px;
    }

    .action-card {
        padding: 16px;
    }

    .action-card h3 {
        font-size: 14px;
    }

    .action-card p {
        font-size: 12px;
    }

    .ongoing-tasks .task-card {
        padding: 16px;
    }

    .ongoing-tasks .filter-tabs-container {
        padding: 8px 12px;
    }

    .ongoing-tasks .filter-tabs {
        padding: 0 5px;
    }
}

/* 确保在所有屏幕尺寸下布局稳定 */
@media (min-width: 1400px) {
    .main-content-area {
        max-width: none;
        padding: 24px 40px;
    }

    .quick-actions .actions-container {
        max-width: 1200px;
    }

    .ongoing-tasks {
        max-width: none;
    }
}

/* 模板管理样式 */
.template-management {
    padding: 20px 24px;
    height: 100%;
    overflow-y: auto;
    background: #f8fafc;
}

.template-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0px;
}

.search-container {
    position: relative;
    flex: 1;
    max-width: 400px;
}

.search-container .search-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #9CA3AF;
}

.search-input {
    width: 100%;
    padding: 12px 14px 12px 42px;
    border: 1px solid #E2E8F0;
    border-radius: 10px;
    font-size: 14px;
    outline: none;
    transition: all 0.2s ease;
    background: white;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.search-input:focus {
    border-color: #3B82F6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-input::placeholder {
    color: #94A3B8;
}

.upload-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    background: linear-gradient(135deg, #3B82F6 0%, #2563EB 100%);
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.25);
}

.upload-btn:hover {
    background: linear-gradient(135deg, #2563EB 0%, #1D4ED8 100%);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.35);
    transform: translateY(-1px);
}

.template-tabs {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
    margin-top: 8px;
}

.template-tabs-group {
    display: flex;
    align-items: center;
    gap: 0;
    background: #E2E8F0;
    border-radius: 40px;
    padding: 4px;
    width: fit-content;
    position: relative;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.06);
}

.template-tabs-group::before {
    content: '';
    position: absolute;
    top: 4px;
    left: 4px;
    width: calc(50% - 4px);
    height: calc(100% - 8px);
    background: linear-gradient(135deg, #F97316 0%, #EA580C 100%);
    border-radius: 32px;
    transition: transform 0.3s ease;
    z-index: 1;
    box-shadow:
        0 2px 8px rgba(249, 115, 22, 0.3),
        0 1px 3px rgba(249, 115, 22, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.template-tabs-group.official-active::before {
    transform: translateX(100%);
    background: linear-gradient(135deg, #3B82F6 0%, #2563EB 100%);
    box-shadow:
        0 2px 8px rgba(37, 99, 235, 0.3),
        0 1px 3px rgba(37, 99, 235, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.tab-btn {
    padding: 14px 28px;
    border: none;
    background: transparent;
    color: #94A3B8;
    font-size: 15px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    display: flex;
    align-items: center;
    gap: 10px;
    min-width: 150px;
    justify-content: center;
    border-radius: 32px;
    z-index: 2;
}

.tab-btn:first-child {
    border-radius: 32px;
}

.tab-btn:nth-child(2) {
    border-radius: 32px;
    margin-left: 0;
}

.tab-btn.active {
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* 当官方模板被选中时（滑动按钮在左侧显示橙色），官方模板按钮文字变白色 */
.template-tabs-group:not(.official-active) .tab-btn:first-child {
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.tab-btn:hover:not(.active) {
    color: #64748B;
}

.tab-btn::before {
    content: '';
    width: 20px;
    height: 20px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    flex-shrink: 0;
}

.tab-btn:first-child::before {
    background-image: url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 48 48' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M18 6H8C6.89543 6 6 6.89543 6 8V18C6 19.1046 6.89543 20 8 20H18C19.1046 20 20 19.1046 20 18V8C20 6.89543 19.1046 6 18 6Z' fill='none' stroke='%2394A3B8' stroke-width='4' stroke-linejoin='round'/%3E%3Cpath d='M18 28H8C6.89543 28 6 28.8954 6 30V40C6 41.1046 6.89543 42 8 42H18C19.1046 42 20 41.1046 20 40V30C20 28.8954 19.1046 28 18 28Z' fill='none' stroke='%2394A3B8' stroke-width='4' stroke-linejoin='round'/%3E%3Cpath d='M35 20C38.866 20 42 16.866 42 13C42 9.13401 38.866 6 35 6C31.134 6 28 9.13401 28 13C28 16.866 31.134 20 35 20Z' fill='none' stroke='%2394A3B8' stroke-width='4' stroke-linejoin='round'/%3E%3Cpath d='M40 28H30C28.8954 28 28 28.8954 28 30V40C28 41.1046 28.8954 42 30 42H40C41.1046 42 42 41.1046 42 40V30C42 28.8954 41.1046 28 40 28Z' fill='none' stroke='%2394A3B8' stroke-width='4' stroke-linejoin='round'/%3E%3C/svg%3E");
}

/* 当官方模板被选中时（滑动按钮在左侧显示橙色），官方模板按钮图标变白色 */
.template-tabs-group:not(.official-active) .tab-btn:first-child::before {
    background-image: url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 48 48' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M18 6H8C6.89543 6 6 6.89543 6 8V18C6 19.1046 6.89543 20 8 20H18C19.1046 20 20 19.1046 20 18V8C20 6.89543 19.1046 6 18 6Z' fill='none' stroke='white' stroke-width='4' stroke-linejoin='round'/%3E%3Cpath d='M18 28H8C6.89543 28 6 28.8954 6 30V40C6 41.1046 6.89543 42 8 42H18C19.1046 42 20 41.1046 20 40V30C20 28.8954 19.1046 28 18 28Z' fill='none' stroke='white' stroke-width='4' stroke-linejoin='round'/%3E%3Cpath d='M35 20C38.866 20 42 16.866 42 13C42 9.13401 38.866 6 35 6C31.134 6 28 9.13401 28 13C28 16.866 31.134 20 35 20Z' fill='none' stroke='white' stroke-width='4' stroke-linejoin='round'/%3E%3Cpath d='M40 28H30C28.8954 28 28 28.8954 28 30V40C28 41.1046 28.8954 42 30 42H40C41.1046 42 42 41.1046 42 40V30C42 28.8954 41.1046 28 40 28Z' fill='none' stroke='white' stroke-width='4' stroke-linejoin='round'/%3E%3C/svg%3E");
}

.tab-btn:first-child.active::before {
    background-image: url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 48 48' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M18 6H8C6.89543 6 6 6.89543 6 8V18C6 19.1046 6.89543 20 8 20H18C19.1046 20 20 19.1046 20 18V8C20 6.89543 19.1046 6 18 6Z' fill='none' stroke='white' stroke-width='4' stroke-linejoin='round'/%3E%3Cpath d='M18 28H8C6.89543 28 6 28.8954 6 30V40C6 41.1046 6.89543 42 8 42H18C19.1046 42 20 41.1046 20 40V30C20 28.8954 19.1046 28 18 28Z' fill='none' stroke='white' stroke-width='4' stroke-linejoin='round'/%3E%3Cpath d='M35 20C38.866 20 42 16.866 42 13C42 9.13401 38.866 6 35 6C31.134 6 28 9.13401 28 13C28 16.866 31.134 20 35 20Z' fill='none' stroke='white' stroke-width='4' stroke-linejoin='round'/%3E%3Cpath d='M40 28H30C28.8954 28 28 28.8954 28 30V40C28 41.1046 28.8954 42 30 42H40C41.1046 42 42 41.1046 42 40V30C42 28.8954 41.1046 28 40 28Z' fill='none' stroke='white' stroke-width='4' stroke-linejoin='round'/%3E%3C/svg%3E");
}

.tab-btn:nth-child(2)::before {
    background-image: url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 48 48' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M4 11.9143L24 19L44 11.9143L24 5L4 11.9143Z' fill='none' stroke='%2394A3B8' stroke-width='4' stroke-linejoin='round'/%3E%3Cpath d='M4 20L24 27L44 20' stroke='%2394A3B8' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M4 28L24 35L44 28' stroke='%2394A3B8' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M4 36L24 43L44 36' stroke='%2394A3B8' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
}

.tab-btn:nth-child(2).active::before {
    background-image: url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 48 48' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M4 11.9143L24 19L44 11.9143L24 5L4 11.9143Z' fill='none' stroke='white' stroke-width='4' stroke-linejoin='round'/%3E%3Cpath d='M4 20L24 27L44 20' stroke='white' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M4 28L24 35L44 28' stroke='white' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'/%3E%3Cpath d='M4 36L24 43L44 36' stroke='white' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
}

.create-template-btn {
    padding: 12px 24px;
    background: #3B82F6;
    color: white;
    border: none;
    border-radius: 30px;
    font-size: 15px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.25);
    opacity: 1;
    transform: translateX(0);
    animation: slideInFromRight 0.3s ease-out;
}

.create-template-btn:hover {
    background: #2563EB;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.35);
    transform: translateY(-1px);
}

/* 创建模板按钮的进入动画 */
@keyframes slideInFromRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.create-template-icon {
    width: 20px;
    height: 20px;
    background: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #3B82F6;
    flex-shrink: 0;
}

.template-grid {
    display: grid;
    /* 默认单列布局，用于官方模板 */
    grid-template-columns: 1fr;
    gap: 16px;
    max-width: 100%;
}

/* 我的模板使用两列布局 */
.template-grid.my-templates {
    grid-template-columns: repeat(2, 1fr);
}

/* 模板加载状态 */
.template-grid .loading-placeholder {
    grid-column: 1 / -1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    background: #f8fafc;
    border-radius: 12px;
    border: 2px dashed #e2e8f0;
}

.template-grid .loading-placeholder .loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #E5E7EB;
    border-top: 3px solid #3B82F6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 12px;
}

.template-grid .loading-placeholder p {
    color: #6B7280;
    font-size: 14px;
    margin: 0;
}

/* 模板空状态 */
.template-grid .empty-placeholder {
    grid-column: 1 / -1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    background: #f8fafc;
    border-radius: 12px;
    border: 2px dashed #e2e8f0;
}

.template-grid .empty-placeholder p {
    color: #6B7280;
    font-size: 16px;
    margin: 0;
}

.template-card {
    display: flex;
    align-items: center;
    padding: 20px;
    background: white;
    border: 1px solid #E2E8F0;
    border-radius: 16px;
    transition: all 0.3s ease;
    min-height: 88px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.template-card:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-color: #CBD5E1;
    transform: translateY(-2px);
}

/* 可编辑模板卡片样式 */
.template-card.editable {
    position: relative;
}

.template-card.editable:hover {
    border-color: #3B82F6;
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
}

.template-card.editable:hover .template-name {
    color: #3B82F6;
}

.template-card.editable:hover .edit-hint {
    opacity: 1;
    transform: translateY(0);
}

.template-icon {
    width: 52px;
    height: 52px;
    background: linear-gradient(135deg, #FEF3C7 0%, #FDE68A 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
}

/* 我的模板（自定义模板）的图标使用蓝色 */
.template-card.editable .template-icon {
    background: linear-gradient(135deg, #3B82F6 0%, #2563EB 100%);
}

.icon-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 3px;
}

.icon-dot {
    width: 6px;
    height: 6px;
    background: white;
    border-radius: 1px;
}

.template-info {
    flex: 1;
    min-width: 0;
}

.template-title {
    margin-bottom: 4px;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.template-name-with-badges {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
}

.current-template-badge {
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    color: #0369a1;
    padding: 3px 10px;
    border-radius: 14px;
    font-size: 11px;
    font-weight: 600;
    white-space: nowrap;
    border: 1px solid #7dd3fc;
    box-shadow: 0 1px 3px rgba(3, 105, 161, 0.1);
    position: relative;
    overflow: hidden;
}

.current-template-badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s ease;
}

.current-template-badge:hover::before {
    left: 100%;
}

.template-type {
    font-size: 12px;
    font-weight: 500;
    color: #6366F1;
    letter-spacing: 0;
    background: rgba(99, 102, 241, 0.1);
    border: 1px solid rgba(99, 102, 241, 0.3);
    border-radius: 12px;
    padding: 4px 8px;
    display: inline-block;
    line-height: 1;
    transition: all 0.2s ease;
}

/* 编辑提示样式 */
.edit-hint {
    margin-top: 4px;
    opacity: 0;
    transform: translateY(-5px);
    transition: all 0.3s ease;
}

.edit-hint span {
    font-size: 12px;
    color: #3B82F6;
    font-weight: 500;
}

/* 官方模板样式 */
.template-card .template-type.official {
    color: #059669;
    background: rgba(5, 150, 105, 0.1);
    border: 1px solid rgba(5, 150, 105, 0.3);
}

.template-type:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.template-name {
    font-size: 14px;
    color: #64748B;
    font-weight: 500;
}

.template-meta {
    font-size: 13px;
    color: #94A3B8;
    margin-top: 0;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

/* 当 template-meta 在 template-title 内部时的样式调整 */
.template-title .template-meta {
    margin-top: 4px;
}

.template-description {
    font-size: 12px;
    color: #64748B;
    line-height: 1.4;
    max-height: 2.8em;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.template-actions {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 8px;
}

.usage-info {
    font-size: 12px;
    color: #64748B;
    text-align: right;
    margin-bottom: 4px;
}

.use-template-btn {
    padding: 8px 18px;
    background: linear-gradient(135deg, #FEF3C7 0%, #FDE68A 100%);
    color: #F97316;
    border: none;
    border-radius: 8px;
    font-size: 13px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
    box-shadow: 0 2px 4px rgba(251, 191, 36, 0.2);
}

/* 我的模板（自定义模板）的按钮使用蓝色 */
.template-card.editable .use-template-btn {
    background: linear-gradient(135deg, #3B82F6 0%, #2563EB 100%);
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
    color: white;
}

.use-template-btn:hover {
    background: linear-gradient(135deg, #FDE68A 0%, #FBBF24 100%);
    box-shadow: 0 4px 8px rgba(251, 191, 36, 0.3);
    transform: translateY(-1px);
}

/* 我的模板（自定义模板）按钮的悬停效果使用蓝色 */
.template-card.editable .use-template-btn:hover {
    background: linear-gradient(135deg, #2563EB 0%, #1D4ED8 100%);
    box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
}

.use-template-btn.current,
.template-card.editable .use-template-btn.current,
.template-card:not(.editable) .use-template-btn.current {
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%) !important;
    color: #0369a1 !important;
    border: 1px solid #7dd3fc !important;
    cursor: default;
    box-shadow: 0 2px 4px rgba(3, 105, 161, 0.1) !important;
    position: relative;
    overflow: hidden;
    font-weight: 600;
}

.use-template-btn.current::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(125, 211, 252, 0.1) 0%, rgba(224, 242, 254, 0.1) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.use-template-btn.current:hover,
.template-card.editable .use-template-btn.current:hover,
.template-card:not(.editable) .use-template-btn.current:hover {
    background: linear-gradient(135deg, #e0f2fe 0%, #bae6fd 100%) !important;
    color: #0369a1 !important;
    transform: none !important;
    box-shadow: 0 3px 6px rgba(3, 105, 161, 0.15) !important;
    border-color: #38bdf8 !important;
}

.use-template-btn.current:hover::before {
    opacity: 1;
}

/* 添加一个小图标效果 */
.use-template-btn.current::after {
    content: '✓';
    margin-left: 6px;
    font-size: 12px;
    font-weight: bold;
}

/* 班级管理样式 - 已移至 ClassManagement.css */
/* .class-management-container {
    padding: 24px;
    height: 100%;
    overflow-y: auto;
    display: flex;
    align-items: center;
    justify-content: center;
}

.class-management-content {
    text-align: left;
}

.class-management-content h2 {
    font-size: 24px;
    color: #1F2937;
    margin-bottom: 16px;
}

.class-management-content p {
    font-size: 16px;
    color: #6B7280;
} */

/* 班级任务样式 */
.class-tasks-container {
    padding: 24px;
    height: 100%;
    overflow-y: auto;
}

.class-tasks-content {
    text-align: center;
}

.class-tasks-content h2 {
    font-size: 24px;
    color: #1F2937;
    margin-bottom: 16px;
}

.class-tasks-content p {
    font-size: 16px;
    color: #6B7280;
}

/* 课程管理样式 */
.course-management-container {
    padding: 24px;
    height: 100%;
    overflow-y: auto;
    display: flex;
    align-items: center;
    justify-content: center;
}

.course-management-content {
    text-align: center;
}

.course-management-content h2 {
    font-size: 24px;
    color: #1F2937;
    margin-bottom: 16px;
}

.course-management-content p {
    font-size: 16px;
    color: #6B7280;
}

/* 班级项目样式 */
.class-projects-container {
    padding: 24px;
    height: 100%;
    overflow-y: auto;
    display: flex;
    align-items: center;
    justify-content: center;
}

.class-projects-content {
    text-align: center;
}

.class-projects-content h2 {
    font-size: 24px;
    color: #1F2937;
    margin-bottom: 16px;
}

.class-projects-content p {
    font-size: 16px;
    color: #6B7280;
}

/* 弹窗加载和错误状态样式 */
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    padding: 0 20px 20px 20px;
    text-align: left;
    width: 100%;
}

.loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #E5E7EB;
    border-top: 3px solid #3B82F6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.error-container {
    display: flex;
    flex-direction: column;
    padding: 0 20px 20px 20px;
    text-align: left;
    width: 100%;
}

.error-message {
    color: #EF4444;
    margin-bottom: 16px;
    font-size: 14px;
}

.retry-btn {
    padding: 8px 16px;
    background: #3B82F6;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.retry-btn:hover {
    background: #2563EB;
}

.empty-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    text-align: center;
}

.empty-hint {
    color: #9CA3AF;
    font-size: 12px;
    margin-top: 8px;
}

.school-card-location {
    display: flex;
    align-items: center;
    gap: 4px;
    color: #6B7280;
    font-size: 12px;
    margin-top: 8px;
}

.class-card-info {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    color: #6B7280;
    font-size: 12px;
    margin-top: 8px;
}



/* 加载占位符样式 */
.loading-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100vh;
    width: 100%;
    background: #f8fafc;
}

.loading-placeholder .loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #E5E7EB;
    border-top: 4px solid #3B82F6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
}

.loading-placeholder p {
    color: #6B7280;
    font-size: 16px;
    margin: 0;
}

/* 班级管理页面样式 - 已移至 ClassManagement.css */
/* .class-management-container {
    padding: 0;
    background: #f8faff;
    min-height: 100vh;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
}

.class-management-content {
    max-width: none;
    margin: 0;
    padding: 0;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
}

.class-management-header {
    margin: 0 0 20px 0;
    padding: 20px 0 0 20px;
    text-align: left;
    width: 100%;
}

.class-management-header h2 {
    font-size: 24px;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
    text-align: left;
    line-height: 1.2;
    position: relative;
}

.class-management-header h2::after {
    content: '';
    position: absolute;
    bottom: -6px;
    left: 0;
    width: 48px;
    height: 3px;
    background: linear-gradient(90deg, #3b82f6 0%, #60a5fa 100%);
    border-radius: 2px;
} */

.school-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.school-name {
    font-size: 16px;
    font-weight: 500;
    color: #4a90e2;
}

.school-location {
    font-size: 14px;
    color: #9ca3af;
}

/* 班级网格布局 - 已移至 ClassManagement.css */
/* .classes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 16px;
    margin: 0;
    height: 390px;
    padding:20px 20px 20px 20px;
    justify-content: start;
    align-content: start;
    width: 100%;
    overflow-y: auto;
    overflow-x: hidden;
}

.classes-grid::-webkit-scrollbar {
    width: 8px;
}

.classes-grid::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 4px;
    margin: 10px 0;
}

.classes-grid::-webkit-scrollbar-thumb {
    background: rgba(59, 130, 246, 0.4);
    border-radius: 4px;
    transition: background 0.3s ease;
}

.classes-grid::-webkit-scrollbar-thumb:hover {
    background: rgba(59, 130, 246, 0.6);
} */

/* 班级卡片样式 - 已移至 ClassManagement.css */
/* .class-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border: 1px solid #e5e7eb;
    transition: all 0.3s ease;
    position: relative;
    text-align: center;
    min-height: 80px;
    max-height: 80px;
    height: 80px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.class-card:hover {
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
    transform: translateY(-4px);
}

.class-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.school-tag {
    background: #f3f4f6;
    color: #6b7280;
    padding: 6px 12px;
    border-radius: 8px;
    font-size: 12px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 4px;
}

.school-tag::before {
    content: "🏫";
    font-size: 14px;
}

.settings-icon {
    color: #9ca3af;
    cursor: pointer;
    transition: all 0.2s ease;
    padding: 4px;
    border-radius: 4px;
}

.settings-icon:hover {
    color: #4a90e2;
    background: #f3f4f6;
}

.class-card-content {
    text-align: center;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.class-name {
    font-size: 18px;
    font-weight: 600;
    color: #1a1a1a;
    margin: 0 0 16px 0;
}

.student-count-section {
    background: #f1f5f9;
    border-radius: 10px;
    padding: 16px;
    margin-top: 4px;
}

.student-count-number {
    font-size: 32px;
    font-weight: 700;
    color: #3b82f6;
    line-height: 1;
    margin-bottom: 6px;
}

.student-count-label {
    font-size: 14px;
    color: #64748b;
    font-weight: 500;
} */

/* 学生卡片操作按钮样式 */
.student-actions {
    display: flex;
    gap: 4px;
    opacity: 0;
    visibility: hidden;
    transition: all 0.2s ease;
    margin-left: auto;
    margin-right: 8px;
}

.student-item:hover .student-actions {
    opacity: 1;
    visibility: visible;
}

.action-btn {
    width: 28px;
    height: 28px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    transition: all 0.2s ease;
    background: #f1f5f9;
    color: #64748b;
}

.action-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.assign-blocks-btn:hover {
    background: #dbeafe;
    color: #3b82f6;
}

.assign-points-btn:hover {
    background: #fef3c7;
    color: #f59e0b;
}

/* 助教标识和空状态样式 - 已移至 ClassManagement.css */
/* .assistant-badge {
    position: absolute;
    top: 12px;
    right: 12px;
    background: #fef3c7;
    color: #d97706;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 600;
}

.no-school-selected,
.no-classes {
    text-align: center;
    padding: 60px 20px;
    color: #6b7280;
}

.no-school-icon,
.no-classes-icon {
    color: #d1d5db;
    margin-bottom: 16px;
}

.no-school-selected h3,
.no-classes h3 {
    font-size: 20px;
    font-weight: 600;
    color: #374151;
    margin: 0 0 8px 0;
}

.no-school-selected p,
.no-classes p {
    font-size: 14px;
    color: #6b7280;
    margin: 0;
} */

