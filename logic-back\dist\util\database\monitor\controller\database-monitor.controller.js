"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseMonitorController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const not_login_decorator_1 = require("../../../../web/router_guard/not-login.decorator");
const query_interceptor_1 = require("../interceptor/query-interceptor");
const query_monitor_service_1 = require("../service/query-monitor.service");
let DatabaseMonitorController = class DatabaseMonitorController {
    queryMonitorService;
    queryInterceptor;
    constructor(queryMonitorService, queryInterceptor) {
        this.queryMonitorService = queryMonitorService;
        this.queryInterceptor = queryInterceptor;
    }
    async getSlowQueries(limit) {
        try {
            const records = this.queryMonitorService.getSlowQueryRecords(limit);
            return {
                code: 200,
                message: '获取慢查询记录成功',
                data: records
            };
        }
        catch (error) {
            return {
                code: 500,
                message: `获取慢查询记录失败: ${error.message}`,
                data: null
            };
        }
    }
    async getQueryMetrics() {
        try {
            const metrics = this.queryMonitorService.getQueryMetrics();
            const activeQueries = this.queryInterceptor.getActiveQueryCount();
            return {
                code: 200,
                message: '获取查询指标成功',
                data: {
                    ...metrics,
                    activeQueries
                }
            };
        }
        catch (error) {
            return {
                code: 500,
                message: `获取查询指标失败: ${error.message}`,
                data: null
            };
        }
    }
    async getActiveQueries() {
        const activeQueries = this.queryInterceptor.getActiveQueries();
        return {
            code: 200,
            message: '获取活跃查询成功',
            data: activeQueries
        };
    }
    async getConfig() {
        try {
            const config = this.queryMonitorService.getConfig();
            return {
                code: 200,
                message: '获取配置成功',
                data: config
            };
        }
        catch (error) {
            return {
                code: 500,
                message: `获取配置失败: ${error.message}`,
                data: null
            };
        }
    }
    async updateConfig(config) {
        try {
            this.queryMonitorService.updateConfig(config);
            const newConfig = this.queryMonitorService.getConfig();
            return {
                code: 200,
                message: '配置更新成功',
                data: newConfig
            };
        }
        catch (error) {
            return {
                code: 500,
                message: `配置更新失败: ${error.message}`,
                data: null
            };
        }
    }
    async resetMetrics() {
        this.queryMonitorService.resetMetrics();
        return {
            code: 200,
            message: '查询指标已重置',
            data: null
        };
    }
    async clearSlowQueries() {
        this.queryMonitorService.clearSlowQueryRecords();
        return {
            code: 200,
            message: '慢查询记录已清空',
            data: null
        };
    }
    async cleanupTimeoutQueries(body) {
        this.queryInterceptor.cleanupTimeoutQueries(body.timeoutMs);
        return {
            code: 200,
            message: '超时查询清理完成',
            data: null
        };
    }
    async enableMonitoring() {
        try {
            this.queryMonitorService.updateConfig({ enableDatabaseMonitoring: true });
            return {
                code: 200,
                message: '数据库监控已启用',
                data: this.queryMonitorService.getConfig()
            };
        }
        catch (error) {
            return {
                code: 500,
                message: `启用监控失败: ${error.message}`,
                data: null
            };
        }
    }
    async disableMonitoring() {
        try {
            this.queryMonitorService.updateConfig({ enableDatabaseMonitoring: false });
            return {
                code: 200,
                message: '数据库监控已禁用',
                data: this.queryMonitorService.getConfig()
            };
        }
        catch (error) {
            return {
                code: 500,
                message: `禁用监控失败: ${error.message}`,
                data: null
            };
        }
    }
    async toggleLightweightMode(body) {
        try {
            this.queryMonitorService.updateConfig({ lightweightMode: body.enabled });
            return {
                code: 200,
                message: `轻量级模式已${body.enabled ? '启用' : '禁用'}`,
                data: this.queryMonitorService.getConfig()
            };
        }
        catch (error) {
            return {
                code: 500,
                message: `切换轻量级模式失败: ${error.message}`,
                data: null
            };
        }
    }
    async healthCheck() {
        try {
            const metrics = this.queryMonitorService.getQueryMetrics();
            const activeQueries = this.queryInterceptor.getActiveQueryCount();
            const config = this.queryMonitorService.getConfig();
            return {
                code: 200,
                message: '数据库监控运行正常',
                data: {
                    status: config.enableDatabaseMonitoring ? 'enabled' : 'disabled',
                    timestamp: new Date().toISOString(),
                    metrics,
                    activeQueries,
                    config
                }
            };
        }
        catch (error) {
            return {
                code: 500,
                message: `健康检查失败: ${error.message}`,
                data: {
                    status: 'error',
                    timestamp: new Date().toISOString(),
                    metrics: null,
                    activeQueries: 0,
                    config: null
                }
            };
        }
    }
};
exports.DatabaseMonitorController = DatabaseMonitorController;
__decorate([
    (0, common_1.Get)('slow-queries'),
    (0, swagger_1.ApiOperation)({ summary: '获取慢查询记录' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, description: '返回记录数量限制' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __param(0, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], DatabaseMonitorController.prototype, "getSlowQueries", null);
__decorate([
    (0, common_1.Get)('metrics'),
    (0, swagger_1.ApiOperation)({ summary: '获取查询性能指标' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], DatabaseMonitorController.prototype, "getQueryMetrics", null);
__decorate([
    (0, common_1.Get)('active-queries'),
    (0, swagger_1.ApiOperation)({ summary: '获取当前活跃查询' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], DatabaseMonitorController.prototype, "getActiveQueries", null);
__decorate([
    (0, common_1.Get)('config'),
    (0, swagger_1.ApiOperation)({ summary: '获取监控配置' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], DatabaseMonitorController.prototype, "getConfig", null);
__decorate([
    (0, common_1.Post)('config'),
    (0, swagger_1.ApiOperation)({ summary: '更新监控配置' }),
    (0, swagger_1.ApiBody)({
        description: '监控配置',
        schema: {
            type: 'object',
            properties: {
                enableDatabaseMonitoring: { type: 'boolean', description: '启用数据库监控' },
                lightweightMode: { type: 'boolean', description: '轻量级模式' },
                slowQueryThreshold: { type: 'number', description: '慢查询阈值(ms)' },
                enableSlowQueryLogging: { type: 'boolean', description: '启用慢查询日志' },
                enableQueryMetrics: { type: 'boolean', description: '启用查询指标' },
                enableStackTrace: { type: 'boolean', description: '启用调用栈捕获' },
                samplingRate: { type: 'number', description: '采样率(1-100)' },
                asyncSlowQueryProcessing: { type: 'boolean', description: '异步处理慢查询' },
                maxSlowQueryRecords: { type: 'number', description: '最大慢查询记录数' }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], DatabaseMonitorController.prototype, "updateConfig", null);
__decorate([
    (0, common_1.Post)('reset-metrics'),
    (0, swagger_1.ApiOperation)({ summary: '重置查询指标' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '重置成功' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], DatabaseMonitorController.prototype, "resetMetrics", null);
__decorate([
    (0, common_1.Post)('clear-slow-queries'),
    (0, swagger_1.ApiOperation)({ summary: '清空慢查询记录' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '清空成功' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], DatabaseMonitorController.prototype, "clearSlowQueries", null);
__decorate([
    (0, common_1.Post)('cleanup-timeout-queries'),
    (0, swagger_1.ApiOperation)({ summary: '清理超时查询' }),
    (0, swagger_1.ApiBody)({
        description: '超时时间配置',
        schema: {
            type: 'object',
            properties: {
                timeoutMs: { type: 'number', description: '超时时间(ms)', default: 300000 }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '清理成功' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], DatabaseMonitorController.prototype, "cleanupTimeoutQueries", null);
__decorate([
    (0, common_1.Post)('enable'),
    (0, swagger_1.ApiOperation)({ summary: '启用数据库监控' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '启用成功' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], DatabaseMonitorController.prototype, "enableMonitoring", null);
__decorate([
    (0, common_1.Post)('disable'),
    (0, swagger_1.ApiOperation)({ summary: '禁用数据库监控' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '禁用成功' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], DatabaseMonitorController.prototype, "disableMonitoring", null);
__decorate([
    (0, common_1.Post)('lightweight-mode'),
    (0, swagger_1.ApiOperation)({ summary: '切换轻量级模式' }),
    (0, swagger_1.ApiBody)({
        description: '轻量级模式配置',
        schema: {
            type: 'object',
            properties: {
                enabled: { type: 'boolean', description: '是否启用轻量级模式' }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '切换成功' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], DatabaseMonitorController.prototype, "toggleLightweightMode", null);
__decorate([
    (0, common_1.Get)('health'),
    (0, swagger_1.ApiOperation)({ summary: '数据库监控健康检查' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '健康检查成功' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], DatabaseMonitorController.prototype, "healthCheck", null);
exports.DatabaseMonitorController = DatabaseMonitorController = __decorate([
    (0, swagger_1.ApiTags)('数据库监控'),
    (0, common_1.Controller)('api/v1/database-monitor'),
    (0, not_login_decorator_1.NotLogin)(),
    __metadata("design:paramtypes", [query_monitor_service_1.QueryMonitorService,
        query_interceptor_1.QueryInterceptor])
], DatabaseMonitorController);
//# sourceMappingURL=database-monitor.controller.js.map