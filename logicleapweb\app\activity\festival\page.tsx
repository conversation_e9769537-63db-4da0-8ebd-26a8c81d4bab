'use client'

import React, { useEffect, useState, useMemo, Suspense } from 'react'
import ActivityHeader from './components/ActivityHeader'
import ActivityDetail from './components/ActivityDetail'
import WorksShowcase from './components/WorksShowcase'
import WorksSelector from './components/WorksSelector'
import WorkDetail from './components/WorkDetail'
import ActivityImporter from './components/ActivityImporter'
import EditorJsonRenderer from './components/EditorJsonRenderer'
import ActivityNavbar from './components/ActivityNavbar'
import BackToTop from './components/BackToTop'
import { worksApi } from '@/lib/api/works'
import { activityApi } from '@/lib/api/activity' // 导入活动API
import { documentApi } from '@/lib/api/document' // 导入文档API
import { useSearchParams, useRouter } from 'next/navigation' // 导入useSearchParams和useRouter
import { message, Spin, Alert } from 'antd' // 导入Spin和Alert组件
import { FileJson, Info, Edit } from 'lucide-react'
import { selectUserState } from '@/lib/store'
import { useSelector } from 'react-redux'
import ActivitySubmitModal from './components/ActivitySubmitModal'
import { GetNotification } from 'logic-common/dist/components/Notification'

// 定义作品类型接口
interface Work {
  id: string
  title: string
  author: string
  authorAvatarUrl?: string; // 新增作者头像URL字段
  coverImage: string
  likes: number
  description: string
  category?: string // 添加分类属性
  viewCount?: number // 添加浏览次数
  liked?: boolean // 添加是否已点赞属性
  userId?: number // 添加用户ID
  content?: string // 添加作品内容
}

interface ApiWork {
  id: number
  title?: string
  description?: string
  coverImage?: string
  viewCount?: number
  likeCount?: number
  author?: {
    nickName?: string
    avatarUrl?: string
  }
}

// 定义活动数据结构
interface ActivityDataType {
  id: string;
  title: string;
  startTime: string;
  endTime: string;
  bannerImage: string;
  organizer: string;
  // 各标签页内容
  detailContent: string;
  detailEditorJson?: any;
  rulesContent: string;
  rulesEditorJson?: any;
  awardsContent: string;
  awardsEditorJson?: any;
  // 各标签页文档ID - 使用string类型以匹配ActivityDetail组件接口
  detailDocId?: string;
  rulesDocId?: string;
  awardsDocId?: string;
  // 添加标签字段
  tags?: Array<{ id: number, name: string, color?: string }>;
  // 添加图片设置，可能是字符串或对象
  imageSettings?: string | { position: { x: number, y: number }, scale: number };
  activityType?: number; // 活动类型，使用数字类型
  // 新增媒体字段
  promotionImage?: string;
  backgroundImage?: string;
  galleryImages?: string;
  attachmentFiles?: string;
}

// 创建一个客户端组件来处理URL参数
function FestivalPageContent() {
  // 获取URL参数中的活动ID
  const searchParams = useSearchParams();
  // searchParams 在 Next.js 中不会为 null，但是要确保类型安全
  const activityIdParam = searchParams ? searchParams.get('id') : null;

  // 添加客户端检测来解决水合错误
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  const [loaded, setLoaded] = useState(false);
  const [detailExpanded, setDetailExpanded] = useState(false);
  const [showcaseWorks, setShowcaseWorks] = useState<Work[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // 添加活动数据加载状态
  const [isLoadingActivity, setIsLoadingActivity] = useState(false);
  const [errorLoadingActivity, setErrorLoadingActivity] = useState<string | null>(null);

  // 添加WorksSelector需要的状态
  const [isWorkSelectorOpen, setIsWorkSelectorOpen] = useState(false);

  // 添加当前选择的过滤器状态
  const [currentFilter, setCurrentFilter] = useState('all');

  // 获取当前过滤器下的作品
  const getCurrentFilterWorks = () => {
    if (currentFilter === 'awarded') {
      return showcaseWorks.filter(work => work.category === 'awarded');
    } else if (currentFilter === 'all') {
      return showcaseWorks.filter(work => !work.category || work.category === 'all');
    }
    return showcaseWorks;
  };

  // 添加ActivityImporter需要的状态
  const [isActivityImporterOpen, setIsActivityImporterOpen] = useState(false);

  // 添加当前活动标签状态
  const [activeTab, setActiveTab] = useState<'detail' | 'rules' | 'awards'>('detail');

  // 活动数据状态 - 修改为可为null
  const [activityData, setActivityData] = useState<ActivityDataType | null>(null);

  // 添加作品详情模态框相关状态
  const [selectedWork, setSelectedWork] = useState<Work | null>(null)
  const [isWorkDetailOpen, setIsWorkDetailOpen] = useState(false)

  // 获取当前用户信息
  const userState = useSelector(selectUserState)
  const isAdmin = userState?.roleId === 4

  // 添加ActivitySubmitModal相关的状态
  const [showSubmitModal, setShowSubmitModal] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  // 在客户端渲染完成后加载活动数据
  useEffect(() => {
    if (isClient && activityIdParam) {
      fetchActivityData();
    } else if (isClient) {
      setLoaded(true);
    }
  }, [isClient, activityIdParam]);

  // 加载活动数据的函数
  const fetchActivityData = async () => {
    // 重置状态
    setIsLoadingActivity(true);
    setErrorLoadingActivity(null);
    setActivityData(null);

    if (!activityIdParam) {
      setErrorLoadingActivity('活动ID未在URL中指定');
      setIsLoadingActivity(false);
      setLoaded(true); // 标记页面已加载以显示错误
      return;
    }

    // 将字符串ID转换为数字
    const activityIdNumber = parseInt(activityIdParam, 10);
    if (isNaN(activityIdNumber)) {
      setErrorLoadingActivity(`无效的活动ID: "${activityIdParam}"`);
      setIsLoadingActivity(false);
      setLoaded(true);
      return;
    }

    try {
      // 调用API获取活动详情
      const response = await activityApi.getDetail(activityIdNumber);
      console.log('获取活动详情API响应:', response);

      // 获取标签信息
      let tags: Array<{ id: number, name: string, color?: string }> = [];

      if (response && response.data) {
        // 处理新的API响应结构
        const apiData = response.data.data || response.data;

        // 将API返回的数据映射到组件状态
        const fetchedActivityData: ActivityDataType = {
          id: String(apiData.id || activityIdNumber),
          title: apiData.name || '未命名活动', // API返回name，映射到title
          startTime: apiData.startTime || '',
          endTime: apiData.endTime || '',
          bannerImage: apiData.coverImage || '/images/festival/banner.jpg', // API返回coverImage，映射到bannerImage
          organizer: apiData.organizer || '',
          detailContent: apiData.detailContent || '',
          detailEditorJson: apiData.detailEditorJson || null,
          rulesContent: apiData.rulesContent || '',
          rulesEditorJson: apiData.rulesEditorJson || null,
          awardsContent: apiData.awardsContent || '',
          awardsEditorJson: apiData.awardsEditorJson || null,
          // 保存文档ID
          detailDocId: apiData.detailDocId ? String(apiData.detailDocId) : undefined,
          rulesDocId: apiData.rulesDocId ? String(apiData.rulesDocId) : undefined,
          awardsDocId: apiData.awardsDocId ? String(apiData.awardsDocId) : undefined,
          // 添加标签数据
          tags: tags,

          activityType: apiData.activityType || 0,
          // 添加媒体字段
          promotionImage: apiData.promotionImage,
          backgroundImage: apiData.backgroundImage,
          galleryImages: apiData.galleryImages,
          attachmentFiles: apiData.attachmentFiles
        };

        setActivityData(fetchedActivityData);
        setLoaded(true); // 数据加载完成后显示页面

        // // 获取各标签页文档内容
        // if (apiData.detailDocId) {
        //   await fetchDocumentContent(apiData.detailDocId, 'detail');
        // }

        // if (apiData.rulesDocId) {
        //   await fetchDocumentContent(apiData.rulesDocId, 'rules');
        // }

        // if (apiData.awardsDocId) {
        //   await fetchDocumentContent(apiData.awardsDocId, 'awards');
        // }

        // 获取相关作品
        // fetchShowcaseWorks();
      }
    } catch (error) {
      console.error('获取活动详情失败:', error);
      const notification = GetNotification();
      notification.error('获取活动详情失败，请稍后重试');
    } finally {
      setIsLoadingActivity(false);
    }
  };

  // // 新增: 获取文档内容的函数
  // const fetchDocumentContent = async (docId: number, tabType: 'detail' | 'rules' | 'awards') => {
  //   if (!docId) return;

  //   try {
  //     console.log(`开始获取${tabType}标签页文档内容，ID:`, docId);

  //     // 调用API获取文档详情
  //     const docResponse = await documentApi.getById(docId);
  //     console.log('文档API响应:', docResponse);

  //     if (docResponse && docResponse.data && docResponse.data.data && docResponse.data.data.content) {
  //       const contentUrl = docResponse.data.data.content;
  //       console.log(`获取到${tabType}文档内容URL:`, contentUrl);

  //       // 如果content是URL，则获取URL内容
  //       if (contentUrl.startsWith('http')) {
  //         try {
  //           const jsonResponse = await fetch(contentUrl);
  //           const jsonData = await jsonResponse.json();
  //           console.log(`成功获取${tabType}文档JSON内容:`, jsonData);

  //           // 根据标签类型更新对应的editorJson
  //           setActivityData(prev => {
  //             if (!prev) return null;

  //             const newState = { ...prev };
  //             switch (tabType) {
  //               case 'detail':
  //                 newState.detailEditorJson = jsonData;
  //                 break;
  //               case 'rules':
  //                 newState.rulesEditorJson = jsonData;
  //                 break;
  //               case 'awards':
  //                 newState.awardsEditorJson = jsonData;
  //                 break;
  //             }
  //             return newState;
  //           });
  //         } catch (fetchError) {
  //           console.error(`获取${tabType}文档JSON内容失败:`, fetchError);
  //         }
  //       } else {
  //         // 如果content不是URL而是直接的JSON字符串，尝试解析
  //         try {
  //           const jsonData = JSON.parse(contentUrl);
  //           console.log(`成功解析${tabType}文档JSON内容:`, jsonData);

  //           setActivityData(prev => {
  //             if (!prev) return null;

  //             const newState = { ...prev };
  //             switch (tabType) {
  //               case 'detail':
  //                 newState.detailEditorJson = jsonData;
  //                 break;
  //               case 'rules':
  //                 newState.rulesEditorJson = jsonData;
  //                 break;
  //               case 'awards':
  //                 newState.awardsEditorJson = jsonData;
  //                 break;
  //             }
  //             return newState;
  //           });
  //         } catch (parseError) {
  //           console.error(`解析${tabType}文档内容失败:`, parseError);
  //         }
  //       }
  //     } else {
  //       console.warn(`文档API返回的数据中没有content字段，响应:`, docResponse);
  //     }
  //   } catch (error) {
  //     console.error(`获取${tabType}文档内容失败:`, error);
  //   }
  // };

  useEffect(() => {
    // 添加页面加载动画
    setLoaded(true);

    // 添加背景动画样式
    const styleElement = document.createElement('style');
    styleElement.innerHTML = `
      @keyframes gradientBG {
        0% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
        100% { background-position: 0% 50%; }
      }
      
      .bg-gradient-animate {
        background: linear-gradient(-45deg, #f9fafb, #eff6ff, #eef2ff, #f5f3ff);
        background-size: 400% 400%;
        animation: gradientBG 15s ease infinite;
      }
      
      @keyframes fadeInUp {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
      }
      
      .animate-fadeInUp {
        animation: fadeInUp 0.8s ease-out forwards;
      }
      
      .animation-delay-200 { animation-delay: 0.2s; }
      .animation-delay-400 { animation-delay: 0.4s; }
      .animation-delay-600 { animation-delay: 0.6s; }
      
      /* 动态圆形装饰 */
      .floating-circle {
        position: absolute;
        border-radius: 50%;
        background: radial-gradient(circle at center, rgba(var(--color-start), 0.5), rgba(var(--color-end), 0.1));
        filter: blur(10px);
        z-index: 0;
      }
      
      .circle1 {
        --color-start: 99, 102, 241;
        --color-end: 79, 70, 229;
        width: 200px;
        height: 200px;
        top: 5%;
        right: -80px;
        animation: float1 25s infinite alternate ease-in-out;
      }
      
      .circle2 {
        --color-start: 59, 130, 246;
        --color-end: 37, 99, 235;
        width: 300px;
        height: 300px;
        bottom: 10%;
        left: -100px;
        animation: float2 20s infinite alternate-reverse ease-in-out;
      }
      
      .circle3 {
        --color-start: 139, 92, 246;
        --color-end: 124, 58, 237;
        width: 150px;
        height: 150px;
        bottom: 30%;
        right: 10%;
        animation: float3 15s infinite alternate ease-in-out;
      }
      
      @keyframes float1 {
        0% { transform: translate(0, 0); }
        100% { transform: translate(-40px, 40px); }
      }
      
      @keyframes float2 {
        0% { transform: translate(0, 0); }
        100% { transform: translate(50px, -30px); }
      }
      
      @keyframes float3 {
        0% { transform: translate(0, 0); }
        100% { transform: translate(-30px, -40px); }
      }
      
      .loading-spinner {
        display: inline-block;
        width: 20px;
        height: 20px;
        border: 2px solid rgba(79, 70, 229, 0.3);
        border-radius: 50%;
        border-top-color: #4f46e5;
        animation: spin 1s ease-in-out infinite;
      }
      
      @keyframes spin {
        to { transform: rotate(360deg); }
      }
    `;
    document.head.appendChild(styleElement);

    return () => {
      document.head.removeChild(styleElement);
    };
  }, []);

  // 监听showcaseWorks变化
  useEffect(() => {
    console.log('页面状态 - 展示作品数量:', showcaseWorks.length);
  }, [showcaseWorks]);

  // // 获取参赛作品的方法
  // const fetchShowcaseWorks = async () => {
  //   if (!activityIdParam) {
  //     return;
  //   }

  //   try {
  //     setIsLoading(true);
  //     const activityId = parseInt(activityIdParam, 10);

  //     console.log('开始获取参赛作品，活动ID:', activityId);

  //     // 使用活动API获取该活动下的所有参赛作品
  //     const response = await activityApi.getActivityWorks(activityId);
  //     if (response?.status === 200) {
  //       // 处理新的API响应结构
  //       const apiWorks = response.data.data || response.data || [];
  //       console.log('从API获取到的作品:', apiWorks);

  //       // 过滤掉status=0的作品（取消报名的作品）
  //       const filteredWorks = apiWorks.filter((activityWork: any) => {
  //         return activityWork.status > 0; // 只保留activityWork.status>0的作品
  //       });

  //       console.log('过滤后的作品数量:', filteredWorks.length);

  //       // 将API返回的作品转换为组件需要的格式
  //       const formattedWorks = filteredWorks.map((activityWork: any) => {
  //         const work = activityWork.work || {};
  //         // 分类判断：当isAwarded为true时，设置为awarded，否则为all
  //         const category = activityWork.isAwarded ? 'awarded' : 'all';

  //         // 判断是否为图片类型
  //         const isImage = activityWork.contentType === 2 || work.type === 'image';

  //         return {
  //           id: String(work.id || activityWork.workId),
  //           title: work.title || '未命名作品',
  //           author: work.author?.nickName || '未知作者',
  //           authorAvatarUrl: work.author?.avatarUrl || '/images/avatar-placeholder.png', // 提取作者头像，提供默认头像
  //           coverImage: work.coverImage || work.url || '/images/festival/fallback.jpg',
  //           likes: work.likeCount || 0,
  //           description: work.description || work.prompt || '暂无描述',
  //           category: category,
  //           userId: work.author?.id || work.userId,
  //           content: work.content || '',
  //           type: isImage ? 'image' : (work.type || 1), // 设置type为'image'或原始类型
  //           url: isImage ? (work.url || work.backupImagePath || work.originalImagePath || work.coverImage) : undefined // 添加用于下载的URL
  //         };
  //       });

  //       console.log('格式化后的作品列表:', formattedWorks);

  //       setShowcaseWorks(formattedWorks);

  //       if (formattedWorks.length === 0) {
  //         const notification = GetNotification();
  //         notification.info('此活动暂无参赛作品');
  //       }
  //     } else {
  //       // 如果API获取失败，显示空状态
  //       console.error('API返回失败:', response);
  //       setShowcaseWorks([]);
  //       const notification = GetNotification();
  //       notification.error('获取参赛作品失败');
  //     }
  //   } catch (error) {
  //     console.error('获取参赛作品失败:', error);
  //     setShowcaseWorks([]);
  //     const notification = GetNotification();
  //     notification.error('获取参赛作品失败');
  //   } finally {
  //     setIsLoading(false);
  //   }
  // };

  // 时长转换辅助函数
  const convertDurationToTime = (seconds: number): string => {
    // 在服务端渲染时，返回默认值，避免水合错误
    if (typeof window === 'undefined') {
      return '00:00';
    }

    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${String(mins).padStart(2, '0')}:${String(secs).padStart(2, '0')}`;
  };

  // 处理选择作品的保存
  const handleSaveSelectedWorks = (selectedWorks: Work[]) => {
    console.log('选择的作品:', selectedWorks);

    if (currentFilter === 'all' || currentFilter === 'awarded') {
      // 为选择的作品添加当前分类标签
      const updatedWorks = selectedWorks.map(work => ({
        ...work,
        category: currentFilter
      }));

      // 查找其他分类的作品 (保留不是当前分类的作品)
      const otherCategoryWorks = showcaseWorks.filter(work =>
        work.category !== currentFilter
      );

      // 合并其他分类作品和当前分类新选择的作品
      setShowcaseWorks([...otherCategoryWorks, ...updatedWorks]);
    } else {
      setShowcaseWorks(selectedWorks);
    }

    setIsWorkSelectorOpen(false);

    // 显示简单的操作成功提示
    const notification = GetNotification();
    notification.success('已更新作品显示设置');
  };

  // 处理取消选择
  const handleCancelSelection = () => {
    setIsWorkSelectorOpen(false);
  };

  // 处理作品列表变化的回调
  const handleWorksChange = (newWorks: Work[]) => {
    setShowcaseWorks(newWorks);
  };

  // 处理活动详情的导入 - 更新对应标签页的内容
  const handleImportActivity = (importedData: any) => {
    const targetTab = importedData.targetTab || 'detail';
    const isEditorJson = importedData.editorJson;

    if (!activityData) {
      const notification = GetNotification();
      notification.error('无法导入内容：活动数据不存在');
      return;
    }

    setActivityData((prev: ActivityDataType | null) => {
      if (!prev) return null; // 如果之前没有数据，返回null

      const newState = { ...prev };

      // 根据导入格式填充对应字段
      if (isEditorJson) {
        // 编辑器JSON格式
        if (targetTab === 'detail') {
          newState.detailContent = ''; // Clear HTML content
          newState.detailEditorJson = importedData.editorJson;
          newState.title = importedData.title || prev.title;
        } else if (targetTab === 'rules') {
          newState.rulesContent = ''; // Clear HTML content
          newState.rulesEditorJson = importedData.editorJson;
        } else if (targetTab === 'awards') {
          newState.awardsContent = ''; // Clear HTML content
          newState.awardsEditorJson = importedData.editorJson;
        }
      } else {
        // 传统HTML/简单JSON格式
        if (targetTab === 'detail') {
          newState.detailEditorJson = null; // Clear editor JSON
          newState.detailContent = importedData.detailContent || '';
          // Update other metadata only when importing for 'detail' tab with traditional format
          newState.title = importedData.title || prev.title;
          newState.startTime = importedData.startTime || prev.startTime;
          newState.endTime = importedData.endTime || prev.endTime;
          newState.bannerImage = importedData.bannerImage || prev.bannerImage;
          newState.organizer = importedData.organizer || prev.organizer;
        } else if (targetTab === 'rules') {
          newState.rulesEditorJson = null; // Clear editor JSON
          // Look for specific key first, then fallback
          if (importedData.rulesContent !== undefined) {
            newState.rulesContent = importedData.rulesContent;
          } else {
            newState.rulesContent = importedData.detailContent || '';
            console.warn("Importing to 'rules' tab using 'detailContent' field. Consider using 'rulesContent' field in your JSON.");
          }
        } else if (targetTab === 'awards') {
          newState.awardsEditorJson = null; // Clear editor JSON
          // Look for specific key first, then fallback
          if (importedData.awardsContent !== undefined) {
            newState.awardsContent = importedData.awardsContent;
          } else {
            newState.awardsContent = importedData.detailContent || '';
            console.warn("Importing to 'awards' tab using 'detailContent' field. Consider using 'awardsContent' field in your JSON.");
          }
        }
      }
      return newState;
    });

    const notification = GetNotification();
    // notification.success(`${getTabName(targetTab)}已导入`);

    // 导入后切换到对应标签
    setActiveTab(targetTab);
  };

  // // 获取标签页名称
  // const getTabName = (tab: 'detail' | 'rules' | 'awards'): string => {
  //   switch (tab) {
  //     case 'detail': return '活动详情';
  //     case 'rules': return '参赛规则';
  //     case 'awards': return '奖项设置';
  //     default: return '活动详情';
  //   }
  // };

  // 现在完成编辑功能，添加处理文档ID更换的函数

  const handleDocIdChange = async (type: 'detail' | 'rules' | 'awards', docId: string) => {
    if (!activityData || !activityData.id) return;

    try {
      const updateData: any = {
        id: Number(activityData.id) // 确保活动ID是数字类型
      };

      // 根据类型设置对应的docId字段，并确保转换为数字类型
      if (type === 'detail') {
        updateData.detailDocId = Number(docId);
      } else if (type === 'rules') {
        updateData.rulesDocId = Number(docId);
      } else if (type === 'awards') {
        updateData.awardsDocId = Number(docId);
      }

      console.log('更新活动文档ID:', updateData);

      // 调用API更新文档ID
      const response = await activityApi.update(updateData);

      if (response?.status === 200) {
        const notification = GetNotification();
        notification.success('文档更新成功');
        // 重新加载活动数据
        if (activityIdParam) {
          setIsLoadingActivity(true);
          // 直接重新获取活动详情，不调用fetchActivityDetails
          const activityIdNumber = parseInt(activityIdParam, 10);
          if (!isNaN(activityIdNumber)) {
            try {
              // 调用API获取活动详情
              const response = await activityApi.getDetail(activityIdNumber);

              if (response && response.data) {
                // 处理新的API响应结构
                const apiData = response.data.data || response.data;

                // 将API返回的数据映射到组件状态
                const fetchedActivityData: ActivityDataType = {
                  id: String(apiData.id || activityIdNumber),
                  title: apiData.name || '未命名活动',
                  startTime: apiData.startTime || '',
                  endTime: apiData.endTime || '',
                  bannerImage: apiData.coverImage || '/images/festival/banner.jpg',
                  organizer: apiData.organizer || '',
                  detailContent: apiData.detailContent || '',
                  detailEditorJson: apiData.detailEditorJson || null,
                  rulesContent: apiData.rulesContent || '',
                  rulesEditorJson: apiData.rulesEditorJson || null,
                  awardsContent: apiData.awardsContent || '',
                  awardsEditorJson: apiData.awardsEditorJson || null,
                  // 保存文档ID (转换为字符串类型)
                  detailDocId: apiData.detailDocId ? String(apiData.detailDocId) : undefined,
                  rulesDocId: apiData.rulesDocId ? String(apiData.rulesDocId) : undefined,
                  awardsDocId: apiData.awardsDocId ? String(apiData.awardsDocId) : undefined,
                  // 添加标签数据
                  tags: [],

                  activityType: apiData.activityType || 0
                };

                setActivityData(fetchedActivityData);
                setLoaded(true);

                // // 获取各标签页文档内容
                // if (apiData.detailDocId) {
                //   await fetchDocumentContent(apiData.detailDocId, 'detail');
                // }

                // if (apiData.rulesDocId) {
                //   await fetchDocumentContent(apiData.rulesDocId, 'rules');
                // }

                // if (apiData.awardsDocId) {
                //   await fetchDocumentContent(apiData.awardsDocId, 'awards');
                // }

                // 获取相关作品
                // fetchShowcaseWorks();
              }
            } catch (error) {
              console.error('重新加载活动失败:', error);
              const notification = GetNotification();
              notification.error('重新加载活动失败');
            } finally {
              setIsLoadingActivity(false);
            }
          }
        }
      } else {
        const notification = GetNotification();
        notification.error(response?.data?.message || '更新失败');
      }
    } catch (error) {
      console.error('更新文档ID失败:', error);
      const notification = GetNotification();
      notification.error('更新文档ID失败，请稍后重试');
    }
  };

  // 添加处理作品点击的函数
  const handleOpenWorkDetail = (work: Work) => {
    console.log('Opening work detail:', work)
    // 尝试从work.id中提取数字部分作为userId（如果没有userId字段）
    if (!work.userId && work.id) {
      const workId = parseInt(work.id)
      if (!isNaN(workId)) {
        work.userId = workId // 临时使用workId作为userId
      }
    }
    setSelectedWork(work)
    setIsWorkDetailOpen(true)
  }

  const handleCloseWorkDetail = () => {
    setIsWorkDetailOpen(false)
    setSelectedWork(null)
  }

  // 处理作品点赞
  const handleLikeWork = async (workId: string) => {
    console.log('liking');

    if (!userState?.isLoggedIn) {
      const notification = GetNotification();
      notification.warning('请先登录再点赞');
      return;
    }

    try {
      const numericWorkId = parseInt(workId)
      if (isNaN(numericWorkId)) {
        const notification = GetNotification();
        notification.error('作品ID无效');
        return;
      }

      const response = await worksApi.toggleLike(numericWorkId, 1) // 1表示作品类型

      if (response?.data?.data) {
        const isLiked = response.data.data.action === 'like'

        // 更新作品列表中的点赞状态
        const updatedWorks = showcaseWorks.map(work => {
          if (work.id === workId) {
            return {
              ...work,
              likes: work.likes + (isLiked ? 1 : -1),
              liked: isLiked
            }
          }
          return work
        })

        setShowcaseWorks(updatedWorks)

        // 如果当前有选中的作品，也更新它的点赞状态
        if (selectedWork && selectedWork.id === workId) {
          setSelectedWork({
            ...selectedWork,
            likes: selectedWork.likes + (isLiked ? 1 : -1),
            liked: isLiked
          })
        }

        const notification = GetNotification();
        notification.success(isLiked ? '点赞成功' : '取消点赞成功');
      }
    } catch (error) {
      console.error('点赞操作失败:', error);
      const notification = GetNotification();
      notification.error('点赞操作失败，请稍后重试');
    }
  }

  // 处理报名按钮点击
  const handleSubmitButtonClick = () => {
    // 如果未登录，先显示登录对话框
    if (!userState?.isLoggedIn) {
      // 这里需要添加登录逻辑
      return;
    }
    setShowSubmitModal(true);
  };

  // 处理提交成功
  const handleSubmitSuccess = () => {
    const notification = GetNotification();
    notification.success('报名成功');
    // 可以在这里添加额外的逻辑
  };

  // 刷新参赛作品列表
  const refreshShowcaseWorks = async () => {
    console.log('刷新参赛作品列表');
    if (refreshing) return;

    try {
      setRefreshing(true);
      // 重新获取作品列表
      // await fetchShowcaseWorks();
    } catch (error) {
      console.error('刷新作品列表失败:', error);
    } finally {
      setRefreshing(false);
    }
  };

  // 返回渲染内容
  return (
    <div className={`activity-festival-page bg-gradient-animate h-screen pb-20 relative overflow-x-hidden overflow-y-auto ${loaded ? 'opacity-100' : 'opacity-0'} transition-opacity duration-500`}>
      {/* 添加透明导航栏 */}
      <ActivityNavbar />

      {/* 动态背景圆形装饰 */}
      <div className="floating-circle circle1"></div>
      <div className="floating-circle circle2"></div>
      <div className="floating-circle circle3"></div>

      {/* 添加导航占位，保持内容不被导航栏遮挡 */}
      <div className="h-14"></div>

      <div className="max-w-7xl mx-auto px-4 py-2 relative z-10">

        {/* 客户端检测 */}
        {!isClient ? (
          // 服务器端渲染时仅显示加载状态
          <div className="flex justify-center items-center py-20 bg-white/80 backdrop-blur-sm rounded-2xl shadow-md">
            <p className="text-gray-600">加载中...</p>
          </div>
        ) : (
          // 客户端渲染完整内容
          <>
            {/* 加载状态 */}
            {isLoadingActivity && (
              <div className="flex justify-center items-center py-20 bg-white/80 backdrop-blur-sm rounded-2xl shadow-md">
                <Spin tip="正在加载活动详情..." size="large" />
              </div>
            )}

            {/* 错误状态 */}
            {errorLoadingActivity && !isLoadingActivity && (
              <div className={`opacity-0 ${loaded ? 'animate-fadeInUp' : ''}`}>
                <Alert
                  message="加载活动详情失败"
                  description={errorLoadingActivity}
                  type="error"
                  showIcon
                  className="rounded-2xl"
                />
              </div>
            )}

            {/* 内容区域 - 只在加载成功且有数据时显示 */}
            {activityData && !isLoadingActivity && !errorLoadingActivity && (
              <>
                <div className={`opacity-0 ${loaded ? 'animate-fadeInUp' : ''}`}>
                  <ActivityHeader
                    title={activityData.title}
                    startTime={activityData.startTime}
                    endTime={activityData.endTime}
                    bannerImage={activityData.bannerImage}
                    expanded={detailExpanded}
                    setExpanded={setDetailExpanded}
                    activityId={activityData.id}
                    tags={activityData.tags}
                    organizer={activityData.organizer}

                    activityType={activityData.activityType || 0}
                    onRefreshWorks={refreshShowcaseWorks}
                    // 传递媒体字段
                    promotionImage={activityData.promotionImage}
                    backgroundImage={activityData.backgroundImage}
                    galleryImages={activityData.galleryImages}
                    attachmentFiles={activityData.attachmentFiles}
                  />
                </div>

                <div className={`mt-4 w-full max-w-7xl mx-auto opacity-0 ${loaded ? 'animate-fadeInUp animation-delay-300' : ''}`}>
                  {detailExpanded && (
                    <ActivityDetail
                      detailContent={activityData.detailContent}
                      detailEditorJson={activityData.detailEditorJson}
                      rulesContent={activityData.rulesContent}
                      rulesEditorJson={activityData.rulesEditorJson}
                      awardsContent={activityData.awardsContent}
                      awardsEditorJson={activityData.awardsEditorJson}
                      activeTab={activeTab}
                      onTabChange={(tab) => setActiveTab(tab)}
                      onImport={isAdmin ? () => setIsActivityImporterOpen(true) : undefined}
                      detailDocId={activityData.detailDocId}
                      rulesDocId={activityData.rulesDocId}
                      awardsDocId={activityData.awardsDocId}
                      onDocIdChange={isAdmin ? handleDocIdChange : undefined}
                    />
                  )}
                </div>

                {/* <div className={`mt-8 opacity-0 ${loaded ? 'animate-fadeInUp animation-delay-400' : ''}`}>
                  {isLoading ? (
                    <div className="flex justify-center items-center py-20 bg-white rounded-2xl shadow-md">
                      <span className="loading-spinner"></span>
                      <p className="ml-3 text-gray-600">正在加载参赛作品...</p>
                    </div>
                  ) : (
                    <WorksShowcase
                      works={showcaseWorks}
                      onOpenWorkSelector={() => setIsWorkSelectorOpen(true)}
                      onFilterChange={(filter) => setCurrentFilter(filter)}
                      initialFilter={currentFilter}
                      onWorksChange={handleWorksChange}
                      onWorkClick={handleOpenWorkDetail}
                    />
                  )}
                </div> */}
              </>
            )}
          </>
        )}
      </div>

      {/* WorksSelector组件 - 只在有活动数据时显示并且在客户端渲染 */}
      {isClient && activityData && (
        <WorksSelector
          isOpen={isWorkSelectorOpen}
          onClose={handleCancelSelection}
          onSave={handleSaveSelectedWorks}
          initialSelectedWorks={getCurrentFilterWorks()}
          currentFilter={currentFilter}
        />
      )}

      {/* ActivityImporter组件 - 只在有活动数据且用户为管理员时显示并且在客户端渲染 */}
      {isClient && activityData && isAdmin && (
        <ActivityImporter
          isOpen={isActivityImporterOpen}
          onClose={() => setIsActivityImporterOpen(false)}
          onImport={handleImportActivity}
          activeTab={activeTab}
        />
      )}

      {/* 添加作品详情模态框组件 */}
      {isWorkDetailOpen && (
        <div className="fixed inset-0 z-50">
          {/* 遮罩层 */}
          <div
            className="absolute inset-0 bg-black/60 backdrop-blur-sm transition-opacity duration-300"
            onClick={handleCloseWorkDetail}
          ></div>
          {/* 作品详情模态框 */}
          <div className="relative z-10">
            <WorkDetail
              work={selectedWork}
              isOpen={isWorkDetailOpen}
              onClose={handleCloseWorkDetail}
              isLiked={selectedWork?.liked || false}
              onLike={handleLikeWork}
            />
          </div>
        </div>
      )}

      {/* ActivitySubmitModal组件 - 只在有活动数据且在客户端渲染时显示 */}
      {isClient && activityData && (
        <ActivitySubmitModal
          visible={showSubmitModal}
          onClose={() => setShowSubmitModal(false)}
          activityId={Number(activityData.id)}
          activityTitle={activityData.title}
          activityType={activityData.activityType}
          onSubmitted={handleSubmitSuccess}
          onRefreshWorks={refreshShowcaseWorks}
        />
      )}

      {/* 回到顶部按钮 */}
      <BackToTop scrollThreshold={100} bottom={80} right={80} zIndex={1000} />
    </div>
  );
}

// 主页面组件，使用Suspense包裹内容组件
export default function FestivalPage() {
  return (
    <Suspense fallback={
      <div className="flex justify-center items-center py-20 bg-white/80 backdrop-blur-sm rounded-2xl shadow-md">
        <p className="text-gray-600">加载中...</p>
      </div>
    }>
      <FestivalPageContent />
    </Suspense>
  )
} 