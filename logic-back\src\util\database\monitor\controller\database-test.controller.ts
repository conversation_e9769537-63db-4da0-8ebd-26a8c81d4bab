import { Controller, Get, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery } from '@nestjs/swagger';
import { DataSource } from 'typeorm';
import { NotLogin } from 'src/web/router_guard/not-login.decorator';

@ApiTags('数据库测试')
@Controller('api/v1/database-test')
@NotLogin() // 数据库测试接口不需要登录验证
export class DatabaseTestController {
  constructor(private readonly dataSource: DataSource) {}

  @Get('slow-query')
  @ApiOperation({ summary: '测试慢查询' })
  @ApiQuery({ name: 'delay', required: false, description: '延迟时间(秒)', type: Number })
  @ApiResponse({ status: 200, description: '测试成功' })
  async testSlowQuery(@Query('delay') delay: number = 2): Promise<{
    code: number;
    message: string;
    data: any;
  }> {
    const startTime = Date.now();
    
    try {
      // 执行一个慢查询（使用 SLEEP 函数）
      const result = await this.dataSource.query(`SELECT SLEEP(${delay}) as sleep_result`);
      
      const executionTime = Date.now() - startTime;
      
      return {
        code: 200,
        message: '慢查询测试完成',
        data: {
          delay: delay,
          executionTime: executionTime,
          result: result
        }
      };
    } catch (error) {
      const executionTime = Date.now() - startTime;
      
      return {
        code: 500,
        message: '慢查询测试失败',
        data: {
          delay: delay,
          executionTime: executionTime,
          error: error.message
        }
      };
    }
  }

  @Get('complex-query')
  @ApiOperation({ summary: '测试复杂查询' })
  @ApiResponse({ status: 200, description: '测试成功' })
  async testComplexQuery(): Promise<{
    code: number;
    message: string;
    data: any;
  }> {
    const startTime = Date.now();
    
    try {
      // 执行一个复杂的查询（笛卡尔积）
      const result = await this.dataSource.query(`
        SELECT COUNT(*) as count 
        FROM (
          SELECT a.id as id1, b.id as id2 
          FROM user_students a 
          CROSS JOIN user_students b 
          LIMIT 10000
        ) as temp
      `);
      
      const executionTime = Date.now() - startTime;
      
      return {
        code: 200,
        message: '复杂查询测试完成',
        data: {
          executionTime: executionTime,
          result: result
        }
      };
    } catch (error) {
      const executionTime = Date.now() - startTime;
      
      return {
        code: 500,
        message: '复杂查询测试失败',
        data: {
          executionTime: executionTime,
          error: error.message
        }
      };
    }
  }

  @Get('batch-queries')
  @ApiOperation({ summary: '测试批量查询' })
  @ApiQuery({ name: 'count', required: false, description: '查询次数', type: Number })
  @ApiResponse({ status: 200, description: '测试成功' })
  async testBatchQueries(@Query('count') count: number = 10): Promise<{
    code: number;
    message: string;
    data: any;
  }> {
    const startTime = Date.now();
    const results: Array<{
      queryNumber: number;
      executionTime: number;
      result: any;
    }> = [];
    
    try {
      for (let i = 0; i < count; i++) {
        const queryStart = Date.now();
        const result = await this.dataSource.query(`SELECT 1 as test_result`);
        const queryTime = Date.now() - queryStart;
        
        results.push({
          queryNumber: i,
          executionTime: queryTime,
          result: result[0]
        });
        
        // 添加一些随机延迟
        if (Math.random() > 0.7) {
          await new Promise(resolve => setTimeout(resolve, Math.random() * 1000));
        }
      }
      
      const totalExecutionTime = Date.now() - startTime;
      
      return {
        code: 200,
        message: '批量查询测试完成',
        data: {
          totalQueries: count,
          totalExecutionTime: totalExecutionTime,
          averageExecutionTime: totalExecutionTime / count,
          results: results
        }
      };
    } catch (error) {
      const totalExecutionTime = Date.now() - startTime;
      
      return {
        code: 500,
        message: '批量查询测试失败',
        data: {
          totalQueries: count,
          totalExecutionTime: totalExecutionTime,
          completedQueries: results.length,
          error: error.message,
          results: results
        }
      };
    }
  }

  @Get('connection-info')
  @ApiOperation({ summary: '获取数据库连接信息' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getConnectionInfo(): Promise<{
    code: number;
    message: string;
    data: any;
  }> {
    try {
      const connectionInfo = await this.dataSource.query(`
        SELECT 
          CONNECTION_ID() as connection_id,
          USER() as current_user,
          DATABASE() as current_database,
          VERSION() as mysql_version,
          @@max_connections as max_connections,
          @@thread_cache_size as thread_cache_size,
          @@query_cache_size as query_cache_size
      `);
      
      const processlist = await this.dataSource.query(`
        SELECT COUNT(*) as active_connections 
        FROM INFORMATION_SCHEMA.PROCESSLIST 
        WHERE COMMAND != 'Sleep'
      `);
      
      return {
        code: 200,
        message: '获取数据库连接信息成功',
        data: {
          connectionInfo: connectionInfo[0],
          activeConnections: processlist[0].active_connections,
          isConnected: this.dataSource.isInitialized
        }
      };
    } catch (error) {
      return {
        code: 500,
        message: '获取数据库连接信息失败',
        data: {
          error: error.message,
          isConnected: this.dataSource.isInitialized
        }
      };
    }
  }
}
