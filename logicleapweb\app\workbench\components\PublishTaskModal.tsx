'use client';

import React, { useState, useEffect } from 'react';
import { X, ArrowLeft } from 'lucide-react';

interface Student {
  userId: number;
  nickName: string;
  studentNumber: string;
  avatarUrl?: string;
}

interface PublishTaskModalProps {
  isOpen: boolean;
  onClose: () => void;
  onBack: () => void;
  selectedSchool: any;
  selectedClass: any;
  selectedTemplate: any;
  selectedDistribution: string;
  energyAmount: string;
  selectedStudents?: number[]; // 可选的选中学生列表
  allStudents?: Student[]; // 可选的所有学生列表，默认使用班级所有学生
}

const PublishTaskModal: React.FC<PublishTaskModalProps> = ({
  isOpen,
  onClose,
  onBack,
  selectedSchool,
  selectedClass,
  selectedTemplate,
  selectedDistribution,
  energyAmount,
  selectedStudents,
  allStudents = []
}) => {
  const [activeTab, setActiveTab] = useState<'info' | 'resources'>('info');
  const [taskName, setTaskName] = useState('');
  const [taskDescription, setTaskDescription] = useState('');
  const [selfEvaluation, setSelfEvaluation] = useState('');
  const [mounted, setMounted] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);

  // 防止水合错误
  useEffect(() => {
    setMounted(true);
  }, []);

  // 支持的文件格式
  const supportedFormats = ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt'];
  const maxFileSize = 10 * 1024 * 1024; // 10MB

  // 处理文件上传
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files) return;

    const validFiles: File[] = [];
    const errors: string[] = [];

    Array.from(files).forEach(file => {
      // 检查文件格式
      const fileExtension = file.name.split('.').pop()?.toLowerCase();
      if (!fileExtension || !supportedFormats.includes(fileExtension)) {
        errors.push(`${file.name}: 不支持的文件格式`);
        return;
      }

      // 检查文件大小
      if (file.size > maxFileSize) {
        errors.push(`${file.name}: 文件大小超过10MB`);
        return;
      }

      validFiles.push(file);
    });

    if (errors.length > 0) {
      alert(errors.join('\n'));
    }

    if (validFiles.length > 0) {
      setUploadedFiles(prev => [...prev, ...validFiles]);
    }

    // 清空input值，允许重复选择同一文件
    event.target.value = '';
  };

  // 删除已上传的文件
  const handleRemoveFile = (index: number) => {
    setUploadedFiles(prev => prev.filter((_, i) => i !== index));
  };

  const handlePublish = () => {
    // 确定要发布任务的学生列表
    const targetStudents = selectedStudents && selectedStudents.length > 0
      ? allStudents.filter(student => selectedStudents.includes(student.userId))
      : allStudents; // 如果没有指定选中学生，则使用班级所有学生

    // 处理发布任务逻辑
    console.log('发布任务:', {
      school: selectedSchool,
      class: selectedClass,
      template: selectedTemplate,
      distribution: selectedDistribution,
      energy: energyAmount,
      taskName,
      taskDescription,
      selfEvaluation,
      targetStudents: targetStudents.map(s => ({
        userId: s.userId,
        nickName: s.nickName,
        studentNumber: s.studentNumber
      })),
      studentCount: targetStudents.length
    });

    // 这里可以调用API发布任务
    onClose();
  };

  // 防止水合错误，在客户端挂载前不渲染
  if (!mounted || !isOpen) return null;

  return (
    <div className="modal-overlay">
      <div className="modal-content publish-task-modal">
        {/* 头部 */}
        <div className="modal-header">
          <button className="close-btn" onClick={onClose}>
            <X size={24} />
          </button>
        </div>

        {/* 步骤指示器 */}
        <div className="step-indicator">
          <div className="step completed">
            <div className="step-number">1</div>
            <div className="step-label">选择班级</div>
          </div>
          <div className="step completed">
            <div className="step-number">2</div>
            <div className="step-label">能量和模板</div>
          </div>
          <div className="step active">
            <div className="step-number">3</div>
            <div className="step-label">发布任务</div>
          </div>
        </div>

        {/* 内容区域 */}
        <div className="modal-content-body">
          {/* 标签页 */}
          <div className="task-tabs">
            <button
              className={`tab-button ${activeTab === 'info' ? 'active' : ''}`}
              onClick={() => setActiveTab('info')}
            >
              任务信息
            </button>
            <button
              className={`tab-button ${activeTab === 'resources' ? 'active' : ''}`}
              onClick={() => setActiveTab('resources')}
            >
              资源与附件
            </button>
          </div>

          {/* 任务信息标签页 */}
          {activeTab === 'info' && (
            <div className="task-info-content">
              {/* 目标学生信息 */}
              <div className="target-students-info">
                <h4>发布对象</h4>
                <div className="students-summary">
                  {selectedStudents && selectedStudents.length > 0 ? (
                    <>
                      <span className="student-count">已选择 {selectedStudents.length} 名学生</span>
                      <div className="selected-students-list">
                        {allStudents
                          .filter(student => selectedStudents.includes(student.userId))
                          .map(student => (
                            <span key={student.userId} className="student-tag">
                              {student.nickName}
                            </span>
                          ))
                        }
                      </div>
                    </>
                  ) : (
                    <span className="student-count">全班学生 ({allStudents.length} 名)</span>
                  )}
                </div>
              </div>

              <div className="form-group">
                <input
                  type="text"
                  className="task-input"
                  placeholder="任务名称"
                  value={taskName}
                  onChange={(e) => setTaskName(e.target.value)}
                />
              </div>

              <div className="form-group">
                <textarea
                  className="task-textarea"
                  placeholder="任务描述"
                  value={taskDescription}
                  onChange={(e) => setTaskDescription(e.target.value)}
                  rows={4}
                />
              </div>

              <div className="form-group">
                <textarea
                  className="task-textarea"
                  placeholder="自评项"
                  value={selfEvaluation}
                  onChange={(e) => setSelfEvaluation(e.target.value)}
                  rows={4}
                />
              </div>
            </div>
          )}

          {/* 资源与附件标签页 */}
          {activeTab === 'resources' && (
            <div className="task-resources-content">
              {/* 选择作品区域 */}
              <div className="resource-section">
                <div className="section-label">选择作品</div>
                <div className="works-area">
                  <div className="works-placeholder">
                    作品列表
                  </div>
                </div>
              </div>

              {/* 选择附件区域 */}
              <div className="resource-section">
                <div className="section-label">选择附件</div>
                <div className="attachment-upload">
                  <div className="upload-area">
                    <input
                      type="file"
                      id="file-upload"
                      className="file-input"
                      multiple
                      accept=".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt"
                      onChange={handleFileUpload}
                    />
                    <label htmlFor="file-upload" className="upload-btn">
                      <span className="upload-icon">+</span>
                    </label>
                  </div>
                  <div className="file-info">
                    支持 jpg、png、gif、pdf、doc、docx、xls、xlsx、ppt、pptx、txt 格式且单个文件不超过 10MB
                  </div>
                </div>

                {/* 已上传文件列表 */}
                {uploadedFiles.length > 0 && (
                  <div className="uploaded-files">
                    <div className="files-label">已上传文件：</div>
                    <div className="files-list">
                      {uploadedFiles.map((file, index) => (
                        <div key={`${file.name}-${file.size}-${index}`} className="file-item">
                          <span className="file-name">{file.name}</span>
                          <span className="file-size">({(file.size / 1024 / 1024).toFixed(2)}MB)</span>
                          <button
                            className="remove-file-btn"
                            onClick={() => handleRemoveFile(index)}
                          >
                            ×
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}


              </div>
            </div>
          )}
        </div>

        {/* 固定的发布按钮 */}
        <div className="publish-section">
          <button
            className="publish-btn"
            onClick={handlePublish}
            disabled={!taskName.trim()}
          >
            开始上课
          </button>
        </div>
      </div>
    </div>
  );
};

export default PublishTaskModal;
