'use client';

import React, { useState } from 'react';
import { Modal, Form, Input, message, Select, Tabs } from 'antd';
import { passwordResetApi } from '../lib/api/password-reset';
import pcaData from '../public/pca.json';
import { InfoCircleOutlined } from '@ant-design/icons';
import SlideCaptcha from './slide-captcha';
import auth from '@/lib/auth';
import request from '@/lib/request';
import userApi from '@/lib/api/user';

interface ForgotPasswordModalProps {
  isOpen: boolean;
  onClose: () => void;
  loginType: 'normal' | 'student';
  onResetSuccess?: (phone: string, password: string) => void;
}

const ForgotPasswordModal: React.FC<ForgotPasswordModalProps> = ({
  isOpen,
  onClose,
  loginType,
  onResetSuccess
}) => {
  const [studentForm] = Form.useForm();
  const [normalForm] = Form.useForm();
  const [verifyForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [province, setProvince] = useState('');
  const [city, setCity] = useState('');
  const [district, setDistrict] = useState('');
  const [cities, setCities] = useState<any[]>([]);
  const [districts, setDistricts] = useState<any[]>([]);
  const [activeTab, setActiveTab] = useState<'student' | 'normal'>(loginType);
  const [showCaptcha, setShowCaptcha] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const [verifyPhone, setVerifyPhone] = useState('');
  const [verifyCode, setVerifyCode] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [verifyError, setVerifyError] = useState('');
  const [captchaVisible, setCaptchaVisible] = useState(false);

  // 判断是否是直辖市
  const isMunicipality = (provinceName: string) => {
    return ['北京市', '上海市', '天津市', '重庆市'].includes(provinceName);
  };

  // 处理省份变化
  const handleProvinceChange = (value: string) => {
    setProvince(value);
    const selectedProvince = pcaData.find(p => p.name === value);
    if (selectedProvince && selectedProvince.children) {
      if (isMunicipality(value)) {
        setCities([]);
        setDistricts(selectedProvince.children[0].children || []);
        setCity(value);
      } else {
        setCities(selectedProvince.children);
        setCity('');
        setDistrict('');
      }
    }
  };

  // 处理城市变化
  const handleCityChange = (value: string) => {
    setCity(value);
    if (!isMunicipality(province)) {
      const selectedProvince = pcaData.find(p => p.name === province);
      const selectedCity = selectedProvince?.children?.find(c => c.name === value);
      if (selectedCity && selectedCity.children) {
        setDistricts(selectedCity.children);
        setDistrict('');
      }
    }
  };

  // 处理表单提交
  const handleSubmit = async () => {
    try {
      setLoading(true);

      // 根据当前激活的标签页选择要验证的表单
      const currentForm = activeTab === 'student' ? studentForm : normalForm;
      const values = await currentForm.validateFields();

      const requestData = {
        ...values,
        userType: activeTab,
        ...(activeTab === 'student' ? {
          province,
          city,
          district
        } : {})
      };

      const response = await passwordResetApi.create(requestData);

      if (response.data.code === 200) {
        message.success(response.data.msg);
        handleClose();
      } else {
        message.error(response.data.message || '提交失败，请重试');
      }
    } catch (error) {
      console.error('提交失败:', error);
      message.error('提交失败，请检查表单信息');
    } finally {
      setLoading(false);
    }
  };

  // 处理模态框关闭
  const handleClose = () => {
    studentForm.resetFields();
    normalForm.resetFields();
    verifyForm.resetFields();
    setProvince('');
    setCity('');
    setDistrict('');
    onClose();
  };

  // 切换标签时重置表单
  const handleTabChange = (key: 'student' | 'normal') => {
    studentForm.resetFields();
    normalForm.resetFields();
    verifyForm.resetFields();
    setProvince('');
    setCity('');
    setDistrict('');
    setActiveTab(key);
  };

  // 处理发送验证码
  const handleSendCode = async () => {
    if (!verifyPhone) {
      setVerifyError('请输入手机号');
      return;
    }

    if (!/^1[3-9]\d{9}$/.test(verifyPhone)) {
      setVerifyError('请输入有效的手机号');
      return;
    }

    // 显示滑块验证
    setCaptchaVisible(true);
  };

  // 处理验证码验证成功
  const handleCaptchaSuccess = async () => {
    try {
      const { data: response } = await userApi.sendVerifyCode(verifyPhone);

      if (response.data && response.data.code === 200) {
        // 即使 code 为 200，也要检查 success 字段
        if (response.data.success === false) {
          // success 明确为 false 时表示业务失败
          message.error(response.data.message || response.data.msg || '发送验证码失败');
          setVerifyError(response.data.message || response.data.msg || '发送验证码失败');
        } else {
          // code 为 200 且 success 不为 false 时才是成功
          message.success('验证码已发送');
          setCountdown(60);
          const timer = setInterval(() => {
            setCountdown(prev => {
              if (prev <= 1) {
                clearInterval(timer);
                return 0;
              }
              return prev - 1;
            });
          }, 1000);
        }
      } else if (response.data && response.data.success === true) {
        // success 明确为 true 时也是成功
        message.success('验证码已发送');
        setCountdown(60);
        const timer = setInterval(() => {
          setCountdown(prev => {
            if (prev <= 1) {
              clearInterval(timer);
              return 0;
            }
            return prev - 1;
          });
        }, 1000);
      } else {
        message.error(response.data?.message || response.data?.msg || '发送验证码失败');
        setVerifyError(response.data?.message || response.data?.msg || '发送验证码失败');
      }
    } catch (error: any) {
      console.error('发送验证码失败:', error);
      const errorMsg = error.response?.data?.message || error.response?.data?.msg || '发送验证码失败，请稍后再试';
      message.error(errorMsg);
      setVerifyError(errorMsg);
    } finally {
      setCaptchaVisible(false);
    }
  };

  // 处理验证码重置密码
  const handleVerifySubmit = async () => {
    if (newPassword !== confirmPassword) {
      message.error('两次输入的密码不一致');
      return;
    }

    try {
      const response = await auth.resetPasswordByCode({
        phone: verifyPhone,
        code: verifyCode,
        newPassword: newPassword
      });

      if (response.data.code === 200) {
        // 先调用回调函数
        onResetSuccess?.(verifyPhone, newPassword);
        // 然后显示成功消息
        message.success('密码重置成功');
        // 最后关闭窗口
        handleClose();
      } else {
        message.error(response.data.message || '密码重置失败');
      }
    } catch (error) {
      console.error('重置密码失败:', error);
      message.error('密码重置失败');
    }
  };

  return (
    <>
      <Modal
        title="忘记密码"
        open={isOpen}
        onCancel={handleClose}
        onOk={handleSubmit}
        confirmLoading={loading}
        width={500}
        modalRender={(modal) => (
          <div style={{
            margin: '5vh 0',
            display: 'flex',
            flexDirection: 'column',
            height: 'fit-content',
            maxHeight: '90vh'
          }}>
            {modal}
          </div>
        )}
        style={{
          maxWidth: '90vw',
          top: 0,
          paddingBottom: 0
        }}
        styles={{
          body: {
            padding: '24px',
            maxHeight: 'calc(90vh - 110px)',
            overflow: 'auto'
          }
        }}
        className="custom-scrollbar-modal"
      >
        <style jsx global>{`
          .custom-scrollbar-modal .ant-modal-body {
            scrollbar-width: thin;
            scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
          }
          .custom-scrollbar-modal .ant-modal-body::-webkit-scrollbar {
            width: 6px;
          }
          .custom-scrollbar-modal .ant-modal-body::-webkit-scrollbar-thumb {
            background-color: rgba(0, 0, 0, 0.2);
            border-radius: 3px;
            transition: background-color 0.3s;
          }
          .custom-scrollbar-modal .ant-modal-body::-webkit-scrollbar-thumb:hover {
            background-color: rgba(0, 0, 0, 0.3);
          }
          .custom-scrollbar-modal .ant-modal-body::-webkit-scrollbar-track {
            background-color: transparent;
          }
        `}</style>
        <div className="mb-4 p-4 bg-blue-50 rounded-lg">
          <div className="flex items-start gap-2">
            <InfoCircleOutlined className="text-blue-500 mt-1" />
            <div className="flex-1">
              <div className="text-blue-600 font-medium">温馨提示</div>
              <div className="text-blue-500 text-sm mt-1">
                请填写以下信息，我们会尽快处理您的密码重置请求。处理完成后，新的密码将会通过您提供的联系方式发送给您。
              </div>
            </div>
          </div>
        </div>

        <Tabs
          activeKey={activeTab}
          onChange={(key) => handleTabChange(key as 'student' | 'normal')}
          items={[
            {
              key: 'student',
              label: '学生用户',
              children: (
                <Form
                  form={studentForm}
                  layout="vertical"
                  requiredMark={false}
                >
                  <Form.Item
                    name="schoolName"
                    label="学校名称"
                    rules={[{ required: true, message: '请输入学校名称' }]}
                  >
                    <Input placeholder="请输入学校名称" />
                  </Form.Item>

                  <Form.Item
                    name="studentNumber"
                    label="学号"
                    rules={[{ required: true, message: '请输入学号' }]}
                  >
                    <Input placeholder="请输入学号" />
                  </Form.Item>

                  <Form.Item
                    name="name"
                    label="姓名"
                    rules={[{ required: true, message: '请输入姓名' }]}
                  >
                    <Input placeholder="请输入姓名" />
                  </Form.Item>

                  <Form.Item
                    name="contactInfo"
                    label="其他联系方式"
                    rules={[{ required: true, message: '请输入其他联系方式' }]}
                  >
                    <Input placeholder="请输入其他联系方式（如微信、QQ等）" />
                  </Form.Item>

                  <Form.Item
                    name="remark"
                    label="备注信息"
                  >
                    <Input.TextArea
                      placeholder="请输入备注信息（选填）"
                      rows={4}
                    />
                  </Form.Item>
                </Form>
              )
            },
            {
              key: 'normal',
              label: '普通用户',
              children: (
                <Tabs defaultActiveKey="manual">
                  <Tabs.TabPane tab="人工处理" key="manual">
                    <Form
                      form={normalForm}
                      layout="vertical"
                      requiredMark={false}
                    >
                      <Form.Item
                        name="phone"
                        label="手机号"
                        rules={[{ required: true, message: '请输入手机号' }]}
                      >
                        <Input placeholder="请输入手机号" />
                      </Form.Item>

                      <Form.Item
                        name="name"
                        label="姓名"
                        rules={[{ required: true, message: '请输入姓名' }]}
                      >
                        <Input placeholder="请输入姓名" />
                      </Form.Item>

                      <Form.Item
                        name="contactInfo"
                        label="其他联系方式"
                        rules={[{ required: true, message: '请输入其他联系方式' }]}
                      >
                        <Input placeholder="请输入其他联系方式（如微信、QQ等）" />
                      </Form.Item>

                      <Form.Item
                        name="remark"
                        label="备注信息"
                      >
                        <Input.TextArea
                          placeholder="请输入备注信息（选填）"
                          rows={4}
                        />
                      </Form.Item>
                    </Form>
                  </Tabs.TabPane>
                  <Tabs.TabPane tab="验证码重置" key="verify">
                    <Form
                      form={verifyForm}
                      layout="vertical"
                      requiredMark={false}
                      onFinish={handleVerifySubmit}
                    >
                      <Form.Item
                        name="phone"
                        label="手机号"
                        rules={[{ required: true, message: '请输入手机号' }]}
                      >
                        <Input
                          placeholder="请输入手机号"
                          autoComplete="off"
                          id="reset-phone"
                          onChange={e => {
                            setVerifyPhone(e.target.value);
                            verifyForm.setFieldValue('phone', e.target.value);
                          }}
                        />
                      </Form.Item>

                      <Form.Item
                        name="verifyCode"
                        label="验证码"
                        rules={[{ required: true, message: '请输入验证码' }]}
                      >
                        <div className="flex gap-2">
                          <Input
                            placeholder="请输入验证码"
                            autoComplete="off"
                            id="reset-verify-code"
                            onChange={e => {
                              setVerifyCode(e.target.value);
                              verifyForm.setFieldValue('verifyCode', e.target.value);
                            }}
                          />
                          <button
                            type="button"
                            onClick={handleSendCode}
                            disabled={countdown > 0 || !verifyPhone}
                            className="min-w-[120px] px-4 py-2 bg-blue-500 text-white rounded disabled:bg-gray-300"
                          >
                            {countdown > 0 ? `${countdown}s` : '获取验证码'}
                          </button>
                        </div>
                      </Form.Item>

                      <Form.Item
                        name="newPassword"
                        label="新密码"
                        rules={[{ required: true, message: '请输入新密码' }]}
                      >
                        <Input.Password
                          placeholder="请输入新密码"
                          autoComplete="new-password"
                          onChange={e => {
                            setNewPassword(e.target.value);
                            verifyForm.setFieldValue('newPassword', e.target.value);
                          }}
                        />
                      </Form.Item>

                      <Form.Item
                        name="confirmPassword"
                        label="确认密码"
                        rules={[{ required: true, message: '请确认新密码' }]}
                      >
                        <Input.Password
                          placeholder="请再次输入新密码"
                          autoComplete="new-password"
                          onChange={e => {
                            setConfirmPassword(e.target.value);
                            verifyForm.setFieldValue('confirmPassword', e.target.value);
                          }}
                        />
                      </Form.Item>

                      <button
                        type="submit"
                        className="w-full px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                      >
                        重置密码
                      </button>
                    </Form>
                  </Tabs.TabPane>
                </Tabs>
              )
            }
          ]}
        />
      </Modal>

      <SlideCaptcha
        isOpen={captchaVisible}
        onClose={() => setCaptchaVisible(false)}
        onSuccess={handleCaptchaSuccess}
      />
    </>
  );
};

export default ForgotPasswordModal; 