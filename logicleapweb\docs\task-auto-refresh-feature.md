# 任务发布自动刷新功能

## 功能描述

当用户在任务发布弹窗中成功发布任务后，如果当前正在查看"进行中的任务"页面，该页面会自动刷新并显示新发布的任务，无需用户手动切换页面或刷新。

## 实现方案

### 1. 全局事件管理器

创建了 `TaskEventManager` 类来管理全局任务相关事件：

- **文件位置**: `logicleapweb/app/utils/task-event-manager.ts`
- **功能**: 提供事件的注册、监听、触发和清理机制
- **事件类型**: 
  - `TASK_PUBLISHED`: 任务发布成功
  - `TASK_UPDATED`: 任务更新
  - `TASK_DELETED`: 任务删除
  - `TASK_STATUS_CHANGED`: 任务状态变更

### 2. 工作台Context增强

在 `TemplateContext` 中添加了任务发布通知机制：

- **文件位置**: `logicleapweb/app/workbench/contexts/TemplateContext.tsx`
- **新增功能**:
  - `taskPublishVersion`: 任务发布版本号
  - `notifyTaskPublished()`: 通知任务发布成功的方法

### 3. OngoingTasks组件自动刷新

修改了 `OngoingTasks` 组件以支持自动刷新：

- **文件位置**: `logicleapweb/app/workbench/components/OngoingTasks.tsx`
- **实现方式**:
  - 监听 `taskPublishVersion` 变化（工作台内部通信）
  - 监听全局 `TASK_PUBLISHED` 事件（跨页面通信）
  - 当检测到任务发布事件时，自动调用 `fetchOngoingTasks()` 刷新数据

### 4. 任务发布触发事件

在任务发布成功后触发相应事件：

#### 工作台任务发布
- **文件位置**: `logicleapweb/app/workbench/components/ClassTasks.tsx`
- **触发时机**: 任务发布API调用成功后
- **触发方式**: 
  - 调用 `notifyTaskPublished()` (Context通知)
  - 触发 `TASK_PUBLISHED` 全局事件

#### 教师空间任务发布
- **文件位置**: `logicleapweb/app/teacher-space/page.tsx`
- **触发时机**: 任务发布API调用成功后
- **触发方式**: 触发 `TASK_PUBLISHED` 全局事件

## 技术特点

### 1. 双重通知机制
- **Context通知**: 用于工作台内部组件间通信
- **全局事件**: 用于跨页面、跨模块通信

### 2. 事件数据结构
```typescript
interface TaskPublishedEventData {
  taskId: number;
  taskName: string;
  classId?: number;
  className?: string;
  teacherId: number;
}
```

### 3. 自动清理
- 组件卸载时自动移除事件监听器
- 避免内存泄漏

## 使用场景

1. **工作台内**: 用户在班级任务页面发布任务 → 主页"进行中的任务"自动刷新
2. **跨页面**: 用户在教师空间发布任务 → 切换到工作台时"进行中的任务"显示最新数据
3. **多标签页**: 在一个标签页发布任务 → 其他标签页的任务列表自动更新

## 扩展性

该架构支持轻松添加更多任务相关事件：
- 任务更新通知
- 任务删除通知
- 任务状态变更通知
- 学生提交作业通知等

## 性能考虑

- 事件监听器在组件卸载时自动清理
- 使用 Set 数据结构存储监听器，提高性能
- 错误处理机制防止单个监听器错误影响其他监听器

## 优化更新 (解决不必要刷新问题)

### 问题描述
初始实现中，"进行中的任务"列表会在没有新任务发布时也进行不必要的刷新。

### 解决方案
1. **移除Context双重通知**: 移除了TemplateContext中的`taskPublishVersion`监听，避免组件重新渲染时的意外触发
2. **简化为单一事件源**: 只使用全局事件管理器进行通信，确保只有真正的任务发布才触发刷新
3. **添加防抖机制**: 使用500ms的防抖延迟，避免短时间内的重复刷新
4. **增强日志记录**: 添加详细的调试日志，便于问题排查

### 优化后的流程
1. 用户发布任务成功
2. 触发全局`TASK_PUBLISHED`事件
3. OngoingTasks组件接收事件
4. 经过500ms防抖延迟后执行刷新
5. 只有真正的任务发布才会触发刷新

### 技术改进
- **单一职责**: 每个事件源只负责一种类型的通知
- **防抖优化**: 避免频繁的API调用
- **内存优化**: 减少不必要的状态监听和更新

## 任务排序优化

### 功能描述
"进行中的任务"列表现在按照任务发布时间进行排序，最新发布的任务显示在最上面。

### 实现细节
1. **API排序**: 在获取任务列表时使用 `orderBy: 'createTime:DESC'` 参数
2. **前端排序**: 在客户端再次按创建时间进行排序，确保顺序正确
3. **时间字段优先级**:
   - 优先使用 `createTime`（任务创建时间）
   - 如果没有 `createTime`，则使用 `startDate`（任务开始时间）作为备选
4. **去重优化**: 在去重处理中，优先保留创建时间最新的任务

### 排序逻辑
```typescript
const sortedTasks = deduplicatedTasks.sort((a, b) => {
  const timeA = a.createTime ? new Date(a.createTime).getTime() : new Date(a.startDate).getTime();
  const timeB = b.createTime ? new Date(b.createTime).getTime() : new Date(b.startDate).getTime();
  return timeB - timeA; // 降序排列：最新的在前面
});
```

### 用户体验
- 用户发布新任务后，该任务会自动出现在列表顶部
- 任务列表按时间顺序排列，便于用户快速找到最近的任务
- 保持了去重功能，避免重复任务显示
