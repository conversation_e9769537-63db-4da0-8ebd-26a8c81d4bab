import React from 'react';
import { Student, StudentListProps, Template } from '../types';

// 获取头像颜色
const getAvatarColor = (index: number): string => {
  const colors = [
    'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
    'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
    'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
    'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
    'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
    'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)',
    'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
    'linear-gradient(135deg, #a8caba 0%, #5d4e75 100%)',
    'linear-gradient(135deg, #d299c2 0%, #fef9d7 100%)'
  ];
  return colors[index % colors.length];
};

const StudentList: React.FC<StudentListProps> = ({
  students,
  loading,
  error,
  selectedStudent,
  selectedStudentIds,
  currentTemplate,
  renderVersion,
  onStudentClick,
  onStudentSelect,
  onRetry,
  onIndividualAssignBlocks,
  onAssignPoints
}) => {
  if (loading) {
    return (
      <div className="loading-message">
        <p>正在加载学生信息...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="error-message">
        <p>{error}</p>
        <button onClick={onRetry} className="retry-button">
          重试
        </button>
      </div>
    );
  }

  if (students.length === 0) {
    return (
      <div className="empty-message">
        <p>暂无学生</p>
      </div>
    );
  }

  return (
    <>
      {students.map((student, index) => (
        <div
          key={`${student.userId}-${renderVersion}`}
          className={`student-item ${selectedStudent?.userId === student.userId ? 'selected' : ''}`}
          onClick={() => onStudentClick(student)}
        >
          <div
            className="student-avatar"
            style={{ background: getAvatarColor(index) }}
          >
            {student.nickName ? student.nickName.charAt(0) : 'S'}
          </div>
          <div className="student-info">
            <div className="student-name">{student.nickName || `学生${student.studentNumber || student.userId}`}</div>
            <div className="student-id">{student.studentNumber || '无学号'}</div>
            {/* 显示当前模板信息 */}
            <div className="student-template">
              {(() => {
                // 优先显示学生个人模板，如果没有则显示教师模板
                const displayTemplate = student.currentTemplate || currentTemplate;

                // 添加调试信息
                if (student.userId === 4041) { // 只为第一个学生打印调试信息
                  console.log('学生模板显示逻辑:', {
                    studentId: student.userId,
                    studentName: student.nickName,
                    studentCurrentTemplate: student.currentTemplate,
                    teacherTemplate: currentTemplate,
                    displayTemplate: displayTemplate
                  });
                }

                if (displayTemplate) {
                  return (
                    <span className={`template-tag ${displayTemplate.isOfficial ? 'official' : 'custom'}`}>
                      🧩 {displayTemplate.templateName}
                    </span>
                  );
                } else {
                  return (
                    <span className="template-tag loading">
                      🧩 加载中...
                    </span>
                  );
                }
              })()}
            </div>
          </div>

          {/* 学生操作按钮 */}
          <div className="student-actions">
            <button
              className="action-btn assign-blocks-btn"
              onClick={(e) => {
                e.stopPropagation();
                onIndividualAssignBlocks(student.userId);
              }}
              title="为此学生分配积木"
            >
              🧩
            </button>
            <button
              className="action-btn assign-points-btn"
              onClick={(e) => {
                e.stopPropagation();
                onAssignPoints(student.userId);
              }}
              title="为此学生分配能量"
            >
              ⚡
            </button>
          </div>
          <button
            className={`student-checkbox ${selectedStudentIds.includes(student.userId) ? 'checked' : ''}`}
            onClick={(e) => {
              e.stopPropagation();
              onStudentSelect(student.userId);
            }}
          >
            {selectedStudentIds.includes(student.userId) ? (
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="3" y="3" width="18" height="18" rx="2" fill="#3b82f6" stroke="#3b82f6" strokeWidth="2"/>
                <path d="M9 12l2 2 4-4" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            ) : (
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="3" y="3" width="18" height="18" rx="2" fill="none" stroke="#d1d5db" strokeWidth="2"/>
              </svg>
            )}
          </button>
        </div>
      ))}
    </>
  );
};

export default StudentList;
