'use client'

import { useState, useRef, useMemo } from 'react';
import { Modal, Button, Table, Upload, Space, Typography, Alert, Progress, Tabs, Checkbox, Select, Input, Tooltip, Dropdown, Menu, Avatar } from 'antd';
import {
  InboxOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  DeleteOutlined,
  KeyOutlined,
  GiftOutlined,
  WarningOutlined,
  SearchOutlined,
  DownOutlined,
  UserOutlined,
  EditOutlined,
  InfoOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import type { UploadFile, UploadProps } from 'antd/es/upload/interface';
import * as XLSX from 'xlsx';
import { keyPackageApi } from '@/lib/api/key_package';
import { GetNotification } from 'logic-common/dist/components/Notification';

const { Dragger } = Upload;
const { Text, Title } = Typography;

interface Student {
  userId: number;
  nickName: string;
  avatarUrl?: string;
  studentNumber?: string;
  availablePoints?: number;
  lastLoginTime?: string;
  templateName?: string;
  id?: number;
}

interface BatchUseKeyPackageModalProps {
  open: boolean;
  selectedStudentIds: number[];
  students: Student[];
  onClose: () => void;
  onSuccess: () => void;
  onGoToAssignPoints?: (studentIds: number[]) => void;
}

interface KeyData {
  key: string;
  valid?: boolean;
  message?: string;
  studentId?: number;
  selected?: boolean;
}

interface ImportResults {
  success: number;
  failed: number;
  total: number;
}

export const BatchUseKeyPackageModal = ({
  open,
  selectedStudentIds,
  students,
  onClose,
  onSuccess,
  onGoToAssignPoints
}: BatchUseKeyPackageModalProps) => {
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [keyData, setKeyData] = useState<KeyData[]>([]);
  const [importStep, setImportStep] = useState<'upload' | 'preview' | 'importing' | 'complete'>('upload');
  const [importResults, setImportResults] = useState<ImportResults>({ success: 0, failed: 0, total: 0 });
  const [loading, setLoading] = useState(false);
  const [importing, setImporting] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [isSpecialExchange, setIsSpecialExchange] = useState(false);
  const [teacherId, setTeacherId] = useState<number | null>(null);
  const notification = GetNotification();

  // 获取当前用户ID作为教师ID
  const getCurrentUserId = () => {
    // 这里应该从Redux store或其他状态管理中获取当前用户ID
    // 暂时返回一个默认值，实际使用时需要替换
    return 1; // 替换为实际的用户ID获取逻辑
  };

  // 文件上传配置
  const uploadProps: UploadProps = {
    name: 'file',
    multiple: false,
    accept: '.xlsx,.xls',
    fileList,
    beforeUpload: (file) => {
      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || 
                     file.type === 'application/vnd.ms-excel';
      if (!isExcel) {
        notification.error('只能上传Excel文件！');
        return false;
      }
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        notification.error('文件大小不能超过10MB！');
        return false;
      }
      return false; // 阻止自动上传
    },
    onChange: (info) => {
      setFileList(info.fileList.slice(-1)); // 只保留最新的一个文件
      if (info.fileList.length > 0) {
        handleFileUpload(info.fileList[0]);
      }
    },
    onRemove: () => {
      setFileList([]);
      setKeyData([]);
      setImportStep('upload');
    }
  };

  // 处理文件上传
  const handleFileUpload = async (file: UploadFile) => {
    if (!file.originFileObj) return;

    setLoading(true);
    setImporting(true);

    try {
      // 读取Excel文件内容
      const reader = new FileReader();
      reader.onload = async (e) => {
        try {
          const data = new Uint8Array(e.target?.result as ArrayBuffer);
          const workbook = XLSX.read(data, { type: 'array' });

          // 获取第一个工作表
          const worksheet = workbook.Sheets[workbook.SheetNames[0]];

          // 将工作表转换为JSON对象
          const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

          // 忽略第一行，获取第二列的数据作为密钥
          const keys: KeyData[] = jsonData.slice(1).map((row: any) => {
            // 确保行是数组并且有第二个元素
            if (Array.isArray(row) && row.length >= 2) {
              return {
                key: String(row[1]).trim(),
                valid: true
              };
            }
            return {
              key: '',
              valid: false,
              message: '无效的数据格式'
            };
          }).filter(item => item.key !== '');

          setKeyData(keys);
          setImportStep('preview');
          notification.success(`成功读取 ${keys.length} 个密钥`);
        } catch (error) {
          console.error('解析Excel文件失败:', error);
          notification.error('解析Excel文件失败，请检查文件格式');
        } finally {
          setLoading(false);
          setImporting(false);
        }
      };

      reader.readAsArrayBuffer(file.originFileObj);
    } catch (error) {
      console.error('读取文件失败:', error);
      notification.error('读取文件失败');
      setLoading(false);
      setImporting(false);
    }
  };

  // 处理手动输入模式
  const handleManualMode = () => {
    if (selectedStudentIds.length === 0) {
      notification.warning('请先选择需要兑换密钥的学生');
      return;
    }

    // 为每个选定的学生创建一个空的密钥记录
    const manualKeyData: KeyData[] = [];

    selectedStudentIds.forEach(studentId => {
      manualKeyData.push({
        key: '',
        valid: false,
        message: '请手动输入密钥',
        studentId,
        selected: false
      });
    });

    setKeyData(manualKeyData);
    setImportStep('preview');
    notification.info(`已为 ${selectedStudentIds.length} 名学生准备手动输入界面`);
  };

  // 开始批量兑换操作
  const handleBatchUseKeys = async () => {
    if (isSpecialExchange && !teacherId) {
      notification.error('特殊兑换模式下必须提供教师ID，请联系管理员');
      return;
    }

    setLoading(true);
    setImportStep('importing');

    let successCount = 0;
    let failedCount = 0;

    // 只处理有效且已分配且已选择的密钥
    const validAssignedKeys = keyData.filter(item => item.valid && item.studentId && item.selected);
    const total = validAssignedKeys.length;

    if (total === 0) {
      notification.warning('没有选择任何有效密钥进行兑换');
      setImportStep('preview');
      setLoading(false);
      return;
    }

    const updatedKeyData = [...keyData];

    // 逐个处理每个密钥
    for (const item of validAssignedKeys) {
      if (!item.studentId || !item.key) continue;

      const keyIndex = keyData.findIndex(k => k.key === item.key);

      try {
        // 准备API参数
        const apiParams = {
          key: item.key,
          userId: item.studentId,
          isSpecial: isSpecialExchange
        };

        // 只有在特殊兑换模式下才添加teacherId
        if (isSpecialExchange) {
          if (!teacherId) {
            updatedKeyData[keyIndex] = {
              ...item,
              valid: false,
              message: '兑换失败: 特殊兑换需要提供教师ID'
            };
            failedCount++;
            continue;
          }
          Object.assign(apiParams, { teacherId });
        }

        // 调用API使用密钥
        const response = await keyPackageApi.useKey(apiParams);
        if (response.data.code === 200) {
          updatedKeyData[keyIndex] = {
            ...item,
            valid: true,
            message: '兑换成功'
          };
          successCount++;
          notification.success(`密钥 ${item.key} 兑换成功`);
        } else {
          updatedKeyData[keyIndex] = {
            ...item,
            valid: false,
            message: '兑换失败'
          };
          failedCount++;
          notification.error('兑换失败');
        }
      } catch (error: any) {
        const errorMessage = error.response?.data?.msg || error.response?.data?.message || '未知错误';
        updatedKeyData[keyIndex] = {
          ...item,
          valid: false,
          message: '兑换失败: ' + errorMessage
        };
        failedCount++;
        notification.error('兑换失败');
      }
    }

    // 更新状态
    setKeyData(updatedKeyData);
    setImportResults({
      success: successCount,
      failed: failedCount,
      total
    });
    setImportStep('complete');

    if (successCount > 0) {
      notification.success(`成功兑换 ${successCount} 个密钥`);
      onSuccess();
    }
  };

  // 重置状态
  const handleReset = () => {
    setFileList([]);
    setKeyData([]);
    setImportStep('upload');
    setImportResults({ success: 0, failed: 0, total: 0 });
  };

  // 处理关闭
  const handleClose = () => {
    handleReset();
    onClose();
  };

  // 渲染上传步骤
  const renderUploadStep = () => {
    return (
      <div className="space-y-6">
        <div className="text-center mb-6">
          <Title level={4} className="text-gray-700 mb-2">批量兑换密钥</Title>
          <Text className="text-gray-500">
            已选择 {selectedStudentIds.length} 名学生
          </Text>
        </div>

        <Dragger
          {...uploadProps}
          disabled={importing}
          className="bg-gradient-to-b from-blue-50/50 to-white"
          style={{ height: 'auto' }}
        >
          <div className="py-6 px-4 md:p-6 sm:p-4 xs:p-3">
            <p className="ant-upload-drag-icon">
              <KeyOutlined className="text-blue-500 text-4xl md:text-5xl sm:text-4xl xs:text-3xl" />
            </p>
            <p className="ant-upload-text font-medium text-gray-700 text-base md:text-lg sm:text-base xs:text-sm">
              请上传密钥
            </p>
            <p className="ant-upload-hint text-gray-500 text-sm md:text-base sm:text-sm xs:text-xs">
              支持单个Excel文件上传(.xlsx或.xls)，系统将读取Excel文件中的密钥
            </p>
          </div>
        </Dragger>

        {/* 手动输入按钮 */}
        <div className="mt-6 border-t border-gray-100 pt-6">
          <Button
            type="default"
            block
            icon={<EditOutlined />}
            onClick={handleManualMode}
            className="h-12 bg-white hover:bg-blue-50 border border-blue-200 hover:border-blue-300 text-blue-600 hover:text-blue-700 font-medium rounded-xl shadow-sm hover:shadow transition-all duration-300"
            disabled={importing}
          >
            <div className="flex items-center justify-center">
              <span className="mr-1 font-medium">手动输入兑换码</span>
              <span className="text-xs text-gray-500 ml-1">跳过上传，直接手动录入</span>
            </div>
          </Button>
        </div>
      </div>
    );
  };

  // 渲染预览步骤
  const renderPreviewStep = () => {
    const filteredStudents = students.filter(student =>
      selectedStudentIds.includes(student.userId) &&
      student.nickName.toLowerCase().includes(searchText.toLowerCase())
    );

    const columns = [
      {
        title: '学生',
        dataIndex: 'student',
        key: 'student',
        render: (_: any, record: KeyData) => {
          const student = students.find(s => s.userId === record.studentId);
          return student ? (
            <div className="flex items-center gap-2">
              <Avatar size={32} src={student.avatarUrl} icon={<UserOutlined />} />
              <div>
                <div className="font-medium">{student.nickName || `学生${student.studentNumber || student.userId}`}</div>
                <div className="text-xs text-gray-500">{student.studentNumber}</div>
              </div>
            </div>
          ) : '-';
        }
      },
      {
        title: '密钥',
        dataIndex: 'key',
        key: 'key',
        render: (key: string, record: KeyData) => (
          <Input
            value={key}
            onChange={(e) => {
              const newKeyData = [...keyData];
              const index = newKeyData.findIndex(item => item === record);
              if (index !== -1) {
                newKeyData[index] = { ...record, key: e.target.value, valid: e.target.value.trim() !== '' };
                setKeyData(newKeyData);
              }
            }}
            placeholder="请输入密钥"
            className="w-full"
          />
        )
      },
      {
        title: '状态',
        dataIndex: 'valid',
        key: 'valid',
        render: (valid: boolean, record: KeyData) => (
          <div className="flex items-center gap-2">
            {valid ? (
              <CheckCircleOutlined className="text-green-500" />
            ) : (
              <CloseCircleOutlined className="text-red-500" />
            )}
            <span className={valid ? 'text-green-600' : 'text-red-600'}>
              {record.message || (valid ? '有效' : '无效')}
            </span>
          </div>
        )
      }
    ];

    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <Title level={4}>密钥分配预览</Title>
          <Button onClick={() => setImportStep('upload')}>返回上传</Button>
        </div>

        <div className="flex justify-between items-center">
          <Input
            placeholder="搜索学生姓名"
            prefix={<SearchOutlined />}
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            className="w-64"
          />
          <Text className="text-gray-500">
            共 {keyData.length} 条记录
          </Text>
        </div>

        <Table
          columns={columns}
          dataSource={keyData}
          rowKey={(record) => `${record.studentId}-${record.key}`}
          pagination={false}
          size="small"
          scroll={{ y: 400 }}
          rowSelection={{
            selectedRowKeys,
            onChange: (keys) => setSelectedRowKeys(keys),
            onSelectAll: (selected) => {
              const newKeyData = keyData.map(item => ({ ...item, selected }));
              setKeyData(newKeyData);
            },
            onSelect: (record, selected) => {
              const newKeyData = keyData.map(item =>
                item === record ? { ...item, selected } : item
              );
              setKeyData(newKeyData);
            }
          }}
        />

        <div className="flex justify-between items-center pt-4 border-t">
          <Text className="text-gray-500">
            已选择 {keyData.filter(item => item.selected).length} 个密钥进行兑换
          </Text>
          <Button
            type="primary"
            onClick={handleBatchUseKeys}
            disabled={keyData.filter(item => item.valid && item.studentId && item.selected).length === 0}
            loading={loading}
          >
            开始兑换 ({keyData.filter(item => item.valid && item.studentId && item.selected).length})
          </Button>
        </div>
      </div>
    );
  };

  return (
    <Modal
      title={
        <div className="flex items-center gap-2">
          <KeyOutlined className="text-blue-500" />
          <span>批量兑换密钥</span>
        </div>
      }
      open={open}
      onCancel={handleClose}
      footer={null}
      width="85%"
      centered
      destroyOnClose
      className="key-exchange-modal"
      styles={{
        body: {
          padding: '20px 24px 24px',
          maxHeight: 'calc(80vh - 160px)',
          overflowY: 'auto',
          overflowX: 'hidden'
        }
      }}
      style={{
        maxWidth: '900px',
        margin: '0 auto',
        top: 0
      }}
    >
      {importStep === 'upload' && renderUploadStep()}

      {importStep === 'preview' && renderPreviewStep()}

      {importStep === 'importing' && (
        <div className="text-center py-8">
          <Progress type="circle" percent={50} />
          <div className="mt-4">
            <Text>正在兑换密钥...</Text>
          </div>
        </div>
      )}

      {importStep === 'complete' && (
        <div className="text-center py-8">
          <div className="mb-4 flex flex-col items-center">
            <div className="w-16 h-16 flex items-center justify-center rounded-full bg-gradient-to-r from-green-400 to-blue-400 mb-4">
              <CheckCircleOutlined className="text-white text-2xl" />
            </div>
            <Title level={4} className="text-gradient bg-gradient-to-r from-green-500 to-blue-500 bg-clip-text text-transparent">
              批量兑换完成
            </Title>
          </div>
          <div className="mt-4 max-w-md mx-auto">
            <Alert
              message={
                <div className="py-1">
                  <div className="grid grid-cols-3 gap-4 text-center my-2">
                    <div className="bg-blue-50 py-3 px-2 rounded-xl">
                      <div className="text-lg font-bold text-blue-600">{importResults.total}</div>
                      <div className="text-xs text-gray-600">总计</div>
                    </div>
                    <div className="bg-green-50 py-3 px-2 rounded-xl">
                      <div className="text-lg font-bold text-green-600">{importResults.success}</div>
                      <div className="text-xs text-gray-600">成功</div>
                    </div>
                    <div className="bg-red-50 py-3 px-2 rounded-xl">
                      <div className="text-lg font-bold text-red-600">{importResults.failed}</div>
                      <div className="text-xs text-gray-600">失败</div>
                    </div>
                  </div>
                </div>
              }
              type="success"
              showIcon={false}
              className="border-0 bg-gradient-to-r from-green-50 to-blue-50"
            />
          </div>
          <div className="flex justify-end pt-4 border-t border-gray-100 mt-6">
            <Button
              type="primary"
              onClick={() => {
                // 获取成功兑换的学生ID
                const successStudentIds = keyData
                  .filter(item => item.valid && item.studentId && item.selected && item.message === '兑换成功')
                  .map(item => item.studentId as number);

                if (successStudentIds.length > 0 && onGoToAssignPoints) {
                  onGoToAssignPoints(successStudentIds);
                } else {
                  handleClose();
                }
              }}
              className="w-full sm:w-auto justify-center px-6"
              icon={<GiftOutlined />}
            >
              前往分配
            </Button>
          </div>
        </div>
      )}
    </Modal>
  );
};
