import React, { useState } from 'react';
import { Tag } from 'antd';
import { BaseReviewComponent, BaseReviewItem } from './BaseReviewComponent';
import { participationAuditApi, AuditResult, activityApi } from '../../../../lib/api/activity';
import request from '../../../../lib/request';

// 参赛作品状态常量
export const ParticipationStatus = {
  CANCELLED: 0,    // 已取消
  SUBMITTED: 1,    // 已报名(旧版)
  APPROVED: 2,     // 已审核通过
  REJECTED: 3,     // 已拒绝
  REVIEWING: 4,    // 评审中
  AWARDED: 5,      // 已获奖
  PENDING: 6       // 审核中(新版)
};

// 内容类型常量
export const ContentType = {
  WORK: 1,     // 作品
  IMAGE: 2,    // 图片
  OTHER: 3     // 其他内容
};

// 参赛作品审核项接口
export interface ParticipationReviewItem extends BaseReviewItem {
  activityId: number;
  workId: number;
  userId: number;
  contentType: number;
  category?: string;
  isAwarded?: boolean;
  activityName?: string;
  userName?: string;
  userAvatarUrl?: string;

  // 作品信息
  work?: {
    id: number;
    title?: string;
    description?: string;
    type?: number;
    coverImage?: string;
    url?: string;
    prompt?: string;
  };
}

const ParticipationReviewComponent: React.FC = () => {
  const [worksMap, setWorksMap] = useState<Record<number, any>>({});

  // 格式化时间
  const formatDate = (dateString: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
  };

  // 获取待审核的参赛作品列表
  const fetchParticipationList = async (): Promise<ParticipationReviewItem[]> => {
    try {
      console.log('API定义的AuditResult常量值:', AuditResult);
      console.log('组件定义的ParticipationStatus常量值:', ParticipationStatus);

      // 获取待审核的参赛作品
      const pendingRes = await participationAuditApi.getReviewList();
      const pendingItems = pendingRes?.data?.data || [];
      console.log('获取待审核的参赛作品:', pendingItems);

      // 获取已审核通过的参赛作品 - AuditResult.APPROVED = 1 (表示通过)
      const approvedRes = await participationAuditApi.getAuditList({ result: AuditResult.APPROVED });
      console.log('获取已审核通过的参赛作品:', approvedRes);
      const approvedItems = approvedRes?.data?.data || [];

      // 获取已拒绝的参赛作品 - AuditResult.REJECTED = 2 (表示拒绝)
      const rejectedRes = await participationAuditApi.getAuditList({ result: AuditResult.REJECTED });
      console.log('获取已拒绝的参赛作品:', rejectedRes);
      const rejectedItems = rejectedRes?.data?.data || [];

      // 收集所有需要获取详情的活动ID和作品ID
      const activityIds = new Set<number>();
      const workIds = new Set<number>();
      const activityWorkIds = new Set<number>();

      [...pendingItems, ...approvedItems, ...rejectedItems].forEach(item => {
        if (item.activityId) activityIds.add(item.activityId);
        if (item.workId) workIds.add(item.workId);
        if (item.id) activityWorkIds.add(item.id);
        if (item.activityWorkId) activityWorkIds.add(item.activityWorkId);
      });

      // 获取所有活动的详情
      const activitiesMap: Record<number, any> = {};
      await Promise.all(
        Array.from(activityIds).map(async (id) => {
          try {
            const res = await activityApi.getActivityInfo(id);
            if (res?.data) {
              // 处理新的API响应结构
              activitiesMap[id] = res.data.data || res.data;
            }
          } catch (error) {
            console.error(`获取活动 ${id} 详情失败:`, error);
          }
        })
      );

      // 获取所有作品的详情
      const activityWorksMap: Record<number, any> = {};
      await Promise.all(
        Array.from(activityWorkIds).map(async (id) => {
          try {
            // 假设此接口返回活动作品关联详情，包括作品信息
            const res = await activityApi.getActivityWorkListById(id);
            if (res?.data) {
              // 处理新的API响应结构
              activityWorksMap[id] = res.data.data || res.data;
            }
          } catch (error) {
            console.error(`获取作品详情失败:`, error);
          }
        })
      );

      // 格式化待审核的参赛作品
      const pendingList = pendingItems.map((item: {
        id: number;
        activityId: number;
        workId: number;
        userId: number;
        contentType: number;
        category?: string;
        isAwarded?: boolean;
        createTime: string;
        updateTime?: string;
      }) => {
        const activity = activitiesMap[item.activityId] || {};
        const workDetail = activityWorksMap[item.id]?.work || {};

        return {
          id: item.id,
          activityId: item.activityId,
          workId: item.workId,
          userId: item.userId,
          contentType: item.contentType,
          category: item.category,
          isAwarded: item.isAwarded,
          status: ParticipationStatus.PENDING, // 审核中
          createTime: item.createTime,
          updateTime: item.updateTime,
          activityName: activity.name,
          userName: workDetail.author?.nickName,
          userAvatarUrl: workDetail.author?.avatarUrl,
          work: {
            id: item.workId,
            title: workDetail.title,
            description: workDetail.description,
            type: workDetail.type,
            coverImage: workDetail.coverImage || workDetail.url,
            url: workDetail.url,
            prompt: workDetail.prompt
          }
        };
      });

      // 格式化已审核的参赛作品
      const auditedList = [...approvedItems, ...rejectedItems].map((item: {
        id: number;
        activityId: number;
        workId: number;
        activityWorkId: number;
        userId: number;
        result: number;
        createTime: string;
        updateTime?: string;
        auditorName?: string;
      }) => {
        const activity = activitiesMap[item.activityId] || {};
        const workDetail = activityWorksMap[item.activityWorkId]?.work || {};

        // 状态映射 - 基于API定义的AuditResult常量
        // AuditResult.APPROVED = 1 表示通过，映射到 ParticipationStatus.APPROVED = 2
        // AuditResult.REJECTED = 2 表示拒绝，映射到 ParticipationStatus.REJECTED = 3
        let status;
        if (item.result === AuditResult.APPROVED) {  // 值为1，表示"审核通过"
          status = ParticipationStatus.APPROVED;     // 值为2，已通过
        } else if (item.result === AuditResult.REJECTED) {  // 值为2，表示"审核拒绝" 
          status = ParticipationStatus.REJECTED;     // 值为3，已拒绝
        } else {
          status = ParticipationStatus.SUBMITTED;    // 默认为已提交
        }

        console.log('审核项结果映射:', {
          id: item.id,
          原始结果: item.result,
          映射状态: status,
          结果说明: item.result === 1 ? '审核通过' : item.result === 2 ? '审核拒绝' : '其他'
        });

        return {
          id: item.id,
          activityId: item.activityId,
          workId: item.workId,
          activityWorkId: item.activityWorkId,
          userId: item.userId,
          contentType: workDetail.type === 'image' ? ContentType.IMAGE : ContentType.WORK,
          status: status,
          createTime: item.createTime,
          updateTime: item.updateTime,
          activityName: activity.name,
          userName: workDetail.author?.nickName || item.auditorName,
          userAvatarUrl: workDetail.author?.avatarUrl,
          work: {
            id: item.workId,
            title: workDetail.title,
            description: workDetail.description,
            type: workDetail.type,
            coverImage: workDetail.coverImage || workDetail.url,
            url: workDetail.url,
            prompt: workDetail.prompt
          }
        };
      });

      // 合并并返回所有结果
      const result = [...pendingList, ...auditedList];
      console.log('最终列表项数:', result.length, '包括待审核:', pendingList.length, '已审核:', auditedList.length);
      return result;
    } catch (error) {
      console.error('获取参赛作品列表失败:', error);
      return [];
    }
  };

  // 获取参赛作品审核记录
  const fetchParticipationAuditRecords = async (id: number) => {
    try {
      const res = await participationAuditApi.getWorkAudits(id);
      console.log('获取审核记录:', res);
      return res?.data?.data || [];
    } catch (error) {
      console.error('获取审核记录失败:', error);
      return [];
    }
  };

  // 执行参赛作品审核
  const reviewParticipation = async (id: number, isApproved: boolean, reason?: string) => {
    try {
      return await participationAuditApi.reviewParticipation(id, {
        isApproved,
        reason
      });
    } catch (error) {
      console.error('审核参赛作品失败:', error);
      throw error;
    }
  };

  // 获取作品类型标签
  const getContentTypeTag = (type: number) => {
    const typeMap: Record<number, { text: string; color: string }> = {
      1: { text: '作品', color: 'blue' },
      2: { text: '图片', color: 'green' },
      3: { text: '其他', color: 'default' },
    };
    const typeInfo = typeMap[type] || { text: '未知', color: 'default' };
    return <Tag color={typeInfo.color}>{typeInfo.text}</Tag>;
  };

  // 自定义渲染参赛作品列表项
  const renderParticipationListItem = (item: ParticipationReviewItem, isSelected: boolean, onSelect: () => void) => {
    return (
      <div
        key={item.id}
        onClick={onSelect}
        className={`p-3 border border-gray-100 rounded-xl mb-2 cursor-pointer transition-colors
          ${isSelected ? 'bg-blue-50' : 'hover:bg-gray-50'}`}
      >
        <div className="flex items-center gap-2">
          <h3 className="font-medium text-gray-800 mb-1 text-sm flex-1">
            {item.work?.title || `作品ID: ${item.workId}`}
          </h3>
          {getContentTypeTag(item.contentType)}
        </div>
        <div className="flex items-center justify-between text-xs text-gray-500">
          <span>活动: {item.activityName}</span>
          <span>{formatDate(item.createTime)}</span>
        </div>
      </div>
    );
  };

  // 自定义渲染参赛作品详情
  const renderParticipationDetail = (item: ParticipationReviewItem) => {
    return (
      <div className="bg-blue-50/50 rounded-xl p-6">
        <h1 className="text-xl font-bold text-gray-800 mb-3">
          {item.work?.title || `作品ID: ${item.workId}`}
        </h1>
        <div className="flex items-center gap-4 text-sm text-gray-500 flex-wrap">
          <span>提交者: {item.userName || `用户ID: ${item.userId}`}</span>
          <span>•</span>
          <span>提交时间: {formatDate(item.createTime)}</span>
          <span>•</span>
          <span>活动: {item.activityName || `活动ID: ${item.activityId}`}</span>
          {getContentTypeTag(item.contentType)}
        </div>

        {item.work?.description && (
          <div className="mt-4 text-sm text-gray-700 bg-white p-4 rounded-lg border border-gray-100">
            <p>{item.work.description}</p>
          </div>
        )}

        {(item.work?.coverImage || item.work?.url) && (
          <div className="mt-4">
            <img
              src={item.work.coverImage || item.work.url}
              alt="作品封面"
              className="w-full max-w-[400px] rounded-lg"
            />
          </div>
        )}

        {item.work?.prompt && (
          <div className="mt-4 text-sm text-gray-700">
            <h3 className="font-medium">AI提示词:</h3>
            <div className="bg-white p-4 rounded-lg border border-gray-100 mt-2">
              {item.work.prompt}
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <BaseReviewComponent<ParticipationReviewItem>
      title="参赛作品审核"
      fetchList={fetchParticipationList}
      reviewApi={reviewParticipation}
      getItemName={(item) => item.work?.title || `作品ID: ${item.workId}`}
      getItemCreator={(item) => item.userName || `用户ID: ${item.userId}`}
      renderListItem={renderParticipationListItem}
      renderDetailView={renderParticipationDetail}
      fetchAuditRecords={fetchParticipationAuditRecords}
      reviewableStatuses={[6]}
      customFilter={(items: ParticipationReviewItem[], filterValue: 'all' | number) => {
        // 对于"已通过"标签(值为1)，特殊处理为查找status=2的项目
        if (filterValue === 1) {
          return items.filter(item => item.status === ParticipationStatus.APPROVED); // 值为2
        }
        // 其他情况保持原有逻辑
        else if (filterValue !== 'all') {
          return items.filter(item => item.status === filterValue);
        }
        return items;
      }}
    />
  );
};

export default ParticipationReviewComponent; 