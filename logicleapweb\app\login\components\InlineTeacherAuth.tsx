'use client'

import { useState, useEffect } from 'react';
import { Form, Input, Select, Upload, Button, message, Typography, theme, AutoComplete } from 'antd';
import { ArrowLeft } from 'lucide-react';
import { PlusOutlined, DeleteOutlined, FileWordOutlined, FileExcelOutlined, FilePdfOutlined, FileImageOutlined, FileOutlined } from '@ant-design/icons';
import teacherApi from '@/lib/api/teacher-auth';
import { uploadApi } from '@/lib/api/upload';
import { schoolApi } from '@/lib/api/school';
// @ts-ignore
import pcaData from '@/public/pca.json';
import { COLORS } from './colors';



// 定义学校接口类型
interface School {
  id: number | string;
  schoolName: string;
  province?: string;
  city?: string;
  district?: string;
}

// 定义省市区数据结构
interface PCADataItem {
  code: string;
  name: string;
  children?: PCADataItem[];
}

interface InlineTeacherAuthProps {
  onSuccess: () => void;
  onBack: () => void;
}

const InlineTeacherAuth: React.FC<InlineTeacherAuthProps> = ({ onSuccess, onBack }) => {
  const [form] = Form.useForm();
  const { token } = theme.useToken();
  
  // 状态
  const [loading, setLoading] = useState(false);
  const [fileList, setFileList] = useState<any[]>([]);
  const [localFiles, setLocalFiles] = useState<{ [uid: string]: File }>({});
  const [error, setError] = useState('');
  const [schoolName, setSchoolName] = useState('');
  const [filteredSchools, setFilteredSchools] = useState<School[]>([]);
  const [province, setProvince] = useState('');
  const [city, setCity] = useState('');
  const [district, setDistrict] = useState('');
  const [cities, setCities] = useState<any[]>([]);
  const [districts, setDistricts] = useState<any[]>([]);
  const [showSchoolDropdown, setShowSchoolDropdown] = useState(false);
  const [windowSize, setWindowSize] = useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 0,
    height: typeof window !== 'undefined' ? window.innerHeight : 0,
  });

  // 窗口大小变化监听
  useEffect(() => {
    function handleResize() {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    }

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // 省市区更新逻辑
  useEffect(() => {
    if (province) {
      const selectedProvince = pcaData.find((p: PCADataItem) => p.name === province);
      if (selectedProvince && selectedProvince.children) {
        if (isMunicipality(province)) {
          // 直辖市的情况
          setCities([]);
          setDistricts(selectedProvince.children[0].children || []);
          if (!city) {
            setCity(province); // 直辖市以省为市
          }
        } else {
          // 普通省份
          setCities(selectedProvince.children);
          if (city) {
            const selectedCity = selectedProvince.children.find((c: any) => c.name === city);
            if (selectedCity && selectedCity.children) {
              setDistricts(selectedCity.children);
            }
          }
        }
      }
    } else {
      setCities([]);
      setDistricts([]);
      setCity('');
      setDistrict('');
    }
  }, [province, city, pcaData]);

  // 监听区县变化，自动查询学校
  useEffect(() => {
    if (province) {
      if ((isMunicipality(province) && district) || 
          (!isMunicipality(province) && city && district)) {
        // 延迟执行，确保UI更新后再查询
        setTimeout(() => {
          fetchSchools();
          // setShowSchoolDropdown(true);
        }, 100);
      }
    }
  }, [province, city, district]);

  // 判断是否是直辖市
  const isMunicipality = (provinceName: string) => {
    return ['北京市', '上海市', '天津市', '重庆市'].includes(provinceName);
  };

  // 提交表单处理
  const handleSubmit = async (values: any) => {
    try {
      // 1.参数校验
      // 校验学校名称
      if (!schoolName) {
        setError('请输入或选择学校');
        message.error('请输入或选择学校');
        return;
      }

      if (!values.realName) {
        setError('请输入您的姓名');
        message.error('请输入您的姓名');
        return;
      }

      // 设置加载状态
      setLoading(true);
      
      // 2.上传文件到OSS
      const attachments = [];
      for (const uid in localFiles) {
        if (fileList.some(file => file.uid === uid)) {
          try {
            const file = localFiles[uid];
            const url = await uploadApi.uploadToOss(file);
            attachments.push({
              url: url,
              name: file.name,
              type: file.type || 'application/octet-stream',
              size: file.size || 0
            });
          } catch (error) {
            console.error('上传文件失败:', error);
            message.error(`文件上传失败，请重试`);
            setLoading(false);
            return;
          }
        }
      }

      // 3.拼接学校信息
      const schoolInfo = `${schoolName}|${province}|${city}|${district}`;
      
      // 构建提交数据
      const submitData = {
        teacherId: Number(localStorage.getItem('userId') || '0'),
        teacherName: values.realName,
        schoolInfo: schoolInfo,
        attachments: attachments
      };
      
      // 4.调用提交接口
      console.log("zww：教师认证提交的参数",submitData);
      
      const response = await teacherApi.submitAuth(submitData);
      
      console.log("zww：教师认证响应的res！",response);
      if (response.data && response.data.code === 200) {
        message.success('教师认证申请已提交，请等待审核');
        onSuccess();
      } else {
        message.error(response.data?.message || (response as any).data.data.message || '认证失败，请稍后重试');
      }
    } catch (error: any) {
      console.error('教师认证失败:', error.response.data.message);
      message.error(error.response.data.message || '认证失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 获取学校列表
  const fetchSchools = async (searchText?: string) => {
    try {
      const params: any = {};

      if (searchText) {
        params.keyword = searchText;
      } else {
        if (province) {
          params.province = province;
          if (!isMunicipality(province) && city) {
            params.city = city;
          }
          if (district) {
            params.district = district;
          }
        }
      }

      const response = await schoolApi.getList(params);

      if (response.data?.code === 200) {
        let schools = response.data?.data || response.data?.list || response.data || [];
        
        // 处理可能的null字符串
        schools = schools.map((school: any) => ({
          ...school,
          province: school.province && school.province !== 'null' ? school.province : '',
          city: school.city && school.city !== 'null' ? school.city : '',
          district: school.district && school.district !== 'null' ? school.district : ''
        }));

        setFilteredSchools(schools);
        // 如果只有一所学校，自动选择
        if (schools.length === 1) {
          handleSchoolSelect(schools[0].schoolName, { data: schools[0] });
        }
      } else {
        console.warn('搜索学校接口返回错误:', response);
        setFilteredSchools([]);
      }
    } catch (error) {
      console.error('获取学校列表失败:', error);
      setFilteredSchools([]);
    }
  };

  // 学校选择处理
  const handleSchoolSelect = (value: any, option: any) => {
    let selectedSchool = option?.data;

    if (!selectedSchool && option?.label && typeof option.value !== 'undefined') {
      selectedSchool = {
        id: option.value,
        schoolName: option.label,
      };
    }

    if (!selectedSchool && typeof value !== 'undefined') {
      if (typeof value === 'string') {
        const currentProvince = province || '';
        const currentCity = city || '';
        const currentDistrict = district || '';

        selectedSchool = {
          id: `custom-${Date.now()}`,
          schoolName: value,
          province: currentProvince,
          city: currentCity,
          district: currentDistrict
        };
      } else {
        selectedSchool = filteredSchools.find(school => school.id === value);
      }
    }

    if (!selectedSchool) {
      console.error('无法找到所选学校的数据:', { value, option });
      return;
    }

    const completeSchoolInfo = filteredSchools.find(
      school => school.id === selectedSchool.id || school.schoolName === selectedSchool.schoolName
    ) || selectedSchool;

    setSchoolName(completeSchoolInfo.schoolName);

    // 处理省市区信息
    const selectedProvince = completeSchoolInfo.province && completeSchoolInfo.province !== 'null'
      ? completeSchoolInfo.province : '';

    if (selectedProvince) {
      setProvince(selectedProvince);

      const selectedCity = isMunicipality(selectedProvince)
        ? selectedProvince
        : (completeSchoolInfo.city && completeSchoolInfo.city !== 'null' ? completeSchoolInfo.city : '');
      setCity(selectedCity);

      const selectedDistrict = completeSchoolInfo.district && completeSchoolInfo.district !== 'null'
        ? completeSchoolInfo.district : '';
      setDistrict(selectedDistrict);
    }

    setShowSchoolDropdown(false);
  };

  // 修改区县选择的处理函数
  const handleDistrictChange = (value: string) => {
    setDistrict(value);
    setSchoolName('');
    setFilteredSchools([]);
    // 区县选择后会触发上面的useEffect，自动查询学校
  };

  // 获取文件图标
  const getFileIcon = (fileType: string) => {
    if (fileType.includes('word') || fileType.includes('docx') || fileType.includes('doc')) {
      return <FileWordOutlined style={{ fontSize: '28px', color: '#2B5797' }} />;
    } else if (fileType.includes('excel') || fileType.includes('xlsx') || fileType.includes('xls') || fileType.includes('csv')) {
      return <FileExcelOutlined style={{ fontSize: '28px', color: '#1D6F42' }} />;
    } else if (fileType.includes('pdf')) {
      return <FilePdfOutlined style={{ fontSize: '28px', color: '#FF0000' }} />;
    } else if (fileType.includes('image/')) {
      return <FileImageOutlined style={{ fontSize: '28px', color: '#FFB400' }} />;
    } else {
      return <FileOutlined style={{ fontSize: '28px', color: '#8C8C8C' }} />;
    }
  };

  // 获取文件扩展名
  const getFileExtension = (fileName: string) => {
    const parts = fileName.split('.');
    if (parts.length > 1) {
      return parts[parts.length - 1].toUpperCase();
    }
    return '';
  };

  // 处理文件删除
  const handleRemoveFile = (file: any) => {
    setFileList(fileList.filter(item => item.uid !== file.uid));

    setLocalFiles(prev => {
      const newLocalFiles = { ...prev };
      delete newLocalFiles[file.uid];
      return newLocalFiles;
    });

    if (file.thumbUrl && file.thumbUrl.startsWith('blob:')) {
      URL.revokeObjectURL(file.thumbUrl);
    }
  };

  // 样式对象
  const modalStyles = {
    sectionTitle: {
      fontSize: windowSize.width <= 576 ? '14px' : '16px',
      fontWeight: 'bold',
      marginBottom: '12px',
      color: 'white',
      position: 'relative' as const,
      paddingLeft: '12px',
      display: 'flex',
      alignItems: 'center',
    },
    sectionTitleBefore: {
      content: '""',
      position: 'absolute' as const,
      left: 0,
      top: '50%',
      transform: 'translateY(-50%)',
      width: '4px',
      height: '16px',
      backgroundColor: token.colorPrimary,
      borderRadius: '2px',
    },
    uploadSection: {
      marginBottom: '24px',
      padding: '16px',
      borderRadius: '12px',
      background: 'rgba(255, 255, 255, 0.1)',
    }
  };

  return (
    <>
      <div className="space-y-6 text-white max-h-[600px] w-full overflow-auto scrollbar-thin scrollbar-thumb-color">
        <div className="flex items-center mb-4">
          <button 
            onClick={onBack} 
            className="mr-4 flex items-center justify-center h-8 w-8 rounded-full bg-white/10 hover:bg-white/20 transition-colors"
          >
            <ArrowLeft size={16} />
          </button>
          <h2 className={`text-xl font-bold ${COLORS.text.white}`}>教师身份认证</h2>
        </div>
        
        <p className={COLORS.text.light_white}>
          请填写以下信息完成教师认证，我们将在1-3个工作日内审核您的资料
        </p>

        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          className="space-y-4"
        >
          {/* 个人信息栏 */}
          <div style={modalStyles.sectionTitle}>
            <span style={modalStyles.sectionTitleBefore}></span>
            个人信息
          </div>
          <Form.Item
            name="realName"
            label={<span className={COLORS.text.white}>姓名</span>}
            rules={[{ required: true, message: '请输入真实姓名' }]}
          >
            <Input 
              placeholder="请输入您的真实姓名" 
              className="bg-white "
            />
          </Form.Item>
          
          {/* 学校信息栏 */}
          <div style={modalStyles.sectionTitle}>
            <span style={modalStyles.sectionTitleBefore}></span>
            学校信息
          </div>

          <Form.Item>
            <AutoComplete
              className="bg-white/10 border-white/20 rounded-lg"
              style={{
                margin: '8px 0 16px',
                width: '100%',
              }}
              placeholder="请输入学校名称搜索，也可直接输入自定义学校名"
              value={schoolName}
              onChange={(value) => {
                if (typeof value === 'string') {
                  setSchoolName(value);
                }
              }}
              onSelect={(value, option) => handleSchoolSelect(value, option)}
              onSearch={(value) => {
                if (value && value.length >= 1) {
                  fetchSchools(value);
                  setShowSchoolDropdown(true);
                }
              }}
              onDropdownVisibleChange={(open) => {
                setShowSchoolDropdown(open);
                if (open && province && (isMunicipality(province) ? true : city) && district) {
                  fetchSchools();
                }
              }}
              open={showSchoolDropdown}
              filterOption={false}
              defaultActiveFirstOption={false}
              notFoundContent={
                filteredSchools.length === 0 ? "未找到相关学校，可直接输入学校名" :
                  !province && !schoolName ? "请先选择省份或直接搜索学校名称" :
                    (!isMunicipality(province) && !city && !schoolName) ? "请先选择城市" :
                      !district && !schoolName ? "请先选择区县" : "请选择学校"
              }
              options={filteredSchools.map(school => {
                const hasDuplicateName = filteredSchools.some(s =>
                  s.schoolName === school.schoolName &&
                  (s.province !== school.province ||
                    s.city !== school.city ||
                    s.district !== school.district)
                );

                const formatLocation = () => {
                  const schoolProvince = school.province && school.province !== 'null' ? school.province : '';
                  const schoolCity = schoolProvince && !isMunicipality(schoolProvince) ?
                    (school.city && school.city !== 'null' ? school.city : '') : '';
                  const schoolDistrict = school.district && school.district !== 'null' ? school.district : '';

                  if (!schoolProvince && !schoolCity && !schoolDistrict) {
                    return '';
                  }

                  return `（${schoolProvince}${schoolCity}${schoolDistrict}）`;
                };

                return {
                  value: hasDuplicateName ?
                    `${school.schoolName}${formatLocation()}` :
                    school.schoolName,
                  label: hasDuplicateName ?
                    `${school.schoolName}${formatLocation()}` :
                    school.schoolName,
                  data: school
                };
              })}
              dropdownStyle={{
                maxHeight: 200,
                overflow: 'auto',
                borderRadius: '12px',
                padding: '8px',
              }}
            />
            <div className="flex gap-2 flex-wrap md:flex-nowrap">
              <Select
                placeholder="省"
                
                value={province || undefined}
                onChange={(value) => {
                  setProvince(value);
                  setCity('');
                  setDistrict('');
                  setSchoolName('');
                  setFilteredSchools([]);
                }}
                className="flex-1 w-full md:w-auto bg-white/10 border-white/20 rounded-lg [&_.ant-select-selection-item]:text-white"
                dropdownStyle={{ borderRadius: '12px' }}
              >
                {pcaData.map((p: PCADataItem) => (
                  <Select.Option key={p.code} value={p.name}>
                    {p.name}
                  </Select.Option>
                ))}
              </Select>

              {!isMunicipality(province) && (
                <Select
                  placeholder="市"
                  value={city || undefined}
                  onChange={(value) => {
                    setCity(value);
                    setDistrict('');
                    setSchoolName('');
                    setFilteredSchools([]);
                  }}
                  disabled={!province}
                  className="flex-1 w-full md:w-auto bg-white/10 border-white/20 rounded-lg [&_.ant-select-selection-item]:text-white"
                  dropdownStyle={{ borderRadius: '12px' }}
                >
                  {cities.map(c => (
                    <Select.Option key={c.code} value={c.name}>
                      {c.name}
                    </Select.Option>
                  ))}
                </Select>
              )}

              <Select
                placeholder="区"
                onChange={handleDistrictChange}
                disabled={!province || (!isMunicipality(province) && !city)}
                value={district}
                className="flex-1 w-full md:w-auto bg-white/10 border-white/20 rounded-lg [&_.ant-select-selection-item]:text-white"
                dropdownStyle={{ borderRadius: '12px' }}
              >
                {districts.map(d => (
                  <Select.Option key={d.code} value={d.name}>
                    {d.name}
                  </Select.Option>
                ))}
              </Select>
            </div>
          </Form.Item>
          
          {/* 附加材料 */}
          <div style={modalStyles.uploadSection}>
            <div style={modalStyles.sectionTitle}>
              <span style={modalStyles.sectionTitleBefore}></span>
              证明材料
            </div>

            <div style={{
              display: 'flex',
              gap: '12px',
              marginTop: '16px',
              width: '100%',
              position: 'relative',
            }}>
              <div
                style={{
                  display: 'flex',
                  gap: '12px',
                  width: '100%',
                  overflowX: 'auto',
                  paddingBottom: '8px',
                  WebkitOverflowScrolling: 'touch',
                  scrollbarWidth: 'auto',
                  whiteSpace: 'nowrap',
                }}
                className="file-scroll-container"
              >
                {fileList.map(file => {
                  const isImage = file.type?.startsWith('image/');
                  if (isImage && file.thumbUrl) {
                    return (
                      <div key={file.uid} className="custom-upload-item" style={{
                        position: 'relative',
                        width: '104px',
                        height: '104px',
                        borderRadius: '12px',
                        display: 'inline-block',
                        flexShrink: 0,
                        overflow: 'hidden'
                      }}>
                        <img
                          src={file.thumbUrl}
                          alt={file.name}
                          style={{
                            width: '100%',
                            height: '100%',
                            objectFit: 'cover'
                          }}
                        />
                        <div className="file-delete-icon"
                          style={{
                            position: 'absolute',
                            top: '0',
                            right: '0',
                            background: 'rgba(0,0,0,0.65)',
                            width: '22px',
                            height: '22px',
                            borderRadius: '0 0 0 8px',
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center',
                            cursor: 'pointer'
                          }}
                          onClick={() => handleRemoveFile(file)}
                        >
                          <DeleteOutlined style={{ color: '#fff', fontSize: '14px' }} />
                        </div>
                      </div>
                    );
                  } else {
                    return (
                      <div key={file.uid} className="custom-file-card"
                        style={{
                          width: '104px',
                          height: '104px',
                          border: '1px solid rgba(255,255,255,0.2)',
                          borderRadius: '12px',
                          padding: '8px',
                          display: 'inline-flex',
                          flexDirection: 'column',
                          alignItems: 'center',
                          justifyContent: 'space-between',
                          position: 'relative',
                          flexShrink: 0,
                          background: 'rgba(255,255,255,0.05)',
                          transition: 'all 0.3s'
                        }}>
                        <div className="file-delete-icon"
                          style={{
                            position: 'absolute',
                            top: '0',
                            right: '0',
                            background: 'rgba(0,0,0,0.65)',
                            width: '22px',
                            height: '22px',
                            borderRadius: '0 0 0 12px',
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center',
                            cursor: 'pointer',
                            transition: 'background 0.2s'
                          }}
                          onClick={() => handleRemoveFile(file)}
                        >
                          <DeleteOutlined style={{ color: '#fff', fontSize: '14px' }} />
                        </div>

                        <div style={{ marginTop: '10px' }}>
                          {getFileIcon(file.type || '')}
                        </div>

                        <div style={{
                          textAlign: 'center',
                          width: '100%',
                          marginTop: '4px',
                          overflow: 'hidden'
                        }}>
                          <Typography.Text
                            ellipsis={{ tooltip: file.name }}
                            style={{ fontSize: '12px', lineHeight: '1.2', color: 'white' }}
                          >
                            {file.name}
                          </Typography.Text>
                          <div style={{
                            fontSize: '11px',
                            color: 'rgba(255,255,255,0.5)',
                            marginTop: '2px',
                            background: 'rgba(255,255,255,0.1)',
                            padding: '0 4px',
                            borderRadius: '4px',
                            display: 'inline-block'
                          }}>
                            {getFileExtension(file.name)}
                          </div>
                        </div>
                      </div>
                    );
                  }
                })}

                {/* 上传按钮 */}
                <Upload
                  showUploadList={false}
                  beforeUpload={(file) => {
                    try {
                      const uid = Date.now().toString();
                      const newFile = {
                        uid: uid,
                        name: file.name,
                        status: 'done',
                        thumbUrl: URL.createObjectURL(file),
                        type: file.type,
                        size: file.size
                      };
                      setFileList([...fileList, newFile]);
                      setLocalFiles(prev => ({
                        ...prev,
                        [uid]: file
                      }));
                    } catch (error) {
                      message.error('文件处理失败，请重试');
                    }
                    return false;
                  }}
                >
                  <button
                    style={{
                      background: 'none',
                      cursor: 'pointer',
                      borderRadius: '12px',
                      padding: '16px',
                      transition: 'all 0.3s',
                      width: '104px',
                      height: '104px',
                      display: 'inline-flex',
                      flexShrink: 0,
                      flexDirection: 'column',
                      justifyContent: 'center',
                      alignItems: 'center',
                      backgroundColor: 'rgba(255,255,255,0.1)',
                      border: '1px dashed rgba(255,255,255,0.3)'
                    }}
                    type="button"
                    disabled={loading}
                  >
                    <PlusOutlined style={{ fontSize: '24px', color: 'white' }} />
                    <div style={{ marginTop: 8, color: 'rgba(255,255,255,0.7)' }}>上传证明</div>
                  </button>
                </Upload>
              </div>
            </div>
            <div className={`text-xs ${COLORS.text.light_primary} mt-2`}>支持图片、PDF或Word文档，不超过10MB</div>
          </div>
          
          {/* 注意事项 */}
          <div className="bg-blue-950/30 border border-blue-800/30 p-4 rounded-lg mt-4">
            <div style={modalStyles.sectionTitle}>
              <span style={{
                ...modalStyles.sectionTitleBefore,
                backgroundColor: '#3b82f6'
              }}></span>
              注意事项
            </div>
            <ul className={`list-none p-0 m-0 ${COLORS.text.light_white} text-sm`}>
              <li className="my-1">• 同一个身份信息仅支持实名认证3个洛基飞跃账号</li>
              <li className="my-1">• 提交审核过后可在沟通群内联系管理员跟进认证</li>
              <li className="my-1">• 审核结果将于1个工作日以短信和站内信的方式通知您</li>
            </ul>
          </div>

          {/* 底部按钮区域 - 使用相对定位并添加足够的底部内边距 */}
          <div className=" flex justify-between w-full pt-4 pr-2 mt-6 ">
            <Button 
              onClick={onBack}
              className={`${COLORS.border.white_dim} ${COLORS.text.primary} hover:${COLORS.border.white_active} hover:text-white/80`}
            >
              返回
            </Button>
            <Button 
              type="primary" 
              htmlType="submit" 
              loading={loading}
              className={`${COLORS.bg.accent} ${COLORS.bg.accent_hover} border-none`}
            >
              提交认证
            </Button>
          </div>
        </Form>
      </div>
    </>
  );
};

export default InlineTeacherAuth; 