'use client'

import { useRouter } from 'next/navigation'
import { Card, Tabs, Select, Button, Empty, Modal, Spin, Carousel, Radio, Tag } from 'antd'
import { PlusOutlined, ArrowLeftOutlined, CodeOutlined, AppstoreOutlined, RobotOutlined, UserOutlined, LeftOutlined, RightOutlined } from '@ant-design/icons'
import { Eye, X, Clock, ArrowRight, ChevronUp, ChevronDown } from 'lucide-react'
import Image from 'next/image'
import { useEffect, useState, useRef } from 'react'
import auth from '@/lib/auth'
import { UserInfo } from '@/types/user'
import { worksApi } from '@/lib/api/works'
import { userApi } from '@/lib/api/user'
import { useSelector } from 'react-redux'
import { useDispatch } from 'react-redux'
import { RootState, setUser } from '@/lib/store'
import { GetNotification } from 'logic-common/dist/components/Notification'
import Link from 'next/link'
import { createScratchUrl } from '@/lib/navigation'
import { motion } from 'framer-motion'
import './carousel.css';  // 需要创建这个文件
import { viewWork } from '@/lib/utils/view-work-modal';
import { filter } from 'lodash'

interface WorkStats {
  totalWorks: number;
  totalWorkViews: number;
  totalImages: number;
  totalImageViews: number;
}

interface WorkItem {
  id: number;
  title: string;
  description: string;
  content: string;
  type: number;
  userId: number;
  status: number;
  coverImage: string;
  createTime: string;
  usageCount: number;
  viewCount: number;
  likeCount: number;
  imageUrl?: string;
  imageWidth?: number;
  imageHeight?: number;
  imageSize?: number;
  prompt?: string;
  screenShotImage?: string;
  editable?: boolean;
  originalWorkId?: number;
  originalAuthorId?: number;
  isDerivative?: number;
  author: {
    nickName: string;
    avatarUrl: string;
  };
  originalAuthor?: {
    nickName: string;
    avatarUrl: string;
    id?: number;
  };
  originalWork?: {
    title: string;
    content: string;
    coverImage: string;
    screenShotImage: string;
    id?: number;
  };
  likeTime?: string;
}

interface Filters {
  type: number | undefined;
  status: string;
  orderBy: string;
  tab?: 'works' | 'images' | 'likes'; // 添加新的tab类型
}

// 添加图片数据接口
interface ImageData {
  id: number;
  prompt: string;
  originalImagePath: string;
  backupImagePath: string;
  width?: number;
  height?: number;
  fileSize?: number;
  uploadTime: string;
  viewCount?: number;
  status?: number;
  createTime?: string;
  author?: {
    nickName: string;
    avatarUrl: string;
  };
}

// 添加自定义箭头组件
const CustomArrow = ({ type, onClick }: { type: 'prev' | 'next', onClick?: () => void }) => (
  <div
    onClick={onClick}
    className={`absolute top-1/2 -translate-y-1/2 cursor-pointer carousel-arrow
      ${type === 'prev' ? 'left-2' : 'right-2'}`}
  >
    {type === 'prev' ? <LeftOutlined /> : <RightOutlined />}
  </div>
);

// 添加一个图片标签组件
const ImageLabel = ({ text }: { text: string }) => (
  <div className="absolute bottom-2 left-2 px-2 py-1 bg-black/50 text-white text-xs rounded">
    {text}
  </div>
);

// 修改获取状态选项的函数
const getStatusOptions = (type: number | undefined, tab: string) => {
  // 如果是点赞标签
  if (tab === 'likes') {
    return [
      { value: 'all', label: '全部内容' },
      { value: '1', label: '作品' },
      { value: '2', label: '图片' },
    ];
  }

  // 如果是图片类型
  if (type === 2) {
    return [
      { value: 'all', label: '全部状态' },
      { value: '0', label: '不公开' },
      { value: '1', label: '公开' },
    ];
  }

  // 默认是作品类型的选项
  return [
    { value: 'all', label: '全部状态' },
    { value: '0', label: '不公开' },
    { value: '1', label: '公开可编辑' },
    { value: '2', label: '公开不可编辑' },
  ];
};

const DEFAULT_AVATAR = 'https://logicleap.oss-cn-guangzhou.aliyuncs.com/base/c2b0f283138abaad873795e1dd8e18b_resized.png'
const DEFAULT_IMAGE = '/images/innovative1.jpg'

export default function WorksManagement() {
  const router = useRouter()
  const userInfo = useSelector((state: RootState) => state.user.userState)
  const dispatch = useDispatch()
  const [works, setWorks] = useState<WorkItem[]>([])
  const [stats, setStats] = useState<WorkStats>({
    totalWorks: 0,
    totalWorkViews: 0,
    totalImages: 0,
    totalImageViews: 0
  })
  const [loading, setLoading] = useState(true)
  const [filters, setFilters] = useState<Filters>({
    type: undefined,
    status: 'all',
    orderBy: 'newest',
    tab: 'works'
  })
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [workToDelete, setWorkToDelete] = useState<WorkItem | null>(null);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewImage, setPreviewImage] = useState<ImageData | null>(null);
  const [mounted, setMounted] = useState(false);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const loadingRef = useRef(null);
  const [publishModalVisible, setPublishModalVisible] = useState(false);
  const [unpublishModalVisible, setUnpublishModalVisible] = useState(false);
  const [workToPublish, setWorkToPublish] = useState<WorkItem | null>(null);
  const [workToUnpublish, setWorkToUnpublish] = useState<WorkItem | null>(null);
  const [publishStatus, setPublishStatus] = useState<number>(1); // 默认可编辑
  const [imageLoading, setImageLoading] = useState(true);
  const [currentSlide, setCurrentSlide] = useState(0);
  const [selectedWork, setSelectedWork] = useState<WorkItem | null>(null)
  // 添加标题展开/收起状态
  const [isTitleExpanded, setIsTitleExpanded] = useState(false);
  const notification = GetNotification();

  useEffect(() => {
    setMounted(true);
  }, []);


  // 添加期格式化函数
  const formatDate = (dateString: string, work: WorkItem | ImageData) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  // 获取作品列表
  const fetchWorks = async () => {
    try {
      setLoading(true);
      const { data: response } = await worksApi.getList({
        type: filters.type,
        status: filters.status === 'all' ? undefined : Number(filters.status),
        userId,
        orderBy: filters.orderBy as 'newest' | 'oldest',
        page: 1,
        size: 10
      });

      if (response.code === 200) {
        // 处理作品列表数据，添加默认作者信息
        const processedWorks = response.data?.map((work: WorkItem) => ({
          ...work,
          coverImage: work.coverImage?.includes('image-placeholder.jpg') ? null : work.coverImage,
          screenShotImage: work.screenShotImage?.includes('image-placeholder.jpg') ? null : work.screenShotImage,
          author: {
            nickName: userInfo.nickName || '未知用户',
            avatarUrl: userInfo.avatarUrl || DEFAULT_AVATAR
          }
        })) || [];

        setWorks(processedWorks);
        setHasMore(response.data.list?.length === 10);
        setPage(1);
      }
    } catch (error) {
      console.error('获取作品列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const user = useSelector((state: RootState) => state.user.userState)
  // 获取作品统计
  const fetchStats = async () => {
    try {
      // 获取作品统计
      const { data: worksResponse } = await worksApi.getStats(userId);
      // 获取图片统计
      const { data: imageResponse } = await worksApi.getImageStats(user.userId);

      if (worksResponse.code === 200 && imageResponse.code === 200) {
        const newStats = {
          totalWorks: Number(worksResponse.data.totalWorks),
          totalWorkViews: Number(worksResponse.data.totalViews),
          totalImages: Number(imageResponse.data?.data.totalImages),
          totalImageViews: Number(imageResponse.data?.data.totalViews)
        };
        setStats(newStats);
      }
    } catch (error) {
      console.error('获取作品统计失败:', error);
      setStats({
        totalWorks: works.length,
        totalWorkViews: calculateTotalWorkViews(works),
        totalImages: 0,
        totalImageViews: 0
      });
    }
  };

  useEffect(() => {
    const savedUser = localStorage.getItem('user');
    if (!savedUser) {
      console.error('未找到用户信息');
      router.push('/home');
      return;
    }

    try {
      const userData = JSON.parse(savedUser);
      if (!userData.userId) {
        console.error('用户数据不完整');
        router.push('/home');
        return;
      }

      dispatch(setUser(userData));

      // 初始化数据
      const initializeData = async () => {
        setLoading(true);
        try {
          if (filters.type === 2) {
            await fetchImageList();
          } else {
            await fetchWorks();
          }
          await fetchStats();
        } catch (error) {
          console.error('获取数据失败:', error);
        } finally {
          setLoading(false);
        }
      };

      initializeData();
    } catch (error) {
      console.error('解析用户数据失败:', error);
      router.push('/home');
    }
  }, []);

  // 修改获取图片列表的方法
  const fetchImageList = async () => {
    try {
      setLoading(true);
      const { data: response } = await worksApi.getAllWorks({
        userId,
        type: 2,
        page: 1,
        orderBy: "newest",
        size: 10,
        status: filters.status === 'all' ? undefined : Number(filters.status)
      });

      console.log('检索状态', Number(filters.status));

      if (response.code === 200) {
        const formattedImages = response.data.list.map((img: ImageData) => ({
          id: img.id,
          title: img.prompt || '未命名图片',
          description: img.prompt,
          type: 2,
          status: img.status !== undefined ? Number(img.status) : 0, // 确保status有明确的值，默认为0(未发布)
          coverImage: img.backupImagePath?.includes('image-placeholder.jpg') ? null : img.backupImagePath,
          imageUrl: img.backupImagePath?.includes('image-placeholder.jpg') ? null : img.backupImagePath,
          // 确保日期格式与作品列表一致
          createTime: img.createTime ? new Date(img.createTime).toISOString() : new Date().toISOString(),
          viewCount: img.viewCount || 0,
          usageCount: 0,
          likeCount: 0,
          author: img.author
        }));

        // 添加去重逻辑
        const uniqueImages = removeDuplicateImages(formattedImages);
        console.log(`原始图片数量: ${formattedImages.length}, 去重后数量: ${uniqueImages.length}`);

        setWorks(uniqueImages);
        setHasMore(formattedImages.length === 10);
        setPage(1);
      }
    } catch (error) {
      console.error('获取图片列表失败11:', error);
    } finally {
      setLoading(false);
    }
  };

  // 添加去重函数
  const removeDuplicateImages = (images: WorkItem[]): WorkItem[] => {
    const uniqueMap = new Map<number, WorkItem>();

    images.forEach(image => {
      if (!uniqueMap.has(image.id)) {
        uniqueMap.set(image.id, image);
      }
    });

    return Array.from(uniqueMap.values());
  };

  // 修改筛选条件变化的 useEffect
  useEffect(() => {
    if (!userInfo?.userId) return;

    // 重置分页状态
    setPage(1);
    setHasMore(true);
    setWorks([]);

    const fetchData = async () => {
      if (filters.tab === 'likes') {
        await fetchLikedList();
      } else if (filters.type === 2) {
        await fetchImageList();
      } else {
        await fetchWorks();
      }
      await fetchStats();
    };

    const debounceTimer = setTimeout(fetchData, 300);
    return () => clearTimeout(debounceTimer);
  }, [filters, userInfo?.userId]);
  const userId = useSelector((state: RootState) => state.user.userState.userId);

  // 获取作品类型对应的图标
  const getTypeIcon = (type: number) => {
    switch (type) {
      case 1:
        return <CodeOutlined className="text-blue-600" />
      case 2:
        return <AppstoreOutlined className="text-blue-600" />
      case 3:
        return <RobotOutlined className="text-blue-600" />
      default:
        return <CodeOutlined className="text-blue-600" />
    }
  }

  const handleCreateWork = async () => {
    const token = localStorage.getItem('token')
    if (!token) {
      notification.error('请先登录')
      router.push('/home')
      return
    }

    // 验证token是否有效
    try {
      console.log('哈哈', userId);

      // 使用userApi.getUserInfo()接口来验证用户登录状态
      const res = await userApi.getUserInfo(userId)
      if (res.code !== 200) {
        notification.error('登录已过期，请重新登录')
        localStorage.removeItem('token')
        localStorage.removeItem('user')
        router.push('/home')
        return
      }

      if (!userInfo?.userId) {
        notification.error('用户信息不完整，请重新登录')
        localStorage.removeItem('token')
        localStorage.removeItem('user')
        router.push('/home')
        return
      }

      // 获取完整的用户状态
      const userData = localStorage.getItem('user');
      const fullUserState = userData ? JSON.parse(userData) : null;

      // 使用 viewWork 打开新窗口
      viewWork({
        content: '', // 新建作品，content 为空
        workId: undefined, // 不设置 workId 表示新建
        userId: userInfo.userId, // 传入当前用户 ID
        isNew: true, // 标记为新建作品
        userState: fullUserState || userInfo // 传入完整的用户状态
      });
    } catch (error) {
      console.error('验证登录状态失败:', error)
      notification.error('验证登录状态失败，请重新登录')
      localStorage.removeItem('token')
      localStorage.removeItem('user')
      router.push('/home')
    }
  }

  const handleEdit = async (work: WorkItem) => {
    if (!userInfo?.userId) {
      notification.error('用户未登录或用户信息不完整')
      return
    }

    try {
      const loadingNotification = notification.loading('正在加载作品...');
      const response = await worksApi.getDetail(work.id);

      if (response?.data?.code === 200 && response.data?.data) {
        const workDetail = response.data.data;
        viewWork({
          content: workDetail.content,
          workId: work.id,
          userId: work.userId
        });
        loadingNotification?.close();
        notification.success('加载成功');
      } else {
        loadingNotification?.close();
        notification.error(response?.data?.message || '获取作品详情失败');
      }
    } catch (error) {
      console.error('获取作品详情失败:', error);
      notification.error('获取作品详情失败');
    }
  }

  const handleDeleteWork = async (work: WorkItem) => {
    try {
      // 先获取作品详情，包括截图
      const response = await worksApi.getDetail(work.id);
      console.log('reponse', response);
      if (response?.data.code === 200 && response.data.data) {
        const workWithScreenshots = {
          ...work,
          screenShotImage: response.data.data.screenShotImage || ''
        };
        setWorkToDelete(workWithScreenshots);
        setDeleteModalVisible(true);
      }
    } catch (error) {
      console.error('获取作品详情失败:', error);
      notification.error('获取作品详情失败');
    }
  };

  const handleConfirmDelete = async () => {
    if (!workToDelete) return;

    try {
      const { data: response } = await worksApi.deleteWork(workToDelete.id);

      if (response.code === 200) {
        notification.success('作品删除成功');
        // 重新获取作品列表和统计数据
        fetchWorks();
        fetchStats();
        setDeleteModalVisible(false);
      } else {
        notification.error(response.message || '删除失败，请重试');
      }
    } catch (error) {
      console.error('删除作品失败:', error);
      notification.error('删除失败，请重试');
    }
  };

  // 修改分组函数
  const groupWorksByDate = (works: WorkItem[]) => {
    const groups: { [key: string]: WorkItem[] } = {};

    works.forEach(work => {
      // 如果是点赞标签页，使用 likeTime 作为分组时间
      const dateToUse = filters.tab === 'likes' ? work.likeTime : work.createTime;
      const date = formatDate(dateToUse || '', work);

      if (!groups[date]) {
        groups[date] = [];
      }
      groups[date].push(work);
    });

    // 创建有序的分组
    const orderedGroups: { [key: string]: WorkItem[] } = {};

    // 获取所有日期并按倒序排序
    const sortedDates = Object.keys(groups).sort((a, b) => {
      const dateA = new Date(a);
      const dateB = new Date(b);
      return dateB.getTime() - dateA.getTime();
    });

    // 按排序后的顺序添加到新对象中
    sortedDates.forEach(date => {
      orderedGroups[date] = groups[date];
    });

    return orderedGroups;
  };

  // 添加计算总浏览量的函数
  const calculateTotalWorkViews = (items: WorkItem[]): number => {
    return items.reduce((sum, work) => sum + (work.viewCount || 0), 0);
  };

  // 修改图片预览处理函数
  const handlePreviewImage = async (id: number) => {
    try {
      // 重置标题展开状态
      setIsTitleExpanded(false);

      // 从当前的 works 列表中找到对应的图片
      const currentImage = works.find(work => work.id === id);
      if (currentImage) {
        // 确保不使用占位图片
        setPreviewImage({
          id: currentImage.id,
          prompt: currentImage.title,
          originalImagePath: currentImage.imageUrl || currentImage.coverImage || '',
          backupImagePath: currentImage.imageUrl || currentImage.coverImage || '',
          width: currentImage.imageWidth,
          height: currentImage.imageHeight,
          fileSize: currentImage.imageSize,
          uploadTime: currentImage.createTime,
          viewCount: currentImage.viewCount,
          // 保存图片作者信息
          author: currentImage.author
        });
        setPreviewVisible(true);
      }
    } catch (error) {
      console.error('获取图片详情失败:', error);
      notification.error('获取图片详情失败');
    }
  };

  const handlePublish = async (work: WorkItem) => {
    try {
      const response = await worksApi.getDetail(work.id);
      if (response?.data.code === 200 && response.data.data) {
        const workWithScreenshot = {
          ...work,
          screenShotImage: response.data.data.screenShotImage || ''
        };
        setWorkToPublish(workWithScreenshot);
        setPublishModalVisible(true);
      }
    } catch (error) {
      console.error('获取作品详情失败:', error);
      notification.error('获取作品详情失败');
    }
  };

  const handleConfirmPublish = async () => {
    if (!workToPublish) return;
    
    try {
      const response = await worksApi.updateWorkStatus(workToPublish.id, 4, publishStatus);
      
      if (response?.data.code === 200) {
        notification.success('作品已提交审核，审核通过后将会发布');
        setWorks(works.map(item => {
          if (item.id === workToPublish.id) {
            return { ...item, status: 4, desiredPublishStatus: publishStatus }; // 设置为审核中状态并保存期望发布状态
          }
          return item;
        }));
        setPublishModalVisible(false);
      } else {
        notification.error(response?.data.message || '发布失败');
      }
    } catch (error: any) {
      console.error('发布作品失败:', error);
      notification.error('发布失败，请重试');
    }
  };

  const handlePublishImage = async (imageId: number) => {
    try {
      const response = await worksApi.updateImageStatus(imageId, 1); // 1 表示公开
      
      if (response?.data.code === 200) {
        notification.success('发布成功');
        // 更新当前列表中的状态
        setWorks(works.map(work => {
          if (work.id === imageId) {
            return {
              ...work,
              status: 1
            }
          }
          return work;
        }));
      } else {
        notification.error(response?.data.message || '发布失败');
      }
    } catch (error) {
      console.error('发布失败:', error);
      notification.error('发布失败，请重试');
    }
  };

  // 修改下架处理函数
  const handleUnpublish = async (work: WorkItem) => {
    try {
      // 如果是图片类型，直接下架
      if (work.type === 2) {
        const response = await worksApi.updateImageStatus(work.id, 0); // 0表示未发布
        
        if (response?.data.code === 200) {
          notification.success('图片已下架');
          // 更新当前列表中的状态
          setWorks(works.map(item => {
            if (item.id === work.id) {
              return { ...item, status: 0 };
            }
            return item;
          }));
          return;
        } else {
          notification.error(response?.data.message || '下架失败');
        }
        return;
      }

      // 如果是作品，先获取作品详情，包括截图
      const response = await worksApi.getDetail(work.id);
      if (response?.data.code === 200 && response.data.data) {
        const workWithScreenshots = {
          ...work,
          screenShotImage: response.data.data.screenShotImage || ''
        };
        setWorkToUnpublish(workWithScreenshots);
        setUnpublishModalVisible(true);
      }
    } catch (error) {
      console.error('处理下架操作失败:', error);
      notification.error('操作失败，请重试');
    }
  };

  // 修改确认下架函数
  const handleConfirmUnpublish = async () => {
    if (!workToUnpublish) return;
    
    try {
      const response = await worksApi.updateWorkStatus(workToUnpublish.id, 0, 0);
      
      if (response?.data.code === 200) {
        notification.success('作品已下架');
        setWorks(works.map(item => {
          if (item.id === workToUnpublish.id) {
            return { ...item, status: 0 };
          }
          return item;
        }));
        setUnpublishModalVisible(false);
      } else {
        notification.error(response?.data.message || '下架失败');
      }
    } catch (error: any) {
      console.error('下架作品失败:', error);
      notification.error('下架失败，请重试');
    }
  };

  // 添加加载更多的监听
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && hasMore && !isLoading && !loading) {
          // console.log('Intersection triggered', { hasMore, isLoading, loading });
          loadMore();
        }
      },
      {
        root: null,
        rootMargin: '100px',
        threshold: 0
      }
    );

    const currentRef = loadingRef.current;
    if (currentRef) {
      observer.observe(currentRef);
    }

    return () => {
      if (currentRef) {
        observer.unobserve(currentRef);
      }
    };
  }, [hasMore, isLoading, loading, page, filters]);

  // 修改加载更多的方法
  const loadMore = async () => {
    try {
      if (isLoading) return;
      setIsLoading(true);

      if (filters.tab === 'likes') {
        try {
          const { data: response } = await worksApi.getLikedList({
            page: page + 1,
            size: 10,
            targetType: filters.type
          });

          if (response.code === 200) {
            const newWorks = response.data.list
              .map((item: any) => {
                if (item.work_like_targetType === 1) {
                  return {
                    id: item.work_id,
                    title: item.work_title,
                    description: item.work_description,
                    type: 1,
                    status: item.work_status,
                    coverImage: item.work_coverImage,
                    createTime: item.work_createTime,
                    viewCount: item.work_viewCount,
                    likeCount: item.work_likeCount,
                    author: {
                      nickName: item.user_nickName || '未知用户',
                      avatarUrl: item.user_avatarUrl || DEFAULT_AVATAR
                    },
                    likeTime: item.likeTime
                  };
                } else {
                  return {
                    id: item.image_id,
                    title: item.image_prompt || '未命名图片',
                    description: item.image_prompt,
                    type: 2,
                    status: item.image_status,
                    coverImage: item.image_backupImagePath,
                    imageUrl: item.image_backupImagePath,
                    createTime: item.image_uploadTime || item.likeTime,
                    viewCount: item.image_viewCount,
                    likeCount: item.image_likeCount,
                    author: {
                      nickName: item.user_nickName || '未知用户',
                      avatarUrl: item.user_avatarUrl || DEFAULT_AVATAR
                    },
                    likeTime: item.likeTime
                  };
                }
              })
              // 过滤掉status为0的作品和图片
              .filter((item: any) => item.status > 0);

            // 使用当前作品列表和新加载的作品列表合并后进行去重
            const combinedWorks = [...works, ...newWorks];
            const uniqueWorks = removeDuplicateImages(combinedWorks);

            // 只有在去重后的数量比原来多才更新状态
            if (uniqueWorks.length > works.length) {
              console.log(`加载更多: 原始作品数量: ${works.length}, 新增作品数量: ${newWorks.length}, 去重后总数量: ${uniqueWorks.length}`);
              setWorks(uniqueWorks);
              setHasMore(newWorks.length === 10);
              setPage(p => p + 1);
            } else {
              console.log('加载更多: 没有新的独特作品');
              setHasMore(false);
            }
          } else {
            setHasMore(false);
          }
        } catch (error) {
          console.error('加载更多失败:', error);
          setHasMore(false);
        }
      } else if (filters.type === 2) {
        try {
          const { data: response } = await worksApi.getAllWorks({
            userId,
            type: 2,
            orderBy: "newest",
            page: page + 1,
            size: 10,
            status: filters.status === 'all' ? undefined : Number(filters.status)
          });

          if (response.code === 200) {
            const newWorks = response.data.list.map((img: ImageData) => ({
              id: img.id,
              title: img.prompt || '未命名图片',
              description: img.prompt,
              type: 2,
              status: img.status !== undefined ? Number(img.status) : 0,
              coverImage: img.backupImagePath?.includes('image-placeholder.jpg') ? null : img.backupImagePath,
              imageUrl: img.backupImagePath?.includes('image-placeholder.jpg') ? null : img.backupImagePath,
              createTime: img.createTime ? new Date(img.createTime).toISOString() : new Date().toISOString(),
              viewCount: img.viewCount || 0,
              usageCount: 0,
              likeCount: 0,
              author: img.author
            }));

            // 使用当前作品列表和新加载的作品列表合并后进行去重
            const combinedWorks = [...works, ...newWorks];
            const uniqueWorks = removeDuplicateImages(combinedWorks);

            // 只有在去重后的数量比原来多才更新状态
            if (uniqueWorks.length > works.length) {
              console.log(`加载更多图片: 原始图片数量: ${works.length}, 新增图片数量: ${newWorks.length}, 去重后总数量: ${uniqueWorks.length}`);
              setWorks(uniqueWorks);
              setHasMore(newWorks.length === 10);
              setPage(p => p + 1);
            } else {
              console.log('加载更多图片: 没有新的独特图片');
              setHasMore(false);
            }
          } else {
            setHasMore(false);
          }
        } catch (error) {
          console.error('加载更多图片失败:', error);
          setHasMore(false);
        }
      } else {
        const { data: response } = await worksApi.getList({
          type: filters.type,
          status: filters.status === 'all' ? undefined : Number(filters.status),
          orderBy: filters.orderBy as 'newest' | 'oldest',
          page: page + 1,
          size: 10
        });

        if (response.code === 200) {
          // 修改这里，处理新加载的作品数据
          const newWorks = (Array.isArray(response.data) ? response.data : response.data || []).map((work: WorkItem) => ({
            ...work,
            coverImage: work.coverImage?.includes('image-placeholder.jpg') ? null : work.coverImage,
            screenShotImage: work.screenShotImage?.includes('image-placeholder.jpg') ? null : work.screenShotImage
          }));

          // 使用当前作品列表和新加载的作品列表合并后进行去重
          const combinedWorks = [...works, ...newWorks];
          const uniqueWorks = removeDuplicateImages(combinedWorks);

          if (uniqueWorks.length > works.length) {
            console.log(`加载更多作品: 原始作品数量: ${works.length}, 新增作品数量: ${newWorks.length}, 去重后总数量: ${uniqueWorks.length}`);
            setWorks(uniqueWorks);
            setHasMore(newWorks.length === 10);
            setPage(p => p + 1);
          } else {
            console.log('加载更多作品: 没有新的独特作品');
            setHasMore(false);
          }
        } else {
          setHasMore(false);
        }
      }
    } catch (error) {
      console.error('加载更多失败:', error);
      setHasMore(false);
    } finally {
      setIsLoading(false);
    }
  };

  // 添加预加载函数
  const preloadImages = (images: WorkItem[]) => {
    images.forEach(work => {
      if (work.coverImage) {
        const img = new window.Image();
        img.src = work.coverImage;
      }
    });
  };

  // 修改轮播图切换处理函数，接收 ref 参数
  const handleSlideChange = (carousel: any, index: number) => {
    if (carousel) {
      carousel.goTo(index);
      setCurrentSlide(index);
    }
  };

  // 添加处理作品点击的函数
  const handleWorkClick = async (work: WorkItem) => {
    try {
      // 调用获取作品详情接口
      const { data: response } = await worksApi.getDetail(work.id);
      console.log('response', response);

      if (response.code === 200) {
        const workDetail = response.data;
        
        // 创建一个新的工作详情对象用于状态更新
        let enhancedWorkDetail = {...workDetail};
        
        // 检查是否有原作品ID，如果有则获取原作品信息
        if (workDetail.originalWorkId) {
          try {
            const { data: originalWorkResponse } = await worksApi.getDetail(workDetail.originalWorkId);
            console.log('originalWorkResponse', originalWorkResponse);
            if (originalWorkResponse.code === 200) {
              enhancedWorkDetail.originalWork = originalWorkResponse.data;
            }
          } catch (error) {
            console.error('获取原作品信息失败:', error);
          }
        }
        
        // 检查是否有原作者ID，如果有则获取原作者信息
        if (workDetail.originalAuthorId) {
          try {
            const originalAuthorResponse = await userApi.getUserInfo(workDetail.originalAuthorId);
            console.log('originalAuthorResponse', originalAuthorResponse);
            if (originalAuthorResponse.code === 200) {
              enhancedWorkDetail.originalAuthor = originalAuthorResponse.data;
            }
          } catch (error) {
            console.error('获取原作者信息失败:', error);
          }
        }

        setSelectedWork(enhancedWorkDetail);
      }
    } catch (error) {
      console.error('获取作品详情失败:', error);
      setSelectedWork(work);
    }
  }

  const fetchLikedList = async () => {
    try {
      console.log("likeList", filters.type);

      setLoading(true);
      const { data: response } = await worksApi.getLikedList({
        page: 1,
        size: 10,
        targetType: filters.type
      });

      if (response.code === 200) {
        const processedWorks = response.data.list?.map((item: any) => {
          // 判断是作品还是图片
          if (item.work_like_targetType === 1) {
            // 处理作品数据
            return {
              id: item.work_id,
              title: item.work_title,
              description: item.work_description,
              type: 1, // 作品类型
              status: item.work_status,
              coverImage: item.work_coverImage,
              createTime: item.work_createTime,
              viewCount: item.work_viewCount,
              likeCount: item.work_likeCount,
              author: {
                nickName: item.user_nickName || '未知用户',
                avatarUrl: item.user_avatarUrl || DEFAULT_AVATAR
              },
              likeTime: item.likeTime
            };
          } else {
            // 处理图片数据
            return {
              id: item.image_id,
              title: item.image_prompt || '未命名图片',
              description: item.image_prompt,
              type: 2, // 图片类型
              status: item.image_status,
              coverImage: item.image_backupImagePath,
              imageUrl: item.image_backupImagePath,
              createTime: item.image_uploadTime || item.likeTime,
              viewCount: item.image_viewCount,
              likeCount: item.image_likeCount,
              author: {
                nickName: item.user_nickName || '未知用户',
                avatarUrl: item.user_avatarUrl || DEFAULT_AVATAR
              },
              likeTime: item.likeTime
            };
          }
        })
          // 过滤掉status为0的作品和图片
          .filter((item: any) => item.status > 0)
          // 点赞列表根据likeTime排序，最新点赞的在前面
          .sort((a: WorkItem, b: WorkItem) => {
            if (!a.likeTime || !b.likeTime) return 0;
            return new Date(b.likeTime).getTime() - new Date(a.likeTime).getTime();
          });

        // 应用去重逻辑
        const uniqueWorks = removeDuplicateImages(processedWorks);
        console.log(`点赞列表: 原始数量: ${processedWorks.length}, 去重后数量: ${uniqueWorks.length}`);

        setWorks(uniqueWorks);
        setHasMore(response.data.list?.length === 10);
        setPage(1);
      }
    } catch (error) {
      console.error('获取点赞列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 添加一个函数来获取操作按钮
  const getActionButtons = (work: WorkItem) => {
    // 如果是在点赞标签页
    if (filters.tab === 'likes') {
      return [
        <Button
          key="view"
          onClick={(e) => {
            e.stopPropagation();
            handleEdit(work);
          }}
          className="btn-modern btn-view"
        >
          查看
        </Button>,
        <Button
          key="unlike"
          onClick={(e) => {
            e.stopPropagation();
            handleUnlike(work);
          }}
          className="btn-modern btn-unlike"
        >
          取消点赞
        </Button>
      ];
    }

    // 如果是在作品或图片标签页
    return [
      <Button
        key="edit"
        onClick={(e) => {
          e.stopPropagation();
          handleEdit(work);
        }}
        className="btn-modern btn-edit"
      >
        编辑
      </Button>,
      work.status === 0 ? (
        <Button
          key="publish"
          onClick={(e) => {
            e.stopPropagation();
            handlePublish(work);
          }}
          className="btn-modern btn-publish"
        >
          发布
        </Button>
      ) : (
        <Button
          key="unpublish"
          onClick={(e) => {
            e.stopPropagation();
            handleUnpublish(work);
          }}
          className="btn-modern btn-unpublish"
        >
          下架
        </Button>
      ),
      <Button
        key="delete"
        onClick={(e) => {
          e.stopPropagation();
          handleDeleteWork(work);
        }}
        className="btn-modern btn-delete"
      >
        删除
      </Button>
    ].filter(Boolean);
  };

  // 添加取消点赞的处理函数
  const handleUnlike = async (work: WorkItem) => {
    try {
      const response = await worksApi.toggleLike(work.id, work.type);
      
      if (response?.data?.code === 200) {
        notification.success('已取消点赞');
        setWorks(works.filter(item => item.id !== work.id));
      } else {
        notification.error(response?.data?.message || '取消点赞失败');
      }
    } catch (error) {
      console.error('取消点赞失败:', error);
      notification.error('取消点赞失败，请重试');
    }
  };

  if (!mounted) {
    return (
      <div className="w-full min-h-screen flex items-center justify-center">
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div className="h-screen flex flex-col overflow-hidden">
      {/* Profile Header Section */}
      <div className="relative bg-gradient-to-r from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
        {/* 背景图片 */}
        <div className="absolute inset-0 w-full h-full">
          <Image
            src="/images/mywork_background.svg"
            alt="背景图片"
            fill
            className="object-cover opacity-50"
            sizes="100vw"
            priority
          />
          <div className="absolute inset-0 bg-gradient-to-b from-transparent to-white/80 dark:to-gray-900/80" />
        </div>

        {/* 内容层 */}
        <div className="container mx-auto px-4 py-8 relative">
          {/* 用户信息区域 */}
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-6 mb-8">
            {/* 左侧用户信息 */}
            <div className="flex items-center gap-6">
              <div className="relative group">
                <div className="w-20 h-20 md:w-24 md:h-24 rounded-2xl overflow-hidden 
                  bg-gradient-to-br from-white to-gray-100
                  border border-gray-200 shadow-lg
                  transform transition-all duration-300 group-hover:scale-105
                  dark:from-gray-800 dark:to-gray-900 dark:border-gray-700">
                  {userInfo?.avatarUrl ? (
                    <img
                      src={userInfo.avatarUrl}
                      alt="用户头像"
                      className="w-full h-full object-cover"
                      sizes="24px"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-700">
                      <UserOutlined className="text-4xl text-gray-400 dark:text-gray-600" />
                    </div>
                  )}
                </div>
                {/* 添加装饰性光晕效果 */}
                <div className="absolute -inset-0.5 bg-gradient-to-r from-blue-500 to-purple-500 opacity-0 group-hover:opacity-15 rounded-2xl blur transition duration-300" />
              </div>

              <div className="space-y-1">
                <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  {userInfo?.nickName || '用户'}
                </h1>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  创作者空间
                </p>
              </div>
            </div>
          </div>

          {/* 在头像下方添加导航和操作按钮 */}
          <div className="flex items-center justify-between mt-4">
            <div className="flex items-center gap-4">
              <Button
                icon={<ArrowLeftOutlined />}
                onClick={() => router.push('/home')}
                className="btn-modern btn-back"
              >
                返回
              </Button>

              {/* 作品/图片分类 */}
              <Tabs
                activeKey={filters.tab || 'works'}
                onChange={(key) => {
                  // 重置滚动位置
                  const mainContent = document.querySelector('.overflow-y-auto');
                  if (mainContent) {
                    mainContent.scrollTop = 0;
                  }

                  // 重置状态
                  setWorks([]);
                  setPage(1);
                  setHasMore(true);

                  // 更新过滤条件
                  setFilters(prev => ({
                    ...prev,
                    tab: key as 'works' | 'images' | 'likes',
                    // 如果切换到点赞标签，重置 type 为 undefined（显示全部）
                    type: key === 'likes' ? undefined : (key === 'images' ? 2 : undefined),
                    status: 'all' // 重置状态筛选
                  }));
                }}
                items={[
                  { key: 'works', label: '作品' },
                  { key: 'images', label: '图片' },
                  { key: 'likes', label: '点赞' },
                ]}
                className="!mb-0 !border-b-0"
                tabBarStyle={{
                  marginBottom: 0,
                }}
                tabBarGutter={24}
              />

              {/* 状态筛选 */}
              <Select
                value={filters.status}
                style={{ width: 180 }}
                onChange={(value) => {
                  // 重置滚动位置
                  const mainContent = document.querySelector('.overflow-y-auto');
                  if (mainContent) {
                    mainContent.scrollTop = 0;
                  }

                  // 重置状态
                  setWorks([]);
                  setPage(1);
                  setHasMore(true);

                  // 更新过滤条件
                  setFilters(prev => ({
                    ...prev,
                    status: value,
                    // 如果是在点赞标签页，根据选择的值更新 type
                    type: prev.tab === 'likes' ? (value === 'all' ? undefined : Number(value)) : prev.type
                  }));
                }}
                options={getStatusOptions(filters.type, filters.tab || 'works')}
                className="!border-gray-200 dark:!border-gray-700"
              />
            </div>

            {/* 右侧统计和创建按钮 */}
            <div className="flex items-center gap-10">
              {/* 统计数据 */}
              <div className="grid grid-cols-4 gap-2">
                <div className="flex flex-col items-center p-2 rounded-xl bg-white/50 backdrop-blur-sm border border-gray-100 shadow-sm
                  transition-all duration-300 hover:shadow-md hover:scale-105
                  dark:bg-gray-800/50 dark:border-gray-700">
                  <div className="text-lg font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                    {stats.totalWorks}
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">作品数</div>
                </div>

                <div className="flex flex-col items-center p-2 rounded-xl bg-white/50 backdrop-blur-sm border border-gray-100 shadow-sm
                  transition-all duration-300 hover:shadow-md hover:scale-105
                  dark:bg-gray-800/50 dark:border-gray-700">
                  <div className="text-lg font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
                    {stats.totalWorkViews}
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">作品被浏览</div>
                </div>

                <div className="flex flex-col items-center p-2 rounded-xl bg-white/50 backdrop-blur-sm border border-gray-100 shadow-sm
                  transition-all duration-300 hover:shadow-md hover:scale-105
                  dark:bg-gray-800/50 dark:border-gray-700">
                  <div className="text-lg font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                    {stats.totalImages}
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">图片数</div>
                </div>

                <div className="flex flex-col items-center p-2 rounded-xl bg-white/50 backdrop-blur-sm border border-gray-100 shadow-sm
                  transition-all duration-300 hover:shadow-md hover:scale-105
                  dark:bg-gray-800/50 dark:border-gray-700">
                  <div className="text-lg font-bold bg-gradient-to-r from-pink-600 to-rose-600 bg-clip-text text-transparent">
                    {stats.totalImageViews}
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">图片被浏览</div>
                </div>
              </div>

              {/* 创建作品按钮 */}
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleCreateWork}
                className="btn-modern btn-create ml-2"
              >
                创建作品
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-y-auto">
        {/* Content */}
        <main className="container mx-auto px-4 py-6 md:px-6">
          {/* Works Grid */}
          <div className="min-h-[500px]">
            {loading ? (
              <div className="text-center py-20">
                <Spin />
              </div>
            ) : works.length > 0 ? (
              <div className="space-y-8">
                {Object.entries(groupWorksByDate(works)).map(([date, dateWorks]) => (
                  <motion.div
                    key={date}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5 }}
                  >
                    <div className="flex items-center mb-4">
                      <h3 className="text-lg font-medium mr-4">{date === formatDate(new Date().toISOString(), works[0]) ? '今天' : date}</h3>
                      <div className="flex-1 border-t border-gray-200"></div>
                    </div>
                    <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
                      {dateWorks.map((work, index) => (
                        <motion.div
                          key={work.id}
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{
                            duration: 0.5,
                            delay: index * 0.1
                          }}
                        >
                          {work.type === 2 ? (
                            <div
                              className="group relative overflow-hidden rounded-lg shadow-lg cursor-pointer bg-white"
                              onClick={() => handlePreviewImage(work.id)}
                            >
                              <div className="aspect-video relative bg-gray-100">
                                {work.coverImage ? (
                                  <Image
                                    src={work.coverImage}
                                    alt={work.title}
                                    fill
                                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                                    className="object-cover transition-transform duration-300 group-hover:scale-110"
                                    placeholder="blur"
                                    blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/4gHYSUNDX1BST0ZJTEUAAQEAAAHIAAAAAAQwAABtbnRyUkdCIFhZWiAH4AABAAEAAAAAAABhY3NwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAQAA9tYAAQAAAADTLQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAlkZXNjAAAA8AAAACRyWFlaAAABFAAAABRnWFlaAAABKAAAABRiWFlaAAABPAAAABR3dHB0AAABUAAAABRyVFJDAAABZAAAAChnVFJDAAABZAAAAChiVFJDAAABZAAAAChjcHJ0AAABjAAAADxtbHVjAAAAAAAAAAEAAAAMZW5VUwAAAAgAAAAcAHMAUgBHAEJYWVogAAAAAAAAb6IAADj1AAADkFhZWiAAAAAAAABimQAAt4UAABjaWFlaIAAAAAAAACSgAAAPhAAAts9YWVogAAAAAAAA9tYAAQAAAADTLXBhcmEAAAAAAAQAAAACZmYAAPKnAAANWQAAE9AAAApbAAAAAAAAAABtbHVjAAAAAAAAAAEAAAAMZW5VUwAAACAAAAAcAEcAbwBvAGcAbABlACAASQBuAGMALgAgADIAMAAxADb/2wBDABQODxIPDRQSEBIXFRQdHx4eHRoaHSQtJSEkMjU1LC0yMi4xODY6NT47Pi0uRGhFS1NWW1xbMkFlbWRYbFBZW1f/2wBDARUXFx4aHR4eHVdRLy8vV1dXV1dXV1dXV1dXV1dXV1dXV1dXV1dXV1dXV1dXV1dXV1dXV1dXV1dXV1f/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAb/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k="
                                    onLoad={(event) => {
                                      const img = event.currentTarget;
                                      img.parentElement?.classList.remove('bg-gray-100');
                                    }}
                                  />
                                ) : (
                                  <div className="w-full h-full flex items-center justify-center bg-gray-100">
                                    <AppstoreOutlined className="text-4xl text-gray-400" />
                                  </div>
                                )}
                              </div>

                              {/* 悬浮信息层 */}
                              <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/0 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                              <div className="absolute bottom-0 left-0 right-0 p-4 transform translate-y-full group-hover:translate-y-0 transition-transform duration-300">
                                <h4 className="text-lg font-semibold text-white mb-2">{work.title}</h4>
                                <div className="flex items-center justify-between text-xs text-gray-200">
                                  <span>{formatDate(work.createTime, work)}</span>
                                  <div className="flex items-center gap-3">
                                    <span className="flex items-center gap-1">
                                      <Eye className="w-4 h-4" />
                                      {work.viewCount || 0}
                                    </span>
                                    {filters.tab !== 'likes' ? (
                                      work.status === 0 ? (
                                        <Button
                                          size="small"
                                          type="primary"
                                          onClick={(e) => {
                                            e.stopPropagation();
                                            handlePublishImage(work.id);
                                          }}
                                          className="btn-modern btn-publish bg-blue-500 hover:bg-blue-600 text-white font-medium"
                                        >
                                          发布
                                        </Button>
                                      ) : (
                                        <Button
                                          size="small"
                                          danger
                                          onClick={(e) => {
                                            e.stopPropagation();
                                            handleUnpublish(work);
                                          }}
                                          className="btn-modern btn-unpublish"
                                        >
                                          下架
                                        </Button>
                                      )
                                    ) : (
                                      <Button
                                        size="small"
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          handleUnlike(work);
                                        }}
                                        className="btn-modern btn-unlike"
                                      >
                                        取消点赞
                                      </Button>
                                    )}
                                  </div>
                                </div>
                              </div>
                            </div>
                          ) : (
                            <Card
                              hoverable
                              onClick={() => handleWorkClick(work)}
                              className="cursor-pointer transition-all duration-200 hover:shadow-lg"
                              cover={
                                <div className="group relative aspect-video overflow-hidden">
                                  <Carousel
                                    ref={(ref) => {
                                      // 为每个轮播图创建一个独立的 ref
                                      if (ref) {
                                        (work as any).carouselRef = ref;
                                      }
                                    }}
                                    dots={{
                                      className: '!bottom-2'
                                    }}
                                    arrows
                                    prevArrow={<CustomArrow type="prev" />}
                                    nextArrow={<CustomArrow type="next" />}
                                    className="group"
                                    beforeChange={(from, to) => setCurrentSlide(to)}
                                    dotPosition="bottom"
                                  >
                                    {work.coverImage && (
                                      <div
                                        className="aspect-video relative cursor-pointer"
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          handleSlideChange((work as any).carouselRef, 0);
                                        }}
                                      >
                                        <img
                                          src={work.coverImage}
                                          alt={`${work.title} 封面`}
                                          className="w-full h-full object-cover"
                                          sizes="300px"
                                        />
                                        <ImageLabel text="作品封面" />
                                      </div>
                                    )}
                                    {work.screenShotImage && (
                                      <div
                                        className="aspect-video relative cursor-pointer"
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          handleSlideChange((work as any).carouselRef, 1);
                                        }}
                                      >
                                        <img
                                          src={work.screenShotImage}
                                          alt={`${work.title} 截图`}
                                          className="w-full h-full object-cover"
                                          sizes="300px"
                                        />
                                        <ImageLabel text="作品截图" />
                                      </div>
                                    )}
                                  </Carousel>
                                </div>
                              }
                              actions={getActionButtons(work)}
                            >
                              <Card.Meta
                                description={
                                  <div className="space-y-2">
                                    <div className="flex items-center gap-2 mb-2">
                                      {getTypeIcon(work.type)}
                                      <h4 className="text-xl font-semibold text-gray-900">{work.title}</h4>
                                    </div>
                                    <p className="text-sm text-gray-500">{work.description || '暂无描述'}</p>
                                    <div className="text-xs text-gray-400 flex justify-between items-center">
                                      <div className="flex items-center gap-2">
                                        <div className="text-gray-500 text-sm">
                                          {work.likeTime ? `点赞于 ${formatDate(work.likeTime, work)}` : formatDate(work.createTime, work)}
                                        </div>
                                        {work.status > 0 && (
                                          work.status === 4 ? (
                                            <Tag color="orange">审核中</Tag>
                                          ) : (
                                            <Tag color={work.status === 1 ? 'green' : 'blue'}>
                                              {work.status === 1 ? '可编辑' : '不可编辑'}
                                            </Tag>
                                          )
                                        )}
                                      </div>
                                      <div className="flex items-center gap-2">
                                        <Eye className="w-4 h-4" />
                                        <span>{work.viewCount || 0}</span>
                                        {work.likeCount > 0 && (
                                          <Tag color="red">{work.likeCount} 赞</Tag>
                                        )}
                                      </div>
                                    </div>
                                  </div>
                                }
                              />
                            </Card>
                          )}
                        </motion.div>
                      ))}
                    </div>
                  </motion.div>
                ))}
              </div>
            ) : (
              <Empty description="暂无作品" />
            )}

            {works.length > 0 && !loading && (
              <div ref={loadingRef} className="py-8 text-center">
                {isLoading ? (
                  <Spin size="small" />
                ) : hasMore ? (
                  <div className="text-gray-500">向下滚动加载更多</div>
                ) : (
                  <div className="text-gray-500">没有更多数据了</div>
                )}
              </div>
            )}
          </div>
        </main>
      </div>

      {/* 删除确认对话框 */}
      <Modal
        title="删除作品"
        open={deleteModalVisible}
        onCancel={() => setDeleteModalVisible(false)}
        footer={[
          <Button
            key="cancel"
            onClick={() => setDeleteModalVisible(false)}
          >
            取消
          </Button>,
          <Button
            key="submit"
            type="primary"
            danger
            onClick={handleConfirmDelete}
          >
            删除
          </Button>
        ]}
        className="delete-modal"
        width="90%"
        style={{
          maxWidth: '1200px',
          margin: '5vh auto',
          top: 0,
          paddingBottom: 0
        }}
        styles={{
          body: {
            padding: '24px',
            height: 'fit-content',
            maxHeight: 'calc(90vh - 110px)',
            overflow: 'hidden'
          }
        }}
        closable={false}
      >
        <div className="flex gap-8 h-full">
          {/* 左侧预览区域 */}
          {workToDelete && (
            <div className="w-[65%] bg-gray-50 p-4 rounded-xl">
              <div className="relative mb-4 w-full">
                <Carousel
                  dots={{
                    className: 'custom-dots !z-[1]'
                  }}
                  arrows
                  prevArrow={<CustomArrow type="prev" />}
                  nextArrow={<CustomArrow type="next" />}
                  className="w-full"
                >
                  {workToDelete.coverImage && (
                    <div className="aspect-video relative w-full">
                      <img
                        src={workToDelete.coverImage}
                        alt={`${workToDelete.title} 封面`}
                        className="w-full h-full object-contain rounded-lg"
                      />
                      <ImageLabel text="作品封面" />
                    </div>
                  )}
                  {workToDelete.screenShotImage && (
                    <div className="aspect-video relative w-full">
                      <img
                        src={workToDelete.screenShotImage}
                        alt={`${workToDelete.title} 截图`}
                        className="w-full h-full object-contain rounded-lg"
                      />
                      <ImageLabel text="作品截图" />
                    </div>
                  )}
                </Carousel>
              </div>
              <div className="flex flex-col gap-2">
                <div className="flex items-center gap-2">
                  {getTypeIcon(workToDelete.type)}
                  <span className="font-medium text-base line-clamp-1">{workToDelete.title}</span>
                </div>
                <p className="text-sm text-gray-500 line-clamp-2">
                  {workToDelete.description || '暂无描述'}
                </p>
                <div className="flex justify-between items-center text-xs text-gray-400 mt-2 pt-2 border-t border-gray-200">
                  <span className="text-gray-500">
                    {formatDate(workToDelete.createTime, workToDelete)}
                  </span>
                  <span className="flex items-center gap-1">
                    <Eye size={14} className="flex-shrink-0" />
                    {workToDelete.viewCount} 次浏览
                  </span>
                </div>
              </div>
            </div>
          )}

          {/* 右侧删除说明 */}
          <div className="w-[35%] flex-shrink-0 min-h-0 flex flex-col">
            <div className="space-y-3 h-full flex flex-col">
              <div className="p-3 bg-red-50 rounded-xl shrink-0">
                <p className="text-sm text-red-600">确定要删除这个作品吗？此操作不可恢复。</p>
              </div>

              <div className="space-y-2 flex-1 min-h-0">
                <h4 className="text-sm font-medium text-gray-700 shrink-0">删除后：</h4>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li className="flex items-start gap-2">
                    <span className="w-1.5 h-1.5 rounded-full bg-red-400 mt-1.5 flex-shrink-0"></span>
                    <span>作品将从你的作品列表中移除</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="w-1.5 h-1.5 rounded-full bg-red-400 mt-1.5 flex-shrink-0"></span>
                    <span>其他用户将无法查看此作品</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="w-1.5 h-1.5 rounded-full bg-red-400 mt-1.5 flex-shrink-0"></span>
                    <span>所有相关的数据将被清除</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </Modal>

      {/* 图片预览 Modal */}
      <div className={`fixed inset-0 z-50 flex items-center justify-center bg-black/60 backdrop-blur-sm p-4 ${previewVisible ? '' : 'hidden'}`}>
        <div className="relative w-[900px] h-[700px] max-h-[85vh] overflow-hidden rounded-[2rem]">
          {/* 关闭按钮 */}
          <button
            onClick={() => setPreviewVisible(false)}
            className="absolute top-6 right-6 z-10 p-2 bg-white/10 hover:bg-white/20 rounded-full transition-colors backdrop-blur-md"
          >
            <X size={20} className="text-white" />
          </button>

          {/* 背景图片 */}
          <div className="absolute inset-0">
            {/* 加载动画 */}
            {imageLoading && (
              <div className="absolute inset-0 flex items-center justify-center bg-gray-900/20 backdrop-blur-sm rounded-[2rem]">
                <div className="w-12 h-12 rounded-full border-2 border-white/20 border-t-white/90 animate-spin" />
              </div>
            )}
            {previewImage && previewImage.originalImagePath && (
              <Image
                src={previewImage.originalImagePath}
                alt={previewImage.prompt || ''}
                fill
                className={`object-cover rounded-[2rem] transition-opacity duration-300 ${imageLoading ? 'opacity-0' : 'opacity-100'
                  }`}
                sizes="(max-width: 1080px) 100vw, 1080px"
                onLoad={() => setImageLoading(false)}
                onError={(e: any) => {
                  e.target.src = '/images/image-placeholder.jpg';
                  setImageLoading(false);
                }}
              />
            )}
            {previewImage && previewImage.backupImagePath && (
              <Image
                src={previewImage.backupImagePath}
                alt={previewImage.prompt || ''}
                fill
                className={`object-cover rounded-[2rem] transition-opacity duration-300 ${imageLoading ? 'opacity-0' : 'opacity-100'
                  }`}
                sizes="(max-width: 1080px) 100vw, 1080px"
                onLoad={() => setImageLoading(false)}
                onError={(e: any) => {
                  e.target.src = '/images/image-placeholder.jpg';
                  setImageLoading(false);
                }}
              />
            )}
            {/* 更柔和的渐变遮罩 */}
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-black/20 rounded-[2rem]" />
          </div>

          {/* 信息层 */}
          {previewImage && (
            <div className="relative h-full flex flex-col justify-between p-8">
              {/* 顶部作者信息 */}
              <div className="bg-white/10 backdrop-blur-md rounded-2xl p-4 w-fit border border-white/20">
                <div className="flex items-center gap-4">
                  <div className="w-16 h-16 rounded-full overflow-hidden relative ring-2 ring-white/30">
                    <Image
                      src={previewImage.author?.avatarUrl || '/images/avatar-placeholder.png'}
                      alt={previewImage.author?.nickName || ''}
                      fill
                      className="object-cover"
                    />
                  </div>
                  <div>
                    <h3 className="text-white font-semibold text-lg">{previewImage.author?.nickName || '未知用户'}</h3>
                    <p className="text-white/70">创作者</p>
                  </div>
                </div>
              </div>

              {/* 底部信息卡片 */}
              <div className="bg-white/10 backdrop-blur-md rounded-2xl p-6 text-white border border-white/20">
                {/* 添加标题展开/收起功能 */}
                <div className="flex items-center justify-between">
                  <h2 className="text-2xl font-bold mb-4 max-w-[90%]">
                    {previewImage.prompt && previewImage.prompt.length > 15 && !isTitleExpanded
                      ? `${previewImage.prompt.substring(0, 15)}...`
                      : previewImage.prompt || '未命名图片'}
                  </h2>
                  {previewImage.prompt && previewImage.prompt.length > 15 && (
                    <button
                      onClick={() => setIsTitleExpanded(!isTitleExpanded)}
                      className="bg-white/20 hover:bg-white/30 text-white rounded-full p-2 transition-colors flex items-center gap-1"
                    >
                      {isTitleExpanded ? "收起" : "展开"} {isTitleExpanded ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
                    </button>
                  )}
                </div>

                {/* 提示词 */}
                {previewImage.prompt && (
                  <div className="mb-4">
                    <div className="flex items-center gap-2 text-white/70 mb-2">
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z" />
                      </svg>
                      提示词
                    </div>
                    <p className="font-mono text-sm text-white/90 bg-black/10 rounded-xl p-3 backdrop-blur-sm">
                      {previewImage.prompt}
                    </p>
                  </div>
                )}

                {/* 底部信息栏 */}
                <div className="flex items-center justify-between mt-4 pt-4 border-t border-white/10">
                  <div className="flex items-center gap-6">
                    <div className="flex items-center gap-2">
                      <Eye className="text-white/70" size={18} />
                      <span className="text-white/90">{previewImage.viewCount || 0}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Clock className="text-white/70" size={18} />
                      <span className="text-white/90">
                        {previewImage.uploadTime ?
                          new Date(previewImage.uploadTime).toLocaleString('zh-CN', {
                            year: 'numeric',
                            month: '2-digit',
                            day: '2-digit',
                            hour: '2-digit',
                            minute: '2-digit'
                          }).replace(/\//g, '-') :
                          '未知时间'
                        }
                      </span>
                    </div>
                  </div>

                  {/* 添加下载按钮 */}

                  <button
                    onClick={() => {
                      const link = document.createElement('a');
                      link.href = previewImage.backupImagePath;
                      const fileName = previewImage.backupImagePath.split('/').pop() || 'image.png';
                      link.download = fileName;
                      document.body.appendChild(link);
                      link.click();
                      document.body.removeChild(link);
                    }}
                    className="flex items-center gap-2 px-4 py-2 bg-white/10 hover:bg-white/20 
                      rounded-xl transition-colors backdrop-blur-sm text-white/90 hover:text-white"
                  >
                    <svg
                      width="18"
                      height="18"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      className="text-white/70"
                    >
                      <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
                      <polyline points="7 10 12 15 17 10" />
                      <line x1="12" y1="15" x2="12" y2="3" />
                    </svg>
                    下载
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 发布确认对话框 */}
      <Modal
        title="发布作品"
        open={publishModalVisible}
        onCancel={() => {
          setPublishModalVisible(false);
          setPublishStatus(1); // 重置状态
        }}
        footer={[
          <Button
            key="cancel"
            onClick={() => {
              setPublishModalVisible(false);
              setPublishStatus(1); // 重置状态
            }}
          >
            取消
          </Button>,
          <Button
            key="submit"
            type="primary"
            onClick={handleConfirmPublish}
          >
            发布
          </Button>
        ]}
        className="publish-modal"
        width="90%"
        style={{
          maxWidth: '1200px',
          margin: '5vh auto',
          top: 0,
          paddingBottom: 0
        }}
        styles={{
          body: {
            padding: '24px',
            height: 'fit-content',
            maxHeight: 'calc(90vh - 110px)',
            overflow: 'hidden'
          }
        }}
        closable={false}
      >
        <div className="flex gap-8 h-full">
          {/* 左侧预览区域 */}
          {workToPublish && (
            <div className="w-[65%] bg-gray-50 p-4 rounded-xl">
              <div className="relative mb-4 w-full">
                <Carousel
                  dots={{
                    className: 'custom-dots !z-[1]'
                  }}
                  arrows
                  prevArrow={<CustomArrow type="prev" />}
                  nextArrow={<CustomArrow type="next" />}
                  className="w-full"
                >
                  {workToPublish.coverImage && (
                    <div className="aspect-video relative w-full">
                      <img
                        src={workToPublish.coverImage}
                        alt={`${workToPublish.title} 封面`}
                        className="w-full h-full object-contain rounded-lg"
                      />
                      <ImageLabel text="作品封面" />
                    </div>
                  )}
                  {workToPublish.screenShotImage && (
                    <div className="aspect-video relative w-full">
                      <img
                        src={workToPublish.screenShotImage}
                        alt={`${workToPublish.title} 截图`}
                        className="w-full h-full object-contain rounded-lg"
                      />
                      <ImageLabel text="作品截图" />
                    </div>
                  )}
                </Carousel>
              </div>
              <div className="flex flex-col gap-2">
                <div className="flex items-center gap-2">
                  {getTypeIcon(workToPublish.type)}
                  <span className="font-medium text-base line-clamp-1">{workToPublish.title}</span>
                </div>
                <p className="text-sm text-gray-500 line-clamp-2">
                  {workToPublish.description || '暂无描述'}
                </p>
                <div className="flex justify-between items-center text-xs text-gray-400 mt-2 pt-2 border-t border-gray-200">
                  <span className="text-gray-500">
                    {formatDate(workToPublish.createTime, workToPublish)}
                  </span>
                  <span className="flex items-center gap-1">
                    <Eye size={14} className="flex-shrink-0" />
                    {workToPublish.viewCount} 次浏览
                  </span>
                </div>
              </div>
            </div>
          )}

          {/* 右侧发布选项 */}
          <div className="w-[35%] flex-shrink-0 min-h-0 flex flex-col">
            <div className="space-y-3 h-full flex flex-col">
              <div className="p-3 bg-blue-50 rounded-xl shrink-0">
                <p className="text-sm text-blue-600">发布后其他用户可以看到这个作品。请选择合适的发布选项。</p>
              </div>

              <div className="space-y-2 flex-1 min-h-0">
                <h4 className="text-sm font-medium text-gray-700 shrink-0">发布选项</h4>
                <Radio.Group
                  value={publishStatus}
                  onChange={e => setPublishStatus(e.target.value)}
                  className="space-y-2 w-full flex flex-col flex-1 min-h-0"
                >
                  <div className="flex flex-col gap-2 w-full flex-1 min-h-0">
                    <Radio value={1} className="!flex items-start w-full p-2 border border-gray-100 rounded-xl hover:bg-gray-50 transition-colors shrink-0">
                      <div className="flex flex-col ml-2">
                        <span className="font-medium text-sm">公开且可编辑</span>
                        <span className="text-xs text-gray-500">其他用户可以查看和复制编辑你的作品</span>
                      </div>
                    </Radio>
                    <Radio value={2} className="!flex items-start w-full p-2 border border-gray-100 rounded-xl hover:bg-gray-50 transition-colors shrink-0">
                      <div className="flex flex-col ml-2">
                        <span className="font-medium text-sm">公开但不可编辑</span>
                        <span className="text-xs text-gray-500">其他用户只能查看，无法编辑你的作品</span>
                      </div>
                    </Radio>
                  </div>
                </Radio.Group>
              </div>
            </div>
          </div>
        </div>
      </Modal>

      {/* 下架确认对话框 */}
      <Modal
        title="下架作品"
        open={unpublishModalVisible}
        onCancel={() => setUnpublishModalVisible(false)}
        footer={[
          <Button
            key="cancel"
            onClick={() => setUnpublishModalVisible(false)}
          >
            取消
          </Button>,
          <Button
            key="submit"
            type="primary"
            danger
            onClick={handleConfirmUnpublish}
          >
            下架
          </Button>
        ]}
      >
        <div className="py-4">
          <p className="text-gray-600 mb-4">确定要下架这个作品吗？下架后其他用户将无法看到这个作品。</p>
          {workToUnpublish && (
            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="relative mb-4">
                <Carousel
                  dots={{
                    className: 'custom-dots !z-[1]'
                  }}
                  arrows
                  prevArrow={<CustomArrow type="prev" />}
                  nextArrow={<CustomArrow type="next" />}
                >
                  {workToUnpublish.coverImage && (
                    <div className="aspect-video relative">
                      <img
                        src={workToUnpublish.coverImage}
                        alt={`${workToUnpublish.title} 封面`}
                        className="w-full h-full object-contain rounded-lg"
                      />
                      <ImageLabel text="作品封面" />
                    </div>
                  )}
                  {workToUnpublish.screenShotImage && (
                    <div className="aspect-video relative">
                      <img
                        src={workToUnpublish.screenShotImage}
                        alt={`${workToUnpublish.title} 截图`}
                        className="w-full h-full object-contain rounded-lg"
                      />
                      <ImageLabel text="作品截图" />
                    </div>
                  )}
                </Carousel>
              </div>
              <div className="flex items-center gap-2 mb-2">
                {getTypeIcon(workToUnpublish.type)}
                <span className="font-medium">{workToUnpublish.title}</span>
              </div>
              <p className="text-sm text-gray-500 mb-2">
                {workToUnpublish.description || '暂无描述'}
              </p>
              <div className="flex justify-between items-center text-xs text-gray-400 mt-2 pt-2 border-t border-gray-200">
                <span className="text-xs text-gray-500">
                  {formatDate(workToUnpublish.createTime, workToUnpublish)}
                </span>
                <span>浏览 {workToUnpublish.viewCount}</span>
              </div>
            </div>
          )}
        </div>
      </Modal>

      {/* 作品详情弹窗 */}
      {selectedWork && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
          <div className="bg-white rounded-2xl w-[800px] max-h-[90vh] overflow-hidden">
            <div className="flex">
              {/* 左侧作品信息 */}
              <div className="w-2/3 p-6">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-2xl font-bold">{selectedWork.title}</h2>
                  <button
                    onClick={() => setSelectedWork(null)}
                    className="p-2 hover:bg-gray-100 rounded-full"
                  >
                    <X size={24} />
                  </button>
                </div>

                {/* 作品图片轮播 */}
                <div className="relative work-slider-container">
                  <Carousel
                    dots={{ className: 'custom-dots' }}
                    arrows
                    prevArrow={<CustomArrow type="prev" />}
                    nextArrow={<CustomArrow type="next" />}
                  >
                    {selectedWork.coverImage && (
                      <div className="relative w-full aspect-video rounded-lg overflow-hidden">
                        <Image
                          src={selectedWork.coverImage}
                          alt={selectedWork.title}
                          fill
                          className="object-cover"
                        />
                        <ImageLabel text="作品封面" />
                      </div>
                    )}
                    {selectedWork.screenShotImage && (
                      <div className="relative w-full aspect-video rounded-lg overflow-hidden">
                        <Image
                          src={selectedWork.screenShotImage}
                          alt={`${selectedWork.title} 截图`}
                          fill
                          className="object-contain bg-gray-50"
                        />
                        <ImageLabel text="作品截图" />
                      </div>
                    )}
                  </Carousel>
                </div>

                {/* 作品描述 */}
                <div className="mb-8">
                  <h4 className="text-sm font-medium text-gray-500 mb-2">描述</h4>
                  <div className="bg-gray-100/80 p-[1px] rounded-2xl">
                    <p className="text-gray-700 bg-gray-50/80 p-4 rounded-2xl shadow-[inset_0_2px_4px_rgba(0,0,0,0.1)]">
                      {selectedWork.description}
                    </p>
                  </div>
                </div>

                {/* 作品统计 */}
                <div className="flex items-center gap-4 text-gray-500">
                  <span className="flex items-center gap-1">
                    <Eye size={16} />
                    {selectedWork.viewCount}
                  </span>
                </div>
              </div>

              {/* 右侧作者信息和操作按钮 */}
              <div className="w-1/3 border-l border-gray-100 p-6 bg-gray-50">


                {/* 原作品信息 */}
                {(selectedWork.originalWorkId || selectedWork.originalAuthorId) && (
                  <div className="mt-6 p-4 bg-blue-50/50 rounded-xl border border-blue-100">
                    <div className="flex items-center gap-2 mb-3">
                      <span className="inline-block bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">
                        衍生作品
                      </span>
                    </div>
                    <div className="text-sm text-gray-600 space-y-2">
                      {selectedWork.originalAuthorId && (
                        <div className="flex items-center gap-2">
                          <span className="text-gray-500">原作者:</span>
                          <div className="flex items-center gap-2">
                            <Image
                              src={selectedWork.originalAuthor?.avatarUrl || DEFAULT_AVATAR}
                              alt={selectedWork.originalAuthor?.nickName || '原作者'}
                              width={24}
                              height={24}
                              className="rounded-full object-cover"
                            />
                            <span>{selectedWork.originalAuthor?.nickName || '原作者'}</span>
                          </div>
                        </div>
                      )}
                      {selectedWork.originalWorkId && (
                        <div className="flex items-center gap-2">
                          <span className="text-gray-500">原作品:</span>
                          <div className="flex-1">
                            {selectedWork.originalWork ? (
                              <div className="flex items-center gap-2">
                                <Image
                                  src={selectedWork.originalWork?.coverImage || selectedWork.originalWork?.screenShotImage || DEFAULT_IMAGE}
                                  alt={selectedWork.originalWork?.title || '原作品'}
                                  width={40}
                                  height={40}
                                  className="rounded-lg object-cover"
                                />
                                <div className="flex-1 min-w-0 max-w-[120px]">
                                  <div className="font-medium truncate" title={selectedWork.originalWork?.title || '原作品'}>
                                    {selectedWork.originalWork?.title || '原作品'}
                                  </div>
                                </div>
                              </div>
                            ) : (
                              <div className="text-sm text-gray-500 italic">
                                <div className="flex items-center gap-2 text-gray-400">
                                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                    <path d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                  </svg>
                                  <span>该作品已被原作者删除</span>
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
} 