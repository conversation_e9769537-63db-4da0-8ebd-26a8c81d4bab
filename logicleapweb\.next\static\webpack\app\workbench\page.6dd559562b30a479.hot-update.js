"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/workbench/page",{

/***/ "(app-pages-browser)/./app/workbench/components/OfficialCourses.tsx":
/*!******************************************************!*\
  !*** ./app/workbench/components/OfficialCourses.tsx ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_Eye_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,Eye,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_Eye_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,Eye,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_Eye_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,Eye,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_Eye_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,Eye,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _OfficialCourses_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./OfficialCourses.css */ \"(app-pages-browser)/./app/workbench/components/OfficialCourses.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst OfficialCourses = (param)=>{\n    let { onBack } = param;\n    _s();\n    const [isExpanded, setIsExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedCourse, setSelectedCourse] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showCourseDetail, setShowCourseDetail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 模拟官方课程数据\n    const [officialCourses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            title: \"IO\",\n            description: \"基础编程入门课程\",\n            coverImage: \"/images/course-placeholder.png\",\n            category: \"official\",\n            difficulty: \"初级\",\n            duration: \"2小时\",\n            studentCount: 0,\n            viewCount: 0,\n            tags: [\n                \"编程基础\"\n            ]\n        },\n        {\n            id: 2,\n            title: \"1\",\n            description: \"进阶编程课程\",\n            coverImage: \"/images/course-placeholder.png\",\n            category: \"official\",\n            difficulty: \"中级\",\n            duration: \"3小时\",\n            studentCount: 0,\n            viewCount: 0,\n            tags: [\n                \"进阶编程\"\n            ]\n        },\n        {\n            id: 3,\n            title: \"Node.js实现开发系列\",\n            description: \"Node.js后端开发完整教程\",\n            coverImage: \"/images/course-placeholder.png\",\n            category: \"official\",\n            difficulty: \"高级\",\n            duration: \"8小时\",\n            studentCount: 0,\n            viewCount: 0,\n            tags: [\n                \"Node.js\",\n                \"后端开发\"\n            ]\n        }\n    ]);\n    // 模拟社区课程数据\n    const [communityCourses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 4,\n            title: \"古色琴声于子\",\n            description: \"音乐编程创作课程\",\n            coverImage: \"/images/course-placeholder.png\",\n            category: \"community\",\n            difficulty: \"中级\",\n            duration: \"4小时\",\n            studentCount: 0,\n            viewCount: 0,\n            tags: [\n                \"音乐\",\n                \"创作\"\n            ]\n        },\n        {\n            id: 5,\n            title: \"000\",\n            description: \"基础算法课程\",\n            coverImage: \"/images/course-placeholder.png\",\n            category: \"community\",\n            difficulty: \"初级\",\n            duration: \"2小时\",\n            studentCount: 0,\n            viewCount: 0,\n            tags: [\n                \"算法\"\n            ]\n        },\n        {\n            id: 6,\n            title: \"游戏系列\",\n            description: \"游戏开发入门教程\",\n            coverImage: \"/images/course-placeholder.png\",\n            category: \"community\",\n            difficulty: \"中级\",\n            duration: \"6小时\",\n            studentCount: 150,\n            viewCount: 0,\n            tags: [\n                \"游戏开发\"\n            ]\n        }\n    ]);\n    // 处理展开全部\n    const handleExpandAll = ()=>{\n        // 添加滑动动画效果\n        const container = document.querySelector(\".official-courses-container\");\n        if (container) {\n            container.classList.add(\"expanding\");\n            setTimeout(()=>{\n                setIsExpanded(true);\n                container.classList.remove(\"expanding\");\n            }, 150);\n        } else {\n            setIsExpanded(true);\n        }\n    };\n    // 处理返回\n    const handleBack = ()=>{\n        if (showCourseDetail) {\n            setShowCourseDetail(false);\n            setSelectedCourse(null);\n        } else if (isExpanded) {\n            // 添加收缩动画效果\n            const container = document.querySelector(\".official-courses-container\");\n            if (container) {\n                container.classList.add(\"collapsing\");\n                setTimeout(()=>{\n                    setIsExpanded(false);\n                    container.classList.remove(\"collapsing\");\n                }, 150);\n            } else {\n                setIsExpanded(false);\n            }\n        } else {\n            onBack === null || onBack === void 0 ? void 0 : onBack();\n        }\n    };\n    // 处理课程点击\n    const handleCourseClick = (course)=>{\n        setSelectedCourse(course);\n        setShowCourseDetail(true);\n    };\n    // 渲染课程卡片\n    const renderCourseCard = (course)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"course-card\",\n            onClick: ()=>handleCourseClick(course),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"course-cover\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"course-placeholder\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Eye_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"course-icon\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                    lineNumber: 159,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"course-info\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"course-title\",\n                            children: course.title\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"course-meta\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"course-difficulty\",\n                                    children: course.difficulty\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 11\n                                }, undefined),\n                                course.studentCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"course-students\",\n                                    children: [\n                                        course.studentCount,\n                                        \"人学习\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, course.id, true, {\n            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n            lineNumber: 154,\n            columnNumber: 5\n        }, undefined);\n    if (showCourseDetail && selectedCourse) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"official-courses-container\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"course-detail-header\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"back-button\",\n                            onClick: handleBack,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Eye_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    size: 20\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"返回\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                            lineNumber: 180,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            children: \"课程详情\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                            lineNumber: 184,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                    lineNumber: 179,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"course-detail-content\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"course-detail-cover\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"course-detail-placeholder\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Eye_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"course-detail-icon\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"course-detail-info\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    children: selectedCourse.title\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"course-description\",\n                                    children: selectedCourse.description\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"course-stats\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"stat-item\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Eye_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        selectedCourse.studentCount,\n                                                        \" 人学习\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                                                    lineNumber: 201,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                                            lineNumber: 199,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"stat-item\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Eye_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                                                    lineNumber: 204,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        selectedCourse.viewCount,\n                                                        \" 次观看\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                                                    lineNumber: 205,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"course-tags\",\n                                    children: selectedCourse.tags.map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"course-tag\",\n                                            children: tag\n                                        }, index, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                                            lineNumber: 211,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"course-actions\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"start-course-btn\",\n                                            children: \"开始学习\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                                            lineNumber: 216,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"preview-course-btn\",\n                                            children: \"预览课程\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                    lineNumber: 187,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n            lineNumber: 178,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (isExpanded) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"official-courses-container expanded\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"expanded-header\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"back-button\",\n                            onClick: handleBack,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Eye_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    size: 20\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"返回\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            children: \"全部课程\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                            lineNumber: 233,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                    lineNumber: 228,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"expanded-content\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"course-section\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"section-title\",\n                                    children: \"官方课程\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                                    lineNumber: 238,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"courses-grid\",\n                                    children: officialCourses.map(renderCourseCard)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                                    lineNumber: 239,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                            lineNumber: 237,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"course-section\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"section-title\",\n                                    children: \"社区课程\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"courses-grid\",\n                                    children: communityCourses.map(renderCourseCard)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                            lineNumber: 244,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                    lineNumber: 236,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n            lineNumber: 227,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"official-courses-container\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"courses-header\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        children: \"官方课程\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                        lineNumber: 258,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"expand-all-btn\",\n                        onClick: handleExpandAll,\n                        children: \"展开全部\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                        lineNumber: 259,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                lineNumber: 257,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"courses-preview\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"course-section\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"section-title\",\n                                children: \"官方课程\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"courses-grid preview\",\n                                children: officialCourses.slice(0, 3).map(renderCourseCard)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                                lineNumber: 267,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                        lineNumber: 265,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"course-section\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"section-title\",\n                                children: \"社区课程\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                                lineNumber: 273,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"courses-grid preview\",\n                                children: communityCourses.slice(0, 3).map(renderCourseCard)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                                lineNumber: 274,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                        lineNumber: 272,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                lineNumber: 264,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n        lineNumber: 256,\n        columnNumber: 5\n    }, undefined);\n};\n_s(OfficialCourses, \"VOinyAeif85qoTk+nommja+DwO8=\");\n_c = OfficialCourses;\n/* harmony default export */ __webpack_exports__[\"default\"] = (OfficialCourses);\nvar _c;\n$RefreshReg$(_c, \"OfficialCourses\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/workbench/components/OfficialCourses.tsx\n"));

/***/ })

});