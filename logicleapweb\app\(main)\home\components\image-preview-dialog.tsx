'use client'

import React, { useState } from 'react'
import Image from 'next/image'
import { Heart, Eye, X, Clock } from 'lucide-react'
import { worksApi } from '@/lib/api/works'
import ossImageProcessor from '@/lib/utils/OssImageProcessor'
import { GetNotification } from 'logic-common/dist/components/Notification'
import { useRouter, usePathname } from 'next/navigation'

// 定义默认头像常量
const DEFAULT_AVATAR = 'https://logicleap.oss-cn-guangzhou.aliyuncs.com/base/c2b0f283138abaad873795e1dd8e18b_resized.png'

interface GalleryImage {
  id: number
  src: string
  title: string
  description?: string
  views?: number
  viewCount?: number
  likeCount?: number
  prompt?: string
  createTime?: string
  author: {
    nickName: string
    avatarUrl: string
  }
}

interface ImagePreviewDialogProps {
  selectedImage: GalleryImage
  onClose: () => void
  userState: {
    isLoggedIn: boolean
    userId?: number
  }
  likedWorks: { [key: number]: boolean }
  setLikedWorks: React.Dispatch<React.SetStateAction<{ [key: number]: boolean }>>
  setShowLoginDialog: (show: boolean) => void
}

const ImagePreviewDialog: React.FC<ImagePreviewDialogProps> = ({
  selectedImage,
  onClose,
  userState,
  likedWorks,
  setLikedWorks,
  setShowLoginDialog
}) => {
  const [imageLoading, setImageLoading] = useState(true)
  const router = useRouter()
  const pathname = usePathname()

  // 获取作者头像的缩略图
  const getAvatarThumbnail = (url: string) => {
    if (!url || url.includes('/images/')) return url;

    return ossImageProcessor.getAvatarThumbnail(url, 128, 90);
  }

  // 获取适合预览的高质量图片
  const getPreviewImage = (url: string) => {
    if (!url) return url;

    try {
      // 判断是否是OSS URL (使用直接的字符串判断)
      const isOssUrl = url.includes('aliyuncs.com') || url.includes('logicleap.oss');
      if (!isOssUrl) {
        return url;
      }

      // 请求比展示尺寸稍大的图片，以适应高清屏幕
      // 容器尺寸为900×700，请求尺寸为1080×840（1.2倍）
      const separator = url.includes('?') ? '&' : '?';
      return `${url}${separator}x-oss-process=image/resize,m_lfit,w_1080,h_840/quality,q_95`;
    } catch (error) {
      console.error('获取预览图片失败:', error);
      return url;
    }
  }

  // 原始图片URL
  const originalImageUrl = selectedImage.src;

  // 获取适合预览的图片URL（适度缩放和压缩，但保持较高质量）
  const previewImageUrl = getPreviewImage(originalImageUrl);

  // 优化后的头像URL
  const optimizedAvatarUrl = getAvatarThumbnail(selectedImage.author.avatarUrl || DEFAULT_AVATAR);

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/60 backdrop-blur-sm p-4">
      <div className="relative w-[900px] h-[700px] max-h-[85vh] overflow-hidden rounded-[2rem]">
        {/* 关闭按钮 */}
        <button
          onClick={onClose}
          className="absolute top-6 right-6 z-10 p-2 bg-white/10 hover:bg-white/20 rounded-full transition-colors backdrop-blur-md"
        >
          <X size={20} className="text-white" />
        </button>

        {/* 背景图片 */}
        <div className="absolute inset-0">
          {/* 加载动画 */}
          {imageLoading && (
            <div className="absolute inset-0 flex items-center justify-center bg-gray-900/20 backdrop-blur-sm rounded-[2rem]">
              <div className="w-12 h-12 rounded-full border-2 border-white/20 border-t-white/90 animate-spin" />
            </div>
          )}
          <Image
            src={previewImageUrl}
            alt={selectedImage.title || '图片预览'}
            fill
            className={`object-cover rounded-[2rem] transition-opacity duration-300 ${imageLoading ? 'opacity-0' : 'opacity-100'
              }`}
            sizes="(max-width: 1080px) 100vw, 1080px"
            onLoad={() => setImageLoading(false)}
            onError={(e) => {
              const target = e.target as HTMLImageElement
              target.src = '/images/image-placeholder.jpg'
              setImageLoading(false)
            }}
          />
          {/* 更柔和的渐变遮罩 */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-black/20 rounded-[2rem]" />
        </div>

        {/* 信息层 */}
        <div className="relative h-full flex flex-col justify-between p-8">
          {/* 顶部作者信息 */}
          <div className="bg-white/10 backdrop-blur-md rounded-2xl p-4 w-fit border border-white/20">
            <div className="flex items-center gap-4">
              <div className="w-16 h-16 rounded-full overflow-hidden relative ring-2 ring-white/30 bg-gray-800/30">
                <Image
                  src={optimizedAvatarUrl}
                  alt={selectedImage.author.nickName}
                  fill
                  className="object-cover"
                  sizes="128px"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = DEFAULT_AVATAR;
                  }}
                />
              </div>
              <div>
                <h3 className="text-white font-semibold text-lg">{selectedImage.author.nickName}</h3>
                <p className="text-white/70">创作者</p>
              </div>
            </div>
          </div>

          {/* 底部信息卡片 */}
          <div className="bg-white/10 backdrop-blur-md rounded-2xl p-6 text-white border border-white/20">
            <h2 className="text-2xl font-bold mb-4">{selectedImage.title}</h2>

            {/* 提示词 */}
            {selectedImage.prompt && (
              <div className="mb-4">
                <div className="flex items-center gap-2 text-white/70 mb-2">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z" />
                  </svg>
                  提示词
                </div>
                <p className="font-mono text-sm text-white/90 bg-black/10 rounded-xl p-3 backdrop-blur-sm">
                  {selectedImage.prompt}
                </p>
              </div>
            )}

            {/* 底部信息栏 */}
            <div className="flex items-center justify-between mt-4 pt-4 border-t border-white/10">
              <div className="flex items-center gap-6">
                <div className="flex items-center gap-2">
                  <Eye className="text-white/70" size={18} />
                  <span className="text-white/90">{selectedImage.viewCount || selectedImage.views || 0} 次浏览</span>
                </div>
                <div className="flex items-center gap-2">
                  <Clock className="text-white/70" size={18} />
                  <span className="text-white/90">
                    {selectedImage.createTime ?
                      new Date(selectedImage.createTime).toLocaleString('zh-CN', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit'
                      }).replace(/\//g, '-') :
                      '未知时间'
                    }
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <Heart className={likedWorks[selectedImage.id] ? "text-red-500" : "text-white/70"} size={18} />
                  <span className="text-white/90">{selectedImage.likeCount || 0} 点赞</span>
                </div>
              </div>

              <div className="flex items-center gap-4">
                <button
                  onClick={async (e) => {
                    e.stopPropagation();
                    const notification = GetNotification();

                    if (!userState.isLoggedIn) {
                      const redirectUrl = pathname;
                      router.push(`/login?redirect=${encodeURIComponent(redirectUrl)}`);
                      return;
                    }

                    // 获取当前点赞状态
                    const isCurrentlyLiked = likedWorks[selectedImage.id];

                    // 更新点赞状态
                    setLikedWorks(prev => ({
                      ...prev,
                      [selectedImage.id]: !isCurrentlyLiked
                    }));

                    // 更新显示的点赞数
                    const newLikeCount = (selectedImage.likeCount || 0) + (isCurrentlyLiked ? -1 : 1);
                    selectedImage.likeCount = newLikeCount;

                    try {
                      // 在后台发送请求
                      const response = await worksApi.toggleLike(selectedImage.id, 2);
                      if (response?.data?.code === 200) {
                        notification.success(isCurrentlyLiked ? '取消点赞成功' : '点赞成功');
                      }
                    } catch (error) {
                      // 如果失败，恢复状态
                      setLikedWorks(prev => ({
                        ...prev,
                        [selectedImage.id]: isCurrentlyLiked
                      }));
                      selectedImage.likeCount = newLikeCount - (isCurrentlyLiked ? -1 : 1);
                      notification.error('操作失败，请重试');
                    }
                  }}
                  className={`flex items-center justify-center w-12 h-12 rounded-xl transition-all duration-200 ${likedWorks[selectedImage.id]
                    ? 'bg-red-500/20 text-red-500 hover:bg-red-500/30'
                    : 'bg-white/10 text-white/70 hover:bg-white/20 hover:text-red-500'
                    }`}
                >
                  {likedWorks[selectedImage.id] ? (
                    <Heart size={24} fill="currentColor" />
                  ) : (
                    <Heart size={24} />
                  )}
                </button>

                {/* 添加下载按钮 */}
                <button
                  onClick={() => {
                    // 创建一个a标签来下载图片，使用原始图片URL而不是预览图片URL
                    const link = document.createElement('a');
                    link.href = originalImageUrl;
                    // 从URL中获取文件名,如果没有则使用默认名称
                    const fileName = originalImageUrl.split('/').pop() || 'image.png';
                    link.download = fileName;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                  }}
                  className="flex items-center gap-2 px-4 py-2 bg-[#4766C2] hover:bg-[#3d57a7] 
                    rounded-xl transition-colors text-white font-medium shadow-lg shadow-blue-500/20"
                >
                  <svg
                    width="18"
                    height="18"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    className="text-white"
                  >
                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
                    <polyline points="7 10 12 15 17 10" />
                    <line x1="12" y1="15" x2="12" y2="3" />
                  </svg>
                  下载
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ImagePreviewDialog 