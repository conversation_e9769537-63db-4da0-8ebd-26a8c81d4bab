'use client'

import React, { useRef, useState, useEffect } from 'react'
import { Upload, X, Check, AlertCircle, FileJson, Info, Eye, PenTool, Award, CheckCircle } from 'lucide-react'
import { GetNotification } from 'logic-common/dist/components/Notification'
import ActivityJsonExample from './ActivityJsonExample'
import EditorJson<PERSON>enderer from './EditorJsonRenderer'
import { selectUserState } from '@/lib/store'
import { useSelector } from 'react-redux'

interface ActivityData {
  title?: string
  startTime?: string
  endTime?: string
  bannerImage?: string
  organizer?: string
  detailContent?: string
  rulesContent?: string // Added for specific key
  awardsContent?: string // Added for specific key
  editorJson?: any 
  targetTab?: 'detail' | 'rules' | 'awards' | 'media'
}

// State for a single tab's file data
interface FileState {
  file: File | null;
  jsonPreview: string;
  parsedData: ActivityData | null;
  showPreview: boolean;
}

// Initial empty state for a tab
const initialFileState: FileState = {
  file: null,
  jsonPreview: "",
  parsedData: null,
  showPreview: false,
};

interface ActivityImporterProps {
  isOpen: boolean
  onClose: () => void
  onImport: (activityData: ActivityData) => Promise<void> | void // Allow async onImport
  activeTab?: 'detail' | 'rules' | 'awards'
}

const ActivityImporter: React.FC<ActivityImporterProps> = ({ 
  isOpen, 
  onClose, 
  onImport, 
  activeTab = 'detail' 
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [importing, setImporting] = useState(false);
  const [showExample, setShowExample] = useState(false);
  const [targetTab, setTargetTab] = useState<'detail' | 'rules' | 'awards'>(activeTab);
  const notification = GetNotification();

  // State to hold file info for each tab
  const [filesData, setFilesData] = useState<Record<'detail' | 'rules' | 'awards', FileState>>({
    detail: { ...initialFileState },
    rules: { ...initialFileState },
    awards: { ...initialFileState },
  });

  // Derived state for the current target tab's data
  const currentTabData = filesData[targetTab];

  // Reset state when modal opens or activeTab changes
  useEffect(() => {
    if (isOpen) {
      setTargetTab(activeTab);
      // Reset all file states when modal opens
      setFilesData({
        detail: { ...initialFileState },
        rules: { ...initialFileState },
        awards: { ...initialFileState },
      });
      setImporting(false);
      setShowExample(false);
    }
  }, [isOpen, activeTab]);

  // Update specific tab's state
  const updateCurrentTabData = (updates: Partial<FileState>) => {
    setFilesData(prev => ({
      ...prev,
      [targetTab]: { ...prev[targetTab], ...updates },
    }));
  };

  // --- Modified File Handling Functions ---

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const selectedFile = e.target.files[0];
      updateCurrentTabData({ file: selectedFile });
      await previewJsonFile(selectedFile);
    }
  };

  const handleDrop = async (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      const droppedFile = e.dataTransfer.files[0];
      const extension = droppedFile.name.split('.').pop()?.toLowerCase();
      if (extension !== 'json') {
        notification.error('只支持JSON格式文件');
        return;
      }
      updateCurrentTabData({ file: droppedFile });
      await previewJsonFile(droppedFile);
    }
  };

  const previewJsonFile = async (jsonFile: File) => {
    let newParsedData: ActivityData | null = null;
    let newJsonPreview = "";
    let newShowPreview = false;

    try {
      const text = await jsonFile.text();
      newJsonPreview = text;
      try {
        const data = JSON.parse(text);
        const isEditorJson = data.blocks && Array.isArray(data.blocks);
        if (isEditorJson) {
          newParsedData = {
            editorJson: data,
            title: extractTitleFromEditorJson(data)
          };
        } else {
          newParsedData = data;
        }
        newShowPreview = true;
      } catch (parseError) {
        console.error('JSON解析错误:', parseError);
        notification.error(`文件 ${jsonFile.name} JSON格式不正确`);
        newParsedData = null; // Ensure parsedData is null on error
      }
    } catch (error) {
      console.error('读取文件错误:', error);
      notification.error(`读取文件 ${jsonFile.name} 失败`);
      newParsedData = null; // Ensure parsedData is null on error
    }
    // Update state once after all processing
    updateCurrentTabData({ 
        jsonPreview: newJsonPreview, 
        parsedData: newParsedData, 
        showPreview: newShowPreview 
    });
  };

  const handleRemoveFile = (tabKey: 'detail' | 'rules' | 'awards') => {
    setFilesData(prev => ({
      ...prev,
      [tabKey]: { ...initialFileState }
    }));
    // Clear the file input visually if removing the current tab's file
    if (tabKey === targetTab && fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // --- Tab Switching --- (No longer clears file)
  const handleTargetTabChange = (newTab: 'detail' | 'rules' | 'awards') => {
    if (targetTab !== newTab) {
      setTargetTab(newTab);
      // Reset the visual file input if the new tab has no file selected
      // This prevents showing the previous tab's file name in the input
      if (!filesData[newTab].file && fileInputRef.current) {
           fileInputRef.current.value = '';
      }
    }
  };

  // --- Modified Import Logic ---
  const handleImport = async () => {
    const filesToImport = Object.entries(filesData)
      .filter(([tab, data]) => data.file !== null && data.parsedData !== null) 
      .map(([tab, data]) => ({ tab: tab as 'detail' | 'rules' | 'awards', data: data.parsedData!, file: data.file! }));

    if (filesToImport.length === 0) {
      notification.warning('请至少为一个标签页选择有效的JSON文件');
      return;
    }

    setImporting(true);
    let successCount = 0;
    let errorCount = 0;
    const errorMessages: string[] = [];

    for (const { tab, data, file } of filesToImport) {
      try {
        // Basic validation specific to non-editor JSON for certain tabs
        if (!data.editorJson) {
           if (tab === 'detail' && !data.detailContent) {
               throw new Error(`文件 ${file.name} 缺少 detailContent 字段`);
           } 
           // Add similar checks if rulesContent/awardsContent become mandatory for those tabs
        }
        
        // Tag the data with its target tab before calling onImport
        const importPayload = { ...data, targetTab: tab };

        // Await the import process if it's async
        await Promise.resolve(onImport(importPayload)); 

        // Clear the state for the successfully imported tab
        handleRemoveFile(tab); // Use the existing remove function
        successCount++;

      } catch (error: any) {
        console.error(`导入 ${getTabName(tab)} (${file.name}) 出错:`, error);
        errorMessages.push(`导入 ${getTabName(tab)} (${file.name}) 失败: ${error.message || '未知错误'}`);
        errorCount++;
      }
    }

    setImporting(false);

    if (successCount > 0) {
      notification.success(`成功导入 ${successCount} 个标签页的内容`);
    }
    if (errorCount > 0) {
      // Display individual error messages
      errorMessages.forEach(msg => notification.error(msg, 5)); // Show for 5 seconds
      // notification.error(`有 ${errorCount} 个标签页导入失败`);
    }

    if (successCount > 0 && errorCount === 0) {
      onClose(); // Close modal only if all selected imports succeed
    }
  };

  // --- Helper Functions ---
  const getTabName = (tab: 'detail' | 'rules' | 'awards'): string => {
    switch (tab) {
      case 'detail': return '活动详情';
      case 'rules': return '参赛规则';
      case 'awards': return '奖项设置';
      default: return '活动详情';
    }
  }
  const extractTitleFromEditorJson = (editorJson: any): string => {
    if (!editorJson.blocks || !Array.isArray(editorJson.blocks)) {
      return '未命名活动'
    }
    
    // 查找heading为1的块作为标题
    const titleBlock = editorJson.blocks.find((block: any) => 
      block.heading === 1 && block.text && block.text.length > 0
    )
    
    if (titleBlock && titleBlock.text.length > 0) {
      return titleBlock.text[0].insert || '未命名活动'
    }
    
    // 如果没有找到标题块，使用第一个文本块
    const firstTextBlock = editorJson.blocks.find((block: any) => 
      block.type === 'text' && block.text && block.text.length > 0
    )
    
    if (firstTextBlock && firstTextBlock.text.length > 0) {
      return firstTextBlock.text[0].insert || '未命名活动'
    }
    
    return '未命名活动'
  }

  // Calculate if the import button should be disabled
  const isImportDisabled = importing || Object.values(filesData).every(data => data.parsedData === null);

  // 获取当前用户信息
  const userState = useSelector(selectUserState)
  const isAdmin = userState?.roleId === 4

  // --- Rendering Logic ---

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white rounded-xl shadow-xl w-full max-w-2xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex justify-between items-center border-b p-4">
          <h3 className="text-xl font-semibold flex items-center">
            <FileJson className="w-5 h-5 mr-2 text-blue-500" />
            导入内容到: {getTabName(targetTab)}
          </h3>
          <button onClick={onClose} className="p-1 rounded-full hover:bg-gray-100"><X className="w-5 h-5" /></button>
        </div>

        {/* Body */}
        <div className="p-6 overflow-y-auto" style={{ maxHeight: 'calc(90vh - 120px)' }}>
          {/* Target Tab Selection */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">选择导入目标：</label>
            <div className="flex space-x-2">
              {(['detail', 'rules', 'awards'] as const).map(tabKey => (
                <button
                  key={tabKey}
                  onClick={() => handleTargetTabChange(tabKey)}
                  className={`relative px-3 py-2 rounded-lg text-sm flex items-center transition-colors ${
                    targetTab === tabKey 
                      ? 'bg-blue-500 text-white' 
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  {tabKey === 'detail' && <PenTool className="w-4 h-4 mr-1.5" />}
                  {tabKey === 'rules' && <Info className="w-4 h-4 mr-1.5" />}
                  {tabKey === 'awards' && <Award className="w-4 h-4 mr-1.5" />}
                  {getTabName(tabKey)}
                  {/* Add checkmark if file is selected for this tab */}
                  {filesData[tabKey].file && (
                    <CheckCircle className="w-3 h-3 text-green-500 absolute -top-1 -right-1 bg-white rounded-full" />
                  )}
                </button>
              ))}
            </div>
          </div>

          {/* File Type Info */}
          <div className="mb-4 p-3 bg-blue-50 rounded-lg flex items-start">
            <AlertCircle className="w-5 h-5 text-blue-500 mr-2 flex-shrink-0 mt-0.5" />
            <div className="text-sm text-blue-700">
              <p>请导入包含{getTabName(targetTab)}的JSON文件</p>
              <button 
                onClick={() => setShowExample(!showExample)}
                className="mt-2 flex items-center text-blue-600 hover:underline"
              >
                <Info className="w-4 h-4 mr-1" />
                {showExample ? '隐藏JSON格式示例' : '查看JSON格式示例'}
              </button>
            </div>
          </div>
          {showExample && <div className="mb-4"><ActivityJsonExample /></div>}

          {/* File Drop/Select Area or Preview Area */}
          {!currentTabData.showPreview ? (
            // Drop Zone
            <div 
              className="border-2 border-dashed border-gray-300 rounded-lg p-8 mb-4 text-center hover:border-blue-400 transition-colors cursor-pointer"
              onClick={() => fileInputRef.current?.click()}
              onDragOver={(e) => e.preventDefault()}
              onDrop={handleDrop}
            >
              <input type="file" ref={fileInputRef} className="hidden" accept=".json" onChange={handleFileChange} />
              <div className="flex flex-col items-center">
                <Upload className="w-12 h-12 text-blue-500 mb-2" />
                <p className="text-gray-700 mb-2">点击或拖放JSON文件至此处</p>
                <p className="text-gray-500 text-sm">为 "{getTabName(targetTab)}" 导入</p>
              </div>
            </div>
          ) : (
            // Preview Area
            <div className="mb-4">
              <div className="flex justify-between items-center mb-2">
                <h4 className="font-medium">"{getTabName(targetTab)}" 内容预览:</h4>
                <button 
                  onClick={() => handleRemoveFile(targetTab)}
                  className="text-blue-500 text-sm hover:underline"
                >
                  取消选择并返回
                </button>
              </div>
              {/* Content Preview */}
              {currentTabData.parsedData && (
                <div className="mb-4 border rounded p-4 bg-gray-50">
                  <h5 className="font-medium text-sm mb-2">活动内容预览:</h5>
                  
                  {/* 编辑器格式JSON预览 */}
                  {currentTabData.parsedData.editorJson ? (
                    <div className="mb-4">
                      <div className="flex items-center justify-between mb-2">
                        <p className="text-sm text-blue-600 font-medium">已识别为编辑器格式</p>
                        <div className="text-xs bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full">
                          共 {currentTabData.parsedData.editorJson.blocks?.length || 0} 个内容块
                        </div>
                      </div>
                      
                      <div className="border rounded bg-white p-3 max-h-96 overflow-auto">
                        <h3 className="text-base font-medium mb-2">{currentTabData.parsedData.title || '未命名活动'}</h3>
                        <div className="border-t pt-2">
                          <EditorJsonRenderer jsonData={currentTabData.parsedData.editorJson} />
                        </div>
                      </div>
                    </div>
                  ) : (
                    // 传统格式预览
                    <>
                      {currentTabData.parsedData.title && <p className="text-sm"><span className="font-semibold">标题:</span> {currentTabData.parsedData.title}</p>}
                      {currentTabData.parsedData.startTime && <p className="text-sm"><span className="font-semibold">开始时间:</span> {currentTabData.parsedData.startTime}</p>}
                      {currentTabData.parsedData.endTime && <p className="text-sm"><span className="font-semibold">结束时间:</span> {currentTabData.parsedData.endTime}</p>}
                      {currentTabData.parsedData.organizer && <p className="text-sm"><span className="font-semibold">组织者:</span> {currentTabData.parsedData.organizer}</p>}
                      
                      {currentTabData.parsedData.detailContent && (
                        <div className="mt-2">
                          <p className="text-sm font-semibold">活动详情内容:</p>
                          <div className="max-h-36 overflow-y-auto mt-1 p-2 bg-white rounded border text-xs">
                            <pre>{currentTabData.parsedData.detailContent.substring(0, 200)}...</pre>
                          </div>
                        </div>
                      )}
                    </>
                  )}
                </div>
              )}
              
              {/* JSON原始预览 */}
              <div className="border rounded">
                <div className="bg-gray-100 p-2 text-xs font-mono border-b">
                  <span className="font-semibold">原始JSON数据</span>
                </div>
                <pre className="text-xs p-3 bg-gray-50 max-h-40 overflow-auto">
                  {currentTabData.jsonPreview}
                </pre>
              </div>
            </div>
          )}

          {/* Display Selected File for Current Tab */}
          {currentTabData.file && !currentTabData.showPreview && (
            <div className="mb-4">
              <h4 className="font-medium mb-2">"{getTabName(targetTab)}" 已选文件:</h4>
              <div className="flex items-center justify-between bg-gray-50 p-2 rounded">
                <div className="flex items-center">
                  <FileJson className="w-4 h-4 text-blue-500 mr-2" />
                  <span className="text-sm truncate max-w-md">{currentTabData.file?.name}</span>
                  <span className="text-xs text-gray-500 ml-2">
                    ({(currentTabData.file?.size / 1024).toFixed(2)} KB)
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <button
                    onClick={() => updateCurrentTabData({ showPreview: true })}
                    className="p-1 rounded bg-blue-100 text-blue-700 text-xs"
                  >
                    预览
                  </button>
                  <button
                    onClick={() => handleRemoveFile(targetTab)}
                    className="p-1 rounded-full hover:bg-gray-200"
                  >
                    <X className="w-4 h-4 text-gray-500" />
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex justify-end items-center gap-3 p-4 bg-gray-50 border-t">
          <button onClick={onClose} className="px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg" disabled={importing}>取消</button>
          {isAdmin && (
            <button
              onClick={handleImport}
              disabled={isImportDisabled}
              className={`px-4 py-2 rounded-lg flex items-center ${isImportDisabled ? 'bg-blue-300 cursor-not-allowed' : 'bg-blue-500 hover:bg-blue-600'} text-white transition-colors`}
            >
              {importing ? (
                <><span className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></span>导入中...</>
              ) : (
                <><Check className="w-4 h-4 mr-2" />确认导入 {Object.values(filesData).filter(d => d.file).length} 个文件</>
              )}
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default ActivityImporter; 