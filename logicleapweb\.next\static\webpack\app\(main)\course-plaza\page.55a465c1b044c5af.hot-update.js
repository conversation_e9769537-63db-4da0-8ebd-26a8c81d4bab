"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/course-plaza/page",{

/***/ "(app-pages-browser)/./app/(main)/course-plaza/components/CourseDetailView.tsx":
/*!*****************************************************************!*\
  !*** ./app/(main)/course-plaza/components/CourseDetailView.tsx ***!
  \*****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CourseDetailView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_api_course__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api/course */ \"(app-pages-browser)/./lib/api/course.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction CourseDetailView(param) {\n    let { course, onBack } = param;\n    var _courseDetail_contentConfig_video, _courseDetail_contentConfig, _courseDetail_contentConfig_document, _courseDetail_contentConfig1, _courseDetail_contentConfig_document1, _courseDetail_contentConfig2, _courseDetail_contentConfig_document2, _courseDetail_contentConfig3, _courseDetail_contentConfig_document3, _courseDetail_contentConfig4;\n    _s();\n    const [seriesCourses, setSeriesCourses] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [seriesDetail, setSeriesDetail] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [courseDetail, setCourseDetail] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [showVideoPlayer, setShowVideoPlayer] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [videoLoaded, setVideoLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // 格式化视频时长\n    const formatDuration = (seconds)=>{\n        if (!seconds || seconds <= 0) return \"\";\n        const hours = Math.floor(seconds / 3600);\n        const minutes = Math.floor(seconds % 3600 / 60);\n        const remainingSeconds = seconds % 60;\n        if (hours > 0) {\n            return \"\".concat(hours, \"小时\").concat(minutes, \"分钟\");\n        } else if (minutes > 0) {\n            return \"\".concat(minutes, \"分钟\").concat(remainingSeconds > 0 ? remainingSeconds + \"秒\" : \"\");\n        } else {\n            return \"\".concat(remainingSeconds, \"秒\");\n        }\n    };\n    // 添加自定义滚动条样式\n    const customScrollbarStyle = \"\\n    .custom-scrollbar::-webkit-scrollbar {\\n      width: 6px;\\n    }\\n    .custom-scrollbar::-webkit-scrollbar-track {\\n      background: #f1f5f9;\\n      border-radius: 3px;\\n    }\\n    .custom-scrollbar::-webkit-scrollbar-thumb {\\n      background: linear-gradient(to bottom, #3b82f6, #2563eb);\\n      border-radius: 3px;\\n    }\\n    .custom-scrollbar::-webkit-scrollbar-thumb:hover {\\n      background: linear-gradient(to bottom, #2563eb, #1d4ed8);\\n    }\\n  \";\n    // 使用useRef跟踪请求状态，避免React严格模式重复请求\n    const requestedSeriesIdRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const isRequestingRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(false);\n    // 添加调试信息\n    console.log(\"\\uD83D\\uDCCB CourseDetailView 接收到的课程数据:\", course);\n    console.log(\"\\uD83D\\uDD0D seriesId 值:\", course.seriesId);\n    console.log(\"\\uD83D\\uDD0D seriesId 类型:\", typeof course.seriesId);\n    // 调试：打印传入的课程数据\n    console.log(\"\\uD83C\\uDFAF CourseDetailView 接收到的课程数据:\", course);\n    // 防止浏览器自动下载\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const preventAutoDownload = (e)=>{\n            var _target_getAttribute;\n            const target = e.target;\n            if (target && target.tagName === \"A\" && ((_target_getAttribute = target.getAttribute(\"href\")) === null || _target_getAttribute === void 0 ? void 0 : _target_getAttribute.includes(\"example.com\"))) {\n                e.preventDefault();\n                console.log(\"\\uD83D\\uDEAB 阻止示例文件自动下载\");\n            }\n        };\n        document.addEventListener(\"click\", preventAutoDownload, true);\n        return ()=>{\n            document.removeEventListener(\"click\", preventAutoDownload, true);\n        };\n    }, []);\n    // 获取系列课程列表\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const fetchSeriesCourses = async ()=>{\n            // 使用 seriesId 或者 id 作为系列ID\n            const seriesId = course.seriesId || course.id;\n            if (!seriesId) {\n                console.warn(\"⚠️ 课程没有seriesId和id，无法获取系列课程列表\");\n                setLoading(false);\n                return;\n            }\n            // 防重复请求\n            if (requestedSeriesIdRef.current === seriesId || isRequestingRef.current) {\n                console.log(\"\\uD83D\\uDEAB 防重复请求：系列课程列表已请求过，seriesId:\", seriesId);\n                return;\n            }\n            try {\n                var _coursesRes_data, _coursesRes_data1;\n                setLoading(true);\n                requestedSeriesIdRef.current = seriesId;\n                isRequestingRef.current = true;\n                console.log(\"\\uD83D\\uDD04 获取系列课程列表，使用ID:\", seriesId);\n                // 首先获取系列详情\n                const { data: seriesRes } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getMarketplaceSeriesDetail(seriesId);\n                if (seriesRes.code === 200 && seriesRes.data) {\n                    const seriesData = seriesRes.data;\n                    // 设置系列详情\n                    setSeriesDetail({\n                        id: seriesData.id,\n                        title: seriesData.title,\n                        description: seriesData.description,\n                        coverImage: seriesData.coverImage && !seriesData.coverImage.includes(\"example.com\") ? seriesData.coverImage : \"\",\n                        category: seriesData.category,\n                        categoryLabel: seriesData.categoryLabel || (seriesData.category === 0 ? \"官方\" : \"社区\"),\n                        status: seriesData.status,\n                        projectMembers: seriesData.projectMembers,\n                        totalCourses: seriesData.totalCourses,\n                        totalStudents: seriesData.totalStudents,\n                        creatorId: seriesData.creatorId || 0,\n                        createdAt: seriesData.createdAt,\n                        updatedAt: seriesData.updatedAt || seriesData.createdAt,\n                        tags: seriesData.tags || []\n                    });\n                }\n                // 使用课程管理API获取系列下的课程列表\n                console.log(\"\\uD83D\\uDD04 使用课程管理API获取系列课程列表...\");\n                const { data: coursesRes } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getManagementSeriesCourses(seriesId, {\n                    page: 1,\n                    pageSize: 50,\n                    status: 1 // 只获取已发布的课程\n                });\n                if (coursesRes.code === 200 && ((_coursesRes_data = coursesRes.data) === null || _coursesRes_data === void 0 ? void 0 : _coursesRes_data.list)) {\n                    const courses = coursesRes.data.list.map((item)=>({\n                            id: item.id,\n                            title: item.title,\n                            orderIndex: item.orderIndex || 0,\n                            status: item.status\n                        }));\n                    // 按orderIndex排序\n                    courses.sort((a, b)=>a.orderIndex - b.orderIndex);\n                    console.log(\"\\uD83D\\uDCDA 从课程管理API获取到课程列表:\", courses);\n                    setSeriesCourses(courses);\n                } else {\n                    console.log(\"⚠️ 课程管理API未返回课程列表\");\n                    setSeriesCourses([]);\n                }\n                // 如果有课程列表，设置默认课程详情（选择第一个课程）\n                if (coursesRes.code === 200 && ((_coursesRes_data1 = coursesRes.data) === null || _coursesRes_data1 === void 0 ? void 0 : _coursesRes_data1.list) && coursesRes.data.list.length > 0) {\n                    const firstCourse = coursesRes.data.list[0];\n                    console.log(\"\\uD83C\\uDFAC 设置默认课程（第一个课程）:\", firstCourse);\n                    // 获取第一个课程的详细信息\n                    try {\n                        const { data: courseDetailRes } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getCourseDetail(firstCourse.id);\n                        if (courseDetailRes.code === 200 && courseDetailRes.data) {\n                            var _seriesRes_data, _seriesRes_data1, _courseData_additionalResources;\n                            const courseData = courseDetailRes.data;\n                            setCourseDetail({\n                                id: courseData.id,\n                                title: courseData.title,\n                                description: courseData.description || ((_seriesRes_data = seriesRes.data) === null || _seriesRes_data === void 0 ? void 0 : _seriesRes_data.description) || \"\",\n                                coverImage: courseData.coverImage && !courseData.coverImage.includes(\"example.com\") ? courseData.coverImage : ((_seriesRes_data1 = seriesRes.data) === null || _seriesRes_data1 === void 0 ? void 0 : _seriesRes_data1.coverImage) || \"\",\n                                hasVideo: courseData.hasVideo || 0,\n                                hasDocument: courseData.hasDocument || 0,\n                                hasAudio: courseData.hasAudio || 0,\n                                videoDuration: courseData.videoDuration || 0,\n                                videoDurationLabel: courseData.videoDurationLabel || \"\",\n                                videoName: courseData.videoName || \"\",\n                                resourcesCount: ((_courseData_additionalResources = courseData.additionalResources) === null || _courseData_additionalResources === void 0 ? void 0 : _courseData_additionalResources.length) || 0,\n                                contentConfig: courseData.contentConfig || {},\n                                teachingInfo: courseData.teachingInfo || [],\n                                additionalResources: courseData.additionalResources || [],\n                                orderIndex: courseData.orderIndex || 1,\n                                status: courseData.status || 1,\n                                statusLabel: courseData.statusLabel || \"已发布\",\n                                currentCourseId: courseData.id\n                            });\n                        }\n                    } catch (courseError) {\n                        console.error(\"❌ 获取课程详情失败:\", courseError);\n                    }\n                }\n            } catch (error) {\n                console.error(\"❌ 获取系列课程列表异常:\", error);\n                setSeriesCourses([]);\n            } finally{\n                setLoading(false);\n                isRequestingRef.current = false;\n            }\n        };\n        fetchSeriesCourses();\n    }, [\n        course.seriesId,\n        course.id\n    ]);\n    // 处理课程点击事件\n    const handleCourseClick = async (courseItem)=>{\n        const seriesId = course.seriesId || course.id;\n        const courseId = courseItem.id;\n        console.log(\"\\uD83C\\uDFAF 点击课程，准备获取详情 - seriesId:\", seriesId, \"courseId:\", courseId);\n        // 先清空课程详情，避免旧数据触发下载\n        setCourseDetail(null);\n        try {\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getCourseMarketplaceDetail(seriesId, courseId);\n            if (res.code === 200 && res.data) {\n                console.log(\"\\uD83D\\uDCDA 获取到课程详情:\", res.data);\n                setCourseDetail(res.data);\n            } else {\n                console.error(\"❌ 获取课程详情失败:\", res);\n                // 如果API失败，使用本地数据（添加示例视频时长和PDF文档）\n                const sampleVideoDuration = courseItem.id === 1 ? 1800 : courseItem.id === 2 ? 2400 : 3600; // 30分钟、40分钟、60分钟\n                const hasVideoSample = courseItem.id <= 2 ? 1 : 0; // 前两个课程有视频\n                const hasDocumentSample = courseItem.id <= 3 ? 1 : 0; // 前三个课程有文档\n                setCourseDetail({\n                    id: courseItem.id,\n                    title: courseItem.title,\n                    description: \"\",\n                    coverImage: course.coverImage || \"\",\n                    hasVideo: hasVideoSample,\n                    hasDocument: hasDocumentSample,\n                    hasAudio: 0,\n                    videoDuration: hasVideoSample ? sampleVideoDuration : 0,\n                    videoDurationLabel: \"\",\n                    videoName: hasVideoSample ? \"\".concat(courseItem.title, \"教学视频\") : \"\",\n                    resourcesCount: 0,\n                    contentConfig: {\n                        hasVideo: hasVideoSample,\n                        hasDocument: hasDocumentSample,\n                        hasAudio: 0,\n                        video: hasVideoSample ? {\n                            url: \"https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4\",\n                            name: \"\".concat(courseItem.title, \"教学视频.mp4\")\n                        } : {\n                            url: \"\",\n                            name: \"\"\n                        },\n                        document: hasDocumentSample ? {\n                            url: \"https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf\",\n                            name: \"\".concat(courseItem.title, \"教学课件.pdf\")\n                        } : {\n                            url: \"\",\n                            name: \"\"\n                        },\n                        audio: {\n                            url: \"\",\n                            name: \"\"\n                        }\n                    },\n                    teachingInfo: [],\n                    additionalResources: [],\n                    orderIndex: courseItem.orderIndex || 1,\n                    status: courseItem.status || 1,\n                    statusLabel: courseItem.status === 1 ? \"已发布\" : \"草稿\",\n                    currentCourseId: courseItem.id\n                });\n            }\n        } catch (error) {\n            console.error(\"❌ 获取课程详情异常:\", error);\n            // 如果API失败，使用本地数据（添加示例视频时长和PDF文档）\n            const sampleVideoDuration = courseItem.id === 1 ? 1800 : courseItem.id === 2 ? 2400 : 3600; // 30分钟、40分钟、60分钟\n            const hasVideoSample = courseItem.id <= 2 ? 1 : 0; // 前两个课程有视频\n            const hasDocumentSample = courseItem.id <= 3 ? 1 : 0; // 前三个课程有文档\n            setCourseDetail({\n                id: courseItem.id,\n                title: courseItem.title,\n                description: \"\",\n                coverImage: course.coverImage || \"\",\n                hasVideo: hasVideoSample,\n                hasDocument: hasDocumentSample,\n                hasAudio: 0,\n                videoDuration: hasVideoSample ? sampleVideoDuration : 0,\n                videoDurationLabel: \"\",\n                videoName: hasVideoSample ? \"\".concat(courseItem.title, \"教学视频\") : \"\",\n                resourcesCount: 0,\n                contentConfig: {\n                    hasVideo: hasVideoSample,\n                    hasDocument: hasDocumentSample,\n                    hasAudio: 0,\n                    video: hasVideoSample ? {\n                        url: \"https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4\",\n                        name: \"\".concat(courseItem.title, \"教学视频.mp4\")\n                    } : {\n                        url: \"\",\n                        name: \"\"\n                    },\n                    document: hasDocumentSample ? {\n                        url: \"https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf\",\n                        name: \"\".concat(courseItem.title, \"教学课件.pdf\")\n                    } : {\n                        url: \"\",\n                        name: \"\"\n                    },\n                    audio: {\n                        url: \"\",\n                        name: \"\"\n                    }\n                },\n                teachingInfo: [],\n                additionalResources: [],\n                orderIndex: courseItem.orderIndex || 1,\n                status: courseItem.status || 1,\n                statusLabel: courseItem.status === 1 ? \"已发布\" : \"草稿\",\n                currentCourseId: courseItem.id\n            });\n        }\n    };\n    // 注释：现在数据直接从API获取，不再需要设置默认数据\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n        initial: {\n            opacity: 0,\n            x: 50\n        },\n        animate: {\n            opacity: 1,\n            x: 0\n        },\n        exit: {\n            opacity: 0,\n            x: -50\n        },\n        transition: {\n            duration: 0.5\n        },\n        className: \"min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"style\", {\n                dangerouslySetInnerHTML: {\n                    __html: customScrollbarStyle\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                lineNumber: 423,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: onBack,\n                    className: \"flex items-center gap-2 px-4 py-2 text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-colors duration-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-5 h-5\",\n                            fill: \"none\",\n                            viewBox: \"0 0 24 24\",\n                            stroke: \"currentColor\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M15 19l-7-7 7-7\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                lineNumber: 432,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                            lineNumber: 431,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"返回课程列表\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                            lineNumber: 434,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                    lineNumber: 427,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                lineNumber: 426,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-br from-white via-blue-50/30 to-indigo-50/50 rounded-2xl border border-blue-200/60 shadow-lg backdrop-blur-sm overflow-hidden mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between p-6 bg-white/70 backdrop-blur-md border-b border-blue-100/50\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900 tracking-tight\",\n                                children: (courseDetail === null || courseDetail === void 0 ? void 0 : courseDetail.title) || (seriesDetail === null || seriesDetail === void 0 ? void 0 : seriesDetail.title) || \"课程详情\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                lineNumber: 441,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"px-5 py-2.5 text-sm font-semibold rounded-xl shadow-sm transition-all duration-200 \".concat((seriesDetail === null || seriesDetail === void 0 ? void 0 : seriesDetail.category) === 0 ? \"bg-gradient-to-r from-emerald-500 to-emerald-600 text-white hover:from-emerald-600 hover:to-emerald-700\" : \"bg-gradient-to-r from-blue-500 to-blue-600 text-white hover:from-blue-600 hover:to-blue-700\"),\n                                children: (seriesDetail === null || seriesDetail === void 0 ? void 0 : seriesDetail.category) === 0 ? \"官方\" : \"社区\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                lineNumber: 444,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                        lineNumber: 440,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-6 p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-40 h-28 bg-gradient-to-br from-orange-100 via-orange-50 to-amber-50 rounded-xl shadow-md overflow-hidden border-2 border-white/80 transition-all duration-300 group-hover:shadow-lg group-hover:scale-105\",\n                                        children: (courseDetail === null || courseDetail === void 0 ? void 0 : courseDetail.coverImage) && !courseDetail.coverImage.includes(\"example.com\") || (seriesDetail === null || seriesDetail === void 0 ? void 0 : seriesDetail.coverImage) && !seriesDetail.coverImage.includes(\"example.com\") ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            src: (courseDetail === null || courseDetail === void 0 ? void 0 : courseDetail.coverImage) || (seriesDetail === null || seriesDetail === void 0 ? void 0 : seriesDetail.coverImage) || \"\",\n                                            alt: (courseDetail === null || courseDetail === void 0 ? void 0 : courseDetail.title) || (seriesDetail === null || seriesDetail === void 0 ? void 0 : seriesDetail.title) || \"课程封面\",\n                                            width: 160,\n                                            height: 112,\n                                            className: \"w-full h-full object-cover\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                            lineNumber: 459,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-full flex flex-col items-center justify-center text-orange-400\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-8 h-8 mb-1\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 1.5,\n                                                        d: \"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 469,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 468,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs font-medium\",\n                                                    children: \"课程封面\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 471,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                            lineNumber: 467,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                        lineNumber: 456,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -inset-1 bg-gradient-to-r from-orange-200/20 to-amber-200/20 rounded-xl blur-sm -z-10 opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                        lineNumber: 476,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                lineNumber: 455,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-r from-purple-50 to-indigo-50 rounded-xl p-5 border border-purple-100/50 shadow-sm\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-700 leading-relaxed\",\n                                            children: (courseDetail === null || courseDetail === void 0 ? void 0 : courseDetail.description) || (seriesDetail === null || seriesDetail === void 0 ? void 0 : seriesDetail.description) || \"暂无课程描述\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                            lineNumber: 482,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                        lineNumber: 481,\n                                        columnNumber: 13\n                                    }, this),\n                                    (seriesDetail === null || seriesDetail === void 0 ? void 0 : seriesDetail.tags) && seriesDetail.tags.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2\",\n                                        children: seriesDetail.tags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-flex items-center px-3 py-1 text-xs font-medium rounded-full border\",\n                                                style: {\n                                                    backgroundColor: \"\".concat(tag.color, \"15\"),\n                                                    borderColor: \"\".concat(tag.color, \"40\"),\n                                                    color: tag.color\n                                                },\n                                                children: tag.name\n                                            }, tag.id, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                lineNumber: 491,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                        lineNumber: 489,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 text-sm text-gray-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4 text-purple-500\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 508,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                lineNumber: 507,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: \"项目成员：\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                lineNumber: 510,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: (seriesDetail === null || seriesDetail === void 0 ? void 0 : seriesDetail.projectMembers) || \"暂无信息\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                lineNumber: 511,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                        lineNumber: 506,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                lineNumber: 480,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                        lineNumber: 453,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                lineNumber: 439,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2 space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-r from-blue-600 to-purple-600 px-6 py-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-xl font-bold text-white flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-6 h-6\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                lineNumber: 528,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                            lineNumber: 527,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"教学视频\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 526,\n                                                    columnNumber: 17\n                                                }, this),\n                                                ((courseDetail === null || courseDetail === void 0 ? void 0 : courseDetail.videoDurationLabel) || (courseDetail === null || courseDetail === void 0 ? void 0 : courseDetail.videoDuration)) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"bg-white/20 backdrop-blur-sm text-white px-3 py-1 rounded-full text-sm font-medium\",\n                                                    children: [\n                                                        \"时长：\",\n                                                        courseDetail.videoDurationLabel || formatDuration(courseDetail.videoDuration || 0)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 533,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                            lineNumber: 525,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                        lineNumber: 524,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: (courseDetail === null || courseDetail === void 0 ? void 0 : courseDetail.hasVideo) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative group\",\n                                            children: !showVideoPlayer ? // 视频预览封面\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative bg-gradient-to-br from-gray-900 to-gray-700 rounded-xl h-80 flex items-center justify-center cursor-pointer overflow-hidden transition-all duration-300 hover:shadow-2xl\",\n                                                onClick: ()=>setShowVideoPlayer(true),\n                                                children: [\n                                                    courseDetail.coverImage && !courseDetail.coverImage.includes(\"example.com\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 bg-cover bg-center opacity-60\",\n                                                        style: {\n                                                            backgroundImage: \"url(\".concat(courseDetail.coverImage, \")\")\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 551,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 558,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative z-10 text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-20 h-20 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4 transition-all duration-300 group-hover:bg-white/30 group-hover:scale-110\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-10 h-10 text-white ml-1\",\n                                                                    fill: \"currentColor\",\n                                                                    viewBox: \"0 0 20 20\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        fillRule: \"evenodd\",\n                                                                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z\",\n                                                                        clipRule: \"evenodd\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                        lineNumber: 564,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                    lineNumber: 563,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                lineNumber: 562,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-white text-xl font-bold mb-2\",\n                                                                children: courseDetail.videoName || \"教学视频\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                lineNumber: 567,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-white/80 text-sm\",\n                                                                children: \"点击播放视频\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                lineNumber: 570,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 561,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute top-4 right-4 bg-red-500 text-white px-2 py-1 rounded text-xs font-bold\",\n                                                        children: \"HD\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 574,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                lineNumber: 545,\n                                                columnNumber: 21\n                                            }, this) : // 实际视频播放器\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative bg-black rounded-xl overflow-hidden\",\n                                                children: [\n                                                    ((_courseDetail_contentConfig = courseDetail.contentConfig) === null || _courseDetail_contentConfig === void 0 ? void 0 : (_courseDetail_contentConfig_video = _courseDetail_contentConfig.video) === null || _courseDetail_contentConfig_video === void 0 ? void 0 : _courseDetail_contentConfig_video.url) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                                        className: \"w-full h-80 object-contain\",\n                                                        controls: true,\n                                                        autoPlay: true,\n                                                        poster: courseDetail.coverImage && !courseDetail.coverImage.includes(\"example.com\") ? courseDetail.coverImage : undefined,\n                                                        onLoadStart: ()=>setVideoLoaded(true),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                                                                src: courseDetail.contentConfig.video.url,\n                                                                type: \"video/mp4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                lineNumber: 589,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            \"您的浏览器不支持视频播放\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 582,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-80 flex items-center justify-center bg-gray-100\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center text-gray-500\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-12 h-12 mx-auto mb-2\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 1.5,\n                                                                        d: \"M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                        lineNumber: 596,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                    lineNumber: 595,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"视频加载失败\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                    lineNumber: 598,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm mt-1\",\n                                                                    children: \"请检查网络连接或稍后重试\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                    lineNumber: 599,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                            lineNumber: 594,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 593,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setShowVideoPlayer(false),\n                                                        className: \"absolute top-4 right-4 bg-black/50 hover:bg-black/70 text-white rounded-full p-2 transition-colors duration-200\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-5 h-5\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M6 18L18 6M6 6l12 12\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                lineNumber: 610,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                            lineNumber: 609,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 605,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                lineNumber: 580,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                            lineNumber: 542,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl h-80 flex items-center justify-center border-2 border-dashed border-gray-300\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center text-gray-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-8 h-8 text-gray-400\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 1.5,\n                                                                d: \"M15.75 10.5l4.72-4.72a.75.75 0 011.28.53v11.38a.75.75 0 01-1.28.53l-4.72-4.72M4.5 18.75h9a2.25 2.25 0 002.25-2.25v-9a2.25 2.25 0 00-2.25-2.25h-9A2.25 2.25 0 002.25 7.5v9a2.25 2.25 0 002.25 2.25z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                lineNumber: 621,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                            lineNumber: 620,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 619,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg font-semibold text-gray-600 mb-2\",\n                                                        children: \"暂无教学视频\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 624,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"该课程暂未提供视频内容\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 625,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                lineNumber: 618,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                            lineNumber: 617,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                        lineNumber: 540,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                lineNumber: 523,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-r from-indigo-500 to-purple-600 px-6 py-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-bold text-white flex items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-6 h-6\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 637,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 636,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"教学课件\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                            lineNumber: 635,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                        lineNumber: 634,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: (courseDetail === null || courseDetail === void 0 ? void 0 : courseDetail.hasDocument) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl overflow-hidden border border-gray-200 shadow-inner\",\n                                            children: ((_courseDetail_contentConfig1 = courseDetail.contentConfig) === null || _courseDetail_contentConfig1 === void 0 ? void 0 : (_courseDetail_contentConfig_document = _courseDetail_contentConfig1.document) === null || _courseDetail_contentConfig_document === void 0 ? void 0 : _courseDetail_contentConfig_document.url) && courseDetail.contentConfig.document.url.trim() !== \"\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"iframe\", {\n                                                        src: \"\".concat(courseDetail.contentConfig.document.url, \"#toolbar=1&navpanes=1&scrollbar=1&page=1&view=FitH\"),\n                                                        className: \"w-full h-[600px] rounded-xl border-0\",\n                                                        title: \"教学课件预览\",\n                                                        allow: \"fullscreen\",\n                                                        loading: \"lazy\",\n                                                        style: {\n                                                            background: \"white\",\n                                                            boxShadow: \"inset 0 0 10px rgba(0,0,0,0.1)\"\n                                                        },\n                                                        onLoad: ()=>console.log(\"PDF课件加载完成\"),\n                                                        onError: (e)=>{\n                                                            console.error(\"PDF课件加载失败\");\n                                                            // 阻止错误传播，避免触发下载\n                                                            e.preventDefault();\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 649,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute top-4 left-4 bg-white/95 backdrop-blur-sm rounded-lg px-3 py-2 shadow-lg border border-white/20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4 text-indigo-600\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                        lineNumber: 671,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                    lineNumber: 670,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-medium text-gray-700\",\n                                                                    children: courseDetail.contentConfig.document.name || \"教学课件.pdf\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                    lineNumber: 673,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                            lineNumber: 669,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 668,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute top-4 right-4 flex gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>{\n                                                                    var _courseDetail_contentConfig_document, _courseDetail_contentConfig;\n                                                                    if (courseDetail === null || courseDetail === void 0 ? void 0 : (_courseDetail_contentConfig = courseDetail.contentConfig) === null || _courseDetail_contentConfig === void 0 ? void 0 : (_courseDetail_contentConfig_document = _courseDetail_contentConfig.document) === null || _courseDetail_contentConfig_document === void 0 ? void 0 : _courseDetail_contentConfig_document.url) {\n                                                                        window.open(courseDetail.contentConfig.document.url, \"_blank\");\n                                                                    }\n                                                                },\n                                                                className: \"bg-white/95 hover:bg-white backdrop-blur-sm rounded-lg p-2 shadow-lg border border-white/20 transition-all duration-200 hover:scale-105\",\n                                                                title: \"在新窗口中打开\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4 text-gray-600\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                        lineNumber: 692,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                    lineNumber: 691,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                lineNumber: 682,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: (courseDetail === null || courseDetail === void 0 ? void 0 : (_courseDetail_contentConfig2 = courseDetail.contentConfig) === null || _courseDetail_contentConfig2 === void 0 ? void 0 : (_courseDetail_contentConfig_document1 = _courseDetail_contentConfig2.document) === null || _courseDetail_contentConfig_document1 === void 0 ? void 0 : _courseDetail_contentConfig_document1.url) || \"#\",\n                                                                download: (courseDetail === null || courseDetail === void 0 ? void 0 : (_courseDetail_contentConfig3 = courseDetail.contentConfig) === null || _courseDetail_contentConfig3 === void 0 ? void 0 : (_courseDetail_contentConfig_document2 = _courseDetail_contentConfig3.document) === null || _courseDetail_contentConfig_document2 === void 0 ? void 0 : _courseDetail_contentConfig_document2.name) || \"教学课件.pdf\",\n                                                                className: \"bg-white/95 hover:bg-white backdrop-blur-sm rounded-lg p-2 shadow-lg border border-white/20 transition-all duration-200 hover:scale-105\",\n                                                                title: \"下载课件\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4 text-gray-600\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                        lineNumber: 704,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                    lineNumber: 703,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                lineNumber: 697,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>{\n                                                                    const iframe = document.querySelector('iframe[title=\"教学课件预览\"]');\n                                                                    if (iframe && iframe.requestFullscreen) {\n                                                                        iframe.requestFullscreen();\n                                                                    }\n                                                                },\n                                                                className: \"bg-white/95 hover:bg-white backdrop-blur-sm rounded-lg p-2 shadow-lg border border-white/20 transition-all duration-200 hover:scale-105\",\n                                                                title: \"全屏查看\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4 text-gray-600\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                        lineNumber: 720,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                    lineNumber: 719,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                lineNumber: 709,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 680,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute bottom-4 left-4 bg-blue-500/90 text-white text-xs px-3 py-1 rounded-full backdrop-blur-sm\",\n                                                        children: \"PDF预览模式\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 726,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                lineNumber: 647,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-96 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-8 h-8 text-indigo-600\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 1.5,\n                                                                    d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                    lineNumber: 735,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                lineNumber: 734,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                            lineNumber: 733,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg font-semibold text-gray-700 mb-2\",\n                                                            children: ((_courseDetail_contentConfig4 = courseDetail.contentConfig) === null || _courseDetail_contentConfig4 === void 0 ? void 0 : (_courseDetail_contentConfig_document3 = _courseDetail_contentConfig4.document) === null || _courseDetail_contentConfig_document3 === void 0 ? void 0 : _courseDetail_contentConfig_document3.name) || \"教学课件\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                            lineNumber: 738,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"课件加载中...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                            lineNumber: 741,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-32 h-2 bg-gray-200 rounded-full mx-auto overflow-hidden\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-full bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full animate-pulse\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                    lineNumber: 744,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                lineNumber: 743,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                            lineNumber: 742,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 732,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                lineNumber: 731,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                            lineNumber: 645,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl h-96 flex items-center justify-center border-2 border-dashed border-gray-300\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-8 h-8 text-gray-400\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 1.5,\n                                                                d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                lineNumber: 756,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                            lineNumber: 755,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 754,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg font-semibold text-gray-600 mb-2\",\n                                                        children: \"暂无教学课件\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 759,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"该课程暂未提供课件内容\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 760,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                lineNumber: 753,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                            lineNumber: 752,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                        lineNumber: 643,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                lineNumber: 633,\n                                columnNumber: 11\n                            }, this),\n                            seriesCourses.length > 0 && ((courseDetail === null || courseDetail === void 0 ? void 0 : courseDetail.teachingInfo) && courseDetail.teachingInfo.length > 0 ? courseDetail.teachingInfo.map((info, index)=>{\n                                // 根据标题确定图标和颜色主题\n                                const getThemeConfig = (title)=>{\n                                    if (title.includes(\"目标\")) {\n                                        return {\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 777,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                lineNumber: 776,\n                                                columnNumber: 23\n                                            }, this),\n                                            gradient: \"from-emerald-500 to-green-600\",\n                                            bgColor: \"from-emerald-50 to-green-50\",\n                                            borderColor: \"border-emerald-200\",\n                                            textColor: \"text-emerald-700\",\n                                            bulletColor: \"bg-emerald-500\"\n                                        };\n                                    } else if (title.includes(\"准备\")) {\n                                        return {\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 790,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                lineNumber: 789,\n                                                columnNumber: 23\n                                            }, this),\n                                            gradient: \"from-blue-500 to-indigo-600\",\n                                            bgColor: \"from-blue-50 to-indigo-50\",\n                                            borderColor: \"border-blue-200\",\n                                            textColor: \"text-blue-700\",\n                                            bulletColor: \"bg-blue-500\"\n                                        };\n                                    } else if (title.includes(\"重难点\")) {\n                                        return {\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 803,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                lineNumber: 802,\n                                                columnNumber: 23\n                                            }, this),\n                                            gradient: \"from-orange-500 to-red-600\",\n                                            bgColor: \"from-orange-50 to-red-50\",\n                                            borderColor: \"border-orange-200\",\n                                            textColor: \"text-orange-700\",\n                                            bulletColor: \"bg-orange-500\"\n                                        };\n                                    } else {\n                                        return {\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 816,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                lineNumber: 815,\n                                                columnNumber: 23\n                                            }, this),\n                                            gradient: \"from-purple-500 to-pink-600\",\n                                            bgColor: \"from-purple-50 to-pink-50\",\n                                            borderColor: \"border-purple-200\",\n                                            textColor: \"text-purple-700\",\n                                            bulletColor: \"bg-purple-500\"\n                                        };\n                                    }\n                                };\n                                const theme = getThemeConfig(info.title);\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gradient-to-r \".concat(theme.gradient, \" px-6 py-4\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-xl font-bold text-white flex items-center gap-3\",\n                                                children: [\n                                                    theme.icon,\n                                                    info.title\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                lineNumber: 834,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                            lineNumber: 833,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-br \".concat(theme.bgColor, \" rounded-xl p-6 border \").concat(theme.borderColor),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"space-y-4\",\n                                                    children: info.content.map((item, itemIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-start gap-4 group\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-8 h-8 \".concat(theme.bulletColor, \" rounded-full flex items-center justify-center flex-shrink-0 shadow-md group-hover:scale-110 transition-transform duration-200\"),\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-white font-bold text-sm\",\n                                                                        children: itemIndex + 1\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                        lineNumber: 847,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                    lineNumber: 846,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"\".concat(theme.textColor, \" font-medium leading-relaxed\"),\n                                                                        children: item\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                        lineNumber: 850,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                    lineNumber: 849,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, itemIndex, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                            lineNumber: 845,\n                                                            columnNumber: 27\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 843,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                lineNumber: 842,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                            lineNumber: 841,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                    lineNumber: 831,\n                                    columnNumber: 17\n                                }, this);\n                            }) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-r from-emerald-500 to-green-600 px-6 py-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-xl font-bold text-white flex items-center gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-6 h-6\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                lineNumber: 869,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                            lineNumber: 868,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"教学目标\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 867,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                lineNumber: 866,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gradient-to-br from-emerald-50 to-green-50 rounded-xl p-6 border border-emerald-200\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                className: \"flex items-start gap-4 group\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-8 h-8 bg-emerald-500 rounded-full flex items-center justify-center flex-shrink-0 shadow-md\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-white font-bold text-sm\",\n                                                                            children: \"1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                            lineNumber: 879,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                        lineNumber: 878,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-emerald-700 font-medium leading-relaxed\",\n                                                                        children: \"掌握课程核心概念和基本原理\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                        lineNumber: 881,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                lineNumber: 877,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                className: \"flex items-start gap-4 group\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-8 h-8 bg-emerald-500 rounded-full flex items-center justify-center flex-shrink-0 shadow-md\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-white font-bold text-sm\",\n                                                                            children: \"2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                            lineNumber: 887,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                        lineNumber: 886,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-emerald-700 font-medium leading-relaxed\",\n                                                                        children: \"培养实际应用和问题解决能力\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                        lineNumber: 889,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                lineNumber: 885,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                className: \"flex items-start gap-4 group\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-8 h-8 bg-emerald-500 rounded-full flex items-center justify-center flex-shrink-0 shadow-md\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-white font-bold text-sm\",\n                                                                            children: \"3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                            lineNumber: 895,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                        lineNumber: 894,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-emerald-700 font-medium leading-relaxed\",\n                                                                        children: \"建立系统性的知识框架和思维模式\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                        lineNumber: 897,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                lineNumber: 893,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 876,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 875,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                lineNumber: 874,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                        lineNumber: 865,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-r from-blue-500 to-indigo-600 px-6 py-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-xl font-bold text-white flex items-center gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-6 h-6\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                lineNumber: 911,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                            lineNumber: 910,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"教学准备\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 909,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                lineNumber: 908,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-200\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                className: \"flex items-start gap-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0 shadow-md\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-white font-bold text-sm\",\n                                                                            children: \"1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                            lineNumber: 921,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                        lineNumber: 920,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-blue-700 font-medium leading-relaxed\",\n                                                                        children: \"准备相关教学材料和演示工具\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                        lineNumber: 923,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                lineNumber: 919,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                className: \"flex items-start gap-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0 shadow-md\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-white font-bold text-sm\",\n                                                                            children: \"2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                            lineNumber: 929,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                        lineNumber: 928,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-blue-700 font-medium leading-relaxed\",\n                                                                        children: \"设计互动环节和实践练习\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                        lineNumber: 931,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                lineNumber: 927,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 918,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 917,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                lineNumber: 916,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                        lineNumber: 907,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-r from-orange-500 to-red-600 px-6 py-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-xl font-bold text-white flex items-center gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-6 h-6\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                lineNumber: 945,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                            lineNumber: 944,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"教学重难点\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 943,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                lineNumber: 942,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gradient-to-br from-orange-50 to-red-50 rounded-xl p-6 border border-orange-200\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                className: \"flex items-start gap-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center flex-shrink-0 shadow-md\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-white font-bold text-sm\",\n                                                                            children: \"重\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                            lineNumber: 955,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                        lineNumber: 954,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-orange-700 font-medium leading-relaxed\",\n                                                                        children: \"核心概念的理解和应用\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                        lineNumber: 957,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                lineNumber: 953,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                className: \"flex items-start gap-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-8 h-8 bg-red-500 rounded-full flex items-center justify-center flex-shrink-0 shadow-md\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-white font-bold text-sm\",\n                                                                            children: \"难\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                            lineNumber: 963,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                        lineNumber: 962,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-red-700 font-medium leading-relaxed\",\n                                                                        children: \"复杂问题的分析和解决方法\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                        lineNumber: 965,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                lineNumber: 961,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 952,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 951,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                lineNumber: 950,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                        lineNumber: 941,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                        lineNumber: 520,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-r from-teal-500 to-cyan-600 px-6 py-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold text-white flex items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-6 h-6\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 985,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 984,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"课程附件\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                            lineNumber: 983,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                        lineNumber: 982,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: (courseDetail === null || courseDetail === void 0 ? void 0 : courseDetail.additionalResources) && courseDetail.additionalResources.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-[216px] overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 hover:scrollbar-thumb-gray-400\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2 pr-2\",\n                                                        children: courseDetail.additionalResources.map((resource, index)=>{\n                                                            // 根据文件扩展名确定文件类型和样式\n                                                            const getFileTypeConfig = (title)=>{\n                                                                var _title_split_pop;\n                                                                const extension = (_title_split_pop = title.split(\".\").pop()) === null || _title_split_pop === void 0 ? void 0 : _title_split_pop.toLowerCase();\n                                                                if (extension === \"pptx\" || extension === \"ppt\") {\n                                                                    return {\n                                                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-5 h-5 text-white\",\n                                                                            fill: \"none\",\n                                                                            stroke: \"currentColor\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0a2 2 0 00-2 2v14a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                                lineNumber: 1006,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                            lineNumber: 1005,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        gradient: \"from-orange-500 to-red-600\",\n                                                                        bgColor: \"from-orange-50 to-red-50\",\n                                                                        borderColor: \"border-orange-200\",\n                                                                        hoverColor: \"group-hover:text-orange-700\",\n                                                                        buttonBg: \"bg-orange-100 hover:bg-orange-200 text-orange-600\",\n                                                                        fileType: \"PowerPoint 演示文稿\"\n                                                                    };\n                                                                } else if (extension === \"pdf\") {\n                                                                    return {\n                                                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-5 h-5 text-white\",\n                                                                            fill: \"none\",\n                                                                            stroke: \"currentColor\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                                lineNumber: 1020,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                            lineNumber: 1019,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        gradient: \"from-red-500 to-pink-600\",\n                                                                        bgColor: \"from-red-50 to-pink-50\",\n                                                                        borderColor: \"border-red-200\",\n                                                                        hoverColor: \"group-hover:text-red-700\",\n                                                                        buttonBg: \"bg-red-100 hover:bg-red-200 text-red-600\",\n                                                                        fileType: \"PDF 文档\"\n                                                                    };\n                                                                } else if (extension === \"docx\" || extension === \"doc\") {\n                                                                    return {\n                                                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-5 h-5 text-white\",\n                                                                            fill: \"none\",\n                                                                            stroke: \"currentColor\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                                lineNumber: 1034,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                            lineNumber: 1033,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        gradient: \"from-blue-500 to-indigo-600\",\n                                                                        bgColor: \"from-blue-50 to-indigo-50\",\n                                                                        borderColor: \"border-blue-200\",\n                                                                        hoverColor: \"group-hover:text-blue-700\",\n                                                                        buttonBg: \"bg-blue-100 hover:bg-blue-200 text-blue-600\",\n                                                                        fileType: \"Word 文档\"\n                                                                    };\n                                                                } else {\n                                                                    return {\n                                                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-5 h-5 text-white\",\n                                                                            fill: \"none\",\n                                                                            stroke: \"currentColor\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                                lineNumber: 1048,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                            lineNumber: 1047,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        gradient: \"from-emerald-500 to-teal-600\",\n                                                                        bgColor: \"from-emerald-50 to-teal-50\",\n                                                                        borderColor: \"border-emerald-200\",\n                                                                        hoverColor: \"group-hover:text-emerald-700\",\n                                                                        buttonBg: \"bg-emerald-100 hover:bg-emerald-200 text-emerald-600\",\n                                                                        fileType: \"附件文档\"\n                                                                    };\n                                                                }\n                                                            };\n                                                            const config = getFileTypeConfig(resource.title);\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"group relative bg-gradient-to-r \".concat(config.bgColor, \" border \").concat(config.borderColor, \" rounded-lg p-3 hover:shadow-md transition-all duration-300 cursor-pointer\"),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-10 h-10 bg-gradient-to-br \".concat(config.gradient, \" rounded-lg flex items-center justify-center shadow-md group-hover:scale-110 transition-transform duration-200\"),\n                                                                            children: config.icon\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                            lineNumber: 1066,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                    className: \"text-sm font-semibold text-gray-900 \".concat(config.hoverColor, \" transition-colors\"),\n                                                                                    children: resource.title\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                                    lineNumber: 1070,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-xs text-gray-500 mt-0.5\",\n                                                                                    children: [\n                                                                                        config.fileType,\n                                                                                        \" \",\n                                                                                        resource.description && \"• \".concat(resource.description)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                                    lineNumber: 1073,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                            lineNumber: 1069,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    className: \"p-1.5 bg-white/80 hover:bg-white rounded-md shadow-sm transition-colors duration-200\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                        className: \"w-3.5 h-3.5 text-gray-600\",\n                                                                                        fill: \"none\",\n                                                                                        stroke: \"currentColor\",\n                                                                                        viewBox: \"0 0 24 24\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                strokeLinecap: \"round\",\n                                                                                                strokeLinejoin: \"round\",\n                                                                                                strokeWidth: 2,\n                                                                                                d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                                                lineNumber: 1080,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                strokeLinecap: \"round\",\n                                                                                                strokeLinejoin: \"round\",\n                                                                                                strokeWidth: 2,\n                                                                                                d: \"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                                                lineNumber: 1081,\n                                                                                                columnNumber: 35\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                                        lineNumber: 1079,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                                    lineNumber: 1078,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                                    href: resource.url,\n                                                                                    target: \"_blank\",\n                                                                                    rel: \"noopener noreferrer\",\n                                                                                    className: \"p-1.5 \".concat(config.buttonBg, \" rounded-md transition-colors duration-200\"),\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                        className: \"w-3.5 h-3.5\",\n                                                                                        fill: \"none\",\n                                                                                        stroke: \"currentColor\",\n                                                                                        viewBox: \"0 0 24 24\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                            strokeLinecap: \"round\",\n                                                                                            strokeLinejoin: \"round\",\n                                                                                            strokeWidth: 2,\n                                                                                            d: \"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                                            lineNumber: 1091,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                                        lineNumber: 1090,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                                    lineNumber: 1084,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                            lineNumber: 1077,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                    lineNumber: 1065,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, index, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                lineNumber: 1064,\n                                                                columnNumber: 25\n                                                            }, this);\n                                                        })\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 996,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 995,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-4 pt-3 border-t border-gray-200\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between text-xs text-gray-500\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \"共 \",\n                                                                    courseDetail.additionalResources.length,\n                                                                    \" 个附件\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                lineNumber: 1105,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"点击下载按钮获取文件\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                lineNumber: 1106,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 1104,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 1103,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : /* 空状态 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl h-32 flex items-center justify-center border-2 border-dashed border-gray-300\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-6 h-6 text-gray-400\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 1.5,\n                                                                d: \"M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                lineNumber: 1116,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                            lineNumber: 1115,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 1114,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-semibold text-gray-600 mb-1\",\n                                                        children: \"暂无课程附件\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 1119,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: \"该课程暂未提供附件下载\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 1120,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                lineNumber: 1113,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                            lineNumber: 1112,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                        lineNumber: 991,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                lineNumber: 981,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-br from-white via-blue-50/20 to-indigo-50/30 rounded-xl border border-blue-200/60 shadow-lg backdrop-blur-sm overflow-hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-r from-blue-50/80 to-indigo-50/80 backdrop-blur-md border-b border-blue-100/50 px-6 py-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-lg flex items-center justify-center shadow-sm\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 text-white\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                            lineNumber: 1134,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 1133,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 1132,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-bold text-gray-900 tracking-tight\",\n                                                    children: \"系列课程列表\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 1137,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center gap-1 px-3 py-1 bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-700 text-sm font-semibold rounded-full border border-blue-200/50 shadow-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-3 h-3\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0a2 2 0 00-2 2v14a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                lineNumber: 1142,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                            lineNumber: 1141,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"共\",\n                                                        seriesCourses.length,\n                                                        \"课\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 1140,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                            lineNumber: 1131,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                        lineNumber: 1130,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center py-12\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"animate-spin rounded-full h-10 w-10 border-3 border-blue-200\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                            lineNumber: 1154,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"animate-spin rounded-full h-10 w-10 border-3 border-blue-600 border-t-transparent absolute top-0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                            lineNumber: 1155,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 1153,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-3 text-gray-600 font-medium\",\n                                                    children: \"加载课程列表...\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 1157,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                            lineNumber: 1152,\n                                            columnNumber: 17\n                                        }, this) : seriesCourses.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3 h-40 overflow-y-auto custom-scrollbar\",\n                                            children: seriesCourses.map((courseItem, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"group relative bg-gradient-to-r from-gray-50/50 to-blue-50/30 hover:from-blue-50 hover:to-indigo-50 rounded-xl px-4 py-1 border border-gray-200/50 hover:border-blue-300/50 transition-all duration-300 hover:shadow-md cursor-pointer\",\n                                                    onClick: ()=>handleCourseClick(courseItem),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute left-4 top-1.5 w-6 h-6 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center text-white text-xs font-bold shadow-sm\",\n                                                            children: index + 1\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                            lineNumber: 1168,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center ml-10\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1 flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                            className: \"text-gray-800 font-medium group-hover:text-gray-900 transition-colors\",\n                                                                            style: {\n                                                                                marginBottom: 0\n                                                                            },\n                                                                            children: courseItem.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                            lineNumber: 1174,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-gray-500\",\n                                                                            children: [\n                                                                                \"第\",\n                                                                                index + 1,\n                                                                                \"课\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                            lineNumber: 1177,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                    lineNumber: 1173,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"inline-flex items-center gap-1 px-3 py-1.5 text-xs font-semibold rounded-full transition-all duration-200 \".concat(courseItem.status === 1 ? \"bg-gradient-to-r from-emerald-100 to-green-100 text-emerald-700 border border-emerald-200/50 shadow-sm\" : \"bg-gradient-to-r from-gray-100 to-slate-100 text-gray-600 border border-gray-200/50\"),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-2 h-2 rounded-full \".concat(courseItem.status === 1 ? \"bg-emerald-500\" : \"bg-gray-400\")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                                lineNumber: 1188,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            courseItem.status === 1 ? \"已发布\" : \"草稿\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                        lineNumber: 1183,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                    lineNumber: 1182,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                            lineNumber: 1172,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, courseItem.id, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 1162,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                            lineNumber: 1160,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-16\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 bg-gradient-to-r from-gray-100 to-gray-200 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-8 h-8 text-gray-400\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 1.5,\n                                                            d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                            lineNumber: 1202,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 1201,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 1200,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-500 font-medium\",\n                                                    children: \"暂无课程\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 1205,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-400 text-sm mt-1\",\n                                                    children: \"该系列还没有添加课程\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 1206,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                            lineNumber: 1199,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                        lineNumber: 1150,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                lineNumber: 1128,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                        lineNumber: 979,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                lineNumber: 518,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n        lineNumber: 415,\n        columnNumber: 5\n    }, this);\n}\n_s(CourseDetailView, \"fijPwY+vJmwhygbo4Cj3GpvYaxQ=\");\n_c = CourseDetailView;\nvar _c;\n$RefreshReg$(_c, \"CourseDetailView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/(main)/course-plaza/components/CourseDetailView.tsx\n"));

/***/ })

});