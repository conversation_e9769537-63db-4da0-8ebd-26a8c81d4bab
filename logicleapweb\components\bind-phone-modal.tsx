import React, { useState } from 'react';
import { Modal } from "antd";
import { X } from 'lucide-react';
import { userApi } from "@/lib/api/user";
import SlideCaptcha from './slide-captcha';
import { useSelector } from 'react-redux';
import { RootState } from '@/lib/store';
import { GetNotification } from 'logic-common/dist/components/Notification';

const notification = GetNotification();

interface BindPhoneModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

const BindPhoneModal: React.FC<BindPhoneModalProps> = ({
  isOpen,
  onClose,
  onSuccess
}) => {
  const [phone, setPhone] = useState('');
  const [smsCode, setSmsCode] = useState('');
  const [countdown, setCountdown] = useState(0);
  const [loading, setLoading] = useState(false);
  const [showCaptcha, setShowCaptcha] = useState(false);
  const [error, setError] = useState('');

  // 获取当前登录用户ID
  const userId = useSelector((state: RootState) => state.user.userState.userId);

  const handleSendCode = async () => {
    if (!phone) {
      setError('请输入手机号');
      return;
    }

    if (!/^1[3-9]\d{9}$/.test(phone)) {
      setError('请输入有效的手机号');
      return;
    }

    // 显示滑动验证
    setShowCaptcha(true);
  };

  // 滑动验证成功后发送验证码
  const handleCaptchaSuccess = async () => {
    try {
      const response = await userApi.sendVerifyCode(phone);

      if (response.data && response.data.code === 200) {
        // 即使 code 为 200，也要检查 success 字段
        if (response.data.success === false) {
          // success 明确为 false 时表示业务失败
          notification.error(response.data.message || response.data.msg || '发送验证码失败');
          setError(response.data.message || response.data.msg || '发送验证码失败');
        } else {
          // code 为 200 且 success 不为 false 时才是成功
          notification.success('验证码已发送');
          setCountdown(60);
          const timer = setInterval(() => {
            setCountdown(prev => {
              if (prev <= 1) {
                clearInterval(timer);
                return 0;
              }
              return prev - 1;
            });
          }, 1000);
        }
      } else if (response.data && response.data.success === true) {
        // success 明确为 true 时也是成功
        notification.success('验证码已发送');
        setCountdown(60);
        const timer = setInterval(() => {
          setCountdown(prev => {
            if (prev <= 1) {
              clearInterval(timer);
              return 0;
            }
            return prev - 1;
          });
        }, 1000);
      } else {
        notification.error(response.data?.message || response.data?.msg || '发送验证码失败');
        setError(response.data?.message || response.data?.msg || '发送验证码失败');
      }
    } catch (error: any) {
      console.error('发送验证码失败:', error);
      const errorMsg = error.response?.data?.message || error.response?.data?.msg || '发送验证码失败，请稍后再试';
      notification.error(errorMsg);
      setError(errorMsg);
    } finally {
      setShowCaptcha(false);
    }
  };

  const handleSubmit = async () => {
    if (!phone || !smsCode) {
      notification.error('请填写完整信息');
      return;
    }

    try {
      setLoading(true);

      // 验证验证码
      try {
        const verifyResponse = await userApi.verifyResponse({
          phone: phone,
          code: smsCode
        });

        if (verifyResponse.data && verifyResponse.data.code !== 200) {
          notification.error(verifyResponse.data.message || '验证码错误或已过期，请检查或重新获取');
          setLoading(false);
          return;
        }
      } catch (error) {
        console.log('验证码验证接口未找到，将在绑定操作中验证验证码');
      }

      // 绑定手机号
      const response = await userApi.bindPhone({ phone, code: smsCode });

      if (response.data.code === 200) {
        notification.success('绑定成功');
        onSuccess();
        onClose();
        setPhone('');
        setSmsCode('');
      } else {
        notification.error(response.data?.message || '绑定失败，请重试');
      }
    } catch (error) {
      console.error('绑定失败:', error);
      notification.error('绑定失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Modal
        open={isOpen}
        onCancel={onClose}
        footer={null}
        width={400}
        centered
        closeIcon={null}
        className="rounded-lg"
      >
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-bold">绑定手机号</h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X size={20} />
            </button>
          </div>

          <div className="mb-4">
            <p className="text-gray-500 mb-4">为了您的账号安全和资料互通，建议绑定手机号</p>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                手机号
              </label>
              <input
                type="text"
                value={phone}
                onChange={(e) => {
                  setPhone(e.target.value);
                  setError('');
                }}
                placeholder="请输入手机号"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              {error && <p className="text-red-500 text-xs mt-1">{error}</p>}
            </div>

            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                验证码
              </label>
              <div className="flex gap-2">
                <input
                  type="text"
                  value={smsCode}
                  onChange={(e) => setSmsCode(e.target.value)}
                  placeholder="请输入验证码"
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <button
                  onClick={handleSendCode}
                  disabled={countdown > 0}
                  className={`px-3 py-2 rounded-md ${countdown > 0
                    ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                    : 'bg-blue-500 text-white hover:bg-blue-600'
                    }`}
                >
                  {countdown > 0 ? `${countdown}秒后重发` : '获取验证码'}
                </button>
              </div>
            </div>

            <button
              onClick={handleSubmit}
              disabled={loading}
              className={`w-full py-2 rounded-md ${loading
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-blue-500 text-white hover:bg-blue-600'
                }`}
            >
              {loading ? '处理中...' : '确认绑定'}
            </button>
          </div>
        </div>
      </Modal>

      {/* 滑动验证码组件 */}
      <SlideCaptcha
        isOpen={showCaptcha}
        onClose={() => setShowCaptcha(false)}
        onSuccess={handleCaptchaSuccess}
      />
    </>
  );
};

export default BindPhoneModal; 