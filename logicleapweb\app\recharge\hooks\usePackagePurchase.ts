'use client'

import { useState, useCallback } from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '../../../lib/store';
import { 
  purchasePackage, 
  getOrderStatus, 
  cancelOrder,
  PaymentChannel, 
  PaymentMode,
  PurchasePackageRequest 
} from '../../../lib/api/package-purchase';
import { RechargeOption } from '../types';

export interface PackagePurchaseInfo {
  orderNo: string;
  paymentUrl?: string;
  qrCode?: string;
  amount: number;
  packageName: string;
  expireTime?: string;
}

export interface UsePackagePurchaseReturn {
  // 状态
  loading: boolean;
  error: string | null;
  currentOrder: PackagePurchaseInfo | null;

  // 操作方法
  purchasePackageOrder: (plan: RechargeOption, channel: PaymentChannel, paymentMode?: PaymentMode) => Promise<PackagePurchaseInfo>;
  checkOrderStatus: (orderNo: string) => Promise<'pending' | 'paid' | 'cancelled'>;
  cancelPackageOrder: (orderNo: string) => Promise<void>;
  clearError: () => void;
  reset: () => void;
}

export function usePackagePurchase(): UsePackagePurchaseReturn {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentOrder, setCurrentOrder] = useState<PackagePurchaseInfo | null>(null);

  // 获取用户状态
  const userState = useSelector((state: RootState) => state.user.userState);

  // 获取真实用户ID的函数
  const getRealUserId = useCallback((): string => {
    // 1. 优先从Redux状态获取
    if (userState.userId) {
      return userState.userId.toString();
    }

    // 2. 从localStorage获取用户信息
    const userData = localStorage.getItem('user');
    if (userData) {
      try {
        const parsedUser = JSON.parse(userData);
        if (parsedUser.userId || parsedUser.id) {
          return (parsedUser.userId || parsedUser.id).toString();
        }
      } catch (e) {
        console.error('解析用户信息失败:', e);
      }
    }

    // 3. 从localStorage直接获取userId
    const storedUserId = localStorage.getItem('userId');
    if (storedUserId) {
      return storedUserId;
    }

    // 4. 如果都没有，返回测试用户ID（开发环境）
    console.warn('未找到真实用户ID，使用测试用户ID');
    return 'test_user';
  }, [userState.userId]);

  // 购买套餐
  const purchasePackageOrder = useCallback(async (
    plan: RechargeOption, 
    channel: PaymentChannel, 
    paymentMode: PaymentMode = 'qrcode'
  ): Promise<PackagePurchaseInfo> => {
    setLoading(true);
    setError(null);

    try {
      // 获取真实用户ID
      const realUserId = getRealUserId();
      console.log('购买套餐，用户ID:', realUserId);

      // 构建购买请求
      const purchaseRequest: PurchasePackageRequest = {
        packageId: plan.packageId || parseInt(plan.id),
        channel,
        paymentMode,
        clientIp: '127.0.0.1', // 这里应该获取真实IP
        returnUrl: window.location.origin + '/recharge?success=true'
      };

      console.log('购买请求参数:', purchaseRequest);

      const result = await purchasePackage(purchaseRequest);

      if (result) {
        const purchaseInfo: PackagePurchaseInfo = {
          orderNo: result.orderNo,
          paymentUrl: result.paymentUrl,
          qrCode: result.qrCode,
          amount: result.amount,
          packageName: result.packageName,
          expireTime: result.expireTime
        };

        setCurrentOrder(purchaseInfo);
        console.log('套餐购买成功:', purchaseInfo);
        return purchaseInfo;
      } else {
        throw new Error('购买套餐失败：无返回数据');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '购买套餐失败';
      console.error('购买套餐失败:', err);
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [getRealUserId]);

  // 查询订单状态
  const checkOrderStatus = useCallback(async (orderNo: string): Promise<'pending' | 'paid' | 'cancelled'> => {
    try {
      const result = await getOrderStatus(orderNo);
      
      if (result) {
        return result.status;
      } else {
        return 'pending';
      }
    } catch (err) {
      console.error('查询订单状态失败:', err);
      setError('查询订单状态失败');
      return 'pending';
    }
  }, []);

  // 取消订单
  const cancelPackageOrder = useCallback(async (orderNo: string): Promise<void> => {
    setLoading(true);
    setError(null);

    try {
      await cancelOrder(orderNo);
      
      // 如果取消的是当前订单，清除当前订单信息
      if (currentOrder && currentOrder.orderNo === orderNo) {
        setCurrentOrder(null);
      }
      
      console.log('订单取消成功:', orderNo);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '取消订单失败';
      console.error('取消订单失败:', err);
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [currentOrder]);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const reset = useCallback(() => {
    setCurrentOrder(null);
    setError(null);
  }, []);

  return {
    // 状态
    loading,
    error,
    currentOrder,

    // 操作方法
    purchasePackageOrder,
    checkOrderStatus,
    cancelPackageOrder,
    clearError,
    reset,
  };
}
