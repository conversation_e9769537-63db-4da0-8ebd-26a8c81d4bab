'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Card, Button, Badge, Spin, message, Modal, Radio, Space, Divider, notification } from 'antd';
import { CheckOutlined, CrownOutlined, FireOutlined, GiftOutlined } from '@ant-design/icons';
import request from '../../lib/request';
import { WS_URL, API_URL } from '@/config/config';
import { getDefaultWebSocket, WebSocketClient } from 'logic-common/dist/api/web_socket/webSocket';
import { createTab } from 'logic-common/dist/utils/tab/Tab';
import { Encrypted } from 'logic-common';


// 声明全局QRCode类型
declare global {
  interface Window {
    QRCode: any;
  }
}

// 定义接口类型
interface PackageInfo {
  packageId: number;
  packageName: string;
  packageDescription: string;
  points: number;
  validityDays: number;
  originalPrice: number;
  currentPrice: number;
  discountRate: number;
  savings: number;
  currency: string;
}

interface PurchaseResponse {
  code: number;
  msg: string;
  data: {
    code: number;
    message: string;
    data: {
      orderNo: string;
      paymentUrl?: string;
      qrCode?: string;
      amount: number;
      packageName: string;
      expireTime?: string;
    } | null;
  } | null;
}


// 创建加密服务实例（在组件外部创建是安全的，因为它只是创建实例）
const encryptionService = Encrypted.create(API_URL);

// 优化支付宝URL的函数
const optimizeAlipayUrl = (url: string): string => {
  try {
    // 如果URL太长，尝试提取关键参数
    if (url.length > 500) {
      console.log('原始URL长度:', url.length);
      console.log('原始URL:', url);

      // 对于支付宝URL，保持原样，但在控制台输出长度信息
      console.log('支付宝URL较长，建议使用更大的二维码尺寸');
    }
    return url;
  } catch (error) {
    console.error('URL优化失败:', error);
    return url;
  }
};

// 使用本地QRCode.js库的二维码生成组件
const QRCodeDisplay: React.FC<{ text: string; size?: number }> = ({ text, size = 200 }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [qrCodeLoaded, setQrCodeLoaded] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 优化URL
  const optimizedText = optimizeAlipayUrl(text);

  // 动态加载QRCode.js库
  useEffect(() => {
    const loadQRCodeScript = () => {
      return new Promise<void>((resolve, reject) => {
        // 检查是否已经加载
        if (window.QRCode) {
          resolve();
          return;
        }

        // 创建script标签
        const script = document.createElement('script');
        script.src = '/utils/qrcode.min.js';
        script.onload = () => {
          setQrCodeLoaded(true);
          resolve();
        };
        script.onerror = () => {
          setError('QRCode库加载失败');
          reject(new Error('QRCode库加载失败'));
        };

        document.head.appendChild(script);
      });
    };

    loadQRCodeScript().catch(console.error);
  }, []);

  // 生成二维码
  useEffect(() => {
    if (canvasRef.current && window.QRCode && qrCodeLoaded && optimizedText) {
      try {
        // 清空画布
        const canvas = canvasRef.current;
        const ctx = canvas.getContext('2d');
        if (ctx) {
          ctx.clearRect(0, 0, size, size);
        }

        // 使用QRCode.js生成二维码
        // 根据URL长度调整参数
        const isLongUrl = optimizedText.length > 300;
        const qrOptions = {
          width: size,
          margin: isLongUrl ? 1 : 2, // 长URL使用更小的边距
          errorCorrectionLevel: isLongUrl ? 'L' : 'M', // 长URL使用低纠错级别以减少密度
          type: 'image/png',
          quality: 0.92,
          color: {
            dark: '#000000',
            light: '#ffffff'
          }
        };

        console.log('二维码生成参数:', {
          urlLength: optimizedText.length,
          isLongUrl,
          options: qrOptions
        });

        window.QRCode.toCanvas(canvas, optimizedText, qrOptions, (error: any) => {
          if (error) {
            console.error('二维码生成失败:', error);
            setError('二维码生成失败');

            // 绘制错误提示
            if (ctx) {
              ctx.fillStyle = '#f0f0f0';
              ctx.fillRect(0, 0, size, size);
              ctx.fillStyle = '#666';
              ctx.font = '14px Arial';
              ctx.textAlign = 'center';
              ctx.fillText('二维码', size / 2, size / 2 - 10);
              ctx.fillText('生成失败', size / 2, size / 2 + 10);
            }
          } else {
            setError(null);
          }
        });
      } catch (err) {
        console.error('二维码生成异常:', err);
        setError('二维码生成异常');
      }
    }
  }, [optimizedText, size, qrCodeLoaded]);

  if (error) {
    return (
      <div
        className="border border-gray-300 rounded flex items-center justify-center bg-gray-100"
        style={{ width: size, height: size }}
      >
        <div className="text-center text-gray-600">
          <div className="text-2xl mb-2">⚠️</div>
          <div className="text-sm">二维码生成失败</div>
        </div>
      </div>
    );
  }

  return (
    <canvas
      ref={canvasRef}
      width={size}
      height={size}
      className="border border-gray-300 rounded"
      style={{ maxWidth: '100%', height: 'auto' }}
    />
  );
};

const BuyPage: React.FC = () => {
  const [packages, setPackages] = useState<PackageInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const [purchasing, setPurchasing] = useState(false);
  const [selectedPackage, setSelectedPackage] = useState<PackageInfo | null>(null);
  const [paymentChannel, setPaymentChannel] = useState<'alipay' | 'wechatpay'>('alipay');
  const [paymentMode, setPaymentMode] = useState<'redirect' | 'qrcode'>('redirect');
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [currentOrderNo, setCurrentOrderNo] = useState<string | null>(null);
  const [paymentStatus, setPaymentStatus] = useState<'idle' | 'pending' | 'checking' | 'success' | 'failed'>('idle');
  const [wsConnected, setWsConnected] = useState(false);
  const [cancelling, setCancelling] = useState(false);

  // WebSocket相关
  const webSocketRef = useRef<WebSocketClient | null>(null);

  // 获取套餐列表
  const fetchPackages = async () => {
    try {
      setLoading(true);

      // 动态获取加密请求头
      const headers = await encryptionService.session.getEncryptionHeaders();

      const response = await encryptionService.request.getEncrypted('/api/v1/package-order/packages', {
        headers
      });

      console.log('完整响应:', response); // 调试日志
      console.log('响应数据:', response.data); // 调试日志

      // 注意：axios会自动解析JSON，所以response.data就是API返回的数据
      const apiResponse = response.data;

      if (apiResponse && apiResponse.code === 200) {
        const packagesData = apiResponse.data;
        console.log('apiResponse', apiResponse);
        console.log('套餐数据类型:', typeof packagesData); // 调试日志
        console.log('是否为数组:', Array.isArray(packagesData)); // 调试日志
        console.log('套餐数据内容:', packagesData); // 调试日志

        // 确保数据是数组
        if (Array.isArray(packagesData)) {
          // 转换数据类型，确保数字字段是数字类型
          const processedPackages = packagesData.map(pkg => ({
            ...pkg,
            points: Number(pkg.points),
            originalPrice: Number(pkg.originalPrice),
            currentPrice: Number(pkg.currentPrice),
            discountRate: Number(pkg.discountRate),
            savings: Number(pkg.savings)
          }));
          console.log('处理后的套餐数据:', processedPackages); // 调试日志
          setPackages(processedPackages);
        } else {
          console.error('套餐数据不是数组:', packagesData);
          console.error('数据类型:', typeof packagesData);
          setPackages([]);
          message.error('套餐数据格式错误');
        }
      } else {
        console.error('API返回错误:', apiResponse);
        setPackages([]);
        message.error(apiResponse?.message || '获取套餐信息失败');
      }
    } catch (error: any) {
      console.error('获取套餐失败:', error);
      setPackages([]);
      message.error('网络错误，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 购买套餐
  const handlePurchase = async () => {
    if (!selectedPackage) return;

    try {
      setPurchasing(true);

      // 动态获取安全会话请求头（用于敏感的购买操作）
      const secureHeaders = await encryptionService.session.getSecureEncryptionHeaders();

      const response = await encryptionService.request.postEncryptedWithBody<PurchaseResponse>('/api/v1/package-order/purchase', {
        packageId: selectedPackage.packageId,
        channel: paymentChannel,
        paymentMode: paymentMode,
        clientIp: '127.0.0.1', // 实际项目中应该获取真实IP
        returnUrl: window.location.origin + '/buy?success=true'
      }, {
        headers: secureHeaders
      });

      const apiResponse = response.data;
      console.log('购买响应:', apiResponse); // 调试日志

      // 处理嵌套的响应结构
      if (apiResponse.code === 200 && apiResponse.data ) {
        const { paymentUrl, qrCode, orderNo } = apiResponse.data;
        console.log('支付数据:', { paymentUrl, qrCode, orderNo, paymentMode }); // 调试日志

        // 记录当前订单号，用于WebSocket通知匹配
        setCurrentOrderNo(orderNo);

        message.success('订单创建成功！');
        setShowPaymentModal(false);

        if (paymentMode === 'redirect' && paymentUrl) {
          // 跳转支付 - 对于支付宝，同时显示二维码选项
          console.log('跳转支付:', paymentUrl);

          if (paymentChannel === 'alipay') {
            // 支付宝跳转支付：显示二维码和跳转选项
            Modal.info({
              title: '支付宝支付',
              content: (
                <div className="text-center">
                  <div className="mb-6">
                    <div className="p-6 bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg border-2 border-blue-200">
                      <div className="text-blue-600 text-4xl mb-3">💰</div>
                      <h3 className="text-lg font-semibold text-blue-800 mb-2">支付宝支付</h3>
                      <p className="text-sm text-blue-700">您可以扫描二维码或点击按钮跳转到支付宝</p>
                    </div>
                  </div>

                  {/* 二维码显示 */}
                  <div className="mb-6 flex justify-center">
                    <div className="p-4 bg-white rounded-lg shadow-md">
                      <QRCodeDisplay text={paymentUrl} size={260} />
                      <p className="text-xs text-gray-500 mt-2">使用支付宝扫描上方二维码</p>
                    </div>
                  </div>

                  {/* 跳转按钮 */}
                  <div className="mb-6">
                    <Button
                      type="primary"
                      size="large"
                      className="w-full"
                      onClick={() => window.open(paymentUrl, '_blank')}
                    >
                      🔗 跳转到支付宝支付
                    </Button>
                  </div>

                  {/* 支付链接 */}
                  <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                    <p className="text-sm text-gray-600 mb-2">支付链接（点击复制）：</p>
                    <div
                      className="text-xs break-all font-mono bg-white p-3 rounded border cursor-pointer hover:bg-blue-50 transition-colors select-all"
                      onClick={() => {
                        navigator.clipboard.writeText(paymentUrl).then(() => {
                          message.success('支付链接已复制到剪贴板');
                        }).catch(() => {
                          message.error('复制失败，请手动复制');
                        });
                      }}
                      title="点击复制支付链接"
                    >
                      {paymentUrl}
                    </div>
                  </div>

                  {/* 订单信息 */}
                  <div className="mb-4 p-3 bg-yellow-50 rounded border-l-4 border-yellow-400">
                    <p className="text-sm text-gray-600">订单号：{orderNo}</p>
                    <p className="text-sm text-gray-600">套餐：{selectedPackage?.packageName}</p>
                    <p className="text-sm text-gray-600">金额：¥{selectedPackage?.currentPrice}</p>
                  </div>

                  {/* 支付说明 */}
                  <div className="text-left p-4 bg-blue-50 rounded text-sm text-gray-600">
                    <p className="font-semibold mb-2">💡 支付方式：</p>
                    <div className="space-y-1">
                      <p>1. <strong>扫码支付</strong>：使用支付宝扫描上方二维码</p>
                      <p>2. <strong>跳转支付</strong>：点击上方按钮跳转到支付宝</p>
                      <p>3. <strong>复制链接</strong>：复制链接在支付宝中打开</p>
                      <p>4. 完成支付后请等待页面自动确认</p>
                    </div>
                  </div>
                </div>
              ),
              width: 500,
              okText: '我已完成支付',
              cancelText: '取消支付',
              onOk: () => {
                message.info('请等待支付结果确认，如长时间未到账请联系客服');
              },
              onCancel: () => {
                message.info('支付已取消');
              }
            });
          } else {
            // 微信支付：直接跳转
            window.open(paymentUrl, '_blank');
          }
        } else if (paymentMode === 'qrcode' && qrCode) {
          console.log('显示二维码:', qrCode);

          // 根据支付渠道显示不同的支付信息
          const isAlipay = paymentChannel === 'alipay';
          const paymentTitle = isAlipay ? '支付宝支付' : '微信支付';
          const paymentIcon = isAlipay ? '💰' : '💚';
          const scanText = isAlipay ? '使用支付宝扫描上方二维码' : '使用微信扫描上方二维码';

          // 显示支付信息和二维码
          Modal.info({
            title: paymentTitle,
            content: (
              <div className="text-center">
                <div className="mb-6">
                  <div className={`p-6 bg-gradient-to-br ${isAlipay ? 'from-blue-50 to-blue-100 border-blue-200' : 'from-green-50 to-green-100 border-green-200'} rounded-lg border-2`}>
                    <div className={`${isAlipay ? 'text-blue-600' : 'text-green-600'} text-4xl mb-3`}>{paymentIcon}</div>
                    <h3 className={`text-lg font-semibold ${isAlipay ? 'text-blue-800' : 'text-green-800'} mb-2`}>{paymentTitle}</h3>
                    <p className={`text-sm ${isAlipay ? 'text-blue-700' : 'text-green-700'}`}>
                      {isAlipay ? '请使用支付宝扫描二维码或复制链接支付' : '请使用微信扫描二维码或复制链接支付'}
                    </p>
                  </div>
                </div>

                {/* 二维码显示 */}
                <div className="mb-6 flex justify-center">
                  <div className="p-4 bg-white rounded-lg shadow-md">
                    <QRCodeDisplay text={qrCode} size={260} />
                    <p className="text-xs text-gray-500 mt-2">{scanText}</p>
                  </div>
                </div>

                {/* 支付链接 */}
                <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                  <p className="text-sm text-gray-600 mb-2">支付链接（点击复制）：</p>
                  <div
                    className="text-xs break-all font-mono bg-white p-3 rounded border cursor-pointer hover:bg-blue-50 transition-colors select-all"
                    onClick={() => {
                      navigator.clipboard.writeText(qrCode).then(() => {
                        message.success('支付链接已复制到剪贴板');
                      }).catch(() => {
                        message.error('复制失败，请手动复制');
                      });
                    }}
                    title="点击复制支付链接"
                  >
                    {qrCode}
                  </div>
                </div>

                {/* 支付码（仅微信支付显示） */}
                {!isAlipay && (
                  <div className="mb-6 p-4 bg-blue-50 rounded-lg">
                    <p className="text-sm text-gray-600 mb-2">支付码：</p>
                    <div className="text-lg font-mono font-bold text-blue-800 bg-white p-3 rounded border">
                      {qrCode.split('pr=')[1] || ''}
                    </div>
                  </div>
                )}

                {/* 订单信息 */}
                <div className="mb-4 p-3 bg-yellow-50 rounded border-l-4 border-yellow-400">
                  <p className="text-sm text-gray-600">订单号：{orderNo}</p>
                  <p className="text-sm text-gray-600">套餐：{selectedPackage?.packageName}</p>
                  <p className="text-sm text-gray-600">金额：¥{selectedPackage?.currentPrice}</p>
                </div>

                {/* 支付说明 */}
                <div className="text-left p-4 bg-blue-50 rounded text-sm text-gray-600">
                  <p className="font-semibold mb-2">💡 支付方式：</p>
                  <div className="space-y-1">
                    {isAlipay ? (
                      <>
                        <p>1. 复制上方支付链接，在支付宝中打开</p>
                        <p>2. 或使用支付宝扫描上方二维码</p>
                        <p>3. 完成支付后请等待页面自动确认</p>
                      </>
                    ) : (
                      <>
                        <p>1. 复制上方支付链接，在微信中打开</p>
                        <p>2. 或在微信中搜索"收付款"，输入支付码</p>
                        <p>3. 完成支付后请等待页面自动确认</p>
                      </>
                    )}
                  </div>
                </div>
              </div>
            ),
            width: 500,
            okText: '我已完成支付',
            cancelText: '取消支付',
            onOk: () => {
              message.info('请等待支付结果确认，如长时间未到账请联系客服');
              // 这里可以添加查询支付状态的逻辑
            },
            onCancel: () => {
              message.info('支付已取消');
            }
          });
        } else {
          console.warn('没有支付URL或二维码:', { paymentUrl, qrCode, paymentMode });
          message.warning('支付信息不完整，请重试');
        }
      } else {
        console.error('购买失败:', apiResponse);
        message.error(apiResponse.data?.message || apiResponse.msg || '购买失败');
      }
    } catch (error: any) {
      console.error('购买失败:', error);
      console.error('错误详情:', error.response?.data);
      message.error('购买失败，请稍后重试');
    } finally {
      setPurchasing(false);
    }
  };

  // 打开支付选择弹窗
  const openPaymentModal = (pkg: PackageInfo) => {
    setSelectedPackage(pkg);
    setShowPaymentModal(true);
  };

  // 获取套餐图标
  const getPackageIcon = (points: number) => {
    if (points >= 1000) return <CrownOutlined className="text-yellow-500" />;
    if (points >= 500) return <FireOutlined className="text-red-500" />;
    return <GiftOutlined className="text-blue-500" />;
  };

  // 获取套餐标签
  const getPackageBadge = (savings: number, discountRate: number) => {
    if (savings >= 20) return <Badge.Ribbon text="超值优惠" color="red" />;
    if (discountRate <= 0.7) return <Badge.Ribbon text="热门推荐" color="volcano" />;
    return null;
  };

  useEffect(() => {
    fetchPackages();

    // 初始化WebSocket连接
    const timer = setInterval(() => {
      initWebSocket();
    }, 1000);

    // 清理函数
    return () => {
      clearInterval(timer);
      if (webSocketRef.current) {
        webSocketRef.current.disconnect();
      }
    };
  }, []);

  // 获取标签页ID
  const getTabId = (): string => {
    const tempTabId = createTab('buy-page');
    if (!localStorage.getItem('buyPageTabId')) {
      localStorage.setItem('buyPageTabId', tempTabId);
    }
    return localStorage.getItem('buyPageTabId') || tempTabId;
  };

  // 初始化WebSocket连接
  const initWebSocket = () => {
    let token = localStorage.getItem('token');

    if (!token) {
      console.log('未找到token，无法连接WebSocket');
      return;
    }

    if (webSocketRef.current) {
      if (!webSocketRef.current.isConnected()) {
        const webSocket = getDefaultWebSocket({
          url: WS_URL,
          token,
        });
        webSocketRef.current = webSocket;
        setWsConnected(true);
        setupWebSocketEvents(webSocket);
      }
    } else {
      const webSocket = getDefaultWebSocket({
        url: WS_URL,
        token,
      });

      webSocketRef.current = webSocket;
      setWsConnected(true);
      setupWebSocketEvents(webSocket);
    }
  };

  // 设置WebSocket事件监听
  const setupWebSocketEvents = (webSocket: WebSocketClient) => {
    // 添加标签页
    webSocket.addTab(getTabId());

    // 监听支付成功通知
    webSocket.on("payment_success_notification", (data: any) => {
      console.log('收到支付成功通知:', data);
      handlePaymentSuccessNotification(data);
    });

    webSocket.on("error", (error: any) => {
      console.error('WebSocket 错误:', error);
      setWsConnected(false);
    });
  };

  // 处理支付成功通知
  const handlePaymentSuccessNotification = (data: any) => {
    console.log('收到支付成功通知:', data);

    // 显示成功通知
    notification.success({
      message: data.title || '支付成功',
      description: data.message || '套餐购买成功！',
      duration: 5,
      placement: 'topRight',
      style: {
        backgroundColor: '#f6ffed',
        border: '1px solid #b7eb8f',
      },
    });

    // 更新支付状态
    setPaymentStatus('success');

    // 关闭支付弹窗
    setTimeout(() => {
      setShowPaymentModal(false);
      // 清除当前订单号
      setCurrentOrderNo(null);
      // 重置支付状态
      setPaymentStatus('idle');
    }, 2000); // 延迟2秒关闭，让用户看到成功状态

    // 刷新套餐列表
    fetchPackages();

    // 显示详细成功信息
    const packageName = data.data?.packageName || '套餐';
    const points = data.data?.points;
    const successMessage = points
      ? `恭喜您成功购买 ${packageName}！获得 ${points} 积分`
      : `恭喜您成功购买 ${packageName}！`;

    message.success(successMessage);

    // 触发全局事件，通知其他组件更新
    window.dispatchEvent(new CustomEvent('packagePurchaseSuccess', {
      detail: {
        packageInfo: data.data,
        points: points
      }
    }));

    console.log('套餐购买成功，订单详情:', data.data);
  };

  // 取消订单
  const handleCancelOrder = async () => {
    if (!currentOrderNo) {
      message.error('没有可取消的订单');
      return;
    }

    try {
      setCancelling(true);

      // 动态获取安全会话请求头（用于敏感的取消订单操作）
      const secureHeaders = await encryptionService.session.getSecureEncryptionHeaders();

      const response = await encryptionService.request.postEncryptedWithBody(`/api/v1/package-order/cancel/${currentOrderNo}`, {}, {
        headers: secureHeaders
      });
      console.log('取消订单响应', response);

      if (response.data.code === 200) {
        message.success('订单已取消');
        setShowPaymentModal(false);
        setCurrentOrderNo(null);
        setPaymentStatus('idle');

        // 刷新套餐列表
        fetchPackages();
      } else {
        message.error(response.data.message || '取消订单失败');
      }
    } catch (error: any) {
      console.error('取消订单失败:', error);
      message.error(error.response?.data?.message || '取消订单失败');
    } finally {
      setCancelling(false);
    }
  };

  // 调试日志
  console.log('当前packages状态:', packages);
  console.log('packages是否为数组:', Array.isArray(packages));
  console.log('packages长度:', packages?.length);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Spin size="large" tip="加载套餐信息中..." />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-12 px-4">
      <div className="max-w-7xl mx-auto">
        {/* 页面标题 */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            选择您的套餐
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            选择最适合您需求的套餐，享受更多功能和服务
          </p>
        </div>

        {/* 套餐列表 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {Array.isArray(packages) ? packages.map((pkg) => {
            const badge = getPackageBadge(pkg.savings, pkg.discountRate);

            return (
              <div key={pkg.packageId} className="relative">
                {badge}
                <Card
                  className="h-full hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2"
                  styles={{ body: { padding: '2rem' } }}
                >
                  <div className="text-center">
                    {/* 套餐图标 */}
                    <div className="text-4xl mb-4">
                      {getPackageIcon(pkg.points)}
                    </div>

                    {/* 套餐名称 */}
                    <h3 className="text-2xl font-bold text-gray-900 mb-2">
                      {pkg.packageName}
                    </h3>

                    {/* 套餐描述 */}
                    <p className="text-gray-600 mb-6">
                      {pkg.packageDescription || '暂无描述'}
                    </p>

                    {/* 价格信息 */}
                    <div className="mb-6">
                      <div className="flex items-center justify-center mb-2">
                        <span className="text-3xl font-bold text-red-500">
                          ¥{pkg.currentPrice}
                        </span>
                        <span className="text-lg text-gray-500 line-through ml-2">
                          ¥{pkg.originalPrice}
                        </span>
                      </div>
                      <div className="text-sm text-green-600">
                        节省 ¥{pkg.savings} ({Math.round((1 - pkg.discountRate) * 100)}% 折扣)
                      </div>
                    </div>

                    {/* 套餐详情 */}
                    <div className="space-y-3 mb-8">
                      <div className="flex items-center justify-center">
                        <CheckOutlined className="text-green-500 mr-2" />
                        <span>{pkg.points} 积分</span>
                      </div>
                      <div className="flex items-center justify-center">
                        <CheckOutlined className="text-green-500 mr-2" />
                        <span>有效期 {pkg.validityDays} 天</span>
                      </div>
                      <div className="flex items-center justify-center">
                        <CheckOutlined className="text-green-500 mr-2" />
                        <span>24小时客服支持</span>
                      </div>
                    </div>

                    {/* 购买按钮 */}
                    <Button
                      type="primary"
                      size="large"
                      className="w-full h-12 text-lg font-semibold"
                      onClick={() => openPaymentModal(pkg)}
                    >
                      立即购买
                    </Button>
                  </div>
                </Card>
              </div>
            );
          }) : (
            <div className="col-span-full text-center py-12">
              <p className="text-red-500 text-lg">数据格式错误：packages不是数组</p>
              <p className="text-gray-500">当前数据类型: {typeof packages}</p>
              <Button
                type="primary"
                className="mt-4"
                onClick={fetchPackages}
              >
                重新加载
              </Button>
            </div>
          )}
        </div>

        {/* 空状态 */}
        {packages.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500 text-lg">暂无可用套餐</p>
            <Button
              type="primary"
              className="mt-4"
              onClick={fetchPackages}
            >
              重新加载
            </Button>
          </div>
        )}
      </div>

      {/* 支付方式选择弹窗 */}
      <Modal
        title={currentOrderNo ? "订单管理" : "选择支付方式"}
        open={showPaymentModal}
        onCancel={() => setShowPaymentModal(false)}
        footer={[
          <Button key="cancel" onClick={() => setShowPaymentModal(false)}>
            关闭
          </Button>,
          currentOrderNo && (
            <Button
              key="cancelOrder"
              danger
              loading={cancelling}
              onClick={handleCancelOrder}
            >
              取消订单
            </Button>
          ),
          <Button
            key="confirm"
            type="primary"
            loading={purchasing}
            onClick={handlePurchase}
            disabled={!!currentOrderNo} // 如果已有订单，禁用确认支付按钮
          >
            确认支付
          </Button>,
        ].filter(Boolean)}
        width={500}
      >
        {currentOrderNo && (
          <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h4 className="text-lg font-semibold text-blue-800 mb-2">当前订单</h4>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-600">订单号:</span>
                <span className="font-mono text-sm">{currentOrderNo}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">套餐:</span>
                <span>{selectedPackage?.packageName}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">金额:</span>
                <span className="font-semibold text-red-600">¥{selectedPackage?.currentPrice}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">状态:</span>
                <Badge
                  status={paymentStatus === 'success' ? 'success' : 'processing'}
                  text={paymentStatus === 'success' ? '支付成功' : '待支付'}
                />
              </div>
            </div>
            <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded">
              <p className="text-sm text-yellow-800">
                💡 提示：如果您已完成支付，请等待系统确认。如果不想继续支付，可以点击"取消订单"。
              </p>
            </div>
          </div>
        )}

        {selectedPackage && !currentOrderNo && (
          <div>
            {/* 订单信息 */}
            <div className="bg-gray-50 p-4 rounded-lg mb-6">
              <h4 className="font-semibold mb-2">订单信息</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>套餐名称：</span>
                  <span>{selectedPackage.packageName}</span>
                </div>
                <div className="flex justify-between">
                  <span>积分数量：</span>
                  <span>{selectedPackage.points} 积分</span>
                </div>
                <div className="flex justify-between">
                  <span>有效期：</span>
                  <span>{selectedPackage.validityDays} 天</span>
                </div>
                <Divider className="my-2" />
                <div className="flex justify-between font-semibold text-lg">
                  <span>支付金额：</span>
                  <span className="text-red-500">¥{selectedPackage.currentPrice}</span>
                </div>
              </div>
            </div>

            {/* 支付渠道选择 */}
            <div className="mb-4">
              <h4 className="font-semibold mb-3">支付渠道</h4>
              <Radio.Group
                value={paymentChannel}
                onChange={(e) => setPaymentChannel(e.target.value)}
                className="w-full"
              >
                <Space direction="vertical" className="w-full">
                  <Radio value="alipay" className="w-full p-3 border rounded-lg">
                    <div className="flex items-center">
                      <span className="text-blue-600 text-lg mr-2">💰</span>
                      <span>支付宝</span>
                    </div>
                  </Radio>
                  <Radio value="wechatpay" className="w-full p-3 border rounded-lg">
                    <div className="flex items-center">
                      <span className="text-green-600 text-lg mr-2">💚</span>
                      <span>微信支付</span>
                    </div>
                  </Radio>
                </Space>
              </Radio.Group>
            </div>

            {/* 支付方式选择 */}
            <div>
              <h4 className="font-semibold mb-3">支付方式</h4>
              <Radio.Group
                value={paymentMode}
                onChange={(e) => setPaymentMode(e.target.value)}
                className="w-full"
              >
                <Space direction="vertical" className="w-full">
                  <Radio value="redirect" className="w-full p-3 border rounded-lg">
                    <div className="flex items-center">
                      <span className="text-blue-600 text-lg mr-2">🔗</span>
                      <span>跳转支付（推荐）</span>
                    </div>
                  </Radio>
                  <Radio value="qrcode" className="w-full p-3 border rounded-lg">
                    <div className="flex items-center">
                      <span className="text-gray-600 text-lg mr-2">📱</span>
                      <span>扫码支付</span>
                    </div>
                  </Radio>
                </Space>
              </Radio.Group>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default BuyPage;