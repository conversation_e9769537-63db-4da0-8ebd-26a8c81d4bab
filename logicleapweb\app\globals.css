@tailwind base;
@tailwind components;
@tailwind utilities;

/* 在您的CSS文件中添加 */
input:-webkit-autofill,
input:-webkit-autofill:hover, 
input:-webkit-autofill:focus {
  -webkit-box-shadow: 0 0 0px 1000px white inset !important;
  -webkit-text-fill-color: inherit !important;
  transition: background-color 5000s ease-in-out 0s;
}

/* 自定义滚动条样式 */
.scrollbar-thin {
  scrollbar-width: thin;
}

.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

.scrollbar-thumb-gray-300::-webkit-scrollbar-thumb {
  background: #d1d5db;
}

.scrollbar-track-gray-100::-webkit-scrollbar-track {
  background: #f3f4f6;
}

.scrollbar-thumb-gray-400::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 255, 255, 255;
  --background-end-rgb: 240, 240, 240;
}

/* 添加全局文字和图片不可选中的样式 */
* {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

img {
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  user-drag: none;
}

/* 保持输入框和文本区域可选中 */
input,
textarea,
[contenteditable='true'] {
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}

html {
  scroll-behavior: smooth;
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(to bottom, transparent, rgb(var(--background-end-rgb))) rgb(var(--background-start-rgb));
  overflow-x: hidden;
}

@media (prefers-reduced-motion: reduce) {
  html {
    scroll-behavior: auto;
  }
}

.container {
  max-width: 1200px;
}

@keyframes breatheShadow {
  0% {
    box-shadow: 0 0 20px rgba(71, 102, 194, 0.2);
  }

  50% {
    box-shadow: 0 0 30px rgba(71, 102, 194, 0.4);
  }

  100% {
    box-shadow: 0 0 20px rgba(71, 102, 194, 0.2);
  }
}

.carousel-container {
  position: relative;
  margin: 0;

  /* 给轮播图容器添加呼吸灯效果 */
  .slick-list {
    border-radius: 1rem;
    animation: breatheShadow 0.5s ease-in-out infinite;
    margin: 0;
  }

  /* 箭头容器样式 */
  .slick-prev,
  .slick-next {
    z-index: 10;
    width: 32px;
    height: 32px;
    opacity: 0;
    transition: all 0.5s ease;
    background: rgba(0, 0, 0, 0.2) !important;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    border-radius: 50%;
    display: flex !important;
    align-items: center;
    justify-content: center;

    svg {
      width: 20px;
      height: 20px;
      stroke: white;
      stroke-width: 2;
      stroke-linecap: round;
      stroke-linejoin: round;
      fill: none;
    }

    &:hover {
      background: rgba(0, 0, 0, 0.3) !important;
    }

    &:before {
      display: none;
    }
  }

  .slick-prev {
    left: 16px;
  }

  .slick-next {
    right: 16px;
  }

  /* 鼠标悬停时显示箭头 */
  &:hover {

    .slick-prev,
    .slick-next {
      opacity: 1;
    }
  }

  /* 点点样式 */
  .slick-dots {
    bottom: -24px;
    margin: 0;
    padding: 0;

    li {
      width: 24px;
      height: 8px;
      margin: 0 4px;
      transition: all 0.5s ease;

      button {
        width: 24px;
        height: 8px;
        padding: 0;

        &:before {
          content: '';
          width: 8px;
          height: 8px;
          background: #4766c2;
          border-radius: 50%;
          opacity: 0.3;
          position: absolute;
          top: 0;
          left: 8px;
          transition: all 0.5s ease;
        }
      }

      &.slick-active {
        button:before {
          width: 24px;
          left: 0;
          border-radius: 4px;
          opacity: 1;
        }
      }
    }
  }

  .slick-slide {
    &:focus {
      outline: none;
    }
  }

  /* 移除 slick-track 的外边距 */
  .slick-track {
    margin-left: 0;
    margin-right: 0;
  }
}

.grid-masonry {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-auto-rows: 180px;
  gap: 1rem;
  width: 100%;
  padding: 1rem;
}

/* 注释掉特性卡片相关样式 */
/*
.feature-card {
  width: 100%;
  height: 100%;
  border-radius: 20px;
  background: #f5f5f5;
  position: relative;
  padding: 1.8rem;
  border: 2px solid #c3c6ce;
  transition: 0.5s ease-out;
  overflow: hidden;
}

.feature-card:nth-child(1) {
  height: 380px;
}

.feature-card:nth-child(3) {
  height: 380px;
}

.feature-card:nth-child(5) {
  height: 180px;
}

.feature-card-details {
  color: black;
  height: 100%;
  gap: .5em;
  display: grid;
  place-content: center;
}

.feature-text-body {
  color: rgb(134, 134, 134);
}

.feature-text-title {
  font-size: 1.5em;
  font-weight: bold;
  color: #4766C2;
}

.feature-card:hover {
  border-color: #4766C2;
  box-shadow: 0 4px 18px 0 rgba(71, 102, 194, 0.25);
}
*/

.gallery-container {
  width: 100%;
  padding: 0 1rem;
}

.gallery-header {
  margin-bottom: 2rem;
}

.gallery-tags::-webkit-scrollbar {
  display: none;
}

.gallery-tag {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 0.5rem 0;
  color: #666;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
  position: relative;
}

.tag-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  color: currentColor;
}

.gallery-tag.active .tag-icon,
.gallery-tag:hover .tag-icon {
  color: #4766c2;
}

.gallery-tag svg {
  width: 16px;
  height: 16px;
}

.gallery-tag.active {
  color: #4766c2;
  background: none;
}

.gallery-tag.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 2px;
  background: #4766c2;
  border-radius: 1px;
}

.gallery-tag:hover {
  color: #4766c2;
}

.gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
  grid-auto-flow: row dense;
}

.gallery-item {
  grid-column: span 1;
  height: fit-content;
  break-inside: avoid;
  page-break-inside: avoid;
}

.gallery-item-inner {
  position: relative;
  background: #1a1a1a;
  border-radius: 12px;
  overflow: hidden;
  transform-origin: center top;
  transition: all 0.5s ease;
}

.gallery-image {
  position: relative;
  width: 100%;
  padding-bottom: 100%;
  /* 默认1:1比例 */
  background: #000;
  transition: all 0.5s ease;
}

.gallery-image img {
  transition: all 0.5s ease;
}

.gallery-content {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 12px;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0) 100%);
  color: white;
  transform: translateY(100%);
  transition: all 0.5s ease;
  scroll-margin-top: 76px;
  /* 导航栏高度 + 额外间距 */
}

.gallery-item:hover .gallery-content {
  transform: translateY(0);
}

.gallery-title {
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 4px;
  color: white;
}

.gallery-description {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 4px;
}

.gallery-item-tag {
  display: inline-block;
  padding: 2px 8px;
  margin: 0 4px 4px 0;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.75rem;
}

.gallery-stats {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 8px;
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.6);
}

.gallery-author {
  display: flex;
  align-items: center;
  gap: 8px;
}

.gallery-author img {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.1);
}

/* 创意展示区容器 */
.showcase-container {
  display: flex;
  width: 100%;
  height: calc(100vh - 60px);
  position: relative;
  background: #fff;
  margin-top: 0;
  border-top: 1px solid rgba(71, 102, 194, 0.15);
}

/* 优化侧边栏样式 */
.showcase-sidebar {
  width: 240px;
  min-width: 240px;
  position: sticky;
  top: 60px;
  /* 导航栏高度 */
  height: calc(100vh - 60px);
  background: #fafbff;
  border-right: 1px solid #e5eeff;
  overflow-y: auto;
  z-index: 20;
  display: flex;
  flex-direction: column;
}

/* 优化标题和标签栏样式 */
.showcase-header {
  position: sticky;
  top: 0;
  background: white;
  padding: 1rem 2rem 0 2rem;
  border-bottom: 1px solid #e5eeff;
  z-index: 10;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
}

/* 内容区域样式 */
.showcase-content {
  flex: 1;
  min-width: 0;
  background: white;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* 筛选按钮样式优化 */
.filter-dropdown {
  position: relative;
}

.filter-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: #f6f9ff;
  border: 1px solid #e5eeff;
  border-radius: 6px;
  color: #4766c2;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.filter-btn:hover {
  background: #edf2ff;
  border-color: #4766c2;
}

/* 标题样式优化 */
.showcase-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 0.5rem;
}

.showcase-title h2 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #333;
}

/* 响应式调整 */
@media (max-width: 1024px) {
  .showcase-sidebar {
    width: 200px;
    min-width: 200px;
  }

  .showcase-container {
    height: auto;
    min-height: calc(100vh - 60px);
  }
}

@media (max-width: 768px) {
  .showcase-container {
    flex-direction: column;
  }

  .showcase-sidebar {
    position: relative;
    top: 0;
    width: 100%;
    height: auto;
    min-height: auto;
    border-right: none;
    border-bottom: 1px solid #e5eeff;
    flex-direction: row;
    overflow-x: auto;
    padding: 0.75rem;
  }

  .sidebar-group {
    display: flex;
    flex-shrink: 0;
    margin-right: 1rem;
    margin-bottom: 0;
    border-bottom: none;
  }

  .sidebar-group-title {
    padding: 0.75rem 1rem;
    border-bottom: none;
    white-space: nowrap;
  }

  .side-nav-item {
    white-space: nowrap;
    padding: 0.75rem 1rem;
    margin: 0 0.25rem;
  }

  /* 隐藏底部内容 */
  .sidebar-footer {
    display: none;
  }
}

/* 移除装饰分割线的固定定位 */
.showcase-header::before {
  display: none;
}

/* 侧边栏分组标题 */
.sidebar-group-title {
  padding: 0.75rem 1.25rem;
  color: #8a94a6;
  font-size: 0.8125rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  position: relative;
}

.sidebar-group-title::after {
  content: '';
  position: absolute;
  left: 1.25rem;
  right: 1.25rem;
  bottom: 0;
  height: 1px;
  background: linear-gradient(to right, #e5eeff 50%, transparent 50%);
  background-size: 8px 1px;
  background-repeat: repeat-x;
}

/* 侧边栏项目式 */
.side-nav-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.side-nav-item:hover {
  background: rgba(71, 102, 194, 0.05);
}

.side-nav-item.active {
  background: rgba(71, 102, 194, 0.1);
  color: #4766c2;
}

.side-nav-item.active .side-nav-icon {
  color: #4766c2;
}

.side-nav-icon {
  margin-right: 0.75rem;
  display: flex;
  align-items: center;
  color: #666;
  transition: color 0.2s ease;
}

/* 子标题样式 */
.subtitle {
  font-size: 0.75rem;
  color: #8a94a6;
  margin-left: 34px;
}

/* 图标容器 */
.side-nav-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  color: currentColor;
  opacity: 0.75;
  transition: all 0.2s ease;
}

/* 悬浮和激活状态 */
.side-nav-item:hover {
  background: linear-gradient(to right, #f6f9ff, transparent);
  color: #4766c2;
}

.side-nav-item.active {
  background: linear-gradient(to right, #f6f9ff, transparent);
  color: #4766c2;
  font-weight: 500;
}

.side-nav-item:hover .side-nav-icon,
.side-nav-item.active .side-nav-icon {
  opacity: 1;
}

/* 滚动条样式 */
.showcase-sidebar::-webkit-scrollbar {
  width: 0px;
}

/* 分组间的间距 */
.sidebar-group {
  padding: 0;
  margin-bottom: 0.5rem;
}

.sidebar-group:last-of-type {
  margin-bottom: 0;
  padding-bottom: 1rem;
}

/* 分割线 */
.sidebar-divider {
  height: 1px;
  background: #f0f0f0;
  margin: 0.5rem 0;
}

/* 内容区 */
.showcase-content {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  scrollbar-width: none;
  /* Firefox */
  -ms-overflow-style: none;
  /* IE and Edge */
}

/* Chrome, Safari and Opera */
.showcase-content::-webkit-scrollbar {
  display: none;
}

.content-container::-webkit-scrollbar {
  display: none;
}

/* 响应式调整 */
@media (max-width: 1024px) {
  .showcase-container {
    flex-direction: column;
  }

  .showcase-sidebar {
    position: static;
    width: 100%;
    height: auto;
    border-right: none;
    border-bottom: 1px solid #eee;
    padding: 0.75rem;
    display: flex;
    overflow-x: auto;
  }

  .side-nav-item {
    white-space: nowrap;
    padding: 0.75rem 1.25rem;
  }

  .side-nav-item.active::before {
    width: 100%;
    height: 3px;
    top: auto;
    bottom: -0.75rem;
  }

  .showcase-content {
    padding: 1.5rem;
  }
}

/* 移除标题相关样式 */
.showcase-title {
  display: none;
}

.gallery-tags {
  display: flex;
  flex-wrap: nowrap;
  gap: 2rem;
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
  white-space: nowrap;
  border-bottom: 1px solid #f0f0f0;
}

.gallery-tags::-webkit-scrollbar {
  display: none;
}

.gallery-tag {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 0.5rem 0;
  color: #666;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
  position: relative;
}

.gallery-tag .tag-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  opacity: 0.6;
  transition: opacity 0.2s ease;
}

.gallery-tag:hover {
  color: #4766c2;
}

.gallery-tag:hover .tag-icon {
  opacity: 1;
}

.gallery-tag.active {
  color: #4766c2;
  font-weight: 500;
}

.gallery-tag.active .tag-icon {
  opacity: 1;
}

.gallery-tag.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 2px;
  background: #4766c2;
  border-radius: 1px;
}

/* 标签栏滚动阴影 */
.gallery-tags::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  width: 40px;
  background: linear-gradient(to right, rgba(255, 255, 255, 0), #fff);
  pointer-events: none;
}

/* 深色模式开关样式 */
.dark-mode-toggle {
  display: flex;
  justify-content: space-between !important;
  margin-top: auto;
  padding: 0.75rem 1.25rem;
  border-top: 1px solid #e5eeff;
  background: linear-gradient(to right, #f6f9ff, transparent);
}

.dark-mode-switch {
  position: relative;
  width: 40px;
  height: 20px;
}

.dark-mode-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.dark-mode-switch label {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #eee;
  transition: 0.5s;
  border-radius: 34px;
}

.dark-mode-switch label:before {
  position: absolute;
  content: '';
  height: 16px;
  width: 16px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  transition: 0.5s;
  border-radius: 50%;
}

.dark-mode-switch input:checked+label {
  background-color: #4766c2;
}

.dark-mode-switch input:checked+label:before {
  transform: translateX(20px);
}

/* 侧边栏底部样式 */
.sidebar-footer {
  margin-top: auto;
  padding: 1rem 1.25rem;
  border-top: 1px solid #f0f0f0;
}

/* 加入我们按钮样式优化 */
.join-us-btn {
  margin: 1rem;
  padding: 0.75rem 1rem;
  background: #fff0f0;
  border: 2px solid #4766c2;
  border-radius: 0.75rem;
  color: #4766c2;
  font-weight: 600;
  transform-style: preserve-3d;
  transition: transform 150ms cubic-bezier(0, 0, 0.58, 1), background 150ms cubic-bezier(0, 0, 0.58, 1);
  position: relative;
}

.join-us-btn::before {
  position: absolute;
  content: '';
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: #edf2ff;
  border-radius: inherit;
  box-shadow: 0 0 0 2px #4766c2, 0 0.4em 0 0 #f6f9ff;
  transform: translate3d(0, 0.5em, -1em);
  transition: transform 150ms cubic-bezier(0, 0, 0.58, 1), box-shadow 150ms cubic-bezier(0, 0, 0.58, 1);
}

.join-us-btn:hover {
  background: #f6f9ff;
  transform: translate(0, 0.15em);
}

.join-us-btn:hover::before {
  box-shadow: 0 0 0 2px #4766c2, 0 0.3em 0 0 #f6f9ff;
  transform: translate3d(0, 0.3em, -1em);
}

.join-us-btn:active {
  background: #f6f9ff;
  transform: translate(0, 0.5em);
}

.join-us-btn:active::before {
  box-shadow: 0 0 0 2px #4766c2, 0 0 #f6f9ff;
  transform: translate3d(0, 0, -1em);
}

/* 底部链接样式 */
.footer-links {
  padding: 1rem 1.25rem;
  border-top: 1px solid #e5eeff;
}

.footer-link-group {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
}

.footer-link-group a {
  color: #8a94a6;
  font-size: 0.75rem;
  text-decoration: none;
  transition: color 0.2s ease;
}

.footer-link-group a:hover {
  color: #4766c2;
}

/* 公司信息 */
.footer-company {
  font-size: 0.75rem;
  color: #b0b7c3;
  line-height: 1.5;
}

/* 优化图标对齐 */
.side-nav-icon svg {
  width: 18px;
  height: 18px;
  opacity: 0.8;
  transition: opacity 0.2s ease;
}

.side-nav-item:hover .side-nav-icon svg,
.side-nav-item.active .side-nav-icon svg {
  opacity: 1;
}

/* 添加分组之间的分隔 */
.sidebar-group+.sidebar-group {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e5eeff;
}

/* 创作组样式优化 */
.sidebar-group.creation-group .side-nav-item {
  margin: 0.5rem 1rem;
  padding: 0.75rem 1rem;
  background: #fff0f0;
  border: 2px solid #4766c2;
  border-radius: 0.75rem;
  transform-style: preserve-3d;
  transition: transform 150ms cubic-bezier(0, 0, 0.58, 1), background 150ms cubic-bezier(0, 0, 0.58, 1);
  position: relative;
}

.sidebar-group.creation-group .side-nav-item::before {
  position: absolute;
  content: '';
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: #edf2ff;
  border-radius: inherit;
  box-shadow: 0 0 0 2px #4766c2, 0 0.4em 0 0 #f6f9ff;
  transform: translate3d(0, 0.5em, -1em);
  transition: transform 150ms cubic-bezier(0, 0, 0.58, 1), box-shadow 150ms cubic-bezier(0, 0, 0.58, 1);
}

.sidebar-group.creation-group .side-nav-item:hover {
  background: #f6f9ff;
  transform: translate(0, 0.15em);
}

.sidebar-group.creation-group .side-nav-item:hover::before {
  box-shadow: 0 0 0 2px #4766c2, 0 0.3em 0 0 #f6f9ff;
  transform: translate3d(0, 0.3em, -1em);
}

.sidebar-group.creation-group .side-nav-item:active {
  background: #f6f9ff;
  transform: translate(0, 0.5em);
}

.sidebar-group.creation-group .side-nav-item:active::before {
  box-shadow: 0 0 0 2px #4766c2, 0 0 #f6f9ff;
  transform: translate3d(0, 0, -1em);
}

/* 优化深色模式开关位置 */
.dark-mode-toggle {
  margin-top: 0.5rem;
  /* 减少上方空间 */
  padding: 0.75rem 1.25rem;
  border-top: 1px solid #f0f0f0;
}

/* 调整分组间距 */
.sidebar-group {
  padding: 0;
  margin-bottom: 0.5rem;
}

.sidebar-group.creation-group {
  margin-bottom: 0.75rem;
}

/* 子标位置优化 */
.sidebar-group.creation-group .subtitle {
  margin-left: 0;
  margin-top: 0.25rem;
  display: block;
}

/* 标题区域样式 */
.showcase-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.showcase-title h2 {
  font-size: 1.25rem;
  font-weight: 500;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

/* 筛选按钮样式 */
.filter-dropdown {
  position: relative;
}

.filter-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 0.5rem 1rem;
  background: #f6f9ff;
  border: 1px solid #e5eeff;
  border-radius: 6px;
  color: #4766c2;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.filter-btn:hover {
  background: #edf2ff;
  border-color: #4766c2;
}

.filter-menu {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 0.5rem;
  background: white;
  border: 1px solid #e5eeff;
  border-radius: 8px;
  padding: 1rem;
  min-width: 200px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.filter-group {
  margin-bottom: 1rem;
}

.filter-group:last-child {
  margin-bottom: 0;
}

.filter-title {
  font-size: 0.875rem;
  color: #666;
  margin-bottom: 0.5rem;
}

.filter-options {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filter-options label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #333;
  cursor: pointer;
}

/* 视图切换按钮 */
.view-mode {
  display: flex;
  gap: 0.5rem;
}

.view-btn {
  padding: 0.5rem;
  background: #f6f9ff;
  border: 1px solid #e5eeff;
  border-radius: 6px;
  color: #666;
  cursor: pointer;
  transition: all 0.2s ease;
}

.view-btn.active,
.view-btn:hover {
  background: #edf2ff;
  border-color: #4766c2;
  color: #4766c2;
}

/* 内容卡片样式优化 */
.gallery-item-inner {
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  background: white;
  border: 1px solid #e5eeff;
  transition: all 0.5s ease;
}

.gallery-item-inner:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.gallery-actions {
  position: absolute;
  top: 1rem;
  right: 1rem;
  display: flex;
  gap: 0.5rem;
  opacity: 0;
  transform: translateY(-10px);
  transition: all 0.5s ease;
}

.gallery-item-inner:hover .gallery-actions {
  opacity: 1;
  transform: translateY(0);
}

.action-btn {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  border: none;
  color: #666;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: white;
  color: #4766c2;
}

/* 内容元信息样式 */
.gallery-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 0.75rem;
}

.meta-left {
  display: flex;
  gap: 1rem;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #666;
  font-size: 0.75rem;
}

.model-tag {
  padding: 0.2rem 0.4rem;
  background: rgba(71, 102, 194, 0.1);
  color: #4766c2;
  border-radius: 4px;
  font-size: 0.7rem;
  font-weight: 500;
}

.author {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.author img {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  object-fit: cover;
}

.author span {
  font-size: 0.75rem;
  color: #666;
}

/* 添加遮罩层样式 */
.filter-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: transparent;
  z-index: 90;
}

/* 调整筛选菜单级别 */
.filter-menu {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 0.5rem;
  background: white;
  border: 1px solid #e5eeff;
  border-radius: 8px;
  padding: 1rem;
  min-width: 200px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 100;
}

/* 创作工具区域样式 */
.creation-tools-section {
  position: relative;
  z-index: 1;
  padding: 4rem 2rem;
  margin: 2rem auto;
  max-width: 2200px;
  scroll-margin-top: 76px;
}

.section-header {
  text-align: center;
  margin-bottom: 3rem;
}

.section-header h2 {
  font-size: 2.5rem;
  font-weight: 600;
  color: #4766c2;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, #4766c2, #6c8ae4);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 2px 10px rgba(71, 102, 194, 0.2);
}

.section-header p {
  font-size: 1.1rem;
  color: #666;
  max-width: 600px;
  margin: 0 auto;
}

.tools-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
  padding: 1rem;
}

.tool-card {
  position: relative;
  background: rgba(255, 255, 255, 0.85);
  border-radius: 24px;
  padding: 2rem;
  text-align: center;
  transition: all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
  border: 1px solid rgba(71, 102, 194, 0.15);
  box-shadow: 0 4px 20px rgba(71, 102, 194, 0.1), inset 0 0 0 1px rgba(255, 255, 255, 0.5);
  transform-style: preserve-3d;
  perspective: 1000px;
  backdrop-filter: blur(10px);
  will-change: transform, box-shadow, border-color, background;
  -webkit-tap-highlight-color: transparent;
  outline: none;
  user-select: none;
  cursor: pointer;
}

.tool-card * {
  pointer-events: none;
}

/* 允许按钮响应事件 */
.tool-card .create-btn {
  pointer-events: auto;
}

.tool-card:hover {
  transform: translateY(-10px) rotateX(5deg);
  box-shadow: 0 20px 40px rgba(71, 102, 194, 0.2), inset 0 0 0 2px rgba(71, 102, 194, 0.5);
  border-color: rgba(71, 102, 194, 0.5);
  background: rgba(255, 255, 255, 0.95);
}

.tool-card::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: 24px;
  padding: 2px;
  background: linear-gradient(135deg, rgba(71, 102, 194, 0.2), rgba(108, 138, 228, 0.2));
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  opacity: 1;
  transition: opacity 0.5s ease;
}

.tool-card:hover::before {
  background: linear-gradient(135deg, rgba(71, 102, 194, 0.8), rgba(108, 138, 228, 0.8));
}

.tool-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #edf2ff, #f6f9ff);
  border-radius: 24px;
  transition: all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
  transform-style: preserve-3d;
  transform: translateZ(20px);
  box-shadow: 0 8px 24px rgba(71, 102, 194, 0.15), inset 0 0 0 1px rgba(71, 102, 194, 0.2);
  position: relative;
  will-change: transform, background, box-shadow;
  overflow: hidden;
  -webkit-tap-highlight-color: transparent;
  outline: none;
  user-select: none;
  pointer-events: none;
}

.tool-icon * {
  -webkit-tap-highlight-color: transparent;
  outline: none;
  user-select: none;
  pointer-events: none;
}

.tool-icon svg {
  transition: all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
  width: 32px;
  height: 32px;
  will-change: transform, color, filter;
  color: #4766c2;
  filter: drop-shadow(0 2px 4px rgba(71, 102, 194, 0.2));
  pointer-events: none;
  -webkit-user-drag: none;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
}

.tool-card:hover .tool-icon {
  transform: translateZ(30px) scale(1.1);
  background: linear-gradient(135deg, #4766c2, #6c8ae4);
  box-shadow: 0 12px 36px rgba(71, 102, 194, 0.3), inset 0 0 0 1px rgba(255, 255, 255, 0.5);
}

.tool-card:hover .tool-icon::before {
  opacity: 0;
}

.tool-card:hover .tool-icon::after {
  opacity: 1;
}

.tool-card:hover .tool-icon svg {
  color: white;
  transform: scale(1.1);
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.tool-card h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1rem;
  transform: translateZ(10px);
  transition: all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  will-change: transform, color, text-shadow;
}

.tool-card:hover h3 {
  color: #4766c2;
  transform: translateZ(20px);
  text-shadow: 0 4px 8px rgba(71, 102, 194, 0.2);
}

.tool-card p {
  font-size: 1rem;
  color: #666;
  margin-bottom: 2rem;
  line-height: 1.6;
  transform: translateZ(5px);
  transition: all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
  will-change: transform, color;
}

.tool-card:hover p {
  color: #4766c2;
  transform: translateZ(15px);
}

.create-btn {
  -webkit-tap-highlight-color: transparent;
  outline: none;
  user-select: none;
  cursor: pointer;
  background: rgba(71, 102, 194, 0.1);
  color: #4766c2;
  border: 1px solid rgba(71, 102, 194, 0.2);
  padding: 0.75rem 2rem;
  border-radius: 12px;
  font-weight: 500;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateZ(15px);
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(71, 102, 194, 0.1), inset 0 0 0 1px rgba(255, 255, 255, 0.5);
  isolation: isolate;
  will-change: transform, box-shadow, border-color, color;
  pointer-events: auto;
}

.create-btn::before {
  content: '';
  position: absolute;
  inset: -1px;
  background: linear-gradient(135deg, #4766c2, #6c8ae4);
  border-radius: inherit;
  opacity: 0;
  transition: opacity 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: -1;
  will-change: opacity;
}

.create-btn::after {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, #4766c2, #6c8ae4);
  border-radius: inherit;
  opacity: 0;
  transition: opacity 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: -1;
}

.create-btn:hover {
  color: white;
  border-color: transparent;
  transform: translateZ(25px);
  box-shadow: 0 10px 30px rgba(71, 102, 194, 0.3);
}

.create-btn:hover::before,
.create-btn:hover::after {
  opacity: 1;
}

.create-btn span {
  position: relative;
  z-index: 1;
  mix-blend-mode: multiply;
  transition: mix-blend-mode 0.5s ease;
  pointer-events: none;
}

.create-btn:hover span {
  mix-blend-mode: normal;
}

/* 响应式布局调整 */
@media (max-width: 1920px) {
  .creation-tools-section {
    max-width: 1700px;
  }

  .tools-container {
    grid-template-columns: repeat(3, minmax(300px, 1fr));
    gap: 2rem;
  }
}

@media (max-width: 1400px) {
  .creation-tools-section {
    max-width: 1200px;
    padding: 3rem 1.5rem;
  }

  .tools-container {
    grid-template-columns: repeat(3, minmax(250px, 1fr));
    gap: 1.5rem;
  }

  .tool-card {
    padding: 1.5rem;
  }
}

@media (max-width: 1024px) {
  .tools-container {
    grid-template-columns: repeat(2, 1fr);
  }

  .section-header h2 {
    font-size: 2rem;
  }
}

@media (max-width: 768px) {
  .creation-tools-section {
    padding: 2rem 1rem;
  }

  .tools-container {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .tool-card {
    padding: 1.5rem;
  }

  .section-header h2 {
    font-size: 1.75rem;
  }
}

/* 作品网格样式 */
.works-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 1rem;
}

.work-item {
  background: white;
  border-radius: 20px;
  border: 1px solid #e5eeff;
  transition: 0.2s ease-in-out;
  margin-bottom: 1rem;
  overflow: hidden;
  height: 340px;
}

.work-item:hover {
  cursor: pointer;
  transform: translateY(-5px);
  border-color: #4766c2;
}

.work-item-inner {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.work-image {
  width: 100%;
  height: 180px;
  position: relative;
  background: linear-gradient(135deg, #4766c2, #6c8ae4);
}

.work-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.work-item:hover .work-image img {
  transform: scale(1.05);
}

.work-content {
  padding: 0.75rem;
  flex: 1;
  display: flex;
  flex-direction: column;
  background: white;
}

.work-title {
  font-family: 'Lucida Sans', sans-serif;
  font-size: 1rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.25rem;
  line-height: 1.3;
}

.work-description {
  font-family: 'Lucida Sans', sans-serif;
  color: #666;
  font-size: 0.813rem;
  margin-bottom: 0.75rem;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-height: 1.5;
}

.work-meta {
  margin-top: auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.meta-left {
  display: flex;
  gap: 1rem;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem 0.5rem;
  background-color: #f6f8ff;
  border-radius: 10px;
  color: #4766c2;
  font-size: 0.75rem;
}

.meta-item svg {
  width: 14px;
  height: 14px;
}

.author {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.author img {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  object-fit: cover;
}

.author span {
  font-size: 0.75rem;
  color: #666;
}

/* 添加响应式布局 */
@media (max-width: 1600px) {
  .works-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (max-width: 1280px) {
  .works-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 1024px) {
  .works-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .showcase-content {
    padding: 0 1rem;
  }
}

@media (max-width: 640px) {
  .works-grid {
    grid-template-columns: 1fr;
  }
}

/* 激活状态的侧边栏项目 */
.side-nav-item.active {
  color: #4766c2;
  background: rgba(71, 102, 194, 0.1);
}

.side-nav-item.active .side-nav-icon {
  color: #4766c2;
}

/* 创作项目样式 */
.side-nav-item.creation-item {
  margin: 0.5rem 1rem;
  padding: 0.75rem 1rem;
  background: #6c8ae4;
  border: 2px solid #4766c2;
  border-radius: 0.75rem;
  color: white;
  transform-style: preserve-3d;
  transition: transform 150ms cubic-bezier(0, 0, 0.58, 1), background 150ms cubic-bezier(0, 0, 0.58, 1);
  position: relative;
}

.side-nav-item.creation-item::before {
  position: absolute;
  content: '';
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: #edf2ff;
  border-radius: inherit;
  box-shadow: 0 0 0 2px #4766c2, 0 0.4em 0 0 #f6f9ff;
  transform: translate3d(0, 0.5em, -1em);
  transition: transform 150ms cubic-bezier(0, 0, 0.58, 1), box-shadow 150ms cubic-bezier(0, 0, 0.58, 1);
}

.side-nav-item.creation-item:hover {
  background: #7d98e8;
  transform: translate(0, 0.15em);
}

.side-nav-item.creation-item:hover::before {
  box-shadow: 0 0 0 2px #4766c2, 0 0.3em 0 0 #f6f9ff;
  transform: translate3d(0, 0.3em, -1em);
}

.side-nav-item.creation-item:active {
  background: #f6f9ff;
  transform: translate(0, 0.5em);
}

.side-nav-item.creation-item:active::before {
  box-shadow: 0 0 0 2px #4766c2, 0 0 #f6f9ff;
  transform: translate3d(0, 0, -1em);
}

.side-nav-item.creation-item .subtitle {
  color: rgba(255, 255, 255, 0.8);
  margin-top: 0.25rem;
  position: relative;
  z-index: 1;
}

.side-nav-item.creation-item .side-nav-icon {
  color: white;
  position: relative;
  z-index: 1;
}

/* 确保内容在3D效果上层 */
.side-nav-item.creation-item>* {
  position: relative;
  z-index: 1;
}

/* 为不同类别的创作按钮添加不同的背景色 */
.sidebar-group:nth-child(2) .creation-item,
.sidebar-group:nth-child(3) .creation-item {
  background: #6c8ae4;
}

/* 未开发功能的样式 */
.side-nav-item.developing {
  opacity: 0.8;
  cursor: not-allowed;
}

.side-nav-item.developing:hover {
  background: rgba(71, 102, 194, 0.05);
}

/* 开发中提示的样式 */
.developing-message {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 1rem 2rem;
  border-radius: 0.5rem;
  z-index: 1000;
}

/* 加入我们按钮样式 */
.side-nav-item.join-us-btn {
  margin: 0.5rem 1rem;
  padding: 0.75rem 1.25rem;
  background-color: #4766c2;
  border: 2px solid #3755ab;
  border-radius: 12px;
  color: white;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: all 0.5s;
  font-weight: 600;
  box-shadow: 0 2px 0 2px #2b4494;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.side-nav-item.join-us-btn::before {
  content: '';
  position: absolute;
  width: 100px;
  height: 120%;
  background-color: #6c8ae4;
  top: 50%;
  transform: skewX(30deg) translate(-150%, -50%);
  transition: all 0.5s;
}

.side-nav-item.join-us-btn:hover {
  background-color: #5577d3;
  color: white;
  box-shadow: 0 2px 0 2px #2b4494;
}

.side-nav-item.join-us-btn:hover::before {
  transform: skewX(30deg) translate(150%, -50%);
  transition-delay: 0.1s;
}

.side-nav-item.join-us-btn:active {
  transform: scale(0.95);
}

.side-nav-item.join-us-btn .side-nav-icon {
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  /* 确保图标在动画效果上层 */
  z-index: 1;
}

.side-nav-item.join-us-btn span {
  position: relative;
  /* 确保文字在动画效果上层 */
  z-index: 1;
}

/* 移除重复的加入我们按钮 */
.sidebar-footer .join-us-btn {
  display: none;
}

/* 优化底部布局 */
.sidebar-footer {
  margin-top: auto;
  padding: 1rem;
  border-top: 1px solid #e5eeff;
}

.footer-links {
  padding-top: 1rem;
  opacity: 0.8;
  font-size: 0.75rem;
}

.footer-link-group {
  display: flex;
  gap: 1rem;
  margin-bottom: 0.5rem;
}

.footer-link-group a {
  color: #666;
  text-decoration: none;
  transition: color 0.2s ease;
}

.footer-link-group a:hover {
  color: #4766c2;
}

/* 作品卡片样式 */
.work-item {
  background: white;
  border-radius: 20px;
  border: 1px solid #e5eeff;
  transition: 0.2s ease-in-out;
  margin-bottom: 1rem;
  overflow: hidden;
  height: 340px;
}

.work-item:hover {
  cursor: pointer;
  transform: translateY(-5px);
  border-color: #4766c2;
}

.work-item-inner {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.work-image {
  width: 100%;
  height: 180px;
  position: relative;
  background: linear-gradient(135deg, #4766c2, #6c8ae4);
}

.work-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.work-item:hover .work-image img {
  transform: scale(1.05);
}

/* 自定义滚动条样式 */
.activity-festival-page {
  scrollbar-width: thin;
  scrollbar-color: rgba(71, 102, 194, 0.3) rgba(71, 102, 194, 0.1);
}

.activity-festival-page::-webkit-scrollbar {
  width: 8px;
}

.activity-festival-page::-webkit-scrollbar-track {
  background: rgba(71, 102, 194, 0.1);
  border-radius: 10px;
}

.activity-festival-page::-webkit-scrollbar-thumb {
  background: rgba(71, 102, 194, 0.3);
  border-radius: 10px;
  transition: background 0.3s ease;
}

.activity-festival-page::-webkit-scrollbar-thumb:hover {
  background: rgba(71, 102, 194, 0.5);
}

/* 活动详情容器滚动条样式 */
.activity-detail-container {
  scrollbar-width: thin;
  scrollbar-color: rgba(71, 102, 194, 0.2) rgba(71, 102, 194, 0.05);
}

.activity-detail-container::-webkit-scrollbar {
  width: 6px;
}

.activity-detail-container::-webkit-scrollbar-track {
  background: rgba(71, 102, 194, 0.05);
  border-radius: 8px;
}

.activity-detail-container::-webkit-scrollbar-thumb {
  background: rgba(71, 102, 194, 0.2);
  border-radius: 8px;
  transition: background 0.3s ease;
}

.activity-detail-container::-webkit-scrollbar-thumb:hover {
  background: rgba(71, 102, 194, 0.4);
}

/* 其他元素保持隐藏滚动条 */
*:not(.activity-festival-page) {
  scrollbar-width: none;
  /* Firefox */
  -ms-overflow-style: none;
  /* IE and Edge */
}

*:not(.activity-festival-page)::-webkit-scrollbar {
  display: none;
  /* Chrome, Safari, Opera */
}

/* 主容器 */
.main-container {
  overflow-y: auto;
  height: 100vh;
}

/* 展示区容器 */
.showcase-container {
  overflow: hidden;
  height: calc(100vh - 60px);
  /* 减去导航栏高度 */
}

/* 内容区 */
.showcase-content {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

/* 内容容器 */
.content-container {
  flex: 1;
  padding: 0.5rem 2rem;
  width: 100%;
  overflow-y: auto;
}

@media screen and (max-width: 1024px) {
  .carousel-container {
    max-width: 100%;
    margin: 0 auto;
  }

  .carousel-container .slick-slide {
    padding: 0 0.5rem;
  }
}

@media screen and (max-width: 768px) {
  .carousel-container .slick-slide>div {
    margin: 0 -0.5rem;
  }

  /* Logo 容器在小屏幕下的调整 */
  .motion.div {
    margin-top: 2rem !important;
  }
}

/* 确保 Logo 容器在所有屏幕尺寸下都保持合适的比例 */
@media screen and (max-width: 480px) {
  .motion.div {
    margin-top: 1rem !important;
  }
}

/* 添加响应式布局相关样式 */
@media screen and (max-width: 1024px) {
  .carousel-container {
    max-width: 100%;
    margin: 0 auto;
  }

  .carousel-container .slick-slide {
    padding: 0 0.5rem;
  }
}

@media screen and (max-width: 768px) {
  .carousel-container .slick-slide>div {
    margin: 0 -0.5rem;
  }
}

/* 优化标题和内容在不同屏幕尺寸下的显示 */
@media screen and (max-width: 640px) {
  h1.text-4xl {
    font-size: 2.5rem;
    line-height: 1.2;
  }

  p.text-base {
    font-size: 1rem;
    line-height: 1.5;
  }

  .carousel-container {
    margin-top: 1rem;
  }
}

/* Logo 容器响应式调整 */
@media screen and (max-width: 480px) {
  .motion-div {
    height: 200px !important;
    margin-top: 1rem !important;
  }
}

/* 确保内容在小屏幕上有适当的间距 */
@media screen and (max-width: 768px) {
  .min-h-\[calc\(80vh-6rem\)\] {
    min-height: auto;
    padding-bottom: 2rem;
  }
}

.create-btn:focus {
  outline: none;
}

.create-btn:focus-visible {
  outline: none;
  box-shadow: 0 4px 12px rgba(71, 102, 194, 0.1), inset 0 0 0 2px rgba(71, 102, 194, 0.4);
}

/* 禁用所有可能的默认交互状态 */
.tool-card:focus,
.tool-card:focus-within,
.tool-card:focus-visible,
.tool-icon:focus,
.tool-icon:focus-within,
.tool-icon:focus-visible,
.tool-icon svg:focus,
.tool-icon svg:focus-within,
.tool-icon svg:focus-visible {
  outline: none;
  box-shadow: none;
  -webkit-tap-highlight-color: transparent;
}

/* 隐藏默认的 Slick 箭头 */
.slick-prev:before,
.slick-next:before {
  display: none !important;
}

/* 自定义箭头按钮样式 */
.slick-prev,
.slick-next {
  width: 40px !important;
  height: 40px !important;
  z-index: 10 !important;
}

.slick-prev:hover,
.slick-next:hover {
  opacity: 1 !important;
}

/* 调整箭头位置 */
.slick-prev {
  left: 16px !important;
}

.slick-next {
  right: 16px !important;
}

/* 当只有一张图片时隐藏箭头 */
.slick-slider.single-image .slick-arrow {
  display: none !important;
}

/* 作品轮播图样式 */
.work-slider-container {
  position: relative;
  margin: 0;
  padding: 0;
}

.work-slider {
  position: relative;
}

/* 轮播图片容器样式 */
.work-slider .slick-slide>div {
  padding: 0;
}

/* 图片容器样式 */
.work-slider .slick-slide .relative {
  width: 100%;
  aspect-ratio: 16/9;
  /* 保持 16:9 的宽高比 */
  position: relative;
  overflow: hidden;
}

/* 封面图片样式 */
.work-slider .slick-slide img.object-cover {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 截图样式 */
.work-slider .slick-slide img.object-contain {
  width: 100%;
  height: 100%;
  object-fit: contain;
  background-color: #f5f5f5;
  /* 添加浅灰色背景 */
}

/* 箭头按钮样式 */
.work-slider-arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 40px;
  height: 40px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 20;
  border: none;
  padding: 0;
  transition: all 0.5s ease;
  opacity: 0;
}

.work-slider-container:hover .work-slider-arrow {
  opacity: 1;
}

.work-slider-arrow:hover {
  background: rgba(0, 0, 0, 0.7);
}

.work-slider-arrow.prev {
  left: 16px;
}

.work-slider-arrow.next {
  right: 16px;
}

/* 轮播容器样式 */
.work-slider .slick-list {
  overflow: hidden;
  margin: 0;
  border-radius: 12px;
  /* 添加圆角 */
}

/* 轮播点样式 */
.work-slider .slick-dots {
  bottom: -25px;
}

.work-slider .slick-dots li button:before {
  font-size: 8px;
  color: #4766c2;
}

.work-slider .slick-dots li.slick-active button:before {
  color: #4766c2;
}

/* AI绘画加载动画 */
.analyze svg path.stick {
  transform: translate(0);
  animation: stick 2s ease infinite;
}

.analyze svg path.star-1 {
  fill: #4766c2;
  animation: sparkles 2s ease infinite, scaleStars 2s ease infinite, colorChange 2s ease infinite;
  animation-delay: 150ms;
}

.analyze svg path.star-2 {
  fill: #6c8ae4;
  animation: sparkles 2s ease infinite, scaleStars 2s ease infinite, colorChange 2s ease infinite;
}

.board {
  animation: bounce 2s ease infinite;
}

@keyframes sparkles {
  0% {
    opacity: 1;
  }

  35% {
    opacity: 1;
  }

  55% {
    opacity: 0;
  }

  75% {
    opacity: 1;
  }

  100% {
    opacity: 1;
  }
}

@keyframes stick {
  0% {
    transform: translate3d(0, 0, 0) rotate(0);
  }

  25% {
    transform: translate3d(0, 0, 0) rotate(0);
  }

  50% {
    transform: translate3d(3px, -2px, 0) rotate(8deg);
  }

  75% {
    transform: translate3d(0, 0, 0) rotate(0);
  }

  100% {
    transform: translate3d(0, 0, 0) rotate(0);
  }
}

@keyframes scaleStars {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(0.9);
  }

  100% {
    transform: scale(1);
  }
}

@keyframes bounce {
  0% {
    transform: translateY(0);
  }

  25% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(0);
  }

  75% {
    transform: translateY(-1px);
  }

  100% {
    transform: translateY(0);
  }
}

@keyframes colorChange {
  0% {
    fill: #4766c2;
  }

  25% {
    fill: #6c8ae4;
  }

  50% {
    fill: #4766c2;
  }

  75% {
    fill: #6c8ae4;
  }

  100% {
    fill: #4766c2;
  }
}

/* 添加响应式样式 */
@media screen and (max-width: 640px) {
  .sidebar-footer {
    padding: 1rem;
  }

  .sidebar-footer .flex {
    flex-wrap: wrap;
    gap: 0.5rem;
  }
}

/* 自定义水平滚动条样式 */
.horizontal-scroll-container {
  scrollbar-width: thin;
  /* For Firefox */
  scrollbar-color: #a0aec0 #edf2f7;
  /* For Firefox */
}

/* For Chrome, Safari, and Edge */
.horizontal-scroll-container::-webkit-scrollbar {
  height: 8px;
}

.horizontal-scroll-container::-webkit-scrollbar-track {
  background-color: #edf2f7;
  border-radius: 10px;
}

.horizontal-scroll-container::-webkit-scrollbar-thumb {
  background-color: #a0aec0;
  border-radius: 10px;
  border: 2px solid #edf2f7;
}

.horizontal-scroll-container::-webkit-scrollbar-thumb:hover {
  background-color: #718096;
}


/* Background Animation */
@keyframes animate {
  0% {
    transform: translateY(0) rotate(0deg);
    opacity: 1;
    border-radius: 0;
  }

  100% {
    transform: translateY(-1000px) rotate(720deg);
    opacity: 0;
    border-radius: 50%;
  }
}

.background-squares {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 1;
}

.background-squares li {
  position: absolute;
  display: block;
  list-style: none;
  width: 20px;
  height: 20px;
  background: rgba(255, 255, 255, 0.1);
  /* Make squares more subtle */
  animation: animate 25s linear infinite;
  bottom: -150px;
}

.background-squares li:nth-child(1) {
  left: 25%;
  width: 80px;
  height: 80px;
  animation-delay: 0s;
}

.background-squares li:nth-child(2) {
  left: 10%;
  width: 20px;
  height: 20px;
  animation-delay: 2s;
  animation-duration: 12s;
}

.background-squares li:nth-child(3) {
  left: 70%;
  width: 20px;
  height: 20px;
  animation-delay: 4s;
}

.background-squares li:nth-child(4) {
  left: 40%;
  width: 60px;
  height: 60px;
  animation-delay: 0s;
  animation-duration: 18s;
}

.background-squares li:nth-child(5) {
  left: 65%;
  width: 20px;
  height: 20px;
  animation-delay: 0s;
}

.background-squares li:nth-child(6) {
  left: 75%;
  width: 110px;
  height: 110px;
  animation-delay: 0.5s;
}

.background-squares li:nth-child(7) {
  left: 35%;
  width: 150px;
  height: 150px;
  animation-delay: 7s;
}

.background-squares li:nth-child(8) {
  left: 50%;
  width: 25px;
  height: 25px;
  animation-delay: 15s;
  animation-duration: 45s;
}

.background-squares li:nth-child(9) {
  left: 20%;
  width: 15px;
  height: 15px;
  animation-delay: 2s;
  animation-duration: 35s;
}

.background-squares li:nth-child(10) {
  left: 85%;
  width: 150px;
  height: 150px;
  animation-delay: 0s;
  animation-duration: 11s;
}

/* New Glowing Orb Animation */
.glow-orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(90px);
  opacity: 0.25;
  z-index: 0;
}

@keyframes float {
  0% {
    transform: translate(0, 0);
  }

  50% {
    transform: translate(40px, -50px) scale(1.1);
  }

  100% {
    transform: translate(0, 0);
  }
}

@keyframes float-reverse {
  0% {
    transform: translate(0, 0);
  }

  50% {
    transform: translate(-40px, 50px) scale(0.9);
  }

  100% {
    transform: translate(0, 0);
  }
}

/* Custom styles for Ant Design Select in Login Form */
.student-login-select .ant-select-selector {
  background-color: rgba(255, 255, 255, 0.7) !important;
  border: 1px solid rgba(96, 165, 250, 0.5) !important;
  border-radius: 0.5rem !important;
  color: #1e3a8a !important;
  display: flex;
}

.student-login-select .ant-select-selection-placeholder,
.student-login-select .ant-select-arrow {
  color: rgba(30, 58, 138, 0.6) !important;
  
}

.student-login-select-dropdown {
  background-color: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(96, 165, 250, 0.5) !important;
  border-radius: 0.5rem !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
  
}

.student-login-select-dropdown .ant-select-item {
  color: #000000 !important;
  font-weight: 500 !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
}

.student-login-select-dropdown .ant-select-item-option-active,
.student-login-select-dropdown .ant-select-item-option-selected {
  background-color: rgba(59, 130, 246, 0.15) !important;
  color: #1e3a8a !important;
  border-radius: 6px !important;
}

.student-login-select-dropdown .ant-select-item-empty {
  color: rgba(30, 58, 138, 0.8) !important;
}

.student-login-select.ant-select-focused .ant-select-selector,
.student-login-select .ant-select-selector:hover {
  border-color: #000000 !important;
  /* blue-500 */
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.4) !important;
}

.student-login-select.has-error .ant-select-selector {
  border-color: #ef4444 !important;
  /* red-500 */
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2) !important;
}

.student-login-select .ant-select-selector:hover {
  border-color: #000000 !important;
  /* blue-500 */
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.4) !important;
}

/* 为InlineTeacherAuth组件中的Select添加样式 */
.bg-white\/10 .ant-select-selection-item {
  color: black !important;
  font-weight: 500 !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
}

.bg-white\/10.ant-select-focused .ant-select-selector,
.bg-white\/10 .ant-select-selector:hover {
  border-color: #000000 !important;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.4) !important;
}

/* 针对带有bg-white/10类的元素中的ant-select组件 */
[class*="bg-white/10"] .ant-select-selection-item {
  color: white !important;
  font-weight: 500 !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
}

[class*="bg-white/10"] .ant-select-selector {
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  background-color: rgba(255, 255, 255, 0.05) !important;
}

[class*="bg-white/10"] .ant-select-focused .ant-select-selector,
[class*="bg-white/10"] .ant-select-selector:hover {
  border-color: #000000 !important;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3) !important;
}

/* 为带有bg-white/10类的元素自定义下拉菜单样式 */
.ant-select-dropdown {
  background-color: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(0, 0, 0, 0.5) !important;
  border-radius: 0.75rem !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
}

.ant-select-dropdown .ant-select-item {
  color: #000000 !important;
  font-weight: 500 !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
}

.ant-select-dropdown .ant-select-item-option-active,
.ant-select-dropdown .ant-select-item-option-selected {
  background-color: rgba(59, 130, 246, 0.15) !important;
  color: #1e3a8a !important;
  border-radius: 6px !important;
}

/* 添加输入框错误样式 */
input.has-error {
  border-color: #ef4444 !important; /* red-500 */
  border-width: 2px !important;
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.3) !important;
  animation: errorShake 0.5s ease-in-out !important;
  background-color: rgba(254, 226, 226, 0.3) !important; /* red-100 with transparency */
}

/* 添加错误抖动动画 */
@keyframes errorShake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-4px); }
  20%, 40%, 60%, 80% { transform: translateX(4px); }
}

/* 添加错误状态的占位符和文本颜色 */
input.has-error::placeholder {
  color: #ef4444 !important;
}

input.has-error + label {
  color: #ef4444 !important;
  font-weight: 500 !important;
}

/* 添加缓动函数 */
.slide-in-right, .slide-out-left, 
.slide-in-left, .slide-out-right {
  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1); /* Material Design 标准缓动 */
}

/* 添加颜色渐变动画 */
@keyframes gradientAnimation {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.animate-gradient {
  background: linear-gradient(-45deg, #4766C2, #2a3f87, #6384e0, #3a5bb8);
  background-size: 400% 400%;
  animation: gradientAnimation 15s ease infinite;
}

.animate-gradient-x {
  background: linear-gradient(90deg, #4766C2, #2a3f87, #6384e0, #3a5bb8);
  background-size: 200% 200%;
  animation: gradientAnimation 15s ease infinite;
}

.animate-gradient-move {
  animation: gradientAnimation 15s ease infinite;
}

/* 添加从右往左滑动的动画样式 */
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}
@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutLeft {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(-100%);
    opacity: 0;
  }
}



@keyframes slideOutRight {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0.5;
  }
}

.slide-in-right {
  animation: slideInRight 0.5s forwards;
}

.slide-out-left {
  animation: slideOutLeft 0.5s forwards;
}

.slide-in-left {
  animation: slideInLeft 0.5s forwards;
}

.slide-out-right {
  animation: slideOutRight 0.5s forwards;
}

.slide-container {
  position: fixed;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 50;
  overflow: hidden;
}

.slide-content {
  position: absolute;
  width: 100%;
  max-width: 32rem; /* max-w-xl */
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(147, 51, 234, 0.1) 100%);
  backdrop-filter: blur(10px);
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding: 1.5rem;
}

/* Custom scrollbar styles */
.scrollbar-thin {
  scrollbar-width: thin;
  /* For Firefox */
  scrollbar-color: #000000 #f3f4f6;
  /* For Firefox */
}

/* For Chrome, Safari, and Edge */
.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background-color: #f3f4f6;
  border-radius: 10px;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: #ef4444;
  border-radius: 10px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background-color: #dc2626;
}

/* 添加蓝色滚动条样式 */
.scrollbar-thin.scrollbar-thumb-color {
  scrollbar-color: #413ea8 #ffffff;
  /* For Firefox */
}

.scrollbar-thin.scrollbar-thumb-blue-500::-webkit-scrollbar-thumb {
  background-color: #000000;
}

.scrollbar-thin.scrollbar-thumb-blue-500::-webkit-scrollbar-thumb:hover {
  background-color: #000000;
}

/* logo发光样式 */
.glow-container {
  position: relative;
}

.glow-container img {
  position: relative;
  z-index: 2;
}

.glow-container::before {
  content: "";
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  background: radial-gradient(circle, rgba(255,255,255,0.8) 0%, rgba(70,200,255,0.4) 50%, rgba(0,0,0,0) 70%);
  filter: blur(15px);
  z-index: 1;
  animation: pulse 2s infinite alternate;
}

@keyframes pulse {
  0% { opacity: 0.7; transform: scale(0.95); }
  100% { opacity: 1; transform: scale(1.05); }
}

/* 彩虹光晕效果 */
@keyframes rainbowGlow {
  0% { filter: drop-shadow(0 0 15px rgba(255, 0, 0, 0.9)); }
  20% { filter: drop-shadow(0 0 15px rgba(255, 165, 0, 0.9)); }
  40% { filter: drop-shadow(0 0 15px rgba(255, 255, 0, 0.9)); }
  60% { filter: drop-shadow(0 0 15px rgba(0, 255, 0, 0.9)); }
  80% { filter: drop-shadow(0 0 15px rgba(0, 0, 255, 0.9)); }
  100% { filter: drop-shadow(0 0 15px rgba(255, 0, 0, 0.9)); }
}

/* 霓虹发光效果 */
@keyframes neonGlow {
  0% { filter: drop-shadow(0 0 5px #fff) drop-shadow(0 0 15px #0ff) drop-shadow(0 0 25px #0ff); }
  50% { filter: drop-shadow(0 0 10px #fff) drop-shadow(0 0 25px #0ff) drop-shadow(0 0 40px #0ff); }
  100% { filter: drop-shadow(0 0 5px #fff) drop-shadow(0 0 15px #0ff) drop-shadow(0 0 25px #0ff); }
}
/* 脉冲蓝色光效果： */
@keyframes blueGlow {
  0% { filter: drop-shadow(0 0 8px rgba(0, 150, 255, 0.7)); }
  50% { filter: drop-shadow(0 0 20px rgba(0, 150, 255, 1)) drop-shadow(0 0 30px rgba(70, 200, 255, 0.9)); }
  100% { filter: drop-shadow(0 0 8px rgba(0, 150, 255, 0.7)); }
}

/* 多层光晕效果： */
/* 霓虹蓝光效果： */
/* 白色/银色光晕 */
/* 金色/黄色光晕： */
/* img {
  filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.9)) 
          drop-shadow(0 0 20px rgba(70, 200, 255, 0.8)) 
          drop-shadow(0 0 30px rgba(120, 170, 255, 0.7));
}
img {
  filter: drop-shadow(0 0 10px #fff) 
          drop-shadow(0 0 20px #0af) 
          drop-shadow(0 0 40px #08f) 
          drop-shadow(0 0 60px #06f);
}
img {
  filter: drop-shadow(0 0 15px rgba(255, 255, 255, 0.9)) 
          drop-shadow(0 0 25px rgba(220, 220, 255, 0.8));
}
img {
  filter: drop-shadow(0 0 15px rgba(255, 230, 140, 0.9)) 
          drop-shadow(0 0 25px rgba(255, 200, 80, 0.8));
} */



/* img {
  animation: blueGlow 6s infinite;
} */
/* .logoGlow {
  mix-blend-mode: screen;
  filter: brightness(1.9) contrast(1.2);
} */

@keyframes sweepLight {
  0% {
    background: linear-gradient(90deg, 
      rgba(255,255,255,0) 0%, 
      rgba(255,255,255,0.9) 5%, 
      rgba(255,255,255,0) 10%);
    background-size: 200% 100%;
    background-position: -100% 0;
  }
  100% {
    background: linear-gradient(90deg, 
      rgba(255,255,255,0) 0%, 
      rgba(255,255,255,0.9) 5%, 
      rgba(255,255,255,0) 10%);
    background-size: 200% 100%;
    background-position: 200% 0;
  }
}



/* 添加新的发光动画类 */
@keyframes glowPulse {
  0% { filter: brightness(1) saturate(1); }
  50% { filter: brightness(3) saturate(2.5) drop-shadow(0 2px 10px rgba(175, 197, 241, 0.6)); }
  100% { filter: brightness(1) saturate(1); }
}

.logoSweep {
  position: relative;
  isolation: isolate;
  animation: glowPulse 6s infinite alternate;
}

/* 全局 Ant Design 组件圆角样式 */
.ant-input,
.ant-input-affix-wrapper,
.ant-select .ant-select-selector,
.ant-input-number,
.ant-textarea {
  border-radius: 12px !important;
}

.ant-input:focus,
.ant-input-affix-wrapper:focus,
.ant-input-focused,
.ant-input-affix-wrapper-focused,
.ant-select-focused .ant-select-selector,
.ant-input-number-focused,
.ant-textarea:focus {
  border-radius: 12px !important;
}

.ant-btn {
  border-radius: 10px !important;
}

.ant-upload {
  border-radius: 10px !important;
}

.ant-upload-list-item {
  border-radius: 8px !important;
}

/* 模态框内的组件圆角 */
.ant-modal .ant-input,
.ant-modal .ant-input-affix-wrapper,
.ant-modal .ant-select .ant-select-selector,
.ant-modal .ant-input-number,
.ant-modal .ant-textarea {
  border-radius: 12px !important;
}


