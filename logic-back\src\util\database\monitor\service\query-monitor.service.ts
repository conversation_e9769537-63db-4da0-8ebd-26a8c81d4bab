import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
export interface SlowQueryRecord {
  query: string;
  parameters?: any[];
  executionTime: number;
  timestamp: Date;
  stack?: string;
  context?: string;
}

export interface QueryMetrics {
  totalQueries: number;
  slowQueries: number;
  averageExecutionTime: number;
  maxExecutionTime: number;
  minExecutionTime: number;
  lastResetTime: Date;
}

@Injectable()
export class QueryMonitorService {
  private readonly logger = new Logger(QueryMonitorService.name);
  private slowQueryThreshold: number;
  private enableSlowQueryLogging: boolean;
  private enableQueryMetrics: boolean;
  private enableStackTrace: boolean;
  private lightweightMode: boolean;
  private samplingRate: number;
  private asyncSlowQueryProcessing: boolean;
  private enableDatabaseMonitoring: boolean;
  private slowQueryRecords: SlowQueryRecord[] = [];
  private queryMetrics: QueryMetrics;
  private maxSlowQueryRecords: number;
  private queryCounter: number = 0; // 用于采样

  constructor(
    private readonly configService: ConfigService,
  ) {
    // 从配置文件读取设置，提供默认值
    this.enableDatabaseMonitoring = this.configService.get<boolean>('database.enableDatabaseMonitoring', true);
    this.lightweightMode = this.configService.get<boolean>('database.lightweightMode', false);
    this.slowQueryThreshold = this.configService.get<number>('database.slowQueryThreshold', 1000);
    this.enableSlowQueryLogging = this.configService.get<boolean>('database.enableSlowQueryLogging', true);
    this.enableQueryMetrics = this.configService.get<boolean>('database.enableQueryMetrics', true);
    this.enableStackTrace = this.configService.get<boolean>('database.enableStackTrace', true);
    this.samplingRate = this.configService.get<number>('database.samplingRate', 100);
    this.asyncSlowQueryProcessing = this.configService.get<boolean>('database.asyncSlowQueryProcessing', true);
    this.maxSlowQueryRecords = this.configService.get<number>('database.maxSlowQueryRecords', 100);

    // 初始化指标
    this.resetMetrics();

    // 根据配置显示启动信息
    if (this.enableDatabaseMonitoring) {
      const mode = this.lightweightMode ? '轻量级模式' : '完整模式';
      const sampling = this.samplingRate < 100 ? `采样率: ${this.samplingRate}%` : '全量监控';
      this.logger.log(`数据库查询监控已启动 [${mode}] - 慢查询阈值: ${this.slowQueryThreshold}ms, ${sampling}`);
    } else {
      this.logger.log('数据库查询监控已禁用');
    }
  }

  /**
   * 记录查询执行
   */
  recordQuery(query: string, executionTime: number, parameters?: any[], context?: string): void {
    // 🔧 主开关检查
    if (!this.enableDatabaseMonitoring) {
      return;
    }

    // 🔧 采样率检查
    this.queryCounter++;
    if (this.samplingRate < 100) {
      const shouldSample = (this.queryCounter % Math.floor(100 / this.samplingRate)) === 0;
      if (!shouldSample) {
        return;
      }
    }

    // 更新指标
    if (this.enableQueryMetrics) {
      this.updateMetrics(executionTime);
    }

    // 检查是否为慢查询
    if (this.enableSlowQueryLogging ) {
      console.log("当前查询为慢查询");
      
      if (this.asyncSlowQueryProcessing) {
        // 🔧 异步处理慢查询记录，减少对主流程的影响
        setImmediate(async () => {
          await this.recordSlowQuery(query, executionTime, parameters, context);
        });
      } else {
        // 同步处理，但不等待数据库操作完成
        this.recordSlowQuery(query, executionTime, parameters, context).catch(error => {
          this.logger.error(`记录慢查询失败: ${error.message}`);
        });
      }
    }
  }

  /**
   * 记录慢查询
   */
  private async recordSlowQuery(query: string, executionTime: number, parameters?: any[], context?: string): Promise<void> {
    const slowQueryRecord: SlowQueryRecord = {
      query: this.lightweightMode ? this.sanitizeQueryLightweight(query) : this.sanitizeQuery(query),
      parameters: this.lightweightMode ? undefined : this.sanitizeParameters(parameters),
      executionTime,
      timestamp: new Date(),
      stack: this.enableStackTrace && !this.lightweightMode ? this.captureStack() : undefined,
      context: this.lightweightMode ? undefined : context
    };

    // 添加到内存记录中（保持向后兼容）
    this.slowQueryRecords.push(slowQueryRecord);

    // 保持内存记录数量限制
    if (this.slowQueryRecords.length > this.maxSlowQueryRecords) {
      this.slowQueryRecords = this.slowQueryRecords.slice(-this.maxSlowQueryRecords);
    }

    // 记录慢查询日志
    this.logSlowQuery(slowQueryRecord);

    // 触发告警
    this.triggerSlowQueryAlert(slowQueryRecord);

    // 注意：不再持久化到数据库，只保留内存记录
  }



  /**
   * 更新查询指标
   */
  private updateMetrics(executionTime: number): void {
    this.queryMetrics.totalQueries++;
    
    if (executionTime >= this.slowQueryThreshold) {
      this.queryMetrics.slowQueries++;
    }

    // 更新执行时间统计
    if (this.queryMetrics.totalQueries === 1) {
      this.queryMetrics.averageExecutionTime = executionTime;
      this.queryMetrics.maxExecutionTime = executionTime;
      this.queryMetrics.minExecutionTime = executionTime;
    } else {
      // 计算平均值
      this.queryMetrics.averageExecutionTime = 
        (this.queryMetrics.averageExecutionTime * (this.queryMetrics.totalQueries - 1) + executionTime) / 
        this.queryMetrics.totalQueries;
      
      // 更新最大最小值
      this.queryMetrics.maxExecutionTime = Math.max(this.queryMetrics.maxExecutionTime, executionTime);
      this.queryMetrics.minExecutionTime = Math.min(this.queryMetrics.minExecutionTime, executionTime);
    }
  }

  /**
   * 记录慢查询日志
   */
  private logSlowQuery(record: SlowQueryRecord): void {
    this.logger.warn(`🐌 慢查询检测 [${record.executionTime}ms] ${record.context || ''}`, {
      query: record.query,
      executionTime: record.executionTime,
      parameters: record.parameters,
      timestamp: record.timestamp,
      context: record.context
    });
  }

  /**
   * 触发慢查询告警
   */
  private triggerSlowQueryAlert(record: SlowQueryRecord): void {
    // 根据执行时间级别触发不同级别的告警
    if (record.executionTime >= 10000) { // 10秒以上
      this.logger.error(`🚨 严重慢查询告警 [${record.executionTime}ms]`, {
        query: record.query,
        executionTime: record.executionTime,
        context: record.context
      });
    } else if (record.executionTime >= 5000) { // 5秒以上
      this.logger.warn(`⚠️ 慢查询告警 [${record.executionTime}ms]`, {
        query: record.query,
        executionTime: record.executionTime,
        context: record.context
      });
    }

    // TODO: 这里可以集成邮件、钉钉、短信等告警方式
    // await this.sendSlowQueryAlert(record);
  }

  /**
   * 清理查询字符串（移除敏感信息）
   */
  private sanitizeQuery(query: string): string {
    if (!query) return '';

    // 限制查询字符串长度
    const maxLength = 1000;
    if (query.length > maxLength) {
      return query.substring(0, maxLength) + '...';
    }

    return query;
  }

  /**
   * 轻量级查询字符串清理（性能优化版本）
   */
  private sanitizeQueryLightweight(query: string): string {
    if (!query) return '';

    // 更短的长度限制，减少字符串处理开销
    const maxLength = 200;
    if (query.length > maxLength) {
      return query.substring(0, maxLength) + '...';
    }

    return query;
  }

  /**
   * 清理参数（移除敏感信息）
   */
  private sanitizeParameters(parameters?: any[]): any[] {
    if (!parameters) return [];
    
    return parameters.map(param => {
      if (typeof param === 'string' && param.length > 100) {
        return param.substring(0, 100) + '...';
      }
      return param;
    });
  }

  /**
   * 捕获调用栈
   */
  private captureStack(): string {
    const stack = new Error().stack;
    if (!stack) return '';
    
    // 只保留前几行，去掉当前方法的调用
    const lines = stack.split('\n').slice(3, 8);
    return lines.join('\n');
  }

  /**
   * 获取慢查询记录（内存中的）
   */
  getSlowQueryRecords(limit?: number): SlowQueryRecord[] {
    const records = [...this.slowQueryRecords].reverse(); // 最新的在前
    return limit ? records.slice(0, limit) : records;
  }

  
   

  /**
   * 获取查询指标
   */
  getQueryMetrics(): QueryMetrics {
    return { ...this.queryMetrics };
  }

  /**
   * 重置指标
   */
  resetMetrics(): void {
    this.queryMetrics = {
      totalQueries: 0,
      slowQueries: 0,
      averageExecutionTime: 0,
      maxExecutionTime: 0,
      minExecutionTime: 0,
      lastResetTime: new Date()
    };
  }

  /**
   * 清空慢查询记录
   */
  clearSlowQueryRecords(): void {
    this.slowQueryRecords = [];
    this.logger.log('慢查询记录已清空');
  }

  /**
   * 获取配置信息
   */
  getConfig() {
    return {
      enableDatabaseMonitoring: this.enableDatabaseMonitoring,
      lightweightMode: this.lightweightMode,
      slowQueryThreshold: this.slowQueryThreshold,
      enableSlowQueryLogging: this.enableSlowQueryLogging,
      enableQueryMetrics: this.enableQueryMetrics,
      enableStackTrace: this.enableStackTrace,
      samplingRate: this.samplingRate,
      asyncSlowQueryProcessing: this.asyncSlowQueryProcessing,
      maxSlowQueryRecords: this.maxSlowQueryRecords,
      queryCounter: this.queryCounter
    };
  }

  /**
   * 动态更新配置
   */
  updateConfig(config: Partial<{
    enableDatabaseMonitoring: boolean;
    lightweightMode: boolean;
    slowQueryThreshold: number;
    enableSlowQueryLogging: boolean;
    enableQueryMetrics: boolean;
    enableStackTrace: boolean;
    samplingRate: number;
    asyncSlowQueryProcessing: boolean;
    maxSlowQueryRecords: number;
  }>): void {
    if (config.enableDatabaseMonitoring !== undefined) {
      this.enableDatabaseMonitoring = config.enableDatabaseMonitoring;
      this.logger.log(`数据库监控已${config.enableDatabaseMonitoring ? '启用' : '禁用'}`);
    }
    if (config.lightweightMode !== undefined) {
      this.lightweightMode = config.lightweightMode;
      this.logger.log(`轻量级模式已${config.lightweightMode ? '启用' : '禁用'}`);
    }
    if (config.slowQueryThreshold !== undefined) {
      this.slowQueryThreshold = config.slowQueryThreshold;
    }
    if (config.enableSlowQueryLogging !== undefined) {
      this.enableSlowQueryLogging = config.enableSlowQueryLogging;
    }
    if (config.enableQueryMetrics !== undefined) {
      this.enableQueryMetrics = config.enableQueryMetrics;
    }
    if (config.enableStackTrace !== undefined) {
      this.enableStackTrace = config.enableStackTrace;
    }
    if (config.samplingRate !== undefined) {
      this.samplingRate = Math.max(1, Math.min(100, config.samplingRate)); // 限制在1-100之间
    }
    if (config.asyncSlowQueryProcessing !== undefined) {
      this.asyncSlowQueryProcessing = config.asyncSlowQueryProcessing;
    }
    if (config.maxSlowQueryRecords !== undefined) {
      this.maxSlowQueryRecords = config.maxSlowQueryRecords;
    }

    this.logger.log('查询监控配置已更新', config);
  }
}
