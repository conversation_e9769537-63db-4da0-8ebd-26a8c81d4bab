# 数据库慢查询监控系统

## 功能概述

本系统为 logic-back 项目提供了完整的数据库查询性能监控和慢查询告警功能。

## 核心功能

### 1. 慢查询检测
- 自动检测执行时间超过阈值的查询
- 记录查询语句、参数、执行时间等详细信息
- 捕获调用栈，便于定位问题代码

### 2. 查询性能指标
- 总查询数统计
- 慢查询数统计
- 平均/最大/最小执行时间
- 当前活跃查询数

### 3. 告警机制
- 多级告警（警告、严重）
- 支持日志告警
- 预留邮件、钉钉告警接口

### 4. 管理接口
- 查看慢查询记录
- 查看性能指标
- 动态配置更新
- 数据清理和重置

## 配置说明

### 🔧 环境变量配置（推荐）
复制 `.env.database-monitor.example` 文件内容到你的 `.env` 文件中：

```bash
# 主开关：完全启用/禁用数据库监控
ENABLE_DB_MONITORING=true

# 轻量级模式：减少监控开销
DB_MONITOR_LIGHTWEIGHT=false

# 采样率：1-100，100表示监控所有查询
DB_MONITOR_SAMPLING_RATE=100

# 调用栈捕获：生产环境建议禁用
ENABLE_STACK_TRACE=true

# 异步处理：减少对主流程的影响
ASYNC_SLOW_QUERY_PROCESSING=true
```

### 数据库配置
在 `databases.dev.yaml` 和 `databases.prod.yaml` 中：

```yaml
mysql:
  # ... 其他配置
  logging: ['query', 'error', 'slow']
  maxQueryExecutionTime: 1000  # 开发环境1秒，生产环境2秒
```

### 应用配置
在 `src/config/database-monitor.config.ts` 中：

```typescript
export const databaseMonitorConfig = {
  database: {
    enableDatabaseMonitoring: true,  // 主开关
    lightweightMode: false,          // 轻量级模式
    slowQueryThreshold: 1000,        // 慢查询阈值(ms)
    enableSlowQueryLogging: true,    // 启用慢查询日志
    enableQueryMetrics: true,        // 启用查询指标
    enableStackTrace: true,          // 启用调用栈捕获
    samplingRate: 100,               // 采样率
    asyncSlowQueryProcessing: true,  // 异步处理
    maxSlowQueryRecords: 100,        // 最大慢查询记录数
  }
};
```

## API 接口

### 监控接口

#### 获取慢查询记录
```
GET /api/v1/database-monitor/slow-queries?limit=10
```

#### 获取性能指标
```
GET /api/v1/database-monitor/metrics
```

#### 获取活跃查询
```
GET /api/v1/database-monitor/active-queries
```

#### 健康检查
```
GET /api/v1/database-monitor/health
```

### 管理接口

#### 启用/禁用监控
```bash
# 启用监控
POST /api/v1/database-monitor/enable

# 禁用监控
POST /api/v1/database-monitor/disable

# 切换轻量级模式
POST /api/v1/database-monitor/lightweight-mode
{
  "enabled": true
}
```

#### 更新配置
```bash
POST /api/v1/database-monitor/config
{
  "enableDatabaseMonitoring": true,
  "lightweightMode": false,
  "slowQueryThreshold": 2000,
  "enableSlowQueryLogging": true,
  "samplingRate": 50
}
```

#### 重置指标
```bash
POST /api/v1/database-monitor/reset-metrics
```

#### 清空慢查询记录
```bash
POST /api/v1/database-monitor/clear-slow-queries
```

### 测试接口

#### 测试慢查询
```
GET /api/v1/database-test/slow-query?delay=3
```

#### 测试复杂查询
```
GET /api/v1/database-test/complex-query
```

#### 测试批量查询
```
GET /api/v1/database-test/batch-queries?count=20
```

## 使用示例

### 1. 启动监控
系统启动时会自动启用数据库监控，无需额外配置。

### 2. 查看慢查询
```bash
curl http://localhost:8003/api/v1/database-monitor/slow-queries
```

### 3. 测试慢查询检测
```bash
curl http://localhost:8003/api/v1/database-test/slow-query?delay=3
```

### 4. 查看性能指标
```bash
curl http://localhost:8003/api/v1/database-monitor/metrics
```

## 日志格式

### 慢查询日志
```
[WARN] 🐌 慢查询检测 [2150ms] TeachingService.oneClickStart
{
  "query": "SELECT * FROM course_teaching_records WHERE ...",
  "executionTime": 2150,
  "parameters": [...],
  "timestamp": "2025-08-02T12:35:05.000Z",
  "context": "TeachingService.oneClickStart (teaching.service.ts:168)"
}
```

### 告警日志
```
[ERROR] 🚨 严重慢查询告警 [12000ms]
{
  "query": "SELECT COUNT(*) FROM user_students a CROSS JOIN ...",
  "executionTime": 12000,
  "context": "DatabaseTestController.testComplexQuery"
}
```

## 性能影响

- 监控开销：< 1ms per query
- 内存占用：约 100KB (100条慢查询记录)
- 可通过配置禁用监控功能

## 故障排除

### 1. 监控不工作
- 检查 `DatabaseMonitorModule` 是否正确导入
- 确认配置文件中的 `logging` 和 `maxQueryExecutionTime` 设置

### 2. 慢查询记录过多
- 调整 `slowQueryThreshold` 阈值
- 减少 `maxSlowQueryRecords` 限制
- 定期清理记录

### 3. 性能影响过大
- 设置 `enableQueryMetrics: false` 禁用指标收集
- 设置 `enableSlowQueryLogging: false` 禁用慢查询日志

## 扩展功能

### 添加邮件告警
在 `QueryMonitorService.triggerSlowQueryAlert` 方法中实现邮件发送逻辑。

### 添加钉钉告警
在 `QueryMonitorService.triggerSlowQueryAlert` 方法中实现钉钉机器人推送。

### 自定义告警规则
扩展 `QueryMonitorService` 类，添加更复杂的告警条件判断。
