"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/workbench/page",{

/***/ "(app-pages-browser)/./lib/api/course-management.ts":
/*!**************************************!*\
  !*** ./lib/api/course-management.ts ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   courseManagementApi: function() { return /* binding */ courseManagementApi; }\n/* harmony export */ });\n/* harmony import */ var _request__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./request */ \"(app-pages-browser)/./lib/api/request.ts\");\n\n// 开发配置\nconst DEV_CONFIG = {\n    // 设置为true可以在开发环境中强制发送真实请求（即使API不存在也会在Network中看到请求）\n    FORCE_REAL_REQUESTS: true,\n    // 设置为true可以看到详细的调试日志\n    ENABLE_DEBUG_LOGS: true\n};\n// 课程管理API类\nclass CourseManagementApi {\n    // 创建系列课程\n    async createSeriesCourse(data) {\n        if (DEV_CONFIG.ENABLE_DEBUG_LOGS) {\n            console.log(\"\\uD83D\\uDE80 开始创建系列课程\");\n            console.log(\"\\uD83D\\uDCDD 请求数据类型: JSON\");\n            console.log(\"\\uD83D\\uDCDD 请求数据:\", data);\n            console.log(\"\\uD83C\\uDF10 目标URL: /api/v1/course-management/series\");\n            console.log(\"⚙️ 环境:\", \"development\");\n        }\n        try {\n            // 只支持JSON格式\n            const config = {\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            };\n            console.log(\"\\uD83D\\uDCE4 发送请求配置:\", config);\n            // 发送真实请求（这样可以在Network面板中看到请求）\n            const response = await _request__WEBPACK_IMPORTED_MODULE_0__.request.post(\"/api/v1/course-management/series\", data, config);\n            if (DEV_CONFIG.ENABLE_DEBUG_LOGS) {\n                console.log(\"✅ 收到真实API响应:\", response.data);\n            }\n            return response.data;\n        } catch (error) {\n            if (DEV_CONFIG.ENABLE_DEBUG_LOGS) {\n                console.error(\"❌ 创建系列课程请求失败:\", error);\n                if (error.response) {\n                    console.error(\"❌ 错误状态:\", error.response.status);\n                    console.error(\"❌ 错误数据:\", error.response.data);\n                    console.error(\"❌ 错误头:\", error.response.headers);\n                }\n            }\n            throw error;\n        }\n    }\n    // 获取我的系列课程列表\n    async getMySeries(params) {\n        if (DEV_CONFIG.ENABLE_DEBUG_LOGS) {\n            console.log(\"\\uD83D\\uDE80 获取我的系列课程列表\");\n            console.log(\"\\uD83D\\uDCDD 请求参数:\", params);\n            console.log(\"\\uD83C\\uDF10 目标URL: /api/v1/course-management/my-series\");\n        }\n        try {\n            const response = await _request__WEBPACK_IMPORTED_MODULE_0__.request.get(\"/api/v1/course-management/my-series\", {\n                params\n            });\n            if (DEV_CONFIG.ENABLE_DEBUG_LOGS) {\n                console.log(\"✅ 收到系列课程列表响应:\", response.data);\n            }\n            return response.data;\n        } catch (error) {\n            if (DEV_CONFIG.ENABLE_DEBUG_LOGS) {\n                console.error(\"❌ 获取系列课程列表失败:\", error);\n            }\n            throw error;\n        }\n    }\n    // 获取系列课程列表（保留原方法以兼容）\n    async getSeriesCourseList(params) {\n        return this.getMySeries({\n            page: params === null || params === void 0 ? void 0 : params.page,\n            pageSize: params === null || params === void 0 ? void 0 : params.size,\n            category: params === null || params === void 0 ? void 0 : params.category,\n            status: (params === null || params === void 0 ? void 0 : params.status) ? parseInt(params.status) : undefined\n        });\n    }\n    // 获取系列课程详情 - 使用课程市场API\n    async getSeriesCourseDetail(id) {\n        try {\n            // 使用课程市场API获取系列详情\n            const response = await _request__WEBPACK_IMPORTED_MODULE_0__.request.get(\"/api/v1/course-marketplace/series/\".concat(id));\n            return response.data;\n        } catch (error) {\n            console.error(\"获取系列课程详情失败:\", error);\n            throw error;\n        }\n    }\n    // 更新系列课程\n    async updateSeriesCourse(id, data) {\n        try {\n            const response = await _request__WEBPACK_IMPORTED_MODULE_0__.request.put(\"/api/v1/course-management/series/\".concat(id), data);\n            return response.data;\n        } catch (error) {\n            console.error(\"更新系列课程失败:\", error);\n            throw error;\n        }\n    }\n    // 删除系列课程\n    async deleteSeriesCourse(id) {\n        try {\n            const response = await _request__WEBPACK_IMPORTED_MODULE_0__.request.delete(\"/api/v1/course-management/series/\".concat(id));\n            return response.data;\n        } catch (error) {\n            console.error(\"删除系列课程失败:\", error);\n            throw error;\n        }\n    }\n    // 删除单个课程\n    async deleteCourse(id) {\n        try {\n            const response = await _request__WEBPACK_IMPORTED_MODULE_0__.request.delete(\"/api/v1/course-management/courses/\".concat(id));\n            return response.data;\n        } catch (error) {\n            console.error(\"删除课程失败:\", error);\n            throw error;\n        }\n    }\n    // 获取系列下的课程列表 - 使用课程管理API\n    async getSeriesCourses(seriesId, params) {\n        if (DEV_CONFIG.ENABLE_DEBUG_LOGS) {\n            console.log(\"\\uD83D\\uDE80 获取系列课程列表\");\n            console.log(\"\\uD83D\\uDCDD 系列ID:\", seriesId);\n            console.log(\"\\uD83D\\uDCDD 请求参数:\", params);\n            console.log(\"\\uD83C\\uDF10 目标URL: /api/v1/course-management/series/\" + seriesId + \"/courses\");\n        }\n        try {\n            // 使用课程管理API获取系列下的课程列表\n            const response = await _request__WEBPACK_IMPORTED_MODULE_0__.request.get(\"/api/v1/course-management/series/\".concat(seriesId, \"/courses\"), {\n                params\n            });\n            if (DEV_CONFIG.ENABLE_DEBUG_LOGS) {\n                console.log(\"✅ 收到系列课程列表响应:\", response.data);\n            }\n            return response.data;\n        } catch (error) {\n            if (DEV_CONFIG.ENABLE_DEBUG_LOGS) {\n                console.error(\"❌ 获取系列课程列表失败:\", error);\n            }\n            throw error;\n        }\n    }\n}\n// 导出API实例\nconst courseManagementApi = new CourseManagementApi();\n/* harmony default export */ __webpack_exports__[\"default\"] = (courseManagementApi);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/api/course-management.ts\n"));

/***/ })

});