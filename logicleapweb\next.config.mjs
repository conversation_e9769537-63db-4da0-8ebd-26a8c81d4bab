/** @type {import('next').NextConfig} */
// 环境判断
const isDevelopment = process.env.NODE_ENV === 'development'
const API_PORT = '8601'
const API_HOST = isDevelopment ? 'http://127.0.0.1:' + API_PORT : 'https://www.logicleapai.cn'

const BASE_URL = `${API_HOST}`
const nextConfig = {
  webpack: (config, { buildId, dev, isServer, defaultLoaders, webpack }) => {
    // 在开发模式下禁用缓存
    if (dev) {
      config.cache = false
    }
    return config
  },
  typescript: {
    // ⚠️ 警告: 仅在无法解决类型问题时使用
    ignoreBuildErrors: true,
  },
  reactStrictMode: true,
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**',
      },
      {
        protocol: 'http',
        hostname: '**',
      }
    ],
    domains: ['mp.weixin.qq.com', 'file.aigpai.com', 'https://www.logicleapai.cn', '************', 'logicleap111-1325196735.cos.ap-guangzhou.myqcloud.com', 'sf-maas-uat-prod.oss-cn-shanghai.aliyuncs.com', 'sc-maas.oss-cn-shanghai.aliyuncs.com', 'localhost', '127.0.0.1', 'logicleap111-1325196735.cos.ap-guangzhou.myqcloud.com', 'logicleap.oss-cn-guangzhou.aliyuncs.com', 'logicleap.oss-cn-guangzhou-internal.aliyuncs.com', 'logicleap.oss-cn-guangzhou.aliyuncs.com', 'hailuo-image-algeng-data.oss-cn-wulanchabu.aliyuncs.com', 'replicate.delivery', 'guangzhou.aliyuncs.com', 'example.com'],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
  },
  async rewrites() {
    return [
      // {
      //   source: '/',
      //   has: [
      //     {
      //       type: 'query',
      //       key: 'id'
      //     }
      //   ],
      //   destination: 'http://localhost:9100/'
      // },
      {
        source: '/api/:path*',
        destination: 'http://127.0.0.1:8003/api/:path*',
        // Next.js rewrites 不支持直接设置头部，需要通过中间件处理
      },
      {
        source: '/editor',
        destination: 'http://localhost:9100/',
      },
      {
        source: '/editor/:path*',
        destination: 'http://localhost:9100/:path*',
      },
      {
        source: '/socket.io/:path*',
        destination: 'http://localhost:9100/socket.io/:path*',
      },
      {
        source: '/libs/:path*',
        destination: 'http://localhost:9100/libs/:path*',
      },
      {
        source: '/token/:path*',
        destination: 'http://localhost:9100/token/:path*',
      },
      {
        source: '/js/:path*',
        destination: 'http://localhost:9100/js/:path*',
      },
      // {
      //   source: '/:docId/:userId/auth',
      //   destination: 'http://localhost:9100/:docId/:userId/auth'
      // },
      // {
      //   source: '/:docId/:userId/:path*',
      //   destination: 'http://localhost:9100/:docId/:userId/:path*'
      // },
      {
        source: '/logicleap',
        destination: `${BASE_URL}/`,
      },
      {
        source: '/static/:path*',
        destination: `${BASE_URL}/static/:path*`,
      },
      {
        source: '/js/:path*',
        destination: `${BASE_URL}/js/:path*`,
      },
      {
        source: '/gui.js',
        destination: `${BASE_URL}/gui.js`,
      },
      {
        source: '/assets/:path*',
        destination: `${BASE_URL}/assets/:path*`,
      },
      {
        source: '/logicleap/:path*',
        destination: `${BASE_URL}/:path*`,
      },
    ]
  },
  async headers() {
    return [
      {
        source: '/:path*',
        headers: [
          {
            key: 'Authorization',
            value: ':authorization',
          },
          {
            key: 'cookie',
            value: ':cookie',
          },
        ],
      },
    ]
  },
  transpilePackages: ['antd'],
  // 关闭所有 Image 组件的警告
  experimental: {
    missingSuspenseWithCSRBailout: true,
  },
  // 或者更精确地关闭 sizes 警告
  eslint: {
    ignoreDuringBuilds: true, // 构建时忽略 ESLint 警告
  },
}

export default nextConfig
