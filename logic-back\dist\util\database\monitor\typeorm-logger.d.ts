import { Logger as TypeOrmLogger, QueryRunner } from 'typeorm';
import { QueryInterceptor } from './interceptor/query-interceptor';
export declare class CustomTypeOrmLogger implements TypeOrmLogger {
    private readonly logger;
    private queryInterceptor?;
    constructor(queryInterceptor?: QueryInterceptor);
    logQuery(query: string, parameters?: any[], queryRunner?: QueryRunner): void;
    logQueryError(error: string | Error, query: string, parameters?: any[], queryRunner?: QueryRunner): void;
    logQuerySlow(time: number, query: string, parameters?: any[], queryRunner?: QueryRunner): void;
    logSchemaBuild(message: string): void;
    logMigration(message: string): void;
    logQuerySuccess(query: string, parameters?: any[], queryRunner?: QueryRunner): void;
    log(level: 'log' | 'info' | 'warn', message: any, queryRunner?: QueryRunner): void;
    private formatQuery;
    private formatParameters;
    private getQueryContext;
}
