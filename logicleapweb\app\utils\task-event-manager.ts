'use client';

/**
 * 全局任务事件管理器
 * 用于在不同组件和页面之间通信任务相关事件
 */
class TaskEventManager {
  private listeners: Map<string, Set<Function>> = new Map();

  /**
   * 添加事件监听器
   * @param event 事件名称
   * @param listener 监听器函数
   */
  on(event: string, listener: Function): void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, new Set());
    }
    this.listeners.get(event)!.add(listener);
  }

  /**
   * 移除事件监听器
   * @param event 事件名称
   * @param listener 监听器函数
   */
  off(event: string, listener: Function): void {
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      eventListeners.delete(listener);
      if (eventListeners.size === 0) {
        this.listeners.delete(event);
      }
    }
  }

  /**
   * 触发事件
   * @param event 事件名称
   * @param data 事件数据
   */
  emit(event: string, data?: any): void {
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      eventListeners.forEach(listener => {
        try {
          listener(data);
        } catch (error) {
          console.error(`任务事件监听器执行错误 [${event}]:`, error);
        }
      });
    }
  }

  /**
   * 移除所有监听器
   */
  removeAllListeners(): void {
    this.listeners.clear();
  }

  /**
   * 获取事件监听器数量
   * @param event 事件名称
   */
  listenerCount(event: string): number {
    return this.listeners.get(event)?.size || 0;
  }
}

// 创建全局单例实例
const taskEventManager = new TaskEventManager();

// 定义事件类型常量
export const TASK_EVENTS = {
  TASK_PUBLISHED: 'task_published',           // 任务发布成功
  TASK_UPDATED: 'task_updated',               // 任务更新
  TASK_DELETED: 'task_deleted',               // 任务删除
  TASK_STATUS_CHANGED: 'task_status_changed', // 任务状态变更
} as const;

export type TaskEventType = typeof TASK_EVENTS[keyof typeof TASK_EVENTS];

// 事件数据接口
export interface TaskPublishedEventData {
  taskId: number;
  taskName: string;
  classId?: number;
  className?: string;
  teacherId: number;
}

export interface TaskUpdatedEventData {
  taskId: number;
  taskName: string;
  changes: Record<string, any>;
}

export interface TaskDeletedEventData {
  taskId: number;
  taskName: string;
}

export interface TaskStatusChangedEventData {
  taskId: number;
  oldStatus: string;
  newStatus: string;
}

export default taskEventManager;
