import React, { useState, useEffect } from 'react';
import { Select, Tag, Spin, Modal, Form, Input, ColorPicker, Button } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { tagApi } from '@/lib/api/activity';
import type { Color } from 'antd/es/color-picker';
import { GetNotification } from 'logic-common/dist/components/Notification';

interface TagSelectorProps {
  value?: number[];
  onChange?: (value: number[]) => void;
}

// 标签类型
interface TagItem {
  id: number;
  name: string;
  color?: string;
  description?: string;
}

const TagSelector: React.FC<TagSelectorProps> = ({ value = [], onChange }) => {
  const [tags, setTags] = useState<TagItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchValue, setSearchValue] = useState('');
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [createForm] = Form.useForm();
  const [createLoading, setCreateLoading] = useState(false);

  // 获取标签列表
  const fetchTags = async (keyword?: string) => {
    setLoading(true);
    try {
      const res = await tagApi.getList({
        keyword,
      });
      setTags(res.data.list || []);
    } catch (error) {
      console.error('获取标签列表失败:', error);
      const notification = GetNotification();
      notification.error('获取标签列表失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTags();
  }, []);

  // 搜索标签
  const handleSearch = (keyword: string) => {
    setSearchValue(keyword);
    fetchTags(keyword);
  };

  // 打开创建标签模态框
  const handleOpenCreateModal = () => {
    setCreateModalVisible(true);
    createForm.resetFields();
  };

  // 创建标签
  const handleCreateTag = async () => {
    try {
      const values = await createForm.validateFields();
      setCreateLoading(true);
      const notification = GetNotification();

      // 转换颜色值
      const colorString = values.color?.toHexString ? values.color?.toHexString() : values.color;

      const res = await tagApi.create({
        name: values.name,
        description: values.description,
        color: colorString,
      });

      notification.success('创建标签成功');
      setCreateModalVisible(false);

      // 更新标签列表
      fetchTags(searchValue);

      // 将新创建的标签添加到选中列表
      if (onChange) {
        onChange([...value, res.data.id]);
      }
    } catch (error: any) {
      const notification = GetNotification();
      if (error.response?.data?.message) {
        notification.error(error.response.data.message);
      } else {
        notification.error('创建标签失败');
      }
      console.error('创建标签失败:', error);
    } finally {
      setCreateLoading(false);
    }
  };

  // 下拉选项
  const options = tags.map(tag => ({
    value: tag.id,
    label: (
      <Tag color={tag.color || '#1677ff'} key={tag.id}>
        {tag.name}
      </Tag>
    ),
  }));

  // 添加创建新标签选项
  const dropdownRender = (menu: React.ReactElement) => (
    <div>
      {menu}
      <div style={{ display: 'flex', padding: '8px', borderTop: '1px solid #e8e8e8' }}>
        <Button type="text" icon={<PlusOutlined />} block onClick={handleOpenCreateModal}>
          创建新标签
        </Button>
      </div>
    </div>
  );

  return (
    <>
      <Select
        mode="multiple"
        style={{ width: '100%' }}
        placeholder="请选择标签"
        value={value}
        onChange={onChange}
        options={options}
        loading={loading}
        filterOption={false}
        onSearch={handleSearch}
        dropdownRender={dropdownRender}
        notFoundContent={loading ? <Spin size="small" /> : null}
      />

      <Modal
        title="创建新标签"
        open={createModalVisible}
        onOk={handleCreateTag}
        onCancel={() => setCreateModalVisible(false)}
        confirmLoading={createLoading}
      >
        <Form form={createForm} layout="vertical">
          <Form.Item
            name="name"
            label="标签名称"
            rules={[{ required: true, message: '请输入标签名称' }]}
          >
            <Input placeholder="请输入标签名称" />
          </Form.Item>

          <Form.Item
            name="description"
            label="标签描述"
          >
            <Input.TextArea placeholder="请输入标签描述" />
          </Form.Item>

          <Form.Item
            name="color"
            label="标签颜色"
          >
            <ColorPicker />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default TagSelector; 