import React, { useState, useEffect } from 'react';
import { Row, Col, Spin, Empty, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>er, But<PERSON>, Typography, Modal, Image } from 'antd';
import { EditOutlined, PictureOutlined, DeleteOutlined } from '@ant-design/icons';
import Link from 'next/link';
import dayjs from 'dayjs';
import { activityApi } from '@/lib/api/activity';
import { documentApi } from '../../../lib/api/document';
import { GetNotification } from 'logic-common/dist/components/Notification';

const { Title, Paragraph } = Typography;
const { TabPane } = Tabs;

export interface Activity {
  id: number;
  name: string;
  startTime: string;
  endTime: string;
  organizer: string;
  coverImage: string;
  status: number;
  createTime: string;
  detailDocId?: string;
  rulesDocId?: string;
  awardsDocId?: string;
}

interface ActivityListProps {
  showOnlyActive?: boolean;
  onEdit?: (activityId: number) => void;
}

// 调试封面图片URL的辅助函数
const getValidImageUrl = (url: string | undefined): string => {
  if (!url) return '/placeholder-image.jpg';

  // 将相对路径转为绝对路径
  if (url.startsWith('/')) {
    return url;
  }

  // 修复协议缺失的问题
  if (url.includes('aliyuncs.com') && !url.startsWith('http')) {
    return `https://${url}`;
  }

  // 检查是否为有效URL
  try {
    new URL(url);
    return url;
  } catch (e) {
    console.error('无效的图片URL:', url, e);

    // 尝试修复缺少协议的URL
    if (!url.startsWith('http') && (url.startsWith('//') || !url.includes('://'))) {
      return `https://${url.startsWith('//') ? url.substring(2) : url}`;
    }

    return '/placeholder-image.jpg';
  }
};

const ActivityList: React.FC<ActivityListProps> = ({
  showOnlyActive = true,
  onEdit
}) => {
  const [activities, setActivities] = useState<Activity[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [rawData, setRawData] = useState<any>(null);
  const [detailVisible, setDetailVisible] = useState(false);
  const [currentActivity, setCurrentActivity] = useState<Activity | null>(null);
  const [detailContent, setDetailContent] = useState<string>('');
  const [rulesContent, setRulesContent] = useState<string>('');
  const [awardsContent, setAwardsContent] = useState<string>('');
  const [contentLoading, setContentLoading] = useState(false);
  const [isMounted, setIsMounted] = useState(false);

  // 添加图片预览相关状态
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewImage, setPreviewImage] = useState('');

  // 避免水合错误
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // 获取活动列表
  const fetchActivities = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('开始获取活动列表，参数:', { showOnlyActive });

      const response = await activityApi.getList({
        page: 1,
        size: 100, // 获取更多数据，不分页展示
      });

      console.log('API响应原始数据:', response);
      setRawData(response);

      if (response.data && response.status === 200) {
        console.log('API响应状态正常，数据结构:', Object.keys(response.data));

        // 检查新的API响应结构：response.data.data.list
        if (response.data.data && response.data.data.list) {
          console.log('使用新的API响应结构: response.data.data.list');
          const { list } = response.data.data;

          if (!Array.isArray(list)) {
            const errorMsg = '列表数据不是数组:' + JSON.stringify(list);
            console.error(errorMsg);
            setError(errorMsg);
            setLoading(false);
            return;
          }

          console.log('原始活动列表数量:', list.length);
          let filteredActivities = [...list];

          if (showOnlyActive) {
            console.log('开始过滤活动，只显示未过期的活动');
            filteredActivities = list.filter((activity: Activity) => {
              const isNotExpired = dayjs(activity.endTime).isAfter(dayjs());
              console.log(`活动 ${activity.id} - ${activity.name}:`, {
                过期状态: isNotExpired ? '未过期' : '已过期',
                状态: activity.status,
                开始时间: activity.startTime,
                结束时间: activity.endTime,
                当前时间: dayjs().format('YYYY-MM-DD HH:mm:ss')
              });
              return isNotExpired;
            });
          }

          console.log('过滤后的活动列表数量:', filteredActivities.length);
          setActivities(filteredActivities);

        } else if (!response.data.list && response.data.records) {
          console.log('数据结构使用records而不是list');
          const { records } = response.data;

          if (!Array.isArray(records)) {
            const errorMsg = '列表数据不是数组:' + JSON.stringify(records);
            console.error(errorMsg);
            setError(errorMsg);
            setLoading(false);
            return;
          }

          console.log('原始活动列表数量:', records.length);
          let filteredActivities = [...records];

          if (showOnlyActive) {
            console.log('开始过滤活动，只显示未过期的活动');
            filteredActivities = records.filter((activity: Activity) => {
              const isNotExpired = dayjs(activity.endTime).isAfter(dayjs());
              console.log(`活动 ${activity.id} - ${activity.name}:`, {
                过期状态: isNotExpired ? '未过期' : '已过期',
                状态: activity.status,
                开始时间: activity.startTime,
                结束时间: activity.endTime,
                当前时间: dayjs().format('YYYY-MM-DD HH:mm:ss')
              });
              return isNotExpired;
            });
          }

          console.log('过滤后的活动列表数量:', filteredActivities.length);
          setActivities(filteredActivities);

        } else if (response.data.list) {
          console.log('使用旧的API响应结构: response.data.list');
          const { list } = response.data;

          if (!Array.isArray(list)) {
            const errorMsg = '列表数据不是数组:' + JSON.stringify(list);
            console.error(errorMsg);
            setError(errorMsg);
            setLoading(false);
            return;
          }

          console.log('原始活动列表数量:', list.length);
          let filteredActivities = [...list];

          if (showOnlyActive) {
            console.log('开始过滤活动，只显示未过期的活动');
            filteredActivities = list.filter((activity: Activity) => {
              const isNotExpired = dayjs(activity.endTime).isAfter(dayjs());
              console.log(`活动 ${activity.id} - ${activity.name}:`, {
                过期状态: isNotExpired ? '未过期' : '已过期',
                状态: activity.status,
                开始时间: activity.startTime,
                结束时间: activity.endTime,
                当前时间: dayjs().format('YYYY-MM-DD HH:mm:ss')
              });
              return isNotExpired;
            });
          }

          console.log('过滤后的活动列表数量:', filteredActivities.length);
          setActivities(filteredActivities);
        } else {
          const errorMsg = '无法识别的API响应数据结构';
          console.error(errorMsg, response.data);
          setError(errorMsg);
        }
      } else {
        const errorMsg = '获取活动列表失败: ' + (response.data?.msg || '未知错误');
        console.error(errorMsg);
        setError(errorMsg);
        const notification = GetNotification();
        notification.error(errorMsg);
      }
    } catch (error) {
      const errorMsg = '获取活动列表异常: ' + (error instanceof Error ? error.message : '未知错误');
      console.error(errorMsg, error);
      setError(errorMsg);
      const notification = GetNotification();
      notification.error(errorMsg);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchActivities();
  }, [showOnlyActive]);

  // 删除活动
  const handleDeleteActivity = async (activityId: number, activityName: string) => {
    const notification = GetNotification();
    try {
      await activityApi.delete(activityId);
      notification.success('活动删除成功');
      // 重新获取活动列表
      fetchActivities();
    } catch (error) {
      console.error('删除活动失败:', error);
      notification.error('删除活动失败');
    }
  };

  // 获取文档内容
  const fetchDocumentContent = async (docId: string): Promise<string> => {
    if (!docId) return '未提供文档ID';

    try {
      const response = await documentApi.getById(Number(docId));
      if (response.data && response.data.data) {
        return response.data.data.content || '文档内容为空';
      }
      return '无法获取文档内容';
    } catch (error) {
      console.error('获取文档内容失败:', error);
      return '获取文档内容失败';
    }
  };

  // 打开活动详情抽屉
  const openActivityDetail = async (activity: Activity) => {
    setCurrentActivity(activity);
    setDetailVisible(true);
    setContentLoading(true);

    try {
      // 并行获取三个文档内容
      const [detailResult, rulesResult, awardsResult] = await Promise.all([
        activity.detailDocId ? fetchDocumentContent(activity.detailDocId) : '未提供活动详情',
        activity.rulesDocId ? fetchDocumentContent(activity.rulesDocId) : '未提供活动规则',
        activity.awardsDocId ? fetchDocumentContent(activity.awardsDocId) : '未提供奖项设置'
      ]);

      setDetailContent(detailResult);
      setRulesContent(rulesResult);
      setAwardsContent(awardsResult);
    } catch (error) {
      console.error('获取活动内容失败:', error);
      const notification = GetNotification();
      notification.error('获取活动内容失败');
    } finally {
      setContentLoading(false);
    }
  };

  // 关闭活动详情抽屉
  const closeActivityDetail = () => {
    setDetailVisible(false);
    setCurrentActivity(null);
  };

  // 添加图片预览功能
  const handlePreviewImage = (imageUrl: string) => {
    if (imageUrl) {
      const fixedUrl = getValidImageUrl(imageUrl);
      setPreviewImage(fixedUrl);
      setPreviewVisible(true);
    }
  };

  // 避免水合错误，在客户端挂载前显示加载状态
  if (!isMounted || loading) {
    return (
      <div className="p-10 flex justify-center">
        <Spin size="large" />
      </div>
    );
  }

  // 如果有错误信息，显示错误提示
  if (error) {
    return (
      <Alert
        message="加载失败"
        description={
          <div>
            <p>{error}</p>
            <p>原始数据: {JSON.stringify(rawData, null, 2)}</p>
            <button
              onClick={fetchActivities}
              className="mt-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              重试
            </button>
          </div>
        }
        type="error"
        showIcon
      />
    );
  }

  // 如果没有活动，显示提示
  if (activities.length === 0) {
    return (
      <div>
        <Empty
          description={
            <span>
              暂无活动
              {showOnlyActive &&
                <span className="block text-xs text-gray-500 mt-1">
                  (只显示未过期的活动)
                </span>
              }
            </span>
          }
        />
        <div className="mt-4">
          <p className="text-sm text-gray-500">调试信息:</p>
          <pre className="text-xs bg-gray-100 p-2 mt-1 rounded overflow-auto max-h-40">
            {JSON.stringify(rawData, null, 2)}
          </pre>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
        {activities.map(activity => (
          <div key={activity.id} className="relative">
            {/* 使用Link包裹卡片，点击跳转到详情页 */}
            <Link href={`/activity/festival/?id=${activity.id}`}>
              <div className="bg-white rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow cursor-pointer">
                <div className="relative">
                  {/* 活动封面图片 */}
                  {activity.coverImage && (
                    <div className="relative" style={{ height: '200px', overflow: 'hidden' }}>
                      <img
                        src={getValidImageUrl(activity.coverImage)}
                        alt={activity.name}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          console.error(`图片加载失败: ${activity.coverImage}`, e);
                          const target = e.target as HTMLImageElement;
                          target.onerror = null; // 防止无限循环
                          target.src = '/placeholder-image.jpg';
                        }}
                      />
                      <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-30 text-white text-xs py-1 px-2 flex justify-between items-center">
                        <span>ID: {activity.id}</span>
                        <div
                          onClick={(e) => {
                            e.preventDefault(); // 阻止Link的跳转
                            e.stopPropagation();
                            handlePreviewImage(activity.coverImage);
                          }}
                          className="cursor-pointer hover:text-blue-300 transition-colors"
                          title="预览图片"
                        >
                          <PictureOutlined />
                        </div>
                      </div>
                    </div>
                  )}

                  {!activity.coverImage && (
                    <div className="w-full h-48 bg-gray-200 flex items-center justify-center">
                      <span className="text-gray-400">无封面图片</span>
                    </div>
                  )}


                </div>

                {/* 活动信息 */}
                <div className="p-3">
                  {/* 活动标题 */}
                  <h3 className="text-base font-medium mb-1 truncate" title={activity.name}>
                    {activity.name}
                  </h3>

                  {/* 活动描述 */}
                  <p className="text-gray-500 text-sm mb-2 truncate" title={activity.organizer}>
                    {activity.organizer}
                  </p>

                  {/* 状态显示 */}
                  <div className="flex items-center justify-end text-xs">
                    <span className={`px-2 py-1 rounded-full ${
                      activity.status === 1
                        ? 'bg-green-100 text-green-800'
                        : 'bg-gray-100 text-gray-600'
                    }`}>
                      {activity.status === 1 ? '已发布' : '草稿'}
                    </span>
                  </div>


                </div>
              </div>
            </Link>

            {/* 操作按钮 */}
            {onEdit && (
              <div className="absolute top-2 right-2 z-10 flex gap-2">
                {/* 编辑按钮 */}
                <div
                  className="bg-blue-500 text-white p-2 rounded-full cursor-pointer hover:bg-blue-600 transition-colors shadow-md"
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    onEdit(activity.id);
                  }}
                  title="编辑活动"
                >
                  <EditOutlined className="text-sm" />
                </div>
                {/* 删除按钮 */}
                <div
                  className="bg-red-500 text-white p-2 rounded-full cursor-pointer hover:bg-red-600 transition-colors shadow-md"
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    Modal.confirm({
                      title: '确认删除',
                      content: `确定要删除活动"${activity.name}"吗？此操作不可恢复。`,
                      okText: '确定',
                      cancelText: '取消',
                      okType: 'danger',
                      onOk: () => handleDeleteActivity(activity.id, activity.name),
                    });
                  }}
                  title="删除活动"
                >
                  <DeleteOutlined className="text-sm" />
                </div>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* 活动详情抽屉 */}
      <Drawer
        title={currentActivity?.name || '活动详情'}
        placement="right"
        width={640}
        onClose={closeActivityDetail}
        open={detailVisible}
        extra={
          <>
            {currentActivity?.coverImage && (
              <Button
                icon={<PictureOutlined />}
                onClick={() => currentActivity && handlePreviewImage(currentActivity.coverImage)}
                style={{ marginRight: 8 }}
              >
                查看封面
              </Button>
            )}
            {onEdit && currentActivity && (
              <Button
                type="primary"
                icon={<EditOutlined />}
                onClick={() => {
                  if (currentActivity && onEdit) {
                    onEdit(currentActivity.id);
                    closeActivityDetail();
                  }
                }}
              >
                编辑
              </Button>
            )}
          </>
        }
      >
        {contentLoading ? (
          <div className="flex justify-center items-center h-64">
            <Spin tip="正在加载内容..." />
          </div>
        ) : (
          <Tabs defaultActiveKey="detail">
            <TabPane tab="活动详情" key="detail">
              <div className="py-4">
                <Title level={4}>活动概览</Title>
                <Paragraph>
                  <strong>主办方:</strong> {currentActivity?.organizer}
                </Paragraph>
                <Paragraph>
                  <strong>时间:</strong> {currentActivity?.startTime ? dayjs(currentActivity.startTime).format('YYYY-MM-DD HH:mm') : '-'} 至 {currentActivity?.endTime ? dayjs(currentActivity.endTime).format('YYYY-MM-DD HH:mm') : '-'}
                </Paragraph>
                <Paragraph>
                  <strong>状态:</strong> {currentActivity?.status === 1 ? '已发布' : '草稿'}
                </Paragraph>

                <Title level={4} className="mt-6">详细信息</Title>
                <div className="p-4 bg-gray-50 rounded-lg overflow-auto max-h-96">
                  <div dangerouslySetInnerHTML={{ __html: detailContent }} />
                </div>
              </div>
            </TabPane>
            <TabPane tab="活动规则" key="rules">
              <div className="py-4">
                <div className="p-4 bg-gray-50 rounded-lg overflow-auto max-h-96">
                  <div dangerouslySetInnerHTML={{ __html: rulesContent }} />
                </div>
              </div>
            </TabPane>
            <TabPane tab="奖项设置" key="awards">
              <div className="py-4">
                <div className="p-4 bg-gray-50 rounded-lg overflow-auto max-h-96">
                  <div dangerouslySetInnerHTML={{ __html: awardsContent }} />
                </div>
              </div>
            </TabPane>
          </Tabs>
        )}
      </Drawer>

      {/* 图片预览模态框 */}
      <Modal
        open={previewVisible}
        title="图片预览"
        footer={null}
        onCancel={() => setPreviewVisible(false)}
        width={800}
      >
        {previewImage && (
          <div className="text-center">
            <Image
              alt="活动封面图片"
              style={{ maxWidth: '100%' }}
              src={previewImage}
              fallback="/placeholder-image.jpg"
              preview={{
                toolbarRender: () => null, // 隐藏工具栏
                zIndex: 2000,  // 确保预览框在最上层
                src: previewImage  // 使用相同的图片URL
              }}
            />
            <div className="mt-4">
              <p className="text-sm">图片URL: {previewImage}</p>
              <p className="text-xs text-gray-500 mt-2">
                如果图片无法显示，可能是由于以下原因：
                <ul className="list-disc pl-4 mt-1">
                  <li>图片URL格式不正确</li>
                  <li>图片资源不存在或已被删除</li>
                  <li>服务器限制访问或跨域问题</li>
                </ul>
              </p>
              <div className="mt-3 flex justify-center">
                <Button
                  type="primary"
                  onClick={() => window.open(previewImage, '_blank')}
                >
                  在新窗口打开图片
                </Button>
                <Button
                  onClick={() => {
                    navigator.clipboard.writeText(previewImage);
                    const notification = GetNotification();
                    notification.success('已复制图片URL');
                  }}
                  style={{ marginLeft: 8 }}
                >
                  复制图片URL
                </Button>
              </div>
            </div>
          </div>
        )}
      </Modal>
    </>
  );
};

export default ActivityList; 