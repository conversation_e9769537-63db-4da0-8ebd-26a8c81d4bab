'use client'

import React, { useState, useEffect, useRef } from 'react';
import { Modal, Button, Input, Upload, message, Spin, Empty } from 'antd';
import { PlusOutlined, UploadOutlined, DownOutlined } from '@ant-design/icons';
import type { UploadFile, UploadProps } from 'antd';
import styles from './EventsTaskModal.module.css';
import { eventsTaskApi } from '@/lib/api/activity/events-task';
import { activityApi } from '@/lib/api/activity';
import { worksApi } from '@/lib/api/works';
import { uploadApi } from '@/lib/api/upload';
import CompetitionForm from './CompetitionForm';
import RegistrationForm from './RegistrationForm';

// 作品接口定义
interface WorkItem {
  id: number;
  title: string;
  description?: string;
  coverImage?: string;
  screenShotImage?: string;
  type: number;
  status: number;
  createTime: string;
  updateTime: string;
}

interface EventsTaskData {
  id: number;
  userId: number;
  activityId: number;
  eventName: string;
  startTime: string;
  endTime: string;
  workId?: number | null;
  workFile?: string | null;
  workDescription?: string | null;
  instructorName?: string;
  schoolName?: string;
  contactPerson?: string;
  contactPhone?: string;
  realName?: string;
  idNumber?: string;
  affiliatedSchool?: string;
  organization?: string;
  instructorPhone?: string;
  competitionGroup?: string;
  registrationFormFile?: string;
  status: number;
  creatorId: number;
  createTime: string;
  updateTime: string;
  isDelete: boolean;
  remark?: string;
}

interface ActivityData {
  id: number;
  name: string;
  competitionGroups?: string;
  registrationForm?: string;
  // 其他活动字段可以根据需要添加
}

interface EventsTaskModalProps {
  visible: boolean;
  onClose: () => void;
  onSuccess?: () => void;
  task: {
    id: number;
    taskName: string;
    taskDescription: string;
    startDate?: string;
    endDate: Date;
    isEventsTask?: boolean;
  } | null;
}

const EventsTaskModal: React.FC<EventsTaskModalProps> = ({ visible, onClose, onSuccess, task }) => {
  const [activeTab, setActiveTab] = useState<'select' | 'upload'>('select');
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [eventsTaskData, setEventsTaskData] = useState<EventsTaskData | null>(null);
  const [activityData, setActivityData] = useState<ActivityData | null>(null);
  const [availableGroups, setAvailableGroups] = useState<string[]>([]);
  const [showScrollArrow, setShowScrollArrow] = useState(false);
  const modalContentRef = useRef<HTMLDivElement>(null);
  const [submitStatus, setSubmitStatus] = useState<{
    canSubmit: boolean;
    reason?: string;
    isResubmit?: boolean;
    rejectReason?: string;
  } | null>(null);

  // 表单字段状态
  const [instructorName, setInstructorName] = useState('');
  const [schoolName, setSchoolName] = useState('');
  const [contactPerson, setContactPerson] = useState('');
  const [contactPhone, setContactPhone] = useState('');
  const [workDescription, setWorkDescription] = useState('');

  // 新增字段状态
  const [realName, setRealName] = useState('');
  const [idNumber, setIdNumber] = useState('');
  const [affiliatedSchool, setAffiliatedSchool] = useState('');
  const [organization, setOrganization] = useState('');
  const [instructorPhone, setInstructorPhone] = useState('');
  const [competitionGroup, setCompetitionGroup] = useState('');
  const [registrationFormFileList, setRegistrationFormFileList] = useState<any[]>([]);



  // 表单验证错误状态
  const [validationErrors, setValidationErrors] = useState<{
    realName?: boolean;
    idNumber?: boolean;
    affiliatedSchool?: boolean;
    organization?: boolean;
    contactPerson?: boolean;
    contactPhone?: boolean;
    competitionGroup?: boolean;
    instructorName?: boolean;
    instructorPhone?: boolean;
    workSelection?: boolean;
    fileUpload?: boolean;
    registrationForm?: boolean;
  }>({});
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [uploadedFileUrls, setUploadedFileUrls] = useState<string[]>([]);

  // 作品相关状态
  const [works, setWorks] = useState<WorkItem[]>([]);
  const [loadingWorks, setLoadingWorks] = useState(false);
  const [selectedWorkIds, setSelectedWorkIds] = useState<number[]>([]);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const pageSize = 10;

  // 加载赛事任务详情
  const loadEventsTaskData = async (taskId: number) => {
    try {
      setLoading(true);
      const response = await eventsTaskApi.getDetail(taskId);
      if (response.data.code === 200) {
        const taskData = response.data.data;
        setEventsTaskData(taskData);

        // 初始化表单数据
        setInstructorName(taskData.instructorName || '');
        setSchoolName(taskData.schoolName || '');
        setContactPerson(taskData.contactPerson || '');
        setContactPhone(taskData.contactPhone || '');
        setWorkDescription(taskData.workDescription || '');

        // 初始化新增字段数据
        setRealName(taskData.realName || '');
        setIdNumber(taskData.idNumber || '');
        setAffiliatedSchool(taskData.affiliatedSchool || '');
        setOrganization(taskData.organization || '');
        setInstructorPhone(taskData.instructorPhone || '');
        setCompetitionGroup(taskData.competitionGroup || '');

        // 初始化报名表文件
        if (taskData.registrationFormFile) {
          // 从URL推断文件类型和名称
          const fileUrl = taskData.registrationFormFile;
          const urlParts = fileUrl.split('/');
          const fileName = urlParts[urlParts.length - 1] || '报名表文件';
          const fileExtension = fileName.split('.').pop()?.toLowerCase() || '';

          // 根据文件扩展名设置MIME类型
          let fileType = '';
          if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(fileExtension)) {
            fileType = `image/${fileExtension === 'jpg' ? 'jpeg' : fileExtension}`;
          } else if (fileExtension === 'pdf') {
            fileType = 'application/pdf';
          } else if (['doc', 'docx'].includes(fileExtension)) {
            fileType = fileExtension === 'doc' ? 'application/msword' : 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
          }

          const formFile = {
            uid: 'registration-form',
            name: fileName,
            status: 'done' as const,
            url: fileUrl,
            type: fileType,
          };
          setRegistrationFormFileList([formFile]);
        } else {
          setRegistrationFormFileList([]);
        }

        // 如果有文件，初始化文件列表
        if (taskData.workFile) {
          // 这里可以根据实际需要处理文件显示
          setFileList([]);
        }

        // 检查提交状态
        await checkSubmitStatus(taskId);

        // 如果任务已提交，加载之前提交的内容
        await loadPreviousSubmission(taskData);

        // 加载活动详情以获取参赛组别
        await loadActivityData(taskData.activityId);
      } else {
        message.error('获取任务详情失败');
      }
    } catch (error) {
      console.error('获取任务详情失败:', error);
      message.error('获取任务详情失败');
    } finally {
      setLoading(false);
    }
  };

  // 加载活动详情
  const loadActivityData = async (activityId: number) => {
    try {
      const response = await activityApi.getActivityInfo(activityId);
      if (response.data && response.status === 200) {
        const activity = response.data.data || response.data;
        setActivityData(activity);

        // 解析参赛组别
        if (activity.competitionGroups) {
          const groups = activity.competitionGroups.split(',').map((group: string) => group.trim()).filter(Boolean);
          setAvailableGroups(groups);
        } else {
          setAvailableGroups([]);
        }
      }
    } catch (error) {
      console.error('获取活动详情失败:', error);
      // 不显示错误提示，因为这不是关键功能
    }
  };

  // 检查任务提交状态
  const checkSubmitStatus = async (taskId: number) => {
    try {
      const response = await eventsTaskApi.checkSubmitStatus(taskId);
      if (response.data.code === 200) {
        setSubmitStatus(response.data.data);
      }
    } catch (error) {
      console.error('检查提交状态失败:', error);
    }
  };

  // 加载之前提交的内容
  const loadPreviousSubmission = async (taskData: EventsTaskData) => {
    // 如果任务状态是已提交(2)或已审核(3)或审核不通过(4)，说明之前已经提交过
    if (taskData.status >= 2) {
      try {
        // 如果有作品ID，设置为选择作品模式并选中该作品
        if (taskData.workId) {
          setActiveTab('select');

          // 先重置作品状态，然后加载作品列表
          resetWorksState();
          await fetchUserWorks(1, true);

          // 设置选中的作品ID（延迟设置，确保作品列表已加载）
          setTimeout(() => {
            if (taskData.workId) {
              setSelectedWorkIds([taskData.workId]);
            }
          }, 100);
        }

        // 如果有作品文件，设置为上传模式并显示文件
        else if (taskData.workFile) {
          setActiveTab('upload');

          // 解析文件URL（可能是逗号分隔的多个文件）
          const fileUrls = taskData.workFile.split(',').filter(url => url.trim());
          setUploadedFileUrls(fileUrls);

          // 创建文件列表显示
          const fileList = fileUrls.map((url, index) => {
            // 从URL中提取文件名
            const urlParts = url.trim().split('/');
            const fileName = urlParts[urlParts.length - 1] || `之前提交的文件${index + 1}`;

            return {
              uid: `previous-file-${index}`,
              name: fileName,
              status: 'done' as const,
              url: url.trim(),
              response: { url: url.trim() }
            };
          });
          setFileList(fileList);
        }

        // 显示提示信息
        if (taskData.workId || taskData.workFile) {
          message.info('已加载上次提交的内容，您可以修改后重新提交');
        }

      } catch (error) {
        console.error('加载之前提交内容失败:', error);
        message.warning('加载之前提交的内容失败，但您仍可以重新提交');
      }
    }
  };

  // 获取用户作品列表
  const fetchUserWorks = async (pageNum = 1, isReset = false) => {
    try {
      // 避免重复加载
      if ((isReset && loadingWorks) || (!isReset && loadingMore)) {
        return;
      }

      // 设置加载状态
      if (isReset) {
        setLoadingWorks(true);
      } else {
        setLoadingMore(true);
      }

      // 从localStorage获取用户信息
      const userData = localStorage.getItem('user');
      const user = userData ? JSON.parse(userData) : null;

      if (!user || !user.userId) {
        console.error('未找到用户信息');
        return;
      }

      const response = await worksApi.getTeacherWorks(
        user.userId,
        pageNum,
        pageSize
      );

      // 处理响应数据
      let newWorks: WorkItem[] = [];
      let total = 0;

      if (response && response.data) {
        if (response.data.data?.data?.list) {
          newWorks = response.data.data.data.list || [];
          total = response.data.data.data.total || newWorks.length;
        } else if (response.data.data?.list) {
          newWorks = response.data.data.list || [];
          total = response.data.data.total || newWorks.length;
        } else if (Array.isArray(response.data.data)) {
          newWorks = response.data.data;
          total = newWorks.length;
        } else if (Array.isArray(response.data)) {
          newWorks = response.data;
          total = newWorks.length;
        }

        // 判断是否还有更多数据
        const hasMoreData = pageNum * pageSize < total;
        setHasMore(hasMoreData);

        if (isReset) {
          setWorks(newWorks);
        } else {
          setWorks(prev => [...prev, ...newWorks]);
        }

        setPage(pageNum);
      }
    } catch (error) {
      console.error('获取作品列表失败:', error);
      message.error('获取作品列表失败');
    } finally {
      if (isReset) {
        setLoadingWorks(false);
      } else {
        setLoadingMore(false);
      }
    }
  };

  // 重置作品状态
  const resetWorksState = () => {
    setWorks([]);
    setPage(1);
    setHasMore(true);
    setSelectedWorkIds([]);
  };

  // 当模态框打开且有任务时，加载任务详情和作品列表
  useEffect(() => {
    if (visible && task?.id && task.isEventsTask) {
      loadEventsTaskData(task.id);
      // 如果是选择作品模式，加载作品列表
      if (activeTab === 'select') {
        resetWorksState();
        fetchUserWorks(1, true);
      }
    }
  }, [visible, task?.id]);

  // 当切换到选择作品标签时，加载作品列表
  useEffect(() => {
    if (visible && activeTab === 'select') {
      // 切换到选择作品模式时，清除上传的文件
      setFileList([]);
      setUploadedFileUrls([]);

      resetWorksState();
      fetchUserWorks(1, true);
    } else if (visible && activeTab === 'upload') {
      // 切换到上传模式时，清除选中的作品
      setSelectedWorkIds([]);
    }
  }, [visible, activeTab]);

  // 加载更多作品
  const loadMoreWorks = () => {
    if (!loadingMore && hasMore) {
      const nextPage = page + 1;
      fetchUserWorks(nextPage, false);
    }
  };

  // 选择作品（支持多选）
  const handleSelectWork = (workId: number) => {
    if (selectedWorkIds.includes(workId)) {
      // 取消选中
      setSelectedWorkIds(selectedWorkIds.filter(id => id !== workId));
    } else {
      // 选中
      setSelectedWorkIds([...selectedWorkIds, workId]);
      // 清除作品选择的验证错误
      if (validationErrors.workSelection) {
        setValidationErrors(prev => ({ ...prev, workSelection: false }));
      }
    }
  };

  // 检查是否需要显示滚动箭头
  const checkScrollArrow = () => {
    const element = modalContentRef.current;
    if (element) {
      const { scrollTop, scrollHeight, clientHeight } = element;
      const isAtBottom = scrollTop + clientHeight >= scrollHeight - 10; // 10px 容差
      setShowScrollArrow(!isAtBottom && scrollHeight > clientHeight);
    }
  };

  // 监听滚动事件
  useEffect(() => {
    const element = modalContentRef.current;
    if (element) {
      element.addEventListener('scroll', checkScrollArrow);
      // 初始检查
      checkScrollArrow();

      return () => {
        element.removeEventListener('scroll', checkScrollArrow);
      };
    }
  }, [visible, eventsTaskData, works]);

  // 阻止作品网格区域的滚轮事件传播到Modal
  useEffect(() => {
    if (!visible) return;

    const handleWheel = (e: WheelEvent) => {
      const target = e.target as HTMLElement;

      // 检查是否在作品网格容器内
      const worksGridContainer = target.closest('[class*="worksGridContainer"]');
      if (worksGridContainer) {
        // 阻止事件传播到Modal
        e.stopPropagation();

        // 找到作品网格元素
        const worksGrid = worksGridContainer.querySelector('[class*="worksGrid"]') as HTMLElement;
        if (worksGrid) {
          // 手动处理横向滚动
          const scrollAmount = e.deltaY;
          worksGrid.scrollLeft += scrollAmount;
        }

        // 阻止默认行为
        e.preventDefault();
        return false;
      }
    };

    // 使用passive: false来允许preventDefault
    document.addEventListener('wheel', handleWheel, { passive: false });

    return () => {
      document.removeEventListener('wheel', handleWheel);
    };
  }, [visible]);

  // 当内容变化时重新检查
  useEffect(() => {
    const timer = setTimeout(checkScrollArrow, 100);
    return () => clearTimeout(timer);
  }, [eventsTaskData, works, activeTab]);

  // 点击箭头滚动到底部
  const handleScrollToBottom = () => {
    const element = modalContentRef.current;
    if (element) {
      element.scrollTo({
        top: element.scrollHeight,
        behavior: 'smooth'
      });
    }
  };

  const handleUploadChange: UploadProps['onChange'] = async (info) => {
    setFileList(info.fileList);

    // 如果有文件，清除文件上传的验证错误
    if (info.fileList.length > 0 && validationErrors.fileUpload) {
      setValidationErrors(prev => ({ ...prev, fileUpload: false }));
    }

    // 如果有新的文件需要上传
    for (const file of info.fileList) {
      if (file.originFileObj && !file.url && file.status !== 'uploading' && file.status !== 'error') {
        try {
          // 设置文件状态为上传中
          file.status = 'uploading';
          setFileList([...info.fileList]);

          // 调用上传API
          const uploadedUrl = await uploadApi.uploadToOss(file.originFileObj);

          // 更新文件状态和URL
          file.status = 'done';
          file.url = uploadedUrl;

          // 更新已上传文件URL列表
          setUploadedFileUrls(prev => [...prev, uploadedUrl]);

          message.success(`${file.name} 上传成功`);
        } catch (error) {
          console.error('文件上传失败:', error);
          file.status = 'error';
          message.error(`${file.name} 上传失败`);
        }

        // 更新文件列表状态
        setFileList([...info.fileList]);
      }
    }
  };

  // 处理文件移除
  const handleRemoveFile = (file: UploadFile) => {
    // 如果文件已上传成功，从已上传URL列表中移除
    if (file.url) {
      setUploadedFileUrls(prev => prev.filter(url => url !== file.url));

      // 只有新上传的文件才调用删除API，之前提交的文件不删除
      if (!file.uid?.startsWith('previous-file-')) {
        uploadApi.deleteFromOss(file.url);
      }
    }
    return true; // 允许移除
  };

  const handleSubmit = async () => {
    if (!eventsTaskData) {
      message.error('任务数据未加载');
      return;
    }

    // 检查是否可以提交
    if (!submitStatus?.canSubmit) {
      message.error(submitStatus?.reason || '当前无法提交任务');
      return;
    }

    // 重置验证错误状态
    const errors: typeof validationErrors = {};

    // 基础表单验证
    if (!realName.trim()) {
      errors.realName = true;
    }
    if (!idNumber.trim()) {
      errors.idNumber = true;
    } else if (idNumber.trim().length !== 18) {
      errors.idNumber = true;
    }
    if (!affiliatedSchool.trim()) {
      errors.affiliatedSchool = true;
    }
    if (!organization.trim()) {
      errors.organization = true;
    }
    if (!contactPerson.trim()) {
      errors.contactPerson = true;
    }
    if (!contactPhone.trim()) {
      errors.contactPhone = true;
    }
    if (!competitionGroup.trim()) {
      errors.competitionGroup = true;
    }

    // 验证报名表文件
    if (activityData?.registrationForm && (!registrationFormFileList.length || !registrationFormFileList.find(f => f.status === 'done' && f.url))) {
      errors.registrationForm = true;
    }

    // 验证作品提交（必须二选一）
    const hasSelectedWork = activeTab === 'select' && selectedWorkIds.length > 0;
    const hasUploadedFile = activeTab === 'upload' && uploadedFileUrls.length > 0;

    if (!hasSelectedWork && !hasUploadedFile) {
      if (activeTab === 'select') {
        errors.workSelection = true;
      } else {
        errors.fileUpload = true;
      }
    }

    if (activeTab === 'select' && selectedWorkIds.length > 1) {
      message.error('当前只支持选择一个作品，请重新选择');
      return;
    }

    // 如果有验证错误，设置错误状态并显示错误信息
    if (Object.keys(errors).length > 0) {
      setValidationErrors(errors);

      // 显示具体的错误信息
      if (errors.realName) {
        message.error('请输入真实姓名');
      } else if (errors.idNumber) {
        if (!idNumber.trim()) {
          message.error('请输入证件号');
        } else {
          message.error('证件号必须为18位');
        }
      } else if (errors.affiliatedSchool) {
        message.error('请输入所属学校');
      } else if (errors.organization) {
        message.error('请输入机构单位');
      } else if (errors.contactPerson) {
        message.error('请输入联系人姓名');
      } else if (errors.contactPhone) {
        message.error('请输入联系电话');
      } else if (errors.competitionGroup) {
        message.error('请选择参赛组别');
      } else if (errors.registrationForm) {
        message.error('请上传报名表文件');
      } else if (errors.workSelection) {
        message.error('请选择一个作品');
      } else if (errors.fileUpload) {
        message.error('请上传至少一个文件');
      }

      return;
    }

    // 清除之前的验证错误
    setValidationErrors({});

    try {
      setSubmitting(true);

      const submitData: any = {
        instructorName: instructorName.trim(),
        schoolName: schoolName.trim(),
        contactPerson: contactPerson.trim(),
        contactPhone: contactPhone.trim(),
        workDescription: workDescription.trim(),
        realName: realName.trim(),
        idNumber: idNumber.trim(),
        affiliatedSchool: affiliatedSchool.trim(),
        organization: organization.trim(),
        instructorPhone: instructorPhone.trim(),
        competitionGroup: competitionGroup.trim(),
        registrationFormFile: registrationFormFileList.find(f => f.status === 'done' && f.url)?.url || '',
        isResubmit: submitStatus?.isResubmit || false,
      };

      // 根据当前模式设置提交数据，并确保清除另一种模式的数据
      if (activeTab === 'select' && selectedWorkIds.length > 0) {
        // 选择作品模式：设置作品ID，清除文件
        submitData.workId = selectedWorkIds[0];
        submitData.workFile = ''; // 明确清除文件字段
      } else if (activeTab === 'upload' && uploadedFileUrls.length > 0) {
        // 上传文件模式：设置文件，清除作品ID
        submitData.workFile = uploadedFileUrls.join(',');
        submitData.workId = 0; // 明确清除作品ID字段
      }

      // 调试：打印提交数据
      console.log('提交数据:', submitData);
      console.log('报名表文件列表:', registrationFormFileList);

      const response = await eventsTaskApi.submit(eventsTaskData.id, submitData);

      if (response.data.code === 200) {
        message.success(submitStatus?.isResubmit ? '重新提交成功' : '提交成功');
        onSuccess?.();
        onClose();
      } else {
        message.error(response.data.message || '提交失败');
      }
    } catch (error) {
      console.error('提交失败:', error);
      message.error('提交失败，请重试');
    } finally {
      setSubmitting(false);
    }
  };

  const handleCancel = () => {
    // 重置表单
    setActiveTab('select');
    setInstructorName('');
    setSchoolName('');
    setContactPerson('');
    setContactPhone('');
    setWorkDescription('');

    // 重置新增字段
    setRealName('');
    setIdNumber('');
    setAffiliatedSchool('');
    setOrganization('');
    setInstructorPhone('');
    setCompetitionGroup('');
    setRegistrationFormFileList([]);

    setFileList([]);
    setUploadedFileUrls([]);
    setEventsTaskData(null);
    setSubmitStatus(null);
    setLoading(false);
    setSubmitting(false);
    // 重置验证错误状态
    setValidationErrors({});
    // 重置作品相关状态
    resetWorksState();
    onClose();
  };

  if (!task) return null;

  return (
    <Modal
      title={null}
      open={visible}
      onCancel={handleCancel}
      footer={null}
      width="90%"
      style={{ maxWidth: '800px' }}
      centered
      className={styles.eventsTaskModal}
      getContainer={false}
    >
      <div className={styles.modalContainer}>
        <div className={styles.modalContent} ref={modalContentRef}>
          {/* 标题和时间 */}
          <div className={styles.titleSection}>
            <div className={styles.titleContainer}>
              <div className={styles.titleIcon}>
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" fill="currentColor"/>
                </svg>
              </div>
              <div className={styles.titleText}>
                <h2>{eventsTaskData?.eventName || task?.taskName || '赛事任务'}</h2>
                <p className={styles.dateRange}>
                  {eventsTaskData ? (
                    <>
                      {new Date(eventsTaskData.startTime).toLocaleDateString('zh-CN')} - {new Date(eventsTaskData.endTime).toLocaleDateString('zh-CN')}
                    </>
                  ) : (
                    <>
                      {task?.startDate && new Date(task.startDate).toLocaleDateString('zh-CN')} - {new Date(task?.endDate || new Date()).toLocaleDateString('zh-CN')}
                    </>
                  )}
                </p>
              </div>
            </div>
          </div>

          
        {/* 提交状态提示 */}
        {submitStatus && !submitStatus.canSubmit && (
          <div className={styles.statusAlert} style={{
            backgroundColor: '#fef2f2',
            borderColor: '#fecaca',
            color: '#dc2626',
            padding: '12px 16px',
            borderRadius: '8px',
            border: '1px solid',
            marginBottom: '16px',
            fontSize: '14px',
            display: 'flex',
            alignItems: 'center',
            gap: '8px'
          }}>
            <span>{submitStatus.reason}</span>
          </div>
        )}

        {submitStatus?.isResubmit && (
          <div className={styles.statusAlert} style={{
            backgroundColor: submitStatus.rejectReason ? '#fef2f2' : '#eff6ff',
            borderColor: submitStatus.rejectReason ? '#fecaca' : '#bfdbfe',
            color: submitStatus.rejectReason ? '#dc2626' : '#1d4ed8',
            padding: '12px 16px',
            borderRadius: '8px',
            border: '1px solid',
            marginBottom: '16px',
            fontSize: '14px'
          }}>
            <div>
              <div style={{ fontWeight: '500', marginBottom: '4px', display: 'flex', alignItems: 'center', gap: '6px' }}>
                <span style={{ fontSize: '16px' }}>{submitStatus.rejectReason ? '❌' : '🔄'}</span>
                <span>{submitStatus.rejectReason ? '审核不通过' : '重新提交模式'}</span>
              </div>
              {submitStatus.rejectReason ? (
                <div style={{ marginBottom: '8px' }}>
                  <div style={{ marginBottom: '4px' }}></div>
                  <div style={{
                    backgroundColor: 'rgba(220, 38, 38, 0.1)',
                    padding: '8px',
                    borderRadius: '4px',
                    fontWeight: '500'
                  }}>
                    {submitStatus.rejectReason}
                  </div>
                  <div style={{ marginTop: '8px', fontSize: '13px' }}>您可以修改后重新提交。</div>
                </div>
              ) : (
                <div style={{ marginBottom: '8px' }}>此任务已提交过，已为您加载上次提交的内容。您可以修改后重新提交。</div>
              )}
            </div>
          </div>
        )}

        {/* 标签切换 */}
        <div className={styles.tabSection}>
          <div className={styles.tabContainer}>
            <div className={styles.tabButtons}>
              <button
                type="button"
                onClick={() => {
                  if (activeTab === 'upload' && (fileList.length > 0 || uploadedFileUrls.length > 0)) {
                    Modal.confirm({
                      title: '切换提交方式',
                      content: '切换到选择作品模式将清除已上传的文件，是否继续？',
                      onOk: () => setActiveTab('select'),
                    });
                  } else {
                    setActiveTab('select');
                  }
                }}
                className={`${styles.tabButton} ${activeTab === 'select' ? styles.active : ''}`}
              >
                <span className={styles.tabIcon}>📁</span>
                选择作品
              </button>
              <button
                type="button"
                onClick={() => {
                  if (activeTab === 'select' && selectedWorkIds.length > 0) {
                    Modal.confirm({
                      title: '切换提交方式',
                      content: '切换到上传作品模式将清除已选择的作品，是否继续？',
                      onOk: () => setActiveTab('upload'),
                    });
                  } else {
                    setActiveTab('upload');
                  }
                }}
                className={`${styles.tabButton} ${activeTab === 'upload' ? styles.active : ''}`}
              >
                <span className={styles.tabIcon}>⬆️</span>
                上传作品
              </button>
            </div>
            <div className={styles.tabIndicator} />
          </div>
          {selectedWorkIds.length > 0 && (
            <div className={styles.selectionStatus}>
              已选择 {selectedWorkIds.length} 个作品
            </div>
          )}
        </div>


        {/* 选择作品模式 */}
        {activeTab === 'select' && (
          <div>
            {/* 作品选择区域 */}
            <div className="relative">
              {loadingWorks ? (
                <div className="flex justify-center items-center p-10">
                  <Spin tip="加载中..." />
                </div>
              ) : works.length > 0 ? (
                <div className={styles.worksGridContainer}>
                  <div
                    className={`${styles.worksGrid} ${validationErrors.workSelection ? styles.errorBorder : ''}`}
                  >
                  {/* 对作品进行排序：已选中的作品显示在前面 */}
                  {works
                    .sort((a, b) => {
                      const aSelected = selectedWorkIds.includes(a.id);
                      const bSelected = selectedWorkIds.includes(b.id);

                      // 已选中的排在前面
                      if (aSelected && !bSelected) return -1;
                      if (!aSelected && bSelected) return 1;

                      // 如果都选中或都未选中，保持原有顺序
                      return 0;
                    })
                    .map(work => (
                    <div
                      key={work.id}
                      className={`${styles.workCard} ${
                        selectedWorkIds.includes(work.id) ? styles.selected : ''
                      }`}
                      onClick={() => handleSelectWork(work.id)}
                    >
                      {/* 作品预览区域 */}
                      <div className={styles.workPreview}>
                        {work.coverImage || work.screenShotImage ? (
                          <img
                            src={work.coverImage || work.screenShotImage}
                            alt={work.title}
                            className={styles.workImage}
                          />
                        ) : (
                          <div className={styles.workPlaceholder}>
                            <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M4 4H20C20.5523 4 21 4.44772 21 5V19C21 19.5523 20.5523 20 20 20H4C3.44772 20 3 19.5523 3 19V5C3 4.44772 3.44772 4 4 4Z" stroke="currentColor" strokeWidth="2"/>
                              <path d="M9 9C9.55228 9 10 8.55228 10 8C10 7.44772 9.55228 7 9 7C8.44772 7 8 7.44772 8 8C8 8.55228 8.44772 9 9 9Z" fill="currentColor"/>
                              <path d="M21 15L16 10L11 15H21Z" stroke="currentColor" strokeWidth="2" strokeLinejoin="round"/>
                            </svg>
                          </div>
                        )}
                        {/* 选择指示器 */}
                        <div className={`${styles.selectionIndicator} ${selectedWorkIds.includes(work.id) ? styles.selected : ''}`}>
                          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9 12L11 14L15 10" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                          </svg>
                        </div>
                      </div>

                      {/* 作品信息区域 */}
                      <div className={styles.workInfo}>
                        <div className={styles.workTitle}>{work.title}</div>
                      </div>
                    </div>
                  ))}
                  </div>
                </div>
              ) : (
                <Empty description="暂无作品" />
              )}

              {/* 加载更多遮罩 */}
              {loadingMore && (
                <div className="absolute inset-0 backdrop-blur-sm bg-white/30 flex justify-center items-center z-50 rounded-md">
                  <div className="bg-white p-4 rounded-xl shadow-lg flex items-center border border-gray-100">
                    <Spin size="small" />
                    <span className="ml-2 text-sm">加载更多作品...</span>
                  </div>
                </div>
              )}
            </div>



            {/* 作品信息 */}
            <div className={styles.formGroup}>
              <div className={styles.formGroupHeader}>
                <h3>作品信息</h3>
                <p>请简要介绍您的作品</p>
              </div>

              <div className={styles.formSection}>
                <label>作品介绍</label>
                <Input.TextArea
                  value={workDescription}
                  onChange={(e) => setWorkDescription(e.target.value)}
                  placeholder="请简要介绍作品的创意、技术特点或亮点..."
                  rows={3}
                  className="w-full"
                />
              </div>
            </div>

            {/* 公共表单组件 */}
            <CompetitionForm
              realName={realName}
              setRealName={setRealName}
              idNumber={idNumber}
              setIdNumber={setIdNumber}
              competitionGroup={competitionGroup}
              setCompetitionGroup={setCompetitionGroup}
              affiliatedSchool={affiliatedSchool}
              setAffiliatedSchool={setAffiliatedSchool}
              organization={organization}
              setOrganization={setOrganization}
              availableGroups={availableGroups}
              contactPerson={contactPerson}
              setContactPerson={setContactPerson}
              contactPhone={contactPhone}
              setContactPhone={setContactPhone}
              instructorName={instructorName}
              setInstructorName={setInstructorName}
              instructorPhone={instructorPhone}
              setInstructorPhone={setInstructorPhone}
              activityData={activityData}
              registrationFormFileList={registrationFormFileList}
              setRegistrationFormFileList={setRegistrationFormFileList}
              uploadApi={uploadApi}
              validationErrors={validationErrors}
              setValidationErrors={setValidationErrors}
            />

            {/* 报名表组件 */}
            <RegistrationForm
              activityData={activityData}
              registrationFormFileList={registrationFormFileList}
              setRegistrationFormFileList={setRegistrationFormFileList}
              uploadApi={uploadApi}
              validationErrors={validationErrors}
              setValidationErrors={setValidationErrors}
            />
          </div>
        )}

        {/* 上传作品模式 */}
        {activeTab === 'upload' && (
          <div>
            {/* 上传区域 */}
            <div className={`${styles.uploadArea} ${validationErrors.fileUpload ? styles.errorBorder : ''}`} style={{ marginTop: '10px' }}>
              <div className="flex flex-col items-center relative z-10">
                <PlusOutlined className={styles.uploadIcon} />
                <p className={styles.uploadText}>上传作品文件</p>
                <p className={styles.uploadHint}>支持多种格式，拖拽或点击上传</p>
              </div>
              <Upload
                fileList={fileList}
                onChange={handleUploadChange}
                onRemove={handleRemoveFile}
                multiple
                showUploadList={{
                  showPreviewIcon: true,
                  showRemoveIcon: true,
                  showDownloadIcon: false,
                }}
                className={`mt-4 ${styles.fileList}`}
              >
                <Button icon={<UploadOutlined />}>选择文件</Button>
              </Upload>
            </div>

            {/* 公共表单组件 - 包含作品信息 */}
            <CompetitionForm
              realName={realName}
              setRealName={setRealName}
              idNumber={idNumber}
              setIdNumber={setIdNumber}
              competitionGroup={competitionGroup}
              setCompetitionGroup={setCompetitionGroup}
              affiliatedSchool={affiliatedSchool}
              setAffiliatedSchool={setAffiliatedSchool}
              organization={organization}
              setOrganization={setOrganization}
              availableGroups={availableGroups}
              contactPerson={contactPerson}
              setContactPerson={setContactPerson}
              contactPhone={contactPhone}
              setContactPhone={setContactPhone}
              instructorName={instructorName}
              setInstructorName={setInstructorName}
              instructorPhone={instructorPhone}
              setInstructorPhone={setInstructorPhone}
              workDescription={workDescription}
              setWorkDescription={setWorkDescription}
              showWorkDescription={true}
              activityData={activityData}
              registrationFormFileList={registrationFormFileList}
              setRegistrationFormFileList={setRegistrationFormFileList}
              uploadApi={uploadApi}
              validationErrors={validationErrors}
              setValidationErrors={setValidationErrors}
            />

            {/* 报名表组件 */}
            <RegistrationForm
              activityData={activityData}
              registrationFormFileList={registrationFormFileList}
              setRegistrationFormFileList={setRegistrationFormFileList}
              uploadApi={uploadApi}
              validationErrors={validationErrors}
              setValidationErrors={setValidationErrors}
            />
          </div>
        )}

        </div>

        {/* 底部按钮 */}
        <div className={styles.buttonGroup}>
          <Button onClick={handleCancel} disabled={submitting}>
            取消
          </Button>
          <div className="flex gap-2">
            <Button
              type="primary"
              onClick={handleSubmit}
              loading={submitting}
              disabled={loading}
            >
              {submitStatus?.isResubmit ? '重新提交' : '提交'}
            </Button>

          </div>
        </div>

        {/* 悬浮的向下箭头 */}
        {showScrollArrow && (
          <div className={styles.scrollArrowContainer} onClick={handleScrollToBottom}>
            <div className={styles.scrollArrow}>
              <DownOutlined />
            </div>
            <div className={styles.scrollText}>向下滑动</div>
          </div>
        )}
      </div>
    </Modal>
  );
};

export default EventsTaskModal;
