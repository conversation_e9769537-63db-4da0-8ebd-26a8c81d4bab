# 其他设备登录检测功能文档

## 概述

前端现在能够智能检测"账号已在其他设备登录"的错误，并显示相应的用户友好提示。

## 功能特性

### 🔍 **智能错误检测**

系统会检测以下几种格式的"其他设备登录"错误：

#### **1. 通过 type 字段检测**
```json
{
  "statusCode": 401,
  "type": "OTHER_DEVICE_LOGIN",
  "message": "其他设备登录"
}
```

#### **2. 通过 msg 字段检测（主要场景）**
```json
{
  "statusCode": 401,
  "details": {
    "code": 401,
    "msg": "您的账号已在其他设备登录",
    "data": null
  }
}
```

#### **3. 通过 message 字段检测**
```json
{
  "statusCode": 401,
  "message": "您的账号已在其他设备登录"
}
```

#### **4. 嵌套结构检测**
```json
{
  "statusCode": 401,
  "details": {
    "type": "OTHER_DEVICE_LOGIN"
  }
}
```

### 🎯 **检测逻辑**

```typescript
const isOtherDeviceLogin = 
  errorData?.type === 'OTHER_DEVICE_LOGIN' ||
  detailsData?.type === 'OTHER_DEVICE_LOGIN' ||
  (errorData?.msg && errorData.msg.includes('账号已在其他设备登录')) ||
  (detailsData?.msg && detailsData.msg.includes('账号已在其他设备登录')) ||
  (errorData?.message && errorData.message.includes('账号已在其他设备登录')) ||
  (detailsData?.message && detailsData.message.includes('账号已在其他设备登录'));
```

### 💬 **用户界面**

当检测到其他设备登录时，会显示以下弹框：

```
┌─────────────────────────────────┐
│            账号异常              │
├─────────────────────────────────┤
│  您的账号已在其他设备登录，      │
│  当前登录已失效，请重新登录      │
├─────────────────────────────────┤
│              [重新登录]          │
└─────────────────────────────────┘
```

#### **弹框特性：**
- ✅ 居中显示
- ✅ 不可关闭（无X按钮）
- ✅ 不可点击遮罩关闭
- ✅ 只有一个"重新登录"按钮
- ✅ 全局只显示一次（防重复）

## 代码实现

### 📝 **核心检测代码**

```typescript
// 处理401状态码的错误
if (error.response?.status === 401) {
  const detailsData = errorData?.details || errorData;

  // 检查是否是其他设备登录的错误
  const isOtherDeviceLogin = 
    errorData?.type === 'OTHER_DEVICE_LOGIN' ||
    detailsData?.type === 'OTHER_DEVICE_LOGIN' ||
    (errorData?.msg && errorData.msg.includes('账号已在其他设备登录')) ||
    (detailsData?.msg && detailsData.msg.includes('账号已在其他设备登录')) ||
    (errorData?.message && errorData.message.includes('账号已在其他设备登录')) ||
    (detailsData?.message && detailsData.message.includes('账号已在其他设备登录'));

  // 如果是其他设备登录，直接显示提示
  if (isOtherDeviceLogin && !hasShownModal) {
    hasShownModal = true;
    Modal.confirm({
      title: '账号异常',
      content: '您的账号已在其他设备登录，当前登录已失效，请重新登录',
      okText: '重新登录',
      maskClosable: false,
      keyboard: true,
      centered: true,
      className: 'other-device-login-modal',
      closable: false,
      cancelButtonProps: {
        style: { display: 'none' }
      },
      onOk: () => {
        handleLogout('其他设备登录，当前会话失效');
        window.location.href = '/home';
      }
    });
    return Promise.reject(error);
  }
}
```

### 🔄 **处理流程**

```mermaid
graph TD
    A[收到401错误] --> B{检查错误消息}
    B -->|包含"账号已在其他设备登录"| C[显示其他设备登录弹框]
    B -->|不包含| D[继续token刷新流程]
    C --> E[用户点击重新登录]
    E --> F[执行登出操作]
    F --> G[跳转到首页]
    D --> H[尝试刷新token]
    H -->|成功| I[重新发起请求]
    H -->|失败| J[显示token过期弹框]
```

## 测试场景

### 🧪 **测试用例**

#### **1. 正常的其他设备登录检测**
```javascript
// 模拟后端返回的错误
const mockError = {
  response: {
    status: 401,
    data: {
      details: {
        code: 401,
        msg: "您的账号已在其他设备登录",
        data: null
      }
    }
  }
};

// 预期结果：显示"账号异常"弹框
```

#### **2. 普通token过期**
```javascript
// 模拟后端返回的错误
const mockError = {
  response: {
    status: 401,
    data: {
      details: {
        code: 401,
        msg: "token已过期",
        data: null
      }
    }
  }
};

// 预期结果：尝试刷新token，不显示其他设备登录弹框
```

#### **3. 其他401错误**
```javascript
// 模拟后端返回的错误
const mockError = {
  response: {
    status: 401,
    data: {
      details: {
        code: 401,
        msg: "权限不足",
        data: null
      }
    }
  }
};

// 预期结果：执行普通的401错误处理，不显示其他设备登录弹框
```

### 📊 **验证方法**

1. **开发者工具验证**：
   ```javascript
   // 在浏览器控制台查看日志
   // 应该看到：🔍 检查其他设备登录状态: { isOtherDeviceLogin: true, ... }
   ```

2. **网络请求验证**：
   - 发起任意API请求
   - 后端返回包含"账号已在其他设备登录"的401错误
   - 前端应该显示相应弹框

3. **用户体验验证**：
   - 弹框标题为"账号异常"
   - 内容提示清晰
   - 只有"重新登录"按钮
   - 点击后跳转到首页

## 与其他功能的区别

### 🔄 **Token刷新 vs 其他设备登录**

| 特性 | Token刷新 | 其他设备登录 |
|------|-----------|--------------|
| **触发条件** | token过期 | 账号在其他设备登录 |
| **用户感知** | 无感知（自动刷新） | 强提示（弹框） |
| **处理方式** | 自动重试请求 | 强制登出 |
| **弹框标题** | "登录失效" | "账号异常" |
| **按钮文本** | "确定" | "重新登录" |

### 🎯 **优先级处理**

1. **最高优先级**：其他设备登录 → 立即显示弹框
2. **中等优先级**：Token过期 → 尝试自动刷新
3. **最低优先级**：其他401错误 → 普通错误处理

## 配置选项

### 🔧 **可自定义的部分**

```typescript
// 弹框配置
Modal.confirm({
  title: '账号异常',                    // 可自定义标题
  content: '您的账号已在其他设备登录...',  // 可自定义内容
  okText: '重新登录',                   // 可自定义按钮文本
  className: 'other-device-login-modal', // 可自定义样式类
  // ... 其他配置
});
```

### 📝 **检测关键词**

```typescript
// 可以添加更多检测关键词
const otherDeviceKeywords = [
  '账号已在其他设备登录',
  '其他设备登录',
  '设备冲突',
  // 可以根据需要添加更多
];
```

## 注意事项

### ⚠️ **重要提醒**

1. **防重复显示**：使用 `hasShownModal` 标志防止重复显示弹框
2. **精确匹配**：只有包含特定关键词才显示其他设备登录弹框
3. **优先处理**：其他设备登录的优先级高于token刷新
4. **用户体验**：弹框不可关闭，确保用户必须处理

### 🔍 **调试信息**

系统会在控制台输出详细的调试信息：

```
🔍 检查其他设备登录状态: {
  isOtherDeviceLogin: true,
  errorData: {...},
  detailsData: {...},
  hasShownModal: false
}
```

## 总结

这个功能确保了：

### ✅ **准确性**
- 精确识别"其他设备登录"错误
- 区分不同类型的401错误

### ✅ **用户体验**
- 清晰的错误提示
- 强制用户处理冲突
- 自动跳转到登录页

### ✅ **健壮性**
- 支持多种错误格式
- 防重复显示
- 完整的错误处理

### ✅ **可维护性**
- 清晰的代码结构
- 详细的调试信息
- 易于扩展和修改
