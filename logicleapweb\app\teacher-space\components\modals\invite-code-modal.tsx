import { Modal, Button } from 'antd';
import { GetNotification } from 'logic-common/dist/components/Notification';

interface InviteCodeModalProps {
  inviteCode: string;
}

export const InviteCodeModal: React.FC<InviteCodeModalProps> = ({
  inviteCode,
}) => {
  const notification = GetNotification();
  
  return (
    <Modal
      title="邀请码生成成功"
      centered
      open
      closable={false}
      footer={[
        <Button key="close" type="primary" onClick={() => Modal.destroyAll()}>
          关闭
        </Button>
      ]}
      className="custom-modal"
      style={{
        maxWidth: '90vw',
        margin: '10vh auto',
        padding: 0,
        top: 0
      }}
      styles={{
        body: {
          padding: '20px'
        }
      }}
    >
      <div className="space-y-4">
        <div className="flex items-center justify-between bg-gray-50 p-3 rounded-lg">
          <span className="font-mono text-lg text-blue-600 mr-4">{inviteCode}</span>
          <Button
            type="text"
            icon={<i className="ri-file-copy-line" />}
            onClick={() => {
              navigator.clipboard.writeText(inviteCode)
                .then(() => notification.success('邀请码已复制'))
                .catch(() => notification.error('复制失败，请手动复制'));
            }}
            style={{ width: '120px' }}
            className="h-8 flex items-center justify-center gap-2 hover:bg-gray-100"
          >
            复制
          </Button>
        </div>
        <div className="text-gray-500 text-sm space-y-2">
          <p>您可以:</p>
          <ul className="list-disc list-inside space-y-1">
            <li>将邀请码分享给学生，让他们加入班级</li>
            <li>邀请其他老师作为协助教师加入班级</li>
          </ul>
          <p className="text-yellow-500">
            <i className="ri-timer-line mr-1" />
            邀请码有效期为24小时
          </p>
        </div>
      </div>
    </Modal>
  );
}; 