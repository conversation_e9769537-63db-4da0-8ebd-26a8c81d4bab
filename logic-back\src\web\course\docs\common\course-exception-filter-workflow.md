
## 时序图

```mermaid
sequenceDiagram
    participant C as 客户端
    participant Ctrl as 控制器
    participant Svc as 业务服务
    participant Filter as CourseExceptionFilter
    participant Log as 日志服务
    participant Resp as HTTP响应

    Note over C,Resp: 异常处理流程
    
    C->>Ctrl: 1.发送请求
    activate Ctrl
    
    Ctrl->>Svc: 2.调用业务方法
    activate Svc
    
    Svc--xCtrl: 3.抛出异常
    deactivate Svc
    
    Note over Filter: 异常过滤器介入
    Ctrl->>Filter: 4.异常被捕获
    activate Filter
    
    Filter->>Log: 5.记录异常日志
    Log-->>Filter: 6.日志完成
    
    Filter->>Filter: 7.判断异常类型
    
    alt 包含"不存在"
        Filter->>Filter: 设置404状态码
    else 包含"已存在"  
        Filter->>Filter: 设置400状态码
    else 数据库异常
        Filter->>Filter: 设置500状态码
    else 其他异常
        Filter->>Filter: 设置500状态码
    end
    
    Filter->>Resp: 8.构造标准响应
    deactivate Filter
    deactivate Ctrl
    
    Resp-->>C: 9.返回错误响应
    
    Note right of C: 响应格式:<br/>{"success":false,<br/>"message":"标签不存在",<br/>"code":404}
```

## 核心处理逻辑

### 异常分类处理
1. **HttpException** → 使用原状态码
2. **业务异常**：
   - "不存在" → 404 NOT_FOUND
   - "已存在" → 400 BAD_REQUEST  
   - "正在被使用" → 400 BAD_REQUEST
   - "无权限" → 403 FORBIDDEN
3. **数据库异常** → 500 INTERNAL_SERVER_ERROR
4. **未知异常** → 500 INTERNAL_SERVER_ERROR

### 与原 try-catch 对比
- **原方式**: 每个方法重复异常处理
- **过滤器**: 统一处理，代码简洁80%