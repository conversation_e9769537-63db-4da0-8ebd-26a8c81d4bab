/*
 Navicat Premium Data Transfer

 Source Server         : logic
 Source Server Type    : MySQL
 Source Server Version : 50744 (5.7.44-log)
 Source Host           : ************:13306
 Source Schema         : logicleaptest

 Target Server Type    : MySQL
 Target Server Version : 50744 (5.7.44-log)
 File Encoding         : 65001

 Date: 30/07/2025 10:17:30
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for course_teaching_records
-- ----------------------------
DROP TABLE IF EXISTS `course_teaching_records`;
CREATE TABLE `course_teaching_records`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `course_id` int(11) NOT NULL COMMENT '课程ID',
  `class_id` int(11) NOT NULL COMMENT '班级ID',
  `teacher_id` int(11) NOT NULL COMMENT '教师ID',
  `status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '状态：0=进行中，1=成功，2=失败',
  `points_allocated` int(11) NOT NULL DEFAULT 0 COMMENT '分配的积分总数',
  `tasks_created` int(11) NOT NULL DEFAULT 0 COMMENT '创建的任务数量',
  `template_applied` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否应用了模板：0=否，1=是',
  `error_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '错误信息',
  `execution_details` json NULL COMMENT '执行详情',
  `lock_acquire_time` int(11) NOT NULL DEFAULT 0 COMMENT '获取锁耗时（毫秒）',
  `total_execution_time` int(11) NOT NULL DEFAULT 0 COMMENT '总执行耗时（毫秒）',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_date` date GENERATED ALWAYS AS (cast(`created_at` as date)) VIRTUAL COMMENT '创建日期（用于分区）' NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_course_class_teacher_date`(`course_id`, `class_id`, `teacher_id`, `created_at`) USING BTREE,
  INDEX `idx_course`(`course_id`) USING BTREE,
  INDEX `idx_class`(`class_id`) USING BTREE,
  INDEX `idx_teacher`(`teacher_id`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE,
  INDEX `idx_teacher_status_time`(`teacher_id`, `status`, `created_at`) USING BTREE,
  INDEX `idx_execution_time`(`total_execution_time`) USING BTREE,
  CONSTRAINT `course_teaching_records_ibfk_1` FOREIGN KEY (`course_id`) REFERENCES `courses` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `course_teaching_records_ibfk_2` FOREIGN KEY (`class_id`) REFERENCES `user_class` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `course_teaching_records_ibfk_3` FOREIGN KEY (`teacher_id`) REFERENCES `user_info` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 485 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '课程教学记录表' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
