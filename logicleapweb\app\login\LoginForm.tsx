'use client'

// 添加全局样式确保下拉框文字居中
import './select-styles.css';

import { fetchSchools, getPcaData } from '@/lib/api/common';
import userApi from '@/lib/api/user';
import { auth } from '@/lib/auth';
import { setUser, clearUser } from '@/lib/store';
import { message, Select, Spin, AutoComplete, Modal } from 'antd';
import axios from 'axios';
import { Eye, EyeOff, X, AlertCircle } from 'lucide-react';
import Image from 'next/image';
import { useRouter, useSearchParams } from 'next/navigation';
import React from 'react';
import { useEffect, useRef, useState, } from 'react';
import { useDispatch } from 'react-redux';

// 导入学校API
import { schoolApi } from '@/lib/api/school';

// 添加import
import InlineSetPassword from './components/inline-set-password';
import InlineBindPhone from './components/inline-bind-phone';
import InlineChooseAccount from './components/inline-choose-account';
import InlineRoleSelection from './components/inline-role-selection';
import InlineTeacherAuth from './components/InlineTeacherAuth';
import SlideContainer from './components/SlideContainer';
import NotificationUtil from '@/lib/utils/notification';

// 颜色变量定义
const COLORS = {
  text: {
    primary: 'text-black',
    secondary: 'text-gray-600',
    accent: 'text-blue-600',
    light: 'text-gray-500',
    white: 'text-white'
  },
  bg: {
    primary: 'bg-white',
    secondary: 'bg-gray-50',
    accent: 'bg-blue-600'
  },
  border: {
    primary: 'border-gray-200',
    accent: 'border-blue-300'
  }
};

// 二维码状态类型
interface QRCodeStatus {
  status: 'waiting' | 'scanned' | 'success' | 'cancelled' | 'expired' | 'error';
  message: string;
  token?: string;
  redirect_url?: string;
};

// 定义密码设置模态框组件
const SetPasswordModal: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  phone: string;
}> = ({
  isOpen,
  onClose,
  onSuccess,
  phone
}) => {
    const [password, setPassword] = useState('');
    const [confirmPassword, setConfirmPassword] = useState('');
    const [loading, setLoading] = useState(false);

    // 处理表单提交
    const handleSubmit = async (e: React.FormEvent) => {
      e.preventDefault();
      if (!password || password.length < 6) {
        message.error('密码长度不能少于6位');
        return;
      }
      if (password !== confirmPassword) {
        message.error('两次输入的密码不一致');
        return;
      }

      setLoading(true);
      try {
        const response = await auth.setPassword(phone, password);
        if (response.data && response.data.code === 200) {
          message.success('密码设置成功');
          onSuccess();
        } else {
          message.error((response.data && response.data.message) || '密码设置失败，请稍后重试');
        }
      } catch (error) {
        console.error('密码设置失败:', error);
        message.error('密码设置失败，请稍后重试');
      } finally {
        setLoading(false);
      }
    };

    // 处理取消操作
    const handleCancel = async () => {
      // 如果用户点击右上角关闭按钮或取消，则自动设置默认密码为123456
      setLoading(true);
      try {
        const response = await auth.setPassword(phone, '123456');
        if (response.data && response.data.code === 200) {
          message.success('已为您设置默认密码: 123456');
          onSuccess();
        } else {
          message.error((response.data && response.data.message) || '默认密码设置失败');
        }
      } catch (error) {
        console.error('默认密码设置失败:', error);
        message.error('默认密码设置失败，请稍后重试');
      } finally {
        setLoading(false);
        onClose();
      }
    };

    if (!isOpen) return null;

    return (
      <div className="fixed inset-0 flex items-center justify-center bg-black/50 z-50">
        <div className="bg-white rounded-lg p-6 max-w-md w-full">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium">设置密码</h3>
            <button onClick={handleCancel} className="text-gray-400 hover:text-gray-600">
              <EyeOff size={20} />
            </button>
          </div>
          <p className="mb-4 text-gray-600">请为您的账号设置一个密码，方便下次登录</p>
          <form onSubmit={handleSubmit}>
            <div className="mb-4">
              <label className="block mb-2 text-sm font-medium">
                <span className="text-red-500">*</span> 密码
              </label>
              <input
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="请输入密码"
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
                required
                minLength={6}
              />
            </div>
            <div className="mb-4">
              <label className="block mb-2 text-sm font-medium">
                <span className="text-red-500">*</span> 确认密码
              </label>
              <input
                type="password"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                placeholder="请再次输入密码"
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
                required
                minLength={6}
              />
            </div>
            <button
              type="submit"
              className="w-full py-2 px-4 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              disabled={loading}
            >
              {loading ? '提交中...' : '确认'}
            </button>
          </form>
        </div>
      </div>
    );
  };

// 定义协议模态框组件
const PolicyModal: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
}> = ({ isOpen, onClose, title, children }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black/50 z-50">
      <div className="bg-white rounded-lg p-6 max-w-2xl w-full max-h-[80vh] flex flex-col">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium">{title}</h3>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
            <X size={20} />
          </button>
        </div>
        <div className="prose max-w-none overflow-y-auto">
          {children}
        </div>
      </div>
    </div>
  );
};

// --- Style Constants ---
const inputBaseClass = `w-full px-4 py-2.5 bg-white/10 border border-blue-300/50 rounded-xl focus:ring-2 focus:ring-blue-500/40 focus:border-blue-500 placeholder:${COLORS.text.light} ${COLORS.text.primary} transition-all duration-200 ease-in-out outline-none backdrop-blur-sm`;
const loginButtonClass = `w-full flex justify-center py-3 px-4 border border-transparent rounded-xl shadow-lg text-sm font-medium ${COLORS.text.white} ${COLORS.bg.accent} hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-blue-100 focus:ring-blue-500 disabled:opacity-60 transition-all`;
const getCodeButtonClass = `px-4 h-[56px] text-sm font-medium rounded-xl disabled:bg-blue-400/80 disabled:cursor-not-allowed whitespace-nowrap hover:bg-blue-700 transition-colors shrink-0`;

// --- Floating Label Styles ---
const floatingInputClass = `peer h-[56px] w-full px-4  bg-transparent border border-blue-300/50 rounded-xl focus:ring-2 focus:ring-blue-500/40 focus:border-blue-500 ${COLORS.text.primary} transition-all duration-200 ease-in-out outline-none backdrop-blur-sm`;
// placeholder显示时，peer监测同级的input状态
const floatingLabelClass = `absolute left-4 top-0 -translate-y-1/2 text-xs ${COLORS.text.primary} bg-white px-2 transition-all duration-100 ease-in-out pointer-events-none peer-placeholder-shown:left-4 peer-placeholder-shown:top-1/2 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:text-base peer-placeholder-shown:text-blue-500/80 peer-placeholder-shown:bg-transparent peer-placeholder-shown:px-0 peer-focus:left-4 peer-focus:top-0 peer-focus:-translate-y-1/2 peer-focus:text-xs peer-focus:bg-white peer-focus:px-2`;

// --- Sub-components for different login modes ---

interface VerifyCodeLoginProps {
  phone: string;
  setPhone: (value: string) => void;
  verifyCode: string;
  setVerifyCode: (value: string) => void;
  handleSendCode: () => void;
  countdown: number;
  validationErrors: Record<string, boolean>;
  setValidationErrors: (errors: Record<string, boolean>) => void;
}

const VerifyCodeLogin: React.FC<VerifyCodeLoginProps> = ({ phone, setPhone, verifyCode, setVerifyCode, handleSendCode, countdown, validationErrors, setValidationErrors }) => (
  <div className="space-y-6">
    <div className="relative">
      <input
        id="phone-verify"
        type="tel"
        required
        value={phone}
        onChange={(e) => setPhone(e.target.value)}
        className={floatingInputClass}
        placeholder=""
        onClick={(e) => e.currentTarget.focus()}
      />
      <label htmlFor="phone-verify" className={floatingLabelClass}>手机号</label>
    </div>
    <div className="flex items-start space-x-2">
      <div className="relative flex-grow">
        <input
          id="code"
          type="text"
          value={verifyCode}
          onChange={(e) => setVerifyCode(e.target.value)}
          className={floatingInputClass}
          placeholder=""
          onClick={(e) => e.currentTarget.focus()}
        />
        <label htmlFor="code" className={floatingLabelClass}>验证码</label>
      </div>
      <button type="button" onClick={handleSendCode} disabled={countdown > 0} className={`${getCodeButtonClass} ${COLORS.bg.accent} ${COLORS.text.white}`}>{countdown > 0 ? `${countdown}s` : '获取验证码'}</button>
    </div>
  </div>
);

interface PasswordLoginProps {
  phone: string;
  setPhone: (value: string) => void;
  password: string;
  setPassword: (value: string) => void;
  showPassword: boolean;
  setShowPassword: (value: boolean) => void;
  validationErrors: Record<string, boolean>;
  setValidationErrors: (errors: Record<string, boolean>) => void;
}

const PasswordLogin: React.FC<PasswordLoginProps> = ({ phone, setPhone, password, setPassword, showPassword, setShowPassword, validationErrors, setValidationErrors }) => (
  <div className="space-y-6">
    <div className="relative">
      <input
        id="phone-password"
        type="tel"
        value={phone}
        onChange={(e) => {
          const newValue = e.target.value;
          setPhone(newValue);
          if (validationErrors.phone && newValue) {
            const newErrors = { ...validationErrors };
            delete newErrors.phone;
            setValidationErrors(newErrors);
          }
        }}
        className={floatingInputClass}
        placeholder=""
        onClick={(e) => e.currentTarget.focus()}
      />
      <label htmlFor="phone-password" className={floatingLabelClass}>手机号</label>
    </div>
    <div className="relative">
      <input
        id="password"
        type={showPassword ? 'text' : 'password'}
        value={password}
        onChange={(e) => {
          const newValue = e.target.value;
          setPassword(newValue);
          if (validationErrors.password && newValue) {
            const newErrors = { ...validationErrors };
            delete newErrors.password;
            setValidationErrors(newErrors);
          }
        }}
        className={floatingInputClass}
        placeholder=""
        onClick={(e) => e.currentTarget.focus()}
      />
      <label htmlFor="password" className={floatingLabelClass}>密码</label>
      <button type="button" onClick={() => setShowPassword(!showPassword)} className={`absolute inset-y-0 right-0 px-3 flex items-center ${COLORS.text.primary} hover:${COLORS.text.primary}`}>{showPassword ? <EyeOff size={18} /> : <Eye size={18} />}</button>
    </div>
  </div>
);

interface StudentLoginProps {
  studentProvince: string | undefined;
  setStudentProvince: (value: string | undefined) => void;
  studentCity: string | undefined;
  setStudentCity: (value: string | undefined) => void;
  studentDistrict: string | undefined;
  setStudentDistrict: (value: string | undefined) => void;
  studentSchool: string | undefined;
  setStudentSchool: (value: string | undefined) => void;
  studentNumber: string;
  setStudentNumber: (value: string) => void;
  studentPassword: string;
  setStudentPassword: (value: string) => void;
  studentShowPassword: boolean;
  setStudentShowPassword: (value: boolean) => void;
  setSelectedSchool: (value: any) => void;
  pcaData: any[];
  cities: any[];
  districts: any[];
  schools: any[];
  isSchoolLoading: boolean;
  handleSchoolSearch: (value: string) => void;
  isMunicipality: (value?: string) => boolean;
  validationErrors: Record<string, boolean>;
  setValidationErrors: (errors: Record<string, boolean>) => void;
  schoolSearchIsFocused: boolean;
  setSchoolSearchIsFocused: (value: boolean) => void;
  studentNumberFocused: boolean;
  setStudentNumberFocused: (value: boolean) => void;
  studentPasswordFocused: boolean;
  setStudentPasswordFocused: (value: boolean) => void;
}

const FloatingLabelSelect: React.FC<any> = ({ label, value, children, hasError, ...props }) => {
  const [isFocused, setIsFocused] = useState(false);
  const hasValue = value !== undefined && value !== null && value !== '';
  const isLabelFloating = isFocused || hasValue;

  return (
    <div className="relative">
      <Select
        {...props}
        value={value}
        onFocus={() => setIsFocused(true)}
        onBlur={() => setIsFocused(false)}
        className={`student-login-select w-full text-center h-[56px]  ${hasError ? 'has-error' : ''}`}
        dropdownStyle={{ backgroundColor: 'rgba(255, 255, 255, 0.95)', boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)' }}
        dropdownMatchSelectWidth={false}
        showSearch={props.showSearch}
        optionFilterProp="label"
        placeholder=""
      >
        {children}
      </Select>
      <label
        className={`absolute z-10  transition-all px-2 duration-100 ease-in-out  pointer-events-none ${isLabelFloating
          ? `-translate-y-1/2 left-4 top-0  text-xs  ${COLORS.text.primary} bg-white   rounded-sm `
          : `flex  left-4  text-base ${COLORS.text.primary} `
          }`}
      >
        {label}
      </label>
    </div>
  );
};

const StudentLogin: React.FC<StudentLoginProps> = ({
  studentProvince, setStudentProvince,
  studentCity, setStudentCity,
  studentDistrict, setStudentDistrict,
  studentSchool, setStudentSchool,
  studentNumber, setStudentNumber,
  studentPassword, setStudentPassword,
  studentShowPassword, setStudentShowPassword,
  setSelectedSchool,
  pcaData, cities, districts, schools,
  isSchoolLoading, handleSchoolSearch, isMunicipality,
  validationErrors, setValidationErrors,
  schoolSearchIsFocused, setSchoolSearchIsFocused,
  studentNumberFocused, setStudentNumberFocused,
  studentPasswordFocused, setStudentPasswordFocused
}) => (
  <div className="grid grid-cols-6 auto-rows-auto gap-x-3 gap-y-6">
    {isMunicipality(studentProvince) ? (
      <>
        <div className="col-span-2">
          <FloatingLabelSelect
            label="省"
            value={studentProvince}
            onChange={(val: string) => {
              setStudentProvince(val);
              setStudentCity(undefined);
              setStudentDistrict(undefined);
              setStudentSchool(undefined);
              setSelectedSchool(null);
              if (validationErrors.studentProvince && val) {
                const newErrors = { ...validationErrors };
                delete newErrors.studentProvince;
                delete newErrors.studentCity;
                delete newErrors.studentDistrict;
                delete newErrors.studentSchool;
                setValidationErrors(newErrors);
              }
            }}
            options={pcaData.map(p => ({
              value: p.name,
              label: p.name
            }))}
            hasError={!!validationErrors.studentProvince}
          />
        </div>
        <div className="col-span-4">
          <FloatingLabelSelect
            label="区"
            value={studentDistrict}
            onChange={(val: string) => {
              setStudentDistrict(val);
              setStudentSchool(undefined);
              setSelectedSchool(null);
              if (validationErrors.studentDistrict && val) {
                const newErrors = { ...validationErrors };
                delete newErrors.studentDistrict;
                delete newErrors.studentSchool;
                setValidationErrors(newErrors);
              }
            }}
            options={districts.map(d => ({
              value: d.name,
              label: d.name
            }))}
            disabled={!studentProvince}
            popupClassName="student-login-select-dropdown"
            hasError={!!validationErrors.studentDistrict}
          />
        </div>
      </>
    ) : (
      <>
        <div className="col-span-2">
          <FloatingLabelSelect
            label="省"
            value={studentProvince}
            onChange={(val: string) => {
              setStudentProvince(val);
              setStudentCity(undefined);
              setStudentDistrict(undefined);
              setStudentSchool(undefined);
              setSelectedSchool(null);
              if (validationErrors.studentProvince && val) {
                const newErrors = { ...validationErrors };
                delete newErrors.studentProvince;
                delete newErrors.studentCity;
                delete newErrors.studentDistrict;
                delete newErrors.studentSchool;
                setValidationErrors(newErrors);
              }
            }}
            options={pcaData.map(p => ({
              value: p.name,
              label: p.name,
              code: p.code
            }))}
            popupClassName="student-login-select-dropdown"
            hasError={!!validationErrors.studentProvince}
          />
        </div>
        <div className="col-span-2">
          <FloatingLabelSelect
            label="市"
            value={studentCity}
            onChange={(val: string) => {
              setStudentCity(val);
              setStudentDistrict(undefined);
              setStudentSchool(undefined);
              setSelectedSchool(null);
              if (validationErrors.studentCity && val) {
                const newErrors = { ...validationErrors };
                delete newErrors.studentCity;
                delete newErrors.studentDistrict;
                delete newErrors.studentSchool;
                setValidationErrors(newErrors);
              }
            }}
            options={cities.map(c => ({
              value: c.name,
              label: c.name,
              code: c.code
            }))}
            disabled={!studentProvince}
            popupClassName="student-login-select-dropdown"
            hasError={!!validationErrors.studentCity}
          />
        </div>
        <div className="col-span-2">
          <FloatingLabelSelect
            label="区"
            value={studentDistrict}
            onChange={(val: string) => {
              setStudentDistrict(val);
              setStudentSchool(undefined);
              setSelectedSchool(null);
              if (validationErrors.studentDistrict && val) {
                const newErrors = { ...validationErrors };
                delete newErrors.studentDistrict;
                delete newErrors.studentSchool;
                setValidationErrors(newErrors);
              }
            }}
            options={districts.map(d => ({
              value: d.name,
              label: d.name,
              code: d.code
            }))}
            disabled={!studentCity}
            popupClassName="student-login-select-dropdown"
            hasError={!!validationErrors.studentDistrict}
          />
        </div>
      </>
    )}

    <div className="col-span-6">
      <div className="relative">
        <AutoComplete
          className={`peer student-login-select w-full h-[56px] ${validationErrors.studentSchool ? 'has-error' : ''}`}
          placeholder=""
          value={studentSchool}
          onChange={(value) => {
            if (typeof value === 'string') {
              setStudentSchool(value);

              if (validationErrors.studentSchool && value) {
                const newErrors = { ...validationErrors };
                delete newErrors.studentSchool;
                setValidationErrors(newErrors);
              }
            }
          }}
          onSelect={(value, option) => {
            console.log("选中学校:", value, option);

            // 保存选中的学校完整信息
            if (option && option.data) {
              const schoolData = option.data;
              const schoolName = schoolData.schoolName || schoolData.name || value;

              // 设置学校名称 - 不清空输入框的值
              console.log("设置学校名称 - 不清空输入框的值");

              setStudentSchool(schoolName);

              // 从学校数据中获取省市区信息
              const schoolProvince = schoolData.province && schoolData.province !== 'null' ? schoolData.province : '';
              const schoolCity = schoolData.city && schoolData.city !== 'null' ? schoolData.city : '';
              const schoolDistrict = schoolData.district && schoolData.district !== 'null' ? schoolData.district : '';

              // 更新省市区选择
              if (schoolProvince) {
                setStudentProvince(schoolProvince);

                // 如果是直辖市，设置城市为省份名
                if (isMunicipality(schoolProvince)) {
                  setStudentCity(schoolProvince);
                } else if (schoolCity) {
                  setStudentCity(schoolCity);
                }

                if (schoolDistrict) {
                  setStudentDistrict(schoolDistrict);
                }
              }

              // 保存完整学校信息
              setSelectedSchool({
                id: schoolData.id,
                schoolName: schoolName,
                province: schoolProvince || studentProvince,
                city: schoolCity || (isMunicipality(schoolProvince) ? schoolProvince : studentCity || ''),
                district: schoolDistrict || studentDistrict
              });
              console.log('已选择学校:', schoolData, '设置学校名称:', schoolName);
            }
          }}
          onSearch={(value) => {
            // 允许空字符串搜索，这样可以显示该地区的所有学校
            handleSchoolSearch(value);
            // 保持输入框的值不变
            if (value !== undefined) {
              setStudentSchool(value);
            }
          }}
          options={Array.isArray(schools) ? schools.map(s => {
            const hasDuplicateName = schools.some(school =>
              school.schoolName === s.schoolName &&
              (school.province !== s.province ||
                school.city !== s.city ||
                school.district !== s.district)
            );

            const formatLocation = () => {
              const schoolProvince = s.province && s.province !== 'null' ? s.province : '';
              const schoolCity = schoolProvince && !isMunicipality(schoolProvince) ?
                (s.city && s.city !== 'null' ? s.city : '') : '';
              const schoolDistrict = s.district && s.district !== 'null' ? s.district : '';

              if (!schoolProvince && !schoolCity && !schoolDistrict) {
                return '';
              }

              return `（${schoolProvince}${schoolCity}${schoolDistrict}）`;
            };

            return {
              value: hasDuplicateName ?
                `${s.schoolName || s.name}${formatLocation()}` :
                (s.schoolName || s.name),
              label: hasDuplicateName ?
                `${s.schoolName || s.name}${formatLocation()}` :
                (s.schoolName || s.name),
              data: s // 将完整的学校数据传递给option
            };
          }) : []}
          onFocus={() => {
            setSchoolSearchIsFocused(true)
            // 选择好地区后自动显示该地区的学校
            if (studentProvince && (isMunicipality(studentProvince) ? true : studentCity) && studentDistrict) {
              handleSchoolSearch("");
            }
          }}
          onBlur={() => setSchoolSearchIsFocused(false)}
          onDropdownVisibleChange={(open) => {
            // 当下拉框打开时，如果已经选择了地区，确保显示该地区的学校
            if (open && studentProvince && (isMunicipality(studentProvince) ? true : studentCity) && studentDistrict) {
              handleSchoolSearch("");
            }
          }}
          filterOption={(inputValue, option) => {
            if (!option?.value) return false;
            return String(option.value).toLowerCase().includes(inputValue.toLowerCase());
          }}
          notFoundContent={
            isSchoolLoading ? <Spin size="small" /> :
              schools.length === 0 ? '未找到学校，请输入关键词搜索' : '请选择学校'
          }
          dropdownStyle={{
            maxHeight: 200,
            overflow: 'auto',
            borderRadius: '12px',
            padding: '8px',
          }}
        />
        <label
          className={`absolute left-4 px-2 top-0 -translate-y-1/2  ${COLORS.text.primary} bg-white  transition-all duration-100 ease-in-out pointer-events-none ${!schoolSearchIsFocused && !studentSchool ? ' top-1/2  text-base bg-transparent ' : 'text-xs'}`}
        >
          请输入学校名称
        </label>
      </div>
    </div>

    <div className="relative col-span-3">
      <input
        type="text"
        id="studentNumber"
        placeholder=""
        value={studentNumber}
        onChange={e => {
          const newValue = e.target.value;
          setStudentNumber(newValue);
          if (validationErrors.studentNumber && newValue) {
            const newErrors = { ...validationErrors };
            delete newErrors.studentNumber;
            setValidationErrors(newErrors);
          }
        }}
        onFocus={() => setStudentNumberFocused(true)}
        onBlur={() => setStudentNumberFocused(false)}
        className={`peer h-[56px] w-full px-4 py-2 bg-transparent border border-blue-300/50 rounded-xl focus:ring-2 focus:ring-blue-500/40 focus:border-blue-500 ${COLORS.text.primary} transition-all duration-200 ease-in-out outline-none backdrop-blur-sm ${validationErrors.studentNumber ? 'has-error' : ''}`}
        onClick={(e) => e.currentTarget.focus()}
      />
      <label
        className={`absolute left-4 top-0 -translate-y-1/2 text-xs ${validationErrors.studentNumber ? 'text-red-500 font-medium' : COLORS.text.primary} bg-white px-2 transition-all duration-100 ease-in-out pointer-events-none peer-placeholder-shown:left-6 peer-placeholder-shown:top-1/2 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:text-base peer-placeholder-shown:bg-transparent peer-placeholder-shown:px-0 peer-focus:left-4 peer-focus:top-0 peer-focus:-translate-y-1/2 peer-focus:text-xs peer-focus:bg-white peer-focus:px-2`}
      >
        学号
      </label>
      {validationErrors.studentNumber && (
        <div className="absolute -bottom-6 left-0 right-0 text-red-500 text-xs flex items-center gap-1">
          <AlertCircle size={12} />
          <span>请输入学号</span>
        </div>
      )}
    </div>
    <div className="relative col-span-3">
      <input
        type={studentShowPassword ? 'text' : 'password'}
        id="studentPassword"
        placeholder=""
        value={studentPassword}
        onChange={e => {
          const newValue = e.target.value;
          setStudentPassword(newValue);
          if (validationErrors.studentPassword && newValue) {
            const newErrors = { ...validationErrors };
            delete newErrors.studentPassword;
            setValidationErrors(newErrors);
          }
        }}
        onFocus={() => setStudentPasswordFocused(true)}
        onBlur={() => setStudentPasswordFocused(false)}
        className={`peer h-[56px] w-full px-4 py-2 bg-transparent border border-blue-300/50 rounded-xl focus:ring-2 focus:ring-blue-500/40 focus:border-blue-500 ${COLORS.text.primary} transition-all duration-200 ease-in-out outline-none backdrop-blur-sm ${validationErrors.studentPassword ? 'has-error' : ''}`}
        onClick={(e) => e.currentTarget.focus()}
      />
      <label
        className={`absolute left-4 top-0 -translate-y-1/2 text-xs ${validationErrors.studentPassword ? 'text-red-500 font-medium' : COLORS.text.primary} bg-white px-2 transition-all duration-100 ease-in-out pointer-events-none peer-placeholder-shown:left-6 peer-placeholder-shown:top-1/2 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:text-base peer-placeholder-shown:bg-transparent peer-placeholder-shown:px-0 peer-focus:left-4 peer-focus:top-0 peer-focus:-translate-y-1/2 peer-focus:text-xs peer-focus:bg-white peer-focus:px-2`}
      >
        密码
      </label>
      <button type="button" onClick={() => setStudentShowPassword(!studentShowPassword)} className={`absolute inset-y-0 right-0 px-3 flex items-center ${COLORS.text.light} hover:${COLORS.text.primary}`}>{studentShowPassword ? <EyeOff size={18} /> : <Eye size={18} />}</button>
      {validationErrors.studentPassword && (
        <div className="absolute -bottom-6 left-0 right-0 text-red-500 text-xs flex items-center gap-1">
          <AlertCircle size={12} />
          <span>请输入密码</span>
        </div>
      )}
    </div>
  </div>
);

export default function LoginForm() {
  const router = useRouter();
  const searchParams = useSearchParams() || new URLSearchParams();
  const redirectToTargetPage = () => {
    // 先设置isSlideOut为true，防止登录表单闪现
    setIsSlideOut(true);
    // 稍后再设置flowOver为true
    setTimeout(() => {
      setFlowOver(true);
      // 关闭所有弹窗状态
      // 主要认证流程弹窗
      setShowPasswordModal(false);
      setShowBindPhoneModal(false);
      setShowRoleSelectModal(false);
      setShowChooseAccountModal(false);
      setShowAuthModal(false); // 教师认证弹窗

      const redirectUrl = searchParams.get('redirect');
      if (redirectUrl) {
        router.push(redirectUrl);
      } else {
        router.push('/home');
      }
    }, 50);
  };
  // 添加一个统一管理用户数据存储的函数
  const saveUserDataToLocalStorage = (userData: any, token?: string, refreshToken?: string) => {
    // 保存用户信息到Redux
    dispatch(setUser(userData));
    // 保存用户信息到localStorage，后续可能作出新的认证或者绑定会触发变化
    localStorage.setItem('user', JSON.stringify(userData));
    localStorage.setItem('userId', userData.userId.toString());
    // 以下token只有在首次登录才会更新
    if (token) {
      localStorage.setItem('token', token);
    }
    if (refreshToken) {
      localStorage.setItem('refreshToken', refreshToken);
    }
  };
  // 结束所有流程标志，这个是防止useEffect检测所有弹窗为false导致的登录表单短暂出现
  const [flowOver, setFlowOver] = useState(false);

  // 检查是否需要直接显示绑定手机号（不显示登录表单）
  const shouldShowBindPhoneDirectly = () => {
    const bindPhone = searchParams.get('bindPhone');
    const needBindPhone = searchParams.get('needBindPhone');
    return bindPhone === 'true' || needBindPhone === 'true';
  };

  // --- Main State ---
  const [loginMode, setLoginMode] = useState<'verify' | 'password' | 'student'>('verify');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [validationErrors, setValidationErrors] = useState<Record<string, boolean>>({});
  // 添加跳过绑定手机号的状态变量
  const [skipBindPhone, setSkipBindPhone] = useState(false);

  // --- Phone/Password State ---
  const [showPassword, setShowPassword] = useState(false);
  const [phone, setPhone] = useState('');
  const [password, setPassword] = useState('');
  const [verifyCode, setVerifyCode] = useState('');
  const [countdown, setCountdown] = useState(0);

  // --- QR Code State ---
  const [qrCodeUrl, setQrCodeUrl] = useState<string>('');
  const [qrCodeStatus, setQrCodeStatus] = useState<any>({ status: 'waiting', message: '等待扫码' });
  const [qrCodeExpired, setQrCodeExpired] = useState(false);
  const [qrCodeLoading, setQrCodeLoading] = useState(true);

  // --- Student Login State ---
  const [pcaData, setPcaData] = useState<any[]>([]);
  const [studentProvince, setStudentProvince] = useState<string | undefined>(undefined);
  const [studentCity, setStudentCity] = useState<string | undefined>(undefined);
  const [studentDistrict, setStudentDistrict] = useState<string | undefined>(undefined);
  const [studentSchool, setStudentSchool] = useState<string | undefined>(undefined);
  const [selectedSchool, setSelectedSchool] = useState<{
    id?: number;
    schoolName?: string;
    province?: string;
    city?: string;
    district?: string;
  } | null>(null);
  const [studentNumber, setStudentNumber] = useState('');
  const [studentPassword, setStudentPassword] = useState('');
  const [studentShowPassword, setStudentShowPassword] = useState(false);
  const [cities, setCities] = useState<any[]>([]);
  const [districts, setDistricts] = useState<any[]>([]);
  const [schools, setSchools] = useState<any[]>([]);
  const [isSchoolLoading, setIsSchoolLoading] = useState(false);
  const [showSchoolDropdown, setShowSchoolDropdown] = useState(false);
  const [schoolSearchIsFocused, setSchoolSearchIsFocused] = useState(false);
  const [studentNumberFocused, setStudentNumberFocused] = useState(false);
  const [studentPasswordFocused, setStudentPasswordFocused] = useState(false);
  const [agreedToTerms, setAgreedToTerms] = useState(false);
  const [policyModalContent, setPolicyModalContent] = useState<{ title: string, content: React.ReactNode } | null>(null);
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [isSlideOut, setIsSlideOut] = useState(false); // 添加滑出状态

  // 在组件内部的状态声明部分添加
  const [forcePasswordSetup, setForcePasswordSetup] = useState(false);

  // --- Refs ---
  const pollIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const countdownIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const verifyCodeCountdownRef = useRef<NodeJS.Timeout | null>(null);

  const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'https://logicleapai.cn';

  // 存储临时用户数据
  const [currentUserData, setCurrentUserData] = useState<any>(null);
  const dispatch = useDispatch();

  // 添加必要的ref和state
  const authFlowPromiseRef = useRef<{
    resolve: () => void;
    reject: (error?: any) => void;
  } | null>(null);
  const [showPasswordModal, setShowPasswordModal] = useState(false);
  const [showBindPhoneModal, setShowBindPhoneModal] = useState(false);
  const [showRoleSelectModal, setShowRoleSelectModal] = useState(false);
  const [showChooseAccountModal, setShowChooseAccountModal] = useState(false);

  // 在组件内部的状态声明部分查找是否有窗口宽度相关变量
  // 如果没有，添加下面这行
  const [windowWidth, setWindowWidth] = useState(typeof window !== 'undefined' ? window.innerWidth : 768);

  // 添加窗口大小监听效果
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const handleResize = () => {
        setWindowWidth(window.innerWidth);
      };

      window.addEventListener('resize', handleResize);
      return () => window.removeEventListener('resize', handleResize);
    }
  }, []);

  // 定义onClose和onSuccess，这些在登录对话框中通常由父组件传递
  const onClose = () => {
    // 关闭登录对话框的默认操作
    const redirectUrl = searchParams.get('redirect');
    if (redirectUrl) {
      router.push(redirectUrl);
    } else {
      window.close();
    }
  };

  const onSuccess = () => {
    // 登录成功的默认操作
    const redirectUrl = searchParams.get('redirect');
    if (redirectUrl) {
      router.push(redirectUrl);
    } else {
      window.close();
    }
  };

  // 绑定手机号成功的处理函数
  const handleBindPhoneSuccessLegacy = () => {
    // 保留此函数以兼容旧代码
    console.log("Legacy bind phone success handler called");
    // 如果需要更新用户信息，可以在这里实现
    if (onSuccess) {
      onSuccess();
    }
  };

  // --- QR Code Logic ---
  const stopPollingQRCodeStatus = () => {
    if (pollIntervalRef.current) clearInterval(pollIntervalRef.current);
    if (countdownIntervalRef.current) clearInterval(countdownIntervalRef.current);
  };

  const checkQRCodeStatus = async (sceneStr: string) => {
    try {
      // 1.使用timeout确保请求不会无限等待
      const response = await axios.get(`${apiBaseUrl}/weixin-scan/check-status/${sceneStr}`, {
        timeout: 5000 // 5秒超时
      });

      // console.log('扫码状态检查结果:', response.data);

      if (response.data && response.data.code === 200) {
        const data = response.data;
        console.log("zww:检测二维码状态的响应", response);
        // 处理两种可能的返回格式
        // 情况1: 已登录成功，返回标准登录格式（msg + data格式）
        if (data.msg === '登录成功' && data.data) {
          console.log("zww：检测到微信扫码登录成功！数据为", data);

          stopPollingQRCodeStatus();
          // 2.提取响应数据
          const tokenData = data.data;
          // console.log('扫码登录成功，获得tokenData666', tokenData);
          // 3.构建基本用户数据
          const userData: any = {
            userId: tokenData.userInfo?.id || 0,
            nickName: tokenData.userInfo?.nickName || '',
            avatarUrl: tokenData.userInfo?.avatarUrl || '',
            phone: tokenData.userInfo?.phone || '',
            isLoggedIn: true,
            roleId: tokenData.userInfo?.roleId || 0,
            registerType: 'weixin',
            sessionId: tokenData.sessionId || ''
          };
          // 4.使用统一的函数保存用户数据到本地存储和redux
          saveUserDataToLocalStorage(userData, tokenData.token, tokenData.refreshToken);
          dispatch(setUser(userData));
          // 5.检查是否未绑定手机号和身份
          if (!userData.phone) {
            console.log("zww微信登录检测到需要绑定手机号");
            try {
              // 1.显示手机号绑定弹窗，promise堵塞等待绑定完成,同时那边的回调会关闭弹框
              await new Promise<void>((resolve, reject) => {
                authFlowPromiseRef.current = { resolve, reject };
                // 2.这一步设置userData到CurrentUserData，后续打开绑定手机号会传递userData里的registerType为weixin
                // 执行对应的显示和接口调用
                console.log("zww：微信登录设置了userData的注册类型registerType为weixin方便后续绑定手机号的逻辑变化，其userData为", userData);
                setCurrentUserData(userData);
                setShowBindPhoneModal(true);
              });
              console.log("zww微信登录绑定手机号流程结束，接下来的userData为，开始检查新账号的roleId,这里的新账号可以是当前，也可以是迁移的目标账号", userData);
              // 3.此时更新新userData的数据信息，同步绑定到目标账号的roleId执行身份认证
              const updatedUserData = JSON.parse(localStorage.getItem('user') || '{}');
              // 4.绑定到新账号上或者当前账号检查roleId
              if (updatedUserData.roleId === 999) {
                // 显示身份绑定弹窗，promise堵塞等待绑定完成,同时那边的回调会关闭弹框
                await new Promise<void>((resolve, reject) => {
                  authFlowPromiseRef.current = { resolve, reject };
                  setCurrentUserData(updatedUserData);
                  setShowRoleSelectModal(true);
                });
                setLoading(false);
              }
              // 5，微信绑定到目标账号的判断流程结束！
              console.log("zww：微信绑定到目标账号的判断流程结束！");
              redirectToTargetPage()
              setLoading(false);
              return

            } catch (error) {
              console.error('身份验证流程出错:', error);
              message.error('登录过程中出现错误，请重试');
            }
            setLoading(false);
          }

          // 6. 这一步是已绑定了手机号的直接检查roleId是否为999，表示未认证用户
          if (userData.roleId === 999) {
            console.log("微信登录检测到未认证用户，处理身份选择");
            // 显示身份绑定弹窗，promise堵塞等待绑定完成,同时那边的回调会关闭弹框
            await new Promise<void>((resolve, reject) => {
              authFlowPromiseRef.current = { resolve, reject };
              setCurrentUserData(userData);
              setShowRoleSelectModal(true);
            });
            setLoading(false);
          }

       
          // 流程结束，回到重定向页面
          redirectToTargetPage()
          setLoading(false);
          return;
        }

        // 情况2: 标准状态检查返回
        const newStatus = data.data;

        // 只有状态变化时才更新状态，避免不必要的渲染
        if (qrCodeStatus.status !== newStatus.status) {
          setQrCodeStatus(newStatus);
        }

        // 如果扫码阶段转为已扫码，增加检查频率
        if (newStatus.status === 'scanned' && qrCodeStatus.status !== 'scanned') {
          stopPollingQRCodeStatus();
          startPollingQRCodeStatus(sceneStr, newStatus.expire_time || '', 1000); // 更快的轮询
        }

        // 如果登录成功，处理用户登录
        if (newStatus.status === 'success' && newStatus.token) {
          stopPollingQRCodeStatus();

          // 存储token临时
          localStorage.setItem('temp_token', newStatus.token);

          // 尝试获取更多用户信息
          try {
            // 使用微信扫码专用接口获取用户信息
            const userResponse = await axios.get(`${apiBaseUrl}/weixin-scan/user-info`, {
              headers: { Authorization: `Bearer ${newStatus.token}` }
            });

            // 检查是否返回了有效数据
            if (userResponse.data.code === 200) {
              // 获取用户信息，进行正常登录流程
              const userInfo = userResponse.data.data.userInfo;
              if (userInfo && userInfo.id) {
                localStorage.setItem('token', newStatus.token);
                localStorage.setItem('userId', userInfo.id.toString());

                // 构建用户数据
                const userData = {
                  userId: userInfo.id,
                  nickName: userInfo.nickName,
                  avatarUrl: userInfo.avatarUrl,
                  gender: userInfo.gender,
                  phone: userInfo.phone,
                  isLoggedIn: true,
                  roleId: userInfo.roleId || 0, // 添加默认值0
                  registerType: userInfo.register_type || '',
                  sessionId: userResponse.data.data.sessionId || ''
                };

                dispatch(setUser(userData));
                localStorage.setItem('user', JSON.stringify(userData));

                if (userData.roleId === 999) {
                  handleLoginSuccess(userData);
                  return;
                }

                // 检查是否需要设置密码
                if (userResponse.data.data.isNewUser) {
                  // 创建用户数据的副本，并添加设置密码标记
                  const newUserData: any = {
                    ...userData,
                    needSetPwd: true // 标记需要设置密码
                  };

                  // 处理登录成功
                  handleLoginSuccess(newUserData);
                  return;
                }

                // 处理普通登录成功的情况
                console.log("微信扫码登录成功，处理登录流程", userData);
                handleLoginSuccess(userData);
                return;
              }
            }
          } catch (error) {
            console.error('微信登录获取用户信息失败:', error);
            // 错误处理：处理登录成功
            if (newStatus.token) {
              localStorage.setItem('token', newStatus.token);

              // 处理登录成功
              handleLoginSuccess({
                userId: Number(localStorage.getItem('userId') || '0'),
                isLoggedIn: true,
                token: newStatus.token
              });
            }
          }
        }

        if (newStatus.status === 'expired' || newStatus.status === 'cancelled') {
          setQrCodeExpired(true);
          stopPollingQRCodeStatus();
        }
      }
    } catch (err) {
      console.error('检查二维码状态失败:', err);
    }
  };

  const startPollingQRCodeStatus = (sceneStr: string, expireTime: string, interval = 2000) => {
    stopPollingQRCodeStatus();

    // 立即检查一次
    checkQRCodeStatus(sceneStr);

    // 设置轮询
    pollIntervalRef.current = setInterval(() => checkQRCodeStatus(sceneStr), interval);

    // 设置过期检查
    try {
      const expireDate = new Date(expireTime);
      countdownIntervalRef.current = setInterval(() => {
        if (new Date() >= expireDate) {
          setQrCodeExpired(true);
          setQrCodeStatus({ status: 'expired', message: '二维码已过期，请刷新' });
          stopPollingQRCodeStatus();
        }
      }, 1000);

      // 额外的超时保护，120秒后如果还在等待，自动设为过期
      setTimeout(() => {
        if (qrCodeStatus.status === 'waiting' && !qrCodeExpired) {
          console.log('二维码超时，自动设置为过期');
          setQrCodeExpired(true);
          setQrCodeStatus({ status: 'expired', message: '二维码已过期，请刷新' });
          stopPollingQRCodeStatus();
        }
      }, 120000);
    } catch (err) {
      console.error('解析过期时间失败:', err);
    }
  };

  const fetchWeixinQRCode = async () => {
    setQrCodeLoading(true);
    setQrCodeExpired(false);
    setQrCodeStatus({ status: 'waiting', message: '等待扫码' });
    stopPollingQRCodeStatus();

    try {
      const response = await axios.get(`${apiBaseUrl}/weixin-scan/qrcode`, {
        params: {
          redirect_url: window.location.href
        },
        timeout: 10000 // 10秒超时
      });

      if (response.data && response.data.code === 200 && response.data.data) {
        const { qrcode_url, scene_str, expire_time } = response.data.data;
        setQrCodeUrl(qrcode_url);
        startPollingQRCodeStatus(scene_str, expire_time);
      } else {
        throw new Error('获取二维码失败');
      }
    } catch (error) {
      console.error('获取微信登录二维码失败:', error);
      setQrCodeStatus({ status: 'error', message: '获取二维码失败，请刷新重试' });
      message.error('获取微信登录二维码失败，请刷新重试');
    } finally {
      setQrCodeLoading(false);
    }
  };

  const refreshQRCode = () => {
    console.log('手动刷新二维码');
    fetchWeixinQRCode();
  };

  // 初始化
  useEffect(() => {
    console.log('=== LoginForm useEffect 开始执行 ===');
    // 检查是否需要显示手机号绑定弹窗
    const bindPhone = searchParams.get('bindPhone');
    const needBindPhone = searchParams.get('needBindPhone');
    const isBindPhoneMode = bindPhone === 'true' || needBindPhone === 'true';

    console.log('URL参数检查:', { bindPhone, needBindPhone, isBindPhoneMode });

    if (isBindPhoneMode) {
      console.log('🚀 检测到绑定手机号模式，跳过微信二维码和省市区数据加载');
      // 直接设置为滑出状态，不显示登录表单
      setIsSlideOut(true);
      // 延迟显示绑定弹窗，确保组件已完全加载
      setTimeout(() => {
        console.log('🚀 显示绑定手机号弹窗');
        setShowBindPhoneModal(true);
      }, 100);
      return; // 跳过其他初始化操作
    }

    // 只有在非绑定手机号模式下才执行这些初始化操作
    fetchWeixinQRCode();

    // 加载省市区数据
    const loadPcaData = async () => {
      try {
        const data = await getPcaData();
        if (Array.isArray(data) && data.length > 0) {
          console.log('PCA数据加载成功:', data.length);
          setPcaData(data);
        } else {
          console.error('PCA数据返回格式异常:', data);
          message.error('地区数据加载失败');
        }
      } catch (error) {
        console.error('获取省市区数据失败:', error);
        message.error('地区数据加载失败，请刷新页面重试');
      }
    };
    loadPcaData();

    // 清理函数
    return () => {
      stopPollingQRCodeStatus();
    };
  }, []);

  // 添加学校变更监听
  useEffect(() => {
    // 清空学校选择当省市区变更时

    if (studentProvince || studentCity || studentDistrict) {
      // setStudentSchool(undefined);
      setSchools([]);
    }
  }, [studentProvince, studentCity, studentDistrict]);

  // --- Phone/Password Logic ---
  const handleSendCode = async () => {
    if (!phone) {
      setError('请输入手机号');
      message.error('请输入手机号');
      return;
    }
    if (countdown > 0) return;

    // const res = await auth.sendLoginCode(phone);
    try {
      const { data: response } = await userApi.sendVerifyCode(phone);
      if (response?.data && response?.data.code === 200) {
        // 即使 code 为 200，也要检查 success 字段
        if (response.data.success === false) {
          // success 明确为 false 时表示业务失败
          message.error(response.data.message || response.data.msg || '发送验证码失败');
          setError(response.data.message || response.data.msg || '发送验证码失败');
        } else {
          // code 为 200 且 success 不为 false 时才是成功
          message.success('验证码已发送');
          setCountdown(60); // 只设置倒计时初始值，useEffect会处理定时器
        }
      } else if (response?.data && response?.data.success === true) {
        // success 明确为 true 时也是成功
        message.success('验证码已发送');
        setCountdown(60); // 只设置倒计时初始值，useEffect会处理定时器
      } else {
        message.error(response?.data?.message || response?.data?.msg || '发送验证码失败');
        setError(response?.data?.message || response?.data?.msg || '发送验证码失败');
      }
    } catch (error: any) {
      console.error('发送验证码失败:', error);
      const errorMsg = error.response?.data?.message || error.response?.data?.msg || '发送验证码失败，请稍后再试';
      message.error(errorMsg);
      setError(errorMsg);
    } finally {
      setLoading(false);
    }

  };

  useEffect(() => {
    if (countdown > 0) {
      verifyCodeCountdownRef.current = setTimeout(() => setCountdown(countdown - 1), 1000);
    }
    return () => { if (verifyCodeCountdownRef.current) clearTimeout(verifyCodeCountdownRef.current); }
  }, [countdown]);

  // --- Student Login Logic ---
  const isMunicipality = (provinceName?: string) => ['北京市', '上海市', '天津市', '重庆市'].includes(provinceName || '');

  useEffect(() => {
    const selectedProvince = pcaData.find(p => p.name === studentProvince);
    if (selectedProvince?.children) {
      if (isMunicipality(studentProvince)) {
        setCities([]);
        setDistricts(selectedProvince.children[0].children || []);
      } else {
        setCities(selectedProvince.children);
        const selectedCity = selectedProvince.children.find((c: any) => c.name === studentCity);
        setDistricts(selectedCity?.children || []);
      }
    } else {
      setCities([]);
      setDistricts([]);
    }
  }, [studentProvince, studentCity, pcaData]);

  const handleSchoolSearch = async (searchText: string) => {
    setIsSchoolLoading(true);

    try {
      // 构建搜索参数
      const params: any = {};

      if (searchText) {
        params.keyword = searchText;
        // 有搜索文本时可以不限制地区
      } else {
        // 无搜索文本时，判断是否有地区筛选
        if (studentProvince) {
          params.province = studentProvince;
          if (!isMunicipality(studentProvince) && studentCity) {
            params.city = studentCity;
          }
          if (studentDistrict) {
            params.district = studentDistrict;
          }
        } else {
          // 如果没有选择省份且没有搜索文本，则不进行搜索
          setSchools([]);
          setIsSchoolLoading(false);
          return;
        }
      }

      console.log('搜索学校参数:', params);
      const response = await schoolApi.getList(params);

      if (response.data?.code === 200) {
        // 获取学校列表
        let schools = response.data?.data || response.data?.list || response.data || [];

        // 处理可能的null字符串
        schools = schools.map((school: any) => ({
          ...school,
          province: school.province && school.province !== 'null' ? school.province : '',
          city: school.city && school.city !== 'null' ? school.city : '',
          district: school.district && school.district !== 'null' ? school.district : ''
        }));

        console.log('获取到学校列表:', schools.length, '所学校');
        setSchools(schools);
      } else {
        console.warn('搜索学校接口返回错误:', response);
        setSchools([]);
      }
    } catch (error) {
      console.error('获取学校列表失败:', error);
      message.error('获取学校列表失败，请稍后重试');
      setSchools([]);
    } finally {
      setIsSchoolLoading(false);
    }
  };

  // --- Main Form Submit Logic ---
  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setError('');
    setLoading(true);
    setValidationErrors({});

    const newErrors: Record<string, boolean> = {};
    let formIsValid = true;

    // 1.表单的参数校验，用于构建err了哪些字段，从而边框标红
    if (loginMode === 'verify') {
      console.log("zww：正在进行表单校验");
      
      if (!phone) {
        newErrors.phone = true;
        formIsValid = false;
      }
      if (!verifyCode) {
        newErrors.verifyCode = true;
        formIsValid = false;
      }
    } else if (loginMode === 'password') {
      if (!phone) {
        newErrors.phone = true;
        formIsValid = false;
      }
      if (!password) {
        newErrors.password = true;
        formIsValid = false;
      }
    } else if (loginMode === 'student') {
      if (!studentProvince) newErrors.studentProvince = true;
      if (!isMunicipality(studentProvince) && !studentCity) newErrors.studentCity = true;
      if (!studentDistrict) newErrors.studentDistrict = true;
      if (!studentSchool) newErrors.studentSchool = true;
      if (!studentNumber) newErrors.studentNumber = true;
      if (!studentPassword) newErrors.studentPassword = true;
      if (Object.keys(newErrors).length > 0) {
        formIsValid = false;
      }
    }
    if (!formIsValid) {
      setValidationErrors(newErrors);
      message.error('请完整填写所有必填信息');
      setLoading(false);
      return;
    }

    //  2.逻辑处理
    try {
      let res: any;
      if (loginMode === 'verify') {
        // 重置强迫设置密码状态值，健壮性增强
        setForcePasswordSetup(false);

        // 1.验证码登录,接口调用
        res = await auth.verifyCodeLogin(phone, verifyCode);
        if (res.code === 200) {
          // 提取数据，使用any类型避免类型错误
          const data: any = res.data || {};
          const userInfo: any = data.userInfo || {};
          const token = data.token;
          const refreshToken = data.refreshToken;

          // 构建用户数据
          const userData: any = {
            userId: userInfo.id || 0,
            nickName: userInfo.nickName || '',
            avatarUrl: userInfo.avatarUrl || '',
            gender: userInfo.gender || 0,
            phone: userInfo.phone || '',
            isLoggedIn: true,
            roleId: userInfo.roleId || 0,
            registerType: userInfo.register_type || '',
            sessionId: data.sessionId || '',
            needSetPwd: data.needSetPwd
          };

          // 使用统一的函数保存用户数据
          saveUserDataToLocalStorage(userData, token, refreshToken);
          dispatch(setUser(userData));
          // 1. 首先检查是否有多个账号
          if (data.roleList && data.roleList.length > 0) {
            console.log("验证码登录检测到多账号:", data.roleList);
            userData.roleList = data.roleList;
            try {
              await new Promise<void>((resolve, reject) => {
                authFlowPromiseRef.current = { resolve, reject };
                setCurrentUserData(userData);
                setShowChooseAccountModal(true);
              });
            } catch (error) {
              console.error('账号选择流程出错:', error);
              message.error('账号选择过程中出现错误，请重试');
            }
            setLoading(false);
          }
          // 2.此时如果选择账号更新了新数据则重新获取本地数据，有可能此时选择了新的账号数据
          const updatedUserData = JSON.parse(localStorage.getItem('user') || '{}');
          // 3. 然后检查roleId是否为999（未认证用户）
          if (updatedUserData.roleId === 999) {
            // 显示身份绑定弹窗，promise堵塞等待绑定完成,同时那边的回调会关闭弹框
            await new Promise<void>((resolve, reject) => {
              authFlowPromiseRef.current = { resolve, reject };
              setCurrentUserData(updatedUserData);
              setShowRoleSelectModal(true);
            });
            setLoading(false);
          }

          
          // 流程结束，回到重定向页面
          redirectToTargetPage()
          setLoading(false);
          return;
        } else {
          throw new Error(res.message || '验证码登录失败');
        }
      } else if (loginMode === 'password') {
        // 1.密码登录接口调用
        res = await auth.login(phone, password);

        if (res.code === 200) {
          // 2.提取数据，使用any类型避免类型错误
          const data = res.data
          const token = res.data.token;
          const refreshToken = res.data.refreshToken;
          const userInfo: any = res.data.userInfo || {};

          // 构建用户数据
          const userData: any = {
            userId: userInfo.id || 0,
            nickName: userInfo.nickName || '',
            avatarUrl: userInfo.avatarUrl || '',
            gender: userInfo.gender || 0,
            phone: userInfo.phone || '',
            isLoggedIn: true,
            roleId: userInfo.roleId || 0,
            registerType: userInfo.register_type || '',
            //如果是多用户，此时会返回一个会话ID，
            // 后续请求非守卫拦截的接口进行账号选择
            sessionId: data.sessionId || ''
          };

          // 使用统一的函数保存用户数据
          saveUserDataToLocalStorage(userData, token, refreshToken);
          dispatch(setUser(userData));
          // 1. 首先检查是否有多个账号
          if (data.roleList && data.roleList.length > 0) {
            console.log("密码登录检测到多账号:", data.roleList);
            userData.roleList = data.roleList;
            // 等待账号选择
            await new Promise<void>((resolve, reject) => {
              authFlowPromiseRef.current = { resolve, reject };
              setCurrentUserData(userData);
              setShowChooseAccountModal(true);
            });
          }
          // 2.此时如果选择账号更新了新数据则重新获取本地数据，有可能此时选择了新的账号数据
          const updatedUserData = JSON.parse(localStorage.getItem('user') || '{}');
          console.log("新的账号数据为", updatedUserData);

          // 3. 无多个账号或者选择好账号然后检查roleId是否为999（未认证用户）
          if (updatedUserData.roleId === 999) {
            console.log("zww：密码登录检测到未认证用户，处理身份选择");
            // 显示身份绑定弹窗，promise堵塞等待绑定完成,同时那边的回调会关闭弹框
            await new Promise<void>((resolve, reject) => {
              authFlowPromiseRef.current = { resolve, reject };
              setCurrentUserData(updatedUserData);
              setShowRoleSelectModal(true);
            });
            console.log("zww：身份检测完毕！");

            setLoading(false);
          }

          redirectToTargetPage()
          setLoading(false);
          return;
        } else {
          throw new Error(res.message || '密码登录失败');
        }
      } else if (loginMode === 'student') {
        // 1.参数校验，拦截表单没填写的信息并且设置Error值返回

        // 判断如果没有省份信息，且不是直辖市的情况下没有市的信息，没有区的信息，学校，学号，密码则报错
        if (!studentProvince || (!isMunicipality(studentProvince) && !studentCity) || !studentDistrict || !studentSchool || !studentNumber || !studentPassword) {
          setError('请完整填写所有学生登录信息');
          setLoading(false);
          return;
        }

        // 2.从选中的对象中构建请求参数
        const loginParams = {
          province: selectedSchool?.province || studentProvince,
          city: selectedSchool?.city || (isMunicipality(studentProvince) ? studentProvince : studentCity || ""), // 确保city不为undefined
          district: selectedSchool?.district || studentDistrict,
          schoolName: selectedSchool?.schoolName || studentSchool,
          schoolId: selectedSchool?.id, // 添加学校ID
          studentNumber,
          password: studentPassword
        };

        console.log('学生登录参数:', loginParams);
        // 3.发起请求
        res = await auth.studentLogin(loginParams);
        console.log('学生登录响应值:', res);

        if (res.code === 200) {
          // 4.提取数据，使用any类型避免类型错误
          const token = res.data.token;
          const refreshToken = res.data.refreshToken;
          const userInfo: any = res.data.userInfo || {};
          // 5.构建本地存储并存储
          const userData: any = {
            userId: userInfo.id || 0,
            nickName: userInfo.nickName || '',
            avatarUrl: userInfo.avatarUrl || '',
            gender: userInfo.gender || 0,
            phone: userInfo.phone || '',
            isLoggedIn: true,
            roleId: userInfo.roleId || 0,
            registerType: userInfo.register_type || '',
            // 添加学生特有信息
            studentNumber: userInfo.studentNumber,
            schoolInfo: userInfo.schoolInfo
          };
          console.log("构造后的userData", userData)
          // 使用统一的函数保存用户数据到本地存储和redux
          saveUserDataToLocalStorage(userData, token, refreshToken);
          dispatch(setUser(userData));
          // 6. 检查是否需要绑定手机号
          if (!userData.phone.trim()) {
            console.log("学生登录检测到需要绑定手机号");
            try {
              // 7.显示手机号绑定弹窗，promise堵塞等待绑定完成,同时那边的回调会关闭弹框
              await new Promise<void>((resolve, reject) => {
                authFlowPromiseRef.current = { resolve, reject };
                setCurrentUserData(userData);
                setShowBindPhoneModal(true);
              });

            } catch (error) {
              console.error('身份验证流程出错:', error);
              message.error('登录过程中出现错误，请重试');
            }
            setLoading(false);
          }
          // 7.用户绑定手机号流程结束，回到重定向回去的页面
          redirectToTargetPage()
          setLoading(false);
          return;
        } else {
          throw new Error(res.message || '学生登录失败');
        }
      }

      if (res && res.code === 200) {
        try {
          // 提取用户数据
          const userData = {
            userId: res.data?.userInfo?.id || 0,
            nickName: res.data?.userInfo?.nickName || '',
            avatarUrl: res.data?.userInfo?.avatarUrl || '',
            gender: res.data?.userInfo?.gender || 0,
            phone: res.data?.userInfo?.phone || '',
            isLoggedIn: true,
            roleId: res.data?.userInfo?.roleId || 0,
            registerType: (res.data?.userInfo as any)?.register_type || '',
            needSetPwd: res.data?.isNewUser || false,
            sessionId: res.data?.sessionId || '',
            roleList: res.data?.roleList || []
          };

          // 保存用户信息到Redux和localStorage
          dispatch(setUser(userData));
          localStorage.setItem('user', JSON.stringify(userData));
          if (res.data?.token) {
            localStorage.setItem('token', res.data.token);
          }

          if (userData.roleId === 999 && (!userData.roleList || userData.roleList.length === 0)) {
            handleLoginSuccess(userData);
            setLoading(false);
            return;
          }

          handleLoginSuccess(userData);
          return; // 直接返回，不再执行后续的重定向
        } catch (error) {
          console.error('处理登录成功数据失败:', error);
          message.error('登录处理出错，请重试');
        }
      } else {
        setError(res?.message || '登录失败，请检查您的凭证。');
      }

    } catch (err: any) {
      setError(err.message || '发生未知错误，请稍后再试。');
    } finally {
      setLoading(false);
    }
  };

  const handleOpenPolicy = (type: 'terms' | 'privacy') => {
    if (type === 'terms') {
      setPolicyModalContent({
        title: '平台服务协议',
        content: (
          <div>
            <h4 className="font-bold">平台服务协议</h4>
            <p>这里是平台服务协议的内容...</p>
          </div>
        )
      });
    } else {
      setPolicyModalContent({
        title: '个人信息保护政策',
        content: (
          <div>
            <h4 className="font-bold">个人信息保护政策</h4>
            <p>这里是个人信息保护政策的内容...</p>
          </div>
        )
      });
    }
  };

  const handleClosePolicy = () => {
    setPolicyModalContent(null);
  };

  // 修改handleLoginSuccess函数，增强角色设置的健壮性
  const handleLoginSuccess = async (userData: any) => {
    console.log("开始登录成功流程，用户数据:", userData);

    // 确保一开始就设置isSlideOut为true，防止登录表单闪现
    setIsSlideOut(true);

    try {
      // 使用统一的函数保存用户数据
      saveUserDataToLocalStorage(userData);

      // 保存当前用户数据以便在弹窗中使用
      setCurrentUserData(userData);

      // 开始身份验证流程
      // 1. 首先检查是否需要设置密码
      if (userData.needSetPwd) {
        console.log("检测到需要设置密码");
        await new Promise<void>((resolve, reject) => {
          authFlowPromiseRef.current = { resolve, reject };
          setCurrentUserData(userData);
          setShowPasswordModal(true);
        });
        // 密码设置成功后，关闭弹窗
        setShowPasswordModal(false);
      }

      // 2. 然后检查是否需要绑定手机号
      if (!userData.phone || userData.needBindPhone) {
        console.log("检测到需要绑定手机号");
        await new Promise<void>((resolve, reject) => {
          authFlowPromiseRef.current = { resolve, reject };
          setShowBindPhoneModal(true);
        });
        // 手机号绑定成功后，关闭弹窗
        setShowBindPhoneModal(false);
      }

      // 重新获取最新的用户数据，因为可能在前面的步骤中已经更新
      const updatedUserData = JSON.parse(localStorage.getItem('user') || '{}');
      console.log("更新后的用户数据:", updatedUserData);

      // 3. 检查是否是未认证用户(roleId === 999)
      if (updatedUserData.roleId === 999) {
        console.log("检测到未认证用户(roleId === 999)，显示角色选择弹窗");
        await new Promise<void>((resolve, reject) => {
          authFlowPromiseRef.current = { resolve, reject };
          setShowRoleSelectModal(true);
        });
        // 角色选择成功后，关闭弹窗
        setShowRoleSelectModal(false);
      }

      // 所有验证都通过后，设置flowOver为true，然后进行跳转
      setFlowOver(true);

      // 延迟跳转，确保状态已更新
      setTimeout(() => {
        const redirectUrl = searchParams.get('redirect');
        if (redirectUrl) {
          router.push(redirectUrl);
        } else {
          router.push('/home');
        }
      }, 100);
    } catch (error) {
      console.error('身份验证流程出错:', error);
      // 处理错误情况，可能是用户取消了某个步骤
      message.error('登录过程中出现错误，请重试');
      setLoading(false);

      // 错误情况下，重置isSlideOut状态
      setTimeout(() => {
        setIsSlideOut(false);
      }, 300);
    }
  };

  // 处理密码设置成功
  const handlePasswordSetSuccess = () => {
    // 先设置isSlideOut为true，防止登录表单闪现
    setIsSlideOut(true);

    // 延迟关闭弹窗，确保状态过渡平滑
    setTimeout(() => {
      setShowPasswordModal(false);
      // 重置强制设置密码状态
      setForcePasswordSetup(false);
      if (authFlowPromiseRef.current) {
        authFlowPromiseRef.current.resolve();
        authFlowPromiseRef.current = null;
      }
    }, 50);
  };

  // 处理手机号绑定成功
  const handleBindPhoneSuccess = () => {
    // 先设置isSlideOut为true，防止登录表单闪现
    setIsSlideOut(true);

    // 延迟关闭弹窗，确保状态过渡平滑
    setTimeout(() => {
      // 检查是否是从活动报名页面跳转过来的
      const bindPhone = searchParams.get('bindPhone');
      const needBindPhone = searchParams.get('needBindPhone');
      if (bindPhone === 'true' || needBindPhone === 'true') {
        // 如果是从活动报名跳转过来的，直接跳转回原页面
        setFlowOver(true);
        setShowBindPhoneModal(false);

        const redirectUrl = searchParams.get('redirect');
        if (redirectUrl) {
          router.push(redirectUrl);
        } else {
          router.push('/home');
        }
        return;
      }

      if (authFlowPromiseRef.current) {
        authFlowPromiseRef.current.resolve();
        authFlowPromiseRef.current = null;
      }
    }, 50);
  };

  // 处理角色选择成功
  const handleRoleSelectSuccess = () => {
    // 先设置isSlideOut为true，防止登录表单闪现
    setIsSlideOut(true);

    // 延迟关闭弹窗，确保状态过渡平滑
    setTimeout(() => {
      if (authFlowPromiseRef.current) {
        authFlowPromiseRef.current.resolve();
        authFlowPromiseRef.current = null;
      }
    }, 50);
  };

  // --- Render Logic ---
  const renderQRCodeContent = () => {
    if (qrCodeLoading) {
      return <div className="w-44 h-44 flex items-center justify-center"><Spin /></div>;
    }

    if (qrCodeExpired) {
      return (
        <div className="relative w-44 h-44 flex flex-col items-center justify-center bg-black bg-opacity-80 rounded-lg">
          <p className="text-white mb-2">二维码已失效</p>
          <button
            onClick={refreshQRCode}
            className="mt-2 px-3 py-1 bg-blue-600 rounded text-sm hover:bg-blue-700 text-white"
          >
            刷新
          </button>
        </div>
      );
    }

    if (qrCodeStatus.status === 'scanned') {
      return (
        <div className="relative w-44 h-44">
          <Image src={qrCodeUrl} alt="微信扫码登录" width={176} height={176} />
          <div className="absolute inset-0 bg-black bg-opacity-70 flex flex-col items-center justify-center rounded-lg">
            <p className="font-semibold text-white mb-1">扫码成功</p>
            <p className="text-sm text-white">请在微信确认登录</p>
          </div>
        </div>
      );
    }

    return qrCodeUrl ? (
      <Image
        src={qrCodeUrl}
        alt="微信扫码登录"
        width={176}
        height={176}
        className="rounded-lg cursor-pointer"
        onClick={refreshQRCode}
        title="点击刷新二维码"
      />
    ) : (
      <div className="w-44 h-44 flex items-center justify-center">
        <Spin />
      </div>
    );
  };

  const TabButton = ({ mode, label }: { mode: 'verify' | 'password' | 'student', label: string }) => (
    <button
      type="button"
      onClick={() => setLoginMode(mode)}
      className={`px-4 py-2 text-sm font-medium transition-colors duration-200 relative ${loginMode === mode ? COLORS.text.primary : `${COLORS.text.secondary} hover:${COLORS.text.primary}`}`}
    >
      {label}
      {loginMode === mode && <span className={`absolute bottom-[-1px] left-1/2 -translate-x-1/2 w-3/4 h-0.5 ${COLORS.bg.accent} rounded-full`}></span>}
    </button>
  );

  // 流程阀门：解锁用户流程，在手机号绑定成功后调用
  // 作用：解除流程阻塞，允许继续下一步操作
  const unlockUserFlow = (msg: string = "") => {
    // 1.重置强制设置密码状态
    console.log("解锁流程,回调的信息为:", msg);

    if (authFlowPromiseRef.current) {
      authFlowPromiseRef.current.resolve();
      authFlowPromiseRef.current = null;
    }
  };

  const handleAuthSuccess = () => {
    console.log("zww：认证流程结束！放行阀门！");

    // 先设置isSlideOut为true，防止登录表单闪现
    setIsSlideOut(true);

    // 延迟关闭弹窗，确保状态过渡平滑
    setTimeout(() => {
      setShowAuthModal(false);
      // 认证成功后，继续登录流程
      if (authFlowPromiseRef.current) {
        authFlowPromiseRef.current.resolve();
        authFlowPromiseRef.current = null;
      }
    }, 50);
  };

  const handleAuthBack = () => {
    // 先设置isSlideOut为true，防止登录表单闪现
    setIsSlideOut(true);

    // 延迟关闭弹窗，确保状态过渡平滑
    setTimeout(() => {
      setShowAuthModal(false);
      // 用户返回，显示角色选择
      setShowRoleSelectModal(true);
    }, 50);
  };

  // 刷新页面，重新渲染，此时清除所有存储，重新进行验证
  useEffect(() => {
    // 检查是否是从活动报名页面跳转过来绑定手机号
    const bindPhone = searchParams.get('bindPhone');
    const needBindPhone = searchParams.get('needBindPhone');
    if (bindPhone === 'true' || needBindPhone === 'true') {
      console.log('检测到从活动报名页面跳转过来绑定手机号，保留用户登录状态');
      return; // 不清除登录信息
    }

    // 清除登录信息
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    localStorage.removeItem('userId');

    // 使用clearUser代替setUser(null)
    dispatch(clearUser());

    console.log('已清除登录信息');
  }, []); // 空依赖数组确保只在组件挂载时执行一次

  // 监听弹框状态，控制登录表单的滑出动画
  useEffect(() => {
    if (flowOver) {
      return;
    }

    const anyModalOpen = showPasswordModal || showBindPhoneModal || showRoleSelectModal || showChooseAccountModal || showAuthModal;

    if (anyModalOpen) {
      setIsSlideOut(true);
    } else {
      // 当所有弹框关闭时，添加延迟再设置isSlideOut为false
      // 这样可以避免在弹框状态切换时中间的一瞬间所有弹窗show都是false的登录表单的闪烁问题
      const timerID = setTimeout(() => {
        // 如果flowOver已经设置为true，则不要改变isSlideOut状态
        if (!flowOver) {
          setIsSlideOut(false);
        }
      }, 300); // 300ms延迟确保状态切换平滑
      // 返回一个清除函数，下一次的useEffect触发时调用
      return () => clearTimeout(timerID);
    }
  }, [showPasswordModal, showBindPhoneModal, showRoleSelectModal, showChooseAccountModal, showAuthModal, flowOver]);

  return (
    <>
      <PolicyModal
        isOpen={!!policyModalContent}
        onClose={handleClosePolicy}
        title={policyModalContent?.title || ''}
      >
        {policyModalContent?.content}
      </PolicyModal>






      {/* 只有在不需要直接显示绑定手机号时才显示登录表单 */}
      {!shouldShowBindPhoneDirectly() && (
        <div className={`w-full max-w-4xl bg-white/10 backdrop-blur-md ${COLORS.border.primary} rounded-xl overflow-hidden shadow-2xl z-20 flex transition-transform duration-300 ${isSlideOut ? 'slide-out-left' : ''}`}>
          {/* Left Column: QR Code */}
          <div className={`hidden md:flex flex-col items-center justify-center w-2/5 p-8 border-r ${COLORS.border.primary}`}>
            <Image src="/images/logic_leap.png" alt="洛基飞跃 Logo" width={56} height={56} />
            <div className="mt-6">
              {renderQRCodeContent()}
            </div>
            <p className={`mt-4 text-sm ${COLORS.text.primary}`}>请使用微信扫描二维码登录</p>
            <button onClick={refreshQRCode} className={`mt-2 text-xs ${COLORS.text.accent} hover:underline`}>
              刷新二维码
            </button>
          </div>

          {/* Right Column: Forms */}
          <div className={`w-full md:w-3/5 p-8 flex flex-col justify-center ${COLORS.bg.primary}`}>
          <div className="text-center mb-4">
            <h2 className={`text-2xl font-bold ${COLORS.text.primary}`}>欢迎回来</h2>
            <p className={`mt-2 text-sm ${COLORS.text.primary}`}>登录以继续您的AI学习之旅</p>
          </div>

          <div className={`mb-4 flex justify-center border-b ${COLORS.border.primary}`}>
            <TabButton mode="verify" label="验证码登录" />
            <TabButton mode="password" label="密码登录" />
            <TabButton mode="student" label="学生登录" />
          </div>

          <form className="space-y-6" onSubmit={handleSubmit}>
            <div>
              {loginMode === 'verify' && (
                <VerifyCodeLogin
                  phone={phone}
                  setPhone={setPhone}
                  verifyCode={verifyCode}
                  setVerifyCode={setVerifyCode}
                  handleSendCode={handleSendCode}
                  countdown={countdown}
                  validationErrors={validationErrors}
                  setValidationErrors={setValidationErrors}
                />
              )}

              {loginMode === 'password' && (
                <PasswordLogin
                  phone={phone}
                  setPhone={setPhone}
                  password={password}
                  setPassword={setPassword}
                  showPassword={showPassword}
                  setShowPassword={setShowPassword}
                  validationErrors={validationErrors}
                  setValidationErrors={setValidationErrors}
                />
              )}

              {loginMode === 'student' && (
                <StudentLogin
                  studentProvince={studentProvince} setStudentProvince={setStudentProvince}
                  studentCity={studentCity} setStudentCity={setStudentCity}
                  studentDistrict={studentDistrict} setStudentDistrict={setStudentDistrict}
                  studentSchool={studentSchool} setStudentSchool={setStudentSchool}
                  studentNumber={studentNumber} setStudentNumber={setStudentNumber}
                  studentPassword={studentPassword} setStudentPassword={setStudentPassword}
                  studentShowPassword={studentShowPassword} setStudentShowPassword={setStudentShowPassword}
                  setSelectedSchool={setSelectedSchool}
                  pcaData={pcaData} cities={cities} districts={districts} schools={schools}
                  isSchoolLoading={isSchoolLoading} handleSchoolSearch={handleSchoolSearch} isMunicipality={isMunicipality}
                  validationErrors={validationErrors}
                  setValidationErrors={setValidationErrors}
                  schoolSearchIsFocused={schoolSearchIsFocused}
                  setSchoolSearchIsFocused={setSchoolSearchIsFocused}
                  studentNumberFocused={studentNumberFocused}
                  setStudentNumberFocused={setStudentNumberFocused}
                  studentPasswordFocused={studentPasswordFocused}
                  setStudentPasswordFocused={setStudentPasswordFocused}
                />
              )}
            </div>

            <div className="pt-2">
              <button type="submit" disabled={loading || !agreedToTerms} className={loginButtonClass}>
                {loading ? '登录中...' : '登录'}
              </button>
            </div>

            <div className={`min-h-[60px] flex flex-col items-center justify-center text-center text-xs ${COLORS.text.secondary} py-2`}>
              <div className="space-y-2">
                <p className="min-h-4">
                  <>未注册洛基飞跃手机号, 通过验证码登录我们将自动帮您注册账号</>
                </p>
                <div className="flex items-center justify-center">
                  <input
                    type="checkbox"
                    id="terms"
                    checked={agreedToTerms}
                    onChange={(e) => setAgreedToTerms(e.target.checked)}
                    className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500 mr-2"
                  />
                  <label htmlFor="terms" className="text-xs">
                    我已阅读并同意
                    <a href="#" onClick={(e) => { e.preventDefault(); handleOpenPolicy('terms'); }} className={`font-medium ${COLORS.text.accent} hover:underline`}>《平台服务协议》</a>
                    和
                    <a href="#" onClick={(e) => { e.preventDefault(); handleOpenPolicy('privacy'); }} className={`font-medium ${COLORS.text.accent} hover:underline`}>《个人信息保护政策》</a>
                  </label>
                </div>
              </div>
            </div>
          </form>
        </div>

      </div>
      )}

      {/* 添加密码设置组件 */}
      <SlideContainer
        isOpen={showPasswordModal}
        onClose={() => setShowPasswordModal(false)}
        beforeClose={() => setIsSlideOut(false)}
      >
        <InlineSetPassword
          onSuccess={unlockUserFlow}
          allowSkip={!forcePasswordSetup} // 验证码登录不允许跳过
          phone={currentUserData?.phone || ''}
        />
      </SlideContainer>

      {/* 添加绑定手机号组件 - 使用滑动容器 */}
      <SlideContainer
        isOpen={showBindPhoneModal}
        onClose={() => setShowBindPhoneModal(false)}
        beforeClose={() => setIsSlideOut(false)}
      >
        <InlineBindPhone
          onSuccess={(phoneOrUserData) => {
            // 检查是否是从活动报名页面跳转过来的
            const bindPhone = searchParams.get('bindPhone');
            const needBindPhone = searchParams.get('needBindPhone');
            if (bindPhone === 'true' || needBindPhone === 'true') {
              // 如果是从活动报名跳转过来的，需要先更新用户数据再跳转

              // 1.判断是否是JSON字符串（微信绑定到已有账号的情况）
              try {
                // 2.尝试解析JSON
                const userData = JSON.parse(phoneOrUserData);
                console.log("zww：活动报名绑定-检测到微信绑定到已有账号返回的userData:", userData);
                // 3.更新当前用户数据为微信到的目标账号信息
                setCurrentUserData(userData);
                dispatch(setUser(userData));
                saveUserDataToLocalStorage(userData);
              } catch (e) {
                // 不是JSON，是普通手机号绑定的情况
                const phone = phoneOrUserData;
                if (currentUserData) {
                  const updatedUserData = { ...currentUserData, phone };
                  console.log("zww：活动报名绑定-更新用户手机号:", phone);
                  // zww：绑定成功后更新当前本地存储和redux里的值
                  setCurrentUserData(updatedUserData);
                  dispatch(setUser(updatedUserData));
                  saveUserDataToLocalStorage(updatedUserData);
                }
              }

              // 更新用户数据后再跳转回原页面
              setFlowOver(true);
              setShowBindPhoneModal(false);

              const redirectUrl = searchParams.get('redirect');
              if (redirectUrl) {
                router.push(redirectUrl);
              } else {
                router.push('/home');
              }
              return;
            }

            // 1.判断是否是JSON字符串（微信绑定到已有账号的情况）
            try {
              // 2.尝试解析JSON
              const userData = JSON.parse(phoneOrUserData);
              console.log("zww：检测到微信绑定到已有账号返回的userData:", userData);
              // 3.更新当前用户数据为微信到的目标账号信息，下一流程继续判断roleId
              setCurrentUserData(userData);
              dispatch(setUser(userData));
              saveUserDataToLocalStorage(userData);
              // 如果不需要进一步操作，解锁流程
              unlockUserFlow("微信绑定到已有账号成功");

            } catch (e) {
              // 不是JSON，是普通手机号绑定的情况
              const phone = phoneOrUserData;
              if (currentUserData) {
                const updatedUserData = { ...currentUserData, phone };
                // zww：绑定成功后更新当前本地存储和redux里的值
                setCurrentUserData(updatedUserData);
                dispatch(setUser(updatedUserData));
                saveUserDataToLocalStorage(updatedUserData);
              }
              // zww：信息的返回，知道结束了哪一步
              unlockUserFlow("绑定手机号的成功回调");
            }
            // 绑定成功后关闭弹窗
            console.log("zww：绑定手机号的流程结束，关闭弹窗");
            setShowBindPhoneModal(false)
          }}
          onSkip={() => {
            setShowBindPhoneModal(false)
            unlockUserFlow("用户跳过了手机绑定")
          }
          }
          allowSkip={true}
          registerType={currentUserData?.registerType || ''} // 使用currentUserData中的registerType
          openid={currentUserData?.openid || ''} // 传递openid
          scene_str={currentUserData?.scene_str || ''} // 传递scene_str
        />
      </SlideContainer>

      {/* 添加角色选择组件 */}
      <SlideContainer
        isOpen={showRoleSelectModal}
        onClose={() => setShowRoleSelectModal(false)}
        beforeClose={() => setIsSlideOut(false)}
      >
        <InlineRoleSelection
          userId={currentUserData?.userId || 0}
          onSuccess={(selectCallBackObject) => {
            // 1.绑定成功后直接关闭弹框显示
            setShowRoleSelectModal(false);
            // 2.判断身份认证是否触及了需要认证的步骤，比如教师认证
            if (selectCallBackObject?.needsAuthentication) {
              console.log("zww：身份认证的后续流程检查到选择了教师，需要弹出教师认证弹窗！");
              // 后续在认证成功的回调触发放行！
              setShowAuthModal(true);
            }
            // 3.不涉及的话，就是选择了学生或者普通用户，那么直接放行！
            else {
              if (authFlowPromiseRef.current) {
                authFlowPromiseRef.current.resolve();
                authFlowPromiseRef.current = null;
              }
            }
          }}
        />
      </SlideContainer>
      {/* 添加账号选择组件 */}
      <SlideContainer
        isOpen={showChooseAccountModal}
        onClose={() => setShowChooseAccountModal(false)}
        beforeClose={() => setIsSlideOut(false)}
      >
        <InlineChooseAccount
          roleList={currentUserData?.roleList || []}
          sessionId={currentUserData?.sessionId || ''}
          onSuccess={(userId) => {
            setShowChooseAccountModal(false);
            if (authFlowPromiseRef.current) {
              authFlowPromiseRef.current.resolve();
              authFlowPromiseRef.current = null;
            }
          }}
        />
      </SlideContainer>
      {/* 添加教师认证组件 */}
      <SlideContainer
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
        beforeClose={() => setIsSlideOut(false)}
      >
        <InlineTeacherAuth
          onSuccess={handleAuthSuccess}
          onBack={handleAuthBack}
        />
      </SlideContainer>

    </>
  );
} 