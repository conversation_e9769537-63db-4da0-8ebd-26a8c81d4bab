import { useState } from 'react';
import { Mo<PERSON>, Input, But<PERSON>, Result, Tooltip, Table } from 'antd';
import { keyPackageApi } from '@/lib/api/key_package';
import { KeyOutlined, CheckCircleOutlined, HistoryOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import { GetNotification } from 'logic-common/dist/components/Notification';

const notification = GetNotification();

interface RedeemKeyModalProps {
  isOpen: boolean;
  onClose: () => void;
  userId: number;
}

const RedeemKeyModal: React.FC<RedeemKeyModalProps> = ({ isOpen, onClose, userId }) => {
  const [key, setKey] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showHistoryModal, setShowHistoryModal] = useState(false);
  const [historyRecords, setHistoryRecords] = useState<any[]>([]);
  const [historyLoading, setHistoryLoading] = useState(false);
  const [redeemResult, setRedeemResult] = useState<{
    success: boolean;
    packageInfo?: {
      name: string;
      points: number;
      validityDays: number;
    };
    message: string;
  } | null>(null);

  const handleKeyChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setKey(e.target.value.trim());
  };

  const validateKey = async () => {
    if (!key) {
      notification.error('请输入密钥');
      return;
    }

    setIsLoading(true);

    try {
      // 先验证密钥
      const { data: validateRes } = await keyPackageApi.validateKey({
        key,
        userId
      });

      if (validateRes.code === 200) {
        // 验证成功，显示确认信息
        const packageInfo = validateRes.data.packageInfo;
        Modal.confirm({
          title: '确认兑换',
          content: (
            <div className="space-y-3">
              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="text-sm text-gray-600 mb-2">您即将兑换：</div>
                <div className="font-medium text-blue-700">{packageInfo.name}</div>
                <div className="text-sm text-gray-600 mt-1">
                  能量点数：<span className="font-medium text-orange-500">{packageInfo.points}</span>
                </div>
                <div className="text-sm text-gray-600">
                  有效期：<span className="font-medium">{packageInfo.validityDays}天</span>
                </div>
              </div>
              <div className="text-sm text-gray-500">
                确认后将立即为您兑换该套餐，此操作不可撤销。
              </div>
            </div>
          ),
          onOk: redeemKey,
          okText: '确认兑换',
          cancelText: '取消',
          centered: true
        });
      } else {
        notification.error(validateRes.message || '密钥验证失败');
      }
    } catch (error) {
      console.error('验证密钥失败:', error);
      notification.error('验证密钥失败，请检查网络连接');
    } finally {
      setIsLoading(false);
    }
  };

  const redeemKey = async () => {
    try {
      const { data: res } = await keyPackageApi.useKey({
        key,
        userId
      });

      if (res.code === 200) {
        // 兑换成功
        setRedeemResult({
          success: true,
          packageInfo: res.data.packageInfo,
          message: res.data.message || '兑换成功'
        });

        // 触发自定义事件，通知导航栏更新能量点数
        const event = new CustomEvent('pointsUpdate', {
          detail: {
            points: res.data.packageInfo?.points || 0,
            change: 'add'
          }
        });
        window.dispatchEvent(event);
      } else {
        setRedeemResult({
          success: false,
          message: res.message || '兑换失败'
        });
      }
    } catch (error) {
      console.error('兑换密钥失败:', error);
      setRedeemResult({
        success: false,
        message: '兑换过程中发生错误'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 获取兑换记录
  const fetchKeyRecords = async () => {
    setHistoryLoading(true);
    try {
      const { data: res } = await keyPackageApi.getUserKeyRecords(userId);
      console.log('res', res);
      if (res.code === 200) {
        setHistoryRecords(res.data);
      } else {
        notification.error('获取兑换记录失败');
      }
    } catch (error) {
      console.error('获取兑换记录失败:', error);
      notification.error('获取兑换记录失败');
    } finally {
      setHistoryLoading(false);
    }
  };

  // 点击历史记录图标
  const handleHistoryClick = () => {
    setShowHistoryModal(true);
    fetchKeyRecords();
  };

  const resetForm = () => {
    setKey('');
    setRedeemResult(null);
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  return (
    <>
      <Modal
        title={
          <div className="flex items-center">
            <KeyOutlined className="mr-2 text-blue-500" />
            <span>兑换密令</span>
          </div>
        }
        open={isOpen}
        onCancel={handleClose}
        footer={null}
        width={500}
        centered
        destroyOnClose
        className="redeem-key-modal"
      >
        {redeemResult ? (
          <Result
            icon={
              redeemResult.success ? (
                <CheckCircleOutlined className="text-green-500" />
              ) : (
                <div className="text-red-500 text-4xl">❌</div>
              )
            }
            title={redeemResult.success ? '兑换成功！' : '兑换失败'}
            subTitle={
              redeemResult.success && redeemResult.packageInfo ? (
                <div className="space-y-2">
                  <div>恭喜您成功兑换 <span className="font-medium text-blue-600">{redeemResult.packageInfo.name}</span></div>
                  <div className="text-sm text-gray-600">
                    获得能量点数：<span className="font-medium text-orange-500">{redeemResult.packageInfo.points}</span>
                  </div>
                  <div className="text-sm text-gray-600">
                    有效期：<span className="font-medium">{redeemResult.packageInfo.validityDays}天</span>
                  </div>
                </div>
              ) : (
                redeemResult.message
              )
            }
            extra={[
              <Button key="continue" onClick={resetForm}>
                继续兑换
              </Button>,
              <Button key="close" type="primary" onClick={handleClose}>
                关闭
              </Button>
            ]}
          />
        ) : (
          <div className="space-y-6 py-4">
            <div className="flex justify-between items-center">
              <div className="text-gray-500 text-sm">
                请输入您的密令进行兑换，获取能量点数和套餐
              </div>
              <Tooltip title="查看兑换记录">
                <Button
                  type="text"
                  icon={<HistoryOutlined />}
                  onClick={handleHistoryClick}
                  className="flex items-center"
                />
              </Tooltip>
            </div>
            <Input
              placeholder="请输入密令"
              value={key}
              onChange={handleKeyChange}
              prefix={<KeyOutlined className="text-gray-400" />}
              size="large"
              allowClear
              onPressEnter={validateKey}
            />
            <div className="flex justify-end">
              <Button
                type="primary"
                onClick={validateKey}
                loading={isLoading}
                disabled={!key}
              >
                立即兑换
              </Button>
            </div>
          </div>
        )}
      </Modal>

      {/* 兑换记录模态框 */}
      <Modal
        title="兑换记录"
        open={showHistoryModal}
        onCancel={() => setShowHistoryModal(false)}
        footer={null}
        width={450}
        centered
      >
        <Table
          dataSource={historyRecords}
          loading={historyLoading}
          rowKey="id"
          pagination={{ pageSize: 6 }}
          columns={[
            {
              title: '兑换时间',
              dataIndex: 'created_at',
              key: 'created_at',
              render: (text) => dayjs(text).format('YYYY-MM-DD HH:mm')
            },
            {
              title: '套餐名称',
              dataIndex: 'packageInfo',
              key: 'packageInfo',
              render: (packageInfo, record) => (
                <span className="text-blue-600">
                  {packageInfo?.keyPackage.name || record.keyPackage.name || '-'}
                </span>
              )
            }
          ]}
          locale={{
            emptyText: '暂无兑换记录'
          }}
          size="middle"
        />
      </Modal>
    </>
  );
};

export default RedeemKeyModal;
