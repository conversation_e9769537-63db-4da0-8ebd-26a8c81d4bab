import { ConfigService } from '@nestjs/config';
export interface SlowQueryRecord {
    query: string;
    parameters?: any[];
    executionTime: number;
    timestamp: Date;
    stack?: string;
    context?: string;
}
export interface QueryMetrics {
    totalQueries: number;
    slowQueries: number;
    averageExecutionTime: number;
    maxExecutionTime: number;
    minExecutionTime: number;
    lastResetTime: Date;
}
export declare class QueryMonitorService {
    private readonly configService;
    private readonly logger;
    private slowQueryThreshold;
    private enableSlowQueryLogging;
    private enableQueryMetrics;
    private enableStackTrace;
    private lightweightMode;
    private samplingRate;
    private asyncSlowQueryProcessing;
    private enableDatabaseMonitoring;
    private slowQueryRecords;
    private queryMetrics;
    private maxSlowQueryRecords;
    private queryCounter;
    constructor(configService: ConfigService);
    recordQuery(query: string, executionTime: number, parameters?: any[], context?: string): void;
    private recordSlowQuery;
    private updateMetrics;
    private logSlowQuery;
    private triggerSlowQueryAlert;
    private sanitizeQuery;
    private sanitizeQueryLightweight;
    private sanitizeParameters;
    private captureStack;
    getSlowQueryRecords(limit?: number): SlowQueryRecord[];
    getQueryMetrics(): QueryMetrics;
    resetMetrics(): void;
    clearSlowQueryRecords(): void;
    getConfig(): {
        enableDatabaseMonitoring: boolean;
        lightweightMode: boolean;
        slowQueryThreshold: number;
        enableSlowQueryLogging: boolean;
        enableQueryMetrics: boolean;
        enableStackTrace: boolean;
        samplingRate: number;
        asyncSlowQueryProcessing: boolean;
        maxSlowQueryRecords: number;
        queryCounter: number;
    };
    updateConfig(config: Partial<{
        enableDatabaseMonitoring: boolean;
        lightweightMode: boolean;
        slowQueryThreshold: number;
        enableSlowQueryLogging: boolean;
        enableQueryMetrics: boolean;
        enableStackTrace: boolean;
        samplingRate: number;
        asyncSlowQueryProcessing: boolean;
        maxSlowQueryRecords: number;
    }>): void;
}
