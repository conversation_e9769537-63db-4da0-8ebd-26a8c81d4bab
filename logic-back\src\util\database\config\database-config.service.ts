import { Injectable } from '@nestjs/common';
import { TypeOrmModuleOptions } from '@nestjs/typeorm';
import { join } from 'path';
import { YamlService } from '../../yaml/yaml.service';
import { RedisOptions } from 'ioredis';
import { CustomTypeOrmLogger } from 'src/util/database/monitor/typeorm-logger';

@Injectable()
export class DatabaseConfigService {
  private readonly databasesConfig: Record<string, any>;
  private customLogger?: CustomTypeOrmLogger;

  constructor(private readonly yamlService: YamlService) {
    try {
      // 根据环境读取相应的配置文件
      this.databasesConfig = this.yamlService.readYamlConfig<Record<string, any>>(
        'databases.yaml',
        'src/util/database/config',
        true
      );
      console.log(`成功加载${this.yamlService.getEnvironment()}环境的数据库配置`);
    } catch (e) {
      console.error(`读取${this.yamlService.getEnvironment()}环境的数据库配置文件失败:`, e);
      throw e;
    }
  }

  getMysqlConfig(queryInterceptor?: any): TypeOrmModuleOptions {
    // 获取MySQL配置
    const mysqlConfig = this.databasesConfig.mysql;

    // 处理路径
    if (mysqlConfig && mysqlConfig.entities) {
      // 使用全局路径处理entities
      mysqlConfig.entities = mysqlConfig.entities.map((path: string) => {
        return path.replace('dist/', join(process.cwd(), 'dist/'));
      });
    }

    // 集成慢查询监控
    if (queryInterceptor) {
      this.customLogger = new CustomTypeOrmLogger(queryInterceptor);
      mysqlConfig.logger = this.customLogger;
      mysqlConfig.logging = ['query', 'error', 'slow'];
      // 使用配置文件中的 maxQueryExecutionTime，不要硬编码覆盖
      // mysqlConfig.maxQueryExecutionTime 保持配置文件中的值
    }

    return mysqlConfig as TypeOrmModuleOptions;
  }

  getRedisConfig(): RedisOptions {
    const redisConfig = this.databasesConfig.redis;
    
    if (!redisConfig) {
      throw new Error('无法从配置文件中获取Redis配置');
    }
    
    return {
      host: redisConfig.host,
      port: redisConfig.port,
      password: redisConfig.password,
      db: redisConfig.db,
    } as RedisOptions;
  }
} 