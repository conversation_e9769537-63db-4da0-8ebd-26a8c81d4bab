import { useState, useEffect } from 'react';
import { Card, Table, Button, Modal, Form, Input, DatePicker, Select, Popconfirm, Tag, Space, InputNumber, Spin, Row, Col, Dropdown, Switch } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, KeyOutlined, DownloadOutlined, FileAddOutlined, SearchOutlined, ReloadOutlined, DownOutlined } from '@ant-design/icons';
import { keyPackageApi } from '@/lib/api/key_package';
import { packageApi } from '@/lib/api/package';
import { GetNotification } from 'logic-common/dist/components/Notification';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import { formatDate } from '@/lib/utils/date';

interface KeyPackage {
  id: number;
  name: string;
  key: string;
  start_date: string | Date;
  end_date: string | Date;
  key_type: number;
  user_role: string;
  status: number;
  package_id: number;
  created_at: string | Date;
  updated_at: string | Date;
  usage_count?: number;

}

interface PackageInfo {
  id: number;
  name: string;
  points: number;
  validityDays: number;
  status: number;
}

interface UserRole {
  id: number;
  code: string;
  name: string;
}

interface KeyPackageManagementProps {
  userId?: number;
}

const KeyPackageManagement: React.FC<KeyPackageManagementProps> = ({ userId }) => {
  const [keyPackageList, setKeyPackageList] = useState<KeyPackage[]>([]);
  const [loading, setLoading] = useState(false);
  const [isAddModalVisible, setIsAddModalVisible] = useState(false);
  const [isEditModalVisible, setIsEditModalVisible] = useState(false);
  const [isKeyPackageModalVisible, setIsKeyPackageModalVisible] = useState(false);
  const [isBatchCreateModalVisible, setIsBatchCreateModalVisible] = useState(false);
  const [batchCreating, setBatchCreating] = useState(false);
  const [batchCreatedKeys, setBatchCreatedKeys] = useState<any[]>([]);
  const [editingId, setEditingId] = useState<number | null>(null);
  const [packageList, setPackageList] = useState<PackageInfo[]>([]);
  const [roleList] = useState<UserRole[]>([
    { id: -1, code: '-1', name: '所有人' },
    { id: 0, code: '0', name: '管理员' },
    { id: 1, code: '1', name: '学生' },
    { id: 2, code: '2', name: '教师' },
    { id: 3, code: '3', name: '普通用户' },
    { id: 4, code: '4', name: '运营人员' }
  ]);
  const [addForm] = Form.useForm();
  const [editForm] = Form.useForm();
  const [batchCreateForm] = Form.useForm();
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });
  const [searchForm] = Form.useForm();
  const [searchParams, setSearchParams] = useState({
    name: '',
    key: '',
    status: undefined
  });
  const [exportFormat, setExportFormat] = useState<'csv' | 'excel' | 'txt' | 'json'>('csv');
  const [exportDropdownVisible, setExportDropdownVisible] = useState(false);
  const notification = GetNotification();

  // 组件加载时获取套餐列表
  useEffect(() => {
    fetchPackageList();
  }, []);

  // 监听搜索参数变化
  useEffect(() => {
    if (isKeyPackageModalVisible) {
      fetchKeyPackageList(1, pagination.pageSize);
    }
  }, [searchParams]);

  // 获取密钥列表
  const fetchKeyPackageList = async (page = 1, size = 10) => {
    try {
      setLoading(true);
      const { data: res } = await keyPackageApi.getList({
        page,
        size,
        ...searchParams
      });
      if (res.code === 200) {
        setKeyPackageList(res.data.list);
        setPagination({
          ...pagination,
          current: page,
          total: res.data.pagination.total
        });
      }
    } catch (error) {
      console.error('获取密钥列表失败:', error);
      notification.error('获取密钥列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取套餐列表
  const fetchPackageList = async () => {
    try {
      const { data: res } = await packageApi.getList();
      console.log('套餐列表:', res);

      if (res.code === 200) {
        setPackageList(res.data.list);
      }
    } catch (error) {
      console.error('获取套餐列表失败:', error);
      notification.error('获取套餐列表失败');
    }
  };

  // 打开密钥列表弹窗时获取数据
  const showKeyPackageModal = async () => {
    await Promise.all([
      fetchKeyPackageList(),
      fetchPackageList()
    ]);
    setIsKeyPackageModalVisible(true);
  };

  // 表格列配置
  const columns: ColumnsType<KeyPackage> = [
    {
      title: '密钥名称',
      dataIndex: 'name',
      key: 'name',
      width: 150,
    },
    {
      title: '密钥',
      dataIndex: 'key',
      key: 'key',
      width: 200,
      ellipsis: true,
    },
    {
      title: '类型',
      dataIndex: 'key_type',
      key: 'key_type',
      width: 120,
      render: (type) => (
        <Tag color={type === 1 ? 'blue' : 'green'}>
          {type === 1 ? '全局一次性' : '每用户一次'}
        </Tag>
      ),
    },
    {
      title: '使用人群',
      dataIndex: 'user_role',
      key: 'user_role',
      width: 120,
      render: (roles: string) => {
        if (roles === '-1') return <Tag color="purple">所有人</Tag>;
        const roleNames = roles.split(',').map((role: string) => {
          const found = roleList.find(r => r.code === role);
          return found ? found.name : role;
        });
        return roleNames.map((name: string, index: number) => (
          <Tag key={index} color="blue">{name}</Tag>
        ));
      },
    },
    {
      title: '有效期',
      key: 'validity',
      width: 200,
      render: (_, record) => (
        <span>
          {formatDate(record.start_date)} 至 {formatDate(record.end_date)}
        </span>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status) => (
        <Tag color={status === 1 ? 'green' : 'red'}>
          {status === 1 ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '使用次数',
      dataIndex: 'usage_count',
      key: 'usage_count',
      width: 80,
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个密钥吗?"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 分页变化
  const handleTableChange = (page: number, pageSize: number) => {
    fetchKeyPackageList(page, pageSize);
  };

  // 添加密钥
  const handleAdd = () => {
    addForm.resetFields();
    addForm.setFieldsValue({
      key_type: 1,
      user_role: '-1',
      status: 1,
    });
    setIsAddModalVisible(true);
    // 确保套餐列表已加载
    fetchPackageList();
  };

  // 编辑密钥
  const handleEdit = async (record: KeyPackage) => {
    setEditingId(record.id);

    // 转换日期格式
    const formData = {
      ...record,
      start_date: dayjs(record.start_date),
      end_date: dayjs(record.end_date),
    };

    editForm.setFieldsValue(formData);
    setIsEditModalVisible(true);
    // 确保套餐列表已加载
    fetchPackageList();
  };

  // 删除密钥
  const handleDelete = async (id: number) => {
    try {
      const { data: res } = await keyPackageApi.delete(id);
      if (res.code === 200) {
        notification.success('删除成功');
        fetchKeyPackageList(pagination.current, pagination.pageSize);
      } else {
        notification.error(res.message || '删除失败');
      }
    } catch (error) {
      console.error('删除密钥失败:', error);
      notification.error('删除密钥失败');
    }
  };

  // 提交添加表单
  const handleAddSubmit = async () => {
    try {
      const values = await addForm.validateFields();

      // 转换日期格式
      const formData = {
        ...values,
        operator_id: userId,
        start_date: dayjs(values.start_date).format('YYYY-MM-DD HH:mm:ss'),
        end_date: dayjs(values.end_date).format('YYYY-MM-DD HH:mm:ss'),
      };
      console.log('formData', formData);
      const { data: res } = await keyPackageApi.add(formData);
      if (res.code === 200) {
        notification.success('添加成功');
        setIsAddModalVisible(false);
        fetchKeyPackageList(pagination.current, pagination.pageSize);
      } else {
        notification.error(res.message || '添加失败');
      }
    } catch (error) {
      console.error('提交表单失败:', error);
    }
  };

  // 提交编辑表单
  const handleEditSubmit = async () => {
    try {
      const values = await editForm.validateFields();

      // 转换日期格式
      const formData = {
        ...values,
        id: editingId,
        start_date: dayjs(values.start_date).format('YYYY-MM-DD HH:mm:ss'),
        end_date: dayjs(values.end_date).format('YYYY-MM-DD HH:mm:ss'),
      };

      const { data: res } = await keyPackageApi.update(formData);
      if (res.code === 200) {
        notification.success('更新成功');
        setIsEditModalVisible(false);
        fetchKeyPackageList(pagination.current, pagination.pageSize);
      } else {
        notification.error(res.message || '更新失败');
      }
    } catch (error) {
      console.error('提交表单失败:', error);
    }
  };

  // 生成随机密钥
  const generateRandomKey = (form: any) => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let key = '';
    for (let i = 0; i < 16; i++) {
      if (i > 0 && i % 4 === 0) key += '-';
      key += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    form.setFieldsValue({ key });
  };

  // 批量创建密钥
  const handleBatchCreate = () => {
    batchCreateForm.resetFields();
    batchCreateForm.setFieldsValue({
      count: 10,
      key_type: 1,
      user_role: '-1',
      status: 1,
    });
    setIsBatchCreateModalVisible(true);
    setBatchCreatedKeys([]);
    // 确保套餐列表已加载
    fetchPackageList();
  };

  // 提交批量创建表单
  const handleBatchCreateSubmit = async () => {
    try {
      const values = await batchCreateForm.validateFields();

      // 转换日期格式
      const formData = {
        ...values,
        start_date: dayjs(values.start_date).format('YYYY-MM-DD HH:mm:ss'),
        end_date: dayjs(values.end_date).format('YYYY-MM-DD HH:mm:ss'),
        operator_id: userId, // 添加操作者ID
      };

      setBatchCreating(true);

      const { data: res } = await keyPackageApi.batchCreate(formData);
      if (res.code === 200) {
        notification.success(`成功创建${res.data.created}个密钥`);
        setBatchCreatedKeys(res.data.keys);
        fetchKeyPackageList(pagination.current, pagination.pageSize);
      } else {
        notification.error(res.message || '批量创建失败');
      }
    } catch (error) {
      console.error('提交表单失败:', error);
      notification.error('批量创建密钥失败');
    } finally {
      setBatchCreating(false);
    }
  };

  // 下载密钥
  const downloadKeys = () => {
    if (!batchCreatedKeys.length) {
      notification.warning('没有可下载的密钥');
      return;
    }

    // 准备导出数据
    const exportData = batchCreatedKeys.map(key => {
      // 查找对应的套餐名称
      let packageName = '未知套餐';
      const packageInfo = packageList.find(pkg => pkg.id === key.package_id);
      if (packageInfo) {
        packageName = packageInfo.name;
      }

      return {
        密钥名称: key.name,
        密钥: key.key,
        有效期开始: key.start_date,
        有效期结束: key.end_date,
        套餐名称: packageName
      };
    });

    let blob: Blob;
    let filename = `密钥批量导出_${dayjs().format('YYYYMMDD_HHmmss')}`;

    switch (exportFormat) {
      case 'csv':
        // CSV格式
        let csvContent = '密钥名称,密钥,有效期开始,有效期结束,套餐名称\n';
        exportData.forEach(item => {
          const row = [
            item.密钥名称,
            item.密钥,
            item.有效期开始,
            item.有效期结束,
            item.套餐名称
          ].join(',');
          csvContent += row + '\n';
        });
        blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        filename += '.csv';
        break;

      case 'excel':
        // Excel格式 (实际是CSV，但用xls扩展名)
        let excelContent = '密钥名称\t密钥\t有效期开始\t有效期结束\t套餐名称\n';
        exportData.forEach(item => {
          const row = [
            item.密钥名称,
            item.密钥,
            item.有效期开始,
            item.有效期结束,
            item.套餐名称
          ].join('\t');
          excelContent += row + '\n';
        });
        blob = new Blob([excelContent], { type: 'application/vnd.ms-excel;charset=utf-8;' });
        filename += '.xls';
        break;

      case 'txt':
        // TXT格式
        let txtContent = '密钥名称\t密钥\t有效期开始\t有效期结束\t套餐名称\n';
        exportData.forEach(item => {
          const row = [
            item.密钥名称,
            item.密钥,
            item.有效期开始,
            item.有效期结束,
            item.套餐名称
          ].join('\t');
          txtContent += row + '\n';
        });
        blob = new Blob([txtContent], { type: 'text/plain;charset=utf-8;' });
        filename += '.txt';
        break;

      case 'json':
        // JSON格式
        blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json;charset=utf-8;' });
        filename += '.json';
        break;

      default:
        notification.error('不支持的导出格式');
        return;
    }

    // 创建下载链接
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');

    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    notification.success(`密钥已成功导出为${exportFormat.toUpperCase()}格式`);
  };

  // 表单组件
  const renderForm = (form: any, onFinish: () => void) => (
    <Form
      form={form}
      layout="vertical"
    >
      <Form.Item
        name="name"
        label="密钥名称"
        rules={[{ required: true, message: '请输入密钥名称' }]}
      >
        <Input placeholder="请输入密钥名称" />
      </Form.Item>

      <Form.Item
        name="key"
        label="密钥"
        rules={[{ required: true, message: '请输入密钥' }]}
        extra="密钥是兑换码的唯一标识，请确保其唯一性"
      >
        <Input
          placeholder="请输入密钥"
          addonAfter={
            <Button
              type="primary"
              onClick={() => generateRandomKey(form)}
              style={{ margin: '-5px -11px', height: '30px' }}
            >
              生成随机密钥
            </Button>
          }
        />
      </Form.Item>

      <Form.Item
        name="key_type"
        label="密钥类型"
        rules={[{ required: true, message: '请选择密钥类型' }]}
      >
        <Select>
          <Select.Option value={1}>全局一次性（只能被兑换一次）</Select.Option>
          <Select.Option value={2}>每用户一次（每个用户只能兑换一次）</Select.Option>
        </Select>
      </Form.Item>

      {/* <Form.Item
        name="key_special"
        label="特殊密钥"
        valuePropName="checked"
        getValueFromEvent={(checked) => checked ? 0 : 1}
        getValueProps={(value) => ({ checked: value === 0 })}
        initialValue={1}
        extra="特殊密钥可用于特定场景，默认为非特殊密钥"
      >
        <Switch 
          checkedChildren="特殊" 
          unCheckedChildren="非特殊" 
        />
      </Form.Item> */}

      <Form.Item
        name="user_role"
        label="使用人群"
        rules={[{ required: true, message: '请选择使用人群' }]}
      >
        <Select>
          <Select.Option value="-1">所有人</Select.Option>
          <Select.Option value="0">管理员</Select.Option>
          <Select.Option value="1">学生</Select.Option>
          <Select.Option value="2">教师</Select.Option>
          <Select.Option value="3">普通用户</Select.Option>
          <Select.Option value="4">运营人员</Select.Option>
        </Select>
      </Form.Item>

      <Form.Item
        name="package_id"
        label="绑定套餐"
        rules={[{ required: true, message: '请选择绑定套餐' }]}
      >
        <Select>
          {packageList && packageList.length > 0 ? (
            packageList.map((pkg) => (
              <Select.Option key={pkg.id} value={pkg.id}>
                {pkg.name} ({pkg.points}点/{pkg.validityDays}天)
              </Select.Option>
            ))
          ) : (
            <Select.Option value="" disabled>
              加载套餐列表中...
            </Select.Option>
          )}
        </Select>
      </Form.Item>

      <Form.Item
        name="start_date"
        label="开始有效日期"
        rules={[{ required: true, message: '请选择开始有效日期' }]}
      >
        <DatePicker showTime style={{ width: '100%' }} />
      </Form.Item>

      <Form.Item
        name="end_date"
        label="结束有效日期"
        rules={[{ required: true, message: '请选择结束有效日期' }]}
      >
        <DatePicker showTime style={{ width: '100%' }} />
      </Form.Item>

      <Form.Item
        name="status"
        label="状态"
        rules={[{ required: true, message: '请选择状态' }]}
      >
        <Select>
          <Select.Option value={1}>启用</Select.Option>
          <Select.Option value={0}>禁用</Select.Option>
        </Select>
      </Form.Item>

      <Form.Item>
        <Button type="primary" onClick={onFinish}>
          提交
        </Button>
      </Form.Item>
    </Form>
  );

  // 批量创建表单组件
  const renderBatchCreateForm = () => (
    <Form
      form={batchCreateForm}
      layout="vertical"
    >
      <Form.Item
        name="namePrefix"
        label="密钥名称前缀"
        rules={[{ required: true, message: '请输入密钥名称前缀' }]}
        extra="系统将自动为每个密钥添加序号，例如: 前缀_1, 前缀_2"
      >
        <Input placeholder="请输入密钥名称前缀" />
      </Form.Item>

      <Form.Item
        name="count"
        label="批量创建数量"
        rules={[
          { required: true, message: '请输入创建数量' },
          { type: 'number', min: 1, max: 500, message: '创建数量必须在1-500之间' }
        ]}
        extra="一次最多可创建500个密钥"
      >
        <InputNumber min={1} max={500} style={{ width: '100%' }} />
      </Form.Item>

      <Form.Item
        name="key_type"
        label="密钥类型"
        rules={[{ required: true, message: '请选择密钥类型' }]}
      >
        <Select>
          <Select.Option value={2}>每用户一次（每个用户只能兑换一次）</Select.Option>
          <Select.Option value={1}>全局一次性（只能被兑换一次）</Select.Option>
        </Select>
      </Form.Item>

      {/* <Form.Item
        name="key_special"
        label="特殊密钥"
        valuePropName="checked"
        getValueFromEvent={(checked) => checked ? 0 : 1}
        getValueProps={(value) => ({ checked: value === 0 })}
        initialValue={1}
        extra="特殊密钥可用于特定场景，默认为非特殊密钥"
      >
        <Switch 
          checkedChildren="特殊" 
          unCheckedChildren="非特殊" 
        />
      </Form.Item> */}

      <Form.Item
        name="user_role"
        label="使用人群"
        rules={[{ required: true, message: '请选择使用人群' }]}
      >
        <Select>
          <Select.Option value="-1">所有人</Select.Option>
          <Select.Option value="0">管理员</Select.Option>
          <Select.Option value="1">学生</Select.Option>
          <Select.Option value="2">教师</Select.Option>
          <Select.Option value="3">普通用户</Select.Option>
          <Select.Option value="4">运营人员</Select.Option>
        </Select>
      </Form.Item>

      <Form.Item
        name="package_id"
        label="绑定套餐"
        rules={[{ required: true, message: '请选择绑定套餐' }]}
      >
        <Select>
          {packageList && packageList.length > 0 ? (
            packageList.map((pkg) => (
              <Select.Option key={pkg.id} value={pkg.id}>
                {pkg.name} ({pkg.points}点/{pkg.validityDays}天)
              </Select.Option>
            ))
          ) : (
            <Select.Option value="" disabled>
              加载套餐列表中...
            </Select.Option>
          )}
        </Select>
      </Form.Item>

      <Form.Item
        name="start_date"
        label="开始有效日期"
        rules={[{ required: true, message: '请选择开始有效日期' }]}
      >
        <DatePicker showTime style={{ width: '100%' }} />
      </Form.Item>

      <Form.Item
        name="end_date"
        label="结束有效日期"
        rules={[{ required: true, message: '请选择结束有效日期' }]}
      >
        <DatePicker showTime style={{ width: '100%' }} />
      </Form.Item>

      <Form.Item
        name="status"
        label="状态"
        rules={[{ required: true, message: '请选择状态' }]}
      >
        <Select>
          <Select.Option value={1}>启用</Select.Option>
          <Select.Option value={0}>禁用</Select.Option>
        </Select>
      </Form.Item>

      <Form.Item>
        <Button type="primary" onClick={handleBatchCreateSubmit} loading={batchCreating}>
          批量创建
        </Button>
      </Form.Item>
    </Form>
  );

  // 密钥结果表格
  const batchResultColumns: ColumnsType<any> = [
    {
      title: '密钥名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '密钥',
      dataIndex: 'key',
      key: 'key',
      ellipsis: true,
    },
    {
      title: '套餐名称',
      dataIndex: 'package_id',
      key: 'package_id',
      render: (packageId) => {
        const packageInfo = packageList.find(pkg => pkg.id === packageId);
        return packageInfo ? packageInfo.name : '未知套餐';
      }
    },
    {
      title: '开始时间',
      dataIndex: 'start_date',
      key: 'start_date',
      render: (date) => formatDate(date),
    },
    {
      title: '结束时间',
      dataIndex: 'end_date',
      key: 'end_date',
      render: (date) => formatDate(date),
    }
  ];

  // 处理搜索
  const handleSearch = async () => {
    const values = await searchForm.validateFields();
    setSearchParams(values);
  };

  // 重置搜索
  const handleResetSearch = () => {
    searchForm.resetFields();
    setSearchParams({
      name: '',
      key: '',
      status: undefined
    });
  };

  // 搜索表单
  const renderSearchForm = () => (
    <Form
      form={searchForm}
      layout="inline"
      className="mb-4"
      onFinish={handleSearch}
    >
      <Row gutter={16} className="w-full">
        <Col span={7}>
          <Form.Item name="name" label="密钥名称">
            <Input placeholder="请输入密钥名称" allowClear />
          </Form.Item>
        </Col>
        <Col span={7}>
          <Form.Item name="key" label="密钥">
            <Input placeholder="请输入密钥" allowClear />
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item name="status" label="状态">
            <Select placeholder="选择状态" allowClear>
              <Select.Option value={1}>启用</Select.Option>
              <Select.Option value={0}>禁用</Select.Option>
            </Select>
          </Form.Item>
        </Col>
        <Col span={4} className="flex justify-end">
          <Space>
            <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
              搜索
            </Button>
            <Button onClick={handleResetSearch} icon={<ReloadOutlined />}>
              重置
            </Button>
          </Space>
        </Col>
      </Row>
    </Form>
  );

  // 设置导出格式并关闭下拉菜单
  const handleSetExportFormat = (format: 'csv' | 'excel' | 'txt' | 'json') => {
    setExportFormat(format);
    setExportDropdownVisible(false);
  };

  // 导出格式下拉菜单
  const exportFormatMenu = (
    <div className="bg-white shadow-md rounded-md overflow-hidden">
      <div className="px-2 py-1 text-xs text-gray-500">选择导出格式</div>
      <div
        className={`px-4 py-2 cursor-pointer hover:bg-gray-100 ${exportFormat === 'csv' ? 'bg-blue-50 text-blue-600' : ''}`}
        onClick={() => handleSetExportFormat('csv')}
      >
        CSV格式
      </div>
      <div
        className={`px-4 py-2 cursor-pointer hover:bg-gray-100 ${exportFormat === 'excel' ? 'bg-blue-50 text-blue-600' : ''}`}
        onClick={() => handleSetExportFormat('excel')}
      >
        Excel格式
      </div>
      <div
        className={`px-4 py-2 cursor-pointer hover:bg-gray-100 ${exportFormat === 'txt' ? 'bg-blue-50 text-blue-600' : ''}`}
        onClick={() => handleSetExportFormat('txt')}
      >
        TXT格式
      </div>
      <div
        className={`px-4 py-2 cursor-pointer hover:bg-gray-100 ${exportFormat === 'json' ? 'bg-blue-50 text-blue-600' : ''}`}
        onClick={() => handleSetExportFormat('json')}
      >
        JSON格式
      </div>
    </div>
  );

  return (
    <>
      {/* 密钥管理卡片 */}
      <Card
        title="密钥管理"
        extra={<Button type="primary" onClick={showKeyPackageModal}>查看全部</Button>}
        className="shadow-sm h-full"
      >
        <div className="space-y-4">
          <Button block onClick={handleAdd}>添加密钥</Button>
          <Button block icon={<FileAddOutlined />} onClick={handleBatchCreate}>批量创建密钥</Button>
        </div>
      </Card>

      {/* 密钥列表弹窗 */}
      <Modal
        title="密钥列表"
        open={isKeyPackageModalVisible}
        onCancel={() => setIsKeyPackageModalVisible(false)}
        width={1350}
        footer={null}
        centered={true}
      >
        {renderSearchForm()}

        <div className="mb-4 space-x-2">
          <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
            添加密钥
          </Button>
          <Button icon={<FileAddOutlined />} onClick={handleBatchCreate}>
            批量创建密钥
          </Button>
        </div>

        <Table
          columns={columns}
          dataSource={keyPackageList}
          rowKey="id"
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            onChange: handleTableChange,
            showTotal: (total) => `共 ${total} 条记录`,
            showSizeChanger: false,
            showQuickJumper: false
          }}
          loading={loading}
          scroll={{ x: 1100 }}
        />
      </Modal>

      {/* 添加密钥弹窗 */}
      <Modal
        title="添加密钥"
        open={isAddModalVisible}
        onCancel={() => setIsAddModalVisible(false)}
        width={700}
        footer={null}
      >
        {renderForm(addForm, handleAddSubmit)}
      </Modal>

      {/* 编辑密钥弹窗 */}
      <Modal
        title="编辑密钥"
        open={isEditModalVisible}
        onCancel={() => setIsEditModalVisible(false)}
        width={700}
        footer={null}
      >
        {renderForm(editForm, handleEditSubmit)}
      </Modal>

      {/* 批量创建密钥弹窗 */}
      <Modal
        title="批量创建密钥"
        open={isBatchCreateModalVisible}
        onCancel={() => setIsBatchCreateModalVisible(false)}
        width={700}
        footer={null}
      >
        {batchCreatedKeys.length > 0 ? (
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <div className="text-green-600">
                成功创建 {batchCreatedKeys.length} 个密钥
              </div>
              <Space size="small">
                <Dropdown
                  overlay={exportFormatMenu}
                  trigger={['click']}
                  open={exportDropdownVisible}
                  onOpenChange={(visible) => setExportDropdownVisible(visible)}
                >
                  <Button type="primary" ghost onClick={() => setExportDropdownVisible(!exportDropdownVisible)}>
                    {`${exportFormat.toUpperCase()}格式`} <DownOutlined />
                  </Button>
                </Dropdown>
                <Button
                  type="primary"
                  icon={<DownloadOutlined />}
                  onClick={downloadKeys}
                >
                  导出密钥
                </Button>
              </Space>
            </div>
            <Table
              columns={batchResultColumns}
              dataSource={batchCreatedKeys}
              rowKey="id"
              pagination={{
                pageSize: 5,
                showSizeChanger: false,
                showQuickJumper: false
              }}
              size="small"
              scroll={{ y: 300, x: 650 }}
            />
            <div className="flex justify-end">
              <Button onClick={() => {
                setBatchCreatedKeys([]);
                batchCreateForm.resetFields();
              }}>继续创建</Button>
            </div>
          </div>
        ) : (
          <>
            {renderBatchCreateForm()}
            {batchCreating && (
              <div className="flex justify-center items-center mt-4">
                <Spin tip="正在创建密钥..." />
              </div>
            )}
          </>
        )}
      </Modal>
    </>
  );
};

export default KeyPackageManagement; 