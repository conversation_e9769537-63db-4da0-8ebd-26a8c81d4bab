"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebModule = void 0;
const common_1 = require("@nestjs/common");
const key_package_module_1 = require("../util/database/mysql/key_package/key_package.module");
const user_auth_module_1 = require("./user_auth/user_auth.module");
const user_srch_templates_module_1 = require("./user_srch_templates/user_srch_templates.module");
const router_guard_module_1 = require("./router_guard/router-guard.module");
const http_response_result_module_1 = require("./http_response_result/http_response_result.module");
const user_class_module_1 = require("./user_class/user_class.module");
const user_srch_work_module_1 = require("./user_srch_work/user_srch_work.module");
const user_srch_task_module_1 = require("./user_scrh_task/user_srch_task.module");
const web_user_info_module_1 = require("./web_user_info/web_user_info.module");
const user_srch_image_module_1 = require("./user_srch_image/user_srch_image.module");
const user_srch_enhance_module_1 = require("./user_srch_enhance/user_srch_enhance.module");
const user_srch_audio_module_1 = require("./user_srch_audio/user_srch_audio.module");
const user_point_package_module_1 = require("./user_point_package/user_point_package.module");
const user_school_module_1 = require("./user_school/user_school.module");
const user_role_relation_module_1 = require("./user_role_relation/user_role_relation.module");
const user_school_relation_module_1 = require("./user_school_relation/user_school_relation.module");
const teacher_task_module_1 = require("./teacher_task/teacher_task.module");
const web_announcement_module_1 = require("./web_announcement/web_announcement.module");
const web_carousel_module_1 = require("./web_carousel/web_carousel.module");
const user_info_module_1 = require("./user_info/user_info.module");
const web_doc_module_1 = require("./web_doc/web_doc.module");
const web_key_package_module_1 = require("./web_key_package/web_key_package.module");
const web_package_module_1 = require("./web_package/web_package.module");
const user_work_info_module_1 = require("./user_work_info/user_work_info.module");
const user_image_info_module_1 = require("./user_image_info/user_image_info.module");
const user_student_module_1 = require("./user_student/user_student.module");
const user_work_like_module_1 = require("./user_work_like/user_work_like.module");
const web_role_module_1 = require("./web_role/web_role.module");
const web_point_module_1 = require("./web_point/web_point.module");
const user_point_module_1 = require("./user_point/user_point.module");
const user_password_reset_module_1 = require("./user_password_reset/user_password_reset.module");
const web_permission_module_1 = require("./web_permission/web_permission.module");
const web_report_module_1 = require("./web_report/web_report.module");
const web_template_folder_module_1 = require("./web_template_folder/web_template_folder.module");
const web_point_permission_module_1 = require("./web_point_permission/web_point_permission.module");
const teacher_task_assignment_module_1 = require("./teacher_task_assignment/teacher_task_assignment.module");
const user_join_role_module_1 = require("./user_join_role/user_join_role.module");
const work_audit_module_1 = require("./work_audit/work_audit.module");
const oss_module_1 = require("./oss/oss.module");
const user_srch_image_segment_module_1 = require("./user_srch_image_segment/user_srch_image_segment.module");
const web_weixin_scan_module_1 = require("./web_weixin_scan/web_weixin_scan.module");
const weixin_message_module_1 = require("./weixin_message/weixin_message.module");
const weixin_module_1 = require("./weixin/weixin.module");
const teacher_audit_module_1 = require("./teacher_audit/teacher_audit.module");
const attachment_module_1 = require("./attachment/attachment.module");
const teacher_audit_attachment_module_1 = require("./teacher_audit_attachment/teacher_audit_attachment.module");
const self_assessment_item_module_1 = require("./self_assessment_item/self_assessment_item.module");
const package_order_business_module_1 = require("./package_order/package-order-business.module");
const user_login_log_module_1 = require("./user_login_log/user-login-log.module");
const user_tag_module_1 = require("./user_tag/user_tag.module");
const web_activity_module_1 = require("./web_activity/web_activity.module");
const web_activity_tag_module_1 = require("./web_activity_tag/web_activity_tag.module");
const web_activity_work_module_1 = require("./web_activity_work/web_activity_work.module");
const web_events_task_module_1 = require("./web_events_task/web_events_task.module");
const course_module_1 = require("./course/course.module");
let WebModule = class WebModule {
};
exports.WebModule = WebModule;
exports.WebModule = WebModule = __decorate([
    (0, common_1.Module)({
        imports: [
            work_audit_module_1.WebWorkAuditModule,
            user_role_relation_module_1.UserRoleRelationModule,
            user_join_role_module_1.UserJoinRoleModule,
            teacher_task_assignment_module_1.TeacherTaskAssignmentModule,
            user_student_module_1.UserStudentModule,
            user_work_like_module_1.UserWorkLikeModule,
            user_image_info_module_1.UserImageInfoModule,
            user_work_info_module_1.UserWorkInfoModule,
            key_package_module_1.KeyPackageModule,
            user_auth_module_1.UserAuthModule,
            user_srch_templates_module_1.UserSrchTemplatesModule,
            router_guard_module_1.RouterGuardModule,
            http_response_result_module_1.HttpResponseResultModule,
            user_class_module_1.UserClassModule,
            user_srch_work_module_1.UserSrchWorkModule,
            user_srch_task_module_1.UserSrchTaskModule,
            web_user_info_module_1.WebUserInfoModule,
            user_srch_image_module_1.UserSrchImageModule,
            user_srch_enhance_module_1.UserSrchEnhanceModule,
            user_srch_audio_module_1.UserSrchAudioModule,
            user_point_package_module_1.UserPointPackageModule,
            user_school_module_1.UserSchoolModule,
            user_info_module_1.UserInfoModule,
            user_school_relation_module_1.UserSchoolRelationModule,
            web_announcement_module_1.WebAnnouncementModule,
            web_carousel_module_1.WebCarouselModule,
            teacher_task_module_1.TeacherTaskModule,
            web_role_module_1.WebRoleModule,
            web_point_module_1.WebPointModule,
            user_point_module_1.UserPointModule,
            user_password_reset_module_1.UserPasswordResetModule,
            web_permission_module_1.WebPermissionModule,
            web_report_module_1.WebReportModule,
            web_template_folder_module_1.WebTemplateFolderModule,
            web_doc_module_1.WebDocModule,
            web_key_package_module_1.WebKeyPackageModule,
            web_package_module_1.WebPackageModule,
            web_point_permission_module_1.WebPointPermissionModule,
            oss_module_1.OssModule,
            user_srch_image_segment_module_1.UserSrchImageSegmentModule,
            web_weixin_scan_module_1.WebWeixinScanModule,
            weixin_message_module_1.WeixinMessageModule,
            weixin_module_1.WeixinModule,
            teacher_audit_module_1.TeacherAuditModule,
            attachment_module_1.AttachmentModule,
            teacher_audit_attachment_module_1.TeacherAuditAttachmentModule,
            self_assessment_item_module_1.SelfAssessmentItemModule,
            package_order_business_module_1.PackageOrderBusinessModule,
            user_login_log_module_1.UserLoginLogModule,
            user_tag_module_1.UserTagModule,
            web_activity_module_1.WebActivityModule,
            web_activity_tag_module_1.WebActivityTagModule,
            web_activity_work_module_1.WebActivityWorkModule,
            web_events_task_module_1.WebEventsTaskModule,
            course_module_1.CourseModule
        ],
        controllers: [],
    })
], WebModule);
//# sourceMappingURL=web.module.js.map