/* 班级任务页面样式 */
.classTasksContainer {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.tasksHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.tasksTitle {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.headerActions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.searchBox {
  position: relative;
  display: flex;
  align-items: center;
}

.searchIcon {
  position: absolute;
  left: 12px;
  color: #999;
  z-index: 1;
  transition: color 0.2s ease;
}

.searchInput {
  padding: 10px 16px 10px 40px;
  border: none;
  border-radius: 8px;
  width: 280px;
  font-size: 14px;
  background: white;
  color: #333;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.searchInput:hover {
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
}

.searchInput:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.1), 0 2px 8px rgba(24, 144, 255, 0.15);
}

.searchInput:focus + .searchIcon,
.searchInput:hover + .searchIcon {
  color: #40a9ff;
}

.searchInput::placeholder {
  color: #999;
  font-size: 14px;
}

.publishTaskBtn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background: linear-gradient(135deg, #ff9a56, #ff6b35);
  color: white;
  border: none;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.publishTaskBtn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
}

.filterSection {
  background: white;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.filterRow {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  gap: 16px;
}

.filterRow:last-child {
  margin-bottom: 0;
}

.filterLabel {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  min-width: 80px;
  flex-shrink: 0;
}

.filterTabs {
  display: flex;
  gap: 8px;
  flex: 1;
}

.filterTab {
  padding: 6px 16px;
  border: 1px solid #d9d9d9;
  background: white;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.filterTab:hover {
  border-color: #40a9ff;
  color: #40a9ff;
}

.filterTab.active {
  background: #1890ff;
  border-color: #1890ff;
  color: white;
}

.dateRange {
  display: flex;
  align-items: center;
  gap: 8px;
}

.dateInputContainer {
  position: relative;
  display: inline-block;
}

.datetimeInput {
  padding: 8px 40px 8px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  cursor: pointer;
  min-width: 220px;
  width: 220px;
  color: transparent;
  transition: all 0.2s ease;
}

/* 日期输入框样式（只选择日期，不包含时间） */
.dateInput {
  padding: 8px 40px 8px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  cursor: pointer;
  min-width: 180px;
  width: 180px;
  color: transparent;
  transition: all 0.2s ease;
}

.dateInput:hover {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
}

.dateInput:focus {
  outline: none;
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 隐藏日期输入框的默认占位符 */
.dateInput::-webkit-datetime-edit {
  color: transparent;
  background: transparent;
}

.dateInput::-webkit-datetime-edit-text {
  color: transparent;
  padding: 0;
}

.dateInput::-webkit-datetime-edit-month-field,
.dateInput::-webkit-datetime-edit-day-field,
.dateInput::-webkit-datetime-edit-year-field {
  color: transparent;
  background: transparent;
  border: none;
  padding: 0;
  width: 0;
  opacity: 0;
}

/* 当有值时显示实际的日期 */
.dateInput[value]:not([value=""])::-webkit-datetime-edit {
  color: #333;
  background: transparent;
}

.dateInput[value]:not([value=""])::-webkit-datetime-edit-text {
  color: #333;
  padding: 0 2px;
}

.dateInput[value]:not([value=""])::-webkit-datetime-edit-month-field,
.dateInput[value]:not([value=""])::-webkit-datetime-edit-day-field,
.dateInput[value]:not([value=""])::-webkit-datetime-edit-year-field {
  color: #333;
  background: transparent;
  border: none;
  padding: 0 2px;
  width: auto;
  opacity: 1;
}

/* 聚焦时显示字段，便于编辑 */
.dateInput:focus::-webkit-datetime-edit {
  color: #333;
}

.dateInput:focus::-webkit-datetime-edit-text {
  color: #333;
}

.dateInput:focus::-webkit-datetime-edit-month-field,
.dateInput:focus::-webkit-datetime-edit-day-field,
.dateInput:focus::-webkit-datetime-edit-year-field {
  color: #333;
  width: auto;
  opacity: 1;
  cursor: text;
  user-select: text;
  -webkit-user-select: text;
}

/* 当输入框为空时，完全隐藏默认字符 */
.dateInput:not([value])::-webkit-datetime-edit,
.dateInput[value=""]::-webkit-datetime-edit {
  color: transparent !important;
  background: transparent !important;
}

.dateInput:not([value])::-webkit-datetime-edit-text,
.dateInput[value=""]::-webkit-datetime-edit-text,
.dateInput:not([value])::-webkit-datetime-edit-month-field,
.dateInput[value=""]::-webkit-datetime-edit-month-field,
.dateInput:not([value])::-webkit-datetime-edit-day-field,
.dateInput[value=""]::-webkit-datetime-edit-day-field,
.dateInput:not([value])::-webkit-datetime-edit-year-field,
.dateInput[value=""]::-webkit-datetime-edit-year-field {
  color: transparent !important;
  background: transparent !important;
  width: 0 !important;
  opacity: 0 !important;
  padding: 0 !important;
  margin: 0 !important;
}

/* 日期输入框的日历图标 */
.dateInput::-webkit-calendar-picker-indicator {
  cursor: pointer;
  padding: 4px;
  border-radius: 2px;
  transition: background-color 0.2s ease;
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  opacity: 0.7;
}

.dateInput::-webkit-calendar-picker-indicator:hover {
  background-color: rgba(24, 144, 255, 0.1);
  opacity: 1;
}

/* 完全隐藏默认的日期时间占位符和字段 */
.datetimeInput::-webkit-datetime-edit {
  color: transparent;
  background: transparent;
}

.datetimeInput::-webkit-datetime-edit-text {
  color: transparent;
  padding: 0;
}

.datetimeInput::-webkit-datetime-edit-month-field,
.datetimeInput::-webkit-datetime-edit-day-field,
.datetimeInput::-webkit-datetime-edit-year-field,
.datetimeInput::-webkit-datetime-edit-hour-field,
.datetimeInput::-webkit-datetime-edit-minute-field {
  color: transparent;
  background: transparent;
  border: none;
  padding: 0;
  width: 0;
  opacity: 0;
}

/* 当有值时显示实际的日期时间 */
.datetimeInput[value]:not([value=""])::-webkit-datetime-edit {
  color: #333;
  background: transparent;
}

.datetimeInput[value]:not([value=""])::-webkit-datetime-edit-text {
  color: #333;
  padding: 0 2px;
}

.datetimeInput[value]:not([value=""])::-webkit-datetime-edit-month-field,
.datetimeInput[value]:not([value=""])::-webkit-datetime-edit-day-field,
.datetimeInput[value]:not([value=""])::-webkit-datetime-edit-year-field,
.datetimeInput[value]:not([value=""])::-webkit-datetime-edit-hour-field,
.datetimeInput[value]:not([value=""])::-webkit-datetime-edit-minute-field {
  color: #333;
  background: transparent;
  border: none;
  padding: 0 2px;
  width: auto;
  opacity: 1;
}

/* 聚焦时也显示字段，但只在有值的情况下 */
.datetimeInput:focus[value]:not([value=""])::-webkit-datetime-edit {
  color: #333;
}

.datetimeInput:focus[value]:not([value=""])::-webkit-datetime-edit-text {
  color: #333;
}

.datetimeInput:focus[value]:not([value=""])::-webkit-datetime-edit-month-field,
.datetimeInput:focus[value]:not([value=""])::-webkit-datetime-edit-day-field,
.datetimeInput:focus[value]:not([value=""])::-webkit-datetime-edit-year-field,
.datetimeInput:focus[value]:not([value=""])::-webkit-datetime-edit-hour-field,
.datetimeInput:focus[value]:not([value=""])::-webkit-datetime-edit-minute-field {
  color: #333;
  width: auto;
  opacity: 1;
}

/* 自定义占位符样式 */
.customPlaceholder {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #999;
  font-size: 14px;
  pointer-events: none;
  z-index: 1;
}

/* 额外的隐藏规则，确保所有默认字符都被隐藏 */
.datetimeInput::-webkit-inner-spin-button,
.datetimeInput::-webkit-clear-button {
  display: none;
}

.datetimeInput::-webkit-datetime-edit-fields-wrapper {
  color: transparent;
}

/* 当输入框为空时，完全隐藏所有内容 */
.datetimeInput:not([value])::-webkit-datetime-edit,
.datetimeInput[value=""]::-webkit-datetime-edit {
  color: transparent !important;
  background: transparent !important;
}

.datetimeInput:not([value])::-webkit-datetime-edit-text,
.datetimeInput[value=""]::-webkit-datetime-edit-text,
.datetimeInput:not([value])::-webkit-datetime-edit-month-field,
.datetimeInput[value=""]::-webkit-datetime-edit-month-field,
.datetimeInput:not([value])::-webkit-datetime-edit-day-field,
.datetimeInput[value=""]::-webkit-datetime-edit-day-field,
.datetimeInput:not([value])::-webkit-datetime-edit-year-field,
.datetimeInput[value=""]::-webkit-datetime-edit-year-field,
.datetimeInput:not([value])::-webkit-datetime-edit-hour-field,
.datetimeInput[value=""]::-webkit-datetime-edit-hour-field,
.datetimeInput:not([value])::-webkit-datetime-edit-minute-field,
.datetimeInput[value=""]::-webkit-datetime-edit-minute-field {
  color: transparent !important;
  background: transparent !important;
  width: 0 !important;
  opacity: 0 !important;
  padding: 0 !important;
  margin: 0 !important;
}

.datetimeInput:hover {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
}

.datetimeInput:focus {
  outline: none;
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}



/* 日历图标样式优化 */
.datetimeInput::-webkit-calendar-picker-indicator {
  cursor: pointer;
  padding: 4px;
  border-radius: 2px;
  transition: background-color 0.2s ease;
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  opacity: 0.7;
  background-size: 16px;
}

.datetimeInput::-webkit-calendar-picker-indicator:hover {
  background-color: rgba(24, 144, 255, 0.1);
  opacity: 1;
}

/* 确保整个输入框都可以点击 */
.datetimeInput::-webkit-datetime-edit {
  padding: 0;
  cursor: pointer;
  margin-right: 30px;
}

.datetimeInput::-webkit-datetime-edit-fields-wrapper {
  padding: 0;
  cursor: pointer;
}

.datetimeInput::-webkit-datetime-edit-text {
  padding: 0 2px;
  cursor: pointer;
}

.datetimeInput::-webkit-datetime-edit-month-field,
.datetimeInput::-webkit-datetime-edit-day-field,
.datetimeInput::-webkit-datetime-edit-year-field,
.datetimeInput::-webkit-datetime-edit-hour-field,
.datetimeInput::-webkit-datetime-edit-minute-field {
  padding: 0 2px;
  cursor: pointer;
}

.dateSeparator {
  color: #666;
  font-size: 14px;
  margin: 0 4px;
}

.refreshButton {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border: 1px solid #d9d9d9;
  background: white;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-left: auto;
  color: #666;
}

.refreshButton:hover {
  border-color: #40a9ff;
  color: #40a9ff;
  background: #f0f8ff;
}

.tasksTable {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.tableHeader {
  display: grid;
  grid-template-columns: 1fr 1fr 2fr 2fr 1fr 2fr;
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
  font-weight: 500;
  color: #333;
}

.headerCell {
  padding: 16px 12px;
  font-size: 14px;
  text-align: center;
}

.tableBody {
  max-height: 600px;
  overflow-y: auto;
}

.tableRow {
  display: grid;
  grid-template-columns: 1fr 1fr 2fr 2fr 1fr 2fr;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s ease;
}

.tableRow:hover {
  background: #f9f9f9;
}

.tableCell {
  padding: 16px 12px;
  font-size: 14px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

.statusBadge {
  padding: 4px 8px;
  border-radius: 12px;
  color: white;
  font-size: 12px;
  font-weight: 500;
}

.actions {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.actionBtn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  border: 1px solid #d9d9d9;
  background: white;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 60px;
  white-space: nowrap;
}

.actionBtn:hover {
  border-color: #40a9ff;
  color: #40a9ff;
}

.viewBtn:hover {
  border-color: #52c41a;
  color: #52c41a;
}

.editBtn:hover {
  border-color: #faad14;
  color: #faad14;
}

.deleteBtn:hover {
  border-color: #ff4d4f;
  color: #ff4d4f;
}

/* 空状态样式 */
.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  text-align: center;
  background: #fafafa;
  border-radius: 8px;
  margin: 20px;
}

.emptyIcon {
  margin-bottom: 24px;
  opacity: 0.8;
}

.emptyTitle {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
}

.emptyDescription {
  font-size: 14px;
  color: #909399;
  line-height: 1.5;
  max-width: 300px;
}

/* 旋转动画 */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 刷新按钮样式增强 */
.refreshButton {
  transition: all 0.3s ease;
}

.refreshButton:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.refreshButton:disabled {
  cursor: not-allowed !important;
  opacity: 0.6 !important;
}
