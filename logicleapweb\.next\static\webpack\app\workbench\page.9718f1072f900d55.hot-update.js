"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/workbench/page",{

/***/ "(app-pages-browser)/./app/workbench/components/CourseListEditModal.tsx":
/*!**********************************************************!*\
  !*** ./app/workbench/components/CourseListEditModal.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FileText,Plus,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FileText,Plus,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FileText,Plus,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FileText,Plus,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FileText,Plus,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _lib_api_course_management__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api/course-management */ \"(app-pages-browser)/./lib/api/course-management.ts\");\n/* harmony import */ var _lib_api_course__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api/course */ \"(app-pages-browser)/./lib/api/course.ts\");\n/* harmony import */ var _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api/upload */ \"(app-pages-browser)/./lib/api/upload.ts\");\n/* harmony import */ var _lib_api_works__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/api/works */ \"(app-pages-browser)/./lib/api/works.ts\");\n/* harmony import */ var _barrel_optimize_names_Select_antd__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Select!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/select/index.js\");\n/* harmony import */ var logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\");\n/* harmony import */ var _TemplatePickerModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./TemplatePickerModal */ \"(app-pages-browser)/./app/workbench/components/TemplatePickerModal.tsx\");\n/* harmony import */ var _CourseListEditModal_css__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./CourseListEditModal.css */ \"(app-pages-browser)/./app/workbench/components/CourseListEditModal.css\");\n/* harmony import */ var _NewPublishTaskModal_css__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./NewPublishTaskModal.css */ \"(app-pages-browser)/./app/workbench/components/NewPublishTaskModal.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n// 获取系列课程详情\nconst fetchSeriesDetail = async (seriesId)=>{\n    console.log(\"\\uD83D\\uDD0D 获取系列课程详情，seriesId:\", seriesId);\n    console.log(\"\\uD83D\\uDD17 调用API: GET /api/v1/course-management/series/{seriesId}\");\n    const response = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getSeriesDetail(seriesId);\n    console.log(\"\\uD83D\\uDCE1 系列详情API响应:\", response);\n    return response.data;\n};\n// API调用函数 - 获取系列下的所有课程（包含未发布和已发布）\nconst fetchCourseList = async (seriesId)=>{\n    console.log(\"\\uD83D\\uDD0D 获取系列课程列表，seriesId:\", seriesId);\n    console.log(\"\\uD83D\\uDD17 调用API: GET /api/v1/course-management/series/{seriesId}/courses\");\n    // 使用课程管理API获取所有状态的课程\n    const response = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getManagementSeriesCourses(seriesId, {\n        page: 1,\n        pageSize: 100\n    });\n    console.log(\"\\uD83D\\uDCE1 API响应:\", response);\n    return response.data;\n};\n// 获取课程详情\nconst fetchCourseDetail = async (seriesId, courseId)=>{\n    return await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getCourseMarketplaceDetail(seriesId, courseId);\n};\n// 获取课程标签\nconst fetchCourseTags = async ()=>{\n    try {\n        console.log(\"\\uD83D\\uDD0D 开始调用 courseApi.getCourseTags\");\n        const result = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getCourseTags({\n            page: 1,\n            pageSize: 100,\n            status: 1 // 只获取启用的标签\n        });\n        console.log(\"\\uD83D\\uDD0D courseApi.getCourseTags 返回结果:\", result);\n        return result;\n    } catch (error) {\n        console.error(\"\\uD83D\\uDD0D courseApi.getCourseTags 调用失败:\", error);\n        throw error;\n    }\n};\nconst CourseListEditModal = (param)=>{\n    let { isVisible, onClose, onSave, seriesTitle, seriesCoverImage, seriesId = 123 // 默认值，实际使用时应该传入真实的seriesId\n     } = param;\n    var _getSelectedCourse, _getSelectedCourse1, _getSelectedCourse2, _getSelectedCourse3, _courseDetail_contentConfig_video, _courseDetail_contentConfig, _courseDetail_contentConfig_document, _courseDetail_contentConfig1, _courseList_find, _courseList_find1;\n    _s();\n    const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_6__.GetNotification)();\n    const [courseList, setCourseList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [rightPanelType, setRightPanelType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"none\");\n    const [selectedCourseId, setSelectedCourseId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [uploadingFiles, setUploadingFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [isPublishing, setIsPublishing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isCreating, setIsCreating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPublishingSeries, setIsPublishingSeries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [seriesStatus, setSeriesStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0); // 0=草稿，1=已发布，2=已归档\n    // 删除确认弹窗状态\n    const [deleteConfirmVisible, setDeleteConfirmVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [courseToDelete, setCourseToDelete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isDeleting, setIsDeleting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 模板选择弹窗状态\n    const [isTemplatePickerOpen, setIsTemplatePickerOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingTitle, setEditingTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(seriesTitle);\n    const [courseGoals, setCourseGoals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [courseObjectives, setCourseObjectives] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [projectMembers, setProjectMembers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // 作品相关状态\n    const [works, setWorks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loadingWorks, setLoadingWorks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedWorkIds, setSelectedWorkIds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // 懒加载相关状态\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [hasMoreWorks, setHasMoreWorks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [loadingMoreWorks, setLoadingMoreWorks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pageSize = 5; // 每次加载5个作品\n    // 课程标签相关状态\n    const [courseTags, setCourseTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedTags, setSelectedTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [tagsLoading, setTagsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 课程详细编辑状态\n    const [courseDetail, setCourseDetail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: \"\",\n        description: \"\",\n        coverImage: \"\",\n        videoUrl: \"\",\n        videoName: \"\",\n        isVideoEnabled: false,\n        attachmentUrl: \"\",\n        attachmentName: \"\",\n        isAttachmentEnabled: false,\n        teachingMaterials: [],\n        // 支持teachingInfo结构\n        teachingInfo: [],\n        // 支持contentConfig结构\n        contentConfig: {\n            hasVideo: 0,\n            hasDocument: 0,\n            hasAudio: 0,\n            video: {\n                url: \"\",\n                name: \"\"\n            },\n            document: {\n                url: \"\",\n                name: \"\"\n            },\n            audio: {\n                url: \"\",\n                name: \"\"\n            }\n        },\n        courseContent: {\n            topic: \"\",\n            content: \"\"\n        },\n        isOneKeyOpen: false,\n        isDistributionEnabled: false,\n        distributionReward: \"\",\n        selectedTemplate: \"\",\n        isDistributionWater: false,\n        requiredEnergy: \"\",\n        energyAmount: \"\",\n        isDistributionLimit: false,\n        distributionConditions: {\n            inviteCount: \"\",\n            taskCount: \"\",\n            experience: \"\"\n        },\n        isDistributionTime: false,\n        distributionTimeConditions: {\n            startTime: \"\",\n            endTime: \"\"\n        },\n        distributionMaterials: [],\n        // 任务配置相关状态\n        taskConfig: {\n            taskName: \"\",\n            taskDuration: \"\",\n            taskDescription: \"\",\n            selfAssessmentItems: [\n                \"\"\n            ],\n            referenceWorks: [],\n            referenceResources: []\n        }\n    });\n    // 获取课程列表数据\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isVisible && seriesId) {\n            // 检查用户登录状态\n            const token = localStorage.getItem(\"token\");\n            console.log(\"\\uD83D\\uDD10 检查登录状态，token存在:\", !!token);\n            console.log(\"\\uD83D\\uDD0D seriesId:\", seriesId);\n            if (!token) {\n                console.error(\"❌ 用户未登录，无法获取课程列表\");\n                // 设置空列表，显示空状态\n                setCourseList([]);\n                setLoading(false);\n                return;\n            }\n            loadCourseList();\n            loadCourseTags();\n            loadSeriesDetail();\n            loadUserWorks();\n        }\n    }, [\n        isVisible,\n        seriesId\n    ]);\n    const loadCourseList = async ()=>{\n        try {\n            setLoading(true);\n            console.log(\"\\uD83D\\uDD0D 开始加载课程列表，seriesId:\", seriesId);\n            const response = await fetchCourseList(seriesId);\n            console.log(\"\\uD83D\\uDCE1 API响应:\", response);\n            if (response.code === 200) {\n                console.log(\"✅ 课程列表数据:\", response.data);\n                const courses = response.data.list || [];\n                console.log(\"✅ 解析的课程数组:\", courses);\n                console.log(\"\\uD83D\\uDCCA 课程ID详情:\", courses.map((c)=>({\n                        id: c.id,\n                        type: typeof c.id,\n                        title: c.title,\n                        status: c.status\n                    })));\n                console.log(\"\\uD83D\\uDCCA 课程状态统计:\", {\n                    total: courses.length,\n                    draft: courses.filter((c)=>c.status === 0).length,\n                    published: courses.filter((c)=>c.status === 1).length\n                });\n                setCourseList(courses);\n            } else {\n                console.error(\"❌ API返回错误:\", response);\n                setCourseList([]);\n            }\n        } catch (error) {\n            var _error_response, _error_response1, _error_response2;\n            console.error(\"❌ 加载课程列表失败:\", error);\n            // 检查是否是认证错误\n            if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 401) {\n                console.error(\"\\uD83D\\uDD10 认证失败，用户未登录或token已过期\");\n            } else if (((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status) === 403) {\n                console.error(\"\\uD83D\\uDEAB 权限不足，无法访问该系列课程\");\n            } else if (((_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.status) === 404) {\n                console.error(\"\\uD83D\\uDCED 系列课程不存在，seriesId:\", seriesId);\n            } else {\n                console.error(\"\\uD83D\\uDD27 其他错误:\", error.message);\n            }\n            setCourseList([]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 加载系列课程详情\n    const loadSeriesDetail = async ()=>{\n        try {\n            console.log(\"\\uD83D\\uDD0D 开始加载系列课程详情，seriesId:\", seriesId);\n            const response = await fetchSeriesDetail(seriesId);\n            console.log(\"\\uD83D\\uDCE1 系列详情响应:\", response);\n            if (response.code === 200 && response.data) {\n                const seriesData = response.data;\n                console.log(\"✅ 系列课程详情:\", seriesData);\n                setSeriesStatus(seriesData.status || 0);\n                console.log(\"\\uD83D\\uDCCA 系列课程状态:\", seriesData.status, \"(0=草稿，1=已发布，2=已归档)\");\n            } else {\n                console.error(\"❌ 获取系列详情失败:\", response.message);\n            }\n        } catch (error) {\n            console.error(\"❌ 加载系列详情异常:\", error);\n        }\n    };\n    // 加载用户作品数据\n    const loadUserWorks = async ()=>{\n        try {\n            var _response_data;\n            setLoadingWorks(true);\n            // 从localStorage获取用户ID\n            const userId = localStorage.getItem(\"userId\") || \"2896\"; // 默认使用2896\n            const response = await _lib_api_works__WEBPACK_IMPORTED_MODULE_5__.worksApi.getTeacherWorks(Number(userId), 1, 1000);\n            // 检查多种可能的数据结构\n            let worksList = [];\n            if (((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.code) === 200) {\n                var _response_data_data, _response_data1, _response_data2;\n                // 情况1: response.data.data.list\n                if ((_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : (_response_data_data = _response_data1.data) === null || _response_data_data === void 0 ? void 0 : _response_data_data.list) {\n                    worksList = response.data.data.list;\n                } else if (Array.isArray((_response_data2 = response.data) === null || _response_data2 === void 0 ? void 0 : _response_data2.data)) {\n                    worksList = response.data.data;\n                } else if (Array.isArray(response.data)) {\n                    worksList = response.data;\n                }\n            } else if (Array.isArray(response)) {\n                worksList = response;\n            }\n            setWorks(worksList);\n        } catch (error) {\n            console.error(\"加载作品数据失败:\", error);\n            setWorks([]);\n        } finally{\n            setLoadingWorks(false);\n        }\n    };\n    // 处理作品选择\n    const handleWorkSelect = (workId)=>{\n        if (selectedWorkIds.includes(workId)) {\n            // 取消选中\n            setSelectedWorkIds((prev)=>prev.filter((id)=>id !== workId));\n        } else {\n            // 选中\n            setSelectedWorkIds((prev)=>[\n                    ...prev,\n                    workId\n                ]);\n        }\n    };\n    // 鼠标滚轮事件处理 - 将垂直滚轮转换为水平滚动\n    const handleWorksWheel = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e)=>{\n        // 阻止默认的垂直滚动\n        e.preventDefault();\n        const container = e.currentTarget;\n        const scrollAmount = e.deltaY; // 获取滚轮的垂直滚动量\n        // 将垂直滚动转换为水平滚动\n        container.scrollLeft += scrollAmount;\n    }, []);\n    // 加载课程标签\n    const loadCourseTags = async ()=>{\n        try {\n            setTagsLoading(true);\n            console.log(\"\\uD83C\\uDFF7️ 开始加载课程标签\");\n            const response = await fetchCourseTags();\n            console.log(\"\\uD83D\\uDCE1 标签API完整响应:\", response);\n            // 检查响应结构\n            if (response && response.data) {\n                console.log(\"\\uD83D\\uDCCA 响应数据:\", response.data);\n                let tags = [];\n                // 处理标准的API响应格式 (response.data.list) - 根据实际API响应\n                if (response.data.list && Array.isArray(response.data.list)) {\n                    tags = response.data.list;\n                    console.log(\"✅ 从 data.list 解析到标签:\", tags.length, \"个\");\n                } else if (Array.isArray(response.data)) {\n                    tags = response.data;\n                    console.log(\"✅ 从 data 数组解析到标签:\", tags.length, \"个\");\n                } else if (response.data.data && response.data.data.list && Array.isArray(response.data.data.list)) {\n                    tags = response.data.data.list;\n                    console.log(\"✅ 从 data.data.list 解析到标签:\", tags.length, \"个\");\n                }\n                // 验证标签数据格式\n                console.log(\"\\uD83D\\uDD0D 原始标签数据:\", tags);\n                console.log(\"\\uD83D\\uDD0D 标签数据类型检查:\");\n                tags.forEach((tag, index)=>{\n                    var _tag_name;\n                    console.log(\"标签\".concat(index, \":\"), {\n                        tag,\n                        hasTag: !!tag,\n                        idType: typeof (tag === null || tag === void 0 ? void 0 : tag.id),\n                        nameType: typeof (tag === null || tag === void 0 ? void 0 : tag.name),\n                        nameValue: tag === null || tag === void 0 ? void 0 : tag.name,\n                        nameNotEmpty: (tag === null || tag === void 0 ? void 0 : (_tag_name = tag.name) === null || _tag_name === void 0 ? void 0 : _tag_name.trim()) !== \"\"\n                    });\n                });\n                const validTags = tags.filter((tag)=>{\n                    const isValid = tag && typeof tag.id === \"number\" && typeof tag.name === \"string\" && tag.name.trim() !== \"\";\n                    if (!isValid) {\n                        console.log(\"❌ 无效标签:\", tag, {\n                            hasTag: !!tag,\n                            idType: typeof (tag === null || tag === void 0 ? void 0 : tag.id),\n                            nameType: typeof (tag === null || tag === void 0 ? void 0 : tag.name),\n                            nameValue: tag === null || tag === void 0 ? void 0 : tag.name\n                        });\n                    }\n                    return isValid;\n                });\n                console.log(\"✅ 有效标签数量:\", validTags.length);\n                console.log(\"✅ 有效标签详情:\", validTags);\n                if (validTags.length > 0) {\n                    setCourseTags(validTags);\n                    console.log(\"✅ 成功设置真实标签数据\");\n                    return;\n                } else {\n                    console.warn(\"⚠️ 没有有效的标签数据\");\n                }\n            } else {\n                console.warn(\"⚠️ API响应格式不正确:\", response);\n            }\n            // 如果没有真实数据，设置空数组\n            console.log(\"\\uD83D\\uDCED 没有标签数据，设置空数组\");\n            setCourseTags([]);\n        } catch (error) {\n            var _error_response, _error_response1, _error_response2;\n            console.error(\"❌ 加载课程标签失败:\", error);\n            console.error(\"❌ 错误详情:\", {\n                message: error.message,\n                status: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status,\n                statusText: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.statusText,\n                data: (_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.data\n            });\n            // 发生错误时设置空数组\n            setCourseTags([]);\n        } finally{\n            setTagsLoading(false);\n        }\n    };\n    // 添加新课程\n    const addNewCourse = ()=>{\n        const newCourse = {\n            id: Date.now(),\n            seriesId: seriesId,\n            title: \"第\".concat(courseList.length + 1, \"课 - 新课时\"),\n            description: \"\",\n            coverImage: \"\",\n            orderIndex: courseList.length + 1,\n            status: 0,\n            statusLabel: \"草稿\",\n            hasVideo: 0,\n            hasDocument: 0,\n            hasAudio: 0,\n            videoDuration: 0,\n            videoDurationLabel: \"\",\n            videoName: \"\",\n            firstTeachingTitle: \"\",\n            resourcesCount: 0,\n            createdAt: new Date().toISOString(),\n            updatedAt: new Date().toISOString()\n        };\n        setCourseList([\n            ...courseList,\n            newCourse\n        ]);\n        // 自动选中新添加的课程\n        showCoursePanel(newCourse.id);\n    };\n    // 显示删除确认弹窗\n    const showDeleteConfirm = (id)=>{\n        setCourseToDelete(id);\n        setDeleteConfirmVisible(true);\n    };\n    // 确认删除课程\n    const confirmDeleteCourse = async ()=>{\n        if (!courseToDelete) return;\n        try {\n            setIsDeleting(true);\n            // 调用删除API\n            await _lib_api_course_management__WEBPACK_IMPORTED_MODULE_2__.courseManagementApi.deleteCourse(courseToDelete);\n            // 从列表中移除课程\n            setCourseList(courseList.filter((course)=>course.id !== courseToDelete));\n            // 如果删除的是当前选中的课程，清空右侧面板\n            if (selectedCourseId === courseToDelete) {\n                setRightPanelType(\"none\");\n                setSelectedCourseId(null);\n            }\n            // 关闭确认弹窗\n            setDeleteConfirmVisible(false);\n            setCourseToDelete(null);\n            // 显示成功提示\n            notification.success(\"课程已成功删除\");\n        } catch (error) {\n            console.error(\"删除课程失败:\", error);\n            notification.error(\"删除课程失败，请重试\");\n        } finally{\n            setIsDeleting(false);\n        }\n    };\n    // 取消删除\n    const cancelDelete = ()=>{\n        if (isDeleting) return; // 正在删除时不允许取消\n        setDeleteConfirmVisible(false);\n        setCourseToDelete(null);\n    };\n    // 更新课程标题\n    const updateCourseTitle = (id, newTitle)=>{\n        setCourseList(courseList.map((course)=>course.id === id ? {\n                ...course,\n                title: newTitle\n            } : course));\n    };\n    // 处理课程封面上传\n    const handleCoverUpload = async (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (file) {\n            // 检查文件类型\n            const allowedTypes = [\n                \"image/jpeg\",\n                \"image/jpg\",\n                \"image/png\",\n                \"image/gif\"\n            ];\n            if (!allowedTypes.includes(file.type)) {\n                notification.error(\"请选择 JPG、PNG 或 GIF 格式的图片文件\");\n                return;\n            }\n            // 检查文件大小 (10MB)\n            if (file.size > 10 * 1024 * 1024) {\n                notification.error(\"文件大小不能超过 10MB\");\n                return;\n            }\n            try {\n                console.log(\"\\uD83D\\uDCE4 开始上传课程封面:\", file.name);\n                // 添加到上传中的文件列表\n                setUploadingFiles((prev)=>new Set(prev).add(\"cover\"));\n                // 先显示预览图片\n                const previewUrl = URL.createObjectURL(file);\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        coverImage: previewUrl\n                    }));\n                // 上传到OSS\n                const imageUrl = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n                console.log(\"✅ 课程封面上传成功:\", imageUrl);\n                // 更新课程详情中的封面为真实URL\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        coverImage: imageUrl\n                    }));\n                // 同时更新课程列表中的封面\n                if (selectedCourseId) {\n                    setCourseList((prev)=>prev.map((course)=>course.id === selectedCourseId ? {\n                                ...course,\n                                coverImage: imageUrl\n                            } : course));\n                }\n                alert(\"课程封面上传成功！\");\n            } catch (error) {\n                console.error(\"❌ 课程封面上传失败:\", error);\n                notification.error(\"课程封面上传失败，请重试\");\n                // 上传失败时清除预览图片\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        coverImage: \"\"\n                    }));\n            } finally{\n                // 从上传中的文件列表移除\n                setUploadingFiles((prev)=>{\n                    const newSet = new Set(prev);\n                    newSet.delete(\"cover\");\n                    return newSet;\n                });\n            }\n        }\n    };\n    // 处理视频上传\n    const handleVideoUpload = async (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (file) {\n            // 检查文件类型\n            const allowedTypes = [\n                \"video/mp4\",\n                \"video/avi\",\n                \"video/mov\",\n                \"video/wmv\",\n                \"video/flv\"\n            ];\n            if (!allowedTypes.includes(file.type)) {\n                alert(\"请选择 MP4、AVI、MOV、WMV 或 FLV 格式的视频文件\");\n                return;\n            }\n            // 检查文件大小 (100MB)\n            if (file.size > 100 * 1024 * 1024) {\n                alert(\"视频文件大小不能超过 100MB\");\n                return;\n            }\n            try {\n                console.log(\"\\uD83D\\uDCE4 开始上传课程视频:\", file.name);\n                // 添加到上传中的文件列表\n                setUploadingFiles((prev)=>new Set(prev).add(\"video\"));\n                // 先显示预览视频\n                const previewUrl = URL.createObjectURL(file);\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        contentConfig: {\n                            ...prev.contentConfig,\n                            video: {\n                                url: previewUrl,\n                                name: file.name\n                            }\n                        }\n                    }));\n                // 上传到OSS\n                const videoUrl = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n                console.log(\"✅ 课程视频上传成功:\", videoUrl);\n                // 更新课程详情中的视频信息为真实URL\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        contentConfig: {\n                            ...prev.contentConfig,\n                            video: {\n                                url: videoUrl,\n                                name: file.name\n                            }\n                        }\n                    }));\n                // 同时更新课程列表中的视频信息\n                if (selectedCourseId) {\n                    setCourseList((prev)=>prev.map((course)=>course.id === selectedCourseId ? {\n                                ...course,\n                                contentConfig: {\n                                    ...course.contentConfig,\n                                    video: {\n                                        url: videoUrl,\n                                        name: file.name\n                                    }\n                                }\n                            } : course));\n                }\n                alert(\"课程视频上传成功！\");\n            } catch (error) {\n                console.error(\"❌ 课程视频上传失败:\", error);\n                alert(\"课程视频上传失败，请重试\");\n                // 上传失败时清除视频信息\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        contentConfig: {\n                            ...prev.contentConfig,\n                            video: {\n                                url: \"\",\n                                name: \"\"\n                            }\n                        }\n                    }));\n            } finally{\n                // 从上传中的文件列表移除\n                setUploadingFiles((prev)=>{\n                    const newSet = new Set(prev);\n                    newSet.delete(\"video\");\n                    return newSet;\n                });\n            }\n        }\n    };\n    // 触发视频文件选择\n    const triggerVideoUpload = ()=>{\n        const input = document.createElement(\"input\");\n        input.type = \"file\";\n        input.accept = \"video/mp4,video/avi,video/mov,video/wmv,video/flv\";\n        input.onchange = (e)=>handleVideoUpload(e);\n        input.click();\n    };\n    // 处理附件上传\n    const handleAttachmentUpload = async (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (file) {\n            // 检查文件类型\n            const allowedTypes = [\n                \"application/pdf\",\n                \"application/msword\",\n                \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\",\n                \"application/vnd.ms-excel\",\n                \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\",\n                \"application/vnd.ms-powerpoint\",\n                \"application/vnd.openxmlformats-officedocument.presentationml.presentation\",\n                \"text/plain\",\n                \"image/jpeg\",\n                \"image/png\",\n                \"image/gif\"\n            ];\n            if (!allowedTypes.includes(file.type)) {\n                alert(\"请选择支持的文件格式：PDF、DOC、DOCX、XLS、XLSX、PPT、PPTX、TXT、JPG、PNG、GIF\");\n                return;\n            }\n            // 检查文件大小 (10MB)\n            if (file.size > 10 * 1024 * 1024) {\n                alert(\"文件大小不能超过 10MB\");\n                return;\n            }\n            try {\n                console.log(\"\\uD83D\\uDCE4 开始上传课程附件:\", file.name);\n                // 添加到上传中的文件列表\n                setUploadingFiles((prev)=>new Set(prev).add(\"document\"));\n                // 上传到OSS\n                const documentUrl = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n                console.log(\"✅ 课程附件上传成功:\", documentUrl);\n                // 更新课程详情中的附件信息\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        contentConfig: {\n                            ...prev.contentConfig,\n                            hasDocument: 1,\n                            document: {\n                                url: documentUrl,\n                                name: file.name\n                            }\n                        }\n                    }));\n                alert(\"课程附件上传成功！\");\n            } catch (error) {\n                console.error(\"❌ 课程附件上传失败:\", error);\n                alert(\"课程附件上传失败，请重试\");\n            } finally{\n                // 从上传中的文件列表移除\n                setUploadingFiles((prev)=>{\n                    const newSet = new Set(prev);\n                    newSet.delete(\"document\");\n                    return newSet;\n                });\n            }\n        }\n    };\n    // 触发附件文件选择\n    const triggerAttachmentUpload = ()=>{\n        const input = document.createElement(\"input\");\n        input.type = \"file\";\n        input.accept = \".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.jpg,.png,.gif\";\n        input.onchange = (e)=>handleAttachmentUpload(e);\n        input.click();\n    };\n    // 处理教学附件上传\n    const handleTeachingMaterialUpload = async (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (file) {\n            // 检查文件类型\n            const allowedTypes = [\n                \"application/pdf\",\n                \"application/msword\",\n                \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\",\n                \"application/vnd.ms-excel\",\n                \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\",\n                \"application/vnd.ms-powerpoint\",\n                \"application/vnd.openxmlformats-officedocument.presentationml.presentation\",\n                \"text/plain\",\n                \"image/jpeg\",\n                \"image/png\",\n                \"image/gif\"\n            ];\n            if (!allowedTypes.includes(file.type)) {\n                alert(\"请选择支持的文件格式：PDF、DOC、DOCX、XLS、XLSX、PPT、PPTX、TXT、JPG、PNG、GIF\");\n                return;\n            }\n            // 检查文件大小 (10MB)\n            if (file.size > 10 * 1024 * 1024) {\n                alert(\"文件大小不能超过 10MB\");\n                return;\n            }\n            try {\n                console.log(\"\\uD83D\\uDCE4 开始上传教学材料:\", file.name);\n                // 添加到上传中的文件列表\n                setUploadingFiles((prev)=>new Set(prev).add(\"teaching-\".concat(Date.now())));\n                // 上传到OSS\n                const materialUrl = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n                console.log(\"✅ 教学材料上传成功:\", materialUrl);\n                // 添加到教学附件列表\n                const newMaterial = {\n                    type: file.type,\n                    name: file.name,\n                    url: materialUrl\n                };\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        teachingMaterials: [\n                            ...prev.teachingMaterials,\n                            newMaterial\n                        ]\n                    }));\n                alert(\"教学材料上传成功！\");\n            } catch (error) {\n                console.error(\"❌ 教学材料上传失败:\", error);\n                alert(\"教学材料上传失败，请重试\");\n            } finally{\n                // 从上传中的文件列表移除\n                setUploadingFiles((prev)=>{\n                    const newSet = new Set(prev);\n                    // 移除所有teaching-开头的项目\n                    Array.from(newSet).forEach((item)=>{\n                        if (item.startsWith(\"teaching-\")) {\n                            newSet.delete(item);\n                        }\n                    });\n                    return newSet;\n                });\n            }\n        }\n    };\n    // 触发教学附件文件选择\n    const triggerTeachingMaterialUpload = ()=>{\n        const input = document.createElement(\"input\");\n        input.type = \"file\";\n        input.accept = \".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.jpg,.png,.gif\";\n        input.onchange = (e)=>handleTeachingMaterialUpload(e);\n        input.click();\n    };\n    // 删除教学附件\n    const removeTeachingMaterial = (index)=>{\n        setCourseDetail((prev)=>({\n                ...prev,\n                teachingMaterials: prev.teachingMaterials.filter((_, i)=>i !== index)\n            }));\n    };\n    // 课程内容管理函数\n    const addTeachingInfoItem = ()=>{\n        setCourseDetail((prev)=>({\n                ...prev,\n                teachingInfo: [\n                    ...prev.teachingInfo,\n                    {\n                        title: \"\",\n                        content: \"\"\n                    }\n                ]\n            }));\n    };\n    const removeTeachingInfoItem = (index)=>{\n        setCourseDetail((prev)=>({\n                ...prev,\n                teachingInfo: prev.teachingInfo.filter((_, i)=>i !== index)\n            }));\n    };\n    const updateTeachingInfoTitle = (index, title)=>{\n        setCourseDetail((prev)=>{\n            const newTeachingInfo = [\n                ...prev.teachingInfo\n            ];\n            newTeachingInfo[index] = {\n                ...newTeachingInfo[index],\n                title\n            };\n            return {\n                ...prev,\n                teachingInfo: newTeachingInfo\n            };\n        });\n    };\n    const updateTeachingInfoContent = (index, content)=>{\n        setCourseDetail((prev)=>{\n            const newTeachingInfo = [\n                ...prev.teachingInfo\n            ];\n            newTeachingInfo[index] = {\n                ...newTeachingInfo[index],\n                content\n            };\n            return {\n                ...prev,\n                teachingInfo: newTeachingInfo\n            };\n        });\n    };\n    // 将UI格式的teachingInfo转换为API格式\n    const convertTeachingInfoForAPI = (teachingInfo)=>{\n        return teachingInfo.map((info)=>({\n                title: info.title,\n                content: info.content.split(\"\\n\").filter((line)=>line.trim()) // 按换行分割并过滤空行\n            }));\n    };\n    // 检查是否有未上传完成的文件（blob URL）\n    const checkForBlobUrls = (course)=>{\n        var _course_contentConfig_video, _course_contentConfig, _course_contentConfig_document, _course_contentConfig1;\n        const issues = [];\n        // 检查封面图片\n        if (course.coverImage && course.coverImage.startsWith(\"blob:\")) {\n            issues.push(\"课程封面图片\");\n        }\n        // 检查视频文件\n        if (((_course_contentConfig = course.contentConfig) === null || _course_contentConfig === void 0 ? void 0 : (_course_contentConfig_video = _course_contentConfig.video) === null || _course_contentConfig_video === void 0 ? void 0 : _course_contentConfig_video.url) && course.contentConfig.video.url.startsWith(\"blob:\")) {\n            issues.push(\"课程视频\");\n        }\n        // 检查文档附件\n        if (((_course_contentConfig1 = course.contentConfig) === null || _course_contentConfig1 === void 0 ? void 0 : (_course_contentConfig_document = _course_contentConfig1.document) === null || _course_contentConfig_document === void 0 ? void 0 : _course_contentConfig_document.url) && course.contentConfig.document.url.startsWith(\"blob:\")) {\n            issues.push(\"课程文档\");\n        }\n        // 检查教学材料\n        if (course.additionalResources) {\n            course.additionalResources.forEach((resource, index)=>{\n                if (resource.url && resource.url.startsWith(\"blob:\")) {\n                    issues.push(\"教学材料\".concat(index + 1));\n                }\n            });\n        }\n        return issues;\n    };\n    // 保存单个课程到后端\n    const saveCourse = async (course)=>{\n        try {\n            var _course_contentConfig_video, _course_contentConfig, _course_contentConfig_document, _course_contentConfig1;\n            // 验证必要的数据\n            if (!course.title || course.title.trim() === \"\") {\n                throw new Error(\"课程标题不能为空\");\n            }\n            // 检查是否有未上传完成的文件\n            const blobIssues = checkForBlobUrls(course);\n            if (blobIssues.length > 0) {\n                throw new Error(\"以下文件尚未上传完成，请等待上传完成后再保存：\".concat(blobIssues.join(\"、\")));\n            }\n            // 准备课程数据\n            const courseData = {\n                seriesId: seriesId,\n                title: course.title,\n                description: course.description || \"\",\n                coverImage: course.coverImage || \"\",\n                hasVideo: ((_course_contentConfig = course.contentConfig) === null || _course_contentConfig === void 0 ? void 0 : (_course_contentConfig_video = _course_contentConfig.video) === null || _course_contentConfig_video === void 0 ? void 0 : _course_contentConfig_video.url) ? 1 : 0,\n                hasDocument: ((_course_contentConfig1 = course.contentConfig) === null || _course_contentConfig1 === void 0 ? void 0 : (_course_contentConfig_document = _course_contentConfig1.document) === null || _course_contentConfig_document === void 0 ? void 0 : _course_contentConfig_document.url) ? 1 : 0,\n                hasAudio: 0,\n                videoDuration: 0,\n                contentConfig: course.contentConfig || {},\n                teachingInfo: convertTeachingInfoForAPI(course.teachingInfo || []),\n                additionalResources: course.additionalResources || [],\n                orderIndex: course.orderIndex || 1\n            };\n            console.log(\"\\uD83D\\uDCBE 准备保存课程数据:\", courseData);\n            let result;\n            // 判断是新课程还是更新课程\n            if (course.id && course.id > 1000000) {\n                // 新课程，使用创建API\n                console.log(\"\\uD83D\\uDCE4 创建新课程\");\n                const { data: response } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.createCourse(courseData);\n                result = response;\n                console.log(\"✅ 课程创建成功\");\n            } else {\n                // 现有课程，使用更新API\n                console.log(\"\\uD83D\\uDCE4 更新现有课程:\", course.id);\n                const { data: response } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.updateCourse(course.id, courseData);\n                result = response;\n                console.log(\"✅ 课程更新成功\");\n            }\n            return result;\n        } catch (error) {\n            console.error(\"❌ 保存课程失败:\", error);\n            throw error;\n        }\n    };\n    // 发布选中的课程\n    const handlePublishCourse = async ()=>{\n        if (!selectedCourseId) {\n            alert(\"请先选择要发布的课程\");\n            return;\n        }\n        const selectedCourse = courseList.find((course)=>course.id === selectedCourseId);\n        if (!selectedCourse) {\n            alert(\"未找到选中的课程\");\n            return;\n        }\n        // 检查课程是否已经发布\n        if (selectedCourse.status === 1) {\n            alert(\"该课程已经发布，无需重复发布\");\n            return;\n        }\n        try {\n            setIsPublishing(true);\n            console.log(\"\\uD83D\\uDCE4 开始发布课程\");\n            console.log(\"\\uD83D\\uDCE4 selectedCourseId:\", selectedCourseId, \"类型:\", typeof selectedCourseId);\n            console.log(\"\\uD83D\\uDCE4 课程信息:\", selectedCourse);\n            console.log(\"\\uD83D\\uDCE4 课程ID字段:\", selectedCourse.id, \"类型:\", typeof selectedCourse.id);\n            console.log(\"\\uD83D\\uDCE4 课程列表中的所有ID:\", courseList.map((c)=>({\n                    id: c.id,\n                    type: typeof c.id,\n                    title: c.title\n                })));\n            // 确保使用正确的课程ID\n            const courseIdToPublish = selectedCourse.id;\n            console.log(\"\\uD83D\\uDCE4 即将发布的课程ID:\", courseIdToPublish, \"类型:\", typeof courseIdToPublish);\n            const { data: response } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.publishCourse(courseIdToPublish);\n            if (response.code === 200) {\n                console.log(\"✅ 课程发布成功:\", response.data);\n                alert(\"课程发布成功！\");\n                // 刷新课程列表以更新状态\n                await loadCourseList();\n            } else {\n                console.error(\"❌ 发布课程失败:\", response.message);\n                alert(response.message || \"发布课程失败\");\n            }\n        } catch (error) {\n            var _error_response, _error_response_data, _error_response1;\n            console.error(\"❌ 发布课程失败:\", error);\n            console.error(\"❌ 错误详情:\", (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data);\n            // 处理具体的错误信息\n            if ((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : (_error_response_data = _error_response1.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) {\n                alert(error.response.data.message);\n            } else if (error.message) {\n                alert(error.message);\n            } else {\n                alert(\"发布课程失败，请重试\");\n            }\n        } finally{\n            setIsPublishing(false);\n        }\n    };\n    // 保存课程列表\n    const handleSave = async ()=>{\n        try {\n            setIsCreating(true);\n            // 检查是否有文件正在上传\n            if (uploadingFiles.size > 0) {\n                alert(\"有文件正在上传中，请等待上传完成后再保存\");\n                setIsCreating(false);\n                return;\n            }\n            console.log(\"\\uD83D\\uDCBE 开始保存课程列表\");\n            // 如果有选中的课程且在编辑状态，先保存当前课程\n            if (selectedCourseId && rightPanelType === \"course\") {\n                const selectedCourse = courseList.find((c)=>c.id === selectedCourseId);\n                if (selectedCourse) {\n                    var _courseDetail_teachingMaterials;\n                    // 更新课程数据\n                    const updatedCourse = {\n                        ...selectedCourse,\n                        title: courseDetail.title,\n                        description: courseDetail.description,\n                        coverImage: courseDetail.coverImage,\n                        hasVideo: courseDetail.isVideoEnabled ? 1 : 0,\n                        hasDocument: courseDetail.isAttachmentEnabled ? 1 : 0,\n                        contentConfig: courseDetail.contentConfig,\n                        teachingInfo: courseDetail.teachingInfo,\n                        additionalResources: ((_courseDetail_teachingMaterials = courseDetail.teachingMaterials) === null || _courseDetail_teachingMaterials === void 0 ? void 0 : _courseDetail_teachingMaterials.map((material)=>({\n                                title: material.name,\n                                url: material.url,\n                                description: material.name\n                            }))) || []\n                    };\n                    // 先更新课程列表中的数据\n                    setCourseList((prev)=>prev.map((course)=>course.id === selectedCourseId ? updatedCourse : course));\n                    // 然后保存到后端\n                    await saveCourse(updatedCourse);\n                }\n            }\n            // 保存系列课程信息\n            const data = {\n                title: editingTitle,\n                courseGoals,\n                courseObjectives,\n                courseList\n            };\n            onSave(data);\n            onClose();\n            console.log(\"✅ 课程列表保存完成\");\n        } catch (error) {\n            console.error(\"❌ 保存失败:\", error);\n            alert(\"保存失败: \".concat(error.message || \"请重试\"));\n        } finally{\n            setIsCreating(false);\n        }\n    };\n    // 发布系列课程\n    const handlePublish = async ()=>{\n        // 如果系列已发布，不执行任何操作\n        if (seriesStatus === 1) {\n            return;\n        }\n        try {\n            setIsPublishingSeries(true);\n            // 检查是否有课程\n            if (courseList.length === 0) {\n                alert(\"发布失败：课程系列中至少需要包含一个课程\");\n                return;\n            }\n            console.log(\"\\uD83D\\uDCE4 开始发布系列课程，系列ID:\", seriesId);\n            const { data: response } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.publishCourseSeries(seriesId);\n            if (response.code === 200) {\n                console.log(\"✅ 系列课程发布成功:\", response.data);\n                // 构建成功消息\n                const publishData = response.data;\n                let successMessage = '系列课程\"'.concat(publishData.title, '\"发布成功！');\n                // 如果有发布统计信息，添加到消息中\n                if (publishData.publishStats) {\n                    const stats = publishData.publishStats;\n                    successMessage += \"\\n\\n发布统计：\\n• 总课程数：\".concat(publishData.totalCourses, \"\\n• 已发布课程：\").concat(publishData.publishedCourses, \"\\n• 视频课程：\").concat(stats.videoCourseCount, \"个\\n• 文档课程：\").concat(stats.documentCourseCount, \"个\\n• 总资源数：\").concat(stats.totalResourcesCount, \"个\");\n                    if (stats.totalVideoDuration > 0) {\n                        const durationMinutes = Math.round(stats.totalVideoDuration / 60);\n                        successMessage += \"\\n• 视频总时长：\".concat(durationMinutes, \"分钟\");\n                    }\n                }\n                alert(successMessage);\n                // 更新系列状态为已发布\n                setSeriesStatus(1);\n                // 刷新课程列表以更新状态\n                await loadCourseList();\n                // 通知父组件刷新数据\n                onSave({\n                    type: \"publish_series\",\n                    seriesId: seriesId,\n                    message: \"系列课程发布成功\"\n                });\n            } else {\n                console.error(\"❌ 发布系列课程失败:\", response.message);\n                alert(response.message || \"发布系列课程失败\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"❌ 发布系列课程出错:\", error);\n            // 处理具体的错误信息\n            let errorMessage = \"发布系列课程失败\";\n            if ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) {\n                errorMessage = error.response.data.message;\n            } else if (error.message) {\n                errorMessage = error.message;\n            }\n            alert(errorMessage);\n        } finally{\n            setIsPublishingSeries(false);\n        }\n    };\n    // 退出编辑模式 - 保存数据并关闭\n    const handleExitEdit = ()=>{\n        handleSave();\n    };\n    // 显示设置面板\n    const showSettingsPanel = ()=>{\n        setRightPanelType(\"settings\");\n        setSelectedCourseId(null);\n    };\n    // 处理模板选择\n    const handleTemplateSelect = (template)=>{\n        setCourseDetail((prev)=>({\n                ...prev,\n                selectedTemplate: template.templateName\n            }));\n        setIsTemplatePickerOpen(false);\n    };\n    // 打开模板选择弹窗\n    const handleOpenTemplatePicker = ()=>{\n        setIsTemplatePickerOpen(true);\n    };\n    // 显示课程编辑面板\n    const showCoursePanel = async (courseId)=>{\n        setRightPanelType(\"course\");\n        setSelectedCourseId(courseId);\n        // 如果作品数据还没有加载，重新加载\n        if (works.length === 0 && !loadingWorks) {\n            loadUserWorks();\n        }\n        // 获取选中的课程并更新courseDetail状态\n        const selectedCourse = courseList.find((course)=>course.id === courseId);\n        console.log(\"\\uD83C\\uDFAF 找到的课程:\", selectedCourse);\n        if (selectedCourse) {\n            try {\n                console.log(\"\\uD83D\\uDD04 获取课程详情，seriesId:\", seriesId, \"courseId:\", courseId);\n                // 获取真实的课程详情数据\n                const { data: res } = await fetchCourseDetail(seriesId, courseId);\n                if (res.code === 200 && res.data) {\n                    var _courseDetailData_additionalResources, _courseDetailData_teachingInfo;\n                    const courseDetailData = res.data;\n                    console.log(\"✅ 获取到课程详情:\", courseDetailData);\n                    // 将真实的 additionalResources 映射到 teachingMaterials\n                    const teachingMaterials = ((_courseDetailData_additionalResources = courseDetailData.additionalResources) === null || _courseDetailData_additionalResources === void 0 ? void 0 : _courseDetailData_additionalResources.map((resource)=>({\n                            type: \"application/octet-stream\",\n                            name: resource.title || resource.name || \"附件\",\n                            url: resource.url\n                        }))) || [];\n                    // 将API返回的teachingInfo数组格式转换为新的格式\n                    const mappedTeachingInfo = ((_courseDetailData_teachingInfo = courseDetailData.teachingInfo) === null || _courseDetailData_teachingInfo === void 0 ? void 0 : _courseDetailData_teachingInfo.map((info)=>({\n                            title: info.title || \"\",\n                            content: Array.isArray(info.content) ? info.content.join(\"\\n\") : info.content || \"\"\n                        }))) || [];\n                    console.log(\"\\uD83D\\uDCCE 映射的教学附件:\", teachingMaterials);\n                    console.log(\"\\uD83D\\uDCDA 映射的教学信息:\", mappedTeachingInfo);\n                    setCourseDetail((prev)=>{\n                        var _courseDetailData_contentConfig_video, _courseDetailData_contentConfig, _courseDetailData_contentConfig_video1, _courseDetailData_contentConfig1, _courseDetailData_contentConfig_document, _courseDetailData_contentConfig2, _courseDetailData_contentConfig_document1, _courseDetailData_contentConfig3;\n                        return {\n                            ...prev,\n                            title: courseDetailData.title,\n                            description: courseDetailData.description,\n                            coverImage: courseDetailData.coverImage || \"\",\n                            isVideoEnabled: courseDetailData.hasVideo === 1,\n                            isAttachmentEnabled: courseDetailData.hasDocument === 1,\n                            contentConfig: courseDetailData.contentConfig || {},\n                            teachingInfo: mappedTeachingInfo,\n                            teachingMaterials: teachingMaterials,\n                            videoUrl: ((_courseDetailData_contentConfig = courseDetailData.contentConfig) === null || _courseDetailData_contentConfig === void 0 ? void 0 : (_courseDetailData_contentConfig_video = _courseDetailData_contentConfig.video) === null || _courseDetailData_contentConfig_video === void 0 ? void 0 : _courseDetailData_contentConfig_video.url) || \"\",\n                            videoName: ((_courseDetailData_contentConfig1 = courseDetailData.contentConfig) === null || _courseDetailData_contentConfig1 === void 0 ? void 0 : (_courseDetailData_contentConfig_video1 = _courseDetailData_contentConfig1.video) === null || _courseDetailData_contentConfig_video1 === void 0 ? void 0 : _courseDetailData_contentConfig_video1.name) || \"\",\n                            attachmentUrl: ((_courseDetailData_contentConfig2 = courseDetailData.contentConfig) === null || _courseDetailData_contentConfig2 === void 0 ? void 0 : (_courseDetailData_contentConfig_document = _courseDetailData_contentConfig2.document) === null || _courseDetailData_contentConfig_document === void 0 ? void 0 : _courseDetailData_contentConfig_document.url) || \"\",\n                            attachmentName: ((_courseDetailData_contentConfig3 = courseDetailData.contentConfig) === null || _courseDetailData_contentConfig3 === void 0 ? void 0 : (_courseDetailData_contentConfig_document1 = _courseDetailData_contentConfig3.document) === null || _courseDetailData_contentConfig_document1 === void 0 ? void 0 : _courseDetailData_contentConfig_document1.name) || \"\"\n                        };\n                    });\n                } else {\n                    console.error(\"❌ 获取课程详情失败:\", res.message);\n                    // 使用基础数据作为后备\n                    setCourseDetail((prev)=>({\n                            ...prev,\n                            title: selectedCourse.title,\n                            description: selectedCourse.description,\n                            coverImage: selectedCourse.coverImage || \"\",\n                            isVideoEnabled: selectedCourse.hasVideo === 1,\n                            isAttachmentEnabled: selectedCourse.hasDocument === 1,\n                            teachingMaterials: [] // 清空附件列表\n                        }));\n                }\n            } catch (error) {\n                console.error(\"❌ 获取课程详情异常:\", error);\n                // 使用基础数据作为后备\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        title: selectedCourse.title,\n                        description: selectedCourse.description,\n                        coverImage: selectedCourse.coverImage || \"\",\n                        isVideoEnabled: selectedCourse.hasVideo === 1,\n                        isAttachmentEnabled: selectedCourse.hasDocument === 1,\n                        teachingMaterials: [] // 清空附件列表\n                    }));\n            }\n        }\n    };\n    // 获取选中的课程\n    const getSelectedCourse = ()=>{\n        return courseList.find((course)=>course.id === selectedCourseId);\n    };\n    if (!isVisible) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"course-list-modal-overlay\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"course-list-modal\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"course-list-header\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"course-list-title-section\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"course-list-title\",\n                                        children: \"课程列表\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 1342,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"course-list-actions\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: showSettingsPanel,\n                                                className: \"course-list-settings-btn \".concat(rightPanelType === \"settings\" ? \"active\" : \"\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1348,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1344,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: addNewCourse,\n                                                className: \"course-list-add-btn\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1351,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1350,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 1343,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                lineNumber: 1341,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onClose,\n                                className: \"course-list-close-btn\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 1356,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                lineNumber: 1355,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                        lineNumber: 1340,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"course-list-content\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"course-list-sidebar\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"course-list-items\",\n                                    children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"course-list-loading\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"加载中...\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 1367,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 1366,\n                                        columnNumber: 17\n                                    }, undefined) : courseList.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"course-list-empty\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"course-list-empty-icon\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"w-12 h-12 text-gray-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1372,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1371,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"course-list-empty-title\",\n                                                children: \"暂无课时\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1374,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"course-list-empty-description\",\n                                                children: \"点击右上角的 + 按钮添加第一个课时\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1375,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: addNewCourse,\n                                                className: \"course-list-empty-btn\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1382,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    \"添加课时\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1378,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 1370,\n                                        columnNumber: 17\n                                    }, undefined) : courseList.map((course)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"course-list-item \".concat(selectedCourseId === course.id ? \"active\" : \"\"),\n                                            onClick: ()=>showCoursePanel(course.id),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-list-item-content\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"course-list-item-text\",\n                                                            children: course.title\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1394,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"course-status-badge \".concat(course.status === 1 ? \"published\" : \"draft\"),\n                                                            children: course.status === 1 ? \"已发布\" : \"未发布\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1395,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1393,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: (e)=>{\n                                                        e.stopPropagation();\n                                                        showDeleteConfirm(course.id);\n                                                    },\n                                                    className: \"course-list-item-delete\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"w-3 h-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1406,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1399,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, course.id, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 1388,\n                                            columnNumber: 19\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 1364,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                lineNumber: 1363,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"course-list-edit-area\",\n                                children: [\n                                    rightPanelType === \"none\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"course-edit-empty\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"course-edit-empty-icon\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"w-16 h-16 text-gray-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1419,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1418,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"course-edit-empty-title\",\n                                                children: \"无课程详情\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1421,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"course-edit-empty-description\",\n                                                children: \"点击左侧课程或设置按钮查看详情\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1422,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 1417,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    rightPanelType === \"settings\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"course-series-cover\",\n                                                children: seriesCoverImage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: seriesCoverImage,\n                                                    alt: \"系列课程封面\",\n                                                    className: \"course-series-cover-image\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1433,\n                                                    columnNumber: 21\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-series-cover-placeholder\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"系列课程封面\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1440,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1439,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1431,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"course-edit-form\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"course-edit-field\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"course-edit-label\",\n                                                                children: \"系列课程标题\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1449,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                value: editingTitle,\n                                                                onChange: (e)=>setEditingTitle(e.target.value),\n                                                                className: \"course-edit-input\",\n                                                                placeholder: \"请输入系列课程标题\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1450,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1448,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"course-edit-field\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"course-edit-label\",\n                                                                children: \"课程标签\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1461,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Select_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                mode: \"multiple\",\n                                                                style: {\n                                                                    width: \"100%\"\n                                                                },\n                                                                placeholder: \"请选择课程标签\",\n                                                                value: selectedTags,\n                                                                onChange: setSelectedTags,\n                                                                loading: tagsLoading,\n                                                                options: courseTags.map((tag)=>{\n                                                                    console.log(\"\\uD83C\\uDFF7️ 渲染标签选项:\", tag);\n                                                                    return {\n                                                                        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            style: {\n                                                                                color: tag.color\n                                                                            },\n                                                                            children: tag.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1473,\n                                                                            columnNumber: 29\n                                                                        }, void 0),\n                                                                        value: tag.id\n                                                                    };\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1462,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    fontSize: \"12px\",\n                                                                    color: \"#666\",\n                                                                    marginTop: \"4px\"\n                                                                },\n                                                                children: [\n                                                                    \"调试: 当前标签数量 \",\n                                                                    courseTags.length,\n                                                                    \", 加载状态: \",\n                                                                    tagsLoading ? \"是\" : \"否\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1482,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1460,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"course-edit-field\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"course-edit-label\",\n                                                                children: \"课程项目成员\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1489,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                value: projectMembers,\n                                                                onChange: (e)=>setProjectMembers(e.target.value),\n                                                                className: \"course-edit-input\",\n                                                                placeholder: \"请输入项目成员，如：张老师、李助教、王同学\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1490,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1488,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1446,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true),\n                                    rightPanelType === \"course\" && getSelectedCourse() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"course-detail-edit\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-detail-top\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"course-detail-cover\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"course-cover-upload-area\",\n                                                                    onClick: ()=>{\n                                                                        var _document_getElementById;\n                                                                        return (_document_getElementById = document.getElementById(\"cover-upload-input\")) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.click();\n                                                                    },\n                                                                    children: courseDetail.coverImage || ((_getSelectedCourse = getSelectedCourse()) === null || _getSelectedCourse === void 0 ? void 0 : _getSelectedCourse.coverImage) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                        src: courseDetail.coverImage || ((_getSelectedCourse1 = getSelectedCourse()) === null || _getSelectedCourse1 === void 0 ? void 0 : _getSelectedCourse1.coverImage),\n                                                                        alt: \"课程封面\",\n                                                                        className: \"course-cover-image\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1514,\n                                                                        columnNumber: 27\n                                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"course-cover-placeholder\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"点击上传课程封面\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1521,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1520,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1509,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    id: \"cover-upload-input\",\n                                                                    type: \"file\",\n                                                                    accept: \"image/jpeg,image/jpg,image/png,image/gif\",\n                                                                    onChange: handleCoverUpload,\n                                                                    style: {\n                                                                        display: \"none\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1525,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"one-click-class-btn\",\n                                                                    onClick: ()=>{\n                                                                        // TODO: 实现一键上课功能\n                                                                        console.log(\"一键上课按钮被点击\");\n                                                                        notification.info(\"一键上课功能开发中...\");\n                                                                    },\n                                                                    children: \"一键上课\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1533,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1508,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"course-detail-basic\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"course-detail-field\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            children: \"课程标题\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1546,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            value: courseDetail.title || ((_getSelectedCourse2 = getSelectedCourse()) === null || _getSelectedCourse2 === void 0 ? void 0 : _getSelectedCourse2.title) || \"\",\n                                                                            onChange: (e)=>{\n                                                                                setCourseDetail((prev)=>({\n                                                                                        ...prev,\n                                                                                        title: e.target.value\n                                                                                    }));\n                                                                                updateCourseTitle(selectedCourseId, e.target.value);\n                                                                            },\n                                                                            placeholder: \"请输入课程标题\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1547,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1545,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"course-detail-field\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            children: \"课程介绍\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1558,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                            value: courseDetail.description || ((_getSelectedCourse3 = getSelectedCourse()) === null || _getSelectedCourse3 === void 0 ? void 0 : _getSelectedCourse3.description) || \"\",\n                                                                            onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                        ...prev,\n                                                                                        description: e.target.value\n                                                                                    })),\n                                                                            placeholder: \"请输入课程介绍\",\n                                                                            rows: 3\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1559,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1557,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1544,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1507,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-detail-section\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            children: \"课程资源\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1571,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"course-resource-item\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"resource-header-right\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"课程视频\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1576,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"switch\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: \"checkbox\",\n                                                                                    checked: courseDetail.isVideoEnabled,\n                                                                                    onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                ...prev,\n                                                                                                isVideoEnabled: e.target.checked\n                                                                                            }))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1578,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"slider\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1583,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1577,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1575,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                courseDetail.isVideoEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"video-content-area\",\n                                                                    children: ((_courseDetail_contentConfig = courseDetail.contentConfig) === null || _courseDetail_contentConfig === void 0 ? void 0 : (_courseDetail_contentConfig_video = _courseDetail_contentConfig.video) === null || _courseDetail_contentConfig_video === void 0 ? void 0 : _courseDetail_contentConfig_video.url) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"video-info-section\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"video-preview\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                                                                    className: \"video-thumbnail\",\n                                                                                    controls: true,\n                                                                                    poster: courseDetail.coverImage,\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                                                                                            src: courseDetail.contentConfig.video.url,\n                                                                                            type: \"video/mp4\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1597,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined),\n                                                                                        \"您的浏览器不支持视频播放\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1592,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1591,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"video-name-centered\",\n                                                                                children: courseDetail.contentConfig.video.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1601,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"upload-btn-horizontal\",\n                                                                                onClick: triggerVideoUpload,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"重新上传\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1603,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1602,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1590,\n                                                                        columnNumber: 29\n                                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"video-upload-section\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"video-placeholder-centered\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"play-icon\",\n                                                                                    children: \"▶\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1609,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1608,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"upload-btn-horizontal\",\n                                                                                onClick: triggerVideoUpload,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"上传视频\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1612,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1611,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1607,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1587,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1574,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"course-resource-item\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"resource-header-right\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"课程附件\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1623,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"switch\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: \"checkbox\",\n                                                                                    checked: courseDetail.isAttachmentEnabled,\n                                                                                    onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                ...prev,\n                                                                                                isAttachmentEnabled: e.target.checked\n                                                                                            }))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1625,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"slider\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1630,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1624,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1622,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                courseDetail.isAttachmentEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"attachment-content-area\",\n                                                                    children: ((_courseDetail_contentConfig1 = courseDetail.contentConfig) === null || _courseDetail_contentConfig1 === void 0 ? void 0 : (_courseDetail_contentConfig_document = _courseDetail_contentConfig1.document) === null || _courseDetail_contentConfig_document === void 0 ? void 0 : _courseDetail_contentConfig_document.url) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"attachment-info-section\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"attachment-preview\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"document-icon\",\n                                                                                        children: \"\\uD83D\\uDCC4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1639,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"attachment-details\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"attachment-name\",\n                                                                                            children: courseDetail.contentConfig.document.name\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1641,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1640,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1638,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"upload-btn-horizontal\",\n                                                                                onClick: triggerAttachmentUpload,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"重新上传\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1645,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1644,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1637,\n                                                                        columnNumber: 29\n                                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"attachment-upload-section\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            className: \"upload-btn-horizontal\",\n                                                                            onClick: triggerAttachmentUpload,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"上传附件\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1651,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1650,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1649,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1634,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1621,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"course-resource-item\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"resource-header-simple\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"教学附件\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1662,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1661,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"teaching-materials\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            className: \"add-material-btn\",\n                                                                            onClick: triggerTeachingMaterialUpload,\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"+\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1666,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"上传\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1667,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1665,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        courseDetail.teachingMaterials && courseDetail.teachingMaterials.length > 0 ? courseDetail.teachingMaterials.map((material, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"material-item\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"material-name\",\n                                                                                        onClick: ()=>{\n                                                                                            if (material.url) {\n                                                                                                window.open(material.url, \"_blank\");\n                                                                                            }\n                                                                                        },\n                                                                                        style: {\n                                                                                            cursor: material.url ? \"pointer\" : \"default\",\n                                                                                            color: material.url ? \"#1890ff\" : \"inherit\",\n                                                                                            textDecoration: material.url ? \"underline\" : \"none\"\n                                                                                        },\n                                                                                        title: material.url ? \"点击下载附件\" : material.name,\n                                                                                        children: [\n                                                                                            \"\\uD83D\\uDCCE \",\n                                                                                            material.name\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1672,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        className: \"remove-material-btn\",\n                                                                                        onClick: ()=>removeTeachingMaterial(index),\n                                                                                        title: \"删除附件\",\n                                                                                        children: \"\\xd7\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1688,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, index, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1671,\n                                                                                columnNumber: 29\n                                                                            }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"empty-materials-hint\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                style: {\n                                                                                    color: \"#999\",\n                                                                                    fontSize: \"14px\"\n                                                                                },\n                                                                                children: \"暂无教学附件\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1699,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1698,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1664,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1660,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1570,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-detail-section\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"section-header\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    children: \"课程内容\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1709,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"add-content-section-btn\",\n                                                                    onClick: addTeachingInfoItem,\n                                                                    title: \"添加课程内容\",\n                                                                    children: \"+ 添加课程内容\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1710,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1708,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"course-content-area\",\n                                                            children: courseDetail.teachingInfo && courseDetail.teachingInfo.length > 0 ? courseDetail.teachingInfo.map((info, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"teaching-info-card\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"card-header\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"card-title\",\n                                                                                    children: [\n                                                                                        \"课程内容 \",\n                                                                                        index + 1\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1723,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    className: \"remove-card-btn\",\n                                                                                    onClick: ()=>removeTeachingInfoItem(index),\n                                                                                    title: \"删除此内容\",\n                                                                                    children: \"\\xd7\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1724,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1722,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"card-content\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"input-group\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                            children: \"标题\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1734,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                            type: \"text\",\n                                                                                            value: info.title,\n                                                                                            onChange: (e)=>updateTeachingInfoTitle(index, e.target.value),\n                                                                                            placeholder: \"请输入标题，如：教学目标、教学方法等\",\n                                                                                            className: \"title-input\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1735,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1733,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"input-group\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                            children: \"内容\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1744,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                                            value: info.content,\n                                                                                            onChange: (e)=>updateTeachingInfoContent(index, e.target.value),\n                                                                                            placeholder: \"请输入具体内容，多个内容项可用换行分隔\",\n                                                                                            className: \"content-textarea\",\n                                                                                            rows: 4\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1745,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1743,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1732,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, index, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1721,\n                                                                    columnNumber: 27\n                                                                }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"empty-content-hint\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: \"暂无课程内容，点击右上角按钮添加\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1758,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1757,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1718,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1707,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-detail-section\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"one-key-section\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"one-key-item\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"重新上课\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1768,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"switch\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"checkbox\",\n                                                                                checked: courseDetail.isOneKeyOpen,\n                                                                                onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                            ...prev,\n                                                                                            isOneKeyOpen: e.target.checked\n                                                                                        }))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1770,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"slider\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1775,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1769,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1767,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            courseDetail.isOneKeyOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"one-key-item\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"分配积木\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1782,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"switch\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                        type: \"checkbox\",\n                                                                                        checked: courseDetail.isDistributionEnabled,\n                                                                                        onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    isDistributionEnabled: e.target.checked\n                                                                                                }))\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1784,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"slider\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1789,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1783,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            courseDetail.isDistributionEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"block-template-section\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        className: \"select-template-btn\",\n                                                                                        onClick: handleOpenTemplatePicker,\n                                                                                        children: \"选择积木模板\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1793,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"selected-template-display\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            children: courseDetail.selectedTemplate || \"选中的模板名字\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1800,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1799,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1792,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1781,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"one-key-item\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"分配能量\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1807,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"switch\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                        type: \"checkbox\",\n                                                                                        checked: courseDetail.isDistributionWater,\n                                                                                        onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    isDistributionWater: e.target.checked\n                                                                                                }))\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1809,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"slider\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1814,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1808,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1806,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    courseDetail.isDistributionWater && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"energy-input-section\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"需要能量：\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1820,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"text\",\n                                                                                value: courseDetail.requiredEnergy || \"\",\n                                                                                onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                            ...prev,\n                                                                                            requiredEnergy: e.target.value\n                                                                                        })),\n                                                                                placeholder: \"请输入需要的能量值\",\n                                                                                className: \"energy-input\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1821,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1819,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"one-key-item\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"分配任务\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1832,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"switch\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                        type: \"checkbox\",\n                                                                                        checked: courseDetail.isDistributionLimit,\n                                                                                        onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    isDistributionLimit: e.target.checked\n                                                                                                }))\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1834,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"slider\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1839,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1833,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1831,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    courseDetail.isDistributionLimit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"task-config-form\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"task-config-row\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"task-config-field\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                                children: \"任务名称:\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                lineNumber: 1849,\n                                                                                                columnNumber: 35\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                type: \"text\",\n                                                                                                value: courseDetail.taskConfig.taskName,\n                                                                                                onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                            ...prev,\n                                                                                                            taskConfig: {\n                                                                                                                ...prev.taskConfig,\n                                                                                                                taskName: e.target.value\n                                                                                                            }\n                                                                                                        })),\n                                                                                                placeholder: \"请输入任务名称\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                lineNumber: 1850,\n                                                                                                columnNumber: 35\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1848,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"task-config-field\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                                children: \"任务持续天数:\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                lineNumber: 1861,\n                                                                                                columnNumber: 35\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                type: \"number\",\n                                                                                                value: courseDetail.taskConfig.taskDuration,\n                                                                                                onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                            ...prev,\n                                                                                                            taskConfig: {\n                                                                                                                ...prev.taskConfig,\n                                                                                                                taskDuration: e.target.value\n                                                                                                            }\n                                                                                                        })),\n                                                                                                placeholder: \"请输入天数\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                lineNumber: 1862,\n                                                                                                columnNumber: 35\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1860,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1847,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"task-config-field task-config-full\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        children: \"任务描述:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1876,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                                        value: courseDetail.taskConfig.taskDescription,\n                                                                                        onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    taskConfig: {\n                                                                                                        ...prev.taskConfig,\n                                                                                                        taskDescription: e.target.value\n                                                                                                    }\n                                                                                                })),\n                                                                                        placeholder: \"请输入任务描述\",\n                                                                                        rows: 4\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1877,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1875,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"task-config-field task-config-full\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        children: [\n                                                                                            \"任务自评项: \",\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"item-number\",\n                                                                                                children: courseDetail.taskConfig.selfAssessmentItems.length\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                lineNumber: 1890,\n                                                                                                columnNumber: 47\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1890,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    courseDetail.taskConfig.selfAssessmentItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"self-assessment-item\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                type: \"text\",\n                                                                                                value: item,\n                                                                                                onChange: (e)=>{\n                                                                                                    const newItems = [\n                                                                                                        ...courseDetail.taskConfig.selfAssessmentItems\n                                                                                                    ];\n                                                                                                    newItems[index] = e.target.value;\n                                                                                                    setCourseDetail((prev)=>({\n                                                                                                            ...prev,\n                                                                                                            taskConfig: {\n                                                                                                                ...prev.taskConfig,\n                                                                                                                selfAssessmentItems: newItems\n                                                                                                            }\n                                                                                                        }));\n                                                                                                },\n                                                                                                placeholder: \"请输入自评项内容\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                lineNumber: 1893,\n                                                                                                columnNumber: 37\n                                                                                            }, undefined)\n                                                                                        }, index, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1892,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        type: \"button\",\n                                                                                        className: \"add-assessment-btn\",\n                                                                                        onClick: ()=>setCourseDetail((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    taskConfig: {\n                                                                                                        ...prev.taskConfig,\n                                                                                                        selfAssessmentItems: [\n                                                                                                            ...prev.taskConfig.selfAssessmentItems,\n                                                                                                            \"\"\n                                                                                                        ]\n                                                                                                    }\n                                                                                                })),\n                                                                                        children: \"+\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1908,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1889,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"task-config-field task-config-full\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        children: \"任务参考作品:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1925,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"reference-works-section\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"works-section\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                    className: \"help-text\",\n                                                                                                    children: \"选择作品作为任务参考资料（可多选）\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                    lineNumber: 1928,\n                                                                                                    columnNumber: 37\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"relative works-scroll-wrapper\",\n                                                                                                    style: {\n                                                                                                        minHeight: \"200px\",\n                                                                                                        cursor: \"grab\",\n                                                                                                        userSelect: \"none\"\n                                                                                                    },\n                                                                                                    children: loadingWorks ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"loading-container\",\n                                                                                                        children: [\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                className: \"loading-spinner\"\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                lineNumber: 1939,\n                                                                                                                columnNumber: 43\n                                                                                                            }, undefined),\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                                children: \"加载中...\"\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                lineNumber: 1940,\n                                                                                                                columnNumber: 43\n                                                                                                            }, undefined)\n                                                                                                        ]\n                                                                                                    }, void 0, true, {\n                                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                        lineNumber: 1938,\n                                                                                                        columnNumber: 41\n                                                                                                    }, undefined) : works.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"works-horizontal-scroll\",\n                                                                                                        onWheel: handleWorksWheel,\n                                                                                                        children: works.map((work)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                className: \"work-card \".concat(selectedWorkIds.includes(work.id) ? \"selected\" : \"\"),\n                                                                                                                onClick: ()=>handleWorkSelect(work.id),\n                                                                                                                children: [\n                                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                        className: \"work-image\",\n                                                                                                                        children: [\n                                                                                                                            work.coverImage || work.screenShotImage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                                                                src: work.coverImage || work.screenShotImage,\n                                                                                                                                alt: work.title,\n                                                                                                                                onError: (e)=>{\n                                                                                                                                    const target = e.currentTarget;\n                                                                                                                                    target.style.display = \"none\";\n                                                                                                                                    const nextElement = target.nextElementSibling;\n                                                                                                                                    if (nextElement) {\n                                                                                                                                        nextElement.style.display = \"flex\";\n                                                                                                                                    }\n                                                                                                                                }\n                                                                                                                            }, void 0, false, {\n                                                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                                lineNumber: 1955,\n                                                                                                                                columnNumber: 51\n                                                                                                                            }, undefined) : null,\n                                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                                className: \"work-placeholder\",\n                                                                                                                                style: {\n                                                                                                                                    display: work.coverImage || work.screenShotImage ? \"none\" : \"flex\"\n                                                                                                                                },\n                                                                                                                                children: \"作品\"\n                                                                                                                            }, void 0, false, {\n                                                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                                lineNumber: 1968,\n                                                                                                                                columnNumber: 49\n                                                                                                                            }, undefined)\n                                                                                                                        ]\n                                                                                                                    }, void 0, true, {\n                                                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                        lineNumber: 1953,\n                                                                                                                        columnNumber: 47\n                                                                                                                    }, undefined),\n                                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                        className: \"work-title\",\n                                                                                                                        children: work.title || work.name || work.workName || \"未命名作品\"\n                                                                                                                    }, void 0, false, {\n                                                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                        lineNumber: 1972,\n                                                                                                                        columnNumber: 47\n                                                                                                                    }, undefined),\n                                                                                                                    selectedWorkIds.includes(work.id) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                        className: \"selected-indicator\",\n                                                                                                                        children: \"✓\"\n                                                                                                                    }, void 0, false, {\n                                                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                        lineNumber: 1974,\n                                                                                                                        columnNumber: 49\n                                                                                                                    }, undefined)\n                                                                                                                ]\n                                                                                                            }, work.id, true, {\n                                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                lineNumber: 1948,\n                                                                                                                columnNumber: 45\n                                                                                                            }, undefined))\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                        lineNumber: 1943,\n                                                                                                        columnNumber: 41\n                                                                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"empty-placeholder\",\n                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                            className: \"empty-text\",\n                                                                                                            children: \"暂无作品\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                            lineNumber: 1981,\n                                                                                                            columnNumber: 43\n                                                                                                        }, undefined)\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                        lineNumber: 1980,\n                                                                                                        columnNumber: 41\n                                                                                                    }, undefined)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                    lineNumber: 1929,\n                                                                                                    columnNumber: 37\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1927,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1926,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1924,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"task-config-field task-config-full\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        children: \"任务参考资源:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1991,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"reference-resources-section\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"reference-resources-grid\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                    type: \"button\",\n                                                                                                    className: \"upload-resource-btn\",\n                                                                                                    onClick: ()=>{\n                                                                                                        // 触发文件上传\n                                                                                                        const input = document.createElement(\"input\");\n                                                                                                        input.type = \"file\";\n                                                                                                        input.accept = \".pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx,.txt,.jpg,.png,.gif\";\n                                                                                                        input.onchange = (e)=>{\n                                                                                                            var _e_target_files;\n                                                                                                            const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n                                                                                                            if (file) {\n                                                                                                                setCourseDetail((prev)=>({\n                                                                                                                        ...prev,\n                                                                                                                        taskConfig: {\n                                                                                                                            ...prev.taskConfig,\n                                                                                                                            referenceResources: [\n                                                                                                                                ...prev.taskConfig.referenceResources,\n                                                                                                                                {\n                                                                                                                                    type: \"file\",\n                                                                                                                                    name: file.name\n                                                                                                                                }\n                                                                                                                            ]\n                                                                                                                        }\n                                                                                                                    }));\n                                                                                                            }\n                                                                                                        };\n                                                                                                        input.click();\n                                                                                                    },\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                                            size: 24\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                            lineNumber: 2020,\n                                                                                                            columnNumber: 39\n                                                                                                        }, undefined),\n                                                                                                        \"上传\"\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                    lineNumber: 1994,\n                                                                                                    columnNumber: 37\n                                                                                                }, undefined),\n                                                                                                courseDetail.taskConfig.referenceResources.map((resource, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"reference-resource-item\",\n                                                                                                        children: [\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                                children: resource.name\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                lineNumber: 2025,\n                                                                                                                columnNumber: 41\n                                                                                                            }, undefined),\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                                type: \"button\",\n                                                                                                                className: \"remove-resource-btn\",\n                                                                                                                onClick: ()=>{\n                                                                                                                    const newResources = courseDetail.taskConfig.referenceResources.filter((_, i)=>i !== index);\n                                                                                                                    setCourseDetail((prev)=>({\n                                                                                                                            ...prev,\n                                                                                                                            taskConfig: {\n                                                                                                                                ...prev.taskConfig,\n                                                                                                                                referenceResources: newResources\n                                                                                                                            }\n                                                                                                                        }));\n                                                                                                                },\n                                                                                                                children: \"\\xd7\"\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                lineNumber: 2026,\n                                                                                                                columnNumber: 41\n                                                                                                            }, undefined)\n                                                                                                        ]\n                                                                                                    }, index, true, {\n                                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                        lineNumber: 2024,\n                                                                                                        columnNumber: 39\n                                                                                                    }, undefined))\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1993,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1992,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1990,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1845,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1766,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1765,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 1505,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                lineNumber: 1415,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                        lineNumber: 1361,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"course-list-footer\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"course-list-footer-left\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handlePublish,\n                                    className: \"course-list-btn course-list-btn-publish\",\n                                    disabled: courseList.length === 0 || isPublishingSeries || seriesStatus === 1,\n                                    title: seriesStatus === 1 ? \"系列课程已发布\" : courseList.length === 0 ? \"发布失败：课程系列中至少需要包含一个课程\" : isPublishingSeries ? \"正在发布系列课程...\" : \"发布系列课程\",\n                                    children: seriesStatus === 1 ? \"已发布\" : isPublishingSeries ? \"正在发布...\" : \"发布系列课程\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 2059,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                lineNumber: 2058,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"course-list-footer-right\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleExitEdit,\n                                        className: \"course-list-btn course-list-btn-exit\",\n                                        children: \"退出编辑模式\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 2082,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handlePublishCourse,\n                                        className: \"course-list-btn course-list-btn-publish-course\",\n                                        disabled: !selectedCourseId || ((_courseList_find = courseList.find((c)=>c.id === selectedCourseId)) === null || _courseList_find === void 0 ? void 0 : _courseList_find.status) === 1 || isPublishing,\n                                        title: !selectedCourseId ? \"请先选择要发布的课程\" : ((_courseList_find1 = courseList.find((c)=>c.id === selectedCourseId)) === null || _courseList_find1 === void 0 ? void 0 : _courseList_find1.status) === 1 ? \"该课程已发布\" : isPublishing ? \"正在发布课程...\" : \"发布选中的课程\",\n                                        children: isPublishing ? \"正在发布...\" : \"发布课程\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 2085,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleSave,\n                                        className: \"course-list-btn course-list-btn-save\",\n                                        disabled: uploadingFiles.size > 0 || isCreating || courseList.length === 0,\n                                        title: courseList.length === 0 ? \"请先添加课程内容\" : uploadingFiles.size > 0 ? \"有文件正在上传中，请等待上传完成\" : isCreating ? courseList.some((c)=>c.id > 1000000) ? \"正在创建课程...\" : \"正在保存课程...\" : courseList.some((c)=>c.id > 1000000) ? \"创建课程\" : \"保存课程\",\n                                        children: uploadingFiles.size > 0 ? \"上传中...\" : isCreating ? courseList.some((c)=>c.id > 1000000) ? \"正在创建...\" : \"正在保存...\" : courseList.some((c)=>c.id > 1000000) ? \"创建课程\" : \"保存课程\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 2101,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                lineNumber: 2081,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                        lineNumber: 2057,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                lineNumber: 1338,\n                columnNumber: 7\n            }, undefined),\n            deleteConfirmVisible && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"modal-overlay\",\n                onClick: cancelDelete,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"delete-confirm-modal\",\n                    onClick: (e)=>e.stopPropagation(),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"delete-confirm-header\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    children: \"确认删除\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 2131,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: cancelDelete,\n                                    className: \"close-btn\",\n                                    disabled: isDeleting,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 2137,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 2132,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                            lineNumber: 2130,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"delete-confirm-content\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: isDeleting ? \"正在删除课程，请稍候...\" : \"确定要删除这个课程吗？删除后无法恢复。\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 2141,\n                                    columnNumber: 15\n                                }, undefined),\n                                isDeleting && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"delete-loading\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"loading-spinner\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 2149,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 2148,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                            lineNumber: 2140,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"delete-confirm-footer\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: cancelDelete,\n                                    className: \"cancel-btn\",\n                                    disabled: isDeleting,\n                                    children: \"取消\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 2154,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: confirmDeleteCourse,\n                                    className: \"confirm-btn\",\n                                    disabled: isDeleting,\n                                    children: isDeleting ? \"正在删除...\" : \"确认删除\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 2161,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                            lineNumber: 2153,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                    lineNumber: 2129,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                lineNumber: 2128,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TemplatePickerModal__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                isOpen: isTemplatePickerOpen,\n                onClose: ()=>setIsTemplatePickerOpen(false),\n                onTemplateSelect: handleTemplateSelect\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                lineNumber: 2174,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n        lineNumber: 1337,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CourseListEditModal, \"oiqOCedO6h8BMnSEuXyTF59Tvh0=\");\n_c = CourseListEditModal;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CourseListEditModal);\nvar _c;\n$RefreshReg$(_c, \"CourseListEditModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC93b3JrYmVuY2gvY29tcG9uZW50cy9Db3Vyc2VMaXN0RWRpdE1vZGFsLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUV3RTtBQUNIO0FBQ0g7QUFDckI7QUFDQTtBQUNGO0FBQ2I7QUFDOEM7QUFDcEI7QUFDckI7QUFDQTtBQWlFbkMsV0FBVztBQUNYLE1BQU1nQixvQkFBb0IsT0FBT0M7SUFDL0JDLFFBQVFDLEdBQUcsQ0FBQyxtQ0FBeUJGO0lBQ3JDQyxRQUFRQyxHQUFHLENBQUM7SUFFWixNQUFNQyxXQUFXLE1BQU1WLHNEQUFTQSxDQUFDVyxlQUFlLENBQUNKO0lBQ2pEQyxRQUFRQyxHQUFHLENBQUMsMkJBQWlCQztJQUM3QixPQUFPQSxTQUFTRSxJQUFJO0FBQ3RCO0FBRUEsa0NBQWtDO0FBQ2xDLE1BQU1DLGtCQUFrQixPQUFPTjtJQUM3QkMsUUFBUUMsR0FBRyxDQUFDLG1DQUF5QkY7SUFDckNDLFFBQVFDLEdBQUcsQ0FBQztJQUVaLHFCQUFxQjtJQUNyQixNQUFNQyxXQUFXLE1BQU1WLHNEQUFTQSxDQUFDYywwQkFBMEIsQ0FBQ1AsVUFBVTtRQUNwRVEsTUFBTTtRQUNOQyxVQUFVO0lBRVo7SUFFQVIsUUFBUUMsR0FBRyxDQUFDLHVCQUFhQztJQUN6QixPQUFPQSxTQUFTRSxJQUFJO0FBQ3RCO0FBRUEsU0FBUztBQUNULE1BQU1LLG9CQUFvQixPQUFPVixVQUFrQlc7SUFDakQsT0FBTyxNQUFNbEIsc0RBQVNBLENBQUNtQiwwQkFBMEIsQ0FBQ1osVUFBVVc7QUFDOUQ7QUFFQSxTQUFTO0FBQ1QsTUFBTUUsa0JBQWtCO0lBQ3RCLElBQUk7UUFDRlosUUFBUUMsR0FBRyxDQUFDO1FBQ1osTUFBTVksU0FBUyxNQUFNckIsc0RBQVNBLENBQUNzQixhQUFhLENBQUM7WUFDM0NQLE1BQU07WUFDTkMsVUFBVTtZQUNWTyxRQUFRLEVBQUUsV0FBVztRQUN2QjtRQUNBZixRQUFRQyxHQUFHLENBQUMsOENBQW9DWTtRQUNoRCxPQUFPQTtJQUNULEVBQUUsT0FBT0csT0FBTztRQUNkaEIsUUFBUWdCLEtBQUssQ0FBQyw4Q0FBb0NBO1FBQ2xELE1BQU1BO0lBQ1I7QUFDRjtBQUVBLE1BQU1DLHNCQUEwRDtRQUFDLEVBQy9EQyxTQUFTLEVBQ1RDLE9BQU8sRUFDUEMsTUFBTSxFQUNOQyxXQUFXLEVBQ1hDLGdCQUFnQixFQUNoQnZCLFdBQVcsSUFBSSwyQkFBMkI7SUFBNUIsRUFDZjtRQW8yQ21Ed0Isb0JBRVFBLHFCQWtDTEEscUJBV01BLHFCQTZCbENDLG1DQUFBQSw2QkErQ0FBLHNDQUFBQSw4QkFvY2tCQyxrQkFJekJBOztJQXQ2RGxCLE1BQU1DLGVBQWU5QiwwRkFBZUE7SUFFcEMsTUFBTSxDQUFDNkIsWUFBWUUsY0FBYyxHQUFHNUMsK0NBQVFBLENBQWUsRUFBRTtJQUM3RCxNQUFNLENBQUM2QyxTQUFTQyxXQUFXLEdBQUc5QywrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUMrQyxnQkFBZ0JDLGtCQUFrQixHQUFHaEQsK0NBQVFBLENBQWlDO0lBQ3JGLE1BQU0sQ0FBQ2lELGtCQUFrQkMsb0JBQW9CLEdBQUdsRCwrQ0FBUUEsQ0FBZ0I7SUFDeEUsTUFBTSxDQUFDbUQsZ0JBQWdCQyxrQkFBa0IsR0FBR3BELCtDQUFRQSxDQUFjLElBQUlxRDtJQUN0RSxNQUFNLENBQUNDLGNBQWNDLGdCQUFnQixHQUFHdkQsK0NBQVFBLENBQUM7SUFDakQsTUFBTSxDQUFDd0QsWUFBWUMsY0FBYyxHQUFHekQsK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDMEQsb0JBQW9CQyxzQkFBc0IsR0FBRzNELCtDQUFRQSxDQUFDO0lBQzdELE1BQU0sQ0FBQzRELGNBQWNDLGdCQUFnQixHQUFHN0QsK0NBQVFBLENBQVMsSUFBSSxtQkFBbUI7SUFFaEYsV0FBVztJQUNYLE1BQU0sQ0FBQzhELHNCQUFzQkMsd0JBQXdCLEdBQUcvRCwrQ0FBUUEsQ0FBQztJQUNqRSxNQUFNLENBQUNnRSxnQkFBZ0JDLGtCQUFrQixHQUFHakUsK0NBQVFBLENBQWdCO0lBQ3BFLE1BQU0sQ0FBQ2tFLFlBQVlDLGNBQWMsR0FBR25FLCtDQUFRQSxDQUFDO0lBRTdDLFdBQVc7SUFDWCxNQUFNLENBQUNvRSxzQkFBc0JDLHdCQUF3QixHQUFHckUsK0NBQVFBLENBQUM7SUFFakUsTUFBTSxDQUFDc0UsY0FBY0MsZ0JBQWdCLEdBQUd2RSwrQ0FBUUEsQ0FBQ3NDO0lBQ2pELE1BQU0sQ0FBQ2tDLGFBQWFDLGVBQWUsR0FBR3pFLCtDQUFRQSxDQUFDO0lBQy9DLE1BQU0sQ0FBQzBFLGtCQUFrQkMsb0JBQW9CLEdBQUczRSwrQ0FBUUEsQ0FBQztJQUN6RCxNQUFNLENBQUM0RSxnQkFBZ0JDLGtCQUFrQixHQUFHN0UsK0NBQVFBLENBQUM7SUFFckQsU0FBUztJQUNULE1BQU0sQ0FBQzhFLE9BQU9DLFNBQVMsR0FBRy9FLCtDQUFRQSxDQUFRLEVBQUU7SUFDNUMsTUFBTSxDQUFDZ0YsY0FBY0MsZ0JBQWdCLEdBQUdqRiwrQ0FBUUEsQ0FBQztJQUNqRCxNQUFNLENBQUNrRixpQkFBaUJDLG1CQUFtQixHQUFHbkYsK0NBQVFBLENBQVcsRUFBRTtJQUVuRSxVQUFVO0lBQ1YsTUFBTSxDQUFDb0YsYUFBYUMsZUFBZSxHQUFHckYsK0NBQVFBLENBQUM7SUFDL0MsTUFBTSxDQUFDc0YsY0FBY0MsZ0JBQWdCLEdBQUd2RiwrQ0FBUUEsQ0FBQztJQUNqRCxNQUFNLENBQUN3RixrQkFBa0JDLG9CQUFvQixHQUFHekYsK0NBQVFBLENBQUM7SUFDekQsTUFBTXlCLFdBQVcsR0FBRyxXQUFXO0lBRS9CLFdBQVc7SUFDWCxNQUFNLENBQUNpRSxZQUFZQyxjQUFjLEdBQUczRiwrQ0FBUUEsQ0FBYyxFQUFFO0lBQzVELE1BQU0sQ0FBQzRGLGNBQWNDLGdCQUFnQixHQUFHN0YsK0NBQVFBLENBQVcsRUFBRTtJQUM3RCxNQUFNLENBQUM4RixhQUFhQyxlQUFlLEdBQUcvRiwrQ0FBUUEsQ0FBQztJQUUvQyxXQUFXO0lBQ1gsTUFBTSxDQUFDeUMsY0FBY3VELGdCQUFnQixHQUFHaEcsK0NBQVFBLENBQUM7UUFDL0NpRyxPQUFPO1FBQ1BDLGFBQWE7UUFDYkMsWUFBWTtRQUNaQyxVQUFVO1FBQ1ZDLFdBQVc7UUFDWEMsZ0JBQWdCO1FBQ2hCQyxlQUFlO1FBQ2ZDLGdCQUFnQjtRQUNoQkMscUJBQXFCO1FBQ3JCQyxtQkFBbUIsRUFBRTtRQUNyQixtQkFBbUI7UUFDbkJDLGNBQWMsRUFBRTtRQUNoQixvQkFBb0I7UUFDcEJDLGVBQWU7WUFDYkMsVUFBVTtZQUNWQyxhQUFhO1lBQ2JDLFVBQVU7WUFDVkMsT0FBTztnQkFBRUMsS0FBSztnQkFBSUMsTUFBTTtZQUFHO1lBQzNCQyxVQUFVO2dCQUFFRixLQUFLO2dCQUFJQyxNQUFNO1lBQUc7WUFDOUJFLE9BQU87Z0JBQUVILEtBQUs7Z0JBQUlDLE1BQU07WUFBRztRQUM3QjtRQUNBRyxlQUFlO1lBQ2JDLE9BQU87WUFDUEMsU0FBUztRQUNYO1FBQ0FDLGNBQWM7UUFDZEMsdUJBQXVCO1FBQ3ZCQyxvQkFBb0I7UUFDcEJDLGtCQUFrQjtRQUNsQkMscUJBQXFCO1FBQ3JCQyxnQkFBZ0I7UUFDaEJDLGNBQWM7UUFDZEMscUJBQXFCO1FBQ3JCQyx3QkFBd0I7WUFDdEJDLGFBQWE7WUFDYkMsV0FBVztZQUNYQyxZQUFZO1FBQ2Q7UUFDQUMsb0JBQW9CO1FBQ3BCQyw0QkFBNEI7WUFDMUJDLFdBQVc7WUFDWEMsU0FBUztRQUNYO1FBQ0FDLHVCQUF1QixFQUFFO1FBQ3pCLFdBQVc7UUFDWEMsWUFBWTtZQUNWQyxVQUFVO1lBQ1ZDLGNBQWM7WUFDZEMsaUJBQWlCO1lBQ2pCQyxxQkFBcUI7Z0JBQUM7YUFBRztZQUN6QkMsZ0JBQWdCLEVBQUU7WUFDbEJDLG9CQUFvQixFQUFFO1FBQ3hCO0lBQ0Y7SUFFQSxXQUFXO0lBQ1g5SSxnREFBU0EsQ0FBQztRQUNSLElBQUlrQyxhQUFhbkIsVUFBVTtZQUN6QixXQUFXO1lBQ1gsTUFBTWdJLFFBQVFDLGFBQWFDLE9BQU8sQ0FBQztZQUNuQ2pJLFFBQVFDLEdBQUcsQ0FBQyxnQ0FBc0IsQ0FBQyxDQUFDOEg7WUFDcEMvSCxRQUFRQyxHQUFHLENBQUMsMEJBQWdCRjtZQUU1QixJQUFJLENBQUNnSSxPQUFPO2dCQUNWL0gsUUFBUWdCLEtBQUssQ0FBQztnQkFDZCxjQUFjO2dCQUNkVyxjQUFjLEVBQUU7Z0JBQ2hCRSxXQUFXO2dCQUNYO1lBQ0Y7WUFFQXFHO1lBQ0FDO1lBQ0FDO1lBQ0FDO1FBQ0Y7SUFDRixHQUFHO1FBQUNuSDtRQUFXbkI7S0FBUztJQUV4QixNQUFNbUksaUJBQWlCO1FBQ3JCLElBQUk7WUFDRnJHLFdBQVc7WUFDWDdCLFFBQVFDLEdBQUcsQ0FBQyxtQ0FBeUJGO1lBRXJDLE1BQU1HLFdBQVcsTUFBTUcsZ0JBQWdCTjtZQUN2Q0MsUUFBUUMsR0FBRyxDQUFDLHVCQUFhQztZQUV6QixJQUFJQSxTQUFTb0ksSUFBSSxLQUFLLEtBQUs7Z0JBQ3pCdEksUUFBUUMsR0FBRyxDQUFDLGFBQWFDLFNBQVNFLElBQUk7Z0JBQ3RDLE1BQU1tSSxVQUFVckksU0FBU0UsSUFBSSxDQUFDb0ksSUFBSSxJQUFJLEVBQUU7Z0JBQ3hDeEksUUFBUUMsR0FBRyxDQUFDLGNBQWNzSTtnQkFDMUJ2SSxRQUFRQyxHQUFHLENBQUMsd0JBQWNzSSxRQUFRRSxHQUFHLENBQUMsQ0FBQ0MsSUFBWTt3QkFDakRDLElBQUlELEVBQUVDLEVBQUU7d0JBQ1JDLE1BQU0sT0FBT0YsRUFBRUMsRUFBRTt3QkFDakIzRCxPQUFPMEQsRUFBRTFELEtBQUs7d0JBQ2RqRSxRQUFRMkgsRUFBRTNILE1BQU07b0JBQ2xCO2dCQUNBZixRQUFRQyxHQUFHLENBQUMsd0JBQWM7b0JBQ3hCNEksT0FBT04sUUFBUU8sTUFBTTtvQkFDckJDLE9BQU9SLFFBQVFTLE1BQU0sQ0FBQyxDQUFDTixJQUFXQSxFQUFFM0gsTUFBTSxLQUFLLEdBQUcrSCxNQUFNO29CQUN4REcsV0FBV1YsUUFBUVMsTUFBTSxDQUFDLENBQUNOLElBQVdBLEVBQUUzSCxNQUFNLEtBQUssR0FBRytILE1BQU07Z0JBQzlEO2dCQUNBbkgsY0FBYzRHO1lBQ2hCLE9BQU87Z0JBQ0x2SSxRQUFRZ0IsS0FBSyxDQUFDLGNBQWNkO2dCQUM1QnlCLGNBQWMsRUFBRTtZQUNsQjtRQUNGLEVBQUUsT0FBT1gsT0FBWTtnQkFJZkEsaUJBRU9BLGtCQUVBQTtZQVBYaEIsUUFBUWdCLEtBQUssQ0FBQyxlQUFlQTtZQUU3QixZQUFZO1lBQ1osSUFBSUEsRUFBQUEsa0JBQUFBLE1BQU1kLFFBQVEsY0FBZGMsc0NBQUFBLGdCQUFnQkQsTUFBTSxNQUFLLEtBQUs7Z0JBQ2xDZixRQUFRZ0IsS0FBSyxDQUFDO1lBQ2hCLE9BQU8sSUFBSUEsRUFBQUEsbUJBQUFBLE1BQU1kLFFBQVEsY0FBZGMsdUNBQUFBLGlCQUFnQkQsTUFBTSxNQUFLLEtBQUs7Z0JBQ3pDZixRQUFRZ0IsS0FBSyxDQUFDO1lBQ2hCLE9BQU8sSUFBSUEsRUFBQUEsbUJBQUFBLE1BQU1kLFFBQVEsY0FBZGMsdUNBQUFBLGlCQUFnQkQsTUFBTSxNQUFLLEtBQUs7Z0JBQ3pDZixRQUFRZ0IsS0FBSyxDQUFDLGtDQUF3QmpCO1lBQ3hDLE9BQU87Z0JBQ0xDLFFBQVFnQixLQUFLLENBQUMsc0JBQVlBLE1BQU1rSSxPQUFPO1lBQ3pDO1lBRUF2SCxjQUFjLEVBQUU7UUFDbEIsU0FBVTtZQUNSRSxXQUFXO1FBQ2I7SUFDRjtJQUVBLFdBQVc7SUFDWCxNQUFNdUcsbUJBQW1CO1FBQ3ZCLElBQUk7WUFDRnBJLFFBQVFDLEdBQUcsQ0FBQyxxQ0FBMkJGO1lBRXZDLE1BQU1HLFdBQVcsTUFBTUosa0JBQWtCQztZQUN6Q0MsUUFBUUMsR0FBRyxDQUFDLHdCQUFjQztZQUUxQixJQUFJQSxTQUFTb0ksSUFBSSxLQUFLLE9BQU9wSSxTQUFTRSxJQUFJLEVBQUU7Z0JBQzFDLE1BQU0rSSxhQUFhakosU0FBU0UsSUFBSTtnQkFDaENKLFFBQVFDLEdBQUcsQ0FBQyxhQUFha0o7Z0JBQ3pCdkcsZ0JBQWdCdUcsV0FBV3BJLE1BQU0sSUFBSTtnQkFDckNmLFFBQVFDLEdBQUcsQ0FBQyx3QkFBY2tKLFdBQVdwSSxNQUFNLEVBQUU7WUFDL0MsT0FBTztnQkFDTGYsUUFBUWdCLEtBQUssQ0FBQyxlQUFlZCxTQUFTZ0osT0FBTztZQUMvQztRQUNGLEVBQUUsT0FBT2xJLE9BQVk7WUFDbkJoQixRQUFRZ0IsS0FBSyxDQUFDLGVBQWVBO1FBQy9CO0lBQ0Y7SUFFQSxXQUFXO0lBQ1gsTUFBTXFILGdCQUFnQjtRQUNwQixJQUFJO2dCQVdFbkk7WUFWSjhELGdCQUFnQjtZQUVoQixzQkFBc0I7WUFDdEIsTUFBTW9GLFNBQVNwQixhQUFhQyxPQUFPLENBQUMsYUFBYSxRQUFRLFdBQVc7WUFFcEUsTUFBTS9ILFdBQVcsTUFBTVIsb0RBQVFBLENBQUMySixlQUFlLENBQUNDLE9BQU9GLFNBQVMsR0FBRztZQUVuRSxjQUFjO1lBQ2QsSUFBSUcsWUFBWSxFQUFFO1lBRWxCLElBQUlySixFQUFBQSxpQkFBQUEsU0FBU0UsSUFBSSxjQUFiRixxQ0FBQUEsZUFBZW9JLElBQUksTUFBSyxLQUFLO29CQUUzQnBJLHFCQUFBQSxpQkFJbUJBO2dCQUx2QiwrQkFBK0I7Z0JBQy9CLEtBQUlBLGtCQUFBQSxTQUFTRSxJQUFJLGNBQWJGLHVDQUFBQSxzQkFBQUEsZ0JBQWVFLElBQUksY0FBbkJGLDBDQUFBQSxvQkFBcUJzSSxJQUFJLEVBQUU7b0JBQzdCZSxZQUFZckosU0FBU0UsSUFBSSxDQUFDQSxJQUFJLENBQUNvSSxJQUFJO2dCQUNyQyxPQUVLLElBQUlnQixNQUFNQyxPQUFPLEVBQUN2SixrQkFBQUEsU0FBU0UsSUFBSSxjQUFiRixzQ0FBQUEsZ0JBQWVFLElBQUksR0FBRztvQkFDM0NtSixZQUFZckosU0FBU0UsSUFBSSxDQUFDQSxJQUFJO2dCQUNoQyxPQUVLLElBQUlvSixNQUFNQyxPQUFPLENBQUN2SixTQUFTRSxJQUFJLEdBQUc7b0JBQ3JDbUosWUFBWXJKLFNBQVNFLElBQUk7Z0JBQzNCO1lBQ0YsT0FFSyxJQUFJb0osTUFBTUMsT0FBTyxDQUFDdkosV0FBVztnQkFDaENxSixZQUFZcko7WUFDZDtZQUVBNEQsU0FBU3lGO1FBQ1gsRUFBRSxPQUFPdkksT0FBTztZQUNkaEIsUUFBUWdCLEtBQUssQ0FBQyxhQUFhQTtZQUMzQjhDLFNBQVMsRUFBRTtRQUNiLFNBQVU7WUFDUkUsZ0JBQWdCO1FBQ2xCO0lBQ0Y7SUFFQSxTQUFTO0lBQ1QsTUFBTTBGLG1CQUFtQixDQUFDQztRQUN4QixJQUFJMUYsZ0JBQWdCMkYsUUFBUSxDQUFDRCxTQUFTO1lBQ3BDLE9BQU87WUFDUHpGLG1CQUFtQjJGLENBQUFBLE9BQVFBLEtBQUtiLE1BQU0sQ0FBQ0wsQ0FBQUEsS0FBTUEsT0FBT2dCO1FBQ3RELE9BQU87WUFDTCxLQUFLO1lBQ0x6RixtQkFBbUIyRixDQUFBQSxPQUFRO3VCQUFJQTtvQkFBTUY7aUJBQU87UUFDOUM7SUFDRjtJQUVBLDBCQUEwQjtJQUMxQixNQUFNRyxtQkFBbUI3SyxrREFBV0EsQ0FBQyxDQUFDOEs7UUFDcEMsWUFBWTtRQUNaQSxFQUFFQyxjQUFjO1FBRWhCLE1BQU1DLFlBQVlGLEVBQUVHLGFBQWE7UUFDakMsTUFBTUMsZUFBZUosRUFBRUssTUFBTSxFQUFFLGFBQWE7UUFFNUMsZUFBZTtRQUNmSCxVQUFVSSxVQUFVLElBQUlGO0lBQzFCLEdBQUcsRUFBRTtJQUVMLFNBQVM7SUFDVCxNQUFNaEMsaUJBQWlCO1FBQ3JCLElBQUk7WUFDRnJELGVBQWU7WUFDZjlFLFFBQVFDLEdBQUcsQ0FBQztZQUlaLE1BQU1DLFdBQVcsTUFBTVU7WUFDdkJaLFFBQVFDLEdBQUcsQ0FBQywyQkFBaUJDO1lBRTdCLFNBQVM7WUFDVCxJQUFJQSxZQUFZQSxTQUFTRSxJQUFJLEVBQUU7Z0JBQzdCSixRQUFRQyxHQUFHLENBQUMsc0JBQVlDLFNBQVNFLElBQUk7Z0JBRXJDLElBQUlrSyxPQUFvQixFQUFFO2dCQUUxQixnREFBZ0Q7Z0JBQ2hELElBQUlwSyxTQUFTRSxJQUFJLENBQUNvSSxJQUFJLElBQUlnQixNQUFNQyxPQUFPLENBQUN2SixTQUFTRSxJQUFJLENBQUNvSSxJQUFJLEdBQUc7b0JBQzNEOEIsT0FBT3BLLFNBQVNFLElBQUksQ0FBQ29JLElBQUk7b0JBQ3pCeEksUUFBUUMsR0FBRyxDQUFDLHdCQUF3QnFLLEtBQUt4QixNQUFNLEVBQUU7Z0JBQ25ELE9BRUssSUFBSVUsTUFBTUMsT0FBTyxDQUFDdkosU0FBU0UsSUFBSSxHQUFHO29CQUNyQ2tLLE9BQU9wSyxTQUFTRSxJQUFJO29CQUNwQkosUUFBUUMsR0FBRyxDQUFDLHFCQUFxQnFLLEtBQUt4QixNQUFNLEVBQUU7Z0JBQ2hELE9BRUssSUFBSTVJLFNBQVNFLElBQUksQ0FBQ0EsSUFBSSxJQUFJRixTQUFTRSxJQUFJLENBQUNBLElBQUksQ0FBQ29JLElBQUksSUFBSWdCLE1BQU1DLE9BQU8sQ0FBQ3ZKLFNBQVNFLElBQUksQ0FBQ0EsSUFBSSxDQUFDb0ksSUFBSSxHQUFHO29CQUNoRzhCLE9BQU9wSyxTQUFTRSxJQUFJLENBQUNBLElBQUksQ0FBQ29JLElBQUk7b0JBQzlCeEksUUFBUUMsR0FBRyxDQUFDLDZCQUE2QnFLLEtBQUt4QixNQUFNLEVBQUU7Z0JBQ3hEO2dCQUVBLFdBQVc7Z0JBQ1g5SSxRQUFRQyxHQUFHLENBQUMsd0JBQWNxSztnQkFDMUJ0SyxRQUFRQyxHQUFHLENBQUM7Z0JBQ1pxSyxLQUFLQyxPQUFPLENBQUMsQ0FBQ0MsS0FBS0M7d0JBT0REO29CQU5oQnhLLFFBQVFDLEdBQUcsQ0FBQyxLQUFXLE9BQU53SyxPQUFNLE1BQUk7d0JBQ3pCRDt3QkFDQUUsUUFBUSxDQUFDLENBQUNGO3dCQUNWRyxRQUFRLFFBQU9ILGdCQUFBQSwwQkFBQUEsSUFBSzdCLEVBQUU7d0JBQ3RCaUMsVUFBVSxRQUFPSixnQkFBQUEsMEJBQUFBLElBQUt2RSxJQUFJO3dCQUMxQjRFLFNBQVMsRUFBRUwsZ0JBQUFBLDBCQUFBQSxJQUFLdkUsSUFBSTt3QkFDcEI2RSxjQUFjTixDQUFBQSxnQkFBQUEsMkJBQUFBLFlBQUFBLElBQUt2RSxJQUFJLGNBQVR1RSxnQ0FBQUEsVUFBV08sSUFBSSxRQUFPO29CQUN0QztnQkFDRjtnQkFFQSxNQUFNQyxZQUFZVixLQUFLdEIsTUFBTSxDQUFDd0IsQ0FBQUE7b0JBQzVCLE1BQU1TLFVBQVVULE9BQ2QsT0FBT0EsSUFBSTdCLEVBQUUsS0FBSyxZQUNsQixPQUFPNkIsSUFBSXZFLElBQUksS0FBSyxZQUNwQnVFLElBQUl2RSxJQUFJLENBQUM4RSxJQUFJLE9BQU87b0JBRXRCLElBQUksQ0FBQ0UsU0FBUzt3QkFDWmpMLFFBQVFDLEdBQUcsQ0FBQyxXQUFXdUssS0FBSzs0QkFDMUJFLFFBQVEsQ0FBQyxDQUFDRjs0QkFDVkcsUUFBUSxRQUFPSCxnQkFBQUEsMEJBQUFBLElBQUs3QixFQUFFOzRCQUN0QmlDLFVBQVUsUUFBT0osZ0JBQUFBLDBCQUFBQSxJQUFLdkUsSUFBSTs0QkFDMUI0RSxTQUFTLEVBQUVMLGdCQUFBQSwwQkFBQUEsSUFBS3ZFLElBQUk7d0JBQ3RCO29CQUNGO29CQUVBLE9BQU9nRjtnQkFDVDtnQkFFQWpMLFFBQVFDLEdBQUcsQ0FBQyxhQUFhK0ssVUFBVWxDLE1BQU07Z0JBQ3pDOUksUUFBUUMsR0FBRyxDQUFDLGFBQWErSztnQkFFekIsSUFBSUEsVUFBVWxDLE1BQU0sR0FBRyxHQUFHO29CQUN4QnBFLGNBQWNzRztvQkFDZGhMLFFBQVFDLEdBQUcsQ0FBQztvQkFDWjtnQkFDRixPQUFPO29CQUNMRCxRQUFRa0wsSUFBSSxDQUFDO2dCQUNmO1lBQ0YsT0FBTztnQkFDTGxMLFFBQVFrTCxJQUFJLENBQUMsa0JBQWtCaEw7WUFDakM7WUFFQSxpQkFBaUI7WUFDakJGLFFBQVFDLEdBQUcsQ0FBQztZQUNaeUUsY0FBYyxFQUFFO1FBRWxCLEVBQUUsT0FBTzFELE9BQVk7Z0JBSVRBLGlCQUNJQSxrQkFDTkE7WUFMUmhCLFFBQVFnQixLQUFLLENBQUMsZUFBZUE7WUFDN0JoQixRQUFRZ0IsS0FBSyxDQUFDLFdBQVc7Z0JBQ3ZCa0ksU0FBU2xJLE1BQU1rSSxPQUFPO2dCQUN0Qm5JLE1BQU0sR0FBRUMsa0JBQUFBLE1BQU1kLFFBQVEsY0FBZGMsc0NBQUFBLGdCQUFnQkQsTUFBTTtnQkFDOUJvSyxVQUFVLEdBQUVuSyxtQkFBQUEsTUFBTWQsUUFBUSxjQUFkYyx1Q0FBQUEsaUJBQWdCbUssVUFBVTtnQkFDdEMvSyxJQUFJLEdBQUVZLG1CQUFBQSxNQUFNZCxRQUFRLGNBQWRjLHVDQUFBQSxpQkFBZ0JaLElBQUk7WUFDNUI7WUFFQSxhQUFhO1lBQ2JzRSxjQUFjLEVBQUU7UUFDbEIsU0FBVTtZQUNSSSxlQUFlO1FBQ2pCO0lBQ0Y7SUFFQSxRQUFRO0lBQ1IsTUFBTXNHLGVBQWU7UUFDbkIsTUFBTUMsWUFBd0I7WUFDNUIxQyxJQUFJMkMsS0FBS0MsR0FBRztZQUNaeEwsVUFBVUE7WUFDVmlGLE9BQU8sSUFBMEIsT0FBdEJ2RCxXQUFXcUgsTUFBTSxHQUFHLEdBQUU7WUFDakM3RCxhQUFhO1lBQ2JDLFlBQVk7WUFDWnNHLFlBQVkvSixXQUFXcUgsTUFBTSxHQUFHO1lBQ2hDL0gsUUFBUTtZQUNSMEssYUFBYTtZQUNiN0YsVUFBVTtZQUNWQyxhQUFhO1lBQ2JDLFVBQVU7WUFDVjRGLGVBQWU7WUFDZkMsb0JBQW9CO1lBQ3BCdkcsV0FBVztZQUNYd0csb0JBQW9CO1lBQ3BCQyxnQkFBZ0I7WUFDaEJDLFdBQVcsSUFBSVIsT0FBT1MsV0FBVztZQUNqQ0MsV0FBVyxJQUFJVixPQUFPUyxXQUFXO1FBQ25DO1FBQ0FwSyxjQUFjO2VBQUlGO1lBQVk0SjtTQUFVO1FBQ3hDLGFBQWE7UUFDYlksZ0JBQWdCWixVQUFVMUMsRUFBRTtJQUM5QjtJQUVBLFdBQVc7SUFDWCxNQUFNdUQsb0JBQW9CLENBQUN2RDtRQUN6QjNGLGtCQUFrQjJGO1FBQ2xCN0Ysd0JBQXdCO0lBQzFCO0lBRUEsU0FBUztJQUNULE1BQU1xSixzQkFBc0I7UUFDMUIsSUFBSSxDQUFDcEosZ0JBQWdCO1FBRXJCLElBQUk7WUFDRkcsY0FBYztZQUVkLFVBQVU7WUFDVixNQUFNM0QsMkVBQW1CQSxDQUFDNk0sWUFBWSxDQUFDcko7WUFFdkMsV0FBVztZQUNYcEIsY0FBY0YsV0FBV3VILE1BQU0sQ0FBQ3FELENBQUFBLFNBQVVBLE9BQU8xRCxFQUFFLEtBQUs1RjtZQUV4RCx1QkFBdUI7WUFDdkIsSUFBSWYscUJBQXFCZSxnQkFBZ0I7Z0JBQ3ZDaEIsa0JBQWtCO2dCQUNsQkUsb0JBQW9CO1lBQ3RCO1lBRUEsU0FBUztZQUNUYSx3QkFBd0I7WUFDeEJFLGtCQUFrQjtZQUVsQixTQUFTO1lBQ1R0QixhQUFhNEssT0FBTyxDQUFDO1FBQ3ZCLEVBQUUsT0FBT3RMLE9BQU87WUFDZGhCLFFBQVFnQixLQUFLLENBQUMsV0FBV0E7WUFDekJVLGFBQWFWLEtBQUssQ0FBQztRQUNyQixTQUFVO1lBQ1JrQyxjQUFjO1FBQ2hCO0lBQ0Y7SUFFQSxPQUFPO0lBQ1AsTUFBTXFKLGVBQWU7UUFDbkIsSUFBSXRKLFlBQVksUUFBUSxhQUFhO1FBQ3JDSCx3QkFBd0I7UUFDeEJFLGtCQUFrQjtJQUNwQjtJQUVBLFNBQVM7SUFDVCxNQUFNd0osb0JBQW9CLENBQUM3RCxJQUFZOEQ7UUFDckM5SyxjQUFjRixXQUFXZ0gsR0FBRyxDQUFDNEQsQ0FBQUEsU0FDM0JBLE9BQU8xRCxFQUFFLEtBQUtBLEtBQUs7Z0JBQUUsR0FBRzBELE1BQU07Z0JBQUVySCxPQUFPeUg7WUFBUyxJQUFJSjtJQUV4RDtJQUVBLFdBQVc7SUFDWCxNQUFNSyxvQkFBb0IsT0FBT0M7WUFDbEJBO1FBQWIsTUFBTUMsUUFBT0Qsc0JBQUFBLE1BQU1FLE1BQU0sQ0FBQ0MsS0FBSyxjQUFsQkgsMENBQUFBLG1CQUFvQixDQUFDLEVBQUU7UUFDcEMsSUFBSUMsTUFBTTtZQUNSLFNBQVM7WUFDVCxNQUFNRyxlQUFlO2dCQUFDO2dCQUFjO2dCQUFhO2dCQUFhO2FBQVk7WUFDMUUsSUFBSSxDQUFDQSxhQUFhbkQsUUFBUSxDQUFDZ0QsS0FBS2hFLElBQUksR0FBRztnQkFDckNsSCxhQUFhVixLQUFLLENBQUM7Z0JBQ25CO1lBQ0Y7WUFFQSxnQkFBZ0I7WUFDaEIsSUFBSTRMLEtBQUtJLElBQUksR0FBRyxLQUFLLE9BQU8sTUFBTTtnQkFDaEN0TCxhQUFhVixLQUFLLENBQUM7Z0JBQ25CO1lBQ0Y7WUFFQSxJQUFJO2dCQUNGaEIsUUFBUUMsR0FBRyxDQUFDLDBCQUFnQjJNLEtBQUszRyxJQUFJO2dCQUVyQyxjQUFjO2dCQUNkOUQsa0JBQWtCMEgsQ0FBQUEsT0FBUSxJQUFJekgsSUFBSXlILE1BQU1vRCxHQUFHLENBQUM7Z0JBRTVDLFVBQVU7Z0JBQ1YsTUFBTUMsYUFBYUMsSUFBSUMsZUFBZSxDQUFDUjtnQkFDdkM3SCxnQkFBZ0I4RSxDQUFBQSxPQUFTO3dCQUFFLEdBQUdBLElBQUk7d0JBQUUzRSxZQUFZZ0k7b0JBQVc7Z0JBRTNELFNBQVM7Z0JBQ1QsTUFBTUcsV0FBVyxNQUFNNU4sc0RBQVNBLENBQUM2TixXQUFXLENBQUNWO2dCQUM3QzVNLFFBQVFDLEdBQUcsQ0FBQyxlQUFlb047Z0JBRTNCLG1CQUFtQjtnQkFDbkJ0SSxnQkFBZ0I4RSxDQUFBQSxPQUFTO3dCQUFFLEdBQUdBLElBQUk7d0JBQUUzRSxZQUFZbUk7b0JBQVM7Z0JBRXpELGVBQWU7Z0JBQ2YsSUFBSXJMLGtCQUFrQjtvQkFDcEJMLGNBQWNrSSxDQUFBQSxPQUFRQSxLQUFLcEIsR0FBRyxDQUFDNEQsQ0FBQUEsU0FDN0JBLE9BQU8xRCxFQUFFLEtBQUszRyxtQkFBbUI7Z0NBQUUsR0FBR3FLLE1BQU07Z0NBQUVuSCxZQUFZbUk7NEJBQVMsSUFBSWhCO2dCQUUzRTtnQkFFQWtCLE1BQU07WUFDUixFQUFFLE9BQU92TSxPQUFPO2dCQUNkaEIsUUFBUWdCLEtBQUssQ0FBQyxlQUFlQTtnQkFDN0JVLGFBQWFWLEtBQUssQ0FBQztnQkFFbkIsY0FBYztnQkFDZCtELGdCQUFnQjhFLENBQUFBLE9BQVM7d0JBQUUsR0FBR0EsSUFBSTt3QkFBRTNFLFlBQVk7b0JBQUc7WUFDckQsU0FBVTtnQkFDUixjQUFjO2dCQUNkL0Msa0JBQWtCMEgsQ0FBQUE7b0JBQ2hCLE1BQU0yRCxTQUFTLElBQUlwTCxJQUFJeUg7b0JBQ3ZCMkQsT0FBT0MsTUFBTSxDQUFDO29CQUNkLE9BQU9EO2dCQUNUO1lBQ0Y7UUFDRjtJQUNGO0lBRUEsU0FBUztJQUNULE1BQU1FLG9CQUFvQixPQUFPZjtZQUNsQkE7UUFBYixNQUFNQyxRQUFPRCxzQkFBQUEsTUFBTUUsTUFBTSxDQUFDQyxLQUFLLGNBQWxCSCwwQ0FBQUEsbUJBQW9CLENBQUMsRUFBRTtRQUNwQyxJQUFJQyxNQUFNO1lBQ1IsU0FBUztZQUNULE1BQU1HLGVBQWU7Z0JBQUM7Z0JBQWE7Z0JBQWE7Z0JBQWE7Z0JBQWE7YUFBWTtZQUN0RixJQUFJLENBQUNBLGFBQWFuRCxRQUFRLENBQUNnRCxLQUFLaEUsSUFBSSxHQUFHO2dCQUNyQzJFLE1BQU07Z0JBQ047WUFDRjtZQUVBLGlCQUFpQjtZQUNqQixJQUFJWCxLQUFLSSxJQUFJLEdBQUcsTUFBTSxPQUFPLE1BQU07Z0JBQ2pDTyxNQUFNO2dCQUNOO1lBQ0Y7WUFFQSxJQUFJO2dCQUNGdk4sUUFBUUMsR0FBRyxDQUFDLDBCQUFnQjJNLEtBQUszRyxJQUFJO2dCQUVyQyxjQUFjO2dCQUNkOUQsa0JBQWtCMEgsQ0FBQUEsT0FBUSxJQUFJekgsSUFBSXlILE1BQU1vRCxHQUFHLENBQUM7Z0JBRTVDLFVBQVU7Z0JBQ1YsTUFBTUMsYUFBYUMsSUFBSUMsZUFBZSxDQUFDUjtnQkFDdkM3SCxnQkFBZ0I4RSxDQUFBQSxPQUFTO3dCQUN2QixHQUFHQSxJQUFJO3dCQUNQbEUsZUFBZTs0QkFDYixHQUFHa0UsS0FBS2xFLGFBQWE7NEJBQ3JCSSxPQUFPO2dDQUNMQyxLQUFLa0g7Z0NBQ0xqSCxNQUFNMkcsS0FBSzNHLElBQUk7NEJBQ2pCO3dCQUNGO29CQUNGO2dCQUVBLFNBQVM7Z0JBQ1QsTUFBTWQsV0FBVyxNQUFNMUYsc0RBQVNBLENBQUM2TixXQUFXLENBQUNWO2dCQUM3QzVNLFFBQVFDLEdBQUcsQ0FBQyxlQUFla0Y7Z0JBRTNCLHFCQUFxQjtnQkFDckJKLGdCQUFnQjhFLENBQUFBLE9BQVM7d0JBQ3ZCLEdBQUdBLElBQUk7d0JBQ1BsRSxlQUFlOzRCQUNiLEdBQUdrRSxLQUFLbEUsYUFBYTs0QkFDckJJLE9BQU87Z0NBQ0xDLEtBQUtiO2dDQUNMYyxNQUFNMkcsS0FBSzNHLElBQUk7NEJBQ2pCO3dCQUNGO29CQUNGO2dCQUVBLGlCQUFpQjtnQkFDakIsSUFBSWpFLGtCQUFrQjtvQkFDcEJMLGNBQWNrSSxDQUFBQSxPQUFRQSxLQUFLcEIsR0FBRyxDQUFDNEQsQ0FBQUEsU0FDN0JBLE9BQU8xRCxFQUFFLEtBQUszRyxtQkFBbUI7Z0NBQy9CLEdBQUdxSyxNQUFNO2dDQUNUMUcsZUFBZTtvQ0FDYixHQUFHMEcsT0FBTzFHLGFBQWE7b0NBQ3ZCSSxPQUFPO3dDQUNMQyxLQUFLYjt3Q0FDTGMsTUFBTTJHLEtBQUszRyxJQUFJO29DQUNqQjtnQ0FDRjs0QkFDRixJQUFJb0c7Z0JBRVI7Z0JBRUFrQixNQUFNO1lBQ1IsRUFBRSxPQUFPdk0sT0FBTztnQkFDZGhCLFFBQVFnQixLQUFLLENBQUMsZUFBZUE7Z0JBQzdCdU0sTUFBTTtnQkFFTixjQUFjO2dCQUNkeEksZ0JBQWdCOEUsQ0FBQUEsT0FBUzt3QkFDdkIsR0FBR0EsSUFBSTt3QkFDUGxFLGVBQWU7NEJBQ2IsR0FBR2tFLEtBQUtsRSxhQUFhOzRCQUNyQkksT0FBTztnQ0FDTEMsS0FBSztnQ0FDTEMsTUFBTTs0QkFDUjt3QkFDRjtvQkFDRjtZQUNGLFNBQVU7Z0JBQ1IsY0FBYztnQkFDZDlELGtCQUFrQjBILENBQUFBO29CQUNoQixNQUFNMkQsU0FBUyxJQUFJcEwsSUFBSXlIO29CQUN2QjJELE9BQU9DLE1BQU0sQ0FBQztvQkFDZCxPQUFPRDtnQkFDVDtZQUNGO1FBQ0Y7SUFDRjtJQUVBLFdBQVc7SUFDWCxNQUFNRyxxQkFBcUI7UUFDekIsTUFBTUMsUUFBUTFILFNBQVMySCxhQUFhLENBQUM7UUFDckNELE1BQU1oRixJQUFJLEdBQUc7UUFDYmdGLE1BQU1FLE1BQU0sR0FBRztRQUNmRixNQUFNRyxRQUFRLEdBQUcsQ0FBQ2hFLElBQU0yRCxrQkFBa0IzRDtRQUMxQzZELE1BQU1JLEtBQUs7SUFDYjtJQUVBLFNBQVM7SUFDVCxNQUFNQyx5QkFBeUIsT0FBT3RCO1lBQ3ZCQTtRQUFiLE1BQU1DLFFBQU9ELHNCQUFBQSxNQUFNRSxNQUFNLENBQUNDLEtBQUssY0FBbEJILDBDQUFBQSxtQkFBb0IsQ0FBQyxFQUFFO1FBQ3BDLElBQUlDLE1BQU07WUFDUixTQUFTO1lBQ1QsTUFBTUcsZUFBZTtnQkFDbkI7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7YUFDRDtZQUVELElBQUksQ0FBQ0EsYUFBYW5ELFFBQVEsQ0FBQ2dELEtBQUtoRSxJQUFJLEdBQUc7Z0JBQ3JDMkUsTUFBTTtnQkFDTjtZQUNGO1lBRUEsZ0JBQWdCO1lBQ2hCLElBQUlYLEtBQUtJLElBQUksR0FBRyxLQUFLLE9BQU8sTUFBTTtnQkFDaENPLE1BQU07Z0JBQ047WUFDRjtZQUVBLElBQUk7Z0JBQ0Z2TixRQUFRQyxHQUFHLENBQUMsMEJBQWdCMk0sS0FBSzNHLElBQUk7Z0JBRXJDLGNBQWM7Z0JBQ2Q5RCxrQkFBa0IwSCxDQUFBQSxPQUFRLElBQUl6SCxJQUFJeUgsTUFBTW9ELEdBQUcsQ0FBQztnQkFFNUMsU0FBUztnQkFDVCxNQUFNaUIsY0FBYyxNQUFNek8sc0RBQVNBLENBQUM2TixXQUFXLENBQUNWO2dCQUNoRDVNLFFBQVFDLEdBQUcsQ0FBQyxlQUFlaU87Z0JBRTNCLGVBQWU7Z0JBQ2ZuSixnQkFBZ0I4RSxDQUFBQSxPQUFTO3dCQUN2QixHQUFHQSxJQUFJO3dCQUNQbEUsZUFBZTs0QkFDYixHQUFHa0UsS0FBS2xFLGFBQWE7NEJBQ3JCRSxhQUFhOzRCQUNiSyxVQUFVO2dDQUNSRixLQUFLa0k7Z0NBQ0xqSSxNQUFNMkcsS0FBSzNHLElBQUk7NEJBQ2pCO3dCQUNGO29CQUNGO2dCQUVBc0gsTUFBTTtZQUNSLEVBQUUsT0FBT3ZNLE9BQU87Z0JBQ2RoQixRQUFRZ0IsS0FBSyxDQUFDLGVBQWVBO2dCQUM3QnVNLE1BQU07WUFDUixTQUFVO2dCQUNSLGNBQWM7Z0JBQ2RwTCxrQkFBa0IwSCxDQUFBQTtvQkFDaEIsTUFBTTJELFNBQVMsSUFBSXBMLElBQUl5SDtvQkFDdkIyRCxPQUFPQyxNQUFNLENBQUM7b0JBQ2QsT0FBT0Q7Z0JBQ1Q7WUFDRjtRQUNGO0lBQ0Y7SUFFQSxXQUFXO0lBQ1gsTUFBTVcsMEJBQTBCO1FBQzlCLE1BQU1QLFFBQVExSCxTQUFTMkgsYUFBYSxDQUFDO1FBQ3JDRCxNQUFNaEYsSUFBSSxHQUFHO1FBQ2JnRixNQUFNRSxNQUFNLEdBQUc7UUFDZkYsTUFBTUcsUUFBUSxHQUFHLENBQUNoRSxJQUFNa0UsdUJBQXVCbEU7UUFDL0M2RCxNQUFNSSxLQUFLO0lBQ2I7SUFFQSxXQUFXO0lBQ1gsTUFBTUksK0JBQStCLE9BQU96QjtZQUM3QkE7UUFBYixNQUFNQyxRQUFPRCxzQkFBQUEsTUFBTUUsTUFBTSxDQUFDQyxLQUFLLGNBQWxCSCwwQ0FBQUEsbUJBQW9CLENBQUMsRUFBRTtRQUNwQyxJQUFJQyxNQUFNO1lBQ1IsU0FBUztZQUNULE1BQU1HLGVBQWU7Z0JBQ25CO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2FBQ0Q7WUFFRCxJQUFJLENBQUNBLGFBQWFuRCxRQUFRLENBQUNnRCxLQUFLaEUsSUFBSSxHQUFHO2dCQUNyQzJFLE1BQU07Z0JBQ047WUFDRjtZQUVBLGdCQUFnQjtZQUNoQixJQUFJWCxLQUFLSSxJQUFJLEdBQUcsS0FBSyxPQUFPLE1BQU07Z0JBQ2hDTyxNQUFNO2dCQUNOO1lBQ0Y7WUFFQSxJQUFJO2dCQUNGdk4sUUFBUUMsR0FBRyxDQUFDLDBCQUFnQjJNLEtBQUszRyxJQUFJO2dCQUVyQyxjQUFjO2dCQUNkOUQsa0JBQWtCMEgsQ0FBQUEsT0FBUSxJQUFJekgsSUFBSXlILE1BQU1vRCxHQUFHLENBQUMsWUFBdUIsT0FBWDNCLEtBQUtDLEdBQUc7Z0JBRWhFLFNBQVM7Z0JBQ1QsTUFBTThDLGNBQWMsTUFBTTVPLHNEQUFTQSxDQUFDNk4sV0FBVyxDQUFDVjtnQkFDaEQ1TSxRQUFRQyxHQUFHLENBQUMsZUFBZW9PO2dCQUUzQixZQUFZO2dCQUNaLE1BQU1DLGNBQWM7b0JBQ2xCMUYsTUFBTWdFLEtBQUtoRSxJQUFJO29CQUNmM0MsTUFBTTJHLEtBQUszRyxJQUFJO29CQUNmRCxLQUFLcUk7Z0JBQ1A7Z0JBRUF0SixnQkFBZ0I4RSxDQUFBQSxPQUFTO3dCQUN2QixHQUFHQSxJQUFJO3dCQUNQcEUsbUJBQW1COytCQUFJb0UsS0FBS3BFLGlCQUFpQjs0QkFBRTZJO3lCQUFZO29CQUM3RDtnQkFFQWYsTUFBTTtZQUNSLEVBQUUsT0FBT3ZNLE9BQU87Z0JBQ2RoQixRQUFRZ0IsS0FBSyxDQUFDLGVBQWVBO2dCQUM3QnVNLE1BQU07WUFDUixTQUFVO2dCQUNSLGNBQWM7Z0JBQ2RwTCxrQkFBa0IwSCxDQUFBQTtvQkFDaEIsTUFBTTJELFNBQVMsSUFBSXBMLElBQUl5SDtvQkFDdkIscUJBQXFCO29CQUNyQkwsTUFBTStFLElBQUksQ0FBQ2YsUUFBUWpELE9BQU8sQ0FBQ2lFLENBQUFBO3dCQUN6QixJQUFJQSxLQUFLQyxVQUFVLENBQUMsY0FBYzs0QkFDaENqQixPQUFPQyxNQUFNLENBQUNlO3dCQUNoQjtvQkFDRjtvQkFDQSxPQUFPaEI7Z0JBQ1Q7WUFDRjtRQUNGO0lBQ0Y7SUFFQSxhQUFhO0lBQ2IsTUFBTWtCLGdDQUFnQztRQUNwQyxNQUFNZCxRQUFRMUgsU0FBUzJILGFBQWEsQ0FBQztRQUNyQ0QsTUFBTWhGLElBQUksR0FBRztRQUNiZ0YsTUFBTUUsTUFBTSxHQUFHO1FBQ2ZGLE1BQU1HLFFBQVEsR0FBRyxDQUFDaEUsSUFBTXFFLDZCQUE2QnJFO1FBQ3JENkQsTUFBTUksS0FBSztJQUNiO0lBRUEsU0FBUztJQUNULE1BQU1XLHlCQUF5QixDQUFDbEU7UUFDOUIxRixnQkFBZ0I4RSxDQUFBQSxPQUFTO2dCQUN2QixHQUFHQSxJQUFJO2dCQUNQcEUsbUJBQW1Cb0UsS0FBS3BFLGlCQUFpQixDQUFDdUQsTUFBTSxDQUFDLENBQUM0RixHQUFHQyxJQUFNQSxNQUFNcEU7WUFDbkU7SUFDRjtJQUVBLFdBQVc7SUFDWCxNQUFNcUUsc0JBQXNCO1FBQzFCL0osZ0JBQWdCOEUsQ0FBQUEsT0FBUztnQkFDdkIsR0FBR0EsSUFBSTtnQkFDUG5FLGNBQWM7dUJBQUltRSxLQUFLbkUsWUFBWTtvQkFBRTt3QkFBRVYsT0FBTzt3QkFBSXNCLFNBQVM7b0JBQUc7aUJBQUU7WUFDbEU7SUFDRjtJQUVBLE1BQU15SSx5QkFBeUIsQ0FBQ3RFO1FBQzlCMUYsZ0JBQWdCOEUsQ0FBQUEsT0FBUztnQkFDdkIsR0FBR0EsSUFBSTtnQkFDUG5FLGNBQWNtRSxLQUFLbkUsWUFBWSxDQUFDc0QsTUFBTSxDQUFDLENBQUM0RixHQUFHQyxJQUFNQSxNQUFNcEU7WUFDekQ7SUFDRjtJQUVBLE1BQU11RSwwQkFBMEIsQ0FBQ3ZFLE9BQWV6RjtRQUM5Q0QsZ0JBQWdCOEUsQ0FBQUE7WUFDZCxNQUFNb0Ysa0JBQWtCO21CQUFJcEYsS0FBS25FLFlBQVk7YUFBQztZQUM5Q3VKLGVBQWUsQ0FBQ3hFLE1BQU0sR0FBRztnQkFBRSxHQUFHd0UsZUFBZSxDQUFDeEUsTUFBTTtnQkFBRXpGO1lBQU07WUFDNUQsT0FBTztnQkFBRSxHQUFHNkUsSUFBSTtnQkFBRW5FLGNBQWN1SjtZQUFnQjtRQUNsRDtJQUNGO0lBRUEsTUFBTUMsNEJBQTRCLENBQUN6RSxPQUFlbkU7UUFDaER2QixnQkFBZ0I4RSxDQUFBQTtZQUNkLE1BQU1vRixrQkFBa0I7bUJBQUlwRixLQUFLbkUsWUFBWTthQUFDO1lBQzlDdUosZUFBZSxDQUFDeEUsTUFBTSxHQUFHO2dCQUFFLEdBQUd3RSxlQUFlLENBQUN4RSxNQUFNO2dCQUFFbkU7WUFBUTtZQUM5RCxPQUFPO2dCQUFFLEdBQUd1RCxJQUFJO2dCQUFFbkUsY0FBY3VKO1lBQWdCO1FBQ2xEO0lBQ0Y7SUFFQSw2QkFBNkI7SUFDN0IsTUFBTUUsNEJBQTRCLENBQUN6SjtRQUNqQyxPQUFPQSxhQUFhK0MsR0FBRyxDQUFDMkcsQ0FBQUEsT0FBUztnQkFDL0JwSyxPQUFPb0ssS0FBS3BLLEtBQUs7Z0JBQ2pCc0IsU0FBUzhJLEtBQUs5SSxPQUFPLENBQUMrSSxLQUFLLENBQUMsTUFBTXJHLE1BQU0sQ0FBQ3NHLENBQUFBLE9BQVFBLEtBQUt2RSxJQUFJLElBQUksYUFBYTtZQUM3RTtJQUNGO0lBRUEsMEJBQTBCO0lBQzFCLE1BQU13RSxtQkFBbUIsQ0FBQ2xEO1lBU3BCQSw2QkFBQUEsdUJBS0FBLGdDQUFBQTtRQWJKLE1BQU1tRCxTQUFTLEVBQUU7UUFFakIsU0FBUztRQUNULElBQUluRCxPQUFPbkgsVUFBVSxJQUFJbUgsT0FBT25ILFVBQVUsQ0FBQ3VKLFVBQVUsQ0FBQyxVQUFVO1lBQzlEZSxPQUFPQyxJQUFJLENBQUM7UUFDZDtRQUVBLFNBQVM7UUFDVCxJQUFJcEQsRUFBQUEsd0JBQUFBLE9BQU8xRyxhQUFhLGNBQXBCMEcsNkNBQUFBLDhCQUFBQSxzQkFBc0J0RyxLQUFLLGNBQTNCc0csa0RBQUFBLDRCQUE2QnJHLEdBQUcsS0FBSXFHLE9BQU8xRyxhQUFhLENBQUNJLEtBQUssQ0FBQ0MsR0FBRyxDQUFDeUksVUFBVSxDQUFDLFVBQVU7WUFDMUZlLE9BQU9DLElBQUksQ0FBQztRQUNkO1FBRUEsU0FBUztRQUNULElBQUlwRCxFQUFBQSx5QkFBQUEsT0FBTzFHLGFBQWEsY0FBcEIwRyw4Q0FBQUEsaUNBQUFBLHVCQUFzQm5HLFFBQVEsY0FBOUJtRyxxREFBQUEsK0JBQWdDckcsR0FBRyxLQUFJcUcsT0FBTzFHLGFBQWEsQ0FBQ08sUUFBUSxDQUFDRixHQUFHLENBQUN5SSxVQUFVLENBQUMsVUFBVTtZQUNoR2UsT0FBT0MsSUFBSSxDQUFDO1FBQ2Q7UUFFQSxTQUFTO1FBQ1QsSUFBSXBELE9BQU9xRCxtQkFBbUIsRUFBRTtZQUM5QnJELE9BQU9xRCxtQkFBbUIsQ0FBQ25GLE9BQU8sQ0FBQyxDQUFDb0YsVUFBVWxGO2dCQUM1QyxJQUFJa0YsU0FBUzNKLEdBQUcsSUFBSTJKLFNBQVMzSixHQUFHLENBQUN5SSxVQUFVLENBQUMsVUFBVTtvQkFDcERlLE9BQU9DLElBQUksQ0FBQyxPQUFpQixPQUFWaEYsUUFBUTtnQkFDN0I7WUFDRjtRQUNGO1FBRUEsT0FBTytFO0lBQ1Q7SUFFQSxZQUFZO0lBQ1osTUFBTUksYUFBYSxPQUFPdkQ7UUFDeEIsSUFBSTtnQkFrQlVBLDZCQUFBQSx1QkFDR0EsZ0NBQUFBO1lBbEJmLFVBQVU7WUFDVixJQUFJLENBQUNBLE9BQU9ySCxLQUFLLElBQUlxSCxPQUFPckgsS0FBSyxDQUFDK0YsSUFBSSxPQUFPLElBQUk7Z0JBQy9DLE1BQU0sSUFBSThFLE1BQU07WUFDbEI7WUFFQSxnQkFBZ0I7WUFDaEIsTUFBTUMsYUFBYVAsaUJBQWlCbEQ7WUFDcEMsSUFBSXlELFdBQVdoSCxNQUFNLEdBQUcsR0FBRztnQkFDekIsTUFBTSxJQUFJK0csTUFBTSwwQkFBK0MsT0FBckJDLFdBQVdDLElBQUksQ0FBQztZQUM1RDtZQUVBLFNBQVM7WUFDVCxNQUFNQyxhQUFhO2dCQUNqQmpRLFVBQVVBO2dCQUNWaUYsT0FBT3FILE9BQU9ySCxLQUFLO2dCQUNuQkMsYUFBYW9ILE9BQU9wSCxXQUFXLElBQUk7Z0JBQ25DQyxZQUFZbUgsT0FBT25ILFVBQVUsSUFBSTtnQkFDakNVLFVBQVV5RyxFQUFBQSx3QkFBQUEsT0FBTzFHLGFBQWEsY0FBcEIwRyw2Q0FBQUEsOEJBQUFBLHNCQUFzQnRHLEtBQUssY0FBM0JzRyxrREFBQUEsNEJBQTZCckcsR0FBRyxJQUFHLElBQUk7Z0JBQ2pESCxhQUFhd0csRUFBQUEseUJBQUFBLE9BQU8xRyxhQUFhLGNBQXBCMEcsOENBQUFBLGlDQUFBQSx1QkFBc0JuRyxRQUFRLGNBQTlCbUcscURBQUFBLCtCQUFnQ3JHLEdBQUcsSUFBRyxJQUFJO2dCQUN2REYsVUFBVTtnQkFDVjRGLGVBQWU7Z0JBQ2YvRixlQUFlMEcsT0FBTzFHLGFBQWEsSUFBSSxDQUFDO2dCQUN4Q0QsY0FBY3lKLDBCQUEwQjlDLE9BQU8zRyxZQUFZLElBQUksRUFBRTtnQkFDakVnSyxxQkFBcUJyRCxPQUFPcUQsbUJBQW1CLElBQUksRUFBRTtnQkFDckRsRSxZQUFZYSxPQUFPYixVQUFVLElBQUk7WUFDbkM7WUFFQXhMLFFBQVFDLEdBQUcsQ0FBQywwQkFBZ0IrUDtZQUU1QixJQUFJblA7WUFFSixlQUFlO1lBQ2YsSUFBSXdMLE9BQU8xRCxFQUFFLElBQUkwRCxPQUFPMUQsRUFBRSxHQUFHLFNBQVM7Z0JBQ3BDLGNBQWM7Z0JBQ2QzSSxRQUFRQyxHQUFHLENBQUM7Z0JBQ1osTUFBTSxFQUFFRyxNQUFNRixRQUFRLEVBQUUsR0FBRyxNQUFNVixzREFBU0EsQ0FBQ3lRLFlBQVksQ0FBQ0Q7Z0JBQ3hEblAsU0FBU1g7Z0JBQ1RGLFFBQVFDLEdBQUcsQ0FBQztZQUNkLE9BQU87Z0JBQ0wsZUFBZTtnQkFDZkQsUUFBUUMsR0FBRyxDQUFDLHdCQUFjb00sT0FBTzFELEVBQUU7Z0JBQ25DLE1BQU0sRUFBRXZJLE1BQU1GLFFBQVEsRUFBRSxHQUFHLE1BQU1WLHNEQUFTQSxDQUFDMFEsWUFBWSxDQUFDN0QsT0FBTzFELEVBQUUsRUFBRXFIO2dCQUNuRW5QLFNBQVNYO2dCQUNURixRQUFRQyxHQUFHLENBQUM7WUFDZDtZQUVBLE9BQU9ZO1FBQ1QsRUFBRSxPQUFPRyxPQUFPO1lBQ2RoQixRQUFRZ0IsS0FBSyxDQUFDLGFBQWFBO1lBQzNCLE1BQU1BO1FBQ1I7SUFDRjtJQUVBLFVBQVU7SUFDVixNQUFNbVAsc0JBQXNCO1FBQzFCLElBQUksQ0FBQ25PLGtCQUFrQjtZQUNyQnVMLE1BQU07WUFDTjtRQUNGO1FBRUEsTUFBTTZDLGlCQUFpQjNPLFdBQVc0TyxJQUFJLENBQUNoRSxDQUFBQSxTQUFVQSxPQUFPMUQsRUFBRSxLQUFLM0c7UUFDL0QsSUFBSSxDQUFDb08sZ0JBQWdCO1lBQ25CN0MsTUFBTTtZQUNOO1FBQ0Y7UUFFQSxhQUFhO1FBQ2IsSUFBSTZDLGVBQWVyUCxNQUFNLEtBQUssR0FBRztZQUMvQndNLE1BQU07WUFDTjtRQUNGO1FBRUEsSUFBSTtZQUNGakwsZ0JBQWdCO1lBQ2hCdEMsUUFBUUMsR0FBRyxDQUFDO1lBQ1pELFFBQVFDLEdBQUcsQ0FBQyxrQ0FBd0IrQixrQkFBa0IsT0FBTyxPQUFPQTtZQUNwRWhDLFFBQVFDLEdBQUcsQ0FBQyxzQkFBWW1RO1lBQ3hCcFEsUUFBUUMsR0FBRyxDQUFDLHdCQUFjbVEsZUFBZXpILEVBQUUsRUFBRSxPQUFPLE9BQU95SCxlQUFlekgsRUFBRTtZQUM1RTNJLFFBQVFDLEdBQUcsQ0FBQyw0QkFBa0J3QixXQUFXZ0gsR0FBRyxDQUFDQyxDQUFBQSxJQUFNO29CQUFFQyxJQUFJRCxFQUFFQyxFQUFFO29CQUFFQyxNQUFNLE9BQU9GLEVBQUVDLEVBQUU7b0JBQUUzRCxPQUFPMEQsRUFBRTFELEtBQUs7Z0JBQUM7WUFFakcsY0FBYztZQUNkLE1BQU1zTCxvQkFBb0JGLGVBQWV6SCxFQUFFO1lBQzNDM0ksUUFBUUMsR0FBRyxDQUFDLDJCQUFpQnFRLG1CQUFtQixPQUFPLE9BQU9BO1lBRTlELE1BQU0sRUFBRWxRLE1BQU1GLFFBQVEsRUFBRSxHQUFHLE1BQU1WLHNEQUFTQSxDQUFDK1EsYUFBYSxDQUFDRDtZQUV6RCxJQUFJcFEsU0FBU29JLElBQUksS0FBSyxLQUFLO2dCQUN6QnRJLFFBQVFDLEdBQUcsQ0FBQyxhQUFhQyxTQUFTRSxJQUFJO2dCQUN0Q21OLE1BQU07Z0JBRU4sY0FBYztnQkFDZCxNQUFNckY7WUFDUixPQUFPO2dCQUNMbEksUUFBUWdCLEtBQUssQ0FBQyxhQUFhZCxTQUFTZ0osT0FBTztnQkFDM0NxRSxNQUFNck4sU0FBU2dKLE9BQU8sSUFBSTtZQUM1QjtRQUNGLEVBQUUsT0FBT2xJLE9BQVk7Z0JBRU1BLGlCQUdyQkEsc0JBQUFBO1lBSkpoQixRQUFRZ0IsS0FBSyxDQUFDLGFBQWFBO1lBQzNCaEIsUUFBUWdCLEtBQUssQ0FBQyxZQUFXQSxrQkFBQUEsTUFBTWQsUUFBUSxjQUFkYyxzQ0FBQUEsZ0JBQWdCWixJQUFJO1lBRTdDLFlBQVk7WUFDWixLQUFJWSxtQkFBQUEsTUFBTWQsUUFBUSxjQUFkYyx3Q0FBQUEsdUJBQUFBLGlCQUFnQlosSUFBSSxjQUFwQlksMkNBQUFBLHFCQUFzQmtJLE9BQU8sRUFBRTtnQkFDakNxRSxNQUFNdk0sTUFBTWQsUUFBUSxDQUFDRSxJQUFJLENBQUM4SSxPQUFPO1lBQ25DLE9BQU8sSUFBSWxJLE1BQU1rSSxPQUFPLEVBQUU7Z0JBQ3hCcUUsTUFBTXZNLE1BQU1rSSxPQUFPO1lBQ3JCLE9BQU87Z0JBQ0xxRSxNQUFNO1lBQ1I7UUFDRixTQUFVO1lBQ1JqTCxnQkFBZ0I7UUFDbEI7SUFDRjtJQUVBLFNBQVM7SUFDVCxNQUFNa08sYUFBYTtRQUNqQixJQUFJO1lBQ0ZoTyxjQUFjO1lBQ2QsY0FBYztZQUNkLElBQUlOLGVBQWU4SyxJQUFJLEdBQUcsR0FBRztnQkFDM0JPLE1BQU07Z0JBQ04vSyxjQUFjO2dCQUNkO1lBQ0Y7WUFFQXhDLFFBQVFDLEdBQUcsQ0FBQztZQUVaLHlCQUF5QjtZQUN6QixJQUFJK0Isb0JBQW9CRixtQkFBbUIsVUFBVTtnQkFDbkQsTUFBTXNPLGlCQUFpQjNPLFdBQVc0TyxJQUFJLENBQUMzSCxDQUFBQSxJQUFLQSxFQUFFQyxFQUFFLEtBQUszRztnQkFDckQsSUFBSW9PLGdCQUFnQjt3QkFXSzVPO29CQVZ2QixTQUFTO29CQUNULE1BQU1pUCxnQkFBZ0I7d0JBQ3BCLEdBQUdMLGNBQWM7d0JBQ2pCcEwsT0FBT3hELGFBQWF3RCxLQUFLO3dCQUN6QkMsYUFBYXpELGFBQWF5RCxXQUFXO3dCQUNyQ0MsWUFBWTFELGFBQWEwRCxVQUFVO3dCQUNuQ1UsVUFBVXBFLGFBQWE2RCxjQUFjLEdBQUcsSUFBSTt3QkFDNUNRLGFBQWFyRSxhQUFhZ0UsbUJBQW1CLEdBQUcsSUFBSTt3QkFDcERHLGVBQWVuRSxhQUFhbUUsYUFBYTt3QkFDekNELGNBQWNsRSxhQUFha0UsWUFBWTt3QkFDdkNnSyxxQkFBcUJsTyxFQUFBQSxrQ0FBQUEsYUFBYWlFLGlCQUFpQixjQUE5QmpFLHNEQUFBQSxnQ0FBZ0NpSCxHQUFHLENBQUNpSSxDQUFBQSxXQUFhO2dDQUNwRTFMLE9BQU8wTCxTQUFTekssSUFBSTtnQ0FDcEJELEtBQUswSyxTQUFTMUssR0FBRztnQ0FDakJmLGFBQWF5TCxTQUFTekssSUFBSTs0QkFDNUIsUUFBTyxFQUFFO29CQUNYO29CQUVBLGNBQWM7b0JBQ2R0RSxjQUFja0ksQ0FBQUEsT0FBUUEsS0FBS3BCLEdBQUcsQ0FBQzRELENBQUFBLFNBQzdCQSxPQUFPMUQsRUFBRSxLQUFLM0csbUJBQW1CeU8sZ0JBQWdCcEU7b0JBR25ELFVBQVU7b0JBQ1YsTUFBTXVELFdBQVdhO2dCQUNuQjtZQUNGO1lBRUEsV0FBVztZQUNYLE1BQU1yUSxPQUFPO2dCQUNYNEUsT0FBTzNCO2dCQUNQRTtnQkFDQUU7Z0JBQ0FoQztZQUNGO1lBRUFMLE9BQU9oQjtZQUNQZTtZQUVBbkIsUUFBUUMsR0FBRyxDQUFDO1FBQ2QsRUFBRSxPQUFPZSxPQUFZO1lBQ25CaEIsUUFBUWdCLEtBQUssQ0FBQyxXQUFXQTtZQUN6QnVNLE1BQU0sU0FBZ0MsT0FBdkJ2TSxNQUFNa0ksT0FBTyxJQUFJO1FBQ2xDLFNBQVU7WUFDUjFHLGNBQWM7UUFDaEI7SUFDRjtJQUVBLFNBQVM7SUFDVCxNQUFNbU8sZ0JBQWdCO1FBQ3BCLGtCQUFrQjtRQUNsQixJQUFJaE8saUJBQWlCLEdBQUc7WUFDdEI7UUFDRjtRQUVBLElBQUk7WUFDRkQsc0JBQXNCO1lBRXRCLFVBQVU7WUFDVixJQUFJakIsV0FBV3FILE1BQU0sS0FBSyxHQUFHO2dCQUMzQnlFLE1BQU07Z0JBQ047WUFDRjtZQUVBdk4sUUFBUUMsR0FBRyxDQUFDLCtCQUFxQkY7WUFFakMsTUFBTSxFQUFFSyxNQUFNRixRQUFRLEVBQUUsR0FBRyxNQUFNVixzREFBU0EsQ0FBQ29SLG1CQUFtQixDQUFDN1E7WUFFL0QsSUFBSUcsU0FBU29JLElBQUksS0FBSyxLQUFLO2dCQUN6QnRJLFFBQVFDLEdBQUcsQ0FBQyxlQUFlQyxTQUFTRSxJQUFJO2dCQUV4QyxTQUFTO2dCQUNULE1BQU15USxjQUFjM1EsU0FBU0UsSUFBSTtnQkFDakMsSUFBSTBRLGlCQUFpQixRQUEwQixPQUFsQkQsWUFBWTdMLEtBQUssRUFBQztnQkFFL0MsbUJBQW1CO2dCQUNuQixJQUFJNkwsWUFBWUUsWUFBWSxFQUFFO29CQUM1QixNQUFNQyxRQUFRSCxZQUFZRSxZQUFZO29CQUN0Q0Qsa0JBQWtCLHFCQUEwREQsT0FBckNBLFlBQVlJLFlBQVksRUFBQyxjQUFvREQsT0FBeENILFlBQVlLLGdCQUFnQixFQUFDLGFBQThDRixPQUFuQ0EsTUFBTUcsZ0JBQWdCLEVBQUMsY0FBa0RILE9BQXRDQSxNQUFNSSxtQkFBbUIsRUFBQyxjQUFzQyxPQUExQkosTUFBTUssbUJBQW1CLEVBQUM7b0JBRXZOLElBQUlMLE1BQU1NLGtCQUFrQixHQUFHLEdBQUc7d0JBQ2hDLE1BQU1DLGtCQUFrQkMsS0FBS0MsS0FBSyxDQUFDVCxNQUFNTSxrQkFBa0IsR0FBRzt3QkFDOURSLGtCQUFrQixhQUE2QixPQUFoQlMsaUJBQWdCO29CQUNqRDtnQkFDRjtnQkFFQWhFLE1BQU11RDtnQkFFTixhQUFhO2dCQUNibE8sZ0JBQWdCO2dCQUVoQixjQUFjO2dCQUNkLE1BQU1zRjtnQkFFTixZQUFZO2dCQUNaOUcsT0FBTztvQkFDTHdILE1BQU07b0JBQ043SSxVQUFVQTtvQkFDVm1KLFNBQVM7Z0JBQ1g7WUFDRixPQUFPO2dCQUNMbEosUUFBUWdCLEtBQUssQ0FBQyxlQUFlZCxTQUFTZ0osT0FBTztnQkFDN0NxRSxNQUFNck4sU0FBU2dKLE9BQU8sSUFBSTtZQUM1QjtRQUNGLEVBQUUsT0FBT2xJLE9BQVk7Z0JBS2ZBLHNCQUFBQTtZQUpKaEIsUUFBUWdCLEtBQUssQ0FBQyxlQUFlQTtZQUU3QixZQUFZO1lBQ1osSUFBSTBRLGVBQWU7WUFDbkIsS0FBSTFRLGtCQUFBQSxNQUFNZCxRQUFRLGNBQWRjLHVDQUFBQSx1QkFBQUEsZ0JBQWdCWixJQUFJLGNBQXBCWSwyQ0FBQUEscUJBQXNCa0ksT0FBTyxFQUFFO2dCQUNqQ3dJLGVBQWUxUSxNQUFNZCxRQUFRLENBQUNFLElBQUksQ0FBQzhJLE9BQU87WUFDNUMsT0FBTyxJQUFJbEksTUFBTWtJLE9BQU8sRUFBRTtnQkFDeEJ3SSxlQUFlMVEsTUFBTWtJLE9BQU87WUFDOUI7WUFFQXFFLE1BQU1tRTtRQUNSLFNBQVU7WUFDUmhQLHNCQUFzQjtRQUN4QjtJQUNGO0lBRUEsbUJBQW1CO0lBQ25CLE1BQU1pUCxpQkFBaUI7UUFDckJuQjtJQUNGO0lBRUEsU0FBUztJQUNULE1BQU1vQixvQkFBb0I7UUFDeEI3UCxrQkFBa0I7UUFDbEJFLG9CQUFvQjtJQUN0QjtJQUVBLFNBQVM7SUFDVCxNQUFNNFAsdUJBQXVCLENBQUNDO1FBQzVCL00sZ0JBQWdCOEUsQ0FBQUEsT0FBUztnQkFDdkIsR0FBR0EsSUFBSTtnQkFDUG5ELGtCQUFrQm9MLFNBQVNDLFlBQVk7WUFDekM7UUFDQTNPLHdCQUF3QjtJQUMxQjtJQUVBLFdBQVc7SUFDWCxNQUFNNE8sMkJBQTJCO1FBQy9CNU8sd0JBQXdCO0lBQzFCO0lBRUEsV0FBVztJQUNYLE1BQU02SSxrQkFBa0IsT0FBT3ZMO1FBQzdCcUIsa0JBQWtCO1FBQ2xCRSxvQkFBb0J2QjtRQUVwQixtQkFBbUI7UUFDbkIsSUFBSW1ELE1BQU1pRixNQUFNLEtBQUssS0FBSyxDQUFDL0UsY0FBYztZQUN2Q3NFO1FBQ0Y7UUFFQSwyQkFBMkI7UUFDM0IsTUFBTStILGlCQUFpQjNPLFdBQVc0TyxJQUFJLENBQUNoRSxDQUFBQSxTQUFVQSxPQUFPMUQsRUFBRSxLQUFLakk7UUFDL0RWLFFBQVFDLEdBQUcsQ0FBQyx1QkFBYW1RO1FBRXpCLElBQUlBLGdCQUFnQjtZQUNsQixJQUFJO2dCQUNGcFEsUUFBUUMsR0FBRyxDQUFDLGlDQUF1QkYsVUFBVSxhQUFhVztnQkFFMUQsY0FBYztnQkFDZCxNQUFNLEVBQUVOLE1BQU02UixHQUFHLEVBQUUsR0FBRyxNQUFNeFIsa0JBQWtCVixVQUFVVztnQkFFeEQsSUFBSXVSLElBQUkzSixJQUFJLEtBQUssT0FBTzJKLElBQUk3UixJQUFJLEVBQUU7d0JBS044Uix1Q0FPQ0E7b0JBWDNCLE1BQU1BLG1CQUFtQkQsSUFBSTdSLElBQUk7b0JBQ2pDSixRQUFRQyxHQUFHLENBQUMsY0FBY2lTO29CQUUxQixpREFBaUQ7b0JBQ2pELE1BQU16TSxvQkFBb0J5TSxFQUFBQSx3Q0FBQUEsaUJBQWlCeEMsbUJBQW1CLGNBQXBDd0MsNERBQUFBLHNDQUFzQ3pKLEdBQUcsQ0FBQyxDQUFDa0gsV0FBbUI7NEJBQ3RGL0csTUFBTTs0QkFDTjNDLE1BQU0wSixTQUFTM0ssS0FBSyxJQUFJMkssU0FBUzFKLElBQUksSUFBSTs0QkFDekNELEtBQUsySixTQUFTM0osR0FBRzt3QkFDbkIsUUFBTyxFQUFFO29CQUVULGlDQUFpQztvQkFDakMsTUFBTW1NLHFCQUFxQkQsRUFBQUEsaUNBQUFBLGlCQUFpQnhNLFlBQVksY0FBN0J3TSxxREFBQUEsK0JBQStCekosR0FBRyxDQUFDLENBQUMyRyxPQUFlOzRCQUM1RXBLLE9BQU9vSyxLQUFLcEssS0FBSyxJQUFJOzRCQUNyQnNCLFNBQVNrRCxNQUFNQyxPQUFPLENBQUMyRixLQUFLOUksT0FBTyxJQUFJOEksS0FBSzlJLE9BQU8sQ0FBQ3lKLElBQUksQ0FBQyxRQUFTWCxLQUFLOUksT0FBTyxJQUFJO3dCQUNwRixRQUFPLEVBQUU7b0JBRVR0RyxRQUFRQyxHQUFHLENBQUMseUJBQWV3RjtvQkFDM0J6RixRQUFRQyxHQUFHLENBQUMseUJBQWVrUztvQkFFM0JwTixnQkFBZ0I4RSxDQUFBQTs0QkFVSnFJLHVDQUFBQSxpQ0FDQ0Esd0NBQUFBLGtDQUNJQSwwQ0FBQUEsa0NBQ0NBLDJDQUFBQTsrQkFiTzs0QkFDdkIsR0FBR3JJLElBQUk7NEJBQ1A3RSxPQUFPa04saUJBQWlCbE4sS0FBSzs0QkFDN0JDLGFBQWFpTixpQkFBaUJqTixXQUFXOzRCQUN6Q0MsWUFBWWdOLGlCQUFpQmhOLFVBQVUsSUFBSTs0QkFDM0NHLGdCQUFnQjZNLGlCQUFpQnRNLFFBQVEsS0FBSzs0QkFDOUNKLHFCQUFxQjBNLGlCQUFpQnJNLFdBQVcsS0FBSzs0QkFDdERGLGVBQWV1TSxpQkFBaUJ2TSxhQUFhLElBQUksQ0FBQzs0QkFDbERELGNBQWN5TTs0QkFDZDFNLG1CQUFtQkE7NEJBQ25CTixVQUFVK00sRUFBQUEsa0NBQUFBLGlCQUFpQnZNLGFBQWEsY0FBOUJ1TSx1REFBQUEsd0NBQUFBLGdDQUFnQ25NLEtBQUssY0FBckNtTSw0REFBQUEsc0NBQXVDbE0sR0FBRyxLQUFJOzRCQUN4RFosV0FBVzhNLEVBQUFBLG1DQUFBQSxpQkFBaUJ2TSxhQUFhLGNBQTlCdU0sd0RBQUFBLHlDQUFBQSxpQ0FBZ0NuTSxLQUFLLGNBQXJDbU0sNkRBQUFBLHVDQUF1Q2pNLElBQUksS0FBSTs0QkFDMURYLGVBQWU0TSxFQUFBQSxtQ0FBQUEsaUJBQWlCdk0sYUFBYSxjQUE5QnVNLHdEQUFBQSwyQ0FBQUEsaUNBQWdDaE0sUUFBUSxjQUF4Q2dNLCtEQUFBQSx5Q0FBMENsTSxHQUFHLEtBQUk7NEJBQ2hFVCxnQkFBZ0IyTSxFQUFBQSxtQ0FBQUEsaUJBQWlCdk0sYUFBYSxjQUE5QnVNLHdEQUFBQSw0Q0FBQUEsaUNBQWdDaE0sUUFBUSxjQUF4Q2dNLGdFQUFBQSwwQ0FBMENqTSxJQUFJLEtBQUk7d0JBQ3BFOztnQkFDRixPQUFPO29CQUNMakcsUUFBUWdCLEtBQUssQ0FBQyxlQUFlaVIsSUFBSS9JLE9BQU87b0JBQ3hDLGFBQWE7b0JBQ2JuRSxnQkFBZ0I4RSxDQUFBQSxPQUFTOzRCQUN2QixHQUFHQSxJQUFJOzRCQUNQN0UsT0FBT29MLGVBQWVwTCxLQUFLOzRCQUMzQkMsYUFBYW1MLGVBQWVuTCxXQUFXOzRCQUN2Q0MsWUFBWWtMLGVBQWVsTCxVQUFVLElBQUk7NEJBQ3pDRyxnQkFBZ0IrSyxlQUFleEssUUFBUSxLQUFLOzRCQUM1Q0oscUJBQXFCNEssZUFBZXZLLFdBQVcsS0FBSzs0QkFDcERKLG1CQUFtQixFQUFFLENBQUMsU0FBUzt3QkFDakM7Z0JBQ0Y7WUFDRixFQUFFLE9BQU96RSxPQUFPO2dCQUNkaEIsUUFBUWdCLEtBQUssQ0FBQyxlQUFlQTtnQkFDN0IsYUFBYTtnQkFDYitELGdCQUFnQjhFLENBQUFBLE9BQVM7d0JBQ3ZCLEdBQUdBLElBQUk7d0JBQ1A3RSxPQUFPb0wsZUFBZXBMLEtBQUs7d0JBQzNCQyxhQUFhbUwsZUFBZW5MLFdBQVc7d0JBQ3ZDQyxZQUFZa0wsZUFBZWxMLFVBQVUsSUFBSTt3QkFDekNHLGdCQUFnQitLLGVBQWV4SyxRQUFRLEtBQUs7d0JBQzVDSixxQkFBcUI0SyxlQUFldkssV0FBVyxLQUFLO3dCQUNwREosbUJBQW1CLEVBQUUsQ0FBQyxTQUFTO29CQUNqQztZQUNGO1FBQ0Y7SUFDRjtJQUVBLFVBQVU7SUFDVixNQUFNbEUsb0JBQW9CO1FBQ3hCLE9BQU9FLFdBQVc0TyxJQUFJLENBQUNoRSxDQUFBQSxTQUFVQSxPQUFPMUQsRUFBRSxLQUFLM0c7SUFDakQ7SUFFQSxJQUFJLENBQUNkLFdBQVcsT0FBTztJQUV2QixxQkFDRSw4REFBQ2tSO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDRDtnQkFBSUMsV0FBVTs7a0NBRWIsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDQzt3Q0FBR0QsV0FBVTtrREFBb0I7Ozs7OztrREFDbEMsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0U7Z0RBQ0NDLFNBQVNaO2dEQUNUUyxXQUFXLDRCQUEwRSxPQUE5Q3ZRLG1CQUFtQixhQUFhLFdBQVc7MERBRWxGLDRFQUFDM0MsOEdBQVFBO29EQUFDa1QsV0FBVTs7Ozs7Ozs7Ozs7MERBRXRCLDhEQUFDRTtnREFBT0MsU0FBU3BIO2dEQUFjaUgsV0FBVTswREFDdkMsNEVBQUNqVCw4R0FBSUE7b0RBQUNpVCxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FJdEIsOERBQUNFO2dDQUFPQyxTQUFTclI7Z0NBQVNrUixXQUFVOzBDQUNsQyw0RUFBQ25ULDhHQUFDQTtvQ0FBQ21ULFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQUtqQiw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUViLDhEQUFDRDtnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQ0Q7b0NBQUlDLFdBQVU7OENBQ1p6USx3QkFDQyw4REFBQ3dRO3dDQUFJQyxXQUFVO2tEQUNiLDRFQUFDSTtzREFBRTs7Ozs7Ozs7OztvREFFSGhSLFdBQVdxSCxNQUFNLEtBQUssa0JBQ3hCLDhEQUFDc0o7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDRDtnREFBSUMsV0FBVTswREFDYiw0RUFBQ2hULDhHQUFRQTtvREFBQ2dULFdBQVU7Ozs7Ozs7Ozs7OzBEQUV0Qiw4REFBQ0s7Z0RBQUdMLFdBQVU7MERBQTBCOzs7Ozs7MERBQ3hDLDhEQUFDSTtnREFBRUosV0FBVTswREFBZ0M7Ozs7OzswREFHN0MsOERBQUNFO2dEQUNDQyxTQUFTcEg7Z0RBQ1RpSCxXQUFVOztrRUFFViw4REFBQ2pULDhHQUFJQTt3REFBQ2lULFdBQVU7Ozs7OztvREFBWTs7Ozs7Ozs7Ozs7O29EQUtoQzVRLFdBQVdnSCxHQUFHLENBQUMsQ0FBQzRELHVCQUNkLDhEQUFDK0Y7NENBRUNDLFdBQVcsb0JBQW1FLE9BQS9DclEscUJBQXFCcUssT0FBTzFELEVBQUUsR0FBRyxXQUFXOzRDQUMzRTZKLFNBQVMsSUFBTXZHLGdCQUFnQkksT0FBTzFELEVBQUU7OzhEQUV4Qyw4REFBQ3lKO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ007NERBQUtOLFdBQVU7c0VBQXlCaEcsT0FBT3JILEtBQUs7Ozs7OztzRUFDckQsOERBQUMyTjs0REFBS04sV0FBVyx1QkFBbUUsT0FBNUNoRyxPQUFPdEwsTUFBTSxLQUFLLElBQUksY0FBYztzRUFDekVzTCxPQUFPdEwsTUFBTSxLQUFLLElBQUksUUFBUTs7Ozs7Ozs7Ozs7OzhEQUduQyw4REFBQ3dSO29EQUNDQyxTQUFTLENBQUN6STt3REFDUkEsRUFBRTZJLGVBQWU7d0RBQ2pCMUcsa0JBQWtCRyxPQUFPMUQsRUFBRTtvREFDN0I7b0RBQ0EwSixXQUFVOzhEQUVWLDRFQUFDblQsOEdBQUNBO3dEQUFDbVQsV0FBVTs7Ozs7Ozs7Ozs7OzJDQWpCVmhHLE9BQU8xRCxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7MENBMEJ4Qiw4REFBQ3lKO2dDQUFJQyxXQUFVOztvQ0FDWnZRLG1CQUFtQix3QkFDbEIsOERBQUNzUTt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEO2dEQUFJQyxXQUFVOzBEQUNiLDRFQUFDL1MsOEdBQVFBO29EQUFDK1MsV0FBVTs7Ozs7Ozs7Ozs7MERBRXRCLDhEQUFDSztnREFBR0wsV0FBVTswREFBMEI7Ozs7OzswREFDeEMsOERBQUNJO2dEQUFFSixXQUFVOzBEQUFnQzs7Ozs7Ozs7Ozs7O29DQU1oRHZRLG1CQUFtQiw0QkFDbEI7OzBEQUVFLDhEQUFDc1E7Z0RBQUlDLFdBQVU7MERBQ1ovUSxpQ0FDQyw4REFBQ3VSO29EQUNDQyxLQUFLeFI7b0RBQ0x5UixLQUFJO29EQUNKVixXQUFVOzs7Ozs4RUFHWiw4REFBQ0Q7b0RBQUlDLFdBQVU7OERBQ2IsNEVBQUNNO2tFQUFLOzs7Ozs7Ozs7Ozs7Ozs7OzBEQU1aLDhEQUFDUDtnREFBSUMsV0FBVTs7a0VBRWIsOERBQUNEO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ1c7Z0VBQU1YLFdBQVU7MEVBQW9COzs7Ozs7MEVBQ3JDLDhEQUFDekU7Z0VBQ0NoRixNQUFLO2dFQUNMcUssT0FBTzVQO2dFQUNQNlAsVUFBVSxDQUFDbkosSUFBTXpHLGdCQUFnQnlHLEVBQUU4QyxNQUFNLENBQUNvRyxLQUFLO2dFQUMvQ1osV0FBVTtnRUFDVmMsYUFBWTs7Ozs7Ozs7Ozs7O2tFQUtoQiw4REFBQ2Y7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDVztnRUFBTVgsV0FBVTswRUFBb0I7Ozs7OzswRUFDckMsOERBQUMxUywyRUFBTUE7Z0VBQ0x5VCxNQUFLO2dFQUNMQyxPQUFPO29FQUFFQyxPQUFPO2dFQUFPO2dFQUN2QkgsYUFBWTtnRUFDWkYsT0FBT3RPO2dFQUNQdU8sVUFBVXRPO2dFQUNWaEQsU0FBU2lEO2dFQUNUME8sU0FBUzlPLFdBQVdnRSxHQUFHLENBQUMrQixDQUFBQTtvRUFDdEJ4SyxRQUFRQyxHQUFHLENBQUMseUJBQWV1SztvRUFDM0IsT0FBTzt3RUFDTHdJLHFCQUNFLDhEQUFDTDs0RUFBS1UsT0FBTztnRkFBRUcsT0FBT2hKLElBQUlnSixLQUFLOzRFQUFDO3NGQUM3QmhKLElBQUl2RSxJQUFJOzs7Ozs7d0VBR2JnTixPQUFPekksSUFBSTdCLEVBQUU7b0VBQ2Y7Z0VBQ0Y7Ozs7OzswRUFHRiw4REFBQ3lKO2dFQUFJaUIsT0FBTztvRUFBRUksVUFBVTtvRUFBUUQsT0FBTztvRUFBUUUsV0FBVztnRUFBTTs7b0VBQUc7b0VBQ3JEalAsV0FBV3FFLE1BQU07b0VBQUM7b0VBQVNqRSxjQUFjLE1BQU07Ozs7Ozs7Ozs7Ozs7a0VBSy9ELDhEQUFDdU47d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDVztnRUFBTVgsV0FBVTswRUFBb0I7Ozs7OzswRUFDckMsOERBQUN6RTtnRUFDQ2hGLE1BQUs7Z0VBQ0xxSyxPQUFPdFA7Z0VBQ1B1UCxVQUFVLENBQUNuSixJQUFNbkcsa0JBQWtCbUcsRUFBRThDLE1BQU0sQ0FBQ29HLEtBQUs7Z0VBQ2pEWixXQUFVO2dFQUNWYyxhQUFZOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OztvQ0FPckJyUixtQkFBbUIsWUFBWVAscUNBQzlCO2tEQUVFLDRFQUFDNlE7NENBQUlDLFdBQVU7OzhEQUViLDhEQUFDRDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNEOzREQUFJQyxXQUFVOzs4RUFDYiw4REFBQ0Q7b0VBQ0NDLFdBQVU7b0VBQ1ZHLFNBQVM7NEVBQU10TTtnRkFBQUEsMkJBQUFBLFNBQVN5TixjQUFjLENBQUMsbUNBQXhCek4sK0NBQUFBLHlCQUErQzhILEtBQUs7OzhFQUVsRXhNLGFBQWEwRCxVQUFVLE1BQUkzRCxxQkFBQUEsaUNBQUFBLHlDQUFBQSxtQkFBcUIyRCxVQUFVLGtCQUN6RCw4REFBQzJOO3dFQUNDQyxLQUFLdFIsYUFBYTBELFVBQVUsTUFBSTNELHNCQUFBQSxpQ0FBQUEsMENBQUFBLG9CQUFxQjJELFVBQVU7d0VBQy9ENk4sS0FBSTt3RUFDSlYsV0FBVTs7Ozs7a0dBR1osOERBQUNEO3dFQUFJQyxXQUFVO2tGQUNiLDRFQUFDTTtzRkFBSzs7Ozs7Ozs7Ozs7Ozs7Ozs4RUFJWiw4REFBQy9FO29FQUNDakYsSUFBRztvRUFDSEMsTUFBSztvRUFDTGtGLFFBQU87b0VBQ1BvRixVQUFVeEc7b0VBQ1YyRyxPQUFPO3dFQUFFTyxTQUFTO29FQUFPOzs7Ozs7OEVBRzNCLDhEQUFDckI7b0VBQ0NGLFdBQVU7b0VBQ1ZHLFNBQVM7d0VBQ1AsaUJBQWlCO3dFQUNqQnhTLFFBQVFDLEdBQUcsQ0FBQzt3RUFDWnlCLGFBQWEwTixJQUFJLENBQUM7b0VBQ3BCOzhFQUNEOzs7Ozs7Ozs7Ozs7c0VBSUgsOERBQUNnRDs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUNEO29FQUFJQyxXQUFVOztzRkFDYiw4REFBQ1c7c0ZBQU07Ozs7OztzRkFDUCw4REFBQ3BGOzRFQUNDaEYsTUFBSzs0RUFDTHFLLE9BQU96UixhQUFhd0QsS0FBSyxNQUFJekQsc0JBQUFBLGlDQUFBQSwwQ0FBQUEsb0JBQXFCeUQsS0FBSyxLQUFJOzRFQUMzRGtPLFVBQVUsQ0FBQ25KO2dGQUNUaEYsZ0JBQWdCOEUsQ0FBQUEsT0FBUzt3RkFBRSxHQUFHQSxJQUFJO3dGQUFFN0UsT0FBTytFLEVBQUU4QyxNQUFNLENBQUNvRyxLQUFLO29GQUFDO2dGQUMxRHpHLGtCQUFrQnhLLGtCQUFtQitILEVBQUU4QyxNQUFNLENBQUNvRyxLQUFLOzRFQUNyRDs0RUFDQUUsYUFBWTs7Ozs7Ozs7Ozs7OzhFQUdoQiw4REFBQ2Y7b0VBQUlDLFdBQVU7O3NGQUNiLDhEQUFDVztzRkFBTTs7Ozs7O3NGQUNQLDhEQUFDYTs0RUFDQ1osT0FBT3pSLGFBQWF5RCxXQUFXLE1BQUkxRCxzQkFBQUEsaUNBQUFBLDBDQUFBQSxvQkFBcUIwRCxXQUFXLEtBQUk7NEVBQ3ZFaU8sVUFBVSxDQUFDbkosSUFBTWhGLGdCQUFnQjhFLENBQUFBLE9BQVM7d0ZBQUUsR0FBR0EsSUFBSTt3RkFBRTVFLGFBQWE4RSxFQUFFOEMsTUFBTSxDQUFDb0csS0FBSztvRkFBQzs0RUFDakZFLGFBQVk7NEVBQ1pXLE1BQU07Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4REFPZCw4REFBQzFCO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ0s7c0VBQUc7Ozs7OztzRUFHSiw4REFBQ047NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDRDtvRUFBSUMsV0FBVTs7c0ZBQ2IsOERBQUNNO3NGQUFLOzs7Ozs7c0ZBQ04sOERBQUNLOzRFQUFNWCxXQUFVOzs4RkFDZiw4REFBQ3pFO29GQUNDaEYsTUFBSztvRkFDTG1MLFNBQVN2UyxhQUFhNkQsY0FBYztvRkFDcEM2TixVQUFVLENBQUNuSixJQUFNaEYsZ0JBQWdCOEUsQ0FBQUEsT0FBUztnR0FBRSxHQUFHQSxJQUFJO2dHQUFFeEUsZ0JBQWdCMEUsRUFBRThDLE1BQU0sQ0FBQ2tILE9BQU87NEZBQUM7Ozs7Ozs4RkFFeEYsOERBQUNwQjtvRkFBS04sV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7O2dFQUduQjdRLGFBQWE2RCxjQUFjLGtCQUMxQiw4REFBQytNO29FQUFJQyxXQUFVOzhFQUVaN1EsRUFBQUEsOEJBQUFBLGFBQWFtRSxhQUFhLGNBQTFCbkUsbURBQUFBLG9DQUFBQSw0QkFBNEJ1RSxLQUFLLGNBQWpDdkUsd0RBQUFBLGtDQUFtQ3dFLEdBQUcsa0JBQ3JDLDhEQUFDb007d0VBQUlDLFdBQVU7OzBGQUNiLDhEQUFDRDtnRkFBSUMsV0FBVTswRkFDYiw0RUFBQ3RNO29GQUNDc00sV0FBVTtvRkFDVjJCLFFBQVE7b0ZBQ1JDLFFBQVF6UyxhQUFhMEQsVUFBVTs7c0dBRS9CLDhEQUFDZ1A7NEZBQU9wQixLQUFLdFIsYUFBYW1FLGFBQWEsQ0FBQ0ksS0FBSyxDQUFDQyxHQUFHOzRGQUFFNEMsTUFBSzs7Ozs7O3dGQUFjOzs7Ozs7Ozs7Ozs7MEZBSTFFLDhEQUFDd0o7Z0ZBQUlDLFdBQVU7MEZBQXVCN1EsYUFBYW1FLGFBQWEsQ0FBQ0ksS0FBSyxDQUFDRSxJQUFJOzs7Ozs7MEZBQzNFLDhEQUFDc007Z0ZBQU9GLFdBQVU7Z0ZBQXdCRyxTQUFTN0U7MEZBQ2pELDRFQUFDZ0Y7OEZBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7a0dBSVYsOERBQUNQO3dFQUFJQyxXQUFVOzswRkFDYiw4REFBQ0Q7Z0ZBQUlDLFdBQVU7MEZBQ2IsNEVBQUNEO29GQUFJQyxXQUFVOzhGQUFZOzs7Ozs7Ozs7OzswRkFFN0IsOERBQUNFO2dGQUFPRixXQUFVO2dGQUF3QkcsU0FBUzdFOzBGQUNqRCw0RUFBQ2dGOzhGQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NFQVNsQiw4REFBQ1A7NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDRDtvRUFBSUMsV0FBVTs7c0ZBQ2IsOERBQUNNO3NGQUFLOzs7Ozs7c0ZBQ04sOERBQUNLOzRFQUFNWCxXQUFVOzs4RkFDZiw4REFBQ3pFO29GQUNDaEYsTUFBSztvRkFDTG1MLFNBQVN2UyxhQUFhZ0UsbUJBQW1CO29GQUN6QzBOLFVBQVUsQ0FBQ25KLElBQU1oRixnQkFBZ0I4RSxDQUFBQSxPQUFTO2dHQUFFLEdBQUdBLElBQUk7Z0dBQUVyRSxxQkFBcUJ1RSxFQUFFOEMsTUFBTSxDQUFDa0gsT0FBTzs0RkFBQzs7Ozs7OzhGQUU3Riw4REFBQ3BCO29GQUFLTixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Z0VBR25CN1EsYUFBYWdFLG1CQUFtQixrQkFDL0IsOERBQUM0TTtvRUFBSUMsV0FBVTs4RUFFWjdRLEVBQUFBLCtCQUFBQSxhQUFhbUUsYUFBYSxjQUExQm5FLG9EQUFBQSx1Q0FBQUEsNkJBQTRCMEUsUUFBUSxjQUFwQzFFLDJEQUFBQSxxQ0FBc0N3RSxHQUFHLGtCQUN4Qyw4REFBQ29NO3dFQUFJQyxXQUFVOzswRkFDYiw4REFBQ0Q7Z0ZBQUlDLFdBQVU7O2tHQUNiLDhEQUFDRDt3RkFBSUMsV0FBVTtrR0FBZ0I7Ozs7OztrR0FDL0IsOERBQUNEO3dGQUFJQyxXQUFVO2tHQUNiLDRFQUFDRDs0RkFBSUMsV0FBVTtzR0FBbUI3USxhQUFhbUUsYUFBYSxDQUFDTyxRQUFRLENBQUNELElBQUk7Ozs7Ozs7Ozs7Ozs7Ozs7OzBGQUc5RSw4REFBQ3NNO2dGQUFPRixXQUFVO2dGQUF3QkcsU0FBU3JFOzBGQUNqRCw0RUFBQ3dFOzhGQUFLOzs7Ozs7Ozs7Ozs7Ozs7O2tHQUlWLDhEQUFDUDt3RUFBSUMsV0FBVTtrRkFDYiw0RUFBQ0U7NEVBQU9GLFdBQVU7NEVBQXdCRyxTQUFTckU7c0ZBQ2pELDRFQUFDd0U7MEZBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzRUFTbEIsOERBQUNQOzREQUFJQyxXQUFVOzs4RUFDYiw4REFBQ0Q7b0VBQUlDLFdBQVU7OEVBQ2IsNEVBQUNNO2tGQUFLOzs7Ozs7Ozs7Ozs4RUFFUiw4REFBQ1A7b0VBQUlDLFdBQVU7O3NGQUNiLDhEQUFDRTs0RUFBT0YsV0FBVTs0RUFBbUJHLFNBQVM5RDs7OEZBQzVDLDhEQUFDaUU7OEZBQUs7Ozs7Ozs4RkFDTiw4REFBQ0E7OEZBQUs7Ozs7Ozs7Ozs7Ozt3RUFFUG5SLGFBQWFpRSxpQkFBaUIsSUFBSWpFLGFBQWFpRSxpQkFBaUIsQ0FBQ3FELE1BQU0sR0FBRyxJQUN6RXRILGFBQWFpRSxpQkFBaUIsQ0FBQ2dELEdBQUcsQ0FBQyxDQUFDaUksVUFBVWpHLHNCQUM1Qyw4REFBQzJIO2dGQUFnQkMsV0FBVTs7a0dBQ3pCLDhEQUFDTTt3RkFDQ04sV0FBVTt3RkFDVkcsU0FBUzs0RkFDUCxJQUFJOUIsU0FBUzFLLEdBQUcsRUFBRTtnR0FDaEJtTyxPQUFPQyxJQUFJLENBQUMxRCxTQUFTMUssR0FBRyxFQUFFOzRGQUM1Qjt3RkFDRjt3RkFDQXFOLE9BQU87NEZBQ0xnQixRQUFRM0QsU0FBUzFLLEdBQUcsR0FBRyxZQUFZOzRGQUNuQ3dOLE9BQU85QyxTQUFTMUssR0FBRyxHQUFHLFlBQVk7NEZBQ2xDc08sZ0JBQWdCNUQsU0FBUzFLLEdBQUcsR0FBRyxjQUFjO3dGQUMvQzt3RkFDQWhCLE9BQU8wTCxTQUFTMUssR0FBRyxHQUFHLFdBQVcwSyxTQUFTekssSUFBSTs7NEZBQy9DOzRGQUNLeUssU0FBU3pLLElBQUk7Ozs7Ozs7a0dBRW5CLDhEQUFDc007d0ZBQ0NGLFdBQVU7d0ZBQ1ZHLFNBQVMsSUFBTTdELHVCQUF1QmxFO3dGQUN0Q3pGLE9BQU07a0dBQ1A7Ozs7Ozs7K0VBckJPeUY7Ozs7MkdBMkJaLDhEQUFDMkg7NEVBQUlDLFdBQVU7c0ZBQ2IsNEVBQUNNO2dGQUFLVSxPQUFPO29GQUFFRyxPQUFPO29GQUFRQyxVQUFVO2dGQUFPOzBGQUFHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4REFRNUQsOERBQUNyQjtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNEOzREQUFJQyxXQUFVOzs4RUFDYiw4REFBQ0s7OEVBQUc7Ozs7Ozs4RUFDSiw4REFBQ0g7b0VBQ0NGLFdBQVU7b0VBQ1ZHLFNBQVMxRDtvRUFDVDlKLE9BQU07OEVBQ1A7Ozs7Ozs7Ozs7OztzRUFJSCw4REFBQ29OOzREQUFJQyxXQUFVO3NFQUNaN1EsYUFBYWtFLFlBQVksSUFBSWxFLGFBQWFrRSxZQUFZLENBQUNvRCxNQUFNLEdBQUcsSUFDL0R0SCxhQUFha0UsWUFBWSxDQUFDK0MsR0FBRyxDQUFDLENBQUMyRyxNQUFNM0Usc0JBQ25DLDhEQUFDMkg7b0VBQWdCQyxXQUFVOztzRkFDekIsOERBQUNEOzRFQUFJQyxXQUFVOzs4RkFDYiw4REFBQ007b0ZBQUtOLFdBQVU7O3dGQUFhO3dGQUFNNUgsUUFBUTs7Ozs7Ozs4RkFDM0MsOERBQUM4SDtvRkFDQ0YsV0FBVTtvRkFDVkcsU0FBUyxJQUFNekQsdUJBQXVCdEU7b0ZBQ3RDekYsT0FBTTs4RkFDUDs7Ozs7Ozs7Ozs7O3NGQUlILDhEQUFDb047NEVBQUlDLFdBQVU7OzhGQUNiLDhEQUFDRDtvRkFBSUMsV0FBVTs7c0dBQ2IsOERBQUNXO3NHQUFNOzs7Ozs7c0dBQ1AsOERBQUNwRjs0RkFDQ2hGLE1BQUs7NEZBQ0xxSyxPQUFPN0QsS0FBS3BLLEtBQUs7NEZBQ2pCa08sVUFBVSxDQUFDbkosSUFBTWlGLHdCQUF3QnZFLE9BQU9WLEVBQUU4QyxNQUFNLENBQUNvRyxLQUFLOzRGQUM5REUsYUFBWTs0RkFDWmQsV0FBVTs7Ozs7Ozs7Ozs7OzhGQUdkLDhEQUFDRDtvRkFBSUMsV0FBVTs7c0dBQ2IsOERBQUNXO3NHQUFNOzs7Ozs7c0dBQ1AsOERBQUNhOzRGQUNDWixPQUFPN0QsS0FBSzlJLE9BQU87NEZBQ25CNE0sVUFBVSxDQUFDbkosSUFBTW1GLDBCQUEwQnpFLE9BQU9WLEVBQUU4QyxNQUFNLENBQUNvRyxLQUFLOzRGQUNoRUUsYUFBWTs0RkFDWmQsV0FBVTs0RkFDVnlCLE1BQU07Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7bUVBN0JKcko7Ozs7K0ZBb0NaLDhEQUFDMkg7Z0VBQUlDLFdBQVU7MEVBQ2IsNEVBQUNJOzhFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhEQU9YLDhEQUFDTDtvREFBSUMsV0FBVTs4REFDYiw0RUFBQ0Q7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDRDtnRUFBSUMsV0FBVTs7a0ZBQ2IsOERBQUNNO2tGQUFLOzs7Ozs7a0ZBQ04sOERBQUNLO3dFQUFNWCxXQUFVOzswRkFDZiw4REFBQ3pFO2dGQUNDaEYsTUFBSztnRkFDTG1MLFNBQVN2UyxhQUFhK0UsWUFBWTtnRkFDbEMyTSxVQUFVLENBQUNuSixJQUFNaEYsZ0JBQWdCOEUsQ0FBQUEsT0FBUzs0RkFBRSxHQUFHQSxJQUFJOzRGQUFFdEQsY0FBY3dELEVBQUU4QyxNQUFNLENBQUNrSCxPQUFPO3dGQUFDOzs7Ozs7MEZBRXRGLDhEQUFDcEI7Z0ZBQUtOLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs0REFJbkI3USxhQUFhK0UsWUFBWSxrQkFDeEI7O2tGQUNFLDhEQUFDNkw7d0VBQUlDLFdBQVU7OzBGQUNiLDhEQUFDTTswRkFBSzs7Ozs7OzBGQUNOLDhEQUFDSztnRkFBTVgsV0FBVTs7a0dBQ2YsOERBQUN6RTt3RkFDQ2hGLE1BQUs7d0ZBQ0xtTCxTQUFTdlMsYUFBYWdGLHFCQUFxQjt3RkFDM0MwTSxVQUFVLENBQUNuSixJQUFNaEYsZ0JBQWdCOEUsQ0FBQUEsT0FBUztvR0FBRSxHQUFHQSxJQUFJO29HQUFFckQsdUJBQXVCdUQsRUFBRThDLE1BQU0sQ0FBQ2tILE9BQU87Z0dBQUM7Ozs7OztrR0FFL0YsOERBQUNwQjt3RkFBS04sV0FBVTs7Ozs7Ozs7Ozs7OzRFQUVqQjdRLGFBQWFnRixxQkFBcUIsa0JBQ2pDLDhEQUFDNEw7Z0ZBQUlDLFdBQVU7O2tHQUNiLDhEQUFDRTt3RkFDQ0YsV0FBVTt3RkFDVkcsU0FBU1I7a0dBQ1Y7Ozs7OztrR0FHRCw4REFBQ0k7d0ZBQUlDLFdBQVU7a0dBQ2IsNEVBQUNNO3NHQUFNblIsYUFBYWtGLGdCQUFnQixJQUFJOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrRkFNaEQsOERBQUMwTDt3RUFBSUMsV0FBVTs7MEZBQ2IsOERBQUNNOzBGQUFLOzs7Ozs7MEZBQ04sOERBQUNLO2dGQUFNWCxXQUFVOztrR0FDZiw4REFBQ3pFO3dGQUNDaEYsTUFBSzt3RkFDTG1MLFNBQVN2UyxhQUFhbUYsbUJBQW1CO3dGQUN6Q3VNLFVBQVUsQ0FBQ25KLElBQU1oRixnQkFBZ0I4RSxDQUFBQSxPQUFTO29HQUFFLEdBQUdBLElBQUk7b0dBQUVsRCxxQkFBcUJvRCxFQUFFOEMsTUFBTSxDQUFDa0gsT0FBTztnR0FBQzs7Ozs7O2tHQUU3Riw4REFBQ3BCO3dGQUFLTixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7b0VBSW5CN1EsYUFBYW1GLG1CQUFtQixrQkFDL0IsOERBQUN5TDt3RUFBSUMsV0FBVTs7MEZBQ2IsOERBQUNNOzBGQUFLOzs7Ozs7MEZBQ04sOERBQUMvRTtnRkFDQ2hGLE1BQUs7Z0ZBQ0xxSyxPQUFPelIsYUFBYW9GLGNBQWMsSUFBSTtnRkFDdENzTSxVQUFVLENBQUNuSixJQUFNaEYsZ0JBQWdCOEUsQ0FBQUEsT0FBUzs0RkFBRSxHQUFHQSxJQUFJOzRGQUFFakQsZ0JBQWdCbUQsRUFBRThDLE1BQU0sQ0FBQ29HLEtBQUs7d0ZBQUM7Z0ZBQ3BGRSxhQUFZO2dGQUNaZCxXQUFVOzs7Ozs7Ozs7Ozs7a0ZBS2hCLDhEQUFDRDt3RUFBSUMsV0FBVTs7MEZBQ2IsOERBQUNNOzBGQUFLOzs7Ozs7MEZBQ04sOERBQUNLO2dGQUFNWCxXQUFVOztrR0FDZiw4REFBQ3pFO3dGQUNDaEYsTUFBSzt3RkFDTG1MLFNBQVN2UyxhQUFhc0YsbUJBQW1CO3dGQUN6Q29NLFVBQVUsQ0FBQ25KLElBQU1oRixnQkFBZ0I4RSxDQUFBQSxPQUFTO29HQUFFLEdBQUdBLElBQUk7b0dBQUUvQyxxQkFBcUJpRCxFQUFFOEMsTUFBTSxDQUFDa0gsT0FBTztnR0FBQzs7Ozs7O2tHQUU3Riw4REFBQ3BCO3dGQUFLTixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7b0VBS25CN1EsYUFBYXNGLG1CQUFtQixrQkFDL0IsOERBQUNzTDt3RUFBSUMsV0FBVTs7MEZBRWIsOERBQUNEO2dGQUFJQyxXQUFVOztrR0FDYiw4REFBQ0Q7d0ZBQUlDLFdBQVU7OzBHQUNiLDhEQUFDVzswR0FBTTs7Ozs7OzBHQUNQLDhEQUFDcEY7Z0dBQ0NoRixNQUFLO2dHQUNMcUssT0FBT3pSLGFBQWFnRyxVQUFVLENBQUNDLFFBQVE7Z0dBQ3ZDeUwsVUFBVSxDQUFDbkosSUFBTWhGLGdCQUFnQjhFLENBQUFBLE9BQVM7NEdBQ3hDLEdBQUdBLElBQUk7NEdBQ1ByQyxZQUFZO2dIQUFFLEdBQUdxQyxLQUFLckMsVUFBVTtnSEFBRUMsVUFBVXNDLEVBQUU4QyxNQUFNLENBQUNvRyxLQUFLOzRHQUFDO3dHQUM3RDtnR0FDQUUsYUFBWTs7Ozs7Ozs7Ozs7O2tHQUdoQiw4REFBQ2Y7d0ZBQUlDLFdBQVU7OzBHQUNiLDhEQUFDVzswR0FBTTs7Ozs7OzBHQUNQLDhEQUFDcEY7Z0dBQ0NoRixNQUFLO2dHQUNMcUssT0FBT3pSLGFBQWFnRyxVQUFVLENBQUNFLFlBQVk7Z0dBQzNDd0wsVUFBVSxDQUFDbkosSUFBTWhGLGdCQUFnQjhFLENBQUFBLE9BQVM7NEdBQ3hDLEdBQUdBLElBQUk7NEdBQ1ByQyxZQUFZO2dIQUFFLEdBQUdxQyxLQUFLckMsVUFBVTtnSEFBRUUsY0FBY3FDLEVBQUU4QyxNQUFNLENBQUNvRyxLQUFLOzRHQUFDO3dHQUNqRTtnR0FDQUUsYUFBWTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBGQU1sQiw4REFBQ2Y7Z0ZBQUlDLFdBQVU7O2tHQUNiLDhEQUFDVztrR0FBTTs7Ozs7O2tHQUNQLDhEQUFDYTt3RkFDQ1osT0FBT3pSLGFBQWFnRyxVQUFVLENBQUNHLGVBQWU7d0ZBQzlDdUwsVUFBVSxDQUFDbkosSUFBTWhGLGdCQUFnQjhFLENBQUFBLE9BQVM7b0dBQ3hDLEdBQUdBLElBQUk7b0dBQ1ByQyxZQUFZO3dHQUFFLEdBQUdxQyxLQUFLckMsVUFBVTt3R0FBRUcsaUJBQWlCb0MsRUFBRThDLE1BQU0sQ0FBQ29HLEtBQUs7b0dBQUM7Z0dBQ3BFO3dGQUNBRSxhQUFZO3dGQUNaVyxNQUFNOzs7Ozs7Ozs7Ozs7MEZBS1YsOERBQUMxQjtnRkFBSUMsV0FBVTs7a0dBQ2IsOERBQUNXOzs0RkFBTTswR0FBTyw4REFBQ0w7Z0dBQUtOLFdBQVU7MEdBQWU3USxhQUFhZ0csVUFBVSxDQUFDSSxtQkFBbUIsQ0FBQ2tCLE1BQU07Ozs7Ozs7Ozs7OztvRkFDOUZ0SCxhQUFhZ0csVUFBVSxDQUFDSSxtQkFBbUIsQ0FBQ2EsR0FBRyxDQUFDLENBQUMrRixNQUFNL0Qsc0JBQ3RELDhEQUFDMkg7NEZBQWdCQyxXQUFVO3NHQUN6Qiw0RUFBQ3pFO2dHQUNDaEYsTUFBSztnR0FDTHFLLE9BQU96RTtnR0FDUDBFLFVBQVUsQ0FBQ25KO29HQUNULE1BQU13SyxXQUFXOzJHQUFJL1MsYUFBYWdHLFVBQVUsQ0FBQ0ksbUJBQW1CO3FHQUFDO29HQUNqRTJNLFFBQVEsQ0FBQzlKLE1BQU0sR0FBR1YsRUFBRThDLE1BQU0sQ0FBQ29HLEtBQUs7b0dBQ2hDbE8sZ0JBQWdCOEUsQ0FBQUEsT0FBUzs0R0FDdkIsR0FBR0EsSUFBSTs0R0FDUHJDLFlBQVk7Z0hBQUUsR0FBR3FDLEtBQUtyQyxVQUFVO2dIQUFFSSxxQkFBcUIyTTs0R0FBUzt3R0FDbEU7Z0dBQ0Y7Z0dBQ0FwQixhQUFZOzs7Ozs7MkZBWk4xSTs7Ozs7a0dBZ0JaLDhEQUFDOEg7d0ZBQ0MzSixNQUFLO3dGQUNMeUosV0FBVTt3RkFDVkcsU0FBUyxJQUFNek4sZ0JBQWdCOEUsQ0FBQUEsT0FBUztvR0FDdEMsR0FBR0EsSUFBSTtvR0FDUHJDLFlBQVk7d0dBQ1YsR0FBR3FDLEtBQUtyQyxVQUFVO3dHQUNsQkkscUJBQXFCOytHQUFJaUMsS0FBS3JDLFVBQVUsQ0FBQ0ksbUJBQW1COzRHQUFFO3lHQUFHO29HQUNuRTtnR0FDRjtrR0FDRDs7Ozs7Ozs7Ozs7OzBGQU1ILDhEQUFDd0s7Z0ZBQUlDLFdBQVU7O2tHQUNiLDhEQUFDVztrR0FBTTs7Ozs7O2tHQUNQLDhEQUFDWjt3RkFBSUMsV0FBVTtrR0FDYiw0RUFBQ0Q7NEZBQUlDLFdBQVU7OzhHQUNiLDhEQUFDSTtvR0FBRUosV0FBVTs4R0FBWTs7Ozs7OzhHQUN6Qiw4REFBQ0Q7b0dBQ0NDLFdBQVU7b0dBQ1ZnQixPQUFPO3dHQUNMbUIsV0FBVzt3R0FDWEgsUUFBUTt3R0FDUkksWUFBWTtvR0FDZDs4R0FFQzFRLDZCQUNDLDhEQUFDcU87d0dBQUlDLFdBQVU7OzBIQUNiLDhEQUFDRDtnSEFBSUMsV0FBVTs7Ozs7OzBIQUNmLDhEQUFDTTswSEFBSzs7Ozs7Ozs7Ozs7b0hBRU45TyxNQUFNaUYsTUFBTSxHQUFHLGtCQUNqQiw4REFBQ3NKO3dHQUNDQyxXQUFVO3dHQUNWcUMsU0FBUzVLO2tIQUVSakcsTUFBTTRFLEdBQUcsQ0FBQyxDQUFDa00scUJBQ1YsOERBQUN2QztnSEFFQ0MsV0FBVyxhQUFpRSxPQUFwRHBPLGdCQUFnQjJGLFFBQVEsQ0FBQytLLEtBQUtoTSxFQUFFLElBQUksYUFBYTtnSEFDekU2SixTQUFTLElBQU05SSxpQkFBaUJpTCxLQUFLaE0sRUFBRTs7a0lBRXZDLDhEQUFDeUo7d0hBQUlDLFdBQVU7OzRIQUNac0MsS0FBS3pQLFVBQVUsSUFBSXlQLEtBQUtDLGVBQWUsaUJBQ3RDLDhEQUFDL0I7Z0lBQ0NDLEtBQUs2QixLQUFLelAsVUFBVSxJQUFJeVAsS0FBS0MsZUFBZTtnSUFDNUM3QixLQUFLNEIsS0FBSzNQLEtBQUs7Z0lBQ2Y2UCxTQUFTLENBQUM5SztvSUFDUixNQUFNOEMsU0FBUzlDLEVBQUVHLGFBQWE7b0lBQzlCMkMsT0FBT3dHLEtBQUssQ0FBQ08sT0FBTyxHQUFHO29JQUN2QixNQUFNa0IsY0FBY2pJLE9BQU9rSSxrQkFBa0I7b0lBQzdDLElBQUlELGFBQWE7d0lBQ2ZBLFlBQVl6QixLQUFLLENBQUNPLE9BQU8sR0FBRztvSUFDOUI7Z0lBQ0Y7Ozs7OzRJQUVBOzBJQUNKLDhEQUFDeEI7Z0lBQUlDLFdBQVU7Z0lBQW1CZ0IsT0FBTztvSUFBRU8sU0FBU2UsS0FBS3pQLFVBQVUsSUFBSXlQLEtBQUtDLGVBQWUsR0FBRyxTQUFTO2dJQUFPOzBJQUFHOzs7Ozs7Ozs7Ozs7a0lBSW5ILDhEQUFDeEM7d0hBQUlDLFdBQVU7a0lBQWNzQyxLQUFLM1AsS0FBSyxJQUFJMlAsS0FBSzFPLElBQUksSUFBSTBPLEtBQUtLLFFBQVEsSUFBSTs7Ozs7O29IQUN4RS9RLGdCQUFnQjJGLFFBQVEsQ0FBQytLLEtBQUtoTSxFQUFFLG1CQUMvQiw4REFBQ3lKO3dIQUFJQyxXQUFVO2tJQUFxQjs7Ozs7OzsrR0F6QmpDc0MsS0FBS2hNLEVBQUU7Ozs7Ozs7OztrSUErQmxCLDhEQUFDeUo7d0dBQUlDLFdBQVU7a0hBQ2IsNEVBQUNEOzRHQUFJQyxXQUFVO3NIQUFhOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEZBU3hDLDhEQUFDRDtnRkFBSUMsV0FBVTs7a0dBQ2IsOERBQUNXO2tHQUFNOzs7Ozs7a0dBQ1AsOERBQUNaO3dGQUFJQyxXQUFVO2tHQUNiLDRFQUFDRDs0RkFBSUMsV0FBVTs7OEdBQ2IsOERBQUNFO29HQUNDM0osTUFBSztvR0FDTHlKLFdBQVU7b0dBQ1ZHLFNBQVM7d0dBQ1AsU0FBUzt3R0FDVCxNQUFNNUUsUUFBUTFILFNBQVMySCxhQUFhLENBQUM7d0dBQ3JDRCxNQUFNaEYsSUFBSSxHQUFHO3dHQUNiZ0YsTUFBTUUsTUFBTSxHQUFHO3dHQUNmRixNQUFNRyxRQUFRLEdBQUcsQ0FBQ2hFO2dIQUNIOzRHQUFiLE1BQU02QyxRQUFPLG9CQUFHQyxNQUFNLENBQXNCQyxLQUFLLGNBQXBDLHFEQUFzQyxDQUFDLEVBQUU7NEdBQ3RELElBQUlGLE1BQU07Z0hBQ1I3SCxnQkFBZ0I4RSxDQUFBQSxPQUFTO3dIQUN2QixHQUFHQSxJQUFJO3dIQUNQckMsWUFBWTs0SEFDVixHQUFHcUMsS0FBS3JDLFVBQVU7NEhBQ2xCTSxvQkFBb0I7bUlBQ2YrQixLQUFLckMsVUFBVSxDQUFDTSxrQkFBa0I7Z0lBQ3JDO29JQUFFYyxNQUFNO29JQUFRM0MsTUFBTTJHLEtBQUszRyxJQUFJO2dJQUFDOzZIQUNqQzt3SEFDSDtvSEFDRjs0R0FDRjt3R0FDRjt3R0FDQTJILE1BQU1JLEtBQUs7b0dBQ2I7O3NIQUVBLDhEQUFDNU8sOEdBQUlBOzRHQUFDNE4sTUFBTTs7Ozs7O3dHQUFNOzs7Ozs7O2dHQUduQnhMLGFBQWFnRyxVQUFVLENBQUNNLGtCQUFrQixDQUFDVyxHQUFHLENBQUMsQ0FBQ2tILFVBQVVsRixzQkFDekQsOERBQUMySDt3R0FBZ0JDLFdBQVU7OzBIQUN6Qiw4REFBQ007MEhBQU1oRCxTQUFTMUosSUFBSTs7Ozs7OzBIQUNwQiw4REFBQ3NNO2dIQUNDM0osTUFBSztnSEFDTHlKLFdBQVU7Z0hBQ1ZHLFNBQVM7b0hBQ1AsTUFBTXlDLGVBQWV6VCxhQUFhZ0csVUFBVSxDQUFDTSxrQkFBa0IsQ0FBQ2tCLE1BQU0sQ0FBQyxDQUFDNEYsR0FBR0MsSUFBTUEsTUFBTXBFO29IQUN2RjFGLGdCQUFnQjhFLENBQUFBLE9BQVM7NEhBQ3ZCLEdBQUdBLElBQUk7NEhBQ1ByQyxZQUFZO2dJQUFFLEdBQUdxQyxLQUFLckMsVUFBVTtnSUFBRU0sb0JBQW9CbU47NEhBQWE7d0hBQ3JFO2dIQUNGOzBIQUNEOzs7Ozs7O3VHQVpPeEs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FpQ3hDLDhEQUFDMkg7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDRDtnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQ0U7b0NBQ0NDLFNBQVM3QjtvQ0FDVDBCLFdBQVU7b0NBQ1Y2QyxVQUFVelQsV0FBV3FILE1BQU0sS0FBSyxLQUFLckcsc0JBQXNCRSxpQkFBaUI7b0NBQzVFcUMsT0FDRXJDLGlCQUFpQixJQUNiLFlBQ0FsQixXQUFXcUgsTUFBTSxLQUFLLElBQ3BCLHlCQUNBckcscUJBQ0UsZ0JBQ0E7OENBR1RFLGlCQUFpQixJQUNkLFFBQ0FGLHFCQUNFLFlBQ0E7Ozs7Ozs7Ozs7OzBDQUlWLDhEQUFDMlA7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRTt3Q0FBT0MsU0FBU2I7d0NBQWdCVSxXQUFVO2tEQUF1Qzs7Ozs7O2tEQUdsRiw4REFBQ0U7d0NBQ0NDLFNBQVNyQzt3Q0FDVGtDLFdBQVU7d0NBQ1Y2QyxVQUFVLENBQUNsVCxvQkFBb0JQLEVBQUFBLG1CQUFBQSxXQUFXNE8sSUFBSSxDQUFDM0gsQ0FBQUEsSUFBS0EsRUFBRUMsRUFBRSxLQUFLM0csK0JBQTlCUCx1Q0FBQUEsaUJBQWlEVixNQUFNLE1BQUssS0FBS3NCO3dDQUNoRzJDLE9BQ0UsQ0FBQ2hELG1CQUNHLGVBQ0FQLEVBQUFBLG9CQUFBQSxXQUFXNE8sSUFBSSxDQUFDM0gsQ0FBQUEsSUFBS0EsRUFBRUMsRUFBRSxLQUFLM0csK0JBQTlCUCx3Q0FBQUEsa0JBQWlEVixNQUFNLE1BQUssSUFDMUQsV0FDQXNCLGVBQ0UsY0FDQTtrREFHVEEsZUFBZSxZQUFZOzs7Ozs7a0RBRTlCLDhEQUFDa1E7d0NBQ0NDLFNBQVNoQzt3Q0FDVDZCLFdBQVU7d0NBQ1Y2QyxVQUFVaFQsZUFBZThLLElBQUksR0FBRyxLQUFLekssY0FBY2QsV0FBV3FILE1BQU0sS0FBSzt3Q0FDekU5RCxPQUNFdkQsV0FBV3FILE1BQU0sS0FBSyxJQUNsQixhQUNBNUcsZUFBZThLLElBQUksR0FBRyxJQUNwQixxQkFDQXpLLGFBQ0dkLFdBQVcwVCxJQUFJLENBQUN6TSxDQUFBQSxJQUFLQSxFQUFFQyxFQUFFLEdBQUcsV0FBVyxjQUFjLGNBQ3JEbEgsV0FBVzBULElBQUksQ0FBQ3pNLENBQUFBLElBQUtBLEVBQUVDLEVBQUUsR0FBRyxXQUFXLFNBQVM7a0RBRzFEekcsZUFBZThLLElBQUksR0FBRyxJQUNuQixXQUNBekssYUFDR2QsV0FBVzBULElBQUksQ0FBQ3pNLENBQUFBLElBQUtBLEVBQUVDLEVBQUUsR0FBRyxXQUFXLFlBQVksWUFDbkRsSCxXQUFXMFQsSUFBSSxDQUFDek0sQ0FBQUEsSUFBS0EsRUFBRUMsRUFBRSxHQUFHLFdBQVcsU0FBUzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O1lBUTlEOUYsc0NBQ0MsOERBQUN1UDtnQkFBSUMsV0FBVTtnQkFBZ0JHLFNBQVNqRzswQkFDdEMsNEVBQUM2RjtvQkFBSUMsV0FBVTtvQkFBdUJHLFNBQVMsQ0FBQ3pJLElBQU1BLEVBQUU2SSxlQUFlOztzQ0FDckUsOERBQUNSOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0s7OENBQUc7Ozs7Ozs4Q0FDSiw4REFBQ0g7b0NBQ0NDLFNBQVNqRztvQ0FDVDhGLFdBQVU7b0NBQ1Y2QyxVQUFValM7OENBRVYsNEVBQUMvRCw4R0FBQ0E7d0NBQUNtVCxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7OztzQ0FHakIsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0k7OENBQ0V4UCxhQUNHLGtCQUNBOzs7Ozs7Z0NBR0xBLDRCQUNDLDhEQUFDbVA7b0NBQUlDLFdBQVU7OENBQ2IsNEVBQUNEO3dDQUFJQyxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7OztzQ0FJckIsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0U7b0NBQ0NDLFNBQVNqRztvQ0FDVDhGLFdBQVU7b0NBQ1Y2QyxVQUFValM7OENBQ1g7Ozs7Ozs4Q0FHRCw4REFBQ3NQO29DQUNDQyxTQUFTckc7b0NBQ1RrRyxXQUFVO29DQUNWNkMsVUFBVWpTOzhDQUVUQSxhQUFhLFlBQVk7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQVFwQyw4REFBQ3BELDREQUFtQkE7Z0JBQ2xCdVYsUUFBUWpTO2dCQUNSaEMsU0FBUyxJQUFNaUMsd0JBQXdCO2dCQUN2Q2lTLGtCQUFrQnhEOzs7Ozs7Ozs7Ozs7QUFJMUI7R0F2Z0VNNVE7S0FBQUE7QUF5Z0VOLCtEQUFlQSxtQkFBbUJBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vYXBwL3dvcmtiZW5jaC9jb21wb25lbnRzL0NvdXJzZUxpc3RFZGl0TW9kYWwudHN4PzJkOGYiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgUmVhY3QsIHsgdXNlU3RhdGUsIHVzZUVmZmVjdCwgdXNlUmVmLCB1c2VDYWxsYmFjayB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IFgsIFNldHRpbmdzLCBQbHVzLCBCb29rT3BlbiwgRmlsZVRleHQgfSBmcm9tICdsdWNpZGUtcmVhY3QnO1xuaW1wb3J0IHsgY291cnNlTWFuYWdlbWVudEFwaSB9IGZyb20gJ0AvbGliL2FwaS9jb3Vyc2UtbWFuYWdlbWVudCc7XG5pbXBvcnQgeyBjb3Vyc2VBcGkgfSBmcm9tICdAL2xpYi9hcGkvY291cnNlJztcbmltcG9ydCB7IHVwbG9hZEFwaSB9IGZyb20gJ0AvbGliL2FwaS91cGxvYWQnO1xuaW1wb3J0IHsgd29ya3NBcGkgfSBmcm9tICdAL2xpYi9hcGkvd29ya3MnO1xuaW1wb3J0IHsgU2VsZWN0IH0gZnJvbSAnYW50ZCc7XG5pbXBvcnQgeyBHZXROb3RpZmljYXRpb24gfSBmcm9tICdsb2dpYy1jb21tb24vZGlzdC9jb21wb25lbnRzL05vdGlmaWNhdGlvbic7XG5pbXBvcnQgVGVtcGxhdGVQaWNrZXJNb2RhbCBmcm9tICcuL1RlbXBsYXRlUGlja2VyTW9kYWwnO1xuaW1wb3J0ICcuL0NvdXJzZUxpc3RFZGl0TW9kYWwuY3NzJztcbmltcG9ydCAnLi9OZXdQdWJsaXNoVGFza01vZGFsLmNzcyc7XG5cbmludGVyZmFjZSBDb3Vyc2VJdGVtIHtcbiAgaWQ6IG51bWJlcjtcbiAgc2VyaWVzSWQ6IG51bWJlcjtcbiAgdGl0bGU6IHN0cmluZztcbiAgZGVzY3JpcHRpb246IHN0cmluZztcbiAgY292ZXJJbWFnZTogc3RyaW5nO1xuICBvcmRlckluZGV4OiBudW1iZXI7XG4gIHN0YXR1czogbnVtYmVyO1xuICBzdGF0dXNMYWJlbDogc3RyaW5nO1xuICBoYXNWaWRlbzogbnVtYmVyO1xuICBoYXNEb2N1bWVudDogbnVtYmVyO1xuICBoYXNBdWRpbzogbnVtYmVyO1xuICB2aWRlb0R1cmF0aW9uOiBudW1iZXI7XG4gIHZpZGVvRHVyYXRpb25MYWJlbDogc3RyaW5nO1xuICB2aWRlb05hbWU6IHN0cmluZztcbiAgZmlyc3RUZWFjaGluZ1RpdGxlOiBzdHJpbmc7XG4gIHJlc291cmNlc0NvdW50OiBudW1iZXI7XG4gIGNyZWF0ZWRBdDogc3RyaW5nO1xuICB1cGRhdGVkQXQ6IHN0cmluZztcbiAgLy8g5paw5aKe5a2X5q6155So5LqO5a2Y5YKo5a6M5pW055qE6K++56iL6K+m5oOFXG4gIGNvbnRlbnRDb25maWc/OiBhbnk7XG4gIHRlYWNoaW5nSW5mbz86IGFueVtdO1xuICBhZGRpdGlvbmFsUmVzb3VyY2VzPzogQXJyYXk8e1xuICAgIHRpdGxlOiBzdHJpbmc7XG4gICAgdXJsOiBzdHJpbmc7XG4gICAgZGVzY3JpcHRpb246IHN0cmluZztcbiAgfT47XG59XG5cbmludGVyZmFjZSBDb3Vyc2VUYWcge1xuICBpZDogbnVtYmVyO1xuICBuYW1lOiBzdHJpbmc7XG4gIGNvbG9yOiBzdHJpbmc7XG4gIGNhdGVnb3J5OiBudW1iZXI7XG4gIGRlc2NyaXB0aW9uOiBzdHJpbmc7XG4gIHN0YXR1czogbnVtYmVyO1xufVxuXG5pbnRlcmZhY2UgQXBpUmVzcG9uc2Uge1xuICBjb2RlOiBudW1iZXI7XG4gIG1lc3NhZ2U6IHN0cmluZztcbiAgZGF0YToge1xuICAgIGxpc3Q6IENvdXJzZUl0ZW1bXTtcbiAgICBwYWdpbmF0aW9uOiB7XG4gICAgICBwYWdlOiBudW1iZXI7XG4gICAgICBwYWdlU2l6ZTogbnVtYmVyO1xuICAgICAgdG90YWw6IG51bWJlcjtcbiAgICAgIHRvdGFsUGFnZXM6IG51bWJlcjtcbiAgICAgIGhhc05leHQ6IGJvb2xlYW47XG4gICAgICBoYXNQcmV2OiBib29sZWFuO1xuICAgIH07XG4gIH07XG59XG5cbmludGVyZmFjZSBDb3Vyc2VMaXN0RWRpdE1vZGFsUHJvcHMge1xuICBpc1Zpc2libGU6IGJvb2xlYW47XG4gIG9uQ2xvc2U6ICgpID0+IHZvaWQ7XG4gIG9uU2F2ZTogKGRhdGE6IGFueSkgPT4gdm9pZDtcbiAgc2VyaWVzVGl0bGU6IHN0cmluZztcbiAgc2VyaWVzQ292ZXJJbWFnZT86IHN0cmluZztcbiAgc2VyaWVzSWQ6IG51bWJlcjtcbn1cblxuLy8g6I635Y+W57O75YiX6K++56iL6K+m5oOFXG5jb25zdCBmZXRjaFNlcmllc0RldGFpbCA9IGFzeW5jIChzZXJpZXNJZDogbnVtYmVyKTogUHJvbWlzZTxhbnk+ID0+IHtcbiAgY29uc29sZS5sb2coJ/CflI0g6I635Y+W57O75YiX6K++56iL6K+m5oOF77yMc2VyaWVzSWQ6Jywgc2VyaWVzSWQpO1xuICBjb25zb2xlLmxvZygn8J+UlyDosIPnlKhBUEk6IEdFVCAvYXBpL3YxL2NvdXJzZS1tYW5hZ2VtZW50L3Nlcmllcy97c2VyaWVzSWR9Jyk7XG5cbiAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBjb3Vyc2VBcGkuZ2V0U2VyaWVzRGV0YWlsKHNlcmllc0lkKTtcbiAgY29uc29sZS5sb2coJ/Cfk6Eg57O75YiX6K+m5oOFQVBJ5ZON5bqUOicsIHJlc3BvbnNlKTtcbiAgcmV0dXJuIHJlc3BvbnNlLmRhdGE7XG59O1xuXG4vLyBBUEnosIPnlKjlh73mlbAgLSDojrflj5bns7vliJfkuIvnmoTmiYDmnInor77nqIvvvIjljIXlkKvmnKrlj5HluIPlkozlt7Llj5HluIPvvIlcbmNvbnN0IGZldGNoQ291cnNlTGlzdCA9IGFzeW5jIChzZXJpZXNJZDogbnVtYmVyKTogUHJvbWlzZTxBcGlSZXNwb25zZT4gPT4ge1xuICBjb25zb2xlLmxvZygn8J+UjSDojrflj5bns7vliJfor77nqIvliJfooajvvIxzZXJpZXNJZDonLCBzZXJpZXNJZCk7XG4gIGNvbnNvbGUubG9nKCfwn5SXIOiwg+eUqEFQSTogR0VUIC9hcGkvdjEvY291cnNlLW1hbmFnZW1lbnQvc2VyaWVzL3tzZXJpZXNJZH0vY291cnNlcycpO1xuXG4gIC8vIOS9v+eUqOivvueoi+euoeeQhkFQSeiOt+WPluaJgOacieeKtuaAgeeahOivvueoi1xuICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGNvdXJzZUFwaS5nZXRNYW5hZ2VtZW50U2VyaWVzQ291cnNlcyhzZXJpZXNJZCwge1xuICAgIHBhZ2U6IDEsXG4gICAgcGFnZVNpemU6IDEwMCxcbiAgICAvLyDkuI3kvKBzdGF0dXPlj4LmlbDvvIzojrflj5bmiYDmnInnirbmgIHnmoTor77nqIvvvIhzdGF0dXM9MOacquWPkeW4g++8jHN0YXR1cz0x5bey5Y+R5biD77yJXG4gIH0pO1xuXG4gIGNvbnNvbGUubG9nKCfwn5OhIEFQSeWTjeW6lDonLCByZXNwb25zZSk7XG4gIHJldHVybiByZXNwb25zZS5kYXRhO1xufTtcblxuLy8g6I635Y+W6K++56iL6K+m5oOFXG5jb25zdCBmZXRjaENvdXJzZURldGFpbCA9IGFzeW5jIChzZXJpZXNJZDogbnVtYmVyLCBjb3Vyc2VJZDogbnVtYmVyKSA9PiB7XG4gIHJldHVybiBhd2FpdCBjb3Vyc2VBcGkuZ2V0Q291cnNlTWFya2V0cGxhY2VEZXRhaWwoc2VyaWVzSWQsIGNvdXJzZUlkKTtcbn07XG5cbi8vIOiOt+WPluivvueoi+agh+etvlxuY29uc3QgZmV0Y2hDb3Vyc2VUYWdzID0gYXN5bmMgKCk6IFByb21pc2U8YW55PiA9PiB7XG4gIHRyeSB7XG4gICAgY29uc29sZS5sb2coJ/CflI0g5byA5aeL6LCD55SoIGNvdXJzZUFwaS5nZXRDb3Vyc2VUYWdzJyk7XG4gICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgY291cnNlQXBpLmdldENvdXJzZVRhZ3Moe1xuICAgICAgcGFnZTogMSxcbiAgICAgIHBhZ2VTaXplOiAxMDAsXG4gICAgICBzdGF0dXM6IDEgLy8g5Y+q6I635Y+W5ZCv55So55qE5qCH562+XG4gICAgfSk7XG4gICAgY29uc29sZS5sb2coJ/CflI0gY291cnNlQXBpLmdldENvdXJzZVRhZ3Mg6L+U5Zue57uT5p6cOicsIHJlc3VsdCk7XG4gICAgcmV0dXJuIHJlc3VsdDtcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCfwn5SNIGNvdXJzZUFwaS5nZXRDb3Vyc2VUYWdzIOiwg+eUqOWksei0pTonLCBlcnJvcik7XG4gICAgdGhyb3cgZXJyb3I7XG4gIH1cbn07XG5cbmNvbnN0IENvdXJzZUxpc3RFZGl0TW9kYWw6IFJlYWN0LkZDPENvdXJzZUxpc3RFZGl0TW9kYWxQcm9wcz4gPSAoe1xuICBpc1Zpc2libGUsXG4gIG9uQ2xvc2UsXG4gIG9uU2F2ZSxcbiAgc2VyaWVzVGl0bGUsXG4gIHNlcmllc0NvdmVySW1hZ2UsXG4gIHNlcmllc0lkID0gMTIzIC8vIOm7mOiupOWAvO+8jOWunumZheS9v+eUqOaXtuW6lOivpeS8oOWFpeecn+WunueahHNlcmllc0lkXG59KSA9PiB7XG4gIGNvbnN0IG5vdGlmaWNhdGlvbiA9IEdldE5vdGlmaWNhdGlvbigpO1xuXG4gIGNvbnN0IFtjb3Vyc2VMaXN0LCBzZXRDb3Vyc2VMaXN0XSA9IHVzZVN0YXRlPENvdXJzZUl0ZW1bXT4oW10pO1xuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtyaWdodFBhbmVsVHlwZSwgc2V0UmlnaHRQYW5lbFR5cGVdID0gdXNlU3RhdGU8J25vbmUnIHwgJ3NldHRpbmdzJyB8ICdjb3Vyc2UnPignbm9uZScpO1xuICBjb25zdCBbc2VsZWN0ZWRDb3Vyc2VJZCwgc2V0U2VsZWN0ZWRDb3Vyc2VJZF0gPSB1c2VTdGF0ZTxudW1iZXIgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW3VwbG9hZGluZ0ZpbGVzLCBzZXRVcGxvYWRpbmdGaWxlc10gPSB1c2VTdGF0ZTxTZXQ8c3RyaW5nPj4obmV3IFNldCgpKTtcbiAgY29uc3QgW2lzUHVibGlzaGluZywgc2V0SXNQdWJsaXNoaW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2lzQ3JlYXRpbmcsIHNldElzQ3JlYXRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbaXNQdWJsaXNoaW5nU2VyaWVzLCBzZXRJc1B1Ymxpc2hpbmdTZXJpZXNdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbc2VyaWVzU3RhdHVzLCBzZXRTZXJpZXNTdGF0dXNdID0gdXNlU3RhdGU8bnVtYmVyPigwKTsgLy8gMD3ojYnnqL/vvIwxPeW3suWPkeW4g++8jDI95bey5b2S5qGjXG5cbiAgLy8g5Yig6Zmk56Gu6K6k5by556qX54q25oCBXG4gIGNvbnN0IFtkZWxldGVDb25maXJtVmlzaWJsZSwgc2V0RGVsZXRlQ29uZmlybVZpc2libGVdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbY291cnNlVG9EZWxldGUsIHNldENvdXJzZVRvRGVsZXRlXSA9IHVzZVN0YXRlPG51bWJlciB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbaXNEZWxldGluZywgc2V0SXNEZWxldGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG5cbiAgLy8g5qih5p2/6YCJ5oup5by556qX54q25oCBXG4gIGNvbnN0IFtpc1RlbXBsYXRlUGlja2VyT3Blbiwgc2V0SXNUZW1wbGF0ZVBpY2tlck9wZW5dID0gdXNlU3RhdGUoZmFsc2UpO1xuXG4gIGNvbnN0IFtlZGl0aW5nVGl0bGUsIHNldEVkaXRpbmdUaXRsZV0gPSB1c2VTdGF0ZShzZXJpZXNUaXRsZSk7XG4gIGNvbnN0IFtjb3Vyc2VHb2Fscywgc2V0Q291cnNlR29hbHNdID0gdXNlU3RhdGUoJycpO1xuICBjb25zdCBbY291cnNlT2JqZWN0aXZlcywgc2V0Q291cnNlT2JqZWN0aXZlc10gPSB1c2VTdGF0ZSgnJyk7XG4gIGNvbnN0IFtwcm9qZWN0TWVtYmVycywgc2V0UHJvamVjdE1lbWJlcnNdID0gdXNlU3RhdGUoJycpO1xuXG4gIC8vIOS9nOWTgeebuOWFs+eKtuaAgVxuICBjb25zdCBbd29ya3MsIHNldFdvcmtzXSA9IHVzZVN0YXRlPGFueVtdPihbXSk7XG4gIGNvbnN0IFtsb2FkaW5nV29ya3MsIHNldExvYWRpbmdXb3Jrc10gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtzZWxlY3RlZFdvcmtJZHMsIHNldFNlbGVjdGVkV29ya0lkc10gPSB1c2VTdGF0ZTxudW1iZXJbXT4oW10pO1xuXG4gIC8vIOaHkuWKoOi9veebuOWFs+eKtuaAgVxuICBjb25zdCBbY3VycmVudFBhZ2UsIHNldEN1cnJlbnRQYWdlXSA9IHVzZVN0YXRlKDEpO1xuICBjb25zdCBbaGFzTW9yZVdvcmtzLCBzZXRIYXNNb3JlV29ya3NdID0gdXNlU3RhdGUodHJ1ZSk7XG4gIGNvbnN0IFtsb2FkaW5nTW9yZVdvcmtzLCBzZXRMb2FkaW5nTW9yZVdvcmtzXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgcGFnZVNpemUgPSA1OyAvLyDmr4/mrKHliqDovb015Liq5L2c5ZOBXG5cbiAgLy8g6K++56iL5qCH562+55u45YWz54q25oCBXG4gIGNvbnN0IFtjb3Vyc2VUYWdzLCBzZXRDb3Vyc2VUYWdzXSA9IHVzZVN0YXRlPENvdXJzZVRhZ1tdPihbXSk7XG4gIGNvbnN0IFtzZWxlY3RlZFRhZ3MsIHNldFNlbGVjdGVkVGFnc10gPSB1c2VTdGF0ZTxudW1iZXJbXT4oW10pO1xuICBjb25zdCBbdGFnc0xvYWRpbmcsIHNldFRhZ3NMb2FkaW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcblxuICAvLyDor77nqIvor6bnu4bnvJbovpHnirbmgIFcbiAgY29uc3QgW2NvdXJzZURldGFpbCwgc2V0Q291cnNlRGV0YWlsXSA9IHVzZVN0YXRlKHtcbiAgICB0aXRsZTogJycsXG4gICAgZGVzY3JpcHRpb246ICcnLFxuICAgIGNvdmVySW1hZ2U6ICcnLFxuICAgIHZpZGVvVXJsOiAnJyxcbiAgICB2aWRlb05hbWU6ICcnLFxuICAgIGlzVmlkZW9FbmFibGVkOiBmYWxzZSxcbiAgICBhdHRhY2htZW50VXJsOiAnJyxcbiAgICBhdHRhY2htZW50TmFtZTogJycsXG4gICAgaXNBdHRhY2htZW50RW5hYmxlZDogZmFsc2UsXG4gICAgdGVhY2hpbmdNYXRlcmlhbHM6IFtdIGFzIHsgdHlwZTogc3RyaW5nOyBuYW1lOiBzdHJpbmc7IHVybDogc3RyaW5nIH1bXSxcbiAgICAvLyDmlK/mjIF0ZWFjaGluZ0luZm/nu5PmnoRcbiAgICB0ZWFjaGluZ0luZm86IFtdIGFzIHsgdGl0bGU6IHN0cmluZzsgY29udGVudDogc3RyaW5nIH1bXSxcbiAgICAvLyDmlK/mjIFjb250ZW50Q29uZmln57uT5p6EXG4gICAgY29udGVudENvbmZpZzoge1xuICAgICAgaGFzVmlkZW86IDAsXG4gICAgICBoYXNEb2N1bWVudDogMCxcbiAgICAgIGhhc0F1ZGlvOiAwLFxuICAgICAgdmlkZW86IHsgdXJsOiAnJywgbmFtZTogJycgfSxcbiAgICAgIGRvY3VtZW50OiB7IHVybDogJycsIG5hbWU6ICcnIH0sXG4gICAgICBhdWRpbzogeyB1cmw6ICcnLCBuYW1lOiAnJyB9XG4gICAgfSxcbiAgICBjb3Vyc2VDb250ZW50OiB7XG4gICAgICB0b3BpYzogJycsXG4gICAgICBjb250ZW50OiAnJ1xuICAgIH0sXG4gICAgaXNPbmVLZXlPcGVuOiBmYWxzZSxcbiAgICBpc0Rpc3RyaWJ1dGlvbkVuYWJsZWQ6IGZhbHNlLFxuICAgIGRpc3RyaWJ1dGlvblJld2FyZDogJycsXG4gICAgc2VsZWN0ZWRUZW1wbGF0ZTogJycsXG4gICAgaXNEaXN0cmlidXRpb25XYXRlcjogZmFsc2UsXG4gICAgcmVxdWlyZWRFbmVyZ3k6ICcnLFxuICAgIGVuZXJneUFtb3VudDogJycsXG4gICAgaXNEaXN0cmlidXRpb25MaW1pdDogZmFsc2UsXG4gICAgZGlzdHJpYnV0aW9uQ29uZGl0aW9uczoge1xuICAgICAgaW52aXRlQ291bnQ6ICcnLFxuICAgICAgdGFza0NvdW50OiAnJyxcbiAgICAgIGV4cGVyaWVuY2U6ICcnXG4gICAgfSxcbiAgICBpc0Rpc3RyaWJ1dGlvblRpbWU6IGZhbHNlLFxuICAgIGRpc3RyaWJ1dGlvblRpbWVDb25kaXRpb25zOiB7XG4gICAgICBzdGFydFRpbWU6ICcnLFxuICAgICAgZW5kVGltZTogJydcbiAgICB9LFxuICAgIGRpc3RyaWJ1dGlvbk1hdGVyaWFsczogW10gYXMgeyB0eXBlOiBzdHJpbmc7IG5hbWU6IHN0cmluZyB9W10sXG4gICAgLy8g5Lu75Yqh6YWN572u55u45YWz54q25oCBXG4gICAgdGFza0NvbmZpZzoge1xuICAgICAgdGFza05hbWU6ICcnLFxuICAgICAgdGFza0R1cmF0aW9uOiAnJyxcbiAgICAgIHRhc2tEZXNjcmlwdGlvbjogJycsXG4gICAgICBzZWxmQXNzZXNzbWVudEl0ZW1zOiBbJyddLFxuICAgICAgcmVmZXJlbmNlV29ya3M6IFtdIGFzIHsgdHlwZTogc3RyaW5nOyBuYW1lOiBzdHJpbmcgfVtdLFxuICAgICAgcmVmZXJlbmNlUmVzb3VyY2VzOiBbXSBhcyB7IHR5cGU6IHN0cmluZzsgbmFtZTogc3RyaW5nOyB1cmw/OiBzdHJpbmcgfVtdXG4gICAgfVxuICB9KTtcblxuICAvLyDojrflj5bor77nqIvliJfooajmlbDmja5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoaXNWaXNpYmxlICYmIHNlcmllc0lkKSB7XG4gICAgICAvLyDmo4Dmn6XnlKjmiLfnmbvlvZXnirbmgIFcbiAgICAgIGNvbnN0IHRva2VuID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3Rva2VuJyk7XG4gICAgICBjb25zb2xlLmxvZygn8J+UkCDmo4Dmn6XnmbvlvZXnirbmgIHvvIx0b2tlbuWtmOWcqDonLCAhIXRva2VuKTtcbiAgICAgIGNvbnNvbGUubG9nKCfwn5SNIHNlcmllc0lkOicsIHNlcmllc0lkKTtcblxuICAgICAgaWYgKCF0b2tlbikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCfinYwg55So5oi35pyq55m75b2V77yM5peg5rOV6I635Y+W6K++56iL5YiX6KGoJyk7XG4gICAgICAgIC8vIOiuvue9ruepuuWIl+ihqO+8jOaYvuekuuepuueKtuaAgVxuICAgICAgICBzZXRDb3Vyc2VMaXN0KFtdKTtcbiAgICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cblxuICAgICAgbG9hZENvdXJzZUxpc3QoKTtcbiAgICAgIGxvYWRDb3Vyc2VUYWdzKCk7XG4gICAgICBsb2FkU2VyaWVzRGV0YWlsKCk7XG4gICAgICBsb2FkVXNlcldvcmtzKCk7XG4gICAgfVxuICB9LCBbaXNWaXNpYmxlLCBzZXJpZXNJZF0pO1xuXG4gIGNvbnN0IGxvYWRDb3Vyc2VMaXN0ID0gYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBzZXRMb2FkaW5nKHRydWUpO1xuICAgICAgY29uc29sZS5sb2coJ/CflI0g5byA5aeL5Yqg6L296K++56iL5YiX6KGo77yMc2VyaWVzSWQ6Jywgc2VyaWVzSWQpO1xuXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoQ291cnNlTGlzdChzZXJpZXNJZCk7XG4gICAgICBjb25zb2xlLmxvZygn8J+ToSBBUEnlk43lupQ6JywgcmVzcG9uc2UpO1xuXG4gICAgICBpZiAocmVzcG9uc2UuY29kZSA9PT0gMjAwKSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCfinIUg6K++56iL5YiX6KGo5pWw5o2uOicsIHJlc3BvbnNlLmRhdGEpO1xuICAgICAgICBjb25zdCBjb3Vyc2VzID0gcmVzcG9uc2UuZGF0YS5saXN0IHx8IFtdO1xuICAgICAgICBjb25zb2xlLmxvZygn4pyFIOino+aekOeahOivvueoi+aVsOe7hDonLCBjb3Vyc2VzKTtcbiAgICAgICAgY29uc29sZS5sb2coJ/Cfk4og6K++56iLSUTor6bmg4U6JywgY291cnNlcy5tYXAoKGM6IGFueSkgPT4gKHtcbiAgICAgICAgICBpZDogYy5pZCxcbiAgICAgICAgICB0eXBlOiB0eXBlb2YgYy5pZCxcbiAgICAgICAgICB0aXRsZTogYy50aXRsZSxcbiAgICAgICAgICBzdGF0dXM6IGMuc3RhdHVzXG4gICAgICAgIH0pKSk7XG4gICAgICAgIGNvbnNvbGUubG9nKCfwn5OKIOivvueoi+eKtuaAgee7n+iuoTonLCB7XG4gICAgICAgICAgdG90YWw6IGNvdXJzZXMubGVuZ3RoLFxuICAgICAgICAgIGRyYWZ0OiBjb3Vyc2VzLmZpbHRlcigoYzogYW55KSA9PiBjLnN0YXR1cyA9PT0gMCkubGVuZ3RoLFxuICAgICAgICAgIHB1Ymxpc2hlZDogY291cnNlcy5maWx0ZXIoKGM6IGFueSkgPT4gYy5zdGF0dXMgPT09IDEpLmxlbmd0aFxuICAgICAgICB9KTtcbiAgICAgICAgc2V0Q291cnNlTGlzdChjb3Vyc2VzKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBBUEnov5Tlm57plJnor686JywgcmVzcG9uc2UpO1xuICAgICAgICBzZXRDb3Vyc2VMaXN0KFtdKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfinYwg5Yqg6L296K++56iL5YiX6KGo5aSx6LSlOicsIGVycm9yKTtcblxuICAgICAgLy8g5qOA5p+l5piv5ZCm5piv6K6k6K+B6ZSZ6K+vXG4gICAgICBpZiAoZXJyb3IucmVzcG9uc2U/LnN0YXR1cyA9PT0gNDAxKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ/CflJAg6K6k6K+B5aSx6LSl77yM55So5oi35pyq55m75b2V5oiWdG9rZW7lt7Lov4fmnJ8nKTtcbiAgICAgIH0gZWxzZSBpZiAoZXJyb3IucmVzcG9uc2U/LnN0YXR1cyA9PT0gNDAzKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ/Cfmqsg5p2D6ZmQ5LiN6Laz77yM5peg5rOV6K6/6Zeu6K+l57O75YiX6K++56iLJyk7XG4gICAgICB9IGVsc2UgaWYgKGVycm9yLnJlc3BvbnNlPy5zdGF0dXMgPT09IDQwNCkge1xuICAgICAgICBjb25zb2xlLmVycm9yKCfwn5OtIOezu+WIl+ivvueoi+S4jeWtmOWcqO+8jHNlcmllc0lkOicsIHNlcmllc0lkKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ/CflKcg5YW25LuW6ZSZ6K+vOicsIGVycm9yLm1lc3NhZ2UpO1xuICAgICAgfVxuXG4gICAgICBzZXRDb3Vyc2VMaXN0KFtdKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIC8vIOWKoOi9veezu+WIl+ivvueoi+ivpuaDhVxuICBjb25zdCBsb2FkU2VyaWVzRGV0YWlsID0gYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zb2xlLmxvZygn8J+UjSDlvIDlp4vliqDovb3ns7vliJfor77nqIvor6bmg4XvvIxzZXJpZXNJZDonLCBzZXJpZXNJZCk7XG5cbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2hTZXJpZXNEZXRhaWwoc2VyaWVzSWQpO1xuICAgICAgY29uc29sZS5sb2coJ/Cfk6Eg57O75YiX6K+m5oOF5ZON5bqUOicsIHJlc3BvbnNlKTtcblxuICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCAmJiByZXNwb25zZS5kYXRhKSB7XG4gICAgICAgIGNvbnN0IHNlcmllc0RhdGEgPSByZXNwb25zZS5kYXRhO1xuICAgICAgICBjb25zb2xlLmxvZygn4pyFIOezu+WIl+ivvueoi+ivpuaDhTonLCBzZXJpZXNEYXRhKTtcbiAgICAgICAgc2V0U2VyaWVzU3RhdHVzKHNlcmllc0RhdGEuc3RhdHVzIHx8IDApO1xuICAgICAgICBjb25zb2xlLmxvZygn8J+TiiDns7vliJfor77nqIvnirbmgIE6Jywgc2VyaWVzRGF0YS5zdGF0dXMsICcoMD3ojYnnqL/vvIwxPeW3suWPkeW4g++8jDI95bey5b2S5qGjKScpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgY29uc29sZS5lcnJvcign4p2MIOiOt+WPluezu+WIl+ivpuaDheWksei0pTonLCByZXNwb25zZS5tZXNzYWdlKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfinYwg5Yqg6L2957O75YiX6K+m5oOF5byC5bi4OicsIGVycm9yKTtcbiAgICB9XG4gIH07XG5cbiAgLy8g5Yqg6L2955So5oi35L2c5ZOB5pWw5o2uXG4gIGNvbnN0IGxvYWRVc2VyV29ya3MgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIHNldExvYWRpbmdXb3Jrcyh0cnVlKTtcblxuICAgICAgLy8g5LuObG9jYWxTdG9yYWdl6I635Y+W55So5oi3SURcbiAgICAgIGNvbnN0IHVzZXJJZCA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCd1c2VySWQnKSB8fCAnMjg5Nic7IC8vIOm7mOiupOS9v+eUqDI4OTZcblxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB3b3Jrc0FwaS5nZXRUZWFjaGVyV29ya3MoTnVtYmVyKHVzZXJJZCksIDEsIDEwMDApO1xuXG4gICAgICAvLyDmo4Dmn6XlpJrnp43lj6/og73nmoTmlbDmja7nu5PmnoRcbiAgICAgIGxldCB3b3Jrc0xpc3QgPSBbXTtcblxuICAgICAgaWYgKHJlc3BvbnNlLmRhdGE/LmNvZGUgPT09IDIwMCkge1xuICAgICAgICAvLyDmg4XlhrUxOiByZXNwb25zZS5kYXRhLmRhdGEubGlzdFxuICAgICAgICBpZiAocmVzcG9uc2UuZGF0YT8uZGF0YT8ubGlzdCkge1xuICAgICAgICAgIHdvcmtzTGlzdCA9IHJlc3BvbnNlLmRhdGEuZGF0YS5saXN0O1xuICAgICAgICB9XG4gICAgICAgIC8vIOaDheWGtTI6IHJlc3BvbnNlLmRhdGEuZGF0YSAo55u05o6l5piv5pWw57uEKVxuICAgICAgICBlbHNlIGlmIChBcnJheS5pc0FycmF5KHJlc3BvbnNlLmRhdGE/LmRhdGEpKSB7XG4gICAgICAgICAgd29ya3NMaXN0ID0gcmVzcG9uc2UuZGF0YS5kYXRhO1xuICAgICAgICB9XG4gICAgICAgIC8vIOaDheWGtTM6IHJlc3BvbnNlLmRhdGEgKOebtOaOpeaYr+aVsOe7hClcbiAgICAgICAgZWxzZSBpZiAoQXJyYXkuaXNBcnJheShyZXNwb25zZS5kYXRhKSkge1xuICAgICAgICAgIHdvcmtzTGlzdCA9IHJlc3BvbnNlLmRhdGE7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICAgIC8vIOaDheWGtTQ6IOebtOaOpeajgOafpXJlc3BvbnNl5piv5ZCm5piv5pWw57uEXG4gICAgICBlbHNlIGlmIChBcnJheS5pc0FycmF5KHJlc3BvbnNlKSkge1xuICAgICAgICB3b3Jrc0xpc3QgPSByZXNwb25zZTtcbiAgICAgIH1cblxuICAgICAgc2V0V29ya3Mod29ya3NMaXN0KTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcign5Yqg6L295L2c5ZOB5pWw5o2u5aSx6LSlOicsIGVycm9yKTtcbiAgICAgIHNldFdvcmtzKFtdKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TG9hZGluZ1dvcmtzKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgLy8g5aSE55CG5L2c5ZOB6YCJ5oupXG4gIGNvbnN0IGhhbmRsZVdvcmtTZWxlY3QgPSAod29ya0lkOiBudW1iZXIpID0+IHtcbiAgICBpZiAoc2VsZWN0ZWRXb3JrSWRzLmluY2x1ZGVzKHdvcmtJZCkpIHtcbiAgICAgIC8vIOWPlua2iOmAieS4rVxuICAgICAgc2V0U2VsZWN0ZWRXb3JrSWRzKHByZXYgPT4gcHJldi5maWx0ZXIoaWQgPT4gaWQgIT09IHdvcmtJZCkpO1xuICAgIH0gZWxzZSB7XG4gICAgICAvLyDpgInkuK1cbiAgICAgIHNldFNlbGVjdGVkV29ya0lkcyhwcmV2ID0+IFsuLi5wcmV2LCB3b3JrSWRdKTtcbiAgICB9XG4gIH07XG5cbiAgLy8g6byg5qCH5rua6L2u5LqL5Lu25aSE55CGIC0g5bCG5Z6C55u05rua6L2u6L2s5o2i5Li65rC05bmz5rua5YqoXG4gIGNvbnN0IGhhbmRsZVdvcmtzV2hlZWwgPSB1c2VDYWxsYmFjaygoZTogUmVhY3QuV2hlZWxFdmVudDxIVE1MRGl2RWxlbWVudD4pID0+IHtcbiAgICAvLyDpmLvmraLpu5jorqTnmoTlnoLnm7Tmu5rliqhcbiAgICBlLnByZXZlbnREZWZhdWx0KCk7XG5cbiAgICBjb25zdCBjb250YWluZXIgPSBlLmN1cnJlbnRUYXJnZXQ7XG4gICAgY29uc3Qgc2Nyb2xsQW1vdW50ID0gZS5kZWx0YVk7IC8vIOiOt+WPlua7mui9rueahOWeguebtOa7muWKqOmHj1xuXG4gICAgLy8g5bCG5Z6C55u05rua5Yqo6L2s5o2i5Li65rC05bmz5rua5YqoXG4gICAgY29udGFpbmVyLnNjcm9sbExlZnQgKz0gc2Nyb2xsQW1vdW50O1xuICB9LCBbXSk7XG5cbiAgLy8g5Yqg6L296K++56iL5qCH562+XG4gIGNvbnN0IGxvYWRDb3Vyc2VUYWdzID0gYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBzZXRUYWdzTG9hZGluZyh0cnVlKTtcbiAgICAgIGNvbnNvbGUubG9nKCfwn4+377iPIOW8gOWni+WKoOi9veivvueoi+agh+etvicpO1xuXG5cblxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaENvdXJzZVRhZ3MoKTtcbiAgICAgIGNvbnNvbGUubG9nKCfwn5OhIOagh+etvkFQSeWujOaVtOWTjeW6lDonLCByZXNwb25zZSk7XG5cbiAgICAgIC8vIOajgOafpeWTjeW6lOe7k+aehFxuICAgICAgaWYgKHJlc3BvbnNlICYmIHJlc3BvbnNlLmRhdGEpIHtcbiAgICAgICAgY29uc29sZS5sb2coJ/Cfk4og5ZON5bqU5pWw5o2uOicsIHJlc3BvbnNlLmRhdGEpO1xuXG4gICAgICAgIGxldCB0YWdzOiBDb3Vyc2VUYWdbXSA9IFtdO1xuXG4gICAgICAgIC8vIOWkhOeQhuagh+WHhueahEFQSeWTjeW6lOagvOW8jyAocmVzcG9uc2UuZGF0YS5saXN0KSAtIOagueaNruWunumZhUFQSeWTjeW6lFxuICAgICAgICBpZiAocmVzcG9uc2UuZGF0YS5saXN0ICYmIEFycmF5LmlzQXJyYXkocmVzcG9uc2UuZGF0YS5saXN0KSkge1xuICAgICAgICAgIHRhZ3MgPSByZXNwb25zZS5kYXRhLmxpc3Q7XG4gICAgICAgICAgY29uc29sZS5sb2coJ+KchSDku44gZGF0YS5saXN0IOino+aekOWIsOagh+etvjonLCB0YWdzLmxlbmd0aCwgJ+S4qicpO1xuICAgICAgICB9XG4gICAgICAgIC8vIOWkhOeQhuebtOaOpeaVsOe7hOagvOW8jyAocmVzcG9uc2UuZGF0YSlcbiAgICAgICAgZWxzZSBpZiAoQXJyYXkuaXNBcnJheShyZXNwb25zZS5kYXRhKSkge1xuICAgICAgICAgIHRhZ3MgPSByZXNwb25zZS5kYXRhO1xuICAgICAgICAgIGNvbnNvbGUubG9nKCfinIUg5LuOIGRhdGEg5pWw57uE6Kej5p6Q5Yiw5qCH562+OicsIHRhZ3MubGVuZ3RoLCAn5LiqJyk7XG4gICAgICAgIH1cbiAgICAgICAgLy8g5aSE55CG5bWM5aWX55qEQVBJ5ZON5bqU5qC85byPIChyZXNwb25zZS5kYXRhLmRhdGEubGlzdCkgLSDlpIfnlKjmoLzlvI9cbiAgICAgICAgZWxzZSBpZiAocmVzcG9uc2UuZGF0YS5kYXRhICYmIHJlc3BvbnNlLmRhdGEuZGF0YS5saXN0ICYmIEFycmF5LmlzQXJyYXkocmVzcG9uc2UuZGF0YS5kYXRhLmxpc3QpKSB7XG4gICAgICAgICAgdGFncyA9IHJlc3BvbnNlLmRhdGEuZGF0YS5saXN0O1xuICAgICAgICAgIGNvbnNvbGUubG9nKCfinIUg5LuOIGRhdGEuZGF0YS5saXN0IOino+aekOWIsOagh+etvjonLCB0YWdzLmxlbmd0aCwgJ+S4qicpO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8g6aqM6K+B5qCH562+5pWw5o2u5qC85byPXG4gICAgICAgIGNvbnNvbGUubG9nKCfwn5SNIOWOn+Wni+agh+etvuaVsOaNrjonLCB0YWdzKTtcbiAgICAgICAgY29uc29sZS5sb2coJ/CflI0g5qCH562+5pWw5o2u57G75Z6L5qOA5p+lOicpO1xuICAgICAgICB0YWdzLmZvckVhY2goKHRhZywgaW5kZXgpID0+IHtcbiAgICAgICAgICBjb25zb2xlLmxvZyhg5qCH562+JHtpbmRleH06YCwge1xuICAgICAgICAgICAgdGFnLFxuICAgICAgICAgICAgaGFzVGFnOiAhIXRhZyxcbiAgICAgICAgICAgIGlkVHlwZTogdHlwZW9mIHRhZz8uaWQsXG4gICAgICAgICAgICBuYW1lVHlwZTogdHlwZW9mIHRhZz8ubmFtZSxcbiAgICAgICAgICAgIG5hbWVWYWx1ZTogdGFnPy5uYW1lLFxuICAgICAgICAgICAgbmFtZU5vdEVtcHR5OiB0YWc/Lm5hbWU/LnRyaW0oKSAhPT0gJydcbiAgICAgICAgICB9KTtcbiAgICAgICAgfSk7XG5cbiAgICAgICAgY29uc3QgdmFsaWRUYWdzID0gdGFncy5maWx0ZXIodGFnID0+IHtcbiAgICAgICAgICBjb25zdCBpc1ZhbGlkID0gdGFnICYmXG4gICAgICAgICAgICB0eXBlb2YgdGFnLmlkID09PSAnbnVtYmVyJyAmJlxuICAgICAgICAgICAgdHlwZW9mIHRhZy5uYW1lID09PSAnc3RyaW5nJyAmJlxuICAgICAgICAgICAgdGFnLm5hbWUudHJpbSgpICE9PSAnJztcblxuICAgICAgICAgIGlmICghaXNWYWxpZCkge1xuICAgICAgICAgICAgY29uc29sZS5sb2coJ+KdjCDml6DmlYjmoIfnrb46JywgdGFnLCB7XG4gICAgICAgICAgICAgIGhhc1RhZzogISF0YWcsXG4gICAgICAgICAgICAgIGlkVHlwZTogdHlwZW9mIHRhZz8uaWQsXG4gICAgICAgICAgICAgIG5hbWVUeXBlOiB0eXBlb2YgdGFnPy5uYW1lLFxuICAgICAgICAgICAgICBuYW1lVmFsdWU6IHRhZz8ubmFtZVxuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgcmV0dXJuIGlzVmFsaWQ7XG4gICAgICAgIH0pO1xuXG4gICAgICAgIGNvbnNvbGUubG9nKCfinIUg5pyJ5pWI5qCH562+5pWw6YePOicsIHZhbGlkVGFncy5sZW5ndGgpO1xuICAgICAgICBjb25zb2xlLmxvZygn4pyFIOacieaViOagh+etvuivpuaDhTonLCB2YWxpZFRhZ3MpO1xuXG4gICAgICAgIGlmICh2YWxpZFRhZ3MubGVuZ3RoID4gMCkge1xuICAgICAgICAgIHNldENvdXJzZVRhZ3ModmFsaWRUYWdzKTtcbiAgICAgICAgICBjb25zb2xlLmxvZygn4pyFIOaIkOWKn+iuvue9ruecn+Wunuagh+etvuaVsOaNricpO1xuICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICBjb25zb2xlLndhcm4oJ+KaoO+4jyDmsqHmnInmnInmlYjnmoTmoIfnrb7mlbDmja4nKTtcbiAgICAgICAgfVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgY29uc29sZS53YXJuKCfimqDvuI8gQVBJ5ZON5bqU5qC85byP5LiN5q2j56GuOicsIHJlc3BvbnNlKTtcbiAgICAgIH1cblxuICAgICAgLy8g5aaC5p6c5rKh5pyJ55yf5a6e5pWw5o2u77yM6K6+572u56m65pWw57uEXG4gICAgICBjb25zb2xlLmxvZygn8J+TrSDmsqHmnInmoIfnrb7mlbDmja7vvIzorr7nva7nqbrmlbDnu4QnKTtcbiAgICAgIHNldENvdXJzZVRhZ3MoW10pO1xuXG4gICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgY29uc29sZS5lcnJvcign4p2MIOWKoOi9veivvueoi+agh+etvuWksei0pTonLCBlcnJvcik7XG4gICAgICBjb25zb2xlLmVycm9yKCfinYwg6ZSZ6K+v6K+m5oOFOicsIHtcbiAgICAgICAgbWVzc2FnZTogZXJyb3IubWVzc2FnZSxcbiAgICAgICAgc3RhdHVzOiBlcnJvci5yZXNwb25zZT8uc3RhdHVzLFxuICAgICAgICBzdGF0dXNUZXh0OiBlcnJvci5yZXNwb25zZT8uc3RhdHVzVGV4dCxcbiAgICAgICAgZGF0YTogZXJyb3IucmVzcG9uc2U/LmRhdGFcbiAgICAgIH0pO1xuXG4gICAgICAvLyDlj5HnlJ/plJnor6/ml7borr7nva7nqbrmlbDnu4RcbiAgICAgIHNldENvdXJzZVRhZ3MoW10pO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRUYWdzTG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIC8vIOa3u+WKoOaWsOivvueoi1xuICBjb25zdCBhZGROZXdDb3Vyc2UgPSAoKSA9PiB7XG4gICAgY29uc3QgbmV3Q291cnNlOiBDb3Vyc2VJdGVtID0ge1xuICAgICAgaWQ6IERhdGUubm93KCksXG4gICAgICBzZXJpZXNJZDogc2VyaWVzSWQsXG4gICAgICB0aXRsZTogYOesrCR7Y291cnNlTGlzdC5sZW5ndGggKyAxfeivviAtIOaWsOivvuaXtmAsXG4gICAgICBkZXNjcmlwdGlvbjogJycsXG4gICAgICBjb3ZlckltYWdlOiAnJyxcbiAgICAgIG9yZGVySW5kZXg6IGNvdXJzZUxpc3QubGVuZ3RoICsgMSxcbiAgICAgIHN0YXR1czogMCxcbiAgICAgIHN0YXR1c0xhYmVsOiAn6I2J56i/JyxcbiAgICAgIGhhc1ZpZGVvOiAwLFxuICAgICAgaGFzRG9jdW1lbnQ6IDAsXG4gICAgICBoYXNBdWRpbzogMCxcbiAgICAgIHZpZGVvRHVyYXRpb246IDAsXG4gICAgICB2aWRlb0R1cmF0aW9uTGFiZWw6ICcnLFxuICAgICAgdmlkZW9OYW1lOiAnJyxcbiAgICAgIGZpcnN0VGVhY2hpbmdUaXRsZTogJycsXG4gICAgICByZXNvdXJjZXNDb3VudDogMCxcbiAgICAgIGNyZWF0ZWRBdDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgICAgdXBkYXRlZEF0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICB9O1xuICAgIHNldENvdXJzZUxpc3QoWy4uLmNvdXJzZUxpc3QsIG5ld0NvdXJzZV0pO1xuICAgIC8vIOiHquWKqOmAieS4reaWsOa3u+WKoOeahOivvueoi1xuICAgIHNob3dDb3Vyc2VQYW5lbChuZXdDb3Vyc2UuaWQpO1xuICB9O1xuXG4gIC8vIOaYvuekuuWIoOmZpOehruiupOW8ueeql1xuICBjb25zdCBzaG93RGVsZXRlQ29uZmlybSA9IChpZDogbnVtYmVyKSA9PiB7XG4gICAgc2V0Q291cnNlVG9EZWxldGUoaWQpO1xuICAgIHNldERlbGV0ZUNvbmZpcm1WaXNpYmxlKHRydWUpO1xuICB9O1xuXG4gIC8vIOehruiupOWIoOmZpOivvueoi1xuICBjb25zdCBjb25maXJtRGVsZXRlQ291cnNlID0gYXN5bmMgKCkgPT4ge1xuICAgIGlmICghY291cnNlVG9EZWxldGUpIHJldHVybjtcblxuICAgIHRyeSB7XG4gICAgICBzZXRJc0RlbGV0aW5nKHRydWUpO1xuXG4gICAgICAvLyDosIPnlKjliKDpmaRBUElcbiAgICAgIGF3YWl0IGNvdXJzZU1hbmFnZW1lbnRBcGkuZGVsZXRlQ291cnNlKGNvdXJzZVRvRGVsZXRlKTtcblxuICAgICAgLy8g5LuO5YiX6KGo5Lit56e76Zmk6K++56iLXG4gICAgICBzZXRDb3Vyc2VMaXN0KGNvdXJzZUxpc3QuZmlsdGVyKGNvdXJzZSA9PiBjb3Vyc2UuaWQgIT09IGNvdXJzZVRvRGVsZXRlKSk7XG5cbiAgICAgIC8vIOWmguaenOWIoOmZpOeahOaYr+W9k+WJjemAieS4reeahOivvueoi++8jOa4heepuuWPs+S+p+mdouadv1xuICAgICAgaWYgKHNlbGVjdGVkQ291cnNlSWQgPT09IGNvdXJzZVRvRGVsZXRlKSB7XG4gICAgICAgIHNldFJpZ2h0UGFuZWxUeXBlKCdub25lJyk7XG4gICAgICAgIHNldFNlbGVjdGVkQ291cnNlSWQobnVsbCk7XG4gICAgICB9XG5cbiAgICAgIC8vIOWFs+mXreehruiupOW8ueeql1xuICAgICAgc2V0RGVsZXRlQ29uZmlybVZpc2libGUoZmFsc2UpO1xuICAgICAgc2V0Q291cnNlVG9EZWxldGUobnVsbCk7XG5cbiAgICAgIC8vIOaYvuekuuaIkOWKn+aPkOekulxuICAgICAgbm90aWZpY2F0aW9uLnN1Y2Nlc3MoJ+ivvueoi+W3suaIkOWKn+WIoOmZpCcpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfliKDpmaTor77nqIvlpLHotKU6JywgZXJyb3IpO1xuICAgICAgbm90aWZpY2F0aW9uLmVycm9yKCfliKDpmaTor77nqIvlpLHotKXvvIzor7fph43or5UnKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0SXNEZWxldGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIC8vIOWPlua2iOWIoOmZpFxuICBjb25zdCBjYW5jZWxEZWxldGUgPSAoKSA9PiB7XG4gICAgaWYgKGlzRGVsZXRpbmcpIHJldHVybjsgLy8g5q2j5Zyo5Yig6Zmk5pe25LiN5YWB6K645Y+W5raIXG4gICAgc2V0RGVsZXRlQ29uZmlybVZpc2libGUoZmFsc2UpO1xuICAgIHNldENvdXJzZVRvRGVsZXRlKG51bGwpO1xuICB9O1xuXG4gIC8vIOabtOaWsOivvueoi+agh+mimFxuICBjb25zdCB1cGRhdGVDb3Vyc2VUaXRsZSA9IChpZDogbnVtYmVyLCBuZXdUaXRsZTogc3RyaW5nKSA9PiB7XG4gICAgc2V0Q291cnNlTGlzdChjb3Vyc2VMaXN0Lm1hcChjb3Vyc2UgPT5cbiAgICAgIGNvdXJzZS5pZCA9PT0gaWQgPyB7IC4uLmNvdXJzZSwgdGl0bGU6IG5ld1RpdGxlIH0gOiBjb3Vyc2VcbiAgICApKTtcbiAgfTtcblxuICAvLyDlpITnkIbor77nqIvlsIHpnaLkuIrkvKBcbiAgY29uc3QgaGFuZGxlQ292ZXJVcGxvYWQgPSBhc3luYyAoZXZlbnQ6IFJlYWN0LkNoYW5nZUV2ZW50PEhUTUxJbnB1dEVsZW1lbnQ+KSA9PiB7XG4gICAgY29uc3QgZmlsZSA9IGV2ZW50LnRhcmdldC5maWxlcz8uWzBdO1xuICAgIGlmIChmaWxlKSB7XG4gICAgICAvLyDmo4Dmn6Xmlofku7bnsbvlnotcbiAgICAgIGNvbnN0IGFsbG93ZWRUeXBlcyA9IFsnaW1hZ2UvanBlZycsICdpbWFnZS9qcGcnLCAnaW1hZ2UvcG5nJywgJ2ltYWdlL2dpZiddO1xuICAgICAgaWYgKCFhbGxvd2VkVHlwZXMuaW5jbHVkZXMoZmlsZS50eXBlKSkge1xuICAgICAgICBub3RpZmljYXRpb24uZXJyb3IoJ+ivt+mAieaLqSBKUEfjgIFQTkcg5oiWIEdJRiDmoLzlvI/nmoTlm77niYfmlofku7YnKTtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuXG4gICAgICAvLyDmo4Dmn6Xmlofku7blpKflsI8gKDEwTUIpXG4gICAgICBpZiAoZmlsZS5zaXplID4gMTAgKiAxMDI0ICogMTAyNCkge1xuICAgICAgICBub3RpZmljYXRpb24uZXJyb3IoJ+aWh+S7tuWkp+Wwj+S4jeiDvei2hei/hyAxME1CJyk7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cblxuICAgICAgdHJ5IHtcbiAgICAgICAgY29uc29sZS5sb2coJ/Cfk6Qg5byA5aeL5LiK5Lyg6K++56iL5bCB6Z2iOicsIGZpbGUubmFtZSk7XG5cbiAgICAgICAgLy8g5re75Yqg5Yiw5LiK5Lyg5Lit55qE5paH5Lu25YiX6KGoXG4gICAgICAgIHNldFVwbG9hZGluZ0ZpbGVzKHByZXYgPT4gbmV3IFNldChwcmV2KS5hZGQoJ2NvdmVyJykpO1xuXG4gICAgICAgIC8vIOWFiOaYvuekuumihOiniOWbvueJh1xuICAgICAgICBjb25zdCBwcmV2aWV3VXJsID0gVVJMLmNyZWF0ZU9iamVjdFVSTChmaWxlKTtcbiAgICAgICAgc2V0Q291cnNlRGV0YWlsKHByZXYgPT4gKHsgLi4ucHJldiwgY292ZXJJbWFnZTogcHJldmlld1VybCB9KSk7XG5cbiAgICAgICAgLy8g5LiK5Lyg5YiwT1NTXG4gICAgICAgIGNvbnN0IGltYWdlVXJsID0gYXdhaXQgdXBsb2FkQXBpLnVwbG9hZFRvT3NzKGZpbGUpO1xuICAgICAgICBjb25zb2xlLmxvZygn4pyFIOivvueoi+WwgemdouS4iuS8oOaIkOWKnzonLCBpbWFnZVVybCk7XG5cbiAgICAgICAgLy8g5pu05paw6K++56iL6K+m5oOF5Lit55qE5bCB6Z2i5Li655yf5a6eVVJMXG4gICAgICAgIHNldENvdXJzZURldGFpbChwcmV2ID0+ICh7IC4uLnByZXYsIGNvdmVySW1hZ2U6IGltYWdlVXJsIH0pKTtcblxuICAgICAgICAvLyDlkIzml7bmm7TmlrDor77nqIvliJfooajkuK3nmoTlsIHpnaJcbiAgICAgICAgaWYgKHNlbGVjdGVkQ291cnNlSWQpIHtcbiAgICAgICAgICBzZXRDb3Vyc2VMaXN0KHByZXYgPT4gcHJldi5tYXAoY291cnNlID0+XG4gICAgICAgICAgICBjb3Vyc2UuaWQgPT09IHNlbGVjdGVkQ291cnNlSWQgPyB7IC4uLmNvdXJzZSwgY292ZXJJbWFnZTogaW1hZ2VVcmwgfSA6IGNvdXJzZVxuICAgICAgICAgICkpO1xuICAgICAgICB9XG5cbiAgICAgICAgYWxlcnQoJ+ivvueoi+WwgemdouS4iuS8oOaIkOWKn++8gScpO1xuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcign4p2MIOivvueoi+WwgemdouS4iuS8oOWksei0pTonLCBlcnJvcik7XG4gICAgICAgIG5vdGlmaWNhdGlvbi5lcnJvcign6K++56iL5bCB6Z2i5LiK5Lyg5aSx6LSl77yM6K+36YeN6K+VJyk7XG5cbiAgICAgICAgLy8g5LiK5Lyg5aSx6LSl5pe25riF6Zmk6aKE6KeI5Zu+54mHXG4gICAgICAgIHNldENvdXJzZURldGFpbChwcmV2ID0+ICh7IC4uLnByZXYsIGNvdmVySW1hZ2U6ICcnIH0pKTtcbiAgICAgIH0gZmluYWxseSB7XG4gICAgICAgIC8vIOS7juS4iuS8oOS4reeahOaWh+S7tuWIl+ihqOenu+mZpFxuICAgICAgICBzZXRVcGxvYWRpbmdGaWxlcyhwcmV2ID0+IHtcbiAgICAgICAgICBjb25zdCBuZXdTZXQgPSBuZXcgU2V0KHByZXYpO1xuICAgICAgICAgIG5ld1NldC5kZWxldGUoJ2NvdmVyJyk7XG4gICAgICAgICAgcmV0dXJuIG5ld1NldDtcbiAgICAgICAgfSk7XG4gICAgICB9XG4gICAgfVxuICB9O1xuXG4gIC8vIOWkhOeQhuinhumikeS4iuS8oFxuICBjb25zdCBoYW5kbGVWaWRlb1VwbG9hZCA9IGFzeW5jIChldmVudDogUmVhY3QuQ2hhbmdlRXZlbnQ8SFRNTElucHV0RWxlbWVudD4pID0+IHtcbiAgICBjb25zdCBmaWxlID0gZXZlbnQudGFyZ2V0LmZpbGVzPy5bMF07XG4gICAgaWYgKGZpbGUpIHtcbiAgICAgIC8vIOajgOafpeaWh+S7tuexu+Wei1xuICAgICAgY29uc3QgYWxsb3dlZFR5cGVzID0gWyd2aWRlby9tcDQnLCAndmlkZW8vYXZpJywgJ3ZpZGVvL21vdicsICd2aWRlby93bXYnLCAndmlkZW8vZmx2J107XG4gICAgICBpZiAoIWFsbG93ZWRUeXBlcy5pbmNsdWRlcyhmaWxlLnR5cGUpKSB7XG4gICAgICAgIGFsZXJ0KCfor7fpgInmi6kgTVA044CBQVZJ44CBTU9W44CBV01WIOaIliBGTFYg5qC85byP55qE6KeG6aKR5paH5Lu2Jyk7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cblxuICAgICAgLy8g5qOA5p+l5paH5Lu25aSn5bCPICgxMDBNQilcbiAgICAgIGlmIChmaWxlLnNpemUgPiAxMDAgKiAxMDI0ICogMTAyNCkge1xuICAgICAgICBhbGVydCgn6KeG6aKR5paH5Lu25aSn5bCP5LiN6IO96LaF6L+HIDEwME1CJyk7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cblxuICAgICAgdHJ5IHtcbiAgICAgICAgY29uc29sZS5sb2coJ/Cfk6Qg5byA5aeL5LiK5Lyg6K++56iL6KeG6aKROicsIGZpbGUubmFtZSk7XG5cbiAgICAgICAgLy8g5re75Yqg5Yiw5LiK5Lyg5Lit55qE5paH5Lu25YiX6KGoXG4gICAgICAgIHNldFVwbG9hZGluZ0ZpbGVzKHByZXYgPT4gbmV3IFNldChwcmV2KS5hZGQoJ3ZpZGVvJykpO1xuXG4gICAgICAgIC8vIOWFiOaYvuekuumihOiniOinhumikVxuICAgICAgICBjb25zdCBwcmV2aWV3VXJsID0gVVJMLmNyZWF0ZU9iamVjdFVSTChmaWxlKTtcbiAgICAgICAgc2V0Q291cnNlRGV0YWlsKHByZXYgPT4gKHtcbiAgICAgICAgICAuLi5wcmV2LFxuICAgICAgICAgIGNvbnRlbnRDb25maWc6IHtcbiAgICAgICAgICAgIC4uLnByZXYuY29udGVudENvbmZpZyxcbiAgICAgICAgICAgIHZpZGVvOiB7XG4gICAgICAgICAgICAgIHVybDogcHJldmlld1VybCxcbiAgICAgICAgICAgICAgbmFtZTogZmlsZS5uYW1lXG4gICAgICAgICAgICB9XG4gICAgICAgICAgfVxuICAgICAgICB9KSk7XG5cbiAgICAgICAgLy8g5LiK5Lyg5YiwT1NTXG4gICAgICAgIGNvbnN0IHZpZGVvVXJsID0gYXdhaXQgdXBsb2FkQXBpLnVwbG9hZFRvT3NzKGZpbGUpO1xuICAgICAgICBjb25zb2xlLmxvZygn4pyFIOivvueoi+inhumikeS4iuS8oOaIkOWKnzonLCB2aWRlb1VybCk7XG5cbiAgICAgICAgLy8g5pu05paw6K++56iL6K+m5oOF5Lit55qE6KeG6aKR5L+h5oGv5Li655yf5a6eVVJMXG4gICAgICAgIHNldENvdXJzZURldGFpbChwcmV2ID0+ICh7XG4gICAgICAgICAgLi4ucHJldixcbiAgICAgICAgICBjb250ZW50Q29uZmlnOiB7XG4gICAgICAgICAgICAuLi5wcmV2LmNvbnRlbnRDb25maWcsXG4gICAgICAgICAgICB2aWRlbzoge1xuICAgICAgICAgICAgICB1cmw6IHZpZGVvVXJsLFxuICAgICAgICAgICAgICBuYW1lOiBmaWxlLm5hbWVcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG4gICAgICAgIH0pKTtcblxuICAgICAgICAvLyDlkIzml7bmm7TmlrDor77nqIvliJfooajkuK3nmoTop4bpopHkv6Hmga9cbiAgICAgICAgaWYgKHNlbGVjdGVkQ291cnNlSWQpIHtcbiAgICAgICAgICBzZXRDb3Vyc2VMaXN0KHByZXYgPT4gcHJldi5tYXAoY291cnNlID0+XG4gICAgICAgICAgICBjb3Vyc2UuaWQgPT09IHNlbGVjdGVkQ291cnNlSWQgPyB7XG4gICAgICAgICAgICAgIC4uLmNvdXJzZSxcbiAgICAgICAgICAgICAgY29udGVudENvbmZpZzoge1xuICAgICAgICAgICAgICAgIC4uLmNvdXJzZS5jb250ZW50Q29uZmlnLFxuICAgICAgICAgICAgICAgIHZpZGVvOiB7XG4gICAgICAgICAgICAgICAgICB1cmw6IHZpZGVvVXJsLFxuICAgICAgICAgICAgICAgICAgbmFtZTogZmlsZS5uYW1lXG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9IDogY291cnNlXG4gICAgICAgICAgKSk7XG4gICAgICAgIH1cblxuICAgICAgICBhbGVydCgn6K++56iL6KeG6aKR5LiK5Lyg5oiQ5Yqf77yBJyk7XG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCfinYwg6K++56iL6KeG6aKR5LiK5Lyg5aSx6LSlOicsIGVycm9yKTtcbiAgICAgICAgYWxlcnQoJ+ivvueoi+inhumikeS4iuS8oOWksei0pe+8jOivt+mHjeivlScpO1xuXG4gICAgICAgIC8vIOS4iuS8oOWksei0peaXtua4hemZpOinhumikeS/oeaBr1xuICAgICAgICBzZXRDb3Vyc2VEZXRhaWwocHJldiA9PiAoe1xuICAgICAgICAgIC4uLnByZXYsXG4gICAgICAgICAgY29udGVudENvbmZpZzoge1xuICAgICAgICAgICAgLi4ucHJldi5jb250ZW50Q29uZmlnLFxuICAgICAgICAgICAgdmlkZW86IHtcbiAgICAgICAgICAgICAgdXJsOiAnJyxcbiAgICAgICAgICAgICAgbmFtZTogJydcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG4gICAgICAgIH0pKTtcbiAgICAgIH0gZmluYWxseSB7XG4gICAgICAgIC8vIOS7juS4iuS8oOS4reeahOaWh+S7tuWIl+ihqOenu+mZpFxuICAgICAgICBzZXRVcGxvYWRpbmdGaWxlcyhwcmV2ID0+IHtcbiAgICAgICAgICBjb25zdCBuZXdTZXQgPSBuZXcgU2V0KHByZXYpO1xuICAgICAgICAgIG5ld1NldC5kZWxldGUoJ3ZpZGVvJyk7XG4gICAgICAgICAgcmV0dXJuIG5ld1NldDtcbiAgICAgICAgfSk7XG4gICAgICB9XG4gICAgfVxuICB9O1xuXG4gIC8vIOinpuWPkeinhumikeaWh+S7tumAieaLqVxuICBjb25zdCB0cmlnZ2VyVmlkZW9VcGxvYWQgPSAoKSA9PiB7XG4gICAgY29uc3QgaW5wdXQgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCdpbnB1dCcpO1xuICAgIGlucHV0LnR5cGUgPSAnZmlsZSc7XG4gICAgaW5wdXQuYWNjZXB0ID0gJ3ZpZGVvL21wNCx2aWRlby9hdmksdmlkZW8vbW92LHZpZGVvL3dtdix2aWRlby9mbHYnO1xuICAgIGlucHV0Lm9uY2hhbmdlID0gKGUpID0+IGhhbmRsZVZpZGVvVXBsb2FkKGUgYXMgYW55KTtcbiAgICBpbnB1dC5jbGljaygpO1xuICB9O1xuXG4gIC8vIOWkhOeQhumZhOS7tuS4iuS8oFxuICBjb25zdCBoYW5kbGVBdHRhY2htZW50VXBsb2FkID0gYXN5bmMgKGV2ZW50OiBSZWFjdC5DaGFuZ2VFdmVudDxIVE1MSW5wdXRFbGVtZW50PikgPT4ge1xuICAgIGNvbnN0IGZpbGUgPSBldmVudC50YXJnZXQuZmlsZXM/LlswXTtcbiAgICBpZiAoZmlsZSkge1xuICAgICAgLy8g5qOA5p+l5paH5Lu257G75Z6LXG4gICAgICBjb25zdCBhbGxvd2VkVHlwZXMgPSBbXG4gICAgICAgICdhcHBsaWNhdGlvbi9wZGYnLFxuICAgICAgICAnYXBwbGljYXRpb24vbXN3b3JkJyxcbiAgICAgICAgJ2FwcGxpY2F0aW9uL3ZuZC5vcGVueG1sZm9ybWF0cy1vZmZpY2Vkb2N1bWVudC53b3JkcHJvY2Vzc2luZ21sLmRvY3VtZW50JyxcbiAgICAgICAgJ2FwcGxpY2F0aW9uL3ZuZC5tcy1leGNlbCcsXG4gICAgICAgICdhcHBsaWNhdGlvbi92bmQub3BlbnhtbGZvcm1hdHMtb2ZmaWNlZG9jdW1lbnQuc3ByZWFkc2hlZXRtbC5zaGVldCcsXG4gICAgICAgICdhcHBsaWNhdGlvbi92bmQubXMtcG93ZXJwb2ludCcsXG4gICAgICAgICdhcHBsaWNhdGlvbi92bmQub3BlbnhtbGZvcm1hdHMtb2ZmaWNlZG9jdW1lbnQucHJlc2VudGF0aW9ubWwucHJlc2VudGF0aW9uJyxcbiAgICAgICAgJ3RleHQvcGxhaW4nLFxuICAgICAgICAnaW1hZ2UvanBlZycsXG4gICAgICAgICdpbWFnZS9wbmcnLFxuICAgICAgICAnaW1hZ2UvZ2lmJ1xuICAgICAgXTtcblxuICAgICAgaWYgKCFhbGxvd2VkVHlwZXMuaW5jbHVkZXMoZmlsZS50eXBlKSkge1xuICAgICAgICBhbGVydCgn6K+36YCJ5oup5pSv5oyB55qE5paH5Lu25qC85byP77yaUERG44CBRE9D44CBRE9DWOOAgVhMU+OAgVhMU1jjgIFQUFTjgIFQUFRY44CBVFhU44CBSlBH44CBUE5H44CBR0lGJyk7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cblxuICAgICAgLy8g5qOA5p+l5paH5Lu25aSn5bCPICgxME1CKVxuICAgICAgaWYgKGZpbGUuc2l6ZSA+IDEwICogMTAyNCAqIDEwMjQpIHtcbiAgICAgICAgYWxlcnQoJ+aWh+S7tuWkp+Wwj+S4jeiDvei2hei/hyAxME1CJyk7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cblxuICAgICAgdHJ5IHtcbiAgICAgICAgY29uc29sZS5sb2coJ/Cfk6Qg5byA5aeL5LiK5Lyg6K++56iL6ZmE5Lu2OicsIGZpbGUubmFtZSk7XG5cbiAgICAgICAgLy8g5re75Yqg5Yiw5LiK5Lyg5Lit55qE5paH5Lu25YiX6KGoXG4gICAgICAgIHNldFVwbG9hZGluZ0ZpbGVzKHByZXYgPT4gbmV3IFNldChwcmV2KS5hZGQoJ2RvY3VtZW50JykpO1xuXG4gICAgICAgIC8vIOS4iuS8oOWIsE9TU1xuICAgICAgICBjb25zdCBkb2N1bWVudFVybCA9IGF3YWl0IHVwbG9hZEFwaS51cGxvYWRUb09zcyhmaWxlKTtcbiAgICAgICAgY29uc29sZS5sb2coJ+KchSDor77nqIvpmYTku7bkuIrkvKDmiJDlip86JywgZG9jdW1lbnRVcmwpO1xuXG4gICAgICAgIC8vIOabtOaWsOivvueoi+ivpuaDheS4reeahOmZhOS7tuS/oeaBr1xuICAgICAgICBzZXRDb3Vyc2VEZXRhaWwocHJldiA9PiAoe1xuICAgICAgICAgIC4uLnByZXYsXG4gICAgICAgICAgY29udGVudENvbmZpZzoge1xuICAgICAgICAgICAgLi4ucHJldi5jb250ZW50Q29uZmlnLFxuICAgICAgICAgICAgaGFzRG9jdW1lbnQ6IDEsXG4gICAgICAgICAgICBkb2N1bWVudDoge1xuICAgICAgICAgICAgICB1cmw6IGRvY3VtZW50VXJsLFxuICAgICAgICAgICAgICBuYW1lOiBmaWxlLm5hbWVcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG4gICAgICAgIH0pKTtcblxuICAgICAgICBhbGVydCgn6K++56iL6ZmE5Lu25LiK5Lyg5oiQ5Yqf77yBJyk7XG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCfinYwg6K++56iL6ZmE5Lu25LiK5Lyg5aSx6LSlOicsIGVycm9yKTtcbiAgICAgICAgYWxlcnQoJ+ivvueoi+mZhOS7tuS4iuS8oOWksei0pe+8jOivt+mHjeivlScpO1xuICAgICAgfSBmaW5hbGx5IHtcbiAgICAgICAgLy8g5LuO5LiK5Lyg5Lit55qE5paH5Lu25YiX6KGo56e76ZmkXG4gICAgICAgIHNldFVwbG9hZGluZ0ZpbGVzKHByZXYgPT4ge1xuICAgICAgICAgIGNvbnN0IG5ld1NldCA9IG5ldyBTZXQocHJldik7XG4gICAgICAgICAgbmV3U2V0LmRlbGV0ZSgnZG9jdW1lbnQnKTtcbiAgICAgICAgICByZXR1cm4gbmV3U2V0O1xuICAgICAgICB9KTtcbiAgICAgIH1cbiAgICB9XG4gIH07XG5cbiAgLy8g6Kem5Y+R6ZmE5Lu25paH5Lu26YCJ5oupXG4gIGNvbnN0IHRyaWdnZXJBdHRhY2htZW50VXBsb2FkID0gKCkgPT4ge1xuICAgIGNvbnN0IGlucHV0ID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnaW5wdXQnKTtcbiAgICBpbnB1dC50eXBlID0gJ2ZpbGUnO1xuICAgIGlucHV0LmFjY2VwdCA9ICcucGRmLC5kb2MsLmRvY3gsLnhscywueGxzeCwucHB0LC5wcHR4LC50eHQsLmpwZywucG5nLC5naWYnO1xuICAgIGlucHV0Lm9uY2hhbmdlID0gKGUpID0+IGhhbmRsZUF0dGFjaG1lbnRVcGxvYWQoZSBhcyBhbnkpO1xuICAgIGlucHV0LmNsaWNrKCk7XG4gIH07XG5cbiAgLy8g5aSE55CG5pWZ5a2m6ZmE5Lu25LiK5LygXG4gIGNvbnN0IGhhbmRsZVRlYWNoaW5nTWF0ZXJpYWxVcGxvYWQgPSBhc3luYyAoZXZlbnQ6IFJlYWN0LkNoYW5nZUV2ZW50PEhUTUxJbnB1dEVsZW1lbnQ+KSA9PiB7XG4gICAgY29uc3QgZmlsZSA9IGV2ZW50LnRhcmdldC5maWxlcz8uWzBdO1xuICAgIGlmIChmaWxlKSB7XG4gICAgICAvLyDmo4Dmn6Xmlofku7bnsbvlnotcbiAgICAgIGNvbnN0IGFsbG93ZWRUeXBlcyA9IFtcbiAgICAgICAgJ2FwcGxpY2F0aW9uL3BkZicsXG4gICAgICAgICdhcHBsaWNhdGlvbi9tc3dvcmQnLFxuICAgICAgICAnYXBwbGljYXRpb24vdm5kLm9wZW54bWxmb3JtYXRzLW9mZmljZWRvY3VtZW50LndvcmRwcm9jZXNzaW5nbWwuZG9jdW1lbnQnLFxuICAgICAgICAnYXBwbGljYXRpb24vdm5kLm1zLWV4Y2VsJyxcbiAgICAgICAgJ2FwcGxpY2F0aW9uL3ZuZC5vcGVueG1sZm9ybWF0cy1vZmZpY2Vkb2N1bWVudC5zcHJlYWRzaGVldG1sLnNoZWV0JyxcbiAgICAgICAgJ2FwcGxpY2F0aW9uL3ZuZC5tcy1wb3dlcnBvaW50JyxcbiAgICAgICAgJ2FwcGxpY2F0aW9uL3ZuZC5vcGVueG1sZm9ybWF0cy1vZmZpY2Vkb2N1bWVudC5wcmVzZW50YXRpb25tbC5wcmVzZW50YXRpb24nLFxuICAgICAgICAndGV4dC9wbGFpbicsXG4gICAgICAgICdpbWFnZS9qcGVnJyxcbiAgICAgICAgJ2ltYWdlL3BuZycsXG4gICAgICAgICdpbWFnZS9naWYnXG4gICAgICBdO1xuXG4gICAgICBpZiAoIWFsbG93ZWRUeXBlcy5pbmNsdWRlcyhmaWxlLnR5cGUpKSB7XG4gICAgICAgIGFsZXJ0KCfor7fpgInmi6nmlK/mjIHnmoTmlofku7bmoLzlvI/vvJpQREbjgIFET0PjgIFET0NY44CBWExT44CBWExTWOOAgVBQVOOAgVBQVFjjgIFUWFTjgIFKUEfjgIFQTkfjgIFHSUYnKTtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuXG4gICAgICAvLyDmo4Dmn6Xmlofku7blpKflsI8gKDEwTUIpXG4gICAgICBpZiAoZmlsZS5zaXplID4gMTAgKiAxMDI0ICogMTAyNCkge1xuICAgICAgICBhbGVydCgn5paH5Lu25aSn5bCP5LiN6IO96LaF6L+HIDEwTUInKTtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuXG4gICAgICB0cnkge1xuICAgICAgICBjb25zb2xlLmxvZygn8J+TpCDlvIDlp4vkuIrkvKDmlZnlrabmnZDmlpk6JywgZmlsZS5uYW1lKTtcblxuICAgICAgICAvLyDmt7vliqDliLDkuIrkvKDkuK3nmoTmlofku7bliJfooahcbiAgICAgICAgc2V0VXBsb2FkaW5nRmlsZXMocHJldiA9PiBuZXcgU2V0KHByZXYpLmFkZChgdGVhY2hpbmctJHtEYXRlLm5vdygpfWApKTtcblxuICAgICAgICAvLyDkuIrkvKDliLBPU1NcbiAgICAgICAgY29uc3QgbWF0ZXJpYWxVcmwgPSBhd2FpdCB1cGxvYWRBcGkudXBsb2FkVG9Pc3MoZmlsZSk7XG4gICAgICAgIGNvbnNvbGUubG9nKCfinIUg5pWZ5a2m5p2Q5paZ5LiK5Lyg5oiQ5YqfOicsIG1hdGVyaWFsVXJsKTtcblxuICAgICAgICAvLyDmt7vliqDliLDmlZnlrabpmYTku7bliJfooahcbiAgICAgICAgY29uc3QgbmV3TWF0ZXJpYWwgPSB7XG4gICAgICAgICAgdHlwZTogZmlsZS50eXBlLFxuICAgICAgICAgIG5hbWU6IGZpbGUubmFtZSxcbiAgICAgICAgICB1cmw6IG1hdGVyaWFsVXJsXG4gICAgICAgIH07XG5cbiAgICAgICAgc2V0Q291cnNlRGV0YWlsKHByZXYgPT4gKHtcbiAgICAgICAgICAuLi5wcmV2LFxuICAgICAgICAgIHRlYWNoaW5nTWF0ZXJpYWxzOiBbLi4ucHJldi50ZWFjaGluZ01hdGVyaWFscywgbmV3TWF0ZXJpYWxdXG4gICAgICAgIH0pKTtcblxuICAgICAgICBhbGVydCgn5pWZ5a2m5p2Q5paZ5LiK5Lyg5oiQ5Yqf77yBJyk7XG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCfinYwg5pWZ5a2m5p2Q5paZ5LiK5Lyg5aSx6LSlOicsIGVycm9yKTtcbiAgICAgICAgYWxlcnQoJ+aVmeWtpuadkOaWmeS4iuS8oOWksei0pe+8jOivt+mHjeivlScpO1xuICAgICAgfSBmaW5hbGx5IHtcbiAgICAgICAgLy8g5LuO5LiK5Lyg5Lit55qE5paH5Lu25YiX6KGo56e76ZmkXG4gICAgICAgIHNldFVwbG9hZGluZ0ZpbGVzKHByZXYgPT4ge1xuICAgICAgICAgIGNvbnN0IG5ld1NldCA9IG5ldyBTZXQocHJldik7XG4gICAgICAgICAgLy8g56e76Zmk5omA5pyJdGVhY2hpbmct5byA5aS055qE6aG555uuXG4gICAgICAgICAgQXJyYXkuZnJvbShuZXdTZXQpLmZvckVhY2goaXRlbSA9PiB7XG4gICAgICAgICAgICBpZiAoaXRlbS5zdGFydHNXaXRoKCd0ZWFjaGluZy0nKSkge1xuICAgICAgICAgICAgICBuZXdTZXQuZGVsZXRlKGl0ZW0pO1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH0pO1xuICAgICAgICAgIHJldHVybiBuZXdTZXQ7XG4gICAgICAgIH0pO1xuICAgICAgfVxuICAgIH1cbiAgfTtcblxuICAvLyDop6blj5HmlZnlrabpmYTku7bmlofku7bpgInmi6lcbiAgY29uc3QgdHJpZ2dlclRlYWNoaW5nTWF0ZXJpYWxVcGxvYWQgPSAoKSA9PiB7XG4gICAgY29uc3QgaW5wdXQgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCdpbnB1dCcpO1xuICAgIGlucHV0LnR5cGUgPSAnZmlsZSc7XG4gICAgaW5wdXQuYWNjZXB0ID0gJy5wZGYsLmRvYywuZG9jeCwueGxzLC54bHN4LC5wcHQsLnBwdHgsLnR4dCwuanBnLC5wbmcsLmdpZic7XG4gICAgaW5wdXQub25jaGFuZ2UgPSAoZSkgPT4gaGFuZGxlVGVhY2hpbmdNYXRlcmlhbFVwbG9hZChlIGFzIGFueSk7XG4gICAgaW5wdXQuY2xpY2soKTtcbiAgfTtcblxuICAvLyDliKDpmaTmlZnlrabpmYTku7ZcbiAgY29uc3QgcmVtb3ZlVGVhY2hpbmdNYXRlcmlhbCA9IChpbmRleDogbnVtYmVyKSA9PiB7XG4gICAgc2V0Q291cnNlRGV0YWlsKHByZXYgPT4gKHtcbiAgICAgIC4uLnByZXYsXG4gICAgICB0ZWFjaGluZ01hdGVyaWFsczogcHJldi50ZWFjaGluZ01hdGVyaWFscy5maWx0ZXIoKF8sIGkpID0+IGkgIT09IGluZGV4KVxuICAgIH0pKTtcbiAgfTtcblxuICAvLyDor77nqIvlhoXlrrnnrqHnkIblh73mlbBcbiAgY29uc3QgYWRkVGVhY2hpbmdJbmZvSXRlbSA9ICgpID0+IHtcbiAgICBzZXRDb3Vyc2VEZXRhaWwocHJldiA9PiAoe1xuICAgICAgLi4ucHJldixcbiAgICAgIHRlYWNoaW5nSW5mbzogWy4uLnByZXYudGVhY2hpbmdJbmZvLCB7IHRpdGxlOiAnJywgY29udGVudDogJycgfV1cbiAgICB9KSk7XG4gIH07XG5cbiAgY29uc3QgcmVtb3ZlVGVhY2hpbmdJbmZvSXRlbSA9IChpbmRleDogbnVtYmVyKSA9PiB7XG4gICAgc2V0Q291cnNlRGV0YWlsKHByZXYgPT4gKHtcbiAgICAgIC4uLnByZXYsXG4gICAgICB0ZWFjaGluZ0luZm86IHByZXYudGVhY2hpbmdJbmZvLmZpbHRlcigoXywgaSkgPT4gaSAhPT0gaW5kZXgpXG4gICAgfSkpO1xuICB9O1xuXG4gIGNvbnN0IHVwZGF0ZVRlYWNoaW5nSW5mb1RpdGxlID0gKGluZGV4OiBudW1iZXIsIHRpdGxlOiBzdHJpbmcpID0+IHtcbiAgICBzZXRDb3Vyc2VEZXRhaWwocHJldiA9PiB7XG4gICAgICBjb25zdCBuZXdUZWFjaGluZ0luZm8gPSBbLi4ucHJldi50ZWFjaGluZ0luZm9dO1xuICAgICAgbmV3VGVhY2hpbmdJbmZvW2luZGV4XSA9IHsgLi4ubmV3VGVhY2hpbmdJbmZvW2luZGV4XSwgdGl0bGUgfTtcbiAgICAgIHJldHVybiB7IC4uLnByZXYsIHRlYWNoaW5nSW5mbzogbmV3VGVhY2hpbmdJbmZvIH07XG4gICAgfSk7XG4gIH07XG5cbiAgY29uc3QgdXBkYXRlVGVhY2hpbmdJbmZvQ29udGVudCA9IChpbmRleDogbnVtYmVyLCBjb250ZW50OiBzdHJpbmcpID0+IHtcbiAgICBzZXRDb3Vyc2VEZXRhaWwocHJldiA9PiB7XG4gICAgICBjb25zdCBuZXdUZWFjaGluZ0luZm8gPSBbLi4ucHJldi50ZWFjaGluZ0luZm9dO1xuICAgICAgbmV3VGVhY2hpbmdJbmZvW2luZGV4XSA9IHsgLi4ubmV3VGVhY2hpbmdJbmZvW2luZGV4XSwgY29udGVudCB9O1xuICAgICAgcmV0dXJuIHsgLi4ucHJldiwgdGVhY2hpbmdJbmZvOiBuZXdUZWFjaGluZ0luZm8gfTtcbiAgICB9KTtcbiAgfTtcblxuICAvLyDlsIZVSeagvOW8j+eahHRlYWNoaW5nSW5mb+i9rOaNouS4ukFQSeagvOW8j1xuICBjb25zdCBjb252ZXJ0VGVhY2hpbmdJbmZvRm9yQVBJID0gKHRlYWNoaW5nSW5mbzogeyB0aXRsZTogc3RyaW5nOyBjb250ZW50OiBzdHJpbmcgfVtdKSA9PiB7XG4gICAgcmV0dXJuIHRlYWNoaW5nSW5mby5tYXAoaW5mbyA9PiAoe1xuICAgICAgdGl0bGU6IGluZm8udGl0bGUsXG4gICAgICBjb250ZW50OiBpbmZvLmNvbnRlbnQuc3BsaXQoJ1xcbicpLmZpbHRlcihsaW5lID0+IGxpbmUudHJpbSgpKSAvLyDmjInmjaLooYzliIblibLlubbov4fmu6TnqbrooYxcbiAgICB9KSk7XG4gIH07XG5cbiAgLy8g5qOA5p+l5piv5ZCm5pyJ5pyq5LiK5Lyg5a6M5oiQ55qE5paH5Lu277yIYmxvYiBVUkzvvIlcbiAgY29uc3QgY2hlY2tGb3JCbG9iVXJscyA9IChjb3Vyc2U6IENvdXJzZUl0ZW0pID0+IHtcbiAgICBjb25zdCBpc3N1ZXMgPSBbXTtcblxuICAgIC8vIOajgOafpeWwgemdouWbvueJh1xuICAgIGlmIChjb3Vyc2UuY292ZXJJbWFnZSAmJiBjb3Vyc2UuY292ZXJJbWFnZS5zdGFydHNXaXRoKCdibG9iOicpKSB7XG4gICAgICBpc3N1ZXMucHVzaCgn6K++56iL5bCB6Z2i5Zu+54mHJyk7XG4gICAgfVxuXG4gICAgLy8g5qOA5p+l6KeG6aKR5paH5Lu2XG4gICAgaWYgKGNvdXJzZS5jb250ZW50Q29uZmlnPy52aWRlbz8udXJsICYmIGNvdXJzZS5jb250ZW50Q29uZmlnLnZpZGVvLnVybC5zdGFydHNXaXRoKCdibG9iOicpKSB7XG4gICAgICBpc3N1ZXMucHVzaCgn6K++56iL6KeG6aKRJyk7XG4gICAgfVxuXG4gICAgLy8g5qOA5p+l5paH5qGj6ZmE5Lu2XG4gICAgaWYgKGNvdXJzZS5jb250ZW50Q29uZmlnPy5kb2N1bWVudD8udXJsICYmIGNvdXJzZS5jb250ZW50Q29uZmlnLmRvY3VtZW50LnVybC5zdGFydHNXaXRoKCdibG9iOicpKSB7XG4gICAgICBpc3N1ZXMucHVzaCgn6K++56iL5paH5qGjJyk7XG4gICAgfVxuXG4gICAgLy8g5qOA5p+l5pWZ5a2m5p2Q5paZXG4gICAgaWYgKGNvdXJzZS5hZGRpdGlvbmFsUmVzb3VyY2VzKSB7XG4gICAgICBjb3Vyc2UuYWRkaXRpb25hbFJlc291cmNlcy5mb3JFYWNoKChyZXNvdXJjZSwgaW5kZXgpID0+IHtcbiAgICAgICAgaWYgKHJlc291cmNlLnVybCAmJiByZXNvdXJjZS51cmwuc3RhcnRzV2l0aCgnYmxvYjonKSkge1xuICAgICAgICAgIGlzc3Vlcy5wdXNoKGDmlZnlrabmnZDmlpkke2luZGV4ICsgMX1gKTtcbiAgICAgICAgfVxuICAgICAgfSk7XG4gICAgfVxuXG4gICAgcmV0dXJuIGlzc3VlcztcbiAgfTtcblxuICAvLyDkv53lrZjljZXkuKror77nqIvliLDlkI7nq69cbiAgY29uc3Qgc2F2ZUNvdXJzZSA9IGFzeW5jIChjb3Vyc2U6IENvdXJzZUl0ZW0pID0+IHtcbiAgICB0cnkge1xuICAgICAgLy8g6aqM6K+B5b+F6KaB55qE5pWw5o2uXG4gICAgICBpZiAoIWNvdXJzZS50aXRsZSB8fCBjb3Vyc2UudGl0bGUudHJpbSgpID09PSAnJykge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ+ivvueoi+agh+mimOS4jeiDveS4uuepuicpO1xuICAgICAgfVxuXG4gICAgICAvLyDmo4Dmn6XmmK/lkKbmnInmnKrkuIrkvKDlrozmiJDnmoTmlofku7ZcbiAgICAgIGNvbnN0IGJsb2JJc3N1ZXMgPSBjaGVja0ZvckJsb2JVcmxzKGNvdXJzZSk7XG4gICAgICBpZiAoYmxvYklzc3Vlcy5sZW5ndGggPiAwKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihg5Lul5LiL5paH5Lu25bCa5pyq5LiK5Lyg5a6M5oiQ77yM6K+3562J5b6F5LiK5Lyg5a6M5oiQ5ZCO5YaN5L+d5a2Y77yaJHtibG9iSXNzdWVzLmpvaW4oJ+OAgScpfWApO1xuICAgICAgfVxuXG4gICAgICAvLyDlh4blpIfor77nqIvmlbDmja5cbiAgICAgIGNvbnN0IGNvdXJzZURhdGEgPSB7XG4gICAgICAgIHNlcmllc0lkOiBzZXJpZXNJZCxcbiAgICAgICAgdGl0bGU6IGNvdXJzZS50aXRsZSxcbiAgICAgICAgZGVzY3JpcHRpb246IGNvdXJzZS5kZXNjcmlwdGlvbiB8fCAnJyxcbiAgICAgICAgY292ZXJJbWFnZTogY291cnNlLmNvdmVySW1hZ2UgfHwgJycsXG4gICAgICAgIGhhc1ZpZGVvOiBjb3Vyc2UuY29udGVudENvbmZpZz8udmlkZW8/LnVybCA/IDEgOiAwLFxuICAgICAgICBoYXNEb2N1bWVudDogY291cnNlLmNvbnRlbnRDb25maWc/LmRvY3VtZW50Py51cmwgPyAxIDogMCxcbiAgICAgICAgaGFzQXVkaW86IDAsXG4gICAgICAgIHZpZGVvRHVyYXRpb246IDAsXG4gICAgICAgIGNvbnRlbnRDb25maWc6IGNvdXJzZS5jb250ZW50Q29uZmlnIHx8IHt9LFxuICAgICAgICB0ZWFjaGluZ0luZm86IGNvbnZlcnRUZWFjaGluZ0luZm9Gb3JBUEkoY291cnNlLnRlYWNoaW5nSW5mbyB8fCBbXSksXG4gICAgICAgIGFkZGl0aW9uYWxSZXNvdXJjZXM6IGNvdXJzZS5hZGRpdGlvbmFsUmVzb3VyY2VzIHx8IFtdLFxuICAgICAgICBvcmRlckluZGV4OiBjb3Vyc2Uub3JkZXJJbmRleCB8fCAxXG4gICAgICB9O1xuXG4gICAgICBjb25zb2xlLmxvZygn8J+SviDlh4blpIfkv53lrZjor77nqIvmlbDmja46JywgY291cnNlRGF0YSk7XG5cbiAgICAgIGxldCByZXN1bHQ6IGFueTtcblxuICAgICAgLy8g5Yik5pat5piv5paw6K++56iL6L+Y5piv5pu05paw6K++56iLXG4gICAgICBpZiAoY291cnNlLmlkICYmIGNvdXJzZS5pZCA+IDEwMDAwMDApIHtcbiAgICAgICAgLy8g5paw6K++56iL77yM5L2/55So5Yib5bu6QVBJXG4gICAgICAgIGNvbnNvbGUubG9nKCfwn5OkIOWIm+W7uuaWsOivvueoiycpO1xuICAgICAgICBjb25zdCB7IGRhdGE6IHJlc3BvbnNlIH0gPSBhd2FpdCBjb3Vyc2VBcGkuY3JlYXRlQ291cnNlKGNvdXJzZURhdGEpO1xuICAgICAgICByZXN1bHQgPSByZXNwb25zZTtcbiAgICAgICAgY29uc29sZS5sb2coJ+KchSDor77nqIvliJvlu7rmiJDlip8nKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIC8vIOeOsOacieivvueoi++8jOS9v+eUqOabtOaWsEFQSVxuICAgICAgICBjb25zb2xlLmxvZygn8J+TpCDmm7TmlrDnjrDmnInor77nqIs6JywgY291cnNlLmlkKTtcbiAgICAgICAgY29uc3QgeyBkYXRhOiByZXNwb25zZSB9ID0gYXdhaXQgY291cnNlQXBpLnVwZGF0ZUNvdXJzZShjb3Vyc2UuaWQsIGNvdXJzZURhdGEpO1xuICAgICAgICByZXN1bHQgPSByZXNwb25zZTtcbiAgICAgICAgY29uc29sZS5sb2coJ+KchSDor77nqIvmm7TmlrDmiJDlip8nKTtcbiAgICAgIH1cblxuICAgICAgcmV0dXJuIHJlc3VsdDtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcign4p2MIOS/neWtmOivvueoi+Wksei0pTonLCBlcnJvcik7XG4gICAgICB0aHJvdyBlcnJvcjtcbiAgICB9XG4gIH07XG5cbiAgLy8g5Y+R5biD6YCJ5Lit55qE6K++56iLXG4gIGNvbnN0IGhhbmRsZVB1Ymxpc2hDb3Vyc2UgPSBhc3luYyAoKSA9PiB7XG4gICAgaWYgKCFzZWxlY3RlZENvdXJzZUlkKSB7XG4gICAgICBhbGVydCgn6K+35YWI6YCJ5oup6KaB5Y+R5biD55qE6K++56iLJyk7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgY29uc3Qgc2VsZWN0ZWRDb3Vyc2UgPSBjb3Vyc2VMaXN0LmZpbmQoY291cnNlID0+IGNvdXJzZS5pZCA9PT0gc2VsZWN0ZWRDb3Vyc2VJZCk7XG4gICAgaWYgKCFzZWxlY3RlZENvdXJzZSkge1xuICAgICAgYWxlcnQoJ+acquaJvuWIsOmAieS4reeahOivvueoiycpO1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIC8vIOajgOafpeivvueoi+aYr+WQpuW3sue7j+WPkeW4g1xuICAgIGlmIChzZWxlY3RlZENvdXJzZS5zdGF0dXMgPT09IDEpIHtcbiAgICAgIGFsZXJ0KCfor6Xor77nqIvlt7Lnu4/lj5HluIPvvIzml6DpnIDph43lpI3lj5HluIMnKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICB0cnkge1xuICAgICAgc2V0SXNQdWJsaXNoaW5nKHRydWUpO1xuICAgICAgY29uc29sZS5sb2coJ/Cfk6Qg5byA5aeL5Y+R5biD6K++56iLJyk7XG4gICAgICBjb25zb2xlLmxvZygn8J+TpCBzZWxlY3RlZENvdXJzZUlkOicsIHNlbGVjdGVkQ291cnNlSWQsICfnsbvlnos6JywgdHlwZW9mIHNlbGVjdGVkQ291cnNlSWQpO1xuICAgICAgY29uc29sZS5sb2coJ/Cfk6Qg6K++56iL5L+h5oGvOicsIHNlbGVjdGVkQ291cnNlKTtcbiAgICAgIGNvbnNvbGUubG9nKCfwn5OkIOivvueoi0lE5a2X5q61OicsIHNlbGVjdGVkQ291cnNlLmlkLCAn57G75Z6LOicsIHR5cGVvZiBzZWxlY3RlZENvdXJzZS5pZCk7XG4gICAgICBjb25zb2xlLmxvZygn8J+TpCDor77nqIvliJfooajkuK3nmoTmiYDmnIlJRDonLCBjb3Vyc2VMaXN0Lm1hcChjID0+ICh7IGlkOiBjLmlkLCB0eXBlOiB0eXBlb2YgYy5pZCwgdGl0bGU6IGMudGl0bGUgfSkpKTtcblxuICAgICAgLy8g56Gu5L+d5L2/55So5q2j56Gu55qE6K++56iLSURcbiAgICAgIGNvbnN0IGNvdXJzZUlkVG9QdWJsaXNoID0gc2VsZWN0ZWRDb3Vyc2UuaWQ7XG4gICAgICBjb25zb2xlLmxvZygn8J+TpCDljbPlsIblj5HluIPnmoTor77nqItJRDonLCBjb3Vyc2VJZFRvUHVibGlzaCwgJ+exu+WeizonLCB0eXBlb2YgY291cnNlSWRUb1B1Ymxpc2gpO1xuXG4gICAgICBjb25zdCB7IGRhdGE6IHJlc3BvbnNlIH0gPSBhd2FpdCBjb3Vyc2VBcGkucHVibGlzaENvdXJzZShjb3Vyc2VJZFRvUHVibGlzaCk7XG5cbiAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDApIHtcbiAgICAgICAgY29uc29sZS5sb2coJ+KchSDor77nqIvlj5HluIPmiJDlip86JywgcmVzcG9uc2UuZGF0YSk7XG4gICAgICAgIGFsZXJ0KCfor77nqIvlj5HluIPmiJDlip/vvIEnKTtcblxuICAgICAgICAvLyDliLfmlrDor77nqIvliJfooajku6Xmm7TmlrDnirbmgIFcbiAgICAgICAgYXdhaXQgbG9hZENvdXJzZUxpc3QoKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDlj5HluIPor77nqIvlpLHotKU6JywgcmVzcG9uc2UubWVzc2FnZSk7XG4gICAgICAgIGFsZXJ0KHJlc3BvbnNlLm1lc3NhZ2UgfHwgJ+WPkeW4g+ivvueoi+Wksei0pScpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDlj5HluIPor77nqIvlpLHotKU6JywgZXJyb3IpO1xuICAgICAgY29uc29sZS5lcnJvcign4p2MIOmUmeivr+ivpuaDhTonLCBlcnJvci5yZXNwb25zZT8uZGF0YSk7XG5cbiAgICAgIC8vIOWkhOeQhuWFt+S9k+eahOmUmeivr+S/oeaBr1xuICAgICAgaWYgKGVycm9yLnJlc3BvbnNlPy5kYXRhPy5tZXNzYWdlKSB7XG4gICAgICAgIGFsZXJ0KGVycm9yLnJlc3BvbnNlLmRhdGEubWVzc2FnZSk7XG4gICAgICB9IGVsc2UgaWYgKGVycm9yLm1lc3NhZ2UpIHtcbiAgICAgICAgYWxlcnQoZXJyb3IubWVzc2FnZSk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBhbGVydCgn5Y+R5biD6K++56iL5aSx6LSl77yM6K+36YeN6K+VJyk7XG4gICAgICB9XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldElzUHVibGlzaGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIC8vIOS/neWtmOivvueoi+WIl+ihqFxuICBjb25zdCBoYW5kbGVTYXZlID0gYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBzZXRJc0NyZWF0aW5nKHRydWUpO1xuICAgICAgLy8g5qOA5p+l5piv5ZCm5pyJ5paH5Lu25q2j5Zyo5LiK5LygXG4gICAgICBpZiAodXBsb2FkaW5nRmlsZXMuc2l6ZSA+IDApIHtcbiAgICAgICAgYWxlcnQoJ+acieaWh+S7tuato+WcqOS4iuS8oOS4re+8jOivt+etieW+heS4iuS8oOWujOaIkOWQjuWGjeS/neWtmCcpO1xuICAgICAgICBzZXRJc0NyZWF0aW5nKGZhbHNlKTtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuXG4gICAgICBjb25zb2xlLmxvZygn8J+SviDlvIDlp4vkv53lrZjor77nqIvliJfooagnKTtcblxuICAgICAgLy8g5aaC5p6c5pyJ6YCJ5Lit55qE6K++56iL5LiU5Zyo57yW6L6R54q25oCB77yM5YWI5L+d5a2Y5b2T5YmN6K++56iLXG4gICAgICBpZiAoc2VsZWN0ZWRDb3Vyc2VJZCAmJiByaWdodFBhbmVsVHlwZSA9PT0gJ2NvdXJzZScpIHtcbiAgICAgICAgY29uc3Qgc2VsZWN0ZWRDb3Vyc2UgPSBjb3Vyc2VMaXN0LmZpbmQoYyA9PiBjLmlkID09PSBzZWxlY3RlZENvdXJzZUlkKTtcbiAgICAgICAgaWYgKHNlbGVjdGVkQ291cnNlKSB7XG4gICAgICAgICAgLy8g5pu05paw6K++56iL5pWw5o2uXG4gICAgICAgICAgY29uc3QgdXBkYXRlZENvdXJzZSA9IHtcbiAgICAgICAgICAgIC4uLnNlbGVjdGVkQ291cnNlLFxuICAgICAgICAgICAgdGl0bGU6IGNvdXJzZURldGFpbC50aXRsZSxcbiAgICAgICAgICAgIGRlc2NyaXB0aW9uOiBjb3Vyc2VEZXRhaWwuZGVzY3JpcHRpb24sXG4gICAgICAgICAgICBjb3ZlckltYWdlOiBjb3Vyc2VEZXRhaWwuY292ZXJJbWFnZSxcbiAgICAgICAgICAgIGhhc1ZpZGVvOiBjb3Vyc2VEZXRhaWwuaXNWaWRlb0VuYWJsZWQgPyAxIDogMCxcbiAgICAgICAgICAgIGhhc0RvY3VtZW50OiBjb3Vyc2VEZXRhaWwuaXNBdHRhY2htZW50RW5hYmxlZCA/IDEgOiAwLFxuICAgICAgICAgICAgY29udGVudENvbmZpZzogY291cnNlRGV0YWlsLmNvbnRlbnRDb25maWcsXG4gICAgICAgICAgICB0ZWFjaGluZ0luZm86IGNvdXJzZURldGFpbC50ZWFjaGluZ0luZm8sXG4gICAgICAgICAgICBhZGRpdGlvbmFsUmVzb3VyY2VzOiBjb3Vyc2VEZXRhaWwudGVhY2hpbmdNYXRlcmlhbHM/Lm1hcChtYXRlcmlhbCA9PiAoe1xuICAgICAgICAgICAgICB0aXRsZTogbWF0ZXJpYWwubmFtZSxcbiAgICAgICAgICAgICAgdXJsOiBtYXRlcmlhbC51cmwsXG4gICAgICAgICAgICAgIGRlc2NyaXB0aW9uOiBtYXRlcmlhbC5uYW1lXG4gICAgICAgICAgICB9KSkgfHwgW11cbiAgICAgICAgICB9O1xuXG4gICAgICAgICAgLy8g5YWI5pu05paw6K++56iL5YiX6KGo5Lit55qE5pWw5o2uXG4gICAgICAgICAgc2V0Q291cnNlTGlzdChwcmV2ID0+IHByZXYubWFwKGNvdXJzZSA9PlxuICAgICAgICAgICAgY291cnNlLmlkID09PSBzZWxlY3RlZENvdXJzZUlkID8gdXBkYXRlZENvdXJzZSA6IGNvdXJzZVxuICAgICAgICAgICkpO1xuXG4gICAgICAgICAgLy8g54S25ZCO5L+d5a2Y5Yiw5ZCO56uvXG4gICAgICAgICAgYXdhaXQgc2F2ZUNvdXJzZSh1cGRhdGVkQ291cnNlKTtcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICAvLyDkv53lrZjns7vliJfor77nqIvkv6Hmga9cbiAgICAgIGNvbnN0IGRhdGEgPSB7XG4gICAgICAgIHRpdGxlOiBlZGl0aW5nVGl0bGUsXG4gICAgICAgIGNvdXJzZUdvYWxzLFxuICAgICAgICBjb3Vyc2VPYmplY3RpdmVzLFxuICAgICAgICBjb3Vyc2VMaXN0XG4gICAgICB9O1xuXG4gICAgICBvblNhdmUoZGF0YSk7XG4gICAgICBvbkNsb3NlKCk7XG5cbiAgICAgIGNvbnNvbGUubG9nKCfinIUg6K++56iL5YiX6KGo5L+d5a2Y5a6M5oiQJyk7XG4gICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgY29uc29sZS5lcnJvcign4p2MIOS/neWtmOWksei0pTonLCBlcnJvcik7XG4gICAgICBhbGVydChg5L+d5a2Y5aSx6LSlOiAke2Vycm9yLm1lc3NhZ2UgfHwgJ+ivt+mHjeivlSd9YCk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldElzQ3JlYXRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICAvLyDlj5HluIPns7vliJfor77nqItcbiAgY29uc3QgaGFuZGxlUHVibGlzaCA9IGFzeW5jICgpID0+IHtcbiAgICAvLyDlpoLmnpzns7vliJflt7Llj5HluIPvvIzkuI3miafooYzku7vkvZXmk43kvZxcbiAgICBpZiAoc2VyaWVzU3RhdHVzID09PSAxKSB7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgdHJ5IHtcbiAgICAgIHNldElzUHVibGlzaGluZ1Nlcmllcyh0cnVlKTtcblxuICAgICAgLy8g5qOA5p+l5piv5ZCm5pyJ6K++56iLXG4gICAgICBpZiAoY291cnNlTGlzdC5sZW5ndGggPT09IDApIHtcbiAgICAgICAgYWxlcnQoJ+WPkeW4g+Wksei0pe+8muivvueoi+ezu+WIl+S4reiHs+WwkemcgOimgeWMheWQq+S4gOS4quivvueoiycpO1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG5cbiAgICAgIGNvbnNvbGUubG9nKCfwn5OkIOW8gOWni+WPkeW4g+ezu+WIl+ivvueoi++8jOezu+WIl0lEOicsIHNlcmllc0lkKTtcblxuICAgICAgY29uc3QgeyBkYXRhOiByZXNwb25zZSB9ID0gYXdhaXQgY291cnNlQXBpLnB1Ymxpc2hDb3Vyc2VTZXJpZXMoc2VyaWVzSWQpO1xuXG4gICAgICBpZiAocmVzcG9uc2UuY29kZSA9PT0gMjAwKSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCfinIUg57O75YiX6K++56iL5Y+R5biD5oiQ5YqfOicsIHJlc3BvbnNlLmRhdGEpO1xuXG4gICAgICAgIC8vIOaehOW7uuaIkOWKn+a2iOaBr1xuICAgICAgICBjb25zdCBwdWJsaXNoRGF0YSA9IHJlc3BvbnNlLmRhdGE7XG4gICAgICAgIGxldCBzdWNjZXNzTWVzc2FnZSA9IGDns7vliJfor77nqItcIiR7cHVibGlzaERhdGEudGl0bGV9XCLlj5HluIPmiJDlip/vvIFgO1xuXG4gICAgICAgIC8vIOWmguaenOacieWPkeW4g+e7n+iuoeS/oeaBr++8jOa3u+WKoOWIsOa2iOaBr+S4rVxuICAgICAgICBpZiAocHVibGlzaERhdGEucHVibGlzaFN0YXRzKSB7XG4gICAgICAgICAgY29uc3Qgc3RhdHMgPSBwdWJsaXNoRGF0YS5wdWJsaXNoU3RhdHM7XG4gICAgICAgICAgc3VjY2Vzc01lc3NhZ2UgKz0gYFxcblxcbuWPkeW4g+e7n+iuoe+8mlxcbuKAoiDmgLvor77nqIvmlbDvvJoke3B1Ymxpc2hEYXRhLnRvdGFsQ291cnNlc31cXG7igKIg5bey5Y+R5biD6K++56iL77yaJHtwdWJsaXNoRGF0YS5wdWJsaXNoZWRDb3Vyc2VzfVxcbuKAoiDop4bpopHor77nqIvvvJoke3N0YXRzLnZpZGVvQ291cnNlQ291bnR95LiqXFxu4oCiIOaWh+aho+ivvueoi++8miR7c3RhdHMuZG9jdW1lbnRDb3Vyc2VDb3VudH3kuKpcXG7igKIg5oC76LWE5rqQ5pWw77yaJHtzdGF0cy50b3RhbFJlc291cmNlc0NvdW50feS4qmA7XG5cbiAgICAgICAgICBpZiAoc3RhdHMudG90YWxWaWRlb0R1cmF0aW9uID4gMCkge1xuICAgICAgICAgICAgY29uc3QgZHVyYXRpb25NaW51dGVzID0gTWF0aC5yb3VuZChzdGF0cy50b3RhbFZpZGVvRHVyYXRpb24gLyA2MCk7XG4gICAgICAgICAgICBzdWNjZXNzTWVzc2FnZSArPSBgXFxu4oCiIOinhumikeaAu+aXtumVv++8miR7ZHVyYXRpb25NaW51dGVzfeWIhumSn2A7XG4gICAgICAgICAgfVxuICAgICAgICB9XG5cbiAgICAgICAgYWxlcnQoc3VjY2Vzc01lc3NhZ2UpO1xuXG4gICAgICAgIC8vIOabtOaWsOezu+WIl+eKtuaAgeS4uuW3suWPkeW4g1xuICAgICAgICBzZXRTZXJpZXNTdGF0dXMoMSk7XG5cbiAgICAgICAgLy8g5Yi35paw6K++56iL5YiX6KGo5Lul5pu05paw54q25oCBXG4gICAgICAgIGF3YWl0IGxvYWRDb3Vyc2VMaXN0KCk7XG5cbiAgICAgICAgLy8g6YCa55+l54i257uE5Lu25Yi35paw5pWw5o2uXG4gICAgICAgIG9uU2F2ZSh7XG4gICAgICAgICAgdHlwZTogJ3B1Ymxpc2hfc2VyaWVzJyxcbiAgICAgICAgICBzZXJpZXNJZDogc2VyaWVzSWQsXG4gICAgICAgICAgbWVzc2FnZTogJ+ezu+WIl+ivvueoi+WPkeW4g+aIkOWKnydcbiAgICAgICAgfSk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBjb25zb2xlLmVycm9yKCfinYwg5Y+R5biD57O75YiX6K++56iL5aSx6LSlOicsIHJlc3BvbnNlLm1lc3NhZ2UpO1xuICAgICAgICBhbGVydChyZXNwb25zZS5tZXNzYWdlIHx8ICflj5HluIPns7vliJfor77nqIvlpLHotKUnKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfinYwg5Y+R5biD57O75YiX6K++56iL5Ye66ZSZOicsIGVycm9yKTtcblxuICAgICAgLy8g5aSE55CG5YW35L2T55qE6ZSZ6K+v5L+h5oGvXG4gICAgICBsZXQgZXJyb3JNZXNzYWdlID0gJ+WPkeW4g+ezu+WIl+ivvueoi+Wksei0pSc7XG4gICAgICBpZiAoZXJyb3IucmVzcG9uc2U/LmRhdGE/Lm1lc3NhZ2UpIHtcbiAgICAgICAgZXJyb3JNZXNzYWdlID0gZXJyb3IucmVzcG9uc2UuZGF0YS5tZXNzYWdlO1xuICAgICAgfSBlbHNlIGlmIChlcnJvci5tZXNzYWdlKSB7XG4gICAgICAgIGVycm9yTWVzc2FnZSA9IGVycm9yLm1lc3NhZ2U7XG4gICAgICB9XG5cbiAgICAgIGFsZXJ0KGVycm9yTWVzc2FnZSk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldElzUHVibGlzaGluZ1NlcmllcyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIC8vIOmAgOWHuue8lui+keaooeW8jyAtIOS/neWtmOaVsOaNruW5tuWFs+mXrVxuICBjb25zdCBoYW5kbGVFeGl0RWRpdCA9ICgpID0+IHtcbiAgICBoYW5kbGVTYXZlKCk7XG4gIH07XG5cbiAgLy8g5pi+56S66K6+572u6Z2i5p2/XG4gIGNvbnN0IHNob3dTZXR0aW5nc1BhbmVsID0gKCkgPT4ge1xuICAgIHNldFJpZ2h0UGFuZWxUeXBlKCdzZXR0aW5ncycpO1xuICAgIHNldFNlbGVjdGVkQ291cnNlSWQobnVsbCk7XG4gIH07XG5cbiAgLy8g5aSE55CG5qih5p2/6YCJ5oupXG4gIGNvbnN0IGhhbmRsZVRlbXBsYXRlU2VsZWN0ID0gKHRlbXBsYXRlOiBhbnkpID0+IHtcbiAgICBzZXRDb3Vyc2VEZXRhaWwocHJldiA9PiAoe1xuICAgICAgLi4ucHJldixcbiAgICAgIHNlbGVjdGVkVGVtcGxhdGU6IHRlbXBsYXRlLnRlbXBsYXRlTmFtZVxuICAgIH0pKTtcbiAgICBzZXRJc1RlbXBsYXRlUGlja2VyT3BlbihmYWxzZSk7XG4gIH07XG5cbiAgLy8g5omT5byA5qih5p2/6YCJ5oup5by556qXXG4gIGNvbnN0IGhhbmRsZU9wZW5UZW1wbGF0ZVBpY2tlciA9ICgpID0+IHtcbiAgICBzZXRJc1RlbXBsYXRlUGlja2VyT3Blbih0cnVlKTtcbiAgfTtcblxuICAvLyDmmL7npLror77nqIvnvJbovpHpnaLmnb9cbiAgY29uc3Qgc2hvd0NvdXJzZVBhbmVsID0gYXN5bmMgKGNvdXJzZUlkOiBudW1iZXIpID0+IHtcbiAgICBzZXRSaWdodFBhbmVsVHlwZSgnY291cnNlJyk7XG4gICAgc2V0U2VsZWN0ZWRDb3Vyc2VJZChjb3Vyc2VJZCk7XG5cbiAgICAvLyDlpoLmnpzkvZzlk4HmlbDmja7ov5jmsqHmnInliqDovb3vvIzph43mlrDliqDovb1cbiAgICBpZiAod29ya3MubGVuZ3RoID09PSAwICYmICFsb2FkaW5nV29ya3MpIHtcbiAgICAgIGxvYWRVc2VyV29ya3MoKTtcbiAgICB9XG5cbiAgICAvLyDojrflj5bpgInkuK3nmoTor77nqIvlubbmm7TmlrBjb3Vyc2VEZXRhaWznirbmgIFcbiAgICBjb25zdCBzZWxlY3RlZENvdXJzZSA9IGNvdXJzZUxpc3QuZmluZChjb3Vyc2UgPT4gY291cnNlLmlkID09PSBjb3Vyc2VJZCk7XG4gICAgY29uc29sZS5sb2coJ/Cfjq8g5om+5Yiw55qE6K++56iLOicsIHNlbGVjdGVkQ291cnNlKTtcblxuICAgIGlmIChzZWxlY3RlZENvdXJzZSkge1xuICAgICAgdHJ5IHtcbiAgICAgICAgY29uc29sZS5sb2coJ/CflIQg6I635Y+W6K++56iL6K+m5oOF77yMc2VyaWVzSWQ6Jywgc2VyaWVzSWQsICdjb3Vyc2VJZDonLCBjb3Vyc2VJZCk7XG5cbiAgICAgICAgLy8g6I635Y+W55yf5a6e55qE6K++56iL6K+m5oOF5pWw5o2uXG4gICAgICAgIGNvbnN0IHsgZGF0YTogcmVzIH0gPSBhd2FpdCBmZXRjaENvdXJzZURldGFpbChzZXJpZXNJZCwgY291cnNlSWQpO1xuXG4gICAgICAgIGlmIChyZXMuY29kZSA9PT0gMjAwICYmIHJlcy5kYXRhKSB7XG4gICAgICAgICAgY29uc3QgY291cnNlRGV0YWlsRGF0YSA9IHJlcy5kYXRhO1xuICAgICAgICAgIGNvbnNvbGUubG9nKCfinIUg6I635Y+W5Yiw6K++56iL6K+m5oOFOicsIGNvdXJzZURldGFpbERhdGEpO1xuXG4gICAgICAgICAgLy8g5bCG55yf5a6e55qEIGFkZGl0aW9uYWxSZXNvdXJjZXMg5pig5bCE5YiwIHRlYWNoaW5nTWF0ZXJpYWxzXG4gICAgICAgICAgY29uc3QgdGVhY2hpbmdNYXRlcmlhbHMgPSBjb3Vyc2VEZXRhaWxEYXRhLmFkZGl0aW9uYWxSZXNvdXJjZXM/Lm1hcCgocmVzb3VyY2U6IGFueSkgPT4gKHtcbiAgICAgICAgICAgIHR5cGU6ICdhcHBsaWNhdGlvbi9vY3RldC1zdHJlYW0nLCAvLyDpu5jorqTnsbvlnotcbiAgICAgICAgICAgIG5hbWU6IHJlc291cmNlLnRpdGxlIHx8IHJlc291cmNlLm5hbWUgfHwgJ+mZhOS7ticsXG4gICAgICAgICAgICB1cmw6IHJlc291cmNlLnVybFxuICAgICAgICAgIH0pKSB8fCBbXTtcblxuICAgICAgICAgIC8vIOWwhkFQSei/lOWbnueahHRlYWNoaW5nSW5mb+aVsOe7hOagvOW8j+i9rOaNouS4uuaWsOeahOagvOW8j1xuICAgICAgICAgIGNvbnN0IG1hcHBlZFRlYWNoaW5nSW5mbyA9IGNvdXJzZURldGFpbERhdGEudGVhY2hpbmdJbmZvPy5tYXAoKGluZm86IGFueSkgPT4gKHtcbiAgICAgICAgICAgIHRpdGxlOiBpbmZvLnRpdGxlIHx8ICcnLFxuICAgICAgICAgICAgY29udGVudDogQXJyYXkuaXNBcnJheShpbmZvLmNvbnRlbnQpID8gaW5mby5jb250ZW50LmpvaW4oJ1xcbicpIDogKGluZm8uY29udGVudCB8fCAnJylcbiAgICAgICAgICB9KSkgfHwgW107XG5cbiAgICAgICAgICBjb25zb2xlLmxvZygn8J+TjiDmmKDlsITnmoTmlZnlrabpmYTku7Y6JywgdGVhY2hpbmdNYXRlcmlhbHMpO1xuICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5OaIOaYoOWwhOeahOaVmeWtpuS/oeaBrzonLCBtYXBwZWRUZWFjaGluZ0luZm8pO1xuXG4gICAgICAgICAgc2V0Q291cnNlRGV0YWlsKHByZXYgPT4gKHtcbiAgICAgICAgICAgIC4uLnByZXYsXG4gICAgICAgICAgICB0aXRsZTogY291cnNlRGV0YWlsRGF0YS50aXRsZSxcbiAgICAgICAgICAgIGRlc2NyaXB0aW9uOiBjb3Vyc2VEZXRhaWxEYXRhLmRlc2NyaXB0aW9uLFxuICAgICAgICAgICAgY292ZXJJbWFnZTogY291cnNlRGV0YWlsRGF0YS5jb3ZlckltYWdlIHx8ICcnLFxuICAgICAgICAgICAgaXNWaWRlb0VuYWJsZWQ6IGNvdXJzZURldGFpbERhdGEuaGFzVmlkZW8gPT09IDEsXG4gICAgICAgICAgICBpc0F0dGFjaG1lbnRFbmFibGVkOiBjb3Vyc2VEZXRhaWxEYXRhLmhhc0RvY3VtZW50ID09PSAxLFxuICAgICAgICAgICAgY29udGVudENvbmZpZzogY291cnNlRGV0YWlsRGF0YS5jb250ZW50Q29uZmlnIHx8IHt9LFxuICAgICAgICAgICAgdGVhY2hpbmdJbmZvOiBtYXBwZWRUZWFjaGluZ0luZm8sIC8vIOS9v+eUqOaYoOWwhOWQjueahOaVmeWtpuS/oeaBr1xuICAgICAgICAgICAgdGVhY2hpbmdNYXRlcmlhbHM6IHRlYWNoaW5nTWF0ZXJpYWxzLCAvLyDkvb/nlKjnnJ/lrp7nmoTpmYTku7bmlbDmja5cbiAgICAgICAgICAgIHZpZGVvVXJsOiBjb3Vyc2VEZXRhaWxEYXRhLmNvbnRlbnRDb25maWc/LnZpZGVvPy51cmwgfHwgJycsXG4gICAgICAgICAgICB2aWRlb05hbWU6IGNvdXJzZURldGFpbERhdGEuY29udGVudENvbmZpZz8udmlkZW8/Lm5hbWUgfHwgJycsXG4gICAgICAgICAgICBhdHRhY2htZW50VXJsOiBjb3Vyc2VEZXRhaWxEYXRhLmNvbnRlbnRDb25maWc/LmRvY3VtZW50Py51cmwgfHwgJycsXG4gICAgICAgICAgICBhdHRhY2htZW50TmFtZTogY291cnNlRGV0YWlsRGF0YS5jb250ZW50Q29uZmlnPy5kb2N1bWVudD8ubmFtZSB8fCAnJ1xuICAgICAgICAgIH0pKTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICBjb25zb2xlLmVycm9yKCfinYwg6I635Y+W6K++56iL6K+m5oOF5aSx6LSlOicsIHJlcy5tZXNzYWdlKTtcbiAgICAgICAgICAvLyDkvb/nlKjln7rnoYDmlbDmja7kvZzkuLrlkI7lpIdcbiAgICAgICAgICBzZXRDb3Vyc2VEZXRhaWwocHJldiA9PiAoe1xuICAgICAgICAgICAgLi4ucHJldixcbiAgICAgICAgICAgIHRpdGxlOiBzZWxlY3RlZENvdXJzZS50aXRsZSxcbiAgICAgICAgICAgIGRlc2NyaXB0aW9uOiBzZWxlY3RlZENvdXJzZS5kZXNjcmlwdGlvbixcbiAgICAgICAgICAgIGNvdmVySW1hZ2U6IHNlbGVjdGVkQ291cnNlLmNvdmVySW1hZ2UgfHwgJycsXG4gICAgICAgICAgICBpc1ZpZGVvRW5hYmxlZDogc2VsZWN0ZWRDb3Vyc2UuaGFzVmlkZW8gPT09IDEsXG4gICAgICAgICAgICBpc0F0dGFjaG1lbnRFbmFibGVkOiBzZWxlY3RlZENvdXJzZS5oYXNEb2N1bWVudCA9PT0gMSxcbiAgICAgICAgICAgIHRlYWNoaW5nTWF0ZXJpYWxzOiBbXSAvLyDmuIXnqbrpmYTku7bliJfooahcbiAgICAgICAgICB9KSk7XG4gICAgICAgIH1cbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDojrflj5bor77nqIvor6bmg4XlvILluLg6JywgZXJyb3IpO1xuICAgICAgICAvLyDkvb/nlKjln7rnoYDmlbDmja7kvZzkuLrlkI7lpIdcbiAgICAgICAgc2V0Q291cnNlRGV0YWlsKHByZXYgPT4gKHtcbiAgICAgICAgICAuLi5wcmV2LFxuICAgICAgICAgIHRpdGxlOiBzZWxlY3RlZENvdXJzZS50aXRsZSxcbiAgICAgICAgICBkZXNjcmlwdGlvbjogc2VsZWN0ZWRDb3Vyc2UuZGVzY3JpcHRpb24sXG4gICAgICAgICAgY292ZXJJbWFnZTogc2VsZWN0ZWRDb3Vyc2UuY292ZXJJbWFnZSB8fCAnJyxcbiAgICAgICAgICBpc1ZpZGVvRW5hYmxlZDogc2VsZWN0ZWRDb3Vyc2UuaGFzVmlkZW8gPT09IDEsXG4gICAgICAgICAgaXNBdHRhY2htZW50RW5hYmxlZDogc2VsZWN0ZWRDb3Vyc2UuaGFzRG9jdW1lbnQgPT09IDEsXG4gICAgICAgICAgdGVhY2hpbmdNYXRlcmlhbHM6IFtdIC8vIOa4heepuumZhOS7tuWIl+ihqFxuICAgICAgICB9KSk7XG4gICAgICB9XG4gICAgfVxuICB9O1xuXG4gIC8vIOiOt+WPlumAieS4reeahOivvueoi1xuICBjb25zdCBnZXRTZWxlY3RlZENvdXJzZSA9ICgpID0+IHtcbiAgICByZXR1cm4gY291cnNlTGlzdC5maW5kKGNvdXJzZSA9PiBjb3Vyc2UuaWQgPT09IHNlbGVjdGVkQ291cnNlSWQpO1xuICB9O1xuXG4gIGlmICghaXNWaXNpYmxlKSByZXR1cm4gbnVsbDtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiY291cnNlLWxpc3QtbW9kYWwtb3ZlcmxheVwiPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb3Vyc2UtbGlzdC1tb2RhbFwiPlxuICAgICAgICB7Lyog5aS06YOoICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvdXJzZS1saXN0LWhlYWRlclwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY291cnNlLWxpc3QtdGl0bGUtc2VjdGlvblwiPlxuICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cImNvdXJzZS1saXN0LXRpdGxlXCI+6K++56iL5YiX6KGoPC9oMj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY291cnNlLWxpc3QtYWN0aW9uc1wiPlxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgb25DbGljaz17c2hvd1NldHRpbmdzUGFuZWx9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgY291cnNlLWxpc3Qtc2V0dGluZ3MtYnRuICR7cmlnaHRQYW5lbFR5cGUgPT09ICdzZXR0aW5ncycgPyAnYWN0aXZlJyA6ICcnfWB9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8U2V0dGluZ3MgY2xhc3NOYW1lPVwidy00IGgtNFwiIC8+XG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICA8YnV0dG9uIG9uQ2xpY2s9e2FkZE5ld0NvdXJzZX0gY2xhc3NOYW1lPVwiY291cnNlLWxpc3QtYWRkLWJ0blwiPlxuICAgICAgICAgICAgICAgIDxQbHVzIGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPlxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDxidXR0b24gb25DbGljaz17b25DbG9zZX0gY2xhc3NOYW1lPVwiY291cnNlLWxpc3QtY2xvc2UtYnRuXCI+XG4gICAgICAgICAgICA8WCBjbGFzc05hbWU9XCJ3LTYgaC02XCIgLz5cbiAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIOS4u+imgeWGheWuuSAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb3Vyc2UtbGlzdC1jb250ZW50XCI+XG4gICAgICAgICAgey8qIOW3puS+p+ivvueoi+WIl+ihqCAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvdXJzZS1saXN0LXNpZGViYXJcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY291cnNlLWxpc3QtaXRlbXNcIj5cbiAgICAgICAgICAgICAge2xvYWRpbmcgPyAoXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb3Vyc2UtbGlzdC1sb2FkaW5nXCI+XG4gICAgICAgICAgICAgICAgICA8cD7liqDovb3kuK0uLi48L3A+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICkgOiBjb3Vyc2VMaXN0Lmxlbmd0aCA9PT0gMCA/IChcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvdXJzZS1saXN0LWVtcHR5XCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvdXJzZS1saXN0LWVtcHR5LWljb25cIj5cbiAgICAgICAgICAgICAgICAgICAgPEJvb2tPcGVuIGNsYXNzTmFtZT1cInctMTIgaC0xMiB0ZXh0LWdyYXktMzAwXCIgLz5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cImNvdXJzZS1saXN0LWVtcHR5LXRpdGxlXCI+5pqC5peg6K++5pe2PC9oMz5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cImNvdXJzZS1saXN0LWVtcHR5LWRlc2NyaXB0aW9uXCI+XG4gICAgICAgICAgICAgICAgICAgIOeCueWHu+WPs+S4iuinkueahCArIOaMiemSrua3u+WKoOesrOS4gOS4quivvuaXtlxuICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXthZGROZXdDb3Vyc2V9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImNvdXJzZS1saXN0LWVtcHR5LWJ0blwiXG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIDxQbHVzIGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPlxuICAgICAgICAgICAgICAgICAgICDmt7vliqDor77ml7ZcbiAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgIGNvdXJzZUxpc3QubWFwKChjb3Vyc2UpID0+IChcbiAgICAgICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICAgICAga2V5PXtjb3Vyc2UuaWR9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGNvdXJzZS1saXN0LWl0ZW0gJHtzZWxlY3RlZENvdXJzZUlkID09PSBjb3Vyc2UuaWQgPyAnYWN0aXZlJyA6ICcnfWB9XG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNob3dDb3Vyc2VQYW5lbChjb3Vyc2UuaWQpfVxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvdXJzZS1saXN0LWl0ZW0tY29udGVudFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImNvdXJzZS1saXN0LWl0ZW0tdGV4dFwiPntjb3Vyc2UudGl0bGV9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17YGNvdXJzZS1zdGF0dXMtYmFkZ2UgJHtjb3Vyc2Uuc3RhdHVzID09PSAxID8gJ3B1Ymxpc2hlZCcgOiAnZHJhZnQnfWB9PlxuICAgICAgICAgICAgICAgICAgICAgICAge2NvdXJzZS5zdGF0dXMgPT09IDEgPyAn5bey5Y+R5biDJyA6ICfmnKrlj5HluIMnfVxuICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgZS5zdG9wUHJvcGFnYXRpb24oKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIHNob3dEZWxldGVDb25maXJtKGNvdXJzZS5pZCk7XG4gICAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJjb3Vyc2UtbGlzdC1pdGVtLWRlbGV0ZVwiXG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICA8WCBjbGFzc05hbWU9XCJ3LTMgaC0zXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICApKVxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7Lyog5Y+z5L6n57yW6L6R5Yy65Z+fICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY291cnNlLWxpc3QtZWRpdC1hcmVhXCI+XG4gICAgICAgICAgICB7cmlnaHRQYW5lbFR5cGUgPT09ICdub25lJyAmJiAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY291cnNlLWVkaXQtZW1wdHlcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvdXJzZS1lZGl0LWVtcHR5LWljb25cIj5cbiAgICAgICAgICAgICAgICAgIDxGaWxlVGV4dCBjbGFzc05hbWU9XCJ3LTE2IGgtMTYgdGV4dC1ncmF5LTMwMFwiIC8+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cImNvdXJzZS1lZGl0LWVtcHR5LXRpdGxlXCI+5peg6K++56iL6K+m5oOFPC9oMz5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJjb3Vyc2UtZWRpdC1lbXB0eS1kZXNjcmlwdGlvblwiPlxuICAgICAgICAgICAgICAgICAg54K55Ye75bem5L6n6K++56iL5oiW6K6+572u5oyJ6ZKu5p+l55yL6K+m5oOFXG4gICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgIHtyaWdodFBhbmVsVHlwZSA9PT0gJ3NldHRpbmdzJyAmJiAoXG4gICAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgICAgey8qIOezu+WIl+ivvueoi+WwgemdoiAqL31cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvdXJzZS1zZXJpZXMtY292ZXJcIj5cbiAgICAgICAgICAgICAgICAgIHtzZXJpZXNDb3ZlckltYWdlID8gKFxuICAgICAgICAgICAgICAgICAgICA8aW1nXG4gICAgICAgICAgICAgICAgICAgICAgc3JjPXtzZXJpZXNDb3ZlckltYWdlfVxuICAgICAgICAgICAgICAgICAgICAgIGFsdD1cIuezu+WIl+ivvueoi+WwgemdolwiXG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiY291cnNlLXNlcmllcy1jb3Zlci1pbWFnZVwiXG4gICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvdXJzZS1zZXJpZXMtY292ZXItcGxhY2Vob2xkZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj7ns7vliJfor77nqIvlsIHpnaI8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgIHsvKiDns7vliJforr7nva7ooajljZUgKi99XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb3Vyc2UtZWRpdC1mb3JtXCI+XG4gICAgICAgICAgICAgICAgICB7Lyog57O75YiX6K++56iL5qCH6aKYICovfVxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb3Vyc2UtZWRpdC1maWVsZFwiPlxuICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiY291cnNlLWVkaXQtbGFiZWxcIj7ns7vliJfor77nqIvmoIfpopg8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2VkaXRpbmdUaXRsZX1cbiAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldEVkaXRpbmdUaXRsZShlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiY291cnNlLWVkaXQtaW5wdXRcIlxuICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwi6K+36L6T5YWl57O75YiX6K++56iL5qCH6aKYXCJcbiAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICB7Lyog6K++56iL5qCH562+ICovfVxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb3Vyc2UtZWRpdC1maWVsZFwiPlxuICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiY291cnNlLWVkaXQtbGFiZWxcIj7or77nqIvmoIfnrb48L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICA8U2VsZWN0XG4gICAgICAgICAgICAgICAgICAgICAgbW9kZT1cIm11bHRpcGxlXCJcbiAgICAgICAgICAgICAgICAgICAgICBzdHlsZT17eyB3aWR0aDogJzEwMCUnIH19XG4gICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLor7fpgInmi6nor77nqIvmoIfnrb5cIlxuICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtzZWxlY3RlZFRhZ3N9XG4gICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9e3NldFNlbGVjdGVkVGFnc31cbiAgICAgICAgICAgICAgICAgICAgICBsb2FkaW5nPXt0YWdzTG9hZGluZ31cbiAgICAgICAgICAgICAgICAgICAgICBvcHRpb25zPXtjb3Vyc2VUYWdzLm1hcCh0YWcgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ/Cfj7fvuI8g5riy5p+T5qCH562+6YCJ6aG5OicsIHRhZyk7XG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICBsYWJlbDogKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIHN0eWxlPXt7IGNvbG9yOiB0YWcuY29sb3IgfX0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7dGFnLm5hbWV9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICApLFxuICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZTogdGFnLmlkXG4gICAgICAgICAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgICAgICAgICAgIH0pfVxuICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICB7Lyog6LCD6K+V5L+h5oGvICovfVxuICAgICAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGZvbnRTaXplOiAnMTJweCcsIGNvbG9yOiAnIzY2NicsIG1hcmdpblRvcDogJzRweCcgfX0+XG4gICAgICAgICAgICAgICAgICAgICAg6LCD6K+VOiDlvZPliY3moIfnrb7mlbDph48ge2NvdXJzZVRhZ3MubGVuZ3RofSwg5Yqg6L2954q25oCBOiB7dGFnc0xvYWRpbmcgPyAn5pivJyA6ICflkKYnfVxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICB7Lyog6K++56iL6aG555uu5oiQ5ZGYICovfVxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb3Vyc2UtZWRpdC1maWVsZFwiPlxuICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiY291cnNlLWVkaXQtbGFiZWxcIj7or77nqIvpobnnm67miJDlkZg8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3Byb2plY3RNZW1iZXJzfVxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0UHJvamVjdE1lbWJlcnMoZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImNvdXJzZS1lZGl0LWlucHV0XCJcbiAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIuivt+i+k+WFpemhueebruaIkOWRmO+8jOWmgu+8muW8oOiAgeW4iOOAgeadjuWKqeaVmeOAgeeOi+WQjOWtplwiXG4gICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC8+XG4gICAgICAgICAgICApfVxuXG4gICAgICAgICAgICB7cmlnaHRQYW5lbFR5cGUgPT09ICdjb3Vyc2UnICYmIGdldFNlbGVjdGVkQ291cnNlKCkgJiYgKFxuICAgICAgICAgICAgICA8PlxuICAgICAgICAgICAgICAgIHsvKiDor77nqIvor6bnu4bnvJbovpHnlYzpnaIgKi99XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb3Vyc2UtZGV0YWlsLWVkaXRcIj5cbiAgICAgICAgICAgICAgICAgIHsvKiDpobbpg6jljLrln5/vvJror77nqIvlsIHpnaLlkozln7rmnKzkv6Hmga8gKi99XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvdXJzZS1kZXRhaWwtdG9wXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY291cnNlLWRldGFpbC1jb3ZlclwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImNvdXJzZS1jb3Zlci11cGxvYWQtYXJlYVwiXG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBkb2N1bWVudC5nZXRFbGVtZW50QnlJZCgnY292ZXItdXBsb2FkLWlucHV0Jyk/LmNsaWNrKCl9XG4gICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAge2NvdXJzZURldGFpbC5jb3ZlckltYWdlIHx8IGdldFNlbGVjdGVkQ291cnNlKCk/LmNvdmVySW1hZ2UgPyAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxpbWdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzcmM9e2NvdXJzZURldGFpbC5jb3ZlckltYWdlIHx8IGdldFNlbGVjdGVkQ291cnNlKCk/LmNvdmVySW1hZ2V9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYWx0PVwi6K++56iL5bCB6Z2iXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJjb3Vyc2UtY292ZXItaW1hZ2VcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb3Vyc2UtY292ZXItcGxhY2Vob2xkZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj7ngrnlh7vkuIrkvKDor77nqIvlsIHpnaI8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgIGlkPVwiY292ZXItdXBsb2FkLWlucHV0XCJcbiAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJmaWxlXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIGFjY2VwdD1cImltYWdlL2pwZWcsaW1hZ2UvanBnLGltYWdlL3BuZyxpbWFnZS9naWZcIlxuICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZUNvdmVyVXBsb2FkfVxuICAgICAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3sgZGlzcGxheTogJ25vbmUnIH19XG4gICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICB7Lyog5LiA6ZSu5LiK6K++5oyJ6ZKuICovfVxuICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm9uZS1jbGljay1jbGFzcy1idG5cIlxuICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAvLyBUT0RPOiDlrp7njrDkuIDplK7kuIror77lip/og71cbiAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ+S4gOmUruS4iuivvuaMiemSruiiq+eCueWHuycpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICBub3RpZmljYXRpb24uaW5mbygn5LiA6ZSu5LiK6K++5Yqf6IO95byA5Y+R5LitLi4uJyk7XG4gICAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgIOS4gOmUruS4iuivvlxuICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb3Vyc2UtZGV0YWlsLWJhc2ljXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb3Vyc2UtZGV0YWlsLWZpZWxkXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8bGFiZWw+6K++56iL5qCH6aKYPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtjb3Vyc2VEZXRhaWwudGl0bGUgfHwgZ2V0U2VsZWN0ZWRDb3Vyc2UoKT8udGl0bGUgfHwgJyd9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNldENvdXJzZURldGFpbChwcmV2ID0+ICh7IC4uLnByZXYsIHRpdGxlOiBlLnRhcmdldC52YWx1ZSB9KSk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdXBkYXRlQ291cnNlVGl0bGUoc2VsZWN0ZWRDb3Vyc2VJZCEsIGUudGFyZ2V0LnZhbHVlKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLor7fovpPlhaXor77nqIvmoIfpophcIlxuICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvdXJzZS1kZXRhaWwtZmllbGRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbD7or77nqIvku4vnu408L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHRleHRhcmVhXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtjb3Vyc2VEZXRhaWwuZGVzY3JpcHRpb24gfHwgZ2V0U2VsZWN0ZWRDb3Vyc2UoKT8uZGVzY3JpcHRpb24gfHwgJyd9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0Q291cnNlRGV0YWlsKHByZXYgPT4gKHsgLi4ucHJldiwgZGVzY3JpcHRpb246IGUudGFyZ2V0LnZhbHVlIH0pKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLor7fovpPlhaXor77nqIvku4vnu41cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICByb3dzPXszfVxuICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgey8qIOivvueoi+i1hOa6kOWMuuWfnyAqL31cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY291cnNlLWRldGFpbC1zZWN0aW9uXCI+XG4gICAgICAgICAgICAgICAgICAgIDxoMz7or77nqIvotYTmupA8L2gzPlxuXG4gICAgICAgICAgICAgICAgICAgIHsvKiDor77nqIvop4bpopEgKi99XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY291cnNlLXJlc291cmNlLWl0ZW1cIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlc291cmNlLWhlYWRlci1yaWdodFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+6K++56iL6KeG6aKRPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cInN3aXRjaFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwiY2hlY2tib3hcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNoZWNrZWQ9e2NvdXJzZURldGFpbC5pc1ZpZGVvRW5hYmxlZH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldENvdXJzZURldGFpbChwcmV2ID0+ICh7IC4uLnByZXYsIGlzVmlkZW9FbmFibGVkOiBlLnRhcmdldC5jaGVja2VkIH0pKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwic2xpZGVyXCI+PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICB7Y291cnNlRGV0YWlsLmlzVmlkZW9FbmFibGVkICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidmlkZW8tY29udGVudC1hcmVhXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHsvKiDmmL7npLrnnJ/lrp7op4bpopHkv6Hmga8gKi99XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtjb3Vyc2VEZXRhaWwuY29udGVudENvbmZpZz8udmlkZW8/LnVybCA/IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInZpZGVvLWluZm8tc2VjdGlvblwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ2aWRlby1wcmV2aWV3XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDx2aWRlb1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInZpZGVvLXRodW1ibmFpbFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29udHJvbHNcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwb3N0ZXI9e2NvdXJzZURldGFpbC5jb3ZlckltYWdlfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNvdXJjZSBzcmM9e2NvdXJzZURldGFpbC5jb250ZW50Q29uZmlnLnZpZGVvLnVybH0gdHlwZT1cInZpZGVvL21wNFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg5oKo55qE5rWP6KeI5Zmo5LiN5pSv5oyB6KeG6aKR5pKt5pS+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvdmlkZW8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidmlkZW8tbmFtZS1jZW50ZXJlZFwiPntjb3Vyc2VEZXRhaWwuY29udGVudENvbmZpZy52aWRlby5uYW1lfTwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9XCJ1cGxvYWQtYnRuLWhvcml6b250YWxcIiBvbkNsaWNrPXt0cmlnZ2VyVmlkZW9VcGxvYWR9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj7ph43mlrDkuIrkvKA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInZpZGVvLXVwbG9hZC1zZWN0aW9uXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInZpZGVvLXBsYWNlaG9sZGVyLWNlbnRlcmVkXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicGxheS1pY29uXCI+4pa2PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b24gY2xhc3NOYW1lPVwidXBsb2FkLWJ0bi1ob3Jpem9udGFsXCIgb25DbGljaz17dHJpZ2dlclZpZGVvVXBsb2FkfT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+5LiK5Lyg6KeG6aKRPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICB7Lyog6K++56iL6ZmE5Lu2ICovfVxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvdXJzZS1yZXNvdXJjZS1pdGVtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZXNvdXJjZS1oZWFkZXItcmlnaHRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPuivvueoi+mZhOS7tjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJzd2l0Y2hcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImNoZWNrYm94XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjaGVja2VkPXtjb3Vyc2VEZXRhaWwuaXNBdHRhY2htZW50RW5hYmxlZH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldENvdXJzZURldGFpbChwcmV2ID0+ICh7IC4uLnByZXYsIGlzQXR0YWNobWVudEVuYWJsZWQ6IGUudGFyZ2V0LmNoZWNrZWQgfSkpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJzbGlkZXJcIj48L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIHtjb3Vyc2VEZXRhaWwuaXNBdHRhY2htZW50RW5hYmxlZCAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImF0dGFjaG1lbnQtY29udGVudC1hcmVhXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHsvKiDmmL7npLrnnJ/lrp7pmYTku7bkv6Hmga8gKi99XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtjb3Vyc2VEZXRhaWwuY29udGVudENvbmZpZz8uZG9jdW1lbnQ/LnVybCA/IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImF0dGFjaG1lbnQtaW5mby1zZWN0aW9uXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImF0dGFjaG1lbnQtcHJldmlld1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImRvY3VtZW50LWljb25cIj7wn5OEPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYXR0YWNobWVudC1kZXRhaWxzXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhdHRhY2htZW50LW5hbWVcIj57Y291cnNlRGV0YWlsLmNvbnRlbnRDb25maWcuZG9jdW1lbnQubmFtZX08L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b24gY2xhc3NOYW1lPVwidXBsb2FkLWJ0bi1ob3Jpem9udGFsXCIgb25DbGljaz17dHJpZ2dlckF0dGFjaG1lbnRVcGxvYWR9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj7ph43mlrDkuIrkvKA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImF0dGFjaG1lbnQtdXBsb2FkLXNlY3Rpb25cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b24gY2xhc3NOYW1lPVwidXBsb2FkLWJ0bi1ob3Jpem9udGFsXCIgb25DbGljaz17dHJpZ2dlckF0dGFjaG1lbnRVcGxvYWR9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj7kuIrkvKDpmYTku7Y8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgIHsvKiDmlZnlrabpmYTku7YgKi99XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY291cnNlLXJlc291cmNlLWl0ZW1cIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlc291cmNlLWhlYWRlci1zaW1wbGVcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPuaVmeWtpumZhOS7tjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRlYWNoaW5nLW1hdGVyaWFsc1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9XCJhZGQtbWF0ZXJpYWwtYnRuXCIgb25DbGljaz17dHJpZ2dlclRlYWNoaW5nTWF0ZXJpYWxVcGxvYWR9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj4rPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj7kuIrkvKA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtjb3Vyc2VEZXRhaWwudGVhY2hpbmdNYXRlcmlhbHMgJiYgY291cnNlRGV0YWlsLnRlYWNoaW5nTWF0ZXJpYWxzLmxlbmd0aCA+IDAgPyAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNvdXJzZURldGFpbC50ZWFjaGluZ01hdGVyaWFscy5tYXAoKG1hdGVyaWFsLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYga2V5PXtpbmRleH0gY2xhc3NOYW1lPVwibWF0ZXJpYWwtaXRlbVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwibWF0ZXJpYWwtbmFtZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAobWF0ZXJpYWwudXJsKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB3aW5kb3cub3BlbihtYXRlcmlhbC51cmwsICdfYmxhbmsnKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY3Vyc29yOiBtYXRlcmlhbC51cmwgPyAncG9pbnRlcicgOiAnZGVmYXVsdCcsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I6IG1hdGVyaWFsLnVybCA/ICcjMTg5MGZmJyA6ICdpbmhlcml0JyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0ZXh0RGVjb3JhdGlvbjogbWF0ZXJpYWwudXJsID8gJ3VuZGVybGluZScgOiAnbm9uZSdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU9e21hdGVyaWFsLnVybCA/ICfngrnlh7vkuIvovb3pmYTku7YnIDogbWF0ZXJpYWwubmFtZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg8J+TjiB7bWF0ZXJpYWwubmFtZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicmVtb3ZlLW1hdGVyaWFsLWJ0blwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHJlbW92ZVRlYWNoaW5nTWF0ZXJpYWwoaW5kZXgpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aXRsZT1cIuWIoOmZpOmZhOS7tlwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIMOXXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgKSlcbiAgICAgICAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZW1wdHktbWF0ZXJpYWxzLWhpbnRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBzdHlsZT17eyBjb2xvcjogJyM5OTknLCBmb250U2l6ZTogJzE0cHgnIH19PuaaguaXoOaVmeWtpumZhOS7tjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICB7Lyog6K++56iL5YaF5a655Yy65Z+fICovfVxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb3Vyc2UtZGV0YWlsLXNlY3Rpb25cIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzZWN0aW9uLWhlYWRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxoMz7or77nqIvlhoXlrrk8L2gzPlxuICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImFkZC1jb250ZW50LXNlY3Rpb24tYnRuXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2FkZFRlYWNoaW5nSW5mb0l0ZW19XG4gICAgICAgICAgICAgICAgICAgICAgICB0aXRsZT1cIua3u+WKoOivvueoi+WGheWuuVwiXG4gICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgKyDmt7vliqDor77nqIvlhoXlrrlcbiAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY291cnNlLWNvbnRlbnQtYXJlYVwiPlxuICAgICAgICAgICAgICAgICAgICAgIHtjb3Vyc2VEZXRhaWwudGVhY2hpbmdJbmZvICYmIGNvdXJzZURldGFpbC50ZWFjaGluZ0luZm8ubGVuZ3RoID4gMCA/IChcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvdXJzZURldGFpbC50ZWFjaGluZ0luZm8ubWFwKChpbmZvLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGtleT17aW5kZXh9IGNsYXNzTmFtZT1cInRlYWNoaW5nLWluZm8tY2FyZFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY2FyZC1oZWFkZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImNhcmQtdGl0bGVcIj7or77nqIvlhoXlrrkge2luZGV4ICsgMX08L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInJlbW92ZS1jYXJkLWJ0blwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHJlbW92ZVRlYWNoaW5nSW5mb0l0ZW0oaW5kZXgpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aXRsZT1cIuWIoOmZpOatpOWGheWuuVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIMOXXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNhcmQtY29udGVudFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJpbnB1dC1ncm91cFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8bGFiZWw+5qCH6aKYPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtpbmZvLnRpdGxlfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gdXBkYXRlVGVhY2hpbmdJbmZvVGl0bGUoaW5kZXgsIGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIuivt+i+k+WFpeagh+mimO+8jOWmgu+8muaVmeWtpuebruagh+OAgeaVmeWtpuaWueazleetiVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGl0bGUtaW5wdXRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImlucHV0LWdyb3VwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbD7lhoXlrrk8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8dGV4dGFyZWFcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17aW5mby5jb250ZW50fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gdXBkYXRlVGVhY2hpbmdJbmZvQ29udGVudChpbmRleCwgZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwi6K+36L6T5YWl5YW35L2T5YaF5a6577yM5aSa5Liq5YaF5a656aG55Y+v55So5o2i6KGM5YiG6ZqUXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJjb250ZW50LXRleHRhcmVhXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByb3dzPXs0fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgKSlcbiAgICAgICAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJlbXB0eS1jb250ZW50LWhpbnRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHA+5pqC5peg6K++56iL5YaF5a6577yM54K55Ye75Y+z5LiK6KeS5oyJ6ZKu5re75YqgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgey8qIOmHjeaWsOS4iuivviAqL31cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY291cnNlLWRldGFpbC1zZWN0aW9uXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwib25lLWtleS1zZWN0aW9uXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJvbmUta2V5LWl0ZW1cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPumHjeaWsOS4iuivvjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJzd2l0Y2hcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImNoZWNrYm94XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjaGVja2VkPXtjb3Vyc2VEZXRhaWwuaXNPbmVLZXlPcGVufVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0Q291cnNlRGV0YWlsKHByZXYgPT4gKHsgLi4ucHJldiwgaXNPbmVLZXlPcGVuOiBlLnRhcmdldC5jaGVja2VkIH0pKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwic2xpZGVyXCI+PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICAgIHtjb3Vyc2VEZXRhaWwuaXNPbmVLZXlPcGVuICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwib25lLWtleS1pdGVtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+5YiG6YWN56ev5pyoPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJzd2l0Y2hcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwiY2hlY2tib3hcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjaGVja2VkPXtjb3Vyc2VEZXRhaWwuaXNEaXN0cmlidXRpb25FbmFibGVkfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldENvdXJzZURldGFpbChwcmV2ID0+ICh7IC4uLnByZXYsIGlzRGlzdHJpYnV0aW9uRW5hYmxlZDogZS50YXJnZXQuY2hlY2tlZCB9KSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwic2xpZGVyXCI+PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge2NvdXJzZURldGFpbC5pc0Rpc3RyaWJ1dGlvbkVuYWJsZWQgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJibG9jay10ZW1wbGF0ZS1zZWN0aW9uXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJzZWxlY3QtdGVtcGxhdGUtYnRuXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVPcGVuVGVtcGxhdGVQaWNrZXJ9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICDpgInmi6nnp6/mnKjmqKHmnb9cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic2VsZWN0ZWQtdGVtcGxhdGUtZGlzcGxheVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPntjb3Vyc2VEZXRhaWwuc2VsZWN0ZWRUZW1wbGF0ZSB8fCAn6YCJ5Lit55qE5qih5p2/5ZCN5a2XJ308L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJvbmUta2V5LWl0ZW1cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj7liIbphY3og73ph488L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cInN3aXRjaFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJjaGVja2JveFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNoZWNrZWQ9e2NvdXJzZURldGFpbC5pc0Rpc3RyaWJ1dGlvbldhdGVyfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldENvdXJzZURldGFpbChwcmV2ID0+ICh7IC4uLnByZXYsIGlzRGlzdHJpYnV0aW9uV2F0ZXI6IGUudGFyZ2V0LmNoZWNrZWQgfSkpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInNsaWRlclwiPjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICAgICAgICB7Y291cnNlRGV0YWlsLmlzRGlzdHJpYnV0aW9uV2F0ZXIgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZW5lcmd5LWlucHV0LXNlY3Rpb25cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPumcgOimgeiDvemHj++8mjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtjb3Vyc2VEZXRhaWwucmVxdWlyZWRFbmVyZ3kgfHwgJyd9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0Q291cnNlRGV0YWlsKHByZXYgPT4gKHsgLi4ucHJldiwgcmVxdWlyZWRFbmVyZ3k6IGUudGFyZ2V0LnZhbHVlIH0pKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLor7fovpPlhaXpnIDopoHnmoTog73ph4/lgLxcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJlbmVyZ3ktaW5wdXRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm9uZS1rZXktaXRlbVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPuWIhumFjeS7u+WKoTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwic3dpdGNoXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImNoZWNrYm94XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2hlY2tlZD17Y291cnNlRGV0YWlsLmlzRGlzdHJpYnV0aW9uTGltaXR9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0Q291cnNlRGV0YWlsKHByZXYgPT4gKHsgLi4ucHJldiwgaXNEaXN0cmlidXRpb25MaW1pdDogZS50YXJnZXQuY2hlY2tlZCB9KSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwic2xpZGVyXCI+PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHsvKiDku7vliqHphY3nva7ooajljZUgKi99XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtjb3Vyc2VEZXRhaWwuaXNEaXN0cmlidXRpb25MaW1pdCAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0YXNrLWNvbmZpZy1mb3JtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Lyog5Lu75Yqh5ZCN56ew5ZKM5oyB57ut5aSp5pWwICovfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0YXNrLWNvbmZpZy1yb3dcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0YXNrLWNvbmZpZy1maWVsZFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbD7ku7vliqHlkI3np7A6PC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtjb3Vyc2VEZXRhaWwudGFza0NvbmZpZy50YXNrTmFtZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0Q291cnNlRGV0YWlsKHByZXYgPT4gKHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4ucHJldixcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGFza0NvbmZpZzogeyAuLi5wcmV2LnRhc2tDb25maWcsIHRhc2tOYW1lOiBlLnRhcmdldC52YWx1ZSB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIuivt+i+k+WFpeS7u+WKoeWQjeensFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGFzay1jb25maWctZmllbGRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8bGFiZWw+5Lu75Yqh5oyB57ut5aSp5pWwOjwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwibnVtYmVyXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtjb3Vyc2VEZXRhaWwudGFza0NvbmZpZy50YXNrRHVyYXRpb259XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldENvdXJzZURldGFpbChwcmV2ID0+ICh7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC4uLnByZXYsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRhc2tDb25maWc6IHsgLi4ucHJldi50YXNrQ29uZmlnLCB0YXNrRHVyYXRpb246IGUudGFyZ2V0LnZhbHVlIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwi6K+36L6T5YWl5aSp5pWwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Lyog5Lu75Yqh5o+P6L+wICovfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0YXNrLWNvbmZpZy1maWVsZCB0YXNrLWNvbmZpZy1mdWxsXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbD7ku7vliqHmj4/ov7A6PC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHRleHRhcmVhXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2NvdXJzZURldGFpbC50YXNrQ29uZmlnLnRhc2tEZXNjcmlwdGlvbn1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldENvdXJzZURldGFpbChwcmV2ID0+ICh7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuLi5wcmV2LFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGFza0NvbmZpZzogeyAuLi5wcmV2LnRhc2tDb25maWcsIHRhc2tEZXNjcmlwdGlvbjogZS50YXJnZXQudmFsdWUgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIuivt+i+k+WFpeS7u+WKoeaPj+i/sFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcm93cz17NH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Lyog5Lu75Yqh6Ieq6K+E6aG5ICovfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0YXNrLWNvbmZpZy1maWVsZCB0YXNrLWNvbmZpZy1mdWxsXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbD7ku7vliqHoh6ror4Tpobk6IDxzcGFuIGNsYXNzTmFtZT1cIml0ZW0tbnVtYmVyXCI+e2NvdXJzZURldGFpbC50YXNrQ29uZmlnLnNlbGZBc3Nlc3NtZW50SXRlbXMubGVuZ3RofTwvc3Bhbj48L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Y291cnNlRGV0YWlsLnRhc2tDb25maWcuc2VsZkFzc2Vzc21lbnRJdGVtcy5tYXAoKGl0ZW0sIGluZGV4KSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBrZXk9e2luZGV4fSBjbGFzc05hbWU9XCJzZWxmLWFzc2Vzc21lbnQtaXRlbVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2l0ZW19XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IG5ld0l0ZW1zID0gWy4uLmNvdXJzZURldGFpbC50YXNrQ29uZmlnLnNlbGZBc3Nlc3NtZW50SXRlbXNdO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG5ld0l0ZW1zW2luZGV4XSA9IGUudGFyZ2V0LnZhbHVlO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNldENvdXJzZURldGFpbChwcmV2ID0+ICh7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuLi5wcmV2LFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGFza0NvbmZpZzogeyAuLi5wcmV2LnRhc2tDb25maWcsIHNlbGZBc3Nlc3NtZW50SXRlbXM6IG5ld0l0ZW1zIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KSk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwi6K+36L6T5YWl6Ieq6K+E6aG55YaF5a65XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImJ1dHRvblwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYWRkLWFzc2Vzc21lbnQtYnRuXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRDb3Vyc2VEZXRhaWwocHJldiA9PiAoe1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4ucHJldixcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRhc2tDb25maWc6IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4ucHJldi50YXNrQ29uZmlnLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZWxmQXNzZXNzbWVudEl0ZW1zOiBbLi4ucHJldi50YXNrQ29uZmlnLnNlbGZBc3Nlc3NtZW50SXRlbXMsICcnXVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgey8qIOS7u+WKoeWPguiAg+S9nOWTgSAqL31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGFzay1jb25maWctZmllbGQgdGFzay1jb25maWctZnVsbFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8bGFiZWw+5Lu75Yqh5Y+C6ICD5L2c5ZOBOjwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVmZXJlbmNlLXdvcmtzLXNlY3Rpb25cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIndvcmtzLXNlY3Rpb25cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cImhlbHAtdGV4dFwiPumAieaLqeS9nOWTgeS9nOS4uuS7u+WKoeWPguiAg+i1hOaWme+8iOWPr+WkmumAie+8iTwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicmVsYXRpdmUgd29ya3Mtc2Nyb2xsLXdyYXBwZXJcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1pbkhlaWdodDogJzIwMHB4JyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjdXJzb3I6ICdncmFiJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB1c2VyU2VsZWN0OiAnbm9uZSdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2xvYWRpbmdXb3JrcyA/IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImxvYWRpbmctY29udGFpbmVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImxvYWRpbmctc3Bpbm5lclwiPjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+5Yqg6L295LitLi4uPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApIDogd29ya3MubGVuZ3RoID4gMCA/IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3b3Jrcy1ob3Jpem9udGFsLXNjcm9sbFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbldoZWVsPXtoYW5kbGVXb3Jrc1doZWVsfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHt3b3Jrcy5tYXAoKHdvcmspID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGtleT17d29yay5pZH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2B3b3JrLWNhcmQgJHtzZWxlY3RlZFdvcmtJZHMuaW5jbHVkZXMod29yay5pZCkgPyAnc2VsZWN0ZWQnIDogJyd9YH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVXb3JrU2VsZWN0KHdvcmsuaWQpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3b3JrLWltYWdlXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7d29yay5jb3ZlckltYWdlIHx8IHdvcmsuc2NyZWVuU2hvdEltYWdlID8gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aW1nXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3JjPXt3b3JrLmNvdmVySW1hZ2UgfHwgd29yay5zY3JlZW5TaG90SW1hZ2V9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYWx0PXt3b3JrLnRpdGxlfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uRXJyb3I9eyhlKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCB0YXJnZXQgPSBlLmN1cnJlbnRUYXJnZXQgYXMgSFRNTEltYWdlRWxlbWVudDtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRhcmdldC5zdHlsZS5kaXNwbGF5ID0gJ25vbmUnO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgbmV4dEVsZW1lbnQgPSB0YXJnZXQubmV4dEVsZW1lbnRTaWJsaW5nIGFzIEhUTUxFbGVtZW50O1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKG5leHRFbGVtZW50KSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG5leHRFbGVtZW50LnN0eWxlLmRpc3BsYXkgPSAnZmxleCc7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkgOiBudWxsfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3b3JrLXBsYWNlaG9sZGVyXCIgc3R5bGU9e3sgZGlzcGxheTogd29yay5jb3ZlckltYWdlIHx8IHdvcmsuc2NyZWVuU2hvdEltYWdlID8gJ25vbmUnIDogJ2ZsZXgnIH19PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICDkvZzlk4FcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwid29yay10aXRsZVwiPnt3b3JrLnRpdGxlIHx8IHdvcmsubmFtZSB8fCB3b3JrLndvcmtOYW1lIHx8ICfmnKrlkb3lkI3kvZzlk4EnfTwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtzZWxlY3RlZFdvcmtJZHMuaW5jbHVkZXMod29yay5pZCkgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzZWxlY3RlZC1pbmRpY2F0b3JcIj7inJM8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZW1wdHktcGxhY2Vob2xkZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZW1wdHktdGV4dFwiPuaaguaXoOS9nOWTgTwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsvKiDku7vliqHlj4LogIPotYTmupAgKi99XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRhc2stY29uZmlnLWZpZWxkIHRhc2stY29uZmlnLWZ1bGxcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGxhYmVsPuS7u+WKoeWPguiAg+i1hOa6kDo8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlZmVyZW5jZS1yZXNvdXJjZXMtc2VjdGlvblwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVmZXJlbmNlLXJlc291cmNlcy1ncmlkXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ1cGxvYWQtcmVzb3VyY2UtYnRuXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIOinpuWPkeaWh+S7tuS4iuS8oFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGlucHV0ID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnaW5wdXQnKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpbnB1dC50eXBlID0gJ2ZpbGUnO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlucHV0LmFjY2VwdCA9ICcucGRmLC5kb2MsLmRvY3gsLnBwdCwucHB0eCwueGxzLC54bHN4LC50eHQsLmpwZywucG5nLC5naWYnO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlucHV0Lm9uY2hhbmdlID0gKGUpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGZpbGUgPSAoZS50YXJnZXQgYXMgSFRNTElucHV0RWxlbWVudCkuZmlsZXM/LlswXTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmIChmaWxlKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNldENvdXJzZURldGFpbChwcmV2ID0+ICh7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4ucHJldixcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0YXNrQ29uZmlnOiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuLi5wcmV2LnRhc2tDb25maWcsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZWZlcmVuY2VSZXNvdXJjZXM6IFtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4ucHJldi50YXNrQ29uZmlnLnJlZmVyZW5jZVJlc291cmNlcyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgeyB0eXBlOiAnZmlsZScsIG5hbWU6IGZpbGUubmFtZSB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBdXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KSk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpbnB1dC5jbGljaygpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8UGx1cyBzaXplPXsyNH0gLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg5LiK5LygXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtjb3Vyc2VEZXRhaWwudGFza0NvbmZpZy5yZWZlcmVuY2VSZXNvdXJjZXMubWFwKChyZXNvdXJjZSwgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBrZXk9e2luZGV4fSBjbGFzc05hbWU9XCJyZWZlcmVuY2UtcmVzb3VyY2UtaXRlbVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPntyZXNvdXJjZS5uYW1lfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInJlbW92ZS1yZXNvdXJjZS1idG5cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBuZXdSZXNvdXJjZXMgPSBjb3Vyc2VEZXRhaWwudGFza0NvbmZpZy5yZWZlcmVuY2VSZXNvdXJjZXMuZmlsdGVyKChfLCBpKSA9PiBpICE9PSBpbmRleCk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNldENvdXJzZURldGFpbChwcmV2ID0+ICh7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4ucHJldixcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0YXNrQ29uZmlnOiB7IC4uLnByZXYudGFza0NvbmZpZywgcmVmZXJlbmNlUmVzb3VyY2VzOiBuZXdSZXNvdXJjZXMgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KSk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIMOXXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICA8Lz5cbiAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8Lz5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiDlupXpg6jmjInpkq4gKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY291cnNlLWxpc3QtZm9vdGVyXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb3Vyc2UtbGlzdC1mb290ZXItbGVmdFwiPlxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVQdWJsaXNofVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJjb3Vyc2UtbGlzdC1idG4gY291cnNlLWxpc3QtYnRuLXB1Ymxpc2hcIlxuICAgICAgICAgICAgICBkaXNhYmxlZD17Y291cnNlTGlzdC5sZW5ndGggPT09IDAgfHwgaXNQdWJsaXNoaW5nU2VyaWVzIHx8IHNlcmllc1N0YXR1cyA9PT0gMX1cbiAgICAgICAgICAgICAgdGl0bGU9e1xuICAgICAgICAgICAgICAgIHNlcmllc1N0YXR1cyA9PT0gMVxuICAgICAgICAgICAgICAgICAgPyAn57O75YiX6K++56iL5bey5Y+R5biDJ1xuICAgICAgICAgICAgICAgICAgOiBjb3Vyc2VMaXN0Lmxlbmd0aCA9PT0gMFxuICAgICAgICAgICAgICAgICAgICA/ICflj5HluIPlpLHotKXvvJror77nqIvns7vliJfkuK3oh7PlsJHpnIDopoHljIXlkKvkuIDkuKror77nqIsnXG4gICAgICAgICAgICAgICAgICAgIDogaXNQdWJsaXNoaW5nU2VyaWVzXG4gICAgICAgICAgICAgICAgICAgICAgPyAn5q2j5Zyo5Y+R5biD57O75YiX6K++56iLLi4uJ1xuICAgICAgICAgICAgICAgICAgICAgIDogJ+WPkeW4g+ezu+WIl+ivvueoiydcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICB7c2VyaWVzU3RhdHVzID09PSAxXG4gICAgICAgICAgICAgICAgPyAn5bey5Y+R5biDJ1xuICAgICAgICAgICAgICAgIDogaXNQdWJsaXNoaW5nU2VyaWVzXG4gICAgICAgICAgICAgICAgICA/ICfmraPlnKjlj5HluIMuLi4nXG4gICAgICAgICAgICAgICAgICA6ICflj5HluIPns7vliJfor77nqIsnXG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY291cnNlLWxpc3QtZm9vdGVyLXJpZ2h0XCI+XG4gICAgICAgICAgICA8YnV0dG9uIG9uQ2xpY2s9e2hhbmRsZUV4aXRFZGl0fSBjbGFzc05hbWU9XCJjb3Vyc2UtbGlzdC1idG4gY291cnNlLWxpc3QtYnRuLWV4aXRcIj5cbiAgICAgICAgICAgICAg6YCA5Ye657yW6L6R5qih5byPXG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlUHVibGlzaENvdXJzZX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiY291cnNlLWxpc3QtYnRuIGNvdXJzZS1saXN0LWJ0bi1wdWJsaXNoLWNvdXJzZVwiXG4gICAgICAgICAgICAgIGRpc2FibGVkPXshc2VsZWN0ZWRDb3Vyc2VJZCB8fCBjb3Vyc2VMaXN0LmZpbmQoYyA9PiBjLmlkID09PSBzZWxlY3RlZENvdXJzZUlkKT8uc3RhdHVzID09PSAxIHx8IGlzUHVibGlzaGluZ31cbiAgICAgICAgICAgICAgdGl0bGU9e1xuICAgICAgICAgICAgICAgICFzZWxlY3RlZENvdXJzZUlkXG4gICAgICAgICAgICAgICAgICA/ICfor7flhYjpgInmi6nopoHlj5HluIPnmoTor77nqIsnXG4gICAgICAgICAgICAgICAgICA6IGNvdXJzZUxpc3QuZmluZChjID0+IGMuaWQgPT09IHNlbGVjdGVkQ291cnNlSWQpPy5zdGF0dXMgPT09IDFcbiAgICAgICAgICAgICAgICAgICAgPyAn6K+l6K++56iL5bey5Y+R5biDJ1xuICAgICAgICAgICAgICAgICAgICA6IGlzUHVibGlzaGluZ1xuICAgICAgICAgICAgICAgICAgICAgID8gJ+ato+WcqOWPkeW4g+ivvueoiy4uLidcbiAgICAgICAgICAgICAgICAgICAgICA6ICflj5HluIPpgInkuK3nmoTor77nqIsnXG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAge2lzUHVibGlzaGluZyA/ICfmraPlnKjlj5HluIMuLi4nIDogJ+WPkeW4g+ivvueoiyd9XG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlU2F2ZX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiY291cnNlLWxpc3QtYnRuIGNvdXJzZS1saXN0LWJ0bi1zYXZlXCJcbiAgICAgICAgICAgICAgZGlzYWJsZWQ9e3VwbG9hZGluZ0ZpbGVzLnNpemUgPiAwIHx8IGlzQ3JlYXRpbmcgfHwgY291cnNlTGlzdC5sZW5ndGggPT09IDB9XG4gICAgICAgICAgICAgIHRpdGxlPXtcbiAgICAgICAgICAgICAgICBjb3Vyc2VMaXN0Lmxlbmd0aCA9PT0gMFxuICAgICAgICAgICAgICAgICAgPyAn6K+35YWI5re75Yqg6K++56iL5YaF5a65J1xuICAgICAgICAgICAgICAgICAgOiB1cGxvYWRpbmdGaWxlcy5zaXplID4gMFxuICAgICAgICAgICAgICAgICAgICA/ICfmnInmlofku7bmraPlnKjkuIrkvKDkuK3vvIzor7fnrYnlvoXkuIrkvKDlrozmiJAnXG4gICAgICAgICAgICAgICAgICAgIDogaXNDcmVhdGluZ1xuICAgICAgICAgICAgICAgICAgICAgID8gKGNvdXJzZUxpc3Quc29tZShjID0+IGMuaWQgPiAxMDAwMDAwKSA/ICfmraPlnKjliJvlu7ror77nqIsuLi4nIDogJ+ato+WcqOS/neWtmOivvueoiy4uLicpXG4gICAgICAgICAgICAgICAgICAgICAgOiAoY291cnNlTGlzdC5zb21lKGMgPT4gYy5pZCA+IDEwMDAwMDApID8gJ+WIm+W7uuivvueoiycgOiAn5L+d5a2Y6K++56iLJylcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICB7dXBsb2FkaW5nRmlsZXMuc2l6ZSA+IDBcbiAgICAgICAgICAgICAgICA/ICfkuIrkvKDkuK0uLi4nXG4gICAgICAgICAgICAgICAgOiBpc0NyZWF0aW5nXG4gICAgICAgICAgICAgICAgICA/IChjb3Vyc2VMaXN0LnNvbWUoYyA9PiBjLmlkID4gMTAwMDAwMCkgPyAn5q2j5Zyo5Yib5bu6Li4uJyA6ICfmraPlnKjkv53lrZguLi4nKVxuICAgICAgICAgICAgICAgICAgOiAoY291cnNlTGlzdC5zb21lKGMgPT4gYy5pZCA+IDEwMDAwMDApID8gJ+WIm+W7uuivvueoiycgOiAn5L+d5a2Y6K++56iLJylcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiDliKDpmaTnoa7orqTlvLnnqpcgKi99XG4gICAgICB7ZGVsZXRlQ29uZmlybVZpc2libGUgJiYgKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1vZGFsLW92ZXJsYXlcIiBvbkNsaWNrPXtjYW5jZWxEZWxldGV9PlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZGVsZXRlLWNvbmZpcm0tbW9kYWxcIiBvbkNsaWNrPXsoZSkgPT4gZS5zdG9wUHJvcGFnYXRpb24oKX0+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImRlbGV0ZS1jb25maXJtLWhlYWRlclwiPlxuICAgICAgICAgICAgICA8aDM+56Gu6K6k5Yig6ZmkPC9oMz5cbiAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2NhbmNlbERlbGV0ZX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJjbG9zZS1idG5cIlxuICAgICAgICAgICAgICAgIGRpc2FibGVkPXtpc0RlbGV0aW5nfVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPFggY2xhc3NOYW1lPVwidy00IGgtNFwiIC8+XG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImRlbGV0ZS1jb25maXJtLWNvbnRlbnRcIj5cbiAgICAgICAgICAgICAgPHA+XG4gICAgICAgICAgICAgICAge2lzRGVsZXRpbmdcbiAgICAgICAgICAgICAgICAgID8gJ+ato+WcqOWIoOmZpOivvueoi++8jOivt+eojeWAmS4uLidcbiAgICAgICAgICAgICAgICAgIDogJ+ehruWumuimgeWIoOmZpOi/meS4quivvueoi+WQl++8n+WIoOmZpOWQjuaXoOazleaBouWkjeOAgidcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAge2lzRGVsZXRpbmcgJiYgKFxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZGVsZXRlLWxvYWRpbmdcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibG9hZGluZy1zcGlubmVyXCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZGVsZXRlLWNvbmZpcm0tZm9vdGVyXCI+XG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXtjYW5jZWxEZWxldGV9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiY2FuY2VsLWJ0blwiXG4gICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2lzRGVsZXRpbmd9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICDlj5bmtohcbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXtjb25maXJtRGVsZXRlQ291cnNlfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImNvbmZpcm0tYnRuXCJcbiAgICAgICAgICAgICAgICBkaXNhYmxlZD17aXNEZWxldGluZ31cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIHtpc0RlbGV0aW5nID8gJ+ato+WcqOWIoOmZpC4uLicgOiAn56Gu6K6k5Yig6ZmkJ31cbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuXG4gICAgICB7Lyog5qih5p2/6YCJ5oup5by556qXICovfVxuICAgICAgPFRlbXBsYXRlUGlja2VyTW9kYWxcbiAgICAgICAgaXNPcGVuPXtpc1RlbXBsYXRlUGlja2VyT3Blbn1cbiAgICAgICAgb25DbG9zZT17KCkgPT4gc2V0SXNUZW1wbGF0ZVBpY2tlck9wZW4oZmFsc2UpfVxuICAgICAgICBvblRlbXBsYXRlU2VsZWN0PXtoYW5kbGVUZW1wbGF0ZVNlbGVjdH1cbiAgICAgIC8+XG4gICAgPC9kaXY+XG4gICk7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBDb3Vyc2VMaXN0RWRpdE1vZGFsO1xuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJ1c2VDYWxsYmFjayIsIlgiLCJTZXR0aW5ncyIsIlBsdXMiLCJCb29rT3BlbiIsIkZpbGVUZXh0IiwiY291cnNlTWFuYWdlbWVudEFwaSIsImNvdXJzZUFwaSIsInVwbG9hZEFwaSIsIndvcmtzQXBpIiwiU2VsZWN0IiwiR2V0Tm90aWZpY2F0aW9uIiwiVGVtcGxhdGVQaWNrZXJNb2RhbCIsImZldGNoU2VyaWVzRGV0YWlsIiwic2VyaWVzSWQiLCJjb25zb2xlIiwibG9nIiwicmVzcG9uc2UiLCJnZXRTZXJpZXNEZXRhaWwiLCJkYXRhIiwiZmV0Y2hDb3Vyc2VMaXN0IiwiZ2V0TWFuYWdlbWVudFNlcmllc0NvdXJzZXMiLCJwYWdlIiwicGFnZVNpemUiLCJmZXRjaENvdXJzZURldGFpbCIsImNvdXJzZUlkIiwiZ2V0Q291cnNlTWFya2V0cGxhY2VEZXRhaWwiLCJmZXRjaENvdXJzZVRhZ3MiLCJyZXN1bHQiLCJnZXRDb3Vyc2VUYWdzIiwic3RhdHVzIiwiZXJyb3IiLCJDb3Vyc2VMaXN0RWRpdE1vZGFsIiwiaXNWaXNpYmxlIiwib25DbG9zZSIsIm9uU2F2ZSIsInNlcmllc1RpdGxlIiwic2VyaWVzQ292ZXJJbWFnZSIsImdldFNlbGVjdGVkQ291cnNlIiwiY291cnNlRGV0YWlsIiwiY291cnNlTGlzdCIsIm5vdGlmaWNhdGlvbiIsInNldENvdXJzZUxpc3QiLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsInJpZ2h0UGFuZWxUeXBlIiwic2V0UmlnaHRQYW5lbFR5cGUiLCJzZWxlY3RlZENvdXJzZUlkIiwic2V0U2VsZWN0ZWRDb3Vyc2VJZCIsInVwbG9hZGluZ0ZpbGVzIiwic2V0VXBsb2FkaW5nRmlsZXMiLCJTZXQiLCJpc1B1Ymxpc2hpbmciLCJzZXRJc1B1Ymxpc2hpbmciLCJpc0NyZWF0aW5nIiwic2V0SXNDcmVhdGluZyIsImlzUHVibGlzaGluZ1NlcmllcyIsInNldElzUHVibGlzaGluZ1NlcmllcyIsInNlcmllc1N0YXR1cyIsInNldFNlcmllc1N0YXR1cyIsImRlbGV0ZUNvbmZpcm1WaXNpYmxlIiwic2V0RGVsZXRlQ29uZmlybVZpc2libGUiLCJjb3Vyc2VUb0RlbGV0ZSIsInNldENvdXJzZVRvRGVsZXRlIiwiaXNEZWxldGluZyIsInNldElzRGVsZXRpbmciLCJpc1RlbXBsYXRlUGlja2VyT3BlbiIsInNldElzVGVtcGxhdGVQaWNrZXJPcGVuIiwiZWRpdGluZ1RpdGxlIiwic2V0RWRpdGluZ1RpdGxlIiwiY291cnNlR29hbHMiLCJzZXRDb3Vyc2VHb2FscyIsImNvdXJzZU9iamVjdGl2ZXMiLCJzZXRDb3Vyc2VPYmplY3RpdmVzIiwicHJvamVjdE1lbWJlcnMiLCJzZXRQcm9qZWN0TWVtYmVycyIsIndvcmtzIiwic2V0V29ya3MiLCJsb2FkaW5nV29ya3MiLCJzZXRMb2FkaW5nV29ya3MiLCJzZWxlY3RlZFdvcmtJZHMiLCJzZXRTZWxlY3RlZFdvcmtJZHMiLCJjdXJyZW50UGFnZSIsInNldEN1cnJlbnRQYWdlIiwiaGFzTW9yZVdvcmtzIiwic2V0SGFzTW9yZVdvcmtzIiwibG9hZGluZ01vcmVXb3JrcyIsInNldExvYWRpbmdNb3JlV29ya3MiLCJjb3Vyc2VUYWdzIiwic2V0Q291cnNlVGFncyIsInNlbGVjdGVkVGFncyIsInNldFNlbGVjdGVkVGFncyIsInRhZ3NMb2FkaW5nIiwic2V0VGFnc0xvYWRpbmciLCJzZXRDb3Vyc2VEZXRhaWwiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiY292ZXJJbWFnZSIsInZpZGVvVXJsIiwidmlkZW9OYW1lIiwiaXNWaWRlb0VuYWJsZWQiLCJhdHRhY2htZW50VXJsIiwiYXR0YWNobWVudE5hbWUiLCJpc0F0dGFjaG1lbnRFbmFibGVkIiwidGVhY2hpbmdNYXRlcmlhbHMiLCJ0ZWFjaGluZ0luZm8iLCJjb250ZW50Q29uZmlnIiwiaGFzVmlkZW8iLCJoYXNEb2N1bWVudCIsImhhc0F1ZGlvIiwidmlkZW8iLCJ1cmwiLCJuYW1lIiwiZG9jdW1lbnQiLCJhdWRpbyIsImNvdXJzZUNvbnRlbnQiLCJ0b3BpYyIsImNvbnRlbnQiLCJpc09uZUtleU9wZW4iLCJpc0Rpc3RyaWJ1dGlvbkVuYWJsZWQiLCJkaXN0cmlidXRpb25SZXdhcmQiLCJzZWxlY3RlZFRlbXBsYXRlIiwiaXNEaXN0cmlidXRpb25XYXRlciIsInJlcXVpcmVkRW5lcmd5IiwiZW5lcmd5QW1vdW50IiwiaXNEaXN0cmlidXRpb25MaW1pdCIsImRpc3RyaWJ1dGlvbkNvbmRpdGlvbnMiLCJpbnZpdGVDb3VudCIsInRhc2tDb3VudCIsImV4cGVyaWVuY2UiLCJpc0Rpc3RyaWJ1dGlvblRpbWUiLCJkaXN0cmlidXRpb25UaW1lQ29uZGl0aW9ucyIsInN0YXJ0VGltZSIsImVuZFRpbWUiLCJkaXN0cmlidXRpb25NYXRlcmlhbHMiLCJ0YXNrQ29uZmlnIiwidGFza05hbWUiLCJ0YXNrRHVyYXRpb24iLCJ0YXNrRGVzY3JpcHRpb24iLCJzZWxmQXNzZXNzbWVudEl0ZW1zIiwicmVmZXJlbmNlV29ya3MiLCJyZWZlcmVuY2VSZXNvdXJjZXMiLCJ0b2tlbiIsImxvY2FsU3RvcmFnZSIsImdldEl0ZW0iLCJsb2FkQ291cnNlTGlzdCIsImxvYWRDb3Vyc2VUYWdzIiwibG9hZFNlcmllc0RldGFpbCIsImxvYWRVc2VyV29ya3MiLCJjb2RlIiwiY291cnNlcyIsImxpc3QiLCJtYXAiLCJjIiwiaWQiLCJ0eXBlIiwidG90YWwiLCJsZW5ndGgiLCJkcmFmdCIsImZpbHRlciIsInB1Ymxpc2hlZCIsIm1lc3NhZ2UiLCJzZXJpZXNEYXRhIiwidXNlcklkIiwiZ2V0VGVhY2hlcldvcmtzIiwiTnVtYmVyIiwid29ya3NMaXN0IiwiQXJyYXkiLCJpc0FycmF5IiwiaGFuZGxlV29ya1NlbGVjdCIsIndvcmtJZCIsImluY2x1ZGVzIiwicHJldiIsImhhbmRsZVdvcmtzV2hlZWwiLCJlIiwicHJldmVudERlZmF1bHQiLCJjb250YWluZXIiLCJjdXJyZW50VGFyZ2V0Iiwic2Nyb2xsQW1vdW50IiwiZGVsdGFZIiwic2Nyb2xsTGVmdCIsInRhZ3MiLCJmb3JFYWNoIiwidGFnIiwiaW5kZXgiLCJoYXNUYWciLCJpZFR5cGUiLCJuYW1lVHlwZSIsIm5hbWVWYWx1ZSIsIm5hbWVOb3RFbXB0eSIsInRyaW0iLCJ2YWxpZFRhZ3MiLCJpc1ZhbGlkIiwid2FybiIsInN0YXR1c1RleHQiLCJhZGROZXdDb3Vyc2UiLCJuZXdDb3Vyc2UiLCJEYXRlIiwibm93Iiwib3JkZXJJbmRleCIsInN0YXR1c0xhYmVsIiwidmlkZW9EdXJhdGlvbiIsInZpZGVvRHVyYXRpb25MYWJlbCIsImZpcnN0VGVhY2hpbmdUaXRsZSIsInJlc291cmNlc0NvdW50IiwiY3JlYXRlZEF0IiwidG9JU09TdHJpbmciLCJ1cGRhdGVkQXQiLCJzaG93Q291cnNlUGFuZWwiLCJzaG93RGVsZXRlQ29uZmlybSIsImNvbmZpcm1EZWxldGVDb3Vyc2UiLCJkZWxldGVDb3Vyc2UiLCJjb3Vyc2UiLCJzdWNjZXNzIiwiY2FuY2VsRGVsZXRlIiwidXBkYXRlQ291cnNlVGl0bGUiLCJuZXdUaXRsZSIsImhhbmRsZUNvdmVyVXBsb2FkIiwiZXZlbnQiLCJmaWxlIiwidGFyZ2V0IiwiZmlsZXMiLCJhbGxvd2VkVHlwZXMiLCJzaXplIiwiYWRkIiwicHJldmlld1VybCIsIlVSTCIsImNyZWF0ZU9iamVjdFVSTCIsImltYWdlVXJsIiwidXBsb2FkVG9Pc3MiLCJhbGVydCIsIm5ld1NldCIsImRlbGV0ZSIsImhhbmRsZVZpZGVvVXBsb2FkIiwidHJpZ2dlclZpZGVvVXBsb2FkIiwiaW5wdXQiLCJjcmVhdGVFbGVtZW50IiwiYWNjZXB0Iiwib25jaGFuZ2UiLCJjbGljayIsImhhbmRsZUF0dGFjaG1lbnRVcGxvYWQiLCJkb2N1bWVudFVybCIsInRyaWdnZXJBdHRhY2htZW50VXBsb2FkIiwiaGFuZGxlVGVhY2hpbmdNYXRlcmlhbFVwbG9hZCIsIm1hdGVyaWFsVXJsIiwibmV3TWF0ZXJpYWwiLCJmcm9tIiwiaXRlbSIsInN0YXJ0c1dpdGgiLCJ0cmlnZ2VyVGVhY2hpbmdNYXRlcmlhbFVwbG9hZCIsInJlbW92ZVRlYWNoaW5nTWF0ZXJpYWwiLCJfIiwiaSIsImFkZFRlYWNoaW5nSW5mb0l0ZW0iLCJyZW1vdmVUZWFjaGluZ0luZm9JdGVtIiwidXBkYXRlVGVhY2hpbmdJbmZvVGl0bGUiLCJuZXdUZWFjaGluZ0luZm8iLCJ1cGRhdGVUZWFjaGluZ0luZm9Db250ZW50IiwiY29udmVydFRlYWNoaW5nSW5mb0ZvckFQSSIsImluZm8iLCJzcGxpdCIsImxpbmUiLCJjaGVja0ZvckJsb2JVcmxzIiwiaXNzdWVzIiwicHVzaCIsImFkZGl0aW9uYWxSZXNvdXJjZXMiLCJyZXNvdXJjZSIsInNhdmVDb3Vyc2UiLCJFcnJvciIsImJsb2JJc3N1ZXMiLCJqb2luIiwiY291cnNlRGF0YSIsImNyZWF0ZUNvdXJzZSIsInVwZGF0ZUNvdXJzZSIsImhhbmRsZVB1Ymxpc2hDb3Vyc2UiLCJzZWxlY3RlZENvdXJzZSIsImZpbmQiLCJjb3Vyc2VJZFRvUHVibGlzaCIsInB1Ymxpc2hDb3Vyc2UiLCJoYW5kbGVTYXZlIiwidXBkYXRlZENvdXJzZSIsIm1hdGVyaWFsIiwiaGFuZGxlUHVibGlzaCIsInB1Ymxpc2hDb3Vyc2VTZXJpZXMiLCJwdWJsaXNoRGF0YSIsInN1Y2Nlc3NNZXNzYWdlIiwicHVibGlzaFN0YXRzIiwic3RhdHMiLCJ0b3RhbENvdXJzZXMiLCJwdWJsaXNoZWRDb3Vyc2VzIiwidmlkZW9Db3Vyc2VDb3VudCIsImRvY3VtZW50Q291cnNlQ291bnQiLCJ0b3RhbFJlc291cmNlc0NvdW50IiwidG90YWxWaWRlb0R1cmF0aW9uIiwiZHVyYXRpb25NaW51dGVzIiwiTWF0aCIsInJvdW5kIiwiZXJyb3JNZXNzYWdlIiwiaGFuZGxlRXhpdEVkaXQiLCJzaG93U2V0dGluZ3NQYW5lbCIsImhhbmRsZVRlbXBsYXRlU2VsZWN0IiwidGVtcGxhdGUiLCJ0ZW1wbGF0ZU5hbWUiLCJoYW5kbGVPcGVuVGVtcGxhdGVQaWNrZXIiLCJyZXMiLCJjb3Vyc2VEZXRhaWxEYXRhIiwibWFwcGVkVGVhY2hpbmdJbmZvIiwiZGl2IiwiY2xhc3NOYW1lIiwiaDIiLCJidXR0b24iLCJvbkNsaWNrIiwicCIsImgzIiwic3BhbiIsInN0b3BQcm9wYWdhdGlvbiIsImltZyIsInNyYyIsImFsdCIsImxhYmVsIiwidmFsdWUiLCJvbkNoYW5nZSIsInBsYWNlaG9sZGVyIiwibW9kZSIsInN0eWxlIiwid2lkdGgiLCJvcHRpb25zIiwiY29sb3IiLCJmb250U2l6ZSIsIm1hcmdpblRvcCIsImdldEVsZW1lbnRCeUlkIiwiZGlzcGxheSIsInRleHRhcmVhIiwicm93cyIsImNoZWNrZWQiLCJjb250cm9scyIsInBvc3RlciIsInNvdXJjZSIsIndpbmRvdyIsIm9wZW4iLCJjdXJzb3IiLCJ0ZXh0RGVjb3JhdGlvbiIsIm5ld0l0ZW1zIiwibWluSGVpZ2h0IiwidXNlclNlbGVjdCIsIm9uV2hlZWwiLCJ3b3JrIiwic2NyZWVuU2hvdEltYWdlIiwib25FcnJvciIsIm5leHRFbGVtZW50IiwibmV4dEVsZW1lbnRTaWJsaW5nIiwid29ya05hbWUiLCJuZXdSZXNvdXJjZXMiLCJkaXNhYmxlZCIsInNvbWUiLCJpc09wZW4iLCJvblRlbXBsYXRlU2VsZWN0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/workbench/components/CourseListEditModal.tsx\n"));

/***/ })

});