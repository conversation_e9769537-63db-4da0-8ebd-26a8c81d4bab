import request from '../request';
import { Encrypted } from 'logic-common';
import { API_URL } from '@/config/config';

// 套餐购买API基础路径
const PACKAGE_ORDER_BASE_URL = '/api/v1/package-order';

// 创建加密服务实例
const encryptionService = Encrypted.create(API_URL);

// 支付渠道枚举
export type PaymentChannel = 'alipay' | 'wechatpay';

// 支付方式枚举  
export type PaymentMode = 'redirect' | 'qrcode';

// 套餐信息接口
export interface PackageInfo {
  packageId: number;
  packageName: string;
  packageDescription: string;
  points: number;
  validityDays: number;
  originalPrice: number;
  currentPrice: number;
  discountRate: number;
  savings: number;
  currency: string;
  promotion?: Record<string, any>;
}

// 购买套餐请求接口
export interface PurchasePackageRequest {
  packageId: number;
  channel: PaymentChannel;
  paymentMode?: PaymentMode;
  clientIp?: string;
  returnUrl?: string;
}

// 购买套餐响应接口
export interface PurchasePackageResponse {
  code: number;
  message: string;
  data: {
    orderNo: string;
    paymentUrl?: string;
    qrCode?: string;
    amount: number;
    packageName: string;
    expireTime?: string;
  } | null;
}

// 订单状态响应接口
export interface OrderStatusResponse {
  code: number;
  message: string;
  data: {
    orderNo: string;
    status: 'pending' | 'paid' | 'cancelled';
    packageName: string;
    amount: number;
    createTime: string;
    paidTime?: string;
  } | null;
}

// 订单列表响应接口
export interface OrderListResponse {
  code: number;
  message: string;
  data: {
    data: Array<{
      orderNo: string;
      packageName: string;
      amount: number;
      status: string;
      createTime: string;
      paidTime?: string;
    }>;
    total: number;
    page: number;
    limit: number;
  } | null;
}

/**
 * 获取可用套餐列表
 */
export const getAvailablePackages = async (): Promise<PackageInfo[]> => {
  try {
    // 动态获取加密请求头
    const headers = await encryptionService.session.getEncryptionHeaders();

    const response = await encryptionService.request.getEncrypted(`${PACKAGE_ORDER_BASE_URL}/packages`, {
      headers
    });

    console.log('套餐列表API响应:', response.data);

    // 处理加密响应
    const apiResponse = response.data;
    if (apiResponse && apiResponse.code === 200) {
      console.log('后端返回的原始套餐数据:', JSON.stringify(apiResponse.data, null, 2));
      return apiResponse.data || [];
    } else {
      throw new Error(apiResponse?.message || '获取套餐列表失败');
    }
  } catch (error) {
    console.error('获取套餐列表失败:', error);
    throw new Error('获取套餐列表失败');
  }
};

/**
 * 购买套餐
 */
export const purchasePackage = async (purchaseData: PurchasePackageRequest): Promise<PurchasePackageResponse['data']> => {
  try {
    // 动态获取安全会话请求头（用于敏感的购买操作）
    const secureHeaders = await encryptionService.session.getSecureEncryptionHeaders();

    const response = await encryptionService.request.postEncryptedWithBody(
      `${PACKAGE_ORDER_BASE_URL}/purchase`,
      purchaseData,
      { headers: secureHeaders }
    );

    console.log('购买套餐API响应:', response.data);

    // 处理加密响应
    const apiResponse = response.data;
    if (apiResponse && apiResponse.code === 200) {
      return apiResponse.data;
    } else {
      throw new Error(apiResponse?.message || '购买套餐失败');
    }
  } catch (error) {
    console.error('购买套餐失败:', error);
    throw new Error('购买套餐失败');
  }
};

/**
 * 查询订单状态
 */
export const getOrderStatus = async (orderNo: string): Promise<OrderStatusResponse['data']> => {
  try {
    // 动态获取加密请求头
    const headers = await encryptionService.session.getEncryptionHeaders();

    const response = await encryptionService.request.getEncrypted(
      `${PACKAGE_ORDER_BASE_URL}/order-status/${orderNo}`,
      { headers }
    );

    console.log('订单状态API响应:', response.data);

    // 处理加密响应
    const apiResponse = response.data;
    if (apiResponse && apiResponse.code === 200) {
      return apiResponse.data;
    } else {
      throw new Error(apiResponse?.message || '查询订单状态失败');
    }
  } catch (error) {
    console.error('查询订单状态失败:', error);
    throw new Error('查询订单状态失败');
  }
};

/**
 * 获取我的订单列表
 */
export const getMyOrders = async (page: number = 1, limit: number = 10): Promise<OrderListResponse['data']> => {
  try {
    // 动态获取加密请求头
    const headers = await encryptionService.session.getEncryptionHeaders();

    const response = await encryptionService.request.getEncrypted(
      `${PACKAGE_ORDER_BASE_URL}/my-orders?page=${page}&limit=${limit}`,
      { headers }
    );

    console.log('我的订单API响应:', response.data);

    // 处理加密响应
    const apiResponse = response.data;
    if (apiResponse && apiResponse.code === 200) {
      return apiResponse.data;
    } else {
      throw new Error(apiResponse?.message || '获取订单列表失败');
    }
  } catch (error) {
    console.error('获取订单列表失败:', error);
    throw new Error('获取订单列表失败');
  }
};

/**
 * 取消订单
 */
export const cancelOrder = async (orderNo: string): Promise<void> => {
  try {
    // 动态获取安全会话请求头（用于敏感的取消订单操作）
    const secureHeaders = await encryptionService.session.getSecureEncryptionHeaders();

    const response = await encryptionService.request.postEncryptedWithBody(
      `${PACKAGE_ORDER_BASE_URL}/cancel/${orderNo}`,
      {},
      { headers: secureHeaders }
    );

    console.log('取消订单API响应:', response.data);

    // 处理加密响应
    const apiResponse = response.data;
    if (apiResponse && apiResponse.code !== 200) {
      throw new Error(apiResponse?.message || '取消订单失败');
    }
  } catch (error) {
    console.error('取消订单失败:', error);
    throw new Error('取消订单失败');
  }
};

/**
 * 获取订单详情
 */
export const getOrderDetail = async (orderNo: string): Promise<any> => {
  try {
    // 动态获取加密请求头
    const headers = await encryptionService.session.getEncryptionHeaders();

    const response = await encryptionService.request.getEncrypted(
      `${PACKAGE_ORDER_BASE_URL}/order/${orderNo}`,
      { headers }
    );

    console.log('订单详情API响应:', response.data);

    // 处理加密响应
    const apiResponse = response.data;
    if (apiResponse && apiResponse.code === 200) {
      return apiResponse.data;
    } else {
      throw new Error(apiResponse?.message || '获取订单详情失败');
    }
  } catch (error) {
    console.error('获取订单详情失败:', error);
    throw new Error('获取订单详情失败');
  }
};
