'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { LayoutDashboard, Users, ListTodo, Briefcase, Book, Settings, ArrowLeftCircle, Shield, ChevronDown } from 'lucide-react';
import Image from 'next/image';
import { UserInfo } from '@/lib/api/user';
import { getTeacherSchools } from '@/lib/api/school';
import { classApi } from '@/lib/api/class';
import { formatSchoolAddress } from '@/lib/utils/address';

interface School {
  id: number;
  schoolName: string;
  province?: string;
  city?: string;
  district?: string;
  createTime?: string;
  updateTime?: string;
}

interface ClassInfo {
  id: number;
  schoolId: number;
  grade: string;
  className: string;
  teacherId: number;
  assistantTeacherId: number;
  inviteCode: string;
  createTime: string;
  updateTime: string;
  studentCount: number;
  isAssistant?: boolean;
}

interface LeftSidebarProps {
  userInfo: Partial<UserInfo>;
  onMenuItemClick?: (itemName: string) => void;
  onSchoolSelect?: (school: School) => void;
  onClassesUpdate?: (classes: ClassInfo[], loading: boolean, error: string | null) => void;
}

const LeftSidebar = ({ userInfo, onMenuItemClick, onSchoolSelect, onClassesUpdate }: LeftSidebarProps) => {
  const [activeItem, setActiveItem] = useState('快速开始');
  const [isClassDropdownOpen, setIsClassDropdownOpen] = useState(false);


  const [schools, setSchools] = useState<School[]>([]);
  const [selectedSchool, setSelectedSchool] = useState<School | null>(null);
  const [schoolsLoading, setSchoolsLoading] = useState(false);
  const [schoolsError, setSchoolsError] = useState<string | null>(null);

  // 监听activeItem变化，当进入班级管理页面时自动打开下拉菜单
  useEffect(() => {
    console.log('activeItem 状态变化:', activeItem);

    // 当切换到班级管理页面时，自动打开下拉菜单
    if (activeItem === '班级管理') {
      setIsClassDropdownOpen(true);
    }
  }, [activeItem]);

  // 下拉菜单的ref，用于检测点击外部区域
  const dropdownRef = useRef<HTMLLIElement>(null);

  // 标志位，防止导航点击和外部点击冲突
  const isNavigatingRef = useRef(false);

  // 点击外部区域关闭下拉菜单并切换到班级管理页面
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        console.log('点击外部区域，isNavigating:', isNavigatingRef.current);

        // 如果正在导航，不处理外部点击
        if (isNavigatingRef.current) {
          isNavigatingRef.current = false;
          return;
        }

        // 如果当前活跃项是班级管理，不关闭下拉菜单
        if (activeItem === '班级管理') {
          return;
        }

        // 关闭下拉菜单
        setIsClassDropdownOpen(false);

        // 如果有选中的学校，切换到班级管理页面
        if (selectedSchool) {
          setActiveItem('班级管理');
          onMenuItemClick?.('班级管理');
        }
      }
    };

    if (isClassDropdownOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isClassDropdownOpen, selectedSchool, onMenuItemClick, activeItem]);

  // 监听自定义事件来关闭下拉菜单
  useEffect(() => {
    const handleCloseDropdown = () => {
      setIsClassDropdownOpen(false);
    };

    document.addEventListener('closeDropdown', handleCloseDropdown);

    return () => {
      document.removeEventListener('closeDropdown', handleCloseDropdown);
    };
  }, []);

  // 获取教师管理的学校列表
  useEffect(() => {
    fetchSchools();
  }, []);





  const navItems = [
    { icon: LayoutDashboard, name: '快速开始', hasDivider: true },
    { icon: Users, name: '班级管理', hasDropdown: true },
    { icon: ListTodo, name: '班级任务' },
    { icon: Briefcase, name: '班级项目', hasDivider: true },
    { icon: Book, name: '课程管理' },
    { icon: Settings, name: '模板管理' },
  ];

  // 处理班级管理点击
  const handleClassManagementClick = () => {
    // 设置为活跃状态
    setActiveItem('班级管理');

    // 如果没有选中学校且有可用学校，自动选择第一个学校
    if (!selectedSchool && schools.length > 0) {
      const firstSchool = schools[0];
      setSelectedSchool(firstSchool);
      onSchoolSelect?.(firstSchool);
      fetchClasses(firstSchool.id);
      console.log('班级管理：自动选择第一个学校:', firstSchool);
    } else if (!selectedSchool && schools.length === 0 && !schoolsLoading) {
      // 如果没有学校数据且不在加载中，重新获取学校列表
      console.log('班级管理：没有学校数据，重新获取学校列表');
      fetchSchools();
    }

    // 如果当前已经是班级管理页面且下拉菜单已打开，则关闭；否则打开
    if (activeItem === '班级管理' && isClassDropdownOpen) {
      setIsClassDropdownOpen(false);
    } else {
      setIsClassDropdownOpen(true);
    }
    // 通知父组件
    onMenuItemClick?.('班级管理');
  };

  // 处理学校选择
  const handleSchoolSelect = useCallback((school: School) => {
    console.log('handleSchoolSelect 被调用，当前activeItem:', activeItem);

    // 不关闭下拉菜单，只更新选中状态
    setSelectedSchool(school);

    // 强制切换到班级管理页面（无论当前在什么页面）
    setActiveItem('班级管理');
    onMenuItemClick?.('班级管理');

    // 始终通知父组件学校选择变化（用于数据更新）
    onSchoolSelect?.(school);

    // 获取该学校的班级列表
    fetchClasses(school.id);
  }, [onMenuItemClick, onSchoolSelect]);

  // 处理返回主页
  const handleBackToHome = () => {
    console.log('点击返回主页按钮');

    // 获取当前域名和端口，然后跳转到home页面
    const currentOrigin = window.location.origin;
    const homeUrl = `${currentOrigin}/home`;
    console.log('当前域名:', currentOrigin);
    console.log('跳转到:', homeUrl);

    // 直接跳转到home页面
    window.location.href = homeUrl;
  };

  // 获取学校列表
  const fetchSchools = async () => {
    setSchoolsLoading(true);
    setSchoolsError(null);

    try {
      const response = await getTeacherSchools();
      console.log('获取学校列表API响应:', response);

      // 检查多种可能的响应格式
      let schoolList = [];
      if (response.data?.code === 200 && response.data?.data) {
        schoolList = response.data.data;
      } else if (response.data?.status === 200 && response.data?.data) {
        schoolList = response.data.data;
      } else if (Array.isArray(response.data)) {
        schoolList = response.data;
      }

      if (schoolList.length > 0) {
        setSchools(schoolList);
        const firstSchool = schoolList[0];
        setSelectedSchool(firstSchool);

        // 通知父组件学校选择变化
        onSchoolSelect?.(firstSchool);

        // 获取第一个学校的班级列表
        fetchClasses(firstSchool.id);

        console.log('成功获取学校列表，数量:', schoolList.length);
        console.log('自动选择第一个学校:', firstSchool);
      } else {
        setSchoolsError('暂无数据');
      }
    } catch (error) {
      console.error('获取学校列表失败:', error);
      setSchoolsError('请检查网络连接失败');
    } finally {
      setSchoolsLoading(false);
    }
  };

  // 获取指定学校的班级列表
  const fetchClasses = async (schoolId: number) => {
    if (!userInfo?.id) {
      console.log('用户未登录，无法获取班级列表');
      return;
    }

    // 通知父组件开始加载
    onClassesUpdate && onClassesUpdate([], true, null);

    try {
      console.log('获取班级列表:', { schoolId, teacherId: userInfo.id });
      const response = await classApi.getTeacherClasses(schoolId, userInfo.id);
      console.log('班级列表API响应:', response);

      if (response.data?.code === 200) {
        const classList = response.data.data || [];
        // 通知父组件数据更新
        onClassesUpdate && onClassesUpdate(classList, false, null);
        console.log('成功获取班级列表，数量:', classList.length);
      } else {
        const errorMsg = '获取班级列表失败';
        // 通知父组件错误状态
        onClassesUpdate && onClassesUpdate([], false, errorMsg);
      }
    } catch (error) {
      console.error('获取班级列表失败:', error);
      const errorMsg = '请检查网络连接';
      // 通知父组件错误状态
      onClassesUpdate && onClassesUpdate([], false, errorMsg);
    }
  };

  return (
    <aside className="left-sidebar">
      <div className="sidebar-header">
        <Shield />
        <span className="text-lg font-bold">教师空间</span>
      </div>

      <div className="teacher-info">
        <Image
          src={userInfo.avatarUrl || "/images/xiaoluo-default.webp"}
          alt={userInfo.nickName || "小洛头像"}
          width={40}
          height={40}
          className="avatar"
          style={{ backgroundColor: 'white' }}
        />
        <div className="teacher-details">
          <p className="teacher-name">{userInfo.nickName || '未登录'}</p>
          <p className="teacher-title">教师</p>
        </div>
      </div>
      <nav className="sidebar-nav">
        <ul>
          {navItems.map((item) => (
            <React.Fragment key={item.name}>
              {item.hasDropdown ? (
                <li className="nav-item-dropdown" ref={dropdownRef}>
                  <div
                    className={`nav-item ${activeItem === item.name ? 'active' : ''} ${isClassDropdownOpen ? 'dropdown-open' : ''}`}
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      handleClassManagementClick();
                    }}
                  >
                    <item.icon className="nav-icon" />
                    <span>{item.name}</span>
                    <ChevronDown
                      className={`dropdown-arrow ${isClassDropdownOpen ? 'rotated' : ''}`}
                      size={16}
                      style={{
                        transform: isClassDropdownOpen ? 'rotate(180deg)' : 'rotate(0deg)',
                        transition: 'transform 0.3s ease'
                      }}
                    />
                  </div>
                  {isClassDropdownOpen && (
                    <div
                      className={`dropdown-menu ${(schoolsLoading || schoolsError || schools.length === 0) ? 'empty' : ''}`}
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                      }}
                    >
                      {schoolsLoading ? (
                        <div className="dropdown-item disabled loading">
                          <div className="loading-spinner"></div>
                          正在加载学校信息...
                        </div>
                      ) : schoolsError ? (
                        <div className="dropdown-item disabled error">
                          {schoolsError}
                        </div>
                      ) : schools.length > 0 ? (
                        schools.map((school) => (
                          <div
                            key={school.id}
                            className={`dropdown-item ${selectedSchool?.id === school.id ? 'selected' : ''}`}
                            onClick={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              handleSchoolSelect(school);
                            }}
                          >
                            <div className="school-info">
                              <div className="school-name">{school.schoolName}</div>
                              {school.province && school.district && (
                                <div className="school-location">
                                  {formatSchoolAddress(school.province, school.city, school.district)}
                                </div>
                              )}
                            </div>
                          </div>
                        ))
                      ) : (
                        <div className="dropdown-item disabled no-data">
                          暂无数据
                        </div>
                      )}
                    </div>
                  )}
                </li>
              ) : (
                <li
                  className={`nav-item ${activeItem === item.name ? 'active' : ''}`}
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('点击导航项:', item.name);
                    console.log('当前下拉菜单状态:', isClassDropdownOpen);
                    console.log('当前活跃项:', activeItem);

                    // 设置导航标志，防止外部点击干扰
                    isNavigatingRef.current = true;

                    // 先关闭下拉菜单
                    setIsClassDropdownOpen(false);

                    // 然后更新活跃项
                    setActiveItem(item.name);

                    // 最后通知父组件
                    onMenuItemClick?.(item.name);

                    console.log('完成设置 - 活跃项:', item.name, '下拉菜单已关闭');

                    // 延迟重置标志位
                    setTimeout(() => {
                      isNavigatingRef.current = false;
                    }, 100);
                  }}
                >
                  <item.icon className="nav-icon" />
                  <span>{item.name}</span>
                </li>
              )}
              {item.hasDivider && <div className="nav-divider"></div>}
            </React.Fragment>
          ))}
        </ul>
      </nav>
      <div className="sidebar-footer" onClick={handleBackToHome}>
        <ArrowLeftCircle className="nav-icon" />
        <span>返回主页</span>
      </div>
    </aside>
  );
};

export default LeftSidebar; 