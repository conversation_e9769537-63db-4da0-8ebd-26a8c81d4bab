/* 定义CSS变量 */
:root {
  /* 黑色主题 */
  --primary-black: black;
  --primary-black-lighter: black;
  --primary-black-focus: rgbablack;
  
  /* 更新边框变量 */
  /* --select-border-color: var(--primary-black-lighter); */
  --select-border-color: var(--primary-black);
  --select-border-width: 1px;
  --select-border-style: solid;
  --select-border: var(--select-border-width) var(--select-border-style) var(--select-border-color);
  --select-border-radius: 0.75rem;
  
  /* 更新焦点阴影变量 */
  --select-focus-shadow-color: rgba(0, 0, 0, 0.3);
  --select-focus-shadow: 0 0 0 2px var(--select-focus-shadow-color);
  
  /* 错误状态变量保持不变 */
  --select-error-border-color: #ef4444;
  --select-error-shadow-color: rgba(239, 68, 68, 0.2);
  --select-error-shadow: 0 0 0 2px var(--select-error-shadow-color);
  
  /* 添加placeholder颜色变量 */
  --placeholder-color: var(--primary-black-lighter);
  
  /* 添加focus状态下的文本颜色变量 */
  --focus-text-color: #9a9a9a;
}

/* 确保Select组件中的文字垂直居中 */
.student-login-select .ant-select-selector {
  display: flex !important;
  align-items: center !important; /* 垂直居中 */
  padding-top: 0 !important;
  padding-bottom: 0 !important;
  padding-left: 16px !important;
  background-color: rgba(255, 255, 255, 0.7) !important; /* 更浅的背景 */
  border: var(--select-border) !important; /* 使用变量 */
  border-radius: var(--select-border-radius) !important; /* 使用变量 */
  cursor: text !important; /* 确保鼠标样式为文本输入 */
}

.student-login-select .ant-select-selection-search {
  display: flex !important;
  align-items: center !important; /* 垂直居中 */
  width: 100% !important;
  height: 100% !important;
  color: var(--primary-black) !important; /* 默认颜色使用黑色主题 */
}

.student-login-select .ant-select-selection-search-input {
  height: 100% !important;
  color: var(--primary-black) !important; /* 默认颜色使用黑色主题 */
}

.student-login-select .ant-select-selection-item {
  display: flex !important;
  align-items: center !important; /* 垂直居中 */
  height: 100% !important;
  line-height: normal !important;
  color: var(--primary-black) !important; /* 默认颜色使用黑色主题 */
  width: 100% !important;
  position: absolute !important;
  left: 0 !important;
  right: 0 !important;
}

/* 焦点状态下的文本颜色 */
.student-login-select.ant-select-focused .ant-select-selection-item {
  color: var(--focus-text-color) !important; /* 焦点状态下文本变为灰色 */
}

.student-login-select .ant-select-selection-placeholder {
  display: flex !important;
  align-items: center !important;
  height: 100% !important;
  opacity: 1 !important; /* 修改为1，让placeholder可见 */
  color: var(--placeholder-color) !important; /* 使用黑色主题 */
}

/* 确保选择框获得焦点时的样式 */
.student-login-select .ant-select-focused .ant-select-selector {
  border-color: var(--primary-black-focus) !important; /* 使用黑色主题 */
  box-shadow: var(--select-focus-shadow) !important; /* 使用变量 */
}

/* 确保下拉框箭头位置正确 */
.student-login-select .ant-select-arrow {
  color: var(--primary-black) !important; /* 使用黑色主题 */
  right: 11px !important;
}

/* 清除按钮样式 */
.student-login-select .ant-select-clear {
  right: 30px !important;
  margin-top: -8px !important;
  z-index: 20 !important;
  background-color: rgba(255, 255, 255, 0.8) !important;
}

/* 强制所有内容居中 */
.text-center {
  text-align: center !important;
}
.text-center * {
  text-align: center !important;
}

/* 确保输入框标签样式正确 */
.student-login-select + label {
  /* 基本样式 */
  display: block !important;
  pointer-events: none !important;
  z-index: 10 !important;
}

/* 未选中状态的标签 - 强制垂直居中 */
.student-login-select + label:not([class*='top-0']) {
  position: absolute !important;
  display: flex !important;
  align-items: center !important; 
  justify-content: flex-start !important;
  height: 100% !important;
  top: 0 !important;
  line-height: normal !important;
  margin: 0 !important;
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}

/* 对登录表单中所有未选中的标签应用垂直居中 */
.flex-col .relative label:not([class*='top-0']) {
  display: flex !important;
  align-items: center !important;
  height: 100% !important;
}

/* 确保下拉菜单中的项目也正确垂直居中 */
.student-login-select-dropdown .ant-select-item {
  display: flex !important;
  align-items: center !important;
  min-height: 32px !important;
  text-align: center !important;
  color: var(--primary-black) !important; /* 使用黑色主题 */
}

/* 确保下拉菜单中的选中项目和悬停项目有正确的样式 */
.student-login-select-dropdown .ant-select-item-option-selected {
  background-color: rgba(0, 0, 0, 0.1) !important; /* 黑色背景，低透明度 */
  color: var(--primary-black) !important; /* 使用黑色主题 */
}

.student-login-select-dropdown .ant-select-item-option-active {
  background-color: rgba(0, 0, 0, 0.05) !important; /* 黑色背景，低透明度 */
}

/* 修改下拉菜单的样式 */
.student-login-select-dropdown {
  background-color: rgba(255, 255, 255, 0.95) !important; /* 白色背景 */
  border: var(--select-border) !important; /* 使用变量 */
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important; /* 轻微阴影 */
}

/* 修改错误状态的样式 */
.student-login-select.has-error .ant-select-selector {
  border-color: var(--select-error-border-color) !important; /* 使用变量 */
  box-shadow: var(--select-error-shadow) !important; /* 使用变量 */
}

/* 添加全局输入框样式 */
input[type="text"],
input[type="tel"],
input[type="password"] {
  border-color: var(--select-border-color) !important;
  color: var(--primary-black) !important;
}

input[type="text"]:focus,
input[type="tel"]:focus,
input[type="password"]:focus {
  border-color: var(--primary-black-focus) !important;
  box-shadow: var(--select-focus-shadow) !important;
}

/* 添加全局placeholder样式 */
::placeholder {
  color: var(--placeholder-color) !important;
  opacity: 1 !important;
}

/* 设置标签文字颜色 */
.student-login-select + label,
.flex-col .relative label {
  color: var(--primary-black) !important;
}

/* 设置浮动标签样式 */
#phone-verify::placeholder-shown + label,
#code::placeholder-shown + label,
#phone-password::placeholder-shown + label,
#password::placeholder-shown + label {
  color: var(--placeholder-color) !important;
} 