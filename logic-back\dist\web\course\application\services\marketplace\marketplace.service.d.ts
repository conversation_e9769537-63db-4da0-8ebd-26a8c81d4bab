import { Repository } from 'typeorm';
import { CourseSeries } from '../../../domain/entities/management/course-series.entity';
import { Course } from '../../../domain/entities/management/course.entity';
import { CourseTag } from '../../../domain/entities/marketplace/course-tag.entity';
import { CourseSeriesTag } from '../../../domain/entities/marketplace/course-series-tag.entity';
import { GetSeriesListQueryDto, SeriesListDataDto } from '../../../application/dto/marketplace/series-list.dto';
import { SeriesDetailDataDto } from '../../../application/dto/marketplace/series-detail.dto';
import { CourseDetailDataDto } from '../../../application/dto/marketplace/course-detail.dto';
import { GetTagsListQueryDto, TagsListDataDto } from '../../../application/dto/marketplace/tags-list.dto';
import { CreateTagDto, UpdateTagDto, TagDetailDataDto } from '../../../application/dto/marketplace/tag-management.dto';
export declare class MarketplaceService {
    private readonly courseSeriesRepository;
    private readonly courseRepository;
    private readonly courseTagRepository;
    private readonly courseSeriesTagRepository;
    private readonly logger;
    constructor(courseSeriesRepository: Repository<CourseSeries>, courseRepository: Repository<Course>, courseTagRepository: Repository<CourseTag>, courseSeriesTagRepository: Repository<CourseSeriesTag>);
    private batchCalculateContentSummary;
    getSeriesList(query: GetSeriesListQueryDto): Promise<SeriesListDataDto>;
    getSeriesDetail(seriesId: number): Promise<SeriesDetailDataDto>;
    getCourseDetail(seriesId: number, courseId: number): Promise<CourseDetailDataDto>;
    getTagsList(query: GetTagsListQueryDto): Promise<TagsListDataDto>;
    createTag(createTagDto: CreateTagDto): Promise<TagDetailDataDto>;
    updateTag(id: number, updateTagDto: UpdateTagDto): Promise<TagDetailDataDto>;
    deleteTag(id: number): Promise<void>;
    getTagById(id: number): Promise<TagDetailDataDto>;
    private applyTagFilters;
    private applyFilters;
    private applySorting;
    private getSeriesTagsMap;
    private getFilterStats;
    private getCategoryLabel;
    private getStatusLabel;
    private getTagCategoryLabel;
    private getTagStatusLabel;
    private formatDuration;
}
