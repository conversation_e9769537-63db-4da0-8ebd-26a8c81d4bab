import React from 'react';
import { Tag } from 'antd';
import { ActivityStatus } from '@/lib/api/activity';

interface ActivityStatusTagProps {
  status: number;
}

const ActivityStatusTag: React.FC<ActivityStatusTagProps> = ({ status }) => {
  switch (status) {
    case ActivityStatus.DRAFT:
      return <Tag color="default">草稿</Tag>;
    case ActivityStatus.PUBLISHED:
      return <Tag color="green">已发布</Tag>;
    case ActivityStatus.ENDED:
      return <Tag color="orange">已结束</Tag>;
    case ActivityStatus.CANCELLED:
      return <Tag color="red">已取消</Tag>;
    case ActivityStatus.REVIEWING:
      return <Tag color="blue">审核中</Tag>;
    default:
      return <Tag color="default">未知</Tag>;
  }
};

export default ActivityStatusTag; 