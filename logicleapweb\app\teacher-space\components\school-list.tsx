/*
 * @Author: Zwww <EMAIL>
 * @Date: 2025-04-29 08:01:01
 * @LastEditors: Zwww <EMAIL>
 * @LastEditTime: 2025-05-09 07:16:17
 * @FilePath: \sourceCode\logicleapweb\app\teacher-space\components\school-list.tsx
 * @Description: 
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
 */
'use client'

import { useState } from 'react';
import { Card, Empty, Button, Modal, Spin } from 'antd';
import { InfoCircleOutlined, ReloadOutlined } from '@ant-design/icons';
import { RiBuilding4Line, RiSchoolLine, RiMapPinLine, RiUserLine, RiTeamLine, RiArrowRightLine, RiRefreshLine } from 'react-icons/ri';
import Image from 'next/image';
import request from '../../../lib/request';
import { isMunicipality } from '../utils/school';
import './school-list.css';
import { classApi } from '@/lib/api/class';
import { schoolApi } from '@/lib/api/school';
import { useSelector } from 'react-redux';
import { RootState } from '@/lib/store'; // 你的 store 类型


interface Class {
  id: number;
  grade: string;
  className: string;
  studentCount: number;
  teacherId?: number;
  assistantTeacherId?: number;
}

interface School {
  id: number;
  province: string;
  city: string;
  district: string;
  schoolName: string;
  stats?: {
    classCount: number;
    studentCount: number;
    teacherCount: number;
  };
  joinTime?: string;
}

interface SchoolListProps {
  schools: School[];
  onSchoolSelect: (school: School) => void;
  onRefreshSchools: () => void;
  loading: boolean;
}

// // 在文件顶部添加方法
// const fetchSchoolStats = async (schoolId: number) => {
//   try {
//     // 获取该学校下的所有班级
//     const classResponse = await classApi.getTeacherClassesSimple(schoolId);
//     let classCount = 0;
//     let studentCount = 0;
//     let teacherCount = 0;
//     let teacherSet = new Set<number>();  // 用Set来去重教师ID

//     if (classResponse.data.code === 200) {
//       const classes = classResponse.data.data;
//       classCount = classes.length;

//       // 统计所有班级的学生总数和教师数量
//       classes.forEach((classInfo: Class) => {
//         studentCount += (classInfo.studentCount || 0);
//         // 主教师
//         if (classInfo.teacherId) teacherSet.add(classInfo.teacherId);
//         // 协助教师
//         if (classInfo.assistantTeacherId) teacherSet.add(classInfo.assistantTeacherId);
//       });

//       teacherCount = teacherSet.size;  // 获取去重后的教师数量
//     }

//     return {
//       classCount,
//       studentCount,
//       teacherCount
//     };
//   } catch (error) {
//     console.error('获取学校统计数据失败:', error);
//     return {
//       classCount: 0,
//       studentCount: 0,
//       teacherCount: 0
//     };
//   }
// };

// 获取学校列表的方法
// const fetchSchools = async () => {
//   const userId = useSelector((state: RootState) => state.user.userState.userId);

//   try {
//     const response = await schoolApi.getUserSchools();
//     console.log('获取学校列表成功', response);

//     if (response.data.code === 200) {
//       const schoolsData = response.data.data;
//       // 获取每个学校的统计数据
//       const schoolsWithStats = await Promise.all(
//         schoolsData.map(async (school: School) => {
//           const stats = await fetchSchoolStats(school.id);
//           return {
//             ...school,
//             stats,
//             // 如果是直辖市，city 设置为与 province 相同
//             city: isMunicipality(school.province) ? school.province : school.city
//           };
//         })
//       );
//       return schoolsWithStats;
//     }
//     return [];
//   } catch (error) {
//     console.error('获取学校列表失败:', error);
//     return [];
//   }
// };

export default function SchoolList({
  schools,
  onSchoolSelect,
  onRefreshSchools,
  loading
}: SchoolListProps) {

  if (loading) {
    return (
      <div className="h-[calc(100vh-160px)] flex items-center justify-center">
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div className="h-[calc(100vh-160px)] flex flex-col">
      <div className="flex items-center justify-between mb-6 flex-none px-1">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2 text-gray-500 text-sm">
            <RiBuilding4Line className="text-lg" />
            <span>共管理</span>
            <span className="text-blue-500 font-medium text-lg">{schools.length}</span>
            <span>所学校</span>
          </div>
        </div>
        <Button
          type="default"
          icon={<RiRefreshLine />}
          className="rounded-full hover:bg-gray-50"
          onClick={onRefreshSchools}
        >
          刷新列表
        </Button>
      </div>

      <div className="flex-1 overflow-y-auto pr-1">
        {schools.length > 0 ? (
          <div className="space-y-4">
            {schools.map((school) => (
              <div
                key={school.id}
                className="school-card group w-full"
                onClick={() => onSchoolSelect(school)}
              >
                <div className="p-6">
                  <div className="flex flex-col h-full">
                    {/* 顶部区域：网格布局 */}
                    <div className="grid grid-cols-12 gap-6">
                      {/* 1. 学校基本信息区域 */}
                      <div className="col-span-12 lg:col-span-7">
                        <div className="flex items-center gap-6">
                          {/* 学校图标 */}
                          <div className="school-icon-container flex-shrink-0">
                            <RiSchoolLine className="text-[48px] text-blue-500" />
                          </div>

                          {/* 学校信息 */}
                          <div className="school-info flex-1 min-w-0">
                            <div className="flex flex-col justify-center">
                              <h3 className="text-xl font-semibold text-gray-800 mb-2 truncate">
                                {school.schoolName}
                              </h3>
                              <div className="flex items-center text-gray-500 text-sm">
                                <RiMapPinLine className="flex-shrink-0" />
                                <span className="truncate">
                                  {(() => {
                                    // 去掉省份后缀
                                    const provinceName = school.province.replace(/[省市]$/, '');
                                    const cityName = school.city?.replace(/[市]$/, '') || '';
                                    const districtName = school.district || '';

                                    if (isMunicipality(school.province)) {
                                      // 直辖市：只显示省份和区县
                                      return `${provinceName} ${districtName}`;
                                    } else {
                                      // 普通省份：显示省份、城市、区县
                                      return `${provinceName} ${cityName} ${districtName}`;
                                    }
                                  })()}
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* 2. 统计信息区域 */}
                      <div className="col-span-12 lg:col-span-5">
                        <div className="grid grid-cols-2 gap-4">
                          <div className="stat-card flex flex-col items-center p-3 bg-blue-50 rounded-lg">
                            <div className="text-2xl font-semibold text-blue-500 mb-1">
                              {school.stats?.studentCount || 0}
                            </div>
                            <div className="text-gray-600 text-sm whitespace-nowrap">
                              <RiUserLine className="mr-1 inline" />
                              在校学生
                            </div>
                          </div>
                          <div className="stat-card flex flex-col items-center p-3 bg-green-50 rounded-lg">
                            <div className="text-2xl font-semibold text-green-500 mb-1">
                              {school.stats?.classCount || 0}
                            </div>
                            <div className="text-gray-600 text-sm whitespace-nowrap">
                              <RiTeamLine className="mr-1 inline" />
                              管理班级
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* 底部操作区 */}
                    <div className="card-footer flex flex-col sm:flex-row items-center gap-4 sm:gap-0 justify-between mt-4 pt-4 border-t border-gray-100">
                      <div className="flex items-center text-sm text-gray-500 min-w-0">
                        <div className="flex items-center truncate">
                          <RiTeamLine className="mr-1 flex-shrink-0" />
                          <span className="truncate">
                            共 <span className="text-blue-500 font-medium">{school.stats?.teacherCount || 1}</span> 位老师管理
                          </span>
                        </div>
                      </div>
                      <Button
                        type="primary"
                        className="school-card-btn transition-all duration-300 hover:translate-x-1 flex-shrink-0 w-full sm:w-auto"
                      >
                        进入管理
                        <RiArrowRightLine className="ml-1 inline" />
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <Empty description="暂无管理的学校" />
        )}
      </div>
    </div>
  );
} 