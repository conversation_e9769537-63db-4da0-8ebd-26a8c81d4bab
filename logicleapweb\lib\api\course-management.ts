import { request } from './request';

// 开发配置
const DEV_CONFIG = {
  // 设置为true可以在开发环境中强制发送真实请求（即使API不存在也会在Network中看到请求）
  FORCE_REAL_REQUESTS: true,
  // 设置为true可以看到详细的调试日志
  ENABLE_DEBUG_LOGS: true
};

// 系列课程数据接口
export interface SeriesCourseData {
  title: string;
  description?: string | null;
  coverImage?: string;
  category: number;
  projectMembers?: string | null;
  tagIds?: number[] | null;
}

// 系列课程响应接口
export interface SeriesCourseResponse {
  id: number;
  title: string;
  description: string;
  coverImage: string;
  category: number;
  categoryLabel: string;
  status: number;
  statusLabel: string;
  projectMembers: string;
  totalCourses: number;
  totalStudents: number;
  contentSummary: {
    videoCourseCount: number;
    documentCourseCount: number;
    totalResourcesCount: number;
    completionRate: number;
  };
  createdAt: string;
  updatedAt: string;
}

// 分页信息接口
export interface PaginationInfo {
  page: number;
  pageSize: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// 系列课程列表响应接口
export interface SeriesCourseListResponse {
  list: SeriesCourseResponse[];
  pagination: PaginationInfo;
}

// API响应格式
export interface ApiResponse<T> {
  code: number;
  message: string;
  data: T;
}

// 课程管理API类
class CourseManagementApi {
  // 创建系列课程
  async createSeriesCourse(data: SeriesCourseData): Promise<ApiResponse<SeriesCourseResponse>> {
    if (DEV_CONFIG.ENABLE_DEBUG_LOGS) {
      console.log('🚀 开始创建系列课程');
      console.log('📝 请求数据类型: JSON');
      console.log('📝 请求数据:', data);
      console.log('🌐 目标URL: /api/v1/course-management/series');
      console.log('⚙️ 环境:', process.env.NODE_ENV);
    }

    try {
      // 只支持JSON格式
      const config = {
        headers: {
          'Content-Type': 'application/json',
        },
      };

      console.log('📤 发送请求配置:', config);

      // 发送真实请求（这样可以在Network面板中看到请求）
      const response = await request.post('/api/v1/course-management/series', data, config);

      if (DEV_CONFIG.ENABLE_DEBUG_LOGS) {
        console.log('✅ 收到真实API响应:', response.data);
      }

      return response.data;
    } catch (error: any) {
      if (DEV_CONFIG.ENABLE_DEBUG_LOGS) {
        console.error('❌ 创建系列课程请求失败:', error);
        if (error.response) {
          console.error('❌ 错误状态:', error.response.status);
          console.error('❌ 错误数据:', error.response.data);
          console.error('❌ 错误头:', error.response.headers);
        }
      }

      throw error;
    }
  }

  // 获取我的系列课程列表
  async getMySeries(params?: {
    page?: number;
    pageSize?: number;
    category?: number;
    status?: number;
  }): Promise<ApiResponse<SeriesCourseListResponse>> {
    if (DEV_CONFIG.ENABLE_DEBUG_LOGS) {
      console.log('🚀 获取我的系列课程列表');
      console.log('📝 请求参数:', params);
      console.log('🌐 目标URL: /api/v1/course-management/my-series');
    }

    try {
      const response = await request.get('/api/v1/course-management/my-series', { params });

      if (DEV_CONFIG.ENABLE_DEBUG_LOGS) {
        console.log('✅ 收到系列课程列表响应:', response.data);
      }

      return response.data;
    } catch (error: any) {
      if (DEV_CONFIG.ENABLE_DEBUG_LOGS) {
        console.error('❌ 获取系列课程列表失败:', error);
      }
      throw error;
    }
  }

  // 获取系列课程列表（保留原方法以兼容）
  async getSeriesCourseList(params?: {
    page?: number;
    size?: number;
    category?: number;
    status?: string;
  }): Promise<ApiResponse<SeriesCourseListResponse>> {
    return this.getMySeries({
      page: params?.page,
      pageSize: params?.size,
      category: params?.category,
      status: params?.status ? parseInt(params.status) : undefined
    });
  }

  // 获取系列课程详情 - 使用课程市场API
  async getSeriesCourseDetail(id: number): Promise<ApiResponse<SeriesCourseResponse>> {
    try {
      // 使用课程市场API获取系列详情
      const response = await request.get(`/api/v1/course-marketplace/series/${id}`);
      return response.data;
    } catch (error) {
      console.error('获取系列课程详情失败:', error);
      throw error;
    }
  }

  // 更新系列课程
  async updateSeriesCourse(id: number, data: Partial<SeriesCourseData>): Promise<ApiResponse<SeriesCourseResponse>> {
    try {
      const response = await request.put(`/api/v1/course-management/series/${id}`, data);
      return response.data;
    } catch (error) {
      console.error('更新系列课程失败:', error);
      throw error;
    }
  }

  // 删除系列课程
  async deleteSeriesCourse(id: number): Promise<ApiResponse<null>> {
    try {
      const response = await request.delete(`/api/v1/course-management/series/${id}`);
      return response.data;
    } catch (error) {
      console.error('删除系列课程失败:', error);
      throw error;
    }
  }

  // 删除单个课程
  async deleteCourse(id: number): Promise<ApiResponse<null>> {
    try {
      const response = await request.delete(`/api/v1/course-management/courses/${id}`);
      return response.data;
    } catch (error) {
      console.error('删除课程失败:', error);
      throw error;
    }
  }

  // 获取系列下的课程列表 - 使用课程管理API
  async getSeriesCourses(seriesId: number, params?: {
    page?: number;
    pageSize?: number;
    status?: number;
  }): Promise<ApiResponse<any>> {
    if (DEV_CONFIG.ENABLE_DEBUG_LOGS) {
      console.log('🚀 获取系列课程列表');
      console.log('📝 系列ID:', seriesId);
      console.log('📝 请求参数:', params);
      console.log('🌐 目标URL: /api/v1/course-management/series/' + seriesId + '/courses');
    }

    try {
      // 使用课程管理API获取系列下的课程列表
      const response = await request.get(`/api/v1/course-management/series/${seriesId}/courses`, { params });

      if (DEV_CONFIG.ENABLE_DEBUG_LOGS) {
        console.log('✅ 收到系列课程列表响应:', response.data);
      }

      return response.data;
    } catch (error: any) {
      if (DEV_CONFIG.ENABLE_DEBUG_LOGS) {
        console.error('❌ 获取系列课程列表失败:', error);
      }
      throw error;
    }
  }

}

// 导出API实例
export const courseManagementApi = new CourseManagementApi();
export default courseManagementApi;
