/* 充值页面主样式 */
.recharge-page {
  min-height: 100vh;
  background: #f8fafc;
  position: relative;
}

/* 头部样式 */
.recharge-header {
  background: white;
  border-bottom: 1px solid #e2e8f0;
  padding: 1rem 0;
  position: sticky;
  top: 0;
  z-index: 50;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1.5rem;
  width: 100%;
  box-sizing: border-box;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.back-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border: none;
  background: #f1f5f9;
  border-radius: 8px;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s ease;
}

.back-btn:hover {
  background: #e2e8f0;
  color: #475569;
}

.header-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.header-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.header-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.header-subtitle {
  font-size: 0.75rem;
  color: #64748b;
  margin: 0;
}

.history-btn {
  padding: 0.5rem 1rem;
  background: #475569;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.history-btn:hover {
  background: #334155;
}



/* 卡片区域 */
.cards-section {
  padding: 2rem 0;
  background: #f8fafc;
  min-height: calc(100vh - 80px);
}

.section-header {
  text-align: center;
  margin-bottom: 2rem;
}

.section-title {
  font-size: 1.875rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.5rem;
}

.section-subtitle {
  font-size: 1rem;
  color: #64748b;
}

/* 充值卡片网格 */
.recharge-cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

/* ===== 现代化卡片设计 ===== */
.modern-cards-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.8rem;
  padding: 1.5rem 0;
  max-width: 1200px;
  margin: 0 auto;

  /* 响应式适配 - 确保在不同缩放级别下都能正常显示 */
  @media (max-width: 1400px) {
    grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
    gap: 1.5rem;
  }

  @media (max-width: 1200px) {
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 1.2rem;
  }

  @media (max-width: 1000px) {
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 1rem;
  }

  @media (max-width: 900px) {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  @media (max-width: 600px) {
    grid-template-columns: 1fr;
    gap: 1.25rem;
    padding: 1rem 0;
  }
}

.modern-recharge-card {
  position: relative;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 20px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  animation: cardFadeIn 0.6s ease-out forwards;
  opacity: 0;
  transform: translateY(20px);
}

@keyframes cardFadeIn {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modern-recharge-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.modern-recharge-card.hovered {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

/* 卡片装饰元素 */
.card-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  overflow: hidden;
}

/* 有效期标签 */
/* 有效期标签 - 新样式设计 */
.validity-tag {
  position: absolute;
  top: 8px;
  right: 8px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 700;
  z-index: 10;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.3);
  transform: rotate(-8deg);
  transition: all 0.3s ease;
  letter-spacing: 0.5px;
}

.validity-tag:hover {
  transform: rotate(0deg) scale(1.05);
  box-shadow: 0 6px 16px rgba(102, 126, 234, 0.5);
}


.validity-tag.yearly {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  box-shadow: 0 4px 12px rgba(250, 112, 154, 0.4);
}

/* 退出确认弹窗样式 */
.exit-confirm-modal .ant-modal-content {
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.exit-confirm-modal .ant-modal-body {
  padding: 0;
}

.exit-confirm-modal .ant-btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.exit-confirm-modal .ant-btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
}

.exit-confirm-modal .ant-btn-dangerous {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  border: none;
  color: white;
  box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
  transition: all 0.3s ease;
}

.exit-confirm-modal .ant-btn-dangerous:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(255, 107, 107, 0.4);
  background: linear-gradient(135deg, #ff5252 0%, #d32f2f 100%);
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(168, 85, 247, 0.1));
  animation: float 6s ease-in-out infinite;
}

.decoration-circle-1 {
  width: 120px;
  height: 120px;
  top: -60px;
  right: -60px;
  animation-delay: 0s;
}

.decoration-circle-2 {
  width: 80px;
  height: 80px;
  bottom: -40px;
  left: -40px;
  animation-delay: 3s;
}

.decoration-line {
  position: absolute;
  top: 50%;
  left: -50%;
  right: -50%;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.2), transparent);
  transform: rotate(-45deg);
  animation: shimmer 4s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-10px) rotate(180deg); }
}

@keyframes shimmer {
  0%, 100% { opacity: 0; }
  50% { opacity: 1; }
}

/* 卡片主体内容 */
.card-main-content {
  position: relative;
  z-index: 10;
  padding: 1.5rem;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 顶部区域 */
.card-top-section {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.icon-container {
  flex-shrink: 0;
}

.icon-background {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.modern-recharge-card:hover .icon-background {
  transform: scale(1.1) rotate(5deg);
  box-shadow: 0 12px 32px rgba(102, 126, 234, 0.4);
}

.card-emoji {
  font-size: 1.5rem;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.title-section {
  flex: 1;
  min-width: 0;
}

.modern-card-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 0.25rem 0;
  line-height: 1.3;
}

.modern-card-subtitle {
  font-size: 0.875rem;
  color: #64748b;
  margin: 0;
  font-weight: 500;
}

/* 价格区域 */
.price-section {
  margin-bottom: 0.75rem;
  text-align: center;
  padding: 0.75rem;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.8), rgba(248, 250, 252, 0.9));
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
}

.price-main {
  margin-bottom: 0.75rem;
}

/* 价格行 - 所有价格信息在一行 */
.price-row {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

/* 原价样式（划线） */
.original-price {
  font-size: 1rem;
  font-weight: 600;
  text-decoration: line-through;
  color: #64748b;
  opacity: 0.7;
}

/* 当前价格样式 */
.current-price {
  font-size: 2rem;
  font-weight: 800;
  color: #1e293b;
  background: linear-gradient(135deg, #1e293b, #475569);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 折扣标识样式 */
.discount-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.2rem 0.4rem;
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  border-radius: 8px;
  font-size: 0.7rem;
  font-weight: 700;
  box-shadow: 0 2px 6px rgba(239, 68, 68, 0.3);
  white-space: nowrap;
}

/* 套餐信息区域 */
.package-info-section {
  margin-top: 0.5rem;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  align-items: center;
}

/* 信息项样式 */
.info-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  text-align: center;
}

.info-label {
  font-size: 0.8rem;
  color: #64748b;
  font-weight: 500;
}

.info-value {
  font-size: 0.9rem;
  font-weight: 700;
  color: #f59e0b;
  background: linear-gradient(135deg, #f59e0b, #ef4444);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 有效期显示样式 */
.validity-display {
  text-align: center;
}

.validity-text {
  font-size: 0.9rem;
  font-weight: 600;
  color: #6366f1;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  white-space: nowrap;
}

/* 折扣信息显示样式 */
.discount-info {
  text-align: center;
}

.discount-text {
  font-size: 0.85rem;
  font-weight: 600;
  color: #10b981;
  background: linear-gradient(135deg, #10b981, #059669);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  white-space: nowrap;
}

/* 特性列表区域 */
.features-section {
  flex: 1;
  margin-bottom: 1rem;
}

.features-list {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  margin-bottom: 0.5rem;
}

.feature-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem 0;
  border-bottom: 1px solid rgba(226, 232, 240, 0.3);
  transition: all 0.2s ease;
}

.feature-row:last-child {
  border-bottom: none;
}

.feature-row:hover {
  background: rgba(248, 250, 252, 0.5);
  padding-left: 0.375rem;
  padding-right: 0.375rem;
  border-radius: 6px;
  border-bottom-color: transparent;
}

.feature-row:hover:last-child {
  border-bottom: none;
}

.feature-left {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex: 1;
}

.feature-icon-wrapper {
  width: 24px;
  height: 24px;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  border: 1px solid rgba(226, 232, 240, 0.4);
}

.feature-emoji {
  font-size: 0.75rem;
}

.feature-label {
  font-size: 0.8rem;
  color: #475569;
  font-weight: 500;
}

.feature-detail {
  font-size: 0.8rem;
  color: #1e293b;
  font-weight: 600;
  text-align: right;
}

/* 剩余特性提示 */
.remaining-features {
  text-align: center;
  padding: 0.5rem 0.75rem;
  margin-top: 0.375rem;
  background: rgba(99, 102, 241, 0.08);
  border-radius: 6px;
  color: #6366f1;
  font-size: 0.75rem;
  font-weight: 500;
}

/* 查看详细提示 */
.view-details {
  text-align: center;
  padding: 0.5rem 0.75rem;
  margin-top: 0.375rem;
  background: rgba(156, 163, 175, 0.08);
  border-radius: 6px;
  color: #6b7280;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.view-details:hover {
  background: rgba(156, 163, 175, 0.12);
  transform: translateY(-1px);
}


/* 底部操作按钮 */
.card-action {
  margin-top: auto;
}

.select-button {
  width: 100%;
  padding: 0.75rem 1.25rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 10px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
  position: relative;
  overflow: hidden;
}

.select-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.select-button:hover::before {
  left: 100%;
}

.select-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4);
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

.arrow-icon {
  width: 16px;
  height: 16px;
  transition: transform 0.3s ease;
}

.select-button:hover .arrow-icon {
  transform: translateX(4px);
}

/* 悬停覆盖层 */
.hover-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.05), rgba(168, 85, 247, 0.05));
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.modern-recharge-card:hover .hover-overlay {
  opacity: 1;
}

/* 现代化卡片响应式设计 - 移动端优化 */
@media (max-width: 768px) {
  .modern-cards-container {
    grid-template-columns: 1fr !important;
    gap: 1.25rem !important;
    padding: 1rem 0 !important;
    margin: 0 1rem !important;
  }

  .modern-recharge-card {
    margin: 0 !important;
    min-height: auto;
  }

  .card-main-content {
    padding: 1.5rem;
  }

  .card-header {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 0.75rem;
  }

  .icon-wrapper {
    width: 50px;
    height: 50px;
  }

  .card-title {
    font-size: 1.125rem;
    text-align: center;
  }

  .main-price .amount {
    font-size: 2.5rem;
  }

  .points-info {
    margin-bottom: 1rem;
  }

  .modern-recharge-card:hover {
    transform: translateY(-4px) scale(1.01);
  }
}

@media (max-width: 480px) {
  .modern-cards-container {
    margin: 0 0.5rem !important;
    gap: 1rem !important;
  }

  .modern-recharge-card {
    margin: 0 !important;
  }

  .card-main-content {
    padding: 1rem;
  }

  .card-title {
    font-size: 1rem;
  }

  .main-price .amount {
    font-size: 2rem;
  }

  .points-info .points-amount {
    font-size: 1.25rem;
  }

  .select-button {
    font-size: 0.875rem;
    padding: 0.75rem 1.5rem;
  }

  .feature-row {
    padding: 0.375rem 0;
  }

  .feature-label {
    font-size: 0.75rem;
  }

  .feature-detail {
    font-size: 0.75rem;
  }
}

/* 页面缩放适配 - 处理浏览器缩放级别 */
@media (max-width: 1600px) and (min-width: 1201px) {
  .modern-cards-container {
    grid-template-columns: repeat(3, 1fr) !important;
    max-width: 1100px;
  }
}

@media (max-width: 1200px) and (min-width: 901px) {
  .modern-cards-container {
    grid-template-columns: repeat(2, 1fr) !important;
    max-width: 800px;
  }
}

@media (max-width: 900px) and (min-width: 601px) {
  .modern-cards-container {
    grid-template-columns: repeat(2, 1fr) !important;
    max-width: 600px;
    gap: 1rem;
  }

  .modern-recharge-card {
    min-height: 400px;
  }
}

/* 超小屏幕和高缩放级别适配 */
@media (max-width: 600px) {
  .modern-cards-container {
    grid-template-columns: 1fr !important;
    margin: 0 0.5rem !important;
  }
}

/* 充值卡片样式 */
.recharge-card {
  position: relative;
  background: white;
  border-radius: 24px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  animation: fadeInUp 0.6s ease-out forwards;
  opacity: 0;
  transform: translateY(30px);
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.recharge-card:hover {
  transform: translateY(-12px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}





/* 背景渐变 */
.card-bg-gradient {
  position: absolute;
  inset: 0;
  opacity: 0.6;
  z-index: 1;
}

/* 悬停光效 */
.card-glow {
  position: absolute;
  inset: 0;
  opacity: 0;
  transition: opacity 0.5s ease;
  z-index: 2;
}

.recharge-card:hover .card-glow {
  opacity: 0.2;
}

/* 卡片头部 */
.card-header {
  position: relative;
  z-index: 10;
  text-align: center;
  padding: 2rem 1.25rem 1rem;
}

.card-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 1.5rem;
  border-radius: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  color: white;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  transition: all 0.5s ease;
}

.recharge-card:hover .card-icon {
  transform: scale(1.1) rotate(6deg);
}

.card-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 0.5rem;
}

.card-subtitle {
  color: #64748b;
  font-size: 0.875rem;
  margin-bottom: 1.5rem;
}

.card-price {
  margin-bottom: 1.5rem;
}

.price-amount {
  font-size: 3rem;
  font-weight: 900;
  color: #1e293b;
  line-height: 1;
}

.price-bonus {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(135deg, #f59e0b, #ef4444);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 50px;
  font-size: 0.875rem;
  font-weight: 700;
  margin-top: 0.5rem;
}

/* 卡片内容 */
.card-content {
  position: relative;
  z-index: 10;
  padding: 0 1.25rem 1.5rem;
}

/* 详细特性列表 */
.card-detailed-features {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.detailed-feature-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.detailed-feature-item:hover {
  background: rgba(255, 255, 255, 0.9);
  transform: translateX(4px);
}

.feature-icon {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  flex-shrink: 0;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.feature-info {
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
  flex: 1;
}

.feature-name {
  font-size: 0.75rem;
  color: #64748b;
  font-weight: 500;
}

.feature-value {
  font-size: 0.875rem;
  color: #1e293b;
  font-weight: 600;
}

/* 剩余特性提示 */
.more-features-info {
  text-align: center;
  font-size: 0.75rem;
  color: #64748b;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 8px;
  margin-top: 0.5rem;
  font-weight: 500;
  border: 1px dashed rgba(100, 116, 139, 0.3);
}

/* 闪光效果 */
.card-shine {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transform: skewX(-25deg);
  transition: left 0.6s ease;
  z-index: 30;
}

.recharge-card:hover .card-shine {
  left: 100%;
}



/* 响应式设计 */
@media (max-width: 768px) {
  .cards-section {
    padding: 1.5rem 0;
  }

  .section-title {
    font-size: 1.5rem;
  }

  .recharge-cards-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }



  .header-left {
    gap: 0.75rem;
  }

  .header-title {
    font-size: 1.125rem;
  }

  .container {
    padding: 0 1rem;
  }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% { transform: translate3d(0, 0, 0); }
  40%, 43% { transform: translate3d(0, -8px, 0); }
  70% { transform: translate3d(0, -4px, 0); }
  90% { transform: translate3d(0, -2px, 0); }
}

/* 模态框样式 */
.plan-detail-modal .ant-modal-content {
  padding: 0;
  border-radius: 24px;
  overflow: hidden;
}


.plan-detail-modal .modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 2rem;
}

.plan-detail-modal .header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.plan-detail-modal .plan-icon {
  width: 64px;
  height: 64px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  color: white;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.plan-detail-modal .plan-title {
  font-size: 2rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
}

.plan-detail-modal .plan-subtitle {
  color: #ffffff;
  margin: 0;
  opacity: 0.9;
}



.plan-detail-modal .price-section {
  text-align: center;
  margin-bottom: 2rem;
}

.plan-detail-modal .price-card {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 1.5rem;
}

.plan-detail-modal .price-amount {
  font-size: 3rem;
  font-weight: 900;
  color: #1e293b;
  margin-bottom: 0.5rem;
}

.plan-detail-modal .price-bonus {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(135deg, #f59e0b, #ef4444);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 50px;
  font-size: 0.875rem;
  font-weight: 700;
}





.plan-detail-modal .payment-button {
  height: 56px;
  border-radius: 16px;
  font-size: 1.125rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  color: white;
  border: none;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.plan-detail-modal .payment-button:hover {
  transform: scale(1.05);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
}

/* 支付模态框样式 */
.payment-modal .ant-modal-content {
  padding: 0;
  border-radius: 16px;
  overflow: hidden;
  max-height: 90vh;
}

.payment-modal .ant-modal-body {
  max-height: calc(90vh - 60px);
  overflow-y: auto;
}

.payment-modal .payment-content {
  background: white;
  padding: 1.5rem;
  min-height: 500px;
  display: flex;
  flex-direction: column;
}

.payment-modal .payment-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
  text-align: center;
  margin-bottom: 1.5rem;
}

.payment-modal .payment-radio-group {
  width: 100%;
  margin-bottom: 1.5rem;
}

.payment-modal .payment-method-item {
  width: 100%;
  height: 48px;
  margin-bottom: 0.5rem;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  background: #f8fafc;
  transition: all 0.2s ease;
}

.payment-modal .payment-method-item:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
}

.payment-modal .payment-method-item.ant-radio-button-wrapper-checked {
  background: #dbeafe;
  border-color: #3b82f6;
}

.payment-modal .method-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0 1rem;
  height: 100%;
}

.payment-modal .method-icon {
  font-size: 1.25rem;
}

.payment-modal .method-name {
  font-weight: 500;
  color: #374151;
  font-size: 0.875rem;
}

.payment-modal .qr-section {
  text-align: center;
  margin-bottom: 1rem;
}

.payment-modal .qr-container {
  background: #f8fafc;
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
}

.payment-modal .qr-code-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.payment-modal .qr-info {
  text-align: center;
}

.payment-modal .qr-description {
  font-size: 0.875rem;
  color: #64748b;
  margin: 0 0 0.5rem 0;
}

.payment-modal .qr-amount {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.payment-modal .qr-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #9ca3af;
}

.payment-modal .loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.payment-modal .loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.payment-modal .qr-placeholder {
  color: #d1d5db;
}

.payment-modal .status-section {
  background: #f0f9ff;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
}

.payment-modal .countdown-tip {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  color: #0369a1;
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
}

.payment-modal .payment-status {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-size: 0.75rem;
  margin-top: 0.5rem;
}

.payment-modal .payment-status.polling {
  color: #0369a1;
}

.payment-modal .payment-status.success {
  color: #059669;
}

.payment-modal .payment-status.failed {
  color: #dc2626;
}

.payment-modal .payment-status.timeout {
  color: #d97706;
}



.payment-modal .test-actions {
  text-align: center;
  margin-top: auto;
  padding: 1rem 0;
  border-top: 1px solid #e5e7eb;
}

/* 集成式套餐详情弹窗样式 */
.plan-detail-modal .modal-content {
  background: white;
  border-radius: 16px;
  overflow: hidden;
}

/* 支付方式选择区域 */
.plan-detail-modal .payment-methods-section {
  margin: 1.5rem 0;
  padding: 1.5rem;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.plan-detail-modal .payment-methods-section .section-title {
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 1rem;
}

.plan-detail-modal .payment-radio-group {
  width: 100%;
}

.plan-detail-modal .payment-method-item {
  width: 100%;
  height: 48px;
  margin-bottom: 0.5rem;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  background: white;
  transition: all 0.2s ease;
}

.plan-detail-modal .payment-method-item:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
}

.plan-detail-modal .payment-method-item.ant-radio-button-wrapper-checked {
  background: #dbeafe;
  border-color: #3b82f6;
}

.plan-detail-modal .method-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0 1rem;
  height: 100%;
}

.plan-detail-modal .method-icon {
  font-size: 1.25rem;
}

.plan-detail-modal .method-name {
  font-weight: 500;
  color: #374151;
  font-size: 0.875rem;
}

/* 支付视图样式 */
.plan-detail-modal .payment-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem 1.5rem 1rem;
  border-bottom: 1px solid #e5e7eb;
}

.plan-detail-modal .back-btn {
  background: none;
  border: none;
  padding: 0.5rem;
  border-radius: 8px;
  cursor: pointer;
  color: #6b7280;
  transition: all 0.2s ease;
}

.plan-detail-modal .back-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

.plan-detail-modal .payment-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.plan-detail-modal .payment-plan-info {
  padding: 1rem 1.5rem;
  background: #f8fafc;
  border-bottom: 1px solid #e5e7eb;
}

.plan-detail-modal .plan-summary {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
}

.plan-detail-modal .plan-icon-small {
  font-size: 1.5rem;
}

.plan-detail-modal .plan-details .plan-name {
  font-weight: 600;
  color: #1f2937;
  font-size: 0.875rem;
}

.plan-detail-modal .plan-details .plan-price {
  font-size: 1.25rem;
  font-weight: 700;
  color: #3b82f6;
}

.plan-detail-modal .payment-method-display {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #6b7280;
}

.plan-detail-modal .qr-section {
  padding: 1.5rem;
  text-align: center;
}

.plan-detail-modal .qr-container {
  background: #f8fafc;
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
}

.plan-detail-modal .qr-code-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.plan-detail-modal .qr-description {
  font-size: 0.875rem;
  color: #64748b;
  margin: 0;
}

.plan-detail-modal .qr-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #9ca3af;
}

.plan-detail-modal .loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.plan-detail-modal .loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.plan-detail-modal .qr-placeholder {
  color: #d1d5db;
}

.plan-detail-modal .status-section {
  background: #f0f9ff;
  border-radius: 8px;
  padding: 1rem 1.5rem;
  margin: 0 1.5rem 1.5rem;
}

.plan-detail-modal .countdown-tip {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  color: #0369a1;
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
}

.plan-detail-modal .payment-status {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-size: 0.75rem;
  margin-top: 0.5rem;
}

.plan-detail-modal .payment-status.polling {
  color: #0369a1;
}

.plan-detail-modal .payment-status.success {
  color: #059669;
}

.plan-detail-modal .payment-status.failed {
  color: #dc2626;
}

.plan-detail-modal .payment-status.timeout {
  color: #d97706;
}

/* ===== 追波风格设计 ===== */
.plan-detail-modal.dribbble-style .ant-modal-content {
  padding: 0;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 40px -8px rgba(0, 0, 0, 0.2);
  max-height: 85vh;
}

.plan-detail-modal.dribbble-style .modal-content {
  background: #ffffff;
  height: auto;
  max-height: 85vh;
  overflow: hidden;
}

/* 左右布局容器 */
.plan-detail-modal .layout-container {
  display: flex;
  min-height: 500px;
  max-height: 85vh;
}

.plan-detail-modal .left-panel {
  flex: 0 0 65%;
  padding: 2rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  position: relative;
}

.plan-detail-modal .left-panel::before {
  content: '';
  position: absolute;
  top: -30%;
  right: -30%;
  width: 160%;
  height: 160%;
  background: radial-gradient(circle, rgba(255,255,255,0.08) 0%, transparent 70%);
  pointer-events: none;
}

.plan-detail-modal .right-panel {
  flex: 0 0 35%;
  padding: 1rem;
  background: #fafbfc;
  border-left: 1px solid #e1e8ed;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  overflow: hidden;
  min-height: 400px;
}

/* 左侧套餐详情样式 */
/* 价格头部区域 */
.plan-detail-modal .price-header-section {
  position: relative;
  z-index: 1;
  margin-bottom: 1.5rem;
  text-align: center;
}

.plan-detail-modal .price-display-top {
  background: rgba(255, 255, 255, 0.15);
  padding: 1.5rem;
  border-radius: 16px;
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.25);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.plan-detail-modal .price-display-top .price-main {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 0.25rem;
  margin-bottom: 1rem;
}

.plan-detail-modal .price-display-top .currency {
  font-size: 1.75rem;
  font-weight: 600;
  opacity: 0.9;
}

.plan-detail-modal .price-display-top .amount {
  font-size: 4rem;
  font-weight: 900;
  line-height: 1;
}

.plan-detail-modal .price-display-top .bonus-tag {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.3);
  padding: 0.75rem 1.25rem;
  border-radius: 30px;
  font-size: 0.875rem;
  font-weight: 700;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.4);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.plan-detail-modal .plan-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 2rem;
  position: relative;
  z-index: 1;
  gap: 1.5rem;
}

.plan-detail-modal .plan-left {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  flex: 1;
}

.plan-detail-modal .plan-badge {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.plan-detail-modal .plan-info {
  flex: 1;
  margin-left: 0.5rem;
}

.plan-detail-modal .plan-icon {
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  backdrop-filter: blur(10px);
}



.plan-detail-modal .plan-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 0 0.375rem 0;
  line-height: 1.2;
}

.plan-detail-modal .plan-subtitle {
  font-size: 0.875rem;
  color: #ffffff;
  opacity: 0.9;
  margin: 0;
  line-height: 1.4;
}

/* 右侧价格显示样式 - 简化版 */
.plan-detail-modal .price-display {
  position: relative;
  z-index: 1;
  text-align: center;
  flex-shrink: 0;
}

.plan-detail-modal .price-display .price-main {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 0.25rem;
}

.plan-detail-modal .price-display .currency {
  font-size: 1.5rem;
  font-weight: 700;
  opacity: 0.9;
}

.plan-detail-modal .price-display .amount {
  font-size: 3.5rem;
  font-weight: 900;
  line-height: 1;
}

/* 积分区域样式 */
.plan-detail-modal .bonus-section {
  margin: 1rem 0 1.5rem 0;
  position: relative;
  z-index: 1;
}

.plan-detail-modal .bonus-section .bonus-tag {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: transparent;
  padding: 0;
  border-radius: 0;
  font-size: 1.1rem;
  font-weight: 700;
  backdrop-filter: none;
  border: none;
  box-shadow: none;
}

.plan-detail-modal .features-list {
  position: relative;
  z-index: 1;
  margin-top: 1rem;
}

.plan-detail-modal .features-title {
  font-size: 0.95rem;
  font-weight: 600;
  margin: 0 0 0.75rem 0;
  opacity: 0.9;
}

.plan-detail-modal .features-simple {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.plan-detail-modal .feature-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.plan-detail-modal .feature-item:last-child {
  border-bottom: none;
}

.plan-detail-modal .feature-item .feature-icon {
  font-size: 1.125rem;
  flex-shrink: 0;
  opacity: 0.9;
}

.plan-detail-modal .feature-item .feature-text {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.plan-detail-modal .feature-item .feature-name {
  font-size: 0.875rem;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.95);
  line-height: 1.3;
}

.plan-detail-modal .feature-item .feature-value {
  font-size: 0.875rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.3;
}

/* 右侧支付方式样式 */
.plan-detail-modal .payment-section {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.plan-detail-modal .payment-title {
  font-size: 1rem;
  font-weight: 700;
  color: #1a202c;
  margin: 0 0 0.2rem 0;
}

.plan-detail-modal .payment-subtitle {
  font-size: 0.65rem;
  color: #718096;
  margin: 0 0 0.5rem 0;
}

.plan-detail-modal .payment-methods {
  flex: 1;
  margin-bottom: 0.5rem;
  overflow-y: auto;
}

.plan-detail-modal .payment-options {
  width: 100%;
}

.plan-detail-modal .payment-options .ant-radio-wrapper {
  display: block !important;
  width: 100%;
  margin: 0 0 0.125rem 0;
  padding: 0;
  line-height: 1;
  min-height: auto;
}

.plan-detail-modal .payment-options .ant-radio-wrapper .ant-radio {
  display: none;
}

.plan-detail-modal .payment-option {
  margin-bottom: 0.125rem;
}

.plan-detail-modal .payment-radio {
  width: 100%;
}

.plan-detail-modal .method-card {
  display: flex;
  align-items: center;
  padding: 0.375rem 0.625rem;
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 100%;
  min-height: 36px;
  height: auto;
}

.plan-detail-modal .method-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #667eea, #764ba2);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.plan-detail-modal .method-card:hover {
  border-color: #667eea;
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.15);
}

.plan-detail-modal .payment-radio.ant-radio-wrapper-checked .method-card {
  border-color: #667eea !important;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.08), rgba(118, 75, 162, 0.08)) !important;
  box-shadow: 0 3px 15px rgba(102, 126, 234, 0.2) !important;
}

.plan-detail-modal .method-icon {
  font-size: 1rem;
  margin-right: 0.5rem;
  position: relative;
  z-index: 1;
}

.plan-detail-modal .method-info {
  flex: 1;
  position: relative;
  z-index: 1;
  min-height: 0;
}

.plan-detail-modal .method-name {
  font-size: 0.8rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 0.025rem;
  line-height: 1.1;
}

.plan-detail-modal .method-desc {
  font-size: 0.7rem;
  color: #718096;
  line-height: 1.1;
}

.plan-detail-modal .method-check {
  position: relative;
  z-index: 1;
}

.plan-detail-modal .check-circle {
  width: 16px;
  height: 16px;
  border: 2px solid #cbd5e0;
  border-radius: 50%;
  position: relative;
  transition: all 0.3s ease;
  background: white;
}

.plan-detail-modal .payment-radio.ant-radio-wrapper-checked .check-circle {
  border-color: #667eea !important;
  background: #667eea !important;
}

.plan-detail-modal .payment-radio.ant-radio-wrapper-checked .check-circle::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 8px;
  background: white;
  border-radius: 50%;
}



.plan-detail-modal .payment-action {
  margin-top: auto;
  padding-top: 0.25rem;
  padding-bottom: 0.5rem;
}

.plan-detail-modal .pay-button {
  width: 100% !important;
  height: 42px !important;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border: none !important;
  border-radius: 8px !important;
  color: white !important;
  font-size: 0.85rem !important;
  font-weight: 700 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
  padding: 0 1rem !important;
  box-shadow: 0 3px 15px rgba(102, 126, 234, 0.3) !important;
  transition: all 0.3s ease !important;
}

.plan-detail-modal .pay-button:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 30px rgba(102, 126, 234, 0.4) !important;
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%) !important;
}

.plan-detail-modal .pay-amount {
  font-weight: 700 !important;
  font-size: 1rem !important;
  color: white !important;
}

.plan-detail-modal .pay-button svg {
  color: white !important;
  opacity: 0.9;
}

/* 支付视图样式 */
.plan-detail-modal .payment-layout {
  display: flex;
  height: 580px;
  max-height: 85vh;
}

.plan-detail-modal .payment-left {
  flex: 0 0 45%;
  padding: 1.75rem;
  background: #fafbfc;
  border-right: 1px solid #e1e8ed;
  overflow-y: auto;
}

.plan-detail-modal .payment-right {
  flex: 0 0 55%;
  padding: 1.75rem;
  background: white;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.plan-detail-modal .payment-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.plan-detail-modal .back-btn {
  width: 36px;
  height: 36px;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #718096;
}

.plan-detail-modal .back-btn:hover {
  background: #f7fafc;
  border-color: #cbd5e0;
  color: #4a5568;
}

.plan-detail-modal .header-info .payment-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1a202c;
  margin: 0 0 0.125rem 0;
}

.plan-detail-modal .header-info .payment-subtitle {
  font-size: 0.75rem;
  color: #718096;
  margin: 0;
}

.plan-detail-modal .order-summary {
  background: white;
  border-radius: 12px;
  padding: 1.25rem;
  margin-bottom: 1.5rem;
  border: 1px solid #e2e8f0;
}

.plan-detail-modal .order-header h3 {
  font-size: 1rem;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 0.75rem 0;
}

.plan-detail-modal .order-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 0;
  border-bottom: 1px solid #e2e8f0;
}

.plan-detail-modal .item-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.plan-detail-modal .item-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  color: white;
}

.plan-detail-modal .item-name {
  font-size: 0.875rem;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 0.125rem;
}

.plan-detail-modal .item-desc {
  font-size: 0.75rem;
  color: #718096;
}

.plan-detail-modal .item-price {
  font-size: 1rem;
  font-weight: 700;
  color: #1a202c;
}

.plan-detail-modal .payment-method-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 0;
  border-bottom: 1px solid #e2e8f0;
}

.plan-detail-modal .method-label {
  font-size: 0.75rem;
  color: #718096;
}

.plan-detail-modal .method-selected {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  font-size: 0.75rem;
  font-weight: 600;
  color: #1a202c;
}

.plan-detail-modal .order-total {
  padding: 0.75rem 0 0;
}

.plan-detail-modal .total-line {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 0.875rem;
}

.plan-detail-modal .total-line span:first-child {
  color: #718096;
}

.plan-detail-modal .total-amount {
  font-size: 1.25rem;
  font-weight: 700;
  color: #667eea;
}

.plan-detail-modal .payment-status-section {
  background: transparent;
  padding: 0.75rem 0;
}

.plan-detail-modal .countdown-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.75rem;
  color: #718096;
  margin-bottom: 0.75rem;
}

.plan-detail-modal .countdown-icon {
  color: #667eea;
}

/* 二维码区域样式 */
.plan-detail-modal .qr-section {
  text-align: center;
  width: 100%;
}

.plan-detail-modal .qr-header {
  margin-bottom: 1.5rem;
}

.plan-detail-modal .qr-header h3 {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1a202c;
  margin: 0 0 0.375rem 0;
}

.plan-detail-modal .qr-header p {
  font-size: 0.75rem;
  color: #718096;
  margin: 0;
}

.plan-detail-modal .qr-container {
  background: #fafbfc;
  border-radius: 16px;
  padding: 1.25rem;
  border: 1px solid #e2e8f0;
  margin-bottom: 1rem;
}

.plan-detail-modal .qr-code-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.plan-detail-modal .qr-frame {
  background: white;
  padding: 0.75rem;
  border-radius: 12px;
  box-shadow: 0 3px 15px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

.plan-detail-modal .qr-tips {
  text-align: center;
  width: 100%;
  max-width: 280px;
}

.plan-detail-modal .tip-item {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  color: #64748b;
  margin-bottom: 0.25rem;
  padding: 0.25rem 0;
  gap: 0.5rem;
}

.plan-detail-modal .tip-item::before {
  content: '';
  width: 3px;
  height: 3px;
  background: #667eea;
  border-radius: 50%;
  flex-shrink: 0;
}

.plan-detail-modal .qr-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #a0aec0;
}

.plan-detail-modal .loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
}

.plan-detail-modal .loading-spinner {
  width: 28px;
  height: 28px;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.plan-detail-modal .qr-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
  color: #cbd5e0;
}

.plan-detail-modal .test-actions {
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e2e8f0;
}

/* ===== 响应式断点布局 ===== */

/* 大屏幕 (1200px+) */
@media (min-width: 1200px) {
  .plan-detail-modal.dribbble-style .ant-modal {
    max-width: 1000px;
  }
}

/* 中等屏幕 (768px - 1199px) */
@media (max-width: 1199px) and (min-width: 768px) {
  .plan-detail-modal.dribbble-style .ant-modal {
    max-width: 90vw;
  }

  .plan-detail-modal .layout-container {
    height: 480px;
  }

  .plan-detail-modal .payment-layout {
    height: 480px;
  }

  .plan-detail-modal .left-panel {
    flex: 0 0 65%;
    padding: 1.5rem;
  }

  .plan-detail-modal .right-panel {
    flex: 0 0 35%;
    padding: 1.5rem;
  }

  .plan-detail-modal .payment-left,
  .plan-detail-modal .payment-right {
    padding: 1.5rem;
  }

  .plan-detail-modal .plan-title {
    font-size: 1.375rem;
  }

  .plan-detail-modal .price-display-top .amount {
    font-size: 3.5rem;
  }

  .plan-detail-modal .price-display-top .currency {
    font-size: 1.5rem;
  }
}

/* 小屏幕 (576px - 767px) */
@media (max-width: 767px) and (min-width: 576px) {
  .plan-detail-modal.dribbble-style .ant-modal {
    max-width: 95vw;
    margin: 1rem;
  }

  .plan-detail-modal.dribbble-style .ant-modal-content {
    max-height: 90vh;
  }

  .plan-detail-modal .layout-container {
    flex-direction: column;
    height: auto;
    max-height: 90vh;
  }

  .plan-detail-modal .left-panel {
    flex: none;
    border-radius: 0;
  }

  .plan-detail-modal .right-panel {
    flex: none;
    border-left: none;
    border-top: 1px solid #e1e8ed;
  }

  .plan-detail-modal .payment-layout {
    flex-direction: column;
    height: auto;
    max-height: 90vh;
  }

  .plan-detail-modal .payment-left {
    flex: none;
    border-right: none;
    border-bottom: 1px solid #e1e8ed;
  }

  .plan-detail-modal .payment-right {
    flex: none;
  }

  .plan-detail-modal .left-panel,
  .plan-detail-modal .right-panel,
  .plan-detail-modal .payment-left,
  .plan-detail-modal .payment-right {
    padding: 1.25rem;
  }

  .plan-detail-modal .plan-title {
    font-size: 1.25rem;
  }

  .plan-detail-modal .price-display-top .amount {
    font-size: 3rem;
  }

  .plan-detail-modal .price-display-top .currency {
    font-size: 1.25rem;
  }

  .plan-detail-modal .price-display-top {
    padding: 1.25rem;
  }




}

/* 超小屏幕 (< 576px) */
@media (max-width: 575px) {
  .plan-detail-modal.dribbble-style .ant-modal {
    max-width: 100vw;
    margin: 0;
    height: 100vh;
  }

  .plan-detail-modal.dribbble-style .ant-modal-content {
    border-radius: 0;
    height: 100vh;
    max-height: 100vh;
  }

  .plan-detail-modal .modal-content {
    height: 100vh;
    max-height: 100vh;
  }

  .plan-detail-modal .layout-container {
    flex-direction: column;
    height: 100vh;
    max-height: 100vh;
  }

  .plan-detail-modal .payment-layout {
    flex-direction: column;
    height: 100vh;
    max-height: 100vh;
  }

  .plan-detail-modal .left-panel,
  .plan-detail-modal .right-panel,
  .plan-detail-modal .payment-left,
  .plan-detail-modal .payment-right {
    padding: 1rem;
    flex: none;
  }

  .plan-detail-modal .plan-title {
    font-size: 1.125rem;
  }

  .plan-detail-modal .price-display-top .amount {
    font-size: 2.5rem;
  }

  .plan-detail-modal .price-display-top .currency {
    font-size: 1.125rem;
  }

  .plan-detail-modal .price-display-top {
    padding: 1rem;
  }



  .plan-detail-modal .method-card {
    padding: 0.875rem;
  }

  .plan-detail-modal .pay-button {
    height: 44px;
    font-size: 0.875rem;
  }

  .plan-detail-modal .qr-container {
    padding: 1rem;
  }

  .plan-detail-modal .order-summary {
    padding: 1rem;
  }

  .plan-detail-modal .qr-loading {
    height: 200px;
  }
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  padding: 2rem;
}

.loading-container .ant-spin {
  color: #6366f1;
}

.loading-container .ant-spin-text {
  color: #64748b;
  font-size: 1rem;
  margin-top: 0.5rem;
}

/* 新布局样式 - 参考提供的设计 */

/* 卡片标题区域 */
.card-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.header-icon {
  flex-shrink: 0;
}

.icon-emoji {
  font-size: 1.2rem;
  color: #10b981;
}

.emoji-icon {
  font-size: 1.2rem;
  display: inline-block;
  line-height: 1;
}

.icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 10px;
  color: white;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
  font-size: 1.25rem;
}

.icon-wrapper:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
}

.card-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
  line-height: 1.3;
}

/* 主要价格显示 */
.main-price {
  text-align: center;
  margin-bottom: 1rem;
}

.price-display {
  display: flex;
  align-items: flex-start;
  justify-content: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.main-price-section {
  display: flex;
  align-items: baseline;
  gap: 0.25rem;
}

.discount-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
  margin-top: 0.25rem;
}

.currency {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
}

.amount {
  font-size: 4rem;
  font-weight: 800;
  color: #1e293b;
  line-height: 1;
}

.period-info {
  margin-left: 0.5rem;
}

.original-amount {
  font-size: 0.875rem;
  color: #94a3b8;
  text-decoration: line-through;
}

.discount-tag {
  display: inline-block;
  padding: 0.125rem 0.375rem;
  background: linear-gradient(135deg, #ff6b6b, #ff5252);
  color: white;
  border-radius: 12px;
  font-size: 0.6875rem;
  font-weight: 600;
  letter-spacing: 0.025em;
  box-shadow: 0 2px 4px rgba(255, 107, 107, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.discount-tag::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.discount-tag:hover::before {
  left: 100%;
}

/* 价格副标题 */
.price-subtitle {
  text-align: center;
  margin-bottom: 1rem;
  color: #94a3b8;
  font-size: 0.875rem;
}

/* 积分信息显示 */
.points-info {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 0.25rem;
  margin-bottom: 1.5rem;
  padding: 0.5rem 0;
}

.points-amount {
  font-size: 1.5rem;
  font-weight: 700;
  color: #f59e0b;
  line-height: 1;
}

.points-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #6b7280;
}

/* 错误状态样式 */
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  padding: 2rem;
}

.error-content {
  text-align: center;
  max-width: 400px;
}

.error-message {
  font-size: 1.125rem;
  color: #ef4444;
  margin-bottom: 1.5rem;
  font-weight: 500;
}

.retry-button {
  background: #6366f1;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.retry-button:hover {
  background: #4f46e5;
  transform: translateY(-1px);
}

/* 空状态样式 */
.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  padding: 2rem;
}

.empty-message {
  font-size: 1.125rem;
  color: #6b7280;
  font-weight: 500;
  text-align: center;
}
