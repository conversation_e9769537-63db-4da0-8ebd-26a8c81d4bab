'use client';

import React, { useState, useEffect } from 'react';
import { Search, Plus, Eye, Edit, Delete, RefreshCw } from 'lucide-react';
import styles from './ClassTasks.module.css';
import { schoolApi } from '../../../lib/api/school';
import { classApi } from '../../../lib/api/class';
import taskApi from '../../../lib/api/task';
import { notification, Modal } from 'antd';
import SchoolSelectionModal from './SchoolSelectionModal';
import ClassSelectionModal from './ClassSelectionModal';
import TemplateSelectionModal from './TemplateSelectionModal';
import NewPublishTaskModal from './NewPublishTaskModal';
import { TaskDetailModal } from '../../teacher-space/components/task-detail-modal';
import { EditTaskModal } from '../../teacher-space/components/modals/edit-task-modal';
import taskEventManager, { TASK_EVENTS, TaskPublishedEventData } from '@/app/utils/task-event-manager';

interface Task {
  id: number;
  taskName: string;
  status: '已完成' | '进行中' | '已结束'|'未开始';
  publishTime: string;
  deadline: string;
  completionRate: number;
  taskId?: number;
  assignmentId?: number;
}

interface School {
  id: number;
  schoolName: string;
}

interface Class {
  id: number;
  className: string;
  schoolId: number;
}

interface Student {
  id: number;
  name: string;
  classId: number;
}

const ClassTasks = () => {
  // 移除对模板上下文的依赖，只使用全局事件
  // const { notifyTaskPublished } = useTemplate();

  // 数据状态
  const [schools, setSchools] = useState<School[]>([]);
  const [classes, setClasses] = useState<Class[]>([]);
  const [students, setStudents] = useState<Student[]>([]);
  const [tasks, setTasks] = useState<Task[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 发布任务多步骤弹窗状态
  const [isSchoolModalOpen, setIsSchoolModalOpen] = useState(false);
  const [isClassModalOpen, setIsClassModalOpen] = useState(false);
  const [isTemplateModalOpen, setIsTemplateModalOpen] = useState(false);
  const [isPublishTaskModalOpen, setIsPublishTaskModalOpen] = useState(false);
  const [publishModalData, setPublishModalData] = useState({
    selectedDistribution: '',
    energyAmount: '',
    selectedTemplate: { id: 0 },
    selectedStudents: [] as number[]
  });
  const [modalSelectedSchool, setModalSelectedSchool] = useState<any>(null);

  // 任务详情弹窗状态
  const [isTaskDetailModalVisible, setIsTaskDetailModalVisible] = useState(false);
  const [selectedTaskForDetail, setSelectedTaskForDetail] = useState<any>(null);

  // 任务编辑弹窗状态
  const [isEditTaskModalVisible, setIsEditTaskModalVisible] = useState(false);
  const [selectedTaskForEdit, setSelectedTaskForEdit] = useState<any>(null);

  // 选择状态
  const [selectedSchool, setSelectedSchool] = useState<School | null>(null);
  const [selectedClass, setSelectedClass] = useState<Class | null>(null);
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [statusFilter, setStatusFilter] = useState('全部');
  const [searchQuery, setSearchQuery] = useState('');

  const statusOptions = ['全部', '已完成', '进行中', '未开始','已结束'];

  // 获取用户关联的学校列表
  const fetchUserSchools = async () => {
    try {
      setLoading(true);
      setError(null);

      // 从localStorage获取用户信息（根据项目中其他组件的使用方式）
      const userInfo = localStorage.getItem('user');

      if (!userInfo) {
        setError('用户未登录');
        console.error('未找到用户信息，请检查登录状态');
        return;
      }

      const user = JSON.parse(userInfo);
      console.log('解析的用户信息:', user);

      // 根据项目中其他组件的使用方式，尝试多种可能的用户ID字段名
      const userId = user.id || user.userId || user.teacherId;

      if (!userId) {
        setError('用户ID不存在');
        console.error('用户信息中未找到ID字段:', user);
        return;
      }

      console.log('使用的用户ID:', userId);

      // 使用项目中已有的API函数
      const response = await schoolApi.getUserSchools();
      console.log('获取学校数据:', response); // 调试日志

      if (response.data.code === 200 && response.data.data) {
        // 确保数据是数组
        const schoolsData = Array.isArray(response.data.data) ? response.data.data : [];
        setSchools(schoolsData);
        // 默认选择第一个学校
        if (schoolsData.length > 0) {
          setSelectedSchool(schoolsData[0]);
          // 获取第一个学校的班级
          fetchSchoolClasses(schoolsData[0].id);
        } else {
          setSelectedSchool(null);
        }
      } else {
        setError(`获取学校列表失败: ${response.data.msg || '未知错误'}`);
        console.error('API返回错误:', response.data);
      }
    } catch (error) {
      console.error('获取学校列表失败:', error);
      setError('网络连接失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取指定学校的班级列表
  const fetchSchoolClasses = async (schoolId: number) => {
    try {
      console.log('=== 开始获取班级数据 ===');
      console.log('目标学校ID:', schoolId);
      console.log('API调用参数:', { schoolId, page: 1, size: 100 });

      // 使用项目中已有的API函数
      const response = await schoolApi.getSchoolClasses({
        schoolId,
        page: 1,
        size: 100
      });

      console.log('=== API响应 ===');
      console.log('完整响应:', response);
      console.log('响应状态:', response.status);
      console.log('响应数据:', response.data);
      console.log('业务代码:', response.data.code);
      console.log('数据部分:', response.data.data);

      if (response.data.code === 200 && response.data.data) {
        // 根据API响应结构，班级数据在 data.data.list 中
        let classesData = [];

        if (response.data.data.list && Array.isArray(response.data.data.list)) {
          classesData = response.data.data.list;
          console.log('从 data.data.list 获取班级数据:', classesData);
        } else if (Array.isArray(response.data.data)) {
          classesData = response.data.data;
          console.log('从 data.data 直接获取班级数据:', classesData);
        }

        console.log('=== 班级数据处理 ===');
        console.log('学校ID:', schoolId);
        console.log('解析出的班级数据:', classesData);
        console.log('班级数量:', classesData.length);

        // 验证班级数据是否属于正确的学校
        if (classesData.length > 0) {
          console.log('第一个班级的schoolId:', classesData[0].schoolId);
          console.log('是否匹配目标学校:', classesData[0].schoolId === schoolId);
        }

        setClasses(classesData);
        console.log('班级状态已更新');

        // 默认选择第一个班级
        if (classesData.length > 0) {
          setSelectedClass(classesData[0]);
          console.log('默认选择班级:', classesData[0].className, '(ID:', classesData[0].id, ')');
          // 获取第一个班级的学生数据
          // fetchClassStudents(classesData[0].id);
        } else {
          setSelectedClass(null);
          setStudents([]);
          console.log('没有班级数据，清空选择');
        }
      } else {
        console.log('API返回错误或无数据:', response.data);
        setClasses([]);
        setSelectedClass(null);
      }
    } catch (error) {
      console.error('获取班级列表失败:', error);
      setClasses([]);
      setSelectedClass(null);
    }
  };


  // 处理班级选择变化
  const handleClassChange = (classItem: Class) => {
    console.log('=== 切换班级 ===');
    console.log('从班级:', selectedClass?.className, '(ID:', selectedClass?.id, ')');
    console.log('切换到班级:', classItem.className, '(ID:', classItem.id, ')');

    // 如果点击的是当前已选中的班级，不需要切换
    if (selectedClass?.id === classItem.id) {
      console.log('点击的是当前班级，无需切换');
      return;
    }

    // 更新选中的班级
    setSelectedClass(classItem);
    console.log('班级切换完成');
  };

  // 处理学校选择变化
  const handleSchoolChange = (school: School) => {
    console.log('=== 切换学校 ===');
    console.log('从学校:', selectedSchool?.schoolName, '(ID:', selectedSchool?.id, ')');
    console.log('切换到学校:', school.schoolName, '(ID:', school.id, ')');
    console.log('当前班级数据:', classes);

    // 先清空班级和学生数据，避免显示上一个学校的数据
    setClasses([]);
    setSelectedClass(null);
    setStudents([]);
    setSelectedSchool(school);

    console.log('已清空班级数据，开始获取新学校的班级...');
    fetchSchoolClasses(school.id);
  };

  // 获取班级的真实任务数据
  const fetchClassTasks = async (classId: number) => {
    try {
      console.log('=== 开始获取班级任务数据 ===');
      console.log('班级ID:', classId);

      // 获取当前用户信息
      const userData = localStorage.getItem('user');
      const user = userData ? JSON.parse(userData) : null;
      const teacherId = user?.userId;

      if (!teacherId) {
        console.error('未找到教师ID');
        return [];
      }

      // 调用任务API获取班级任务列表
      const response = await taskApi.getTaskList({
        classId: classId,
        teacherId: teacherId,
        page: 1,
        size: 100,
        orderBy: 'startDate'
      });

      console.log('班级任务API响应:', response);

      if (response.data.code === 200 && response.data.data) {
        const tasksData = response.data.data.list || response.data.data || [];
        console.log('获取到的任务数据:', tasksData);

        // 将API返回的数据转换为组件需要的格式
        const formattedTasks = tasksData.map((task: any) => ({
          id: task.id,
          taskName: task.taskName || '未命名任务',
          status: getTaskStatus(task),
          publishTime: formatDate(task.startDate || task.createTime),
          deadline: formatDate(task.endDate),
          completionRate: calculateCompletionRate(task)
        }));

        return formattedTasks;
      } else {
        console.log('获取班级任务失败:', response.data);
        return [];
      }
    } catch (error) {
      console.error('获取班级任务数据失败:', error);
      return [];
    }
  };

  // 根据任务数据判断状态
  const getTaskStatus = (task: any): '已完成' | '进行中' | '已结束'|'未开始' => {
    const now = new Date();
    const endDate = new Date(task.endDate);
    const startDate = new Date(task.startDate || task.createTime);

    console.log('任务状态判断:', {
      taskName: task.taskName,
      now: now.toISOString(),
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
      nowTime: now.getTime(),
      startTime: startDate.getTime(),
      endTime: endDate.getTime(),
      assignments: task.assignments?.length || 0
    });

    // 1. 检查是否未开始
    if (now < startDate) {
      console.log('任务未开始');
      return '未开始';
    }

    // 2. 检查是否已完成（基于assignments数据）
    if (task.assignments && task.assignments.length > 0) {
      const completedCount = task.assignments.filter((assignment: any) =>
        assignment.taskStatus === 2 || assignment.workId // taskStatus=2表示已完成，或有作品ID
      ).length;
      const totalCount = task.assignments.length;
      const completionRate = (completedCount / totalCount) * 100;

      console.log('完成情况:', { completedCount, totalCount, completionRate });

      if (completionRate === 100) {
        console.log('任务已完成');
        return '已完成';
      }
    }

    // 3. 检查是否超过截止时间
    if (now > endDate) {
      console.log('任务已结束（超过截止时间）');
      return '已结束';
    }

    // 4. 在有效期内且未完成
    if (now >= startDate && now <= endDate) {
      console.log('任务进行中');
      return '进行中';
    }

    // 兜底状态
    console.log('任务状态异常，使用默认状态');
    return '进行中';
  };

  // 计算完成率
  const calculateCompletionRate = (task: any): number => {
    if (task.assignments && task.assignments.length > 0) {
      const completedCount = task.assignments.filter((assignment: any) =>
        assignment.taskStatus === 2 // 假设2表示已完成
      ).length;
      return Math.round((completedCount / task.assignments.length) * 100);
    }
    return 0;
  };

  // 格式化日期
  const formatDate = (dateString: string): string => {
    if (!dateString) return '';
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}/${month}/${day}`;
  };

  // 组件挂载时获取数据
  useEffect(() => {
    fetchUserSchools();
  }, []);

  // 监听selectedClass变化
  useEffect(() => {
    console.log('selectedClass状态变化:', selectedClass);
  }, [selectedClass]);

  // 当选择的班级改变时，加载任务数据
  useEffect(() => {
    if (selectedClass) {
      console.log('开始加载班级任务数据:', selectedClass.className);
      loadTasksData();
    } else {
      console.log('没有选中班级，清空任务数据');
      setTasks([]);
    }
  }, [selectedClass]);

  // 获取任务数据（使用真实API）
  const loadTasksData = async () => {
    if (!selectedClass) {
      setTasks([]);
      return;
    }

    try {
      setLoading(true);
      const tasksData = await fetchClassTasks(selectedClass.id);
      setTasks(tasksData);
    } catch (error) {
      console.error('加载任务数据失败:', error);
      setTasks([]);
    } finally {
      setLoading(false);
    }
  };

  // 根据筛选条件过滤任务数据
  const getFilteredTasks = (): Task[] => {
    console.log('=== 开始筛选任务数据 ===');
    console.log('原始任务数量:', tasks.length);
    console.log('当前筛选条件:', { statusFilter, startDate, endDate, searchQuery });

    let baseTasks = [...tasks];

    // 根据状态筛选
    if (statusFilter !== '全部') {
      const beforeFilter = baseTasks.length;
      baseTasks = baseTasks.filter(task => task.status === statusFilter);
      console.log(`状态筛选 "${statusFilter}": ${beforeFilter} -> ${baseTasks.length}`);
    } else {
      console.log('状态筛选: 显示全部状态');
    }

    // 根据日期筛选
    if (startDate || endDate) {
      baseTasks = baseTasks.filter(task => {
        // 将任务时间转换为日期字符串进行比较
        const taskDateStr = task.publishTime.split(' ')[0]; // 获取日期部分 "2025/6/21"
        const taskDate = new Date(taskDateStr.replace(/\//g, '-')); // 转换为标准格式

        const start = startDate ? new Date(startDate) : null;
        const end = endDate ? new Date(endDate) : null;

        // 设置时间为当天的开始和结束
        if (start) {
          start.setHours(0, 0, 0, 0);
        }
        if (end) {
          end.setHours(23, 59, 59, 999);
        }

        if (start && end) {
          return taskDate >= start && taskDate <= end;
        } else if (start) {
          return taskDate >= start;
        } else if (end) {
          return taskDate <= end;
        }
        return true;
      });
    }

    // 根据搜索关键词筛选
    if (searchQuery.trim()) {
      baseTasks = baseTasks.filter(task =>
        task.taskName.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    console.log('最终筛选结果:', baseTasks.length);
    console.log('=== 筛选完成 ===');
    return baseTasks;
  };



  const getStatusColor = (status: string) => {
    switch (status) {
      case '已完成':
        return '#52c41a'; // 绿色
      case '进行中':
        return '#1890ff'; // 蓝色
      case '已结束':
        return '#ff4d4f'; // 红色
      case '未开始':
        return '#faad14'; // 黄色
      default:
        return '#d9d9d9'; // 灰色
    }
  };

  // 处理日期输入框点击事件
  const handleDateInputClick = (event: React.MouseEvent<HTMLInputElement>) => {
    const input = event.currentTarget;
    input.showPicker?.();
  };

  // 处理开始日期变化
  const handleStartDateChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newStartDate = event.target.value;
    setStartDate(newStartDate);

    // 如果开始日期晚于结束日期，清空结束日期
    if (endDate && newStartDate > endDate) {
      setEndDate('');
    }
  };

  // 处理结束日期变化
  const handleEndDateChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newEndDate = event.target.value;

    // 如果结束日期早于开始日期，不允许设置
    if (startDate && newEndDate < startDate) {
      alert('结束日期不能早于开始日期');
      return;
    }

    setEndDate(newEndDate);
  };



  // 处理刷新列表
  const handleRefreshList = async () => {
    try {
      setRefreshing(true);


      // 如果没有选择学校，重新获取学校数据
      if (!selectedSchool) {
        console.log('没有选择学校，重新获取学校数据...');
        await fetchUserSchools();
        return;
      }

      // 如果没有选择班级，重新获取班级数据
      if (!selectedClass) {
        console.log('没有选择班级，重新获取班级数据...');
        await fetchSchoolClasses(selectedSchool.id);
        return;
      }

      // 重新获取当前班级的任务数据
      console.log('重新获取班级任务数据...');
      await loadTasksData();

      // 显示刷新成功的提示
      setTimeout(() => {
        const currentTasks = getFilteredTasks();
      }, 100);

    } catch (error) {
      console.error('❌ 刷新任务列表失败:', error);
      // 这里可以添加错误提示给用户
    } finally {
      setRefreshing(false);
    }
  };

  // 重置筛选条件
  const handleResetFilters = () => {
    setStartDate('');
    setEndDate('');
    setStatusFilter('全部');
    setSearchQuery('');
    console.log('已重置所有筛选条件');
  };

  // 处理发布任务按钮点击 - 启动多步骤流程
  const handlePublishTaskClick = async () => {
    // 如果已经选择了学校和班级，直接跳到模板选择步骤
    if (selectedSchool && selectedClass) {
      setModalSelectedSchool(selectedSchool);
      setIsTemplateModalOpen(true);
    } else {
      try {
        // 获取用户的学校列表
        const response = await schoolApi.getUserSchools();

        if (response.data.code === 200) {
          const schoolsData = response.data.data || [];

          if (schoolsData.length === 1) {
            // 只有一个学校，直接选择并跳到班级选择
            setModalSelectedSchool(schoolsData[0]);
            setIsClassModalOpen(true);
          } else if (schoolsData.length > 1) {
            // 多个学校，显示学校选择弹窗
            setIsSchoolModalOpen(true);
          } else {
            // 没有学校，可以显示提示信息
            console.warn('用户没有关联的学校');
          }
        }
      } catch (error) {
        console.error('获取学校列表失败:', error);
        // 出错时仍然显示学校选择弹窗
        setIsSchoolModalOpen(true);
      }
    }
  };

  // 处理学校选择完成
  const handleSchoolSelect = (school: any) => {
    setModalSelectedSchool(school);
    setIsSchoolModalOpen(false);
    setIsClassModalOpen(true);
  };

  // 处理班级选择完成
  const handleClassSelect = (classData: any) => {
    setIsClassModalOpen(false);
    setIsTemplateModalOpen(true);
  };

  // 处理模板选择完成
  const handleTemplateSelect = (modalData: any) => {
    setPublishModalData(modalData);
    setIsTemplateModalOpen(false);
    setIsPublishTaskModalOpen(true);
  };

  // 处理最终发布任务确认
  const handlePublishTaskConfirm = async (taskData: any) => {
    try {
      console.log('发布任务数据:', taskData);
      console.log('模板数据:', publishModalData);

      // 构建任务发布参数
      const publishParams = {
        taskName: taskData.taskName,
        taskDescription: taskData.taskDescription || '',
        taskType: 1,
        priority: 1,
        startDate: new Date(),
        endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7天后
        taskContent: taskData.taskDescription || '',
        templateId: publishModalData.selectedTemplate?.id || 0,
        classId: selectedClass?.id || 0,
        studentIds: publishModalData.selectedStudents.length > 0
          ? publishModalData.selectedStudents
          : students.map(s => s.id),
        selfAssessmentItems: taskData.selfAssessmentItems || [],
        attachments: [],
        allowLateSubmission: true,
        lateSubmissionPolicy: {
          deductionPerDay: 5,
          maxDeductionDays: 7,
          minScore: 0
        }
      };

      // 调用发布任务API
      const response = await taskApi.publishTask(publishParams);

      if (response.data.code === 200) {
        notification.success({
          message: '任务发布成功',
          description: `任务"${taskData.taskName}"已成功发布到${selectedClass?.className}班级`
        });

        // 关闭所有弹窗并重置状态
        setIsPublishTaskModalOpen(false);
        setPublishModalData({
          selectedDistribution: '',
          energyAmount: '',
          selectedTemplate: { id: 0 },
          selectedStudents: []
        });

        // 刷新任务列表
        await loadTasksData();

        // 移除Context通知，只使用全局事件
        // notifyTaskPublished();

        // 触发全局任务发布事件
        const userData = localStorage.getItem('user');
        const eventData: TaskPublishedEventData = {
          taskId: response.data.data?.id || 0,
          taskName: taskData.taskName,
          classId: selectedClass?.id,
          className: selectedClass?.className,
          teacherId: userData ? JSON.parse(userData).userId : 0
        };
        taskEventManager.emit(TASK_EVENTS.TASK_PUBLISHED, eventData);
      } else {
        notification.error({
          message: '任务发布失败',
          description: response.data.msg || '发布任务时发生错误'
        });
      }
    } catch (error) {
      console.error('发布任务失败:', error);
      notification.error({
        message: '任务发布失败',
        description: '网络错误或服务器异常，请稍后重试'
      });
    }
  };

  // 处理任务查看
  const handleViewTask = async (task: Task) => {
    try {
      console.log('查看任务详情:', task);

      // 获取任务详细信息
      const response = await taskApi.getTaskDetail(task.id);
      if (response.data.code === 200) {
        setSelectedTaskForDetail(response.data.data);
        setIsTaskDetailModalVisible(true);
      } else {
        // 如果获取详情失败，使用基本任务信息
        setSelectedTaskForDetail(task);
        setIsTaskDetailModalVisible(true);
      }
    } catch (error) {
      console.error('获取任务详情失败:', error);
      // 即使获取详情失败，也显示基本信息
      setSelectedTaskForDetail(task);
      setIsTaskDetailModalVisible(true);
    }
  };

  // 处理任务编辑
  const handleEditTask = async (task: Task) => {
    try {
      console.log('编辑任务:', task);

      // 获取任务详细信息
      const response = await taskApi.getTaskDetail(task.id);
      if (response.data.code === 200) {
        setSelectedTaskForEdit(response.data.data);
        setIsEditTaskModalVisible(true);
      } else {
        // 如果获取详情失败，使用基本任务信息
        setSelectedTaskForEdit(task);
        setIsEditTaskModalVisible(true);
      }
    } catch (error) {
      console.error('获取任务详情失败:', error);
      // 即使获取详情失败，也显示基本信息
      setSelectedTaskForEdit(task);
      setIsEditTaskModalVisible(true);
    }
  };

  // 处理任务删除
  const handleDeleteTask = (task: Task) => {
    Modal.confirm({
      title: '确认删除任务',
      content: (
        <div>
          <p>确定要删除任务 "{task.taskName}" 吗？</p>
          <p className="text-red-500 mt-2">此操作将同时删除所有学生的任务分配记录，且不可恢复！</p>
        </div>
      ),
      okText: '确定删除',
      cancelText: '取消',
      okButtonProps: { danger: true },
      centered: true,
      onOk: async () => {
        try {
          await taskApi.deleteTask(task.id);
          notification.success({
            message: '删除任务成功',
            description: `任务"${task.taskName}"已成功删除`
          });
          // 刷新任务列表
          await loadTasksData();
        } catch (error: any) {
          notification.error(error.response?.data?.message || '删除任务失败');
        }
      }
    });
  };

  return (
    <div className={styles.classTasksContainer}>
      {/* 顶部标题栏 */}
      <div className={styles.tasksHeader}>
        <h1 className={styles.tasksTitle}>班级任务</h1>
        <div className={styles.headerActions}>
          <div className={styles.searchBox}>
            <Search size={18} className={styles.searchIcon} />
            <input
              type="text"
              placeholder="搜索任务名称、任务内容..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className={styles.searchInput}
            />
          </div>
          <button
            className={styles.publishTaskBtn}
            onClick={handlePublishTaskClick}
          >
            <Plus size={16} />
            发布任务
          </button>
        </div>
      </div>

      {/* 学校选择 */}
      <div className={styles.filterSection}>
        <div className={styles.filterRow}>
          <span className={styles.filterLabel}>学校</span>
          <div className={styles.filterTabs}>
            {loading ? (
              <span>加载中...</span>
            ) : error ? (
              <span style={{ color: '#ff4d4f' }}>{error}</span>
            ) : !Array.isArray(schools) || schools.length === 0 ? (
              <span>暂无数据</span>
            ) : (
              schools.map((school) => (
                <button
                  key={school.id}
                  className={`${styles.filterTab} ${selectedSchool?.id === school.id ? styles.active : ''}`}
                  onClick={() => handleSchoolChange(school)}
                >
                  {school.schoolName}
                </button>
              ))
            )}
          </div>
        </div>

        {/* 班级选择 */}
        <div className={styles.filterRow}>
          <span className={styles.filterLabel}>班级</span>
          <div className={styles.filterTabs}>
            {(() => {
            
              return null;
            })()}
            {selectedSchool ? (
              !Array.isArray(classes) || classes.length === 0 ? (
                <span>加载班级中... (学校: {selectedSchool.schoolName})</span>
              ) : (
                classes.map((classItem) => (
                  <button
                    key={classItem.id}
                    className={`${styles.filterTab} ${selectedClass?.id === classItem.id ? styles.active : ''}`}
                    onClick={() => {
                      console.log('班级按钮被点击:', classItem.className);
                      handleClassChange(classItem);
                    }}
                  >
                    {classItem.className}
                  </button>
                ))
              )
            ) : (
              <span>请先选择学校</span>
            )}
          </div>
        </div>

        {/* 日期选择 */}
        <div className={styles.filterRow}>
          <span className={styles.filterLabel}>选择日期</span>
          <div className={styles.dateRange}>
            <div className={styles.dateInputContainer}>
              <input
                type="date"
                value={startDate}
                onChange={handleStartDateChange}
                onClick={handleDateInputClick}
                className={styles.dateInput}
                min=""
              />
              {!startDate && <span className={styles.customPlaceholder}>开始日期</span>}
            </div>
            <span className={styles.dateSeparator}>至</span>
            <div className={styles.dateInputContainer}>
              <input
                type="date"
                value={endDate}
                onChange={handleEndDateChange}
                onClick={handleDateInputClick}
                className={styles.dateInput}
                min={startDate || ""}
              />
              {!endDate && <span className={styles.customPlaceholder}>结束日期</span>}
            </div>
          </div>
        </div>

        {/* 状态筛选 */}
        <div className={styles.filterRow}>
          <span className={styles.filterLabel}>所有类型</span>
          <div className={styles.filterTabs}>
            {statusOptions.map((status) => (
              <button
                key={status}
                className={`${styles.filterTab} ${statusFilter === status ? styles.active : ''}`}
                onClick={() => setStatusFilter(status)}
              >
                {status}
              </button>
            ))}
          </div>
          <button
            className={styles.refreshButton}
            onClick={handleRefreshList}
            disabled={refreshing}
            style={{
              opacity: refreshing ? 0.6 : 1,
              cursor: refreshing ? 'not-allowed' : 'pointer'
            }}
          >
            <RefreshCw
              size={14}
              style={{
                animation: refreshing ? 'spin 1s linear infinite' : 'none'
              }}
            />
            {refreshing ? '刷新中...' : '刷新列表'}
          </button>
        </div>
      </div>

   

      {/* 任务列表表头 */}
      <div className={styles.tasksTable}>
        <div className={styles.tableHeader}>
          <div className={styles.headerCell}>任务名称</div>
          <div className={styles.headerCell}>状态</div>
          <div className={styles.headerCell}>开始时间</div>
          <div className={styles.headerCell}>截止时间</div>
          <div className={styles.headerCell}>完成率</div>
          <div className={styles.headerCell}>操作</div>
        </div>

        {/* 任务列表 */}
        <div className={styles.tableBody}>
          {getFilteredTasks().length > 0 ? (
            getFilteredTasks().map((task) => (
              <div key={task.id} className={styles.tableRow}>
                <div className={styles.tableCell}>{task.taskName}</div>
                <div className={styles.tableCell}>
                  <span
                    className={styles.statusBadge}
                    style={{ backgroundColor: getStatusColor(task.status) }}
                  >
                    {task.status}
                  </span>
                </div>
                <div className={styles.tableCell}>{task.publishTime}</div>
                <div className={styles.tableCell}>{task.deadline}</div>
                <div className={styles.tableCell}>{task.completionRate}%</div>
                <div className={`${styles.tableCell} ${styles.actions}`}>
                  <button
                    className={`${styles.actionBtn} ${styles.viewBtn}`}
                    onClick={() => handleViewTask(task)}
                  >
                    <Eye size={14} />
                    查看
                  </button>
                  <button
                    className={`${styles.actionBtn} ${styles.editBtn}`}
                    onClick={() => handleEditTask(task)}
                  >
                    <Edit size={14} />
                    编辑
                  </button>
                  <button
                    className={`${styles.actionBtn} ${styles.deleteBtn}`}
                    onClick={() => handleDeleteTask(task)}
                  >
                    <Delete size={14} />
                    删除
                  </button>
                </div>
              </div>
            ))
          ) : (
            <div className={styles.emptyState}>
              <div className={styles.emptyIcon}>
                <svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <circle cx="32" cy="32" r="30" stroke="#E5E7EB" strokeWidth="2" fill="#F9FAFB"/>
                  <path d="M24 28h16M24 32h12M24 36h8" stroke="#9CA3AF" strokeWidth="2" strokeLinecap="round"/>
                  <circle cx="44" cy="20" r="8" fill="#FEF3C7" stroke="#F59E0B" strokeWidth="2"/>
                  <path d="M41 20h6M44 17v6" stroke="#F59E0B" strokeWidth="2" strokeLinecap="round"/>
                </svg>
              </div>
              <div className={styles.emptyTitle}>暂无任务数据</div>
              <div className={styles.emptyDescription}>
                {!selectedSchool ? '请先选择学校' :
                 !selectedClass ? '请先选择班级' :
                 '当前班级暂无任务数据'}
              </div>
            </div>
          )}
        </div>
      </div>
      {/* 多步骤发布任务弹窗 */}
      <SchoolSelectionModal
        isOpen={isSchoolModalOpen}
        onClose={() => setIsSchoolModalOpen(false)}
        onSchoolSelect={handleSchoolSelect}
        actionType="发布任务"
      />

      <ClassSelectionModal
        isOpen={isClassModalOpen}
        onClose={() => setIsClassModalOpen(false)}
        onBack={() => {
          setIsClassModalOpen(false);
          setIsSchoolModalOpen(true);
        }}
        onClassSelect={handleClassSelect}
        selectedSchool={modalSelectedSchool}
        actionType="发布任务"
      />

      <TemplateSelectionModal
        isOpen={isTemplateModalOpen}
        onClose={() => setIsTemplateModalOpen(false)}
        onBack={() => {
          setIsTemplateModalOpen(false);
          setIsClassModalOpen(true);
        }}
        onConfirm={handlePublishTaskConfirm}
        selectedSchool={modalSelectedSchool}
        selectedClass={selectedClass}
        actionType="发布任务"
      />

      <NewPublishTaskModal
        isOpen={isPublishTaskModalOpen}
        onClose={() => setIsPublishTaskModalOpen(false)}
        onBack={() => {
          setIsPublishTaskModalOpen(false);
          setIsClassModalOpen(true);
        }}
        onConfirm={handlePublishTaskConfirm}
        modalData={publishModalData}
      />

      {/* 任务详情查看弹窗 */}
      <TaskDetailModal
        task={selectedTaskForDetail}
        visible={isTaskDetailModalVisible}
        onClose={() => {
          setIsTaskDetailModalVisible(false);
          setSelectedTaskForDetail(null);
        }}
        students={students.map(student => ({
          id: student.id,
          userId: student.id,
          studentNumber: `${student.id}`, // 使用ID作为学号
          nickName: student.name,
          avatarUrl: '', // 默认空头像
          availablePoints: 0,
          totalPoints: 0,
          classId: student.classId,
          className: selectedClass?.className || '',
          schoolId: selectedSchool?.id || 0,
          schoolName: selectedSchool?.schoolName || ''
        }))}
        onRefresh={loadTasksData}
        currentClassId={selectedClass?.id || 0}
      />

      {/* 任务编辑弹窗 */}
      <EditTaskModal
        visible={isEditTaskModalVisible}
        task={selectedTaskForEdit}
        onClose={() => {
          setIsEditTaskModalVisible(false);
          setSelectedTaskForEdit(null);
        }}
        onSuccess={() => {
          // 编辑成功后刷新任务列表
          loadTasksData();
        }}
        currentClassId={selectedClass?.id || 0}
      />
    </div>
  );
};




export default ClassTasks;
