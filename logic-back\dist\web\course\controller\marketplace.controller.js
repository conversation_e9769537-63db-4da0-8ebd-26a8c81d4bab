"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MarketplaceController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const marketplace_service_1 = require("../application/services/marketplace/marketplace.service");
const series_list_dto_1 = require("../application/dto/marketplace/series-list.dto");
const series_detail_dto_1 = require("../application/dto/marketplace/series-detail.dto");
const course_detail_dto_1 = require("../application/dto/marketplace/course-detail.dto");
const tags_list_dto_1 = require("../application/dto/marketplace/tags-list.dto");
const tag_management_dto_1 = require("../application/dto/marketplace/tag-management.dto");
const http_response_result_service_1 = require("../../http_response_result/http_response_result.service");
const not_login_decorator_1 = require("../../router_guard/not-login.decorator");
let MarketplaceController = class MarketplaceController {
    marketplaceService;
    httpResponseResultService;
    constructor(marketplaceService, httpResponseResultService) {
        this.marketplaceService = marketplaceService;
        this.httpResponseResultService = httpResponseResultService;
    }
    async getSeriesList(query) {
        const result = await this.marketplaceService.getSeriesList(query);
        return this.httpResponseResultService.success(result, 'success', 200);
    }
    async getSeriesDetail(seriesId) {
        const result = await this.marketplaceService.getSeriesDetail(seriesId);
        return this.httpResponseResultService.success(result, 'success', 200);
    }
    async getCourseDetail(seriesId, courseId) {
        const result = await this.marketplaceService.getCourseDetail(seriesId, courseId);
        return this.httpResponseResultService.success(result, 'success', 200);
    }
    async getTagsList(query) {
        const result = await this.marketplaceService.getTagsList(query);
        return this.httpResponseResultService.success(result, 'success', 200);
    }
    async createTag(createTagDto) {
        const result = await this.marketplaceService.createTag(createTagDto);
        return this.httpResponseResultService.success(result, '标签创建成功', 200);
    }
    async updateTag(id, updateTagDto) {
        const result = await this.marketplaceService.updateTag(id, updateTagDto);
        return this.httpResponseResultService.success(result, '标签更新成功', 200);
    }
    async deleteTag(id) {
        await this.marketplaceService.deleteTag(id);
        return this.httpResponseResultService.success(null, '标签删除成功', 200);
    }
    async getTagById(id) {
        const result = await this.marketplaceService.getTagById(id);
        return this.httpResponseResultService.success(result, 'success', 200);
    }
};
exports.MarketplaceController = MarketplaceController;
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '查询系列课程列表',
        description: '获取课程广场的系列课程列表，支持分页、分类筛选和标签筛选'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '查询成功',
        type: series_list_dto_1.SeriesListResponseDto,
        examples: {
            success: {
                summary: '成功响应',
                value: {
                    code: 200,
                    message: 'success',
                    data: {
                        list: [
                            {
                                id: 1,
                                title: 'Python编程入门系列',
                                description: '适合零基础学员的Python课程',
                                coverImage: 'https://example.com/cover1.jpg',
                                category: 0,
                                categoryLabel: '官方',
                                status: 1,
                                statusLabel: '已发布',
                                projectMembers: '张老师、李助教',
                                totalCourses: 5,
                                totalStudents: 1200,
                                contentSummary: {
                                    hasVideo: true,
                                    hasDocument: true,
                                    hasAudio: false,
                                    videoCourseCount: 4,
                                    documentCourseCount: 5,
                                    averageVideoDuration: 1800,
                                    totalResourcesCount: 15
                                },
                                createdAt: '2024-01-15T10:30:00Z',
                                tags: [
                                    {
                                        id: 1,
                                        name: '编程',
                                        color: '#007bff',
                                        category: 1,
                                        categoryLabel: '类型'
                                    },
                                    {
                                        id: 2,
                                        name: '入门',
                                        color: '#28a745',
                                        category: 0,
                                        categoryLabel: '难度'
                                    }
                                ]
                            }
                        ],
                        pagination: {
                            page: 1,
                            pageSize: 10,
                            total: 25,
                            totalPages: 3,
                            hasNext: true,
                            hasPrev: false
                        },
                        filterStats: {
                            totalSeries: 25,
                            officialCount: 18,
                            communityCount: 7,
                            videoSeriesCount: 20,
                            documentSeriesCount: 22
                        }
                    }
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: '请求参数错误',
        examples: {
            badRequest: {
                summary: '参数错误',
                value: {
                    code: 400,
                    message: '页码必须大于0',
                    data: null
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: '服务器内部错误',
        examples: {
            serverError: {
                summary: '服务器错误',
                value: {
                    code: 500,
                    message: '服务器内部错误，请稍后重试',
                    data: null
                }
            }
        }
    }),
    (0, common_1.Get)('series'),
    (0, not_login_decorator_1.NotLogin)(),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [series_list_dto_1.GetSeriesListQueryDto]),
    __metadata("design:returntype", Promise)
], MarketplaceController.prototype, "getSeriesList", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '查询系列课程详情及子课程',
        description: '获取系列课程详情，包含该系列下所有已发布的子课程，默认返回第一个课程作为当前显示课程'
    }),
    (0, swagger_1.ApiParam)({ name: 'seriesId', description: '系列课程ID', type: Number }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '查询成功',
        type: series_detail_dto_1.SeriesDetailResponseDto,
        examples: {
            success: {
                summary: '成功响应',
                value: {
                    code: 200,
                    message: 'success',
                    data: {
                        id: 123,
                        title: 'React前端开发系列',
                        description: '从零开始学习React开发，包含基础概念、组件开发、状态管理等核心内容',
                        coverImage: 'https://example.com/series-cover.jpg',
                        category: 0,
                        categoryLabel: '官方',
                        status: 1,
                        statusLabel: '已发布',
                        projectMembers: '张老师、李助教、王同学',
                        totalCourses: 3,
                        totalStudents: 850,
                        creatorId: 100,
                        createdAt: '2024-01-15T10:30:00Z',
                        updatedAt: '2024-01-22T09:15:00Z',
                        tags: [
                            {
                                id: 1,
                                name: '前端开发',
                                color: '#007bff',
                                category: 1,
                                categoryLabel: '类型'
                            }
                        ],
                        courses: [
                            {
                                id: 1,
                                title: '第一课：React基础概念',
                                description: '了解React的核心概念和基本用法',
                                coverImage: 'https://example.com/course1-cover.jpg',
                                orderIndex: 1,
                                status: 1,
                                statusLabel: '已发布',
                                hasVideo: 1,
                                hasDocument: 1,
                                hasAudio: 0,
                                videoDuration: 1800,
                                videoDurationLabel: '30分钟',
                                videoName: 'React基础概念讲解.mp4',
                                firstTeachingTitle: '教学目标',
                                resourcesCount: 2
                            }
                        ],
                        defaultCourse: {
                            id: 1,
                            title: '第一课：React基础概念',
                            contentConfig: {
                                hasVideo: 1,
                                hasDocument: 1,
                                video: {
                                    url: 'https://example.com/videos/react-basics.mp4',
                                    name: 'React基础概念讲解.mp4'
                                }
                            },
                            teachingInfo: [
                                {
                                    title: '教学目标',
                                    content: ['理解React的核心概念']
                                }
                            ],
                            additionalResources: [
                                {
                                    title: 'React官方文档',
                                    url: 'https://react.dev/',
                                    description: 'React官方学习资源'
                                }
                            ]
                        },
                        currentCourseId: 1
                    }
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: '系列课程不存在',
        examples: {
            notFound: {
                summary: '系列不存在',
                value: {
                    code: 404,
                    message: '系列课程不存在',
                    data: null
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: '服务器内部错误',
        examples: {
            serverError: {
                summary: '服务器错误',
                value: {
                    code: 500,
                    message: '服务器内部错误，请稍后重试',
                    data: null
                }
            }
        }
    }),
    (0, common_1.Get)('series/:seriesId'),
    (0, not_login_decorator_1.NotLogin)(),
    __param(0, (0, common_1.Param)('seriesId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], MarketplaceController.prototype, "getSeriesDetail", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '切换到指定子课程',
        description: '获取系列中指定课程的详细内容'
    }),
    (0, swagger_1.ApiParam)({ name: 'seriesId', description: '系列课程ID', type: Number }),
    (0, swagger_1.ApiParam)({ name: 'courseId', description: '子课程ID', type: Number }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '查询成功',
        type: course_detail_dto_1.CourseDetailResponseDto,
        examples: {
            success: {
                summary: '成功响应',
                value: {
                    code: 200,
                    message: 'success',
                    data: {
                        id: 2,
                        title: '第二课：组件与Props',
                        description: '学习React组件的创建和属性传递',
                        coverImage: 'https://example.com/course2-cover.jpg',
                        hasVideo: 1,
                        hasDocument: 1,
                        hasAudio: 1,
                        videoDuration: 2400,
                        videoDurationLabel: '40分钟',
                        videoName: 'React组件与Props.mp4',
                        resourcesCount: 3,
                        contentConfig: {
                            hasVideo: 1,
                            hasDocument: 1,
                            hasAudio: 1,
                            video: {
                                url: 'https://example.com/videos/react-components.mp4',
                                name: 'React组件与Props.mp4'
                            },
                            document: {
                                url: 'https://example.com/documents/react-components-slides.pdf',
                                name: 'React组件与Props课件.pdf'
                            },
                            audio: {
                                url: 'https://example.com/audio/react-components.mp3',
                                name: 'React组件与Props音频.mp3'
                            }
                        },
                        teachingInfo: [
                            {
                                title: '教学目标',
                                content: [
                                    '理解React组件的概念和作用',
                                    '掌握函数组件和类组件的创建方法'
                                ]
                            }
                        ],
                        additionalResources: [
                            {
                                title: 'React组件文档',
                                url: 'https://react.dev/learn/your-first-component',
                                description: 'React官方组件学习指南'
                            }
                        ],
                        orderIndex: 2,
                        status: 1,
                        statusLabel: '已发布',
                        currentCourseId: 2
                    }
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: '系列或课程不存在',
        examples: {
            seriesNotFound: {
                summary: '系列不存在',
                value: {
                    code: 404,
                    message: '系列课程不存在，ID: 123',
                    data: null
                }
            },
            courseNotFound: {
                summary: '课程不存在',
                value: {
                    code: 404,
                    message: '课程不存在或未发布，系列ID: 123，课程ID: 999',
                    data: null
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: '服务器内部错误',
        examples: {
            serverError: {
                summary: '服务器错误',
                value: {
                    code: 500,
                    message: '服务器内部错误，请稍后重试',
                    data: null
                }
            }
        }
    }),
    (0, common_1.Get)('series/:seriesId/courses/:courseId'),
    (0, not_login_decorator_1.NotLogin)(),
    __param(0, (0, common_1.Param)('seriesId')),
    __param(1, (0, common_1.Param)('courseId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number]),
    __metadata("design:returntype", Promise)
], MarketplaceController.prototype, "getCourseDetail", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '获取课程标签列表',
        description: '获取所有激活状态的课程标签，用于筛选'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '查询成功',
        type: tags_list_dto_1.TagsListResponseDto,
        examples: {
            success: {
                summary: '成功响应',
                value: {
                    code: 200,
                    message: 'success',
                    data: {
                        list: [
                            {
                                id: 1,
                                name: '编程',
                                color: '#007bff',
                                category: 1,
                                categoryLabel: '类型',
                                description: '编程相关课程',
                                usageCount: 25,
                                status: 1,
                                statusLabel: '启用',
                                createdAt: '2024-01-10T10:30:00Z'
                            },
                            {
                                id: 2,
                                name: '入门',
                                color: '#28a745',
                                category: 0,
                                categoryLabel: '官方',
                                description: '适合初学者的课程',
                                usageCount: 18,
                                status: 1,
                                statusLabel: '启用',
                                createdAt: '2024-01-10T10:30:00Z'
                            }
                        ],
                        pagination: {
                            page: 1,
                            pageSize: 50,
                            total: 12,
                            totalPages: 1,
                            hasNext: false,
                            hasPrev: false
                        }
                    }
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: '请求参数错误',
        examples: {
            badRequest: {
                summary: '参数错误',
                value: {
                    code: 400,
                    message: '页码必须大于0',
                    data: null
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: '服务器内部错误',
        examples: {
            serverError: {
                summary: '服务器错误',
                value: {
                    code: 500,
                    message: '服务器内部错误，请稍后重试',
                    data: null
                }
            }
        }
    }),
    (0, common_1.Get)('tags'),
    (0, not_login_decorator_1.NotLogin)(),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [tags_list_dto_1.GetTagsListQueryDto]),
    __metadata("design:returntype", Promise)
], MarketplaceController.prototype, "getTagsList", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '创建标签',
        description: '创建新的课程标签'
    }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: '创建成功',
        type: tag_management_dto_1.TagDetailResponseDto,
        examples: {
            success: {
                summary: '成功响应',
                value: {
                    code: 200,
                    message: '标签创建成功',
                    data: {
                        id: 1,
                        name: '编程',
                        color: '#007bff',
                        category: 1,
                        categoryLabel: '类型',
                        description: '编程相关课程',
                        usageCount: 0,
                        status: 1,
                        statusLabel: '启用',
                        createdAt: '2024-01-10T10:30:00Z',
                        updatedAt: '2024-01-10T10:30:00Z'
                    }
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: '请求参数错误或标签名称已存在',
        examples: {
            nameExists: {
                summary: '标签名称已存在',
                value: {
                    code: 400,
                    message: '标签名称 "编程" 已存在',
                    data: null
                }
            },
            validationError: {
                summary: '参数验证错误',
                value: {
                    statusCode: 400,
                    message: ['标签名称不能为空', '标签分类必须是 0=难度，1=类型，2=特色，3=其他 中的一个'],
                    error: 'Bad Request'
                }
            }
        }
    }),
    (0, common_1.Post)('tags'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [tag_management_dto_1.CreateTagDto]),
    __metadata("design:returntype", Promise)
], MarketplaceController.prototype, "createTag", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '更新标签',
        description: '更新指定标签的信息'
    }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '标签ID', type: Number }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '更新成功',
        type: tag_management_dto_1.TagDetailResponseDto
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: '标签不存在',
        examples: {
            notFound: {
                summary: '标签不存在',
                value: {
                    code: 404,
                    message: '标签不存在，ID: 999',
                    data: null
                }
            }
        }
    }),
    (0, common_1.Put)('tags/:id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, tag_management_dto_1.UpdateTagDto]),
    __metadata("design:returntype", Promise)
], MarketplaceController.prototype, "updateTag", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '删除标签',
        description: '删除指定的标签'
    }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '标签ID', type: Number }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '删除成功',
        type: tag_management_dto_1.DeleteTagResponseDto,
        examples: {
            success: {
                summary: '成功响应',
                value: {
                    code: 200,
                    message: '标签删除成功',
                    data: null
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: '标签不存在',
        examples: {
            notFound: {
                summary: '标签不存在',
                value: {
                    code: 404,
                    message: '标签不存在，ID: 999',
                    data: null
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: '标签正在使用中，无法删除',
        examples: {
            inUse: {
                summary: '标签正在使用',
                value: {
                    code: 400,
                    message: '标签正在被 5 个系列课程使用，无法删除',
                    data: null
                }
            }
        }
    }),
    (0, common_1.Delete)('tags/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], MarketplaceController.prototype, "deleteTag", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: '获取标签详情',
        description: '获取指定标签的详细信息'
    }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '标签ID', type: Number }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '查询成功',
        type: tag_management_dto_1.TagDetailResponseDto
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: '标签不存在',
        examples: {
            notFound: {
                summary: '标签不存在',
                value: {
                    code: 404,
                    message: '标签不存在，ID: 999',
                    data: null
                }
            }
        }
    }),
    (0, common_1.Get)('tags/:id'),
    (0, not_login_decorator_1.NotLogin)(),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], MarketplaceController.prototype, "getTagById", null);
exports.MarketplaceController = MarketplaceController = __decorate([
    (0, swagger_1.ApiTags)('课程广场'),
    (0, common_1.Controller)('api/v1/course-marketplace'),
    (0, swagger_1.ApiBearerAuth)("access-token"),
    __metadata("design:paramtypes", [marketplace_service_1.MarketplaceService,
        http_response_result_service_1.HttpResponseResultService])
], MarketplaceController);
//# sourceMappingURL=marketplace.controller.js.map