import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Ta<PERSON>, <PERSON><PERSON>, Dropdown, Tag } from 'antd';
import { BlockOutlined, EditOutlined, DeleteOutlined, EllipsisOutlined, EyeOutlined, AppstoreOutlined, ArrowLeftOutlined, FolderOutlined, StarOutlined, PlusOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import { PermissionTemplateDisplay } from '../../types/index';
import { useState, useEffect } from 'react';
import PermissionModal from '@/components/permission-modal';
import PermissionTemplateModal from '@/components/permission-template-modal';
import { roleTemplateFolderApi } from '../../../../lib/api/role-template-folder';
import { getOfficialTemplates, getTemplateInfo } from '../../../../lib/api/role';

interface AssignBlocksModalProps {
  visible: boolean;
  onCancel: () => void;
  isClassCardAssign: boolean;
  loadingTemplates: boolean;
  templates: PermissionTemplateDisplay[];
  studentTemplateUsage: Record<number, number>;
  teacherTemplate?: { templateId: number };
  onSelectTemplate: (templateId: number) => void;
  onTemplateUsageClick: (e: React.MouseEvent, template: PermissionTemplateDisplay) => void;
  userId?: number;
  onRefreshTemplates?: () => void; // 新增：刷新模板列表的回调
}

export const AssignBlocksModal: React.FC<AssignBlocksModalProps> = ({
  visible,
  onCancel,
  isClassCardAssign,
  loadingTemplates,
  templates,
  studentTemplateUsage,
  teacherTemplate,
  onSelectTemplate,
  onTemplateUsageClick,
  userId = 0,
  onRefreshTemplates,
}) => {
  const [activeTab, setActiveTab] = useState('my');
  const [isPermissionModalVisible, setIsPermissionModalVisible] = useState(false);
  const [isCreateTemplateModalVisible, setIsCreateTemplateModalVisible] = useState(false);
  const [selectedTemplateId, setSelectedTemplateId] = useState<number | null>(null);
  const [currentView, setCurrentView] = useState<'folders' | 'templates'>('folders');
  const [currentFolder, setCurrentFolder] = useState<any>(null);
  const [folders, setFolders] = useState<any[]>([]);
  const [loadingFolders, setLoadingFolders] = useState(false);
  const [folderTemplates, setFolderTemplates] = useState<PermissionTemplateDisplay[]>([]);
  const [loadingFolderTemplates, setLoadingFolderTemplates] = useState(false);
  const [specialTemplate, setSpecialTemplate] = useState<PermissionTemplateDisplay | null>(null);
  const [loadingSpecialTemplate, setLoadingSpecialTemplate] = useState(false);

  //console.log('AssignBlocksModal 渲染 - activeTab:', activeTab, 'currentView:', currentView);

  // 处理模板点击，显示模板详情
  const handleTemplateClick = (template: PermissionTemplateDisplay) => {
    //console.log('handleTemplateClick 被调用', template.id, template.templateName);
    setSelectedTemplateId(template.id);
    setIsPermissionModalVisible(true);
  };

  // 处理创建模板按钮点击
  const handleCreateTemplate = () => {
    setIsCreateTemplateModalVisible(true);
  };

  // 处理模板创建成功
  const handleCreateTemplateSuccess = () => {
    // 关闭创建模板弹窗
    setIsCreateTemplateModalVisible(false);

    // 刷新模板列表
    if (onRefreshTemplates) {
      onRefreshTemplates();
    }

    // 切换到"我的模板"标签页，因为新创建的模板会在这里
    setActiveTab('my');

    // 如果当前在官方模板的文件夹视图中，也需要刷新
    if (activeTab === 'official' && currentView === 'folders' && currentFolder) {
      // 刷新文件夹内的模板
      handleFolderClick(currentFolder);
    } else if (activeTab === 'official' && currentView === 'folders') {
      // 刷新文件夹列表和特殊模板
      fetchFolders();
      fetchSpecialTemplate();
    }
  };

  // 获取特殊模板（userId为-1的模板）
  const fetchSpecialTemplate = async () => {
    if (activeTab !== 'official') return;

    setLoadingSpecialTemplate(true);
    try {
      const response = await getOfficialTemplates();
      if (response.data.code === 200) {
        // 查找userId为-1的模板
        const specialTemplateData = response.data.data.find((template: any) => template.userId === -1);

        if (specialTemplateData) {
          // 获取模板的详细信息
          const templateInfoResponse = await getTemplateInfo(specialTemplateData.id);
          console.log('获取特殊模板响应2:', templateInfoResponse);
          if (templateInfoResponse.data.code === 200) {
            const templateData = templateInfoResponse.data.data;
            setSpecialTemplate({
              id: templateData.id,
              templateName: templateData.templateName,
              templateDescription: templateData.templateDescription || '',
              createTime: templateData.createTime,
              isDefault: false,
              isOfficial: true,
              lastModified: templateData.updateTime || templateData.createTime
            });
          }
        }
      }
    } catch (error) {
      console.error('获取特殊模板失败:', error);
    } finally {
      setLoadingSpecialTemplate(false);
    }
  };

  // 获取文件夹列表
  const fetchFolders = async () => {

    if (activeTab !== 'official') return;
    // //console.log('activeTab', activeTab);
    setLoadingFolders(true);
    try {
      const res = await roleTemplateFolderApi.getFolderList();
      console.log('res', res);
      if (res.data.code === 200) {
        // 获取每个文件夹的模板数量
        const foldersWithCount = await Promise.all(

          res.data.data.data.map(async (folder: any) => {
            const templatesRes = await roleTemplateFolderApi.getFolderTemplates(folder.id);
            return {
              ...folder,
              templateCount: templatesRes.data.code === 200 ? templatesRes.data.data.data.length : 0
            };
          })
        );
        // //console.log('foldersWithCount', foldersWithCount);
        setFolders(foldersWithCount);
      }
    } catch (error) {
      console.error('获取文件夹列表失败:', error);
    } finally {
      setLoadingFolders(false);
    }
  };

  // 处理文件夹点击
  const handleFolderClick = async (folder: any) => {
    setCurrentFolder(folder);
    setCurrentView('templates');
    setFolderTemplates([]); // 先清空之前的数据
    setLoadingFolderTemplates(true); // 开始加载时显示加载动画

    try {
      const folderRes = await roleTemplateFolderApi.getFolderTemplates(folder.id);
      console.log("zww", folderRes);

      if (folderRes.data.code === 200) {
        const templatePromises = folderRes.data.data.data.map((item: { id: number }) =>
          roleTemplateFolderApi.getTemplateInfo(item.id)
        );
        console.log('获取文件夹模板响应:', templatePromises);
        const templateResults = await Promise.all(templatePromises);
        const templates = templateResults
          .filter(res => res.data.code === 200)
          .map(res => {
            const templateData = res.data.data;
            return {
              id: templateData.id,
              templateName: templateData.templateName,
              templateDescription: templateData.templateDescription || '',
              createTime: templateData.createTime,
              isDefault: false,
              isOfficial: true,
              lastModified: templateData.updateTime || templateData.createTime,
              roleId: templateData.roleId,
              userId: templateData.userId,
              status: templateData.status
            };
          });

        setFolderTemplates(templates);
      }
    } catch (error) {

      console.error('获取文件夹模板失败-分配模板界面:', error);
    } finally {
      setLoadingFolderTemplates(false); // 无论成功失败都关闭加载动画
    }
  };

  // 返回文件夹列表
  const handleBackToFolders = () => {
    setCurrentView('folders');
    setCurrentFolder(null);
    setFolderTemplates([]); // 清空模板列表数据
  };

  // 文件夹列表组件
  const FolderList = () => (
    <div className="grid grid-cols-1 gap-3">
      {/* 特殊模板（userId为-1的模板）置顶显示 */}
      {specialTemplate && (
        <div
          key={`special-${specialTemplate.id}`}
          className="bg-white border border-gray-100 p-4 rounded-xl cursor-pointer hover:shadow-md transition-all group relative z-10"
          style={{ position: 'relative' }}
          data-template-id={specialTemplate.id}
          data-testid={`special-template-${specialTemplate.id}`}
        >
          <div className="flex items-center justify-between gap-4">
            <div className="flex items-center gap-3 flex-1 min-w-0">
              <div className="w-10 h-10 rounded-lg bg-amber-50 group-hover:bg-amber-100 flex-shrink-0 flex items-center justify-center transition-colors">
                <StarOutlined className="text-amber-500 text-lg" />
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium text-gray-800 group-hover:text-blue-600 transition-colors truncate max-w-[calc(100%-70px)]">
                    {specialTemplate.templateName}
                  </span>
                  <Tag color="gold" className="flex-shrink-0 rounded-full text-xs">
                    推荐
                  </Tag>
                  {teacherTemplate?.templateId === specialTemplate.id && (
                    <Tag color="green" className="flex-shrink-0 rounded-full text-xs">
                      已使用
                    </Tag>
                  )}
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  推荐使用的模板
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2 flex-shrink-0">
              <Button
                type="default"
                size="small"
                className="flex-shrink-0"
                onClick={(e) => {
                  e.stopPropagation();
                  handleTemplateClick(specialTemplate);
                }}
              >
                查看详情
              </Button>
              <Button
                type={teacherTemplate?.templateId === specialTemplate.id ? "default" : "primary"}
                size="small"
                className={teacherTemplate?.templateId === specialTemplate.id ? "flex-shrink-0" : "bg-blue-500 flex-shrink-0"}
                onClick={(e) => {
                  e.stopPropagation();
                  if (teacherTemplate?.templateId !== specialTemplate.id) {
                    onSelectTemplate(specialTemplate.id);
                  }
                }}
                disabled={teacherTemplate?.templateId === specialTemplate.id}
              >
                {teacherTemplate?.templateId === specialTemplate.id ? "当前已使用此模板" : "使用此模板"}
              </Button>
            </div>
          </div>
          <div className="mt-3 text-xs text-gray-500">
            {studentTemplateUsage[specialTemplate.id] ? (
              <span
                className="cursor-pointer hover:text-blue-500 transition-colors"
                onClick={(e) => {
                  e.stopPropagation();
                  onTemplateUsageClick(e, specialTemplate);
                }}
              >
                {studentTemplateUsage[specialTemplate.id]} 名学生正在使用
              </span>
            ) : (
              <span>暂无学生使用</span>
            )}
          </div>
          {/* 添加覆盖整个卡片的可点击区域 */}
          <div
            className="absolute inset-0 cursor-pointer z-0"
            onClick={() => {
              //console.log('点击特殊模板容器 - 绝对定位层', specialTemplate.id);
              onSelectTemplate(specialTemplate.id);
            }}
          />
        </div>
      )}

      {/* 文件夹列表 */}
      {folders.map(folder => (
        <div
          key={folder.id}
          className="bg-white border border-gray-100 p-4 rounded-xl cursor-pointer hover:shadow-md transition-all group relative z-10"
          style={{ position: 'relative' }}
          data-folder-id={folder.id}
          data-testid={`folder-${folder.id}`}
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 rounded-lg bg-blue-50 flex items-center justify-center group-hover:bg-blue-100 transition-colors">
                <FolderOutlined className="text-blue-500 text-lg" />
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-800 group-hover:text-blue-600 transition-colors">
                  {folder.folderName}
                  <Tag color="gold" className="ml-2 rounded-full text-xs">官方</Tag>
                </h3>
                <p className="text-xs text-gray-400 mt-0.5">
                  {folder.templateCount || 0} 个模板
                </p>
              </div>
            </div>
          </div>
          {/* 添加覆盖整个卡片的可点击区域 */}
          <div
            className="absolute inset-0 cursor-pointer z-0"
            onClick={() => {
              //console.log('点击文件夹 - 绝对定位层', folder.id, folder.folderName);
              handleFolderClick(folder);
            }}
          />
        </div>
      ))}
    </div>
  );

  // 当切换到官方模板标签时加载文件夹和特殊模板
  useEffect(() => {
    if (activeTab === 'official' && currentView === 'folders') {
      fetchFolders();
      fetchSpecialTemplate();
    }
  }, [activeTab, currentView]);

  // 监听模态框显示状态
  useEffect(() => {
    if (visible && activeTab === 'official' && currentView === 'folders') {
      fetchFolders();
      fetchSpecialTemplate();
    }
  }, [visible]);

  return (
    <Modal
      title={
        <div className="flex items-center gap-4 pr-8">
          <span>分配积木</span>
          <div className="flex items-center gap-2 flex-1">
            <Button
              type={activeTab === 'my' ? 'primary' : 'default'}
              onClick={() => {
                //console.log('切换到我的模板');
                setActiveTab('my');
                setCurrentView('templates');
              }}
              className="rounded-full min-w-[80px] whitespace-nowrap"
              size="small"
            >
              我的模板
            </Button>
            <Button
              type={activeTab === 'official' ? 'primary' : 'default'}
              onClick={() => {
                //console.log('切换到官方模板');
                setActiveTab('official');
                setCurrentView('folders');
              }}
              className="rounded-full min-w-[80px] whitespace-nowrap"
              size="small"
            >
              官方模板
            </Button>
          </div>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            size="small"
            onClick={handleCreateTemplate}
            className="rounded-full flex items-center"
          >
            创建模板
          </Button>
        </div>
      }
      open={visible}
      onCancel={onCancel}
      footer={null}
      width={800}
      centered
      modalRender={(modal) => (
        <div style={{
          margin: '20px 0',
          display: 'flex',
          flexDirection: 'column',
          height: 'fit-content',
          maxHeight: '90vh'
        }}>
          {modal}
        </div>
      )}
      style={{
        maxWidth: '90vw',
        top: 0,
        paddingBottom: 0
      }}
      styles={{
        body: {
          padding: '24px',
          maxHeight: 'calc(90vh - 110px)',
          overflow: 'auto'
        }
      }}
      className="custom-scrollbar-modal"
    >
      <style jsx global>{`
        .custom-scrollbar-modal .ant-modal-body {
          scrollbar-width: thin;
          scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
        }
        .custom-scrollbar-modal .ant-modal-body::-webkit-scrollbar {
          width: 6px;
        }
        .custom-scrollbar-modal .ant-modal-body::-webkit-scrollbar-thumb {
          background-color: rgba(0, 0, 0, 0.2);
          border-radius: 3px;
          transition: background-color 0.3s;
        }
        .custom-scrollbar-modal .ant-modal-body::-webkit-scrollbar-thumb:hover {
          background-color: rgba(0, 0, 0, 0.3);
        }
        .custom-scrollbar-modal .ant-modal-body::-webkit-scrollbar-track {
          background-color: transparent;
        }
        
        /* 确保官方模板可点击 */
        .cursor-pointer {
          cursor: pointer !important;
          pointer-events: auto !important;
        }
        
        /* 提高模板卡片的z-index，防止被覆盖 */
        .group.relative {
          z-index: 10 !important;
        }
      `}</style>
      <div className="space-y-3">
        {activeTab === 'official' && currentView === 'folders' ? (
          // 官方模板文件夹视图
          loadingFolders || loadingSpecialTemplate ? (
            <div className="flex justify-center py-8">
              <Spin />
            </div>
          ) : (
            <div className="grid grid-cols-1 gap-3">
              {/* 特殊模板（userId为-1的模板）置顶显示 */}
              {specialTemplate && (
                <div
                  key={`special-${specialTemplate.id}`}
                  className="bg-white border border-gray-100 p-4 rounded-xl cursor-pointer hover:shadow-md transition-all group relative z-10"
                  style={{ position: 'relative' }}
                  data-template-id={specialTemplate.id}
                  data-testid={`special-template-${specialTemplate.id}`}
                >
                  <div className="flex items-center justify-between gap-4">
                    <div className="flex items-center gap-3 flex-1 min-w-0">
                      <div className="w-10 h-10 rounded-lg bg-amber-50 group-hover:bg-amber-100 flex-shrink-0 flex items-center justify-center transition-colors">
                        <StarOutlined className="text-amber-500 text-lg" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2">
                          <span className="text-sm font-medium text-gray-800 group-hover:text-blue-600 transition-colors truncate max-w-[calc(100%-70px)]">
                            {specialTemplate.templateName}
                          </span>
                          <Tag color="gold" className="flex-shrink-0 rounded-full text-xs">
                            推荐
                          </Tag>
                          {teacherTemplate?.templateId === specialTemplate.id && (
                            <Tag color="green" className="flex-shrink-0 rounded-full text-xs">
                              已使用
                            </Tag>
                          )}
                        </div>
                        <p className="text-xs text-gray-500 mt-1">
                          推荐使用的模板
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2 flex-shrink-0">
                      <Button
                        type="default"
                        size="small"
                        className="flex-shrink-0"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleTemplateClick(specialTemplate);
                        }}
                      >
                        查看详情
                      </Button>
                      <Button
                        type={teacherTemplate?.templateId === specialTemplate.id ? "default" : "primary"}
                        size="small"
                        className={teacherTemplate?.templateId === specialTemplate.id ? "flex-shrink-0" : "bg-blue-500 flex-shrink-0"}
                        onClick={(e) => {
                          e.stopPropagation();
                          if (teacherTemplate?.templateId !== specialTemplate.id) {
                            onSelectTemplate(specialTemplate.id);
                          }
                        }}
                        disabled={teacherTemplate?.templateId === specialTemplate.id}
                      >
                        {teacherTemplate?.templateId === specialTemplate.id ? "当前已使用此模板" : "使用此模板"}
                      </Button>
                    </div>
                  </div>
                  <div className="mt-3 text-xs text-gray-500">
                    {studentTemplateUsage[specialTemplate.id] ? (
                      <span
                        className="cursor-pointer hover:text-blue-500 transition-colors"
                        onClick={(e) => {
                          e.stopPropagation();
                          onTemplateUsageClick(e, specialTemplate);
                        }}
                      >
                        {studentTemplateUsage[specialTemplate.id]} 名学生正在使用
                      </span>
                    ) : (
                      <span>暂无学生使用</span>
                    )}
                  </div>
                  {/* 添加覆盖整个卡片的可点击区域 */}
                  <div
                    className="absolute inset-0 cursor-pointer z-0"
                    onClick={() => {
                      //console.log('点击特殊模板容器 - 绝对定位层', specialTemplate.id);
                      onSelectTemplate(specialTemplate.id);
                    }}
                  />
                </div>
              )}

              {/* 文件夹列表 */}
              {folders.map(folder => (
                <div
                  key={folder.id}
                  className="bg-white border border-gray-100 p-4 rounded-xl cursor-pointer hover:shadow-md transition-all group relative z-10"
                  style={{ position: 'relative' }}
                  data-folder-id={folder.id}
                  data-testid={`folder-${folder.id}`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 rounded-lg bg-blue-50 flex items-center justify-center group-hover:bg-blue-100 transition-colors">
                        <FolderOutlined className="text-blue-500 text-lg" />
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-gray-800 group-hover:text-blue-600 transition-colors">
                          {folder.folderName}
                          <Tag color="gold" className="ml-2 rounded-full text-xs">官方</Tag>
                        </h3>
                        <p className="text-xs text-gray-400 mt-0.5">
                          {folder.templateCount || 0} 个模板
                        </p>
                      </div>
                    </div>
                  </div>
                  {/* 添加覆盖整个卡片的可点击区域 */}
                  <div
                    className="absolute inset-0 cursor-pointer z-0"
                    onClick={() => {
                      //console.log('点击文件夹 - 绝对定位层', folder.id, folder.folderName);
                      handleFolderClick(folder);
                    }}
                  />
                </div>
              ))}
            </div>
          )
        ) : activeTab === 'official' && currentView === 'templates' ? (
          // 文件夹内模板列表视图
          <>
            <div className="flex items-center mb-4">
              <Button
                icon={<ArrowLeftOutlined />}
                onClick={handleBackToFolders}
                size="small"
                className="mr-2"
              >
                返回
              </Button>
              <span className="text-base font-medium">
                {currentFolder?.folderName}
              </span>
            </div>
            {loadingFolderTemplates ? (
              <div className="flex justify-center py-8">
                <Spin />
              </div>
            ) : (
              folderTemplates.map(template => (
                <div
                  key={template.id}
                  className="bg-white border border-gray-100 p-4 rounded-xl cursor-pointer hover:shadow-md transition-all group relative z-10"
                  style={{ position: 'relative' }}
                  data-template-id={template.id}
                  data-testid={`folder-template-${template.id}`}
                >
                  <div className="flex items-center justify-between gap-4">
                    <div className="flex items-center gap-3 flex-1 min-w-0">
                      <div className="w-10 h-10 rounded-lg bg-amber-50 group-hover:bg-amber-100 flex-shrink-0 flex items-center justify-center transition-colors">
                        <BlockOutlined className="text-amber-500 text-lg" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2">
                          <span className="text-sm font-medium text-gray-800 group-hover:text-blue-600 transition-colors truncate max-w-[calc(100%-70px)]">
                            {template.templateName}
                          </span>
                          <Tag color="gold" className="flex-shrink-0 rounded-full text-xs">
                            官方
                          </Tag>
                          {teacherTemplate?.templateId === template.id && (
                            <Tag color="green" className="flex-shrink-0 rounded-full text-xs">
                              已使用
                            </Tag>
                          )}
                        </div>
                        <p className="text-xs text-gray-500 mt-1">
                          创建于 {dayjs(template.createTime).format('YYYY-MM-DD')}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2 flex-shrink-0">
                      <Button
                        type="default"
                        size="small"
                        className="flex-shrink-0"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleTemplateClick(template);
                        }}
                      >
                        查看详情
                      </Button>
                      <Button
                        type={teacherTemplate?.templateId === template.id ? "default" : "primary"}
                        size="small"
                        className={teacherTemplate?.templateId === template.id ? "flex-shrink-0" : "bg-blue-500 flex-shrink-0"}
                        onClick={(e) => {
                          e.stopPropagation();
                          if (teacherTemplate?.templateId !== template.id) {
                            onSelectTemplate(template.id);
                          }
                        }}
                        disabled={teacherTemplate?.templateId === template.id}
                      >
                        {teacherTemplate?.templateId === template.id ? "当前已使用此模板" : "使用此模板"}
                      </Button>
                    </div>
                  </div>
                  <div className="mt-3 text-xs text-gray-500">
                    {studentTemplateUsage[template.id] ? (
                      <span
                        className="cursor-pointer hover:text-blue-500 transition-colors"
                        onClick={(e) => {
                          e.stopPropagation();
                          onTemplateUsageClick(e, template);
                        }}
                      >
                        {studentTemplateUsage[template.id]} 名学生正在使用
                      </span>
                    ) : (
                      <span>暂无学生使用</span>
                    )}
                  </div>
                  {/* 添加覆盖整个卡片的可点击区域 */}
                  <div
                    className="absolute inset-0 cursor-pointer z-0"
                    onClick={() => {
                      //console.log('点击官方模板容器 - 绝对定位层', template.id, template.templateName);
                      onSelectTemplate(template.id);
                    }}
                  />
                </div>
              ))
            )}
          </>
        ) : (
          // 我的模板视图
          loadingTemplates ? (
            <div className="flex justify-center py-8">
              <Spin />
            </div>
          ) : templates.filter(t => !t.isOfficial).map(template => (
            <div
              key={template.id}
              className="bg-white border border-gray-100 p-4 rounded-xl cursor-pointer hover:shadow-md transition-all group relative z-10"
              style={{ position: 'relative' }}
              data-template-id={template.id}
              data-testid={`my-template-${template.id}`}
            >
              <div className="flex items-center justify-between gap-4">
                <div className="flex items-center gap-3 flex-1 min-w-0">
                  <div className="w-10 h-10 rounded-lg bg-blue-50 group-hover:bg-blue-100 flex-shrink-0 flex items-center justify-center transition-colors">
                    <AppstoreOutlined className="text-blue-500 text-lg" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium text-gray-800 group-hover:text-blue-600 transition-colors truncate max-w-[calc(100%-70px)]">
                        {template.templateName}
                      </span>
                      <Tag color="blue" className="flex-shrink-0 rounded-full text-xs">
                        自定义
                      </Tag>
                      {teacherTemplate?.templateId === template.id && (
                        <Tag color="green" className="flex-shrink-0 rounded-full text-xs">
                          已使用
                        </Tag>
                      )}
                    </div>
                    <p className="text-xs text-gray-500 mt-1">
                      创建于 {dayjs(template.createTime).format('YYYY-MM-DD')}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-2 flex-shrink-0">
                  <Button
                    type="default"
                    size="small"
                    className="flex-shrink-0"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleTemplateClick(template);
                    }}
                  >
                    查看详情
                  </Button>
                  <Button
                    type={teacherTemplate?.templateId === template.id ? "default" : "primary"}
                    size="small"
                    className={teacherTemplate?.templateId === template.id ? "flex-shrink-0" : "bg-blue-500 flex-shrink-0"}
                    onClick={(e) => {
                      e.stopPropagation();
                      if (teacherTemplate?.templateId !== template.id) {
                        onSelectTemplate(template.id);
                      }
                    }}
                    disabled={teacherTemplate?.templateId === template.id}
                  >
                    {teacherTemplate?.templateId === template.id ? "当前已使用此模板" : "使用此模板"}
                  </Button>
                </div>
              </div>
              <div className="mt-3 text-xs text-gray-500">
                {studentTemplateUsage[template.id] ? (
                  <span
                    className="cursor-pointer hover:text-blue-500 transition-colors"
                    onClick={(e) => {
                      e.stopPropagation();
                      onTemplateUsageClick(e, template);
                    }}
                  >
                    {studentTemplateUsage[template.id]} 名学生正在使用
                  </span>
                ) : (
                  <span>暂无学生使用</span>
                )}
              </div>
              {/* 添加覆盖整个卡片的可点击区域 */}
              <div
                className="absolute inset-0 cursor-pointer z-0"
                onClick={() => {
                  //console.log('点击自定义模板容器 - 绝对定位层', template.id, template.templateName);
                  onSelectTemplate(template.id);
                }}
              />
            </div>
          ))
        )}
      </div>

      {/* 添加权限查看弹窗 */}
      {isPermissionModalVisible && selectedTemplateId && (
        <PermissionModal
          userId={userId}
          visible={isPermissionModalVisible}
          onClose={() => {
            setIsPermissionModalVisible(false);
            setSelectedTemplateId(null);
          }}
          templateId={selectedTemplateId}
          readOnly={true}
          hideMoreOptions={true}
          onUseTemplate={(templateId) => {
            onSelectTemplate(templateId);
            setIsPermissionModalVisible(false);
            setSelectedTemplateId(null);
          }}
        />
      )}

      {/* 添加创建模板弹窗 */}
      {isCreateTemplateModalVisible && (
        <PermissionTemplateModal
          visible={isCreateTemplateModalVisible}
          onClose={() => {
            setIsCreateTemplateModalVisible(false);
          }}
          onSuccess={handleCreateTemplateSuccess}
          roleId={2} // 老师角色ID
          userId={userId}
        />
      )}
    </Modal>
  );
};
