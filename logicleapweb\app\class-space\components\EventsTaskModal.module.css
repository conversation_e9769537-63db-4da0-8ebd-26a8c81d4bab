/* 赛事任务模态框样式 - 简约专业设计 */
.eventsTaskModal {
  /* 模态框整体样式 */
}

.eventsTaskModal .ant-modal-content {
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 20px 40px -8px rgba(0, 0, 0, 0.08), 0 8px 16px -4px rgba(0, 0, 0, 0.04);
  background: #ffffff;
  border: 1px solid rgba(229, 231, 235, 0.8);
  max-height: 90vh !important;
  overflow-y: auto !important;
  display: flex !important;
  flex-direction: column !important;
}

.eventsTaskModal .ant-modal-wrap {
  padding: 20px;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.eventsTaskModal .ant-modal-body {
  padding: 0;
  background: transparent;
}

/* 主容器样式 */
.modalContainer {
  padding: 32px;
  background: transparent;
  animation: fadeInUp 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  height: calc(90vh - 80px);
  display: flex;
  flex-direction: column;
  position: relative;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 内容区域 - 可滚动 */
.modalContent {
  flex: 1;
  overflow-y: auto;
  padding-right: 8px;
  margin-right: -8px;
}

/* 标题区域 - 简约设计 */
.titleSection {
  margin-bottom: 16px;
  padding-bottom: 24px;
  border-bottom: 1px solid #e5e7eb;
  position: relative;
  flex-shrink: 0;
}

.titleContainer {
  display: flex;
  align-items: center;
  gap: 16px;
}

.titleIcon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #3b82f6 0%, #6366f1 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.titleText {
  flex: 1;
}

.titleText h2 {
  font-size: 24px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 4px 0;
  line-height: 1.2;
}

.dateRange {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
  font-weight: 500;
}

/* 作品选择卡片样式 - 简约专业设计 */
.workCard {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
  flex-shrink: 0;
  width: 180px;
}

.workCard:hover {
  border-color: #d1d5db;
  box-shadow: 0 4px 8px -2px rgba(0, 0, 0, 0.05);
  transform: translateY(-1px);
}

.workCard.selected {
  border-color: #3b82f6;
  background: #fefefe;
  box-shadow: 0 4px 12px -2px rgba(59, 130, 246, 0.15);
  transform: translateY(-1px);
}

/* 作品预览区域 - 简约设计 */
.workPreview {
  aspect-ratio: 16/10;
  background: #f9fafb;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 12px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.workImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.2s ease;
}

.workCard:hover .workImage {
  transform: scale(1.02);
}

.workPlaceholder {
  color: #9ca3af;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

/* 选择指示器 */
.selectionIndicator {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 24px;
  height: 24px;
  background: rgba(255, 255, 255, 0.9);
  border: 2px solid #e5e7eb;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  color: transparent;
}

.selectionIndicator.selected {
  background: #3b82f6;
  border-color: #3b82f6;
  color: white;
}

/* 作品信息区域 - 简约设计 */
.workInfo {
  padding: 0;
}

.workTitle {
  font-size: 14px;
  font-weight: 500;
  color: #111827;
  margin-bottom: 6px;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.workMeta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.workId {
  color: #6b7280;
  font-weight: 500;
}

.workStatus {
  display: inline-flex;
  align-items: center;
  padding: 2px 8px;
  border-radius: 6px;
  font-size: 11px;
  font-weight: 500;
}

.workStatus.published {
  background: #dcfce7;
  color: #166534;
}

.workStatus.draft {
  background: #fef3c7;
  color: #92400e;
}

/* 作品网格容器 - 用于捕获滚轮事件 */
.worksGridContainer {
  position: relative;
  /* 确保能够捕获滚轮事件 */
  pointer-events: auto;
  /* 创建新的层叠上下文 */
  z-index: 10;
  /* 阻止滚动链传播 */
  overscroll-behavior: contain;
}

/* 作品网格布局 - 横向滚动设计 */
.worksGrid {
  display: flex;
  gap: 16px;
  overflow-x: auto;
  overflow-y: hidden;
  padding: 8px 0;
  scrollbar-width: thin;
  scrollbar-color: #d1d5db #f9fafb;
  /* 确保可以接收焦点和滚轮事件 */
  position: relative;
  cursor: grab;
  /* 确保滚动事件被正确捕获 */
  touch-action: pan-x;
  /* 创建新的层叠上下文，确保事件处理优先级 */
  z-index: 10;
  /* 确保事件不会穿透 */
  pointer-events: auto;
  /* 阻止滚动链传播到父元素 */
  overscroll-behavior: contain;
}

.worksGrid:active {
  cursor: grabbing;
}

/* 当鼠标悬停在作品网格上时的视觉提示 */
.worksGrid:hover {
  /* 可以添加一些微妙的视觉反馈 */
  background-color: rgba(0, 0, 0, 0.01);
}

/* 横向滚动条样式优化 */
.worksGrid::-webkit-scrollbar {
  height: 6px;
}

.worksGrid::-webkit-scrollbar-track {
  background: #f9fafb;
  border-radius: 3px;
}

.worksGrid::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 3px;
}

.worksGrid::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

.worksGrid::-webkit-scrollbar {
  width: 6px;
}

.worksGrid::-webkit-scrollbar-track {
  background: #f9fafb;
  border-radius: 3px;
}

.worksGrid::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 3px;
}

.worksGrid::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* 移动端滚动条优化 */
@media (max-width: 768px) {
  .worksGrid::-webkit-scrollbar {
    width: 4px;
  }

  .worksGrid {
    scrollbar-width: thin;
  }
}

/* 上传区域样式 - 简约设计 */
.uploadArea {
  border: 2px dashed #d1d5db;
  border-radius: 12px;
  padding: 32px 24px;
  text-align: center;
  background: #fafafa;
  transition: all 0.2s ease;
  position: relative;
}

.uploadArea:hover {
  border-color: #3b82f6;
  background: #f8fafc;
}

.uploadIcon {
  font-size: 32px;
  color: #6b7280;
  margin-bottom: 16px;
}

.uploadArea:hover .uploadIcon {
  color: #3b82f6;
}

.uploadText {
  color: #374151;
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
}

.uploadHint {
  color: #6b7280;
  font-size: 14px;
}

/* 表单区域样式 - 简约设计 */
.formGroup {
  background: #f9fafb;
  border-radius: 16px;
  padding: 24px;
  margin-top: 12px;
  margin-bottom: 12px;
  border: 1px solid #e5e7eb;
}

.formGroupHeader {
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.formGroupHeader h3 {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 4px 0;
}

.formGroupHeader p {
  font-size: 13px;
  color: #6b7280;
  margin: 0;
}

.formRow {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 16px;
}

@media (max-width: 640px) {
  .formRow {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}

.formSection {
  margin-bottom: 16px;
}

.formSection label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
}

/* 输入框样式优化 */
.formSection .ant-input,
.formSection .ant-input-affix-wrapper,
.formSection .ant-select .ant-select-selector,
.formSection .ant-input-number {
  border-radius: 12px;
  border: 1px solid #d1d5db;
  background: #ffffff;
  padding: 12px 16px;
  font-size: 14px;
  transition: all 0.2s ease;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.formSection .ant-input:focus,
.formSection .ant-input-affix-wrapper:focus,
.formSection .ant-input-focused,
.formSection .ant-input-affix-wrapper-focused,
.formSection .ant-select-focused .ant-select-selector,
.formSection .ant-input-number-focused {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.formSection .ant-input:hover,
.formSection .ant-input-affix-wrapper:hover,
.formSection .ant-select:hover .ant-select-selector,
.formSection .ant-input-number:hover {
  border-color: #9ca3af;
}

/* 文本域特殊样式 */
.formSection .ant-input {
  min-height: 40px;
}

.formSection textarea.ant-input {
  min-height: 80px;
  resize: vertical;
}

/* 网格布局优化 */
.gridCols2 {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

@media (max-width: 640px) {
  .gridCols2 {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}

/* 标签切换区域 - 简约设计 */
.tabSection {
  flex-shrink: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.tabContainer {
  position: relative;
  background: #f9fafb;
  border-radius: 12px;
  padding: 4px;
  display: inline-flex;
}

.tabButtons {
  display: flex;
  position: relative;
  z-index: 2;
}

.tabButton {
  background: none;
  border: none;
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  position: relative;
  z-index: 3;
}

.tabButton:hover {
  color: #374151;
}

.tabButton.active {
  color: #3b82f6;
  background: #ffffff;
  box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.06), 0 1px 2px -1px rgba(0, 0, 0, 0.06);
}

.tabIcon {
  font-size: 16px;
}

.selectionStatus {
  font-size: 13px;
  color: #3b82f6;
  font-weight: 500;
  padding: 6px 12px;
  background: #eff6ff;
  border-radius: 8px;
  display: inline-block;
  flex-shrink: 0;
}

/* 底部按钮区域 - 简约设计 */
.buttonGroup {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
  padding-top: 24px;
  border-top: 1px solid #e5e7eb;
  gap: 16px;
  flex-shrink: 0;
  background: #ffffff;
  margin-left: -32px;
  margin-right: -32px;
  margin-bottom: -32px;
  padding-left: 32px;
  padding-right: 32px;
  padding-bottom: 32px;
}

.buttonGroup .ant-btn {
  border-radius: 8px;
  font-weight: 500;
  height: 44px;
  padding: 0 24px;
  font-size: 14px;
  transition: all 0.2s ease;
}

.buttonGroup .ant-btn-primary {
  background: #3b82f6;
  border-color: #3b82f6;
  box-shadow: 0 2px 4px -1px rgba(59, 130, 246, 0.2);
}

.buttonGroup .ant-btn-primary:hover {
  background: #2563eb;
  border-color: #2563eb;
  box-shadow: 0 4px 8px -2px rgba(59, 130, 246, 0.3);
}

.buttonGroup .ant-btn-primary:hover {
  background: linear-gradient(135deg, #1d4ed8 0%, #7c3aed 100%);
  transform: translateY(-2px);
  box-shadow: 0 12px 24px -6px rgba(59, 130, 246, 0.5);
}



/* 取消按钮样式 */
.buttonGroup .ant-btn:not(.ant-btn-primary) {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 2px solid rgba(226, 232, 240, 0.8);
  color: #64748b;
}

.buttonGroup .ant-btn:not(.ant-btn-primary):hover {
  border-color: rgba(59, 130, 246, 0.6);
  color: #3b82f6;
  background: linear-gradient(135deg, #ffffff 0%, #eff6ff 100%);
  transform: translateY(-1px);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .eventsTaskModal .ant-modal-content {
    width: 95% !important;
    max-width: 700px !important;
    margin: 20px auto !important;
  }

  .worksGrid {
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .eventsTaskModal .ant-modal-content {
    width: 95% !important;
    max-width: 600px !important;
    margin: 16px auto !important;
    max-height: 90vh !important;
    overflow-y: auto !important;
  }

  .modalContainer {
    padding: 20px;
  }

  .titleSection {
    margin-bottom: 24px;
    padding-bottom: 16px;
  }

  .titleSection h2 {
    font-size: 20px;
  }

  .worksGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    max-height: 300px;
  }

  .workCard {
    padding: 8px;
  }

  .workPreview {
    aspect-ratio: 4/3;
  }

  .uploadArea {
    padding: 24px 16px;
  }

  .buttonGroup {
    flex-direction: column;
    gap: 12px;
    margin-top: 24px;
    padding-top: 16px;
  }

  .buttonGroup > div {
    display: flex;
    gap: 12px;
  }

  .gridCols2 {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .tabButtons {
    gap: 12px;
  }

  .tabButton {
    padding: 10px 20px;
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .eventsTaskModal .ant-modal-content {
    width: 98% !important;
    max-width: none !important;
    margin: 8px auto !important;
    max-height: 95vh !important;
    border-radius: 16px !important;
  }

  .modalContainer {
    padding: 16px;
  }

  .titleSection {
    margin-bottom: 20px;
    padding-bottom: 12px;
  }

  .titleSection h2 {
    font-size: 18px;
  }

  .titleSection p {
    font-size: 13px;
  }

  .worksGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
    max-height: 250px;
  }

  .workCard {
    padding: 6px;
  }

  .workPreview {
    aspect-ratio: 4/3;
    margin-bottom: 6px;
  }

  .workInfo {
    gap: 4px;
  }

  .workTitle {
    font-size: 14px;
  }

  .workMeta {
    font-size: 11px;
  }

  .uploadArea {
    padding: 20px 12px;
  }

  .uploadIcon {
    font-size: 36px;
  }

  .uploadText {
    font-size: 14px;
  }

  .uploadHint {
    font-size: 12px;
  }

  .buttonGroup {
    margin-top: 20px;
    padding-top: 12px;
  }

  .buttonGroup .ant-btn {
    height: 42px;
    font-size: 13px;
    padding: 0 16px;
  }

  .tabButton {
    padding: 8px 16px;
    font-size: 12px;
    height: 36px;
  }

  .formSection {
    margin-bottom: 16px;
  }

  .formSection label {
    font-size: 13px;
    margin-bottom: 6px;
  }

  .teacherSchoolSection {
    padding: 0;
  }
}

/* 超小屏幕适配 */
@media (max-width: 360px) {
  .eventsTaskModal .ant-modal-content {
    width: 100% !important;
    margin: 0 !important;
    border-radius: 0 !important;
    max-height: 100vh !important;
  }

  .modalContainer {
    padding: 12px;
  }

  .titleSection h2 {
    font-size: 16px;
  }

  .worksGrid {
    max-height: 200px;
    gap: 8px;
  }

  .workCard {
    padding: 8px;
  }

  .buttonGroup {
    flex-direction: column;
    gap: 8px;
  }

  .buttonGroup > div {
    flex-direction: column;
    gap: 8px;
  }

  .tabButtons {
    flex-direction: column;
    gap: 8px;
  }

  .tabButton {
    width: 100%;
    justify-content: center;
  }
}

/* 加载状态优化 */
.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #64748b;
}

.loadingText {
  margin-top: 16px;
  font-size: 14px;
  font-weight: 500;
}

/* 空状态优化 */
.emptyContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #94a3b8;
}

.emptyIcon {
  font-size: 64px;
  margin-bottom: 20px;
  opacity: 0.6;
}

.emptyText {
  font-size: 16px;
  color: #64748b;
  font-weight: 500;
}

/* 轮播图样式优化 */
.eventsTaskModal :global(.custom-dots) {
  bottom: 12px !important;
}

.eventsTaskModal :global(.custom-dots li button) {
  background: rgba(255, 255, 255, 0.7) !important;
  border-radius: 50% !important;
  width: 8px !important;
  height: 8px !important;
  border: 1px solid rgba(59, 130, 246, 0.3) !important;
}

.eventsTaskModal :global(.custom-dots li.ant-carousel-dot-active button) {
  background: #3b82f6 !important;
  border-color: #3b82f6 !important;
}

/* 文件列表样式 */
.fileList {
  margin-top: 20px;
}

.fileList .ant-upload-list-item {
  border-radius: 12px;
  border: 2px solid rgba(226, 232, 240, 0.8);
  margin-bottom: 12px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  transition: all 0.3s ease;
}

.fileList .ant-upload-list-item:hover {
  border-color: rgba(59, 130, 246, 0.6);
  box-shadow: 0 4px 8px -2px rgba(59, 130, 246, 0.15);
}

/* 选中状态提示 */
.selectedInfo {
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  border: 2px solid rgba(59, 130, 246, 0.3);
  border-radius: 16px;
  padding: 12px 16px;
  font-size: 14px;
  color: #1d4ed8;
  margin-bottom: 20px;
  animation: slideInDown 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 500;
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-15px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 文件列表样式 */
.fileList {
  margin-top: 16px;
}

.fileList .ant-upload-list-item {
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  margin-bottom: 8px;
}

/* 输入框样式优化 */
.formSection .ant-input,
.formSection .ant-input-affix-wrapper {
  border-radius: 12px;
  border-color: #d1d5db;
}

.formSection .ant-input:focus,
.formSection .ant-input-affix-wrapper:focus,
.formSection .ant-input-focused,
.formSection .ant-input-affix-wrapper-focused {
  border-color: #6b7280;
  box-shadow: 0 0 0 2px rgba(107, 114, 128, 0.1);
}

/* 文本域样式 */
.formSection .ant-input {
  resize: vertical;
  min-height: 100px;
}

/* 移除模块CSS中的全局样式，这些已经在globals.css中定义 */

/* 网格布局优化 */
.gridCols2 {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.gridCols3 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
}

/* 轮播图样式 */
.eventsTaskModal :global(.custom-dots) {
  bottom: 8px !important;
}

.eventsTaskModal :global(.custom-dots li button) {
  background: rgba(255, 255, 255, 0.5) !important;
  border-radius: 50% !important;
  width: 6px !important;
  height: 6px !important;
}

.eventsTaskModal :global(.custom-dots li.ant-carousel-dot-active button) {
  background: #fff !important;
}

/* 水平滚动容器 */
.horizontalScrollContainer {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e0 #f7fafc;
}

.horizontalScrollContainer::-webkit-scrollbar {
  height: 6px;
}

.horizontalScrollContainer::-webkit-scrollbar-track {
  background: #f7fafc;
  border-radius: 3px;
}

.horizontalScrollContainer::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 3px;
}

.horizontalScrollContainer::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
}

@media (max-width: 640px) {
  .gridCols2,
  .gridCols3 {
    grid-template-columns: 1fr;
  }
}

/* 悬浮向下箭头容器样式 */
.scrollArrowContainer {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  z-index: 1000;
  animation: bounceArrow 2s infinite;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 悬浮向下箭头样式 */
.scrollArrow {
  color: #60a5fa;
  font-size: 24px;
  margin-bottom: 4px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 滑动提示文字样式 */
.scrollText {
  color: #60a5fa;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
  opacity: 0.8;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.scrollArrowContainer:hover .scrollArrow {
  transform: scale(1.2);
  color: #3b82f6;
}

.scrollArrowContainer:hover .scrollText {
  color: #3b82f6;
  opacity: 1;
}

.scrollArrowContainer:active .scrollArrow {
  transform: scale(1.0);
  color: #2563eb;
}

.scrollArrowContainer:active .scrollText {
  color: #2563eb;
}

/* 箭头弹跳动画 */
@keyframes bounceArrow {
  0%, 20%, 50%, 80%, 100% {
    transform: translateX(-50%) translateY(0);
  }
  40% {
    transform: translateX(-50%) translateY(-8px);
  }
  60% {
    transform: translateX(-50%) translateY(-4px);
  }
}

/* 移动端适配 */
@media (max-width: 768px) {
  .scrollArrowContainer {
    bottom: 16px;
  }

  .scrollArrow {
    font-size: 20px;
  }

  .scrollText {
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  .scrollArrowContainer {
    bottom: 12px;
  }

  .scrollArrow {
    font-size: 18px;
  }

  .scrollText {
    font-size: 10px;
  }
}

/* 表单验证错误样式 */
.errorBorder {
  border: 2px solid #ef4444 !important;
  border-radius: 8px;
  animation: shakeError 0.5s ease-in-out;
}

/* 错误抖动动画 */
@keyframes shakeError {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
  20%, 40%, 60%, 80% { transform: translateX(2px); }
}

/* 状态提示样式 */
.statusAlert {
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  animation: slideInDown 0.3s ease-out;
}

.statusAlert:hover {
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
