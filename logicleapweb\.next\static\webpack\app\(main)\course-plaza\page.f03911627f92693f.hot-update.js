"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/course-plaza/page",{

/***/ "(app-pages-browser)/./app/(main)/course-plaza/page.tsx":
/*!******************************************!*\
  !*** ./app/(main)/course-plaza/page.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CoursePlaza; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/./node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _lib_api_course__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api/course */ \"(app-pages-browser)/./lib/api/course.ts\");\n/* harmony import */ var _components_CourseDetailView__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./components/CourseDetailView */ \"(app-pages-browser)/./app/(main)/course-plaza/components/CourseDetailView.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// 动态导入 AIBackground 组件，并禁用 SSR\nconst AIBackground = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_components_AIBackground_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/AIBackground */ \"(app-pages-browser)/./components/AIBackground.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\(main)\\\\course-plaza\\\\page.tsx -> \" + \"@/components/AIBackground\"\n        ]\n    },\n    ssr: false\n});\n_c = AIBackground;\nfunction CoursePlaza() {\n    _s();\n    const [seriesCourses, setSeriesCourses] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [communityCourses, setCommunityCourses] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true);\n    const [showAllOfficialCourses, setShowAllOfficialCourses] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [loadingMore, setLoadingMore] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [showAllCommunityCourses, setShowAllCommunityCourses] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [loadingMoreCommunity, setLoadingMoreCommunity] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [selectedCourse, setSelectedCourse] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [showCourseDetail, setShowCourseDetail] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [showOfficialSidebar, setShowOfficialSidebar] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [showCommunitySidebar, setShowCommunitySidebar] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    // 处理展示更多官方课程 - 打开侧边栏\n    const handleShowMoreOfficialCourses = ()=>{\n        setShowOfficialSidebar(true);\n    };\n    // 处理展示更多社区课程 - 打开侧边栏\n    const handleShowMoreCommunityCourses = ()=>{\n        setShowCommunitySidebar(true);\n    };\n    // 处理课程卡片点击\n    const handleCourseClick = (course)=>{\n        console.log(\"\\uD83C\\uDFAF 点击课程:\", course);\n        console.log(\"\\uD83D\\uDD0D 点击课程的seriesId:\", course.seriesId);\n        console.log(\"\\uD83D\\uDD0D 点击课程的完整数据:\", JSON.stringify(course, null, 2));\n        setSelectedCourse(course);\n        setShowCourseDetail(true);\n    };\n    // 返回课程列表\n    const handleBackToCourseList = ()=>{\n        setShowCourseDetail(false);\n        setSelectedCourse(null);\n    };\n    // 获取要显示的官方课程列表 - 显示所有课程\n    const getDisplayedOfficialCourses = ()=>{\n        return seriesCourses;\n    };\n    // 获取要显示的社区课程列表 - 显示所有课程\n    const getDisplayedCommunityCourses = ()=>{\n        return communityCourses;\n    };\n    // 获取系列课程数据\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        const fetchSeriesCourses = async ()=>{\n            try {\n                setLoading(true);\n                console.log(\"\\uD83D\\uDD04 开始获取课程市场系列课程列表...\");\n                console.log(\"\\uD83D\\uDD04 准备调用 courseApi.getMarketplaceSeries（获取课程市场系列课程）...\");\n                // 使用课程市场系列课程列表接口\n                const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_4__.courseApi.getMarketplaceSeries({\n                    page: 1,\n                    pageSize: 50 // 获取更多数据\n                });\n                if (res.code === 200 && res.data && res.data.list) {\n                    // 打印每个系列课程的详细信息\n                    res.data.list.forEach((item, index)=>{\n                        console.log(\"\\uD83D\\uDCCB 系列课程 \".concat(index + 1, \":\"), {\n                            id: item.id,\n                            title: item.title,\n                            category: item.category,\n                            categoryLabel: item.categoryLabel,\n                            status: item.status,\n                            statusLabel: item.statusLabel,\n                            totalCourses: item.totalCourses,\n                            totalStudents: item.totalStudents,\n                            coverImage: item.coverImage // 添加封面图片信息\n                        });\n                    });\n                    // 筛选 categoryLabel 为 \"官方\" 的官方课程，全部显示\n                    const officialCourses = res.data.list.filter((item)=>{\n                        console.log('\\uD83D\\uDD0D 检查系列课程 \"'.concat(item.title, '\": category=').concat(item.category, ', categoryLabel=\"').concat(item.categoryLabel, '\"'));\n                        return item.categoryLabel === \"官方\"; // 筛选官方课程 (categoryLabel = \"官方\")\n                    }).map((item)=>{\n                        var _item_totalStudents;\n                        console.log('\\uD83D\\uDCCB 官方系列课程 \"'.concat(item.title, '\": category=').concat(item.category, ', categoryLabel=\"').concat(item.categoryLabel, '\", coverImage=\"').concat(item.coverImage, '\"'));\n                        return {\n                            id: item.id,\n                            seriesId: item.id,\n                            title: item.title,\n                            lessons: \"共\".concat(item.totalCourses || 0, \"课时\"),\n                            views: ((_item_totalStudents = item.totalStudents) === null || _item_totalStudents === void 0 ? void 0 : _item_totalStudents.toString()) || \"0\",\n                            coverImage: item.coverImage && !item.coverImage.includes(\"example.com\") ? item.coverImage : null // 过滤示例URL\n                        };\n                    });\n                    // 筛选 categoryLabel 为 \"社区\" 的社区课程，全部显示\n                    const communityCourses = res.data.list.filter((item)=>{\n                        console.log('\\uD83D\\uDD0D 检查社区课程 \"'.concat(item.title, '\": category=').concat(item.category, ', categoryLabel=\"').concat(item.categoryLabel, '\"'));\n                        return item.categoryLabel === \"社区\"; // 筛选社区课程 (categoryLabel = \"社区\")\n                    }).map((item)=>{\n                        var _item_totalStudents;\n                        console.log('\\uD83D\\uDCCB 社区系列课程 \"'.concat(item.title, '\": category=').concat(item.category, ', categoryLabel=\"').concat(item.categoryLabel, '\", coverImage=\"').concat(item.coverImage, '\"'));\n                        return {\n                            id: item.id,\n                            seriesId: item.id,\n                            title: item.title,\n                            lessons: \"共\".concat(item.totalCourses || 0, \"课时\"),\n                            views: ((_item_totalStudents = item.totalStudents) === null || _item_totalStudents === void 0 ? void 0 : _item_totalStudents.toString()) || \"0\",\n                            coverImage: item.coverImage && !item.coverImage.includes(\"example.com\") ? item.coverImage : null // 过滤示例URL\n                        };\n                    });\n                    setSeriesCourses(officialCourses);\n                    setCommunityCourses(communityCourses);\n                } else {\n                    setSeriesCourses([]);\n                }\n            } catch (error) {\n                console.error(\"❌ 获取系列课程失败:\", error);\n                console.error(\"❌ 错误详情:\", error);\n                setSeriesCourses([]);\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchSeriesCourses();\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen\",\n        children: showCourseDetail && selectedCourse ? // 课程详情页面 - 带格子背景\n        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8 relative min-h-screen\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 z-[-1]\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AIBackground, {}, void 0, false, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                    lineNumber: 160,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto relative z-10\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CourseDetailView__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        course: selectedCourse,\n                        onBack: handleBackToCourseList\n                    }, void 0, false, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n            lineNumber: 158,\n            columnNumber: 9\n        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full h-64 relative\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        src: \"/images/mywork_background.svg\",\n                        alt: \"背景图片\",\n                        fill: true,\n                        className: \"object-cover\",\n                        priority: true\n                    }, void 0, false, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                    lineNumber: 174,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 py-8 relative min-h-screen\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 z-[-1]\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AIBackground, {}, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto relative z-10\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8\n                                    },\n                                    className: \"mb-12\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: \"官方课程\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 13\n                                                }, this),\n                                                (()=>{\n                                                    console.log(\"\\uD83D\\uDD0D 按钮显示条件检查:\", {\n                                                        loading,\n                                                        seriesCoursesLength: seriesCourses.length,\n                                                        shouldShowButton: !loading && seriesCourses.length > 6\n                                                    });\n                                                    return !loading && seriesCourses.length > 6;\n                                                })() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handleShowMoreOfficialCourses,\n                                                    disabled: loadingMore,\n                                                    className: \"group relative flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-blue-500 to-blue-600 text-white font-medium rounded-full shadow-lg hover:shadow-xl hover:from-blue-600 hover:to-blue-700 transform hover:scale-105 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none disabled:shadow-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 bg-gradient-to-r from-blue-400 to-blue-500 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                            lineNumber: 214,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative flex items-center gap-2\",\n                                                            children: loadingMore ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-5 h-5 animate-spin\",\n                                                                        fill: \"none\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                                className: \"opacity-25\",\n                                                                                cx: \"12\",\n                                                                                cy: \"12\",\n                                                                                r: \"10\",\n                                                                                stroke: \"currentColor\",\n                                                                                strokeWidth: \"4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                                lineNumber: 219,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                className: \"opacity-75\",\n                                                                                fill: \"currentColor\",\n                                                                                d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                                lineNumber: 220,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                        lineNumber: 218,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm\",\n                                                                        children: \"加载中...\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                        lineNumber: 222,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-semibold\",\n                                                                        children: \"展开全部\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                        lineNumber: 226,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-5 h-5 transition-all duration-300 \".concat(showAllOfficialCourses ? \"rotate-180 scale-110\" : \"group-hover:translate-y-0.5\"),\n                                                                        fill: \"none\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        stroke: \"currentColor\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\",\n                                                                            strokeWidth: 2.5,\n                                                                            d: \"M19 9l-7 7-7-7\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                            lineNumber: 233,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                        lineNumber: 227,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                            lineNumber: 215,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                    lineNumber: 209,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                            lineNumber: 199,\n                                            columnNumber: 11\n                                        }, this),\n                                        loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                            children: [\n                                                1,\n                                                2,\n                                                3\n                                            ].map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white rounded-lg border border-blue-200 shadow-sm overflow-hidden\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-36 bg-gradient-to-br from-blue-100 to-blue-200 animate-pulse flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-12 h-12 text-blue-600 opacity-50\",\n                                                                fill: \"none\",\n                                                                viewBox: \"0 0 48 48\",\n                                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M44 24C44 22.8954 43.1046 22 42 22C40.8954 22 40 22.8954 40 24H44ZM24 8C25.1046 8 26 7.10457 26 6C26 4.89543 25.1046 4 24 4V8ZM39 40H9V44H39V40ZM8 39V9H4V39H8ZM40 24V39H44V24H40ZM9 8H24V4H9V8ZM9 40C8.44772 40 8 39.5523 8 39H4C4 41.7614 6.23857 44 9 44V40ZM39 44C41.7614 44 44 41.7614 44 39H40C40 39.5523 39.5523 40 39 40V44ZM8 9C8 8.44772 8.44771 8 9 8V4C6.23858 4 4 6.23857 4 9H8Z\",\n                                                                        fill: \"currentColor\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                        lineNumber: 247,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M6 35L16.6931 25.198C17.4389 24.5143 18.5779 24.4953 19.3461 25.1538L32 36\",\n                                                                        stroke: \"currentColor\",\n                                                                        strokeWidth: \"4\",\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                        lineNumber: 248,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M28 31L32.7735 26.2265C33.4772 25.5228 34.5914 25.4436 35.3877 26.0408L42 31\",\n                                                                        stroke: \"currentColor\",\n                                                                        strokeWidth: \"4\",\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                        lineNumber: 249,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M37 18L37 6\",\n                                                                        stroke: \"currentColor\",\n                                                                        strokeWidth: \"4\",\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                        lineNumber: 250,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M32 11L37 6L42 11\",\n                                                                        stroke: \"currentColor\",\n                                                                        strokeWidth: \"4\",\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                        lineNumber: 251,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                lineNumber: 246,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                            lineNumber: 245,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-4 bg-gray-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-4 bg-gray-200 rounded animate-pulse mb-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                    lineNumber: 255,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"h-3 bg-gray-200 rounded animate-pulse w-16\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                            lineNumber: 257,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"h-3 bg-gray-200 rounded animate-pulse w-12\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                            lineNumber: 258,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                    lineNumber: 256,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                            lineNumber: 254,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, i, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 17\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 13\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                            layout: true,\n                                            transition: {\n                                                duration: 0.5,\n                                                ease: \"easeInOut\"\n                                            },\n                                            children: getDisplayedOfficialCourses().map((course, index)=>{\n                                                const isNewlyVisible = showAllOfficialCourses && index >= 6;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                                    className: \"bg-white rounded-lg border border-blue-200 shadow-sm overflow-hidden hover:shadow-md transition-shadow duration-200 cursor-pointer\",\n                                                    onClick: ()=>handleCourseClick(course),\n                                                    initial: {\n                                                        opacity: isNewlyVisible ? 0 : 1,\n                                                        y: isNewlyVisible ? 30 : 0,\n                                                        scale: isNewlyVisible ? 0.9 : 1\n                                                    },\n                                                    animate: {\n                                                        opacity: 1,\n                                                        y: 0,\n                                                        scale: 1\n                                                    },\n                                                    transition: {\n                                                        duration: isNewlyVisible ? 0.6 : 0.4,\n                                                        delay: isNewlyVisible ? (index - 6) * 0.15 + 0.2 : index * 0.08,\n                                                        ease: \"easeOut\",\n                                                        type: \"spring\",\n                                                        stiffness: 100,\n                                                        damping: 15\n                                                    },\n                                                    whileHover: {\n                                                        y: -8,\n                                                        scale: 1.03,\n                                                        boxShadow: \"0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)\",\n                                                        transition: {\n                                                            duration: 0.3,\n                                                            ease: \"easeOut\"\n                                                        }\n                                                    },\n                                                    layout: true,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-36 relative \".concat(!course.coverImage ? \"bg-gradient-to-br from-blue-100 to-blue-200\" : \"\"),\n                                                            children: course.coverImage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                src: course.coverImage,\n                                                                alt: course.title,\n                                                                fill: true,\n                                                                className: \"object-cover\",\n                                                                onLoad: ()=>{\n                                                                    console.log('✅ 封面图片加载成功: \"'.concat(course.title, '\" - ').concat(course.coverImage));\n                                                                },\n                                                                onError: (e)=>{\n                                                                    // 图片加载失败时隐藏图片，显示默认图标\n                                                                    console.log('❌ 封面图片加载失败: \"'.concat(course.title, '\" - ').concat(course.coverImage));\n                                                                    const target = e.target;\n                                                                    target.style.display = \"none\";\n                                                                    // 添加浅蓝色背景\n                                                                    const parent = target.parentElement;\n                                                                    if (parent) {\n                                                                        parent.className = parent.className + \" bg-gradient-to-br from-blue-100 to-blue-200\";\n                                                                    }\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                lineNumber: 302,\n                                                                columnNumber: 23\n                                                            }, this) : // 当没有封面图片时显示的图标\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-full h-full flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-12 h-12 text-blue-600 opacity-70\",\n                                                                    fill: \"none\",\n                                                                    viewBox: \"0 0 48 48\",\n                                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M44 24C44 22.8954 43.1046 22 42 22C40.8954 22 40 22.8954 40 24H44ZM24 8C25.1046 8 26 7.10457 26 6C26 4.89543 25.1046 4 24 4V8ZM39 40H9V44H39V40ZM8 39V9H4V39H8ZM40 24V39H44V24H40ZM9 8H24V4H9V8ZM9 40C8.44772 40 8 39.5523 8 39H4C4 41.7614 6.23857 44 9 44V40ZM39 44C41.7614 44 44 41.7614 44 39H40C40 39.5523 39.5523 40 39 40V44ZM8 9C8 8.44772 8.44771 8 9 8V4C6.23858 4 4 6.23857 4 9H8Z\",\n                                                                            fill: \"currentColor\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                            lineNumber: 326,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M6 35L16.6931 25.198C17.4389 24.5143 18.5779 24.4953 19.3461 25.1538L32 36\",\n                                                                            stroke: \"currentColor\",\n                                                                            strokeWidth: \"4\",\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                            lineNumber: 327,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M28 31L32.7735 26.2265C33.4772 25.5228 34.5914 25.4436 35.3877 26.0408L42 31\",\n                                                                            stroke: \"currentColor\",\n                                                                            strokeWidth: \"4\",\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                            lineNumber: 328,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M37 18L37 6\",\n                                                                            stroke: \"currentColor\",\n                                                                            strokeWidth: \"4\",\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                            lineNumber: 329,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M32 11L37 6L42 11\",\n                                                                            stroke: \"currentColor\",\n                                                                            strokeWidth: \"4\",\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                            lineNumber: 330,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                    lineNumber: 325,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                lineNumber: 324,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                            lineNumber: 300,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-4 bg-gray-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"font-medium text-gray-800 mb-3 text-base line-clamp-2\",\n                                                                    children: course.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                    lineNumber: 338,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between text-sm text-gray-600\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-500\",\n                                                                            children: course.lessons\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                            lineNumber: 340,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-1.5\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                    className: \"w-4 h-4 text-gray-400\",\n                                                                                    fill: \"currentColor\",\n                                                                                    viewBox: \"0 0 20 20\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                            d: \"M10 12a2 2 0 100-4 2 2 0 000 4z\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                                            lineNumber: 343,\n                                                                                            columnNumber: 27\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                            fillRule: \"evenodd\",\n                                                                                            d: \"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z\",\n                                                                                            clipRule: \"evenodd\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                                            lineNumber: 344,\n                                                                                            columnNumber: 27\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                                    lineNumber: 342,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-gray-600 font-medium\",\n                                                                                    children: course.views\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                                    lineNumber: 346,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                            lineNumber: 341,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                    lineNumber: 339,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                            lineNumber: 337,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, course.id, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                    lineNumber: 273,\n                                                    columnNumber: 19\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 9\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8,\n                                        delay: 0.2\n                                    },\n                                    className: \"mb-12\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: \"课程社区\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                    lineNumber: 365,\n                                                    columnNumber: 13\n                                                }, this),\n                                                (()=>{\n                                                    console.log(\"\\uD83D\\uDD0D 社区按钮显示条件检查:\", {\n                                                        loading,\n                                                        communityCoursesLength: communityCourses.length,\n                                                        shouldShowButton: !loading && communityCourses.length > 6\n                                                    });\n                                                    return !loading && communityCourses.length > 6;\n                                                })() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handleShowMoreCommunityCourses,\n                                                    disabled: loadingMoreCommunity,\n                                                    className: \"group relative flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-green-500 to-green-600 text-white font-medium rounded-full shadow-lg hover:shadow-xl hover:from-green-600 hover:to-green-700 transform hover:scale-105 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none disabled:shadow-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 bg-gradient-to-r from-green-400 to-green-500 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                            lineNumber: 379,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative flex items-center gap-2\",\n                                                            children: loadingMoreCommunity ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-5 h-5 animate-spin\",\n                                                                        fill: \"none\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                                className: \"opacity-25\",\n                                                                                cx: \"12\",\n                                                                                cy: \"12\",\n                                                                                r: \"10\",\n                                                                                stroke: \"currentColor\",\n                                                                                strokeWidth: \"4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                                lineNumber: 384,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                className: \"opacity-75\",\n                                                                                fill: \"currentColor\",\n                                                                                d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                                lineNumber: 385,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                        lineNumber: 383,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm\",\n                                                                        children: \"加载中...\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                        lineNumber: 387,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-semibold\",\n                                                                        children: \"展开全部\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                        lineNumber: 391,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-5 h-5 transition-all duration-300 \".concat(showAllCommunityCourses ? \"rotate-180 scale-110\" : \"group-hover:translate-y-0.5\"),\n                                                                        fill: \"none\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        stroke: \"currentColor\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\",\n                                                                            strokeWidth: 2.5,\n                                                                            d: \"M19 9l-7 7-7-7\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                            lineNumber: 398,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                        lineNumber: 392,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                            lineNumber: 380,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                    lineNumber: 374,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                            lineNumber: 364,\n                                            columnNumber: 11\n                                        }, this),\n                                        loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                            children: [\n                                                1,\n                                                2,\n                                                3\n                                            ].map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white rounded-lg border border-blue-200 shadow-sm overflow-hidden animate-pulse\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-36 bg-gradient-to-br from-blue-100 to-blue-200 flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-12 h-12 text-blue-600 opacity-50\",\n                                                                fill: \"none\",\n                                                                viewBox: \"0 0 48 48\",\n                                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M44 24C44 22.8954 43.1046 22 42 22C40.8954 22 40 22.8954 40 24H44ZM24 8C25.1046 8 26 7.10457 26 6C26 4.89543 25.1046 4 24 4V8ZM39 40H9V44H39V40ZM8 39V9H4V39H8ZM40 24V39H44V24H40ZM9 8H24V4H9V8ZM9 40C8.44772 40 8 39.5523 8 39H4C4 41.7614 6.23857 44 9 44V40ZM39 44C41.7614 44 44 41.7614 44 39H40C40 39.5523 39.5523 40 39 40V44ZM8 9C8 8.44772 8.44771 8 9 8V4C6.23858 4 4 6.23857 4 9H8Z\",\n                                                                        fill: \"currentColor\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                        lineNumber: 412,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M6 35L16.6931 25.198C17.4389 24.5143 18.5779 24.4953 19.3461 25.1538L32 36\",\n                                                                        stroke: \"currentColor\",\n                                                                        strokeWidth: \"4\",\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                        lineNumber: 413,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M28 31L32.7735 26.2265C33.4772 25.5228 34.5914 25.4436 35.3877 26.0408L42 31\",\n                                                                        stroke: \"currentColor\",\n                                                                        strokeWidth: \"4\",\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                        lineNumber: 414,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M37 18L37 6\",\n                                                                        stroke: \"currentColor\",\n                                                                        strokeWidth: \"4\",\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                        lineNumber: 415,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M32 11L37 6L42 11\",\n                                                                        stroke: \"currentColor\",\n                                                                        strokeWidth: \"4\",\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                        lineNumber: 416,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                lineNumber: 411,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                            lineNumber: 410,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-4 bg-gray-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-4 bg-gray-200 rounded mb-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                    lineNumber: 420,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"h-3 bg-gray-200 rounded w-16\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                            lineNumber: 422,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"h-3 bg-gray-200 rounded w-8\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                            lineNumber: 423,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                    lineNumber: 421,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                            lineNumber: 419,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, i, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                    lineNumber: 409,\n                                                    columnNumber: 17\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                            lineNumber: 407,\n                                            columnNumber: 13\n                                        }, this) : communityCourses.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-12\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-400 text-lg mb-2\",\n                                                    children: \"暂无社区课程\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                    lineNumber: 431,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-500 text-sm\",\n                                                    children: \"社区课程正在建设中...\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                    lineNumber: 432,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                            lineNumber: 430,\n                                            columnNumber: 13\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                            layout: true,\n                                            transition: {\n                                                duration: 0.5,\n                                                ease: \"easeInOut\"\n                                            },\n                                            children: getDisplayedCommunityCourses().map((course, index)=>{\n                                                const isNewlyVisible = showAllCommunityCourses && index >= 6;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                                    className: \"bg-white rounded-lg border border-blue-200 shadow-sm overflow-hidden hover:shadow-md transition-shadow duration-200 cursor-pointer\",\n                                                    onClick: ()=>handleCourseClick(course),\n                                                    initial: {\n                                                        opacity: isNewlyVisible ? 0 : 1,\n                                                        y: isNewlyVisible ? 30 : 0,\n                                                        scale: isNewlyVisible ? 0.9 : 1\n                                                    },\n                                                    animate: {\n                                                        opacity: 1,\n                                                        y: 0,\n                                                        scale: 1\n                                                    },\n                                                    transition: {\n                                                        duration: isNewlyVisible ? 0.6 : 0.4,\n                                                        delay: isNewlyVisible ? (index - 6) * 0.15 + 0.2 : index * 0.08,\n                                                        ease: \"easeOut\",\n                                                        type: \"spring\",\n                                                        stiffness: 100,\n                                                        damping: 15\n                                                    },\n                                                    whileHover: {\n                                                        y: -8,\n                                                        scale: 1.03,\n                                                        boxShadow: \"0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)\",\n                                                        transition: {\n                                                            duration: 0.3,\n                                                            ease: \"easeOut\"\n                                                        }\n                                                    },\n                                                    layout: true,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-36 relative \".concat(!course.coverImage ? \"bg-gradient-to-br from-blue-100 to-blue-200\" : \"\"),\n                                                            children: course.coverImage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                src: course.coverImage,\n                                                                alt: course.title,\n                                                                fill: true,\n                                                                className: \"object-cover\",\n                                                                onLoad: ()=>{\n                                                                    console.log('✅ 封面图片加载成功: \"'.concat(course.title, '\" - ').concat(course.coverImage));\n                                                                },\n                                                                onError: (e)=>{\n                                                                    // 图片加载失败时隐藏图片，显示默认图标\n                                                                    console.log('❌ 封面图片加载失败: \"'.concat(course.title, '\" - ').concat(course.coverImage));\n                                                                    const target = e.target;\n                                                                    target.style.display = \"none\";\n                                                                    // 添加浅蓝色背景\n                                                                    const parent = target.parentElement;\n                                                                    if (parent) {\n                                                                        parent.className = parent.className + \" bg-gradient-to-br from-blue-100 to-blue-200\";\n                                                                    }\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                lineNumber: 472,\n                                                                columnNumber: 23\n                                                            }, this) : // 当没有封面图片时显示的图标\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-full h-full flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-12 h-12 text-blue-600 opacity-70\",\n                                                                    fill: \"none\",\n                                                                    viewBox: \"0 0 48 48\",\n                                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M44 24C44 22.8954 43.1046 22 42 22C40.8954 22 40 22.8954 40 24H44ZM24 8C25.1046 8 26 7.10457 26 6C26 4.89543 25.1046 4 24 4V8ZM39 40H9V44H39V40ZM8 39V9H4V39H8ZM40 24V39H44V24H40ZM9 8H24V4H9V8ZM9 40C8.44772 40 8 39.5523 8 39H4C4 41.7614 6.23857 44 9 44V40ZM39 44C41.7614 44 44 41.7614 44 39H40C40 39.5523 39.5523 40 39 40V44ZM8 9C8 8.44772 8.44771 8 9 8V4C6.23858 4 4 6.23857 4 9H8Z\",\n                                                                            fill: \"currentColor\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                            lineNumber: 496,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M6 35L16.6931 25.198C17.4389 24.5143 18.5779 24.4953 19.3461 25.1538L32 36\",\n                                                                            stroke: \"currentColor\",\n                                                                            strokeWidth: \"4\",\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                            lineNumber: 497,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M28 31L32.7735 26.2265C33.4772 25.5228 34.5914 25.4436 35.3877 26.0408L42 31\",\n                                                                            stroke: \"currentColor\",\n                                                                            strokeWidth: \"4\",\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                            lineNumber: 498,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M37 18L37 6\",\n                                                                            stroke: \"currentColor\",\n                                                                            strokeWidth: \"4\",\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                            lineNumber: 499,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            d: \"M32 11L37 6L42 11\",\n                                                                            stroke: \"currentColor\",\n                                                                            strokeWidth: \"4\",\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                            lineNumber: 500,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                    lineNumber: 495,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                lineNumber: 494,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                            lineNumber: 470,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-4 bg-gray-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"font-medium text-gray-800 mb-3 text-base line-clamp-2\",\n                                                                    children: course.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                    lineNumber: 508,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between text-sm text-gray-600\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-500\",\n                                                                            children: course.lessons\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                            lineNumber: 510,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-1.5\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                    className: \"w-4 h-4 text-gray-400\",\n                                                                                    fill: \"currentColor\",\n                                                                                    viewBox: \"0 0 20 20\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                            d: \"M10 12a2 2 0 100-4 2 2 0 000 4z\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                                            lineNumber: 513,\n                                                                                            columnNumber: 27\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                            fillRule: \"evenodd\",\n                                                                                            d: \"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z\",\n                                                                                            clipRule: \"evenodd\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                                            lineNumber: 514,\n                                                                                            columnNumber: 27\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                                    lineNumber: 512,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-gray-600 font-medium\",\n                                                                                    children: course.views\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                                    lineNumber: 516,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                            lineNumber: 511,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                                    lineNumber: 509,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                            lineNumber: 507,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, course.id, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                                    lineNumber: 443,\n                                                    columnNumber: 19\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                            lineNumber: 435,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                                    lineNumber: 358,\n                                    columnNumber: 9\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                            lineNumber: 191,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n                    lineNumber: 185,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true)\n    }, void 0, false, {\n        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\page.tsx\",\n        lineNumber: 155,\n        columnNumber: 5\n    }, this);\n}\n_s(CoursePlaza, \"gyljU7inwf6shDYB1IcTy/Ch8OM=\");\n_c1 = CoursePlaza;\nvar _c, _c1;\n$RefreshReg$(_c, \"AIBackground\");\n$RefreshReg$(_c1, \"CoursePlaza\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/(main)/course-plaza/page.tsx\n"));

/***/ })

});