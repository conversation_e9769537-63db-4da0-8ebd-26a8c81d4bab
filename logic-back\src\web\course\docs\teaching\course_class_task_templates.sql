/*
 Navicat Premium Data Transfer

 Source Server         : logic
 Source Server Type    : MySQL
 Source Server Version : 50744 (5.7.44-log)
 Source Host           : ************:13306
 Source Schema         : logicleaptest

 Target Server Type    : MySQL
 Target Server Version : 50744 (5.7.44-log)
 File Encoding         : 65001

 Date: 30/07/2025 10:16:56
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for course_class_task_templates
-- ----------------------------
DROP TABLE IF EXISTS `course_class_task_templates`;
CREATE TABLE `course_class_task_templates`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '任务模板ID',
  `course_id` int(11) NOT NULL COMMENT '所属课程ID',
  `task_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '任务名称',
  `task_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '任务描述',
  `duration_days` int(11) NOT NULL DEFAULT 7 COMMENT '任务持续天数',
  `attachments` json NULL COMMENT '附件/资源链接',
  `work_ids_str` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '关联作品ID字符串',
  `self_assessment_items` json NULL COMMENT '自评项列表',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '模板状态：0=禁用，1=启用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `attachments_count` int(11) GENERATED ALWAYS AS ((case when isnull(`attachments`) then 0 else json_length(`attachments`) end)) VIRTUAL COMMENT '附件数量' NULL,
  `assessment_items_count` int(11) GENERATED ALWAYS AS ((case when isnull(`self_assessment_items`) then 0 else json_length(`self_assessment_items`) end)) VIRTUAL COMMENT '自评项数量' NULL,
  `first_attachment_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci GENERATED ALWAYS AS (json_unquote(json_extract(`attachments`,'$[0].type'))) VIRTUAL COMMENT '第一个附件类型' NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_course`(`course_id`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_course_status`(`course_id`, `status`) USING BTREE,
  INDEX `idx_attachments_count`(`attachments_count`) USING BTREE,
  INDEX `idx_assessment_items_count`(`assessment_items_count`) USING BTREE,
  INDEX `idx_first_attachment_type`(`first_attachment_type`) USING BTREE,
  CONSTRAINT `course_class_task_templates_ibfk_1` FOREIGN KEY (`course_id`) REFERENCES `courses` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1005 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '课程班级任务模板表' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
