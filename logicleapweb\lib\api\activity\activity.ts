import request from '../../request'

// 活动参数类型
export interface ActivityParams {
  name: string;
  startTime: Date;
  endTime: Date;
  coverImage?: string;
  organizer: string;
  tagIds?: number[];
  status?: number;

  activityType?: number;
  // 新增媒体字段
  attachmentFiles?: string;
  promotionImage?: string;
  // 参赛组别字段
  competitionGroups?: string;
  // 报名表字段
  registrationForm?: string;
}

// 查询参数类型
export interface ActivityQueryParams {
  status?: number;
  page?: number;
  size?: number;
  keyword?: string;
  tagId?: number;
}

export const activityApi = {
  // 基础URL
  activityBaseUrl: '/api/v1/activity',
  activityWorkBaseUrl: '/api/v1/activity_work',
  activityTagBaseUrl: '/api/v1/activity_tag',
  tagBaseUrl: '/api/v1/tag',

  // 获取活动作品列表
  getActivityWorkListById: (id: number) => {
    return request.get(`${activityApi.activityWorkBaseUrl}/list/by-id/${id}`);
  },

  // 获取活动信息
  getActivityInfo: (id: number) => {
    return request.get(`${activityApi.activityBaseUrl}/infoActivity/${id}`);
  },

  // 获取活动列表（分页）
  getListUsePage: (params: ActivityQueryParams) => {
    return request.post(`${activityApi.activityBaseUrl}/page`, params);
  },

  // 创建活动
  create: (params: ActivityParams) => {
    return request.post(`${activityApi.activityBaseUrl}/createActivity`, params);
  },

  // 更新活动
  update: (params: ActivityParams & { id: number }) => {
    return request.put(`${activityApi.activityBaseUrl}/updateActivity/${params.id}`, params);
  },

  // 删除活动
  delete: (id: number) => {
    return request.delete(`${activityApi.activityBaseUrl}/deleteActivity/${id}`);
  },

  // 获取活动详情
  getDetail: (id: number) => {
    return request.get(`${activityApi.activityBaseUrl}/infoActivity/${id}`);
  },

  // 获取活动列表
  getList: (params: ActivityQueryParams) => {
    return request.get(`${activityApi.activityBaseUrl}/listActivity`, { params });
  },

  // 获取活动详情（包含作品）
  getDetailWithWorks: (id: number) => {
    return request.get(`${activityApi.activityBaseUrl}/infoActivityWithWorks/${id}`);
  },

  // 根据活动类型获取活动内容
  getActivityContent: (id: number) => {
    return request.get(`${activityApi.activityBaseUrl}/infoActivityContent/${id}`);
  },

  // 报名活动/提交作品
  submitWork: (params: {
    activityId: number;
    workId: number;
    contentType?: number;
  }) => {
    const submitData = {
      activityId: params.activityId,
      works: [{
        workId: params.workId,
        category: 'all',
        contentType: params.contentType || 1,
        isImage: params.contentType === 2
      }]
    };

    if (params.contentType === 2) {
      return request.post(`${activityApi.activityWorkBaseUrl}/add-image`, submitData);
    }

    return request.post(`${activityApi.activityWorkBaseUrl}/add-works`, submitData);
  },

  // 检查用户是否已经提交作品
  checkUserSubmitted: (activityId: number) => {
    return request.get(`${activityApi.activityWorkBaseUrl}/check-submitted/${activityId}`);
  },

  // 获取用户已报名的活动
  getUserActivities: () => {
    return request.get(`${activityApi.activityBaseUrl}/user/activities`);
  },

  // 获取活动的作品列表
  getActivityWorks: (activityId: number, params?: {
    isAwarded?: boolean;
    category?: string;
    status?: number;
    contentType?: number;
    userId?: number;
  }) => {
    return request.get(`${activityApi.activityWorkBaseUrl}/list/${activityId}`, { params });
  },

  // 更新报名状态
  updateSubmissionStatus: (id: number, status: number) => {
    return request.put(`${activityApi.activityWorkBaseUrl}/update-status/${id}`, { status });
  },

  // 获取用户的报名状态
  getUserSubmissionStatus: (activityId: number) => {
    return request.get(`${activityApi.activityWorkBaseUrl}/check-submitted/${activityId}`);
  },

  // 取消报名
  cancelSubmission: (id: number) => {
    return request.put(`${activityApi.activityWorkBaseUrl}/cancel/${id}`);
  },

  // 审核活动
  reviewActivity: (id: number, data: {
    isApproved: boolean;
    reason?: string;
  }) => {
    return request.post(`${activityApi.activityBaseUrl}/review/${id}`, data);
  },

  // 获取待审核活动列表
  getReviewList: () => {
    return request.get(`${activityApi.activityBaseUrl}/review/list`);
  }
}
