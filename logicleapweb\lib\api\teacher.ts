import request from '../request';

export const teacherApi = {
    /**
     * 获取教师的统计数据，例如学生数、班级数
     * @param teacherId 教师ID
     */
    getTeacherStat: async (teacherId: number) => {
        try {
            const response = await request.get(`/api/user/class/teacher/${teacherId}/stats`);
            return response.data;
        } catch (error) {
            console.error('获取教师统计数据失败:', error);
            throw error;
        }
    },
};

export default teacherApi; 