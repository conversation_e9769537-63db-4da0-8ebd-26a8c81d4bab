/* 发布任务弹窗样式 */
.publish-task-modal {
  max-width: 500px;
  min-width: 400px;
  height: 700px;
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 50%, #e6f3ff 100%);
  border: 2px solid #93c5fd;
  border-radius: 16px;
  display: flex;
  flex-direction: column;
}

.publish-task-modal .modal-header {
  padding: 16px 20px 0 20px;
  display: flex;
  justify-content: flex-end;
}

.publish-task-modal .modal-content-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 任务标签页样式 */
.task-tabs {
  display: flex;
  margin: 0 20px 20px 20px;
  border-bottom: 2px solid #e2e8f0;
  position: relative;
}

.task-tabs .tab-button {
  flex: 1;
  padding: 12px 16px;
  background: none;
  border: none;
  font-size: 16px;
  font-weight: 600;
  color: #64748b;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.task-tabs .tab-button.active {
  color: #3b82f6;
}

.task-tabs .tab-button.active::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  height: 2px;
  background: #3b82f6;
}

.task-tabs .tab-button:hover:not(.active) {
  color: #475569;
}

/* 任务信息内容样式 */
.task-info-content {
  padding: 0 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 目标学生信息样式 */
.target-students-info {
  margin-bottom: 8px;
  padding: 16px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.target-students-info h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.students-summary {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.student-count {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

.selected-students-list {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  max-height: 120px;
  overflow-y: auto;
}

.student-tag {
  display: inline-block;
  padding: 4px 8px;
  background: #3b82f6;
  color: white;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.selected-students-list::-webkit-scrollbar {
  width: 4px;
}

.selected-students-list::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 2px;
}

.selected-students-list::-webkit-scrollbar-thumb {
  background: rgba(59, 130, 246, 0.3);
  border-radius: 2px;
}

.selected-students-list::-webkit-scrollbar-thumb:hover {
  background: rgba(59, 130, 246, 0.5);
}

.form-group {
  display: flex;
  flex-direction: column;
}

.task-input {
  width: 100%;
  height: 50px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  padding: 12px 16px;
  font-size: 16px;
  color: #1e293b;
  outline: none;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.task-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.task-input::placeholder {
  color: #94a3b8;
  font-weight: 500;
}

.task-textarea {
  width: 100%;
  min-height: 100px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  padding: 12px 16px;
  font-size: 16px;
  color: #1e293b;
  outline: none;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  resize: vertical;
  font-family: inherit;
}

.task-textarea:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.task-textarea::placeholder {
  color: #94a3b8;
  font-weight: 500;
}

/* 资源内容样式 */
.task-resources-content {
  padding: 0 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  max-height: 450px;
  overflow-y: auto;
}

.resource-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.section-label {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
}

/* 作品区域样式 */
.works-area {
  width: 100%;
  height: 160px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
  border: 2px solid #3b82f6;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
}

.works-placeholder {
  color: #64748b;
  font-size: 16px;
  font-weight: 500;
}

/* 附件上传样式 */
.attachment-upload {
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.upload-area {
  position: relative;
}

.file-input {
  display: none;
}

.upload-btn {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border: 2px solid #3b82f6;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  flex-shrink: 0;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.upload-btn:hover {
  border-color: #1d4ed8;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
  transform: translateY(-1px);
}

.upload-icon {
  font-size: 32px;
  font-weight: bold;
  color: white;
}

.attachment-upload .file-info {
  color: #64748b;
  font-size: 13px;
  line-height: 1.5;
  flex: 1;
  margin-top: 0;
  padding-top: 8px;
  word-wrap: break-word;
}

/* 已上传文件列表样式 */
.uploaded-files {
  margin-top: 16px;
}

.files-label {
  font-size: 14px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 8px;
}

.files-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.file-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
  border: 1px solid #e2e8f0;
  border-radius: 10px;
  backdrop-filter: blur(10px);
}

.file-name {
  flex: 1;
  font-size: 15px;
  color: #1e293b;
  word-break: break-all;
  font-weight: 500;
}

.file-size {
  font-size: 13px;
  color: #64748b;
}

.remove-file-btn {
  width: 24px;
  height: 24px;
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 50%;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.remove-file-btn:hover {
  background: #dc2626;
  transform: scale(1.1);
}

/* 滚动条样式 */
.task-resources-content::-webkit-scrollbar {
  width: 6px;
}

.task-resources-content::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

.task-resources-content::-webkit-scrollbar-thumb {
  background: rgba(59, 130, 246, 0.3);
  border-radius: 3px;
}

.task-resources-content::-webkit-scrollbar-thumb:hover {
  background: rgba(59, 130, 246, 0.5);
}

/* 发布按钮样式 */
.publish-section {
  padding: 0px 20px 20px 20px;
  display: flex;
  justify-content: center;
  margin-top: -10px;
}

.publish-btn {
  width: 200px;
  height: 50px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
}

.publish-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
  transform: translateY(-2px);
}

.publish-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  box-shadow: none;
  transform: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .publish-task-modal {
    min-width: 320px;
    max-width: 95%;
    height: 80vh;
  }

  .task-input,
  .task-textarea {
    font-size: 14px;
  }

  .task-tabs .tab-button {
    font-size: 14px;
    padding: 10px 12px;
  }

  .publish-btn {
    width: 100%;
    margin: 0 20px;
  }
}
