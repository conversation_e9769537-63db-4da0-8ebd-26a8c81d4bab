# Course 模块优化 TODO 清单

## 🎯 总体目标
对整个 Course 模块进行架构层面的优化，提升代码质量和可维护性，解决当前存在的技术债务。

## 📋 优化任务清单

### 🔥 **第一阶段：重构 Management Controller** (预计 2-3 天)

#### 1.1 移动 DTO 定义到专门文件 ⭐⭐⭐

- [x] 完成进度

- **文件**: `controller/management.controller.ts` (第 18-59 行)
- **问题**: DTO 类定义在控制器文件中，违反单一职责原则
- **目标**: 
  - 创建 `application/dto/management/query.dto.ts`
  - 移动 `GetMyCourseSeriesQueryDto` 和 `GetSeriesCoursesQueryDto`
- **预计时间**: 30 分钟
- **验收标准**: 控制器文件不再包含 DTO 定义

#### 1.2 简化控制器方法 ⭐⭐⭐

- [x] 完成进度

- **文件**: `controller/management.controller.ts`
- **问题**: `getMyCourseSeries` 方法 40+ 行，包含过多业务逻辑
- **目标**:
  - 将数据转换逻辑移到服务层
  - 控制器只负责参数接收和响应返回
  - 每个控制器方法不超过 15 行
- **预计时间**: 2 小时
- **验收标准**: 控制器方法简洁，业务逻辑在服务层

#### 1.3 统一响应格式处理 ⭐⭐
- **问题**: 重复的响应构建代码
- **目标**: 创建 `ResponseBuilder` 工具类
- **预计时间**: 1 小时
- **验收标准**: 减少 50% 的重复响应构建代码

#### 1.4 优化参数验证 ⭐⭐

- [x] 完成进度

- **目标**: 使用 ValidationPipe 和装饰器统一参数验证
- **预计时间**: 1 小时
- **验收标准**: 移除手动参数验证代码

### 🔥 **第二阶段：模块依赖解耦** (预计 1-2 天)

#### 2.1 创建独立的锁管理模块 ⭐⭐⭐
- **文件**: `course.module.ts` (第 24-33 行)
- **问题**: 直接依赖 Payment 模块的锁管理器
- **目标**:
  - 在 `src/common/lock` 创建通用锁管理模块
  - 包含 `DistributedLock`, `OptimisticLock`, `PessimisticLock`
- **预计时间**: 3 小时
- **验收标准**: Course 模块不再直接依赖 Payment 模块

#### 2.2 移除 Payment 模块依赖 ⭐⭐⭐
- **目标**: 从 Course 模块中移除对 Payment 模块的直接依赖
- **预计时间**: 1 小时
- **验收标准**: import 语句中不再有 payment 路径

#### 2.3 更新配置结构 ⭐⭐
- **文件**: `course.module.ts` (第 51-57 行)
- **问题**: 配置命名空间混乱 (使用 payment 命名空间)
- **目标**: 使用 course 专用的配置命名空间
- **预计时间**: 30 分钟
- **验收标准**: 配置结构清晰，命名合理

### 🔥 **第三阶段：服务层重构** (预计 3-4 天)

#### 3.1 拆分 oneClickStart 方法 ⭐⭐⭐
- **文件**: `application/services/teaching/teaching.service.ts`
- **问题**: 200+ 行的巨型方法
- **目标**: 拆分为 8-10 个小方法，每个不超过 30 行
- **预计时间**: 4 小时
- **验收标准**: 方法复杂度降低，可读性提升

#### 3.2 提取数据收集方法 ⭐⭐
- **目标**: 将 Promise.all 数据获取逻辑提取为 `gatherTeachingContext` 方法
- **预计时间**: 1 小时
- **验收标准**: 数据收集逻辑独立，便于测试

#### 3.3 提取核心事务逻辑 ⭐⭐
- **目标**: 将事务内业务逻辑提取为 `executeOneClickStartWithTransaction` 方法
- **预计时间**: 2 小时
- **验收标准**: 事务逻辑清晰，嵌套层级减少

#### 3.4 移除测试代码 ⭐
- **问题**: 生产代码中包含 `testRollbackStep` 测试逻辑
- **目标**: 移除硬编码测试逻辑，使用配置控制
- **预计时间**: 30 分钟
- **验收标准**: 生产代码干净，无测试代码

### ⚡ **第四阶段：异常处理统一** (预计 1-2 天)

#### 4.1 创建全局异常过滤器 ⭐⭐⭐
- **文件**: `controller/teaching.controller.ts` (第 37-64 行)
- **问题**: 异常处理逻辑冗长，重复度高
- **目标**: 创建 `CourseExceptionFilter` 全局过滤器
- **预计时间**: 2 小时
- **验收标准**: 控制器中异常处理代码减少 80%

#### 4.2 统一错误响应格式 ⭐⭐
- **目标**: 标准化所有错误响应的数据结构
- **预计时间**: 1 小时
- **验收标准**: 错误响应格式一致

### ⚡ **第五阶段：性能优化** (预计 2-3 天)

#### 5.1 优化数据库查询 ⭐⭐⭐
- **文件**: `application/services/management/management.service.ts` (第 75-84 行)
- **问题**: 复杂聚合查询可能存在性能问题
- **目标**: 
  - 添加查询索引优化建议
  - 实现查询结果缓存
- **预计时间**: 3 小时
- **验收标准**: 查询性能提升 30%+

#### 5.2 添加缓存机制 ⭐⭐
- **目标**: 为课程设置、模板等相对静态数据添加缓存
- **预计时间**: 2 小时
- **验收标准**: 热点数据缓存命中率 > 80%

#### 5.3 实现查询监控 ⭐
- **目标**: 添加慢查询监控和告警
- **预计时间**: 1 小时
- **验收标准**: 能够监控和记录慢查询

### 🔧 **第六阶段：测试完善** (预计 2-3 天)

#### 6.1 完善单元测试 ⭐⭐⭐
- **目标**: 为重构后的方法编写单元测试
- **覆盖率目标**: > 90%
- **预计时间**: 4 小时
- **验收标准**: 所有核心方法有对应测试

#### 6.2 添加集成测试 ⭐⭐
- **目标**: 测试模块间的协作
- **预计时间**: 2 小时
- **验收标准**: 主要业务流程有集成测试

#### 6.3 性能测试 ⭐
- **目标**: 验证优化效果
- **预计时间**: 1 小时
- **验收标准**: 性能指标达到预期

## 📊 成功指标

### 代码质量指标
- [ ] 方法平均行数 < 30 行
- [ ] 圈复杂度 < 10
- [ ] 代码重复率 < 5%
- [ ] 测试覆盖率 > 90%

### 性能指标
- [ ] 平均响应时间 < 500ms
- [ ] 99分位响应时间 < 2s
- [ ] 并发处理能力 > 100 QPS
- [ ] 错误率 < 0.1%

## 🚀 实施计划

### 第一周 (高优先级)
- **周一-周二**: 重构 Management Controller
- **周三**: 模块依赖解耦
- **周四-周五**: 服务层重构 (第一部分)

### 第二周 (中优先级)
- **周一-周二**: 服务层重构 (完成)
- **周三**: 异常处理统一
- **周四-周五**: 性能优化

### 第三周 (完善阶段)
- **周一-周三**: 测试完善
- **周四-周五**: 文档更新和验收

## 📝 注意事项

### 风险控制
- [ ] **渐进式重构**: 每次只重构一个模块，确保功能不受影响
- [ ] **充分测试**: 每个重构步骤都要有对应的测试验证
- [ ] **回滚准备**: 保留原始代码备份，出问题时可快速回滚

### 团队协作
- [ ] **代码审查**: 每个重构 PR 都需要 Code Review
- [ ] **文档更新**: 及时更新相关技术文档
- [ ] **知识分享**: 重构完成后进行团队分享

---

**创建时间**: 2025-01-02  
**负责人**: [待分配]  
**预计完成时间**: 3周  
**优先级**: 🔥 高优先级 -> ⚡ 中优先级 -> 🔧 低优先级
