import React, { useEffect, useState, useLayoutEffect } from 'react';
import styles from '@/components/login-dialog.module.css';
import userApi from '@/lib/api/user';
import { useSelector } from 'react-redux';
import { UserState } from '../../../types/user';
import { RootState } from '../../../lib/store';
import { Form, Input, Modal, notification, Select, Space, UploadFile, Upload, Button, Spin, Typography, theme, Tooltip, AutoComplete } from 'antd';
import { GetNotification } from 'logic-common/dist/components/Notification';
import { PlusOutlined, LoadingOutlined, FileWordOutlined, FileExcelOutlined, FilePdfOutlined, FileImageOutlined, FileOutlined, DeleteOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import pcaData from '../../../public/pca.json';
import { schoolApi } from '@/lib/api/school';
import { uploadApi } from '@/lib/api/upload';
import teacherApi from '@/lib/api/teacher-auth';

// 定义滚动条样式
const scrollbarStyles = `
  .file-scroll-container::-webkit-scrollbar {
    height: 8px;
    background-color: #f1f1f1;
  }
  
  .file-scroll-container::-webkit-scrollbar-thumb {
    background-color: #888;
    border-radius: 4px;
  }
  
  .file-scroll-container::-webkit-scrollbar-thumb:hover {
    background-color: #555;
  }
  
  .file-scroll-container::-webkit-scrollbar-track {
    background-color: #f1f1f1;
    border-radius: 4px;
  }
  
  /* 添加模态框自适应样式 */
  @media screen and (max-width: 768px) {
    .teacher-auth-modal {
      width: 90% !important;
      margin: 0 auto;
    }
  }
  
  /* 模态框内容滚动样式 */
  .teacher-auth-modal-body {
    max-height: calc(80vh - 72px); /* 预留底部按钮的空间 */
    overflow-y: auto;
    padding-right: 8px;
  }
  
  .teacher-auth-modal-body::-webkit-scrollbar {
    width: 6px;
    background-color: #f1f1f1;
  }
  
  .teacher-auth-modal-body::-webkit-scrollbar-thumb {
    background-color: #888;
    border-radius: 3px;
  }
  
  .teacher-auth-modal-body::-webkit-scrollbar-thumb:hover {
    background-color: #555;
  }
  
  /* 左右布局容器 */
  .auth-flex-container {
    display: flex;
    flex-direction: row;
    gap: 16px;
    width: 100%;
    flex-wrap: wrap;
    margin-bottom: 24px; /* 增加底部边距，确保不会与提交按钮重叠 */
  }
  
  /* 左侧和右侧列 */
  .auth-column-left, .auth-column-right {
    flex: 1 1 calc(50% - 8px);
    min-width: 0;
    display: flex;
    flex-direction: column;
  }
  
  /* 底部横跨列 */
  .auth-column-bottom {
    flex: 1 1 100%;
    margin-top: 16px;
  }
  
  /* 响应式布局 */
  @media screen and (max-width: 768px) {
    .auth-flex-container {
      flex-direction: column;
      gap: 16px;
    }
   
    .auth-column-left, .auth-column-right {
      flex: 1 1 100%;
    }
  }
  
  /* 固定底部按钮样式 */
  .teacher-auth-modal-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 16px 0;
    background-color: white;
    text-align: center;
    border-top: 1px solid #f0f0f0;
    border-radius: 0 0 16px 16px;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);
  }
`;

// 定义接口类型
export interface School {
  id: number | string;
  schoolName: string;
  province?: string;
  city?: string;
  district?: string;
}

// 组件的props类型定义
interface TamProps {
  // 成功回调函数，为静态类型的
  onSuccess: () => void
  visible: boolean
  handleCloseTeacherAuthModal: () => void
}


// 定义表单字段类型
type FieldType = {
  nickName: string;
  schoolInfo: {
    schoolName: string;
    province: string;
    city: string;
    district: string;
  }
}



// 组件的dom,箭头函数的形参传入成功执行后的回调函数
const TeacherAuthModal = ({ onSuccess, visible, handleCloseTeacherAuthModal }: TamProps) => {
  // 逻辑层

  // 获取主题变量
  const { token } = theme.useToken();

  // 添加窗口大小状态
  const [windowSize, setWindowSize] = useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 0,
    height: typeof window !== 'undefined' ? window.innerHeight : 0,
  });

  // 监听窗口大小变化
  useEffect(() => {
    function handleResize() {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    }

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // 根据窗口大小计算模态窗宽度
  const getModalWidth = () => {
    if (windowSize.width <= 576) return '95%';
    if (windowSize.width <= 768) return 460;
    return 780; // 增加宽度以适应左右布局
  };

  // 获取提示框
  const nt = GetNotification()
  // 文件列表
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  // 本地文件对象列表，用于存储实际文件对象
  const [localFiles, setLocalFiles] = useState<{ [uid: string]: File }>({});
  // 从redux中获取userId
  const user = useSelector((state: RootState) => state.user.userState)
  const [error, setError] = useState('');
  const [schoolName, setSchoolName] = useState('');
  const [filteredSchools, setFilteredSchools] = useState<School[]>([]);
  const [province, setProvince] = useState('');
  const [city, setCity] = useState('');
  const [district, setDistrict] = useState('');
  const [cities, setCities] = useState<any[]>([]);
  const [districts, setDistricts] = useState<any[]>([]);
  // 学校名词搜索框
  const [schoolSearchText, setSchoolSearchText] = useState('');
  // 学校下拉框的展示
  const [showSchoolDropdown, setShowSchoolDropdown] = useState(false);
  // 表单
  const [form] = Form.useForm<FieldType>();
  // 提交状态
  const [submitting, setSubmitting] = useState(false);
  // 上传状态 - 新增
  const [uploading, setUploading] = useState(false);
  // 上传提示文字 - 新增
  const [uploadingText, setUploadingText] = useState('');

  // 钩子
  // 省市区发生变动的时候更新下拉框的值
  useEffect(() => {
    if (province) {
      const selectedProvince = pcaData.find(p => p.name === province);
      if (selectedProvince && selectedProvince.children) {
        if (isMunicipality(province)) {
          // 直辖市的情况下，城市列表为空，直接设置区县列表
          setCities([]);
          setDistricts(selectedProvince.children[0].children || []);
          if (!city) {
            setCity(province); // 直接将省份名称设为城市名称
          }
        } else {
          // 普通省份的处理
          setCities(selectedProvince.children);
          if (city) {
            const selectedCity = selectedProvince.children.find((c: any) => c.name === city);
            if (selectedCity && selectedCity.children) {
              setDistricts(selectedCity.children);
            }
          }
        }
      }
    } else {
      setCities([]);
      setDistricts([]);
      setCity('');
      setDistrict('');
    }
  }, [province, city, pcaData]);



  // 判断是否是直辖市
  const isMunicipality = (provinceName: string) => {
    return ['北京市', '上海市', '天津市', '重庆市'].includes(provinceName);
  };

  const handleSubmit = async () => {
    try {
      // 表单验证
      const values = await form.validateFields();

      if (!schoolName) {
        setError('请输入或选择学校');
        nt.error('请输入或选择学校');
        return;
      }

      if (!values.nickName) {
        setError('请输入您的姓名');
        nt.error('请输入您的姓名');
        return;
      }

      // 设置提交状态
      setSubmitting(true);
      setUploading(true);
      setUploadingText('提交认证申请中，请稍候...');

      // 拼接学校信息
      const schoolInfo = `${schoolName}|${province}|${city}|${district}`;

      try {
        // 上传所有本地文件到OSS
        const attachments = [];

        for (const uid in localFiles) {
          if (fileList.some(file => file.uid === uid)) {
            try {
              const file = localFiles[uid];
              // 上传文件到OSS
              console.log("上传ing");

              const url = await uploadApi.uploadToOss(file);
              // 添加到附件列表
              attachments.push({
                url: url,
                name: file.name,
                type: file.type || 'application/octet-stream',
                size: file.size || 0
              });
            } catch (error) {
              console.error('上传文件失败:', error);
              nt.error(`文件 ${localFiles[uid].name} 上传失败`);
              setSubmitting(false);
              setUploading(false);
              return;
            }
          }
        }

        // 准备提交数据
        const submitData = {
          teacherId: user.userId,
          teacherName: values.nickName,
          schoolInfo: schoolInfo,
          attachments: attachments,
        };

        console.log('提交教师认证信息:', submitData);

        // 调用提交接口
        const response = await teacherApi.submitAuth(submitData);
        console.log('API响应:', response);

        // 检查响应状态
        if (response.data && response.data.code === 200) {
          nt.success('认证申请提交成功，请等待审核');
          // 关闭模态框并执行成功回调
          handleCloseTeacherAuthModal();
          onSuccess();
        } else {
          // 处理业务错误响应 (HTTP 200但业务错误)
          const errorMessage = response.data?.message || '认证申请提交失败';
          nt.error(errorMessage);
          console.log('业务错误:', response.data);
        }
      } catch (apiError: any) {
        // 处理HTTP异常，这里可以捕获到后端抛出的HttpException
        console.error('API错误:', apiError);

        // 在控制台打印出完整的错误对象，方便调试
        console.log('完整错误对象:', JSON.stringify(apiError, null, 2));

        if (apiError.response) {
          // 处理后端返回的结构化错误
          const errorData = apiError.response.data;
          console.log('后端错误数据:', errorData);

          // 直接显示完整的错误消息，确保用户能看到
          if (typeof errorData === 'object' && errorData !== null) {
            console.log("错误消息是对象");
            nt.error(errorData.message || '提交失败，请稍后重试');
          } else if (typeof errorData === 'string') {
            console.log("错误消息是字符串");
            nt.error(errorData || '提交失败，请稍后重试');
          } else {
            console.log("错误消息格式未知");
            nt.error(`请求失败 (${apiError.response.status}): 请稍后重试`);
          }
        } else if (apiError.request) {
          // 请求已发送但没有收到响应
          nt.error('服务器无响应，请检查网络连接');
        } else {
          // 请求配置错误
          nt.error(apiError.message || '网络错误，请检查网络连接');
        }
      }
    } catch (error: any) {
      console.error('表单验证或其他错误:', error);
      nt.error(error.message || '提交失败，请检查表单信息');
    } finally {
      setSubmitting(false);
      setUploading(false);
    }
  };


  // 认证信息包括的提示信息For列表

  const handleCancel = () => {
    // 上传中不允许关闭
    if (uploading) return;
    handleCloseTeacherAuthModal();
  };

  const handleSchoolSelect = (value: any, option: any) => {
    // 1. 尝试从option.data获取学校数据
    // 2. 如果option本身是学校对象，则使用option
    // 3. 如果以上都失败，则通过id在filteredSchools中查找学校
    // 4. 如果找不到学校，则使用手动输入的值创建一个新学校对象
    let selectedSchool = option?.data;
    console.log('zww:', selectedSchool);

    if (!selectedSchool && option?.label && typeof option.value !== 'undefined') {
      selectedSchool = {
        id: option.value,
        schoolName: option.label,
        // 这里不设置省市区，会在下面从filteredSchools中找到完整信息
      };
    }

    if (!selectedSchool && typeof value !== 'undefined') {
      if (typeof value === 'string') {
        // 用户输入的自定义学校，创建一个新的学校对象
        const currentProvince = province || '';
        const currentCity = city || '';
        const currentDistrict = district || '';

        selectedSchool = {
          id: `custom-${Date.now()}`,
          schoolName: value,
          province: currentProvince,
          city: currentCity,
          district: currentDistrict
        };
      } else {
        // 通过id在已过滤的学校列表中查找
        selectedSchool = filteredSchools.find(school => school.id === value);
      }
    }

    if (!selectedSchool) {
      console.error('无法找到所选学校的数据:', { value, option });
      return;
    }

    // 从filteredSchools中找到完整的学校信息（包括省市区）
    const completeSchoolInfo = filteredSchools.find(
      school => school.id === selectedSchool.id || school.schoolName === selectedSchool.schoolName
    ) || selectedSchool;

    console.log('选中学校数据:', completeSchoolInfo);

    setSchoolName(completeSchoolInfo.schoolName);

    // 处理省市区信息，确保没有null字符串
    const selectedProvince = completeSchoolInfo.province && completeSchoolInfo.province !== 'null'
      ? completeSchoolInfo.province : '';

    if (selectedProvince) {
      setProvince(selectedProvince);

      // 设置城市（如果是直辖市，则城市名与省份名相同）
      const selectedCity = isMunicipality(selectedProvince)
        ? selectedProvince
        : (completeSchoolInfo.city && completeSchoolInfo.city !== 'null' ? completeSchoolInfo.city : '');
      setCity(selectedCity);

      // 设置区县
      const selectedDistrict = completeSchoolInfo.district && completeSchoolInfo.district !== 'null'
        ? completeSchoolInfo.district : '';
      setDistrict(selectedDistrict);
    }

    // 隐藏学校下拉列表
    setShowSchoolDropdown(false);
  };



  const fetchSchools = async (searchText?: string) => {
    // 如果有搜索文本，则不需要考虑地区筛选条件
    // 如果没有搜索文本，且没有完整的地区信息，则不搜索
    if (!searchText && (!province || (!isMunicipality(province) && !city) || !district)) {
      setFilteredSchools([]);
      return;
    }

    try {
      const params: any = {};

      if (searchText) {
        params.keyword = searchText;
        // 有搜索文本时可以不限制地区
      } else {
        // 无搜索文本时，必须有地区筛选
        if (province) {
          params.province = province;
          if (!isMunicipality(province) && city) {
            params.city = city;
          }
          if (district) {
            params.district = district;
          }
        }
      }

      console.log('搜索学校参数:', params);
      const response = await schoolApi.getList(params);

      if (response.data?.code === 200) {
        // 获取学校列表
        let schools = response.data?.data || response.data?.list || response.data || [];

        // 处理可能的null字符串
        schools = schools.map((school: any) => ({
          ...school,
          province: school.province && school.province !== 'null' ? school.province : '',
          city: school.city && school.city !== 'null' ? school.city : '',
          district: school.district && school.district !== 'null' ? school.district : ''
        }));

        console.log('获取到学校列表:', schools);
        setFilteredSchools(schools);
      } else {
        console.warn('搜索学校接口返回错误:', response);
        setFilteredSchools([]);
      }
    } catch (error) {
      console.error('获取学校列表失败:', error);
      setFilteredSchools([]);
    }
  };

  // 获取文件图标
  const getFileIcon = (fileType: string) => {
    if (fileType.includes('word') || fileType.includes('docx') || fileType.includes('doc')) {
      return <FileWordOutlined style={{ fontSize: '28px', color: '#2B5797' }} />;
    } else if (fileType.includes('excel') || fileType.includes('xlsx') || fileType.includes('xls') || fileType.includes('csv')) {
      return <FileExcelOutlined style={{ fontSize: '28px', color: '#1D6F42' }} />;
    } else if (fileType.includes('pdf')) {
      return <FilePdfOutlined style={{ fontSize: '28px', color: '#FF0000' }} />;
    } else if (fileType.includes('image/')) {
      return <FileImageOutlined style={{ fontSize: '28px', color: '#FFB400' }} />;
    } else {
      return <FileOutlined style={{ fontSize: '28px', color: '#8C8C8C' }} />;
    }
  };

  // 获取文件扩展名
  const getFileExtension = (fileName: string) => {
    const parts = fileName.split('.');
    if (parts.length > 1) {
      return parts[parts.length - 1].toUpperCase();
    }
    return '';
  };

  // 自定义文件项渲染
  const customFileItemRender = (originNode: React.ReactElement, file: UploadFile, fileList: UploadFile[]) => {
    const isImage = file.type?.startsWith('image/');

    // 如果是图片，使用默认渲染
    if (isImage && file.thumbUrl) {
      return (
        <div className="custom-upload-item" style={{ position: 'relative' }}>
          {originNode}
          <div className="file-delete-icon"
            style={{
              position: 'absolute',
              top: '0',
              right: '0',
              background: 'rgba(0,0,0,0.65)',
              width: '22px',
              height: '22px',
              borderRadius: '0 0 0 8px',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              cursor: 'pointer'
            }}
            onClick={(e) => {
              e.stopPropagation();
              // 直接调用上传组件的onRemove方法
              handleRemoveFile(file);
            }}
          >
            <DeleteOutlined style={{ color: '#fff', fontSize: '14px' }} />
          </div>
        </div>
      );
    }

    // 非图片类型，自定义渲染
    return (
      <div className="custom-file-card"
        style={{
          width: '104px',
          height: '104px',
          border: `1px solid ${token.colorBorderSecondary}`,
          borderRadius: '12px',
          padding: '8px',
          display: 'inline-flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'space-between',
          position: 'relative',
          background: token.colorFillQuaternary,
          boxShadow: `0 2px 8px ${token.colorBgElevated}`,
          transition: 'all 0.3s'
        }}>
        <div className="file-delete-icon"
          style={{
            position: 'absolute',
            top: '0',
            right: '0',
            background: 'rgba(0,0,0,0.65)',
            width: '22px',
            height: '22px',
            borderRadius: '0 0 0 12px',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            cursor: 'pointer',
            transition: 'background 0.2s'
          }}
          onClick={(e) => {
            e.stopPropagation();
            handleRemoveFile(file);
          }}
        >
          <DeleteOutlined style={{ color: '#fff', fontSize: '14px' }} />
        </div>

        <div style={{ marginTop: '10px' }}>
          {getFileIcon(file.type || '')}
        </div>

        <div style={{
          textAlign: 'center',
          width: '100%',
          marginTop: '4px',
          overflow: 'hidden'
        }}>
          <Typography.Text
            ellipsis={{ tooltip: file.name }}
            style={{ fontSize: '12px', lineHeight: '1.2' }}
          >
            {file.name}
          </Typography.Text>
          <div style={{
            fontSize: '11px',
            color: token.colorTextSecondary,
            marginTop: '2px',
            background: token.colorFillSecondary,
            padding: '0 4px',
            borderRadius: '4px',
            display: 'inline-block'
          }}>
            {getFileExtension(file.name)}
          </div>
        </div>
      </div>
    );
  };

  // 处理文件删除
  const handleRemoveFile = (file: UploadFile) => {
    // 从文件列表中移除
    setFileList(fileList.filter(item => item.uid !== file.uid));

    // 从本地文件映射中移除
    setLocalFiles(prev => {
      const newLocalFiles = { ...prev };
      delete newLocalFiles[file.uid];
      return newLocalFiles;
    });

    // 如果有临时URL，需要释放
    if (file.thumbUrl && file.thumbUrl.startsWith('blob:')) {
      URL.revokeObjectURL(file.thumbUrl);
    }
  };

  // 样式对象
  const modalStyles = {
    header: {
      marginBottom: '24px',
      textAlign: 'center' as const,
      fontSize: windowSize.width <= 576 ? '20px' : '24px',
      fontWeight: 'bold',
      color: token.colorTextHeading,
    },
    sectionTitle: {
      fontSize: windowSize.width <= 576 ? '14px' : '16px',
      fontWeight: 'bold',
      marginBottom: '12px',
      color: token.colorTextHeading,
      position: 'relative' as const,
      paddingLeft: '12px',
      display: 'flex',
      alignItems: 'center',
    },
    sectionTitleBefore: {
      content: '""',
      position: 'absolute' as const,
      left: 0,
      top: '50%',
      transform: 'translateY(-50%)',
      width: '4px',
      height: '16px',
      backgroundColor: token.colorPrimary,
      borderRadius: '2px',
    },
    formContainer: {
      padding: '0 8px',
    },
    listItem: {
      margin: '6px 0',
      fontSize: '14px',
      color: token.colorTextSecondary,
    },
    uploadSection: {
      marginBottom: '24px',
      padding: '16px',
      borderRadius: '12px',
      background: token.colorFillTertiary,
    },
    submitButton: {
      borderRadius: '32px',
      height: '40px',
      padding: '0 32px',
      fontSize: '16px',
      boxShadow: `0 4px 12px ${token.colorPrimaryBg}`,
      transition: 'all 0.3s',
    },
  };

  //  视图层
  return (
    <>
      <style>{scrollbarStyles}</style>
      <Modal
        open={visible}
        zIndex={1000}
        onCancel={handleCancel}
        footer={null}
        centered
        maskClosable={!uploading}
        closable={!uploading}
        keyboard={!uploading}
        width={getModalWidth()}
        style={{
          borderRadius: '16px',
          overflow: 'hidden',
          top: windowSize.width <= 576 ? 20 : undefined // 小屏幕时调整位置
        }}
        styles={{
          body: {
            padding: windowSize.width <= 576 ? '16px 20px 88px' : '24px 32px 88px',
            maxHeight: '80vh',
            overflowX: 'hidden' // 防止水平滚动条
          }
        }}
        maskStyle={{ backdropFilter: 'blur(4px)', background: 'rgba(0, 0, 0, 0.45)' }}
        className="teacher-auth-modal"
      >
        {/* 上传遮罩 */}
        {uploading && (
          <div style={{
            position: 'absolute',
            left: 0,
            top: 0,
            width: '100%',
            height: '100%',
            background: 'rgba(0, 0, 0, 0.7)',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            zIndex: 10,
            borderRadius: '16px',
            color: 'white',
            textAlign: 'center',
            backdropFilter: 'blur(4px)',
          }}>
            <Spin indicator={<LoadingOutlined style={{ fontSize: 36, color: token.colorPrimary }} spin />} />
            <div style={{ marginTop: 16, fontSize: '16px', fontWeight: 500 }}>{uploadingText}</div>
          </div>
        )}

        <div className="teacher-auth-modal-body">
          {/* 认证标题 */}
          <div style={modalStyles.header}>
            教师身份认证
          </div>

          {/* 认证信息表单 */}
          <Form
            form={form}
            onFinish={handleSubmit}
            layout="vertical"
            style={modalStyles.formContainer}
          >
            <div className="auth-flex-container">
              {/* 左侧列：个人信息、学校信息和附加材料 */}
              <div className="auth-column-left">
                {/* 个人信息栏 */}
                <div style={modalStyles.sectionTitle}>
                  <span style={modalStyles.sectionTitleBefore}></span>
                  个人信息
                </div>
                <Form.Item
                  label="姓名"
                  name="nickName"
                  rules={[{ required: true, message: '请输入您的姓名' }]}
                >
                  <Input
                    placeholder='请输入您的姓名'
                    style={{ borderRadius: '8px', height: '40px' }}
                  />
                </Form.Item>

                {/* 学校信息栏 */}
                <div style={modalStyles.sectionTitle}>
                  <span style={modalStyles.sectionTitleBefore}></span>
                  学校信息
                </div>

                {/* 学校位置 */}
                <Form.Item>
                  <AutoComplete
                    style={{
                      margin: '8px 0 16px',
                      borderRadius: '8px',
                      width: '100%'
                    }}
                    placeholder="请输入学校名称搜索，也可直接输入自定义学校名"
                    value={schoolName}
                    onChange={(value) => {
                      if (typeof value === 'string') {
                        setSchoolName(value);
                      }
                    }}
                    onSelect={(value, option) => handleSchoolSelect(value, option)}
                    onSearch={(value) => {
                      setSchoolSearchText(value);
                      if (value && value.length >= 1) {
                        fetchSchools(value);
                        setShowSchoolDropdown(true);
                      }
                    }}
                    onDropdownVisibleChange={(open) => {
                      setShowSchoolDropdown(open);
                      if (open && province && (isMunicipality(province) ? true : city) && district) {
                        fetchSchools();
                      }
                    }}
                    open={showSchoolDropdown}
                    filterOption={(inputValue, option) => {
                      if (!option?.value) return false;
                      return String(option.value).toLowerCase().includes(inputValue.toLowerCase());
                    }}
                    defaultActiveFirstOption={false}
                    notFoundContent={
                      filteredSchools.length === 0 ? "未找到相关学校，可直接输入学校名" :
                        !province && !schoolSearchText ? "请先选择省份或直接搜索学校名称" :
                          (!isMunicipality(province) && !city && !schoolSearchText) ? "请先选择城市" :
                            !district && !schoolSearchText ? "请先选择区县" : "请选择学校"
                    }
                    options={filteredSchools.map(school => {
                      const hasDuplicateName = filteredSchools.some(s =>
                        s.schoolName === school.schoolName &&
                        (s.province !== school.province ||
                          s.city !== school.city ||
                          s.district !== school.district)
                      );

                      const formatLocation = () => {
                        const schoolProvince = school.province && school.province !== 'null' ? school.province : '';
                        const schoolCity = schoolProvince && !isMunicipality(schoolProvince) ?
                          (school.city && school.city !== 'null' ? school.city : '') : '';
                        const schoolDistrict = school.district && school.district !== 'null' ? school.district : '';

                        if (!schoolProvince && !schoolCity && !schoolDistrict) {
                          return '';
                        }

                        return `（${schoolProvince}${schoolCity}${schoolDistrict}）`;
                      };

                      return {
                        value: hasDuplicateName ?
                          `${school.schoolName}${formatLocation()}` :
                          school.schoolName,
                        label: hasDuplicateName ?
                          `${school.schoolName}${formatLocation()}` :
                          school.schoolName,
                        data: school
                      };
                    })}
                    dropdownStyle={{
                      maxHeight: 200,
                      overflow: 'auto',
                      borderRadius: '12px',
                      padding: '8px',
                    }}
                  />
                  <div style={{ display: 'flex', gap: '12px', flexWrap: windowSize.width <= 576 ? 'wrap' : 'nowrap' }}>
                    <Select
                      placeholder="省"
                      value={province || undefined}
                      onChange={(value) => {
                        setProvince(value);
                        setCity('');
                        setDistrict('');
                        setSchoolName('');
                        setFilteredSchools([]);
                      }}
                      style={{ flex: windowSize.width <= 576 ? '1 1 100%' : 1, borderRadius: '8px' }}
                      dropdownStyle={{ borderRadius: '12px' }}
                    >
                      {pcaData.map(p => (
                        <Select.Option key={p.code} value={p.name}>
                          {p.name}
                        </Select.Option>
                      ))}
                    </Select>

                    {!isMunicipality(province) && (
                      <Select
                        placeholder="市"
                        value={city || undefined}
                        onChange={(value) => {
                          setCity(value);
                          setDistrict('');
                          setSchoolName('');
                          setFilteredSchools([]);
                        }}
                        disabled={!province}
                        style={{ flex: windowSize.width <= 576 ? '1 1 48%' : 1, borderRadius: '8px' }}
                        dropdownStyle={{ borderRadius: '12px' }}
                      >
                        {cities.map(c => (
                          <Select.Option key={c.code} value={c.name}>
                            {c.name}
                          </Select.Option>
                        ))}
                      </Select>
                    )}

                    <Select
                      placeholder="区"
                      onChange={(value) => {
                        setDistrict(value);
                        setSchoolName('');
                        setFilteredSchools([]);
                      }}
                      disabled={!province || (!isMunicipality(province) && !city)}
                      value={district}
                      style={{ flex: windowSize.width <= 576 ? '1 1 48%' : 1, borderRadius: '8px' }}
                      dropdownStyle={{ borderRadius: '12px' }}
                    >
                      {districts.map(d => (
                        <Select.Option key={d.code} value={d.name}>
                          {d.name}
                        </Select.Option>
                      ))}
                    </Select>
                  </div>
                </Form.Item>

                {/* 附加材料 */}
                <div style={modalStyles.uploadSection}>
                  <div style={modalStyles.sectionTitle}>
                    <span style={modalStyles.sectionTitleBefore}></span>
                    附加材料
                  </div>

                  <div style={{
                    display: 'flex',
                    gap: '12px',
                    marginTop: '16px',
                    width: '100%',
                    position: 'relative',
                  }}>
                    <div
                      style={{
                        display: 'flex',
                        gap: '12px',
                        width: '100%',
                        overflowX: 'auto',
                        paddingBottom: '8px',
                        WebkitOverflowScrolling: 'touch',
                        scrollbarWidth: 'auto',
                        whiteSpace: 'nowrap',
                      }}
                      className="file-scroll-container"
                    >
                      {fileList.map(file => {
                        const isImage = file.type?.startsWith('image/');
                        if (isImage && file.thumbUrl) {
                          return (
                            <div key={file.uid} className="custom-upload-item" style={{
                              position: 'relative',
                              width: '104px',
                              height: '104px',
                              borderRadius: '12px',
                              display: 'inline-block',
                              flexShrink: 0, /* 防止图片收缩 */
                              overflow: 'hidden'
                            }}>
                              <img
                                src={file.thumbUrl}
                                alt={file.name}
                                style={{
                                  width: '100%',
                                  height: '100%',
                                  objectFit: 'cover'
                                }}
                              />
                              <div className="file-delete-icon"
                                style={{
                                  position: 'absolute',
                                  top: '0',
                                  right: '0',
                                  background: 'rgba(0,0,0,0.65)',
                                  width: '22px',
                                  height: '22px',
                                  borderRadius: '0 0 0 8px',
                                  display: 'flex',
                                  justifyContent: 'center',
                                  alignItems: 'center',
                                  cursor: 'pointer'
                                }}
                                onClick={() => handleRemoveFile(file)}
                              >
                                <DeleteOutlined style={{ color: '#fff', fontSize: '14px' }} />
                              </div>
                            </div>
                          );
                        } else {
                          return (
                            <div key={file.uid} className="custom-file-card"
                              style={{
                                width: '104px',
                                height: '104px',
                                border: `1px solid ${token.colorBorderSecondary}`,
                                borderRadius: '12px',
                                padding: '8px',
                                display: 'inline-flex',
                                flexDirection: 'column',
                                alignItems: 'center',
                                justifyContent: 'space-between',
                                position: 'relative',
                                flexShrink: 0, /* 防止文件卡片收缩 */
                                background: token.colorFillQuaternary,
                                boxShadow: `0 2px 8px ${token.colorBgElevated}`,
                                transition: 'all 0.3s'
                              }}>
                              <div className="file-delete-icon"
                                style={{
                                  position: 'absolute',
                                  top: '0',
                                  right: '0',
                                  background: 'rgba(0,0,0,0.65)',
                                  width: '22px',
                                  height: '22px',
                                  borderRadius: '0 0 0 12px',
                                  display: 'flex',
                                  justifyContent: 'center',
                                  alignItems: 'center',
                                  cursor: 'pointer',
                                  transition: 'background 0.2s'
                                }}
                                onClick={() => handleRemoveFile(file)}
                              >
                                <DeleteOutlined style={{ color: '#fff', fontSize: '14px' }} />
                              </div>

                              <div style={{ marginTop: '10px' }}>
                                {getFileIcon(file.type || '')}
                              </div>

                              <div style={{
                                textAlign: 'center',
                                width: '100%',
                                marginTop: '4px',
                                overflow: 'hidden'
                              }}>
                                <Typography.Text
                                  ellipsis={{ tooltip: file.name }}
                                  style={{ fontSize: '12px', lineHeight: '1.2' }}
                                >
                                  {file.name}
                                </Typography.Text>
                                <div style={{
                                  fontSize: '11px',
                                  color: token.colorTextSecondary,
                                  marginTop: '2px',
                                  background: token.colorFillSecondary,
                                  padding: '0 4px',
                                  borderRadius: '4px',
                                  display: 'inline-block'
                                }}>
                                  {getFileExtension(file.name)}
                                </div>
                              </div>
                            </div>
                          );
                        }
                      })}

                      {/* 上传按钮 */}
                      <Upload
                        showUploadList={false}
                        beforeUpload={(file) => {
                          try {
                            const uid = Date.now().toString();
                            const newFile = {
                              uid: uid,
                              name: file.name,
                              status: 'done' as const,
                              thumbUrl: URL.createObjectURL(file),
                              type: file.type,
                              size: file.size
                            };
                            setFileList([...fileList, newFile]);
                            setLocalFiles(prev => ({
                              ...prev,
                              [uid]: file
                            }));
                          } catch (error) {
                            nt.error('文件处理失败，请重试');
                          }
                          return false;
                        }}
                      >
                        <button
                          style={{
                            border: 0,
                            background: 'none',
                            cursor: 'pointer',
                            borderRadius: '12px',
                            padding: '16px',
                            transition: 'all 0.3s',
                            width: '104px',
                            height: '104px',
                            display: 'inline-flex',
                            flexShrink: 0, /* 防止按钮收缩 */
                            flexDirection: 'column',
                            justifyContent: 'center',
                            alignItems: 'center',
                            backgroundColor: token.colorBgContainer,
                            boxShadow: `0 2px 8px ${token.colorBorderSecondary}`
                          }}
                          type="button"
                          disabled={uploading}
                        >
                          <PlusOutlined style={{ fontSize: '24px', color: token.colorPrimary }} />
                          <div style={{ marginTop: 8, color: token.colorTextSecondary }}>点击上传</div>
                        </button>
                      </Upload>
                    </div>
                  </div>
                </div>
              </div>

              {/* 右侧列：注意事项和认证信息可包括 */}
              <div className="auth-column-right">
                {/* 注意事项 */}
                <div style={{

                  padding: '16px',
                  borderRadius: '12px',
                  background: token.colorInfoBg,
                  border: `2px solid ${token.colorInfoBorder}`,
                  height: 'fit-content', /* 高度适应内容 */
                  marginBottom: '6px',
                  marginTop: '16px'
                }}>
                  <div style={modalStyles.sectionTitle}>
                    <span style={{
                      ...modalStyles.sectionTitleBefore,
                      backgroundColor: token.colorInfo
                    }}></span>
                    注意事项
                  </div>
                  <ul style={{ listStyle: 'none', padding: 0, margin: 0 }}>
                    {[
                      '同一个身份信息仅支持实名认证3个洛基飞跃账号。',
                      '提交审核过后可在沟通群内联系管理员跟进认证。',
                      '审核结果将于1个工作日以短信和站内信的方式通知您。审核不通过，请根据页面提示，修改认证证明材料并再次提交审核。'
                    ].map((item, index) => (
                      <li key={index} style={modalStyles.listItem}>
                        <span style={{ color: token.colorInfo, marginRight: '8px' }}>•</span>
                        {item}
                      </li>
                    ))}
                  </ul>
                </div>

                {/* 认证信息可包括 */}
                <div style={{
                  marginTop: '16px',
                  padding: '16px',
                  borderRadius: '12px',
                  background: token.colorSuccessBg,
                  border: `2px solid ${token.colorSuccessBorder}`,
                  height: 'fit-content' /* 高度适应内容 */
                }}>
                  <div style={modalStyles.sectionTitle}>
                    <span style={{
                      ...modalStyles.sectionTitleBefore,
                      backgroundColor: token.colorSuccess
                    }}></span>
                    认证信息可包括
                  </div>
                  <ul style={{ listStyle: 'none', padding: 0, margin: 0 }}>
                    {['教师资格证', '教师工作证', '中华人民共和国居民身份证信息', '学校或教育机构授权'].map((item, index) => (
                      <li key={index} style={modalStyles.listItem}>
                        <span style={{ color: token.colorSuccess, marginRight: '8px' }}>•</span>
                        {item}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          </Form>
        </div>

        {/* 固定在底部的提交按钮 */}
        <div className="teacher-auth-modal-footer" style={{ borderTop: '1px solid #f0f0f0' /* 使用浅色边框替代红线 */ }}>
          <Button
            type="primary"
            onClick={() => form.submit()}
            loading={submitting}
            disabled={submitting}
            style={modalStyles.submitButton}
          >
            {submitting ? '提交中...' : '确认提交'}
          </Button>
        </div>
      </Modal>
    </>
  )
}


export default TeacherAuthModal;