# 参赛协议

## 一、参赛资格

1. **年龄要求**：参赛者必须为在校学生，年龄在6-18岁之间
2. **技能要求**：参赛者需具备基本的编程能力和逻辑思维
3. **作品数量**：每位参赛者最多可提交3件作品参赛
4. **团队参赛**：允许团队参赛，但团队成员不得超过3人

## 二、作品要求

### 2.1 原创性要求
- 作品必须为参赛者原创，不得抄袭他人作品
- 如发现抄袭行为，将取消参赛资格
- 参赛者需对作品的原创性负责

### 2.2 内容要求
- 作品内容积极向上，符合社会主义核心价值观
- 不得包含违法、暴力、色情等不良内容
- 作品应具有一定的创新性和实用性

### 2.3 技术要求
- 作品需使用指定的编程平台或工具
- 提交的作品文件格式需符合要求
- 作品提交后不得修改或撤回

## 三、知识产权

### 3.1 参赛者权利
- 参赛者享有作品的著作权和署名权
- 参赛者有权在非商业用途下使用自己的作品

### 3.2 主办方权利
- 主办方有权将优秀作品用于宣传推广
- 主办方有权在活动相关场合展示参赛作品
- 主办方承诺不会将作品用于商业用途

### 3.3 侵权责任
- 参赛者保证作品的原创性，如有侵权行为，后果自负
- 因侵权产生的法律责任由参赛者承担

## 四、评审规则

### 4.1 评审原则
- 评审将本着**公平、公正、公开**的原则进行
- 评审专家由行业权威人士组成
- 评审过程全程录像，确保透明度

### 4.2 评审标准
1. **创新性**（30%）：作品的创意和新颖程度
2. **技术性**（30%）：编程技术的运用和实现难度
3. **实用性**（25%）：作品的实际应用价值
4. **完整性**（15%）：作品的完成度和展示效果

### 4.3 评审结果
- 评审结果为最终结果，不接受申诉
- 获奖名单将在官方网站公布
- 主办方保留最终解释权

## 五、奖项设置

### 5.1 奖项类别
- **一等奖**：3名，奖金5000元 + 证书
- **二等奖**：6名，奖金3000元 + 证书  
- **三等奖**：10名，奖金1000元 + 证书
- **优秀奖**：20名，证书 + 纪念品

### 5.2 特殊奖项
- **最佳创意奖**：1名
- **最佳技术奖**：1名
- **最受欢迎奖**：1名（网络投票产生）

## 六、参赛流程

1. **报名阶段**：在线填写报名表并提交
2. **作品提交**：按要求上传作品文件
3. **初审阶段**：专家进行初步筛选
4. **决赛阶段**：入围作品进行现场展示
5. **颁奖典礼**：公布结果并颁发奖项

## 七、注意事项

⚠️ **重要提醒**：
- 请确保联系方式准确，以便及时接收通知
- 参赛费用全免，如有收费行为请举报
- 比赛期间请保持手机畅通
- 如有疑问，请及时联系组委会

## 八、联系方式

📞 **咨询热线**：400-123-4567  
📧 **邮箱**：<EMAIL>  
🌐 **官网**：www.example.com  
📍 **地址**：广东省佛山市xxx区xxx路xxx号

---

**我已仔细阅读并完全同意上述参赛协议的所有条款，愿意遵守比赛规则，承担相应责任。**
