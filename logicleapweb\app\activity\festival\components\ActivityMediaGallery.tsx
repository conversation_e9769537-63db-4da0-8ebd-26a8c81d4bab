'use client'

import React, { useState } from 'react';
import { <PERSON><PERSON>, But<PERSON>, Card, Divider, Typography, Space, Tag } from 'antd';
import {
  PictureOutlined,
  FileOutlined,
  FileTextOutlined,
  DownloadOutlined,
  FullscreenOutlined,
  CloseOutlined
} from '@ant-design/icons';

const { Title, Text } = Typography;

interface ActivityMediaGalleryProps {
  // 媒体文件数据
  promotionImage?: string;
  backgroundImage?: string;
  galleryImages?: string;
  attachmentFiles?: string;
  // 活动基本信息
  activityTitle?: string;
  className?: string;
}

const ActivityMediaGallery: React.FC<ActivityMediaGalleryProps> = ({
  promotionImage,
  attachmentFiles,
  activityTitle = '活动',
  className = ''
}) => {
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [previewTitle, setPreviewTitle] = useState('');
  
  // 解析附件文件列表 - 新格式：文件名1,URL1,文件名2,URL2
  const attachmentFileList = attachmentFiles ? (() => {
    const parts = attachmentFiles.split(',').filter(part => part.trim());
    const files = [];

    // 每两个元素为一组：文件名和URL
    for (let i = 0; i < parts.length; i += 2) {
      if (i + 1 < parts.length) {
        const fileName = parts[i].trim();
        const fileUrl = parts[i + 1].trim();
        files.push({ name: fileName, url: fileUrl });
      }
    }
    return files;
  })() : [];

  // 获取文件名（兼容旧格式）
  const getFileName = (url: string) => {
    const parts = url.split('/');
    return parts[parts.length - 1] || 'unknown';
  };

  // 获取文件扩展名
  const getFileExtension = (url: string) => {
    const fileName = getFileName(url);
    const parts = fileName.split('.');
    return parts.length > 1 ? parts[parts.length - 1].toLowerCase() : '';
  };

  // 获取文件类型标签颜色
  const getFileTypeColor = (extension: string) => {
    const colorMap: { [key: string]: string } = {
      'pdf': 'red',
      'doc': 'blue',
      'docx': 'blue',
      'xls': 'green',
      'xlsx': 'green',
      'ppt': 'orange',
      'pptx': 'orange',
      'txt': 'default',
      'zip': 'purple',
      'rar': 'purple',
    };
    return colorMap[extension] || 'default';
  };

  // 获取文件类型样式
  const getFileTypeStyle = (extension: string) => {
    const styleMap: { [key: string]: string } = {
      'pdf': 'bg-red-100 text-red-700 border border-red-200',
      'doc': 'bg-blue-100 text-blue-700 border border-blue-200',
      'docx': 'bg-blue-100 text-blue-700 border border-blue-200',
      'xls': 'bg-green-100 text-green-700 border border-green-200',
      'xlsx': 'bg-green-100 text-green-700 border border-green-200',
      'ppt': 'bg-orange-100 text-orange-700 border border-orange-200',
      'pptx': 'bg-orange-100 text-orange-700 border border-orange-200',
      'txt': 'bg-gray-100 text-gray-700 border border-gray-200',
      'jpg': 'bg-purple-100 text-purple-700 border border-purple-200',
      'jpeg': 'bg-purple-100 text-purple-700 border border-purple-200',
      'png': 'bg-purple-100 text-purple-700 border border-purple-200',
      'gif': 'bg-purple-100 text-purple-700 border border-purple-200',
      'zip': 'bg-cyan-100 text-cyan-700 border border-cyan-200',
      'rar': 'bg-cyan-100 text-cyan-700 border border-cyan-200'
    };
    return styleMap[extension.toLowerCase()] || 'bg-gray-100 text-gray-700 border border-gray-200';
  };

  // 处理图片预览
  const handlePreview = (url: string, title: string) => {
    setPreviewImage(url);
    setPreviewTitle(title);
    setPreviewVisible(true);
  };

  // 处理文件下载
  const handleDownload = async (url: string, fileName: string) => {
    try {
      // 使用fetch获取文件内容
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error('下载失败');
      }

      // 获取文件blob
      const blob = await response.blob();

      // 创建下载链接
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = fileName; // 使用原始文件名
      link.target = '_blank';

      // 触发下载
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // 清理URL对象
      window.URL.revokeObjectURL(downloadUrl);
    } catch (error) {
      console.error('下载文件失败:', error);
      // 如果fetch失败，回退到直接链接下载
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;
      link.target = '_blank';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  // 如果没有宣传图片和附件文件，不渲染组件
  if (!promotionImage && !attachmentFileList.length) {
    return null;
  }

  return (
    <div className={`activity-media-gallery ${className}`}>
      <div className="mb-6">
        <Title level={3} className="flex items-center mb-4">
          <FileTextOutlined className="mr-2 text-blue-500" />
          活动详情
        </Title>
        
        {/* 宣传图片长图展示 */}
        {promotionImage && (
          <div className="mb-6">
            <div className="w-full">
              <img
                src={promotionImage}
                alt="活动宣传图"
                className="w-full h-auto cursor-pointer hover:opacity-90 transition-opacity duration-300"
                style={{
                  objectFit: 'fill',
                  display: 'block',
                  minHeight: '200px',
                  borderRadius: '12px'
                }}
                onClick={() => handlePreview(promotionImage, '活动宣传图')}
              />
            </div>
          </div>
        )}

        {/* 分割线 */}
        {promotionImage && attachmentFileList.length > 0 && (
          <Divider className="my-6" />
        )}

        {/* 附件文件 */}
        {attachmentFileList.length > 0 && (
          <div className="mb-6">
            <div className="flex items-center justify-between mb-4">
              <Title level={4} className="mb-0 flex items-center">
                <FileOutlined className="mr-2 text-slate-600" />
                附件文件
              </Title>
              <Tag color="processing" className="px-3 py-1 text-sm font-medium">
                {attachmentFileList.length} 个文件
              </Tag>
            </div>

            <div className="bg-white rounded-xl border border-gray-200 overflow-hidden shadow-sm">
              {attachmentFileList.map((fileItem, index) => {
                const fileName = fileItem.name;
                const fileUrl = fileItem.url;
                const extension = getFileExtension(fileUrl);
                const isLast = index === attachmentFileList.length - 1;

                return (
                  <div key={index}>
                    <div className="flex items-center justify-between p-4 hover:bg-gray-50 transition-colors duration-200 group">
                      <div className="flex items-center flex-1 min-w-0">
                        <div className="flex-shrink-0 w-12 h-12 bg-gradient-to-br from-blue-50 to-indigo-100 rounded-xl flex items-center justify-center mr-4 group-hover:from-blue-100 group-hover:to-indigo-200 transition-all duration-200">
                          <FileOutlined className="text-xl text-blue-600" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-3 mb-1">
                            <Text
                              ellipsis={{ tooltip: fileName }}
                              className="font-semibold text-gray-900 text-base group-hover:text-blue-700 transition-colors"
                            >
                              {fileName}
                            </Text>
                            <div className={`px-2 py-1 rounded-md text-xs font-medium ${getFileTypeStyle(extension)}`}>
                              {extension.toUpperCase()}
                            </div>
                          </div>
                          <Text className="text-sm text-gray-500">
                            文件 {index + 1} · 点击下载
                          </Text>
                        </div>
                      </div>
                      <Button
                        type="primary"
                        size="middle"
                        icon={<DownloadOutlined />}
                        onClick={() => handleDownload(fileUrl, fileName)}
                        className="ml-4 flex-shrink-0 shadow-sm hover:shadow-md transition-shadow"
                      >
                        下载
                      </Button>
                    </div>
                    {!isLast && <div className="border-b border-gray-100 mx-4" />}
                  </div>
                );
              })}
            </div>
          </div>
        )}
      </div>

      {/* 图片预览模态框 */}
      <Modal
        open={previewVisible}
        title={null}
        footer={null}
        onCancel={() => setPreviewVisible(false)}
        width="100vw"
        height="100vh"
        style={{
          top: 0,
          left: 0,
          margin: 0,
          padding: 0,
          maxWidth: 'none'
        }}
        styles={{
          body: {
            padding: 0,
            margin: 0,
            height: '100vh',
            overflow: 'auto',
            backgroundColor: 'black'
          },
          content: {
            padding: 0,
            margin: 0,
            backgroundColor: 'black'
          },
          header: {
            display: 'none'
          }
        }}
        closeIcon={null}
        mask={false}
        className="fullscreen-preview-modal"
        destroyOnClose
      >
        <div
          className="w-full h-full flex items-start justify-center bg-black cursor-pointer"
          onClick={() => setPreviewVisible(false)}
        >
          <img
            src={previewImage}
            alt={previewTitle}
            className="w-full h-auto cursor-pointer"
            style={{ display: 'block' }}
            onClick={() => setPreviewVisible(false)}
          />
        </div>
      </Modal>

      <style jsx>{`
        .activity-media-gallery .ant-card-cover img {
          transition: transform 0.3s ease;
        }
        
        .activity-media-gallery .ant-card:hover .ant-card-cover img {
          transform: scale(1.05);
        }
        
        .activity-media-gallery .ant-image {
          border-radius: 8px;
          overflow: hidden;
        }
      `}</style>
    </div>
  );
};

export default ActivityMediaGallery;
