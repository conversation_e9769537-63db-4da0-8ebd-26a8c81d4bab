'use client'

import React, { useState, useEffect } from 'react'
import { PenTool, Edit, Save, X, FileJson, FolderSymlink, Info, Award } from 'lucide-react'
import EditorJsonRenderer from './EditorJsonRenderer'
import { selectUserState } from '@/lib/store'
import { useSelector } from 'react-redux'
import { documentApi } from '../../../../lib/api/document'
import { Modal, Select } from 'antd'
import { GetNotification } from 'logic-common/dist/components/Notification'

interface ActivityDetailProps {
  detailContent: string;
  detailEditorJson?: any;
  rulesContent: string;
  rulesEditorJson?: any;
  awardsContent: string;
  awardsEditorJson?: any;
  activeTab: 'detail' | 'rules' | 'awards';
  onTabChange?: (tab: 'detail' | 'rules' | 'awards') => void;
  onImport?: () => void;
  detailDocId?: string;
  rulesDocId?: string;
  awardsDocId?: string;
  onDocIdChange?: (type: 'detail' | 'rules' | 'awards', docId: string) => void;
}

const ActivityDetail: React.FC<ActivityDetailProps> = ({
  detailContent,
  detailEditorJson,
  rulesContent,
  rulesEditorJson,
  awardsContent,
  awardsEditorJson,
  activeTab: initialActiveTab,
  onTabChange,
  onImport,
  detailDocId,
  rulesDocId,
  awardsDocId,
  onDocIdChange
}) => {
  const [activeTab, setActiveTab] = useState(initialActiveTab);
  const [animate, setAnimate] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const notification = GetNotification();
  
  // 使用useEffect同步外部传入的activeTab变化
  useEffect(() => {
    setActiveTab(initialActiveTab);
  }, [initialActiveTab]);
  
  // 各标签页的可编辑内容状态
  const [editableDetailContent, setEditableDetailContent] = useState(detailContent);
  const [editableDetailEditorJson, setEditableDetailEditorJson] = useState(detailEditorJson);
  const [editableRulesContent, setEditableRulesContent] = useState(rulesContent);
  const [editableRulesEditorJson, setEditableRulesEditorJson] = useState(rulesEditorJson);
  const [editableAwardsContent, setEditableAwardsContent] = useState(awardsContent);
  const [editableAwardsEditorJson, setEditableAwardsEditorJson] = useState(awardsEditorJson);

  // 添加标签名称状态和可编辑功能
  const [tabNames, setTabNames] = useState({
    detail: '活动详情',
    rules: '参赛规则',
    awards: '奖项设置',
    editingTab: '',
  });
  
  const [editingTabName, setEditingTabName] = useState<'detail' | 'rules' | 'awards' | null>(null);
  
  // 切换标签时添加内容显示动画
  useEffect(() => {
    setAnimate(false);
    setTimeout(() => {
      setAnimate(true);
    }, 50);
  }, [activeTab]);

  // 处理内容编辑变更
  const handleContentChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setEditableDetailContent(e.target.value);
  };
  
  // 处理规则编辑变更
  const handleRulesChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setEditableRulesContent(e.target.value);
  };
  
  // 处理奖项编辑变更
  const handleAwardsChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setEditableAwardsContent(e.target.value);
  };

  // 处理标签名称点击编辑
  const handleTabNameClick = (tab: 'detail' | 'rules' | 'awards') => {
    if (isEditing) {
      setEditingTabName(tab);
      setTabNames({
        ...tabNames,
        editingTab: tabNames[tab]
      });
    }
  };
  
  // 处理标签名称编辑保存
  const handleTabNameSave = () => {
    if (editingTabName) {
      setTabNames({
        ...tabNames,
        [editingTabName]: tabNames.editingTab
      });
      setEditingTabName(null);
    }
  };

  // 修改标签切换处理，添加回调
  const handleTabChange = (tab: 'detail' | 'rules' | 'awards') => {
    setActiveTab(tab);
    if (onTabChange) {
      onTabChange(tab);
    }
  };

  // 更新内部状态当props变化时
  useEffect(() => setEditableDetailContent(detailContent), [detailContent]);
  useEffect(() => setEditableDetailEditorJson(detailEditorJson), [detailEditorJson]);
  useEffect(() => setEditableRulesContent(rulesContent), [rulesContent]);
  useEffect(() => setEditableRulesEditorJson(rulesEditorJson), [rulesEditorJson]);
  useEffect(() => setEditableAwardsContent(awardsContent), [awardsContent]);
  useEffect(() => setEditableAwardsEditorJson(awardsEditorJson), [awardsEditorJson]);

  // 获取当前用户信息
  const userState = useSelector(selectUserState)
  const isAdmin = userState?.roleId === 4

  // 添加文档选择相关状态
  const [documents, setDocuments] = useState<Array<{ id: string, title: string }>>([]);
  const [showDocumentModal, setShowDocumentModal] = useState(false);
  const [selectedDocType, setSelectedDocType] = useState<'detail' | 'rules' | 'awards'>('detail');
  const [selectedDocId, setSelectedDocId] = useState<string>('');
  
  // 加载用户文档列表
  const fetchDocuments = async () => {
    try {
      const response = await documentApi.getAllDocs();
      console.log('获取文档列表响应:', response);
      
      if (response?.status === 200 && response.data.data) {
        // 处理返回的数据格式，确保正确获取id字段
        setDocuments(response.data.data.map((doc: any) => {
          // 这里根据实际返回的字段调整，使用id作为文档ID
          const docId = doc.id !== undefined ? doc.id : doc.docId;
          return {
            id: String(docId), // 确保是字符串类型
            title: doc.title || `文档 ${docId}`
          };
        }));
        
        if (response.data.data.length === 0) {
          notification.info('暂无可用文档');
        }
      } else {
        throw new Error(response?.data?.message || '获取文档列表失败');
      }
    } catch (error) {
      console.error('获取文档列表失败:', error);
      notification.error('获取文档列表失败');
    }
  };
  
  // 打开文档选择弹窗
  const openDocumentSelector = (type: 'detail' | 'rules' | 'awards') => {
    setSelectedDocType(type);
    const currentDocId = type === 'detail' ? detailDocId : type === 'rules' ? rulesDocId : awardsDocId;
    setSelectedDocId(currentDocId || '');
    fetchDocuments();
    setShowDocumentModal(true);
  };
  
  // 确认选择文档
  const confirmDocSelection = () => {
    if (onDocIdChange && selectedDocId) {
      // 确保文档ID是字符串类型
      const docIdString = String(selectedDocId);
      onDocIdChange(selectedDocType, docIdString);
      notification.success(`已更换${selectedDocType === 'detail' ? '详情' : selectedDocType === 'rules' ? '规则' : '奖项'}文档`);
    }
    setShowDocumentModal(false);
  };

  return (
    <div className="activity-detail-container bg-white rounded-2xl overflow-hidden shadow-md border border-gray-100 hover:shadow-lg transition-shadow duration-300 relative">
      {/* 装饰元素 */}
      <div className="absolute -top-10 -right-10 w-32 h-32 bg-indigo-100 rounded-full blur-3xl opacity-30 animate-blob"></div>
      <div className="absolute -bottom-12 -left-10 w-32 h-32 bg-blue-100 rounded-full blur-3xl opacity-30 animate-blob animation-delay-2000"></div>
      
      {/* 编辑和导入按钮统一放在这里 */}
      <div className="absolute top-2 right-2 z-50 flex space-x-2">
        {isEditing ? (
          <div className="flex space-x-2">
            <button 
              onClick={() => setIsEditing(false)} 
              className="p-2 bg-green-500 text-white rounded-full hover:bg-green-600 transition-colors"
              title="保存编辑"
            >
              <Save className="w-4 h-4" />
            </button>
            <button 
              onClick={() => setIsEditing(false)}
              className="p-2 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors"
              title="取消编辑"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        ) : (
          <div className="flex space-x-2">
            {/* 导入按钮 - 只在是管理员且有onImport时显示 */}
            {onImport && isAdmin && (
              <button 
                onClick={onImport}
                className="p-2 bg-blue-500 text-white rounded-full hover:bg-blue-600 transition-colors"
                title="导入内容"
              >
                <FileJson className="w-4 h-4" />
              </button>
            )}
            {/* 非编辑模式的编辑按钮 */}
            {!isEditing && isAdmin && (
              <button 
                onClick={() => setIsEditing(true)}
                className="p-2 bg-blue-500 text-white rounded-full hover:bg-blue-600 transition-colors shadow-md"
                title="进入编辑模式"
              >
                <Edit className="w-4 h-4" />
              </button>
            )}
          </div>
        )}
      </div>
      
      {/* 导航标签 */}
      <div className="flex border-b border-gray-100 relative bg-gray-50/80 backdrop-blur-sm">
        <button 
          className={`px-7 py-4 text-sm font-medium transition-all relative overflow-hidden rounded-tl-2xl ${
            activeTab === 'detail' 
              ? 'text-blue-600 bg-white' 
              : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100/50'
          }`}
          onClick={() => handleTabChange('detail')}
        >
          <div className="flex items-center">
            <PenTool className="w-4 h-4 mr-2" />
            {editingTabName === 'detail' ? (
              <div className="flex items-center gap-1">
                <input
                  type="text"
                  className="w-20 p-1 text-xs border border-blue-300 rounded-lg"
                  value={tabNames.editingTab}
                  onChange={(e) => setTabNames({...tabNames, editingTab: e.target.value})}
                  autoFocus
                  onBlur={handleTabNameSave}
                  onKeyDown={(e) => e.key === 'Enter' && handleTabNameSave()}
                  onClick={(e) => e.stopPropagation()}
                />
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleTabNameSave();
                  }}
                  className="p-1 bg-green-500 text-white rounded-full"
                >
                  <Save className="w-3 h-3" />
                </button>
              </div>
            ) : (
              <span 
                onClick={(e) => {
                  if (isEditing) {
                    e.stopPropagation();
                    handleTabNameClick('detail');
                  }
                }}
                className={isEditing ? "cursor-pointer hover:underline flex items-center gap-1" : ""}
              >
                {tabNames.detail}
                {isEditing && <Edit className="w-3 h-3 text-blue-500 ml-1" />}
              </span>
            )}
          </div>
          {activeTab === 'detail' && (
            <span className="absolute bottom-0 left-0 w-full h-0.5 bg-gradient-to-r from-blue-500 to-indigo-500"></span>
          )}
        </button>
        <button 
          className={`px-7 py-4 text-sm font-medium transition-all relative overflow-hidden ${
            activeTab === 'rules' 
              ? 'text-blue-600 bg-white' 
              : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100/50'
          }`}
          onClick={() => handleTabChange('rules')}
        >
          <div className="flex items-center">
            <Info className="w-4 h-4 mr-2" />
            {editingTabName === 'rules' ? (
              <div className="flex items-center gap-1">
                <input
                  type="text"
                  className="w-20 p-1 text-xs border border-blue-300 rounded-lg"
                  value={tabNames.editingTab}
                  onChange={(e) => setTabNames({...tabNames, editingTab: e.target.value})}
                  autoFocus
                  onBlur={handleTabNameSave}
                  onKeyDown={(e) => e.key === 'Enter' && handleTabNameSave()}
                  onClick={(e) => e.stopPropagation()}
                />
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleTabNameSave();
                  }}
                  className="p-1 bg-green-500 text-white rounded-full"
                >
                  <Save className="w-3 h-3" />
                </button>
              </div>
            ) : (
              <span 
                onClick={(e) => {
                  if (isEditing) {
                    e.stopPropagation();
                    handleTabNameClick('rules');
                  }
                }}
                className={isEditing ? "cursor-pointer hover:underline flex items-center gap-1" : ""}
              >
                {tabNames.rules}
                {isEditing && <Edit className="w-3 h-3 text-blue-500 ml-1" />}
              </span>
            )}
          </div>
          {activeTab === 'rules' && (
            <span className="absolute bottom-0 left-0 w-full h-0.5 bg-gradient-to-r from-blue-500 to-indigo-500"></span>
          )}
        </button>
        <button
          className={`px-7 py-4 text-sm font-medium transition-all relative overflow-hidden rounded-tr-2xl ${
            activeTab === 'awards'
              ? 'text-blue-600 bg-white'
              : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100/50'
          }`}
          onClick={() => handleTabChange('awards')}
        >
          <div className="flex items-center">
            <Award className="w-4 h-4 mr-2" />
            {editingTabName === 'awards' ? (
              <div className="flex items-center gap-1">
                <input
                  type="text"
                  className="w-20 p-1 text-xs border border-blue-300 rounded-lg"
                  value={tabNames.editingTab}
                  onChange={(e) => setTabNames({...tabNames, editingTab: e.target.value})}
                  autoFocus
                  onBlur={handleTabNameSave}
                  onKeyDown={(e) => e.key === 'Enter' && handleTabNameSave()}
                  onClick={(e) => e.stopPropagation()}
                />
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleTabNameSave();
                  }}
                  className="p-1 bg-green-500 text-white rounded-full"
                >
                  <Save className="w-3 h-3" />
                </button>
              </div>
            ) : (
              <span
                onClick={(e) => {
                  if (isEditing) {
                    e.stopPropagation();
                    handleTabNameClick('awards');
                  }
                }}
                className={isEditing ? "cursor-pointer hover:underline flex items-center gap-1" : ""}
              >
                {tabNames.awards}
                {isEditing && <Edit className="w-3 h-3 text-blue-500 ml-1" />}
              </span>
            )}
          </div>
          {activeTab === 'awards' && (
            <span className="absolute bottom-0 left-0 w-full h-0.5 bg-gradient-to-r from-blue-500 to-indigo-500"></span>
          )}
        </button>
      </div>
      
      {/* 活动详情内容 - 根据activeTab和editorJson决定渲染方式 */}
      <div className={`p-7 ${activeTab === 'detail' ? 'block' : 'hidden'} ${animate ? 'animate-fadeIn' : 'opacity-0'}`}>
        {isEditing ? (
          <div className="mb-4 flex justify-between items-center">
            <button 
              onClick={() => openDocumentSelector('detail')}
              className="flex items-center px-3 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
            >
              <FolderSymlink className="w-4 h-4 mr-2" />
              更换详情文档 {detailDocId ? `(当前ID: ${detailDocId})` : ''}
            </button>
            
            {editableDetailEditorJson ? (
              <div>详情编辑器JSON内容仅供展示</div>
            ) : (
              <textarea
                className="w-full h-[300px] p-4 border border-blue-200 rounded-lg"
                value={editableDetailContent}
                onChange={(e) => setEditableDetailContent(e.target.value)}
                readOnly
                placeholder="请使用更换文档功能修改内容"
              />
            )}
          </div>
        ) : (
          editableDetailEditorJson ? (
            <EditorJsonRenderer jsonData={editableDetailEditorJson} />
          ) : (
            <div className="prose prose-gray max-w-none"
              dangerouslySetInnerHTML={{ __html: editableDetailContent }}
            />
          )
        )}
      </div>
      
      {/* 参赛规则 - 根据activeTab和editorJson决定渲染方式 */}
      <div className={`p-7 ${activeTab === 'rules' ? 'block' : 'hidden'} ${animate ? 'animate-fadeIn' : 'opacity-0'}`}>
        {isEditing ? (
          <div className="mb-4">
            <button 
              onClick={() => openDocumentSelector('rules')}
              className="flex items-center px-3 py-2 mb-4 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
            >
              <FolderSymlink className="w-4 h-4 mr-2" />
              更换规则文档 {rulesDocId ? `(当前ID: ${rulesDocId})` : ''}
            </button>
            
            {editableRulesEditorJson ? (
              <div>规则编辑器JSON内容仅供展示</div>
            ) : (
              <textarea
                className="w-full h-[400px] p-4 border border-blue-200 rounded-lg font-mono text-sm"
                value={editableRulesContent}
                onChange={(e) => setEditableRulesContent(e.target.value)}
                readOnly
                placeholder="请使用更换文档功能修改内容"
              />
            )}
          </div>
        ) : (
          editableRulesEditorJson ? (
            <EditorJsonRenderer jsonData={editableRulesEditorJson} />
          ) : (
            <div dangerouslySetInnerHTML={{ __html: editableRulesContent }} />
          )
        )}
      </div>
      
      {/* 奖项设置 - 根据activeTab和editorJson决定渲染方式 */}
      <div className={`p-7 ${activeTab === 'awards' ? 'block' : 'hidden'} ${animate ? 'animate-fadeIn' : 'opacity-0'}`}>
        {isEditing ? (
          <div className="mb-4">
            <button 
              onClick={() => openDocumentSelector('awards')}
              className="flex items-center px-3 py-2 mb-4 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
            >
              <FolderSymlink className="w-4 h-4 mr-2" />
              更换奖项文档 {awardsDocId ? `(当前ID: ${awardsDocId})` : ''}
            </button>
            
            {editableAwardsEditorJson ? (
              <div>奖项编辑器JSON内容仅供展示</div>
            ) : (
              <textarea
                className="w-full h-[400px] p-4 border border-blue-200 rounded-lg font-mono text-sm"
                value={editableAwardsContent}
                onChange={(e) => setEditableAwardsContent(e.target.value)}
                readOnly
                placeholder="请使用更换文档功能修改内容"
              />
            )}
          </div>
        ) : (
          editableAwardsEditorJson ? (
            <EditorJsonRenderer jsonData={editableAwardsEditorJson} />
          ) : (
            <div dangerouslySetInnerHTML={{ __html: editableAwardsContent }} />
          )
        )}
      </div>

      {/* 文档选择弹窗 */}
      <Modal
        title={`选择${selectedDocType === 'detail' ? '详情' : selectedDocType === 'rules' ? '规则' : '奖项'}文档`}
        open={showDocumentModal}
        onOk={confirmDocSelection}
        onCancel={() => setShowDocumentModal(false)}
        width={600}
      >
        <div className="py-4">
          <div className="mb-4 p-3 bg-blue-50 rounded-lg text-sm text-blue-700">
            <p className="font-medium mb-1">文档ID使用说明：</p>
            <p>选择的文档ID将直接保存到活动表中的{selectedDocType === 'detail' ? 'detailDocId' : selectedDocType === 'rules' ? 'rulesDocId' : 'awardsDocId'}字段。</p>
            <p className="mt-1">系统将使用此ID从文档服务获取内容。</p>
          </div>
          
          <Select
            style={{ width: '100%' }}
            placeholder="请选择文档"
            value={selectedDocId}
            onChange={(value) => setSelectedDocId(value)}
            options={documents.map(doc => ({ value: doc.id, label: `${doc.title} (ID: ${doc.id})` }))}
            showSearch
            filterOption={(input, option) =>
              (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
            }
            notFoundContent="未找到文档，请先创建文档"
          />
          <div className="mt-4 text-gray-500 text-sm">
            选择的文档将用于替换当前{selectedDocType === 'detail' ? '活动详情' : selectedDocType === 'rules' ? '参赛规则' : '奖项设置'}。
            当前选择的文档ID: {selectedDocId || '未选择'}
          </div>
        </div>
      </Modal>
    </div>
  )
}

// 添加动画样式
const styles = `
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes blob {
  0% { transform: scale(1); }
  33% { transform: scale(1.1); }
  66% { transform: scale(0.9); }
  100% { transform: scale(1); }
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease-out forwards;
}

.animate-blob {
  animation: blob 7s infinite alternate;
}

.animation-delay-2000 {
  animation-delay: 2s;
}
`;

// 在客户端添加样式
if (typeof document !== 'undefined') {
  const styleElement = document.createElement('style');
  styleElement.innerHTML = styles;
  document.head.appendChild(styleElement);
}

export default ActivityDetail 