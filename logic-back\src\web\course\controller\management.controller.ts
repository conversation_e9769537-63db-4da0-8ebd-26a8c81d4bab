
import { Controller, Get, Post, Put, Delete, Body, Param, Query, Request } from '@nestjs/common';

import { ManagementService } from '../application/services/management/management.service';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery, ApiParam, ApiBody, ApiBearerAuth } from '@nestjs/swagger';
import { CreateCourseSeriesDto, UpdateCourseSeriesDto, CourseSeriesResponseDto } from '../application/dto/management/course-series.dto';
import { CreateCourseDto, CourseResponseDto } from '../application/dto/management/course.dto';
import { CreateTaskTemplateDto, TaskTemplateResponseDto } from '../application/dto/management/task-template.dto';
import { CourseSettingsDto, CourseSettingsResponseDto } from '../application/dto/management/course-settings.dto';
import { HttpResponseResultService } from 'src/web/http_response_result/http_response_result.service';
import { GetMyCourseSeriesQueryDto, GetSeriesCoursesQueryDto } from '../application/dto/management/query.dto';



@ApiTags('课程管理')
@Controller('api/v1/course-management')
@ApiBearerAuth('access-token')
export class ManagementController {
  constructor(private readonly managementService: ManagementService
    , private readonly httpResponseResultService: HttpResponseResultService

  ) { }
  // 1.获取我的系列课程列表++
  @ApiOperation({ summary: '获取我的系列课程列表' })
  @ApiQuery({ name: 'page', description: '页码，默认1', required: false, type: Number })
  @ApiQuery({ name: 'pageSize', description: '每页数量，默认10', required: false, type: Number })
  @ApiQuery({ name: 'status', description: '状态筛选：0=草稿，1=已发布，2=已归档', required: false, type: Number })
  @ApiQuery({ name: 'keyword', description: '搜索关键词', required: false, type: String })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    schema: {
      example: {
        code: 200,
        message: 'success',
        data: {
          list: [
            {
              id: 1,
              title: 'JavaScript高级编程',
              description: '深入学习JavaScript高级特性',
              coverImage: 'https://example.com/js-cover.jpg',
              category: 0,
              categoryLabel: '官方',
              status: 0,
              statusLabel: '草稿',
              projectMembers: '王老师、李助教',
              totalCourses: 3,
              totalStudents: 0,
              contentSummary: {
                videoCourseCount: 2,
                documentCourseCount: 3,
                totalResourcesCount: 8,
                completionRate: 0.6
              },
              createdAt: '2024-01-20T14:30:00Z',
              updatedAt: '2024-01-22T09:15:00Z'
            }
          ],
          pagination: {
            page: 1,
            pageSize: 10,
            total: 5,
            totalPages: 1,
            hasNext: false,
            hasPrev: false
          }
        }
      }
    }
  })
  @Get('my-series')
  async getMyCourseSeries(
    @Request() req,
    @Query() query: GetMyCourseSeriesQueryDto
  ) {
    const userId = req.user.id;
    const { page, pageSize, status, keyword } = query;

    const resultData = await this.managementService.getMyCourseSeries_Formatted(
      userId, page, pageSize, status, keyword
    );

    return this.httpResponseResultService.success(resultData, "success", 200);
  }

  // 2.获取系列下的课程列表++
  @ApiOperation({ summary: '获取系列下的课程列表' })
  @ApiParam({ name: 'seriesId', description: '系列ID', type: Number })
  @ApiQuery({ name: 'page', description: '页码，默认1', required: false, type: Number })
  @ApiQuery({ name: 'pageSize', description: '每页数量，默认20', required: false, type: Number })
  @ApiQuery({ name: 'status', description: '状态筛选：0=草稿，1=已发布，2=已归档', required: false, type: Number })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    schema: {
      example: {
        code: 200,
        message: 'success',
        data: {
          list: [
            {
              id: 10,
              seriesId: 123,
              title: "第四课：Hooks进阶",
              description: "深入学习React Hooks的高级用法",
              coverImage: "https://example.com/course4-cover.jpg",
              orderIndex: 4,
              status: 0,
              statusLabel: "草稿",
              hasVideo: 1,
              hasDocument: 1,
              hasAudio: 0,
              videoDuration: 2400,
              videoDurationLabel: "40分钟",
              videoName: "React Hooks进阶.mp4",
              firstTeachingTitle: "教学目标",
              resourcesCount: 3,
              createdAt: "2024-01-25T16:20:00Z",
              updatedAt: "2024-01-25T16:20:00Z"
            }
          ],
          pagination: {
            page: 1,
            pageSize: 20,
            total: 2,
            totalPages: 1,
            hasNext: false,
            hasPrev: false
          }
        }
      }
    }
  })
  @ApiResponse({ status: 404, description: '系列不存在' })
  @Get('series/:seriesId/courses')
  async getSeriesCourses(
    @Request() req,
    @Param('seriesId') seriesId: number,
    @Query() query: GetSeriesCoursesQueryDto
  ) {
    const userId = req.user.id;
    const { page, pageSize, status } = query;

    const resultData = await this.managementService.getSeriesCourses_Formatted(
      seriesId, status, page, pageSize, userId
    );

    return this.httpResponseResultService.success(resultData, "success", 200);
  }

  // 3.获取系列详情
  @ApiOperation({ summary: '获取系列详情' })
  @ApiParam({ name: 'id', description: '系列ID', type: Number })
  @ApiResponse({ status: 200, description: '成功获取系列详情', type: CourseSeriesResponseDto })
  @ApiResponse({ status: 404, description: '系列不存在' })
  @Get('series/:id')
  async getSeriesDetail(@Param('id') id: number) {
    const result = await this.managementService.findCourseSeriesById(id);
    return this.httpResponseResultService.success(result, "success", 200);
  }

  // 4.创建系列课程
  @ApiOperation({ summary: '创建系列课程' })
  @ApiBody({ type: CreateCourseSeriesDto })
  @ApiResponse({ status: 200, description: '系列课程创建成功', type: CourseSeriesResponseDto })
  @Post('series')
  async createCourseSeries(
    @Request() req,
    @Body() courseSeriesData: CreateCourseSeriesDto
  ) {
    const userId = req.user.id;

    const result = await this.managementService.createCourseSeries(courseSeriesData, userId);
    return this.httpResponseResultService.success(result, "success", 200);
  }

  // 5.更新系列课程
  @ApiOperation({ summary: '更新系列课程' })
  @ApiParam({ name: 'id', description: '系列ID', type: Number })
  @ApiBody({ type: UpdateCourseSeriesDto })
  @ApiResponse({ status: 200, description: '系列课程更新成功', type: CourseSeriesResponseDto })
  @ApiResponse({ status: 404, description: '系列不存在' })
  @Put('series/:id')
  async updateCourseSeries(
    @Request() req,
    @Param('id') id: number,
    @Body() updateData: UpdateCourseSeriesDto
  ) {
    const userId = req.user.id;
    const result = await this.managementService.updateCourseSeries(id, updateData, userId);
    return this.httpResponseResultService.success(result, "success", 200);
  }

  // 6.删除系列课程
  @ApiOperation({ summary: '删除系列课程' })
  @ApiParam({ name: 'id', description: '系列ID', type: Number })
  @ApiResponse({ status: 200, description: '系列课程删除成功' })
  @ApiResponse({ status: 404, description: '系列不存在' })
  @Delete('series/:id')
  async deleteCourseSeries(
    @Request() req,
    @Param('id') id: number
  ) {
    const userId = req.user.id;
    const result = await this.managementService.removeCourseSeries(id, userId);
    return this.httpResponseResultService.success(result, "success", 200);
  }

  // 7.发布课程系列++
  @ApiOperation({ summary: '发布课程系列' })
  @ApiParam({ name: 'seriesId', description: '系列ID', type: Number })
  @ApiResponse({
    status: 200,
    description: '系列课程发布成功',
    schema: {
      example: {
        code: 200,
        message: "系列课程发布成功",
        data: {
          id: 124,
          title: "Node.js后端开发系列",
          status: 1,
          statusLabel: "已发布",
          publishedAt: "2024-01-26T15:30:00Z",
          totalCourses: 3,
          publishedCourses: 3,
          publishStats: {
            videoCourseCount: 3,
            documentCourseCount: 3,
            totalVideoDuration: 10800,
            totalResourcesCount: 12
          }
        }
      }
    }
  })
  @ApiResponse({ status: 400, description: '发布失败：课程系列中至少需要包含一个课程' })
  @ApiResponse({ status: 403, description: '无权限发布此课程系列' })
  @ApiResponse({ status: 404, description: '系列不存在' })
  @Post('series/:seriesId/publish')
  async publishCourseSeries(
    @Request() req,
    @Param('seriesId') seriesId: number
  ) {
    const userId = req.user.id;
    const resultData = await this.managementService.publishCourseSeries_Formatted(seriesId, userId);
    return this.httpResponseResultService.success(resultData, "success", 200);
  }

  // 8.创建课程
  @ApiOperation({ summary: '创建课程' })
  @ApiBody({
    type: CreateCourseDto,
    description: '课程创建信息',
    examples: {
      example1: {
        summary: '创建Node.js基础课程',
        value: {
          seriesId: 2,
          title: "第一课：Node.js基础入门",
          description: "了解Node.js的基本概念、安装配置和核心模块",
          coverImage: "https://example.com/nodejs-basic-cover.jpg",
          hasVideo: 1,
          hasDocument: 1,
          hasAudio: 0,
          videoDuration: 3600,
          contentConfig: {
            hasVideo: 1,
            hasDocument: 1,
            hasAudio: 0,
            video: {
              url: "https://example.com/videos/nodejs-basics.mp4",
              name: "Node.js基础入门讲解.mp4"
            },
            document: {
              url: "https://example.com/documents/nodejs-basics-slides.pdf",
              name: "Node.js基础入门课件.pdf"
            }
          },
          teachingInfo: [
            {
              title: "教学目标",
              content: [
                "理解Node.js的基本概念和特点",
                "掌握Node.js的安装和环境配置"
              ]
            }
          ],
          additionalResources: [
            {
              title: "Node.js官方文档",
              url: "https://nodejs.org/docs/",
              description: "Node.js官方学习资源"
            }
          ],
          orderIndex: 1
        }
      }
    }
  })
  @ApiResponse({ status: 200, description: '课程创建成功', type: CourseResponseDto })
  @ApiResponse({ status: 404, description: '系列不存在' })
  @Post('courses')
  async createCourse(
    @Request() req,
    @Body() courseData: CreateCourseDto
  ) {
    const userId = req.user.id;
    const result = await this.managementService.createCourse(courseData, userId);
    return this.httpResponseResultService.success(result, "success", 200);
  }

  // 9.设置课程配置
  @ApiOperation({ summary: '设置课程配置' })
  @ApiParam({ name: 'courseId', description: '课程ID', type: Number })
  @ApiBody({
    type: CourseSettingsDto,
    description: '课程配置信息',
    examples: {
      example1: {
        summary: '设置课程积分和模板',
        value: {
          templateId: 1,
          requiredPoints: 100,
          autoCreateTasks: 1
        }
      },
      example2: {
        summary: '仅设置所需积分',
        value: {
          requiredPoints: 50
        }
      },
      example3: {
        summary: '关闭自动创建任务',
        value: {
          autoCreateTasks: 0
        }
      }
    }
  })
  @ApiResponse({ status: 200, description: '课程配置设置成功', type: CourseSettingsResponseDto })
  @ApiResponse({ status: 404, description: '课程不存在' })
  @Post('courses/:courseId/settings')
  async setCourseSettings(
    @Request() req,
    @Param('courseId') courseId: number,
    @Body() settingsData: CourseSettingsDto
  ) {
    const userId = req.user.id;
    const result = await this.managementService.setCourseSettings(courseId, settingsData, userId);
    return this.httpResponseResultService.success(result, "success", 200);
  }

  // 10.添加任务模板
  @ApiOperation({ summary: '添加任务模板' })
  @ApiParam({ name: 'courseId', description: '课程ID', type: Number })
  @ApiBody({
    type: CreateTaskTemplateDto,
    description: '任务模板信息',
    examples: {
      example1: {
        summary: '创建Node.js基础练习任务',
        value: {
          taskName: "Node.js基础练习",
          taskDescription: "创建一个简单的Node.js应用，实现文件读写和HTTP服务器功能",
          durationDays: 7,
          attachments: [
            {
              "title": "练习模板",
              "url": "https://example.com/nodejs-template.zip",
              "type": "file"
            },
            {
              "title": "参考文档",
              "url": "https://example.com/nodejs-reference.pdf",
              "type": "document"
            }
          ],
          workIdsStr: "200,202,203",
          selfAssessmentItems: [
            {
              "content": "是否正确使用了fs模块进行文件操作？",
              "sequence": 1
            },
            {
              "content": "HTTP服务器是否能正常启动和响应请求？",
              "sequence": 2
            },
            {
              "content": "代码是否遵循了Node.js的最佳实践？",
              "sequence": 3
            }
          ]
        }
      }
    }
  })
  @ApiResponse({ status: 200, description: '任务模板添加成功', type: TaskTemplateResponseDto })
  @ApiResponse({ status: 404, description: '课程不存在' })
  @Post('courses/:courseId/task-templates')
  async addTaskTemplate(
    @Request() req,
    @Param('courseId') courseId: number,
    @Body() templateData: CreateTaskTemplateDto
  ) {
    const userId = req.user.id;

    // 将路径参数中的courseId传给服务方法
    const result = await this.managementService.addTaskTemplate(courseId, templateData, userId);
    return this.httpResponseResultService.success(result, "success", 200);
  }

  // 11.获取课程详情
  @ApiOperation({ summary: '获取课程详情' })
  @ApiParam({ name: 'id', description: '课程ID', type: Number })
  @ApiResponse({ status: 200, description: '成功获取课程详情', type: CourseResponseDto })
  @ApiResponse({ status: 404, description: '课程不存在' })
  @Get('courses/:id')
  async getCourseDetail(
    @Request() req,
    @Param('id') id: number
  ) {
    const userId = req.user.id;

    const result = await this.managementService.findCourseById(id, userId);
    return this.httpResponseResultService.success(result, "success", 200);
  }

  // 12.更新课程
  @ApiOperation({ summary: '更新课程' })
  @ApiParam({ name: 'id', description: '课程ID', type: Number })
  @ApiBody({ type: CreateCourseDto, description: '课程更新信息' })
  @ApiResponse({ status: 200, description: '课程更新成功', type: CourseResponseDto })
  @ApiResponse({ status: 404, description: '课程不存在' })
  @Put('courses/:id')
  async updateCourse(
    @Request() req,
    @Param('id') id: number,
    @Body() updateData: CreateCourseDto
  ) {
    const userId = req.user.id;
    const result = await this.managementService.updateCourse(id, updateData, userId);
    return this.httpResponseResultService.success(result, "success", 200);
  }

  // 13.删除课程
  @ApiOperation({ summary: '删除课程' })
  @ApiParam({ name: 'id', description: '课程ID', type: Number })
  @ApiResponse({ status: 200, description: '课程删除成功' })
  @ApiResponse({ status: 404, description: '课程不存在' })
  @Delete('courses/:id')
  async deleteCourse(
    @Request() req,
    @Param('id') id: number
  ) {
    const userId = req.user.id;
    const result = await this.managementService.removeCourse(id, userId);
    return this.httpResponseResultService.success(result, "success", 200);
  }

  // 14.调整课程排序
  @ApiOperation({ summary: '调整课程排序' })
  @ApiParam({ name: 'seriesId', description: '系列ID', type: Number })
  @ApiBody({
    description: '课程排序信息',
    schema: {
      type: 'object',
      properties: {
        courseOrders: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              courseId: { type: 'number', description: '课程ID' },
              orderIndex: { type: 'number', description: '新的排序索引' }
            }
          }
        }
      },
      example: {
        courseOrders: [
          { courseId: 1, orderIndex: 1 },
          { courseId: 2, orderIndex: 2 },
          { courseId: 3, orderIndex: 3 }
        ]
      }
    }
  })
  @ApiResponse({ status: 200, description: '课程排序调整成功' })
  @ApiResponse({ status: 404, description: '系列不存在' })
  @Put('series/:seriesId/course-orders')
  async updateCourseOrders(
    @Request() req,
    @Param('seriesId') seriesId: number,
    @Body() body: { courseOrders: Array<{ courseId: number; orderIndex: number }> }
  ) {
    const userId = req.user.id;
    const result = await this.managementService.updateCourseOrders(seriesId, body.courseOrders, userId);
    return this.httpResponseResultService.success(result, "success", 200);
  }
}
