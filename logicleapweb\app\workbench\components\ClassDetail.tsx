import React, { useState, useEffect, useRef } from 'react';
import { ArrowLeft, Search, Plus, Settings, Trash2, Edit, UserPlus, Upload, Download, Users, Link, Gift, Blocks, ChevronDown, Zap } from 'lucide-react';
import { classApi } from '../../../lib/api/class';
import { studentApi } from '../../../lib/api/student';
import { useSelector } from 'react-redux';
import { RootState } from '../../../lib/store';
import { useTemplate } from '../contexts/TemplateContext';
import { AddStudentModal } from './AddStudentModal';
import {
  EditClassModal,
  ImportStudentModal,
  PublishTaskModal,
  ResetPasswordModal
} from '../../teacher-space/components/modals';
import { AssignBlocksModal } from './AssignBlocksModal';
import TransferClassModal from './TransferClassModal';
import { AssignPointsModal } from './AssignPointsModal';
import { BatchUseKeyPackageModal } from './BatchUseKeyPackageModal';
import StudentList from './StudentList';
import { ClassInfo, Student, UserRole, ClassDetailProps } from './types';
import './ClassDetail.css';

// 类型定义已移至 ./types/index.ts

const ClassDetail: React.FC<ClassDetailProps> = ({
  classInfo: initialClassInfo,
  selectedSchool,
  onBack,
  onClassInfoUpdate,
  onClassDeleted
}) => {
  // 创建内部状态来管理班级信息，这样可以在编辑后更新显示
  const [classInfo, setClassInfo] = useState<ClassInfo>(initialClassInfo);
  const [students, setStudents] = useState<Student[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null);

  // 同步外部传入的classInfo变化
  useEffect(() => {
    setClassInfo(initialClassInfo);
  }, [initialClassInfo]);
  const [isAddStudentModalVisible, setIsAddStudentModalVisible] = useState(false);
  const [isSettingsDropdownOpen, setIsSettingsDropdownOpen] = useState(false);
  const [isBatchActionsDropdownOpen, setIsBatchActionsDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const settingsDropdownRef = useRef<HTMLDivElement>(null);
  const batchActionsDropdownRef = useRef<HTMLDivElement>(null);

  // 模态框状态
  const [isEditClassModalVisible, setIsEditClassModalVisible] = useState(false);
  const [isImportStudentModalVisible, setIsImportStudentModalVisible] = useState(false);
  const [isTransferClassModalVisible, setIsTransferClassModalVisible] = useState(false);
  const [isInviteCodeModalVisible, setIsInviteCodeModalVisible] = useState(false);
  const [isAssignBlocksModalVisible, setIsAssignBlocksModalVisible] = useState(false);
  const [inviteCode, setInviteCode] = useState<string>('');

  // 新增功能模态框状态
  const [isPublishTaskModalVisible, setIsPublishTaskModalVisible] = useState(false);
  const [isResetPasswordModalVisible, setIsResetPasswordModalVisible] = useState(false);
  const [isAssignPointsModalVisible, setIsAssignPointsModalVisible] = useState(false);
  const [isBatchUseKeyPackageModalVisible, setIsBatchUseKeyPackageModalVisible] = useState(false);
  // const [isRedeemKeyModalVisible, setIsRedeemKeyModalVisible] = useState(false); // 未使用，已移除

  // 转让管理相关状态
  const [searchedTeacher, setSearchedTeacher] = useState<any>(null);
  const [transferLoading, setTransferLoading] = useState(false);

  // PublishTaskModal 相关状态
  const [fileList, setFileList] = useState<any[]>([]);
  const [uploading, setUploading] = useState(false);
  const [templates, setTemplates] = useState<any[]>([]);
  const [officialTemplates] = useState<any[]>([]); // 保留以避免引用错误
  const [selectedTemplateId, setSelectedTemplateId] = useState<number | null>(null);

  // AssignBlocksModal 相关状态
  const [loadingTemplates, setLoadingTemplates] = useState(false);
  const [studentTemplateUsage, setStudentTemplateUsage] = useState<Record<number, number>>({});
  const [teacherTemplate, setTeacherTemplate] = useState<{ templateId: number } | undefined>();
  const [selectedStudentId, setSelectedStudentId] = useState<number | null>(null);

  // 添加userRoles状态，与teacher-space保持一致
  const [userRoles, setUserRoles] = useState<Array<{ userId: number; roleId: number }>>([]);

  // 多选状态
  const [selectedStudentIds, setSelectedStudentIds] = useState<number[]>([]);
  const [isSelectAll, setIsSelectAll] = useState(false);

  // 使用全局模板状态
  const { currentTemplate, globalTemplateChangeVersion, refreshCurrentTemplate } = useTemplate();

  // 全局当前模板信息（用于没有个人模板的学生）
  const [globalCurrentTemplate, setGlobalCurrentTemplate] = useState<any>(null);

  // 获取教师当前模板（从班级接口获取）
  const fetchTeacherCurrentTemplate = async () => {
    try {
      if (!classInfo?.id || !classInfo?.schoolId) return null;

      const response = await classApi.getTeacherClasses(classInfo.schoolId, userId);
      if (response.data.code === 200 && response.data.data) {
        // 找到当前班级的模板信息
        const currentClass = response.data.data.find((cls: any) => cls.id === classInfo.id);
        if (currentClass && currentClass.templateName) {
          const templateInfo = {
            templateId: currentClass.templateId || 0,
            templateName: currentClass.templateName,
            isOfficial: currentClass.isOfficial || false
          };
          setGlobalCurrentTemplate(templateInfo);
          console.log('获取到教师当前模板:', templateInfo);
          return templateInfo;
        }
      }
    } catch (error) {
      console.error('获取教师当前模板失败:', error);
    }
    return null;
  };

  // 同步教师模板到学生（当教师模板更新时调用）
  const syncTeacherTemplateToStudents = async () => {
    try {
      const newTemplate = await fetchTeacherCurrentTemplate();
      if (newTemplate && students.length > 0) {
        console.log('同步教师模板到学生:', newTemplate);

        // 使用 ref 获取最新的 personalTemplateAssignments
        const currentPersonalAssignments = personalTemplateAssignmentsRef.current;

        // 更新所有没有个人分配模板的学生
        setStudents(prevStudents => prevStudents.map(student => {
          // 如果学生有个人分配的模板，保持不变
          if (currentPersonalAssignments.has(student.userId)) {
            console.log(`保持学生 ${student.nickName} 的个人模板 (syncTeacherTemplateToStudents)`);
            return student;
          }

          // 否则更新为教师当前模板
          console.log(`更新学生 ${student.nickName} 为教师模板 (syncTeacherTemplateToStudents)`);
          return {
            ...student,
            currentTemplate: newTemplate
          };
        }));
      }
    } catch (error) {
      console.error('同步教师模板失败:', error);
    }
  };

  // 存储个人分配的模板信息，避免被fetchStudents覆盖
  const [personalTemplateAssignments, setPersonalTemplateAssignments] = useState<Map<number, { templateId: number; templateName: string; isOfficial: boolean }>>(new Map());

  // 添加强制重新渲染的状态
  const [renderVersion] = useState(0); // setRenderVersion暂时未使用

  // 获取用户信息
  const userId = useSelector((state: RootState) => state.user.userState.userId);
  const roleId = useSelector((state: RootState) => state.user.userState.roleId);

  // 获取班级学生数据
  const fetchStudents = async () => {
    if (!classInfo?.id) return;

    try {
      setLoading(true);
      setError(null);

      // 并行获取班级学生基础数据和教师当前模板
      const [response] = await Promise.all([
        classApi.getClassStudents(classInfo.id),
        fetchTeacherCurrentTemplate()
      ]);
      if (response.data.code === 200) {
        const studentsData = response.data.data || [];

        if (studentsData.length === 0) {
          setStudents([]);
          setSelectedStudent(null);

          // 如果学生数量变为0，也要更新班级信息
          if (classInfo.studentCount !== 0) {
            const updatedClassInfo = {
              ...classInfo,
              studentCount: 0
            };
            setClassInfo(updatedClassInfo);

            // 通知父组件更新班级列表中的学生数
            onClassInfoUpdate?.(updatedClassInfo);

          }
          return;
        }

        // 提取学生ID列表
        const userIds = studentsData.map((s: Student) => s.userId);

        // 批量获取学生详细信息（包括模板和能量信息）
        const studentInfoMap = await (userIds.length > 20
          ? Promise.all(
            Array.from({ length: Math.ceil(userIds.length / 20) },
              (_, i) => userIds.slice(i * 20, (i + 1) * 20)
            ).map(batchId =>
              studentApi.getStudentsBatchInfo(batchId)
                .then(({ data: { data } }) => {
                  return data;
                })
                .catch((error) => {
                  console.error('获取学生批量信息失败:', error);
                  return {};
                })
            )
          ).then(results => Object.assign({}, ...results))
          : studentApi.getStudentsBatchInfo(userIds)
            .then(({ data: { data } }) => {
              return data;
            })
            .catch((error) => {
              console.error('获取学生批量信息失败:', error);
              return {};
            })
        );



        // 合并学生数据
        const completeStudents = studentsData.map((s: Student) => {
          const studentInfo = studentInfoMap[s.userId];
          // 检查是否有个人分配的模板
          const personalTemplate = personalTemplateAssignments.get(s.userId);

          return {
            ...s,
            ...studentInfo,
            id: s.userId, // 添加id属性以兼容teacher-space类型
            nickName: s.nickName || `学生${s.studentNumber || s.userId}`, // 确保nickName总是有值
            totalPoints: studentInfo?.totalPoints || 0,
            availablePoints: studentInfo?.availablePoints || 0,
            avatarUrl: s.avatarUrl || '/default-avatar.png', // 确保avatarUrl总是有值
            // 优先级：个人分配的模板 > 学生API返回的模板 > 教师当前模板 > null
            currentTemplate: (() => {
              const result = personalTemplate ||
                studentInfo?.currentTemplate ||
                globalCurrentTemplate;

              // 调试信息
              if (s.userId === studentsData[0]?.userId) {
                console.log('学生模板分配逻辑:', {
                  studentId: s.userId,
                  studentName: s.nickName,
                  personalTemplate,
                  studentApiTemplate: studentInfo?.currentTemplate,
                  globalCurrentTemplate,
                  finalTemplate: result
                });
              }

              return result;
            })()
          };
        });



        setStudents(completeStudents);

        // 如果学生数量发生变化，更新班级信息并通知父组件
        if (completeStudents.length !== classInfo.studentCount) {
          const updatedClassInfo = {
            ...classInfo,
            studentCount: completeStudents.length
          };
          setClassInfo(updatedClassInfo);

          // 通知父组件更新班级列表中的学生数
          onClassInfoUpdate?.(updatedClassInfo);

        }

        // 设置userRoles，与teacher-space保持一致
        const newUserRoles: UserRole[] = completeStudents.map((student: Student) => ({
          userId: student.userId,
          roleId: 1 // 学生角色ID为1
        }));

        // 添加教师自己的角色
        newUserRoles.push({
          userId: userId,
          roleId: 2 // 教师角色ID为2
        });

        setUserRoles(newUserRoles);

        // 默认选择第一个学生
        if (completeStudents.length > 0) {
          setSelectedStudent(completeStudents[0]);
        }
      } else {
        setError(response.data.message || '获取学生列表失败');
      }
    } catch (err) {
      console.error('获取学生列表失败:', err);
      console.error('错误详情:', {
        message: err instanceof Error ? err.message : '未知错误',
        stack: err instanceof Error ? err.stack : undefined,
        classId: classInfo.id
      });
      setError('获取学生列表失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };



  // 获取权限模板列表
  const fetchTemplates = async () => {
    setLoadingTemplates(true);
    try {
      const { getRoleTemplateList, getOfficialTemplates } = await import('../../../lib/api/role');

      // 同时获取教师自定义模板和官方模板
      const [customResponse, officialResponse] = await Promise.all([
        getRoleTemplateList(userId),
        getOfficialTemplates()
      ]);

      if (customResponse.data.code === 200 && officialResponse.data.code === 200) {
        const customTemplates = customResponse.data.data || [];
        const officialTemplates = officialResponse.data.data || [];

        // 为官方模板添加标记
        const markedOfficialTemplates = officialTemplates.map((template: any) => ({
          ...template,
          isOfficial: true
        }));

        // 合并所有模板
        const allTemplates = [...customTemplates, ...markedOfficialTemplates];
        setTemplates(allTemplates);
      }
    } catch (error) {
      console.error('获取权限模板列表失败:', error);
    } finally {
      setLoadingTemplates(false);
    }
  };

  // 获取模板使用情况
  const fetchTemplateUsage = async () => {
    try {
      const { getUserCurrentTemplate, getStudentTemplates } = await import('../../../lib/api/role');

      // 获取教师使用的模板
      const teacherResponse = await getUserCurrentTemplate(userId);
      if (teacherResponse.data.code === 200) {
        const teacherTemplateData = teacherResponse.data.data;

        setTeacherTemplate(teacherTemplateData);

        // 同时更新当前模板状态，确保与模板管理同步
        // 暂时注释掉以停止循环
        /*
        if (teacherTemplateData) {
          // 使用refreshCurrentTemplate来更新全局模板状态
          await refreshCurrentTemplate();
        }
        */
      }

      // 获取学生使用的模板
      const studentResponse = await getStudentTemplates({
        teacherId: userId,
        page: 1,
        size: 200
      });
      if (studentResponse.data.code === 200) {
        // 统计每个模板被使用的次数
        const usage: { [key: number]: number } = {};
        studentResponse.data.data.list.forEach((item: any) => {
          if (item.templateId) {
            usage[item.templateId] = (usage[item.templateId] || 0) + 1;
          }
        });
        setStudentTemplateUsage(usage);
      }
    } catch (error) {
      console.error('获取模板使用情况失败:', error);
    }
  };

  // 组件挂载时获取学生数据和模板
  useEffect(() => {
    fetchStudents();
    fetchTemplates();
    fetchTemplateUsage();
  }, [classInfo?.id, userId]);

  // 当组件重新挂载或班级变化时，确保获取最新的当前模板
  // 暂时注释掉以停止循环
  /*
  useEffect(() => {
    if (classInfo?.id && userId) {
      refreshCurrentTemplate();
    }
  }, [classInfo?.id, userId]);
  */

  // 移除这个useEffect，避免在currentTemplate变化时覆盖个人分配的模板

  // 使用 useRef 来保存最新的 personalTemplateAssignments
  const personalTemplateAssignmentsRef = useRef(personalTemplateAssignments);
  personalTemplateAssignmentsRef.current = personalTemplateAssignments;

  // 监听全局模板变化，重新获取教师当前模板并更新学生数据
  useEffect(() => {
    if (globalTemplateChangeVersion > 0 && classInfo?.id) {
      console.log('检测到模板变化，重新获取教师当前模板');

      // 重新获取教师当前模板
      fetchTeacherCurrentTemplate().then((newTemplate) => {
        if (newTemplate && students.length > 0) {
          // 不清除个人分配的模板记录，保留学生的个人模板数据
          // setPersonalTemplateAssignments(new Map()); // 注释掉这行

          // 使用 ref 获取最新的 personalTemplateAssignments
          const currentPersonalAssignments = personalTemplateAssignmentsRef.current;

          // 只更新没有个人模板的学生为新的教师当前模板
          console.log('全局模板变化，开始更新学生模板:', {
            newTemplate,
            personalTemplateAssignments: Array.from(currentPersonalAssignments.entries()),
            studentsCount: students.length
          });

          setStudents(prevStudents => prevStudents.map(student => {
            const hasPersonalTemplate = currentPersonalAssignments.has(student.userId);
            console.log(`学生 ${student.nickName} (${student.userId}):`, {
              hasPersonalTemplate,
              currentTemplate: student.currentTemplate,
              willUpdate: !hasPersonalTemplate
            });

            // 如果学生有个人分配的模板，保持不变
            if (hasPersonalTemplate) {
              console.log(`保持学生 ${student.nickName} 的个人模板`);
              return student;
            }
            // 否则更新为新的教师当前模板
            console.log(`更新学生 ${student.nickName} 为教师模板`);
            return {
              ...student,
              currentTemplate: newTemplate
            };
          }));
        }
      });
    }
  }, [globalTemplateChangeVersion, classInfo?.id]);

  // 定期检查教师模板更新
  useEffect(() => {
    if (!classInfo?.id) return;

    // 立即检查一次
    syncTeacherTemplateToStudents();

    // 设置定时器，每30秒检查一次模板更新
    const interval = setInterval(() => {
      syncTeacherTemplateToStudents();
    }, 30000); // 30秒

    return () => clearInterval(interval);
  }, [classInfo?.id, students.length]);

  // 点击外部关闭下拉菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        // setIsMoreActionsDropdownOpen(false); // 已移除
      }
      if (settingsDropdownRef.current && !settingsDropdownRef.current.contains(event.target as Node)) {
        setIsSettingsDropdownOpen(false);
      }
      if (batchActionsDropdownRef.current && !batchActionsDropdownRef.current.contains(event.target as Node)) {
        setIsBatchActionsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // 处理学生点击选择（用于右侧显示详情）
  const handleStudentClick = (student: Student) => {
    setSelectedStudent(student);
  };

  // 处理删除学生
  const handleDeleteStudent = async () => {
    if (!selectedStudent) {
      alert('请先选择要删除的学生');
      return;
    }

    // 显示确认对话框
    const confirmed = window.confirm(
      `确定要将 ${selectedStudent.nickName} 移出班级吗？\n\n此操作不可恢复！`
    );

    if (!confirmed) {
      return;
    }

    try {
      console.log('删除学生:', selectedStudent);

      // 调用删除学生的API，传入学生的userId数组
      const response = await classApi.removeStudentFromClass([selectedStudent.userId]);

      console.log('删除学生 API 响应:', response.data);

      // 检查响应状态
      if (response.data.code === 200) {
        console.log('删除学生成功');
        alert('学生已成功移出班级');

        // 清除选中的学生
        setSelectedStudent(null);

        // 重新获取学生列表
        await fetchStudents();
      } else {
        console.error('删除学生失败:', response.data.message);
        alert(response.data.message || '删除学生失败');
      }
    } catch (error) {
      console.error('删除学生失败:', error);
      alert('删除学生失败，请稍后重试');
    }
  };

  // 编辑班级
  const handleEditClass = async (values: { className: string }) => {
    const { GetNotification } = await import('logic-common/dist/components/Notification');
    const notification = GetNotification();

    try {
      console.log('开始编辑班级:', {
        classId: classInfo.id,
        values: values,
        originalClassName: classInfo.className
      });

      const response = await classApi.updateClass(classInfo.id, {
        className: values.className,
        grade: classInfo.grade || '' // 确保传递grade字段，如果没有则使用空字符串
      });

      console.log('编辑班级API响应:', response);

      if (response.data.code === 200) {
        console.log('编辑班级成功');
        notification.success('编辑班级成功');
        setIsEditClassModalVisible(false);

        // 更新本地班级信息
        const updatedClassInfo = {
          ...classInfo,
          className: values.className
        };
        setClassInfo(updatedClassInfo);

        // 通知父组件更新班级列表中的班级信息
        onClassInfoUpdate?.(updatedClassInfo);
      } else {
        console.error('编辑班级失败 - API返回错误:', {
          code: response.data.code,
          message: response.data.message,
          data: response.data
        });
        notification.error(response.data.message || '编辑班级失败');
        throw new Error(response.data.message || '编辑班级失败'); // 抛出错误让模态框知道失败了
      }
    } catch (error: any) {
      console.error('编辑班级失败 - 请求异常:', {
        error: error,
        message: error.message,
        response: error.response?.data,
        status: error.response?.status
      });

      // 只有在没有显示过错误消息的情况下才显示通用错误
      if (!error.message || error.message === '编辑班级失败') {
        notification.error(error.response?.data?.message || '编辑班级失败，请稍后重试');
      }

      throw error; // 重新抛出错误，让模态框保持打开状态
    }
  };

  // 导入学生
  const handleImportStudents = async (file: File): Promise<boolean> => {
    try {
      console.log('导入学生文件:', file);
      // 这里需要实现文件解析和导入逻辑
      alert('导入学生功能正在开发中');
      setIsImportStudentModalVisible(false);
      return true;
    } catch (error) {
      console.error('导入学生失败:', error);
      alert('导入学生失败，请稍后重试');
      return false;
    }
  };

  // 导出学生
  const handleExportStudents = async () => {
    try {
      const response = await studentApi.exportStudents(classInfo.id);
      console.log('导出学生成功:', response);
      alert('导出学生成功');
    } catch (error) {
      console.error('导出学生失败:', error);
      alert('导出学生失败，请稍后重试');
    }
  };

  // 搜索教师
  const handleSearchTeacher = async (phone: string) => {
    try {
      const response = await classApi.searchTeacherByPhone(phone);
      console.log('搜索教师响应:', response);
      if (response.data.code === 200) {
        setSearchedTeacher(response.data.data);
      } else {
        const { GetNotification } = await import('logic-common/dist/components/Notification');
        const notification = GetNotification();
        notification.error(response.data.message || '搜索教师失败');
      }
    } catch (error: any) {
      console.error('搜索教师失败:', error);
      const { GetNotification } = await import('logic-common/dist/components/Notification');
      const notification = GetNotification();
      notification.error(error.response?.data?.message || '搜索教师失败');
    }
  };

  // 转让班级
  const handleTransferClass = async (values: { transferType: 'search' | 'assistant' | 'remove'; phone?: string }) => {
    const { GetNotification } = await import('logic-common/dist/components/Notification');
    const notification = GetNotification();

    setTransferLoading(true);
    try {
      let newTeacherId: number;

      if (values.transferType === 'search') {
        if (!searchedTeacher) {
          notification.error('请先搜索并选择教师');
          return;
        }
        newTeacherId = searchedTeacher.id;
      } else {
        // 检查是否有协助教师
        if (!classInfo.assistantTeacherId) {
          notification.error('该班级没有协助教师');
          return;
        }
        newTeacherId = classInfo.assistantTeacherId;
      }

      const response = await classApi.transferClass(classInfo.id, newTeacherId, values.transferType);

      if (response.data.code === 200) {
        notification.success('转让班级成功');
        setIsTransferClassModalVisible(false);
        setSearchedTeacher(null);

        // 转让成功后返回班级管理页面
        onBack();
      } else {
        notification.error(response.data.message || '转让班级失败');
      }
    } catch (error: any) {
      console.error('转让班级失败:', error);
      notification.error(error.response?.data?.message || '转让班级失败');
    } finally {
      setTransferLoading(false);
    }
  };

  // 移出协助教师
  const handleRemoveAssistant = async () => {
    const { GetNotification } = await import('logic-common/dist/components/Notification');
    const notification = GetNotification();

    try {
      // 这里需要调用移出协助教师的API
      // 暂时使用转让API，将assistantTeacherId设为0
      const response = await classApi.updateClass(classInfo.id, {
        assistantTeacherId: 0
      });

      if (response.data.code === 200) {
        notification.success('移出协助教师成功');
        setIsTransferClassModalVisible(false);

        // 更新本地班级信息
        const updatedClassInfo = {
          ...classInfo,
          assistantTeacherId: 0
        };
        setClassInfo(updatedClassInfo);
        onClassInfoUpdate?.(updatedClassInfo);
      } else {
        notification.error(response.data.message || '移出协助教师失败');
      }
    } catch (error: any) {
      console.error('移出协助教师失败:', error);
      notification.error(error.response?.data?.message || '移出协助教师失败');
    }
  };

  // 生成邀请码
  const handleGenerateInviteCode = async () => {
    try {
      const response = await classApi.generateInviteCode(classInfo.id);

      if (response.data.code === 200) {
        setInviteCode(response.data.data.inviteCode);
        setIsInviteCodeModalVisible(true);
      } else {
        console.error('生成邀请码失败:', response.data.message);
        alert(response.data.message || '生成邀请码失败');
      }
    } catch (error) {
      console.error('生成邀请码失败:', error);
      alert('生成邀请码失败，请稍后重试');
    }
  };

  // 分配积木（批量或选中学生）
  const handleAssignBlocks = async () => {
    console.log('=== 分配积木开始 ===');
    console.log('selectedStudentIds:', selectedStudentIds);
    console.log('selectedStudentIds.length:', selectedStudentIds.length);

    // 如果有选中的学生，设置为单个学生分配模式
    if (selectedStudentIds.length === 1) {
      console.log('单个学生分配模式，studentId:', selectedStudentIds[0]);
      setSelectedStudentId(selectedStudentIds[0]);
    } else {
      console.log('批量分配模式，学生数量:', selectedStudentIds.length);
      setSelectedStudentId(null);
    }

    await fetchTemplates();
    await fetchTemplateUsage();
    setIsAssignBlocksModalVisible(true);
  };

  // 单独为学生分配积木
  const handleIndividualAssignBlocks = async (studentId: number) => {
    console.log('=== 单独为学生分配积木 ===');
    console.log('studentId:', studentId);

    // 设置为单个学生分配模式
    setSelectedStudentId(studentId);
    setSelectedStudentIds([]); // 清空批量选择

    await fetchTemplates();
    await fetchTemplateUsage();
    setIsAssignBlocksModalVisible(true);
  };

  // 删除班级
  const handleDeleteClass = async () => {
    const { Modal } = await import('antd');
    const { GetNotification } = await import('logic-common/dist/components/Notification');
    const notification = GetNotification();

    // 先检查是否有学生
    if (students.length > 0) {
      Modal.warning({
        title: '无法删除班级',
        centered: true,
        content: (
          <div>
            <p>班级中还有 {students.length} 名学生，请先移除所有学生后再删除班级。</p>
            <div className="mt-4 text-gray-500 text-sm">
              <p>删除步骤：</p>
              <ol className="list-decimal ml-4 mt-2">
                <li>选择要移除的学生</li>
                <li>使用批量操作移除所有学生</li>
                <li>再次尝试删除班级</li>
              </ol>
            </div>
          </div>
        ),
        okText: '知道了'
      });
      return;
    }

    // 如果没有学生，显示删除确认对话框
    Modal.confirm({
      title: '确认删除班级',
      content: (
        <div>
          <p>您确定要删除 <strong>{classInfo.className}</strong> 吗？</p>
          <div className="mt-3 p-3 bg-red-50 rounded-lg border border-red-200">
            <div className="flex items-center gap-2 text-red-600">
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              <span className="font-medium">此操作不可恢复！</span>
            </div>
            <p className="mt-1 text-sm text-red-500">
              删除班级将永久移除班级信息，包括班级设置、模板配置等数据。
            </p>
          </div>
        </div>
      ),
      okText: '确定删除',
      cancelText: '取消',
      okButtonProps: { danger: true },
      centered: true,
      onOk: async () => {
        try {
          const response = await classApi.deleteClass(classInfo.id);

          if (response.data.code === 200) {
            notification.success('删除班级成功');
            // 通知父组件班级已被删除
            onClassDeleted?.(classInfo.id);
            // 返回到班级管理页面
            onBack();
          } else {
            notification.error(response.data.message || '删除班级失败');
          }
        } catch (error: any) {
          console.error('删除班级失败:', error);
          if (error.response?.data?.message) {
            notification.error(error.response.data.message);
          } else {
            notification.error('删除班级失败，请稍后重试');
          }
        }
      }
    });
  };

  // 下拉菜单项处理函数
  const handleMenuItemClick = (action: string) => {
    // setIsMoreActionsDropdownOpen(false); // 已移除

    switch (action) {
      case 'edit':
        setIsEditClassModalVisible(true);
        break;
      case 'addStudent':
        setIsAddStudentModalVisible(true);
        break;
      case 'importStudent':
        setIsImportStudentModalVisible(true);
        break;
      case 'exportStudent':
        handleExportStudents();
        break;
      case 'transfer':
        setIsTransferClassModalVisible(true);
        break;
      case 'invite':
        handleGenerateInviteCode();
        break;
      case 'batchRedeem':
        console.log('批量兑换密令');
        setIsBatchUseKeyPackageModalVisible(true);
        break;
      case 'assignBlocks':
        handleAssignBlocks();
        break;
      case 'deleteClass':
        handleDeleteClass();
        break;
      default:
        break;
    }
  };

  // 设置下拉菜单项处理函数
  const handleSettingsMenuItemClick = (action: string) => {
    setIsSettingsDropdownOpen(false);

    switch (action) {
      case 'edit':
        setIsEditClassModalVisible(true);
        break;
      case 'addStudent':
        setIsAddStudentModalVisible(true);
        break;
      case 'importStudent':
        setIsImportStudentModalVisible(true);
        break;
      case 'exportStudent':
        handleExportStudents();
        break;
      case 'transfer':
        setIsTransferClassModalVisible(true);
        break;
      case 'invite':
        handleGenerateInviteCode();
        break;
      case 'batchRedeem':
        console.log('批量兑换密令');
        setIsBatchUseKeyPackageModalVisible(true);
        break;
      case 'assignBlocks':
        handleAssignBlocks();
        break;
      case 'deleteClass':
        handleDeleteClass();
        break;
      default:
        break;
    }
  };

  // 确保选中状态同步
  useEffect(() => {
    const shouldBeSelectAll = selectedStudentIds.length > 0 && selectedStudentIds.length === students.length;
    if (isSelectAll !== shouldBeSelectAll) {
      console.log('修复选中状态同步:', {
        isSelectAll,
        shouldBeSelectAll,
        selectedStudentIds: selectedStudentIds.length,
        totalStudents: students.length
      });
      setIsSelectAll(shouldBeSelectAll);
    }
  }, [selectedStudentIds, students.length, isSelectAll]);

  // 全选/取消全选处理函数
  const handleSelectAll = () => {
    if (isSelectAll) {
      // 取消全选
      setSelectedStudentIds([]);
      setIsSelectAll(false);
    } else {
      // 全选
      const allStudentIds = students.map(student => student.userId);
      setSelectedStudentIds(allStudentIds);
      setIsSelectAll(true);
    }
  };

  // 单个学生选择处理函数
  const handleStudentSelect = (studentId: number) => {
    if (selectedStudentIds.includes(studentId)) {
      // 取消选择
      const newSelectedIds = selectedStudentIds.filter(id => id !== studentId);
      setSelectedStudentIds(newSelectedIds);
      setIsSelectAll(false);
    } else {
      // 选择
      const newSelectedIds = [...selectedStudentIds, studentId];
      setSelectedStudentIds(newSelectedIds);
      // 检查是否全选
      if (newSelectedIds.length === students.length) {
        setIsSelectAll(true);
      }
    }
  };

  // 批量操作处理函数
  const handleBatchAction = (action: string) => {
    setIsBatchActionsDropdownOpen(false);

    if (selectedStudentIds.length === 0) {
      import('logic-common/dist/components/Notification').then(({ GetNotification }) => {
        GetNotification().warning('请先选择要操作的学生');
      });
      return;
    }

    switch (action) {
      case 'batchDelete':
        handleBatchRemoveStudents(selectedStudentIds);
        break;
      case 'batchAssignBlocks':
        handleAssignBlocks();
        break;
      case 'batchAssignPoints':
        // 清除单个学生选择，确保进入批量模式
        setSelectedStudent(null);
        setIsAssignPointsModalVisible(true);
        break;
      case 'batchUseKeyPackage':
        handleBatchUseKeyPackage();
        break;
      case 'batchExport':
        console.log('批量导出学生:', selectedStudentIds);
        import('logic-common/dist/components/Notification').then(({ GetNotification }) => {
          GetNotification().info('批量导出学生功能正在开发中');
        });
        break;
      default:
        break;
    }
  };

  // 批量移出班级的改进版本
  const handleBatchRemoveStudents = async (studentIds: number[]) => {
    try {
      // 获取选中的学生信息
      const selectedStudentsInfo = students.filter(s => studentIds.includes(s.userId));

      // 计算总可用积分
      const totalAvailablePoints = selectedStudentsInfo.reduce(
        (sum, student) => sum + (student.availablePoints || 0),
        0
      );

      const { Modal } = await import('antd');
      const { InfoCircleOutlined } = await import('@ant-design/icons');

      Modal.confirm({
        title: '确认批量移出班级',
        content: (
          <div>
            <p>确定要将选中的 {studentIds.length} 名学生移出班级吗？</p>
            {totalAvailablePoints > 0 && (
              <div className="mt-2 p-3 bg-yellow-50 rounded-lg">
                <div className="flex items-center gap-2 text-yellow-600">
                  <InfoCircleOutlined />
                  <span>选中的学生共有 {totalAvailablePoints} 点可用能量</span>
                </div>
                <p className="mt-1 text-sm text-yellow-500">
                  移出班级后，可用能量将返还到各自的套餐积分中
                </p>
              </div>
            )}
          </div>
        ),
        okText: '确定移出',
        cancelText: '取消',
        centered: true,
        okButtonProps: { danger: true },
        onOk: async () => {
          try {
            const { GetNotification } = await import('logic-common/dist/components/Notification');
            const notification = GetNotification();
            const hideLoading = notification.loading('正在移出学生...');

            const response = await classApi.removeStudentFromClass(studentIds);

            if (hideLoading) {
              hideLoading.close();
            }

            if (response.data.code === 200) {
              notification.success(`成功移出 ${studentIds.length} 名学生`);

              // 清除选择状态
              setSelectedStudentIds([]);
              setIsSelectAll(false);
              setSelectedStudent(null);

              // 重新获取学生列表
              await fetchStudents();
            } else {
              notification.error(response.data.message || '批量移出学生失败');
            }
          } catch (error: any) {
            const { GetNotification } = await import('logic-common/dist/components/Notification');
            const notification = GetNotification();
            notification.error(error.response?.data?.message || '批量移出学生失败');
            console.error('批量移出学生失败:', error);
          }
        }
      });
    } catch (error) {
      console.error('批量移出学生失败:', error);
    }
  };

  // 处理添加学生
  const handleAddStudent = async (values: { studentNumber: string; nickName: string }) => {
    try {
      console.log('添加学生:', values);

      // 添加默认密码
      const studentData = {
        ...values,
        password: '123456'
      };

      // 调用添加学生的API
      const response = await classApi.addStudentToClass(classInfo.id, studentData);

      console.log('添加学生 API 响应:', response.data);

      // 检查响应状态
      if (response.data.code === 200) {
        console.log('添加学生成功');
        setIsAddStudentModalVisible(false);
        // 重新获取学生列表
        await fetchStudents();
      } else {
        console.error('添加学生失败:', response.data.message);
        alert(response.data.message || '添加学生失败');
      }
    } catch (error) {
      console.error('添加学生失败:', error);
      alert('添加学生失败，请稍后重试');
    }
  };

  // 处理文件上传
  const handleUpload = async (file: File) => {
    setUploading(true);
    try {
      // 这里可以添加文件上传逻辑
      console.log('上传文件:', file);
      return { success: true };
    } catch (error) {
      console.error('文件上传失败:', error);
      return { success: false };
    } finally {
      setUploading(false);
    }
  };

  // 处理文件删除
  const handleRemoveFile = async (file: any) => {
    try {
      setFileList(prev => prev.filter(f => f.uid !== file.uid));
      return true;
    } catch (error) {
      console.error('删除文件失败:', error);
      return false;
    }
  };

  // 处理发布任务
  const handlePublishTask = async (values: any) => {
    try {
      console.log('发布任务:', values);

      // 导入taskApi
      const { default: taskApi } = await import('../../../lib/api/task');
      const { GetNotification } = await import('logic-common/dist/components/Notification');
      const notification = GetNotification();

      // 构建任务参数
      const params = {
        taskName: values.taskName,
        taskDescription: values.taskDescription || '',
        taskType: 1, // 默认任务类型
        startDate: values.startDate ? new Date(values.startDate) : undefined,
        endDate: values.endDate ? new Date(values.endDate) : new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 默认7天后
        taskContent: values.taskContent || '',
        attachments: fileList.map(file => file.url || file.response?.url).filter(Boolean),
        teacherId: userId,
        classId: classInfo?.id,
        studentIds: selectedStudentIds.length > 0 ? selectedStudentIds : students.map(s => s.userId),
        selfAssessmentItems: values.selfAssessmentItems || [],
        priority: 1, // 默认优先级
        isPublic: 0, // 默认不公开
        allowLateSubmission: values.allowLateSubmission || false
      };

      console.log('发布任务参数:', params);

      // 调用发布任务API
      const response = await taskApi.publishTask(params);

      if (response.data.code === 200) {
        // 显示详细的成功提示
        const { Modal } = await import('antd');
        const studentCount = selectedStudentIds.length > 0 ? selectedStudentIds.length : students.length;
        const className = classInfo?.className || '当前班级';

        Modal.success({
          title: '任务发布成功',
          content: (
            <div>
              <p>任务 <strong>{params.taskName}</strong> 已成功发布到 <strong>{className}</strong>。</p>
              <p>共有 <strong>{studentCount}</strong> 名学生将收到此任务。</p>
              <p>学生可以在班级空间查看和提交任务。</p>
            </div>
          ),
          okText: '确定',
          onOk: () => {
            // 可以在这里添加导航到任务管理页面的逻辑
          }
        });

        setIsPublishTaskModalVisible(false);

        // 清理表单数据
        setFileList([]);
        setSelectedStudentIds([]);
        setSelectedTemplateId(null);

        // 刷新学生列表（如果需要显示任务相关信息）
        await fetchStudents();
      } else {
        notification.error(response.data.message || '任务发布失败');
      }
    } catch (error: any) {
      console.error('发布任务失败:', error);
      const { GetNotification } = await import('logic-common/dist/components/Notification');
      const notification = GetNotification();
      notification.error(error.response?.data?.message || '发布任务失败，请稍后重试');
    }
  };

  // 处理重置密码
  const handleResetPassword = async () => {
    try {
      if (!selectedStudent) {
        alert('请先选择要重置密码的学生');
        return;
      }

      console.log('重置密码:', selectedStudent);
      // 这里可以添加重置密码的API调用
      setIsResetPasswordModalVisible(false);
      alert('密码重置成功，新密码为：123456');
    } catch (error) {
      console.error('重置密码失败:', error);
      alert('重置密码失败，请稍后重试');
    }
  };

  // 处理选择模板
  const handleSelectTemplate = async (templateId: number) => {
    try {
      const { addUserJoinRole, batchAddUserJoinRole } = await import('../../../lib/api/role');
      const { GetNotification } = await import('logic-common/dist/components/Notification');
      const notification = GetNotification();

      // 确定要分配的学生
      const targetStudents = selectedStudentId !== null ? [selectedStudentId] : selectedStudentIds;

      console.log('=== 模板分配详情 ===');
      console.log('selectedStudentId:', selectedStudentId);
      console.log('selectedStudentIds:', selectedStudentIds);
      console.log('targetStudents:', targetStudents);
      console.log('templateId:', templateId);

      if (targetStudents.length === 0) {
        console.log('❌ 没有选中任何学生');
        notification.warning('请先选择学生');
        return;
      }

      const hideLoading = notification.loading('正在分配积木...');
      const userRolesMap = userRoles || [];

      try {
        // 准备用户数据 - 与teacher-space保持一致的逻辑
        const usersData = targetStudents
          .map(userId => {
            const userInfo = userRolesMap.find(u => u.userId === userId);
            if (!userInfo?.roleId) return null;
            return {
              userId: userId,
              roleId: userInfo.roleId,
              templateId: templateId,
              originalTemplateId: templateId
            } as const;
          })
          .filter((item): item is { userId: number; roleId: number; templateId: number; originalTemplateId: number } => item !== null);

        console.log('准备分配模板:', {
          templateId,
          targetStudents,
          userRolesMap,
          usersData
        });

        if (usersData.length === 0) {
          notification.error('无有效用户可分配');
          return;
        }

        // 分批并发处理
        const results = await (usersData.length > 20
          ? Promise.all(
            Array.from(
              { length: Math.ceil(usersData.length / 20) },
              (_, i) => usersData.slice(i * 20, (i + 1) * 20)
            ).map(batchUsers =>
              batchAddUserJoinRole({ users: batchUsers })
                .then(({ data }) => data)
                .catch(() => ({ code: 500, data: { successCount: 0, failCount: batchUsers.length } }))
            )
          ).then(results => ({
            code: results.some(r => r.code !== 200) ? 500 : 200,
            data: {
              successCount: results.reduce((sum, r) => sum + (r.data?.successCount || 0), 0),
              failCount: results.reduce((sum, r) => sum + (r.data?.failCount || 0), 0)
            }
          }))
          : batchAddUserJoinRole({ users: usersData })
            .then(({ data }) => data)
            .catch(() => ({ code: 500, data: { successCount: 0, failCount: usersData.length } }))
        );

        if (hideLoading) {
          hideLoading.close();
        }

        // 显示结果
        if (results.code === 200) {
          const { successCount = 0, failCount = 0 } = results.data || {};

          if (successCount > 0 && failCount === 0) {
            notification.success(`成功为 ${successCount} 名学生分配积木`);
          } else if (successCount > 0 && failCount > 0) {
            notification.warning(`成功为 ${successCount} 名学生分配积木，${failCount} 名学生分配失败`);
          } else {
            notification.error('积木分配失败');
          }
        } else {
          notification.error('积木分配失败');
        }

        // 立即更新已分配学生的模板信息，无需等待API刷新
        const selectedTemplate = templates.find(t => t.id === templateId);
        if (selectedTemplate) {
          const templateData = {
            templateId: templateId,
            templateName: selectedTemplate.templateName || selectedTemplate.name,
            isOfficial: selectedTemplate.isOfficial || false
          };

          // 更新personalTemplateAssignments Map，确保数据持久化
          setPersonalTemplateAssignments(prev => {
            const newMap = new Map(prev);
            targetStudents.forEach(studentId => {
              newMap.set(studentId, templateData);
            });
            return newMap;
          });

          // 更新学生状态
          const updatedStudents = students.map(student => {
            if (targetStudents.includes(student.userId)) {
              // 被选中的学生：设置为新分配的模板
              return {
                ...student,
                currentTemplate: templateData
              };
            }
            // 未选中的学生：保持原有状态
            return student;
          });
          setStudents(updatedStudents);

          console.log('模板分配成功，已更新personalTemplateAssignments:', {
            targetStudents,
            templateData,
            personalTemplateAssignmentsBefore: Array.from(personalTemplateAssignments.entries()),
            personalTemplateAssignmentsAfter: 'will be updated'
          });

          // 延迟打印更新后的状态
          setTimeout(() => {
            console.log('personalTemplateAssignments更新后:', Array.from(personalTemplateAssignments.entries()));
          }, 100);
        }

        // 关闭弹窗并清理状态
        setIsAssignBlocksModalVisible(false);
        setSelectedStudentId(null);
        setSelectedStudentIds([]); // 清空选中的学生
        setIsSelectAll(false); // 取消全选状态

        // 刷新相关数据
        await fetchTemplateUsage(); // 刷新模板使用情况
        // 当前模板信息由全局状态管理，无需手动刷新

      } catch (error) {
        if (hideLoading) {
          hideLoading.close();
        }
        throw error;
      }

    } catch (error) {
      console.error('分配模板失败:', error);
      const { GetNotification } = await import('logic-common/dist/components/Notification');
      const notification = GetNotification();
      notification.error('分配模板失败，请稍后重试');
    }
  };

  // 处理模板使用情况点击
  const handleTemplateUsageClick = (e: React.MouseEvent, template: any) => {
    e.stopPropagation();
    console.log('查看模板使用情况:', template);
  };

  // 处理单个学生分配能量
  const handleAssignPoints = async (values: { availablePoints: number; studentExpiries?: { [id: number]: string | undefined }; remark?: string }) => {
    if (!selectedStudent) {
      const { GetNotification } = await import('logic-common/dist/components/Notification');
      const notification = GetNotification();
      notification.error('请先选择学生');
      return;
    }



    // 从 studentExpiries 中提取单个学生的过期时间
    const expireTime = values.studentExpiries?.[selectedStudent.userId];

    try {
      const { GetNotification } = await import('logic-common/dist/components/Notification');
      const notification = GetNotification();
      const hideLoading = notification.loading('正在分配能量...');

      const { pointsApi } = await import('../../../lib/api/points');
      await pointsApi.assignPermission({
        studentUserId: selectedStudent.userId,
        availablePoints: values.availablePoints,
        expireTime: expireTime, // 使用提取出的过期时间
        remark: values.remark
      });

      if (hideLoading) {
        hideLoading.close();
      }

      notification.success('分配能量成功');
      setIsAssignPointsModalVisible(false);
      // 刷新学生列表
      await refreshStudentList();
    } catch (error: any) {
      const { GetNotification } = await import('logic-common/dist/components/Notification');
      const notification = GetNotification();
      // 增加更具体的错误提示
      notification.error(error.response?.data?.message || '分配能量失败');
    }
  };

  // 刷新学生列表
  const refreshStudentList = async () => {
    await fetchStudents();
  };

  // 更新教师的当前模板（通过UserJoinRole表）
  const updateClassCurrentTemplate = async (templateId: number, templateName: string, isOfficial: boolean) => {
    try {
      console.log('更新教师当前模板:', { userId, roleId, templateId, templateName, isOfficial });

      // 使用addUserJoinRole API来更新教师的模板
      const { addUserJoinRole } = await import('../../../lib/api/role');
      const response = await addUserJoinRole({
        userId: userId,
        roleId: roleId || 2, // 默认为教师角色
        templateId: templateId
      });

      if (response.data.code === 200) {
        // 更新本地的全局当前模板状态
        const newTemplate = {
          templateId: templateId,
          templateName: templateName,
          isOfficial: isOfficial
        };
        setGlobalCurrentTemplate(newTemplate);
        console.log('教师当前模板更新成功:', newTemplate);
      } else {
        console.error('更新教师当前模板失败:', response.data);
      }
    } catch (error) {
      console.error('更新教师当前模板失败:', error);
    }
  };

  // 更新学生的当前模板信息
  const handleUpdateStudentTemplate = (studentIds: number[], templateInfo: { templateId: number; templateName: string; isOfficial?: boolean }) => {
    const templateData = {
      templateId: templateInfo.templateId,
      templateName: templateInfo.templateName,
      isOfficial: templateInfo.isOfficial || false
    };

    // 保存个人分配的模板信息
    setPersonalTemplateAssignments(prev => {
      const newMap = new Map(prev);
      studentIds.forEach(studentId => {
        newMap.set(studentId, templateData);
      });
      return newMap;
    });

    // 更新学生状态
    setStudents(prevStudents =>
      prevStudents.map(student => {
        if (studentIds.includes(student.userId)) {
          return {
            ...student,
            currentTemplate: templateData
          };
        }
        return student;
      })
    );

    // 不再自动更新教师模板，只分配给学生
    // updateClassCurrentTemplate(templateData.templateId, templateData.templateName, templateData.isOfficial);
  };

  // 处理批量/单个兑换密钥
  const handleBatchUseKeyPackage = (studentId?: number) => {
    if (studentId) {
      // 单个学生触发
      setSelectedStudentIds([studentId]);
      setIsBatchUseKeyPackageModalVisible(true);
    } else {
      // 批量操作触发
      if (selectedStudentIds.length === 0) {
        import('logic-common/dist/components/Notification').then(({ GetNotification }) => {
          GetNotification().warning('请先选择学生');
        });
        return;
      }
      setIsBatchUseKeyPackageModalVisible(true);
    }
  };

  // 处理兑换密令成功
  const handleBatchUseKeyPackageSuccess = () => {
    refreshStudentList();
    setIsBatchUseKeyPackageModalVisible(false);
  };

  // 处理从兑换密令跳转到分配能量
  const handleGoToAssignPointsFromRedeem = (studentIds: number[]) => {
    setIsBatchUseKeyPackageModalVisible(false);
    // 设置选中的学生
    setSelectedStudentIds(studentIds);
    // 打开分配能量弹窗
    setIsAssignPointsModalVisible(true);
  };

  // 批量分配能量处理函数
  const handleBatchAssignPoints = async (values: { availablePoints: number; studentExpiries?: { [id: number]: string | undefined }; remark?: string }) => {
    if (selectedStudentIds.length === 0) {
      const { GetNotification } = await import('logic-common/dist/components/Notification');
      const notification = GetNotification();
      notification.error('请先选择学生');
      return;
    }



    if (!values.studentExpiries) {
      const { GetNotification } = await import('logic-common/dist/components/Notification');
      const notification = GetNotification();
      notification.error('未能获取学生过期时间信息');
      return;
    }

    try {
      const { GetNotification } = await import('logic-common/dist/components/Notification');
      const notification = GetNotification();
      const hideLoading = notification.loading('正在批量分配能量...');

      // 调用新的批量分配 API
      const { pointsApi } = await import('../../../lib/api/points');
      const response = await pointsApi.batchAssignPermission({
        availablePoints: values.availablePoints,
        studentExpiries: values.studentExpiries,
        remark: values.remark
      });
      console.log("批量分配积分res", response);

      if (hideLoading) {
        hideLoading.close();
      }

      if (response.data.code === 200) {
        // 后端现在返回处理结果数组，可以根据需要处理
        const results = response.data.data;
        // 可以根据 results 中的信息给出更详细的成功/失败提示，
        // 但为了简单起见，我们仍然使用之前的逻辑
        notification.success(`成功为 ${results.success} 名学生分配能量`);

        setIsAssignPointsModalVisible(false);
        // 刷新学生列表
        await refreshStudentList(); // 确保使用 await
        setSelectedStudentIds([]); // 清空选择
        setIsSelectAll(false);
      } else {
        // 处理 API 返回的错误信息
        notification.error(response.data.message || '批量分配能量失败');
      }
    } catch (error: any) {
      console.error('批量分配能量失败:', error);
      const { GetNotification } = await import('logic-common/dist/components/Notification');
      const notification = GetNotification();
      // 处理请求级别的错误
      notification.error(error.response?.data?.message || '批量分配能量失败，请检查网络连接或稍后重试');
    }
  };

  if (!selectedSchool) {
    return (
      <div className="class-detail-container">
        <div className="class-detail-content">
          <div className="error-message">
            <p>学校信息不存在</p>
            <button onClick={onBack} className="back-button">
              返回班级管理
            </button>
          </div>
        </div>
      </div>
    );
  }

  // 生成随机头像颜色
  const getAvatarColor = (index: number) => {
    const colors = [
      'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
      'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
      'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
      'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
      'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
    ];
    // 确保index是有效的正数
    const safeIndex = Math.max(0, index);
    return colors[safeIndex % colors.length];
  };

  return (
    <div className="class-detail-container">
      {/* 页面头部 */}
      <div className="class-detail-header">
        <button
          className="back-button"
          onClick={onBack}
        >
          <ArrowLeft size={20} />
          返回班级管理
        </button>
      </div>

      <div className="class-detail-content">
        {/* 左侧：班级信息和学生列表 */}
        <div className="left-section">
          {/* 左侧头部：设置按钮和班级信息 */}
          <div className="left-section-header">
            <div className="settings-container" ref={settingsDropdownRef}>
              <button
                className={`settings-btn ${isSettingsDropdownOpen ? 'active' : ''}`}
                onClick={() => setIsSettingsDropdownOpen(!isSettingsDropdownOpen)}
              >
                <Settings size={20} />
              </button>

              {isSettingsDropdownOpen && (
                <div className="dropdown-menu">
                  <div className="dropdown-menu-items">
                    <div
                      className="dropdown-menu-item"
                      onClick={() => handleSettingsMenuItemClick('edit')}
                    >
                      <div className="dropdown-menu-item-icon">
                        <Edit size={16} style={{ color: '#8b5cf6' }} />
                      </div>
                      <span>编辑班级</span>
                    </div>

                    <div
                      className="dropdown-menu-item"
                      onClick={() => handleSettingsMenuItemClick('addStudent')}
                    >
                      <div className="dropdown-menu-item-icon">
                        <UserPlus size={16} style={{ color: '#10b981' }} />
                      </div>
                      <span>添加学生</span>
                    </div>

                    <div
                      className="dropdown-menu-item"
                      onClick={() => handleSettingsMenuItemClick('importStudent')}
                    >
                      <div className="dropdown-menu-item-icon">
                        <Upload size={16} style={{ color: '#3b82f6' }} />
                      </div>
                      <span>导入学生</span>
                    </div>

                    <div
                      className="dropdown-menu-item"
                      onClick={() => handleSettingsMenuItemClick('exportStudent')}
                    >
                      <div className="dropdown-menu-item-icon">
                        <Download size={16} style={{ color: '#10b981' }} />
                      </div>
                      <span>导出学生</span>
                    </div>

                    <div
                      className="dropdown-menu-item"
                      onClick={() => handleSettingsMenuItemClick('transfer')}
                    >
                      <div className="dropdown-menu-item-icon">
                        <Users size={16} style={{ color: '#f59e0b' }} />
                      </div>
                      <span>转让管理</span>
                    </div>

                    <div
                      className="dropdown-menu-item"
                      onClick={() => handleSettingsMenuItemClick('invite')}
                    >
                      <div className="dropdown-menu-item-icon">
                        <Link size={16} style={{ color: '#3b82f6' }} />
                      </div>
                      <span>生成邀请码</span>
                    </div>

                    <div
                      className="dropdown-menu-item"
                      onClick={() => handleSettingsMenuItemClick('batchRedeem')}
                    >
                      <div className="dropdown-menu-item-icon">
                        <Gift size={16} style={{ color: '#f59e0b' }} />
                      </div>
                      <span>批量兑换密令</span>
                    </div>

                    <div
                      className="dropdown-menu-item"
                      onClick={() => handleSettingsMenuItemClick('assignBlocks')}
                    >
                      <div className="dropdown-menu-item-icon">
                        <Blocks size={16} style={{ color: '#3b82f6' }} />
                      </div>
                      <span>分配积木</span>
                    </div>

                    <div className="dropdown-menu-divider"></div>

                    <div
                      className="dropdown-menu-item danger"
                      onClick={() => handleSettingsMenuItemClick('deleteClass')}
                    >
                      <div className="dropdown-menu-item-icon">
                        <Trash2 size={16} />
                      </div>
                      <span>删除班级</span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* 班级头像和信息 */}
          <div className="class-info-header">
            <div className="class-avatar">
              <div className="robot-avatar">🤖</div>
            </div>
            <div className="class-info">
              <p className="school-name">{selectedSchool.schoolName}</p>
              <h2 className="class-name">{classInfo.className}</h2>
            </div>
          </div>

          {/* 搜索框 */}
          <div className="search-section">
            <div className="search-box">
              <Search size={16} className="search-icon" />
            </div>
          </div>

          {/* 学生列表 */}
          <div className="students-section">
            <div className="students-header">
              <h3>学生</h3>
              <div className="students-actions">
                <button
                  className="add-student-btn"
                  onClick={() => setIsAddStudentModalVisible(true)}
                >
                  <Plus size={16} />
                </button>
                <button
                  className={`select-all-btn ${isSelectAll ? 'active' : ''}`}
                  onClick={() => {
                    console.log('点击全选按钮，当前状态:', {
                      isSelectAll,
                      selectedStudentIds: selectedStudentIds.length,
                      totalStudents: students.length
                    });
                    handleSelectAll();
                  }}
                  title={isSelectAll ? '取消全选' : '全选'}
                >
                  <span>{isSelectAll ? '取消全选' : '全选'}</span>
                </button>

                {/* 批量操作按钮 */}
                {selectedStudentIds.length > 0 && (
                  <div className="batch-actions-container" ref={batchActionsDropdownRef}>
                    <button
                      className="batch-actions-btn"
                      onClick={() => setIsBatchActionsDropdownOpen(!isBatchActionsDropdownOpen)}
                      title="批量操作"
                    >
                      <span>批量操作({selectedStudentIds.length})</span>
                      <ChevronDown size={16} />
                    </button>

                    {/* 批量操作下拉菜单 */}
                    {isBatchActionsDropdownOpen && (
                      <div className="batch-actions-dropdown">
                        <button
                          className="batch-action-item"
                          onClick={() => handleBatchAction('batchAssignBlocks')}
                        >
                          <Blocks size={16} />
                          <span>批量分配积木</span>
                        </button>
                        <button
                          className="batch-action-item"
                          onClick={() => handleBatchAction('batchAssignPoints')}
                        >
                          <Zap size={16} />
                          <span>批量分配能量</span>
                        </button>
                        <button
                          className="batch-action-item"
                          onClick={() => handleBatchAction('batchUseKeyPackage')}
                        >
                          <Gift size={16} />
                          <span>批量兑换密令</span>
                        </button>
                        <button
                          className="batch-action-item"
                          onClick={() => handleBatchAction('batchDelete')}
                        >
                          <Trash2 size={16} />
                          <span>批量移出班级</span>
                        </button>
                        <button
                          className="batch-action-item"
                          onClick={() => handleBatchAction('batchExport')}
                        >
                          <Download size={16} />
                          <span>批量导出</span>
                        </button>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>

            <div className="students-list">
              <StudentList
                students={students}
                loading={loading}
                error={error}
                selectedStudent={selectedStudent}
                selectedStudentIds={selectedStudentIds}
                currentTemplate={globalCurrentTemplate || currentTemplate} // 传递教师模板作为回退
                renderVersion={renderVersion}
                onStudentClick={handleStudentClick}
                onStudentSelect={handleStudentSelect}
                onRetry={fetchStudents}
                onIndividualAssignBlocks={handleIndividualAssignBlocks}
                onAssignPoints={(studentId: number) => {
                  const student = students.find(s => s.userId === studentId);
                  if (student) {
                    setSelectedStudent(student);
                    setIsAssignPointsModalVisible(true);
                  }
                }}
              />
            </div>
          </div>
        </div>

        {/* 右侧：功能区域 */}
        <div className="right-section">
          {/* 学生信息头部 */}
          <div className="student-info-header">
            <div className="student-avatar-large">
              <div
                className="student-avatar-circle"
                style={{
                  background: selectedStudent ?
                    getAvatarColor(Math.max(0, students.findIndex(s => s.userId === selectedStudent.userId))) :
                    getAvatarColor(0)
                }}
              >
                {selectedStudent ? selectedStudent.nickName?.charAt(0) || 'S' : 'S'}
              </div>
            </div>
            <div className="student-details">
              <div className="student-name-large">
                {selectedStudent ? selectedStudent.nickName || `学生${selectedStudent.studentNumber || selectedStudent.userId}` : '请选择学生'}
              </div>
              <div className="student-id-large">
                {selectedStudent ? selectedStudent.studentNumber || '无学号' : ''}
              </div>
            </div>
            <button
              className="delete-student-btn"
              onClick={handleDeleteStudent}
              disabled={!selectedStudent}
            >
              <Trash2 size={20} />
            </button>
          </div>

          {/* 功能区域容器 */}
          <div className="functions-container">
            {/* 更多功能 */}
            <div className="functions-section">
              <h3>更多功能</h3>
              <div className="function-buttons">
                <button
                  className="function-btn publish-task"
                  onClick={() => setIsPublishTaskModalVisible(true)}
                >
                  <div className="function-icon">📝</div>
                  <span>发布任务</span>
                </button>
                <button
                  className="function-btn distribute-blocks"
                  onClick={handleAssignBlocks}
                >
                  <div className="function-icon">🧩</div>
                  <span>分配积木</span>
                </button>
                <button
                  className="function-btn distribute-energy"
                  onClick={() => setIsAssignPointsModalVisible(true)}
                >
                  <div className="function-icon">⚡</div>
                  <span>分配能量</span>
                </button>
                <button
                  className="function-btn exchange-tokens"
                  onClick={() => setIsBatchUseKeyPackageModalVisible(true)}
                >
                  <div className="function-icon">🎁</div>
                  <span>兑换密令</span>
                </button>
                <button
                  className="function-btn reset-password"
                  onClick={() => setIsResetPasswordModalVisible(true)}
                >
                  <div className="function-icon">🔑</div>
                  <span>重置密码</span>
                </button>
              </div>
            </div>

            {/* 课程学习情况 */}
            <div className="learning-status">
              <h3>课程学习情况</h3>
              <div className="status-placeholder">
                该区域功能正在等待开放
              </div>
            </div>
          </div>

          {/* 可用能量/总能量 */}
          <div className="energy-section">
            <div className="energy-header">
              <div className="energy-label">可用能量/总能量</div>
              <div className="energy-display">
                <span className="energy-available-number">
                  {selectedStudent?.availablePoints || 0}
                </span>
                <span className="energy-total-number">
                  /{selectedStudent?.totalPoints || 0}
                </span>
              </div>
            </div>
            <div className="energy-bar">
              <div
                className="energy-progress"
                style={{
                  width: selectedStudent?.totalPoints
                    ? `${(Number(selectedStudent.availablePoints || 0) / Number(selectedStudent.totalPoints || 0)) * 100}%`
                    : '0%'
                }}
              ></div>
            </div>
          </div>

          {/* 当前模板 */}
          <div className="template-section">
            <div className="template-header">
              <span>当前模板</span>
              {(() => {
                const displayTemplate = selectedStudent?.currentTemplate || globalCurrentTemplate || currentTemplate;
                return displayTemplate && (
                  <span className={`template-badge ${displayTemplate.isOfficial ? '' : 'custom'}`}>
                    {displayTemplate.isOfficial ? '官方' : '自定义'}
                  </span>
                );
              })()}
            </div>
            <div className="template-info">
              <div className="template-icon">🧩</div>
              <span className={!selectedStudent?.currentTemplate && !globalCurrentTemplate && !currentTemplate ? 'template-loading' : ''}>
                {/* 优先显示学生个人模板，其次教师当前模板，最后全局模板 */}
                {(() => {
                  if (selectedStudent?.currentTemplate) {
                    return selectedStudent.currentTemplate.templateName;
                  } else if (globalCurrentTemplate) {
                    return globalCurrentTemplate.templateName;
                  } else if (currentTemplate) {
                    return currentTemplate.templateName;
                  } else if (selectedStudent) {
                    return '加载中...';
                  } else {
                    return '请选择学生';
                  }
                })()}
              </span>
              {!selectedStudent?.currentTemplate && !globalCurrentTemplate && !currentTemplate && (
                <button
                  onClick={async () => {
                    await fetchTeacherCurrentTemplate();
                    await refreshCurrentTemplate();
                  }}
                  style={{
                    marginLeft: '8px',
                    padding: '2px 6px',
                    fontSize: '10px',
                    backgroundColor: '#2196F3',
                    color: 'white',
                    border: 'none',
                    borderRadius: '3px',
                    cursor: 'pointer'
                  }}
                >
                  刷新
                </button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* 添加学生弹窗 */}
      <AddStudentModal
        visible={isAddStudentModalVisible}
        onCancel={() => setIsAddStudentModalVisible(false)}
        onOk={handleAddStudent}
      />

      {/* 编辑班级弹窗 */}
      <EditClassModal
        visible={isEditClassModalVisible}
        onCancel={() => setIsEditClassModalVisible(false)}
        onOk={handleEditClass}
        initialValues={{
          className: classInfo.className
        }}
      />

      {/* 导入学生弹窗 */}
      <ImportStudentModal
        visible={isImportStudentModalVisible}
        onCancel={() => setIsImportStudentModalVisible(false)}
        onImport={handleImportStudents}
        classId={classInfo.id}
      />

      {/* 转让班级弹窗 */}
      <TransferClassModal
        visible={isTransferClassModalVisible}
        onCancel={() => {
          setIsTransferClassModalVisible(false);
          setSearchedTeacher(null);
        }}
        onOk={handleTransferClass}
        onSearchTeacher={handleSearchTeacher}
        searchedTeacher={searchedTeacher}
        hasAssistantTeacher={!!classInfo.assistantTeacherId}
        onRemoveAssistant={handleRemoveAssistant}
        loading={transferLoading}
      />

      {/* 邀请码弹窗 */}
      {isInviteCodeModalVisible && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold">邀请码生成成功</h2>
              <button
                className="text-gray-500 hover:text-gray-700"
                onClick={() => setIsInviteCodeModalVisible(false)}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="mb-6">
              <div className="flex items-center justify-between bg-gray-50 p-3 rounded-lg mb-4">
                <span className="font-mono text-lg text-blue-600 mr-4">{inviteCode}</span>
                <button
                  className="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600"
                  onClick={() => {
                    navigator.clipboard.writeText(inviteCode)
                      .then(() => alert('邀请码已复制'))
                      .catch(() => alert('复制失败，请手动复制'));
                  }}
                >
                  复制
                </button>
              </div>
              <div className="text-gray-500 text-sm space-y-2">
                <p>您可以:</p>
                <ul className="list-disc list-inside space-y-1">
                  <li>将邀请码分享给学生，让他们加入班级</li>
                  <li>邀请其他老师作为协助教师加入班级</li>
                </ul>
                <p className="text-yellow-500">
                  ⏰ 邀请码有效期为24小时
                </p>
              </div>
            </div>
            <div className="flex justify-end">
              <button
                className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                onClick={() => setIsInviteCodeModalVisible(false)}
              >
                关闭
              </button>
            </div>
          </div>
        </div>
      )}



      {/* 发布任务弹窗 */}
      <PublishTaskModal
        visible={isPublishTaskModalVisible}
        onCancel={() => setIsPublishTaskModalVisible(false)}
        onOk={handlePublishTask}
        students={students}
        selectedStudents={selectedStudentIds}
        setSelectedStudents={setSelectedStudentIds}
        fileList={fileList}
        uploading={uploading}
        handleUpload={handleUpload}
        handleRemoveFile={handleRemoveFile}
        displayTemplates={templates}
        officialTemplates={officialTemplates}
        selectedTemplateId={selectedTemplateId}
        setSelectedTemplateId={setSelectedTemplateId}
      />

      {/* 重置密码弹窗 */}
      <ResetPasswordModal
        visible={isResetPasswordModalVisible}
        onCancel={() => setIsResetPasswordModalVisible(false)}
        onOk={handleResetPassword}
        isBatch={false}
        count={1}
      />

      {/* 分配积木弹窗 */}
      <AssignBlocksModal
        visible={isAssignBlocksModalVisible}
        onCancel={() => {
          setIsAssignBlocksModalVisible(false);
          setSelectedStudentId(null);
          setSelectedStudentIds([]);
        }}
        isClassCardAssign={selectedStudentId !== null}
        loadingTemplates={loadingTemplates}
        templates={templates}
        studentTemplateUsage={studentTemplateUsage}
        teacherTemplate={teacherTemplate}
        onSelectTemplate={handleSelectTemplate}
        onTemplateUsageClick={handleTemplateUsageClick}
        userId={userId}
        onRefreshTemplates={fetchTemplates}
        students={students}
        selectedStudentIds={selectedStudentIds}
        userRoles={userRoles}
        onSuccess={() => {
          refreshStudentList();
          fetchTemplateUsage();
        }}
        onUpdateStudentTemplate={handleUpdateStudentTemplate}
      />

      {/* 分配能量弹窗 */}
      <AssignPointsModal
        visible={isAssignPointsModalVisible}
        onCancel={() => {
          setIsAssignPointsModalVisible(false);
          setSelectedStudent(null);
        }}
        // 根据 selectedStudent 是否存在来决定调用哪个处理函数
        onOk={selectedStudent ? handleAssignPoints : handleBatchAssignPoints}
        studentName={selectedStudent ? `${selectedStudent.nickName}` : `已选择 ${selectedStudentIds.length} 名学生`}
        studentId={selectedStudent?.id || 0}
        userId={selectedStudent?.userId || 0}
        student={selectedStudent}
        isBatch={!selectedStudent}
        selectedStudents={selectedStudentIds}
        students={students}
        onSuccess={() => {
          // 移除这里的刷新，因为 onOk 内部已经处理了
        }}
        refreshStudentList={refreshStudentList}
        onGoToRedeemKey={(studentIds) => {
          console.log('前往兑换密钥:', studentIds);
          setIsBatchUseKeyPackageModalVisible(true);
        }}
      />

      {/* 兑换密令弹窗 */}
      <BatchUseKeyPackageModal
        open={isBatchUseKeyPackageModalVisible}
        selectedStudentIds={selectedStudentIds}
        students={students}
        onClose={() => setIsBatchUseKeyPackageModalVisible(false)}
        onSuccess={handleBatchUseKeyPackageSuccess}
        onGoToAssignPoints={handleGoToAssignPointsFromRedeem}
      />
    </div>
  );
};

export default ClassDetail;
