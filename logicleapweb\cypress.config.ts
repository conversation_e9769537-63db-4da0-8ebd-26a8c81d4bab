import { defineConfig } from "cypress";

export default defineConfig({
  e2e: {
    setupNodeEvents(on, config) {
      // 在这里实现节点事件监听器
      // 所有命令增加 500ms 延迟
      on('before:run', () => {
        return new Promise(resolve => setTimeout(resolve, 500));
      });
    },
    experimentalStudio: true,
    baseUrl: 'http://localhost:3000',
    // 添加这些配置以减慢测试执行速度
    defaultCommandTimeout: 5000,      // 命令默认超时时间
    execTimeout: 60000,               // 执行超时
    pageLoadTimeout: 60000,           // 页面加载超时
    requestTimeout: 15000,            // 请求超时
    responseTimeout: 15000,           // 响应超时
    animationDistanceThreshold: 5,    // 动画距离阈值
    // 减慢下一个命令之前的等待时间
    taskTimeout: 60000,
    // 使用环境变量减慢执行速度
    env: {
      delayBetweenCommands: 100,  // 命令之间的延迟(毫秒)
    }
  },
});
