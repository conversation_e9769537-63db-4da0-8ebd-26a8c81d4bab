'use client';

import React from 'react';
import Image from 'next/image';
import { Bell, MessageSquare, ChevronDown, MoreHorizontal } from 'lucide-react';
import { UserInfo } from '@/lib/api/user';

interface RightSidebarProps {
    userInfo: Partial<UserInfo>;
    stats: {
        studentCount: number;
        classCount: number;
        courseCount: number;
    };
    points: number;
}

const RightSidebar = ({ userInfo, stats, points }: RightSidebarProps) => {
    const courses = [
        { name: '人工智能启蒙课', series: '4课时系列' },
        { name: '机器学习基础', series: '8课时系列' },
        { name: '深度学习入门', series: '12课时系列' },
        { name: '编程思维训练', series: '6课时系列' },
    ];
    
    return (
        <aside className="right-sidebar bg-gray-100 flex flex-col h-full p-4">
            {/* Combined Header and Profile Module */}
            <div className="bg-white rounded-t-lg">
                {/* Header Section */}
                <div className="p-4">
                    <div className="user-info flex justify-between items-center">
                        <div className="header-icons flex gap-4">
                            <Bell className="text-gray-500 w-5 h-5" />
                            <MessageSquare className="text-gray-500 w-5 h-5 scale-x-[-1]" />
                        </div>
                        <div className="user-dropdown flex items-center gap-2 cursor-pointer">
                            <Image
                                src={userInfo.avatarUrl || "/images/xiaoluo-default.webp"}
                                alt={userInfo.nickName || "小洛头像"}
                                width={32}
                                height={32}
                                className="rounded-full"
                                style={{ backgroundColor: 'white' }}
                            />
                            <ChevronDown size={16} className="text-gray-500 hover:text-gray-700 transition-all duration-200 transform hover:scale-110 hover:translate-y-0.5" />
                        </div>
                    </div>
                </div>

                {/* Profile Section */}
                <div className="p-6">
                    <section className="user-profile-card flex flex-col items-center">
                        <div className="profile-avatar-container relative mb-6 mt-6 w-20 h-20 mx-auto">
                            <div className="circular-progress absolute inset-0 w-20 h-20">
                                {/* Circular progress overlay would be implemented here */}
                                <div className="progress-blue absolute" style={{ borderWidth: '2px', borderStyle: 'solid', borderColor: '#1E88E5 transparent transparent transparent', borderRadius: '50%', width: '100%', height: '100%', transform: 'rotate(-45deg)' }}></div>
                                <div className="progress-pink absolute" style={{ borderWidth: '2px', borderStyle: 'solid', borderColor: 'transparent #FF4081 #FF4081 transparent', borderRadius: '50%', width: '100%', height: '100%', transform: 'rotate(45deg)' }}></div>
                                <div className="progress-gray absolute" style={{ borderWidth: '2px', borderStyle: 'solid', borderColor: 'transparent transparent #BDBDBD #BDBDBD', borderRadius: '50%', width: '100%', height: '100%', transform: 'rotate(180deg)' }}></div>
                            </div>
                            <Image
                                src={userInfo.avatarUrl || "/images/xiaoluo-default.webp"}
                                alt={userInfo.nickName || "小洛头像"}
                                width={80}
                                height={80}
                                className="rounded-full z-10 relative w-20 h-20 object-cover"
                                style={{ backgroundColor: 'white' }}
                            />
                        </div>
                        <h3 className="profile-name text-base font-medium text-gray-800 mb-4 mt-2">{userInfo.nickName || '未登录'}</h3>
                        
                        <div className="stats-grid grid grid-cols-3 w-full text-center mb-6">
                            <div>
                                <p className="stat-value text-gray-800 font-bold">{userInfo.nickName ? (stats.studentCount || '258') : '0'}</p>
                                <p className="stat-label text-sm text-gray-500">总学生数</p>
                            </div>
                            <div>
                                <p className="stat-value text-cyan-400 font-bold">{userInfo.nickName ? (stats.classCount || '3') : '0'}</p>
                                <p className="stat-label text-sm text-gray-500">总班级数</p>
                            </div>
                            <div>
                                <p className="stat-value text-gray-800 font-bold">{userInfo.nickName ? (stats.courseCount || '50') : '0'}</p>
                                <p className="stat-label text-sm text-gray-500">总课程数</p>
                            </div>
                        </div>

                        <div className="energy-balance flex items-center justify-between w-full px-2">
                            <div className="flex flex-col">
                                <p className="text-gray-500 text-sm mb-0 leading-none whitespace-nowrap">剩余能量</p>
                                <p className="energy-value text-gray-800 font-bold -mt-3 leading-none">{userInfo.nickName ? (points || '9,999') : '0'}</p>
                            </div>
                            <button className="recharge-btn bg-blue-500 hover:bg-blue-600 text-white rounded-md px-4 py-1.5 ml-4 transition-colors duration-200 text-sm font-medium whitespace-nowrap">
                                充值
                            </button>
                        </div>
                    </section>
                </div>
            </div>

            {/* Courses Module */}
            <div className="bg-white p-4 border-t border-gray-100">
                <section className="my-courses-card mb-6">
                    <div className="card-header flex justify-between items-center mb-3">
                        <h4 className="text-gray-800 font-medium">我的课程</h4>
                        <MoreHorizontal size={18} className="text-gray-400" />
                    </div>
                    <div className="border-t border-gray-100 mb-3"></div>
                    <ul className="courses-list space-y-3">
                        {courses.map((course, index) => (
                            <li key={index} className="course-item flex items-center">
                                <div className="course-icon bg-gradient-to-br from-blue-500 to-purple-600 w-12 h-12 rounded-lg mr-1 flex-shrink-0 flex items-center justify-center">
                                    <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M12 2L2 7v10c0 5.55 3.84 9.74 9 11 5.16-1.26 9-5.45 9-11V7l-10-5z"/>
                                        <path d="M9 12l2 2 4-4" stroke="currentColor" strokeWidth="2" fill="none" strokeLinecap="round" strokeLinejoin="round"/>
                                    </svg>
                                </div>
                                <div className="course-details flex-grow min-w-0 -ml-1">
                                    <p className="course-name text-sm font-medium text-gray-800 truncate mt-1">{course.name}</p>
                                    <p className="course-series text-xs text-gray-500">{course.series}</p>
                                </div>
                                <button className="draft-btn text-xs text-green-600 bg-green-50 hover:bg-green-100 border border-green-200 px-6 py-1 rounded-md transition-colors duration-200 font-medium ml-2 whitespace-nowrap min-w-[48px]">草稿</button>
                            </li>
                        ))}
                    </ul>
                </section>

                {/* Template Section */}
                <section className="current-template-card bg-white rounded-lg border border-gray-100 shadow-sm p-4 relative">
                    <svg className="w-4 h-4 text-gray-500 absolute top-4 right-4" width="24" height="24" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M42 19H5.99998" stroke="currentColor" strokeWidth="4" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M30 7L42 19" stroke="currentColor" strokeWidth="4" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M6.79897 29H42.799" stroke="currentColor" strokeWidth="4" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M6.79895 29L18.799 41" stroke="currentColor" strokeWidth="4" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                    <p className="text-gray-500 text-sm mb-2">当前使用模板</p>
                    <p className="text-gray-800">系统默认全权限模板</p>
                </section>
            </div>
        </aside>
    );
};

export default RightSidebar; 
