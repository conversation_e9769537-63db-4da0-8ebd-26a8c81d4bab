import request from '../../request'

// 审核记录API
export const activityAuditApi = {
  // 获取活动审核记录
  getActivityAuditById: (activityId: number) => {
    return request.get(`/api/v1/app/audit/activity/listByActivity/${activityId}`)
  },

  // 获取审核记录列表
  getList: (params: {
    page?: number;
    size?: number;
    activityId?: number;
    auditorId?: number;
    result?: number;
    startTime?: Date;
    endTime?: Date;
  }) => {
    return request.get('/api/v1/app/audit/activity/listWithFilter', { params });
  },

  // 获取单个活动详情
  getActivityInfo: (id: number) => {
    return request.get(`/api/v1/app/activity/infoActivity/${id}`);
  },

  // 获取所有状态的活动
  getAllActivity: () => {
    return request.get(`/api/v1/app/activity/review/list`);
  },

  // 获取指定活动的审核记录
  getActivityAudits: (activityId: number) => {
    return request.get(`/api/v1/app/audit/activity/listByActivity/${activityId}`);
  },

  // 获取审核记录详情
  getAuditInfo: (id: number) => {
    return request.get(`/api/v1/app/audit/activity/info/${id}`);
  },

  // 手动添加审核记录
  addAudit: (params: {
    activityId: number;
    result: number;
    reason?: string;
    beforeStatus: number;
    afterStatus: number;
  }) => {
    return request.post('/api/v1/app/audit/activity/add', params);
  }
};

// 参赛作品审核API
export const participationAuditApi = {
  // 审核参赛作品
  reviewParticipation: (id: number, data: {
    isApproved: boolean;
    reason?: string;
  }) => {
    return request.post(`/api/v1/activity_work/review/${id}`, data);
  },

  // 批量审核参赛作品
  batchReviewParticipation: (data: {
    ids: number[];
    isApproved: boolean;
    reason?: string;
  }) => {
    return request.post(`/api/v1/activity_work/batch-review`, data);
  },

  // 获取待审核参赛作品列表
  getReviewList: (activityId?: number) => {
    return request.get(`/api/v1/activity_work/review/list`, {
      params: activityId ? { activityId } : undefined
    });
  },

  // 根据活动ID获取待审核参赛作品列表
  getReviewListByActivity: (activityId: number) => {
    return request.get(`/api/v1/activity_work/review/list/${activityId}`);
  },

  // 获取审核记录列表
  getAuditList: (params: {
    page?: number;
    size?: number;
    activityId?: number;
    workId?: number;
    activityWorkId?: number;
    userId?: number;
    auditorId?: number;
    result?: number;
    startTime?: Date;
    endTime?: Date;
  }) => {
    return request.get('/api/v1/app/audit/participation/listWithFilter', { params });
  },

  // 获取指定活动的审核记录
  getActivityAudits: (activityId: number) => {
    return request.get(`/api/v1/app/audit/participation/listByActivity/${activityId}`);
  },

  // 获取指定作品的审核记录
  getWorkAudits: (activityWorkId: number) => {
    return request.get(`/api/v1/app/audit/participation/listByActivityWork/${activityWorkId}`);
  },

  // 获取指定用户的审核记录
  getUserAudits: (userId: number) => {
    return request.get(`/api/v1/app/audit/participation/listByUser/${userId}`);
  },

  // 获取审核记录详情
  getAuditInfo: (id: number) => {
    return request.get(`/api/v1/app/audit/participation/info/${id}`);
  },

  // 手动添加审核记录
  addAudit: (params: {
    activityWorkId: number;
    result: number;
    reason?: string;
    beforeStatus: number;
    afterStatus: number;
  }) => {
    return request.post('/api/v1/app/audit/participation/add', params);
  }
};
