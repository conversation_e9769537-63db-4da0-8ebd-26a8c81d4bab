"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseConfigService = void 0;
const common_1 = require("@nestjs/common");
const path_1 = require("path");
const yaml_service_1 = require("../../yaml/yaml.service");
const typeorm_logger_1 = require("../monitor/typeorm-logger");
let DatabaseConfigService = class DatabaseConfigService {
    yamlService;
    databasesConfig;
    customLogger;
    constructor(yamlService) {
        this.yamlService = yamlService;
        try {
            this.databasesConfig = this.yamlService.readYamlConfig('databases.yaml', 'src/util/database/config', true);
            console.log(`成功加载${this.yamlService.getEnvironment()}环境的数据库配置`);
        }
        catch (e) {
            console.error(`读取${this.yamlService.getEnvironment()}环境的数据库配置文件失败:`, e);
            throw e;
        }
    }
    getMysqlConfig(queryInterceptor) {
        const mysqlConfig = this.databasesConfig.mysql;
        if (mysqlConfig && mysqlConfig.entities) {
            mysqlConfig.entities = mysqlConfig.entities.map((path) => {
                return path.replace('dist/', (0, path_1.join)(process.cwd(), 'dist/'));
            });
        }
        if (queryInterceptor) {
            this.customLogger = new typeorm_logger_1.CustomTypeOrmLogger(queryInterceptor);
            mysqlConfig.logger = this.customLogger;
            mysqlConfig.logging = ['query', 'error', 'slow'];
        }
        return mysqlConfig;
    }
    getRedisConfig() {
        const redisConfig = this.databasesConfig.redis;
        if (!redisConfig) {
            throw new Error('无法从配置文件中获取Redis配置');
        }
        return {
            host: redisConfig.host,
            port: redisConfig.port,
            password: redisConfig.password,
            db: redisConfig.db,
        };
    }
};
exports.DatabaseConfigService = DatabaseConfigService;
exports.DatabaseConfigService = DatabaseConfigService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [yaml_service_1.YamlService])
], DatabaseConfigService);
//# sourceMappingURL=database-config.service.js.map