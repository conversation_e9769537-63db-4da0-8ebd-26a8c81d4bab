import axios from 'axios';
import type { AxiosError } from 'axios';
import { API_URL } from '../config/config';
import { Modal } from 'antd';
import { store } from '../lib/store';
import { clearUser } from '../lib/store';

// 定义响应数据类型
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
}

const request = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  }
});

let isTokenExpiredGlobal = false;

// 添加全局标志，用于控制弹框显示
export let hasShownModal = false;

// 添加重置方法
export const resetModalFlag = () => {
  hasShownModal = false;
};

// 刷新token相关变量
let isRefreshing = false;
let failedQueue: Array<{
  resolve: (value?: any) => void;
  reject: (reason?: any) => void;
  config: any;
}> = [];

// 处理队列中的请求
const processQueue = (error: any, token: string | null = null) => {
  failedQueue.forEach(({ resolve, reject, config }) => {
    if (error) {
      reject(error);
    } else {
      if (token) {
        config.headers.Authorization = token;
      }
      resolve(request(config));
    }
  });

  failedQueue = [];
};

// 刷新token的函数
const refreshToken = async (): Promise<string> => {
  const refreshToken = localStorage.getItem('refreshToken');
  console.log('🔄 开始刷新token，refreshToken:', refreshToken ? `${refreshToken.substring(0, 20)}...` : '无');

  if (!refreshToken) {
    console.error('❌ 没有refreshToken，无法刷新');
    throw new Error('No refresh token available');
  }

  try {
    console.log('📤 发送刷新token请求到:', '/api/router-guard/refresh-token');
    const response = await axios.post('/api/router-guard/refresh-token', {
      refreshToken: refreshToken
    }, {
      baseURL: API_URL
    });

    console.log('📥 刷新token响应:', response.data);

    if (response.data.code === 200) {
      const { token, refreshToken: newRefreshToken } = response.data.data;
      console.log('✅ 刷新token成功，新token:', token ? `${token.substring(0, 20)}...` : '无');
      localStorage.setItem('token', token);
      localStorage.setItem('refreshToken', newRefreshToken);
      return token;
    } else {
      console.error('❌ 刷新token失败，响应码:', response.data.code, '消息:', response.data.message || response.data.msg);
      throw new Error(`Token refresh failed: ${response.data.message || response.data.msg || 'Unknown error'}`);
    }
  } catch (error: any) {
    console.error('❌ 刷新token异常:', error);
    console.error('错误详情:', {
      message: error.message,
      response: error.response?.data,
      status: error.response?.status
    });

    // 刷新失败，清除所有token
    localStorage.removeItem('token');
    localStorage.removeItem('refreshToken');
    localStorage.removeItem('user');
    clearUser();
    throw error;
  }
};

// 请求拦截器
request.interceptors.request.use(
  async config => {
    const token = localStorage.getItem('token');
    const refreshTokenValue = localStorage.getItem('refreshToken');

    // 检查请求的URL是否为登录接口或刷新token接口
    if (config.url && (
      config.url.includes('/api/user-auth/password') ||
      config.url.includes('/api/router-guard/refresh-token')
    )) {
      return config; // 不拦截登录和刷新token请求
    }

    if (token) {
      config.headers.Authorization = token;
    } else if (refreshTokenValue && !isRefreshing) {
      // 没有token但有refreshToken，尝试主动刷新
      console.log('🔄 请求拦截器检测到缺少token但有refreshToken，主动尝试刷新');

      try {
        // 标记正在刷新，避免重复刷新
        isRefreshing = true;

        const newToken = await refreshToken();
        console.log('✅ 请求拦截器中刷新token成功');

        // 设置新token到当前请求
        config.headers.Authorization = newToken;

        // 处理队列中的其他请求
        processQueue(null, newToken);

      } catch (refreshError) {
        console.error('❌ 请求拦截器中刷新token失败:', refreshError);

        // 处理队列中的其他请求
        processQueue(refreshError, null);

        // 刷新失败，清除refreshToken并拒绝请求
        handleLogout('请求拦截器中refreshToken刷新失败');
        return Promise.reject(new Error('Token刷新失败，请重新登录'));

      } finally {
        isRefreshing = false;
      }
    } else if (!refreshTokenValue) {
      console.warn('请求拦截器 - 未找到token和refreshToken');
    } else {
      console.warn('请求拦截器 - 未找到token，但正在刷新中');
    }

    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

interface ErrorResponse {
  code: number;
  message: string;
  type?: string;
}

request.interceptors.response.use(
  (response) => {
    if (response.config.url?.includes('/login/password') && response.data?.code === 200) {
      resetModalFlag();
      isTokenExpiredGlobal = false; // 重置token过期标志
    }
    return response;
  },
  async (error) => {
    const originalRequest = error.config;
    const errorData = error.response?.data;

    // 处理401状态码的错误
    if (error.response?.status === 401) {
      console.log('🚨 收到401错误:', {
        url: originalRequest?.url,
        status: error.response?.status,
        errorData: errorData,
        hasType: !!errorData?.type,
        hasMsg: !!errorData?.msg
      });

      // 处理token过期和其他设备登录的情况
      // 检查多种可能的错误格式和消息
      // 支持嵌套的错误结构（如 details 字段）
      const detailsData = errorData?.details || errorData;

      // 检查是否是其他设备登录的错误
      const isOtherDeviceLogin =
        errorData?.type === 'OTHER_DEVICE_LOGIN' ||
        detailsData?.type === 'OTHER_DEVICE_LOGIN' ||
        (errorData?.msg && errorData.msg.includes('账号已在其他设备登录')) ||
        (detailsData?.msg && detailsData.msg.includes('账号已在其他设备登录')) ||
        (errorData?.message && errorData.message.includes('账号已在其他设备登录')) ||
        (detailsData?.message && detailsData.message.includes('账号已在其他设备登录'));

      console.log('🔍 检查其他设备登录状态:', {
        isOtherDeviceLogin,
        errorData: errorData,
        detailsData: detailsData,
        hasShownModal
      });

      // 如果是其他设备登录，直接显示提示
      if (isOtherDeviceLogin && !hasShownModal) {
        hasShownModal = true;
        Modal.confirm({
          title: '账号异常',
          content: '您的账号已在其他设备登录，当前登录已失效，请重新登录',
          okText: '重新登录',
          maskClosable: false,
          keyboard: true,
          centered: true,
          className: 'other-device-login-modal',
          closable: false,
          cancelButtonProps: {
            style: { display: 'none' }
          },
          onOk: () => {
            handleLogout('其他设备登录，当前会话失效');
          }
        });
        return Promise.reject(error);
      }

      // 处理token过期的情况，尝试无感刷新
      // 使用上面已定义的 detailsData 变量
      const isTokenExpired =
        ['TOKEN_EXPIRED', 'INVALID_TOKEN'].includes(errorData?.type || detailsData?.type) ||
        (errorData?.msg && (
          errorData.msg.includes('登录已过期') ||
          errorData.msg.includes('token无效') ||
          errorData.msg.includes('token已过期') ||
          errorData.msg.includes('请先登录')
        )) ||
        (detailsData?.msg && (
          detailsData.msg.includes('登录已过期') ||
          detailsData.msg.includes('token无效') ||
          detailsData.msg.includes('token已过期') ||
          detailsData.msg.includes('请先登录')
        )) ||
        (errorData?.message && (
          errorData.message.includes('登录已过期') ||
          errorData.message.includes('token无效') ||
          errorData.message.includes('token已过期') ||
          errorData.message.includes('请先登录')
        )) ||
        (detailsData?.message && (
          detailsData.message.includes('登录已过期') ||
          detailsData.message.includes('token无效') ||
          detailsData.message.includes('token已过期') ||
          detailsData.message.includes('请先登录')
        )) ||
        (error.response?.status === 401 && (errorData?.code === 401 || detailsData?.code === 401));

      console.log('🔍 检查token过期状态:', {
        isTokenExpired,
        errorData: errorData,
        detailsData: detailsData,
        errorType: errorData?.type,
        errorMsg: errorData?.msg,
        errorMessage: errorData?.message,
        errorCode: errorData?.code,
        detailsType: detailsData?.type,
        detailsMsg: detailsData?.msg,
        detailsMessage: detailsData?.message,
        detailsCode: detailsData?.code,
        status: error.response?.status,
        url: originalRequest?.url
      });

      if (isTokenExpired) {
        console.log('🔍 检测到token过期，准备无感刷新');

        // 如果已经在刷新中，将请求加入队列
        if (isRefreshing) {
          console.log('⏳ 已在刷新中，将请求加入队列');
          return new Promise((resolve, reject) => {
            failedQueue.push({ resolve, reject, config: originalRequest });
          });
        }

        // 如果没有refreshToken，直接登出
        const refreshTokenValue = localStorage.getItem('refreshToken');
        if (!refreshTokenValue) {
          console.log('❌ 没有refreshToken，直接登出');
          handleLogout('缺少refreshToken');
          return Promise.reject(error);
        }

        // 开始刷新token
        console.log('🔄 开始刷新token流程');
        isRefreshing = true;

        try {
          const newToken = await refreshToken();
          console.log('✅ 刷新token成功，处理队列中的请求');
          processQueue(null, newToken);

          // 重新发起原始请求
          originalRequest.headers.Authorization = newToken;
          console.log('🔁 重新发起原始请求:', originalRequest.url);
          return request(originalRequest);
        } catch (refreshError) {
          console.error('❌ 刷新token失败:', refreshError);
          processQueue(refreshError, null);
          handleLogout('refreshToken刷新失败');
          return Promise.reject(refreshError);
        } finally {
          isRefreshing = false;
        }
      }

      // 其他401错误，直接登出
      if (!isTokenExpiredGlobal) {
        isTokenExpiredGlobal = true;
        handleLogout('非token过期的401错误');
      }
    }

    // 错误仍然需要传递给调用者处理
    return Promise.reject(error);
  }
);

// 统一的登出处理函数
const handleLogout = (reason: string = '未知原因') => {
  console.log('🚪 执行登出操作，原因:', reason);
  console.log('🗑️ 清除localStorage中的认证信息');

  // 记录清除前的状态
  const beforeClear = {
    token: localStorage.getItem('token') ? '存在' : '不存在',
    refreshToken: localStorage.getItem('refreshToken') ? '存在' : '不存在',
    user: localStorage.getItem('user') ? '存在' : '不存在'
  };
  console.log('清除前状态:', beforeClear);

  localStorage.removeItem('token');
  localStorage.removeItem('user');
  localStorage.removeItem('refreshToken');
  store.dispatch(clearUser());

  console.log('✅ 登出处理完成');
};

export default request; 