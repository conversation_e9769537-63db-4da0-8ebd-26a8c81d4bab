'use client'

import { useState, useEffect } from 'react';
import { Card, Button, Table, Tag, Space, Modal, Select, Input, message, Tooltip, Image } from 'antd';
import { EyeOutlined, CheckOutlined, CloseOutlined, SearchOutlined, FileTextOutlined } from '@ant-design/icons';
import { activityApi } from '@/lib/api/activity';
import { participationAuditApi } from '@/lib/api/activity/audit';
import { GetNotification } from 'logic-common/dist/components/Notification';
import dayjs from 'dayjs';

const { Option } = Select;
const { TextArea } = Input;

interface ActivityWork {
  id: number;
  activityId: number;
  workId: number;
  userId: number;
  status: number;
  createdAt: string;
  updatedAt: string;
  activity?: {
    id: number;
    name: string;
  };
  user?: {
    id: number;
    nickName: string;
  };
  work?: {
    id: number;
    title: string;
    coverImage?: string;
    type: number;
  };
}

interface WorkReviewManagementProps {
  userId?: number;
}

export default function WorkReviewManagement({ userId }: WorkReviewManagementProps) {
  const [works, setWorks] = useState<ActivityWork[]>([]);
  const [loading, setLoading] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [selectedWork, setSelectedWork] = useState<ActivityWork | null>(null);
  const [reviewModalVisible, setReviewModalVisible] = useState(false);
  const [reviewReason, setReviewReason] = useState('');
  const [reviewAction, setReviewAction] = useState<'approve' | 'reject'>('approve');
  const [filterStatus, setFilterStatus] = useState<number | undefined>(undefined);
  const [searchKeyword, setSearchKeyword] = useState('');
  
  const notification = GetNotification();

  // 获取待审核作品列表
  const fetchReviewWorks = async () => {
    try {
      setLoading(true);
      // 暂时使用模拟数据，因为审核API可能还未完全实现
      console.log('尝试获取待审核作品列表...');

      // 尝试获取所有活动的作品
      const activitiesResponse = await activityApi.getList({ page: 1, size: 100 });
      if (activitiesResponse?.data && activitiesResponse.status === 200) {
        const activitiesData = activitiesResponse.data.data || activitiesResponse.data;
        const activities = activitiesData.list || activitiesData || [];

        // 获取所有活动的作品
        const allWorks: any[] = [];
        for (const activity of activities) {
          try {
            const worksResponse = await activityApi.getDetailWithWorks(activity.id);
            if (worksResponse?.data && worksResponse.status === 200) {
              const worksData = worksResponse.data.data || worksResponse.data;
              const works = worksData.works || worksData.activityWorks || [];
              works.forEach((work: any) => {
                allWorks.push({
                  ...work,
                  activity: { id: activity.id, name: activity.name }
                });
              });
            }
          } catch (workError) {
            console.log(`获取活动${activity.id}的作品失败:`, workError);
          }
        }

        setWorks(allWorks);
        console.log('获取到的作品数据:', allWorks);
      }
    } catch (error) {
      console.error('获取待审核作品失败:', error);
      notification.error('获取待审核作品失败');
      // 设置空数组避免页面崩溃
      setWorks([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchReviewWorks();
  }, []);

  // 处理审核
  const handleReview = async () => {
    if (!selectedWork) return;

    try {
      const response = await participationAuditApi.reviewParticipation(selectedWork.id, {
        isApproved: reviewAction === 'approve',
        reason: reviewReason,
      });

      if (response?.status === 200) {
        notification.success(`${reviewAction === 'approve' ? '通过' : '拒绝'}审核成功`);
        setReviewModalVisible(false);
        setSelectedWork(null);
        setReviewReason('');
        fetchReviewWorks();
      } else {
        notification.error('审核失败');
      }
    } catch (error) {
      console.error('审核失败:', error);
      notification.error('审核失败');
    }
  };

  // 打开审核模态框
  const openReviewModal = (work: ActivityWork, action: 'approve' | 'reject') => {
    setSelectedWork(work);
    setReviewAction(action);
    setReviewModalVisible(true);
  };

  // 过滤作品
  const filteredWorks = works.filter(work => {
    const matchStatus = filterStatus === undefined || work.status === filterStatus;
    const matchKeyword = !searchKeyword || 
      work.activity?.name?.toLowerCase().includes(searchKeyword.toLowerCase()) ||
      work.work?.title?.toLowerCase().includes(searchKeyword.toLowerCase()) ||
      work.user?.nickName?.toLowerCase().includes(searchKeyword.toLowerCase());
    
    return matchStatus && matchKeyword;
  });

  // 获取状态标签
  const getStatusTag = (status: number) => {
    switch (status) {
      case 0:
        return <Tag color="orange">待审核</Tag>;
      case 1:
        return <Tag color="green">已通过</Tag>;
      case 2:
        return <Tag color="red">已拒绝</Tag>;
      default:
        return <Tag>未知</Tag>;
    }
  };

  // 表格列定义
  const columns = [
    {
      title: '作品封面',
      dataIndex: ['work', 'coverImage'],
      key: 'coverImage',
      width: 80,
      render: (coverImage: string, record: ActivityWork) => (
        coverImage ? (
          <Image
            src={coverImage}
            alt="作品封面"
            width={50}
            height={50}
            style={{ objectFit: 'cover', borderRadius: '4px' }}
            fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
          />
        ) : (
          <div className="w-12 h-12 bg-gray-200 rounded flex items-center justify-center">
            <FileTextOutlined className="text-gray-400" />
          </div>
        )
      ),
    },
    {
      title: '作品标题',
      dataIndex: ['work', 'title'],
      key: 'workTitle',
      width: 150,
      ellipsis: true,
    },
    {
      title: '活动名称',
      dataIndex: ['activity', 'name'],
      key: 'activityName',
      width: 150,
      ellipsis: true,
    },
    {
      title: '提交用户',
      dataIndex: ['user', 'nickName'],
      key: 'userName',
      width: 100,
      ellipsis: true,
    },
    {
      title: '提交时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 120,
      render: (text: string) => dayjs(text).format('YYYY-MM-DD HH:mm'),
    },
    {
      title: '审核状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: number) => getStatusTag(status),
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (record: ActivityWork) => (
        <Space size="small">
          {record.status === 0 && (
            <>
              <Tooltip title="通过审核">
                <Button
                  type="link"
                  size="small"
                  icon={<CheckOutlined />}
                  onClick={() => openReviewModal(record, 'approve')}
                  style={{ color: '#52c41a' }}
                />
              </Tooltip>
              <Tooltip title="拒绝审核">
                <Button
                  type="link"
                  size="small"
                  icon={<CloseOutlined />}
                  onClick={() => openReviewModal(record, 'reject')}
                  danger
                />
              </Tooltip>
            </>
          )}
          <Tooltip title="查看详情">
            <Button
              type="link"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => {
                // 这里可以添加查看作品详情的逻辑
                message.info('查看作品详情功能待实现');
              }}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <>
      <Card
        title="作品审核管理"
        extra={
          <Button type="primary" onClick={() => setIsModalVisible(true)}>
            查看全部
          </Button>
        }
        className="shadow-sm"
      >
        <div className="space-y-4">
          <div className="text-sm text-gray-500">
            审核学生提交的活动作品，管理参赛内容
          </div>
          <div className="text-xs text-gray-400">
            待审核作品: {works.filter(w => w.status === 0).length} 个
          </div>
        </div>
      </Card>

      {/* 作品审核列表模态框 */}
      <Modal
        title="作品审核管理"
        open={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        width={1200}
        footer={null}
      >
        {/* 筛选和搜索 */}
        <div className="mb-4 flex gap-4">
          <Select
            placeholder="筛选状态"
            style={{ width: 120 }}
            allowClear
            value={filterStatus}
            onChange={setFilterStatus}
          >
            <Option value={0}>待审核</Option>
            <Option value={1}>已通过</Option>
            <Option value={2}>已拒绝</Option>
          </Select>
          <Input
            placeholder="搜索活动名称、作品标题或用户名"
            prefix={<SearchOutlined />}
            style={{ width: 300 }}
            value={searchKeyword}
            onChange={(e) => setSearchKeyword(e.target.value)}
          />
        </div>

        <Table
          columns={columns}
          dataSource={filteredWorks}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
          scroll={{ x: 800 }}
        />
      </Modal>

      {/* 审核模态框 */}
      <Modal
        title={`${reviewAction === 'approve' ? '通过' : '拒绝'}审核`}
        open={reviewModalVisible}
        onOk={handleReview}
        onCancel={() => {
          setReviewModalVisible(false);
          setSelectedWork(null);
          setReviewReason('');
        }}
        okText="确认"
        cancelText="取消"
      >
        <div className="space-y-4">
          <div>
            <strong>作品标题：</strong>{selectedWork?.work?.title}
          </div>
          <div>
            <strong>活动名称：</strong>{selectedWork?.activity?.name}
          </div>
          <div>
            <strong>提交用户：</strong>{selectedWork?.user?.nickName}
          </div>
          <div>
            <label className="block text-sm font-medium mb-2">
              {reviewAction === 'approve' ? '通过' : '拒绝'}理由：
            </label>
            <TextArea
              rows={4}
              placeholder={`请输入${reviewAction === 'approve' ? '通过' : '拒绝'}理由...`}
              value={reviewReason}
              onChange={(e) => setReviewReason(e.target.value)}
            />
          </div>
        </div>
      </Modal>
    </>
  );
}
