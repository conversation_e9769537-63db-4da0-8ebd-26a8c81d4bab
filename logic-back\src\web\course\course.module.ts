import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';


// 实体

import { TaskTemplate } from './domain/entities/teaching/task-template.entity';

import { CourseTeachingRecord } from './domain/entities/teaching/course-teaching-record.entity';
import { CourseTag } from './domain/entities/marketplace/course-tag.entity';
import { CourseSeriesTag } from './domain/entities/marketplace/course-series-tag.entity';


// 控制器

import { TeachingController } from './controller/teaching.controller';
import { MarketplaceController } from './controller/marketplace.controller';

// 服务
import { ManagementService } from './application/services/management/management.service';
import { TeachingService } from './application/services/teaching/teaching.service';

// 锁管理相关 - 使用 payment 模块的分布式锁
import { LockManager } from '../../payment/lock/lock.manager';
import { DistributedLock } from '../../payment/lock/distributed.lock';
import { OptimisticLock } from '../../payment/lock/optimistic.lock';
import { PessimisticLock } from '../../payment/lock/pessimistic.lock';
import { PaymentConfigService } from '../../payment/config/payment-config.service';
import { RedisModule } from '../../util/database/redis/redis.module';
import { DatabaseConfigModule } from '../../util/database/config/database-config.module';
import { YamlModule } from '../../util/yaml/yaml.module';
import lockConfig from './config/lock.config';

import { TaskSelfAssessmentItem } from 'src/util/database/mysql/task_self_assessment_item/entities/task_self_assessment_item.entity';
import { TeacherTask } from 'src/util/database/mysql/teacher_task/entities/teacher_task.entity';
import { TeacherTaskAssignment } from 'src/util/database/mysql/teacher_task_assignment/entities/teacher_task_assignment.entity';
import { HttpResponseResultModule } from '../http_response_result/http_response_result.module';
import { UserClassModule } from 'src/util/database/mysql/user_class/user_class.module';
import { UserJoinRoleModule } from 'src/util/database/mysql/user_join_role/user_join_role.module';
import { UserStudentModule } from 'src/util/database/mysql/user_student/user_student.module';
import { UserInfoModule } from 'src/util/database/mysql/user_info/user_info.module';
import { WebPointPermissionModule } from '../web_point_permission/web_point_permission.module';
import { MarketplaceService } from './application/services/marketplace/marketplace.service';
import { CourseExceptionFilter } from './filters/course-exception.filter';
import { ManagementController } from './controller/management.controller';
import { CourseSeries } from './domain/entities/management/course-series.entity';
import { CourseSettings } from './domain/entities/management/course-settings.entity';
import { Course } from './domain/entities/management/course.entity';

@Module({
  imports: [
    ConfigModule.forRoot({
      load: [() => ({
        // 注意：虽然命名为 payment，但这里只包含锁管理配置
        // 因为 payment 模块的 LockManager 期望配置在 payment.* 命名空间下
        payment: lockConfig
      })],
    }), // 加载锁管理配置（复用 payment 命名空间）
    TypeOrmModule.forFeature([
      CourseSeries,
      Course,
      TaskTemplate,
      CourseSettings,
      CourseTeachingRecord,
      CourseTag,
      CourseSeriesTag,
      TeacherTask,
      TeacherTaskAssignment,
      TaskSelfAssessmentItem,
    ]),
    HttpResponseResultModule,
    UserClassModule,
    UserStudentModule,
    UserInfoModule,
    WebPointPermissionModule,
    UserJoinRoleModule,
    RedisModule, // 添加 Redis 模块支持分布式锁
    DatabaseConfigModule, // 添加数据库配置模块
    YamlModule, // 添加 YAML 配置模块
  ],
  controllers: [
    ManagementController,
    TeachingController,
    MarketplaceController,
  ],
  providers: [
    ManagementService,
    TeachingService,
    MarketplaceService,
    CourseExceptionFilter,
    // 使用 payment 模块的锁管理器
    LockManager,
    DistributedLock,
    OptimisticLock,
    PessimisticLock,
    PaymentConfigService,
  ],
  exports: [
    ManagementService,
    TeachingService,
    MarketplaceService,
  ]
})
export class CourseModule { }