// 定义登录组件中使用的颜色变量

export const COLORS = {
  text: {
    primary: 'text-black',
    secondary: 'text-gray-700',
    accent: 'text-blue-700',
    light: 'text-gray-600',
    white: 'text-white',
    error: 'text-red-600',
    light_white: 'text-gray-200', // 白色的浅色版本，常用于描述文字
    light_primary: 'text-gray-300', // 主色的浅色版本
    blue_light: 'text-blue-200' // 浅蓝色文字
  },
  bg: {
    primary: 'bg-white',
    secondary: 'bg-gray-100',
    accent: 'bg-blue-700',
    accent_hover: 'hover:bg-blue-800',
    transparent: 'bg-transparent',
    white_dim: 'bg-white/20',
    white_dim_hover: 'hover:bg-white/30'
  },
  border: {
    primary: 'border-gray-300',
    accent: 'border-blue-400',
    white_dim: 'border-white/30',
    white_active: 'border-white/60',
    error: 'border-red-600'
  }
}; 