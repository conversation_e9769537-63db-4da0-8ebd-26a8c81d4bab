'use client'

import { useState, useEffect } from 'react';
import { Modal, Table, Tag, Button, Empty, Spin } from 'antd';
import { RechargeRecord } from '../types';
import { useRecharge } from '../hooks/useRecharge';

interface RechargeHistoryProps {
  visible: boolean;
  onClose: () => void;
}

export default function RechargeHistory({ visible, onClose }: RechargeHistoryProps) {
  const { rechargeHistory, loading, getRechargeHistory } = useRecharge();

  useEffect(() => {
    if (visible) {
      getRechargeHistory();
    }
  }, [visible, getRechargeHistory]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
        return 'success';
      case 'pending':
        return 'processing';
      case 'failed':
        return 'error';
      case 'cancelled':
        return 'error'; // 已取消显示为红色
      default:
        return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'success':
        return '已完成';
      case 'pending':
        return '处理中';
      case 'failed':
        return '失败';
      case 'cancelled':
        return '已取消';
      default:
        return '未知';
    }
  };

  const getMethodText = (method: string) => {
    switch (method) {
      case 'wechat':
        return '微信支付';
      case 'alipay':
        return '支付宝';
      case 'unionpay':
        return '银联支付';
      case 'package':
        return '套餐购买';
      default:
        return method;
    }
  };

  // 渲染单个订单卡片
  const renderOrderCard = (record: RechargeRecord) => (
    <div
      key={record.id}
      style={{
        background: '#ffffff',
        border: '1px solid #e2e8f0',
        borderRadius: '12px',
        padding: '20px',
        marginBottom: '16px',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
        transition: 'all 0.3s ease'
      }}
      onMouseEnter={(e) => {
        e.currentTarget.style.borderColor = '#667eea';
        e.currentTarget.style.boxShadow = '0 4px 12px rgba(102, 126, 234, 0.15)';
        e.currentTarget.style.transform = 'translateY(-2px)';
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.borderColor = '#e2e8f0';
        e.currentTarget.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';
        e.currentTarget.style.transform = 'translateY(0)';
      }}
    >
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
        marginBottom: '16px'
      }}>
        <div>
          <div style={{
            fontSize: '16px',
            fontWeight: '600',
            color: '#1e293b',
            marginBottom: '4px'
          }}>
            {record.packageName || '套餐购买'}
          </div>
          <div style={{
            fontSize: '12px',
            color: '#64748b',
            fontFamily: 'Monaco, Menlo, monospace'
          }}>
            订单号: {record.id}
          </div>
        </div>
        <Tag color={getStatusColor(record.status)} style={{
          borderRadius: '6px',
          fontWeight: '500',
          fontSize: '12px'
        }}>
          {getStatusText(record.status)}
        </Tag>
      </div>

      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(180px, 1fr))',
        gap: '12px'
      }}
      className="order-details-grid"
      >
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          padding: '8px 12px',
          background: '#f8fafc',
          borderRadius: '8px'
        }}>
          <span style={{ fontSize: '13px', color: '#64748b', fontWeight: '500' }}>
            支付金额
          </span>
          <span style={{
            fontSize: '14px',
            color: '#059669',
            fontWeight: '700'
          }}>
            ¥{record.amount}
          </span>
        </div>
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          padding: '8px 12px',
          background: '#f8fafc',
          borderRadius: '8px'
        }}>
          <span style={{ fontSize: '13px', color: '#64748b', fontWeight: '500' }}>
            支付方式
          </span>
          <span style={{ fontSize: '13px', color: '#1e293b', fontWeight: '600' }}>
            {getMethodText(record.method)}
          </span>
        </div>
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          padding: '8px 12px',
          background: '#f8fafc',
          borderRadius: '8px'
        }}>
          <span style={{ fontSize: '13px', color: '#64748b', fontWeight: '500' }}>
            创建时间
          </span>
          <span style={{ fontSize: '13px', color: '#1e293b', fontWeight: '600' }}>
            {new Date(record.createTime).toLocaleString('zh-CN')}
          </span>
        </div>
        {record.completeTime && (
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            padding: '8px 12px',
            background: '#f8fafc',
            borderRadius: '8px'
          }}>
            <span style={{ fontSize: '13px', color: '#64748b', fontWeight: '500' }}>
              完成时间
            </span>
            <span style={{ fontSize: '13px', color: '#1e293b', fontWeight: '600' }}>
              {new Date(record.completeTime).toLocaleString('zh-CN')}
            </span>
          </div>
        )}
      </div>
    </div>
  );

  return (
    <Modal
      title="购买记录"
      open={visible}
      onCancel={onClose}
      footer={null}
      width="90%"
      style={{
        maxWidth: '1000px',
        top: '20px'
      }}
      className="recharge-history-modal"
    >
      <div className="history-content">
        {loading ? (
          <div style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            padding: '3rem',
            color: '#64748b'
          }}>
            <Spin size="large" />
            <p style={{ marginTop: '1rem', fontSize: '0.875rem' }}>
              加载购买记录中...
            </p>
          </div>
        ) : rechargeHistory.length > 0 ? (
          <div style={{
            maxHeight: 'calc(80vh - 200px)',
            minHeight: '300px',
            overflowY: 'auto',
            paddingRight: '8px'
          }}>
            <div>
              {rechargeHistory.map(record => renderOrderCard(record))}
            </div>
            {rechargeHistory.length > 6 && (
              <div style={{
                textAlign: 'center',
                padding: '16px',
                color: '#64748b',
                fontSize: '14px',
                borderTop: '1px solid #e2e8f0',
                marginTop: '16px'
              }}>
                共 {rechargeHistory.length} 条记录
              </div>
            )}
          </div>
        ) : (
          <Empty
            description="暂无购买记录"
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        )}
      </div>

      <style jsx>{`
        .history-content {
          min-height: 400px;
          max-height: calc(80vh - 100px);
        }

        :global(.recharge-history-modal) {
          max-height: 90vh;
        }

        :global(.recharge-history-modal .ant-modal-content) {
          max-height: 90vh;
          overflow: hidden;
          display: flex;
          flex-direction: column;
        }

        :global(.recharge-history-modal .ant-modal-header) {
          border-bottom: 1px solid #e2e8f0;
          padding: 20px 24px 16px;
          flex-shrink: 0;
        }

        :global(.recharge-history-modal .ant-modal-title) {
          font-size: 18px;
          font-weight: 600;
          color: #1e293b;
        }

        :global(.recharge-history-modal .ant-modal-body) {
          padding: 24px;
          flex: 1;
          overflow: hidden;
        }

        :global(.recharge-history-modal .ant-empty) {
          padding: 3rem 1rem;
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
          :global(.recharge-history-modal) {
            margin: 0 !important;
            max-width: 100% !important;
            width: 100% !important;
            height: 100vh !important;
            max-height: 100vh !important;
            top: 0 !important;
          }

          :global(.recharge-history-modal .ant-modal-content) {
            height: 100vh;
            max-height: 100vh;
            border-radius: 0;
          }

          :global(.recharge-history-modal .ant-modal-header) {
            padding: 16px 20px 12px;
          }

          :global(.recharge-history-modal .ant-modal-body) {
            padding: 16px 20px;
          }

          .history-content {
            min-height: calc(100vh - 120px);
            max-height: calc(100vh - 120px);
          }
        }

        @media (max-width: 480px) {
          :global(.recharge-history-modal .ant-modal-header) {
            padding: 12px 16px 8px;
          }

          :global(.recharge-history-modal .ant-modal-body) {
            padding: 12px 16px;
          }

          :global(.recharge-history-modal .ant-modal-title) {
            font-size: 16px;
          }

          .order-details-grid {
            grid-template-columns: 1fr !important;
            gap: 8px !important;
          }
        }

        /* 订单卡片响应式 */
        @media (max-width: 600px) {
          .order-details-grid {
            grid-template-columns: 1fr !important;
          }
        }

        @media (max-width: 900px) and (min-width: 601px) {
          .order-details-grid {
            grid-template-columns: repeat(2, 1fr) !important;
          }
        }
      `}</style>
    </Modal>
  );
}
