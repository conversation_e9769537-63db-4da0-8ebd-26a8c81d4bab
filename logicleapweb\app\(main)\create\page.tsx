'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Code, Image, MessageSquare } from 'lucide-react'
import AIBackground from '@/components/AIBackground'
import { useRouter, usePathname } from 'next/navigation'
import { useSelector } from 'react-redux'
import { RootState } from '@/lib/store'
import { cardStyles, buttonStyles } from '@/lib/styles'
import LoginDialog from '@/components/login-dialog'
import { GetNotification } from 'logic-common/dist/components/Notification';

export default function Create() {
  const [mounted, setMounted] = useState(false)
  const router = useRouter()
  const pathname = usePathname()
  const isLoggedIn = useSelector((state: RootState) => state.user.userState.isLoggedIn)
  const [showLoginDialog, setShowLoginDialog] = useState(false)
  const [notificationManager, setNotificationManager] = useState<any>(null)

  useEffect(() => {
    setMounted(true)
    // 在客户端获取通知管理器实例
    const manager = GetNotification();
    setNotificationManager(manager);
  }, [])

  const handleCreateClick = (toolName: string) => {
    if (!isLoggedIn) {
            const redirectUrl = pathname;
      router.push(`/login?redirect=${encodeURIComponent(redirectUrl)}`);
      return
    }
    
    if (toolName === "图形化编程") {
      router.push(`/loading?to=/logicleap&message=正在启动图形化编程环境...`)
    } else if (toolName === "节点式开发" || toolName === "AI IDE") {
      if (notificationManager) {
        notificationManager.info('正在开发中，敬请期待');
      }
    }
  }

  if (!mounted) {
    return null // 或者返回一个加载状态
  }

  const tools = [
    { name: "图形化编程", description: "通过简单的图像化编程快速实现你的创意", icon: Code },
    { name: "节点式开发", description: "使用节点式开发工具，进阶学习人工智能", icon: Image },
    { name: "AI IDE", description: "使用AI IDE，开发AI应用", icon: MessageSquare },
  ]

  return (
    <div className="container mx-auto px-4 relative min-h-screen py-16">
      <div className="absolute inset-0 z-[-1]">
        <AIBackground />
      </div>

      <div className="max-w-6xl mx-auto">
        <motion.h1
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-4xl font-bold text-center text-[#4766C2] mb-16"
        >
          创造你的 AI 作品
        </motion.h1>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="grid md:grid-cols-3 gap-8 mb-24"
        >
          {tools.map((tool, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{
                opacity: 1,
                scale: 1,
                transition: { delay: index * 0.2 }
              }}
              whileHover={{ scale: 1.02 }}
              className={cardStyles.custom({
                isGradient: false,
                isCentered: true,
                isHoverable: true,
                padding: 'p-8',
                scale: '102'
              })}
            >
              <tool.icon className="mx-auto text-[#4766C2]" size={48} />
              <h2 className="text-2xl font-semibold mt-6 mb-4">{tool.name}</h2>
              <p className="text-gray-600 mb-6">{tool.description}</p>
              <button 
                onClick={() => handleCreateClick(tool.name)}
                className={`${buttonStyles.primary} px-6 py-2.5`}
              >
                开始创作
              </button>
            </motion.div>
          ))}
        </motion.div>

        <motion.section
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          className={`${cardStyles.gradientCentered()} max-w-3xl mx-auto`}
        >
          <h2 className="text-3xl font-bold text-[#4766C2] mb-6">展示你的创意</h2>
          <p className="text-xl text-gray-600 mb-8">将你的AI作品分享给世界，获得反馈和灵感</p>
          <button className={`${buttonStyles.primary} px-8 py-3`}>
            上传作品
          </button>
        </motion.section>

        {mounted && showLoginDialog && (
          <LoginDialog 
            isOpen={showLoginDialog}
            onClose={() => setShowLoginDialog(false)}
            onSuccess={() => {
              setShowLoginDialog(false)
            }}
          />
        )}
      </div>
    </div>
  )
}

