2025-08-03 19:36:07.275 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (D:\\logicleap\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (D:\\logicleap\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (D:\\logicleap\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (D:\\logicleap\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (D:\\logicleap\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (D:\\logicleap\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (D:\\logicleap\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (D:\\logicleap\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (D:\\logicleap\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-08-03 19:36:07.338 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:36:07.339 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:36:07.339 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:36:07.340 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:36:07.341 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:36:07.341 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:36:07.341 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:36:07.342 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:36:07.342 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:36:07.342 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:36:07.342 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:36:07.343 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:36:07.343 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:36:07.343 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:36:07.344 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:36:07.344 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:36:07.344 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:36:07.344 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:36:07.344 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:36:07.345 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:38:20.201 [ERROR] [GlobalExceptionFilter] GET /api/user-point/total - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (D:\logicleap\logic-back\src\web\router_guard\router-guard.service.ts:127:15)
    at ApiAuthGuard.canActivate (D:\logicleap\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (D:\logicleap\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (D:\logicleap\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (D:\logicleap\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (D:\logicleap\logic-back\node_modules\router\index.js:435:11)
2025-08-03 19:38:20.202 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY"}
{"url":"/api/user-point/total","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"登录已过期","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T11:38:20.202Z"}
2025-08-03 19:38:20.222 [ERROR] [GlobalExceptionFilter] GET /api/web/announcement/publishedIdsByTarget - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (D:\logicleap\logic-back\src\web\router_guard\router-guard.service.ts:127:15)
    at ApiAuthGuard.canActivate (D:\logicleap\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (D:\logicleap\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (D:\logicleap\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (D:\logicleap\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (D:\logicleap\logic-back\node_modules\router\index.js:435:11)
2025-08-03 19:38:20.223 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY"}
{"url":"/api/web/announcement/publishedIdsByTarget","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"登录已过期","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T11:38:20.222Z"}
2025-08-03 19:38:20.228 [ERROR] [GlobalExceptionFilter] GET /api/user-point/packages?userId=2096 - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (D:\logicleap\logic-back\src\web\router_guard\router-guard.service.ts:127:15)
    at ApiAuthGuard.canActivate (D:\logicleap\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (D:\logicleap\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (D:\logicleap\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (D:\logicleap\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (D:\logicleap\logic-back\node_modules\router\index.js:435:11)
2025-08-03 19:38:20.228 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY"}
{"url":"/api/user-point/packages?userId=2096","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"登录已过期","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T11:38:20.228Z"}
2025-08-03 19:38:20.252 [ERROR] [GlobalExceptionFilter] POST /api/web/announcement/list - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (D:\logicleap\logic-back\src\web\router_guard\router-guard.service.ts:127:15)
    at ApiAuthGuard.canActivate (D:\logicleap\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (D:\logicleap\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (D:\logicleap\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (D:\logicleap\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (D:\logicleap\logic-back\node_modules\router\index.js:435:11)
2025-08-03 19:38:20.253 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY"}
{"url":"/api/web/announcement/list","method":"POST","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"登录已过期","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T11:38:20.252Z"}
2025-08-03 19:38:20.263 [ERROR] [GlobalExceptionFilter] GET /api/teacher-task/list?studentId=2096&roleId=4&page=1&size=50 - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (D:\logicleap\logic-back\src\web\router_guard\router-guard.service.ts:127:15)
    at ApiAuthGuard.canActivate (D:\logicleap\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (D:\logicleap\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (D:\logicleap\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (D:\logicleap\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (D:\logicleap\logic-back\node_modules\router\index.js:435:11)
2025-08-03 19:38:20.265 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY"}
{"url":"/api/teacher-task/list?studentId=2096&roleId=4&page=1&size=50","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"登录已过期","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T11:38:20.264Z"}
2025-08-03 19:38:20.275 [ERROR] [GlobalExceptionFilter] POST /api/web/announcement/list - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (D:\logicleap\logic-back\src\web\router_guard\router-guard.service.ts:127:15)
    at ApiAuthGuard.canActivate (D:\logicleap\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (D:\logicleap\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (D:\logicleap\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (D:\logicleap\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (D:\logicleap\logic-back\node_modules\router\index.js:435:11)
2025-08-03 19:38:20.277 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY"}
{"url":"/api/web/announcement/list","method":"POST","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"登录已过期","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T11:38:20.276Z"}
2025-08-03 19:38:20.287 [ERROR] [GlobalExceptionFilter] GET /api/user-point/total - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (D:\logicleap\logic-back\src\web\router_guard\router-guard.service.ts:127:15)
    at ApiAuthGuard.canActivate (D:\logicleap\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (D:\logicleap\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (D:\logicleap\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (D:\logicleap\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (D:\logicleap\logic-back\node_modules\router\index.js:435:11)
2025-08-03 19:38:20.289 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY"}
{"url":"/api/user-point/total","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"登录已过期","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T11:38:20.288Z"}
2025-08-03 19:38:20.298 [ERROR] [GlobalExceptionFilter] GET /api/web/announcement/publishedIdsByTarget - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (D:\logicleap\logic-back\src\web\router_guard\router-guard.service.ts:127:15)
    at ApiAuthGuard.canActivate (D:\logicleap\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (D:\logicleap\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (D:\logicleap\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (D:\logicleap\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (D:\logicleap\logic-back\node_modules\router\index.js:435:11)
2025-08-03 19:38:20.299 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY"}
{"url":"/api/web/announcement/publishedIdsByTarget","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"登录已过期","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T11:38:20.298Z"}
2025-08-03 19:38:20.310 [ERROR] [GlobalExceptionFilter] GET /api/user-point/packages?userId=2096 - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (D:\logicleap\logic-back\src\web\router_guard\router-guard.service.ts:127:15)
    at ApiAuthGuard.canActivate (D:\logicleap\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (D:\logicleap\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (D:\logicleap\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (D:\logicleap\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (D:\logicleap\logic-back\node_modules\router\index.js:435:11)
2025-08-03 19:38:20.321 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY"}
{"url":"/api/user-point/packages?userId=2096","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"登录已过期","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T11:38:20.310Z"}
2025-08-03 19:38:20.329 [ERROR] [GlobalExceptionFilter] GET /api/user-point/packages?userId=2096 - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (D:\logicleap\logic-back\src\web\router_guard\router-guard.service.ts:127:15)
    at ApiAuthGuard.canActivate (D:\logicleap\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (D:\logicleap\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (D:\logicleap\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (D:\logicleap\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (D:\logicleap\logic-back\node_modules\router\index.js:435:11)
2025-08-03 19:38:20.329 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY"}
{"url":"/api/user-point/packages?userId=2096","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"登录已过期","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T11:38:20.329Z"}
2025-08-03 19:38:20.347 [ERROR] [GlobalExceptionFilter] GET /api/user-point/packages?userId=2096 - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (D:\logicleap\logic-back\src\web\router_guard\router-guard.service.ts:127:15)
    at ApiAuthGuard.canActivate (D:\logicleap\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (D:\logicleap\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (D:\logicleap\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (D:\logicleap\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (D:\logicleap\logic-back\node_modules\router\index.js:435:11)
2025-08-03 19:38:20.347 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY"}
{"url":"/api/user-point/packages?userId=2096","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"登录已过期","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T11:38:20.347Z"}
2025-08-03 19:38:21.176 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:38:21.661 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 19:38:21.661 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T11:38:21.661Z"}
2025-08-03 19:38:22.162 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:38:23.164 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:38:24.159 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:38:25.168 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:38:25.240 [ERROR] [GlobalExceptionFilter] GET /api/web-carousel/active - Connection lost: The server closed the connection. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY"}
Error: Connection lost: The server closed the connection.
    at Socket.<anonymous> (D:\logicleap\logic-back\node_modules\mysql2\lib\base\connection.js:113:31)
    at Socket.emit (node:events:524:28)
    at TCP.<anonymous> (node:net:343:12)
2025-08-03 19:38:25.241 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY"}
{"url":"/api/web-carousel/active","method":"GET","statusCode":500,"message":"Connection lost: The server closed the connection.","details":{"name":"Error","stack":"Error: Connection lost: The server closed the connection.\n    at Socket.<anonymous> (D:\\logicleap\\logic-back\\node_modules\\mysql2\\lib\\base\\connection.js:113:31)\n    at Socket.emit (node:events:524:28)\n    at TCP.<anonymous> (node:net:343:12)"},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T11:38:25.241Z"}
2025-08-03 19:38:26.162 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:38:27.163 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:38:28.165 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:38:29.164 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:38:30.165 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:38:31.168 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:38:32.163 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:38:33.164 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:38:34.167 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:38:35.165 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:38:36.164 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:38:37.181 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:38:38.165 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:38:39.186 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:38:40.164 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:38:41.166 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:38:42.163 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:38:43.165 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:38:44.164 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:38:45.165 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:38:46.165 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:38:47.163 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:38:48.164 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:38:49.163 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:38:50.164 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:38:51.164 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:38:52.165 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:38:53.165 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:38:54.170 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:38:55.175 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:38:56.164 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:38:57.164 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:38:58.163 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:38:59.163 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:39:00.164 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:39:01.163 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:39:02.164 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:39:03.163 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:39:04.164 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:39:05.164 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:39:06.166 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:39:07.164 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:39:08.164 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:39:09.164 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:39:10.170 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:39:11.163 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:39:12.162 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:39:13.162 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:39:14.163 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:39:15.163 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:39:16.163 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:39:17.166 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:39:18.164 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:39:19.163 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:39:20.163 [ERROR] [GlobalExceptionFilter] POST /api/web/announcement/list - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (D:\logicleap\logic-back\src\web\router_guard\router-guard.service.ts:127:15)
    at ApiAuthGuard.canActivate (D:\logicleap\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (D:\logicleap\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (D:\logicleap\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (D:\logicleap\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (D:\logicleap\logic-back\node_modules\router\index.js:435:11)
2025-08-03 19:39:20.165 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY"}
{"url":"/api/web/announcement/list","method":"POST","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"登录已过期","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T11:39:20.164Z"}
2025-08-03 19:39:20.173 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:39:21.165 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:39:21.664 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 19:39:21.664 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T11:39:21.664Z"}
2025-08-03 19:39:22.163 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:39:23.164 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:39:24.172 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:39:25.164 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:39:26.173 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:39:27.164 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:39:28.164 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:39:29.163 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:39:30.164 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:39:31.164 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:39:32.163 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:39:33.164 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:39:34.164 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:39:35.164 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:39:36.165 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:39:37.164 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:39:38.163 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:39:39.163 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:39:40.163 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:39:41.165 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:39:42.168 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:39:43.164 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:39:44.169 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:39:45.163 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:39:46.163 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:39:47.163 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:39:48.163 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:39:49.165 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:39:50.163 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:39:51.168 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:39:52.163 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:39:53.168 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:39:54.163 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:39:55.163 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:39:56.163 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:39:57.162 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:39:58.168 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:39:59.162 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:40:00.169 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:40:01.162 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:40:02.169 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:40:03.162 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:40:04.169 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:40:05.161 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:40:06.168 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:40:07.162 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:40:08.169 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:40:09.162 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:40:10.169 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:40:11.165 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:40:12.187 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:40:13.163 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:40:14.168 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:40:15.163 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:40:16.174 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:40:17.169 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:40:18.163 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:40:19.163 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:40:20.162 [ERROR] [GlobalExceptionFilter] POST /api/web/announcement/list - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (D:\logicleap\logic-back\src\web\router_guard\router-guard.service.ts:127:15)
    at ApiAuthGuard.canActivate (D:\logicleap\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (D:\logicleap\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (D:\logicleap\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (D:\logicleap\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (D:\logicleap\logic-back\node_modules\router\index.js:435:11)
2025-08-03 19:40:20.162 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY"}
{"url":"/api/web/announcement/list","method":"POST","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"登录已过期","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T11:40:20.162Z"}
2025-08-03 19:40:20.168 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:40:21.164 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:40:21.678 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 19:40:21.681 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T11:40:21.680Z"}
2025-08-03 19:40:22.162 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:40:23.163 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:40:24.162 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:40:25.163 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:40:26.162 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:40:27.165 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:40:28.163 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:40:29.211 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY","stack":[null]}
2025-08-03 19:40:29.322 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 19:40:29.322 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T11:40:29.322Z"}
2025-08-03 19:40:29.328 [ERROR] [GlobalExceptionFilter] GET /api/user-school/listByUserId - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (D:\logicleap\logic-back\src\web\router_guard\router-guard.service.ts:127:15)
    at ApiAuthGuard.canActivate (D:\logicleap\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (D:\logicleap\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (D:\logicleap\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (D:\logicleap\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (D:\logicleap\logic-back\node_modules\router\index.js:435:11)
2025-08-03 19:40:29.329 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY"}
{"url":"/api/user-school/listByUserId","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"登录已过期","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T11:40:29.328Z"}
2025-08-03 19:40:29.334 [ERROR] [GlobalExceptionFilter] GET /api/teacher-task/list?teacherId=2096&page=1&size=50&orderBy=createTime:DESC - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (D:\logicleap\logic-back\src\web\router_guard\router-guard.service.ts:127:15)
    at ApiAuthGuard.canActivate (D:\logicleap\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (D:\logicleap\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (D:\logicleap\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (D:\logicleap\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (D:\logicleap\logic-back\node_modules\router\index.js:435:11)
2025-08-03 19:40:29.334 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY"}
{"url":"/api/teacher-task/list?teacherId=2096&page=1&size=50&orderBy=createTime:DESC","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"登录已过期","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T11:40:29.334Z"}
2025-08-03 19:40:29.338 [ERROR] [GlobalExceptionFilter] GET /api/user/srch/templates/current/2096 - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (D:\logicleap\logic-back\src\web\router_guard\router-guard.service.ts:127:15)
    at ApiAuthGuard.canActivate (D:\logicleap\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (D:\logicleap\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (D:\logicleap\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (D:\logicleap\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (D:\logicleap\logic-back\node_modules\router\index.js:435:11)
2025-08-03 19:40:29.338 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY"}
{"url":"/api/user/srch/templates/current/2096","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"登录已过期","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T11:40:29.338Z"}
2025-08-03 19:40:29.345 [ERROR] [GlobalExceptionFilter] GET /api/user-school/listByUserId - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (D:\logicleap\logic-back\src\web\router_guard\router-guard.service.ts:127:15)
    at ApiAuthGuard.canActivate (D:\logicleap\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (D:\logicleap\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (D:\logicleap\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (D:\logicleap\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (D:\logicleap\logic-back\node_modules\router\index.js:435:11)
2025-08-03 19:40:29.346 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY"}
{"url":"/api/user-school/listByUserId","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"登录已过期","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T11:40:29.345Z"}
2025-08-03 19:40:29.354 [ERROR] [GlobalExceptionFilter] GET /api/teacher-task/list?teacherId=2096&page=1&size=50&orderBy=createTime:DESC - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (D:\logicleap\logic-back\src\web\router_guard\router-guard.service.ts:127:15)
    at ApiAuthGuard.canActivate (D:\logicleap\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (D:\logicleap\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (D:\logicleap\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (D:\logicleap\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (D:\logicleap\logic-back\node_modules\router\index.js:435:11)
2025-08-03 19:40:29.355 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY"}
{"url":"/api/teacher-task/list?teacherId=2096&page=1&size=50&orderBy=createTime:DESC","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"登录已过期","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T11:40:29.355Z"}
2025-08-03 19:40:29.359 [ERROR] [GlobalExceptionFilter] GET /api/user/srch/templates/current/2096 - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (D:\logicleap\logic-back\src\web\router_guard\router-guard.service.ts:127:15)
    at ApiAuthGuard.canActivate (D:\logicleap\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (D:\logicleap\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (D:\logicleap\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (D:\logicleap\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (D:\logicleap\logic-back\node_modules\router\index.js:435:11)
2025-08-03 19:40:29.360 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY"}
{"url":"/api/user/srch/templates/current/2096","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"登录已过期","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T11:40:29.360Z"}
2025-08-03 19:40:29.363 [ERROR] [GlobalExceptionFilter] GET /api/user/class/teacher/2096/stats - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (D:\logicleap\logic-back\src\web\router_guard\router-guard.service.ts:127:15)
    at ApiAuthGuard.canActivate (D:\logicleap\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (D:\logicleap\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (D:\logicleap\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (D:\logicleap\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (D:\logicleap\logic-back\node_modules\router\index.js:435:11)
2025-08-03 19:40:29.364 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY"}
{"url":"/api/user/class/teacher/2096/stats","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"登录已过期","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T11:40:29.364Z"}
2025-08-03 19:40:29.373 [ERROR] [GlobalExceptionFilter] GET /api/user-point/total - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (D:\logicleap\logic-back\src\web\router_guard\router-guard.service.ts:127:15)
    at ApiAuthGuard.canActivate (D:\logicleap\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (D:\logicleap\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (D:\logicleap\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (D:\logicleap\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (D:\logicleap\logic-back\node_modules\router\index.js:435:11)
2025-08-03 19:40:29.374 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY"}
{"url":"/api/user-point/total","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"登录已过期","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T11:40:29.373Z"}
2025-08-03 19:40:32.427 [ERROR] [GlobalExceptionFilter] GET /api/user/srch/templates/current/2096 - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (D:\logicleap\logic-back\src\web\router_guard\router-guard.service.ts:127:15)
    at ApiAuthGuard.canActivate (D:\logicleap\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (D:\logicleap\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (D:\logicleap\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (D:\logicleap\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (D:\logicleap\logic-back\node_modules\router\index.js:435:11)
2025-08-03 19:40:32.427 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY"}
{"url":"/api/user/srch/templates/current/2096","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"登录已过期","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T11:40:32.427Z"}
2025-08-03 19:40:32.431 [ERROR] [GlobalExceptionFilter] GET /api/user/srch/templates/folders/list - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (D:\logicleap\logic-back\src\web\router_guard\router-guard.service.ts:127:15)
    at ApiAuthGuard.canActivate (D:\logicleap\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (D:\logicleap\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (D:\logicleap\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (D:\logicleap\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (D:\logicleap\logic-back\node_modules\router\index.js:435:11)
2025-08-03 19:40:32.431 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY"}
{"url":"/api/user/srch/templates/folders/list","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"登录已过期","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T11:40:32.431Z"}
2025-08-03 19:40:35.266 [ERROR] [GlobalExceptionFilter] GET /api/v1/course-management/my-series?page=1&pageSize=20 - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (D:\logicleap\logic-back\src\web\router_guard\router-guard.service.ts:127:15)
    at ApiAuthGuard.canActivate (D:\logicleap\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (D:\logicleap\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (D:\logicleap\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (D:\logicleap\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (D:\logicleap\logic-back\node_modules\router\index.js:435:11)
2025-08-03 19:40:35.267 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY"}
{"url":"/api/v1/course-management/my-series?page=1&pageSize=20","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"登录已过期","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T11:40:35.267Z"}
2025-08-03 19:40:35.271 [ERROR] [GlobalExceptionFilter] GET /api/v1/course-management/my-series?page=1&pageSize=20 - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (D:\logicleap\logic-back\src\web\router_guard\router-guard.service.ts:127:15)
    at ApiAuthGuard.canActivate (D:\logicleap\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (D:\logicleap\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (D:\logicleap\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (D:\logicleap\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (D:\logicleap\logic-back\node_modules\router\index.js:435:11)
2025-08-03 19:40:35.272 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY"}
{"url":"/api/v1/course-management/my-series?page=1&pageSize=20","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"登录已过期","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T11:40:35.271Z"}
2025-08-03 19:40:36.596 [ERROR] [GlobalExceptionFilter] GET /api/v1/course-management/my-series?page=1&pageSize=20 - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (D:\logicleap\logic-back\src\web\router_guard\router-guard.service.ts:127:15)
    at ApiAuthGuard.canActivate (D:\logicleap\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (D:\logicleap\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (D:\logicleap\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (D:\logicleap\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (D:\logicleap\logic-back\node_modules\router\index.js:435:11)
2025-08-03 19:40:36.597 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":7684,"hostname":"QQY"}
{"url":"/api/v1/course-management/my-series?page=1&pageSize=20","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"登录已过期","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T11:40:36.596Z"}
2025-08-03 19:43:26.292 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (D:\\logicleap\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (D:\\logicleap\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (D:\\logicleap\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (D:\\logicleap\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (D:\\logicleap\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (D:\\logicleap\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (D:\\logicleap\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (D:\\logicleap\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (D:\\logicleap\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-08-03 19:43:26.453 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY","stack":[null]}
2025-08-03 19:43:26.454 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY","stack":[null]}
2025-08-03 19:43:26.457 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY","stack":[null]}
2025-08-03 19:43:26.458 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY","stack":[null]}
2025-08-03 19:43:26.458 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY","stack":[null]}
2025-08-03 19:43:26.459 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY","stack":[null]}
2025-08-03 19:43:26.460 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY","stack":[null]}
2025-08-03 19:43:26.460 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY","stack":[null]}
2025-08-03 19:43:26.461 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY","stack":[null]}
2025-08-03 19:43:26.461 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY","stack":[null]}
2025-08-03 19:43:26.461 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY","stack":[null]}
2025-08-03 19:43:26.462 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY","stack":[null]}
2025-08-03 19:43:26.463 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY","stack":[null]}
2025-08-03 19:43:26.464 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY","stack":[null]}
2025-08-03 19:43:26.465 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY","stack":[null]}
2025-08-03 19:43:26.466 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY","stack":[null]}
2025-08-03 19:43:26.466 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY","stack":[null]}
2025-08-03 19:43:26.467 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY","stack":[null]}
2025-08-03 19:43:26.467 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY","stack":[null]}
2025-08-03 19:43:26.468 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY","stack":[null]}
2025-08-03 19:43:27.454 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY","stack":[null]}
2025-08-03 19:43:28.268 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY","stack":[null]}
2025-08-03 19:43:29.262 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY","stack":[null]}
2025-08-03 19:43:30.261 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY","stack":[null]}
2025-08-03 19:43:31.266 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY","stack":[null]}
2025-08-03 19:43:32.266 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY","stack":[null]}
2025-08-03 19:43:33.260 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY","stack":[null]}
2025-08-03 19:43:34.264 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY","stack":[null]}
2025-08-03 19:43:35.266 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY","stack":[null]}
2025-08-03 19:43:36.264 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY","stack":[null]}
2025-08-03 19:43:37.267 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY","stack":[null]}
2025-08-03 19:43:38.261 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY","stack":[null]}
2025-08-03 19:43:39.265 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY","stack":[null]}
2025-08-03 19:43:40.265 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY","stack":[null]}
2025-08-03 19:43:41.262 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY","stack":[null]}
2025-08-03 19:43:42.260 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY","stack":[null]}
2025-08-03 19:43:43.524 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY","stack":[null]}
2025-08-03 19:43:44.266 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY","stack":[null]}
2025-08-03 19:43:45.285 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY","stack":[null]}
2025-08-03 19:43:46.276 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY","stack":[null]}
2025-08-03 19:43:47.297 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY","stack":[null]}
2025-08-03 19:43:48.265 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY","stack":[null]}
2025-08-03 19:43:49.306 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY","stack":[null]}
2025-08-03 19:43:50.266 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY","stack":[null]}
2025-08-03 19:43:51.264 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY","stack":[null]}
2025-08-03 19:43:52.264 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY","stack":[null]}
2025-08-03 19:43:53.261 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY","stack":[null]}
2025-08-03 19:43:54.266 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY","stack":[null]}
2025-08-03 19:43:55.261 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY","stack":[null]}
2025-08-03 19:43:56.265 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY","stack":[null]}
2025-08-03 19:43:57.264 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY","stack":[null]}
2025-08-03 19:43:58.264 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY","stack":[null]}
2025-08-03 19:43:59.260 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY","stack":[null]}
2025-08-03 19:44:00.261 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY","stack":[null]}
2025-08-03 19:44:01.266 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY","stack":[null]}
2025-08-03 19:44:02.271 [ERROR] [GlobalExceptionFilter] POST /api/web/announcement/list - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (D:\logicleap\logic-back\src\web\router_guard\router-guard.service.ts:127:15)
    at ApiAuthGuard.canActivate (D:\logicleap\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (D:\logicleap\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (D:\logicleap\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (D:\logicleap\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (D:\logicleap\logic-back\node_modules\router\index.js:435:11)
2025-08-03 19:44:02.272 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/web/announcement/list","method":"POST","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"登录已过期","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T11:44:02.272Z"}
2025-08-03 19:44:02.281 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY","stack":[null]}
2025-08-03 19:44:03.264 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY","stack":[null]}
2025-08-03 19:44:03.782 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 19:44:03.784 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T11:44:03.783Z"}
2025-08-03 19:44:04.260 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY","stack":[null]}
2025-08-03 19:44:05.263 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY","stack":[null]}
2025-08-03 19:44:06.260 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY","stack":[null]}
2025-08-03 19:44:07.271 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY","stack":[null]}
2025-08-03 19:44:08.261 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY","stack":[null]}
2025-08-03 19:44:09.269 [ERROR] [WebSocketGateway] 连接处理异常: jwt expired {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY","stack":[null]}
2025-08-03 19:44:09.894 [ERROR] [GlobalExceptionFilter] POST /api/user-auth/logout - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (D:\logicleap\logic-back\src\web\router_guard\router-guard.service.ts:127:15)
    at ApiAuthGuard.canActivate (D:\logicleap\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (D:\logicleap\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (D:\logicleap\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (D:\logicleap\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (D:\logicleap\logic-back\node_modules\router\index.js:435:11)
2025-08-03 19:44:09.896 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/user-auth/logout","method":"POST","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"登录已过期","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T11:44:09.896Z"}
2025-08-03 19:44:19.585 [ERROR] [WeixinApiUtilService] 获取AccessToken异常: Client network socket disconnected before secure TLS connection was established {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY","stack":[null]}
2025-08-03 19:44:19.585 [ERROR] [WeixinApiUtilService] 创建二维码异常: Client network socket disconnected before secure TLS connection was established {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY","stack":[null]}
2025-08-03 19:44:19.586 [ERROR] [WeixinQrCodeService] 生成二维码失败: Client network socket disconnected before secure TLS connection was established {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY","stack":[null]}
2025-08-03 19:44:19.587 [ERROR] [WeixinQrCodeService] 生成登录二维码失败: 生成二维码失败 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY","stack":[null]}
2025-08-03 19:44:19.587 [ERROR] [WebWeixinScanService] 生成二维码失败: 生成登录二维码失败 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY","stack":[null]}
2025-08-03 19:46:24.084 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 19:46:24.084 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T11:46:24.084Z"}
2025-08-03 19:46:24.112 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 19:46:24.113 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T11:46:24.112Z"}
2025-08-03 19:46:28.574 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 19:46:28.575 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T11:46:28.575Z"}
2025-08-03 19:46:29.497 [ERROR] [UserAuthController] 获取用户角色信息失败: Connection lost: The server closed the connection. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY","stack":[null]}
2025-08-03 19:47:25.558 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 19:47:25.559 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T11:47:25.559Z"}
2025-08-03 19:47:28.599 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 19:47:28.600 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T11:47:28.599Z"}
2025-08-03 19:47:53.590 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 19:47:53.590 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T11:47:53.590Z"}
2025-08-03 19:48:23.660 [ERROR] [GlobalExceptionFilter] GET /api/v1/course-management/courses/4304/task-templates/1156 - Cannot GET /api/v1/course-management/courses/4304/task-templates/1156 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/v1/course-management/courses/4304/task-templates/1156
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 19:48:23.661 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/v1/course-management/courses/4304/task-templates/1156","method":"GET","statusCode":404,"message":"Cannot GET /api/v1/course-management/courses/4304/task-templates/1156","details":{"message":"Cannot GET /api/v1/course-management/courses/4304/task-templates/1156","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T11:48:23.660Z"}
2025-08-03 19:48:28.615 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 19:48:28.615 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T11:48:28.615Z"}
2025-08-03 19:48:38.482 [ERROR] [GlobalExceptionFilter] GET /api/v1/course-management/courses/4303/task-templates/1155 - Cannot GET /api/v1/course-management/courses/4303/task-templates/1155 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/v1/course-management/courses/4303/task-templates/1155
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 19:48:38.482 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/v1/course-management/courses/4303/task-templates/1155","method":"GET","statusCode":404,"message":"Cannot GET /api/v1/course-management/courses/4303/task-templates/1155","details":{"message":"Cannot GET /api/v1/course-management/courses/4303/task-templates/1155","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T11:48:38.482Z"}
2025-08-03 19:49:29.244 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 19:49:29.244 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T11:49:29.244Z"}
2025-08-03 19:49:44.393 [ERROR] [GlobalExceptionFilter] GET /api/teacher-task/list?teacherId=2896&page=1&size=50&orderBy=createTime:DESC - Connection lost: The server closed the connection. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
QueryFailedError: Connection lost: The server closed the connection.
    at Query.onResult (D:\logicleap\logic-back\node_modules\typeorm\driver\src\driver\mysql\MysqlQueryRunner.ts:243:33)
    at PoolConnection._notifyError (D:\logicleap\logic-back\node_modules\mysql2\lib\base\connection.js:223:21)
    at Socket.<anonymous> (D:\logicleap\logic-back\node_modules\mysql2\lib\base\connection.js:119:12)
    at Socket.emit (node:events:524:28)
    at TCP.<anonymous> (node:net:343:12)
2025-08-03 19:49:44.393 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/teacher-task/list?teacherId=2896&page=1&size=50&orderBy=createTime:DESC","method":"GET","statusCode":500,"message":"Connection lost: The server closed the connection.","details":{"name":"QueryFailedError","stack":"QueryFailedError: Connection lost: The server closed the connection.\n    at Query.onResult (D:\\logicleap\\logic-back\\node_modules\\typeorm\\driver\\src\\driver\\mysql\\MysqlQueryRunner.ts:243:33)\n    at PoolConnection._notifyError (D:\\logicleap\\logic-back\\node_modules\\mysql2\\lib\\base\\connection.js:223:21)\n    at Socket.<anonymous> (D:\\logicleap\\logic-back\\node_modules\\mysql2\\lib\\base\\connection.js:119:12)\n    at Socket.emit (node:events:524:28)\n    at TCP.<anonymous> (node:net:343:12)"},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T11:49:44.393Z"}
2025-08-03 19:49:58.026 [ERROR] [GlobalExceptionFilter] GET /api/teacher-task/list?teacherId=2896&page=1&size=50&orderBy=createTime:DESC - Connection lost: The server closed the connection. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
QueryFailedError: Connection lost: The server closed the connection.
    at Query.onResult (D:\logicleap\logic-back\node_modules\typeorm\driver\src\driver\mysql\MysqlQueryRunner.ts:243:33)
    at PoolConnection._notifyError (D:\logicleap\logic-back\node_modules\mysql2\lib\base\connection.js:223:21)
    at Socket.<anonymous> (D:\logicleap\logic-back\node_modules\mysql2\lib\base\connection.js:119:12)
    at Socket.emit (node:events:524:28)
    at TCP.<anonymous> (node:net:343:12)
2025-08-03 19:49:58.026 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/teacher-task/list?teacherId=2896&page=1&size=50&orderBy=createTime:DESC","method":"GET","statusCode":500,"message":"Connection lost: The server closed the connection.","details":{"name":"QueryFailedError","stack":"QueryFailedError: Connection lost: The server closed the connection.\n    at Query.onResult (D:\\logicleap\\logic-back\\node_modules\\typeorm\\driver\\src\\driver\\mysql\\MysqlQueryRunner.ts:243:33)\n    at PoolConnection._notifyError (D:\\logicleap\\logic-back\\node_modules\\mysql2\\lib\\base\\connection.js:223:21)\n    at Socket.<anonymous> (D:\\logicleap\\logic-back\\node_modules\\mysql2\\lib\\base\\connection.js:119:12)\n    at Socket.emit (node:events:524:28)\n    at TCP.<anonymous> (node:net:343:12)"},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T11:49:58.026Z"}
2025-08-03 19:50:30.241 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 19:50:30.241 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T11:50:30.241Z"}
2025-08-03 19:51:31.243 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 19:51:31.243 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T11:51:31.243Z"}
2025-08-03 19:52:31.257 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 19:52:31.258 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T11:52:31.257Z"}
2025-08-03 19:53:31.268 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 19:53:31.268 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T11:53:31.268Z"}
2025-08-03 19:54:07.531 [ERROR] [GlobalExceptionFilter] GET /api/v1/course-management/courses/4304/task-templates/1156 - Cannot GET /api/v1/course-management/courses/4304/task-templates/1156 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/v1/course-management/courses/4304/task-templates/1156
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 19:54:07.531 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/v1/course-management/courses/4304/task-templates/1156","method":"GET","statusCode":404,"message":"Cannot GET /api/v1/course-management/courses/4304/task-templates/1156","details":{"message":"Cannot GET /api/v1/course-management/courses/4304/task-templates/1156","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T11:54:07.531Z"}
2025-08-03 19:54:11.135 [ERROR] [GlobalExceptionFilter] GET /api/v1/course-management/courses/4303/task-templates/1155 - Cannot GET /api/v1/course-management/courses/4303/task-templates/1155 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/v1/course-management/courses/4303/task-templates/1155
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 19:54:11.136 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/v1/course-management/courses/4303/task-templates/1155","method":"GET","statusCode":404,"message":"Cannot GET /api/v1/course-management/courses/4303/task-templates/1155","details":{"message":"Cannot GET /api/v1/course-management/courses/4303/task-templates/1155","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T11:54:11.135Z"}
2025-08-03 19:54:18.082 [ERROR] [GlobalExceptionFilter] GET /api/v1/course-management/courses/4304/task-templates/1156 - Cannot GET /api/v1/course-management/courses/4304/task-templates/1156 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/v1/course-management/courses/4304/task-templates/1156
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 19:54:18.082 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/v1/course-management/courses/4304/task-templates/1156","method":"GET","statusCode":404,"message":"Cannot GET /api/v1/course-management/courses/4304/task-templates/1156","details":{"message":"Cannot GET /api/v1/course-management/courses/4304/task-templates/1156","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T11:54:18.082Z"}
2025-08-03 19:54:31.286 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 19:54:31.286 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T11:54:31.286Z"}
2025-08-03 19:54:46.973 [ERROR] [GlobalExceptionFilter] GET /api/v1/course-management/courses/4304/task-templates/1156 - Cannot GET /api/v1/course-management/courses/4304/task-templates/1156 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/v1/course-management/courses/4304/task-templates/1156
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 19:54:46.973 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/v1/course-management/courses/4304/task-templates/1156","method":"GET","statusCode":404,"message":"Cannot GET /api/v1/course-management/courses/4304/task-templates/1156","details":{"message":"Cannot GET /api/v1/course-management/courses/4304/task-templates/1156","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T11:54:46.973Z"}
2025-08-03 19:55:31.298 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 19:55:31.298 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T11:55:31.298Z"}
2025-08-03 19:56:31.313 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 19:56:31.313 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T11:56:31.313Z"}
2025-08-03 19:57:32.236 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 19:57:32.237 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T11:57:32.237Z"}
2025-08-03 19:58:33.239 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 19:58:33.240 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T11:58:33.239Z"}
2025-08-03 19:59:34.246 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 19:59:34.246 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T11:59:34.246Z"}
2025-08-03 20:00:35.238 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 20:00:35.239 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T12:00:35.239Z"}
2025-08-03 20:01:36.235 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 20:01:36.235 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T12:01:36.235Z"}
2025-08-03 20:02:37.243 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 20:02:37.243 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T12:02:37.243Z"}
2025-08-03 20:03:37.266 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 20:03:37.266 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T12:03:37.266Z"}
2025-08-03 20:04:38.237 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 20:04:38.237 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T12:04:38.237Z"}
2025-08-03 20:05:39.237 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 20:05:39.237 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T12:05:39.237Z"}
2025-08-03 20:06:40.246 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 20:06:40.246 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T12:06:40.246Z"}
2025-08-03 20:07:41.236 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 20:07:41.237 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T12:07:41.236Z"}
2025-08-03 20:08:42.249 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 20:08:42.250 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T12:08:42.249Z"}
2025-08-03 20:09:43.236 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 20:09:43.236 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T12:09:43.236Z"}
2025-08-03 20:10:44.251 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 20:10:44.252 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T12:10:44.252Z"}
2025-08-03 20:11:45.240 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 20:11:45.241 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T12:11:45.240Z"}
2025-08-03 20:12:46.251 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 20:12:46.251 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T12:12:46.251Z"}
2025-08-03 20:13:47.238 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 20:13:47.239 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T12:13:47.239Z"}
2025-08-03 20:14:48.252 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 20:14:48.253 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T12:14:48.252Z"}
2025-08-03 20:15:49.266 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 20:15:49.266 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T12:15:49.266Z"}
2025-08-03 20:16:50.247 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 20:16:50.247 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T12:16:50.247Z"}
2025-08-03 20:17:50.266 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 20:17:50.267 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T12:17:50.266Z"}
2025-08-03 20:18:51.248 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 20:18:51.249 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T12:18:51.248Z"}
2025-08-03 20:19:52.251 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 20:19:52.255 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T12:19:52.251Z"}
2025-08-03 20:20:53.233 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 20:20:53.233 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T12:20:53.233Z"}
2025-08-03 20:21:54.238 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 20:21:54.239 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T12:21:54.238Z"}
2025-08-03 20:22:55.243 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 20:22:55.244 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T12:22:55.243Z"}
2025-08-03 20:23:56.242 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 20:23:56.242 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T12:23:56.242Z"}
2025-08-03 20:24:57.239 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 20:24:57.239 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T12:24:57.239Z"}
2025-08-03 20:25:58.247 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 20:25:58.247 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T12:25:58.247Z"}
2025-08-03 20:26:59.251 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 20:26:59.251 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T12:26:59.251Z"}
2025-08-03 20:28:00.250 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 20:28:00.251 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T12:28:00.250Z"}
2025-08-03 20:29:01.257 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 20:29:01.258 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T12:29:01.257Z"}
2025-08-03 20:29:04.411 [ERROR] [GlobalExceptionFilter] GET /api/v1/course-marketplace/tags?page=1&pageSize=100&status=1 - Connection lost: The server closed the connection. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
Error: Connection lost: The server closed the connection.
    at Socket.<anonymous> (D:\logicleap\logic-back\node_modules\mysql2\lib\base\connection.js:113:31)
    at Socket.emit (node:events:524:28)
    at TCP.<anonymous> (node:net:343:12)
2025-08-03 20:29:04.411 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/v1/course-marketplace/tags?page=1&pageSize=100&status=1","method":"GET","statusCode":500,"message":"Connection lost: The server closed the connection.","details":{"name":"Error","stack":"Error: Connection lost: The server closed the connection.\n    at Socket.<anonymous> (D:\\logicleap\\logic-back\\node_modules\\mysql2\\lib\\base\\connection.js:113:31)\n    at Socket.emit (node:events:524:28)\n    at TCP.<anonymous> (node:net:343:12)"},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T12:29:04.411Z"}
2025-08-03 20:30:02.247 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 20:30:02.247 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T12:30:02.247Z"}
2025-08-03 20:31:03.247 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 20:31:03.247 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T12:31:03.247Z"}
2025-08-03 20:32:04.251 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 20:32:04.252 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T12:32:04.252Z"}
2025-08-03 20:33:05.255 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 20:33:05.256 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T12:33:05.255Z"}
2025-08-03 20:34:06.242 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 20:34:06.242 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T12:34:06.242Z"}
2025-08-03 20:35:07.251 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 20:35:07.251 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T12:35:07.251Z"}
2025-08-03 20:36:08.253 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 20:36:08.253 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T12:36:08.253Z"}
2025-08-03 20:37:09.251 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 20:37:09.251 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T12:37:09.251Z"}
2025-08-03 20:38:10.247 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 20:38:10.248 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T12:38:10.248Z"}
2025-08-03 20:39:11.250 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 20:39:11.250 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T12:39:11.250Z"}
2025-08-03 20:40:11.274 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 20:40:11.275 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T12:40:11.274Z"}
2025-08-03 20:45:45.482 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 20:45:45.482 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T12:45:45.482Z"}
2025-08-03 20:46:06.007 [ERROR] [GlobalExceptionFilter] GET /api/v1/course-management/courses/4303/task-templates/1155 - Cannot GET /api/v1/course-management/courses/4303/task-templates/1155 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/v1/course-management/courses/4303/task-templates/1155
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 20:46:06.009 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/v1/course-management/courses/4303/task-templates/1155","method":"GET","statusCode":404,"message":"Cannot GET /api/v1/course-management/courses/4303/task-templates/1155","details":{"message":"Cannot GET /api/v1/course-management/courses/4303/task-templates/1155","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T12:46:06.008Z"}
2025-08-03 20:46:08.629 [ERROR] [GlobalExceptionFilter] GET /api/v1/course-management/courses/4304/task-templates/1156 - Cannot GET /api/v1/course-management/courses/4304/task-templates/1156 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/v1/course-management/courses/4304/task-templates/1156
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 20:46:08.630 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/v1/course-management/courses/4304/task-templates/1156","method":"GET","statusCode":404,"message":"Cannot GET /api/v1/course-management/courses/4304/task-templates/1156","details":{"message":"Cannot GET /api/v1/course-management/courses/4304/task-templates/1156","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T12:46:08.629Z"}
2025-08-03 20:46:45.507 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 20:46:45.507 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T12:46:45.507Z"}
2025-08-03 20:47:46.245 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 20:47:46.246 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T12:47:46.245Z"}
2025-08-03 20:48:47.239 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 20:48:47.239 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T12:48:47.239Z"}
2025-08-03 20:49:48.247 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 20:49:48.248 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T12:49:48.247Z"}
2025-08-03 20:50:49.250 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 20:50:49.250 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T12:50:49.250Z"}
2025-08-03 20:51:50.240 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 20:51:50.240 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T12:51:50.240Z"}
2025-08-03 20:52:51.239 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 20:52:51.239 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T12:52:51.239Z"}
2025-08-03 20:53:52.244 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 20:53:52.244 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T12:53:52.244Z"}
2025-08-03 20:54:53.250 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 20:54:53.250 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T12:54:53.250Z"}
2025-08-03 20:55:54.247 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 20:55:54.248 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T12:55:54.248Z"}
2025-08-03 20:56:55.239 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 20:56:55.240 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T12:56:55.240Z"}
2025-08-03 20:57:56.248 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 20:57:56.248 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T12:57:56.248Z"}
2025-08-03 20:58:57.248 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 20:58:57.248 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T12:58:57.248Z"}
2025-08-03 20:59:58.237 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 20:59:58.237 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T12:59:58.237Z"}
2025-08-03 21:00:59.245 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 21:00:59.246 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T13:00:59.245Z"}
2025-08-03 21:02:00.245 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 21:02:00.246 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T13:02:00.245Z"}
2025-08-03 21:03:01.243 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 21:03:01.243 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T13:03:01.243Z"}
2025-08-03 21:04:02.249 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 21:04:02.249 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T13:04:02.249Z"}
2025-08-03 21:05:03.237 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 21:05:03.238 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T13:05:03.237Z"}
2025-08-03 21:06:04.233 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 21:06:04.233 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T13:06:04.233Z"}
2025-08-03 21:07:05.244 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 21:07:05.245 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T13:07:05.245Z"}
2025-08-03 21:08:06.238 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 21:08:06.239 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T13:08:06.238Z"}
2025-08-03 21:09:06.261 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 21:09:06.262 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T13:09:06.261Z"}
2025-08-03 21:10:06.272 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 21:10:06.273 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T13:10:06.273Z"}
2025-08-03 21:11:06.286 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 21:11:06.286 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T13:11:06.286Z"}
2025-08-03 21:12:06.303 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 21:12:06.303 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T13:12:06.303Z"}
2025-08-03 21:13:06.317 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 21:13:06.318 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T13:13:06.318Z"}
2025-08-03 21:14:06.329 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 21:14:06.329 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T13:14:06.329Z"}
2025-08-03 21:15:06.346 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 21:15:06.346 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T13:15:06.346Z"}
2025-08-03 21:15:22.663 [ERROR] [GlobalExceptionFilter] GET /api/v1/course-management/courses/4304/task-templates/1156 - Cannot GET /api/v1/course-management/courses/4304/task-templates/1156 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/v1/course-management/courses/4304/task-templates/1156
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 21:15:22.663 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/v1/course-management/courses/4304/task-templates/1156","method":"GET","statusCode":404,"message":"Cannot GET /api/v1/course-management/courses/4304/task-templates/1156","details":{"message":"Cannot GET /api/v1/course-management/courses/4304/task-templates/1156","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T13:15:22.663Z"}
2025-08-03 21:15:37.666 [ERROR] [GlobalExceptionFilter] GET /api/v1/course-management/courses/4303/task-templates/1155 - Cannot GET /api/v1/course-management/courses/4303/task-templates/1155 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/v1/course-management/courses/4303/task-templates/1155
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 21:15:37.666 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/v1/course-management/courses/4303/task-templates/1155","method":"GET","statusCode":404,"message":"Cannot GET /api/v1/course-management/courses/4303/task-templates/1155","details":{"message":"Cannot GET /api/v1/course-management/courses/4303/task-templates/1155","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T13:15:37.666Z"}
2025-08-03 21:15:39.210 [ERROR] [GlobalExceptionFilter] GET /api/v1/course-management/courses/4304/task-templates/1156 - Cannot GET /api/v1/course-management/courses/4304/task-templates/1156 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/v1/course-management/courses/4304/task-templates/1156
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 21:15:39.211 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/v1/course-management/courses/4304/task-templates/1156","method":"GET","statusCode":404,"message":"Cannot GET /api/v1/course-management/courses/4304/task-templates/1156","details":{"message":"Cannot GET /api/v1/course-management/courses/4304/task-templates/1156","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T13:15:39.210Z"}
2025-08-03 21:15:54.521 [ERROR] [GlobalExceptionFilter] GET /api/v1/course-management/courses/4304/task-templates/1156 - Cannot GET /api/v1/course-management/courses/4304/task-templates/1156 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/v1/course-management/courses/4304/task-templates/1156
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 21:15:54.521 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/v1/course-management/courses/4304/task-templates/1156","method":"GET","statusCode":404,"message":"Cannot GET /api/v1/course-management/courses/4304/task-templates/1156","details":{"message":"Cannot GET /api/v1/course-management/courses/4304/task-templates/1156","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T13:15:54.521Z"}
2025-08-03 21:16:06.376 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 21:16:06.377 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T13:16:06.376Z"}
2025-08-03 21:17:06.393 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 21:17:06.393 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T13:17:06.393Z"}
2025-08-03 21:18:07.249 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 21:18:07.250 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T13:18:07.249Z"}
2025-08-03 21:19:08.236 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 21:19:08.236 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T13:19:08.236Z"}
2025-08-03 21:20:09.247 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 21:20:09.247 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T13:20:09.247Z"}
2025-08-03 21:21:10.249 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 21:21:10.249 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T13:21:10.249Z"}
2025-08-03 21:21:58.378 [ERROR] [GlobalExceptionFilter] GET /api/v1/course-marketplace/tags?page=1&pageSize=100&status=1 - Connection lost: The server closed the connection. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
Error: Connection lost: The server closed the connection.
    at Socket.<anonymous> (D:\logicleap\logic-back\node_modules\mysql2\lib\base\connection.js:113:31)
    at Socket.emit (node:events:524:28)
    at TCP.<anonymous> (node:net:343:12)
2025-08-03 21:21:58.379 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/v1/course-marketplace/tags?page=1&pageSize=100&status=1","method":"GET","statusCode":500,"message":"Connection lost: The server closed the connection.","details":{"name":"Error","stack":"Error: Connection lost: The server closed the connection.\n    at Socket.<anonymous> (D:\\logicleap\\logic-back\\node_modules\\mysql2\\lib\\base\\connection.js:113:31)\n    at Socket.emit (node:events:524:28)\n    at TCP.<anonymous> (node:net:343:12)"},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T13:21:58.378Z"}
2025-08-03 21:22:11.238 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 21:22:11.239 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T13:22:11.239Z"}
2025-08-03 21:22:22.999 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 21:22:22.999 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T13:22:22.999Z"}
2025-08-03 21:31:18.614 [ERROR] [GlobalExceptionFilter] GET /api/v1/course-management/courses/4304/task-templates/1156 - Cannot GET /api/v1/course-management/courses/4304/task-templates/1156 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/v1/course-management/courses/4304/task-templates/1156
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 21:31:18.614 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/v1/course-management/courses/4304/task-templates/1156","method":"GET","statusCode":404,"message":"Cannot GET /api/v1/course-management/courses/4304/task-templates/1156","details":{"message":"Cannot GET /api/v1/course-management/courses/4304/task-templates/1156","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T13:31:18.614Z"}
2025-08-03 21:31:47.028 [ERROR] [GlobalExceptionFilter] GET /api/v1/course-management/courses/4303/task-templates/1155 - Cannot GET /api/v1/course-management/courses/4303/task-templates/1155 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/v1/course-management/courses/4303/task-templates/1155
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 21:31:47.028 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/v1/course-management/courses/4303/task-templates/1155","method":"GET","statusCode":404,"message":"Cannot GET /api/v1/course-management/courses/4303/task-templates/1155","details":{"message":"Cannot GET /api/v1/course-management/courses/4303/task-templates/1155","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T13:31:47.028Z"}
2025-08-03 21:31:49.855 [ERROR] [GlobalExceptionFilter] GET /api/v1/course-management/courses/4304/task-templates/1156 - Cannot GET /api/v1/course-management/courses/4304/task-templates/1156 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/v1/course-management/courses/4304/task-templates/1156
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 21:31:49.856 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/v1/course-management/courses/4304/task-templates/1156","method":"GET","statusCode":404,"message":"Cannot GET /api/v1/course-management/courses/4304/task-templates/1156","details":{"message":"Cannot GET /api/v1/course-management/courses/4304/task-templates/1156","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T13:31:49.855Z"}
2025-08-03 21:31:56.431 [ERROR] [GlobalExceptionFilter] GET /api/v1/course-management/courses/4303/task-templates/1155 - Cannot GET /api/v1/course-management/courses/4303/task-templates/1155 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/v1/course-management/courses/4303/task-templates/1155
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 21:31:56.432 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/v1/course-management/courses/4303/task-templates/1155","method":"GET","statusCode":404,"message":"Cannot GET /api/v1/course-management/courses/4303/task-templates/1155","details":{"message":"Cannot GET /api/v1/course-management/courses/4303/task-templates/1155","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T13:31:56.431Z"}
2025-08-03 21:32:15.841 [ERROR] [GlobalExceptionFilter] GET /api/v1/course-management/courses/4303/task-templates/1155 - Cannot GET /api/v1/course-management/courses/4303/task-templates/1155 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/v1/course-management/courses/4303/task-templates/1155
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 21:32:15.841 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/v1/course-management/courses/4303/task-templates/1155","method":"GET","statusCode":404,"message":"Cannot GET /api/v1/course-management/courses/4303/task-templates/1155","details":{"message":"Cannot GET /api/v1/course-management/courses/4303/task-templates/1155","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T13:32:15.841Z"}
2025-08-03 21:32:18.253 [ERROR] [GlobalExceptionFilter] GET /api/v1/course-management/courses/4304/task-templates/1156 - Cannot GET /api/v1/course-management/courses/4304/task-templates/1156 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/v1/course-management/courses/4304/task-templates/1156
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 21:32:18.253 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/v1/course-management/courses/4304/task-templates/1156","method":"GET","statusCode":404,"message":"Cannot GET /api/v1/course-management/courses/4304/task-templates/1156","details":{"message":"Cannot GET /api/v1/course-management/courses/4304/task-templates/1156","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T13:32:18.253Z"}
2025-08-03 21:33:22.985 [ERROR] [GlobalExceptionFilter] GET /api/v1/course-management/courses/4303/task-templates/1155 - Cannot GET /api/v1/course-management/courses/4303/task-templates/1155 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/v1/course-management/courses/4303/task-templates/1155
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 21:33:22.988 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/v1/course-management/courses/4303/task-templates/1155","method":"GET","statusCode":404,"message":"Cannot GET /api/v1/course-management/courses/4303/task-templates/1155","details":{"message":"Cannot GET /api/v1/course-management/courses/4303/task-templates/1155","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T13:33:22.986Z"}
2025-08-03 21:33:45.085 [ERROR] [GlobalExceptionFilter] GET /api/v1/course-management/courses/4303/task-templates/1155 - Cannot GET /api/v1/course-management/courses/4303/task-templates/1155 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/v1/course-management/courses/4303/task-templates/1155
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 21:33:45.089 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/v1/course-management/courses/4303/task-templates/1155","method":"GET","statusCode":404,"message":"Cannot GET /api/v1/course-management/courses/4303/task-templates/1155","details":{"message":"Cannot GET /api/v1/course-management/courses/4303/task-templates/1155","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T13:33:45.086Z"}
2025-08-03 21:34:18.829 [ERROR] [GlobalExceptionFilter] GET /api/v1/course-management/courses/4304/task-templates/1156 - Cannot GET /api/v1/course-management/courses/4304/task-templates/1156 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/v1/course-management/courses/4304/task-templates/1156
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 21:34:18.830 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/v1/course-management/courses/4304/task-templates/1156","method":"GET","statusCode":404,"message":"Cannot GET /api/v1/course-management/courses/4304/task-templates/1156","details":{"message":"Cannot GET /api/v1/course-management/courses/4304/task-templates/1156","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T13:34:18.829Z"}
2025-08-03 21:45:26.271 [ERROR] [GlobalExceptionFilter] GET /api/v1/course-management/courses/4304/task-templates/1156 - Cannot GET /api/v1/course-management/courses/4304/task-templates/1156 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/v1/course-management/courses/4304/task-templates/1156
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 21:45:26.271 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/v1/course-management/courses/4304/task-templates/1156","method":"GET","statusCode":404,"message":"Cannot GET /api/v1/course-management/courses/4304/task-templates/1156","details":{"message":"Cannot GET /api/v1/course-management/courses/4304/task-templates/1156","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T13:45:26.271Z"}
2025-08-03 22:00:16.956 [ERROR] [WebWeixinScanService] 清理过期记录失败: Connection lost: The server closed the connection. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY","stack":[null]}
2025-08-03 22:15:58.490 [ERROR] [GlobalExceptionFilter] GET /api/v1/course-management/courses/4304/task-templates/1156 - Cannot GET /api/v1/course-management/courses/4304/task-templates/1156 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/v1/course-management/courses/4304/task-templates/1156
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:15:58.491 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/v1/course-management/courses/4304/task-templates/1156","method":"GET","statusCode":404,"message":"Cannot GET /api/v1/course-management/courses/4304/task-templates/1156","details":{"message":"Cannot GET /api/v1/course-management/courses/4304/task-templates/1156","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:15:58.490Z"}
2025-08-03 22:16:41.423 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:16:41.424 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:16:41.423Z"}
2025-08-03 22:16:46.124 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:16:46.125 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:16:46.125Z"}
2025-08-03 22:16:47.070 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:16:47.070 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:16:47.070Z"}
2025-08-03 22:17:42.896 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:17:42.898 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:17:42.897Z"}
2025-08-03 22:17:47.095 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:17:47.096 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:17:47.096Z"}
2025-08-03 22:18:42.909 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:18:42.910 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:18:42.909Z"}
2025-08-03 22:18:47.109 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:18:47.109 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:18:47.109Z"}
2025-08-03 22:19:42.926 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:19:42.927 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:19:42.926Z"}
2025-08-03 22:19:47.120 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:19:47.120 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:19:47.120Z"}
2025-08-03 22:20:42.943 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:20:42.944 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:20:42.943Z"}
2025-08-03 22:20:47.130 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:20:47.131 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:20:47.130Z"}
2025-08-03 22:21:42.960 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:21:42.960 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:21:42.960Z"}
2025-08-03 22:21:47.147 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:21:47.147 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:21:47.147Z"}
2025-08-03 22:22:42.976 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:22:42.977 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:22:42.976Z"}
2025-08-03 22:22:47.158 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:22:47.159 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:22:47.158Z"}
2025-08-03 22:23:43.007 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:23:43.008 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:23:43.007Z"}
2025-08-03 22:23:47.168 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:23:47.169 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:23:47.168Z"}
2025-08-03 22:24:43.024 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:24:43.028 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:24:43.024Z"}
2025-08-03 22:24:47.180 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:24:47.181 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:24:47.180Z"}
2025-08-03 22:25:43.040 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:25:43.041 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:25:43.041Z"}
2025-08-03 22:25:47.192 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:25:47.192 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:25:47.192Z"}
2025-08-03 22:26:43.239 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:26:43.239 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:26:43.239Z"}
2025-08-03 22:26:47.236 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:26:47.236 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:26:47.236Z"}
2025-08-03 22:27:44.248 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:27:44.248 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:27:44.248Z"}
2025-08-03 22:27:48.248 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:27:48.248 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:27:48.248Z"}
2025-08-03 22:28:45.241 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:28:45.241 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:28:45.241Z"}
2025-08-03 22:28:49.239 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:28:49.239 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:28:49.239Z"}
2025-08-03 22:29:46.246 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:29:46.246 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:29:46.246Z"}
2025-08-03 22:29:50.231 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:29:50.231 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:29:50.231Z"}
2025-08-03 22:30:47.235 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:30:47.235 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:30:47.235Z"}
2025-08-03 22:30:51.247 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:30:51.247 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:30:51.247Z"}
2025-08-03 22:31:48.243 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:31:48.243 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:31:48.243Z"}
2025-08-03 22:31:52.236 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:31:52.236 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:31:52.236Z"}
2025-08-03 22:32:49.237 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:32:49.237 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:32:49.237Z"}
2025-08-03 22:32:53.233 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:32:53.234 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:32:53.233Z"}
2025-08-03 22:33:50.232 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:33:50.233 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:33:50.232Z"}
2025-08-03 22:33:54.243 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:33:54.243 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:33:54.243Z"}
2025-08-03 22:34:51.247 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:34:51.247 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:34:51.247Z"}
2025-08-03 22:34:55.241 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:34:55.241 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:34:55.241Z"}
2025-08-03 22:35:52.245 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:35:52.245 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:35:52.245Z"}
2025-08-03 22:35:56.245 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:35:56.245 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:35:56.245Z"}
2025-08-03 22:36:26.827 [ERROR] [GlobalExceptionFilter] GET /api/v1/course-marketplace/series?page=1&pageSize=50 - Connection lost: The server closed the connection. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
Error: Connection lost: The server closed the connection.
    at Socket.<anonymous> (D:\logicleap\logic-back\node_modules\mysql2\lib\base\connection.js:113:31)
    at Socket.emit (node:events:524:28)
    at TCP.<anonymous> (node:net:343:12)
2025-08-03 22:36:26.827 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/v1/course-marketplace/series?page=1&pageSize=50","method":"GET","statusCode":500,"message":"Connection lost: The server closed the connection.","details":{"name":"Error","stack":"Error: Connection lost: The server closed the connection.\n    at Socket.<anonymous> (D:\\logicleap\\logic-back\\node_modules\\mysql2\\lib\\base\\connection.js:113:31)\n    at Socket.emit (node:events:524:28)\n    at TCP.<anonymous> (node:net:343:12)"},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:36:26.827Z"}
2025-08-03 22:36:53.240 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:36:53.240 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:36:53.240Z"}
2025-08-03 22:36:57.244 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:36:57.244 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:36:57.244Z"}
2025-08-03 22:37:54.241 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:37:54.241 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:37:54.241Z"}
2025-08-03 22:37:58.245 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:37:58.245 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:37:58.245Z"}
2025-08-03 22:38:54.268 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:38:54.269 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:38:54.269Z"}
2025-08-03 22:38:58.259 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:38:58.259 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:38:58.259Z"}
2025-08-03 22:39:55.235 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:39:55.235 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:39:55.235Z"}
2025-08-03 22:39:59.234 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:39:59.234 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:39:59.234Z"}
2025-08-03 22:40:56.245 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:40:56.245 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:40:56.245Z"}
2025-08-03 22:41:00.237 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:41:00.238 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:41:00.237Z"}
2025-08-03 22:41:57.239 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:41:57.239 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:41:57.239Z"}
2025-08-03 22:42:01.234 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:42:01.235 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:42:01.234Z"}
2025-08-03 22:42:58.240 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:42:58.241 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:42:58.241Z"}
2025-08-03 22:43:02.234 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:43:02.234 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:43:02.234Z"}
2025-08-03 22:43:59.250 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:43:59.250 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:43:59.250Z"}
2025-08-03 22:44:03.236 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:44:03.237 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:44:03.236Z"}
2025-08-03 22:45:00.245 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:45:00.245 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:45:00.245Z"}
2025-08-03 22:45:04.250 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:45:04.251 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:45:04.250Z"}
2025-08-03 22:46:01.242 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:46:01.242 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:46:01.242Z"}
2025-08-03 22:46:05.243 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:46:05.243 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:46:05.243Z"}
2025-08-03 22:47:02.235 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:47:02.236 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:47:02.236Z"}
2025-08-03 22:47:06.232 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:47:06.232 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:47:06.232Z"}
2025-08-03 22:48:03.246 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:48:03.246 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:48:03.246Z"}
2025-08-03 22:48:07.233 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:48:07.233 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:48:07.233Z"}
2025-08-03 22:49:04.238 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:49:04.238 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:49:04.238Z"}
2025-08-03 22:49:08.240 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:49:08.240 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:49:08.240Z"}
2025-08-03 22:50:05.246 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:50:05.247 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:50:05.246Z"}
2025-08-03 22:50:09.239 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:50:09.239 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:50:09.239Z"}
2025-08-03 22:51:06.250 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:51:06.250 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:51:06.250Z"}
2025-08-03 22:51:10.237 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:51:10.238 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:51:10.238Z"}
2025-08-03 22:52:07.250 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:52:07.251 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:52:07.250Z"}
2025-08-03 22:52:11.247 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-08-03 22:52:11.248 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":26792,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-08-03T14:52:11.247Z"}
