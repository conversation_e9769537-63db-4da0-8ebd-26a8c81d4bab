import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DatabaseConfigService } from '../config/database-config.service';
import { YamlService } from 'src/util/yaml/yaml.service';
import { DatabaseMonitorModule } from 'src/util/database/monitor/database-monitor.module';
import { QueryInterceptor } from 'src/util/database/monitor/interceptor/query-interceptor';
import { AnnouncementReadRecordModule } from './announcement_read_record/announcement_read_record.module';
import { AnnouncementModule } from './announcement/announcement.module';
import { AudioTrainModelModule } from './audio_train_model/audio_train_model.module';
import { BlockPermissionsModule } from './block_permissions/block_permissions.module';
import { BlockModule } from './block/block.module';
import { UserClassModule } from './user_class/user_class.module';
import { DocModule } from './doc/doc.module';
import { ExtensionPermissionsModule } from './extension_permissions/extension_permissions.module';
import { ExtensionsModule } from './extensions/extensions.module';
import { UserImageEnhanceModule } from './user_image_enhance/user_image_enhance.module';
import { UserImageInfoModule } from './user_image_info/user_image_info.module';
import { UserImageSegmentModule } from './user_image_segment/user_image_segment.module';
import { ImageTrainModelModule } from './image_train_model/image_train_model.module';
import { UserInfoModule } from './user_info/user_info.module';
import { KeyPackageRecordModule } from './key_package_record/key_package_record.module';
import { PackageInfoModule } from './package_info/package_info.module';
import { UserPasswordResetRequestModule } from './user_password_reset_request/user_password_reset_request.module';
import { UserPointsOfflineMessageModule } from './user_points_offline_message/user_points_offline_message.module';
import { UserPointsPermissionModule } from './user_points_permission/user_points_permission.module';
import { UserPointsModule } from './user_points/user_points.module';
import { PoseTrainModelModule } from './pose_train_model/pose_train_model.module';
import { UserReportModule } from './user_report/user_report.module';
import { RolePermissionTemplatesModule } from './role_permission_templates/role_permission_templates.module';
import { UserRolePermissionModule } from './user_role_permission/user_role_permission.module';
import { UserRoleRelationModule } from './user_role_relation/user_role_relation.module';
import { RoleTemplateBlockPermissionModule } from './role_template_block_permission/role_template_block_permission.module';
import { RoleTemplateExtensionPermissionModule } from './role_template_extension_permission/role_template_extension_permission.module';
import { RoleTemplateFolderJoinTemplateModule } from './role_template_folder_join_template/role_template_folder_join_template.module';
import { RoleTemplateFolderModule } from './role_template_folder/role_template_folder.module';
import { UserRoleModule } from './user_role/user_role.module';
import { UserSchoolRelationModule } from './user_school_relation/user_school_relation.module';
import { UserSchoolModule } from './user_school/user_school.module';
import { UserStudentModule } from './user_student/user_student.module';
import { TeacherTaskAssignmentModule } from './teacher_task_assignment/teacher_task_assignment.module';
import { TeacherTaskModule } from './teacher_task/teacher_task.module';
import { UserJoinRoleModule } from './user_join_role/user_join_role.module';
import { UserPackageModule } from './user_package/user_package.module';
import { UserVoiceInfoModule } from './user_voice_info/user_voice_info.module';
import { UserWorkInfoModule } from './user_work_info/user_work_info.module';
import { UserWorkLikeModule } from './user_work_like/user_work_like.module';
import { WorkModelModule } from './work_model/work_model.module';
import { SpaceCarouselMapModule } from './space_carousel_map/space_carousel_map.module';
import { ActivityTagModule } from './activity_tag/activity_tag.module';
import { ActivityWorkModule } from './activity_work/activity_work.module';
import { ActivityModule } from './activity/activity.module';
import { ActivityAuditModule } from './activity_audit/activity_audit.module';
import { AnnouncementAuditModule } from './announcement_audit/announcement_audit.module';
import { CarouselAuditModule } from './carousel_audit/carousel_audit.module';
import { ParticipationAuditModule } from './participation_audit/participation_audit.module';
import { WorkAuditModule } from './work_audit/work_audit.module';
import { TagModule } from './tag/tag.module';
import { TableJoingModule } from './table_joing/table_joing.module';
import { WebWeixinScanModule } from './web_weixin_scan/web_weixin_scan.module';
import { TaskSelfAssessmentItemModule } from './task_self_assessment_item/task_self_assessment_item.module';
import { StudentSelfAssessmentSubmissionModule } from './student_self_assessment_submission/student_self_assessment_submission.module';
import { PaymentOrderModule } from './payment_order/payment-order.module';
import { PaymentRefundModule } from './payment_refund/payment-refund.module';
import { PackageOrderModule } from './package_order/package-order.module';
import { UserLoginLogModule } from './user_login_log/user_login_log.module';

@Module({
  imports: [
    DatabaseMonitorModule, // 先导入数据库监控模块
    TypeOrmModule.forRootAsync({
      inject: [YamlService, QueryInterceptor],
      useFactory: async (yamlService: YamlService, queryInterceptor: QueryInterceptor) => {
        const configService = new DatabaseConfigService(yamlService);
        return configService.getMysqlConfig(queryInterceptor);
      },

    }),
    AnnouncementReadRecordModule,
    AnnouncementModule,
    AudioTrainModelModule,
    BlockPermissionsModule,
    BlockModule,
    UserClassModule,
    DocModule,
    ExtensionPermissionsModule,
    ExtensionsModule,
    UserImageEnhanceModule,
    UserImageInfoModule,
    UserImageSegmentModule,
    ImageTrainModelModule,
    UserInfoModule,
    KeyPackageRecordModule,
    PackageInfoModule,
    UserPasswordResetRequestModule,
    UserPointsOfflineMessageModule,
    UserPointsPermissionModule,
    UserPointsModule,
    PoseTrainModelModule,
    UserReportModule,
    RolePermissionTemplatesModule,
    UserRolePermissionModule,
    UserRoleRelationModule,
    RoleTemplateBlockPermissionModule,
    RoleTemplateExtensionPermissionModule,
    RoleTemplateFolderJoinTemplateModule,
    RoleTemplateFolderModule,
    UserRoleModule,
    UserSchoolRelationModule,
    UserSchoolModule,
    UserStudentModule,
    TeacherTaskAssignmentModule,
    TeacherTaskModule,
    UserJoinRoleModule,
    UserPackageModule,
    UserVoiceInfoModule,
    UserWorkInfoModule,
    UserWorkLikeModule,
    WorkModelModule,
    SpaceCarouselMapModule,
    ActivityTagModule,
    ActivityWorkModule,
    ActivityModule,
    ActivityAuditModule,
    AnnouncementAuditModule,
    CarouselAuditModule,
    ParticipationAuditModule,
    WorkAuditModule,
    TagModule,
    TableJoingModule,
    WebWeixinScanModule,
    TaskSelfAssessmentItemModule,
    StudentSelfAssessmentSubmissionModule,
    PaymentOrderModule,
    PaymentRefundModule,
    PackageOrderModule,
    UserLoginLogModule,
  ],
  providers: [DatabaseConfigService],
})
export class MysqlModule { }
