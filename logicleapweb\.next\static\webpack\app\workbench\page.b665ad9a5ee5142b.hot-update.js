"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/workbench/page",{

/***/ "(app-pages-browser)/./app/workbench/components/MainContent.tsx":
/*!**************************************************!*\
  !*** ./app/workbench/components/MainContent.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _QuickActions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./QuickActions */ \"(app-pages-browser)/./app/workbench/components/QuickActions.tsx\");\n/* harmony import */ var _OngoingTasks__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./OngoingTasks */ \"(app-pages-browser)/./app/workbench/components/OngoingTasks.tsx\");\n/* harmony import */ var _TemplateManagement__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./TemplateManagement */ \"(app-pages-browser)/./app/workbench/components/TemplateManagement.tsx\");\n/* harmony import */ var _ClassManagement__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ClassManagement */ \"(app-pages-browser)/./app/workbench/components/ClassManagement.tsx\");\n/* harmony import */ var _ClassDetail__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ClassDetail */ \"(app-pages-browser)/./app/workbench/components/ClassDetail.tsx\");\n/* harmony import */ var _ClassTasks__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ClassTasks */ \"(app-pages-browser)/./app/workbench/components/ClassTasks.tsx\");\n/* harmony import */ var _CourseManagement__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./CourseManagement */ \"(app-pages-browser)/./app/workbench/components/CourseManagement.tsx\");\n/* harmony import */ var _ClassProjects__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./ClassProjects */ \"(app-pages-browser)/./app/workbench/components/ClassProjects.tsx\");\n/* harmony import */ var _SchoolSelectionModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./SchoolSelectionModal */ \"(app-pages-browser)/./app/workbench/components/SchoolSelectionModal.tsx\");\n/* harmony import */ var _ClassSelectionModal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./ClassSelectionModal */ \"(app-pages-browser)/./app/workbench/components/ClassSelectionModal.tsx\");\n/* harmony import */ var _TemplateSelectionModal__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./TemplateSelectionModal */ \"(app-pages-browser)/./app/workbench/components/TemplateSelectionModal.tsx\");\n/* harmony import */ var _teacher_space_components_modals_create_class_modal__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../../teacher-space/components/modals/create-class-modal */ \"(app-pages-browser)/./app/teacher-space/components/modals/create-class-modal.tsx\");\n/* harmony import */ var _lib_api_school__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/lib/api/school */ \"(app-pages-browser)/./lib/api/school.ts\");\n/* harmony import */ var _lib_api_class__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/lib/api/class */ \"(app-pages-browser)/./lib/api/class.ts\");\n/* harmony import */ var _barrel_optimize_names_Search_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/message/index.js\");\n/* harmony import */ var _icon_park_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @icon-park/react */ \"(app-pages-browser)/./node_modules/@icon-park/react/es/icons/HandUp.js\");\n/* harmony import */ var _icon_park_react_styles_index_css__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @icon-park/react/styles/index.css */ \"(app-pages-browser)/./node_modules/@icon-park/react/styles/index.css\");\n/* harmony import */ var _SchoolSelectionModal_css__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./SchoolSelectionModal.css */ \"(app-pages-browser)/./app/workbench/components/SchoolSelectionModal.css\");\n/* harmony import */ var _contexts_TemplateContext__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ../contexts/TemplateContext */ \"(app-pages-browser)/./app/workbench/contexts/TemplateContext.tsx\");\n/* harmony import */ var _TemplateSelectionModal_css__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./TemplateSelectionModal.css */ \"(app-pages-browser)/./app/workbench/components/TemplateSelectionModal.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst MainContent = (param)=>{\n    let { activeView = \"快速开始\", selectedSchool, userInfo, classes = [], classesLoading = false, classesError = null, onCloseDropdown, onClassesUpdate, onSchoolChange } = param;\n    _s();\n    const [isSchoolModalOpen, setIsSchoolModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isClassModalOpen, setIsClassModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isTemplateModalOpen, setIsTemplateModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isCreateClassModalOpen, setIsCreateClassModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentActionType, setCurrentActionType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [modalSelectedSchool, setModalSelectedSchool] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedClass, setSelectedClass] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showClassDetail, setShowClassDetail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedClassForDetail, setSelectedClassForDetail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 使用模板上下文\n    const { currentTemplate, globalTemplateChangeVersion } = (0,_contexts_TemplateContext__WEBPACK_IMPORTED_MODULE_18__.useTemplate)();\n    // 监听全局模板变化，通知所有班级详情组件刷新\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (globalTemplateChangeVersion > 0 && currentTemplate) {\n            console.log(\"MainContent - 检测到全局模板变化，版本号:\", globalTemplateChangeVersion);\n            console.log(\"MainContent - 新的当前模板:\", currentTemplate);\n        // 这里可以添加通知所有班级组件刷新的逻辑\n        // 由于班级详情组件已经在监听globalTemplateChangeVersion，\n        // 这里主要是为了确保状态同步\n        }\n    }, [\n        globalTemplateChangeVersion,\n        currentTemplate\n    ]);\n    // 监听学校选择变化，强制跳回班级管理页面\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedSchool) {\n            console.log(\"检测到学校变化，强制跳回班级管理页面:\", selectedSchool);\n            // 重置班级详情显示状态，强制显示班级列表\n            setShowClassDetail(false);\n            setSelectedClassForDetail(null);\n            // 通知父组件学校变化\n            if (onSchoolChange) {\n                onSchoolChange(selectedSchool);\n            }\n        }\n    }, [\n        selectedSchool,\n        onSchoolChange\n    ]);\n    const handleQuickStartClick = async function() {\n        let actionType = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"发布任务\";\n        setCurrentActionType(actionType);\n        try {\n            // 获取用户的学校列表\n            const response = await _lib_api_school__WEBPACK_IMPORTED_MODULE_14__.schoolApi.getUserSchools();\n            if (response.data.code === 200) {\n                const schoolsData = response.data.data || [];\n                if (schoolsData.length === 1) {\n                    // 只有一个学校，直接选择并跳到班级选择\n                    setModalSelectedSchool(schoolsData[0]);\n                    setIsClassModalOpen(true);\n                } else if (schoolsData.length > 1) {\n                    // 多个学校，显示学校选择弹窗\n                    setIsSchoolModalOpen(true);\n                } else {\n                    // 没有学校，可以显示提示信息\n                    console.warn(\"用户没有关联的学校\");\n                }\n            }\n        } catch (error) {\n            console.error(\"获取学校列表失败:\", error);\n            // 出错时仍然显示学校选择弹窗\n            setIsSchoolModalOpen(true);\n        }\n    };\n    const handleCloseModal = ()=>{\n        setIsSchoolModalOpen(false);\n        setIsClassModalOpen(false);\n        setIsTemplateModalOpen(false);\n        setModalSelectedSchool(null);\n        setSelectedClass(null);\n        setCurrentActionType(\"\");\n    };\n    const handleSchoolSelect = (school)=>{\n        setModalSelectedSchool(school);\n        setIsSchoolModalOpen(false);\n        setIsClassModalOpen(true);\n    };\n    const handleClassSelect = (classData)=>{\n        setSelectedClass(classData);\n        setIsClassModalOpen(false);\n        setIsTemplateModalOpen(true);\n    };\n    const handleBackToSchool = ()=>{\n        setIsClassModalOpen(false);\n        setIsSchoolModalOpen(true);\n    };\n    const handleBackToClass = ()=>{\n        setIsTemplateModalOpen(false);\n        setIsClassModalOpen(true);\n    };\n    const handleClassClick = (classInfo)=>{\n        console.log(\"点击班级:\", classInfo);\n        setSelectedClassForDetail(classInfo);\n        setShowClassDetail(true);\n    };\n    const handleBackToClassManagement = ()=>{\n        setShowClassDetail(false);\n        setSelectedClassForDetail(null);\n    };\n    // 处理班级信息更新\n    const handleClassInfoUpdate = (updatedClassInfo)=>{\n        // 更新班级列表中对应的班级信息\n        const updatedClasses = classes.map((classItem)=>classItem.id === updatedClassInfo.id ? {\n                ...classItem,\n                ...updatedClassInfo\n            } : classItem);\n        // 通知父组件更新班级列表\n        onClassesUpdate === null || onClassesUpdate === void 0 ? void 0 : onClassesUpdate(updatedClasses);\n        // 同时更新当前选中的班级详情\n        setSelectedClassForDetail(updatedClassInfo);\n    };\n    // 处理班级删除\n    const handleClassDeleted = (deletedClassId)=>{\n        console.log(\"班级已删除:\", deletedClassId);\n        // 从班级列表中移除被删除的班级\n        const updatedClasses = classes.filter((cls)=>cls.id !== deletedClassId);\n        // 通知父组件更新班级列表\n        onClassesUpdate === null || onClassesUpdate === void 0 ? void 0 : onClassesUpdate(updatedClasses);\n    };\n    // 处理添加班级\n    const handleAddClass = ()=>{\n        if (!selectedSchool) {\n            _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"].error(\"请先选择学校\");\n            return;\n        }\n        setIsCreateClassModalOpen(true);\n    };\n    // 处理模板选择确认\n    const handleTemplateConfirm = (taskData)=>{\n        console.log(\"模板选择确认:\", taskData);\n        // 简单关闭弹窗，不执行具体的发布逻辑\n        handleCloseModal();\n    };\n    // 处理创建班级表单提交\n    const handleCreateClass = async (values)=>{\n        if (!selectedSchool) {\n            _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"].error(\"请先选择学校\");\n            return;\n        }\n        if (!(userInfo === null || userInfo === void 0 ? void 0 : userInfo.id)) {\n            _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"].error(\"用户信息不完整，请重新登录\");\n            return;\n        }\n        if (values.className.length > 8) {\n            _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"].error(\"班级名称不能超过8个字符\");\n            return;\n        }\n        try {\n            // 使用 createClass API，需要传递 teacherId\n            const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_15__.classApi.createClass(selectedSchool.id, values.className, userInfo.id);\n            if (response.data.code === 200) {\n                _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"].success(\"创建班级成功\");\n                setIsCreateClassModalOpen(false);\n                // 刷新班级列表\n                try {\n                    const classesResponse = await _lib_api_class__WEBPACK_IMPORTED_MODULE_15__.classApi.getTeacherClassesSimple(selectedSchool.id);\n                    if (classesResponse.data.code === 200) {\n                        onClassesUpdate === null || onClassesUpdate === void 0 ? void 0 : onClassesUpdate(classesResponse.data.data || []);\n                    }\n                } catch (error) {\n                    console.error(\"刷新班级列表失败:\", error);\n                }\n            } else {\n                _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"].error(response.data.message || \"该班级已存在或创建失败\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"创建班级失败:\", error);\n            _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"].error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"创建班级失败\");\n        }\n    };\n    // 处理创建班级弹窗关闭\n    const handleCreateClassModalClose = ()=>{\n        setIsCreateClassModalOpen(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"main-content relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"main-header\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"search-bar\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Search_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                className: \"search-icon\",\n                                size: 18\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                                lineNumber: 285,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                placeholder: \"搜索课程、任务或学生...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                        lineNumber: 284,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"start-class-btn\",\n                        onClick: ()=>handleQuickStartClick(\"快速上课\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_icon_park_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                theme: \"filled\",\n                                size: 20,\n                                fill: [\n                                    \"#ffffff\"\n                                ],\n                                className: \"start-class-icon\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"快速上课\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                                lineNumber: 290,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                lineNumber: 283,\n                columnNumber: 7\n            }, undefined),\n            activeView === \"模板管理\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TemplateManagement__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                selectedSchool: selectedSchool,\n                userInfo: userInfo\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                lineNumber: 296,\n                columnNumber: 9\n            }, undefined) : activeView === \"班级管理\" ? showClassDetail && selectedClassForDetail && selectedSchool ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ClassDetail__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                classInfo: selectedClassForDetail,\n                selectedSchool: selectedSchool,\n                onBack: handleBackToClassManagement,\n                onClassInfoUpdate: handleClassInfoUpdate,\n                onClassDeleted: handleClassDeleted\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                lineNumber: 299,\n                columnNumber: 11\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ClassManagement__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                selectedSchool: selectedSchool,\n                userInfo: userInfo,\n                classes: classes,\n                classesLoading: classesLoading,\n                classesError: classesError,\n                onClassClick: handleClassClick,\n                onCloseDropdown: onCloseDropdown,\n                onAddClass: handleAddClass\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                lineNumber: 307,\n                columnNumber: 11\n            }, undefined) : activeView === \"班级任务\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ClassTasks__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                lineNumber: 319,\n                columnNumber: 9\n            }, undefined) : activeView === \"课程管理\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CourseManagement__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                lineNumber: 321,\n                columnNumber: 9\n            }, undefined) : activeView === \"班级项目\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ClassProjects__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                lineNumber: 323,\n                columnNumber: 9\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_QuickActions__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                        lineNumber: 326,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_OngoingTasks__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                        lineNumber: 327,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SchoolSelectionModal__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                isOpen: isSchoolModalOpen,\n                onClose: handleCloseModal,\n                actionType: currentActionType,\n                onSchoolSelect: handleSchoolSelect\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                lineNumber: 331,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ClassSelectionModal__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                isOpen: isClassModalOpen,\n                onClose: handleCloseModal,\n                onBack: handleBackToSchool,\n                actionType: currentActionType,\n                selectedSchool: modalSelectedSchool,\n                onClassSelect: handleClassSelect\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                lineNumber: 338,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TemplateSelectionModal__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                isOpen: isTemplateModalOpen,\n                onClose: handleCloseModal,\n                onBack: handleBackToClass,\n                onConfirm: handleTemplateConfirm,\n                actionType: currentActionType,\n                selectedSchool: modalSelectedSchool,\n                selectedClass: selectedClass\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                lineNumber: 347,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_teacher_space_components_modals_create_class_modal__WEBPACK_IMPORTED_MODULE_13__.CreateClassModal, {\n                visible: isCreateClassModalOpen,\n                onCancel: handleCreateClassModalClose,\n                onOk: handleCreateClass\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                lineNumber: 357,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n        lineNumber: 282,\n        columnNumber: 5\n    }, undefined);\n};\n_s(MainContent, \"vuiDOKB9kf9CftAMGz7Fil9059k=\", false, function() {\n    return [\n        _contexts_TemplateContext__WEBPACK_IMPORTED_MODULE_18__.useTemplate\n    ];\n});\n_c = MainContent;\n/* harmony default export */ __webpack_exports__[\"default\"] = (MainContent);\nvar _c;\n$RefreshReg$(_c, \"MainContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/workbench/components/MainContent.tsx\n"));

/***/ })

});