"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomTypeOrmLogger = void 0;
const common_1 = require("@nestjs/common");
class CustomTypeOrmLogger {
    logger = new common_1.Logger('TypeORM');
    queryInterceptor;
    constructor(queryInterceptor) {
        this.queryInterceptor = queryInterceptor;
    }
    logQuery(query, parameters, queryRunner) {
        let queryId;
        if (this.queryInterceptor) {
            const context = this.getQueryContext();
            queryId = this.queryInterceptor.beforeQuery(query, parameters, context);
        }
        if (queryRunner && queryId) {
            queryRunner.__queryId = queryId;
            queryRunner.__queryStartTime = Date.now();
        }
    }
    logQueryError(error, query, parameters, queryRunner) {
        const errorObj = typeof error === 'string' ? new Error(error) : error;
        if (this.queryInterceptor && queryRunner) {
            const queryId = queryRunner.__queryId;
            if (queryId) {
                this.queryInterceptor.afterSlowQuery(queryId, errorObj);
            }
        }
        this.logger.error(`查询执行错误: ${errorObj.message}`, {
            query: this.formatQuery(query),
            parameters: this.formatParameters(parameters),
            error: errorObj.message,
            stack: errorObj.stack
        });
    }
    logQuerySlow(time, query, parameters, queryRunner) {
        console.log(`🐌 logQuerySlow 被调用了！执行时间: ${time}ms`);
        if (this.queryInterceptor && queryRunner) {
            const queryId = queryRunner.__queryId;
            if (queryId) {
                console.log(`🎯 调用 afterSlowQuery，queryId: ${queryId}`);
                this.queryInterceptor.afterSlowQuery(queryId);
            }
            else {
                console.log(`❌ queryId 不存在，无法调用 afterSlowQuery`);
            }
        }
        else {
            console.log(`❌ queryInterceptor 或 queryRunner 不存在`);
        }
        this.logger.warn(`慢查询检测 [${time}ms]: ${this.formatQuery(query)}`, {
            executionTime: time,
            parameters: this.formatParameters(parameters)
        });
    }
    logSchemaBuild(message) {
        this.logger.log(`Schema: ${message}`);
    }
    logMigration(message) {
        this.logger.log(`Migration: ${message}`);
    }
    logQuerySuccess(query, parameters, queryRunner) {
        if (this.queryInterceptor && queryRunner) {
            const queryId = queryRunner.__queryId;
            const startTime = queryRunner.__queryStartTime;
            if (queryId && startTime) {
                this.queryInterceptor.afterSlowQuery(queryId);
                delete queryRunner.__queryId;
                delete queryRunner.__queryStartTime;
            }
        }
    }
    log(level, message, queryRunner) {
        switch (level) {
            case 'log':
                this.logger.log(message);
                break;
            case 'info':
                this.logger.log(message);
                break;
            case 'warn':
                this.logger.warn(message);
                break;
        }
    }
    formatQuery(query) {
        if (!query)
            return '';
        const formatted = query.replace(/\s+/g, ' ').trim();
        const maxLength = 200;
        if (formatted.length > maxLength) {
            return formatted.substring(0, maxLength) + '...';
        }
        return formatted;
    }
    formatParameters(parameters) {
        if (!parameters || parameters.length === 0)
            return [];
        return parameters.map(param => {
            if (typeof param === 'string' && param.length > 50) {
                return param.substring(0, 50) + '...';
            }
            if (typeof param === 'object' && param !== null) {
                try {
                    const str = JSON.stringify(param);
                    if (str.length > 100) {
                        return str.substring(0, 100) + '...';
                    }
                    return param;
                }
                catch {
                    return '[Object]';
                }
            }
            return param;
        });
    }
    getQueryContext() {
        const stack = new Error().stack;
        if (!stack)
            return '';
        const lines = stack.split('\n');
        for (let i = 1; i < lines.length; i++) {
            const line = lines[i];
            if (line &&
                !line.includes('typeorm') &&
                !line.includes('CustomTypeOrmLogger') &&
                !line.includes('QueryInterceptor')) {
                const match = line.match(/at\s+(.+)\s+\((.+):(\d+):(\d+)\)/);
                if (match) {
                    const [, functionName, filePath, lineNumber] = match;
                    const fileName = filePath.split('/').pop() || filePath.split('\\').pop();
                    return `${functionName} (${fileName}:${lineNumber})`;
                }
            }
        }
        return 'Unknown';
    }
}
exports.CustomTypeOrmLogger = CustomTypeOrmLogger;
//# sourceMappingURL=typeorm-logger.js.map