import { TypeOrmModuleOptions } from '@nestjs/typeorm';
import { YamlService } from '../../yaml/yaml.service';
import { RedisOptions } from 'ioredis';
export declare class DatabaseConfigService {
    private readonly yamlService;
    private readonly databasesConfig;
    private customLogger?;
    constructor(yamlService: YamlService);
    getMysqlConfig(queryInterceptor?: any): TypeOrmModuleOptions;
    getRedisConfig(): RedisOptions;
}
