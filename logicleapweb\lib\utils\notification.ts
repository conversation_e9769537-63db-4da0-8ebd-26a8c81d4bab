import { GetNotification } from 'logic-common/dist/components/Notification';

/**
 * 统一的消息提示工具类 - 单例模式
 */
class NotificationUtil {
    // 单例实例
    private static instance: NotificationUtil | null = null;
    // 通知对象，只获取一次
    private notification;

    /**
     * 获取单例实例
     */
    public static getInstance(): NotificationUtil {
        if (!NotificationUtil.instance) {
            NotificationUtil.instance = new NotificationUtil();
        }
        return NotificationUtil.instance;
    }

    // 私有构造函数，防止外部直接创建实例
    private constructor() {
        // 只在初始化时获取一次通知对象
        this.notification = GetNotification();
    }

    /**
     * 显示消息提示
     * @param {string} message - 消息内容
     * @param {string} type - 消息类型 ('success' | 'error' | 'info' | 'warning')
     * @param {number} duration - 显示时长(毫秒)，默认3000ms
     */
    show(message: string, type: 'success' | 'error' | 'info' | 'warning' = 'info', duration?: number) {
        this.notification.showAutoClose(message, type, duration);
    }

    /**
     * 显示成功消息
     * @param {string} message - 消息内容
     * @param {number} duration - 显示时长(毫秒)，可选
     */
    success(message: string, duration?: number) {
        this.notification.success(message, duration);
    }

    /**
     * 显示错误消息
     * @param {string} message - 消息内容
     * @param {number} duration - 显示时长(毫秒)，可选
     */
    error(message: string, duration?: number) {
        this.notification.error(message, duration);
    }

    /**
     * 显示警告消息
     * @param {string} message - 消息内容
     * @param {number} duration - 显示时长(毫秒)，可选
     */
    warning(message: string, duration?: number) {
        this.notification.warning(message, duration);
    }

    /**
     * 显示信息消息
     * @param {string} message - 消息内容
     * @param {number} duration - 显示时长(毫秒)，可选
     */
    info(message: string, duration?: number) {
        this.notification.info(message, duration);
    }
}

// 创建一个简便的导出方式
export default NotificationUtil.getInstance();

// 同时导出类，便于扩展
export { NotificationUtil }; 