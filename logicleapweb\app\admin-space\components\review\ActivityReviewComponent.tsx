import React from 'react';
import { Tag } from 'antd';
import { BaseReviewComponent, BaseReviewItem } from './BaseReviewComponent';
import { activityApi, activityAuditApi, AuditResult } from '@/lib/api/activity';
import request from '../../../../lib/request';

// 活动审核项接口
export interface ActivityReviewItem extends BaseReviewItem {
  name: string;
  startTime: string;
  endTime: string;
  coverImage?: string;
  detailDocId?: string;
  rulesDocId?: string;
  awardsDocId?: string;
  organizer: string;
  creatorId: number;
  activityType?: number;
  imageSettings?: string;
  reviewReason?: string;
  auditRecords?: any[];
}

const ActivityReviewComponent: React.FC = () => {
  // 格式化时间
  const formatDate = (dateString: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
  };

  // 获取审核列表
  const fetchActivityList = async (): Promise<ActivityReviewItem[]> => {
    try {
      // 获取所有状态的活动
      const pendingRes = await activityAuditApi.getAllActivity();
      const pendingActivities = pendingRes?.data?.data || [];

      // 获取已通过的审核记录对应的活动
      const approvedRes = await activityAuditApi.getList({ result: AuditResult.APPROVED });
      let approvedActivities: any[] = [];
      if (approvedRes?.data?.data && Array.isArray(approvedRes.data.data)) {
        const activityIdMap = new Map();
        approvedRes.data.data.forEach((audit: any) => {
          if (audit && audit.activityId) {
            activityIdMap.set(audit.activityId, true);
          }
        });

        approvedActivities = await Promise.all(
          Array.from(activityIdMap.keys()).map((id: number) => fetchActivityDetail(id))
        );
      }

      // 获取已拒绝的审核记录对应的活动
      const rejectedRes = await activityAuditApi.getList({ result: AuditResult.REJECTED });
      let rejectedActivities: any[] = [];
      if (rejectedRes?.data?.data && Array.isArray(rejectedRes.data.data)) {
        const activityIdMap = new Map();
        rejectedRes.data.data.forEach((audit: any) => {
          if (audit && audit.activityId) {
            activityIdMap.set(audit.activityId, true);
          }
        });

        rejectedActivities = await Promise.all(
          Array.from(activityIdMap.keys()).map((id: number) => fetchActivityDetail(id))
        );
      }

      // 合并并过滤掉空值
      const activities = [
        ...pendingActivities,
        ...approvedActivities.filter(Boolean),
        ...rejectedActivities.filter(Boolean)
      ];

      // 去重
      return Array.from(
        new Map(activities.map((item: ActivityReviewItem) => [item.id, item])).values()
      );
    } catch (error) {
      console.error('获取活动列表失败:', error);
      return [];
    }
  };

  // 获取单个活动详情和审核记录
  const fetchActivityDetail = async (id: number) => {
    try {
      // 获取活动详情
      const activityRes = await activityAuditApi.getActivityInfo(id);
      if (!activityRes?.data) return null;

      return activityRes.data;
    } catch (error) {
      console.error('获取活动详情失败:', error);
      return null;
    }
  };

  // 获取活动审核记录
  const fetchActivityAuditRecords = async (activityId: number) => {
    try {
      const auditRes = await activityAuditApi.getActivityAuditById(activityId) as any;
      return auditRes?.data?.data || [];
    } catch (error) {
      console.error('获取审核记录失败:', error);
      return [];
    }
  };

  // 执行活动审核
  const reviewActivity = async (id: number, isApproved: boolean, reason?: string) => {
    try {
      return await activityApi.reviewActivity(id, { isApproved, reason });
    } catch (error) {
      console.error('审核活动失败:', error);
      throw error;
    }
  };

  // 自定义渲染活动详情
  const renderActivityDetail = (activity: ActivityReviewItem) => {
    return (
      <div className="bg-blue-50/50 rounded-xl p-6">
        <h1 className="text-xl font-bold text-gray-800 mb-3">
          {activity.name}
        </h1>
        <div className="flex items-center gap-4 text-sm text-gray-500 flex-wrap">
          <span>创建人：{activity.organizer}</span>
          <span>•</span>
          <span>创建时间：{formatDate(activity.createTime)}</span>
          <span>•</span>
          <span>活动类型：{activity.activityType === 1 ? '作品活动' : '图片活动'}</span>
        </div>
        <div className="mt-4 text-sm text-gray-500">
          <div>活动时间：{formatDate(activity.startTime)} 至 {formatDate(activity.endTime)}</div>
          {activity.coverImage && (
            <div className="mt-4">
              <img
                src={activity.coverImage}
                alt="活动封面"
                className="w-full max-w-[300px] rounded-lg"
              />
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <BaseReviewComponent<ActivityReviewItem>
      title="活动审核"
      fetchList={fetchActivityList}
      reviewApi={reviewActivity}
      getItemName={(item) => item.name}
      getItemCreator={(item) => item.organizer}
      renderDetailView={renderActivityDetail}
      fetchAuditRecords={fetchActivityAuditRecords}
    />
  );
};

export default ActivityReviewComponent; 