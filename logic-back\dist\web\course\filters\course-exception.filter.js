"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var CourseExceptionFilter_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CourseExceptionFilter = void 0;
const common_1 = require("@nestjs/common");
let CourseExceptionFilter = CourseExceptionFilter_1 = class CourseExceptionFilter {
    logger = new common_1.Logger(CourseExceptionFilter_1.name);
    catch(exception, host) {
        const ctx = host.switchToHttp();
        const response = ctx.getResponse();
        const request = ctx.getRequest();
        this.logException(exception, request);
        const result = this.handleException(exception);
        response.status(result.statusCode).json(result.body);
    }
    logException(exception, request) {
        const { method, url } = request;
        const message = exception?.message || '未知错误';
        this.logger.error(`${method} ${url} - ${message}`, exception?.stack || exception);
    }
    handleException(exception) {
        if (exception instanceof common_1.HttpException) {
            const status = exception.getStatus();
            const message = exception.message;
            return {
                statusCode: status,
                body: this.createErrorResponse(message, status)
            };
        }
        const message = exception?.message || '服务器内部错误';
        if (message.includes('不存在')) {
            return {
                statusCode: common_1.HttpStatus.NOT_FOUND,
                body: this.createErrorResponse(message, 404)
            };
        }
        if (message.includes('已存在')) {
            return {
                statusCode: common_1.HttpStatus.BAD_REQUEST,
                body: this.createErrorResponse(message, 400)
            };
        }
        if (message.includes('正在被') && message.includes('使用')) {
            return {
                statusCode: common_1.HttpStatus.BAD_REQUEST,
                body: this.createErrorResponse(message, 400)
            };
        }
        if (message.includes('无权限') || message.includes('权限不足')) {
            return {
                statusCode: common_1.HttpStatus.FORBIDDEN,
                body: this.createErrorResponse(message, 403)
            };
        }
        if (message.includes('不能为空') || message.includes('必须') || message.includes('无效')) {
            return {
                statusCode: common_1.HttpStatus.BAD_REQUEST,
                body: this.createErrorResponse(message, 400)
            };
        }
        if (this.isDatabaseError(exception)) {
            return {
                statusCode: common_1.HttpStatus.INTERNAL_SERVER_ERROR,
                body: this.createErrorResponse('数据库操作失败，请稍后重试', 500)
            };
        }
        return {
            statusCode: common_1.HttpStatus.INTERNAL_SERVER_ERROR,
            body: this.createErrorResponse('服务器内部错误，请稍后重试', 500)
        };
    }
    createErrorResponse(message, statusCode) {
        return {
            success: false,
            message: message,
            data: null,
            statusCode: statusCode,
            timestamp: new Date().toISOString()
        };
    }
    isDatabaseError(exception) {
        const errorCode = exception?.code;
        const errorMessage = exception?.message?.toLowerCase() || '';
        const mysqlErrorCodes = ['ER_DUP_ENTRY', 'ER_NO_REFERENCED_ROW', 'ER_ROW_IS_REFERENCED'];
        const typeormErrors = ['queryrunner', 'connection', 'transaction'];
        return (mysqlErrorCodes.includes(errorCode) ||
            typeormErrors.some(keyword => errorMessage.includes(keyword)) ||
            errorMessage.includes('duplicate') ||
            errorMessage.includes('foreign key'));
    }
};
exports.CourseExceptionFilter = CourseExceptionFilter;
exports.CourseExceptionFilter = CourseExceptionFilter = CourseExceptionFilter_1 = __decorate([
    (0, common_1.Catch)()
], CourseExceptionFilter);
//# sourceMappingURL=course-exception.filter.js.map