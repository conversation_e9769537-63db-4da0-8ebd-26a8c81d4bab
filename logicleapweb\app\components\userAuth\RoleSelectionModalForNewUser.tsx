import React, { useState, useEffect } from 'react';
import styles from '@/components/login-dialog.module.css';
import userApi from '@/lib/api/user';
import { useSelector } from 'react-redux';
import { UserState } from '../../../types/user';
import { RootState } from '../../../lib/store';
import { Button, notification, Spin } from 'antd';
import { GetNotification } from 'logic-common/dist/components/Notification';
import TeacherAuthModal from './teacherAuthModal';
import { UserOutlined, BookOutlined, TeamOutlined } from '@ant-design/icons';

// 组件的props类型定义
interface RsmfnProps {
  // 成功回调函数，为静态类型的
  onSuccess: () => void
  // 关闭当前模态框回调函数
  handleCloseRsmfnModal: () => void
}

// 角色Id的map映射
const roleIdMap = {
  'student': 1, // 学生角色Id
  'teacher': 2, // 教师角色Id
  'normal': 3, // 普通用户角色Id
}

// 定义角色key类型
type RoleType = keyof typeof roleIdMap;

// 组件的dom,箭头函数的形参传入成功执行后的回调函数
const RoleSelectionModalForNewUser = ({ onSuccess, handleCloseRsmfnModal }: RsmfnProps) => {
  // 从redux中获取userId
  const user = useSelector((state: RootState) => state.user.userState)

  // 教师认证模态框可见的布尔值
  const [isTeacherAuthModalVisible, setIsTeacherAuthModalVisible] = useState(false);
  // 提交状态
  const [isSubmitting, setIsSubmitting] = useState(false);

  // 组件内注册弹框
  const nt = GetNotification()

  // 输出用于调试
  useEffect(() => {
    console.log('RoleSelectionModalForNewUser 组件已加载');
    console.log('当前用户信息:', user);
  }, [user]);

  //  逻辑层

  //类型只能是map映射里的key
  const [selectedRole, setSelectedRole] = useState<RoleType | ''>('');

  const handleSubmit = async () => {
    if (selectedRole) {
      setIsSubmitting(true);
      try {
        console.log("准备分配角色:", selectedRole);
        // 根据key映射对应的roleID
        const selectedRoleId = roleIdMap[selectedRole];
        console.log("映射的roleId:", selectedRoleId);
        
        // 如果是普通用户3或者是学生1就直接更新用户角色，
        if (selectedRoleId == 1 || selectedRoleId == 3) {
          console.log("分配角色, userId:", user.userId, "roleId:", selectedRoleId);
          // 如果当前redux里没有userId则从localstorage里拿
          const res = await userApi.assignRole(user.userId, selectedRoleId);
          console.log("分配角色响应:", res);
          
          if (res.data.code === 200) {
            nt.success('身份绑定成功！')
            //调用父组件传给子组件的回调函数改变父组件的值关闭弹框
            console.log("调用onSuccess回调");
            if (onSuccess) onSuccess();
          } else {
            console.error("角色分配API响应错误:", res.data);
            nt.error(res.data.message || '身份绑定失败，请稍后重试');
          }
        }
        // 如果是教师就弹出教师认证，
        if (selectedRoleId === 2) {
          console.log("选择教师角色，显示教师认证弹窗");
          setIsTeacherAuthModalVisible(true);
        }
      } catch (error) {
        console.error('角色选择失败:', error);
        nt.error('身份绑定失败，请稍后重试');
      } finally {
        setIsSubmitting(false);
      }
    } else {
      nt.warning('请选择一个身份');
    }
  }

  // 角色卡片配置
  const roleCards = [
    {
      type: 'normal',
      title: '普通用户',
      icon: <UserOutlined style={{ fontSize: '36px' }} />,
      description: '体验基础功能'
    },
    {
      type: 'student',
      title: '学生',
      icon: <BookOutlined style={{ fontSize: '36px' }} />,
      description: '学习与成长'
    },
    {
      type: 'teacher',
      title: '教师',
      icon: <TeamOutlined style={{ fontSize: '36px' }} />,
      description: '教学与管理'
    }
  ];

  //  视图层
  return (
    <>
      {/* wrapper包装器 同时起一个模态框的遮罩层作用*/}
      <div
        className="fixed inset-0  flex items-center justify-center bg-black bg-opacity-50 transition-opacity duration-300"
        style={{
          zIndex: 999
        }}
        onClick={handleCloseRsmfnModal}
      >
        {/* 选择框-位于正中央 */}
        <div
          className="w-[700px] max-w-[90vw] bg-white rounded-2xl shadow-2xl transform transition-all duration-300 ease-in-out"
          onClick={(e) => e.stopPropagation()} // 阻止事件冒泡到遮罩层
        >
          {/* 弹框信息 */}
          <div className="py-8 text-center">
            <h2 className="text-2xl font-bold text-gray-800">请确认你的身份</h2>
            <p className="mt-2 text-gray-500">选择一个适合您的身份，以获得相应的功能和权限</p>
          </div>

          {/* 角色卡片wrapper */}
          <div className="flex flex-wrap justify-center gap-6 px-6 pb-6">
            {roleCards.map((card) => (
              <div
                key={card.type}
                className={`w-[180px] p-6 rounded-xl cursor-pointer transition-all duration-300 transform hover:scale-105 ${selectedRole === card.type
                  ? 'bg-blue-500 text-white shadow-lg'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                onClick={() => {
                  setSelectedRole(selectedRole === card.type ? '' : card.type as RoleType);
                }}
              >
                <div className="flex flex-col items-center text-center">
                  <div className={`mb-4 ${selectedRole === card.type ? 'text-white' : 'text-blue-500'}`}>
                    {card.icon}
                  </div>
                  <h3 className="text-lg font-medium mb-2">{card.title}</h3>
                  <p className={`text-sm ${selectedRole === card.type ? 'text-blue-100' : 'text-gray-500'}`}>
                    {card.description}
                  </p>
                </div>
              </div>
            ))}
          </div>

          {/* 确定按钮区域 */}
          <div className="flex justify-center py-6 border-t border-gray-100">
            <Button
              type="primary"
              size="large"
              className="px-8 h-10 rounded-lg"
              style={{
                backgroundColor: selectedRole ? '#1890ff' : '#d9d9d9',
                borderColor: selectedRole ? '#1890ff' : '#d9d9d9'
              }}
              onClick={handleSubmit}
              disabled={!selectedRole || isSubmitting}
            >
              {isSubmitting ? <Spin size="small" /> : '确认选择'}
            </Button>
          </div>
        </div>
      </div>

      {/* 教师认证模态框 */}
      <TeacherAuthModal
        handleCloseTeacherAuthModal={() => setIsTeacherAuthModalVisible(false)}
        visible={isTeacherAuthModalVisible}
        onSuccess={() => { onSuccess() }} />
    </>
  )
}

export default RoleSelectionModalForNewUser;