'use client'

import React, { useState, useEffect, useCallback } from 'react'
import Image from 'next/image'
import { courseApi } from '@/lib/api/course'
import type { CourseSeriesDetail } from '@/lib/api/course'

interface SeriesItem {
  id: number;
  title: string;
  description: string;
  coverImage?: string;
  courseCount: number;
  category: string;
  createdAt: string;
}

interface LazySeriesListProps {
  title?: string;
  pageSize?: number;
  className?: string;
}

export default function LazySeriesList({ 
  title = "创作的系列课程作品", 
  pageSize = 6,
  className = ""
}: LazySeriesListProps) {
  const [seriesList, setSeriesList] = useState<SeriesItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(1);
  const [error, setError] = useState<string | null>(null);

  // 加载系列课程数据
  const loadSeriesData = useCallback(async (pageNum: number, isLoadMore = false) => {
    if (loading) return;
    
    try {
      setLoading(true);
      setError(null);

      // 模拟API调用 - 实际应该调用系列课程列表接口
      const mockSeriesIds = Array.from({ length: 20 }, (_, i) => i + 1);
      const startIndex = (pageNum - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const currentPageIds = mockSeriesIds.slice(startIndex, endIndex);

      if (currentPageIds.length === 0) {
        setHasMore(false);
        return;
      }

      const seriesPromises = currentPageIds.map(async (id) => {
        try {
          // 使用课程市场API获取系列详情
          const { data: res } = await courseApi.getMarketplaceSeriesDetail(id);
          if (res.code === 200 && res.data) {
            return {
              id: res.data.id,
              title: res.data.title,
              description: res.data.description,
              coverImage: res.data.coverImage,
              courseCount: res.data.courseCount || res.data.courses?.length || Math.floor(Math.random() * 10 + 3),
              category: res.data.category === 0 ? '社区' : '官方',
              createdAt: res.data.createdAt
            };
          }
          return null;
        } catch (error) {
          console.error(`获取系列课程 ${id} 失败:`, error);
          // 返回模拟数据
          return {
            id,
            title: `系列课程 ${id}`,
            description: `这是第 ${id} 个系列课程的描述，包含多个相关的学习内容。`,
            coverImage: `/images/course-covers/series-${id % 5 + 1}.jpg`,
            courseCount: Math.floor(Math.random() * 10 + 3),
            category: Math.random() > 0.5 ? '社区' : '官方',
            createdAt: new Date().toISOString()
          };
        }
      });

      const results = await Promise.all(seriesPromises);
      const validSeries = results.filter(series => series !== null) as SeriesItem[];

      if (isLoadMore) {
        setSeriesList(prev => [...prev, ...validSeries]);
      } else {
        setSeriesList(validSeries);
      }

      // 检查是否还有更多数据
      if (validSeries.length < pageSize) {
        setHasMore(false);
      }

    } catch (error) {
      console.error('加载系列课程失败:', error);
      setError('加载失败，请重试');
    } finally {
      setLoading(false);
    }
  }, [loading, pageSize]);

  // 初始加载
  useEffect(() => {
    loadSeriesData(1);
  }, [loadSeriesData]);

  // 加载更多
  const handleLoadMore = () => {
    if (!loading && hasMore) {
      const nextPage = page + 1;
      setPage(nextPage);
      loadSeriesData(nextPage, true);
    }
  };

  return (
    <div className={`w-full ${className}`}>
      <h2 className="text-2xl font-bold text-gray-900 mb-6">{title}</h2>
      
      {/* 系列课程网格 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        {seriesList.map((series) => (
          <div 
            key={series.id} 
            className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden hover:shadow-md transition-shadow duration-200 cursor-pointer"
          >
            {/* 封面图片 */}
            <div className="h-48 relative bg-gradient-to-br from-gray-50 to-gray-100">
              {series.coverImage ? (
                <Image
                  src={series.coverImage}
                  alt={series.title}
                  fill
                  className="object-cover"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.style.display = 'none';
                  }}
                />
              ) : (
                <div className="w-full h-full bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center">
                  <svg className="w-16 h-16 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd"/>
                  </svg>
                </div>
              )}
              
              {/* 分类标签 */}
              <div className="absolute top-3 left-3">
                <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                  series.category === '官方' 
                    ? 'bg-blue-100 text-blue-800' 
                    : 'bg-green-100 text-green-800'
                }`}>
                  {series.category}
                </span>
              </div>
            </div>
            
            {/* 课程信息 */}
            <div className="p-4">
              <h3 className="font-medium text-gray-900 mb-2 line-clamp-2">{series.title}</h3>
              <p className="text-sm text-gray-600 mb-3 line-clamp-2">{series.description}</p>
              <div className="flex items-center justify-between text-sm text-gray-500">
                <span>{series.courseCount} 个课程</span>
                <span>{new Date(series.createdAt).toLocaleDateString()}</span>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* 加载状态 */}
      {loading && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {Array.from({ length: pageSize }).map((_, i) => (
            <div key={i} className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
              <div className="h-48 bg-gray-200 animate-pulse"></div>
              <div className="p-4">
                <div className="h-4 bg-gray-200 rounded animate-pulse mb-2"></div>
                <div className="h-3 bg-gray-200 rounded animate-pulse mb-3 w-3/4"></div>
                <div className="flex justify-between">
                  <div className="h-3 bg-gray-200 rounded animate-pulse w-16"></div>
                  <div className="h-3 bg-gray-200 rounded animate-pulse w-20"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* 错误状态 */}
      {error && (
        <div className="text-center py-8">
          <p className="text-red-600 mb-4">{error}</p>
          <button 
            onClick={() => loadSeriesData(page)}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            重试
          </button>
        </div>
      )}

      {/* 加载更多按钮 */}
      {!loading && hasMore && !error && (
        <div className="text-center">
          <button 
            onClick={handleLoadMore}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
          >
            加载更多 ({pageSize} 个)
          </button>
        </div>
      )}

      {/* 没有更多数据 */}
      {!loading && !hasMore && seriesList.length > 0 && (
        <div className="text-center py-4">
          <p className="text-gray-500">已显示全部内容</p>
        </div>
      )}

      {/* 空状态 */}
      {!loading && seriesList.length === 0 && !error && (
        <div className="text-center py-12">
          <svg className="w-16 h-16 text-gray-300 mx-auto mb-4" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd"/>
          </svg>
          <p className="text-gray-500">暂无系列课程</p>
        </div>
      )}
    </div>
  );
}
