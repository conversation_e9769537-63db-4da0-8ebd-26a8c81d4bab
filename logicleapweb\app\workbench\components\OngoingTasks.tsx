'use client';

import React, { useState, useEffect, useRef } from 'react';
import { ChevronDown, Star, RefreshCw } from 'lucide-react';
import taskApi, { TaskStatus } from '@/lib/api/task';
import { schoolApi } from '@/lib/api/school';
import { GetNotification } from 'logic-common/dist/components/Notification';
import taskEventManager, { TASK_EVENTS } from '@/app/utils/task-event-manager';

interface OngoingTask {
  id: number;
  taskName: string;
  taskDescription: string;
  endDate: Date;
  startDate: Date;
  createTime?: Date; // 添加创建时间字段用于排序
  classId?: number; // 添加班级ID字段
  assignments: Array<{
    id: number;
    taskStatus: number;
    studentId: number;
  }>;
  classInfo?: {
    className: string;
    schoolName?: string;
  };
}

const OngoingTasks = () => {
  const [tasks, setTasks] = useState<OngoingTask[]>([]);
  const [loading, setLoading] = useState(true);
  const notification = GetNotification();
  // 移除对 taskPublishVersion 的依赖，只使用全局事件
  // const { taskPublishVersion } = useTemplate();

  // 使用 ref 来跟踪刷新状态
  const refreshTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 检查两个时间区间是否重叠
  const isTimeRangeOverlapping = (
    start1: Date,
    end1: Date,
    start2: Date,
    end2: Date
  ): boolean => {
    return start1 <= end2 && start2 <= end1;
  };

  // 按任务名称和时间区间去重
  const deduplicateTasksByNameAndTimeRange = (tasks: OngoingTask[]): OngoingTask[] => {
    const taskMap = new Map<string, OngoingTask[]>();

    // 按任务名称分组
    tasks.forEach(task => {
      const taskName = task.taskName.trim();
      if (!taskMap.has(taskName)) {
        taskMap.set(taskName, []);
      }
      taskMap.get(taskName)!.push(task);
    });

    const result: OngoingTask[] = [];

    // 对每个任务名称组进行时间区间去重
    taskMap.forEach((taskGroup, taskName) => {
      if (taskGroup.length === 1) {
        // 只有一个任务，直接添加
        result.push(taskGroup[0]);
        return;
      }

      // 多个同名任务，需要检查时间区间重叠
      const nonOverlappingTasks: OngoingTask[] = [];

      for (const currentTask of taskGroup) {
        const currentStart = new Date(currentTask.startDate);
        const currentEnd = new Date(currentTask.endDate);

        // 检查当前任务是否与已选择的任务时间重叠
        const hasOverlap = nonOverlappingTasks.some(existingTask => {
          const existingStart = new Date(existingTask.startDate);
          const existingEnd = new Date(existingTask.endDate);
          return isTimeRangeOverlapping(currentStart, currentEnd, existingStart, existingEnd);
        });

        if (!hasOverlap) {
          // 没有重叠，可以添加
          nonOverlappingTasks.push(currentTask);
        } else {
          // 有重叠，选择更新的任务（ID更大的）
          const overlappingTaskIndex = nonOverlappingTasks.findIndex(existingTask => {
            const existingStart = new Date(existingTask.startDate);
            const existingEnd = new Date(existingTask.endDate);
            return isTimeRangeOverlapping(currentStart, currentEnd, existingStart, existingEnd);
          });

          if (overlappingTaskIndex !== -1) {
            const existingTask = nonOverlappingTasks[overlappingTaskIndex];

            // 优先使用创建时间判断哪个任务更新，如果没有创建时间则使用ID
            const currentTime = currentTask.createTime ? new Date(currentTask.createTime).getTime() : currentTask.id;
            const existingTime = existingTask.createTime ? new Date(existingTask.createTime).getTime() : existingTask.id;

            // 如果当前任务更新（创建时间更晚或ID更大），则替换
            if (currentTime > existingTime) {
              nonOverlappingTasks[overlappingTaskIndex] = currentTask;
            }
          }
        }
      }

      result.push(...nonOverlappingTasks);
    });

    console.log(`任务去重：原始 ${tasks.length} 个任务，去重后 ${result.length} 个任务`);
    return result;
  };

  // 批量获取班级和学校信息
  const getClassAndSchoolInfoBatch = async (classIds: number[]) => {
    try {
      // 获取用户的学校列表
      const schoolsResponse = await schoolApi.getUserSchools();
      if (schoolsResponse.data.code !== 200) {
        return {};
      }

      const schools = schoolsResponse.data.data || [];
      const classInfoMap: { [classId: number]: { className: string; schoolName: string } } = {};

      // 遍历学校，获取所有班级信息
      for (const school of schools) {
        try {
          const classesResponse = await schoolApi.getSchoolClasses({
            schoolId: school.id,
            page: 1,
            size: 100
          });

          if (classesResponse.data.code === 200) {
            const classes = classesResponse.data.data?.list || classesResponse.data.data || [];

            // 为每个需要的班级ID创建映射
            classes.forEach((cls: any) => {
              if (classIds.includes(cls.id)) {
                classInfoMap[cls.id] = {
                  className: cls.className,
                  schoolName: school.schoolName
                };
              }
            });
          }
        } catch (error) {
          console.error(`获取学校 ${school.id} 的班级信息失败:`, error);
        }
      }

      return classInfoMap;
    } catch (error) {
      console.error('批量获取班级和学校信息失败:', error);
      return {};
    }
  };

  // 获取进行中的任务
  const fetchOngoingTasks = async () => {
    try {
      setLoading(true);

      // 获取当前用户信息
      const userData = localStorage.getItem('user');
      const user = userData ? JSON.parse(userData) : null;
      const teacherId = user?.userId;

      if (!teacherId) {
        notification.error('未找到用户信息');
        return;
      }

      const response = await taskApi.getTaskList({
        teacherId, // 获取当前教师的任务
        page: 1,
        size: 50,
        orderBy: 'createTime:DESC' // 按创建时间降序排序，获取最新发布的任务
      });

      if (response.data.code === 200) {
        const taskList = response.data.data.list || [];

        // 过滤出结束时间还没结束的任务（进行中的任务）
        const ongoingTasks = taskList.filter((task: OngoingTask) => {
          const endDate = new Date(task.endDate);
          const startDate = new Date(task.startDate);
          const now = new Date();

          // 任务已开始且还没结束
          return startDate <= now && endDate > now;
        });

        // 去重处理：按任务名称和时间区间去重
        const deduplicatedTasks = deduplicateTasksByNameAndTimeRange(ongoingTasks);

        console.log('OngoingTasks - 去重前任务数量:', ongoingTasks.length, '去重后任务数量:', deduplicatedTasks.length);

        // 按创建时间排序：最新发布的任务排在最上面
        const sortedTasks = deduplicatedTasks.sort((a, b) => {
          // 优先使用 createTime，如果没有则使用 startDate 作为备选
          const timeA = a.createTime ? new Date(a.createTime).getTime() : new Date(a.startDate).getTime();
          const timeB = b.createTime ? new Date(b.createTime).getTime() : new Date(b.startDate).getTime();

          // 降序排列：最新的在前面
          return timeB - timeA;
        });

        console.log('OngoingTasks - 按发布时间排序的任务列表:', sortedTasks.map((task, index) => ({
          排序位置: index + 1,
          任务ID: task.id,
          任务名称: task.taskName,
          创建时间: task.createTime,
          开始时间: task.startDate,
          使用时间: task.createTime ? '创建时间' : '开始时间'
        })));

        // 批量获取班级和学校信息
        const classIds = [...new Set(sortedTasks.map(task => task.classId).filter(Boolean))];
        const classInfoMap = await getClassAndSchoolInfoBatch(classIds);

        // 为每个任务添加班级和学校信息
        const tasksWithClassInfo = sortedTasks.map(task => ({
          ...task,
          classInfo: task.classId ? classInfoMap[task.classId] : null
        }));

        setTasks(tasksWithClassInfo);
      } else {
        notification.error('获取任务列表失败');
      }
    } catch (error) {
      console.error('获取进行中任务失败:', error);
      notification.error('获取任务列表失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchOngoingTasks();
  }, []);

  // 防抖刷新函数
  const debouncedRefresh = (reason: string, eventData?: any) => {
    // 清除之前的定时器
    if (refreshTimeoutRef.current) {
      clearTimeout(refreshTimeoutRef.current);
    }

    // 设置新的定时器，500ms 后执行刷新
    refreshTimeoutRef.current = setTimeout(() => {
      console.log(`OngoingTasks - ${reason}，自动刷新任务列表`, eventData);
      fetchOngoingTasks();
    }, 500);
  };

  // 监听全局任务发布事件
  useEffect(() => {
    const handleTaskPublished = (data: any) => {
      debouncedRefresh('收到全局任务发布事件', data);
    };

    // 添加事件监听器
    taskEventManager.on(TASK_EVENTS.TASK_PUBLISHED, handleTaskPublished);

    // 清理函数
    return () => {
      taskEventManager.off(TASK_EVENTS.TASK_PUBLISHED, handleTaskPublished);
      // 清理定时器
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current);
      }
    };
  }, []);

  // 计算任务进度（已完成的学生数 / 总学生数）
  const calculateProgress = (assignments: OngoingTask['assignments']) => {
    if (!assignments || assignments.length === 0) return 0;
    const completedCount = assignments.filter(a => a.taskStatus === TaskStatus.COMPLETED).length;
    return Math.round((completedCount / assignments.length) * 100);
  };

  // 计算提交人数
  const getSubmissionInfo = (assignments: OngoingTask['assignments']) => {
    if (!assignments || assignments.length === 0) return '0 / 0';
    const completedCount = assignments.filter(a => a.taskStatus === TaskStatus.COMPLETED).length;
    return `${completedCount} / ${assignments.length}`;
  };

  // 计算剩余时间
  const getTimeLeft = (endDate: Date) => {
    const now = new Date();
    const end = new Date(endDate);
    const diffMs = end.getTime() - now.getTime();

    if (diffMs <= 0) return '已过期';

    const days = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

    if (days > 0) {
      return `${days}天${hours}小时`;
    } else if (hours > 0) {
      return `${hours}小时`;
    } else {
      return `${minutes}分钟`;
    }
  };

  const headers = ['进度', '课程名称', '提交人数', '评分', '剩余时间', '所属班级'];

  return (
    <section className="ongoing-tasks">
      <div className="flex justify-between items-center mb-4">
        <h2 className="section-title">进行中的任务</h2>
        <button
          onClick={fetchOngoingTasks}
          disabled={loading}
          className={`flex items-center gap-2 px-3 py-1.5 text-sm rounded-lg border transition-colors ${
            loading
              ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
              : 'bg-white text-gray-600 hover:bg-gray-50 hover:text-gray-800 border-gray-200'
          }`}
        >
          <RefreshCw size={14} className={loading ? 'animate-spin' : ''} />
          刷新
        </button>
      </div>
      <div className="filter-tabs-container">
        <div className="filter-tabs">
          {headers.map((header) => (
            <div key={header} className="filter-tab">
              {header} <ChevronDown size={14} className="text-gray-400" />
            </div>
          ))}
        </div>
      </div>
      <div className="tasks-grid">
        {loading ? (
          <div className="flex justify-center items-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <span className="ml-2 text-gray-500">加载中...</span>
          </div>
        ) : tasks.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-12 text-gray-500">
            <div className="text-4xl mb-4">📋</div>
            <div className="text-lg font-medium mb-2">暂无进行中的任务</div>
            <div className="text-sm">当前没有正在进行的任务</div>
          </div>
        ) : (
          tasks.map((task) => {
            const progress = calculateProgress(task.assignments);
            const submissionInfo = getSubmissionInfo(task.assignments);
            const timeLeft = getTimeLeft(task.endDate);

            return (
              <div key={task.id} className="task-card">
                <div className="task-card-content">
                  <div className="task-left">
                    <div className="progress-circle-modern">
                      <div className="progress-text">{progress}%</div>
                    </div>
                    <div className="task-details">
                      <h3 className="task-title">{task.taskName}</h3>
                    </div>
                  </div>
                  <div className="task-right">
                    <div className="task-stats">
                      <div className="stat-item">
                        <span className="stat-value">{submissionInfo}</span>
                      </div>
                      <div className="stat-item">
                        <Star size={14} className="star-icon" fill="#fbbf24" stroke="#fbbf24" />
                        <span className="stat-value">4.5</span>
                      </div>
                      <div className="stat-item">
                        <span className="stat-value">{timeLeft}</span>
                      </div>
                      <div className="class-info">
                        <div className="class-badge">
                          <div className="class-name">{task.classInfo?.schoolName || '我的任务'}</div>
                          <div className="subclass-name">{task.classInfo?.className || '教师发布'}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            );
          })
        )}
      </div>
    </section>
  );
};

export default OngoingTasks; 