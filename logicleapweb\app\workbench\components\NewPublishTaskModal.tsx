'use client';

import React, { useState, useEffect } from 'react';
import { X, Upload, Plus } from 'lucide-react';
import { worksApi } from '@/lib/api/works';
import taskApi, { TaskType, Priority } from '@/lib/api/task';
import { GetNotification } from 'logic-common/dist/components/Notification';
import './NewPublishTaskModal.css';

interface WorkItem {
  id: number;
  title: string;
  name?: string;
  workName?: string;
  description?: string;
  coverImage?: string;
  screenShotImage?: string;
  type: number;
  status: number;
  createTime: string;
  updateTime: string;
}

interface NewPublishTaskModalProps {
  isOpen: boolean;
  onClose: () => void;
  onBack: () => void;
  onConfirm: (taskData: any) => void;
  modalData: {
    selectedDistribution: string;
    energyAmount: string;
    selectedTemplate: any;
    selectedStudents: number[];
    selectedSchool?: any;
    selectedClass?: any;
  };
}

const NewPublishTaskModal: React.FC<NewPublishTaskModalProps> = ({
  isOpen,
  onClose,
  onBack,
  onConfirm,
  modalData
}) => {
  const [activeTab, setActiveTab] = useState<'task' | 'resources'>('task');
  const [taskData, setTaskData] = useState({
    taskName: '',
    taskDescription: '',
    selfAssessmentItems: [] as string[],
    startTime: '',
    endTime: ''
  });
  const [works, setWorks] = useState<WorkItem[]>([]);
  const [selectedWorkIds, setSelectedWorkIds] = useState<number[]>([]);
  const [attachments, setAttachments] = useState<File[]>([]);
  const [loadingWorks, setLoadingWorks] = useState(false);
  const [showQuickTimeSelector, setShowQuickTimeSelector] = useState<'start' | 'end' | null>(null);

  // 鼠标拖拽滚动状态
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, scrollLeft: 0 });

  const notification = GetNotification();

  // 当切换到资源标签页时获取作品列表
  useEffect(() => {
    if (activeTab === 'resources' && isOpen) {
      fetchWorks();
    }
  }, [activeTab, isOpen]);

  // 获取教师作品列表
  const fetchTeacherWorks = async () => {
    setLoadingWorks(true);
    try {
      // 获取当前用户ID
      const userInfo = localStorage.getItem('user');
      let userId = 1; // 默认值

      if (userInfo) {
        try {
          const parsed = JSON.parse(userInfo);
          userId = parsed.userId || parsed.id || parsed.teacherId || 1;
        } catch (e) {
          console.error('解析用户信息失败:', e);
        }
      }

      console.log('获取作品列表，用户ID:', userId);
      const response = await worksApi.getTeacherWorks(userId, 1, 100);

      console.log('作品API响应:', response);

      // 处理多层嵌套的数据结构
      let worksList: any[] = [];

      if (response && response.data) {
        if (response.data.code === 200) {
          if (response.data.data?.data?.list) {
            // 双层data嵌套
            worksList = response.data.data.data.list || [];
          } else if (response.data.data?.list) {
            // 单层data嵌套
            worksList = response.data.data.list || [];
          } else if (Array.isArray(response.data.data)) {
            // data是数组
            worksList = response.data.data;
          } else if (Array.isArray(response.data)) {
            // 直接是数组
            worksList = response.data;
          }
        }
      }

      console.log('解析后的作品列表:', worksList);
      setWorks(worksList);

      if (response.data.code !== 200) {
        notification.error('获取作品列表失败');
      }
    } catch (error) {
      console.error('获取作品列表失败:', error);
      notification.error('获取作品列表失败');
    } finally {
      setLoadingWorks(false);
    }
  };

  useEffect(() => {
    if (isOpen) {
      fetchTeacherWorks();
    }
  }, [isOpen]);

  // 处理任务信息输入
  const handleTaskDataChange = (field: string, value: string) => {
    setTaskData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // 处理自评项
  const handleSelfAssessmentChange = (index: number, value: string) => {
    const newItems = [...taskData.selfAssessmentItems];
    newItems[index] = value;
    setTaskData(prev => ({
      ...prev,
      selfAssessmentItems: newItems
    }));
  };

  const addSelfAssessmentItem = () => {
    setTaskData(prev => ({
      ...prev,
      selfAssessmentItems: [...prev.selfAssessmentItems, '']
    }));
  };

  const removeSelfAssessmentItem = (index: number) => {
    if (taskData.selfAssessmentItems.length > 1) {
      const newItems = taskData.selfAssessmentItems.filter((_, i) => i !== index);
      setTaskData(prev => ({
        ...prev,
        selfAssessmentItems: newItems
      }));
    }
  };

  // 处理作品选择
  const handleWorkSelect = (workId: number) => {
    setSelectedWorkIds(prev => {
      if (prev.includes(workId)) {
        return prev.filter(id => id !== workId);
      } else {
        return [...prev, workId];
      }
    });
  };

  // 获取作品列表
  const fetchWorks = async () => {
    if (loadingWorks) return;

    setLoadingWorks(true);
    try {
      // 获取当前用户ID
      const userData = localStorage.getItem('user');
      const userId = userData ? JSON.parse(userData).userId : null;

      if (!userId) {
        console.error('用户未登录');
        setWorks([]);
        return;
      }

      // 使用与TemplateSelectionModal相同的API
      const response = await worksApi.getTeacherWorks(userId, 1, 1000);

      // 处理多层嵌套的数据结构
      let newWorks: any[] = [];

      if (response && response.data) {
        if (response.data.data?.data?.list) {
          // 双层data嵌套
          newWorks = response.data.data.data.list || [];
        } else if (response.data.data?.list) {
          // 单层data嵌套
          newWorks = response.data.data.list || [];
        } else if (Array.isArray(response.data.data)) {
          // data是数组
          newWorks = response.data.data;
        } else if (Array.isArray(response.data)) {
          // 直接是数组
          newWorks = response.data;
        }
      }

      console.log('获取到的作品数据:', newWorks);
      if (newWorks.length > 0) {
        console.log('第一个作品的数据结构:', newWorks[0]);
        console.log('第一个作品的标题字段:', {
          title: newWorks[0].title,
          name: newWorks[0].name,
          workName: newWorks[0].workName
        });
      }
      setWorks(newWorks);
    } catch (error) {
      console.error('获取作品失败:', error);
      setWorks([]);
    } finally {
      setLoadingWorks(false);
    }
  };

  // 处理文件上传
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    const validFiles = files.filter(file => {
      const validTypes = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf', 
                         'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                         'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                         'application/vnd.ms-powerpoint', 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
                         'text/plain'];
      const maxSize = 10 * 1024 * 1024; // 10MB
      
      if (!validTypes.includes(file.type)) {
        notification.error(`文件 ${file.name} 格式不支持`);
        return false;
      }
      if (file.size > maxSize) {
        notification.error(`文件 ${file.name} 大小超过10MB限制`);
        return false;
      }
      return true;
    });
    
    setAttachments(prev => [...prev, ...validFiles]);
  };

  // 移除附件
  const removeAttachment = (index: number) => {
    setAttachments(prev => prev.filter((_, i) => i !== index));
  };

  // 处理水平滚动并阻止外部滚动
  const handleWheelScroll = (e: React.WheelEvent<HTMLDivElement>) => {
    // 只阻止事件传播，不调用preventDefault避免被动监听器警告
    e.stopPropagation();

    // 查找实际的滚动容器
    const scrollContainer = e.currentTarget.querySelector('.works-horizontal-scroll') as HTMLDivElement;

    if (!scrollContainer) {
      return;
    }

    const { scrollLeft, scrollWidth, clientWidth } = scrollContainer;

    // 检查是否有可滚动内容
    const hasScrollableContent = scrollWidth > clientWidth;

    // 只有当有可滚动内容时才进行水平滚动
    if (hasScrollableContent && e.deltaY !== 0) {
      // 增加滚动速度倍数，让滚动更流畅
      const scrollMultiplier = 4; // 进一步增加滚动速度
      const deltaX = e.deltaY * scrollMultiplier;

      // 计算新的滚动位置
      const newScrollLeft = scrollLeft + deltaX;
      const maxScrollLeft = scrollWidth - clientWidth;

      // 限制滚动范围并应用平滑滚动
      const targetScrollLeft = Math.max(0, Math.min(newScrollLeft, maxScrollLeft));

      // 使用 requestAnimationFrame 实现更平滑的滚动
      requestAnimationFrame(() => {
        scrollContainer.scrollLeft = targetScrollLeft;
      });
    }
  };

  // 处理鼠标拖拽滚动
  const handleMouseDown = (e: React.MouseEvent<HTMLDivElement>) => {
    const scrollContainer = e.currentTarget.querySelector('.works-horizontal-scroll') as HTMLDivElement;
    if (!scrollContainer) return;

    setIsDragging(true);
    setDragStart({
      x: e.pageX - scrollContainer.offsetLeft,
      scrollLeft: scrollContainer.scrollLeft
    });

    // 改变鼠标样式
    e.currentTarget.style.cursor = 'grabbing';
  };

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!isDragging) return;

    e.preventDefault(); // 鼠标事件可以安全地使用preventDefault
    const scrollContainer = e.currentTarget.querySelector('.works-horizontal-scroll') as HTMLDivElement;
    if (!scrollContainer) return;

    const x = e.pageX - scrollContainer.offsetLeft;
    const walk = (x - dragStart.x) * 2; // 调整拖拽速度
    scrollContainer.scrollLeft = dragStart.scrollLeft - walk;
  };

  const handleMouseUp = (e: React.MouseEvent<HTMLDivElement>) => {
    setIsDragging(false);
    e.currentTarget.style.cursor = 'grab';
  };

  const handleMouseLeave = (e: React.MouseEvent<HTMLDivElement>) => {
    setIsDragging(false);
    e.currentTarget.style.cursor = 'grab';
  };

  // 格式化时间显示
  const formatTimeDisplay = (timeStr: string) => {
    if (!timeStr) return '';
    const date = new Date(timeStr);
    if (isNaN(date.getTime())) return timeStr;

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');

    return `${year}/${month}/${day} ${hours}:${minutes}`;
  };

  // 快速时间选择
  const handleQuickTimeSelect = (option: string, type: 'start' | 'end') => {
    const now = new Date();
    let targetTime: Date;

    if (type === 'start') {
      if (option === '现在') {
        targetTime = now;
      } else {
        targetTime = now;
      }
      setTaskData(prev => ({ ...prev, startTime: targetTime.toISOString() }));
    } else {
      // 结束时间基于开始时间计算
      const startTime = taskData.startTime ? new Date(taskData.startTime) : now;

      switch (option) {
        case '1小时':
          targetTime = new Date(startTime.getTime() + 60 * 60 * 1000);
          break;
        case '6小时':
          targetTime = new Date(startTime.getTime() + 6 * 60 * 60 * 1000);
          break;
        case '12小时':
          targetTime = new Date(startTime.getTime() + 12 * 60 * 60 * 1000);
          break;
        case '1天':
          targetTime = new Date(startTime.getTime() + 24 * 60 * 60 * 1000);
          break;
        case '7天':
          targetTime = new Date(startTime.getTime() + 7 * 24 * 60 * 60 * 1000);
          break;
        default:
          targetTime = new Date(startTime.getTime() + 60 * 60 * 1000);
      }
      setTaskData(prev => ({ ...prev, endTime: targetTime.toISOString() }));
    }

    setShowQuickTimeSelector(null);
  };

  // 检查表单是否有效
  const isFormValid = () => {
    return taskData.taskName.trim() && taskData.startTime && taskData.endTime;
  };

  // 清空表单数据的通用函数
  const clearFormData = () => {
    setTaskData({
      taskName: '',
      taskDescription: '',
      selfAssessmentItems: [],
      startTime: '',
      endTime: ''
    });
    setSelectedWorkIds([]);
    setAttachments([]);
    setActiveTab('task'); // 重置到任务信息标签页
  };

  // 处理上一步按钮点击
  const handleBack = () => {
    clearFormData();
    onBack();
  };

  // 处理弹窗关闭
  const handleClose = () => {
    clearFormData();
    onClose();
  };

  // 确认发布
  const handleConfirm = async () => {
    if (!taskData.taskName.trim()) {
      notification.error('请输入任务名称');
      return;
    }

    if (!taskData.startTime) {
      notification.error('请设置开始时间');
      return;
    }

    if (!taskData.endTime) {
      notification.error('请设置结束时间');
      return;
    }

    try {
      // 获取当前用户信息
      const userInfo = localStorage.getItem('user');
      let teacherId = 1; // 默认值

      if (userInfo) {
        try {
          const parsed = JSON.parse(userInfo);
          teacherId = parsed.userId || parsed.id || parsed.teacherId || 1;
        } catch (e) {
          console.error('解析用户信息失败:', e);
        }
      }

      // 准备任务发布参数
      const publishParams = {
        taskName: taskData.taskName,
        taskDescription: taskData.taskDescription || '',
        taskType: TaskType.GRAPHIC, // 默认为图形化任务
        startDate: new Date(taskData.startTime),
        endDate: new Date(taskData.endTime),
        priority: Priority.NORMAL,
        workIds: selectedWorkIds,
        selfAssessmentItems: taskData.selfAssessmentItems.filter(item => item.trim()),
        studentIds: modalData.selectedStudents,
        attachments: attachments.map(file => file.name), // 这里应该是上传后的文件URL
        templateId: modalData.selectedTemplate?.id,
        isPublic: 0,
        allowLateSubmission: false
      };

      console.log('发布任务参数:', publishParams);

      // 调用任务发布API
      const response = await taskApi.publishTask(publishParams);

      if (response.data.code === 200) {
        notification.success('任务发布成功！开始上课');

        // 准备最终数据传递给父组件
        const finalTaskData = {
          ...taskData,
          selectedWorkIds,
          attachments,
          modalData,
          publishedTaskId: response.data.data.id
        };

        onConfirm(finalTaskData);
      } else {
        notification.error(response.data.message || '任务发布失败');
      }
    } catch (error) {
      console.error('发布任务失败:', error);
      notification.error('任务发布失败，请重试');
    }
  };

  if (!isOpen) return null;

  return (
    <div className="modal-overlay">
      <div className="modal-wrapper">
        <button className="modal-close-btn-outside" onClick={handleClose}>
          <X size={20} />
        </button>
        <div
          className="modal-content new-publish-task-modal"
          onClick={() => setShowQuickTimeSelector(null)}
        >

        {/* 步骤指示器 */}
        <div className="step-indicator">
          <div className="step completed">
            <div className="step-number">1</div>
            <div className="step-label">选择班级</div>
          </div>
          <div className="step active">
            <div className="step-number">2</div>
            <div className="step-label">发布任务</div>
          </div>
        </div>

        {/* 标签页切换 */}
        <div className="tab-switcher">
          <button 
            className={`tab-btn ${activeTab === 'task' ? 'active' : ''}`}
            onClick={() => setActiveTab('task')}
          >
            任务信息
          </button>
          <button 
            className={`tab-btn ${activeTab === 'resources' ? 'active' : ''}`}
            onClick={() => setActiveTab('resources')}
          >
            资源与附件
          </button>
        </div>

        {/* 内容区域 */}
        <div className="modal-content-body">
          {activeTab === 'task' ? (
            <div className="task-info-tab">
              <div className="form-group">
                <input
                  type="text"
                  className="form-input"
                  placeholder="任务名称"
                  value={taskData.taskName}
                  onChange={(e) => setTaskData(prev => ({ ...prev, taskName: e.target.value }))}
                />
              </div>

              <div className="form-group">
                <textarea
                  className="form-textarea"
                  placeholder="任务描述"
                  value={taskData.taskDescription}
                  onChange={(e) => setTaskData(prev => ({ ...prev, taskDescription: e.target.value }))}
                  rows={4}
                />
              </div>

              <div className="form-group">
                <div className="self-assessment-section">
                  {taskData.selfAssessmentItems.length === 0 ? (
                    <button
                      type="button"
                      className="add-self-assessment-btn"
                      onClick={() => setTaskData(prev => ({ ...prev, selfAssessmentItems: [''] }))}
                    >
                      + 添加自评项
                    </button>
                  ) : (
                    <>
                      <label className="form-label">自评项</label>
                      {taskData.selfAssessmentItems.map((item, index) => (
                        <div key={index} className="self-assessment-item">
                          <input
                            type="text"
                            className="form-input"
                            placeholder={`自评项 ${index + 1}`}
                            value={item}
                            onChange={(e) => {
                              const newItems = [...taskData.selfAssessmentItems];
                              newItems[index] = e.target.value;
                              setTaskData(prev => ({ ...prev, selfAssessmentItems: newItems }));
                            }}
                          />
                          <button
                            type="button"
                            className="remove-btn"
                            onClick={() => {
                              const newItems = taskData.selfAssessmentItems.filter((_, i) => i !== index);
                              setTaskData(prev => ({ ...prev, selfAssessmentItems: newItems }));
                            }}
                          >
                            ×
                          </button>
                        </div>
                      ))}
                      <button
                        type="button"
                        className="add-btn"
                        onClick={() => setTaskData(prev => ({ ...prev, selfAssessmentItems: [...prev.selfAssessmentItems, ''] }))}
                      >
                        + 添加自评项
                      </button>
                    </>
                  )}
                </div>
              </div>

              {/* 时间设置 */}
              <div className="form-group">
                <div className="time-settings">
                  <div className="time-row">
                    <div className="time-field-container">
                      <div
                        className={`time-field ${taskData.startTime ? 'has-selected-date' : ''}`}
                        onClick={(e) => {
                          e.stopPropagation();
                          setShowQuickTimeSelector(showQuickTimeSelector === 'start' ? null : 'start');
                        }}
                      >
                        <input
                          type="text"
                          className="form-input"
                          value={formatTimeDisplay(taskData.startTime)}
                          placeholder="点击设置开始时间"
                          readOnly
                        />
                      </div>
                      {showQuickTimeSelector === 'start' && (
                        <div className="quick-time-selector" onClick={(e) => e.stopPropagation()}>
                          <button
                            className="quick-time-btn"
                            onClick={() => handleQuickTimeSelect('现在', 'start')}
                          >
                            现在
                          </button>
                        </div>
                      )}
                    </div>
                    <div className="time-field-container">
                      <div
                        className={`time-field ${taskData.endTime ? 'has-selected-date' : ''}`}
                        onClick={(e) => {
                          e.stopPropagation();
                          setShowQuickTimeSelector(showQuickTimeSelector === 'end' ? null : 'end');
                        }}
                      >
                        <input
                          type="text"
                          className="form-input"
                          value={formatTimeDisplay(taskData.endTime)}
                          placeholder="点击设置结束时间"
                          readOnly
                        />
                      </div>
                      {showQuickTimeSelector === 'end' && (
                        <div className="quick-time-selector" onClick={(e) => e.stopPropagation()}>
                          <button
                            className="quick-time-btn"
                            onClick={() => handleQuickTimeSelect('1小时', 'end')}
                          >
                            1小时
                          </button>
                          <button
                            className="quick-time-btn"
                            onClick={() => handleQuickTimeSelect('6小时', 'end')}
                          >
                            6小时
                          </button>
                          <button
                            className="quick-time-btn"
                            onClick={() => handleQuickTimeSelect('12小时', 'end')}
                          >
                            12小时
                          </button>
                          <button
                            className="quick-time-btn"
                            onClick={() => handleQuickTimeSelect('1天', 'end')}
                          >
                            1天
                          </button>
                          <button
                            className="quick-time-btn"
                            onClick={() => handleQuickTimeSelect('7天', 'end')}
                          >
                            7天
                          </button>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="resources-tab">
              <div className="works-section">
                <h4>选择作品</h4>
                <p className="help-text">选择作品作为任务参考资料（可多选）</p>
                <div
                  className="relative works-scroll-wrapper"
                  onWheel={handleWheelScroll}
                  onMouseDown={handleMouseDown}
                  onMouseMove={handleMouseMove}
                  onMouseUp={handleMouseUp}
                  onMouseLeave={handleMouseLeave}
                  style={{
                    minHeight: '200px',
                    cursor: 'grab',
                    userSelect: 'none'
                  }}
                >
                  {loadingWorks ? (
                    <div
                      className="loading-container"
                      style={{ minHeight: '200px' }}
                    >
                      <div className="loading-spinner"></div>
                      <span>加载中...</span>
                    </div>
                  ) : works.length > 0 ? (
                    <div className="works-horizontal-scroll">
                      {works.map((work) => (
                        <div
                          key={work.id}
                          className={`work-card ${selectedWorkIds.includes(work.id) ? 'selected' : ''}`}
                          onClick={() => handleWorkSelect(work.id)}
                        >
                          <div className="work-image">
                            {work.coverImage || work.screenShotImage ? (
                              <img
                                src={work.coverImage || work.screenShotImage}
                                alt={work.title}
                                onError={(e) => {
                                  const target = e.currentTarget as HTMLImageElement;
                                  target.style.display = 'none';
                                  const nextElement = target.nextElementSibling as HTMLElement;
                                  if (nextElement) {
                                    nextElement.style.display = 'flex';
                                  }
                                }}
                              />
                            ) : null}
                            <div className="work-placeholder" style={{ display: work.coverImage || work.screenShotImage ? 'none' : 'flex' }}>
                              作品
                            </div>
                          </div>
                          <div className="work-title">{work.title || work.name || work.workName || '未命名作品'}</div>
                          {selectedWorkIds.includes(work.id) && (
                            <div className="selected-indicator">✓</div>
                          )}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="empty-placeholder">
                      <div className="empty-text">作品列表</div>
                    </div>
                  )}
                </div>
              </div>

              <div className="attachments-section">
                <h4>附件上传</h4>
                <div className="upload-area">
                  <input
                    type="file"
                    multiple
                    accept=".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt"
                    onChange={handleFileUpload}
                    style={{ display: 'none' }}
                    id="file-upload"
                  />
                  <label htmlFor="file-upload" className="upload-btn">
                    +
                  </label>
                  <span className="file-format-info">
                    支持 jpg、png、gif、pdf、doc、docx、xls、xlsx、ppt、pptx、txt 格式，单个文件大小不超过10MB
                  </span>
                </div>
                {attachments.length > 0 && (
                  <div className="attachments-list">
                    {attachments.map((file, index) => (
                      <div key={index} className="attachment-item">
                        <span className="file-name">{file.name}</span>
                        <button
                          onClick={() => removeAttachment(index)}
                          className="remove-attachment-btn"
                        >
                          ×
                        </button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* 底部按钮 */}
        <div className="modal-footer">
          <button
            className="prev-btn"
            onClick={handleBack}
          >
            上一步
          </button>
          <button
            className={`start-class-btn ${isFormValid() ? 'enabled' : 'disabled'}`}
            onClick={handleConfirm}
            disabled={!isFormValid()}
          >
            开始上课
          </button>
        </div>
      </div>
      </div>
    </div>
  );
};

export default NewPublishTaskModal;
