"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/course-plaza/page",{

/***/ "(app-pages-browser)/./app/(main)/course-plaza/components/CourseDetailView.tsx":
/*!*****************************************************************!*\
  !*** ./app/(main)/course-plaza/components/CourseDetailView.tsx ***!
  \*****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CourseDetailView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_api_course__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api/course */ \"(app-pages-browser)/./lib/api/course.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction CourseDetailView(param) {\n    let { course, onBack } = param;\n    var _courseDetail_contentConfig_video, _courseDetail_contentConfig, _courseDetail_contentConfig_document, _courseDetail_contentConfig1, _courseDetail_contentConfig_document1, _courseDetail_contentConfig2, _courseDetail_contentConfig_document2, _courseDetail_contentConfig3, _courseDetail_contentConfig_document3, _courseDetail_contentConfig4;\n    _s();\n    const [seriesCourses, setSeriesCourses] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [seriesDetail, setSeriesDetail] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [courseDetail, setCourseDetail] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [showVideoPlayer, setShowVideoPlayer] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [videoLoaded, setVideoLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // 格式化视频时长\n    const formatDuration = (seconds)=>{\n        if (!seconds || seconds <= 0) return \"\";\n        const hours = Math.floor(seconds / 3600);\n        const minutes = Math.floor(seconds % 3600 / 60);\n        const remainingSeconds = seconds % 60;\n        if (hours > 0) {\n            return \"\".concat(hours, \"小时\").concat(minutes, \"分钟\");\n        } else if (minutes > 0) {\n            return \"\".concat(minutes, \"分钟\").concat(remainingSeconds > 0 ? remainingSeconds + \"秒\" : \"\");\n        } else {\n            return \"\".concat(remainingSeconds, \"秒\");\n        }\n    };\n    // 添加自定义滚动条样式\n    const customScrollbarStyle = \"\\n    .custom-scrollbar::-webkit-scrollbar {\\n      width: 6px;\\n    }\\n    .custom-scrollbar::-webkit-scrollbar-track {\\n      background: #f1f5f9;\\n      border-radius: 3px;\\n    }\\n    .custom-scrollbar::-webkit-scrollbar-thumb {\\n      background: linear-gradient(to bottom, #3b82f6, #2563eb);\\n      border-radius: 3px;\\n    }\\n    .custom-scrollbar::-webkit-scrollbar-thumb:hover {\\n      background: linear-gradient(to bottom, #2563eb, #1d4ed8);\\n    }\\n  \";\n    // 使用useRef跟踪请求状态，避免React严格模式重复请求\n    const requestedSeriesIdRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const isRequestingRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(false);\n    // 添加调试信息\n    console.log(\"\\uD83D\\uDCCB CourseDetailView 接收到的课程数据:\", course);\n    console.log(\"\\uD83D\\uDD0D seriesId 值:\", course.seriesId);\n    console.log(\"\\uD83D\\uDD0D seriesId 类型:\", typeof course.seriesId);\n    // 调试：打印传入的课程数据\n    console.log(\"\\uD83C\\uDFAF CourseDetailView 接收到的课程数据:\", course);\n    // 防止浏览器自动下载\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const preventAutoDownload = (e)=>{\n            var _target_getAttribute;\n            const target = e.target;\n            if (target && target.tagName === \"A\" && ((_target_getAttribute = target.getAttribute(\"href\")) === null || _target_getAttribute === void 0 ? void 0 : _target_getAttribute.includes(\"example.com\"))) {\n                e.preventDefault();\n                console.log(\"\\uD83D\\uDEAB 阻止示例文件自动下载\");\n            }\n        };\n        document.addEventListener(\"click\", preventAutoDownload, true);\n        return ()=>{\n            document.removeEventListener(\"click\", preventAutoDownload, true);\n        };\n    }, []);\n    // 获取系列课程列表\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const fetchSeriesCourses = async ()=>{\n            // 使用 seriesId 或者 id 作为系列ID\n            const seriesId = course.seriesId || course.id;\n            if (!seriesId) {\n                console.warn(\"⚠️ 课程没有seriesId和id，无法获取系列课程列表\");\n                setLoading(false);\n                return;\n            }\n            // 防重复请求\n            if (requestedSeriesIdRef.current === seriesId || isRequestingRef.current) {\n                console.log(\"\\uD83D\\uDEAB 防重复请求：系列课程列表已请求过，seriesId:\", seriesId);\n                return;\n            }\n            try {\n                var _coursesRes_data, _coursesRes_data1;\n                setLoading(true);\n                requestedSeriesIdRef.current = seriesId;\n                isRequestingRef.current = true;\n                console.log(\"\\uD83D\\uDD04 获取系列课程列表，使用ID:\", seriesId);\n                // 首先获取系列详情\n                const { data: seriesRes } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getMarketplaceSeriesDetail(seriesId);\n                if (seriesRes.code === 200 && seriesRes.data) {\n                    const seriesData = seriesRes.data;\n                    // 设置系列详情\n                    setSeriesDetail({\n                        id: seriesData.id,\n                        title: seriesData.title,\n                        description: seriesData.description,\n                        coverImage: seriesData.coverImage && !seriesData.coverImage.includes(\"example.com\") ? seriesData.coverImage : \"\",\n                        category: seriesData.category,\n                        categoryLabel: seriesData.categoryLabel || (seriesData.category === 0 ? \"官方\" : \"社区\"),\n                        status: seriesData.status,\n                        projectMembers: seriesData.projectMembers,\n                        totalCourses: seriesData.totalCourses,\n                        totalStudents: seriesData.totalStudents,\n                        creatorId: seriesData.creatorId || 0,\n                        createdAt: seriesData.createdAt,\n                        updatedAt: seriesData.updatedAt || seriesData.createdAt,\n                        tags: seriesData.tags || []\n                    });\n                }\n                // 使用课程管理API获取系列下的课程列表\n                console.log(\"\\uD83D\\uDD04 使用课程管理API获取系列课程列表...\");\n                const { data: coursesRes } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getManagementSeriesCourses(seriesId, {\n                    page: 1,\n                    pageSize: 50,\n                    status: 1 // 只获取已发布的课程\n                });\n                if (coursesRes.code === 200 && ((_coursesRes_data = coursesRes.data) === null || _coursesRes_data === void 0 ? void 0 : _coursesRes_data.list)) {\n                    const courses = coursesRes.data.list.map((item)=>({\n                            id: item.id,\n                            title: item.title,\n                            orderIndex: item.orderIndex || 0,\n                            status: item.status\n                        }));\n                    // 按orderIndex排序\n                    courses.sort((a, b)=>a.orderIndex - b.orderIndex);\n                    console.log(\"\\uD83D\\uDCDA 从课程管理API获取到课程列表:\", courses);\n                    setSeriesCourses(courses);\n                } else {\n                    console.log(\"⚠️ 课程管理API未返回课程列表\");\n                    setSeriesCourses([]);\n                }\n                // 如果有课程列表，设置默认课程详情（选择第一个课程）\n                if (coursesRes.code === 200 && ((_coursesRes_data1 = coursesRes.data) === null || _coursesRes_data1 === void 0 ? void 0 : _coursesRes_data1.list) && coursesRes.data.list.length > 0) {\n                    const firstCourse = coursesRes.data.list[0];\n                    console.log(\"\\uD83C\\uDFAC 设置默认课程（第一个课程）:\", firstCourse);\n                    // 获取第一个课程的详细信息\n                    try {\n                        const { data: courseDetailRes } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getCourseDetail(firstCourse.id);\n                        if (courseDetailRes.code === 200 && courseDetailRes.data) {\n                            var _seriesRes_data, _seriesRes_data1, _courseData_additionalResources;\n                            const courseData = courseDetailRes.data;\n                            setCourseDetail({\n                                id: courseData.id,\n                                title: courseData.title,\n                                description: courseData.description || ((_seriesRes_data = seriesRes.data) === null || _seriesRes_data === void 0 ? void 0 : _seriesRes_data.description) || \"\",\n                                coverImage: courseData.coverImage && !courseData.coverImage.includes(\"example.com\") ? courseData.coverImage : ((_seriesRes_data1 = seriesRes.data) === null || _seriesRes_data1 === void 0 ? void 0 : _seriesRes_data1.coverImage) || \"\",\n                                hasVideo: courseData.hasVideo || 0,\n                                hasDocument: courseData.hasDocument || 0,\n                                hasAudio: courseData.hasAudio || 0,\n                                videoDuration: courseData.videoDuration || 0,\n                                videoDurationLabel: courseData.videoDurationLabel || \"\",\n                                videoName: courseData.videoName || \"\",\n                                resourcesCount: ((_courseData_additionalResources = courseData.additionalResources) === null || _courseData_additionalResources === void 0 ? void 0 : _courseData_additionalResources.length) || 0,\n                                contentConfig: courseData.contentConfig || {},\n                                teachingInfo: courseData.teachingInfo || [],\n                                additionalResources: courseData.additionalResources || [],\n                                orderIndex: courseData.orderIndex || 1,\n                                status: courseData.status || 1,\n                                statusLabel: courseData.statusLabel || \"已发布\",\n                                currentCourseId: courseData.id\n                            });\n                        }\n                    } catch (courseError) {\n                        console.error(\"❌ 获取课程详情失败:\", courseError);\n                    }\n                }\n            } catch (error) {\n                console.error(\"❌ 获取系列课程列表异常:\", error);\n                setSeriesCourses([]);\n            } finally{\n                setLoading(false);\n                isRequestingRef.current = false;\n            }\n        };\n        fetchSeriesCourses();\n    }, [\n        course.seriesId,\n        course.id\n    ]);\n    // 处理课程点击事件\n    const handleCourseClick = async (courseItem)=>{\n        const seriesId = course.seriesId || course.id;\n        const courseId = courseItem.id;\n        console.log(\"\\uD83C\\uDFAF 点击课程，准备获取详情 - seriesId:\", seriesId, \"courseId:\", courseId);\n        // 先清空课程详情，避免旧数据触发下载\n        setCourseDetail(null);\n        try {\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getCourseMarketplaceDetail(seriesId, courseId);\n            if (res.code === 200 && res.data) {\n                console.log(\"\\uD83D\\uDCDA 获取到课程详情:\", res.data);\n                setCourseDetail(res.data);\n            } else {\n                console.error(\"❌ 获取课程详情失败:\", res);\n                // 如果API失败，使用本地数据（添加示例视频时长和PDF文档）\n                const sampleVideoDuration = courseItem.id === 1 ? 1800 : courseItem.id === 2 ? 2400 : 3600; // 30分钟、40分钟、60分钟\n                const hasVideoSample = courseItem.id <= 2 ? 1 : 0; // 前两个课程有视频\n                const hasDocumentSample = courseItem.id <= 3 ? 1 : 0; // 前三个课程有文档\n                setCourseDetail({\n                    id: courseItem.id,\n                    title: courseItem.title,\n                    description: \"\",\n                    coverImage: course.coverImage || \"\",\n                    hasVideo: hasVideoSample,\n                    hasDocument: hasDocumentSample,\n                    hasAudio: 0,\n                    videoDuration: hasVideoSample ? sampleVideoDuration : 0,\n                    videoDurationLabel: \"\",\n                    videoName: hasVideoSample ? \"\".concat(courseItem.title, \"教学视频\") : \"\",\n                    resourcesCount: 0,\n                    contentConfig: {\n                        hasVideo: hasVideoSample,\n                        hasDocument: hasDocumentSample,\n                        hasAudio: 0,\n                        video: hasVideoSample ? {\n                            url: \"https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4\",\n                            name: \"\".concat(courseItem.title, \"教学视频.mp4\")\n                        } : {\n                            url: \"\",\n                            name: \"\"\n                        },\n                        document: hasDocumentSample ? {\n                            url: \"https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf\",\n                            name: \"\".concat(courseItem.title, \"教学课件.pdf\")\n                        } : {\n                            url: \"\",\n                            name: \"\"\n                        },\n                        audio: {\n                            url: \"\",\n                            name: \"\"\n                        }\n                    },\n                    teachingInfo: [],\n                    additionalResources: [],\n                    orderIndex: courseItem.orderIndex || 1,\n                    status: courseItem.status || 1,\n                    statusLabel: courseItem.status === 1 ? \"已发布\" : \"草稿\",\n                    currentCourseId: courseItem.id\n                });\n            }\n        } catch (error) {\n            console.error(\"❌ 获取课程详情异常:\", error);\n            // 如果API失败，使用本地数据（添加示例视频时长和PDF文档）\n            const sampleVideoDuration = courseItem.id === 1 ? 1800 : courseItem.id === 2 ? 2400 : 3600; // 30分钟、40分钟、60分钟\n            const hasVideoSample = courseItem.id <= 2 ? 1 : 0; // 前两个课程有视频\n            const hasDocumentSample = courseItem.id <= 3 ? 1 : 0; // 前三个课程有文档\n            setCourseDetail({\n                id: courseItem.id,\n                title: courseItem.title,\n                description: \"\",\n                coverImage: course.coverImage || \"\",\n                hasVideo: hasVideoSample,\n                hasDocument: hasDocumentSample,\n                hasAudio: 0,\n                videoDuration: hasVideoSample ? sampleVideoDuration : 0,\n                videoDurationLabel: \"\",\n                videoName: hasVideoSample ? \"\".concat(courseItem.title, \"教学视频\") : \"\",\n                resourcesCount: 0,\n                contentConfig: {\n                    hasVideo: hasVideoSample,\n                    hasDocument: hasDocumentSample,\n                    hasAudio: 0,\n                    video: hasVideoSample ? {\n                        url: \"https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4\",\n                        name: \"\".concat(courseItem.title, \"教学视频.mp4\")\n                    } : {\n                        url: \"\",\n                        name: \"\"\n                    },\n                    document: hasDocumentSample ? {\n                        url: \"https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf\",\n                        name: \"\".concat(courseItem.title, \"教学课件.pdf\")\n                    } : {\n                        url: \"\",\n                        name: \"\"\n                    },\n                    audio: {\n                        url: \"\",\n                        name: \"\"\n                    }\n                },\n                teachingInfo: [],\n                additionalResources: [],\n                orderIndex: courseItem.orderIndex || 1,\n                status: courseItem.status || 1,\n                statusLabel: courseItem.status === 1 ? \"已发布\" : \"草稿\",\n                currentCourseId: courseItem.id\n            });\n        }\n    };\n    // 注释：现在数据直接从API获取，不再需要设置默认数据\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n        initial: {\n            opacity: 0,\n            x: 50\n        },\n        animate: {\n            opacity: 1,\n            x: 0\n        },\n        exit: {\n            opacity: 0,\n            x: -50\n        },\n        transition: {\n            duration: 0.5\n        },\n        className: \"min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"style\", {\n                dangerouslySetInnerHTML: {\n                    __html: customScrollbarStyle\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                lineNumber: 423,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: onBack,\n                    className: \"flex items-center gap-2 px-4 py-2 text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-colors duration-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-5 h-5\",\n                            fill: \"none\",\n                            viewBox: \"0 0 24 24\",\n                            stroke: \"currentColor\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M15 19l-7-7 7-7\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                lineNumber: 432,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                            lineNumber: 431,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"返回课程列表\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                            lineNumber: 434,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                    lineNumber: 427,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                lineNumber: 426,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-br from-white via-blue-50/30 to-indigo-50/50 rounded-2xl border border-blue-200/60 shadow-lg backdrop-blur-sm overflow-hidden mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between p-6 bg-white/70 backdrop-blur-md border-b border-blue-100/50\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900 tracking-tight\",\n                                children: (courseDetail === null || courseDetail === void 0 ? void 0 : courseDetail.title) || (seriesDetail === null || seriesDetail === void 0 ? void 0 : seriesDetail.title) || \"课程详情\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                lineNumber: 441,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"px-5 py-2.5 text-sm font-semibold rounded-xl shadow-sm transition-all duration-200 \".concat((seriesDetail === null || seriesDetail === void 0 ? void 0 : seriesDetail.category) === 0 ? \"bg-gradient-to-r from-emerald-500 to-emerald-600 text-white hover:from-emerald-600 hover:to-emerald-700\" : \"bg-gradient-to-r from-blue-500 to-blue-600 text-white hover:from-blue-600 hover:to-blue-700\"),\n                                children: (seriesDetail === null || seriesDetail === void 0 ? void 0 : seriesDetail.category) === 0 ? \"官方\" : \"社区\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                lineNumber: 444,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                        lineNumber: 440,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-6 p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-40 h-28 bg-gradient-to-br from-orange-100 via-orange-50 to-amber-50 rounded-xl shadow-md overflow-hidden border-2 border-white/80 transition-all duration-300 group-hover:shadow-lg group-hover:scale-105\",\n                                        children: (courseDetail === null || courseDetail === void 0 ? void 0 : courseDetail.coverImage) && !courseDetail.coverImage.includes(\"example.com\") || (seriesDetail === null || seriesDetail === void 0 ? void 0 : seriesDetail.coverImage) && !seriesDetail.coverImage.includes(\"example.com\") ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            src: (courseDetail === null || courseDetail === void 0 ? void 0 : courseDetail.coverImage) || (seriesDetail === null || seriesDetail === void 0 ? void 0 : seriesDetail.coverImage) || \"\",\n                                            alt: (courseDetail === null || courseDetail === void 0 ? void 0 : courseDetail.title) || (seriesDetail === null || seriesDetail === void 0 ? void 0 : seriesDetail.title) || \"课程封面\",\n                                            width: 160,\n                                            height: 112,\n                                            className: \"w-full h-full object-cover\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                            lineNumber: 459,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-full flex flex-col items-center justify-center text-orange-400\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-8 h-8 mb-1\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 1.5,\n                                                        d: \"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 469,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 468,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs font-medium\",\n                                                    children: \"课程封面\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 471,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                            lineNumber: 467,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                        lineNumber: 456,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -inset-1 bg-gradient-to-r from-orange-200/20 to-amber-200/20 rounded-xl blur-sm -z-10 opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                        lineNumber: 476,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                lineNumber: 455,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-r from-purple-50 to-indigo-50 rounded-xl p-5 border border-purple-100/50 shadow-sm\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-700 leading-relaxed\",\n                                            children: (courseDetail === null || courseDetail === void 0 ? void 0 : courseDetail.description) || (seriesDetail === null || seriesDetail === void 0 ? void 0 : seriesDetail.description) || \"暂无课程描述\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                            lineNumber: 482,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                        lineNumber: 481,\n                                        columnNumber: 13\n                                    }, this),\n                                    (seriesDetail === null || seriesDetail === void 0 ? void 0 : seriesDetail.tags) && seriesDetail.tags.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2\",\n                                        children: seriesDetail.tags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-flex items-center px-3 py-1 text-xs font-medium rounded-full border\",\n                                                style: {\n                                                    backgroundColor: \"\".concat(tag.color, \"15\"),\n                                                    borderColor: \"\".concat(tag.color, \"40\"),\n                                                    color: tag.color\n                                                },\n                                                children: tag.name\n                                            }, tag.id, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                lineNumber: 491,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                        lineNumber: 489,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 text-sm text-gray-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4 text-purple-500\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 508,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                lineNumber: 507,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: \"项目成员：\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                lineNumber: 510,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: (seriesDetail === null || seriesDetail === void 0 ? void 0 : seriesDetail.projectMembers) || \"暂无信息\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                lineNumber: 511,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                        lineNumber: 506,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                lineNumber: 480,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                        lineNumber: 453,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                lineNumber: 439,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2 space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-r from-blue-600 to-purple-600 px-6 py-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-xl font-bold text-white flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-6 h-6\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                lineNumber: 528,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                            lineNumber: 527,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"教学视频\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 526,\n                                                    columnNumber: 17\n                                                }, this),\n                                                ((courseDetail === null || courseDetail === void 0 ? void 0 : courseDetail.videoDurationLabel) || (courseDetail === null || courseDetail === void 0 ? void 0 : courseDetail.videoDuration)) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"bg-white/20 backdrop-blur-sm text-white px-3 py-1 rounded-full text-sm font-medium\",\n                                                    children: [\n                                                        \"时长：\",\n                                                        courseDetail.videoDurationLabel || formatDuration(courseDetail.videoDuration || 0)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 533,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                            lineNumber: 525,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                        lineNumber: 524,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: (courseDetail === null || courseDetail === void 0 ? void 0 : courseDetail.hasVideo) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative group\",\n                                            children: !showVideoPlayer ? // 视频预览封面\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative bg-gradient-to-br from-gray-900 to-gray-700 rounded-xl h-80 flex items-center justify-center cursor-pointer overflow-hidden transition-all duration-300 hover:shadow-2xl\",\n                                                onClick: ()=>setShowVideoPlayer(true),\n                                                children: [\n                                                    courseDetail.coverImage && !courseDetail.coverImage.includes(\"example.com\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 bg-cover bg-center opacity-60\",\n                                                        style: {\n                                                            backgroundImage: \"url(\".concat(courseDetail.coverImage, \")\")\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 551,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 558,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative z-10 text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-20 h-20 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4 transition-all duration-300 group-hover:bg-white/30 group-hover:scale-110\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-10 h-10 text-white ml-1\",\n                                                                    fill: \"currentColor\",\n                                                                    viewBox: \"0 0 20 20\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        fillRule: \"evenodd\",\n                                                                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z\",\n                                                                        clipRule: \"evenodd\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                        lineNumber: 564,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                    lineNumber: 563,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                lineNumber: 562,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-white text-xl font-bold mb-2\",\n                                                                children: courseDetail.videoName || \"教学视频\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                lineNumber: 567,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-white/80 text-sm\",\n                                                                children: \"点击播放视频\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                lineNumber: 570,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 561,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute top-4 right-4 bg-red-500 text-white px-2 py-1 rounded text-xs font-bold\",\n                                                        children: \"HD\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 574,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                lineNumber: 545,\n                                                columnNumber: 21\n                                            }, this) : // 实际视频播放器\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative bg-black rounded-xl overflow-hidden\",\n                                                children: [\n                                                    ((_courseDetail_contentConfig = courseDetail.contentConfig) === null || _courseDetail_contentConfig === void 0 ? void 0 : (_courseDetail_contentConfig_video = _courseDetail_contentConfig.video) === null || _courseDetail_contentConfig_video === void 0 ? void 0 : _courseDetail_contentConfig_video.url) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                                        className: \"w-full h-80 object-contain\",\n                                                        controls: true,\n                                                        autoPlay: true,\n                                                        poster: courseDetail.coverImage && !courseDetail.coverImage.includes(\"example.com\") ? courseDetail.coverImage : undefined,\n                                                        onLoadStart: ()=>setVideoLoaded(true),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                                                                src: courseDetail.contentConfig.video.url,\n                                                                type: \"video/mp4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                lineNumber: 589,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            \"您的浏览器不支持视频播放\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 582,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-80 flex items-center justify-center bg-gray-100\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center text-gray-500\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-12 h-12 mx-auto mb-2\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 1.5,\n                                                                        d: \"M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                        lineNumber: 596,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                    lineNumber: 595,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"视频加载失败\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                    lineNumber: 598,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm mt-1\",\n                                                                    children: \"请检查网络连接或稍后重试\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                    lineNumber: 599,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                            lineNumber: 594,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 593,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setShowVideoPlayer(false),\n                                                        className: \"absolute top-4 right-4 bg-black/50 hover:bg-black/70 text-white rounded-full p-2 transition-colors duration-200\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-5 h-5\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M6 18L18 6M6 6l12 12\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                lineNumber: 610,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                            lineNumber: 609,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 605,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                lineNumber: 580,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                            lineNumber: 542,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl h-80 flex items-center justify-center border-2 border-dashed border-gray-300\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center text-gray-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-8 h-8 text-gray-400\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 1.5,\n                                                                d: \"M15.75 10.5l4.72-4.72a.75.75 0 011.28.53v11.38a.75.75 0 01-1.28.53l-4.72-4.72M4.5 18.75h9a2.25 2.25 0 002.25-2.25v-9a2.25 2.25 0 00-2.25-2.25h-9A2.25 2.25 0 002.25 7.5v9a2.25 2.25 0 002.25 2.25z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                lineNumber: 621,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                            lineNumber: 620,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 619,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg font-semibold text-gray-600 mb-2\",\n                                                        children: \"暂无教学视频\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 624,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"该课程暂未提供视频内容\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 625,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                lineNumber: 618,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                            lineNumber: 617,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                        lineNumber: 540,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                lineNumber: 523,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-r from-indigo-500 to-purple-600 px-6 py-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-bold text-white flex items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-6 h-6\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 637,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 636,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"教学课件\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                            lineNumber: 635,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                        lineNumber: 634,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: (courseDetail === null || courseDetail === void 0 ? void 0 : courseDetail.hasDocument) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl overflow-hidden border border-gray-200 shadow-inner\",\n                                            children: ((_courseDetail_contentConfig1 = courseDetail.contentConfig) === null || _courseDetail_contentConfig1 === void 0 ? void 0 : (_courseDetail_contentConfig_document = _courseDetail_contentConfig1.document) === null || _courseDetail_contentConfig_document === void 0 ? void 0 : _courseDetail_contentConfig_document.url) && courseDetail.contentConfig.document.url.trim() !== \"\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"iframe\", {\n                                                        src: \"\".concat(courseDetail.contentConfig.document.url, \"#toolbar=1&navpanes=1&scrollbar=1&page=1&view=FitH\"),\n                                                        className: \"w-full h-[600px] rounded-xl border-0\",\n                                                        title: \"教学课件预览\",\n                                                        allow: \"fullscreen\",\n                                                        loading: \"lazy\",\n                                                        style: {\n                                                            background: \"white\",\n                                                            boxShadow: \"inset 0 0 10px rgba(0,0,0,0.1)\"\n                                                        },\n                                                        onLoad: ()=>console.log(\"PDF课件加载完成\"),\n                                                        onError: (e)=>{\n                                                            console.error(\"PDF课件加载失败\");\n                                                            // 阻止错误传播，避免触发下载\n                                                            e.preventDefault();\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 649,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute top-4 left-4 bg-white/95 backdrop-blur-sm rounded-lg px-3 py-2 shadow-lg border border-white/20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4 text-indigo-600\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                        lineNumber: 671,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                    lineNumber: 670,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-medium text-gray-700\",\n                                                                    children: courseDetail.contentConfig.document.name || \"教学课件.pdf\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                    lineNumber: 673,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                            lineNumber: 669,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 668,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute top-4 right-4 flex gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>{\n                                                                    var _courseDetail_contentConfig_document, _courseDetail_contentConfig;\n                                                                    if (courseDetail === null || courseDetail === void 0 ? void 0 : (_courseDetail_contentConfig = courseDetail.contentConfig) === null || _courseDetail_contentConfig === void 0 ? void 0 : (_courseDetail_contentConfig_document = _courseDetail_contentConfig.document) === null || _courseDetail_contentConfig_document === void 0 ? void 0 : _courseDetail_contentConfig_document.url) {\n                                                                        window.open(courseDetail.contentConfig.document.url, \"_blank\");\n                                                                    }\n                                                                },\n                                                                className: \"bg-white/95 hover:bg-white backdrop-blur-sm rounded-lg p-2 shadow-lg border border-white/20 transition-all duration-200 hover:scale-105\",\n                                                                title: \"在新窗口中打开\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4 text-gray-600\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                        lineNumber: 692,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                    lineNumber: 691,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                lineNumber: 682,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: (courseDetail === null || courseDetail === void 0 ? void 0 : (_courseDetail_contentConfig2 = courseDetail.contentConfig) === null || _courseDetail_contentConfig2 === void 0 ? void 0 : (_courseDetail_contentConfig_document1 = _courseDetail_contentConfig2.document) === null || _courseDetail_contentConfig_document1 === void 0 ? void 0 : _courseDetail_contentConfig_document1.url) || \"#\",\n                                                                download: (courseDetail === null || courseDetail === void 0 ? void 0 : (_courseDetail_contentConfig3 = courseDetail.contentConfig) === null || _courseDetail_contentConfig3 === void 0 ? void 0 : (_courseDetail_contentConfig_document2 = _courseDetail_contentConfig3.document) === null || _courseDetail_contentConfig_document2 === void 0 ? void 0 : _courseDetail_contentConfig_document2.name) || \"教学课件.pdf\",\n                                                                className: \"bg-white/95 hover:bg-white backdrop-blur-sm rounded-lg p-2 shadow-lg border border-white/20 transition-all duration-200 hover:scale-105\",\n                                                                title: \"下载课件\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4 text-gray-600\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                        lineNumber: 704,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                    lineNumber: 703,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                lineNumber: 697,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>{\n                                                                    const iframe = document.querySelector('iframe[title=\"教学课件预览\"]');\n                                                                    if (iframe && iframe.requestFullscreen) {\n                                                                        iframe.requestFullscreen();\n                                                                    }\n                                                                },\n                                                                className: \"bg-white/95 hover:bg-white backdrop-blur-sm rounded-lg p-2 shadow-lg border border-white/20 transition-all duration-200 hover:scale-105\",\n                                                                title: \"全屏查看\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4 text-gray-600\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                        lineNumber: 720,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                    lineNumber: 719,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                lineNumber: 709,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 680,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute bottom-4 left-4 bg-blue-500/90 text-white text-xs px-3 py-1 rounded-full backdrop-blur-sm\",\n                                                        children: \"PDF预览模式\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 726,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                lineNumber: 647,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-96 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-8 h-8 text-indigo-600\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 1.5,\n                                                                    d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                    lineNumber: 735,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                lineNumber: 734,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                            lineNumber: 733,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg font-semibold text-gray-700 mb-2\",\n                                                            children: ((_courseDetail_contentConfig4 = courseDetail.contentConfig) === null || _courseDetail_contentConfig4 === void 0 ? void 0 : (_courseDetail_contentConfig_document3 = _courseDetail_contentConfig4.document) === null || _courseDetail_contentConfig_document3 === void 0 ? void 0 : _courseDetail_contentConfig_document3.name) || \"教学课件\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                            lineNumber: 738,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"课件加载中...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                            lineNumber: 741,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-32 h-2 bg-gray-200 rounded-full mx-auto overflow-hidden\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-full bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full animate-pulse\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                    lineNumber: 744,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                lineNumber: 743,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                            lineNumber: 742,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 732,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                lineNumber: 731,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                            lineNumber: 645,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl h-96 flex items-center justify-center border-2 border-dashed border-gray-300\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-8 h-8 text-gray-400\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 1.5,\n                                                                d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                lineNumber: 756,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                            lineNumber: 755,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 754,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg font-semibold text-gray-600 mb-2\",\n                                                        children: \"暂无教学课件\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 759,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"该课程暂未提供课件内容\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 760,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                lineNumber: 753,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                            lineNumber: 752,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                        lineNumber: 643,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                lineNumber: 633,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                        lineNumber: 520,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-r from-teal-500 to-cyan-600 px-6 py-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold text-white flex items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-6 h-6\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 778,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 777,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"课程附件\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                            lineNumber: 776,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                        lineNumber: 775,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: (courseDetail === null || courseDetail === void 0 ? void 0 : courseDetail.additionalResources) && courseDetail.additionalResources.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-[216px] overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 hover:scrollbar-thumb-gray-400\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2 pr-2\",\n                                                        children: courseDetail.additionalResources.map((resource, index)=>{\n                                                            // 根据文件扩展名确定文件类型和样式\n                                                            const getFileTypeConfig = (title)=>{\n                                                                var _title_split_pop;\n                                                                const extension = (_title_split_pop = title.split(\".\").pop()) === null || _title_split_pop === void 0 ? void 0 : _title_split_pop.toLowerCase();\n                                                                if (extension === \"pptx\" || extension === \"ppt\") {\n                                                                    return {\n                                                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-5 h-5 text-white\",\n                                                                            fill: \"none\",\n                                                                            stroke: \"currentColor\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0a2 2 0 00-2 2v14a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                                lineNumber: 799,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                            lineNumber: 798,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        gradient: \"from-orange-500 to-red-600\",\n                                                                        bgColor: \"from-orange-50 to-red-50\",\n                                                                        borderColor: \"border-orange-200\",\n                                                                        hoverColor: \"group-hover:text-orange-700\",\n                                                                        buttonBg: \"bg-orange-100 hover:bg-orange-200 text-orange-600\",\n                                                                        fileType: \"PowerPoint 演示文稿\"\n                                                                    };\n                                                                } else if (extension === \"pdf\") {\n                                                                    return {\n                                                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-5 h-5 text-white\",\n                                                                            fill: \"none\",\n                                                                            stroke: \"currentColor\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                                lineNumber: 813,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                            lineNumber: 812,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        gradient: \"from-red-500 to-pink-600\",\n                                                                        bgColor: \"from-red-50 to-pink-50\",\n                                                                        borderColor: \"border-red-200\",\n                                                                        hoverColor: \"group-hover:text-red-700\",\n                                                                        buttonBg: \"bg-red-100 hover:bg-red-200 text-red-600\",\n                                                                        fileType: \"PDF 文档\"\n                                                                    };\n                                                                } else if (extension === \"docx\" || extension === \"doc\") {\n                                                                    return {\n                                                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-5 h-5 text-white\",\n                                                                            fill: \"none\",\n                                                                            stroke: \"currentColor\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                                lineNumber: 827,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                            lineNumber: 826,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        gradient: \"from-blue-500 to-indigo-600\",\n                                                                        bgColor: \"from-blue-50 to-indigo-50\",\n                                                                        borderColor: \"border-blue-200\",\n                                                                        hoverColor: \"group-hover:text-blue-700\",\n                                                                        buttonBg: \"bg-blue-100 hover:bg-blue-200 text-blue-600\",\n                                                                        fileType: \"Word 文档\"\n                                                                    };\n                                                                } else {\n                                                                    return {\n                                                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-5 h-5 text-white\",\n                                                                            fill: \"none\",\n                                                                            stroke: \"currentColor\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                                lineNumber: 841,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                            lineNumber: 840,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        gradient: \"from-emerald-500 to-teal-600\",\n                                                                        bgColor: \"from-emerald-50 to-teal-50\",\n                                                                        borderColor: \"border-emerald-200\",\n                                                                        hoverColor: \"group-hover:text-emerald-700\",\n                                                                        buttonBg: \"bg-emerald-100 hover:bg-emerald-200 text-emerald-600\",\n                                                                        fileType: \"附件文档\"\n                                                                    };\n                                                                }\n                                                            };\n                                                            const config = getFileTypeConfig(resource.title);\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"group relative bg-gradient-to-r \".concat(config.bgColor, \" border \").concat(config.borderColor, \" rounded-lg p-3 hover:shadow-md transition-all duration-300 cursor-pointer\"),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-10 h-10 bg-gradient-to-br \".concat(config.gradient, \" rounded-lg flex items-center justify-center shadow-md group-hover:scale-110 transition-transform duration-200\"),\n                                                                            children: config.icon\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                            lineNumber: 859,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                    className: \"text-sm font-semibold text-gray-900 \".concat(config.hoverColor, \" transition-colors\"),\n                                                                                    children: resource.title\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                                    lineNumber: 863,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-xs text-gray-500 mt-0.5\",\n                                                                                    children: [\n                                                                                        config.fileType,\n                                                                                        \" \",\n                                                                                        resource.description && \"• \".concat(resource.description)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                                    lineNumber: 866,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                            lineNumber: 862,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    className: \"p-1.5 bg-white/80 hover:bg-white rounded-md shadow-sm transition-colors duration-200\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                        className: \"w-3.5 h-3.5 text-gray-600\",\n                                                                                        fill: \"none\",\n                                                                                        stroke: \"currentColor\",\n                                                                                        viewBox: \"0 0 24 24\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                strokeLinecap: \"round\",\n                                                                                                strokeLinejoin: \"round\",\n                                                                                                strokeWidth: 2,\n                                                                                                d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                                                lineNumber: 873,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                strokeLinecap: \"round\",\n                                                                                                strokeLinejoin: \"round\",\n                                                                                                strokeWidth: 2,\n                                                                                                d: \"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                                                lineNumber: 874,\n                                                                                                columnNumber: 35\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                                        lineNumber: 872,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                                    lineNumber: 871,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                                    href: resource.url,\n                                                                                    target: \"_blank\",\n                                                                                    rel: \"noopener noreferrer\",\n                                                                                    className: \"p-1.5 \".concat(config.buttonBg, \" rounded-md transition-colors duration-200\"),\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                        className: \"w-3.5 h-3.5\",\n                                                                                        fill: \"none\",\n                                                                                        stroke: \"currentColor\",\n                                                                                        viewBox: \"0 0 24 24\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                            strokeLinecap: \"round\",\n                                                                                            strokeLinejoin: \"round\",\n                                                                                            strokeWidth: 2,\n                                                                                            d: \"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                                            lineNumber: 884,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                                        lineNumber: 883,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                                    lineNumber: 877,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                            lineNumber: 870,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                    lineNumber: 858,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, index, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                lineNumber: 857,\n                                                                columnNumber: 25\n                                                            }, this);\n                                                        })\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 789,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 788,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-4 pt-3 border-t border-gray-200\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between text-xs text-gray-500\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \"共 \",\n                                                                    courseDetail.additionalResources.length,\n                                                                    \" 个附件\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                lineNumber: 898,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"点击下载按钮获取文件\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                lineNumber: 899,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 897,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 896,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : /* 空状态 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl h-32 flex items-center justify-center border-2 border-dashed border-gray-300\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-6 h-6 text-gray-400\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 1.5,\n                                                                d: \"M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                lineNumber: 909,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                            lineNumber: 908,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 907,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-semibold text-gray-600 mb-1\",\n                                                        children: \"暂无课程附件\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 912,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: \"该课程暂未提供附件下载\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 913,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                lineNumber: 906,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                            lineNumber: 905,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                        lineNumber: 784,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                lineNumber: 774,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-br from-white via-blue-50/20 to-indigo-50/30 rounded-xl border border-blue-200/60 shadow-lg backdrop-blur-sm overflow-hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-r from-blue-50/80 to-indigo-50/80 backdrop-blur-md border-b border-blue-100/50 px-6 py-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-lg flex items-center justify-center shadow-sm\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 text-white\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                            lineNumber: 927,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 926,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 925,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-bold text-gray-900 tracking-tight\",\n                                                    children: \"系列课程列表\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 930,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center gap-1 px-3 py-1 bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-700 text-sm font-semibold rounded-full border border-blue-200/50 shadow-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-3 h-3\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0a2 2 0 00-2 2v14a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                lineNumber: 935,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                            lineNumber: 934,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"共\",\n                                                        seriesCourses.length,\n                                                        \"课\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 933,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                            lineNumber: 924,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                        lineNumber: 923,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center py-12\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"animate-spin rounded-full h-10 w-10 border-3 border-blue-200\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                            lineNumber: 947,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"animate-spin rounded-full h-10 w-10 border-3 border-blue-600 border-t-transparent absolute top-0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                            lineNumber: 948,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 946,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-3 text-gray-600 font-medium\",\n                                                    children: \"加载课程列表...\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 950,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                            lineNumber: 945,\n                                            columnNumber: 17\n                                        }, this) : seriesCourses.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3 h-40 overflow-y-auto custom-scrollbar\",\n                                            children: seriesCourses.map((courseItem, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"group relative bg-gradient-to-r from-gray-50/50 to-blue-50/30 hover:from-blue-50 hover:to-indigo-50 rounded-xl px-4 py-1 border border-gray-200/50 hover:border-blue-300/50 transition-all duration-300 hover:shadow-md cursor-pointer\",\n                                                    onClick: ()=>handleCourseClick(courseItem),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute left-4 top-1.5 w-6 h-6 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center text-white text-xs font-bold shadow-sm\",\n                                                            children: index + 1\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                            lineNumber: 961,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center ml-10\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1 flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                            className: \"text-gray-800 font-medium group-hover:text-gray-900 transition-colors\",\n                                                                            style: {\n                                                                                marginBottom: 0\n                                                                            },\n                                                                            children: courseItem.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                            lineNumber: 967,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-gray-500\",\n                                                                            children: [\n                                                                                \"第\",\n                                                                                index + 1,\n                                                                                \"课\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                            lineNumber: 970,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                    lineNumber: 966,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"inline-flex items-center gap-1 px-3 py-1.5 text-xs font-semibold rounded-full transition-all duration-200 \".concat(courseItem.status === 1 ? \"bg-gradient-to-r from-emerald-100 to-green-100 text-emerald-700 border border-emerald-200/50 shadow-sm\" : \"bg-gradient-to-r from-gray-100 to-slate-100 text-gray-600 border border-gray-200/50\"),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-2 h-2 rounded-full \".concat(courseItem.status === 1 ? \"bg-emerald-500\" : \"bg-gray-400\")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                                lineNumber: 981,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            courseItem.status === 1 ? \"已发布\" : \"草稿\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                        lineNumber: 976,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                    lineNumber: 975,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                            lineNumber: 965,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, courseItem.id, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 955,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                            lineNumber: 953,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-16\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 bg-gradient-to-r from-gray-100 to-gray-200 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-8 h-8 text-gray-400\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 1.5,\n                                                            d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                            lineNumber: 995,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 994,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 993,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-500 font-medium\",\n                                                    children: \"暂无课程\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 998,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-400 text-sm mt-1\",\n                                                    children: \"该系列还没有添加课程\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 999,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                            lineNumber: 992,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                        lineNumber: 943,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                lineNumber: 921,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                        lineNumber: 772,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                lineNumber: 518,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n        lineNumber: 415,\n        columnNumber: 5\n    }, this);\n}\n_s(CourseDetailView, \"fijPwY+vJmwhygbo4Cj3GpvYaxQ=\");\n_c = CourseDetailView;\nvar _c;\n$RefreshReg$(_c, \"CourseDetailView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/(main)/course-plaza/components/CourseDetailView.tsx\n"));

/***/ })

});