"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/workbench/page",{

/***/ "(app-pages-browser)/./app/workbench/components/CourseListEditModal.tsx":
/*!**********************************************************!*\
  !*** ./app/workbench/components/CourseListEditModal.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FileText,Plus,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FileText,Plus,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FileText,Plus,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FileText,Plus,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FileText,Plus,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _lib_api_course__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api/course */ \"(app-pages-browser)/./lib/api/course.ts\");\n/* harmony import */ var _lib_api_upload__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api/upload */ \"(app-pages-browser)/./lib/api/upload.ts\");\n/* harmony import */ var _ToastManager__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ToastManager */ \"(app-pages-browser)/./app/workbench/components/ToastManager.tsx\");\n/* harmony import */ var _barrel_optimize_names_Select_antd__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Select!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/select/index.js\");\n/* harmony import */ var _CourseListEditModal_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./CourseListEditModal.css */ \"(app-pages-browser)/./app/workbench/components/CourseListEditModal.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n// 获取系列课程详情\nconst fetchSeriesDetail = async (seriesId)=>{\n    console.log(\"\\uD83D\\uDD0D 获取系列课程详情，seriesId:\", seriesId);\n    console.log(\"\\uD83D\\uDD17 调用API: GET /api/v1/course-management/series/{seriesId}\");\n    const response = await _lib_api_course__WEBPACK_IMPORTED_MODULE_2__.courseApi.getSeriesDetail(seriesId);\n    console.log(\"\\uD83D\\uDCE1 系列详情API响应:\", response);\n    return response.data;\n};\n// API调用函数 - 获取系列下的所有课程（包含未发布和已发布）\nconst fetchCourseList = async (seriesId)=>{\n    console.log(\"\\uD83D\\uDD0D 获取系列课程列表，seriesId:\", seriesId);\n    console.log(\"\\uD83D\\uDD17 调用API: GET /api/v1/course-management/series/{seriesId}/courses\");\n    // 使用课程管理API获取所有状态的课程\n    const response = await _lib_api_course__WEBPACK_IMPORTED_MODULE_2__.courseApi.getManagementSeriesCourses(seriesId, {\n        page: 1,\n        pageSize: 100\n    });\n    console.log(\"\\uD83D\\uDCE1 API响应:\", response);\n    return response.data;\n};\n// 获取课程详情\nconst fetchCourseDetail = async (seriesId, courseId)=>{\n    return await _lib_api_course__WEBPACK_IMPORTED_MODULE_2__.courseApi.getCourseMarketplaceDetail(seriesId, courseId);\n};\n// 获取课程标签\nconst fetchCourseTags = async ()=>{\n    try {\n        console.log(\"\\uD83D\\uDD0D 开始调用 courseApi.getCourseTags\");\n        const result = await _lib_api_course__WEBPACK_IMPORTED_MODULE_2__.courseApi.getCourseTags({\n            page: 1,\n            pageSize: 100,\n            status: 1 // 只获取启用的标签\n        });\n        console.log(\"\\uD83D\\uDD0D courseApi.getCourseTags 返回结果:\", result);\n        return result;\n    } catch (error) {\n        console.error(\"\\uD83D\\uDD0D courseApi.getCourseTags 调用失败:\", error);\n        throw error;\n    }\n};\nconst CourseListEditModal = (param)=>{\n    let { isVisible, onClose, onSave, seriesTitle, seriesCoverImage, seriesId = 123 // 默认值，实际使用时应该传入真实的seriesId\n     } = param;\n    var _getSelectedCourse, _getSelectedCourse1, _getSelectedCourse2, _getSelectedCourse3, _courseDetail_contentConfig_video, _courseDetail_contentConfig, _courseDetail_contentConfig_document, _courseDetail_contentConfig1, _courseList_find, _courseList_find1;\n    _s();\n    const { showSuccess, showError } = (0,_ToastManager__WEBPACK_IMPORTED_MODULE_4__.useToast)();\n    const [courseList, setCourseList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [rightPanelType, setRightPanelType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"none\");\n    const [selectedCourseId, setSelectedCourseId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [uploadingFiles, setUploadingFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [isPublishing, setIsPublishing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isCreating, setIsCreating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPublishingSeries, setIsPublishingSeries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [seriesStatus, setSeriesStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0); // 0=草稿，1=已发布，2=已归档\n    const [editingTitle, setEditingTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(seriesTitle);\n    const [courseGoals, setCourseGoals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [courseObjectives, setCourseObjectives] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [projectMembers, setProjectMembers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // 课程标签相关状态\n    const [courseTags, setCourseTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedTags, setSelectedTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [tagsLoading, setTagsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 课程详细编辑状态\n    const [courseDetail, setCourseDetail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: \"\",\n        description: \"\",\n        coverImage: \"\",\n        videoUrl: \"\",\n        videoName: \"\",\n        isVideoEnabled: false,\n        attachmentUrl: \"\",\n        attachmentName: \"\",\n        isAttachmentEnabled: false,\n        teachingMaterials: [],\n        // 支持teachingInfo结构\n        teachingInfo: [],\n        // 支持contentConfig结构\n        contentConfig: {\n            hasVideo: 0,\n            hasDocument: 0,\n            hasAudio: 0,\n            video: {\n                url: \"\",\n                name: \"\"\n            },\n            document: {\n                url: \"\",\n                name: \"\"\n            },\n            audio: {\n                url: \"\",\n                name: \"\"\n            }\n        },\n        courseContent: {\n            topic: \"\",\n            content: \"\"\n        },\n        isOneKeyOpen: false,\n        isDistributionEnabled: false,\n        distributionReward: \"\",\n        selectedTemplate: \"\",\n        isDistributionWater: false,\n        requiredEnergy: \"\",\n        energyAmount: \"\",\n        isDistributionLimit: false,\n        distributionConditions: {\n            inviteCount: \"\",\n            taskCount: \"\",\n            experience: \"\"\n        },\n        isDistributionTime: false,\n        distributionTimeConditions: {\n            startTime: \"\",\n            endTime: \"\"\n        },\n        distributionMaterials: [],\n        // 任务配置相关状态\n        taskConfig: {\n            taskName: \"\",\n            taskDuration: \"\",\n            taskDescription: \"\",\n            selfAssessmentItems: [\n                \"\"\n            ],\n            referenceWorks: [],\n            referenceResources: []\n        }\n    });\n    // 获取课程列表数据\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isVisible && seriesId) {\n            // 检查用户登录状态\n            const token = localStorage.getItem(\"token\");\n            console.log(\"\\uD83D\\uDD10 检查登录状态，token存在:\", !!token);\n            console.log(\"\\uD83D\\uDD0D seriesId:\", seriesId);\n            if (!token) {\n                console.error(\"❌ 用户未登录，无法获取课程列表\");\n                // 设置空列表，显示空状态\n                setCourseList([]);\n                setLoading(false);\n                return;\n            }\n            loadCourseList();\n            loadCourseTags();\n            loadSeriesDetail();\n        }\n    }, [\n        isVisible,\n        seriesId\n    ]);\n    const loadCourseList = async ()=>{\n        try {\n            setLoading(true);\n            console.log(\"\\uD83D\\uDD0D 开始加载课程列表，seriesId:\", seriesId);\n            const response = await fetchCourseList(seriesId);\n            console.log(\"\\uD83D\\uDCE1 API响应:\", response);\n            if (response.code === 200) {\n                console.log(\"✅ 课程列表数据:\", response.data);\n                const courses = response.data.list || [];\n                console.log(\"✅ 解析的课程数组:\", courses);\n                console.log(\"\\uD83D\\uDCCA 课程ID详情:\", courses.map((c)=>({\n                        id: c.id,\n                        type: typeof c.id,\n                        title: c.title,\n                        status: c.status\n                    })));\n                console.log(\"\\uD83D\\uDCCA 课程状态统计:\", {\n                    total: courses.length,\n                    draft: courses.filter((c)=>c.status === 0).length,\n                    published: courses.filter((c)=>c.status === 1).length\n                });\n                setCourseList(courses);\n            } else {\n                console.error(\"❌ API返回错误:\", response);\n                setCourseList([]);\n            }\n        } catch (error) {\n            var _error_response, _error_response1, _error_response2;\n            console.error(\"❌ 加载课程列表失败:\", error);\n            // 检查是否是认证错误\n            if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 401) {\n                console.error(\"\\uD83D\\uDD10 认证失败，用户未登录或token已过期\");\n            } else if (((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status) === 403) {\n                console.error(\"\\uD83D\\uDEAB 权限不足，无法访问该系列课程\");\n            } else if (((_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.status) === 404) {\n                console.error(\"\\uD83D\\uDCED 系列课程不存在，seriesId:\", seriesId);\n            } else {\n                console.error(\"\\uD83D\\uDD27 其他错误:\", error.message);\n            }\n            setCourseList([]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 加载系列课程详情\n    const loadSeriesDetail = async ()=>{\n        try {\n            console.log(\"\\uD83D\\uDD0D 开始加载系列课程详情，seriesId:\", seriesId);\n            const response = await fetchSeriesDetail(seriesId);\n            console.log(\"\\uD83D\\uDCE1 系列详情响应:\", response);\n            if (response.code === 200 && response.data) {\n                const seriesData = response.data;\n                console.log(\"✅ 系列课程详情:\", seriesData);\n                setSeriesStatus(seriesData.status || 0);\n                console.log(\"\\uD83D\\uDCCA 系列课程状态:\", seriesData.status, \"(0=草稿，1=已发布，2=已归档)\");\n            } else {\n                console.error(\"❌ 获取系列详情失败:\", response.message);\n            }\n        } catch (error) {\n            console.error(\"❌ 加载系列详情异常:\", error);\n        }\n    };\n    // 加载课程标签\n    const loadCourseTags = async ()=>{\n        try {\n            setTagsLoading(true);\n            console.log(\"\\uD83C\\uDFF7️ 开始加载课程标签\");\n            const response = await fetchCourseTags();\n            console.log(\"\\uD83D\\uDCE1 标签API完整响应:\", response);\n            // 检查响应结构\n            if (response && response.data) {\n                console.log(\"\\uD83D\\uDCCA 响应数据:\", response.data);\n                let tags = [];\n                // 处理标准的API响应格式 (response.data.list) - 根据实际API响应\n                if (response.data.list && Array.isArray(response.data.list)) {\n                    tags = response.data.list;\n                    console.log(\"✅ 从 data.list 解析到标签:\", tags.length, \"个\");\n                } else if (Array.isArray(response.data)) {\n                    tags = response.data;\n                    console.log(\"✅ 从 data 数组解析到标签:\", tags.length, \"个\");\n                } else if (response.data.data && response.data.data.list && Array.isArray(response.data.data.list)) {\n                    tags = response.data.data.list;\n                    console.log(\"✅ 从 data.data.list 解析到标签:\", tags.length, \"个\");\n                }\n                // 验证标签数据格式\n                console.log(\"\\uD83D\\uDD0D 原始标签数据:\", tags);\n                console.log(\"\\uD83D\\uDD0D 标签数据类型检查:\");\n                tags.forEach((tag, index)=>{\n                    var _tag_name;\n                    console.log(\"标签\".concat(index, \":\"), {\n                        tag,\n                        hasTag: !!tag,\n                        idType: typeof (tag === null || tag === void 0 ? void 0 : tag.id),\n                        nameType: typeof (tag === null || tag === void 0 ? void 0 : tag.name),\n                        nameValue: tag === null || tag === void 0 ? void 0 : tag.name,\n                        nameNotEmpty: (tag === null || tag === void 0 ? void 0 : (_tag_name = tag.name) === null || _tag_name === void 0 ? void 0 : _tag_name.trim()) !== \"\"\n                    });\n                });\n                const validTags = tags.filter((tag)=>{\n                    const isValid = tag && typeof tag.id === \"number\" && typeof tag.name === \"string\" && tag.name.trim() !== \"\";\n                    if (!isValid) {\n                        console.log(\"❌ 无效标签:\", tag, {\n                            hasTag: !!tag,\n                            idType: typeof (tag === null || tag === void 0 ? void 0 : tag.id),\n                            nameType: typeof (tag === null || tag === void 0 ? void 0 : tag.name),\n                            nameValue: tag === null || tag === void 0 ? void 0 : tag.name\n                        });\n                    }\n                    return isValid;\n                });\n                console.log(\"✅ 有效标签数量:\", validTags.length);\n                console.log(\"✅ 有效标签详情:\", validTags);\n                if (validTags.length > 0) {\n                    setCourseTags(validTags);\n                    console.log(\"✅ 成功设置真实标签数据\");\n                    return;\n                } else {\n                    console.warn(\"⚠️ 没有有效的标签数据\");\n                }\n            } else {\n                console.warn(\"⚠️ API响应格式不正确:\", response);\n            }\n            // 如果没有真实数据，设置空数组\n            console.log(\"\\uD83D\\uDCED 没有标签数据，设置空数组\");\n            setCourseTags([]);\n        } catch (error) {\n            var _error_response, _error_response1, _error_response2;\n            console.error(\"❌ 加载课程标签失败:\", error);\n            console.error(\"❌ 错误详情:\", {\n                message: error.message,\n                status: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status,\n                statusText: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.statusText,\n                data: (_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.data\n            });\n            // 发生错误时设置空数组\n            setCourseTags([]);\n        } finally{\n            setTagsLoading(false);\n        }\n    };\n    // 添加新课程\n    const addNewCourse = ()=>{\n        const newCourse = {\n            id: Date.now(),\n            seriesId: seriesId,\n            title: \"第\".concat(courseList.length + 1, \"课 - 新课时\"),\n            description: \"\",\n            coverImage: \"\",\n            orderIndex: courseList.length + 1,\n            status: 0,\n            statusLabel: \"草稿\",\n            hasVideo: 0,\n            hasDocument: 0,\n            hasAudio: 0,\n            videoDuration: 0,\n            videoDurationLabel: \"\",\n            videoName: \"\",\n            firstTeachingTitle: \"\",\n            resourcesCount: 0,\n            createdAt: new Date().toISOString(),\n            updatedAt: new Date().toISOString()\n        };\n        setCourseList([\n            ...courseList,\n            newCourse\n        ]);\n        // 自动选中新添加的课程\n        showCoursePanel(newCourse.id);\n    };\n    // 删除课程\n    const deleteCourse = (id)=>{\n        setCourseList(courseList.filter((course)=>course.id !== id));\n    };\n    // 更新课程标题\n    const updateCourseTitle = (id, newTitle)=>{\n        setCourseList(courseList.map((course)=>course.id === id ? {\n                ...course,\n                title: newTitle\n            } : course));\n    };\n    // 处理课程封面上传\n    const handleCoverUpload = async (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (file) {\n            // 检查文件类型\n            const allowedTypes = [\n                \"image/jpeg\",\n                \"image/jpg\",\n                \"image/png\",\n                \"image/gif\"\n            ];\n            if (!allowedTypes.includes(file.type)) {\n                alert(\"请选择 JPG、PNG 或 GIF 格式的图片文件\");\n                return;\n            }\n            // 检查文件大小 (10MB)\n            if (file.size > 10 * 1024 * 1024) {\n                alert(\"文件大小不能超过 10MB\");\n                return;\n            }\n            try {\n                console.log(\"\\uD83D\\uDCE4 开始上传课程封面:\", file.name);\n                // 添加到上传中的文件列表\n                setUploadingFiles((prev)=>new Set(prev).add(\"cover\"));\n                // 先显示预览图片\n                const previewUrl = URL.createObjectURL(file);\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        coverImage: previewUrl\n                    }));\n                // 上传到OSS\n                const imageUrl = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_3__.uploadApi.uploadToOss(file);\n                console.log(\"✅ 课程封面上传成功:\", imageUrl);\n                // 更新课程详情中的封面为真实URL\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        coverImage: imageUrl\n                    }));\n                // 同时更新课程列表中的封面\n                if (selectedCourseId) {\n                    setCourseList((prev)=>prev.map((course)=>course.id === selectedCourseId ? {\n                                ...course,\n                                coverImage: imageUrl\n                            } : course));\n                }\n                alert(\"课程封面上传成功！\");\n            } catch (error) {\n                console.error(\"❌ 课程封面上传失败:\", error);\n                alert(\"课程封面上传失败，请重试\");\n                // 上传失败时清除预览图片\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        coverImage: \"\"\n                    }));\n            } finally{\n                // 从上传中的文件列表移除\n                setUploadingFiles((prev)=>{\n                    const newSet = new Set(prev);\n                    newSet.delete(\"cover\");\n                    return newSet;\n                });\n            }\n        }\n    };\n    // 处理视频上传\n    const handleVideoUpload = async (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (file) {\n            // 检查文件类型\n            const allowedTypes = [\n                \"video/mp4\",\n                \"video/avi\",\n                \"video/mov\",\n                \"video/wmv\",\n                \"video/flv\"\n            ];\n            if (!allowedTypes.includes(file.type)) {\n                alert(\"请选择 MP4、AVI、MOV、WMV 或 FLV 格式的视频文件\");\n                return;\n            }\n            // 检查文件大小 (100MB)\n            if (file.size > 100 * 1024 * 1024) {\n                alert(\"视频文件大小不能超过 100MB\");\n                return;\n            }\n            try {\n                console.log(\"\\uD83D\\uDCE4 开始上传课程视频:\", file.name);\n                // 添加到上传中的文件列表\n                setUploadingFiles((prev)=>new Set(prev).add(\"video\"));\n                // 先显示预览视频\n                const previewUrl = URL.createObjectURL(file);\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        contentConfig: {\n                            ...prev.contentConfig,\n                            video: {\n                                url: previewUrl,\n                                name: file.name\n                            }\n                        }\n                    }));\n                // 上传到OSS\n                const videoUrl = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_3__.uploadApi.uploadToOss(file);\n                console.log(\"✅ 课程视频上传成功:\", videoUrl);\n                // 更新课程详情中的视频信息为真实URL\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        contentConfig: {\n                            ...prev.contentConfig,\n                            video: {\n                                url: videoUrl,\n                                name: file.name\n                            }\n                        }\n                    }));\n                // 同时更新课程列表中的视频信息\n                if (selectedCourseId) {\n                    setCourseList((prev)=>prev.map((course)=>course.id === selectedCourseId ? {\n                                ...course,\n                                contentConfig: {\n                                    ...course.contentConfig,\n                                    video: {\n                                        url: videoUrl,\n                                        name: file.name\n                                    }\n                                }\n                            } : course));\n                }\n                alert(\"课程视频上传成功！\");\n            } catch (error) {\n                console.error(\"❌ 课程视频上传失败:\", error);\n                alert(\"课程视频上传失败，请重试\");\n                // 上传失败时清除视频信息\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        contentConfig: {\n                            ...prev.contentConfig,\n                            video: {\n                                url: \"\",\n                                name: \"\"\n                            }\n                        }\n                    }));\n            } finally{\n                // 从上传中的文件列表移除\n                setUploadingFiles((prev)=>{\n                    const newSet = new Set(prev);\n                    newSet.delete(\"video\");\n                    return newSet;\n                });\n            }\n        }\n    };\n    // 触发视频文件选择\n    const triggerVideoUpload = ()=>{\n        const input = document.createElement(\"input\");\n        input.type = \"file\";\n        input.accept = \"video/mp4,video/avi,video/mov,video/wmv,video/flv\";\n        input.onchange = (e)=>handleVideoUpload(e);\n        input.click();\n    };\n    // 处理附件上传\n    const handleAttachmentUpload = async (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (file) {\n            // 检查文件类型\n            const allowedTypes = [\n                \"application/pdf\",\n                \"application/msword\",\n                \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\",\n                \"application/vnd.ms-excel\",\n                \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\",\n                \"application/vnd.ms-powerpoint\",\n                \"application/vnd.openxmlformats-officedocument.presentationml.presentation\",\n                \"text/plain\",\n                \"image/jpeg\",\n                \"image/png\",\n                \"image/gif\"\n            ];\n            if (!allowedTypes.includes(file.type)) {\n                alert(\"请选择支持的文件格式：PDF、DOC、DOCX、XLS、XLSX、PPT、PPTX、TXT、JPG、PNG、GIF\");\n                return;\n            }\n            // 检查文件大小 (10MB)\n            if (file.size > 10 * 1024 * 1024) {\n                alert(\"文件大小不能超过 10MB\");\n                return;\n            }\n            try {\n                console.log(\"\\uD83D\\uDCE4 开始上传课程附件:\", file.name);\n                // 添加到上传中的文件列表\n                setUploadingFiles((prev)=>new Set(prev).add(\"document\"));\n                // 上传到OSS\n                const documentUrl = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_3__.uploadApi.uploadToOss(file);\n                console.log(\"✅ 课程附件上传成功:\", documentUrl);\n                // 更新课程详情中的附件信息\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        contentConfig: {\n                            ...prev.contentConfig,\n                            hasDocument: 1,\n                            document: {\n                                url: documentUrl,\n                                name: file.name\n                            }\n                        }\n                    }));\n                alert(\"课程附件上传成功！\");\n            } catch (error) {\n                console.error(\"❌ 课程附件上传失败:\", error);\n                alert(\"课程附件上传失败，请重试\");\n            } finally{\n                // 从上传中的文件列表移除\n                setUploadingFiles((prev)=>{\n                    const newSet = new Set(prev);\n                    newSet.delete(\"document\");\n                    return newSet;\n                });\n            }\n        }\n    };\n    // 触发附件文件选择\n    const triggerAttachmentUpload = ()=>{\n        const input = document.createElement(\"input\");\n        input.type = \"file\";\n        input.accept = \".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.jpg,.png,.gif\";\n        input.onchange = (e)=>handleAttachmentUpload(e);\n        input.click();\n    };\n    // 处理教学附件上传\n    const handleTeachingMaterialUpload = async (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (file) {\n            // 检查文件类型\n            const allowedTypes = [\n                \"application/pdf\",\n                \"application/msword\",\n                \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\",\n                \"application/vnd.ms-excel\",\n                \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\",\n                \"application/vnd.ms-powerpoint\",\n                \"application/vnd.openxmlformats-officedocument.presentationml.presentation\",\n                \"text/plain\",\n                \"image/jpeg\",\n                \"image/png\",\n                \"image/gif\"\n            ];\n            if (!allowedTypes.includes(file.type)) {\n                alert(\"请选择支持的文件格式：PDF、DOC、DOCX、XLS、XLSX、PPT、PPTX、TXT、JPG、PNG、GIF\");\n                return;\n            }\n            // 检查文件大小 (10MB)\n            if (file.size > 10 * 1024 * 1024) {\n                alert(\"文件大小不能超过 10MB\");\n                return;\n            }\n            try {\n                console.log(\"\\uD83D\\uDCE4 开始上传教学材料:\", file.name);\n                // 添加到上传中的文件列表\n                setUploadingFiles((prev)=>new Set(prev).add(\"teaching-\".concat(Date.now())));\n                // 上传到OSS\n                const materialUrl = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_3__.uploadApi.uploadToOss(file);\n                console.log(\"✅ 教学材料上传成功:\", materialUrl);\n                // 添加到教学附件列表\n                const newMaterial = {\n                    type: file.type,\n                    name: file.name,\n                    url: materialUrl\n                };\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        teachingMaterials: [\n                            ...prev.teachingMaterials,\n                            newMaterial\n                        ]\n                    }));\n                alert(\"教学材料上传成功！\");\n            } catch (error) {\n                console.error(\"❌ 教学材料上传失败:\", error);\n                alert(\"教学材料上传失败，请重试\");\n            } finally{\n                // 从上传中的文件列表移除\n                setUploadingFiles((prev)=>{\n                    const newSet = new Set(prev);\n                    // 移除所有teaching-开头的项目\n                    Array.from(newSet).forEach((item)=>{\n                        if (item.startsWith(\"teaching-\")) {\n                            newSet.delete(item);\n                        }\n                    });\n                    return newSet;\n                });\n            }\n        }\n    };\n    // 触发教学附件文件选择\n    const triggerTeachingMaterialUpload = ()=>{\n        const input = document.createElement(\"input\");\n        input.type = \"file\";\n        input.accept = \".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.jpg,.png,.gif\";\n        input.onchange = (e)=>handleTeachingMaterialUpload(e);\n        input.click();\n    };\n    // 删除教学附件\n    const removeTeachingMaterial = (index)=>{\n        setCourseDetail((prev)=>({\n                ...prev,\n                teachingMaterials: prev.teachingMaterials.filter((_, i)=>i !== index)\n            }));\n    };\n    // 课程内容管理函数\n    const addTeachingInfoItem = ()=>{\n        setCourseDetail((prev)=>({\n                ...prev,\n                teachingInfo: [\n                    ...prev.teachingInfo,\n                    {\n                        title: \"\",\n                        content: \"\"\n                    }\n                ]\n            }));\n    };\n    const removeTeachingInfoItem = (index)=>{\n        setCourseDetail((prev)=>({\n                ...prev,\n                teachingInfo: prev.teachingInfo.filter((_, i)=>i !== index)\n            }));\n    };\n    const updateTeachingInfoTitle = (index, title)=>{\n        setCourseDetail((prev)=>{\n            const newTeachingInfo = [\n                ...prev.teachingInfo\n            ];\n            newTeachingInfo[index] = {\n                ...newTeachingInfo[index],\n                title\n            };\n            return {\n                ...prev,\n                teachingInfo: newTeachingInfo\n            };\n        });\n    };\n    const updateTeachingInfoContent = (index, content)=>{\n        setCourseDetail((prev)=>{\n            const newTeachingInfo = [\n                ...prev.teachingInfo\n            ];\n            newTeachingInfo[index] = {\n                ...newTeachingInfo[index],\n                content\n            };\n            return {\n                ...prev,\n                teachingInfo: newTeachingInfo\n            };\n        });\n    };\n    // 将UI格式的teachingInfo转换为API格式\n    const convertTeachingInfoForAPI = (teachingInfo)=>{\n        return teachingInfo.map((info)=>({\n                title: info.title,\n                content: info.content.split(\"\\n\").filter((line)=>line.trim()) // 按换行分割并过滤空行\n            }));\n    };\n    // 检查是否有未上传完成的文件（blob URL）\n    const checkForBlobUrls = (course)=>{\n        var _course_contentConfig_video, _course_contentConfig, _course_contentConfig_document, _course_contentConfig1;\n        const issues = [];\n        // 检查封面图片\n        if (course.coverImage && course.coverImage.startsWith(\"blob:\")) {\n            issues.push(\"课程封面图片\");\n        }\n        // 检查视频文件\n        if (((_course_contentConfig = course.contentConfig) === null || _course_contentConfig === void 0 ? void 0 : (_course_contentConfig_video = _course_contentConfig.video) === null || _course_contentConfig_video === void 0 ? void 0 : _course_contentConfig_video.url) && course.contentConfig.video.url.startsWith(\"blob:\")) {\n            issues.push(\"课程视频\");\n        }\n        // 检查文档附件\n        if (((_course_contentConfig1 = course.contentConfig) === null || _course_contentConfig1 === void 0 ? void 0 : (_course_contentConfig_document = _course_contentConfig1.document) === null || _course_contentConfig_document === void 0 ? void 0 : _course_contentConfig_document.url) && course.contentConfig.document.url.startsWith(\"blob:\")) {\n            issues.push(\"课程文档\");\n        }\n        // 检查教学材料\n        if (course.additionalResources) {\n            course.additionalResources.forEach((resource, index)=>{\n                if (resource.url && resource.url.startsWith(\"blob:\")) {\n                    issues.push(\"教学材料\".concat(index + 1));\n                }\n            });\n        }\n        return issues;\n    };\n    // 保存单个课程到后端\n    const saveCourse = async (course)=>{\n        try {\n            var _course_contentConfig_video, _course_contentConfig, _course_contentConfig_document, _course_contentConfig1;\n            // 验证必要的数据\n            if (!course.title || course.title.trim() === \"\") {\n                throw new Error(\"课程标题不能为空\");\n            }\n            // 检查是否有未上传完成的文件\n            const blobIssues = checkForBlobUrls(course);\n            if (blobIssues.length > 0) {\n                throw new Error(\"以下文件尚未上传完成，请等待上传完成后再保存：\".concat(blobIssues.join(\"、\")));\n            }\n            // 准备课程数据\n            const courseData = {\n                seriesId: seriesId,\n                title: course.title,\n                description: course.description || \"\",\n                coverImage: course.coverImage || \"\",\n                hasVideo: ((_course_contentConfig = course.contentConfig) === null || _course_contentConfig === void 0 ? void 0 : (_course_contentConfig_video = _course_contentConfig.video) === null || _course_contentConfig_video === void 0 ? void 0 : _course_contentConfig_video.url) ? 1 : 0,\n                hasDocument: ((_course_contentConfig1 = course.contentConfig) === null || _course_contentConfig1 === void 0 ? void 0 : (_course_contentConfig_document = _course_contentConfig1.document) === null || _course_contentConfig_document === void 0 ? void 0 : _course_contentConfig_document.url) ? 1 : 0,\n                hasAudio: 0,\n                videoDuration: 0,\n                contentConfig: course.contentConfig || {},\n                teachingInfo: convertTeachingInfoForAPI(course.teachingInfo || []),\n                additionalResources: course.additionalResources || [],\n                orderIndex: course.orderIndex || 1\n            };\n            console.log(\"\\uD83D\\uDCBE 准备保存课程数据:\", courseData);\n            let result;\n            // 判断是新课程还是更新课程\n            if (course.id && course.id > 1000000) {\n                // 新课程，使用创建API\n                console.log(\"\\uD83D\\uDCE4 创建新课程\");\n                const { data: response } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_2__.courseApi.createCourse(courseData);\n                result = response;\n                console.log(\"✅ 课程创建成功\");\n            } else {\n                // 现有课程，使用更新API\n                console.log(\"\\uD83D\\uDCE4 更新现有课程:\", course.id);\n                const { data: response } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_2__.courseApi.updateCourse(course.id, courseData);\n                result = response;\n                console.log(\"✅ 课程更新成功\");\n            }\n            return result;\n        } catch (error) {\n            console.error(\"❌ 保存课程失败:\", error);\n            throw error;\n        }\n    };\n    // 发布选中的课程\n    const handlePublishCourse = async ()=>{\n        if (!selectedCourseId) {\n            alert(\"请先选择要发布的课程\");\n            return;\n        }\n        const selectedCourse = courseList.find((course)=>course.id === selectedCourseId);\n        if (!selectedCourse) {\n            alert(\"未找到选中的课程\");\n            return;\n        }\n        // 检查课程是否已经发布\n        if (selectedCourse.status === 1) {\n            alert(\"该课程已经发布，无需重复发布\");\n            return;\n        }\n        try {\n            setIsPublishing(true);\n            console.log(\"\\uD83D\\uDCE4 开始发布课程\");\n            console.log(\"\\uD83D\\uDCE4 selectedCourseId:\", selectedCourseId, \"类型:\", typeof selectedCourseId);\n            console.log(\"\\uD83D\\uDCE4 课程信息:\", selectedCourse);\n            console.log(\"\\uD83D\\uDCE4 课程ID字段:\", selectedCourse.id, \"类型:\", typeof selectedCourse.id);\n            console.log(\"\\uD83D\\uDCE4 课程列表中的所有ID:\", courseList.map((c)=>({\n                    id: c.id,\n                    type: typeof c.id,\n                    title: c.title\n                })));\n            // 确保使用正确的课程ID\n            const courseIdToPublish = selectedCourse.id;\n            console.log(\"\\uD83D\\uDCE4 即将发布的课程ID:\", courseIdToPublish, \"类型:\", typeof courseIdToPublish);\n            const { data: response } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_2__.courseApi.publishCourse(courseIdToPublish);\n            if (response.code === 200) {\n                console.log(\"✅ 课程发布成功:\", response.data);\n                alert(\"课程发布成功！\");\n                // 刷新课程列表以更新状态\n                await loadCourseList();\n            } else {\n                console.error(\"❌ 发布课程失败:\", response.message);\n                alert(response.message || \"发布课程失败\");\n            }\n        } catch (error) {\n            var _error_response, _error_response_data, _error_response1;\n            console.error(\"❌ 发布课程失败:\", error);\n            console.error(\"❌ 错误详情:\", (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data);\n            // 处理具体的错误信息\n            if ((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : (_error_response_data = _error_response1.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) {\n                alert(error.response.data.message);\n            } else if (error.message) {\n                alert(error.message);\n            } else {\n                alert(\"发布课程失败，请重试\");\n            }\n        } finally{\n            setIsPublishing(false);\n        }\n    };\n    // 保存课程列表\n    const handleSave = async ()=>{\n        try {\n            setIsCreating(true);\n            // 检查是否有文件正在上传\n            if (uploadingFiles.size > 0) {\n                alert(\"有文件正在上传中，请等待上传完成后再保存\");\n                setIsCreating(false);\n                return;\n            }\n            console.log(\"\\uD83D\\uDCBE 开始保存课程列表\");\n            // 如果有选中的课程且在编辑状态，先保存当前课程\n            if (selectedCourseId && rightPanelType === \"course\") {\n                const selectedCourse = courseList.find((c)=>c.id === selectedCourseId);\n                if (selectedCourse) {\n                    var _courseDetail_teachingMaterials;\n                    // 更新课程数据\n                    const updatedCourse = {\n                        ...selectedCourse,\n                        title: courseDetail.title,\n                        description: courseDetail.description,\n                        coverImage: courseDetail.coverImage,\n                        hasVideo: courseDetail.isVideoEnabled ? 1 : 0,\n                        hasDocument: courseDetail.isAttachmentEnabled ? 1 : 0,\n                        contentConfig: courseDetail.contentConfig,\n                        teachingInfo: courseDetail.teachingInfo,\n                        additionalResources: ((_courseDetail_teachingMaterials = courseDetail.teachingMaterials) === null || _courseDetail_teachingMaterials === void 0 ? void 0 : _courseDetail_teachingMaterials.map((material)=>({\n                                title: material.name,\n                                url: material.url,\n                                description: material.name\n                            }))) || []\n                    };\n                    // 先更新课程列表中的数据\n                    setCourseList((prev)=>prev.map((course)=>course.id === selectedCourseId ? updatedCourse : course));\n                    // 然后保存到后端\n                    await saveCourse(updatedCourse);\n                }\n            }\n            // 保存系列课程信息\n            const data = {\n                title: editingTitle,\n                courseGoals,\n                courseObjectives,\n                courseList\n            };\n            onSave(data);\n            onClose();\n            console.log(\"✅ 课程列表保存完成\");\n        } catch (error) {\n            console.error(\"❌ 保存失败:\", error);\n            alert(\"保存失败: \".concat(error.message || \"请重试\"));\n        } finally{\n            setIsCreating(false);\n        }\n    };\n    // 发布系列课程\n    const handlePublish = async ()=>{\n        // 如果系列已发布，不执行任何操作\n        if (seriesStatus === 1) {\n            return;\n        }\n        try {\n            setIsPublishingSeries(true);\n            // 检查是否有课程\n            if (courseList.length === 0) {\n                alert(\"发布失败：课程系列中至少需要包含一个课程\");\n                return;\n            }\n            console.log(\"\\uD83D\\uDCE4 开始发布系列课程，系列ID:\", seriesId);\n            const { data: response } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_2__.courseApi.publishCourseSeries(seriesId);\n            if (response.code === 200) {\n                console.log(\"✅ 系列课程发布成功:\", response.data);\n                // 构建成功消息\n                const publishData = response.data;\n                let successMessage = '系列课程\"'.concat(publishData.title, '\"发布成功！');\n                // 如果有发布统计信息，添加到消息中\n                if (publishData.publishStats) {\n                    const stats = publishData.publishStats;\n                    successMessage += \"\\n\\n发布统计：\\n• 总课程数：\".concat(publishData.totalCourses, \"\\n• 已发布课程：\").concat(publishData.publishedCourses, \"\\n• 视频课程：\").concat(stats.videoCourseCount, \"个\\n• 文档课程：\").concat(stats.documentCourseCount, \"个\\n• 总资源数：\").concat(stats.totalResourcesCount, \"个\");\n                    if (stats.totalVideoDuration > 0) {\n                        const durationMinutes = Math.round(stats.totalVideoDuration / 60);\n                        successMessage += \"\\n• 视频总时长：\".concat(durationMinutes, \"分钟\");\n                    }\n                }\n                alert(successMessage);\n                // 更新系列状态为已发布\n                setSeriesStatus(1);\n                // 刷新课程列表以更新状态\n                await loadCourseList();\n                // 通知父组件刷新数据\n                onSave({\n                    type: \"publish_series\",\n                    seriesId: seriesId,\n                    message: \"系列课程发布成功\"\n                });\n            } else {\n                console.error(\"❌ 发布系列课程失败:\", response.message);\n                alert(response.message || \"发布系列课程失败\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"❌ 发布系列课程出错:\", error);\n            // 处理具体的错误信息\n            let errorMessage = \"发布系列课程失败\";\n            if ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) {\n                errorMessage = error.response.data.message;\n            } else if (error.message) {\n                errorMessage = error.message;\n            }\n            alert(errorMessage);\n        } finally{\n            setIsPublishingSeries(false);\n        }\n    };\n    // 退出编辑模式 - 保存数据并关闭\n    const handleExitEdit = ()=>{\n        handleSave();\n    };\n    // 显示设置面板\n    const showSettingsPanel = ()=>{\n        setRightPanelType(\"settings\");\n        setSelectedCourseId(null);\n    };\n    // 显示课程编辑面板\n    const showCoursePanel = async (courseId)=>{\n        console.log(\"\\uD83C\\uDFAF showCoursePanel 被调用\");\n        console.log(\"\\uD83C\\uDFAF 传入的courseId:\", courseId, \"类型:\", typeof courseId);\n        console.log(\"\\uD83C\\uDFAF 当前课程列表:\", courseList.map((c)=>({\n                id: c.id,\n                type: typeof c.id,\n                title: c.title\n            })));\n        setRightPanelType(\"course\");\n        setSelectedCourseId(courseId);\n        // 获取选中的课程并更新courseDetail状态\n        const selectedCourse = courseList.find((course)=>course.id === courseId);\n        console.log(\"\\uD83C\\uDFAF 找到的课程:\", selectedCourse);\n        if (selectedCourse) {\n            try {\n                console.log(\"\\uD83D\\uDD04 获取课程详情，seriesId:\", seriesId, \"courseId:\", courseId);\n                // 获取真实的课程详情数据\n                const { data: res } = await fetchCourseDetail(seriesId, courseId);\n                if (res.code === 200 && res.data) {\n                    var _courseDetailData_additionalResources, _courseDetailData_teachingInfo;\n                    const courseDetailData = res.data;\n                    console.log(\"✅ 获取到课程详情:\", courseDetailData);\n                    // 将真实的 additionalResources 映射到 teachingMaterials\n                    const teachingMaterials = ((_courseDetailData_additionalResources = courseDetailData.additionalResources) === null || _courseDetailData_additionalResources === void 0 ? void 0 : _courseDetailData_additionalResources.map((resource)=>({\n                            type: \"application/octet-stream\",\n                            name: resource.title || resource.name || \"附件\",\n                            url: resource.url\n                        }))) || [];\n                    // 将API返回的teachingInfo数组格式转换为新的格式\n                    const mappedTeachingInfo = ((_courseDetailData_teachingInfo = courseDetailData.teachingInfo) === null || _courseDetailData_teachingInfo === void 0 ? void 0 : _courseDetailData_teachingInfo.map((info)=>({\n                            title: info.title || \"\",\n                            content: Array.isArray(info.content) ? info.content.join(\"\\n\") : info.content || \"\"\n                        }))) || [];\n                    console.log(\"\\uD83D\\uDCCE 映射的教学附件:\", teachingMaterials);\n                    console.log(\"\\uD83D\\uDCDA 映射的教学信息:\", mappedTeachingInfo);\n                    setCourseDetail((prev)=>{\n                        var _courseDetailData_contentConfig_video, _courseDetailData_contentConfig, _courseDetailData_contentConfig_video1, _courseDetailData_contentConfig1, _courseDetailData_contentConfig_document, _courseDetailData_contentConfig2, _courseDetailData_contentConfig_document1, _courseDetailData_contentConfig3;\n                        return {\n                            ...prev,\n                            title: courseDetailData.title,\n                            description: courseDetailData.description,\n                            coverImage: courseDetailData.coverImage || \"\",\n                            isVideoEnabled: courseDetailData.hasVideo === 1,\n                            isAttachmentEnabled: courseDetailData.hasDocument === 1,\n                            contentConfig: courseDetailData.contentConfig || {},\n                            teachingInfo: mappedTeachingInfo,\n                            teachingMaterials: teachingMaterials,\n                            videoUrl: ((_courseDetailData_contentConfig = courseDetailData.contentConfig) === null || _courseDetailData_contentConfig === void 0 ? void 0 : (_courseDetailData_contentConfig_video = _courseDetailData_contentConfig.video) === null || _courseDetailData_contentConfig_video === void 0 ? void 0 : _courseDetailData_contentConfig_video.url) || \"\",\n                            videoName: ((_courseDetailData_contentConfig1 = courseDetailData.contentConfig) === null || _courseDetailData_contentConfig1 === void 0 ? void 0 : (_courseDetailData_contentConfig_video1 = _courseDetailData_contentConfig1.video) === null || _courseDetailData_contentConfig_video1 === void 0 ? void 0 : _courseDetailData_contentConfig_video1.name) || \"\",\n                            attachmentUrl: ((_courseDetailData_contentConfig2 = courseDetailData.contentConfig) === null || _courseDetailData_contentConfig2 === void 0 ? void 0 : (_courseDetailData_contentConfig_document = _courseDetailData_contentConfig2.document) === null || _courseDetailData_contentConfig_document === void 0 ? void 0 : _courseDetailData_contentConfig_document.url) || \"\",\n                            attachmentName: ((_courseDetailData_contentConfig3 = courseDetailData.contentConfig) === null || _courseDetailData_contentConfig3 === void 0 ? void 0 : (_courseDetailData_contentConfig_document1 = _courseDetailData_contentConfig3.document) === null || _courseDetailData_contentConfig_document1 === void 0 ? void 0 : _courseDetailData_contentConfig_document1.name) || \"\"\n                        };\n                    });\n                } else {\n                    console.error(\"❌ 获取课程详情失败:\", res.message);\n                    // 使用基础数据作为后备\n                    setCourseDetail((prev)=>({\n                            ...prev,\n                            title: selectedCourse.title,\n                            description: selectedCourse.description,\n                            coverImage: selectedCourse.coverImage || \"\",\n                            isVideoEnabled: selectedCourse.hasVideo === 1,\n                            isAttachmentEnabled: selectedCourse.hasDocument === 1,\n                            teachingMaterials: [] // 清空附件列表\n                        }));\n                }\n            } catch (error) {\n                console.error(\"❌ 获取课程详情异常:\", error);\n                // 使用基础数据作为后备\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        title: selectedCourse.title,\n                        description: selectedCourse.description,\n                        coverImage: selectedCourse.coverImage || \"\",\n                        isVideoEnabled: selectedCourse.hasVideo === 1,\n                        isAttachmentEnabled: selectedCourse.hasDocument === 1,\n                        teachingMaterials: [] // 清空附件列表\n                    }));\n            }\n        }\n    };\n    // 获取选中的课程\n    const getSelectedCourse = ()=>{\n        return courseList.find((course)=>course.id === selectedCourseId);\n    };\n    if (!isVisible) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"course-list-modal-overlay\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"course-list-modal\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"course-list-header\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"course-list-title-section\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"course-list-title\",\n                                    children: \"课程列表\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 1199,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"course-list-actions\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: showSettingsPanel,\n                                            className: \"course-list-settings-btn \".concat(rightPanelType === \"settings\" ? \"active\" : \"\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1205,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 1201,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: addNewCourse,\n                                            className: \"course-list-add-btn\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1208,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 1207,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 1200,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                            lineNumber: 1198,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"course-list-close-btn\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"w-6 h-6\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                lineNumber: 1213,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                            lineNumber: 1212,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                    lineNumber: 1197,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"course-list-content\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"course-list-sidebar\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"course-list-items\",\n                                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"course-list-loading\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"加载中...\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 1224,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 1223,\n                                    columnNumber: 17\n                                }, undefined) : courseList.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"course-list-empty\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"course-list-empty-icon\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-12 h-12 text-gray-300\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1229,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 1228,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"course-list-empty-title\",\n                                            children: \"暂无课时\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 1231,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"course-list-empty-description\",\n                                            children: \"点击右上角的 + 按钮添加第一个课时\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 1232,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: addNewCourse,\n                                            className: \"course-list-empty-btn\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1239,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                \"添加课时\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 1235,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 1227,\n                                    columnNumber: 17\n                                }, undefined) : courseList.map((course)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"course-list-item \".concat(selectedCourseId === course.id ? \"active\" : \"\"),\n                                        onClick: ()=>showCoursePanel(course.id),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"course-list-item-content\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"course-list-item-text\",\n                                                        children: course.title\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1251,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"course-status-badge \".concat(course.status === 1 ? \"published\" : \"draft\"),\n                                                        children: course.status === 1 ? \"已发布\" : \"未发布\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1252,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1250,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                    deleteCourse(course.id);\n                                                },\n                                                className: \"course-list-item-delete\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-3 h-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1263,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1256,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, course.id, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 1245,\n                                        columnNumber: 19\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                lineNumber: 1221,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                            lineNumber: 1220,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"course-list-edit-area\",\n                            children: [\n                                rightPanelType === \"none\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"course-edit-empty\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"course-edit-empty-icon\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-16 h-16 text-gray-300\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1276,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 1275,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"course-edit-empty-title\",\n                                            children: \"无课程详情\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 1278,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"course-edit-empty-description\",\n                                            children: \"点击左侧课程或设置按钮查看详情\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 1279,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 1274,\n                                    columnNumber: 15\n                                }, undefined),\n                                rightPanelType === \"settings\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"course-series-cover\",\n                                            children: seriesCoverImage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: seriesCoverImage,\n                                                alt: \"系列课程封面\",\n                                                className: \"course-series-cover-image\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1290,\n                                                columnNumber: 21\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"course-series-cover-placeholder\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"系列课程封面\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1297,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1296,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 1288,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"course-edit-form\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-edit-field\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"course-edit-label\",\n                                                            children: \"系列课程标题\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1306,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: editingTitle,\n                                                            onChange: (e)=>setEditingTitle(e.target.value),\n                                                            className: \"course-edit-input\",\n                                                            placeholder: \"请输入系列课程标题\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1307,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1305,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-edit-field\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"course-edit-label\",\n                                                            children: \"课程标签\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1318,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Select_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            mode: \"multiple\",\n                                                            style: {\n                                                                width: \"100%\"\n                                                            },\n                                                            placeholder: \"请选择课程标签\",\n                                                            value: selectedTags,\n                                                            onChange: setSelectedTags,\n                                                            loading: tagsLoading,\n                                                            options: courseTags.map((tag)=>{\n                                                                console.log(\"\\uD83C\\uDFF7️ 渲染标签选项:\", tag);\n                                                                return {\n                                                                    label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            color: tag.color\n                                                                        },\n                                                                        children: tag.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1330,\n                                                                        columnNumber: 29\n                                                                    }, void 0),\n                                                                    value: tag.id\n                                                                };\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1319,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                fontSize: \"12px\",\n                                                                color: \"#666\",\n                                                                marginTop: \"4px\"\n                                                            },\n                                                            children: [\n                                                                \"调试: 当前标签数量 \",\n                                                                courseTags.length,\n                                                                \", 加载状态: \",\n                                                                tagsLoading ? \"是\" : \"否\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1339,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1317,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-edit-field\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"course-edit-label\",\n                                                            children: \"课程项目成员\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1346,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: projectMembers,\n                                                            onChange: (e)=>setProjectMembers(e.target.value),\n                                                            className: \"course-edit-input\",\n                                                            placeholder: \"请输入项目成员，如：张老师、李助教、王同学\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1347,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1345,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 1303,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true),\n                                rightPanelType === \"course\" && getSelectedCourse() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"course-detail-edit\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"course-detail-top\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"course-detail-cover\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"course-cover-upload-area\",\n                                                                onClick: ()=>{\n                                                                    var _document_getElementById;\n                                                                    return (_document_getElementById = document.getElementById(\"cover-upload-input\")) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.click();\n                                                                },\n                                                                children: courseDetail.coverImage || ((_getSelectedCourse = getSelectedCourse()) === null || _getSelectedCourse === void 0 ? void 0 : _getSelectedCourse.coverImage) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                    src: courseDetail.coverImage || ((_getSelectedCourse1 = getSelectedCourse()) === null || _getSelectedCourse1 === void 0 ? void 0 : _getSelectedCourse1.coverImage),\n                                                                    alt: \"课程封面\",\n                                                                    className: \"course-cover-image\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1371,\n                                                                    columnNumber: 27\n                                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"course-cover-placeholder\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"点击上传课程封面\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1378,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1377,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1366,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                id: \"cover-upload-input\",\n                                                                type: \"file\",\n                                                                accept: \"image/jpeg,image/jpg,image/png,image/gif\",\n                                                                onChange: handleCoverUpload,\n                                                                style: {\n                                                                    display: \"none\"\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1382,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1365,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"course-detail-basic\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"course-detail-field\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        children: \"课程标题\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1392,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        value: courseDetail.title || ((_getSelectedCourse2 = getSelectedCourse()) === null || _getSelectedCourse2 === void 0 ? void 0 : _getSelectedCourse2.title) || \"\",\n                                                                        onChange: (e)=>{\n                                                                            setCourseDetail((prev)=>({\n                                                                                    ...prev,\n                                                                                    title: e.target.value\n                                                                                }));\n                                                                            updateCourseTitle(selectedCourseId, e.target.value);\n                                                                        },\n                                                                        placeholder: \"请输入课程标题\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1393,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1391,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"course-detail-field\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        children: \"课程介绍\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1404,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                        value: courseDetail.description || ((_getSelectedCourse3 = getSelectedCourse()) === null || _getSelectedCourse3 === void 0 ? void 0 : _getSelectedCourse3.description) || \"\",\n                                                                        onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                    ...prev,\n                                                                                    description: e.target.value\n                                                                                })),\n                                                                        placeholder: \"请输入课程介绍\",\n                                                                        rows: 3\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1405,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1403,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1390,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1364,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"course-detail-section\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        children: \"课程资源\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1417,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"course-resource-item\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"resource-header-right\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"课程视频\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1422,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"switch\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"checkbox\",\n                                                                                checked: courseDetail.isVideoEnabled,\n                                                                                onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                            ...prev,\n                                                                                            isVideoEnabled: e.target.checked\n                                                                                        }))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1424,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"slider\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1429,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1423,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1421,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            courseDetail.isVideoEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"video-content-area\",\n                                                                children: ((_courseDetail_contentConfig = courseDetail.contentConfig) === null || _courseDetail_contentConfig === void 0 ? void 0 : (_courseDetail_contentConfig_video = _courseDetail_contentConfig.video) === null || _courseDetail_contentConfig_video === void 0 ? void 0 : _courseDetail_contentConfig_video.url) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"video-info-section\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"video-preview\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                                                                className: \"video-thumbnail\",\n                                                                                controls: true,\n                                                                                poster: courseDetail.coverImage,\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                                                                                        src: courseDetail.contentConfig.video.url,\n                                                                                        type: \"video/mp4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1443,\n                                                                                        columnNumber: 35\n                                                                                    }, undefined),\n                                                                                    \"您的浏览器不支持视频播放\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1438,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1437,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"video-name-centered\",\n                                                                            children: courseDetail.contentConfig.video.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1447,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            className: \"upload-btn-horizontal\",\n                                                                            onClick: triggerVideoUpload,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"重新上传\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1449,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1448,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1436,\n                                                                    columnNumber: 29\n                                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"video-upload-section\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"video-placeholder-centered\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"play-icon\",\n                                                                                children: \"▶\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1455,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1454,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            className: \"upload-btn-horizontal\",\n                                                                            onClick: triggerVideoUpload,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"上传视频\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1458,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1457,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1453,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1433,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1420,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"course-resource-item\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"resource-header-right\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"课程附件\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1469,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"switch\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"checkbox\",\n                                                                                checked: courseDetail.isAttachmentEnabled,\n                                                                                onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                            ...prev,\n                                                                                            isAttachmentEnabled: e.target.checked\n                                                                                        }))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1471,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"slider\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1476,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1470,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1468,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            courseDetail.isAttachmentEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"attachment-content-area\",\n                                                                children: ((_courseDetail_contentConfig1 = courseDetail.contentConfig) === null || _courseDetail_contentConfig1 === void 0 ? void 0 : (_courseDetail_contentConfig_document = _courseDetail_contentConfig1.document) === null || _courseDetail_contentConfig_document === void 0 ? void 0 : _courseDetail_contentConfig_document.url) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"attachment-info-section\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"attachment-preview\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"document-icon\",\n                                                                                    children: \"\\uD83D\\uDCC4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1485,\n                                                                                    columnNumber: 33\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"attachment-details\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"attachment-name\",\n                                                                                        children: courseDetail.contentConfig.document.name\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1487,\n                                                                                        columnNumber: 35\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1486,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1484,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            className: \"upload-btn-horizontal\",\n                                                                            onClick: triggerAttachmentUpload,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"重新上传\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1491,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1490,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1483,\n                                                                    columnNumber: 29\n                                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"attachment-upload-section\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"upload-btn-horizontal\",\n                                                                        onClick: triggerAttachmentUpload,\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"上传附件\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1497,\n                                                                            columnNumber: 33\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1496,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1495,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1480,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1467,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"course-resource-item\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"resource-header-simple\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"教学附件\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1508,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1507,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"teaching-materials\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"add-material-btn\",\n                                                                        onClick: triggerTeachingMaterialUpload,\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"+\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1512,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"上传\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1513,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1511,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    courseDetail.teachingMaterials && courseDetail.teachingMaterials.length > 0 ? courseDetail.teachingMaterials.map((material, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"material-item\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"material-name\",\n                                                                                    onClick: ()=>{\n                                                                                        if (material.url) {\n                                                                                            window.open(material.url, \"_blank\");\n                                                                                        }\n                                                                                    },\n                                                                                    style: {\n                                                                                        cursor: material.url ? \"pointer\" : \"default\",\n                                                                                        color: material.url ? \"#1890ff\" : \"inherit\",\n                                                                                        textDecoration: material.url ? \"underline\" : \"none\"\n                                                                                    },\n                                                                                    title: material.url ? \"点击下载附件\" : material.name,\n                                                                                    children: [\n                                                                                        \"\\uD83D\\uDCCE \",\n                                                                                        material.name\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1518,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    className: \"remove-material-btn\",\n                                                                                    onClick: ()=>removeTeachingMaterial(index),\n                                                                                    title: \"删除附件\",\n                                                                                    children: \"\\xd7\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1534,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, index, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1517,\n                                                                            columnNumber: 29\n                                                                        }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"empty-materials-hint\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            style: {\n                                                                                color: \"#999\",\n                                                                                fontSize: \"14px\"\n                                                                            },\n                                                                            children: \"暂无教学附件\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1545,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1544,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1510,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1506,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1416,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"course-detail-section\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"section-header\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                children: \"课程内容\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1555,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"add-content-section-btn\",\n                                                                onClick: addTeachingInfoItem,\n                                                                title: \"添加课程内容\",\n                                                                children: \"+ 添加课程内容\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1556,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1554,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"course-content-area\",\n                                                        children: courseDetail.teachingInfo && courseDetail.teachingInfo.length > 0 ? courseDetail.teachingInfo.map((info, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"teaching-info-card\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"card-header\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"card-title\",\n                                                                                children: [\n                                                                                    \"课程内容 \",\n                                                                                    index + 1\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1569,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"remove-card-btn\",\n                                                                                onClick: ()=>removeTeachingInfoItem(index),\n                                                                                title: \"删除此内容\",\n                                                                                children: \"\\xd7\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1570,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1568,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"card-content\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"input-group\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        children: \"标题\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1580,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                        type: \"text\",\n                                                                                        value: info.title,\n                                                                                        onChange: (e)=>updateTeachingInfoTitle(index, e.target.value),\n                                                                                        placeholder: \"请输入标题，如：教学目标、教学方法等\",\n                                                                                        className: \"title-input\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1581,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1579,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"input-group\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        children: \"内容\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1590,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                                        value: info.content,\n                                                                                        onChange: (e)=>updateTeachingInfoContent(index, e.target.value),\n                                                                                        placeholder: \"请输入具体内容，多个内容项可用换行分隔\",\n                                                                                        className: \"content-textarea\",\n                                                                                        rows: 4\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1591,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1589,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1578,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1567,\n                                                                columnNumber: 27\n                                                            }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"empty-content-hint\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"暂无课程内容，点击右上角按钮添加\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1604,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1603,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1564,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1553,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"course-detail-section\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"one-key-section\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"one-key-item\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"重新上课\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1614,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"switch\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"checkbox\",\n                                                                            checked: courseDetail.isOneKeyOpen,\n                                                                            onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                        ...prev,\n                                                                                        isOneKeyOpen: e.target.checked\n                                                                                    }))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1616,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"slider\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1621,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1615,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1613,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        courseDetail.isOneKeyOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"one-key-item\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"分配积木\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1628,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"switch\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: \"checkbox\",\n                                                                                    checked: courseDetail.isDistributionEnabled,\n                                                                                    onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                ...prev,\n                                                                                                isDistributionEnabled: e.target.checked\n                                                                                            }))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1630,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"slider\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1635,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1629,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        courseDetail.isDistributionEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"block-template-section\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    className: \"select-template-btn\",\n                                                                                    children: \"选择积木模板\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1639,\n                                                                                    columnNumber: 33\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"selected-template-display\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: courseDetail.selectedTemplate || \"选中的模板名字\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1643,\n                                                                                        columnNumber: 35\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1642,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1638,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1627,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"one-key-item\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"分配能量\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1650,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"switch\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: \"checkbox\",\n                                                                                    checked: courseDetail.isDistributionWater,\n                                                                                    onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                ...prev,\n                                                                                                isDistributionWater: e.target.checked\n                                                                                            }))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1652,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"slider\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1657,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1651,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1649,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                courseDetail.isDistributionWater && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"energy-input-section\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"需要能量：\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1663,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            value: courseDetail.requiredEnergy || \"\",\n                                                                            onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                        ...prev,\n                                                                                        requiredEnergy: e.target.value\n                                                                                    })),\n                                                                            placeholder: \"请输入需要的能量值\",\n                                                                            className: \"energy-input\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1664,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1662,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"one-key-item\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"分配任务\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1675,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"switch\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: \"checkbox\",\n                                                                                    checked: courseDetail.isDistributionLimit,\n                                                                                    onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                ...prev,\n                                                                                                isDistributionLimit: e.target.checked\n                                                                                            }))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1677,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"slider\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1682,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1676,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1674,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                courseDetail.isDistributionLimit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"task-config-form\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"task-config-row\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"task-config-field\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                            children: \"任务名称:\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1692,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                            type: \"text\",\n                                                                                            value: courseDetail.taskConfig.taskName,\n                                                                                            onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        taskConfig: {\n                                                                                                            ...prev.taskConfig,\n                                                                                                            taskName: e.target.value\n                                                                                                        }\n                                                                                                    })),\n                                                                                            placeholder: \"请输入任务名称\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1693,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1691,\n                                                                                    columnNumber: 33\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"task-config-field\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                            children: \"任务持续天数:\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1704,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                            type: \"number\",\n                                                                                            value: courseDetail.taskConfig.taskDuration,\n                                                                                            onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        taskConfig: {\n                                                                                                            ...prev.taskConfig,\n                                                                                                            taskDuration: e.target.value\n                                                                                                        }\n                                                                                                    })),\n                                                                                            placeholder: \"请输入天数\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1705,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1703,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1690,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"task-config-field task-config-full\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                    children: \"任务描述:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1719,\n                                                                                    columnNumber: 33\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                                    value: courseDetail.taskConfig.taskDescription,\n                                                                                    onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                ...prev,\n                                                                                                taskConfig: {\n                                                                                                    ...prev.taskConfig,\n                                                                                                    taskDescription: e.target.value\n                                                                                                }\n                                                                                            })),\n                                                                                    placeholder: \"请输入任务描述\",\n                                                                                    rows: 4\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1720,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1718,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"task-config-field task-config-full\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                    children: [\n                                                                                        \"任务自评项: \",\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"item-number\",\n                                                                                            children: courseDetail.taskConfig.selfAssessmentItems.length\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1733,\n                                                                                            columnNumber: 47\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1733,\n                                                                                    columnNumber: 33\n                                                                                }, undefined),\n                                                                                courseDetail.taskConfig.selfAssessmentItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"self-assessment-item\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                            type: \"text\",\n                                                                                            value: item,\n                                                                                            onChange: (e)=>{\n                                                                                                const newItems = [\n                                                                                                    ...courseDetail.taskConfig.selfAssessmentItems\n                                                                                                ];\n                                                                                                newItems[index] = e.target.value;\n                                                                                                setCourseDetail((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        taskConfig: {\n                                                                                                            ...prev.taskConfig,\n                                                                                                            selfAssessmentItems: newItems\n                                                                                                        }\n                                                                                                    }));\n                                                                                            },\n                                                                                            placeholder: \"请输入自评项内容\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1736,\n                                                                                            columnNumber: 37\n                                                                                        }, undefined)\n                                                                                    }, index, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1735,\n                                                                                        columnNumber: 35\n                                                                                    }, undefined)),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    type: \"button\",\n                                                                                    className: \"add-assessment-btn\",\n                                                                                    onClick: ()=>setCourseDetail((prev)=>({\n                                                                                                ...prev,\n                                                                                                taskConfig: {\n                                                                                                    ...prev.taskConfig,\n                                                                                                    selfAssessmentItems: [\n                                                                                                        ...prev.taskConfig.selfAssessmentItems,\n                                                                                                        \"\"\n                                                                                                    ]\n                                                                                                }\n                                                                                            })),\n                                                                                    children: \"+\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1751,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1732,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"task-config-field task-config-full\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                    children: \"任务参考作品:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1768,\n                                                                                    columnNumber: 33\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"reference-works-section\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                            type: \"button\",\n                                                                                            className: \"select-works-btn\",\n                                                                                            children: \"选择作品\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1770,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"reference-works-grid\",\n                                                                                            children: [\n                                                                                                courseDetail.taskConfig.referenceWorks.map((work, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"reference-work-item\",\n                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                            children: work.name || \"作品\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                            lineNumber: 1774,\n                                                                                                            columnNumber: 41\n                                                                                                        }, undefined)\n                                                                                                    }, index, false, {\n                                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                        lineNumber: 1773,\n                                                                                                        columnNumber: 39\n                                                                                                    }, undefined)),\n                                                                                                Array.from({\n                                                                                                    length: Math.max(0, 3 - courseDetail.taskConfig.referenceWorks.length)\n                                                                                                }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"reference-work-item empty\"\n                                                                                                    }, \"empty-\".concat(index), false, {\n                                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                        lineNumber: 1779,\n                                                                                                        columnNumber: 39\n                                                                                                    }, undefined))\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1771,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1769,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1767,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"task-config-field task-config-full\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                    children: \"任务参考资源:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1787,\n                                                                                    columnNumber: 33\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"reference-resources-section\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"reference-resources-grid\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                type: \"button\",\n                                                                                                className: \"upload-resource-btn\",\n                                                                                                onClick: ()=>{\n                                                                                                    // 触发文件上传\n                                                                                                    const input = document.createElement(\"input\");\n                                                                                                    input.type = \"file\";\n                                                                                                    input.accept = \".pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx,.txt,.jpg,.png,.gif\";\n                                                                                                    input.onchange = (e)=>{\n                                                                                                        var _e_target_files;\n                                                                                                        const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n                                                                                                        if (file) {\n                                                                                                            setCourseDetail((prev)=>({\n                                                                                                                    ...prev,\n                                                                                                                    taskConfig: {\n                                                                                                                        ...prev.taskConfig,\n                                                                                                                        referenceResources: [\n                                                                                                                            ...prev.taskConfig.referenceResources,\n                                                                                                                            {\n                                                                                                                                type: \"file\",\n                                                                                                                                name: file.name\n                                                                                                                            }\n                                                                                                                        ]\n                                                                                                                    }\n                                                                                                                }));\n                                                                                                        }\n                                                                                                    };\n                                                                                                    input.click();\n                                                                                                },\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                                                        size: 24\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                        lineNumber: 1816,\n                                                                                                        columnNumber: 39\n                                                                                                    }, undefined),\n                                                                                                    \"上传\"\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                lineNumber: 1790,\n                                                                                                columnNumber: 37\n                                                                                            }, undefined),\n                                                                                            courseDetail.taskConfig.referenceResources.map((resource, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"reference-resource-item\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                            children: resource.name\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                            lineNumber: 1821,\n                                                                                                            columnNumber: 41\n                                                                                                        }, undefined),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                            type: \"button\",\n                                                                                                            className: \"remove-resource-btn\",\n                                                                                                            onClick: ()=>{\n                                                                                                                const newResources = courseDetail.taskConfig.referenceResources.filter((_, i)=>i !== index);\n                                                                                                                setCourseDetail((prev)=>({\n                                                                                                                        ...prev,\n                                                                                                                        taskConfig: {\n                                                                                                                            ...prev.taskConfig,\n                                                                                                                            referenceResources: newResources\n                                                                                                                        }\n                                                                                                                    }));\n                                                                                                            },\n                                                                                                            children: \"\\xd7\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                            lineNumber: 1822,\n                                                                                                            columnNumber: 41\n                                                                                                        }, undefined)\n                                                                                                    ]\n                                                                                                }, index, true, {\n                                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                    lineNumber: 1820,\n                                                                                                    columnNumber: 39\n                                                                                                }, undefined))\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1789,\n                                                                                        columnNumber: 35\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1788,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1786,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1688,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1612,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1611,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 1362,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                            lineNumber: 1272,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                    lineNumber: 1218,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"course-list-footer\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"course-list-footer-left\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handlePublish,\n                                className: \"course-list-btn course-list-btn-publish\",\n                                disabled: courseList.length === 0 || isPublishingSeries || seriesStatus === 1,\n                                title: seriesStatus === 1 ? \"系列课程已发布\" : courseList.length === 0 ? \"发布失败：课程系列中至少需要包含一个课程\" : isPublishingSeries ? \"正在发布系列课程...\" : \"发布系列课程\",\n                                children: seriesStatus === 1 ? \"已发布\" : isPublishingSeries ? \"正在发布...\" : \"发布系列课程\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                lineNumber: 1855,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                            lineNumber: 1854,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"course-list-footer-right\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleExitEdit,\n                                    className: \"course-list-btn course-list-btn-exit\",\n                                    children: \"退出编辑模式\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 1878,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handlePublishCourse,\n                                    className: \"course-list-btn course-list-btn-publish-course\",\n                                    disabled: !selectedCourseId || ((_courseList_find = courseList.find((c)=>c.id === selectedCourseId)) === null || _courseList_find === void 0 ? void 0 : _courseList_find.status) === 1 || isPublishing,\n                                    title: !selectedCourseId ? \"请先选择要发布的课程\" : ((_courseList_find1 = courseList.find((c)=>c.id === selectedCourseId)) === null || _courseList_find1 === void 0 ? void 0 : _courseList_find1.status) === 1 ? \"该课程已发布\" : isPublishing ? \"正在发布课程...\" : \"发布选中的课程\",\n                                    children: isPublishing ? \"正在发布...\" : \"发布课程\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 1881,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleSave,\n                                    className: \"course-list-btn course-list-btn-save\",\n                                    disabled: uploadingFiles.size > 0 || isCreating || courseList.length === 0,\n                                    title: courseList.length === 0 ? \"请先添加课程内容\" : uploadingFiles.size > 0 ? \"有文件正在上传中，请等待上传完成\" : isCreating ? courseList.some((c)=>c.id > 1000000) ? \"正在创建课程...\" : \"正在保存课程...\" : courseList.some((c)=>c.id > 1000000) ? \"创建课程\" : \"保存课程\",\n                                    children: uploadingFiles.size > 0 ? \"上传中...\" : isCreating ? courseList.some((c)=>c.id > 1000000) ? \"正在创建...\" : \"正在保存...\" : courseList.some((c)=>c.id > 1000000) ? \"创建课程\" : \"保存课程\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 1897,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                            lineNumber: 1877,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                    lineNumber: 1853,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n            lineNumber: 1195,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n        lineNumber: 1194,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CourseListEditModal, \"xaSWfMPbK+yJLLLHUT0WW6ySol8=\", false, function() {\n    return [\n        _ToastManager__WEBPACK_IMPORTED_MODULE_4__.useToast\n    ];\n});\n_c = CourseListEditModal;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CourseListEditModal);\nvar _c;\n$RefreshReg$(_c, \"CourseListEditModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/workbench/components/CourseListEditModal.tsx\n"));

/***/ })

});