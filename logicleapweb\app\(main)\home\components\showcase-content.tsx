import React, { MutableRefObject, useState } from 'react';
import Image from 'next/image';
import { Heart, Eye, X, Info } from 'lucide-react';
import { Spin, Tag } from 'antd';
import { ShowType, GalleryImage, WorkItem } from '../types';
import ossImageProcessor from '@/lib/utils/OssImageProcessor';
import { GetNotification } from 'logic-common/dist/components/Notification';
import { useRouter, usePathname } from 'next/navigation';

// 这些类型应该从主页面中导入，这里为了组件化暂时定义
interface TagItem {
  id: number;
  label: string;
  icon: React.ReactNode;
  hidden?: boolean;
}

interface ShowcaseContentProps {
  showType: ShowType;
  filterTitle: string;
  activeTag: string;
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  handleSearch: () => void;
  isSearching: boolean;
  imageTags: TagItem[];
  workTags: TagItem[];
  handleTagClick: (label: string) => void;
  galleryImages: GalleryImage[];
  worksList: WorkItem[];
  handleImageClick: (image: GalleryImage) => Promise<void> | void;
  handleWorkClick: (work: WorkItem) => void;
  likedWorks: Record<number, boolean>;
  setLikedWorks: (fn: (prev: Record<number, boolean>) => Record<number, boolean>) => void;
  setShowLoginDialog: (show: boolean) => void;
  userState: { isLoggedIn: boolean };
  worksApi: any;
  setReportTarget: (target: { id: number, type: number } | null) => void;
  setShowReportDialog: (show: boolean) => void;
  DEFAULT_AVATAR: string;
  isLoading: boolean;
  hasMore: boolean;
  searchLoadingRef: MutableRefObject<HTMLDivElement | null>;
  normalLoadingRef: MutableRefObject<HTMLDivElement | null>;
}

const ShowcaseContent: React.FC<ShowcaseContentProps> = ({
  showType,
  filterTitle,
  activeTag,
  searchQuery,
  setSearchQuery,
  handleSearch,
  isSearching,
  imageTags,
  workTags,
  handleTagClick,
  galleryImages,
  worksList,
  handleImageClick,
  handleWorkClick,
  likedWorks,
  setLikedWorks,
  setShowLoginDialog,
  userState,
  worksApi,
  setReportTarget,
  setShowReportDialog,
  DEFAULT_AVATAR,
  isLoading,
  hasMore,
  searchLoadingRef,
  normalLoadingRef
}) => {
  const router = useRouter();
  const pathname = usePathname();
  // 添加图片加载状态管理
  const [loadingImages, setLoadingImages] = useState<Record<string, boolean>>({});

  // 标记图片加载完成
  const handleImageLoad = (id: number) => {
    setLoadingImages(prev => ({
      ...prev,
      [id.toString()]: false
    }));
  };

  // 标记图片开始加载
  const handleImageLoadStart = (id: number) => {
    setLoadingImages(prev => ({
      ...prev,
      [id.toString()]: true
    }));
  };

  // 获取图片网格缩略图
  const getGridThumbnail = (url: string) => {
    if (!url || url.includes('/images/')) return url;

    // 使用高清填充缩略图方法，确保图片填满容器并且高质量
    try {
      // 使用500x500的尺寸，确保在高分屏上依然清晰
      return ossImageProcessor.getHDFillThumbnail(url, 500, 500, 'webp');
    } catch (error) {
      console.error('获取网格缩略图失败:', error);
      return url;
    }
  }

  // 获取作品封面缩略图
  const getWorkCoverThumbnail = (url: string) => {
    if (!url || url.includes('/images/')) return url;

    // 使用高清填充缩略图方法，确保16:9比例填满容器
    try {
      // 使用800x450的尺寸，确保16:9比例在高分屏上依然清晰
      return ossImageProcessor.getHDFillThumbnail(url, 800, 450, 'webp');
    } catch (error) {
      console.error('获取作品封面缩略图失败:', error);
      return url;
    }
  }

  // 获取用户头像缩略图
  const getAvatarThumbnail = (url: string) => {
    if (!url || url.includes('/images/')) return url;

    return ossImageProcessor.getAvatarThumbnail(url, 64, 85);
  }

  return (
    <div className="showcase-content">
      {/* 标题和筛选部分 */}
      <div className="showcase-header">
        <div className="showcase-title flex items-center justify-between">
          <h2>{filterTitle}</h2>

          {/* 搜索框居中 */}
          <div className="flex-1 max-w-xl mx-auto">
            <div className="relative flex items-center">
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    handleSearch();
                  }
                }}
                placeholder={`搜索你感兴趣的${showType === 'scratch-image' ? '图片' : '作品'}或创作者...`}
                className="w-full h-10 pl-10 pr-20 rounded-full bg-white/80 
                  border border-gray-200 focus:border-[#4766C2] focus:ring-1 
                  focus:ring-[#4766C2] outline-none transition-colors"
              />
              <div className="absolute left-3 flex items-center pointer-events-none">
                <svg className="w-5 h-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
                </svg>
              </div>

              {/* <div className="absolute inset-y-0 right-0 flex items-center pr-1">
                {searchQuery && (
                  <button
                    onClick={() => setSearchQuery('')}
                    className="p-1.5 text-gray-400 hover:text-gray-600"
                  >
                    <X size={16} />
                  </button>
                )}
                <button
                  onClick={handleSearch}
                  disabled={isSearching}
                  className="h-8 px-4 bg-[#4766C2] hover:bg-[#3d57a7] text-white rounded-full 
                    transition-colors duration-200 text-sm font-medium
                    border-2 border-[#4766C2]
                    disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  搜索
                </button>
              </div> */}
            </div>
          </div>
        </div>

        <div className="gallery-tags">
          {(showType === 'scratch-image' ? imageTags : workTags)
            .filter(tag => !tag.hidden || tag.label === activeTag)
            .map(tag => (
              <span
                key={tag.id}
                className={`gallery-tag ${activeTag === tag.label ? 'active' : ''}`}
                onClick={() => handleTagClick(tag.label)}
              >
                <span className="tag-icon">{tag.icon}</span>
                {tag.label}
              </span>
            ))
          }
        </div>
      </div>

      {/* 内容展示区域 */}
      <div className="content-container">
        {showType === 'scratch-image' ? (
          <>
            <div className="gallery-grid" style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',
              gap: '1rem',
              gridAutoFlow: 'row dense',
            }}>
              {galleryImages.map((item, index) => {
                // 获取该图片的缩略图URL

                const thumbnailUrl = getGridThumbnail(item.imageUrl || '/images/image-placeholder.jpg');

                return (
                  <div
                    key={`gallery-${item.id}-${index}`}
                    className="gallery-item relative aspect-square rounded-2xl overflow-hidden group cursor-pointer"
                    onClick={() => handleImageClick(item)}
                  >
                    {/* 加载状态显示 */}
                    {/* {loadingImages[item.id.toString()] !== false && (
                      <div className="absolute inset-0 flex items-center justify-center bg-gray-100 rounded-2xl">
                        <div className="w-8 h-8 border-2 border-gray-200 border-t-blue-500 rounded-full animate-spin"></div>
                      </div>
                    )} */}

                    <Image
                      src={thumbnailUrl}
                      alt={item.title || '创作图片展示'}
                      fill
                      loading="eager"
                      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                      className={`object-cover transition-transform duration-300 group-hover:scale-110 ${loadingImages[item.id.toString()] === false ? 'opacity-100' : 'opacity-0'
                        }`}
                      onLoad={() => handleImageLoad(item.id)}
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = '/images/image-placeholder.jpg';
                        handleImageLoad(item.id);
                      }}
                      priority={index < 6} // 优先加载前6张图片
                    />
                    {/* 右上角固定按钮 */}
                    <div className="absolute top-3 right-3 flex items-center gap-2 z-10">
                      <button
                        onClick={async (e) => {
                          e.stopPropagation();

                          if (!userState.isLoggedIn) {
                            const redirectUrl = pathname;
                            router.push(`/login?redirect=${encodeURIComponent(redirectUrl)}`);
                            return;
                          }
                          
                          const notification = GetNotification();

                          // 获取当前点赞状态
                          const isCurrentlyLiked = likedWorks[item.id];

                          // 更新点赞状态
                          setLikedWorks(prev => ({
                            ...prev,
                            [item.id]: !isCurrentlyLiked
                          }));

                          // 更新显示的点赞数
                          const newLikeCount = (item.likeCount || 0) + (isCurrentlyLiked ? -1 : 1);
                          item.likeCount = newLikeCount;

                          try {
                            // 在后台发送请求
                            const response = await worksApi.toggleLike(item.id, 2);
                            if (response?.data?.code === 200) {
                              notification.success(isCurrentlyLiked ? '取消点赞成功' : '点赞成功');
                            }
                          } catch (error) {
                            // 如果失败，恢复状态
                            setLikedWorks(prev => ({
                              ...prev,
                              [item.id]: isCurrentlyLiked
                            }));
                            item.likeCount = newLikeCount - (isCurrentlyLiked ? -1 : 1);
                            notification.error('操作失败，请重试');
                          }
                        }}
                        className={`flex items-center justify-center w-10 h-10 rounded-xl backdrop-blur-sm transition-all duration-200 ${likedWorks[item.id]
                          ? 'bg-red-500 text-white hover:bg-red-600'
                          : 'bg-black/20 text-white hover:bg-black/30'
                          } opacity-0 group-hover:opacity-100`}
                      >
                        {likedWorks[item.id] ? (
                          <Heart size={20} fill="currentColor" />
                        ) : (
                          <Heart size={20} />
                        )}
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          // 创建一个a标签来下载图片，使用原始URL而不是缩略图
                          const link = document.createElement('a');
                          link.href = item.imageUrl as string;
                          // 从URL中获取文件名,如果没有则使用默认名称
                          const fileName = item.imageUrl?.split('/').pop() || 'image.png';
                          link.download = fileName;
                          document.body.appendChild(link);
                          link.click();
                          document.body.removeChild(link);
                        }}
                        className="flex items-center justify-center w-10 h-10 bg-black/20 hover:bg-black/30 
                          rounded-xl backdrop-blur-sm transition-all duration-200 text-white opacity-0 group-hover:opacity-100"
                      >
                        <svg
                          width="20"
                          height="20"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          className="text-white"
                        >
                          <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
                          <polyline points="7 10 12 15 17 10" />
                          <line x1="12" y1="15" x2="12" y2="3" />
                        </svg>
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          if (!userState.isLoggedIn) {
                            const redirectUrl = pathname;
                            router.push(`/login?redirect=${encodeURIComponent(redirectUrl)}`);
                            return;
                          }
                          setReportTarget({ id: item.id, type: 2 });
                          setShowReportDialog(true);
                        }}
                        className="flex items-center justify-center w-10 h-10 bg-black/20 hover:bg-black/30 
                          rounded-xl backdrop-blur-sm transition-all duration-200 text-white opacity-0 group-hover:opacity-100"
                      >
                        <svg
                          width="20"
                          height="20"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                        >
                          <path d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                        </svg>
                      </button>
                    </div>

                    {/* 底部信息遮罩 */}
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <div className="absolute bottom-0 left-0 right-0 p-4">
                        <h3 className="text-lg font-semibold truncate text-white">{item.title}</h3>
                        <p className="text-sm opacity-80 truncate text-white/90">{item.description}</p>

                        <div data-info="底部统计信息" className="flex items-center gap-4 mt-2 text-white/90">
                          <span className="flex items-center gap-1">
                            <Eye size={16} className="text-white/70" />
                            {item.views || item.viewCount || 0}
                          </span>
                          <span className="flex items-center gap-1">
                            <Heart size={16} className="text-white/70" />
                            {item.likeCount || 0}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>

            {/* 加载更多区域 */}
            <div
              ref={activeTag === '搜索结果' ? searchLoadingRef : normalLoadingRef}
              className="py-8 text-center"
            >
              {isLoading ? (
                <Spin size="small" />
              ) : hasMore ? (
                <div className="text-gray-500">向下滚动加载更多</div>
              ) : (
                <div className="text-gray-500">没有更多数据了</div>
              )}
            </div>
          </>
        ) : showType === 'scratch-work' ? (
          <>
            <div className="works-grid">
              {worksList.map((work, index) => {
                // 获取作品封面缩略图
                const coverImage = work.coverImage || work.screenShotImage || '/images/work-placeholder.jpg';
                const thumbnailUrl = getWorkCoverThumbnail(coverImage);

                // 获取作者头像缩略图
                const avatarUrl = work.author?.avatarUrl || DEFAULT_AVATAR;
                const avatarThumbnailUrl = getAvatarThumbnail(avatarUrl);

                return (
                  <div
                    key={`work-${work.id}-${index}`}
                    className="relative bg-white rounded-2xl overflow-hidden group cursor-pointer shadow-sm hover:shadow-xl transition-all duration-300 flex flex-col"
                    onClick={() => handleWorkClick(work)}
                  >
                    {/* 作品封面 */}
                    <div className="relative aspect-video w-full overflow-hidden">
                      {/* 加载状态显示 */}
                      {loadingImages[work.id.toString()] !== false && (
                        <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
                          <div className="w-8 h-8 border-2 border-gray-200 border-t-blue-500 rounded-full animate-spin"></div>
                        </div>
                      )}

                      <Image
                        src={thumbnailUrl}
                        alt={`${work.title || '未命名作品'}的作品展示`}
                        fill
                        loading="eager"
                        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                        className={`object-cover transition-transform duration-300 group-hover:scale-105 ${loadingImages[work.id.toString()] === false ? 'opacity-100' : 'opacity-0'
                          }`}
                        onLoad={() => handleImageLoad(work.id)}
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.src = work.screenShotImage || '/images/work-placeholder.jpg';
                          handleImageLoad(work.id);
                        }}
                        priority={index < 3} // 优先加载前三个作品
                      />
                      {/* 右上角操作按钮 */}
                      <div className="absolute top-3 right-3 flex items-center gap-2 z-10">
                        <button
                          onClick={async (e) => {
                            e.stopPropagation();

                            if (!userState.isLoggedIn) {
                              const redirectUrl = pathname;
                              router.push(`/login?redirect=${encodeURIComponent(redirectUrl)}`);
                              return;
                            }
                            
                            const notification = GetNotification();

                            const isCurrentlyLiked = likedWorks[work.id];
                            setLikedWorks(prev => ({
                              ...prev,
                              [work.id]: !isCurrentlyLiked
                            }));

                            const newLikeCount = (work.likeCount || 0) + (isCurrentlyLiked ? -1 : 1);
                            work.likeCount = newLikeCount;

                            try {
                              const response = await worksApi.toggleLike(work.id, 1);
                              if (response?.data?.code === 200) {
                                notification.success(isCurrentlyLiked ? '取消点赞成功' : '点赞成功');
                              }
                            } catch (error) {
                              setLikedWorks(prev => ({
                                ...prev,
                                [work.id]: isCurrentlyLiked
                              }));
                              work.likeCount = newLikeCount - (isCurrentlyLiked ? -1 : 1);
                              notification.error('操作失败，请重试');
                            }
                          }}
                          className={`flex items-center justify-center w-10 h-10 rounded-xl backdrop-blur-sm transition-all duration-200 ${likedWorks[work.id]
                            ? 'bg-red-500 text-white hover:bg-red-600'
                            : 'bg-black/20 text-white hover:bg-black/30'
                            } opacity-0 group-hover:opacity-100`}
                        >
                          {likedWorks[work.id] ? (
                            <Heart size={20} fill="currentColor" />
                          ) : (
                            <Heart size={20} />
                          )}
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            if (!userState.isLoggedIn) {
                              const redirectUrl = pathname;
                              router.push(`/login?redirect=${encodeURIComponent(redirectUrl)}`);
                              return;
                            }
                            setReportTarget({ id: work.id, type: 1 });
                            setShowReportDialog(true);
                          }}
                          className="flex items-center justify-center w-10 h-10 bg-black/20 hover:bg-black/30 
                            rounded-xl backdrop-blur-sm transition-all duration-200 text-white opacity-0 group-hover:opacity-100"
                        >
                          <svg
                            width="20"
                            height="20"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                          >
                            <path d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                          </svg>
                        </button>
                      </div>
                    </div>

                    {/* 作品信息 */}
                    <div className="flex flex-col flex-1 p-4">
                      <div className="flex items-start justify-between gap-4 mb-3">
                        <div className="flex-1 min-w-0">
                          <h3 className="text-lg font-semibold text-gray-900 truncate">
                            {work.title || '未命名作品'}
                          </h3>
                          <p className="text-sm text-gray-500 line-clamp-2 mt-1 min-h-[2.5rem]">
                            {work.description || '暂无描述'}
                          </p>
                        </div>
                        {work.status > 0 && (
                          <Tag
                            color={work.status === 1 ? 'success' : 'processing'}
                            className="flex-shrink-0"
                          >
                            {work.status === 1 ? '可编辑' : '不可编辑'}
                          </Tag>
                        )}
                      </div>

                      {/* 底部信息 */}
                      <div className="flex items-center justify-between pt-3 border-t border-gray-100 mt-auto">
                        <div className="flex items-center gap-4 text-gray-500 text-sm">
                          <span className="flex items-center gap-1">
                            <Eye size={16} />
                            {work.viewCount || 0}
                          </span>
                          <span className="flex items-center gap-1">
                            <Heart size={16} className={likedWorks[work.id] ? 'text-red-500' : ''} />
                            {work.likeCount || 0}
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="w-6 h-6 rounded-full overflow-hidden relative bg-gray-100">
                            <Image
                              src={avatarThumbnailUrl}
                              alt={work.author?.nickName || '用户头像'}
                              fill
                              loading="eager"
                              className="object-cover"
                              sizes="24px"
                              onError={(e) => {
                                const target = e.target as HTMLImageElement;
                                target.src = DEFAULT_AVATAR;
                              }}
                            />
                          </div>
                          <span className="text-sm text-gray-600">
                            {work.author?.nickName || '未知用户'}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>

            {/* 加载更多区域 */}
            <div
              ref={activeTag === '搜索结果' ? searchLoadingRef : normalLoadingRef}
              className="py-8 text-center"
            >
              {isLoading ? (
                <Spin size="small" />
              ) : hasMore ? (
                <div className="text-gray-500">向下滚动加载更多</div>
              ) : (
                <div className="text-gray-500">没有更多数据了</div>
              )}
            </div>
          </>
        ) : null}
      </div>
    </div>
  );
};

export default ShowcaseContent; 