"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/workbench/page",{

/***/ "(app-pages-browser)/./app/workbench/components/LeftSidebar.tsx":
/*!**************************************************!*\
  !*** ./app/workbench/components/LeftSidebar.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftCircle,Book,Briefcase,ChevronDown,LayoutDashboard,ListTodo,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftCircle,Book,Briefcase,ChevronDown,LayoutDashboard,ListTodo,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftCircle,Book,Briefcase,ChevronDown,LayoutDashboard,ListTodo,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/list-todo.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftCircle,Book,Briefcase,ChevronDown,LayoutDashboard,ListTodo,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftCircle,Book,Briefcase,ChevronDown,LayoutDashboard,ListTodo,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftCircle,Book,Briefcase,ChevronDown,LayoutDashboard,ListTodo,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftCircle,Book,Briefcase,ChevronDown,LayoutDashboard,ListTodo,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftCircle,Book,Briefcase,ChevronDown,LayoutDashboard,ListTodo,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeftCircle,Book,Briefcase,ChevronDown,LayoutDashboard,ListTodo,Settings,Shield,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-arrow-left.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _lib_api_school__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api/school */ \"(app-pages-browser)/./lib/api/school.ts\");\n/* harmony import */ var _lib_api_class__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api/class */ \"(app-pages-browser)/./lib/api/class.ts\");\n/* harmony import */ var _lib_utils_address__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils/address */ \"(app-pages-browser)/./lib/utils/address.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst LeftSidebar = (param)=>{\n    let { userInfo, onMenuItemClick, onSchoolSelect, onClassesUpdate } = param;\n    _s();\n    const [activeItem, setActiveItem] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"快速开始\");\n    const [isClassDropdownOpen, setIsClassDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [schools, setSchools] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedSchool, setSelectedSchool] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [schoolsLoading, setSchoolsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [schoolsError, setSchoolsError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 监听activeItem变化，当进入班级管理页面时自动打开下拉菜单\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"activeItem 状态变化:\", activeItem);\n        // 当切换到班级管理页面时，自动打开下拉菜单\n        if (activeItem === \"班级管理\") {\n            setIsClassDropdownOpen(true);\n        }\n    }, [\n        activeItem\n    ]);\n    // 下拉菜单的ref，用于检测点击外部区域\n    const dropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 标志位，防止导航点击和外部点击冲突\n    const isNavigatingRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // 点击外部区域关闭下拉菜单并切换到班级管理页面\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleClickOutside = (event)=>{\n            if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n                console.log(\"点击外部区域，isNavigating:\", isNavigatingRef.current);\n                // 如果正在导航，不处理外部点击\n                if (isNavigatingRef.current) {\n                    isNavigatingRef.current = false;\n                    return;\n                }\n                // 如果当前活跃项是班级管理，不关闭下拉菜单\n                if (activeItem === \"班级管理\") {\n                    return;\n                }\n                // 关闭下拉菜单\n                setIsClassDropdownOpen(false);\n                // 如果有选中的学校，切换到班级管理页面\n                if (selectedSchool) {\n                    setActiveItem(\"班级管理\");\n                    onMenuItemClick === null || onMenuItemClick === void 0 ? void 0 : onMenuItemClick(\"班级管理\");\n                }\n            }\n        };\n        if (isClassDropdownOpen) {\n            document.addEventListener(\"mousedown\", handleClickOutside);\n        }\n        return ()=>{\n            document.removeEventListener(\"mousedown\", handleClickOutside);\n        };\n    }, [\n        isClassDropdownOpen,\n        selectedSchool,\n        onMenuItemClick,\n        activeItem\n    ]);\n    // 监听自定义事件来关闭下拉菜单\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleCloseDropdown = ()=>{\n            setIsClassDropdownOpen(false);\n        };\n        document.addEventListener(\"closeDropdown\", handleCloseDropdown);\n        return ()=>{\n            document.removeEventListener(\"closeDropdown\", handleCloseDropdown);\n        };\n    }, []);\n    // 获取教师管理的学校列表\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchSchools();\n    }, []);\n    const navItems = [\n        {\n            icon: _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            name: \"快速开始\",\n            hasDivider: true\n        },\n        {\n            icon: _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            name: \"班级管理\",\n            hasDropdown: true\n        },\n        {\n            icon: _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            name: \"班级任务\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            name: \"班级项目\",\n            hasDivider: true\n        },\n        {\n            icon: _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            name: \"课程管理\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            name: \"模板管理\"\n        }\n    ];\n    // 处理班级管理点击\n    const handleClassManagementClick = ()=>{\n        // 设置为活跃状态\n        setActiveItem(\"班级管理\");\n        // 如果没有选中学校且有可用学校，自动选择第一个学校\n        if (!selectedSchool && schools.length > 0) {\n            const firstSchool = schools[0];\n            setSelectedSchool(firstSchool);\n            onSchoolSelect === null || onSchoolSelect === void 0 ? void 0 : onSchoolSelect(firstSchool);\n            fetchClasses(firstSchool.id);\n            console.log(\"班级管理：自动选择第一个学校:\", firstSchool);\n        } else if (!selectedSchool && schools.length === 0 && !schoolsLoading) {\n            // 如果没有学校数据且不在加载中，重新获取学校列表\n            console.log(\"班级管理：没有学校数据，重新获取学校列表\");\n            fetchSchools();\n        }\n        // 如果当前已经是班级管理页面且下拉菜单已打开，则关闭；否则打开\n        if (activeItem === \"班级管理\" && isClassDropdownOpen) {\n            setIsClassDropdownOpen(false);\n        } else {\n            setIsClassDropdownOpen(true);\n        }\n        // 通知父组件\n        onMenuItemClick === null || onMenuItemClick === void 0 ? void 0 : onMenuItemClick(\"班级管理\");\n    };\n    // 处理学校选择\n    const handleSchoolSelect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((school)=>{\n        console.log(\"handleSchoolSelect 被调用，当前activeItem:\", activeItem);\n        // 不关闭下拉菜单，只更新选中状态\n        setSelectedSchool(school);\n        // 强制切换到班级管理页面（无论当前在什么页面）\n        setActiveItem(\"班级管理\");\n        onMenuItemClick === null || onMenuItemClick === void 0 ? void 0 : onMenuItemClick(\"班级管理\");\n        // 始终通知父组件学校选择变化（用于数据更新）\n        onSchoolSelect === null || onSchoolSelect === void 0 ? void 0 : onSchoolSelect(school);\n        // 获取该学校的班级列表\n        fetchClasses(school.id);\n    }, [\n        onMenuItemClick,\n        onSchoolSelect\n    ]);\n    // 处理返回主页\n    const handleBackToHome = ()=>{\n        console.log(\"点击返回主页按钮\");\n        // 获取当前域名和端口，然后跳转到home页面\n        const currentOrigin = window.location.origin;\n        const homeUrl = \"\".concat(currentOrigin, \"/home\");\n        console.log(\"当前域名:\", currentOrigin);\n        console.log(\"跳转到:\", homeUrl);\n        // 直接跳转到home页面\n        window.location.href = homeUrl;\n    };\n    // 获取学校列表\n    const fetchSchools = async ()=>{\n        setSchoolsLoading(true);\n        setSchoolsError(null);\n        try {\n            var _response_data, _response_data1, _response_data2, _response_data3;\n            const response = await (0,_lib_api_school__WEBPACK_IMPORTED_MODULE_3__.getTeacherSchools)();\n            console.log(\"获取学校列表API响应:\", response);\n            // 检查多种可能的响应格式\n            let schoolList = [];\n            if (((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.code) === 200 && ((_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.data)) {\n                schoolList = response.data.data;\n            } else if (((_response_data2 = response.data) === null || _response_data2 === void 0 ? void 0 : _response_data2.status) === 200 && ((_response_data3 = response.data) === null || _response_data3 === void 0 ? void 0 : _response_data3.data)) {\n                schoolList = response.data.data;\n            } else if (Array.isArray(response.data)) {\n                schoolList = response.data;\n            }\n            if (schoolList.length > 0) {\n                setSchools(schoolList);\n                const firstSchool = schoolList[0];\n                setSelectedSchool(firstSchool);\n                // 通知父组件学校选择变化\n                onSchoolSelect === null || onSchoolSelect === void 0 ? void 0 : onSchoolSelect(firstSchool);\n                // 获取第一个学校的班级列表\n                fetchClasses(firstSchool.id);\n                console.log(\"成功获取学校列表，数量:\", schoolList.length);\n                console.log(\"自动选择第一个学校:\", firstSchool);\n            } else {\n                setSchoolsError(\"暂无数据\");\n            }\n        } catch (error) {\n            console.error(\"获取学校列表失败:\", error);\n            setSchoolsError(\"请检查网络连接失败\");\n        } finally{\n            setSchoolsLoading(false);\n        }\n    };\n    // 获取指定学校的班级列表\n    const fetchClasses = async (schoolId)=>{\n        if (!(userInfo === null || userInfo === void 0 ? void 0 : userInfo.id)) {\n            console.log(\"用户未登录，无法获取班级列表\");\n            return;\n        }\n        // 通知父组件开始加载\n        onClassesUpdate && onClassesUpdate([], true, null);\n        try {\n            var _response_data;\n            console.log(\"获取班级列表:\", {\n                schoolId,\n                teacherId: userInfo.id\n            });\n            const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_4__.classApi.getTeacherClasses(schoolId, userInfo.id);\n            console.log(\"班级列表API响应:\", response);\n            if (((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.code) === 200) {\n                const classList = response.data.data || [];\n                // 通知父组件数据更新\n                onClassesUpdate && onClassesUpdate(classList, false, null);\n                console.log(\"成功获取班级列表，数量:\", classList.length);\n            } else {\n                const errorMsg = \"获取班级列表失败\";\n                // 通知父组件错误状态\n                onClassesUpdate && onClassesUpdate([], false, errorMsg);\n            }\n        } catch (error) {\n            console.error(\"获取班级列表失败:\", error);\n            const errorMsg = \"请检查网络连接\";\n            // 通知父组件错误状态\n            onClassesUpdate && onClassesUpdate([], false, errorMsg);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: \"left-sidebar\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"sidebar-header\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                        lineNumber: 275,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-lg font-bold\",\n                        children: \"教师空间\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                        lineNumber: 276,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                lineNumber: 274,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"teacher-info\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        src: userInfo.avatarUrl || \"/images/xiaoluo-default.webp\",\n                        alt: userInfo.nickName || \"小洛头像\",\n                        width: 40,\n                        height: 40,\n                        className: \"avatar\",\n                        style: {\n                            backgroundColor: \"white\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                        lineNumber: 280,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"teacher-details\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"teacher-name\",\n                                children: userInfo.nickName || \"未登录\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"teacher-title\",\n                                children: \"教师\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                lineNumber: 290,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                lineNumber: 279,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"sidebar-nav\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                    children: navItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), {\n                            children: [\n                                item.hasDropdown ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"nav-item-dropdown\",\n                                    ref: dropdownRef,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"nav-item \".concat(activeItem === item.name ? \"active\" : \"\", \" \").concat(isClassDropdownOpen ? \"dropdown-open\" : \"\"),\n                                            onClick: (e)=>{\n                                                e.preventDefault();\n                                                e.stopPropagation();\n                                                handleClassManagementClick();\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                    className: \"nav-icon\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                    lineNumber: 307,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: item.name\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                    lineNumber: 308,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"dropdown-arrow \".concat(isClassDropdownOpen ? \"rotated\" : \"\"),\n                                                    size: 16,\n                                                    style: {\n                                                        transform: isClassDropdownOpen ? \"rotate(180deg)\" : \"rotate(0deg)\",\n                                                        transition: \"transform 0.3s ease\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                    lineNumber: 309,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        isClassDropdownOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"dropdown-menu \".concat(schoolsLoading || schoolsError || schools.length === 0 ? \"empty\" : \"\"),\n                                            onClick: (e)=>{\n                                                e.preventDefault();\n                                                e.stopPropagation();\n                                            },\n                                            children: schoolsLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"dropdown-item disabled loading\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"loading-spinner\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                        lineNumber: 328,\n                                                        columnNumber: 27\n                                                    }, undefined),\n                                                    \"正在加载学校信息...\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                lineNumber: 327,\n                                                columnNumber: 25\n                                            }, undefined) : schoolsError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"dropdown-item disabled error\",\n                                                children: schoolsError\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                lineNumber: 332,\n                                                columnNumber: 25\n                                            }, undefined) : schools.length > 0 ? schools.map((school)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"dropdown-item \".concat((selectedSchool === null || selectedSchool === void 0 ? void 0 : selectedSchool.id) === school.id ? \"selected\" : \"\"),\n                                                    onClick: (e)=>{\n                                                        e.preventDefault();\n                                                        e.stopPropagation();\n                                                        handleSchoolSelect(school);\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"school-info\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"school-name\",\n                                                                children: school.schoolName\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                                lineNumber: 347,\n                                                                columnNumber: 31\n                                                            }, undefined),\n                                                            school.province && school.district && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"school-location\",\n                                                                children: (0,_lib_utils_address__WEBPACK_IMPORTED_MODULE_5__.formatSchoolAddress)(school.province, school.city, school.district)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                                lineNumber: 349,\n                                                                columnNumber: 33\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 29\n                                                    }, undefined)\n                                                }, school.id, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                    lineNumber: 337,\n                                                    columnNumber: 27\n                                                }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"dropdown-item disabled no-data\",\n                                                children: \"暂无数据\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                                lineNumber: 357,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                            lineNumber: 319,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                    lineNumber: 298,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"nav-item \".concat(activeItem === item.name ? \"active\" : \"\"),\n                                    onClick: (e)=>{\n                                        e.preventDefault();\n                                        e.stopPropagation();\n                                        console.log(\"点击导航项:\", item.name);\n                                        console.log(\"当前下拉菜单状态:\", isClassDropdownOpen);\n                                        console.log(\"当前活跃项:\", activeItem);\n                                        // 设置导航标志，防止外部点击干扰\n                                        isNavigatingRef.current = true;\n                                        // 先关闭下拉菜单\n                                        setIsClassDropdownOpen(false);\n                                        // 然后更新活跃项\n                                        setActiveItem(item.name);\n                                        // 最后通知父组件\n                                        onMenuItemClick === null || onMenuItemClick === void 0 ? void 0 : onMenuItemClick(item.name);\n                                        console.log(\"完成设置 - 活跃项:\", item.name, \"下拉菜单已关闭\");\n                                        // 延迟重置标志位\n                                        setTimeout(()=>{\n                                            isNavigatingRef.current = false;\n                                        }, 100);\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                            className: \"nav-icon\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                            lineNumber: 394,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: item.name\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                            lineNumber: 395,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                    lineNumber: 365,\n                                    columnNumber: 17\n                                }, undefined),\n                                item.hasDivider && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"nav-divider\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                                    lineNumber: 398,\n                                    columnNumber: 35\n                                }, undefined)\n                            ]\n                        }, item.name, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                            lineNumber: 296,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                    lineNumber: 294,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                lineNumber: 293,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"sidebar-footer\",\n                onClick: handleBackToHome,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeftCircle_Book_Briefcase_ChevronDown_LayoutDashboard_ListTodo_Settings_Shield_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        className: \"nav-icon\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                        lineNumber: 404,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"返回主页\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                        lineNumber: 405,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n                lineNumber: 403,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\LeftSidebar.tsx\",\n        lineNumber: 273,\n        columnNumber: 5\n    }, undefined);\n};\n_s(LeftSidebar, \"sRB/Ml9AeQvFB94OE2ocIHTANvQ=\");\n_c = LeftSidebar;\n/* harmony default export */ __webpack_exports__[\"default\"] = (LeftSidebar);\nvar _c;\n$RefreshReg$(_c, \"LeftSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/workbench/components/LeftSidebar.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/workbench/components/MainContent.tsx":
/*!**************************************************!*\
  !*** ./app/workbench/components/MainContent.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _QuickActions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./QuickActions */ \"(app-pages-browser)/./app/workbench/components/QuickActions.tsx\");\n/* harmony import */ var _OngoingTasks__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./OngoingTasks */ \"(app-pages-browser)/./app/workbench/components/OngoingTasks.tsx\");\n/* harmony import */ var _TemplateManagement__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./TemplateManagement */ \"(app-pages-browser)/./app/workbench/components/TemplateManagement.tsx\");\n/* harmony import */ var _ClassManagement__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ClassManagement */ \"(app-pages-browser)/./app/workbench/components/ClassManagement.tsx\");\n/* harmony import */ var _ClassDetail__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ClassDetail */ \"(app-pages-browser)/./app/workbench/components/ClassDetail.tsx\");\n/* harmony import */ var _ClassTasks__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ClassTasks */ \"(app-pages-browser)/./app/workbench/components/ClassTasks.tsx\");\n/* harmony import */ var _CourseManagement__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./CourseManagement */ \"(app-pages-browser)/./app/workbench/components/CourseManagement.tsx\");\n/* harmony import */ var _ClassProjects__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./ClassProjects */ \"(app-pages-browser)/./app/workbench/components/ClassProjects.tsx\");\n/* harmony import */ var _SchoolSelectionModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./SchoolSelectionModal */ \"(app-pages-browser)/./app/workbench/components/SchoolSelectionModal.tsx\");\n/* harmony import */ var _ClassSelectionModal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./ClassSelectionModal */ \"(app-pages-browser)/./app/workbench/components/ClassSelectionModal.tsx\");\n/* harmony import */ var _TemplateSelectionModal__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./TemplateSelectionModal */ \"(app-pages-browser)/./app/workbench/components/TemplateSelectionModal.tsx\");\n/* harmony import */ var _teacher_space_components_modals_create_class_modal__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../../teacher-space/components/modals/create-class-modal */ \"(app-pages-browser)/./app/teacher-space/components/modals/create-class-modal.tsx\");\n/* harmony import */ var _lib_api_school__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/lib/api/school */ \"(app-pages-browser)/./lib/api/school.ts\");\n/* harmony import */ var _lib_api_class__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/lib/api/class */ \"(app-pages-browser)/./lib/api/class.ts\");\n/* harmony import */ var _barrel_optimize_names_Search_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/message/index.js\");\n/* harmony import */ var _icon_park_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @icon-park/react */ \"(app-pages-browser)/./node_modules/@icon-park/react/es/icons/HandUp.js\");\n/* harmony import */ var _icon_park_react_styles_index_css__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @icon-park/react/styles/index.css */ \"(app-pages-browser)/./node_modules/@icon-park/react/styles/index.css\");\n/* harmony import */ var _SchoolSelectionModal_css__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./SchoolSelectionModal.css */ \"(app-pages-browser)/./app/workbench/components/SchoolSelectionModal.css\");\n/* harmony import */ var _contexts_TemplateContext__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ../contexts/TemplateContext */ \"(app-pages-browser)/./app/workbench/contexts/TemplateContext.tsx\");\n/* harmony import */ var _TemplateSelectionModal_css__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./TemplateSelectionModal.css */ \"(app-pages-browser)/./app/workbench/components/TemplateSelectionModal.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst MainContent = (param)=>{\n    let { activeView = \"快速开始\", selectedSchool, userInfo, classes = [], classesLoading = false, classesError = null, onCloseDropdown, onClassesUpdate, onSchoolChange } = param;\n    _s();\n    const [isSchoolModalOpen, setIsSchoolModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isClassModalOpen, setIsClassModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isTemplateModalOpen, setIsTemplateModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isCreateClassModalOpen, setIsCreateClassModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentActionType, setCurrentActionType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [modalSelectedSchool, setModalSelectedSchool] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedClass, setSelectedClass] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showClassDetail, setShowClassDetail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedClassForDetail, setSelectedClassForDetail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 使用模板上下文\n    const { currentTemplate, globalTemplateChangeVersion } = (0,_contexts_TemplateContext__WEBPACK_IMPORTED_MODULE_18__.useTemplate)();\n    // 监听全局模板变化，通知所有班级详情组件刷新\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (globalTemplateChangeVersion > 0 && currentTemplate) {\n            console.log(\"MainContent - 检测到全局模板变化，版本号:\", globalTemplateChangeVersion);\n            console.log(\"MainContent - 新的当前模板:\", currentTemplate);\n        // 这里可以添加通知所有班级组件刷新的逻辑\n        // 由于班级详情组件已经在监听globalTemplateChangeVersion，\n        // 这里主要是为了确保状态同步\n        }\n    }, [\n        globalTemplateChangeVersion,\n        currentTemplate\n    ]);\n    // 监听学校选择变化，强制跳回班级管理页面\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedSchool) {\n            console.log(\"检测到学校变化，强制跳回班级管理页面:\", selectedSchool);\n            // 重置班级详情显示状态，强制显示班级列表\n            setShowClassDetail(false);\n            setSelectedClassForDetail(null);\n            // 通知父组件学校变化\n            if (onSchoolChange) {\n                onSchoolChange(selectedSchool);\n            }\n        }\n    }, [\n        selectedSchool,\n        onSchoolChange\n    ]);\n    const handleQuickStartClick = async function() {\n        let actionType = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"发布任务\";\n        setCurrentActionType(actionType);\n        try {\n            // 获取用户的学校列表\n            const response = await _lib_api_school__WEBPACK_IMPORTED_MODULE_14__.schoolApi.getUserSchools();\n            if (response.data.code === 200) {\n                const schoolsData = response.data.data || [];\n                if (schoolsData.length === 1) {\n                    // 只有一个学校，直接选择并跳到班级选择\n                    setModalSelectedSchool(schoolsData[0]);\n                    setIsClassModalOpen(true);\n                } else if (schoolsData.length > 1) {\n                    // 多个学校，显示学校选择弹窗\n                    setIsSchoolModalOpen(true);\n                } else {\n                    // 没有学校，可以显示提示信息\n                    console.warn(\"用户没有关联的学校\");\n                }\n            }\n        } catch (error) {\n            console.error(\"获取学校列表失败:\", error);\n            // 出错时仍然显示学校选择弹窗\n            setIsSchoolModalOpen(true);\n        }\n    };\n    const handleCloseModal = ()=>{\n        setIsSchoolModalOpen(false);\n        setIsClassModalOpen(false);\n        setIsTemplateModalOpen(false);\n        setModalSelectedSchool(null);\n        setSelectedClass(null);\n        setCurrentActionType(\"\");\n    };\n    const handleSchoolSelect = (school)=>{\n        setModalSelectedSchool(school);\n        setIsSchoolModalOpen(false);\n        setIsClassModalOpen(true);\n    };\n    const handleClassSelect = (classData)=>{\n        setSelectedClass(classData);\n        setIsClassModalOpen(false);\n        setIsTemplateModalOpen(true);\n    };\n    const handleBackToSchool = ()=>{\n        setIsClassModalOpen(false);\n        setIsSchoolModalOpen(true);\n    };\n    const handleBackToClass = ()=>{\n        setIsTemplateModalOpen(false);\n        setIsClassModalOpen(true);\n    };\n    const handleClassClick = (classInfo)=>{\n        console.log(\"点击班级:\", classInfo);\n        setSelectedClassForDetail(classInfo);\n        setShowClassDetail(true);\n    };\n    const handleBackToClassManagement = ()=>{\n        setShowClassDetail(false);\n        setSelectedClassForDetail(null);\n    };\n    // 处理班级信息更新\n    const handleClassInfoUpdate = (updatedClassInfo)=>{\n        // 更新班级列表中对应的班级信息\n        const updatedClasses = classes.map((classItem)=>classItem.id === updatedClassInfo.id ? {\n                ...classItem,\n                ...updatedClassInfo\n            } : classItem);\n        // 通知父组件更新班级列表\n        onClassesUpdate === null || onClassesUpdate === void 0 ? void 0 : onClassesUpdate(updatedClasses);\n        // 同时更新当前选中的班级详情\n        setSelectedClassForDetail(updatedClassInfo);\n    };\n    // 处理班级删除\n    const handleClassDeleted = (deletedClassId)=>{\n        console.log(\"班级已删除:\", deletedClassId);\n        // 从班级列表中移除被删除的班级\n        const updatedClasses = classes.filter((cls)=>cls.id !== deletedClassId);\n        // 通知父组件更新班级列表\n        onClassesUpdate === null || onClassesUpdate === void 0 ? void 0 : onClassesUpdate(updatedClasses);\n    };\n    // 处理添加班级\n    const handleAddClass = ()=>{\n        if (!selectedSchool) {\n            _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"].error(\"请先选择学校\");\n            return;\n        }\n        setIsCreateClassModalOpen(true);\n    };\n    // 处理模板选择确认\n    const handleTemplateConfirm = (taskData)=>{\n        console.log(\"模板选择确认:\", taskData);\n        // 简单关闭弹窗，不执行具体的发布逻辑\n        handleCloseModal();\n    };\n    // 处理创建班级表单提交\n    const handleCreateClass = async (values)=>{\n        if (!selectedSchool) {\n            _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"].error(\"请先选择学校\");\n            return;\n        }\n        if (!(userInfo === null || userInfo === void 0 ? void 0 : userInfo.id)) {\n            _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"].error(\"用户信息不完整，请重新登录\");\n            return;\n        }\n        if (values.className.length > 8) {\n            _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"].error(\"班级名称不能超过8个字符\");\n            return;\n        }\n        try {\n            // 使用 createClass API，需要传递 teacherId\n            const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_15__.classApi.createClass(selectedSchool.id, values.className, userInfo.id);\n            if (response.data.code === 200) {\n                _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"].success(\"创建班级成功\");\n                setIsCreateClassModalOpen(false);\n                // 刷新班级列表\n                try {\n                    const classesResponse = await _lib_api_class__WEBPACK_IMPORTED_MODULE_15__.classApi.getTeacherClassesSimple(selectedSchool.id);\n                    if (classesResponse.data.code === 200) {\n                        onClassesUpdate === null || onClassesUpdate === void 0 ? void 0 : onClassesUpdate(classesResponse.data.data || []);\n                    }\n                } catch (error) {\n                    console.error(\"刷新班级列表失败:\", error);\n                }\n            } else {\n                _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"].error(response.data.message || \"该班级已存在或创建失败\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"创建班级失败:\", error);\n            _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"].error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"创建班级失败\");\n        }\n    };\n    // 处理创建班级弹窗关闭\n    const handleCreateClassModalClose = ()=>{\n        setIsCreateClassModalOpen(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"main-content relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"main-header\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"search-bar\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Search_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                className: \"search-icon\",\n                                size: 18\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                                lineNumber: 284,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                placeholder: \"搜索课程、任务或学生...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                                lineNumber: 285,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                        lineNumber: 283,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"start-class-btn\",\n                        onClick: ()=>handleQuickStartClick(\"快速上课\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_icon_park_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                theme: \"filled\",\n                                size: 20,\n                                fill: [\n                                    \"#ffffff\"\n                                ],\n                                className: \"start-class-icon\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                                lineNumber: 288,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"快速上课\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                        lineNumber: 287,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                lineNumber: 282,\n                columnNumber: 7\n            }, undefined),\n            activeView === \"模板管理\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TemplateManagement__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                selectedSchool: selectedSchool,\n                userInfo: userInfo\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                lineNumber: 295,\n                columnNumber: 9\n            }, undefined) : activeView === \"班级管理\" ? showClassDetail && selectedClassForDetail && selectedSchool ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ClassDetail__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                classInfo: selectedClassForDetail,\n                selectedSchool: selectedSchool,\n                onBack: handleBackToClassManagement,\n                onClassInfoUpdate: handleClassInfoUpdate,\n                onClassDeleted: handleClassDeleted\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                lineNumber: 298,\n                columnNumber: 11\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ClassManagement__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                selectedSchool: selectedSchool,\n                userInfo: userInfo,\n                classes: classes,\n                classesLoading: classesLoading,\n                classesError: classesError,\n                onClassClick: handleClassClick,\n                onCloseDropdown: onCloseDropdown,\n                onAddClass: handleAddClass\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                lineNumber: 306,\n                columnNumber: 11\n            }, undefined) : activeView === \"班级任务\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ClassTasks__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                lineNumber: 318,\n                columnNumber: 9\n            }, undefined) : activeView === \"课程管理\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CourseManagement__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                lineNumber: 320,\n                columnNumber: 9\n            }, undefined) : activeView === \"班级项目\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ClassProjects__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                lineNumber: 322,\n                columnNumber: 9\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_QuickActions__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                        lineNumber: 325,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_OngoingTasks__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                        lineNumber: 326,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SchoolSelectionModal__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                isOpen: isSchoolModalOpen,\n                onClose: handleCloseModal,\n                actionType: currentActionType,\n                onSchoolSelect: handleSchoolSelect\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                lineNumber: 330,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ClassSelectionModal__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                isOpen: isClassModalOpen,\n                onClose: handleCloseModal,\n                onBack: handleBackToSchool,\n                actionType: currentActionType,\n                selectedSchool: modalSelectedSchool,\n                onClassSelect: handleClassSelect\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                lineNumber: 337,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TemplateSelectionModal__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                isOpen: isTemplateModalOpen,\n                onClose: handleCloseModal,\n                onBack: handleBackToClass,\n                onConfirm: handleTemplateConfirm,\n                actionType: currentActionType,\n                selectedSchool: modalSelectedSchool,\n                selectedClass: selectedClass\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                lineNumber: 346,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_teacher_space_components_modals_create_class_modal__WEBPACK_IMPORTED_MODULE_13__.CreateClassModal, {\n                visible: isCreateClassModalOpen,\n                onCancel: handleCreateClassModalClose,\n                onOk: handleCreateClass\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                lineNumber: 356,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n        lineNumber: 281,\n        columnNumber: 5\n    }, undefined);\n};\n_s(MainContent, \"vuiDOKB9kf9CftAMGz7Fil9059k=\", false, function() {\n    return [\n        _contexts_TemplateContext__WEBPACK_IMPORTED_MODULE_18__.useTemplate\n    ];\n});\n_c = MainContent;\n/* harmony default export */ __webpack_exports__[\"default\"] = (MainContent);\nvar _c;\n$RefreshReg$(_c, \"MainContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC93b3JrYmVuY2gvY29tcG9uZW50cy9NYWluQ29udGVudC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRW1EO0FBQ1Q7QUFDQTtBQUNZO0FBQ047QUFDUjtBQUNGO0FBQ1k7QUFDTjtBQUNjO0FBQ0Y7QUFDTTtBQUM4QjtBQUMvQztBQUNGO0FBQ0w7QUFDUDtBQUNXO0FBQ0M7QUFDUDtBQUNzQjtBQUNwQjtBQXdDdEMsTUFBTXFCLGNBQWM7UUFBQyxFQUNuQkMsYUFBYSxNQUFNLEVBQ25CQyxjQUFjLEVBQ2RDLFFBQVEsRUFDUkMsVUFBVSxFQUFFLEVBQ1pDLGlCQUFpQixLQUFLLEVBQ3RCQyxlQUFlLElBQUksRUFDbkJDLGVBQWUsRUFDZkMsZUFBZSxFQUNmQyxjQUFjLEVBQ0c7O0lBQ2pCLE1BQU0sQ0FBQ0MsbUJBQW1CQyxxQkFBcUIsR0FBRy9CLCtDQUFRQSxDQUFDO0lBQzNELE1BQU0sQ0FBQ2dDLGtCQUFrQkMsb0JBQW9CLEdBQUdqQywrQ0FBUUEsQ0FBQztJQUN6RCxNQUFNLENBQUNrQyxxQkFBcUJDLHVCQUF1QixHQUFHbkMsK0NBQVFBLENBQUM7SUFDL0QsTUFBTSxDQUFDb0Msd0JBQXdCQywwQkFBMEIsR0FBR3JDLCtDQUFRQSxDQUFDO0lBQ3JFLE1BQU0sQ0FBQ3NDLG1CQUFtQkMscUJBQXFCLEdBQUd2QywrQ0FBUUEsQ0FBQztJQUMzRCxNQUFNLENBQUN3QyxxQkFBcUJDLHVCQUF1QixHQUFHekMsK0NBQVFBLENBQU07SUFDcEUsTUFBTSxDQUFDMEMsZUFBZUMsaUJBQWlCLEdBQUczQywrQ0FBUUEsQ0FBTTtJQUN4RCxNQUFNLENBQUM0QyxpQkFBaUJDLG1CQUFtQixHQUFHN0MsK0NBQVFBLENBQUM7SUFDdkQsTUFBTSxDQUFDOEMsd0JBQXdCQywwQkFBMEIsR0FBRy9DLCtDQUFRQSxDQUFNO0lBRTFFLFVBQVU7SUFDVixNQUFNLEVBQUVnRCxlQUFlLEVBQUVDLDJCQUEyQixFQUFFLEdBQUc5Qix1RUFBV0E7SUFFcEUsd0JBQXdCO0lBQ3hCbEIsZ0RBQVNBLENBQUM7UUFDUixJQUFJZ0QsOEJBQThCLEtBQUtELGlCQUFpQjtZQUN0REUsUUFBUUMsR0FBRyxDQUFDLGdDQUFnQ0Y7WUFDNUNDLFFBQVFDLEdBQUcsQ0FBQyx5QkFBeUJIO1FBRXJDLHNCQUFzQjtRQUN0Qiw0Q0FBNEM7UUFDNUMsZ0JBQWdCO1FBQ2xCO0lBQ0YsR0FBRztRQUFDQztRQUE2QkQ7S0FBZ0I7SUFFakQsc0JBQXNCO0lBQ3RCL0MsZ0RBQVNBLENBQUM7UUFDUixJQUFJcUIsZ0JBQWdCO1lBQ2xCNEIsUUFBUUMsR0FBRyxDQUFDLHVCQUF1QjdCO1lBQ25DLHNCQUFzQjtZQUN0QnVCLG1CQUFtQjtZQUNuQkUsMEJBQTBCO1lBRTFCLFlBQVk7WUFDWixJQUFJbEIsZ0JBQWdCO2dCQUNsQkEsZUFBZVA7WUFDakI7UUFDRjtJQUNGLEdBQUc7UUFBQ0E7UUFBZ0JPO0tBQWU7SUFFbkMsTUFBTXVCLHdCQUF3QjtZQUFPQyw4RUFBcUI7UUFDeERkLHFCQUFxQmM7UUFFckIsSUFBSTtZQUNGLFlBQVk7WUFDWixNQUFNQyxXQUFXLE1BQU14Qyx1REFBU0EsQ0FBQ3lDLGNBQWM7WUFFL0MsSUFBSUQsU0FBU0UsSUFBSSxDQUFDQyxJQUFJLEtBQUssS0FBSztnQkFDOUIsTUFBTUMsY0FBY0osU0FBU0UsSUFBSSxDQUFDQSxJQUFJLElBQUksRUFBRTtnQkFFNUMsSUFBSUUsWUFBWUMsTUFBTSxLQUFLLEdBQUc7b0JBQzVCLHFCQUFxQjtvQkFDckJsQix1QkFBdUJpQixXQUFXLENBQUMsRUFBRTtvQkFDckN6QixvQkFBb0I7Z0JBQ3RCLE9BQU8sSUFBSXlCLFlBQVlDLE1BQU0sR0FBRyxHQUFHO29CQUNqQyxnQkFBZ0I7b0JBQ2hCNUIscUJBQXFCO2dCQUN2QixPQUFPO29CQUNMLGdCQUFnQjtvQkFDaEJtQixRQUFRVSxJQUFJLENBQUM7Z0JBQ2Y7WUFDRjtRQUNGLEVBQUUsT0FBT0MsT0FBTztZQUNkWCxRQUFRVyxLQUFLLENBQUMsYUFBYUE7WUFDM0IsZ0JBQWdCO1lBQ2hCOUIscUJBQXFCO1FBQ3ZCO0lBQ0Y7SUFFQSxNQUFNK0IsbUJBQW1CO1FBQ3ZCL0IscUJBQXFCO1FBQ3JCRSxvQkFBb0I7UUFDcEJFLHVCQUF1QjtRQUN2Qk0sdUJBQXVCO1FBQ3ZCRSxpQkFBaUI7UUFDakJKLHFCQUFxQjtJQUN2QjtJQUVBLE1BQU13QixxQkFBcUIsQ0FBQ0M7UUFDMUJ2Qix1QkFBdUJ1QjtRQUN2QmpDLHFCQUFxQjtRQUNyQkUsb0JBQW9CO0lBQ3RCO0lBRUEsTUFBTWdDLG9CQUFvQixDQUFDQztRQUN6QnZCLGlCQUFpQnVCO1FBQ2pCakMsb0JBQW9CO1FBQ3BCRSx1QkFBdUI7SUFDekI7SUFFQSxNQUFNZ0MscUJBQXFCO1FBQ3pCbEMsb0JBQW9CO1FBQ3BCRixxQkFBcUI7SUFDdkI7SUFFQSxNQUFNcUMsb0JBQW9CO1FBQ3hCakMsdUJBQXVCO1FBQ3ZCRixvQkFBb0I7SUFDdEI7SUFJQSxNQUFNb0MsbUJBQW1CLENBQUNDO1FBQ3hCcEIsUUFBUUMsR0FBRyxDQUFDLFNBQVNtQjtRQUNyQnZCLDBCQUEwQnVCO1FBQzFCekIsbUJBQW1CO0lBQ3JCO0lBRUEsTUFBTTBCLDhCQUE4QjtRQUNsQzFCLG1CQUFtQjtRQUNuQkUsMEJBQTBCO0lBQzVCO0lBRUEsV0FBVztJQUNYLE1BQU15Qix3QkFBd0IsQ0FBQ0M7UUFDN0IsaUJBQWlCO1FBQ2pCLE1BQU1DLGlCQUFpQmxELFFBQVFtRCxHQUFHLENBQUNDLENBQUFBLFlBQ2pDQSxVQUFVQyxFQUFFLEtBQUtKLGlCQUFpQkksRUFBRSxHQUNoQztnQkFBRSxHQUFHRCxTQUFTO2dCQUFFLEdBQUdILGdCQUFnQjtZQUFDLElBQ3BDRztRQUdOLGNBQWM7UUFDZGhELDRCQUFBQSxzQ0FBQUEsZ0JBQWtCOEM7UUFFbEIsZ0JBQWdCO1FBQ2hCM0IsMEJBQTBCMEI7SUFDNUI7SUFFQSxTQUFTO0lBQ1QsTUFBTUsscUJBQXFCLENBQUNDO1FBQzFCN0IsUUFBUUMsR0FBRyxDQUFDLFVBQVU0QjtRQUV0QixpQkFBaUI7UUFDakIsTUFBTUwsaUJBQWlCbEQsUUFBUXdELE1BQU0sQ0FBQ0MsQ0FBQUEsTUFBT0EsSUFBSUosRUFBRSxLQUFLRTtRQUV4RCxjQUFjO1FBQ2RuRCw0QkFBQUEsc0NBQUFBLGdCQUFrQjhDO0lBQ3BCO0lBRUEsU0FBUztJQUNULE1BQU1RLGlCQUFpQjtRQUNyQixJQUFJLENBQUM1RCxnQkFBZ0I7WUFDbkJMLDRFQUFPQSxDQUFDNEMsS0FBSyxDQUFDO1lBQ2Q7UUFDRjtRQUVBeEIsMEJBQTBCO0lBQzVCO0lBRUEsV0FBVztJQUNYLE1BQU04Qyx3QkFBd0IsQ0FBQ0M7UUFDN0JsQyxRQUFRQyxHQUFHLENBQUMsV0FBV2lDO1FBQ3ZCLG9CQUFvQjtRQUNwQnRCO0lBQ0Y7SUFFQSxhQUFhO0lBQ2IsTUFBTXVCLG9CQUFvQixPQUFPQztRQUMvQixJQUFJLENBQUNoRSxnQkFBZ0I7WUFDbkJMLDRFQUFPQSxDQUFDNEMsS0FBSyxDQUFDO1lBQ2Q7UUFDRjtRQUVBLElBQUksRUFBQ3RDLHFCQUFBQSwrQkFBQUEsU0FBVXNELEVBQUUsR0FBRTtZQUNqQjVELDRFQUFPQSxDQUFDNEMsS0FBSyxDQUFDO1lBQ2Q7UUFDRjtRQUVBLElBQUl5QixPQUFPQyxTQUFTLENBQUM1QixNQUFNLEdBQUcsR0FBRztZQUMvQjFDLDRFQUFPQSxDQUFDNEMsS0FBSyxDQUFDO1lBQ2Q7UUFDRjtRQUVBLElBQUk7WUFDRixvQ0FBb0M7WUFDcEMsTUFBTVAsV0FBVyxNQUFNdkMscURBQVFBLENBQUN5RSxXQUFXLENBQUNsRSxlQUFldUQsRUFBRSxFQUFFUyxPQUFPQyxTQUFTLEVBQUVoRSxTQUFTc0QsRUFBRTtZQUU1RixJQUFJdkIsU0FBU0UsSUFBSSxDQUFDQyxJQUFJLEtBQUssS0FBSztnQkFDOUJ4Qyw0RUFBT0EsQ0FBQ3dFLE9BQU8sQ0FBQztnQkFDaEJwRCwwQkFBMEI7Z0JBRTFCLFNBQVM7Z0JBQ1QsSUFBSTtvQkFDRixNQUFNcUQsa0JBQWtCLE1BQU0zRSxxREFBUUEsQ0FBQzRFLHVCQUF1QixDQUFDckUsZUFBZXVELEVBQUU7b0JBQ2hGLElBQUlhLGdCQUFnQmxDLElBQUksQ0FBQ0MsSUFBSSxLQUFLLEtBQUs7d0JBQ3JDN0IsNEJBQUFBLHNDQUFBQSxnQkFBa0I4RCxnQkFBZ0JsQyxJQUFJLENBQUNBLElBQUksSUFBSSxFQUFFO29CQUNuRDtnQkFDRixFQUFFLE9BQU9LLE9BQU87b0JBQ2RYLFFBQVFXLEtBQUssQ0FBQyxhQUFhQTtnQkFDN0I7WUFDRixPQUFPO2dCQUNMNUMsNEVBQU9BLENBQUM0QyxLQUFLLENBQUNQLFNBQVNFLElBQUksQ0FBQ3ZDLE9BQU8sSUFBSTtZQUN6QztRQUNGLEVBQUUsT0FBTzRDLE9BQVk7Z0JBRUxBLHNCQUFBQTtZQURkWCxRQUFRVyxLQUFLLENBQUMsV0FBV0E7WUFDekI1Qyw0RUFBT0EsQ0FBQzRDLEtBQUssQ0FBQ0EsRUFBQUEsa0JBQUFBLE1BQU1QLFFBQVEsY0FBZE8sdUNBQUFBLHVCQUFBQSxnQkFBZ0JMLElBQUksY0FBcEJLLDJDQUFBQSxxQkFBc0I1QyxPQUFPLEtBQUk7UUFDakQ7SUFDRjtJQUVBLGFBQWE7SUFDYixNQUFNMkUsOEJBQThCO1FBQ2xDdkQsMEJBQTBCO0lBQzVCO0lBRUEscUJBQ0UsOERBQUN3RDtRQUFLTixXQUFVOzswQkFDZCw4REFBQ087Z0JBQU9QLFdBQVU7O2tDQUNoQiw4REFBQ1E7d0JBQUlSLFdBQVU7OzBDQUNiLDhEQUFDdkUsbUZBQU1BO2dDQUFDdUUsV0FBVTtnQ0FBY1MsTUFBTTs7Ozs7OzBDQUN0Qyw4REFBQ0M7Z0NBQU1DLE1BQUs7Z0NBQU9DLGFBQVk7Ozs7Ozs7Ozs7OztrQ0FFakMsOERBQUNDO3dCQUFPYixXQUFVO3dCQUFrQmMsU0FBUyxJQUFNakQsc0JBQXNCOzswQ0FDdkUsOERBQUNsQyx5REFBTUE7Z0NBQUNvRixPQUFNO2dDQUFTTixNQUFNO2dDQUFJTyxNQUFNO29DQUFDO2lDQUFVO2dDQUFFaEIsV0FBVTs7Ozs7OzBDQUM5RCw4REFBQ2lCOzBDQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7WUFLVG5GLGVBQWUsdUJBQ2QsOERBQUNqQiwyREFBa0JBO2dCQUFDa0IsZ0JBQWdCQTtnQkFBZ0JDLFVBQVVBOzs7Ozs0QkFDNURGLGVBQWUsU0FDakJ1QixtQkFBbUJFLDBCQUEwQnhCLCtCQUMzQyw4REFBQ2hCLG9EQUFXQTtnQkFDVmdFLFdBQVd4QjtnQkFDWHhCLGdCQUFnQkE7Z0JBQ2hCbUYsUUFBUWxDO2dCQUNSbUMsbUJBQW1CbEM7Z0JBQ25CbUMsZ0JBQWdCN0I7Ozs7OzBDQUdsQiw4REFBQ3pFLHdEQUFlQTtnQkFDZGlCLGdCQUFnQkE7Z0JBQ2hCQyxVQUFVQTtnQkFDVkMsU0FBU0E7Z0JBQ1RDLGdCQUFnQkE7Z0JBQ2hCQyxjQUFjQTtnQkFDZGtGLGNBQWN2QztnQkFDZDFDLGlCQUFpQkE7Z0JBQ2pCa0YsWUFBWTNCOzs7Ozs0QkFHZDdELGVBQWUsdUJBQ2pCLDhEQUFDZCxtREFBVUE7Ozs7NEJBQ1RjLGVBQWUsdUJBQ2pCLDhEQUFDYix5REFBZ0JBOzs7OzRCQUNmYSxlQUFlLHVCQUNqQiw4REFBQ1osc0RBQWFBOzs7OzBDQUVkOztrQ0FDRSw4REFBQ1AscURBQVlBOzs7OztrQ0FDYiw4REFBQ0MscURBQVlBOzs7Ozs7OzBCQUlqQiw4REFBQ08sOERBQW9CQTtnQkFDbkJvRyxRQUFRaEY7Z0JBQ1JpRixTQUFTakQ7Z0JBQ1RULFlBQVlmO2dCQUNaMEUsZ0JBQWdCakQ7Ozs7OzswQkFHbEIsOERBQUNwRCw2REFBbUJBO2dCQUNsQm1HLFFBQVE5RTtnQkFDUitFLFNBQVNqRDtnQkFDVDJDLFFBQVF0QztnQkFDUmQsWUFBWWY7Z0JBQ1poQixnQkFBZ0JrQjtnQkFDaEJ5RSxlQUFlaEQ7Ozs7OzswQkFHakIsOERBQUNyRCxnRUFBc0JBO2dCQUNyQmtHLFFBQVE1RTtnQkFDUjZFLFNBQVNqRDtnQkFDVDJDLFFBQVFyQztnQkFDUjhDLFdBQVcvQjtnQkFDWDlCLFlBQVlmO2dCQUNaaEIsZ0JBQWdCa0I7Z0JBQ2hCRSxlQUFlQTs7Ozs7OzBCQUdqQiw4REFBQzdCLGtHQUFnQkE7Z0JBQ2ZzRyxTQUFTL0U7Z0JBQ1RnRixVQUFVeEI7Z0JBQ1Z5QixNQUFNaEM7Ozs7Ozs7Ozs7OztBQUlkO0dBM1NNakU7O1FBc0JxREQsbUVBQVdBOzs7S0F0QmhFQztBQTZTTiwrREFBZUEsV0FBV0EsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9hcHAvd29ya2JlbmNoL2NvbXBvbmVudHMvTWFpbkNvbnRlbnQudHN4P2U0YTAiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgUmVhY3QsIHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCBRdWlja0FjdGlvbnMgZnJvbSAnLi9RdWlja0FjdGlvbnMnO1xuaW1wb3J0IE9uZ29pbmdUYXNrcyBmcm9tICcuL09uZ29pbmdUYXNrcyc7XG5pbXBvcnQgVGVtcGxhdGVNYW5hZ2VtZW50IGZyb20gJy4vVGVtcGxhdGVNYW5hZ2VtZW50JztcbmltcG9ydCBDbGFzc01hbmFnZW1lbnQgZnJvbSAnLi9DbGFzc01hbmFnZW1lbnQnO1xuaW1wb3J0IENsYXNzRGV0YWlsIGZyb20gJy4vQ2xhc3NEZXRhaWwnO1xuaW1wb3J0IENsYXNzVGFza3MgZnJvbSAnLi9DbGFzc1Rhc2tzJztcbmltcG9ydCBDb3Vyc2VNYW5hZ2VtZW50IGZyb20gJy4vQ291cnNlTWFuYWdlbWVudCc7XG5pbXBvcnQgQ2xhc3NQcm9qZWN0cyBmcm9tICcuL0NsYXNzUHJvamVjdHMnO1xuaW1wb3J0IFNjaG9vbFNlbGVjdGlvbk1vZGFsIGZyb20gJy4vU2Nob29sU2VsZWN0aW9uTW9kYWwnO1xuaW1wb3J0IENsYXNzU2VsZWN0aW9uTW9kYWwgZnJvbSAnLi9DbGFzc1NlbGVjdGlvbk1vZGFsJztcbmltcG9ydCBUZW1wbGF0ZVNlbGVjdGlvbk1vZGFsIGZyb20gJy4vVGVtcGxhdGVTZWxlY3Rpb25Nb2RhbCc7XG5pbXBvcnQgeyBDcmVhdGVDbGFzc01vZGFsIH0gZnJvbSAnLi4vLi4vdGVhY2hlci1zcGFjZS9jb21wb25lbnRzL21vZGFscy9jcmVhdGUtY2xhc3MtbW9kYWwnO1xuaW1wb3J0IHsgc2Nob29sQXBpIH0gZnJvbSAnQC9saWIvYXBpL3NjaG9vbCc7XG5pbXBvcnQgeyBjbGFzc0FwaSB9IGZyb20gJ0AvbGliL2FwaS9jbGFzcyc7XG5pbXBvcnQgeyBTZWFyY2ggfSBmcm9tICdsdWNpZGUtcmVhY3QnO1xuaW1wb3J0IHsgbWVzc2FnZSB9IGZyb20gJ2FudGQnO1xuaW1wb3J0IHsgSGFuZFVwIH0gZnJvbSAnQGljb24tcGFyay9yZWFjdCc7XG5pbXBvcnQgJ0BpY29uLXBhcmsvcmVhY3Qvc3R5bGVzL2luZGV4LmNzcyc7XG5pbXBvcnQgJy4vU2Nob29sU2VsZWN0aW9uTW9kYWwuY3NzJztcbmltcG9ydCB7IHVzZVRlbXBsYXRlIH0gZnJvbSAnLi4vY29udGV4dHMvVGVtcGxhdGVDb250ZXh0JztcbmltcG9ydCAnLi9UZW1wbGF0ZVNlbGVjdGlvbk1vZGFsLmNzcyc7XG5cbmludGVyZmFjZSBTY2hvb2wge1xuICBpZDogbnVtYmVyO1xuICBzY2hvb2xOYW1lOiBzdHJpbmc7XG4gIHByb3ZpbmNlOiBzdHJpbmc7XG4gIGNpdHk6IHN0cmluZztcbiAgZGlzdHJpY3Q6IHN0cmluZztcbn1cblxuaW50ZXJmYWNlIENsYXNzSW5mbyB7XG4gIGlkOiBudW1iZXI7XG4gIHNjaG9vbElkOiBudW1iZXI7XG4gIGdyYWRlOiBzdHJpbmc7XG4gIGNsYXNzTmFtZTogc3RyaW5nO1xuICB0ZWFjaGVySWQ6IG51bWJlcjtcbiAgYXNzaXN0YW50VGVhY2hlcklkOiBudW1iZXI7XG4gIGludml0ZUNvZGU6IHN0cmluZztcbiAgY3JlYXRlVGltZTogc3RyaW5nO1xuICB1cGRhdGVUaW1lOiBzdHJpbmc7XG4gIHN0dWRlbnRDb3VudDogbnVtYmVyO1xuICBpc0Fzc2lzdGFudD86IGJvb2xlYW47XG59XG5cbmludGVyZmFjZSBNYWluQ29udGVudFByb3BzIHtcbiAgYWN0aXZlVmlldz86IHN0cmluZztcbiAgc2VsZWN0ZWRTY2hvb2w/OiBTY2hvb2wgfCBudWxsO1xuICB1c2VySW5mbz86IHtcbiAgICBpZDogbnVtYmVyO1xuICAgIG5pY2tOYW1lOiBzdHJpbmc7XG4gICAgW2tleTogc3RyaW5nXTogYW55O1xuICB9O1xuICBjbGFzc2VzPzogQ2xhc3NJbmZvW107XG4gIGNsYXNzZXNMb2FkaW5nPzogYm9vbGVhbjtcbiAgY2xhc3Nlc0Vycm9yPzogc3RyaW5nIHwgbnVsbDtcbiAgb25DbG9zZURyb3Bkb3duPzogKCkgPT4gdm9pZDtcbiAgb25DbGFzc2VzVXBkYXRlPzogKGNsYXNzZXM6IENsYXNzSW5mb1tdKSA9PiB2b2lkOyAvLyDmlrDlop7vvJrnj63nuqfliJfooajmm7TmlrDlm57osINcbiAgb25TY2hvb2xDaGFuZ2U/OiAoc2Nob29sOiBTY2hvb2wpID0+IHZvaWQ7IC8vIOaWsOWinu+8muWtpuagoeWPmOWMluWbnuiwg1xufVxuXG5jb25zdCBNYWluQ29udGVudCA9ICh7XG4gIGFjdGl2ZVZpZXcgPSAn5b+r6YCf5byA5aeLJyxcbiAgc2VsZWN0ZWRTY2hvb2wsXG4gIHVzZXJJbmZvLFxuICBjbGFzc2VzID0gW10sXG4gIGNsYXNzZXNMb2FkaW5nID0gZmFsc2UsXG4gIGNsYXNzZXNFcnJvciA9IG51bGwsXG4gIG9uQ2xvc2VEcm9wZG93bixcbiAgb25DbGFzc2VzVXBkYXRlLFxuICBvblNjaG9vbENoYW5nZVxufTogTWFpbkNvbnRlbnRQcm9wcykgPT4ge1xuICBjb25zdCBbaXNTY2hvb2xNb2RhbE9wZW4sIHNldElzU2Nob29sTW9kYWxPcGVuXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2lzQ2xhc3NNb2RhbE9wZW4sIHNldElzQ2xhc3NNb2RhbE9wZW5dID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbaXNUZW1wbGF0ZU1vZGFsT3Blbiwgc2V0SXNUZW1wbGF0ZU1vZGFsT3Blbl0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtpc0NyZWF0ZUNsYXNzTW9kYWxPcGVuLCBzZXRJc0NyZWF0ZUNsYXNzTW9kYWxPcGVuXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2N1cnJlbnRBY3Rpb25UeXBlLCBzZXRDdXJyZW50QWN0aW9uVHlwZV0gPSB1c2VTdGF0ZSgnJyk7XG4gIGNvbnN0IFttb2RhbFNlbGVjdGVkU2Nob29sLCBzZXRNb2RhbFNlbGVjdGVkU2Nob29sXSA9IHVzZVN0YXRlPGFueT4obnVsbCk7XG4gIGNvbnN0IFtzZWxlY3RlZENsYXNzLCBzZXRTZWxlY3RlZENsYXNzXSA9IHVzZVN0YXRlPGFueT4obnVsbCk7XG4gIGNvbnN0IFtzaG93Q2xhc3NEZXRhaWwsIHNldFNob3dDbGFzc0RldGFpbF0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtzZWxlY3RlZENsYXNzRm9yRGV0YWlsLCBzZXRTZWxlY3RlZENsYXNzRm9yRGV0YWlsXSA9IHVzZVN0YXRlPGFueT4obnVsbCk7XG5cbiAgLy8g5L2/55So5qih5p2/5LiK5LiL5paHXG4gIGNvbnN0IHsgY3VycmVudFRlbXBsYXRlLCBnbG9iYWxUZW1wbGF0ZUNoYW5nZVZlcnNpb24gfSA9IHVzZVRlbXBsYXRlKCk7XG5cbiAgLy8g55uR5ZCs5YWo5bGA5qih5p2/5Y+Y5YyW77yM6YCa55+l5omA5pyJ54+t57qn6K+m5oOF57uE5Lu25Yi35pawXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKGdsb2JhbFRlbXBsYXRlQ2hhbmdlVmVyc2lvbiA+IDAgJiYgY3VycmVudFRlbXBsYXRlKSB7XG4gICAgICBjb25zb2xlLmxvZygnTWFpbkNvbnRlbnQgLSDmo4DmtYvliLDlhajlsYDmqKHmnb/lj5jljJbvvIzniYjmnKzlj7c6JywgZ2xvYmFsVGVtcGxhdGVDaGFuZ2VWZXJzaW9uKTtcbiAgICAgIGNvbnNvbGUubG9nKCdNYWluQ29udGVudCAtIOaWsOeahOW9k+WJjeaooeadvzonLCBjdXJyZW50VGVtcGxhdGUpO1xuXG4gICAgICAvLyDov5nph4zlj6/ku6Xmt7vliqDpgJrnn6XmiYDmnInnj63nuqfnu4Tku7bliLfmlrDnmoTpgLvovpFcbiAgICAgIC8vIOeUseS6juePree6p+ivpuaDhee7hOS7tuW3sue7j+WcqOebkeWQrGdsb2JhbFRlbXBsYXRlQ2hhbmdlVmVyc2lvbu+8jFxuICAgICAgLy8g6L+Z6YeM5Li76KaB5piv5Li65LqG56Gu5L+d54q25oCB5ZCM5q2lXG4gICAgfVxuICB9LCBbZ2xvYmFsVGVtcGxhdGVDaGFuZ2VWZXJzaW9uLCBjdXJyZW50VGVtcGxhdGVdKTtcblxuICAvLyDnm5HlkKzlrabmoKHpgInmi6nlj5jljJbvvIzlvLrliLbot7Plm57nj63nuqfnrqHnkIbpobXpnaJcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoc2VsZWN0ZWRTY2hvb2wpIHtcbiAgICAgIGNvbnNvbGUubG9nKCfmo4DmtYvliLDlrabmoKHlj5jljJbvvIzlvLrliLbot7Plm57nj63nuqfnrqHnkIbpobXpnaI6Jywgc2VsZWN0ZWRTY2hvb2wpO1xuICAgICAgLy8g6YeN572u54+t57qn6K+m5oOF5pi+56S654q25oCB77yM5by65Yi25pi+56S654+t57qn5YiX6KGoXG4gICAgICBzZXRTaG93Q2xhc3NEZXRhaWwoZmFsc2UpO1xuICAgICAgc2V0U2VsZWN0ZWRDbGFzc0ZvckRldGFpbChudWxsKTtcblxuICAgICAgLy8g6YCa55+l54i257uE5Lu25a2m5qCh5Y+Y5YyWXG4gICAgICBpZiAob25TY2hvb2xDaGFuZ2UpIHtcbiAgICAgICAgb25TY2hvb2xDaGFuZ2Uoc2VsZWN0ZWRTY2hvb2wpO1xuICAgICAgfVxuICAgIH1cbiAgfSwgW3NlbGVjdGVkU2Nob29sLCBvblNjaG9vbENoYW5nZV0pO1xuXG4gIGNvbnN0IGhhbmRsZVF1aWNrU3RhcnRDbGljayA9IGFzeW5jIChhY3Rpb25UeXBlOiBzdHJpbmcgPSAn5Y+R5biD5Lu75YqhJykgPT4ge1xuICAgIHNldEN1cnJlbnRBY3Rpb25UeXBlKGFjdGlvblR5cGUpO1xuXG4gICAgdHJ5IHtcbiAgICAgIC8vIOiOt+WPlueUqOaIt+eahOWtpuagoeWIl+ihqFxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBzY2hvb2xBcGkuZ2V0VXNlclNjaG9vbHMoKTtcblxuICAgICAgaWYgKHJlc3BvbnNlLmRhdGEuY29kZSA9PT0gMjAwKSB7XG4gICAgICAgIGNvbnN0IHNjaG9vbHNEYXRhID0gcmVzcG9uc2UuZGF0YS5kYXRhIHx8IFtdO1xuXG4gICAgICAgIGlmIChzY2hvb2xzRGF0YS5sZW5ndGggPT09IDEpIHtcbiAgICAgICAgICAvLyDlj6rmnInkuIDkuKrlrabmoKHvvIznm7TmjqXpgInmi6nlubbot7PliLDnj63nuqfpgInmi6lcbiAgICAgICAgICBzZXRNb2RhbFNlbGVjdGVkU2Nob29sKHNjaG9vbHNEYXRhWzBdKTtcbiAgICAgICAgICBzZXRJc0NsYXNzTW9kYWxPcGVuKHRydWUpO1xuICAgICAgICB9IGVsc2UgaWYgKHNjaG9vbHNEYXRhLmxlbmd0aCA+IDEpIHtcbiAgICAgICAgICAvLyDlpJrkuKrlrabmoKHvvIzmmL7npLrlrabmoKHpgInmi6nlvLnnqpdcbiAgICAgICAgICBzZXRJc1NjaG9vbE1vZGFsT3Blbih0cnVlKTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAvLyDmsqHmnInlrabmoKHvvIzlj6/ku6XmmL7npLrmj5DnpLrkv6Hmga9cbiAgICAgICAgICBjb25zb2xlLndhcm4oJ+eUqOaIt+ayoeacieWFs+iBlOeahOWtpuagoScpO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+iOt+WPluWtpuagoeWIl+ihqOWksei0pTonLCBlcnJvcik7XG4gICAgICAvLyDlh7rplJnml7bku43nhLbmmL7npLrlrabmoKHpgInmi6nlvLnnqpdcbiAgICAgIHNldElzU2Nob29sTW9kYWxPcGVuKHRydWUpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVDbG9zZU1vZGFsID0gKCkgPT4ge1xuICAgIHNldElzU2Nob29sTW9kYWxPcGVuKGZhbHNlKTtcbiAgICBzZXRJc0NsYXNzTW9kYWxPcGVuKGZhbHNlKTtcbiAgICBzZXRJc1RlbXBsYXRlTW9kYWxPcGVuKGZhbHNlKTtcbiAgICBzZXRNb2RhbFNlbGVjdGVkU2Nob29sKG51bGwpO1xuICAgIHNldFNlbGVjdGVkQ2xhc3MobnVsbCk7XG4gICAgc2V0Q3VycmVudEFjdGlvblR5cGUoJycpO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZVNjaG9vbFNlbGVjdCA9IChzY2hvb2w6IGFueSkgPT4ge1xuICAgIHNldE1vZGFsU2VsZWN0ZWRTY2hvb2woc2Nob29sKTtcbiAgICBzZXRJc1NjaG9vbE1vZGFsT3BlbihmYWxzZSk7XG4gICAgc2V0SXNDbGFzc01vZGFsT3Blbih0cnVlKTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVDbGFzc1NlbGVjdCA9IChjbGFzc0RhdGE6IGFueSkgPT4ge1xuICAgIHNldFNlbGVjdGVkQ2xhc3MoY2xhc3NEYXRhKTtcbiAgICBzZXRJc0NsYXNzTW9kYWxPcGVuKGZhbHNlKTtcbiAgICBzZXRJc1RlbXBsYXRlTW9kYWxPcGVuKHRydWUpO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZUJhY2tUb1NjaG9vbCA9ICgpID0+IHtcbiAgICBzZXRJc0NsYXNzTW9kYWxPcGVuKGZhbHNlKTtcbiAgICBzZXRJc1NjaG9vbE1vZGFsT3Blbih0cnVlKTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVCYWNrVG9DbGFzcyA9ICgpID0+IHtcbiAgICBzZXRJc1RlbXBsYXRlTW9kYWxPcGVuKGZhbHNlKTtcbiAgICBzZXRJc0NsYXNzTW9kYWxPcGVuKHRydWUpO1xuICB9O1xuXG5cblxuICBjb25zdCBoYW5kbGVDbGFzc0NsaWNrID0gKGNsYXNzSW5mbzogYW55KSA9PiB7XG4gICAgY29uc29sZS5sb2coJ+eCueWHu+ePree6pzonLCBjbGFzc0luZm8pO1xuICAgIHNldFNlbGVjdGVkQ2xhc3NGb3JEZXRhaWwoY2xhc3NJbmZvKTtcbiAgICBzZXRTaG93Q2xhc3NEZXRhaWwodHJ1ZSk7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlQmFja1RvQ2xhc3NNYW5hZ2VtZW50ID0gKCkgPT4ge1xuICAgIHNldFNob3dDbGFzc0RldGFpbChmYWxzZSk7XG4gICAgc2V0U2VsZWN0ZWRDbGFzc0ZvckRldGFpbChudWxsKTtcbiAgfTtcblxuICAvLyDlpITnkIbnj63nuqfkv6Hmga/mm7TmlrBcbiAgY29uc3QgaGFuZGxlQ2xhc3NJbmZvVXBkYXRlID0gKHVwZGF0ZWRDbGFzc0luZm86IENsYXNzSW5mbykgPT4ge1xuICAgIC8vIOabtOaWsOePree6p+WIl+ihqOS4reWvueW6lOeahOePree6p+S/oeaBr1xuICAgIGNvbnN0IHVwZGF0ZWRDbGFzc2VzID0gY2xhc3Nlcy5tYXAoY2xhc3NJdGVtID0+XG4gICAgICBjbGFzc0l0ZW0uaWQgPT09IHVwZGF0ZWRDbGFzc0luZm8uaWRcbiAgICAgICAgPyB7IC4uLmNsYXNzSXRlbSwgLi4udXBkYXRlZENsYXNzSW5mbyB9XG4gICAgICAgIDogY2xhc3NJdGVtXG4gICAgKTtcblxuICAgIC8vIOmAmuefpeeItue7hOS7tuabtOaWsOePree6p+WIl+ihqFxuICAgIG9uQ2xhc3Nlc1VwZGF0ZT8uKHVwZGF0ZWRDbGFzc2VzKTtcblxuICAgIC8vIOWQjOaXtuabtOaWsOW9k+WJjemAieS4reeahOePree6p+ivpuaDhVxuICAgIHNldFNlbGVjdGVkQ2xhc3NGb3JEZXRhaWwodXBkYXRlZENsYXNzSW5mbyk7XG4gIH07XG5cbiAgLy8g5aSE55CG54+t57qn5Yig6ZmkXG4gIGNvbnN0IGhhbmRsZUNsYXNzRGVsZXRlZCA9IChkZWxldGVkQ2xhc3NJZDogbnVtYmVyKSA9PiB7XG4gICAgY29uc29sZS5sb2coJ+ePree6p+W3suWIoOmZpDonLCBkZWxldGVkQ2xhc3NJZCk7XG5cbiAgICAvLyDku47nj63nuqfliJfooajkuK3np7vpmaTooqvliKDpmaTnmoTnj63nuqdcbiAgICBjb25zdCB1cGRhdGVkQ2xhc3NlcyA9IGNsYXNzZXMuZmlsdGVyKGNscyA9PiBjbHMuaWQgIT09IGRlbGV0ZWRDbGFzc0lkKTtcblxuICAgIC8vIOmAmuefpeeItue7hOS7tuabtOaWsOePree6p+WIl+ihqFxuICAgIG9uQ2xhc3Nlc1VwZGF0ZT8uKHVwZGF0ZWRDbGFzc2VzKTtcbiAgfTtcblxuICAvLyDlpITnkIbmt7vliqDnj63nuqdcbiAgY29uc3QgaGFuZGxlQWRkQ2xhc3MgPSAoKSA9PiB7XG4gICAgaWYgKCFzZWxlY3RlZFNjaG9vbCkge1xuICAgICAgbWVzc2FnZS5lcnJvcign6K+35YWI6YCJ5oup5a2m5qChJyk7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgc2V0SXNDcmVhdGVDbGFzc01vZGFsT3Blbih0cnVlKTtcbiAgfTtcblxuICAvLyDlpITnkIbmqKHmnb/pgInmi6nnoa7orqRcbiAgY29uc3QgaGFuZGxlVGVtcGxhdGVDb25maXJtID0gKHRhc2tEYXRhOiBhbnkpID0+IHtcbiAgICBjb25zb2xlLmxvZygn5qih5p2/6YCJ5oup56Gu6K6kOicsIHRhc2tEYXRhKTtcbiAgICAvLyDnroDljZXlhbPpl63lvLnnqpfvvIzkuI3miafooYzlhbfkvZPnmoTlj5HluIPpgLvovpFcbiAgICBoYW5kbGVDbG9zZU1vZGFsKCk7XG4gIH07XG5cbiAgLy8g5aSE55CG5Yib5bu654+t57qn6KGo5Y2V5o+Q5LqkXG4gIGNvbnN0IGhhbmRsZUNyZWF0ZUNsYXNzID0gYXN5bmMgKHZhbHVlczogeyBjbGFzc05hbWU6IHN0cmluZyB9KSA9PiB7XG4gICAgaWYgKCFzZWxlY3RlZFNjaG9vbCkge1xuICAgICAgbWVzc2FnZS5lcnJvcign6K+35YWI6YCJ5oup5a2m5qChJyk7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgaWYgKCF1c2VySW5mbz8uaWQpIHtcbiAgICAgIG1lc3NhZ2UuZXJyb3IoJ+eUqOaIt+S/oeaBr+S4jeWujOaVtO+8jOivt+mHjeaWsOeZu+W9lScpO1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIGlmICh2YWx1ZXMuY2xhc3NOYW1lLmxlbmd0aCA+IDgpIHtcbiAgICAgIG1lc3NhZ2UuZXJyb3IoJ+ePree6p+WQjeensOS4jeiDvei2hei/hzjkuKrlrZfnrKYnKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICB0cnkge1xuICAgICAgLy8g5L2/55SoIGNyZWF0ZUNsYXNzIEFQSe+8jOmcgOimgeS8oOmAkiB0ZWFjaGVySWRcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgY2xhc3NBcGkuY3JlYXRlQ2xhc3Moc2VsZWN0ZWRTY2hvb2wuaWQsIHZhbHVlcy5jbGFzc05hbWUsIHVzZXJJbmZvLmlkKTtcblxuICAgICAgaWYgKHJlc3BvbnNlLmRhdGEuY29kZSA9PT0gMjAwKSB7XG4gICAgICAgIG1lc3NhZ2Uuc3VjY2Vzcygn5Yib5bu654+t57qn5oiQ5YqfJyk7XG4gICAgICAgIHNldElzQ3JlYXRlQ2xhc3NNb2RhbE9wZW4oZmFsc2UpO1xuXG4gICAgICAgIC8vIOWIt+aWsOePree6p+WIl+ihqFxuICAgICAgICB0cnkge1xuICAgICAgICAgIGNvbnN0IGNsYXNzZXNSZXNwb25zZSA9IGF3YWl0IGNsYXNzQXBpLmdldFRlYWNoZXJDbGFzc2VzU2ltcGxlKHNlbGVjdGVkU2Nob29sLmlkKTtcbiAgICAgICAgICBpZiAoY2xhc3Nlc1Jlc3BvbnNlLmRhdGEuY29kZSA9PT0gMjAwKSB7XG4gICAgICAgICAgICBvbkNsYXNzZXNVcGRhdGU/LihjbGFzc2VzUmVzcG9uc2UuZGF0YS5kYXRhIHx8IFtdKTtcbiAgICAgICAgICB9XG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgY29uc29sZS5lcnJvcign5Yi35paw54+t57qn5YiX6KGo5aSx6LSlOicsIGVycm9yKTtcbiAgICAgICAgfVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgbWVzc2FnZS5lcnJvcihyZXNwb25zZS5kYXRhLm1lc3NhZ2UgfHwgJ+ivpeePree6p+W3suWtmOWcqOaIluWIm+W7uuWksei0pScpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+WIm+W7uuePree6p+Wksei0pTonLCBlcnJvcik7XG4gICAgICBtZXNzYWdlLmVycm9yKGVycm9yLnJlc3BvbnNlPy5kYXRhPy5tZXNzYWdlIHx8ICfliJvlu7rnj63nuqflpLHotKUnKTtcbiAgICB9XG4gIH07XG5cbiAgLy8g5aSE55CG5Yib5bu654+t57qn5by556qX5YWz6ZetXG4gIGNvbnN0IGhhbmRsZUNyZWF0ZUNsYXNzTW9kYWxDbG9zZSA9ICgpID0+IHtcbiAgICBzZXRJc0NyZWF0ZUNsYXNzTW9kYWxPcGVuKGZhbHNlKTtcbiAgfTtcblxuICByZXR1cm4gKFxuICAgIDxtYWluIGNsYXNzTmFtZT1cIm1haW4tY29udGVudCByZWxhdGl2ZVwiPlxuICAgICAgPGhlYWRlciBjbGFzc05hbWU9XCJtYWluLWhlYWRlclwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNlYXJjaC1iYXJcIj5cbiAgICAgICAgICA8U2VhcmNoIGNsYXNzTmFtZT1cInNlYXJjaC1pY29uXCIgc2l6ZT17MTh9IC8+XG4gICAgICAgICAgPGlucHV0IHR5cGU9XCJ0ZXh0XCIgcGxhY2Vob2xkZXI9XCLmkJzntKLor77nqIvjgIHku7vliqHmiJblrabnlJ8uLi5cIiAvPlxuICAgICAgICA8L2Rpdj5cbiAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9XCJzdGFydC1jbGFzcy1idG5cIiBvbkNsaWNrPXsoKSA9PiBoYW5kbGVRdWlja1N0YXJ0Q2xpY2soJ+W/q+mAn+S4iuivvicpfT5cbiAgICAgICAgICA8SGFuZFVwIHRoZW1lPVwiZmlsbGVkXCIgc2l6ZT17MjB9IGZpbGw9e1tcIiNmZmZmZmZcIl19IGNsYXNzTmFtZT1cInN0YXJ0LWNsYXNzLWljb25cIiAvPlxuICAgICAgICAgIDxzcGFuPuW/q+mAn+S4iuivvjwvc3Bhbj5cbiAgICAgICAgPC9idXR0b24+XG4gICAgICA8L2hlYWRlcj5cblxuICAgICAgey8qIOagueaNrmFjdGl2ZVZpZXfmuLLmn5PkuI3lkIzlhoXlrrkgKi99XG4gICAgICB7YWN0aXZlVmlldyA9PT0gJ+aooeadv+euoeeQhicgPyAoXG4gICAgICAgIDxUZW1wbGF0ZU1hbmFnZW1lbnQgc2VsZWN0ZWRTY2hvb2w9e3NlbGVjdGVkU2Nob29sfSB1c2VySW5mbz17dXNlckluZm99IC8+XG4gICAgICApIDogYWN0aXZlVmlldyA9PT0gJ+ePree6p+euoeeQhicgPyAoXG4gICAgICAgIHNob3dDbGFzc0RldGFpbCAmJiBzZWxlY3RlZENsYXNzRm9yRGV0YWlsICYmIHNlbGVjdGVkU2Nob29sID8gKFxuICAgICAgICAgIDxDbGFzc0RldGFpbFxuICAgICAgICAgICAgY2xhc3NJbmZvPXtzZWxlY3RlZENsYXNzRm9yRGV0YWlsfVxuICAgICAgICAgICAgc2VsZWN0ZWRTY2hvb2w9e3NlbGVjdGVkU2Nob29sfVxuICAgICAgICAgICAgb25CYWNrPXtoYW5kbGVCYWNrVG9DbGFzc01hbmFnZW1lbnR9XG4gICAgICAgICAgICBvbkNsYXNzSW5mb1VwZGF0ZT17aGFuZGxlQ2xhc3NJbmZvVXBkYXRlfVxuICAgICAgICAgICAgb25DbGFzc0RlbGV0ZWQ9e2hhbmRsZUNsYXNzRGVsZXRlZH1cbiAgICAgICAgICAvPlxuICAgICAgICApIDogKFxuICAgICAgICAgIDxDbGFzc01hbmFnZW1lbnRcbiAgICAgICAgICAgIHNlbGVjdGVkU2Nob29sPXtzZWxlY3RlZFNjaG9vbH1cbiAgICAgICAgICAgIHVzZXJJbmZvPXt1c2VySW5mb31cbiAgICAgICAgICAgIGNsYXNzZXM9e2NsYXNzZXN9XG4gICAgICAgICAgICBjbGFzc2VzTG9hZGluZz17Y2xhc3Nlc0xvYWRpbmd9XG4gICAgICAgICAgICBjbGFzc2VzRXJyb3I9e2NsYXNzZXNFcnJvcn1cbiAgICAgICAgICAgIG9uQ2xhc3NDbGljaz17aGFuZGxlQ2xhc3NDbGlja31cbiAgICAgICAgICAgIG9uQ2xvc2VEcm9wZG93bj17b25DbG9zZURyb3Bkb3dufVxuICAgICAgICAgICAgb25BZGRDbGFzcz17aGFuZGxlQWRkQ2xhc3N9XG4gICAgICAgICAgLz5cbiAgICAgICAgKVxuICAgICAgKSA6IGFjdGl2ZVZpZXcgPT09ICfnj63nuqfku7vliqEnID8gKFxuICAgICAgICA8Q2xhc3NUYXNrcyAvPlxuICAgICAgKSA6IGFjdGl2ZVZpZXcgPT09ICfor77nqIvnrqHnkIYnID8gKFxuICAgICAgICA8Q291cnNlTWFuYWdlbWVudCAvPlxuICAgICAgKSA6IGFjdGl2ZVZpZXcgPT09ICfnj63nuqfpobnnm64nID8gKFxuICAgICAgICA8Q2xhc3NQcm9qZWN0cyAvPlxuICAgICAgKSA6IChcbiAgICAgICAgPD5cbiAgICAgICAgICA8UXVpY2tBY3Rpb25zIC8+XG4gICAgICAgICAgPE9uZ29pbmdUYXNrcyAvPlxuICAgICAgICA8Lz5cbiAgICAgICl9XG5cbiAgICAgIDxTY2hvb2xTZWxlY3Rpb25Nb2RhbFxuICAgICAgICBpc09wZW49e2lzU2Nob29sTW9kYWxPcGVufVxuICAgICAgICBvbkNsb3NlPXtoYW5kbGVDbG9zZU1vZGFsfVxuICAgICAgICBhY3Rpb25UeXBlPXtjdXJyZW50QWN0aW9uVHlwZX1cbiAgICAgICAgb25TY2hvb2xTZWxlY3Q9e2hhbmRsZVNjaG9vbFNlbGVjdH1cbiAgICAgIC8+XG5cbiAgICAgIDxDbGFzc1NlbGVjdGlvbk1vZGFsXG4gICAgICAgIGlzT3Blbj17aXNDbGFzc01vZGFsT3Blbn1cbiAgICAgICAgb25DbG9zZT17aGFuZGxlQ2xvc2VNb2RhbH1cbiAgICAgICAgb25CYWNrPXtoYW5kbGVCYWNrVG9TY2hvb2x9XG4gICAgICAgIGFjdGlvblR5cGU9e2N1cnJlbnRBY3Rpb25UeXBlfVxuICAgICAgICBzZWxlY3RlZFNjaG9vbD17bW9kYWxTZWxlY3RlZFNjaG9vbH1cbiAgICAgICAgb25DbGFzc1NlbGVjdD17aGFuZGxlQ2xhc3NTZWxlY3R9XG4gICAgICAvPlxuXG4gICAgICA8VGVtcGxhdGVTZWxlY3Rpb25Nb2RhbFxuICAgICAgICBpc09wZW49e2lzVGVtcGxhdGVNb2RhbE9wZW59XG4gICAgICAgIG9uQ2xvc2U9e2hhbmRsZUNsb3NlTW9kYWx9XG4gICAgICAgIG9uQmFjaz17aGFuZGxlQmFja1RvQ2xhc3N9XG4gICAgICAgIG9uQ29uZmlybT17aGFuZGxlVGVtcGxhdGVDb25maXJtfVxuICAgICAgICBhY3Rpb25UeXBlPXtjdXJyZW50QWN0aW9uVHlwZX1cbiAgICAgICAgc2VsZWN0ZWRTY2hvb2w9e21vZGFsU2VsZWN0ZWRTY2hvb2x9XG4gICAgICAgIHNlbGVjdGVkQ2xhc3M9e3NlbGVjdGVkQ2xhc3N9XG4gICAgICAvPlxuXG4gICAgICA8Q3JlYXRlQ2xhc3NNb2RhbFxuICAgICAgICB2aXNpYmxlPXtpc0NyZWF0ZUNsYXNzTW9kYWxPcGVufVxuICAgICAgICBvbkNhbmNlbD17aGFuZGxlQ3JlYXRlQ2xhc3NNb2RhbENsb3NlfVxuICAgICAgICBvbk9rPXtoYW5kbGVDcmVhdGVDbGFzc31cbiAgICAgIC8+XG4gICAgPC9tYWluPlxuICApO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgTWFpbkNvbnRlbnQ7ICJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwidXNlRWZmZWN0IiwiUXVpY2tBY3Rpb25zIiwiT25nb2luZ1Rhc2tzIiwiVGVtcGxhdGVNYW5hZ2VtZW50IiwiQ2xhc3NNYW5hZ2VtZW50IiwiQ2xhc3NEZXRhaWwiLCJDbGFzc1Rhc2tzIiwiQ291cnNlTWFuYWdlbWVudCIsIkNsYXNzUHJvamVjdHMiLCJTY2hvb2xTZWxlY3Rpb25Nb2RhbCIsIkNsYXNzU2VsZWN0aW9uTW9kYWwiLCJUZW1wbGF0ZVNlbGVjdGlvbk1vZGFsIiwiQ3JlYXRlQ2xhc3NNb2RhbCIsInNjaG9vbEFwaSIsImNsYXNzQXBpIiwiU2VhcmNoIiwibWVzc2FnZSIsIkhhbmRVcCIsInVzZVRlbXBsYXRlIiwiTWFpbkNvbnRlbnQiLCJhY3RpdmVWaWV3Iiwic2VsZWN0ZWRTY2hvb2wiLCJ1c2VySW5mbyIsImNsYXNzZXMiLCJjbGFzc2VzTG9hZGluZyIsImNsYXNzZXNFcnJvciIsIm9uQ2xvc2VEcm9wZG93biIsIm9uQ2xhc3Nlc1VwZGF0ZSIsIm9uU2Nob29sQ2hhbmdlIiwiaXNTY2hvb2xNb2RhbE9wZW4iLCJzZXRJc1NjaG9vbE1vZGFsT3BlbiIsImlzQ2xhc3NNb2RhbE9wZW4iLCJzZXRJc0NsYXNzTW9kYWxPcGVuIiwiaXNUZW1wbGF0ZU1vZGFsT3BlbiIsInNldElzVGVtcGxhdGVNb2RhbE9wZW4iLCJpc0NyZWF0ZUNsYXNzTW9kYWxPcGVuIiwic2V0SXNDcmVhdGVDbGFzc01vZGFsT3BlbiIsImN1cnJlbnRBY3Rpb25UeXBlIiwic2V0Q3VycmVudEFjdGlvblR5cGUiLCJtb2RhbFNlbGVjdGVkU2Nob29sIiwic2V0TW9kYWxTZWxlY3RlZFNjaG9vbCIsInNlbGVjdGVkQ2xhc3MiLCJzZXRTZWxlY3RlZENsYXNzIiwic2hvd0NsYXNzRGV0YWlsIiwic2V0U2hvd0NsYXNzRGV0YWlsIiwic2VsZWN0ZWRDbGFzc0ZvckRldGFpbCIsInNldFNlbGVjdGVkQ2xhc3NGb3JEZXRhaWwiLCJjdXJyZW50VGVtcGxhdGUiLCJnbG9iYWxUZW1wbGF0ZUNoYW5nZVZlcnNpb24iLCJjb25zb2xlIiwibG9nIiwiaGFuZGxlUXVpY2tTdGFydENsaWNrIiwiYWN0aW9uVHlwZSIsInJlc3BvbnNlIiwiZ2V0VXNlclNjaG9vbHMiLCJkYXRhIiwiY29kZSIsInNjaG9vbHNEYXRhIiwibGVuZ3RoIiwid2FybiIsImVycm9yIiwiaGFuZGxlQ2xvc2VNb2RhbCIsImhhbmRsZVNjaG9vbFNlbGVjdCIsInNjaG9vbCIsImhhbmRsZUNsYXNzU2VsZWN0IiwiY2xhc3NEYXRhIiwiaGFuZGxlQmFja1RvU2Nob29sIiwiaGFuZGxlQmFja1RvQ2xhc3MiLCJoYW5kbGVDbGFzc0NsaWNrIiwiY2xhc3NJbmZvIiwiaGFuZGxlQmFja1RvQ2xhc3NNYW5hZ2VtZW50IiwiaGFuZGxlQ2xhc3NJbmZvVXBkYXRlIiwidXBkYXRlZENsYXNzSW5mbyIsInVwZGF0ZWRDbGFzc2VzIiwibWFwIiwiY2xhc3NJdGVtIiwiaWQiLCJoYW5kbGVDbGFzc0RlbGV0ZWQiLCJkZWxldGVkQ2xhc3NJZCIsImZpbHRlciIsImNscyIsImhhbmRsZUFkZENsYXNzIiwiaGFuZGxlVGVtcGxhdGVDb25maXJtIiwidGFza0RhdGEiLCJoYW5kbGVDcmVhdGVDbGFzcyIsInZhbHVlcyIsImNsYXNzTmFtZSIsImNyZWF0ZUNsYXNzIiwic3VjY2VzcyIsImNsYXNzZXNSZXNwb25zZSIsImdldFRlYWNoZXJDbGFzc2VzU2ltcGxlIiwiaGFuZGxlQ3JlYXRlQ2xhc3NNb2RhbENsb3NlIiwibWFpbiIsImhlYWRlciIsImRpdiIsInNpemUiLCJpbnB1dCIsInR5cGUiLCJwbGFjZWhvbGRlciIsImJ1dHRvbiIsIm9uQ2xpY2siLCJ0aGVtZSIsImZpbGwiLCJzcGFuIiwib25CYWNrIiwib25DbGFzc0luZm9VcGRhdGUiLCJvbkNsYXNzRGVsZXRlZCIsIm9uQ2xhc3NDbGljayIsIm9uQWRkQ2xhc3MiLCJpc09wZW4iLCJvbkNsb3NlIiwib25TY2hvb2xTZWxlY3QiLCJvbkNsYXNzU2VsZWN0Iiwib25Db25maXJtIiwidmlzaWJsZSIsIm9uQ2FuY2VsIiwib25PayJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/workbench/components/MainContent.tsx\n"));

/***/ })

});