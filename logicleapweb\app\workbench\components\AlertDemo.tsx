'use client';

import React from 'react';
import { useCustomAlert } from './hooks/useCustomAlert';
import CustomAlert from './CustomAlert';

const AlertDemo: React.FC = () => {
  const { alertState, success, warning, error, confirm } = useCustomAlert();

  const handleSuccessAlert = () => {
    success('localhost:3000 显示课程发布成功！');
  };

  const handleWarningAlert = () => {
    warning('请选择支持的文件格式：PDF、DOC、DOCX、XLS、XLSX、PPT、PPTX、TXT、JPG、PNG、GIF');
  };

  const handleErrorAlert = () => {
    error('网络连接失败，请稍后重试');
  };

  const handleConfirmAlert = () => {
    confirm(
      '确定要删除这个系列课程吗？删除后无法恢复！',
      () => {
        success('删除成功！');
      },
      {
        type: 'warning',
        title: '删除确认'
      }
    );
  };

  return (
    <div style={{ padding: '20px', display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
      <button 
        onClick={handleSuccessAlert}
        style={{
          padding: '10px 20px',
          backgroundColor: '#52c41a',
          color: 'white',
          border: 'none',
          borderRadius: '6px',
          cursor: 'pointer'
        }}
      >
        成功提示
      </button>
      
      <button 
        onClick={handleWarningAlert}
        style={{
          padding: '10px 20px',
          backgroundColor: '#faad14',
          color: 'white',
          border: 'none',
          borderRadius: '6px',
          cursor: 'pointer'
        }}
      >
        警告提示
      </button>
      
      <button 
        onClick={handleErrorAlert}
        style={{
          padding: '10px 20px',
          backgroundColor: '#ff4d4f',
          color: 'white',
          border: 'none',
          borderRadius: '6px',
          cursor: 'pointer'
        }}
      >
        错误提示
      </button>
      
      <button 
        onClick={handleConfirmAlert}
        style={{
          padding: '10px 20px',
          backgroundColor: '#1890ff',
          color: 'white',
          border: 'none',
          borderRadius: '6px',
          cursor: 'pointer'
        }}
      >
        确认对话框
      </button>

      <CustomAlert
        isOpen={alertState.isOpen}
        title={alertState.title}
        message={alertState.message}
        type={alertState.type}
        onConfirm={alertState.onConfirm}
        onCancel={alertState.onCancel}
        confirmText={alertState.confirmText}
        cancelText={alertState.cancelText}
        showCancel={alertState.showCancel}
      />
    </div>
  );
};

export default AlertDemo;
