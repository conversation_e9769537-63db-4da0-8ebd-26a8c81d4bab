'use client';

import React from 'react';
import { Modal, Form, Input } from 'antd';
import { InfoCircleOutlined } from '@ant-design/icons';

interface AddStudentForm {
  studentNumber: string;
  nickName: string;
}

interface AddStudentModalProps {
  visible: boolean;
  onCancel: () => void;
  onOk: (values: AddStudentForm) => Promise<void>;
}

export const AddStudentModal: React.FC<AddStudentModalProps> = ({
  visible,
  onCancel,
  onOk,
}) => {
  const [form] = Form.useForm();

  return (
    <Modal
      title="添加学生"
      open={visible}
      onCancel={() => {
        onCancel();
        form.resetFields();
      }}
      footer={null}
      centered
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={onOk}
      >
        <Form.Item
          label="学号"
          name="studentNumber"
          rules={[{ required: true, message: '请输入学号' }]}
        >
          <Input placeholder="请输入学号" />
        </Form.Item>

        <Form.Item
          label="姓名"
          name="nickName"
          rules={[{ required: true, message: '请输入姓名' }]}
        >
          <Input placeholder="请输入姓名" />
        </Form.Item>

        <div className="bg-blue-50 rounded-lg p-4">
          <div className="flex items-start gap-3">
            <InfoCircleOutlined className="text-blue-500 mt-0.5" />
            <div className="text-sm text-blue-700">
              <div className="font-medium mb-1">温馨提示</div>
              <div className="text-blue-600">
                • 学生的默认密码为123456
              </div>
            </div>
          </div>
        </div>

        <Form.Item className="mb-0 mt-6">
          <div className="flex justify-end gap-2">
            <button
              type="button"
              onClick={() => {
                onCancel();
                form.resetFields();
              }}
              className="px-4 py-2 text-gray-600 bg-gray-100 rounded hover:bg-gray-200 transition-colors"
            >
              取消
            </button>
            <button
              type="submit"
              className="px-4 py-2 text-white bg-blue-500 rounded hover:bg-blue-600 transition-colors"
            >
              确定
            </button>
          </div>
        </Form.Item>
      </Form>
    </Modal>
  );
};
