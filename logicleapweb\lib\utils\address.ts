/**
 * 地址格式化工具函数
 */

/**
 * 格式化学校地址显示
 * @param province 省份
 * @param city 城市
 * @param district 区县
 * @returns 格式化后的地址字符串
 */
export function formatSchoolAddress(province: string, city?: string, district?: string): string {
  if (!province) return '';
  
  // 检查是否是直辖市
  const isMunicipality = ['北京市', '上海市', '天津市', '重庆市'].includes(province);
  
  // 去掉省份后缀
  const provinceName = province.replace(/[省市]$/, '');
  const cityName = city?.replace(/[市]$/, '') || '';
  const districtName = district || '';
  
  if (isMunicipality) {
    // 直辖市：只显示省份和区县
    return `${provinceName} ${districtName}`.trim();
  } else {
    // 普通省份：显示省份、城市、区县
    return `${provinceName} ${cityName} ${districtName}`.trim();
  }
}

/**
 * 检查是否是直辖市
 * @param province 省份名称
 * @returns 是否是直辖市
 */
export function isMunicipality(province: string): boolean {
  return ['北京市', '上海市', '天津市', '重庆市'].includes(province);
}
