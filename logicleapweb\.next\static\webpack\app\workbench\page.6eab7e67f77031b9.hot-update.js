"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/workbench/page",{

/***/ "(app-pages-browser)/./app/workbench/components/CourseListEditModal.tsx":
/*!**********************************************************!*\
  !*** ./app/workbench/components/CourseListEditModal.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FileText,Plus,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FileText,Plus,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FileText,Plus,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FileText,Plus,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FileText,Plus,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _lib_api_course_management__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api/course-management */ \"(app-pages-browser)/./lib/api/course-management.ts\");\n/* harmony import */ var _lib_api_course__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api/course */ \"(app-pages-browser)/./lib/api/course.ts\");\n/* harmony import */ var _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api/upload */ \"(app-pages-browser)/./lib/api/upload.ts\");\n/* harmony import */ var _lib_api_works__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/api/works */ \"(app-pages-browser)/./lib/api/works.ts\");\n/* harmony import */ var _barrel_optimize_names_Select_antd__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Select!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/select/index.js\");\n/* harmony import */ var logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\");\n/* harmony import */ var _TemplatePickerModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./TemplatePickerModal */ \"(app-pages-browser)/./app/workbench/components/TemplatePickerModal.tsx\");\n/* harmony import */ var _CourseListEditModal_css__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./CourseListEditModal.css */ \"(app-pages-browser)/./app/workbench/components/CourseListEditModal.css\");\n/* harmony import */ var _NewPublishTaskModal_css__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./NewPublishTaskModal.css */ \"(app-pages-browser)/./app/workbench/components/NewPublishTaskModal.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n// 获取系列课程详情\nconst fetchSeriesDetail = async (seriesId)=>{\n    console.log(\"\\uD83D\\uDD0D 获取系列课程详情，seriesId:\", seriesId);\n    console.log(\"\\uD83D\\uDD17 调用API: GET /api/v1/course-management/series/{seriesId}\");\n    const response = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getSeriesDetail(seriesId);\n    console.log(\"\\uD83D\\uDCE1 系列详情API响应:\", response);\n    return response.data;\n};\n// API调用函数 - 获取系列下的所有课程（包含未发布和已发布）\nconst fetchCourseList = async (seriesId)=>{\n    console.log(\"\\uD83D\\uDD0D 获取系列课程列表，seriesId:\", seriesId);\n    console.log(\"\\uD83D\\uDD17 调用API: GET /api/v1/course-management/series/{seriesId}/courses\");\n    // 使用课程管理API获取所有状态的课程\n    const response = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getManagementSeriesCourses(seriesId, {\n        page: 1,\n        pageSize: 100\n    });\n    console.log(\"\\uD83D\\uDCE1 API响应:\", response);\n    return response.data;\n};\n// 获取课程详情\nconst fetchCourseDetail = async (seriesId, courseId)=>{\n    return await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getCourseMarketplaceDetail(seriesId, courseId);\n};\n// 获取课程标签\nconst fetchCourseTags = async ()=>{\n    try {\n        console.log(\"\\uD83D\\uDD0D 开始调用 courseApi.getCourseTags\");\n        const result = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getCourseTags({\n            page: 1,\n            pageSize: 100,\n            status: 1 // 只获取启用的标签\n        });\n        console.log(\"\\uD83D\\uDD0D courseApi.getCourseTags 返回结果:\", result);\n        return result;\n    } catch (error) {\n        console.error(\"\\uD83D\\uDD0D courseApi.getCourseTags 调用失败:\", error);\n        throw error;\n    }\n};\nconst CourseListEditModal = (param)=>{\n    let { isVisible, onClose, onSave, seriesTitle, seriesCoverImage, seriesId = 123 // 默认值，实际使用时应该传入真实的seriesId\n     } = param;\n    var _getSelectedCourse, _getSelectedCourse1, _getSelectedCourse2, _getSelectedCourse3, _courseDetail_contentConfig_video, _courseDetail_contentConfig, _courseDetail_contentConfig_document, _courseDetail_contentConfig1, _courseList_find, _courseList_find1;\n    _s();\n    const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_6__.GetNotification)();\n    const [courseList, setCourseList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [rightPanelType, setRightPanelType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"none\");\n    const [selectedCourseId, setSelectedCourseId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [uploadingFiles, setUploadingFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [isPublishing, setIsPublishing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isCreating, setIsCreating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPublishingSeries, setIsPublishingSeries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [seriesStatus, setSeriesStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0); // 0=草稿，1=已发布，2=已归档\n    // 删除确认弹窗状态\n    const [deleteConfirmVisible, setDeleteConfirmVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [courseToDelete, setCourseToDelete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isDeleting, setIsDeleting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 模板选择弹窗状态\n    const [isTemplatePickerOpen, setIsTemplatePickerOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingTitle, setEditingTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(seriesTitle);\n    const [courseGoals, setCourseGoals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [courseObjectives, setCourseObjectives] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [projectMembers, setProjectMembers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // 作品相关状态\n    const [works, setWorks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loadingWorks, setLoadingWorks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedWorkIds, setSelectedWorkIds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // 懒加载相关状态\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [hasMoreWorks, setHasMoreWorks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [loadingMoreWorks, setLoadingMoreWorks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pageSize = 5; // 每次加载5个作品\n    // 课程标签相关状态\n    const [courseTags, setCourseTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedTags, setSelectedTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [tagsLoading, setTagsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 课程详细编辑状态\n    const [courseDetail, setCourseDetail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: \"\",\n        description: \"\",\n        coverImage: \"\",\n        videoUrl: \"\",\n        videoName: \"\",\n        isVideoEnabled: false,\n        attachmentUrl: \"\",\n        attachmentName: \"\",\n        isAttachmentEnabled: false,\n        teachingMaterials: [],\n        // 支持teachingInfo结构\n        teachingInfo: [],\n        // 支持contentConfig结构\n        contentConfig: {\n            hasVideo: 0,\n            hasDocument: 0,\n            hasAudio: 0,\n            video: {\n                url: \"\",\n                name: \"\"\n            },\n            document: {\n                url: \"\",\n                name: \"\"\n            },\n            audio: {\n                url: \"\",\n                name: \"\"\n            }\n        },\n        courseContent: {\n            topic: \"\",\n            content: \"\"\n        },\n        isOneKeyOpen: false,\n        isDistributionEnabled: false,\n        distributionReward: \"\",\n        selectedTemplate: \"\",\n        isDistributionWater: false,\n        requiredEnergy: \"\",\n        energyAmount: \"\",\n        isDistributionLimit: false,\n        distributionConditions: {\n            inviteCount: \"\",\n            taskCount: \"\",\n            experience: \"\"\n        },\n        isDistributionTime: false,\n        distributionTimeConditions: {\n            startTime: \"\",\n            endTime: \"\"\n        },\n        distributionMaterials: [],\n        // 任务配置相关状态\n        taskConfig: {\n            taskName: \"\",\n            taskDuration: \"\",\n            taskDescription: \"\",\n            selfAssessmentItems: [\n                \"\"\n            ],\n            referenceWorks: [],\n            referenceResources: []\n        }\n    });\n    // 获取课程列表数据\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isVisible && seriesId) {\n            // 检查用户登录状态\n            const token = localStorage.getItem(\"token\");\n            console.log(\"\\uD83D\\uDD10 检查登录状态，token存在:\", !!token);\n            console.log(\"\\uD83D\\uDD0D seriesId:\", seriesId);\n            if (!token) {\n                console.error(\"❌ 用户未登录，无法获取课程列表\");\n                // 设置空列表，显示空状态\n                setCourseList([]);\n                setLoading(false);\n                return;\n            }\n            loadCourseList();\n            loadCourseTags();\n            loadSeriesDetail();\n            loadUserWorks();\n        }\n    }, [\n        isVisible,\n        seriesId\n    ]);\n    const loadCourseList = async ()=>{\n        try {\n            setLoading(true);\n            console.log(\"\\uD83D\\uDD0D 开始加载课程列表，seriesId:\", seriesId);\n            const response = await fetchCourseList(seriesId);\n            console.log(\"\\uD83D\\uDCE1 API响应:\", response);\n            if (response.code === 200) {\n                console.log(\"✅ 课程列表数据:\", response.data);\n                const courses = response.data.list || [];\n                console.log(\"✅ 解析的课程数组:\", courses);\n                console.log(\"\\uD83D\\uDCCA 课程ID详情:\", courses.map((c)=>({\n                        id: c.id,\n                        type: typeof c.id,\n                        title: c.title,\n                        status: c.status\n                    })));\n                console.log(\"\\uD83D\\uDCCA 课程状态统计:\", {\n                    total: courses.length,\n                    draft: courses.filter((c)=>c.status === 0).length,\n                    published: courses.filter((c)=>c.status === 1).length\n                });\n                setCourseList(courses);\n            } else {\n                console.error(\"❌ API返回错误:\", response);\n                setCourseList([]);\n            }\n        } catch (error) {\n            var _error_response, _error_response1, _error_response2;\n            console.error(\"❌ 加载课程列表失败:\", error);\n            // 检查是否是认证错误\n            if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 401) {\n                console.error(\"\\uD83D\\uDD10 认证失败，用户未登录或token已过期\");\n            } else if (((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status) === 403) {\n                console.error(\"\\uD83D\\uDEAB 权限不足，无法访问该系列课程\");\n            } else if (((_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.status) === 404) {\n                console.error(\"\\uD83D\\uDCED 系列课程不存在，seriesId:\", seriesId);\n            } else {\n                console.error(\"\\uD83D\\uDD27 其他错误:\", error.message);\n            }\n            setCourseList([]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 加载系列课程详情\n    const loadSeriesDetail = async ()=>{\n        try {\n            console.log(\"\\uD83D\\uDD0D 开始加载系列课程详情，seriesId:\", seriesId);\n            const response = await fetchSeriesDetail(seriesId);\n            console.log(\"\\uD83D\\uDCE1 系列详情响应:\", response);\n            if (response.code === 200 && response.data) {\n                const seriesData = response.data;\n                console.log(\"✅ 系列课程详情:\", seriesData);\n                setSeriesStatus(seriesData.status || 0);\n                console.log(\"\\uD83D\\uDCCA 系列课程状态:\", seriesData.status, \"(0=草稿，1=已发布，2=已归档)\");\n            } else {\n                console.error(\"❌ 获取系列详情失败:\", response.message);\n            }\n        } catch (error) {\n            console.error(\"❌ 加载系列详情异常:\", error);\n        }\n    };\n    // 加载用户作品数据 - 支持分页\n    const loadUserWorks = async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1, append = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        try {\n            var _response_data;\n            if (append) {\n                setLoadingMoreWorks(true);\n            } else {\n                setLoadingWorks(true);\n                setWorks([]); // 重置作品列表\n                setCurrentPage(1);\n                setHasMoreWorks(true);\n            }\n            // 从localStorage获取用户ID\n            const userId = localStorage.getItem(\"userId\") || \"2896\"; // 默认使用2896\n            const response = await _lib_api_works__WEBPACK_IMPORTED_MODULE_5__.worksApi.getTeacherWorks(Number(userId), page, pageSize);\n            // 检查多种可能的数据结构\n            let worksList = [];\n            if (((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.code) === 200) {\n                var _response_data_data, _response_data1, _response_data2;\n                // 情况1: response.data.data.list\n                if ((_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : (_response_data_data = _response_data1.data) === null || _response_data_data === void 0 ? void 0 : _response_data_data.list) {\n                    worksList = response.data.data.list;\n                } else if (Array.isArray((_response_data2 = response.data) === null || _response_data2 === void 0 ? void 0 : _response_data2.data)) {\n                    worksList = response.data.data;\n                } else if (Array.isArray(response.data)) {\n                    worksList = response.data;\n                }\n            } else if (Array.isArray(response)) {\n                worksList = response;\n            }\n            // 根据是否追加来设置作品数据\n            if (append) {\n                setWorks((prev)=>[\n                        ...prev,\n                        ...worksList\n                    ]);\n            } else {\n                setWorks(worksList);\n            }\n            // 更新分页状态\n            setCurrentPage(page);\n            // 检查是否还有更多数据\n            if (worksList.length < pageSize) {\n                setHasMoreWorks(false);\n            }\n        } catch (error) {\n            console.error(\"加载作品数据失败:\", error);\n            if (!append) {\n                setWorks([]);\n            }\n        } finally{\n            setLoadingWorks(false);\n            setLoadingMoreWorks(false);\n        }\n    };\n    // 加载更多作品\n    const loadMoreWorks = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (!hasMoreWorks || loadingMoreWorks) return;\n        const nextPage = currentPage + 1;\n        await loadUserWorks(nextPage, true);\n    }, [\n        currentPage,\n        hasMoreWorks,\n        loadingMoreWorks\n    ]);\n    // 处理作品选择\n    const handleWorkSelect = (workId)=>{\n        if (selectedWorkIds.includes(workId)) {\n            // 取消选中\n            setSelectedWorkIds((prev)=>prev.filter((id)=>id !== workId));\n        } else {\n            // 选中\n            setSelectedWorkIds((prev)=>[\n                    ...prev,\n                    workId\n                ]);\n        }\n    };\n    // 滚动事件处理 - 检测是否需要加载更多\n    const handleWorksScroll = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e)=>{\n        const { scrollLeft, scrollWidth, clientWidth } = e.currentTarget;\n        // 当滚动到接近右边界时（距离右边界小于200px），触发懒加载\n        if (scrollWidth - scrollLeft - clientWidth < 200 && hasMoreWorks && !loadingMoreWorks) {\n            loadMoreWorks();\n        }\n    }, [\n        hasMoreWorks,\n        loadingMoreWorks,\n        loadMoreWorks\n    ]);\n    // 鼠标滚轮事件处理 - 将垂直滚轮转换为水平滚动\n    const handleWorksWheel = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e)=>{\n        // 阻止默认的垂直滚动\n        e.preventDefault();\n        const container = e.currentTarget;\n        const scrollAmount = e.deltaY; // 获取滚轮的垂直滚动量\n        // 将垂直滚动转换为水平滚动\n        container.scrollLeft += scrollAmount;\n    }, []);\n    // 加载课程标签\n    const loadCourseTags = async ()=>{\n        try {\n            setTagsLoading(true);\n            console.log(\"\\uD83C\\uDFF7️ 开始加载课程标签\");\n            const response = await fetchCourseTags();\n            console.log(\"\\uD83D\\uDCE1 标签API完整响应:\", response);\n            // 检查响应结构\n            if (response && response.data) {\n                console.log(\"\\uD83D\\uDCCA 响应数据:\", response.data);\n                let tags = [];\n                // 处理标准的API响应格式 (response.data.list) - 根据实际API响应\n                if (response.data.list && Array.isArray(response.data.list)) {\n                    tags = response.data.list;\n                    console.log(\"✅ 从 data.list 解析到标签:\", tags.length, \"个\");\n                } else if (Array.isArray(response.data)) {\n                    tags = response.data;\n                    console.log(\"✅ 从 data 数组解析到标签:\", tags.length, \"个\");\n                } else if (response.data.data && response.data.data.list && Array.isArray(response.data.data.list)) {\n                    tags = response.data.data.list;\n                    console.log(\"✅ 从 data.data.list 解析到标签:\", tags.length, \"个\");\n                }\n                // 验证标签数据格式\n                console.log(\"\\uD83D\\uDD0D 原始标签数据:\", tags);\n                console.log(\"\\uD83D\\uDD0D 标签数据类型检查:\");\n                tags.forEach((tag, index)=>{\n                    var _tag_name;\n                    console.log(\"标签\".concat(index, \":\"), {\n                        tag,\n                        hasTag: !!tag,\n                        idType: typeof (tag === null || tag === void 0 ? void 0 : tag.id),\n                        nameType: typeof (tag === null || tag === void 0 ? void 0 : tag.name),\n                        nameValue: tag === null || tag === void 0 ? void 0 : tag.name,\n                        nameNotEmpty: (tag === null || tag === void 0 ? void 0 : (_tag_name = tag.name) === null || _tag_name === void 0 ? void 0 : _tag_name.trim()) !== \"\"\n                    });\n                });\n                const validTags = tags.filter((tag)=>{\n                    const isValid = tag && typeof tag.id === \"number\" && typeof tag.name === \"string\" && tag.name.trim() !== \"\";\n                    if (!isValid) {\n                        console.log(\"❌ 无效标签:\", tag, {\n                            hasTag: !!tag,\n                            idType: typeof (tag === null || tag === void 0 ? void 0 : tag.id),\n                            nameType: typeof (tag === null || tag === void 0 ? void 0 : tag.name),\n                            nameValue: tag === null || tag === void 0 ? void 0 : tag.name\n                        });\n                    }\n                    return isValid;\n                });\n                console.log(\"✅ 有效标签数量:\", validTags.length);\n                console.log(\"✅ 有效标签详情:\", validTags);\n                if (validTags.length > 0) {\n                    setCourseTags(validTags);\n                    console.log(\"✅ 成功设置真实标签数据\");\n                    return;\n                } else {\n                    console.warn(\"⚠️ 没有有效的标签数据\");\n                }\n            } else {\n                console.warn(\"⚠️ API响应格式不正确:\", response);\n            }\n            // 如果没有真实数据，设置空数组\n            console.log(\"\\uD83D\\uDCED 没有标签数据，设置空数组\");\n            setCourseTags([]);\n        } catch (error) {\n            var _error_response, _error_response1, _error_response2;\n            console.error(\"❌ 加载课程标签失败:\", error);\n            console.error(\"❌ 错误详情:\", {\n                message: error.message,\n                status: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status,\n                statusText: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.statusText,\n                data: (_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.data\n            });\n            // 发生错误时设置空数组\n            setCourseTags([]);\n        } finally{\n            setTagsLoading(false);\n        }\n    };\n    // 添加新课程\n    const addNewCourse = ()=>{\n        const newCourse = {\n            id: Date.now(),\n            seriesId: seriesId,\n            title: \"第\".concat(courseList.length + 1, \"课 - 新课时\"),\n            description: \"\",\n            coverImage: \"\",\n            orderIndex: courseList.length + 1,\n            status: 0,\n            statusLabel: \"草稿\",\n            hasVideo: 0,\n            hasDocument: 0,\n            hasAudio: 0,\n            videoDuration: 0,\n            videoDurationLabel: \"\",\n            videoName: \"\",\n            firstTeachingTitle: \"\",\n            resourcesCount: 0,\n            createdAt: new Date().toISOString(),\n            updatedAt: new Date().toISOString()\n        };\n        setCourseList([\n            ...courseList,\n            newCourse\n        ]);\n        // 自动选中新添加的课程\n        showCoursePanel(newCourse.id);\n    };\n    // 显示删除确认弹窗\n    const showDeleteConfirm = (id)=>{\n        setCourseToDelete(id);\n        setDeleteConfirmVisible(true);\n    };\n    // 确认删除课程\n    const confirmDeleteCourse = async ()=>{\n        if (!courseToDelete) return;\n        try {\n            setIsDeleting(true);\n            // 调用删除API\n            await _lib_api_course_management__WEBPACK_IMPORTED_MODULE_2__.courseManagementApi.deleteCourse(courseToDelete);\n            // 从列表中移除课程\n            setCourseList(courseList.filter((course)=>course.id !== courseToDelete));\n            // 如果删除的是当前选中的课程，清空右侧面板\n            if (selectedCourseId === courseToDelete) {\n                setRightPanelType(\"none\");\n                setSelectedCourseId(null);\n            }\n            // 关闭确认弹窗\n            setDeleteConfirmVisible(false);\n            setCourseToDelete(null);\n            // 显示成功提示\n            notification.success(\"课程已成功删除\");\n        } catch (error) {\n            console.error(\"删除课程失败:\", error);\n            notification.error(\"删除课程失败，请重试\");\n        } finally{\n            setIsDeleting(false);\n        }\n    };\n    // 取消删除\n    const cancelDelete = ()=>{\n        if (isDeleting) return; // 正在删除时不允许取消\n        setDeleteConfirmVisible(false);\n        setCourseToDelete(null);\n    };\n    // 更新课程标题\n    const updateCourseTitle = (id, newTitle)=>{\n        setCourseList(courseList.map((course)=>course.id === id ? {\n                ...course,\n                title: newTitle\n            } : course));\n    };\n    // 处理课程封面上传\n    const handleCoverUpload = async (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (file) {\n            // 检查文件类型\n            const allowedTypes = [\n                \"image/jpeg\",\n                \"image/jpg\",\n                \"image/png\",\n                \"image/gif\"\n            ];\n            if (!allowedTypes.includes(file.type)) {\n                notification.error(\"请选择 JPG、PNG 或 GIF 格式的图片文件\");\n                return;\n            }\n            // 检查文件大小 (10MB)\n            if (file.size > 10 * 1024 * 1024) {\n                notification.error(\"文件大小不能超过 10MB\");\n                return;\n            }\n            try {\n                console.log(\"\\uD83D\\uDCE4 开始上传课程封面:\", file.name);\n                // 添加到上传中的文件列表\n                setUploadingFiles((prev)=>new Set(prev).add(\"cover\"));\n                // 先显示预览图片\n                const previewUrl = URL.createObjectURL(file);\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        coverImage: previewUrl\n                    }));\n                // 上传到OSS\n                const imageUrl = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n                console.log(\"✅ 课程封面上传成功:\", imageUrl);\n                // 更新课程详情中的封面为真实URL\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        coverImage: imageUrl\n                    }));\n                // 同时更新课程列表中的封面\n                if (selectedCourseId) {\n                    setCourseList((prev)=>prev.map((course)=>course.id === selectedCourseId ? {\n                                ...course,\n                                coverImage: imageUrl\n                            } : course));\n                }\n                alert(\"课程封面上传成功！\");\n            } catch (error) {\n                console.error(\"❌ 课程封面上传失败:\", error);\n                notification.error(\"课程封面上传失败，请重试\");\n                // 上传失败时清除预览图片\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        coverImage: \"\"\n                    }));\n            } finally{\n                // 从上传中的文件列表移除\n                setUploadingFiles((prev)=>{\n                    const newSet = new Set(prev);\n                    newSet.delete(\"cover\");\n                    return newSet;\n                });\n            }\n        }\n    };\n    // 处理视频上传\n    const handleVideoUpload = async (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (file) {\n            // 检查文件类型\n            const allowedTypes = [\n                \"video/mp4\",\n                \"video/avi\",\n                \"video/mov\",\n                \"video/wmv\",\n                \"video/flv\"\n            ];\n            if (!allowedTypes.includes(file.type)) {\n                alert(\"请选择 MP4、AVI、MOV、WMV 或 FLV 格式的视频文件\");\n                return;\n            }\n            // 检查文件大小 (100MB)\n            if (file.size > 100 * 1024 * 1024) {\n                alert(\"视频文件大小不能超过 100MB\");\n                return;\n            }\n            try {\n                console.log(\"\\uD83D\\uDCE4 开始上传课程视频:\", file.name);\n                // 添加到上传中的文件列表\n                setUploadingFiles((prev)=>new Set(prev).add(\"video\"));\n                // 先显示预览视频\n                const previewUrl = URL.createObjectURL(file);\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        contentConfig: {\n                            ...prev.contentConfig,\n                            video: {\n                                url: previewUrl,\n                                name: file.name\n                            }\n                        }\n                    }));\n                // 上传到OSS\n                const videoUrl = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n                console.log(\"✅ 课程视频上传成功:\", videoUrl);\n                // 更新课程详情中的视频信息为真实URL\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        contentConfig: {\n                            ...prev.contentConfig,\n                            video: {\n                                url: videoUrl,\n                                name: file.name\n                            }\n                        }\n                    }));\n                // 同时更新课程列表中的视频信息\n                if (selectedCourseId) {\n                    setCourseList((prev)=>prev.map((course)=>course.id === selectedCourseId ? {\n                                ...course,\n                                contentConfig: {\n                                    ...course.contentConfig,\n                                    video: {\n                                        url: videoUrl,\n                                        name: file.name\n                                    }\n                                }\n                            } : course));\n                }\n                alert(\"课程视频上传成功！\");\n            } catch (error) {\n                console.error(\"❌ 课程视频上传失败:\", error);\n                alert(\"课程视频上传失败，请重试\");\n                // 上传失败时清除视频信息\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        contentConfig: {\n                            ...prev.contentConfig,\n                            video: {\n                                url: \"\",\n                                name: \"\"\n                            }\n                        }\n                    }));\n            } finally{\n                // 从上传中的文件列表移除\n                setUploadingFiles((prev)=>{\n                    const newSet = new Set(prev);\n                    newSet.delete(\"video\");\n                    return newSet;\n                });\n            }\n        }\n    };\n    // 触发视频文件选择\n    const triggerVideoUpload = ()=>{\n        const input = document.createElement(\"input\");\n        input.type = \"file\";\n        input.accept = \"video/mp4,video/avi,video/mov,video/wmv,video/flv\";\n        input.onchange = (e)=>handleVideoUpload(e);\n        input.click();\n    };\n    // 处理附件上传\n    const handleAttachmentUpload = async (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (file) {\n            // 检查文件类型\n            const allowedTypes = [\n                \"application/pdf\",\n                \"application/msword\",\n                \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\",\n                \"application/vnd.ms-excel\",\n                \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\",\n                \"application/vnd.ms-powerpoint\",\n                \"application/vnd.openxmlformats-officedocument.presentationml.presentation\",\n                \"text/plain\",\n                \"image/jpeg\",\n                \"image/png\",\n                \"image/gif\"\n            ];\n            if (!allowedTypes.includes(file.type)) {\n                alert(\"请选择支持的文件格式：PDF、DOC、DOCX、XLS、XLSX、PPT、PPTX、TXT、JPG、PNG、GIF\");\n                return;\n            }\n            // 检查文件大小 (10MB)\n            if (file.size > 10 * 1024 * 1024) {\n                alert(\"文件大小不能超过 10MB\");\n                return;\n            }\n            try {\n                console.log(\"\\uD83D\\uDCE4 开始上传课程附件:\", file.name);\n                // 添加到上传中的文件列表\n                setUploadingFiles((prev)=>new Set(prev).add(\"document\"));\n                // 上传到OSS\n                const documentUrl = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n                console.log(\"✅ 课程附件上传成功:\", documentUrl);\n                // 更新课程详情中的附件信息\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        contentConfig: {\n                            ...prev.contentConfig,\n                            hasDocument: 1,\n                            document: {\n                                url: documentUrl,\n                                name: file.name\n                            }\n                        }\n                    }));\n                alert(\"课程附件上传成功！\");\n            } catch (error) {\n                console.error(\"❌ 课程附件上传失败:\", error);\n                alert(\"课程附件上传失败，请重试\");\n            } finally{\n                // 从上传中的文件列表移除\n                setUploadingFiles((prev)=>{\n                    const newSet = new Set(prev);\n                    newSet.delete(\"document\");\n                    return newSet;\n                });\n            }\n        }\n    };\n    // 触发附件文件选择\n    const triggerAttachmentUpload = ()=>{\n        const input = document.createElement(\"input\");\n        input.type = \"file\";\n        input.accept = \".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.jpg,.png,.gif\";\n        input.onchange = (e)=>handleAttachmentUpload(e);\n        input.click();\n    };\n    // 处理教学附件上传\n    const handleTeachingMaterialUpload = async (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (file) {\n            // 检查文件类型\n            const allowedTypes = [\n                \"application/pdf\",\n                \"application/msword\",\n                \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\",\n                \"application/vnd.ms-excel\",\n                \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\",\n                \"application/vnd.ms-powerpoint\",\n                \"application/vnd.openxmlformats-officedocument.presentationml.presentation\",\n                \"text/plain\",\n                \"image/jpeg\",\n                \"image/png\",\n                \"image/gif\"\n            ];\n            if (!allowedTypes.includes(file.type)) {\n                alert(\"请选择支持的文件格式：PDF、DOC、DOCX、XLS、XLSX、PPT、PPTX、TXT、JPG、PNG、GIF\");\n                return;\n            }\n            // 检查文件大小 (10MB)\n            if (file.size > 10 * 1024 * 1024) {\n                alert(\"文件大小不能超过 10MB\");\n                return;\n            }\n            try {\n                console.log(\"\\uD83D\\uDCE4 开始上传教学材料:\", file.name);\n                // 添加到上传中的文件列表\n                setUploadingFiles((prev)=>new Set(prev).add(\"teaching-\".concat(Date.now())));\n                // 上传到OSS\n                const materialUrl = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n                console.log(\"✅ 教学材料上传成功:\", materialUrl);\n                // 添加到教学附件列表\n                const newMaterial = {\n                    type: file.type,\n                    name: file.name,\n                    url: materialUrl\n                };\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        teachingMaterials: [\n                            ...prev.teachingMaterials,\n                            newMaterial\n                        ]\n                    }));\n                alert(\"教学材料上传成功！\");\n            } catch (error) {\n                console.error(\"❌ 教学材料上传失败:\", error);\n                alert(\"教学材料上传失败，请重试\");\n            } finally{\n                // 从上传中的文件列表移除\n                setUploadingFiles((prev)=>{\n                    const newSet = new Set(prev);\n                    // 移除所有teaching-开头的项目\n                    Array.from(newSet).forEach((item)=>{\n                        if (item.startsWith(\"teaching-\")) {\n                            newSet.delete(item);\n                        }\n                    });\n                    return newSet;\n                });\n            }\n        }\n    };\n    // 触发教学附件文件选择\n    const triggerTeachingMaterialUpload = ()=>{\n        const input = document.createElement(\"input\");\n        input.type = \"file\";\n        input.accept = \".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.jpg,.png,.gif\";\n        input.onchange = (e)=>handleTeachingMaterialUpload(e);\n        input.click();\n    };\n    // 删除教学附件\n    const removeTeachingMaterial = (index)=>{\n        setCourseDetail((prev)=>({\n                ...prev,\n                teachingMaterials: prev.teachingMaterials.filter((_, i)=>i !== index)\n            }));\n    };\n    // 课程内容管理函数\n    const addTeachingInfoItem = ()=>{\n        setCourseDetail((prev)=>({\n                ...prev,\n                teachingInfo: [\n                    ...prev.teachingInfo,\n                    {\n                        title: \"\",\n                        content: \"\"\n                    }\n                ]\n            }));\n    };\n    const removeTeachingInfoItem = (index)=>{\n        setCourseDetail((prev)=>({\n                ...prev,\n                teachingInfo: prev.teachingInfo.filter((_, i)=>i !== index)\n            }));\n    };\n    const updateTeachingInfoTitle = (index, title)=>{\n        setCourseDetail((prev)=>{\n            const newTeachingInfo = [\n                ...prev.teachingInfo\n            ];\n            newTeachingInfo[index] = {\n                ...newTeachingInfo[index],\n                title\n            };\n            return {\n                ...prev,\n                teachingInfo: newTeachingInfo\n            };\n        });\n    };\n    const updateTeachingInfoContent = (index, content)=>{\n        setCourseDetail((prev)=>{\n            const newTeachingInfo = [\n                ...prev.teachingInfo\n            ];\n            newTeachingInfo[index] = {\n                ...newTeachingInfo[index],\n                content\n            };\n            return {\n                ...prev,\n                teachingInfo: newTeachingInfo\n            };\n        });\n    };\n    // 将UI格式的teachingInfo转换为API格式\n    const convertTeachingInfoForAPI = (teachingInfo)=>{\n        return teachingInfo.map((info)=>({\n                title: info.title,\n                content: info.content.split(\"\\n\").filter((line)=>line.trim()) // 按换行分割并过滤空行\n            }));\n    };\n    // 检查是否有未上传完成的文件（blob URL）\n    const checkForBlobUrls = (course)=>{\n        var _course_contentConfig_video, _course_contentConfig, _course_contentConfig_document, _course_contentConfig1;\n        const issues = [];\n        // 检查封面图片\n        if (course.coverImage && course.coverImage.startsWith(\"blob:\")) {\n            issues.push(\"课程封面图片\");\n        }\n        // 检查视频文件\n        if (((_course_contentConfig = course.contentConfig) === null || _course_contentConfig === void 0 ? void 0 : (_course_contentConfig_video = _course_contentConfig.video) === null || _course_contentConfig_video === void 0 ? void 0 : _course_contentConfig_video.url) && course.contentConfig.video.url.startsWith(\"blob:\")) {\n            issues.push(\"课程视频\");\n        }\n        // 检查文档附件\n        if (((_course_contentConfig1 = course.contentConfig) === null || _course_contentConfig1 === void 0 ? void 0 : (_course_contentConfig_document = _course_contentConfig1.document) === null || _course_contentConfig_document === void 0 ? void 0 : _course_contentConfig_document.url) && course.contentConfig.document.url.startsWith(\"blob:\")) {\n            issues.push(\"课程文档\");\n        }\n        // 检查教学材料\n        if (course.additionalResources) {\n            course.additionalResources.forEach((resource, index)=>{\n                if (resource.url && resource.url.startsWith(\"blob:\")) {\n                    issues.push(\"教学材料\".concat(index + 1));\n                }\n            });\n        }\n        return issues;\n    };\n    // 保存单个课程到后端\n    const saveCourse = async (course)=>{\n        try {\n            var _course_contentConfig_video, _course_contentConfig, _course_contentConfig_document, _course_contentConfig1;\n            // 验证必要的数据\n            if (!course.title || course.title.trim() === \"\") {\n                throw new Error(\"课程标题不能为空\");\n            }\n            // 检查是否有未上传完成的文件\n            const blobIssues = checkForBlobUrls(course);\n            if (blobIssues.length > 0) {\n                throw new Error(\"以下文件尚未上传完成，请等待上传完成后再保存：\".concat(blobIssues.join(\"、\")));\n            }\n            // 准备课程数据\n            const courseData = {\n                seriesId: seriesId,\n                title: course.title,\n                description: course.description || \"\",\n                coverImage: course.coverImage || \"\",\n                hasVideo: ((_course_contentConfig = course.contentConfig) === null || _course_contentConfig === void 0 ? void 0 : (_course_contentConfig_video = _course_contentConfig.video) === null || _course_contentConfig_video === void 0 ? void 0 : _course_contentConfig_video.url) ? 1 : 0,\n                hasDocument: ((_course_contentConfig1 = course.contentConfig) === null || _course_contentConfig1 === void 0 ? void 0 : (_course_contentConfig_document = _course_contentConfig1.document) === null || _course_contentConfig_document === void 0 ? void 0 : _course_contentConfig_document.url) ? 1 : 0,\n                hasAudio: 0,\n                videoDuration: 0,\n                contentConfig: course.contentConfig || {},\n                teachingInfo: convertTeachingInfoForAPI(course.teachingInfo || []),\n                additionalResources: course.additionalResources || [],\n                orderIndex: course.orderIndex || 1\n            };\n            console.log(\"\\uD83D\\uDCBE 准备保存课程数据:\", courseData);\n            let result;\n            // 判断是新课程还是更新课程\n            if (course.id && course.id > 1000000) {\n                // 新课程，使用创建API\n                console.log(\"\\uD83D\\uDCE4 创建新课程\");\n                const { data: response } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.createCourse(courseData);\n                result = response;\n                console.log(\"✅ 课程创建成功\");\n            } else {\n                // 现有课程，使用更新API\n                console.log(\"\\uD83D\\uDCE4 更新现有课程:\", course.id);\n                const { data: response } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.updateCourse(course.id, courseData);\n                result = response;\n                console.log(\"✅ 课程更新成功\");\n            }\n            return result;\n        } catch (error) {\n            console.error(\"❌ 保存课程失败:\", error);\n            throw error;\n        }\n    };\n    // 发布选中的课程\n    const handlePublishCourse = async ()=>{\n        if (!selectedCourseId) {\n            alert(\"请先选择要发布的课程\");\n            return;\n        }\n        const selectedCourse = courseList.find((course)=>course.id === selectedCourseId);\n        if (!selectedCourse) {\n            alert(\"未找到选中的课程\");\n            return;\n        }\n        // 检查课程是否已经发布\n        if (selectedCourse.status === 1) {\n            alert(\"该课程已经发布，无需重复发布\");\n            return;\n        }\n        try {\n            setIsPublishing(true);\n            console.log(\"\\uD83D\\uDCE4 开始发布课程\");\n            console.log(\"\\uD83D\\uDCE4 selectedCourseId:\", selectedCourseId, \"类型:\", typeof selectedCourseId);\n            console.log(\"\\uD83D\\uDCE4 课程信息:\", selectedCourse);\n            console.log(\"\\uD83D\\uDCE4 课程ID字段:\", selectedCourse.id, \"类型:\", typeof selectedCourse.id);\n            console.log(\"\\uD83D\\uDCE4 课程列表中的所有ID:\", courseList.map((c)=>({\n                    id: c.id,\n                    type: typeof c.id,\n                    title: c.title\n                })));\n            // 确保使用正确的课程ID\n            const courseIdToPublish = selectedCourse.id;\n            console.log(\"\\uD83D\\uDCE4 即将发布的课程ID:\", courseIdToPublish, \"类型:\", typeof courseIdToPublish);\n            const { data: response } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.publishCourse(courseIdToPublish);\n            if (response.code === 200) {\n                console.log(\"✅ 课程发布成功:\", response.data);\n                alert(\"课程发布成功！\");\n                // 刷新课程列表以更新状态\n                await loadCourseList();\n            } else {\n                console.error(\"❌ 发布课程失败:\", response.message);\n                alert(response.message || \"发布课程失败\");\n            }\n        } catch (error) {\n            var _error_response, _error_response_data, _error_response1;\n            console.error(\"❌ 发布课程失败:\", error);\n            console.error(\"❌ 错误详情:\", (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data);\n            // 处理具体的错误信息\n            if ((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : (_error_response_data = _error_response1.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) {\n                alert(error.response.data.message);\n            } else if (error.message) {\n                alert(error.message);\n            } else {\n                alert(\"发布课程失败，请重试\");\n            }\n        } finally{\n            setIsPublishing(false);\n        }\n    };\n    // 保存课程列表\n    const handleSave = async ()=>{\n        try {\n            setIsCreating(true);\n            // 检查是否有文件正在上传\n            if (uploadingFiles.size > 0) {\n                alert(\"有文件正在上传中，请等待上传完成后再保存\");\n                setIsCreating(false);\n                return;\n            }\n            console.log(\"\\uD83D\\uDCBE 开始保存课程列表\");\n            // 如果有选中的课程且在编辑状态，先保存当前课程\n            if (selectedCourseId && rightPanelType === \"course\") {\n                const selectedCourse = courseList.find((c)=>c.id === selectedCourseId);\n                if (selectedCourse) {\n                    var _courseDetail_teachingMaterials;\n                    // 更新课程数据\n                    const updatedCourse = {\n                        ...selectedCourse,\n                        title: courseDetail.title,\n                        description: courseDetail.description,\n                        coverImage: courseDetail.coverImage,\n                        hasVideo: courseDetail.isVideoEnabled ? 1 : 0,\n                        hasDocument: courseDetail.isAttachmentEnabled ? 1 : 0,\n                        contentConfig: courseDetail.contentConfig,\n                        teachingInfo: courseDetail.teachingInfo,\n                        additionalResources: ((_courseDetail_teachingMaterials = courseDetail.teachingMaterials) === null || _courseDetail_teachingMaterials === void 0 ? void 0 : _courseDetail_teachingMaterials.map((material)=>({\n                                title: material.name,\n                                url: material.url,\n                                description: material.name\n                            }))) || []\n                    };\n                    // 先更新课程列表中的数据\n                    setCourseList((prev)=>prev.map((course)=>course.id === selectedCourseId ? updatedCourse : course));\n                    // 然后保存到后端\n                    await saveCourse(updatedCourse);\n                }\n            }\n            // 保存系列课程信息\n            const data = {\n                title: editingTitle,\n                courseGoals,\n                courseObjectives,\n                courseList\n            };\n            onSave(data);\n            onClose();\n            console.log(\"✅ 课程列表保存完成\");\n        } catch (error) {\n            console.error(\"❌ 保存失败:\", error);\n            alert(\"保存失败: \".concat(error.message || \"请重试\"));\n        } finally{\n            setIsCreating(false);\n        }\n    };\n    // 发布系列课程\n    const handlePublish = async ()=>{\n        // 如果系列已发布，不执行任何操作\n        if (seriesStatus === 1) {\n            return;\n        }\n        try {\n            setIsPublishingSeries(true);\n            // 检查是否有课程\n            if (courseList.length === 0) {\n                alert(\"发布失败：课程系列中至少需要包含一个课程\");\n                return;\n            }\n            console.log(\"\\uD83D\\uDCE4 开始发布系列课程，系列ID:\", seriesId);\n            const { data: response } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.publishCourseSeries(seriesId);\n            if (response.code === 200) {\n                console.log(\"✅ 系列课程发布成功:\", response.data);\n                // 构建成功消息\n                const publishData = response.data;\n                let successMessage = '系列课程\"'.concat(publishData.title, '\"发布成功！');\n                // 如果有发布统计信息，添加到消息中\n                if (publishData.publishStats) {\n                    const stats = publishData.publishStats;\n                    successMessage += \"\\n\\n发布统计：\\n• 总课程数：\".concat(publishData.totalCourses, \"\\n• 已发布课程：\").concat(publishData.publishedCourses, \"\\n• 视频课程：\").concat(stats.videoCourseCount, \"个\\n• 文档课程：\").concat(stats.documentCourseCount, \"个\\n• 总资源数：\").concat(stats.totalResourcesCount, \"个\");\n                    if (stats.totalVideoDuration > 0) {\n                        const durationMinutes = Math.round(stats.totalVideoDuration / 60);\n                        successMessage += \"\\n• 视频总时长：\".concat(durationMinutes, \"分钟\");\n                    }\n                }\n                alert(successMessage);\n                // 更新系列状态为已发布\n                setSeriesStatus(1);\n                // 刷新课程列表以更新状态\n                await loadCourseList();\n                // 通知父组件刷新数据\n                onSave({\n                    type: \"publish_series\",\n                    seriesId: seriesId,\n                    message: \"系列课程发布成功\"\n                });\n            } else {\n                console.error(\"❌ 发布系列课程失败:\", response.message);\n                alert(response.message || \"发布系列课程失败\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"❌ 发布系列课程出错:\", error);\n            // 处理具体的错误信息\n            let errorMessage = \"发布系列课程失败\";\n            if ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) {\n                errorMessage = error.response.data.message;\n            } else if (error.message) {\n                errorMessage = error.message;\n            }\n            alert(errorMessage);\n        } finally{\n            setIsPublishingSeries(false);\n        }\n    };\n    // 退出编辑模式 - 保存数据并关闭\n    const handleExitEdit = ()=>{\n        handleSave();\n    };\n    // 显示设置面板\n    const showSettingsPanel = ()=>{\n        setRightPanelType(\"settings\");\n        setSelectedCourseId(null);\n    };\n    // 处理模板选择\n    const handleTemplateSelect = (template)=>{\n        setCourseDetail((prev)=>({\n                ...prev,\n                selectedTemplate: template.templateName\n            }));\n        setIsTemplatePickerOpen(false);\n    };\n    // 打开模板选择弹窗\n    const handleOpenTemplatePicker = ()=>{\n        setIsTemplatePickerOpen(true);\n    };\n    // 显示课程编辑面板\n    const showCoursePanel = async (courseId)=>{\n        setRightPanelType(\"course\");\n        setSelectedCourseId(courseId);\n        // 如果作品数据还没有加载，重新加载\n        if (works.length === 0 && !loadingWorks) {\n            loadUserWorks();\n        }\n        // 获取选中的课程并更新courseDetail状态\n        const selectedCourse = courseList.find((course)=>course.id === courseId);\n        console.log(\"\\uD83C\\uDFAF 找到的课程:\", selectedCourse);\n        if (selectedCourse) {\n            try {\n                console.log(\"\\uD83D\\uDD04 获取课程详情，seriesId:\", seriesId, \"courseId:\", courseId);\n                // 获取真实的课程详情数据\n                const { data: res } = await fetchCourseDetail(seriesId, courseId);\n                if (res.code === 200 && res.data) {\n                    var _courseDetailData_additionalResources, _courseDetailData_teachingInfo;\n                    const courseDetailData = res.data;\n                    console.log(\"✅ 获取到课程详情:\", courseDetailData);\n                    // 将真实的 additionalResources 映射到 teachingMaterials\n                    const teachingMaterials = ((_courseDetailData_additionalResources = courseDetailData.additionalResources) === null || _courseDetailData_additionalResources === void 0 ? void 0 : _courseDetailData_additionalResources.map((resource)=>({\n                            type: \"application/octet-stream\",\n                            name: resource.title || resource.name || \"附件\",\n                            url: resource.url\n                        }))) || [];\n                    // 将API返回的teachingInfo数组格式转换为新的格式\n                    const mappedTeachingInfo = ((_courseDetailData_teachingInfo = courseDetailData.teachingInfo) === null || _courseDetailData_teachingInfo === void 0 ? void 0 : _courseDetailData_teachingInfo.map((info)=>({\n                            title: info.title || \"\",\n                            content: Array.isArray(info.content) ? info.content.join(\"\\n\") : info.content || \"\"\n                        }))) || [];\n                    console.log(\"\\uD83D\\uDCCE 映射的教学附件:\", teachingMaterials);\n                    console.log(\"\\uD83D\\uDCDA 映射的教学信息:\", mappedTeachingInfo);\n                    setCourseDetail((prev)=>{\n                        var _courseDetailData_contentConfig_video, _courseDetailData_contentConfig, _courseDetailData_contentConfig_video1, _courseDetailData_contentConfig1, _courseDetailData_contentConfig_document, _courseDetailData_contentConfig2, _courseDetailData_contentConfig_document1, _courseDetailData_contentConfig3;\n                        return {\n                            ...prev,\n                            title: courseDetailData.title,\n                            description: courseDetailData.description,\n                            coverImage: courseDetailData.coverImage || \"\",\n                            isVideoEnabled: courseDetailData.hasVideo === 1,\n                            isAttachmentEnabled: courseDetailData.hasDocument === 1,\n                            contentConfig: courseDetailData.contentConfig || {},\n                            teachingInfo: mappedTeachingInfo,\n                            teachingMaterials: teachingMaterials,\n                            videoUrl: ((_courseDetailData_contentConfig = courseDetailData.contentConfig) === null || _courseDetailData_contentConfig === void 0 ? void 0 : (_courseDetailData_contentConfig_video = _courseDetailData_contentConfig.video) === null || _courseDetailData_contentConfig_video === void 0 ? void 0 : _courseDetailData_contentConfig_video.url) || \"\",\n                            videoName: ((_courseDetailData_contentConfig1 = courseDetailData.contentConfig) === null || _courseDetailData_contentConfig1 === void 0 ? void 0 : (_courseDetailData_contentConfig_video1 = _courseDetailData_contentConfig1.video) === null || _courseDetailData_contentConfig_video1 === void 0 ? void 0 : _courseDetailData_contentConfig_video1.name) || \"\",\n                            attachmentUrl: ((_courseDetailData_contentConfig2 = courseDetailData.contentConfig) === null || _courseDetailData_contentConfig2 === void 0 ? void 0 : (_courseDetailData_contentConfig_document = _courseDetailData_contentConfig2.document) === null || _courseDetailData_contentConfig_document === void 0 ? void 0 : _courseDetailData_contentConfig_document.url) || \"\",\n                            attachmentName: ((_courseDetailData_contentConfig3 = courseDetailData.contentConfig) === null || _courseDetailData_contentConfig3 === void 0 ? void 0 : (_courseDetailData_contentConfig_document1 = _courseDetailData_contentConfig3.document) === null || _courseDetailData_contentConfig_document1 === void 0 ? void 0 : _courseDetailData_contentConfig_document1.name) || \"\"\n                        };\n                    });\n                } else {\n                    console.error(\"❌ 获取课程详情失败:\", res.message);\n                    // 使用基础数据作为后备\n                    setCourseDetail((prev)=>({\n                            ...prev,\n                            title: selectedCourse.title,\n                            description: selectedCourse.description,\n                            coverImage: selectedCourse.coverImage || \"\",\n                            isVideoEnabled: selectedCourse.hasVideo === 1,\n                            isAttachmentEnabled: selectedCourse.hasDocument === 1,\n                            teachingMaterials: [] // 清空附件列表\n                        }));\n                }\n            } catch (error) {\n                console.error(\"❌ 获取课程详情异常:\", error);\n                // 使用基础数据作为后备\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        title: selectedCourse.title,\n                        description: selectedCourse.description,\n                        coverImage: selectedCourse.coverImage || \"\",\n                        isVideoEnabled: selectedCourse.hasVideo === 1,\n                        isAttachmentEnabled: selectedCourse.hasDocument === 1,\n                        teachingMaterials: [] // 清空附件列表\n                    }));\n            }\n        }\n    };\n    // 获取选中的课程\n    const getSelectedCourse = ()=>{\n        return courseList.find((course)=>course.id === selectedCourseId);\n    };\n    if (!isVisible) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"course-list-modal-overlay\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"course-list-modal\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"course-list-header\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"course-list-title-section\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"course-list-title\",\n                                        children: \"课程列表\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 1384,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"course-list-actions\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: showSettingsPanel,\n                                                className: \"course-list-settings-btn \".concat(rightPanelType === \"settings\" ? \"active\" : \"\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1390,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1386,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: addNewCourse,\n                                                className: \"course-list-add-btn\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1393,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1392,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 1385,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                lineNumber: 1383,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onClose,\n                                className: \"course-list-close-btn\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 1398,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                lineNumber: 1397,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                        lineNumber: 1382,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"course-list-content\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"course-list-sidebar\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"course-list-items\",\n                                    children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"course-list-loading\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"加载中...\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 1409,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 1408,\n                                        columnNumber: 17\n                                    }, undefined) : courseList.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"course-list-empty\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"course-list-empty-icon\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"w-12 h-12 text-gray-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1414,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1413,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"course-list-empty-title\",\n                                                children: \"暂无课时\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1416,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"course-list-empty-description\",\n                                                children: \"点击右上角的 + 按钮添加第一个课时\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1417,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: addNewCourse,\n                                                className: \"course-list-empty-btn\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1424,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    \"添加课时\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1420,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 1412,\n                                        columnNumber: 17\n                                    }, undefined) : courseList.map((course)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"course-list-item \".concat(selectedCourseId === course.id ? \"active\" : \"\"),\n                                            onClick: ()=>showCoursePanel(course.id),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-list-item-content\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"course-list-item-text\",\n                                                            children: course.title\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1436,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"course-status-badge \".concat(course.status === 1 ? \"published\" : \"draft\"),\n                                                            children: course.status === 1 ? \"已发布\" : \"未发布\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1437,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1435,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: (e)=>{\n                                                        e.stopPropagation();\n                                                        showDeleteConfirm(course.id);\n                                                    },\n                                                    className: \"course-list-item-delete\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"w-3 h-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1448,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1441,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, course.id, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 1430,\n                                            columnNumber: 19\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 1406,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                lineNumber: 1405,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"course-list-edit-area\",\n                                children: [\n                                    rightPanelType === \"none\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"course-edit-empty\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"course-edit-empty-icon\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"w-16 h-16 text-gray-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1461,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1460,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"course-edit-empty-title\",\n                                                children: \"无课程详情\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1463,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"course-edit-empty-description\",\n                                                children: \"点击左侧课程或设置按钮查看详情\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1464,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 1459,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    rightPanelType === \"settings\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"course-series-cover\",\n                                                children: seriesCoverImage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: seriesCoverImage,\n                                                    alt: \"系列课程封面\",\n                                                    className: \"course-series-cover-image\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1475,\n                                                    columnNumber: 21\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-series-cover-placeholder\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"系列课程封面\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1482,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1481,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1473,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"course-edit-form\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"course-edit-field\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"course-edit-label\",\n                                                                children: \"系列课程标题\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1491,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                value: editingTitle,\n                                                                onChange: (e)=>setEditingTitle(e.target.value),\n                                                                className: \"course-edit-input\",\n                                                                placeholder: \"请输入系列课程标题\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1492,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1490,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"course-edit-field\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"course-edit-label\",\n                                                                children: \"课程标签\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1503,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Select_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                mode: \"multiple\",\n                                                                style: {\n                                                                    width: \"100%\"\n                                                                },\n                                                                placeholder: \"请选择课程标签\",\n                                                                value: selectedTags,\n                                                                onChange: setSelectedTags,\n                                                                loading: tagsLoading,\n                                                                options: courseTags.map((tag)=>{\n                                                                    console.log(\"\\uD83C\\uDFF7️ 渲染标签选项:\", tag);\n                                                                    return {\n                                                                        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            style: {\n                                                                                color: tag.color\n                                                                            },\n                                                                            children: tag.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1515,\n                                                                            columnNumber: 29\n                                                                        }, void 0),\n                                                                        value: tag.id\n                                                                    };\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1504,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    fontSize: \"12px\",\n                                                                    color: \"#666\",\n                                                                    marginTop: \"4px\"\n                                                                },\n                                                                children: [\n                                                                    \"调试: 当前标签数量 \",\n                                                                    courseTags.length,\n                                                                    \", 加载状态: \",\n                                                                    tagsLoading ? \"是\" : \"否\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1524,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1502,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"course-edit-field\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"course-edit-label\",\n                                                                children: \"课程项目成员\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1531,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                value: projectMembers,\n                                                                onChange: (e)=>setProjectMembers(e.target.value),\n                                                                className: \"course-edit-input\",\n                                                                placeholder: \"请输入项目成员，如：张老师、李助教、王同学\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1532,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1530,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1488,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true),\n                                    rightPanelType === \"course\" && getSelectedCourse() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"course-detail-edit\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-detail-top\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"course-detail-cover\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"course-cover-upload-area\",\n                                                                    onClick: ()=>{\n                                                                        var _document_getElementById;\n                                                                        return (_document_getElementById = document.getElementById(\"cover-upload-input\")) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.click();\n                                                                    },\n                                                                    children: courseDetail.coverImage || ((_getSelectedCourse = getSelectedCourse()) === null || _getSelectedCourse === void 0 ? void 0 : _getSelectedCourse.coverImage) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                        src: courseDetail.coverImage || ((_getSelectedCourse1 = getSelectedCourse()) === null || _getSelectedCourse1 === void 0 ? void 0 : _getSelectedCourse1.coverImage),\n                                                                        alt: \"课程封面\",\n                                                                        className: \"course-cover-image\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1556,\n                                                                        columnNumber: 27\n                                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"course-cover-placeholder\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"点击上传课程封面\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1563,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1562,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1551,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    id: \"cover-upload-input\",\n                                                                    type: \"file\",\n                                                                    accept: \"image/jpeg,image/jpg,image/png,image/gif\",\n                                                                    onChange: handleCoverUpload,\n                                                                    style: {\n                                                                        display: \"none\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1567,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"one-click-class-btn\",\n                                                                    onClick: ()=>{\n                                                                        // TODO: 实现一键上课功能\n                                                                        console.log(\"一键上课按钮被点击\");\n                                                                        notification.info(\"一键上课功能开发中...\");\n                                                                    },\n                                                                    children: \"一键上课\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1575,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1550,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"course-detail-basic\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"course-detail-field\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            children: \"课程标题\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1588,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            value: courseDetail.title || ((_getSelectedCourse2 = getSelectedCourse()) === null || _getSelectedCourse2 === void 0 ? void 0 : _getSelectedCourse2.title) || \"\",\n                                                                            onChange: (e)=>{\n                                                                                setCourseDetail((prev)=>({\n                                                                                        ...prev,\n                                                                                        title: e.target.value\n                                                                                    }));\n                                                                                updateCourseTitle(selectedCourseId, e.target.value);\n                                                                            },\n                                                                            placeholder: \"请输入课程标题\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1589,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1587,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"course-detail-field\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            children: \"课程介绍\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1600,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                            value: courseDetail.description || ((_getSelectedCourse3 = getSelectedCourse()) === null || _getSelectedCourse3 === void 0 ? void 0 : _getSelectedCourse3.description) || \"\",\n                                                                            onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                        ...prev,\n                                                                                        description: e.target.value\n                                                                                    })),\n                                                                            placeholder: \"请输入课程介绍\",\n                                                                            rows: 3\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1601,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1599,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1586,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1549,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-detail-section\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            children: \"课程资源\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1613,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"course-resource-item\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"resource-header-right\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"课程视频\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1618,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"switch\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: \"checkbox\",\n                                                                                    checked: courseDetail.isVideoEnabled,\n                                                                                    onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                ...prev,\n                                                                                                isVideoEnabled: e.target.checked\n                                                                                            }))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1620,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"slider\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1625,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1619,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1617,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                courseDetail.isVideoEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"video-content-area\",\n                                                                    children: ((_courseDetail_contentConfig = courseDetail.contentConfig) === null || _courseDetail_contentConfig === void 0 ? void 0 : (_courseDetail_contentConfig_video = _courseDetail_contentConfig.video) === null || _courseDetail_contentConfig_video === void 0 ? void 0 : _courseDetail_contentConfig_video.url) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"video-info-section\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"video-preview\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                                                                    className: \"video-thumbnail\",\n                                                                                    controls: true,\n                                                                                    poster: courseDetail.coverImage,\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                                                                                            src: courseDetail.contentConfig.video.url,\n                                                                                            type: \"video/mp4\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1639,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined),\n                                                                                        \"您的浏览器不支持视频播放\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1634,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1633,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"video-name-centered\",\n                                                                                children: courseDetail.contentConfig.video.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1643,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"upload-btn-horizontal\",\n                                                                                onClick: triggerVideoUpload,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"重新上传\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1645,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1644,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1632,\n                                                                        columnNumber: 29\n                                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"video-upload-section\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"video-placeholder-centered\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"play-icon\",\n                                                                                    children: \"▶\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1651,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1650,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"upload-btn-horizontal\",\n                                                                                onClick: triggerVideoUpload,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"上传视频\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1654,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1653,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1649,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1629,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1616,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"course-resource-item\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"resource-header-right\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"课程附件\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1665,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"switch\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: \"checkbox\",\n                                                                                    checked: courseDetail.isAttachmentEnabled,\n                                                                                    onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                ...prev,\n                                                                                                isAttachmentEnabled: e.target.checked\n                                                                                            }))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1667,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"slider\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1672,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1666,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1664,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                courseDetail.isAttachmentEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"attachment-content-area\",\n                                                                    children: ((_courseDetail_contentConfig1 = courseDetail.contentConfig) === null || _courseDetail_contentConfig1 === void 0 ? void 0 : (_courseDetail_contentConfig_document = _courseDetail_contentConfig1.document) === null || _courseDetail_contentConfig_document === void 0 ? void 0 : _courseDetail_contentConfig_document.url) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"attachment-info-section\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"attachment-preview\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"document-icon\",\n                                                                                        children: \"\\uD83D\\uDCC4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1681,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"attachment-details\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"attachment-name\",\n                                                                                            children: courseDetail.contentConfig.document.name\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1683,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1682,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1680,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"upload-btn-horizontal\",\n                                                                                onClick: triggerAttachmentUpload,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"重新上传\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1687,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1686,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1679,\n                                                                        columnNumber: 29\n                                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"attachment-upload-section\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            className: \"upload-btn-horizontal\",\n                                                                            onClick: triggerAttachmentUpload,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"上传附件\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1693,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1692,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1691,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1676,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1663,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"course-resource-item\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"resource-header-simple\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"教学附件\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1704,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1703,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"teaching-materials\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            className: \"add-material-btn\",\n                                                                            onClick: triggerTeachingMaterialUpload,\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"+\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1708,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"上传\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1709,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1707,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        courseDetail.teachingMaterials && courseDetail.teachingMaterials.length > 0 ? courseDetail.teachingMaterials.map((material, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"material-item\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"material-name\",\n                                                                                        onClick: ()=>{\n                                                                                            if (material.url) {\n                                                                                                window.open(material.url, \"_blank\");\n                                                                                            }\n                                                                                        },\n                                                                                        style: {\n                                                                                            cursor: material.url ? \"pointer\" : \"default\",\n                                                                                            color: material.url ? \"#1890ff\" : \"inherit\",\n                                                                                            textDecoration: material.url ? \"underline\" : \"none\"\n                                                                                        },\n                                                                                        title: material.url ? \"点击下载附件\" : material.name,\n                                                                                        children: [\n                                                                                            \"\\uD83D\\uDCCE \",\n                                                                                            material.name\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1714,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        className: \"remove-material-btn\",\n                                                                                        onClick: ()=>removeTeachingMaterial(index),\n                                                                                        title: \"删除附件\",\n                                                                                        children: \"\\xd7\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1730,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, index, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1713,\n                                                                                columnNumber: 29\n                                                                            }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"empty-materials-hint\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                style: {\n                                                                                    color: \"#999\",\n                                                                                    fontSize: \"14px\"\n                                                                                },\n                                                                                children: \"暂无教学附件\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1741,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1740,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1706,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1702,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1612,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-detail-section\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"section-header\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    children: \"课程内容\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1751,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"add-content-section-btn\",\n                                                                    onClick: addTeachingInfoItem,\n                                                                    title: \"添加课程内容\",\n                                                                    children: \"+ 添加课程内容\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1752,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1750,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"course-content-area\",\n                                                            children: courseDetail.teachingInfo && courseDetail.teachingInfo.length > 0 ? courseDetail.teachingInfo.map((info, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"teaching-info-card\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"card-header\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"card-title\",\n                                                                                    children: [\n                                                                                        \"课程内容 \",\n                                                                                        index + 1\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1765,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    className: \"remove-card-btn\",\n                                                                                    onClick: ()=>removeTeachingInfoItem(index),\n                                                                                    title: \"删除此内容\",\n                                                                                    children: \"\\xd7\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1766,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1764,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"card-content\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"input-group\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                            children: \"标题\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1776,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                            type: \"text\",\n                                                                                            value: info.title,\n                                                                                            onChange: (e)=>updateTeachingInfoTitle(index, e.target.value),\n                                                                                            placeholder: \"请输入标题，如：教学目标、教学方法等\",\n                                                                                            className: \"title-input\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1777,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1775,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"input-group\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                            children: \"内容\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1786,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                                            value: info.content,\n                                                                                            onChange: (e)=>updateTeachingInfoContent(index, e.target.value),\n                                                                                            placeholder: \"请输入具体内容，多个内容项可用换行分隔\",\n                                                                                            className: \"content-textarea\",\n                                                                                            rows: 4\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1787,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1785,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1774,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, index, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1763,\n                                                                    columnNumber: 27\n                                                                }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"empty-content-hint\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: \"暂无课程内容，点击右上角按钮添加\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1800,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1799,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1760,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1749,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-detail-section\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"one-key-section\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"one-key-item\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"重新上课\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1810,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"switch\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"checkbox\",\n                                                                                checked: courseDetail.isOneKeyOpen,\n                                                                                onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                            ...prev,\n                                                                                            isOneKeyOpen: e.target.checked\n                                                                                        }))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1812,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"slider\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1817,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1811,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1809,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            courseDetail.isOneKeyOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"one-key-item\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"分配积木\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1824,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"switch\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                        type: \"checkbox\",\n                                                                                        checked: courseDetail.isDistributionEnabled,\n                                                                                        onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    isDistributionEnabled: e.target.checked\n                                                                                                }))\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1826,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"slider\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1831,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1825,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            courseDetail.isDistributionEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"block-template-section\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        className: \"select-template-btn\",\n                                                                                        onClick: handleOpenTemplatePicker,\n                                                                                        children: \"选择积木模板\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1835,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"selected-template-display\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            children: courseDetail.selectedTemplate || \"选中的模板名字\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1842,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1841,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1834,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1823,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"one-key-item\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"分配能量\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1849,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"switch\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                        type: \"checkbox\",\n                                                                                        checked: courseDetail.isDistributionWater,\n                                                                                        onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    isDistributionWater: e.target.checked\n                                                                                                }))\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1851,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"slider\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1856,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1850,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1848,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    courseDetail.isDistributionWater && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"energy-input-section\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"需要能量：\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1862,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"text\",\n                                                                                value: courseDetail.requiredEnergy || \"\",\n                                                                                onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                            ...prev,\n                                                                                            requiredEnergy: e.target.value\n                                                                                        })),\n                                                                                placeholder: \"请输入需要的能量值\",\n                                                                                className: \"energy-input\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1863,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1861,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"one-key-item\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"分配任务\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1874,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"switch\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                        type: \"checkbox\",\n                                                                                        checked: courseDetail.isDistributionLimit,\n                                                                                        onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    isDistributionLimit: e.target.checked\n                                                                                                }))\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1876,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"slider\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1881,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1875,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1873,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    courseDetail.isDistributionLimit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"task-config-form\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"task-config-row\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"task-config-field\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                                children: \"任务名称:\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                lineNumber: 1891,\n                                                                                                columnNumber: 35\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                type: \"text\",\n                                                                                                value: courseDetail.taskConfig.taskName,\n                                                                                                onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                            ...prev,\n                                                                                                            taskConfig: {\n                                                                                                                ...prev.taskConfig,\n                                                                                                                taskName: e.target.value\n                                                                                                            }\n                                                                                                        })),\n                                                                                                placeholder: \"请输入任务名称\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                lineNumber: 1892,\n                                                                                                columnNumber: 35\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1890,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"task-config-field\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                                children: \"任务持续天数:\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                lineNumber: 1903,\n                                                                                                columnNumber: 35\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                type: \"number\",\n                                                                                                value: courseDetail.taskConfig.taskDuration,\n                                                                                                onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                            ...prev,\n                                                                                                            taskConfig: {\n                                                                                                                ...prev.taskConfig,\n                                                                                                                taskDuration: e.target.value\n                                                                                                            }\n                                                                                                        })),\n                                                                                                placeholder: \"请输入天数\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                lineNumber: 1904,\n                                                                                                columnNumber: 35\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1902,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1889,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"task-config-field task-config-full\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        children: \"任务描述:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1918,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                                        value: courseDetail.taskConfig.taskDescription,\n                                                                                        onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    taskConfig: {\n                                                                                                        ...prev.taskConfig,\n                                                                                                        taskDescription: e.target.value\n                                                                                                    }\n                                                                                                })),\n                                                                                        placeholder: \"请输入任务描述\",\n                                                                                        rows: 4\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1919,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1917,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"task-config-field task-config-full\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        children: [\n                                                                                            \"任务自评项: \",\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"item-number\",\n                                                                                                children: courseDetail.taskConfig.selfAssessmentItems.length\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                lineNumber: 1932,\n                                                                                                columnNumber: 47\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1932,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    courseDetail.taskConfig.selfAssessmentItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"self-assessment-item\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                type: \"text\",\n                                                                                                value: item,\n                                                                                                onChange: (e)=>{\n                                                                                                    const newItems = [\n                                                                                                        ...courseDetail.taskConfig.selfAssessmentItems\n                                                                                                    ];\n                                                                                                    newItems[index] = e.target.value;\n                                                                                                    setCourseDetail((prev)=>({\n                                                                                                            ...prev,\n                                                                                                            taskConfig: {\n                                                                                                                ...prev.taskConfig,\n                                                                                                                selfAssessmentItems: newItems\n                                                                                                            }\n                                                                                                        }));\n                                                                                                },\n                                                                                                placeholder: \"请输入自评项内容\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                lineNumber: 1935,\n                                                                                                columnNumber: 37\n                                                                                            }, undefined)\n                                                                                        }, index, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1934,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        type: \"button\",\n                                                                                        className: \"add-assessment-btn\",\n                                                                                        onClick: ()=>setCourseDetail((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    taskConfig: {\n                                                                                                        ...prev.taskConfig,\n                                                                                                        selfAssessmentItems: [\n                                                                                                            ...prev.taskConfig.selfAssessmentItems,\n                                                                                                            \"\"\n                                                                                                        ]\n                                                                                                    }\n                                                                                                })),\n                                                                                        children: \"+\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1950,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1931,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"task-config-field task-config-full\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        children: \"任务参考作品:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1967,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"reference-works-section\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"works-section\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                    className: \"help-text\",\n                                                                                                    children: \"选择作品作为任务参考资料（可多选）\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                    lineNumber: 1970,\n                                                                                                    columnNumber: 37\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"relative works-scroll-wrapper\",\n                                                                                                    style: {\n                                                                                                        minHeight: \"200px\",\n                                                                                                        cursor: \"grab\",\n                                                                                                        userSelect: \"none\"\n                                                                                                    },\n                                                                                                    children: loadingWorks ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"loading-container\",\n                                                                                                        children: [\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                className: \"loading-spinner\"\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                lineNumber: 1981,\n                                                                                                                columnNumber: 43\n                                                                                                            }, undefined),\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                                children: \"加载中...\"\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                lineNumber: 1982,\n                                                                                                                columnNumber: 43\n                                                                                                            }, undefined)\n                                                                                                        ]\n                                                                                                    }, void 0, true, {\n                                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                        lineNumber: 1980,\n                                                                                                        columnNumber: 41\n                                                                                                    }, undefined) : works.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"works-horizontal-scroll\",\n                                                                                                        onWheel: handleWorksWheel,\n                                                                                                        onScroll: handleWorksScroll,\n                                                                                                        children: works.map((work)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                className: \"work-card \".concat(selectedWorkIds.includes(work.id) ? \"selected\" : \"\"),\n                                                                                                                onClick: ()=>handleWorkSelect(work.id),\n                                                                                                                children: [\n                                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                        className: \"work-image\",\n                                                                                                                        children: [\n                                                                                                                            work.coverImage || work.screenShotImage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                                                                src: work.coverImage || work.screenShotImage,\n                                                                                                                                alt: work.title,\n                                                                                                                                onError: (e)=>{\n                                                                                                                                    const target = e.currentTarget;\n                                                                                                                                    target.style.display = \"none\";\n                                                                                                                                    const nextElement = target.nextElementSibling;\n                                                                                                                                    if (nextElement) {\n                                                                                                                                        nextElement.style.display = \"flex\";\n                                                                                                                                    }\n                                                                                                                                }\n                                                                                                                            }, void 0, false, {\n                                                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                                lineNumber: 1998,\n                                                                                                                                columnNumber: 51\n                                                                                                                            }, undefined) : null,\n                                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                                className: \"work-placeholder\",\n                                                                                                                                style: {\n                                                                                                                                    display: work.coverImage || work.screenShotImage ? \"none\" : \"flex\"\n                                                                                                                                },\n                                                                                                                                children: \"作品\"\n                                                                                                                            }, void 0, false, {\n                                                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                                lineNumber: 2011,\n                                                                                                                                columnNumber: 49\n                                                                                                                            }, undefined)\n                                                                                                                        ]\n                                                                                                                    }, void 0, true, {\n                                                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                        lineNumber: 1996,\n                                                                                                                        columnNumber: 47\n                                                                                                                    }, undefined),\n                                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                        className: \"work-title\",\n                                                                                                                        children: work.title || work.name || work.workName || \"未命名作品\"\n                                                                                                                    }, void 0, false, {\n                                                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                        lineNumber: 2015,\n                                                                                                                        columnNumber: 47\n                                                                                                                    }, undefined),\n                                                                                                                    selectedWorkIds.includes(work.id) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                        className: \"selected-indicator\",\n                                                                                                                        children: \"✓\"\n                                                                                                                    }, void 0, false, {\n                                                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                        lineNumber: 2017,\n                                                                                                                        columnNumber: 49\n                                                                                                                    }, undefined)\n                                                                                                                ]\n                                                                                                            }, work.id, true, {\n                                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                lineNumber: 1991,\n                                                                                                                columnNumber: 45\n                                                                                                            }, undefined))\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                        lineNumber: 1985,\n                                                                                                        columnNumber: 41\n                                                                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"empty-placeholder\",\n                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                            className: \"empty-text\",\n                                                                                                            children: \"暂无作品\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                            lineNumber: 2024,\n                                                                                                            columnNumber: 43\n                                                                                                        }, undefined)\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                        lineNumber: 2023,\n                                                                                                        columnNumber: 41\n                                                                                                    }, undefined)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                    lineNumber: 1971,\n                                                                                                    columnNumber: 37\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1969,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1968,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1966,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"task-config-field task-config-full\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        children: \"任务参考资源:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 2034,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"reference-resources-section\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"reference-resources-grid\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                    type: \"button\",\n                                                                                                    className: \"upload-resource-btn\",\n                                                                                                    onClick: ()=>{\n                                                                                                        // 触发文件上传\n                                                                                                        const input = document.createElement(\"input\");\n                                                                                                        input.type = \"file\";\n                                                                                                        input.accept = \".pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx,.txt,.jpg,.png,.gif\";\n                                                                                                        input.onchange = (e)=>{\n                                                                                                            var _e_target_files;\n                                                                                                            const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n                                                                                                            if (file) {\n                                                                                                                setCourseDetail((prev)=>({\n                                                                                                                        ...prev,\n                                                                                                                        taskConfig: {\n                                                                                                                            ...prev.taskConfig,\n                                                                                                                            referenceResources: [\n                                                                                                                                ...prev.taskConfig.referenceResources,\n                                                                                                                                {\n                                                                                                                                    type: \"file\",\n                                                                                                                                    name: file.name\n                                                                                                                                }\n                                                                                                                            ]\n                                                                                                                        }\n                                                                                                                    }));\n                                                                                                            }\n                                                                                                        };\n                                                                                                        input.click();\n                                                                                                    },\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                                            size: 24\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                            lineNumber: 2063,\n                                                                                                            columnNumber: 39\n                                                                                                        }, undefined),\n                                                                                                        \"上传\"\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                    lineNumber: 2037,\n                                                                                                    columnNumber: 37\n                                                                                                }, undefined),\n                                                                                                courseDetail.taskConfig.referenceResources.map((resource, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"reference-resource-item\",\n                                                                                                        children: [\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                                children: resource.name\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                lineNumber: 2068,\n                                                                                                                columnNumber: 41\n                                                                                                            }, undefined),\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                                type: \"button\",\n                                                                                                                className: \"remove-resource-btn\",\n                                                                                                                onClick: ()=>{\n                                                                                                                    const newResources = courseDetail.taskConfig.referenceResources.filter((_, i)=>i !== index);\n                                                                                                                    setCourseDetail((prev)=>({\n                                                                                                                            ...prev,\n                                                                                                                            taskConfig: {\n                                                                                                                                ...prev.taskConfig,\n                                                                                                                                referenceResources: newResources\n                                                                                                                            }\n                                                                                                                        }));\n                                                                                                                },\n                                                                                                                children: \"\\xd7\"\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                lineNumber: 2069,\n                                                                                                                columnNumber: 41\n                                                                                                            }, undefined)\n                                                                                                        ]\n                                                                                                    }, index, true, {\n                                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                        lineNumber: 2067,\n                                                                                                        columnNumber: 39\n                                                                                                    }, undefined))\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 2036,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 2035,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 2033,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1887,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1808,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1807,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 1547,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                lineNumber: 1457,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                        lineNumber: 1403,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"course-list-footer\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"course-list-footer-left\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handlePublish,\n                                    className: \"course-list-btn course-list-btn-publish\",\n                                    disabled: courseList.length === 0 || isPublishingSeries || seriesStatus === 1,\n                                    title: seriesStatus === 1 ? \"系列课程已发布\" : courseList.length === 0 ? \"发布失败：课程系列中至少需要包含一个课程\" : isPublishingSeries ? \"正在发布系列课程...\" : \"发布系列课程\",\n                                    children: seriesStatus === 1 ? \"已发布\" : isPublishingSeries ? \"正在发布...\" : \"发布系列课程\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 2102,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                lineNumber: 2101,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"course-list-footer-right\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleExitEdit,\n                                        className: \"course-list-btn course-list-btn-exit\",\n                                        children: \"退出编辑模式\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 2125,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handlePublishCourse,\n                                        className: \"course-list-btn course-list-btn-publish-course\",\n                                        disabled: !selectedCourseId || ((_courseList_find = courseList.find((c)=>c.id === selectedCourseId)) === null || _courseList_find === void 0 ? void 0 : _courseList_find.status) === 1 || isPublishing,\n                                        title: !selectedCourseId ? \"请先选择要发布的课程\" : ((_courseList_find1 = courseList.find((c)=>c.id === selectedCourseId)) === null || _courseList_find1 === void 0 ? void 0 : _courseList_find1.status) === 1 ? \"该课程已发布\" : isPublishing ? \"正在发布课程...\" : \"发布选中的课程\",\n                                        children: isPublishing ? \"正在发布...\" : \"发布课程\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 2128,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleSave,\n                                        className: \"course-list-btn course-list-btn-save\",\n                                        disabled: uploadingFiles.size > 0 || isCreating || courseList.length === 0,\n                                        title: courseList.length === 0 ? \"请先添加课程内容\" : uploadingFiles.size > 0 ? \"有文件正在上传中，请等待上传完成\" : isCreating ? courseList.some((c)=>c.id > 1000000) ? \"正在创建课程...\" : \"正在保存课程...\" : courseList.some((c)=>c.id > 1000000) ? \"创建课程\" : \"保存课程\",\n                                        children: uploadingFiles.size > 0 ? \"上传中...\" : isCreating ? courseList.some((c)=>c.id > 1000000) ? \"正在创建...\" : \"正在保存...\" : courseList.some((c)=>c.id > 1000000) ? \"创建课程\" : \"保存课程\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 2144,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                lineNumber: 2124,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                        lineNumber: 2100,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                lineNumber: 1380,\n                columnNumber: 7\n            }, undefined),\n            deleteConfirmVisible && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"modal-overlay\",\n                onClick: cancelDelete,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"delete-confirm-modal\",\n                    onClick: (e)=>e.stopPropagation(),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"delete-confirm-header\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    children: \"确认删除\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 2174,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: cancelDelete,\n                                    className: \"close-btn\",\n                                    disabled: isDeleting,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 2180,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 2175,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                            lineNumber: 2173,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"delete-confirm-content\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: isDeleting ? \"正在删除课程，请稍候...\" : \"确定要删除这个课程吗？删除后无法恢复。\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 2184,\n                                    columnNumber: 15\n                                }, undefined),\n                                isDeleting && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"delete-loading\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"loading-spinner\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 2192,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 2191,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                            lineNumber: 2183,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"delete-confirm-footer\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: cancelDelete,\n                                    className: \"cancel-btn\",\n                                    disabled: isDeleting,\n                                    children: \"取消\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 2197,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: confirmDeleteCourse,\n                                    className: \"confirm-btn\",\n                                    disabled: isDeleting,\n                                    children: isDeleting ? \"正在删除...\" : \"确认删除\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 2204,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                            lineNumber: 2196,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                    lineNumber: 2172,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                lineNumber: 2171,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TemplatePickerModal__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                isOpen: isTemplatePickerOpen,\n                onClose: ()=>setIsTemplatePickerOpen(false),\n                onTemplateSelect: handleTemplateSelect\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                lineNumber: 2217,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n        lineNumber: 1379,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CourseListEditModal, \"QwhisgEEjETopIHipMPhIkdFoBw=\");\n_c = CourseListEditModal;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CourseListEditModal);\nvar _c;\n$RefreshReg$(_c, \"CourseListEditModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/workbench/components/CourseListEditModal.tsx\n"));

/***/ })

});