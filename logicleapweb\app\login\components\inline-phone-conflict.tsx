'use client'

import { useState } from 'react';
import { But<PERSON>, Modal } from 'antd';
import NotificationUtil from '@/lib/utils/notification';

interface UserInfo {
    id: number;
    nickName: string;
    avatarUrl?: string;
    role?: {
        id: number;
        name: string;
    };
    schoolInfo?: {
        province?: string;
        city?: string;
        district?: string;
        schoolName?: string;
    };
    student?: {
        school?: {
            province?: string;
            city?: string;
            district?: string;
            schoolName?: string;
        }
    };
    schoolName?: string;
    registerType?: string;
}

interface InlinePhoneConflictProps {
    open: boolean;
    onClose: () => void;
    onContinueBind: () => void;
    onReplaceAccount: (selectedUser:any) => void;
    onBindWeixinToUser?: (userId: number) => void;
    bindUserList: UserInfo[];
    loading: boolean;
    registerType?: string;
}

const InlinePhoneConflict: React.FC<InlinePhoneConflictProps> = ({
    open,
    onClose,
    onContinueBind,
    onReplaceAccount,
    onBindWeixinToUser,
    bindUserList,
    loading,
    registerType = ''
}) => {
    const [selectedUser, setSelectedUser] = useState<UserInfo | null>(null);

    // 处理选择用户
    const handleSelectUser = (user: UserInfo) => {
        if (selectedUser?.id === user.id) {
            setSelectedUser(null);
        } else {
            setSelectedUser(user);
        }
    };

    // 处理替换账号
    const handleReplaceAccount = () => {
        // 2.传递被替换的用户ID，执行替换流程，该方法为inline-bind-phone传进的prop函数
       // 修改后
onReplaceAccount(selectedUser);
    };

    // 处理微信绑定到用户
    const handleBindWeixinToUser = () => {
        if (!selectedUser) {
            NotificationUtil.warning('请选择要绑定的账号');
            return;
        }

        if (onBindWeixinToUser) {
            onBindWeixinToUser(selectedUser.id);
        }
    };

    // 获取绑定弹窗标题
    const getBindModalTitle = () => {
        if (bindUserList.length >= 3) {
            return "该手机号已绑定了3个账号";
        } else {
            return "该手机号已被绑定";
        }
    };

    // 获取绑定弹窗描述
    const getBindModalDescription = () => {
        if (registerType === 'weixin') {
            return "请选择一个账号作为以后扫码登录进入的账号";
        } else if (bindUserList.length >= 3) {
            return "同一个手机号最多只能绑定3个账号，请选择一个账号进行替换";
        } else {
            return "该手机号已经绑定了以下账号，您可以继续绑定或选择替换";
        }
    };

    return (
        <Modal
            title={getBindModalTitle()}
            open={open}
            onCancel={onClose}
            footer={null}
            width={600}
            centered
        >
            <div className="py-4">
                <p className="text-gray-500 mb-6">{getBindModalDescription()}</p>

                <div className="max-h-[300px] overflow-y-auto mb-6">
                    {bindUserList.map(user => (
                        <div
                            key={user.id}
                            className={`border p-4 rounded-lg mb-4 cursor-pointer transition ${selectedUser?.id === user.id
                                    ? 'border-blue-500 bg-blue-50'
                                    : 'border-gray-200 hover:border-blue-300'
                                }`}
                            onClick={() => handleSelectUser(user)}
                        >
                            <div className="flex items-center">
                                <div className="flex-shrink-0 w-10 h-10 bg-gray-200 rounded-full overflow-hidden">
                                    {user.avatarUrl ? (
                                        <img src={user.avatarUrl} alt="头像" className="w-full h-full object-cover" />
                                    ) : (
                                        <div className="w-full h-full flex items-center justify-center bg-gray-300 text-gray-600">
                                            {user.nickName?.substring(0, 1) || '用'}
                                        </div>
                                    )}
                                </div>

                                <div className="ml-4 flex-1">
                                    <div className="font-medium">{user.nickName || '未命名用户'}</div>
                                    <div className="text-sm text-gray-500 mt-1">
                                        {user.role?.name || '未知角色'}
                                        {(user.schoolInfo?.schoolName || user.student?.school?.schoolName || user.schoolName) &&
                                            ` · ${user.schoolInfo?.schoolName || user.student?.school?.schoolName || user.schoolName}`
                                        }
                                    </div>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>

                {/* 不同注册类型显示不同的按钮组 */}
                {registerType === 'weixin' ? (
                    <div className="flex gap-8 w-full justify-center">
                        <button
                            className={`px-8 py-2 rounded-lg border transition ${!selectedUser
                                    ? "border-gray-300 text-gray-400 bg-gray-100 cursor-not-allowed"
                                    : "border-blue-500 text-blue-600 bg-white hover:bg-blue-50"
                                }`}
                            onClick={handleBindWeixinToUser}
                            disabled={!selectedUser}
                        >
                            绑定账号
                        </button>
                    </div>
                ) : (
                    <div className="flex gap-8 w-full justify-center">
                        {selectedUser ? (
                            <button
                                className="px-8 py-2 rounded-lg bg-green-500 text-white hover:bg-green-600 transition"
                                onClick={handleReplaceAccount}
                                disabled={loading}
                            >
                                替换账号
                            </button>
                        ) : (
                            <button
                                className={`px-8 py-2 rounded-lg ${bindUserList.length >= 3
                                        ? 'bg-gray-400 cursor-not-allowed'
                                        : 'bg-blue-500 hover:bg-blue-600'
                                    } text-white transition`}
                                onClick={() => {
                                    Modal.confirm({
                                        title: '确认继续绑定',
                                        content: '绑定成功后，当前密码会与当前已绑定的手机号密码同步。确定继续吗？',
                                        okText: '确认绑定',
                                        cancelText: '取消',
                                        onOk: onContinueBind
                                    });
                                }}
                                disabled={loading || bindUserList.length >= 3}
                            >
                                继续绑定
                            </button>
                        )}
                    </div>
                )}
            </div>
        </Modal>
    );
};

export default InlinePhoneConflict; 