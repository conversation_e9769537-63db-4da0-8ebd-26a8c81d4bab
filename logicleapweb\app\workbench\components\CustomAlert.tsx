'use client';

import React from 'react';
import './CustomAlert.css';

interface CustomAlertProps {
  isOpen: boolean;
  title?: string;
  message: string;
  type?: 'info' | 'success' | 'warning' | 'error';
  onConfirm: () => void;
  onCancel?: () => void;
  confirmText?: string;
  cancelText?: string;
  showCancel?: boolean;
}

const CustomAlert: React.FC<CustomAlertProps> = ({
  isOpen,
  title,
  message,
  type = 'info',
  onConfirm,
  onCancel,
  confirmText = '确定',
  cancelText = '取消',
  showCancel = false
}) => {
  if (!isOpen) return null;

  const handleOverlayClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      if (showCancel && onCancel) {
        onCancel();
      }
    }
  };

  const getIcon = () => {
    switch (type) {
      case 'success':
        return (
          <div className="custom-alert-icon custom-alert-icon-success">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <path d="M9 12l2 2 4-4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"/>
            </svg>
          </div>
        );
      case 'warning':
        return (
          <div className="custom-alert-icon custom-alert-icon-warning">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <path d="M12 9v4" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
              <path d="M12 17h.01" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
              <path d="M10.29 3.86L1.82 18a2 2 0 001.71 3h16.94a2 2 0 001.71-3L13.71 3.86a2 2 0 00-3.42 0z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </div>
        );
      case 'error':
        return (
          <div className="custom-alert-icon custom-alert-icon-error">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"/>
              <line x1="15" y1="9" x2="9" y2="15" stroke="currentColor" strokeWidth="2"/>
              <line x1="9" y1="9" x2="15" y2="15" stroke="currentColor" strokeWidth="2"/>
            </svg>
          </div>
        );
      default:
        return (
          <div className="custom-alert-icon custom-alert-icon-info">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"/>
              <path d="M12 16v-4" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
              <path d="M12 8h.01" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
            </svg>
          </div>
        );
    }
  };

  return (
    <div className="custom-alert-overlay" onClick={handleOverlayClick}>
      <div className="custom-alert-modal" onClick={(e) => e.stopPropagation()}>
        <div className="custom-alert-content">
          {getIcon()}
          <div className="custom-alert-text">
            {title && <h3 className="custom-alert-title">{title}</h3>}
            <p className="custom-alert-message">{message}</p>
          </div>
        </div>
        <div className="custom-alert-actions">
          {showCancel && onCancel && (
            <button 
              className="custom-alert-btn custom-alert-btn-cancel"
              onClick={onCancel}
            >
              {cancelText}
            </button>
          )}
          <button 
            className="custom-alert-btn custom-alert-btn-confirm"
            onClick={onConfirm}
          >
            {confirmText}
          </button>
        </div>
      </div>
    </div>
  );
};

export default CustomAlert;
