"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(main)/course-plaza/page",{

/***/ "(app-pages-browser)/./app/(main)/course-plaza/components/CourseDetailView.tsx":
/*!*****************************************************************!*\
  !*** ./app/(main)/course-plaza/components/CourseDetailView.tsx ***!
  \*****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CourseDetailView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_api_course__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api/course */ \"(app-pages-browser)/./lib/api/course.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction CourseDetailView(param) {\n    let { course, onBack } = param;\n    var _courseDetail_contentConfig_video, _courseDetail_contentConfig, _courseDetail_contentConfig_document, _courseDetail_contentConfig1, _courseDetail_contentConfig_document1, _courseDetail_contentConfig2, _courseDetail_contentConfig_document2, _courseDetail_contentConfig3, _courseDetail_contentConfig_document3, _courseDetail_contentConfig4;\n    _s();\n    const [seriesCourses, setSeriesCourses] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [seriesDetail, setSeriesDetail] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [courseDetail, setCourseDetail] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [showVideoPlayer, setShowVideoPlayer] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [videoLoaded, setVideoLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // 格式化视频时长\n    const formatDuration = (seconds)=>{\n        if (!seconds || seconds <= 0) return \"\";\n        const hours = Math.floor(seconds / 3600);\n        const minutes = Math.floor(seconds % 3600 / 60);\n        const remainingSeconds = seconds % 60;\n        if (hours > 0) {\n            return \"\".concat(hours, \"小时\").concat(minutes, \"分钟\");\n        } else if (minutes > 0) {\n            return \"\".concat(minutes, \"分钟\").concat(remainingSeconds > 0 ? remainingSeconds + \"秒\" : \"\");\n        } else {\n            return \"\".concat(remainingSeconds, \"秒\");\n        }\n    };\n    // 添加自定义滚动条样式\n    const customScrollbarStyle = \"\\n    .custom-scrollbar::-webkit-scrollbar {\\n      width: 6px;\\n    }\\n    .custom-scrollbar::-webkit-scrollbar-track {\\n      background: #f1f5f9;\\n      border-radius: 3px;\\n    }\\n    .custom-scrollbar::-webkit-scrollbar-thumb {\\n      background: linear-gradient(to bottom, #3b82f6, #2563eb);\\n      border-radius: 3px;\\n    }\\n    .custom-scrollbar::-webkit-scrollbar-thumb:hover {\\n      background: linear-gradient(to bottom, #2563eb, #1d4ed8);\\n    }\\n  \";\n    // 使用useRef跟踪请求状态，避免React严格模式重复请求\n    const requestedSeriesIdRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const isRequestingRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(false);\n    // 添加调试信息\n    console.log(\"\\uD83D\\uDCCB CourseDetailView 接收到的课程数据:\", course);\n    console.log(\"\\uD83D\\uDD0D seriesId 值:\", course.seriesId);\n    console.log(\"\\uD83D\\uDD0D seriesId 类型:\", typeof course.seriesId);\n    // 调试：打印传入的课程数据\n    console.log(\"\\uD83C\\uDFAF CourseDetailView 接收到的课程数据:\", course);\n    // 防止浏览器自动下载\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const preventAutoDownload = (e)=>{\n            var _target_getAttribute;\n            const target = e.target;\n            if (target && target.tagName === \"A\" && ((_target_getAttribute = target.getAttribute(\"href\")) === null || _target_getAttribute === void 0 ? void 0 : _target_getAttribute.includes(\"example.com\"))) {\n                e.preventDefault();\n                console.log(\"\\uD83D\\uDEAB 阻止示例文件自动下载\");\n            }\n        };\n        document.addEventListener(\"click\", preventAutoDownload, true);\n        return ()=>{\n            document.removeEventListener(\"click\", preventAutoDownload, true);\n        };\n    }, []);\n    // 获取系列课程列表\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const fetchSeriesCourses = async ()=>{\n            // 使用 seriesId 或者 id 作为系列ID\n            const seriesId = course.seriesId || course.id;\n            if (!seriesId) {\n                console.warn(\"⚠️ 课程没有seriesId和id，无法获取系列课程列表\");\n                setLoading(false);\n                return;\n            }\n            // 防重复请求\n            if (requestedSeriesIdRef.current === seriesId || isRequestingRef.current) {\n                console.log(\"\\uD83D\\uDEAB 防重复请求：系列课程列表已请求过，seriesId:\", seriesId);\n                return;\n            }\n            try {\n                var _coursesRes_data, _coursesRes_data1;\n                setLoading(true);\n                requestedSeriesIdRef.current = seriesId;\n                isRequestingRef.current = true;\n                console.log(\"\\uD83D\\uDD04 获取系列课程列表，使用ID:\", seriesId);\n                // 首先获取系列详情\n                const { data: seriesRes } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getMarketplaceSeriesDetail(seriesId);\n                if (seriesRes.code === 200 && seriesRes.data) {\n                    const seriesData = seriesRes.data;\n                    // 设置系列详情\n                    setSeriesDetail({\n                        id: seriesData.id,\n                        title: seriesData.title,\n                        description: seriesData.description,\n                        coverImage: seriesData.coverImage && !seriesData.coverImage.includes(\"example.com\") ? seriesData.coverImage : \"\",\n                        category: seriesData.category,\n                        categoryLabel: seriesData.categoryLabel || (seriesData.category === 0 ? \"官方\" : \"社区\"),\n                        status: seriesData.status,\n                        projectMembers: seriesData.projectMembers,\n                        totalCourses: seriesData.totalCourses,\n                        totalStudents: seriesData.totalStudents,\n                        creatorId: seriesData.creatorId || 0,\n                        createdAt: seriesData.createdAt,\n                        updatedAt: seriesData.updatedAt || seriesData.createdAt,\n                        tags: seriesData.tags || []\n                    });\n                }\n                // 使用课程管理API获取系列下的课程列表\n                console.log(\"\\uD83D\\uDD04 使用课程管理API获取系列课程列表...\");\n                const { data: coursesRes } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getManagementSeriesCourses(seriesId, {\n                    page: 1,\n                    pageSize: 50,\n                    status: 1 // 只获取已发布的课程\n                });\n                if (coursesRes.code === 200 && ((_coursesRes_data = coursesRes.data) === null || _coursesRes_data === void 0 ? void 0 : _coursesRes_data.list)) {\n                    const courses = coursesRes.data.list.map((item)=>({\n                            id: item.id,\n                            title: item.title,\n                            orderIndex: item.orderIndex || 0,\n                            status: item.status\n                        }));\n                    // 按orderIndex排序\n                    courses.sort((a, b)=>a.orderIndex - b.orderIndex);\n                    console.log(\"\\uD83D\\uDCDA 从课程管理API获取到课程列表:\", courses);\n                    setSeriesCourses(courses);\n                } else {\n                    console.log(\"⚠️ 课程管理API未返回课程列表\");\n                    setSeriesCourses([]);\n                }\n                // 如果有课程列表，设置默认课程详情（选择第一个课程）\n                if (coursesRes.code === 200 && ((_coursesRes_data1 = coursesRes.data) === null || _coursesRes_data1 === void 0 ? void 0 : _coursesRes_data1.list) && coursesRes.data.list.length > 0) {\n                    const firstCourse = coursesRes.data.list[0];\n                    console.log(\"\\uD83C\\uDFAC 设置默认课程（第一个课程）:\", firstCourse);\n                    // 获取第一个课程的详细信息\n                    try {\n                        const { data: courseDetailRes } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getCourseDetail(firstCourse.id);\n                        if (courseDetailRes.code === 200 && courseDetailRes.data) {\n                            var _seriesRes_data, _seriesRes_data1, _courseData_additionalResources;\n                            const courseData = courseDetailRes.data;\n                            setCourseDetail({\n                                id: courseData.id,\n                                title: courseData.title,\n                                description: courseData.description || ((_seriesRes_data = seriesRes.data) === null || _seriesRes_data === void 0 ? void 0 : _seriesRes_data.description) || \"\",\n                                coverImage: courseData.coverImage && !courseData.coverImage.includes(\"example.com\") ? courseData.coverImage : ((_seriesRes_data1 = seriesRes.data) === null || _seriesRes_data1 === void 0 ? void 0 : _seriesRes_data1.coverImage) || \"\",\n                                hasVideo: courseData.hasVideo || 0,\n                                hasDocument: courseData.hasDocument || 0,\n                                hasAudio: courseData.hasAudio || 0,\n                                videoDuration: courseData.videoDuration || 0,\n                                videoDurationLabel: courseData.videoDurationLabel || \"\",\n                                videoName: courseData.videoName || \"\",\n                                resourcesCount: ((_courseData_additionalResources = courseData.additionalResources) === null || _courseData_additionalResources === void 0 ? void 0 : _courseData_additionalResources.length) || 0,\n                                contentConfig: courseData.contentConfig || {},\n                                teachingInfo: courseData.teachingInfo || [],\n                                additionalResources: courseData.additionalResources || [],\n                                orderIndex: courseData.orderIndex || 1,\n                                status: courseData.status || 1,\n                                statusLabel: courseData.statusLabel || \"已发布\",\n                                currentCourseId: courseData.id\n                            });\n                        }\n                    } catch (courseError) {\n                        console.error(\"❌ 获取课程详情失败:\", courseError);\n                    }\n                }\n            } catch (error) {\n                console.error(\"❌ 获取系列课程列表异常:\", error);\n                setSeriesCourses([]);\n            } finally{\n                setLoading(false);\n                isRequestingRef.current = false;\n            }\n        };\n        fetchSeriesCourses();\n    }, [\n        course.seriesId,\n        course.id\n    ]);\n    // 处理课程点击事件\n    const handleCourseClick = async (courseItem)=>{\n        const seriesId = course.seriesId || course.id;\n        const courseId = courseItem.id;\n        console.log(\"\\uD83C\\uDFAF 点击课程，准备获取详情 - seriesId:\", seriesId, \"courseId:\", courseId);\n        // 先清空课程详情，避免旧数据触发下载\n        setCourseDetail(null);\n        try {\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getCourseMarketplaceDetail(seriesId, courseId);\n            if (res.code === 200 && res.data) {\n                console.log(\"\\uD83D\\uDCDA 获取到课程详情:\", res.data);\n                setCourseDetail(res.data);\n            } else {\n                console.error(\"❌ 获取课程详情失败:\", res);\n                // 如果API失败，使用本地数据（添加示例视频时长和PDF文档）\n                const sampleVideoDuration = courseItem.id === 1 ? 1800 : courseItem.id === 2 ? 2400 : 3600; // 30分钟、40分钟、60分钟\n                const hasVideoSample = courseItem.id <= 2 ? 1 : 0; // 前两个课程有视频\n                const hasDocumentSample = courseItem.id <= 3 ? 1 : 0; // 前三个课程有文档\n                setCourseDetail({\n                    id: courseItem.id,\n                    title: courseItem.title,\n                    description: \"\",\n                    coverImage: course.coverImage || \"\",\n                    hasVideo: hasVideoSample,\n                    hasDocument: hasDocumentSample,\n                    hasAudio: 0,\n                    videoDuration: hasVideoSample ? sampleVideoDuration : 0,\n                    videoDurationLabel: \"\",\n                    videoName: hasVideoSample ? \"\".concat(courseItem.title, \"教学视频\") : \"\",\n                    resourcesCount: 0,\n                    contentConfig: {\n                        hasVideo: hasVideoSample,\n                        hasDocument: hasDocumentSample,\n                        hasAudio: 0,\n                        video: hasVideoSample ? {\n                            url: \"https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4\",\n                            name: \"\".concat(courseItem.title, \"教学视频.mp4\")\n                        } : {\n                            url: \"\",\n                            name: \"\"\n                        },\n                        document: hasDocumentSample ? {\n                            url: \"https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf\",\n                            name: \"\".concat(courseItem.title, \"教学课件.pdf\")\n                        } : {\n                            url: \"\",\n                            name: \"\"\n                        },\n                        audio: {\n                            url: \"\",\n                            name: \"\"\n                        }\n                    },\n                    teachingInfo: [],\n                    additionalResources: [],\n                    orderIndex: courseItem.orderIndex || 1,\n                    status: courseItem.status || 1,\n                    statusLabel: courseItem.status === 1 ? \"已发布\" : \"草稿\",\n                    currentCourseId: courseItem.id\n                });\n            }\n        } catch (error) {\n            console.error(\"❌ 获取课程详情异常:\", error);\n            // 如果API失败，使用本地数据（添加示例视频时长和PDF文档）\n            const sampleVideoDuration = courseItem.id === 1 ? 1800 : courseItem.id === 2 ? 2400 : 3600; // 30分钟、40分钟、60分钟\n            const hasVideoSample = courseItem.id <= 2 ? 1 : 0; // 前两个课程有视频\n            const hasDocumentSample = courseItem.id <= 3 ? 1 : 0; // 前三个课程有文档\n            setCourseDetail({\n                id: courseItem.id,\n                title: courseItem.title,\n                description: \"\",\n                coverImage: course.coverImage || \"\",\n                hasVideo: hasVideoSample,\n                hasDocument: hasDocumentSample,\n                hasAudio: 0,\n                videoDuration: hasVideoSample ? sampleVideoDuration : 0,\n                videoDurationLabel: \"\",\n                videoName: hasVideoSample ? \"\".concat(courseItem.title, \"教学视频\") : \"\",\n                resourcesCount: 0,\n                contentConfig: {\n                    hasVideo: hasVideoSample,\n                    hasDocument: hasDocumentSample,\n                    hasAudio: 0,\n                    video: hasVideoSample ? {\n                        url: \"https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4\",\n                        name: \"\".concat(courseItem.title, \"教学视频.mp4\")\n                    } : {\n                        url: \"\",\n                        name: \"\"\n                    },\n                    document: hasDocumentSample ? {\n                        url: \"https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf\",\n                        name: \"\".concat(courseItem.title, \"教学课件.pdf\")\n                    } : {\n                        url: \"\",\n                        name: \"\"\n                    },\n                    audio: {\n                        url: \"\",\n                        name: \"\"\n                    }\n                },\n                teachingInfo: [],\n                additionalResources: [],\n                orderIndex: courseItem.orderIndex || 1,\n                status: courseItem.status || 1,\n                statusLabel: courseItem.status === 1 ? \"已发布\" : \"草稿\",\n                currentCourseId: courseItem.id\n            });\n        }\n    };\n    // 注释：现在数据直接从API获取，不再需要设置默认数据\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n        initial: {\n            opacity: 0,\n            x: 50\n        },\n        animate: {\n            opacity: 1,\n            x: 0\n        },\n        exit: {\n            opacity: 0,\n            x: -50\n        },\n        transition: {\n            duration: 0.5\n        },\n        className: \"min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"style\", {\n                dangerouslySetInnerHTML: {\n                    __html: customScrollbarStyle\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                lineNumber: 423,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: onBack,\n                    className: \"flex items-center gap-2 px-4 py-2 text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-colors duration-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-5 h-5\",\n                            fill: \"none\",\n                            viewBox: \"0 0 24 24\",\n                            stroke: \"currentColor\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M15 19l-7-7 7-7\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                lineNumber: 432,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                            lineNumber: 431,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"返回课程列表\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                            lineNumber: 434,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                    lineNumber: 427,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                lineNumber: 426,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-br from-white via-blue-50/30 to-indigo-50/50 rounded-2xl border border-blue-200/60 shadow-lg backdrop-blur-sm overflow-hidden mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between p-6 bg-white/70 backdrop-blur-md border-b border-blue-100/50\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900 tracking-tight\",\n                                children: (courseDetail === null || courseDetail === void 0 ? void 0 : courseDetail.title) || (seriesDetail === null || seriesDetail === void 0 ? void 0 : seriesDetail.title) || \"课程详情\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                lineNumber: 441,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"px-5 py-2.5 text-sm font-semibold rounded-xl shadow-sm transition-all duration-200 \".concat((seriesDetail === null || seriesDetail === void 0 ? void 0 : seriesDetail.category) === 0 ? \"bg-gradient-to-r from-emerald-500 to-emerald-600 text-white hover:from-emerald-600 hover:to-emerald-700\" : \"bg-gradient-to-r from-blue-500 to-blue-600 text-white hover:from-blue-600 hover:to-blue-700\"),\n                                children: (seriesDetail === null || seriesDetail === void 0 ? void 0 : seriesDetail.category) === 0 ? \"官方\" : \"社区\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                lineNumber: 444,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                        lineNumber: 440,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-6 p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-40 h-28 bg-gradient-to-br from-orange-100 via-orange-50 to-amber-50 rounded-xl shadow-md overflow-hidden border-2 border-white/80 transition-all duration-300 group-hover:shadow-lg group-hover:scale-105\",\n                                        children: (courseDetail === null || courseDetail === void 0 ? void 0 : courseDetail.coverImage) && !courseDetail.coverImage.includes(\"example.com\") || (seriesDetail === null || seriesDetail === void 0 ? void 0 : seriesDetail.coverImage) && !seriesDetail.coverImage.includes(\"example.com\") ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            src: (courseDetail === null || courseDetail === void 0 ? void 0 : courseDetail.coverImage) || (seriesDetail === null || seriesDetail === void 0 ? void 0 : seriesDetail.coverImage) || \"\",\n                                            alt: (courseDetail === null || courseDetail === void 0 ? void 0 : courseDetail.title) || (seriesDetail === null || seriesDetail === void 0 ? void 0 : seriesDetail.title) || \"课程封面\",\n                                            width: 160,\n                                            height: 112,\n                                            className: \"w-full h-full object-cover\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                            lineNumber: 459,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-full flex flex-col items-center justify-center text-orange-400\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-8 h-8 mb-1\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 1.5,\n                                                        d: \"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 469,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 468,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs font-medium\",\n                                                    children: \"课程封面\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 471,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                            lineNumber: 467,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                        lineNumber: 456,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -inset-1 bg-gradient-to-r from-orange-200/20 to-amber-200/20 rounded-xl blur-sm -z-10 opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                        lineNumber: 476,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                lineNumber: 455,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-r from-purple-50 to-indigo-50 rounded-xl p-5 border border-purple-100/50 shadow-sm\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-700 leading-relaxed\",\n                                            children: (courseDetail === null || courseDetail === void 0 ? void 0 : courseDetail.description) || (seriesDetail === null || seriesDetail === void 0 ? void 0 : seriesDetail.description) || \"暂无课程描述\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                            lineNumber: 482,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                        lineNumber: 481,\n                                        columnNumber: 13\n                                    }, this),\n                                    (seriesDetail === null || seriesDetail === void 0 ? void 0 : seriesDetail.tags) && seriesDetail.tags.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2\",\n                                        children: seriesDetail.tags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-flex items-center px-3 py-1 text-xs font-medium rounded-full border\",\n                                                style: {\n                                                    backgroundColor: \"\".concat(tag.color, \"15\"),\n                                                    borderColor: \"\".concat(tag.color, \"40\"),\n                                                    color: tag.color\n                                                },\n                                                children: tag.name\n                                            }, tag.id, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                lineNumber: 491,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                        lineNumber: 489,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 text-sm text-gray-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4 text-purple-500\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 508,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                lineNumber: 507,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: \"项目成员：\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                lineNumber: 510,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: (seriesDetail === null || seriesDetail === void 0 ? void 0 : seriesDetail.projectMembers) || \"暂无信息\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                lineNumber: 511,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                        lineNumber: 506,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                lineNumber: 480,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                        lineNumber: 453,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                lineNumber: 439,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2 space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-r from-blue-600 to-purple-600 px-6 py-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-xl font-bold text-white flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-6 h-6\",\n                                                            fill: \"currentColor\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                lineNumber: 528,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                            lineNumber: 527,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"教学视频\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 526,\n                                                    columnNumber: 17\n                                                }, this),\n                                                ((courseDetail === null || courseDetail === void 0 ? void 0 : courseDetail.videoDurationLabel) || (courseDetail === null || courseDetail === void 0 ? void 0 : courseDetail.videoDuration)) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"bg-white/20 backdrop-blur-sm text-white px-3 py-1 rounded-full text-sm font-medium\",\n                                                    children: [\n                                                        \"时长：\",\n                                                        courseDetail.videoDurationLabel || formatDuration(courseDetail.videoDuration || 0)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 533,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                            lineNumber: 525,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                        lineNumber: 524,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: (courseDetail === null || courseDetail === void 0 ? void 0 : courseDetail.hasVideo) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative group\",\n                                            children: !showVideoPlayer ? // 视频预览封面\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative bg-gradient-to-br from-gray-900 to-gray-700 rounded-xl h-80 flex items-center justify-center cursor-pointer overflow-hidden transition-all duration-300 hover:shadow-2xl\",\n                                                onClick: ()=>setShowVideoPlayer(true),\n                                                children: [\n                                                    courseDetail.coverImage && !courseDetail.coverImage.includes(\"example.com\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 bg-cover bg-center opacity-60\",\n                                                        style: {\n                                                            backgroundImage: \"url(\".concat(courseDetail.coverImage, \")\")\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 551,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 558,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative z-10 text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-20 h-20 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4 transition-all duration-300 group-hover:bg-white/30 group-hover:scale-110\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-10 h-10 text-white ml-1\",\n                                                                    fill: \"currentColor\",\n                                                                    viewBox: \"0 0 20 20\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        fillRule: \"evenodd\",\n                                                                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z\",\n                                                                        clipRule: \"evenodd\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                        lineNumber: 564,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                    lineNumber: 563,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                lineNumber: 562,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-white text-xl font-bold mb-2\",\n                                                                children: courseDetail.videoName || \"教学视频\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                lineNumber: 567,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-white/80 text-sm\",\n                                                                children: \"点击播放视频\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                lineNumber: 570,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 561,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute top-4 right-4 bg-red-500 text-white px-2 py-1 rounded text-xs font-bold\",\n                                                        children: \"HD\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 574,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                lineNumber: 545,\n                                                columnNumber: 21\n                                            }, this) : // 实际视频播放器\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative bg-black rounded-xl overflow-hidden\",\n                                                children: [\n                                                    ((_courseDetail_contentConfig = courseDetail.contentConfig) === null || _courseDetail_contentConfig === void 0 ? void 0 : (_courseDetail_contentConfig_video = _courseDetail_contentConfig.video) === null || _courseDetail_contentConfig_video === void 0 ? void 0 : _courseDetail_contentConfig_video.url) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                                        className: \"w-full h-80 object-contain\",\n                                                        controls: true,\n                                                        autoPlay: true,\n                                                        poster: courseDetail.coverImage && !courseDetail.coverImage.includes(\"example.com\") ? courseDetail.coverImage : undefined,\n                                                        onLoadStart: ()=>setVideoLoaded(true),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                                                                src: courseDetail.contentConfig.video.url,\n                                                                type: \"video/mp4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                lineNumber: 589,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            \"您的浏览器不支持视频播放\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 582,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-80 flex items-center justify-center bg-gray-100\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center text-gray-500\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-12 h-12 mx-auto mb-2\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 1.5,\n                                                                        d: \"M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                        lineNumber: 596,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                    lineNumber: 595,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"视频加载失败\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                    lineNumber: 598,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm mt-1\",\n                                                                    children: \"请检查网络连接或稍后重试\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                    lineNumber: 599,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                            lineNumber: 594,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 593,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setShowVideoPlayer(false),\n                                                        className: \"absolute top-4 right-4 bg-black/50 hover:bg-black/70 text-white rounded-full p-2 transition-colors duration-200\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-5 h-5\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M6 18L18 6M6 6l12 12\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                lineNumber: 610,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                            lineNumber: 609,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 605,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                lineNumber: 580,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                            lineNumber: 542,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl h-80 flex items-center justify-center border-2 border-dashed border-gray-300\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center text-gray-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-8 h-8 text-gray-400\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 1.5,\n                                                                d: \"M15.75 10.5l4.72-4.72a.75.75 0 011.28.53v11.38a.75.75 0 01-1.28.53l-4.72-4.72M4.5 18.75h9a2.25 2.25 0 002.25-2.25v-9a2.25 2.25 0 00-2.25-2.25h-9A2.25 2.25 0 002.25 7.5v9a2.25 2.25 0 002.25 2.25z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                lineNumber: 621,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                            lineNumber: 620,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 619,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg font-semibold text-gray-600 mb-2\",\n                                                        children: \"暂无教学视频\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 624,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"该课程暂未提供视频内容\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 625,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                lineNumber: 618,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                            lineNumber: 617,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                        lineNumber: 540,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                lineNumber: 523,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-r from-indigo-500 to-purple-600 px-6 py-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-bold text-white flex items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-6 h-6\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 637,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 636,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"教学课件\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                            lineNumber: 635,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                        lineNumber: 634,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: (courseDetail === null || courseDetail === void 0 ? void 0 : courseDetail.hasDocument) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl overflow-hidden border border-gray-200 shadow-inner\",\n                                            children: ((_courseDetail_contentConfig1 = courseDetail.contentConfig) === null || _courseDetail_contentConfig1 === void 0 ? void 0 : (_courseDetail_contentConfig_document = _courseDetail_contentConfig1.document) === null || _courseDetail_contentConfig_document === void 0 ? void 0 : _courseDetail_contentConfig_document.url) && courseDetail.contentConfig.document.url.trim() !== \"\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"iframe\", {\n                                                        src: \"\".concat(courseDetail.contentConfig.document.url, \"#toolbar=1&navpanes=1&scrollbar=1&page=1&view=FitH\"),\n                                                        className: \"w-full h-[600px] rounded-xl border-0\",\n                                                        title: \"教学课件预览\",\n                                                        allow: \"fullscreen\",\n                                                        loading: \"lazy\",\n                                                        style: {\n                                                            background: \"white\",\n                                                            boxShadow: \"inset 0 0 10px rgba(0,0,0,0.1)\"\n                                                        },\n                                                        onLoad: ()=>console.log(\"PDF课件加载完成\"),\n                                                        onError: (e)=>{\n                                                            console.error(\"PDF课件加载失败\");\n                                                            // 阻止错误传播，避免触发下载\n                                                            e.preventDefault();\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 649,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute top-4 left-4 bg-white/95 backdrop-blur-sm rounded-lg px-3 py-2 shadow-lg border border-white/20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4 text-indigo-600\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                        lineNumber: 671,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                    lineNumber: 670,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-medium text-gray-700\",\n                                                                    children: courseDetail.contentConfig.document.name || \"教学课件.pdf\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                    lineNumber: 673,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                            lineNumber: 669,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 668,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute top-4 right-4 flex gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>{\n                                                                    var _courseDetail_contentConfig_document, _courseDetail_contentConfig;\n                                                                    if (courseDetail === null || courseDetail === void 0 ? void 0 : (_courseDetail_contentConfig = courseDetail.contentConfig) === null || _courseDetail_contentConfig === void 0 ? void 0 : (_courseDetail_contentConfig_document = _courseDetail_contentConfig.document) === null || _courseDetail_contentConfig_document === void 0 ? void 0 : _courseDetail_contentConfig_document.url) {\n                                                                        window.open(courseDetail.contentConfig.document.url, \"_blank\");\n                                                                    }\n                                                                },\n                                                                className: \"bg-white/95 hover:bg-white backdrop-blur-sm rounded-lg p-2 shadow-lg border border-white/20 transition-all duration-200 hover:scale-105\",\n                                                                title: \"在新窗口中打开\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4 text-gray-600\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                        lineNumber: 692,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                    lineNumber: 691,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                lineNumber: 682,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: (courseDetail === null || courseDetail === void 0 ? void 0 : (_courseDetail_contentConfig2 = courseDetail.contentConfig) === null || _courseDetail_contentConfig2 === void 0 ? void 0 : (_courseDetail_contentConfig_document1 = _courseDetail_contentConfig2.document) === null || _courseDetail_contentConfig_document1 === void 0 ? void 0 : _courseDetail_contentConfig_document1.url) || \"#\",\n                                                                download: (courseDetail === null || courseDetail === void 0 ? void 0 : (_courseDetail_contentConfig3 = courseDetail.contentConfig) === null || _courseDetail_contentConfig3 === void 0 ? void 0 : (_courseDetail_contentConfig_document2 = _courseDetail_contentConfig3.document) === null || _courseDetail_contentConfig_document2 === void 0 ? void 0 : _courseDetail_contentConfig_document2.name) || \"教学课件.pdf\",\n                                                                className: \"bg-white/95 hover:bg-white backdrop-blur-sm rounded-lg p-2 shadow-lg border border-white/20 transition-all duration-200 hover:scale-105\",\n                                                                title: \"下载课件\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4 text-gray-600\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                        lineNumber: 704,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                    lineNumber: 703,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                lineNumber: 697,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>{\n                                                                    const iframe = document.querySelector('iframe[title=\"教学课件预览\"]');\n                                                                    if (iframe && iframe.requestFullscreen) {\n                                                                        iframe.requestFullscreen();\n                                                                    }\n                                                                },\n                                                                className: \"bg-white/95 hover:bg-white backdrop-blur-sm rounded-lg p-2 shadow-lg border border-white/20 transition-all duration-200 hover:scale-105\",\n                                                                title: \"全屏查看\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4 text-gray-600\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                        lineNumber: 720,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                    lineNumber: 719,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                lineNumber: 709,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 680,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute bottom-4 left-4 bg-blue-500/90 text-white text-xs px-3 py-1 rounded-full backdrop-blur-sm\",\n                                                        children: \"PDF预览模式\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 726,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                lineNumber: 647,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-96 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-8 h-8 text-indigo-600\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 1.5,\n                                                                    d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                    lineNumber: 735,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                lineNumber: 734,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                            lineNumber: 733,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg font-semibold text-gray-700 mb-2\",\n                                                            children: ((_courseDetail_contentConfig4 = courseDetail.contentConfig) === null || _courseDetail_contentConfig4 === void 0 ? void 0 : (_courseDetail_contentConfig_document3 = _courseDetail_contentConfig4.document) === null || _courseDetail_contentConfig_document3 === void 0 ? void 0 : _courseDetail_contentConfig_document3.name) || \"教学课件\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                            lineNumber: 738,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"课件加载中...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                            lineNumber: 741,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-32 h-2 bg-gray-200 rounded-full mx-auto overflow-hidden\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-full bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full animate-pulse\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                    lineNumber: 744,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                lineNumber: 743,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                            lineNumber: 742,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 732,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                lineNumber: 731,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                            lineNumber: 645,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl h-96 flex items-center justify-center border-2 border-dashed border-gray-300\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-8 h-8 text-gray-400\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 1.5,\n                                                                d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                lineNumber: 756,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                            lineNumber: 755,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 754,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg font-semibold text-gray-600 mb-2\",\n                                                        children: \"暂无教学课件\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 759,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"该课程暂未提供课件内容\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 760,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                lineNumber: 753,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                            lineNumber: 752,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                        lineNumber: 643,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                lineNumber: 633,\n                                columnNumber: 11\n                            }, this),\n                            seriesCourses.length > 0 && ((courseDetail === null || courseDetail === void 0 ? void 0 : courseDetail.teachingInfo) && courseDetail.teachingInfo.length > 0 ? courseDetail.teachingInfo.map((info, index)=>{\n                                // 根据标题确定图标和颜色主题\n                                const getThemeConfig = (title)=>{\n                                    if (title.includes(\"目标\")) {\n                                        return {\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 777,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                lineNumber: 776,\n                                                columnNumber: 23\n                                            }, this),\n                                            gradient: \"from-emerald-500 to-green-600\",\n                                            bgColor: \"from-emerald-50 to-green-50\",\n                                            borderColor: \"border-emerald-200\",\n                                            textColor: \"text-emerald-700\",\n                                            bulletColor: \"bg-emerald-500\"\n                                        };\n                                    } else if (title.includes(\"准备\")) {\n                                        return {\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 790,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                lineNumber: 789,\n                                                columnNumber: 23\n                                            }, this),\n                                            gradient: \"from-blue-500 to-indigo-600\",\n                                            bgColor: \"from-blue-50 to-indigo-50\",\n                                            borderColor: \"border-blue-200\",\n                                            textColor: \"text-blue-700\",\n                                            bulletColor: \"bg-blue-500\"\n                                        };\n                                    } else if (title.includes(\"重难点\")) {\n                                        return {\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 803,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                lineNumber: 802,\n                                                columnNumber: 23\n                                            }, this),\n                                            gradient: \"from-orange-500 to-red-600\",\n                                            bgColor: \"from-orange-50 to-red-50\",\n                                            borderColor: \"border-orange-200\",\n                                            textColor: \"text-orange-700\",\n                                            bulletColor: \"bg-orange-500\"\n                                        };\n                                    } else {\n                                        return {\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 816,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                lineNumber: 815,\n                                                columnNumber: 23\n                                            }, this),\n                                            gradient: \"from-purple-500 to-pink-600\",\n                                            bgColor: \"from-purple-50 to-pink-50\",\n                                            borderColor: \"border-purple-200\",\n                                            textColor: \"text-purple-700\",\n                                            bulletColor: \"bg-purple-500\"\n                                        };\n                                    }\n                                };\n                                const theme = getThemeConfig(info.title);\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gradient-to-r \".concat(theme.gradient, \" px-6 py-4\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-xl font-bold text-white flex items-center gap-3\",\n                                                children: [\n                                                    theme.icon,\n                                                    info.title\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                lineNumber: 834,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                            lineNumber: 833,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-br \".concat(theme.bgColor, \" rounded-xl p-6 border \").concat(theme.borderColor),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"space-y-4\",\n                                                    children: info.content.map((item, itemIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-start gap-4 group\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-8 h-8 \".concat(theme.bulletColor, \" rounded-full flex items-center justify-center flex-shrink-0 shadow-md group-hover:scale-110 transition-transform duration-200\"),\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-white font-bold text-sm\",\n                                                                        children: itemIndex + 1\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                        lineNumber: 847,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                    lineNumber: 846,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"\".concat(theme.textColor, \" font-medium leading-relaxed\"),\n                                                                        children: item\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                        lineNumber: 850,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                    lineNumber: 849,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, itemIndex, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                            lineNumber: 845,\n                                                            columnNumber: 27\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 843,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                lineNumber: 842,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                            lineNumber: 841,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                    lineNumber: 831,\n                                    columnNumber: 17\n                                }, this);\n                            }) : null)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                        lineNumber: 520,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-r from-teal-500 to-cyan-600 px-6 py-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold text-white flex items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-6 h-6\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 873,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 872,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"课程附件\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                            lineNumber: 871,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                        lineNumber: 870,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: (courseDetail === null || courseDetail === void 0 ? void 0 : courseDetail.additionalResources) && courseDetail.additionalResources.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-[216px] overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 hover:scrollbar-thumb-gray-400\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2 pr-2\",\n                                                        children: courseDetail.additionalResources.map((resource, index)=>{\n                                                            // 根据文件扩展名确定文件类型和样式\n                                                            const getFileTypeConfig = (title)=>{\n                                                                var _title_split_pop;\n                                                                const extension = (_title_split_pop = title.split(\".\").pop()) === null || _title_split_pop === void 0 ? void 0 : _title_split_pop.toLowerCase();\n                                                                if (extension === \"pptx\" || extension === \"ppt\") {\n                                                                    return {\n                                                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-5 h-5 text-white\",\n                                                                            fill: \"none\",\n                                                                            stroke: \"currentColor\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0a2 2 0 00-2 2v14a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                                lineNumber: 894,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                            lineNumber: 893,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        gradient: \"from-orange-500 to-red-600\",\n                                                                        bgColor: \"from-orange-50 to-red-50\",\n                                                                        borderColor: \"border-orange-200\",\n                                                                        hoverColor: \"group-hover:text-orange-700\",\n                                                                        buttonBg: \"bg-orange-100 hover:bg-orange-200 text-orange-600\",\n                                                                        fileType: \"PowerPoint 演示文稿\"\n                                                                    };\n                                                                } else if (extension === \"pdf\") {\n                                                                    return {\n                                                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-5 h-5 text-white\",\n                                                                            fill: \"none\",\n                                                                            stroke: \"currentColor\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                                lineNumber: 908,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                            lineNumber: 907,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        gradient: \"from-red-500 to-pink-600\",\n                                                                        bgColor: \"from-red-50 to-pink-50\",\n                                                                        borderColor: \"border-red-200\",\n                                                                        hoverColor: \"group-hover:text-red-700\",\n                                                                        buttonBg: \"bg-red-100 hover:bg-red-200 text-red-600\",\n                                                                        fileType: \"PDF 文档\"\n                                                                    };\n                                                                } else if (extension === \"docx\" || extension === \"doc\") {\n                                                                    return {\n                                                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-5 h-5 text-white\",\n                                                                            fill: \"none\",\n                                                                            stroke: \"currentColor\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                                lineNumber: 922,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                            lineNumber: 921,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        gradient: \"from-blue-500 to-indigo-600\",\n                                                                        bgColor: \"from-blue-50 to-indigo-50\",\n                                                                        borderColor: \"border-blue-200\",\n                                                                        hoverColor: \"group-hover:text-blue-700\",\n                                                                        buttonBg: \"bg-blue-100 hover:bg-blue-200 text-blue-600\",\n                                                                        fileType: \"Word 文档\"\n                                                                    };\n                                                                } else {\n                                                                    return {\n                                                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-5 h-5 text-white\",\n                                                                            fill: \"none\",\n                                                                            stroke: \"currentColor\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                                lineNumber: 936,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                            lineNumber: 935,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        gradient: \"from-emerald-500 to-teal-600\",\n                                                                        bgColor: \"from-emerald-50 to-teal-50\",\n                                                                        borderColor: \"border-emerald-200\",\n                                                                        hoverColor: \"group-hover:text-emerald-700\",\n                                                                        buttonBg: \"bg-emerald-100 hover:bg-emerald-200 text-emerald-600\",\n                                                                        fileType: \"附件文档\"\n                                                                    };\n                                                                }\n                                                            };\n                                                            const config = getFileTypeConfig(resource.title);\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"group relative bg-gradient-to-r \".concat(config.bgColor, \" border \").concat(config.borderColor, \" rounded-lg p-3 hover:shadow-md transition-all duration-300 cursor-pointer\"),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-10 h-10 bg-gradient-to-br \".concat(config.gradient, \" rounded-lg flex items-center justify-center shadow-md group-hover:scale-110 transition-transform duration-200\"),\n                                                                            children: config.icon\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                            lineNumber: 954,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                    className: \"text-sm font-semibold text-gray-900 \".concat(config.hoverColor, \" transition-colors\"),\n                                                                                    children: resource.title\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                                    lineNumber: 958,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-xs text-gray-500 mt-0.5\",\n                                                                                    children: [\n                                                                                        config.fileType,\n                                                                                        \" \",\n                                                                                        resource.description && \"• \".concat(resource.description)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                                    lineNumber: 961,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                            lineNumber: 957,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    className: \"p-1.5 bg-white/80 hover:bg-white rounded-md shadow-sm transition-colors duration-200\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                        className: \"w-3.5 h-3.5 text-gray-600\",\n                                                                                        fill: \"none\",\n                                                                                        stroke: \"currentColor\",\n                                                                                        viewBox: \"0 0 24 24\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                strokeLinecap: \"round\",\n                                                                                                strokeLinejoin: \"round\",\n                                                                                                strokeWidth: 2,\n                                                                                                d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                                                lineNumber: 968,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                                strokeLinecap: \"round\",\n                                                                                                strokeLinejoin: \"round\",\n                                                                                                strokeWidth: 2,\n                                                                                                d: \"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                                                lineNumber: 969,\n                                                                                                columnNumber: 35\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                                        lineNumber: 967,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                                    lineNumber: 966,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                                    href: resource.url,\n                                                                                    target: \"_blank\",\n                                                                                    rel: \"noopener noreferrer\",\n                                                                                    className: \"p-1.5 \".concat(config.buttonBg, \" rounded-md transition-colors duration-200\"),\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                        className: \"w-3.5 h-3.5\",\n                                                                                        fill: \"none\",\n                                                                                        stroke: \"currentColor\",\n                                                                                        viewBox: \"0 0 24 24\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                            strokeLinecap: \"round\",\n                                                                                            strokeLinejoin: \"round\",\n                                                                                            strokeWidth: 2,\n                                                                                            d: \"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                                            lineNumber: 979,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                                        lineNumber: 978,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                                    lineNumber: 972,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                            lineNumber: 965,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                    lineNumber: 953,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, index, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                lineNumber: 952,\n                                                                columnNumber: 25\n                                                            }, this);\n                                                        })\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 884,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 883,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-4 pt-3 border-t border-gray-200\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between text-xs text-gray-500\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \"共 \",\n                                                                    courseDetail.additionalResources.length,\n                                                                    \" 个附件\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                lineNumber: 993,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"点击下载按钮获取文件\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                lineNumber: 994,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 992,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 991,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : /* 空状态 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl h-32 flex items-center justify-center border-2 border-dashed border-gray-300\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-6 h-6 text-gray-400\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 1.5,\n                                                                d: \"M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                lineNumber: 1004,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                            lineNumber: 1003,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 1002,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-semibold text-gray-600 mb-1\",\n                                                        children: \"暂无课程附件\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 1007,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: \"该课程暂未提供附件下载\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 1008,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                lineNumber: 1001,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                            lineNumber: 1000,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                        lineNumber: 879,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                lineNumber: 869,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-br from-white via-blue-50/20 to-indigo-50/30 rounded-xl border border-blue-200/60 shadow-lg backdrop-blur-sm overflow-hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-r from-blue-50/80 to-indigo-50/80 backdrop-blur-md border-b border-blue-100/50 px-6 py-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-lg flex items-center justify-center shadow-sm\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 text-white\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                            lineNumber: 1022,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 1021,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 1020,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-bold text-gray-900 tracking-tight\",\n                                                    children: \"系列课程列表\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 1025,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center gap-1 px-3 py-1 bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-700 text-sm font-semibold rounded-full border border-blue-200/50 shadow-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-3 h-3\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0a2 2 0 00-2 2v14a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                lineNumber: 1030,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                            lineNumber: 1029,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"共\",\n                                                        seriesCourses.length,\n                                                        \"课\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 1028,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                            lineNumber: 1019,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                        lineNumber: 1018,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center py-12\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"animate-spin rounded-full h-10 w-10 border-3 border-blue-200\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                            lineNumber: 1042,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"animate-spin rounded-full h-10 w-10 border-3 border-blue-600 border-t-transparent absolute top-0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                            lineNumber: 1043,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 1041,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-3 text-gray-600 font-medium\",\n                                                    children: \"加载课程列表...\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 1045,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                            lineNumber: 1040,\n                                            columnNumber: 17\n                                        }, this) : seriesCourses.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3 h-40 overflow-y-auto custom-scrollbar\",\n                                            children: seriesCourses.map((courseItem, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"group relative bg-gradient-to-r from-gray-50/50 to-blue-50/30 hover:from-blue-50 hover:to-indigo-50 rounded-xl px-4 py-1 border border-gray-200/50 hover:border-blue-300/50 transition-all duration-300 hover:shadow-md cursor-pointer\",\n                                                    onClick: ()=>handleCourseClick(courseItem),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute left-4 top-1.5 w-6 h-6 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center text-white text-xs font-bold shadow-sm\",\n                                                            children: index + 1\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                            lineNumber: 1056,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center ml-10\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1 flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                            className: \"text-gray-800 font-medium group-hover:text-gray-900 transition-colors\",\n                                                                            style: {\n                                                                                marginBottom: 0\n                                                                            },\n                                                                            children: courseItem.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                            lineNumber: 1062,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-gray-500\",\n                                                                            children: [\n                                                                                \"第\",\n                                                                                index + 1,\n                                                                                \"课\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                            lineNumber: 1065,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                    lineNumber: 1061,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"inline-flex items-center gap-1 px-3 py-1.5 text-xs font-semibold rounded-full transition-all duration-200 \".concat(courseItem.status === 1 ? \"bg-gradient-to-r from-emerald-100 to-green-100 text-emerald-700 border border-emerald-200/50 shadow-sm\" : \"bg-gradient-to-r from-gray-100 to-slate-100 text-gray-600 border border-gray-200/50\"),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-2 h-2 rounded-full \".concat(courseItem.status === 1 ? \"bg-emerald-500\" : \"bg-gray-400\")\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                                lineNumber: 1076,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            courseItem.status === 1 ? \"已发布\" : \"草稿\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                        lineNumber: 1071,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                                    lineNumber: 1070,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                            lineNumber: 1060,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, courseItem.id, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 1050,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                            lineNumber: 1048,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-16\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 bg-gradient-to-r from-gray-100 to-gray-200 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-8 h-8 text-gray-400\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 1.5,\n                                                            d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                            lineNumber: 1090,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                        lineNumber: 1089,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 1088,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-500 font-medium\",\n                                                    children: \"暂无课程\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 1093,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-400 text-sm mt-1\",\n                                                    children: \"该系列还没有添加课程\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                                    lineNumber: 1094,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                            lineNumber: 1087,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                        lineNumber: 1038,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                                lineNumber: 1016,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                        lineNumber: 867,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n                lineNumber: 518,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\(main)\\\\course-plaza\\\\components\\\\CourseDetailView.tsx\",\n        lineNumber: 415,\n        columnNumber: 5\n    }, this);\n}\n_s(CourseDetailView, \"fijPwY+vJmwhygbo4Cj3GpvYaxQ=\");\n_c = CourseDetailView;\nvar _c;\n$RefreshReg$(_c, \"CourseDetailView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/(main)/course-plaza/components/CourseDetailView.tsx\n"));

/***/ })

});