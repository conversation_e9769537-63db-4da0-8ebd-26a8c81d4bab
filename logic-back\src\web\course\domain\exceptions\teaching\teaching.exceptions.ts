import { HttpException, HttpStatus } from '@nestjs/common';

/**
 * 并发冲突异常
 */
export class ConcurrencyConflictException extends HttpException {
  constructor(courseId: number, classId: number, lockKey: string) {
    const response = {
      code: 409,
      message: '系统繁忙，请稍后重试',
      data: {
        reason: '无法获取操作锁，可能有其他用户正在为此班级开启课程',
        courseId,
        classId,
        lockKey,
        retryAfter: 30
      }
    };
    super(response, HttpStatus.CONFLICT);
  }
}

/**
 * 重复操作异常
 */
export class DuplicateOperationException extends HttpException {
  constructor(
    courseId: number, 
    classId: number, 
    teacherId: number, 
    existingRecordId: number, 
    lastExecutionTime: string
  ) {
    const response = {
      code: 405,
      message: '该课程已在此班级开启，请勿连续重复点击',
      data: {
        reason: '检测到重复的一键上课操作',
        courseId,
        classId,
        teacherId,
        existingRecordId,
        lastExecutionTime
      }
    };
    super(response, HttpStatus.METHOD_NOT_ALLOWED);
  }
}

/**
 * 课程不存在或未发布异常
 */
export class CourseNotFoundOrNotPublishedException extends HttpException {
  constructor(courseId: number) {
    const response = {
      code: 500,
      message: '课程不存在或未发布',
      data: {
        courseId,
        reason: '指定的课程不存在或状态不是已发布'
      }
    };
    super(response, HttpStatus.INTERNAL_SERVER_ERROR);
  }
}

/**
 * 学生积分不足异常
 */
export class InsufficientStudentPointsException extends HttpException {
  constructor(
    studentId: number,
    currentPoints: number,
    requiredPoints: number,
    studentName?: string
  ) {
    const response = {
      code: 402, // 402 Payment Required - 积分不足，需要充值
      message: '学生积分不足，无法完成积分分配',
      data: {
        reason: '部分学生积分不足，无法完成一键上课操作',
        studentId,
        studentName: studentName || `学生${studentId}`,
        currentPoints,
        requiredPoints,
        shortfall: requiredPoints - currentPoints,
        suggestion: '请为学生充值积分后重试，或降低课程所需积分'
      }
    };
    super(response, HttpStatus.PAYMENT_REQUIRED);
  }
}

/**
 * 教师权限不足异常
 */
export class InsufficientTeacherPermissionException extends HttpException {
  constructor(teacherId: number, classId: number, courseId?: number, courseStatus?: number, creatorId?: number) {
    let reason = '您没有权限操作此班级';

    // 根据课程状态提供更详细的错误信息
    if (courseId && courseStatus !== undefined && creatorId !== undefined) {
      if (courseStatus === 0) {
        // 课程为草稿状态
        if (creatorId !== teacherId) {
          reason = '该课程尚未发布，只有课程创建者可以开启教学';
        }
      } else if (courseStatus === 2) {
        // 课程已归档
        reason = '该课程已归档，无法开启教学';
      }
    }

    const response = {
      code: 403,
      message: '权限不足',
      data: {
        reason,
        teacherId,
        classId,
        courseId,
        courseStatus,
        creatorId
      }
    };
    super(response, HttpStatus.FORBIDDEN);
  }
}

/**
 * 班级无学生异常
 */
export class EmptyClassException extends HttpException {
  constructor(classId: number, studentCount: number = 0) {
    const response = {
      code: 422,
      message: '业务逻辑错误',
      data: {
        reason: '班级中没有学生，无法开启课程',
        classId,
        studentCount
      }
    };
    super(response, HttpStatus.UNPROCESSABLE_ENTITY);
  }
}

/**
 * 课程设置不完整异常
 */
export class IncompleteSettingsException extends HttpException {
  constructor(courseId: number, missingSettings: string[]) {
    const response = {
      code: 422,
      message: '业务逻辑错误',
      data: {
        reason: '课程设置不完整，请先完成课程配置',
        courseId,
        missingSettings
      }
    };
    super(response, HttpStatus.UNPROCESSABLE_ENTITY);
  }
}

/**
 * 部分操作失败异常
 */
export class PartialFailureException extends HttpException {
  constructor(data: any) {
    const response = {
      code: 206,
      message: '课程开启完成，但部分操作失败',
      data
    };
    super(response, HttpStatus.PARTIAL_CONTENT);
  }
}
