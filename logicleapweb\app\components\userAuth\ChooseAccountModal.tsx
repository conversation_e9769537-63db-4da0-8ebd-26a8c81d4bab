import React, { useState } from 'react';
import { Modal } from 'antd';
import { useDispatch } from 'react-redux';
import userApi from '@/lib/api/user';
import { GetNotification } from 'logic-common/dist/components/Notification';

// 定义Role类型
export interface Role {
  userId: number;
  roleId: number;
  roleName: string;
  nickName: string;
  avatar?: string;
  schoolInfo?: {
    province: string;
    city: string;
    district: string;
    schoolName: string;
  };
  studentNumber?: string;
}

// 组件props类型
export interface ChooseAccountModalProps {
  isOpen: boolean;
  onClose: () => void;
  roleList: Role[];
  sessionId: string;
  setShowRoleSelection: (value: boolean) => void;
  roleSelectResolverRef: React.MutableRefObject<((value: any) => void) | null>;
}

// 角色选择设置弹窗组件
const ChooseAccountModal: React.FC<ChooseAccountModalProps> = ({
  isOpen,
  onClose,
  roleList,
  sessionId,
  setShowRoleSelection,
  roleSelectResolverRef
}) => {
  const [loading, setLoading] = useState(false);
  const dispatch = useDispatch();

  // 防止弹窗立即关闭
  const handleRoleSelect = async (role: Role) => {
    const notification = GetNotification();
    try {
      setLoading(true);
      console.log("选择角色:", role);

      // 判断是否有sessionId，决定使用哪个接口
      let response;
      if (sessionId) {
        // 使用select-identity接口（未登录用户选择角色）
        response = await userApi.selectIdentity(role.userId, sessionId);
      } else {
        // 使用switchToUser接口（已登录用户切换用户ID）
        response = await userApi.switchToUser(role.userId);
      }

      if (response.code === 200) {
        // 从响应体获取data.userinfo 然后存到本地存储
        if (!response.data?.userInfo) {
          throw new Error('获取用户信息失败');
        }
        const data = response.data;
        const userInfo = data.userInfo;
        // 判断当前userInfo的roleId如果为999，那么设置本地存储标志位，后续可以加载后在挂载一次的地方进行判断例如NavBar
        if (userInfo.roleId === 999) {
          // 2.设置一个标记，表示需要进行身份认证
          localStorage.setItem('needRoleAuth', 'true');
        }

        // 存储数据到本地存储
        localStorage.setItem('user', JSON.stringify(userInfo));
        localStorage.setItem('token', data.token);
        localStorage.setItem('refreshToken', data.refreshToken || '');
        localStorage.setItem('userId', userInfo?.id?.toString() || '');

        // 确保数据都已经更新后再触发回调，使用更长的延迟
        // 解析Promise，允许登录流程继续
        if (roleSelectResolverRef.current) {
          console.log("解析角色选择Promise，继续登录流程");
          roleSelectResolverRef.current(response);
          roleSelectResolverRef.current = null;
        }
        notification.success("选择账号成功，即将刷新网页...")
        // 刷新页面以应用新角色权限，而不是调用onClose
        window.location.reload();
      } else {
        notification.error(response.message || '选择账号失败');
        setLoading(false);
      }
    } catch (error) {
      console.error('选择账号失败:', error);
      notification.error('选择账号失败，请稍后重试');
      setLoading(false);
    }
  };

  if (!isOpen) {
    console.log("ChooseRoleModal不显示，因为isOpen为false");
    return null;
  }

  return (
    <Modal open={isOpen} footer={null} onCancel={onClose} width={900} centered>
      <div className="bg-blue-50 rounded-xl p-8 flex flex-col items-center border border-blue-200">
        <h2 className="text-2xl font-semibold mb-8">请选择您要登录的账号</h2>
        <div className="flex gap-8 justify-center w-full">
          {roleList.map((role, idx) => (
            <div
              key={role.userId + '-' + role.roleId}
              className={`relative bg-white rounded-xl shadow-md flex flex-col items-center px-10 py-8 cursor-pointer hover:shadow-lg hover:translate-y-[-20px] transition-transform duration-100 will-change-transform min-w-[220px] ${loading ? 'opacity-50 cursor-not-allowed' : ''}`}
              style={{ transformOrigin: 'center center' }}
              onClick={() => !loading && handleRoleSelect(role)}
            >
              {/* 头像 */}
              <div className="w-24 h-24 rounded-full  mb-4 flex items-center justify-center">
                <img src={role.avatar || "https://logicleap.oss-cn-guangzhou.aliyuncs.com/base/c2b0f283138abaad873795e1dd8e18b_resized.png"} />
              </div>
              {/* 角色标签 */}
              <div className="absolute top-4 right-4">
                <span className="bg-blue-100 text-blue-600 text-xs px-3 py-1 rounded-full border border-blue-200">
                  {role.roleName}
                </span>
              </div>
              {/* 昵称 */}
              <div className="text-lg font-medium mb-1">{role.nickName}</div>
              {/* 地区 */}
              {role.schoolInfo && (
                <div className="text-gray-500 text-sm mb-1">
                  {role.schoolInfo.province}{role.schoolInfo.city}{role.schoolInfo.district}
                </div>
              )}
              {/* 学校名 */}
              {role.schoolInfo && (
                <div className="text-xl font-bold text-blue-800 mb-1">{role.schoolInfo.schoolName}</div>
              )}
              {/* 学号（可选） */}
              {role.studentNumber && (
                <div className="text-xs text-gray-400">学号：{role.studentNumber}</div>
              )}
            </div>
          ))}
        </div>
      </div>
    </Modal>
  );
};

export default ChooseAccountModal; 