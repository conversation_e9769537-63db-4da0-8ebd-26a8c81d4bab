import { Injectable, Logger } from '@nestjs/common';
import { QueryMonitorService } from '../service/query-monitor.service';

export interface QueryInfo {
  query: string;
  parameters?: any[];
  startTime: number;
  context?: string;
}

@Injectable()
export class QueryInterceptor {
  private readonly logger = new Logger(QueryInterceptor.name);
  private activeQueries = new Map<string, QueryInfo>();

  constructor(private readonly queryMonitorService: QueryMonitorService) {}

  /**
   * 查询开始前调用
   */
  beforeQuery(query: string, parameters?: any[], context?: string): string {

    const queryId = this.generateQueryId();
    const queryInfo: QueryInfo = {
      query,
      parameters,
      startTime: Date.now(),
      context
    };
    // console.log("查询前信息",queryInfo);
    this.activeQueries.set(queryId, queryInfo);
    return queryId;
  }

  /**
   * 慢查询或者查询错误后调用
   */
  afterSlowQuery(queryId: string, error?: Error): void {
    const queryInfo = this.activeQueries.get(queryId);
    if (!queryInfo) {
      return;
    }
    const executionTime = Date.now() - queryInfo.startTime;
    console.log("慢查询执行结束,总耗时为",executionTime);
    // 记录慢查询执行
    this.queryMonitorService.recordQuery(
      queryInfo.query,
      executionTime,
      queryInfo.parameters,
      queryInfo.context
    );

    // 如果有错误，记录错误日志
    if (error) {
      this.logger.error(`查询执行失败 [${executionTime}ms]: ${error.message}`, {
        query: queryInfo.query,
        parameters: queryInfo.parameters,
        executionTime,
        error: error.message,
        context: queryInfo.context
      });
    }

    // 清理活跃查询记录
    this.activeQueries.delete(queryId);
  }

  /**
   * 生成查询ID
   */
  private generateQueryId(): string {
    return `query_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 获取当前活跃查询数量
   */
  getActiveQueryCount(): number {
    return this.activeQueries.size;
  }

  /**
   * 获取活跃查询信息
   */
  getActiveQueries(): QueryInfo[] {
    return Array.from(this.activeQueries.values()).map(info => ({
      ...info,
      runningTime: Date.now() - info.startTime
    }));
  }

  /**
   * 清理超时的活跃查询（防止内存泄漏）
   */
  cleanupTimeoutQueries(timeoutMs: number = 300000): void { // 默认5分钟超时
    const now = Date.now();
    const timeoutQueries: string[] = [];

    for (const [queryId, queryInfo] of this.activeQueries.entries()) {
      if (now - queryInfo.startTime > timeoutMs) {
        timeoutQueries.push(queryId);
        
        // 记录超时查询
        this.logger.warn(`检测到超时查询 [${now - queryInfo.startTime}ms]`, {
          query: queryInfo.query,
          parameters: queryInfo.parameters,
          context: queryInfo.context
        });
      }
    }

    // 清理超时查询
    timeoutQueries.forEach(queryId => {
      this.activeQueries.delete(queryId);
    });

    if (timeoutQueries.length > 0) {
      this.logger.warn(`清理了 ${timeoutQueries.length} 个超时查询`);
    }
  }
}
