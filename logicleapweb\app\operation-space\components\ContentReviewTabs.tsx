import React, { useState } from 'react';
import { Tabs, Modal } from 'antd';
import WorkReviewComponent from '../../admin-space/components/review/WorkReviewComponent';
import ParticipationReviewComponent from '../../admin-space/components/review/ParticipationReviewComponent';

const ContentReviewTabs: React.FC<{visible: boolean, onClose: () => void}> = ({visible, onClose}) => {
  const [activeTabKey, setActiveTabKey] = useState('work');

  const items = [
    {
      key: 'work',
      label: '作品审核',
      children: <WorkReviewComponent />
    },
    {
      key: 'participation',
      label: '参赛作品审核',
      children: <ParticipationReviewComponent />
    }
  ];

  return (
    <Modal
      title="内容审核管理"
      open={visible}
      onCancel={onClose}
      footer={null}
      width={1200}
      className="review-modal"
      style={{ top: 20 }}
      styles={{ body: { padding: 0 } }}
      centered
    >
      <Tabs
        activeKey={activeTabKey}
        onChange={setActiveTabKey}
        items={items}
        tabPosition="left"
        style={{ minHeight: '600px' }}
      />
    </Modal>
  );
};

export default ContentReviewTabs; 