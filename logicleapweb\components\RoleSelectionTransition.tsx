'use client'

import React, { useState, useEffect } from 'react';
import { FormTransitionContainer } from './FormTransitionContainer';
import RoleSelectionModalForNewUser from '../app/components/userAuth/RoleSelectionModalForNewUser';
import userApi from '@/lib/api/user';
import { GetNotification } from 'logic-common/dist/components/Notification';
import { UserOutlined, BookOutlined, TeamOutlined } from '@ant-design/icons';
import { Button, Spin } from 'antd';

interface RoleSelectionTransitionProps {
  onClose: () => void;
  onSuccess: () => void;
  userId: number;
}

// 角色Id的map映射
const roleIdMap = {
  'student': 1, // 学生角色Id
  'teacher': 2, // 教师角色Id
  'normal': 3, // 普通用户角色Id
}

// 定义角色key类型
type RoleType = keyof typeof roleIdMap;

const RoleSelectionTransition: React.FC<RoleSelectionTransitionProps> = ({ 
  onClose, 
  onSuccess,
  userId 
}) => {
  const [activeStep, setActiveStep] = useState(0);
  const [selectedRole, setSelectedRole] = useState<RoleType | ''>('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const nt = GetNotification();

  // 处理关闭模态框
  const handleCloseModal = () => {
    // 如果用户直接关闭，默认分配普通用户角色
    onClose();
  };

  // 处理角色选择提交
  const handleSubmit = async () => {
    if (selectedRole) {
      setIsSubmitting(true);
      try {
        console.log("准备分配角色:", selectedRole);
        // 根据key映射对应的roleID
        const selectedRoleId = roleIdMap[selectedRole];
        console.log("映射的roleId:", selectedRoleId);
        
        // 如果是普通用户3或者是学生1就直接更新用户角色
        if (selectedRoleId === 1 || selectedRoleId === 3) {
          console.log("分配角色, userId:", userId, "roleId:", selectedRoleId);
          const res = await userApi.assignRole(userId, selectedRoleId);
          console.log("分配角色响应:", res);
          
          if (res.data.code === 200) {
            nt.success('身份绑定成功！');
            onSuccess();
          } else {
            console.error("角色分配API响应错误:", res.data);
            nt.error(res.data.message || '身份绑定失败，请稍后重试');
          }
        }
        // 如果是教师就弹出教师认证
        if (selectedRoleId === 2) {
          console.log("选择教师角色，显示教师认证弹窗");
          // 这里暂时直接分配为教师角色，实际项目中应该有单独的教师认证流程
          const res = await userApi.assignRole(userId, selectedRoleId);
          if (res.data.code === 200) {
            nt.success('教师身份绑定成功！');
            onSuccess();
          } else {
            nt.error(res.data.message || '身份绑定失败，请稍后重试');
          }
        }
      } catch (error) {
        console.error('角色选择失败:', error);
        nt.error('身份绑定失败，请稍后重试');
      } finally {
        setIsSubmitting(false);
      }
    } else {
      nt.warning('请选择一个身份');
    }
  };

  // 角色卡片配置
  const roleCards = [
    {
      type: 'normal',
      title: '普通用户',
      icon: <UserOutlined style={{ fontSize: '36px' }} />,
      description: '体验基础功能'
    },
    {
      type: 'student',
      title: '学生',
      icon: <BookOutlined style={{ fontSize: '36px' }} />,
      description: '学习与成长'
    },
    {
      type: 'teacher',
      title: '教师',
      icon: <TeamOutlined style={{ fontSize: '36px' }} />,
      description: '教学与管理'
    }
  ];

  // 添加从右侧滑入的动画效果
  useEffect(() => {
    const timer = setTimeout(() => {
      const container = document.querySelector('.role-selection-container');
      if (container) {
        container.classList.add('slide-in');
      }
    }, 100);

    return () => clearTimeout(timer);
  }, []);

  return (
    <div 
      className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50"
      onClick={handleCloseModal}
    >
      <div 
        className="bg-white rounded-2xl shadow-2xl w-[700px] max-w-[90vw] overflow-hidden role-selection-container transform translate-x-full transition-transform duration-500"
        onClick={(e) => e.stopPropagation()}
      >
        <FormTransitionContainer activeStep={activeStep} transitionDuration={400}>
          <div className="p-8">
            {/* 弹框信息 */}
            <div className="py-8 text-center">
              <h2 className="text-2xl font-bold text-gray-800">请确认你的身份</h2>
              <p className="mt-2 text-gray-500">选择一个适合您的身份，以获得相应的功能和权限</p>
            </div>

            {/* 角色卡片wrapper */}
            <div className="flex flex-wrap justify-center gap-6 px-6 pb-6">
              {roleCards.map((card) => (
                <div
                  key={card.type}
                  className={`w-[180px] p-6 rounded-xl cursor-pointer transition-all duration-300 transform hover:scale-105 ${selectedRole === card.type
                    ? 'bg-blue-500 text-white shadow-lg'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  onClick={() => {
                    setSelectedRole(selectedRole === card.type ? '' : card.type as RoleType);
                  }}
                >
                  <div className="flex flex-col items-center text-center">
                    <div className={`mb-4 ${selectedRole === card.type ? 'text-white' : 'text-blue-500'}`}>
                      {card.icon}
                    </div>
                    <h3 className="text-lg font-medium mb-2">{card.title}</h3>
                    <p className={`text-sm ${selectedRole === card.type ? 'text-blue-100' : 'text-gray-500'}`}>
                      {card.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>

            {/* 确定按钮区域 */}
            <div className="flex justify-center py-6 border-t border-gray-100 mt-4">
              <Button
                type="primary"
                size="large"
                className="px-8 h-10 rounded-lg"
                style={{
                  backgroundColor: selectedRole ? '#1890ff' : '#d9d9d9',
                  borderColor: selectedRole ? '#1890ff' : '#d9d9d9'
                }}
                onClick={handleSubmit}
                disabled={!selectedRole || isSubmitting}
              >
                {isSubmitting ? <Spin size="small" /> : '确认选择'}
              </Button>
            </div>
          </div>
        </FormTransitionContainer>
      </div>
    </div>
  );
};

export default RoleSelectionTransition; 