'use client';

import React, { useState } from 'react';
import { ArrowRight } from 'lucide-react';
import SchoolSelectionModal from './SchoolSelectionModal';
import ClassSelectionModal from './ClassSelectionModal';
import TemplateSelectionModal from './TemplateSelectionModal';
import NewPublishTaskModal from './NewPublishTaskModal';

import { schoolApi } from '@/lib/api/school';
import './SchoolSelectionModal.css';
import './TemplateSelectionModal.css';

const QuickActions = () => {
    const [isSchoolModalOpen, setIsSchoolModalOpen] = useState(false);
    const [isClassModalOpen, setIsClassModalOpen] = useState(false);
    const [isTemplateModalOpen, setIsTemplateModalOpen] = useState(false);
    const [isPublishTaskModalOpen, setIsPublishTaskModalOpen] = useState(false);

    const [selectedAction, setSelectedAction] = useState('');
    const [selectedSchool, setSelectedSchool] = useState<any>(null);
    const [selectedClass, setSelectedClass] = useState<any>(null);
    const [selectedTemplate, setSelectedTemplate] = useState<any>(null);

    const actions = [
        { title: '发布任务', description: '点击为学生快速发布任务', color: 'green' },
        { title: '分配积木', description: '点击为学生快速分配积木', color: 'purple' },
        { title: '分配能量', description: '点击为学生快速分配能量', color: 'orange' }
    ];

    const handleActionClick = async (actionTitle: string) => {
        setSelectedAction(actionTitle);

        try {
            // 获取用户的学校列表
            const response = await schoolApi.getUserSchools();

            if (response.data.code === 200) {
                const schoolsData = response.data.data || [];

                if (schoolsData.length === 1) {
                    // 只有一个学校，直接选择并跳到班级选择
                    setSelectedSchool(schoolsData[0]);
                    setIsClassModalOpen(true);
                } else if (schoolsData.length > 1) {
                    // 多个学校，显示学校选择弹窗
                    setIsSchoolModalOpen(true);
                } else {
                    // 没有学校，可以显示提示信息
                    console.warn('用户没有关联的学校');
                }
            }
        } catch (error) {
            console.error('获取学校列表失败:', error);
            // 出错时仍然显示学校选择弹窗
            setIsSchoolModalOpen(true);
        }
    };

    const handleCloseModal = () => {
        setIsSchoolModalOpen(false);
        setIsClassModalOpen(false);
        setIsTemplateModalOpen(false);
        setIsPublishTaskModalOpen(false);
        setSelectedAction('');
        setSelectedSchool(null);
        setSelectedClass(null);
    };

    const handleSchoolSelect = (school: any) => {
        setSelectedSchool(school);
        setIsSchoolModalOpen(false);
        setIsClassModalOpen(true);
    };

    const handleClassSelect = (classData: any) => {
        setSelectedClass(classData);
        setIsClassModalOpen(false);

        // 根据操作类型决定下一步
        if (selectedAction === '发布任务') {
            // 发布任务直接跳过模板选择，进入发布任务弹窗
            setIsPublishTaskModalOpen(true);
        } else {
            // 分配积木和分配能量需要进入模板选择
            setIsTemplateModalOpen(true);
        }
    };

    const handleBackToSchool = () => {
        setIsClassModalOpen(false);
        setIsSchoolModalOpen(true);
    };

    const handleBackToClass = () => {
        setIsTemplateModalOpen(false);
        setIsClassModalOpen(true);
    };

    const handlePublishTaskConfirm = async (taskData: any) => {
        console.log('发布任务确认:', taskData);

        if (taskData.publishedTaskId) {
            // 任务已经在NewPublishTaskModal中发布成功
            console.log('任务发布成功，任务ID:', taskData.publishedTaskId);
            console.log('发布到学校:', selectedSchool?.name);
            console.log('发布到班级:', selectedClass?.name);
            console.log('选择的学生数量:', taskData.modalData?.selectedStudents?.length || 0);

            // 可以在这里添加额外的成功处理逻辑
            // 比如刷新任务列表、显示成功页面等
        }

        handleCloseModal();
    };

    const handlePublishTaskBack = () => {
        setIsPublishTaskModalOpen(false);
        setIsClassModalOpen(true);
    };





    return (
        <section className="quick-actions">
            <h2 className="section-title">快速操作</h2>
            <div className="actions-container">
                {actions.map(action => (
                    <div
                        key={action.title}
                        className={`action-card ${action.color}`}
                        onClick={() => handleActionClick(action.title)}
                    >
                        <div className="card-content">
                            <h3>{action.title}</h3>
                            <p>{action.description}</p>
                        </div>
                        <div className="card-icon">
                            <ArrowRight />
                        </div>
                    </div>
                ))}
            </div>

            <SchoolSelectionModal
                isOpen={isSchoolModalOpen}
                onClose={handleCloseModal}
                actionType={selectedAction}
                onSchoolSelect={handleSchoolSelect}
            />

            <ClassSelectionModal
                isOpen={isClassModalOpen}
                onClose={handleCloseModal}
                onBack={handleBackToSchool}
                actionType={selectedAction}
                selectedSchool={selectedSchool}
                onClassSelect={handleClassSelect}
            />

            <TemplateSelectionModal
                isOpen={isTemplateModalOpen}
                onClose={handleCloseModal}
                onBack={handleBackToClass}
                onConfirm={handlePublishTaskConfirm}
                actionType={selectedAction}
                selectedSchool={selectedSchool}
                selectedClass={selectedClass}
            />

            <NewPublishTaskModal
                isOpen={isPublishTaskModalOpen}
                onClose={() => setIsPublishTaskModalOpen(false)}
                onBack={handlePublishTaskBack}
                onConfirm={handlePublishTaskConfirm}
                modalData={{
                    selectedDistribution: 'none',
                    energyAmount: '0',
                    selectedTemplate: selectedTemplate || { id: 1, name: '默认模板' },
                    selectedStudents: selectedClass?.students?.map((s: any) => s.id) || [],
                    selectedSchool: selectedSchool,
                    selectedClass: selectedClass
                }}
            />


        </section>
    );
};

export default QuickActions; 