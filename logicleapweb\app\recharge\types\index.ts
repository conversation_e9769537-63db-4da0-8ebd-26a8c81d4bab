export interface RechargeOption {
  id: string;
  title: string;
  subtitle: string;
  amount: number;
  originalAmount?: number; // 原价，用于显示打折
  bonus: number;
  icon: string;
  features: string[];
  detailedFeatures: DetailedFeature[];
  popular: boolean;
  gradient: string;
  bgGradient: string;
  // 新增字段，兼容后端API
  packageId?: number;
  packageName?: string;
  packageDescription?: string;
  points?: number;
  validityDays?: number;
  currentPrice?: number;
  discountRate?: number;
  savings?: number;
  currency?: string;
}

export interface DetailedFeature {
  name: string;
  value: string;
  icon: string;
}

export interface PaymentMethod {
  id: string;
  name: string;
  icon: string;
  qrCode?: string;
}

export interface PaymentInfo {
  orderId: string;
  amount: number;
  method: string;
  status: 'pending' | 'success' | 'failed';
  createTime: Date;
  qrCode?: string; // 二维码URL或base64数据
}

export interface RechargeRecord {
  id: string;
  amount: number;
  bonus: number;
  method: string;
  status: 'pending' | 'success' | 'failed' | 'cancelled';
  createTime: Date;
  completeTime?: Date;
  packageName?: string; // 套餐名称，用于套餐订单记录
}
