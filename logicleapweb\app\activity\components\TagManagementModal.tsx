import React, { useState, useEffect } from 'react';
import { Modal, Button, Table, Space, Tag, Form, Input, ColorPicker, Popconfirm } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import { tagApi } from '@/lib/api/activity';
import type { TablePaginationConfig } from 'antd/es/table';
import type { FilterValue, SorterResult } from 'antd/es/table/interface';
import { GetNotification } from 'logic-common/dist/components/Notification';

interface TagManagementModalProps {
  visible: boolean;
  onCancel: () => void;
  onSelectTags: (selectedTagIds: number[]) => void;
  selectedTagIds?: number[];
}

interface TagItem {
  id: number;
  name: string;
  color?: string;
  description?: string;
  createTime?: string;
}

const TagManagementModal: React.FC<TagManagementModalProps> = ({
  visible,
  onCancel,
  onSelectTags,
  selectedTagIds = []
}) => {
  const [form] = Form.useForm();
  const [tags, setTags] = useState<TagItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>(selectedTagIds);
  const [pagination, setPagination] = useState<TablePaginationConfig>({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  
  const [editingTag, setEditingTag] = useState<TagItem | null>(null);
  const [isFormVisible, setIsFormVisible] = useState(false);
  const [formLoading, setFormLoading] = useState(false);
  const [searchKeyword, setSearchKeyword] = useState('');

  // 获取标签列表
  const fetchTags = async (page = 1, size = 10, keyword = '') => {
    setLoading(true);
    try {
      const res = await tagApi.getList({
        page,
        size,
        keyword,
      });
      
      // 处理不同格式的响应数据
      let tagList = [];
      let totalCount = 0;

      if (res?.data) {
        // 处理新的API格式：{ data: { list: [], pagination: { total: number } } }
        if (res.data.data && res.data.data.list && Array.isArray(res.data.data.list)) {
          tagList = res.data.data.list;
          totalCount = res.data.data.pagination?.total || 0;
        }
        // 处理标准格式：{ list: [], pagination: { total: number } }
        else if (res.data.list && Array.isArray(res.data.list)) {
          tagList = res.data.list;
          totalCount = res.data.pagination?.total || 0;
        }
        // 处理替代格式：直接返回数组
        else if (Array.isArray(res.data)) {
          tagList = res.data;
          totalCount = res.data.length;
        }
        // 处理其他可能的格式
        else if (typeof res.data === 'object') {
          console.log('Unexpected response format:', res.data);
          tagList = [];
          totalCount = 0;
        }
      }
      
      setTags(tagList);
      setPagination({
        ...pagination,
        current: page,
        pageSize: size,
        total: totalCount,
      });
      
    } catch (error) {
      console.error('获取标签列表失败:', error);
      const notification = GetNotification();
      notification.error('获取标签列表失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (visible) {
      fetchTags(pagination.current, pagination.pageSize, searchKeyword);
      setSelectedRowKeys(selectedTagIds);
    }
  }, [visible, selectedTagIds]);

  // 表格变化处理
  const handleTableChange = (
    newPagination: TablePaginationConfig,
    filters: Record<string, FilterValue | null>,
    sorter: SorterResult<TagItem> | SorterResult<TagItem>[]
  ) => {
    fetchTags(newPagination.current, newPagination.pageSize, searchKeyword);
  };

  // 搜索标签
  const handleSearch = () => {
    fetchTags(1, pagination.pageSize, searchKeyword);
  };

  // 打开添加标签表单
  const handleAddTag = () => {
    setEditingTag(null);
    form.resetFields();
    setIsFormVisible(true);
  };

  // 打开编辑标签表单
  const handleEditTag = (tag: TagItem) => {
    setEditingTag(tag);
    form.setFieldsValue({
      name: tag.name,
      description: tag.description,
      color: tag.color,
    });
    setIsFormVisible(true);
  };

  // 提交表单
  const handleFormSubmit = async () => {
    try {
      const values = await form.validateFields();
      setFormLoading(true);
      const notification = GetNotification();
      
      // 转换颜色值
      const colorString = values.color?.toHexString ? values.color?.toHexString() : values.color;
      
      if (editingTag) {
        // 更新标签
        await tagApi.update(editingTag.id, {
          name: values.name,
          description: values.description,
          color: colorString,
        });
        notification.success('更新标签成功');
      } else {
        // 创建标签
        await tagApi.create({
          name: values.name,
          description: values.description,
          color: colorString,
        });
        notification.success('创建标签成功');
      }
      
      setIsFormVisible(false);
      fetchTags(pagination.current, pagination.pageSize, searchKeyword);
    } catch (error: any) {
      const notification = GetNotification();
      if (error.response?.data?.message) {
        notification.error(error.response?.data?.message);
      } else {
        notification.error(editingTag ? '更新标签失败' : '创建标签失败');
      }
      console.error(editingTag ? '更新标签失败:' : '创建标签失败:', error);
    } finally {
      setFormLoading(false);
    }
  };

  // 删除标签
  const handleDeleteTag = async (id: number) => {
    const notification = GetNotification();
    try {
      await tagApi.delete(id);
      notification.success('删除标签成功');
      // 如果在选中的标签中，则移除
      if (selectedRowKeys.includes(id)) {
        const newSelectedRowKeys = selectedRowKeys.filter(key => key !== id);
        setSelectedRowKeys(newSelectedRowKeys);
      }
      fetchTags(pagination.current, pagination.pageSize, searchKeyword);
    } catch (error) {
      console.error('删除标签失败:', error);
      notification.error('删除标签失败');
    }
  };

  // 行选择变化
  const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
    setSelectedRowKeys(newSelectedRowKeys);
  };

  // 确认选择标签
  const handleConfirmSelection = () => {
    onSelectTags(selectedRowKeys as number[]);
    onCancel();
  };

  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
  };

  const columns = [
    {
      title: '标签名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: TagItem) => (
        <Tag color={record.color || '#1677ff'}>{text}</Tag>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: TagItem) => (
        <Space size="middle">
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleEditTag(record)}
          />
          <Popconfirm
            title="确定要删除这个标签吗？"
            onConfirm={() => handleDeleteTag(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
            />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <>
      <Modal
        title="标签管理"
        open={visible}
        onCancel={onCancel}
        width={800}
        footer={[
          <Button key="cancel" onClick={onCancel}>
            取消
          </Button>,
          <Button key="confirm" type="primary" onClick={handleConfirmSelection}>
            确认选择
          </Button>,
        ]}
      >
        <div style={{ marginBottom: 16 }}>
          <Input.Search
            placeholder="搜索标签"
            allowClear
            value={searchKeyword}
            onChange={e => setSearchKeyword(e.target.value)}
            onSearch={handleSearch}
            style={{ width: 300, marginRight: 16 }}
          />
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAddTag}
          >
            添加标签
          </Button>
        </div>
        <Table
          rowKey="id"
          rowSelection={rowSelection}
          columns={columns}
          dataSource={tags}
          pagination={pagination}
          loading={loading}
          onChange={handleTableChange}
        />
      </Modal>

      <Modal
        title={editingTag ? '编辑标签' : '添加标签'}
        open={isFormVisible}
        onOk={handleFormSubmit}
        onCancel={() => setIsFormVisible(false)}
        confirmLoading={formLoading}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="name"
            label="标签名称"
            rules={[{ required: true, message: '请输入标签名称' }]}
          >
            <Input placeholder="请输入标签名称" />
          </Form.Item>
          
          <Form.Item
            name="description"
            label="标签描述"
          >
            <Input.TextArea placeholder="请输入标签描述" />
          </Form.Item>
          
          <Form.Item
            name="color"
            label="标签颜色"
          >
            <ColorPicker />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default TagManagementModal; 