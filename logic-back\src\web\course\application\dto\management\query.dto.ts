import { IsOptional, IsInt, Min, IsString } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

/**
 * 获取我的系列课程列表查询参数DTO
 */
export class GetMyCourseSeriesQueryDto {
  @ApiProperty({
    description: '页码，默认1',
    required: false,
    minimum: 1,
    default: 1,
    example: 1
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Type(() => Number)
  page?: number = 1;

  @ApiProperty({
    description: '每页数量，默认10',
    required: false,
    minimum: 1,
    maximum: 100,
    default: 10,
    example: 10
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Type(() => Number)
  pageSize?: number = 10;

  @ApiProperty({
    description: '状态筛选：0=草稿，1=已发布，2=已归档',
    required: false,
    enum: [0, 1, 2],
    example: 0
  })
  @IsOptional()
  @IsInt()
  @Type(() => Number)
  status?: number;

  @ApiProperty({
    description: '搜索关键词',
    required: false,
    maxLength: 100,
    example: 'JavaScript'
  })
  @IsOptional()
  @IsString()
  keyword?: string;
}

/**
 * 获取系列下课程列表查询参数DTO
 */
export class GetSeriesCoursesQueryDto {
  @ApiProperty({
    description: '页码，默认1',
    required: false,
    minimum: 1,
    default: 1,
    example: 1
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Type(() => Number)
  page?: number = 1;

  @ApiProperty({
    description: '每页数量，默认20',
    required: false,
    minimum: 1,
    maximum: 100,
    default: 20,
    example: 20
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Type(() => Number)
  pageSize?: number = 20;

  @ApiProperty({
    description: '状态筛选：0=草稿，1=已发布，2=已归档',
    required: false,
    enum: [0, 1, 2],
    example: 0
  })
  @IsOptional()
  @IsInt()
  @Type(() => Number)
  status?: number;
}
