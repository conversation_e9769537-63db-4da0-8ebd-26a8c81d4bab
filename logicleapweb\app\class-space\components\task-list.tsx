'use client'

import React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react';
import { Card, Empty, Spin, Button, Tag, Select } from 'antd';
import { ReloadOutlined } from '@ant-design/icons';
import { Eye } from 'lucide-react';
import taskApi from '@/lib/api/task';
import { TaskStatus } from '@/lib/api/task';
import { eventsTaskApi } from '@/lib/api/activity/events-task';
import { GetNotification } from 'logic-common/dist/components/Notification';
import EventsTaskModal from './EventsTaskModal';

interface TaskListProps {
  userId: number;
  onTaskClick: (task: Task) => void;
  onlyEventsTask?: boolean; // 是否只显示赛事任务
}

interface Task {
  id: number;
  taskName: string;
  taskDescription: string;
  taskType: number;
  priority: number;
  startDate?: string;
  endDate: Date;
  taskContent?: string;
  attachments?: string;
  assignments: TaskAssignment[];
  onSubmitted?: () => void;
  isEventsTask?: boolean; // 标识是否为赛事任务
}

// 赛事任务接口
interface EventsTask {
  id: number;
  userId: number;
  activityId: number;
  eventName: string;
  startTime: string;
  endTime: string;
  instructorName?: string;
  schoolName?: string;
  contactPerson?: string;
  contactPhone?: string;
  status: number; // 0-待开始 1-进行中 2-已提交 3-已审核 4-审核不通过
  workId?: number;
  workFile?: string;
  workDescription?: string;
  creatorId?: number;
  createTime: string;
  updateTime: string;
  remark?: string;
}

interface TaskAssignment {
  id: number;
  taskId: number;
  studentId: number;
  taskStatus: number;
  submitTime?: Date;
  taskScore?: number;
  feedback?: string;
  workId?: number;
}

export interface TaskListRef {
  fetchTasks: () => Promise<void>;
  openTaskById: (taskId: number) => void;
}

const TaskList = forwardRef<TaskListRef, TaskListProps>(({ userId, onTaskClick, onlyEventsTask = false }, ref) => {
  const [tasks, setTasks] = useState<Task[]>([]);
  const [eventsTasks, setEventsTasks] = useState<EventsTask[]>([]);
  const [loading, setLoading] = useState(false);
  const [activeTaskFilter, setActiveTaskFilter] = useState('all');
  const [taskSortBy, setTaskSortBy] = useState('newest');
  const [showEventsTaskModal, setShowEventsTaskModal] = useState(false);
  const [selectedEventsTask, setSelectedEventsTask] = useState<Task | null>(null);
  const notification = GetNotification();

  // 获取赛事任务列表
  const fetchEventsTasks = async () => {
    try {
      const response = await eventsTaskApi.getMyTasks();
      if (response.data.code === 200) {
        const eventsTaskList = response.data.data || [];
        setEventsTasks(eventsTaskList);
        return eventsTaskList;
      }
      return [];
    } catch (error) {
      console.error('获取赛事任务列表失败:', error);
      return [];
    }
  };

  // 将赛事任务转换为Task格式
  const convertEventsTaskToTask = (eventsTask: EventsTask): Task => {
    return {
      id: eventsTask.id,
      taskName: eventsTask.eventName,
      taskDescription: eventsTask.remark || '赛事任务',
      taskType: 999, // 特殊类型标识赛事任务
      priority: 1,
      startDate: eventsTask.startTime,
      endDate: new Date(eventsTask.endTime),
      taskContent: eventsTask.workDescription || '',
      attachments: eventsTask.workFile || '',
      assignments: [{
        id: eventsTask.id,
        taskId: eventsTask.id,
        studentId: eventsTask.userId,
        taskStatus: eventsTask.status, // 使用赛事任务的状态
        submitTime: undefined,
        taskScore: undefined,
        feedback: undefined,
        workId: eventsTask.workId
      }],
      isEventsTask: true // 标识为赛事任务
    };
  };

  // 获取任务列表
  const fetchTasks = async () => {
    try {
      setLoading(true);
      const userData = localStorage.getItem('user');
      const user = userData ? JSON.parse(userData) : null;
      const roleId = user?.roleId || 1;

      let allTasks: Task[] = [];

      // 如果只显示赛事任务，则只获取赛事任务
      if (onlyEventsTask) {
        const eventsTaskList = await fetchEventsTasks();
        const convertedEventsTasks = eventsTaskList.map(convertEventsTaskToTask);
        allTasks = convertedEventsTasks;
      } else {
        // 并行获取普通任务和赛事任务
        const [taskResponse, eventsTaskList] = await Promise.all([
          taskApi.getTaskList({
            studentId: userId,
            roleId,
            page: 1,
            size: 50
          }),
          fetchEventsTasks()
        ]);

        // 处理普通任务
        if (taskResponse.data.code === 200) {
          const taskList = taskResponse.data.data.list;
          // 检查并更新每个任务的状态
          for (const task of taskList) {
            if (task.assignments && task.assignments.length > 0) {
              const assignment = task.assignments[0];
              const now = new Date();
              const startDate = task.startDate ? new Date(task.startDate) : null;
              const endDate = new Date(task.endDate);
              let newStatus = assignment.taskStatus;

              if (now > endDate && assignment.taskStatus !== TaskStatus.COMPLETED) {
                newStatus = TaskStatus.EXPIRED;
              }
              else if (!startDate || now >= startDate) {
                if (assignment.taskStatus === TaskStatus.NOT_STARTED) {
                  newStatus = TaskStatus.IN_PROGRESS;
                }
              }

              if (newStatus !== assignment.taskStatus) {
                try {
                  await taskApi.updateTaskStatus({
                    assignmentId: assignment.id,
                    taskStatus: newStatus
                  });
                } catch (error) {
                  console.error('更新任务状态失败:', error);
                }
              }
            }
          }

          const updatedResponse = await taskApi.getTaskList({
            studentId: userId,
            roleId,
            page: 1,
            size: 50
          });
          if (updatedResponse.data.code === 200) {
            allTasks = updatedResponse.data.data.list;
          }
        }

        // 处理赛事任务，转换为Task格式并合并
        const convertedEventsTasks = eventsTaskList.map(convertEventsTaskToTask);
        allTasks = [...allTasks, ...convertedEventsTasks];
      }

      setTasks(allTasks);
    } catch (error) {
      console.error('获取任务列表失败:', error);
      notification.error('获取任务列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 根据任务ID打开对应的任务
  const openTaskById = (taskId: number) => {
    const task = tasks.find(t => t.id === taskId);
    if (task) {
      handleTaskClick(task);
    }
  };

  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    fetchTasks,
    openTaskById
  }));

  // 添加自动刷新任务列表的函数
  const handleTaskSubmitted = () => {
    fetchTasks();
  };

  // 处理任务点击
  const handleTaskClick = (task: Task) => {
    if (task.isEventsTask) {
      // 赛事任务：打开专用模态框
      setSelectedEventsTask(task);
      setShowEventsTaskModal(true);
    } else {
      // 普通任务：使用原有的处理方式
      onTaskClick({
        ...task,
        onSubmitted: handleTaskSubmitted
      });
    }
  };

  // 关闭赛事任务模态框
  const handleCloseEventsTaskModal = () => {
    setShowEventsTaskModal(false);
    setSelectedEventsTask(null);
  };

  // 赛事任务提交成功回调
  const handleEventsTaskSuccess = () => {
    fetchTasks(); // 重新加载任务列表
  };

  // 判断任务是否过期
  const isTaskExpired = (endDate: Date) => {
    return new Date() > new Date(endDate);
  };

  // 判断任务是否已开始
  const isTaskStarted = (startDate?: string) => {
    if (!startDate) return true;
    return new Date() > new Date(startDate);
  };

  // 获取任务状态
  const getTaskStatus = (task: Task, assignment?: TaskAssignment) => {
    // 如果是赛事任务，使用赛事任务的状态逻辑
    if (task.isEventsTask && assignment) {
      switch (assignment.taskStatus) {
        case 0: // 待开始
          return { color: 'purple', text: '待开始' };
        case 1: // 进行中
          return { color: 'processing', text: '进行中' };
        case 2: // 已提交
          return { color: 'gold', text: '已提交' };
        case 3: // 已审核
          return { color: 'success', text: '已审核' };
        case 4: // 审核不通过
          return { color: 'error', text: '审核不通过' };
        default:
          return { color: 'purple', text: '未知状态' };
      }
    }

    // 普通任务的状态逻辑
    if (assignment) {
      if (assignment.taskStatus === TaskStatus.COMPLETED) {
        return { color: 'success', text: '已完成' };
      }
      if (assignment.taskStatus === TaskStatus.RE_DO) {
        return { color: 'warning', text: '需要修改' };
      }
      if (assignment.taskScore !== undefined && assignment.taskScore !== null) {
        return { color: 'success', text: '已完成' };
      }
      if (assignment.workId) {
        return { color: 'success', text: '已完成' };
      }
    }

    if (isTaskExpired(task.endDate)) {
      return { color: 'error', text: '已过期' };
    }

    if (isTaskStarted(task.startDate)) {
      return { color: 'processing', text: '进行中' };
    }

    return { color: 'purple', text: '未开始' };
  };

  // 计算剩余时间
  const getRemainingTime = (startDate: string | Date | undefined, endDate: string | Date) => {
    const now = new Date();
    const start = startDate ? new Date(startDate) : now;
    const end = new Date(endDate);

    if (startDate && now < start) {
      const totalDiff = end.getTime() - start.getTime();
      const days = Math.floor(totalDiff / (1000 * 60 * 60 * 24));
      const hours = Math.floor((totalDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      const minutes = Math.floor((totalDiff % (1000 * 60 * 60)) / (1000 * 60));

      let result = '总时长: ';
      if (days > 0) result += `${days}天`;
      if (hours > 0) result += `${hours}小时`;
      if (minutes > 0) result += `${minutes}分钟`;
      return result || '不到1分钟';
    }

    const remainingDiff = end.getTime() - now.getTime();
    if (remainingDiff <= 0) return '已过期';

    const days = Math.floor(remainingDiff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((remainingDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((remainingDiff % (1000 * 60 * 60)) / (1000 * 60));

    let result = `剩余: ${days}天 ${hours}小时 ${minutes}分钟`;
    return result || '不到1分钟';
  };

  // 获取过滤和排序后的任务列表
  const getFilteredAndSortedTasks = (tasks: Task[]) => {
    let filteredTasks = [...tasks];

    switch (activeTaskFilter) {
      case 'completed':
        filteredTasks = tasks.filter(task => {
          const assignment = task.assignments?.[0];
          if (task.isEventsTask) {
            return assignment?.taskStatus === 3; // 赛事任务已审核状态
          }
          return assignment?.taskStatus === TaskStatus.COMPLETED;
        });
        break;
      case 'in_progress':
        filteredTasks = tasks.filter(task => {
          const assignment = task.assignments?.[0];
          if (task.isEventsTask) {
            return assignment?.taskStatus === 1; // 赛事任务进行中状态
          }
          return !isTaskExpired(task.endDate) &&
            isTaskStarted(task.startDate) &&
            assignment?.taskStatus !== TaskStatus.COMPLETED;
        });
        break;
      case 'expired':
        filteredTasks = tasks.filter(task => {
          if (task.isEventsTask) {
            const assignment = task.assignments?.[0];
            return assignment?.taskStatus === 3; // 赛事任务已取消状态
          }
          return isTaskExpired(task.endDate);
        });
        break;
    }

    // 排序逻辑：赛事任务永远置顶
    switch (taskSortBy) {
      case 'deadline':
        filteredTasks.sort((a, b) => {
          // 赛事任务优先级最高
          if (a.isEventsTask && !b.isEventsTask) return -1;
          if (!a.isEventsTask && b.isEventsTask) return 1;

          // 如果都是赛事任务或都是普通任务，按截止时间排序
          const aExpired = isTaskExpired(a.endDate);
          const bExpired = isTaskExpired(b.endDate);
          if (aExpired && !bExpired) return 1;
          if (!aExpired && bExpired) return -1;
          return new Date(a.endDate).getTime() - new Date(b.endDate).getTime();
        });
        break;
      case 'newest':
        filteredTasks.sort((a, b) => {
          // 赛事任务优先级最高
          if (a.isEventsTask && !b.isEventsTask) return -1;
          if (!a.isEventsTask && b.isEventsTask) return 1;

          // 如果都是赛事任务或都是普通任务，按创建时间排序
          const aTime = a.startDate ? new Date(a.startDate).getTime() : 0;
          const bTime = b.startDate ? new Date(b.startDate).getTime() : 0;
          return bTime - aTime;
        });
        break;
      default:
        // 默认排序：赛事任务置顶
        filteredTasks.sort((a, b) => {
          if (a.isEventsTask && !b.isEventsTask) return -1;
          if (!a.isEventsTask && b.isEventsTask) return 1;
          return 0;
        });
        break;
    }

    return filteredTasks;
  };

  useEffect(() => {
    if (userId) {
      fetchTasks();
    }
  }, [userId]);

  return (
    <div className="content-area h-full flex flex-col">
      <div className="bg-gray-50 p-3 rounded-lg shrink-0">
        <div className="flex flex-nowrap items-center gap-2 min-w-0">
          <h2 className="content-area-title text-base font-medium whitespace-nowrap">任务</h2>
          <div className="flex flex-nowrap items-center gap-1.5 min-w-0 ml-auto">
            <Button
              type="text"
              icon={<ReloadOutlined />}
              onClick={fetchTasks}
              className="flex items-center justify-center text-gray-600 hover:text-blue-600 h-7 w-7 min-w-7 p-0"
              title="刷新"
            />
            <Select
              size="small"
              value={activeTaskFilter}
              onChange={setActiveTaskFilter}
              style={{ minWidth: 70 }}
              className="h-7 !min-w-[70px]"
              popupClassName="!min-w-[100px]"
              options={[
                { label: '全部', value: 'all' },
                {
                  label: (
                    <span className="flex items-center gap-1">
                      <span className="w-1.5 h-1.5 rounded-full bg-blue-500"></span>
                      <span className="text-gray-600 text-xs">进行中</span>
                    </span>
                  ),
                  value: 'in_progress'
                },
                {
                  label: (
                    <span className="flex items-center gap-1">
                      <span className="w-1.5 h-1.5 rounded-full bg-green-500"></span>
                      <span className="text-gray-600 text-xs">已完成</span>
                    </span>
                  ),
                  value: 'completed'
                },
                {
                  label: (
                    <span className="flex items-center gap-1">
                      <span className="w-1.5 h-1.5 rounded-full bg-gray-400"></span>
                      <span className="text-gray-600 text-xs">已过期</span>
                    </span>
                  ),
                  value: 'expired'
                }
              ]}
            />
            <Select
              size="small"
              value={taskSortBy}
              onChange={setTaskSortBy}
              style={{ minWidth: 80 }}
              className="h-7 !min-w-[80px]"
              dropdownMatchSelectWidth={false}
              listHeight={300}
              popupMatchSelectWidth={false}
              optionLabelProp="label"
            >
              <Select.Option value="newest" label="最新">
                按最新排序 
              </Select.Option>
              <Select.Option value="deadline" label="截止">
                按截止时间排序 
              </Select.Option>
            </Select>
          </div>
        </div>
      </div>
      <div className="content-area-body flex-1 overflow-y-auto">
        {loading ? (
          <div className="flex justify-center py-8">
            <Spin />
          </div>
        ) : tasks.length > 0 ? (
          <div className="tasks-container">
            {getFilteredAndSortedTasks(tasks).map((task) => {
              const assignment = task.assignments?.[0];
              const isEventsTask = task.isEventsTask;
              return (
                <Card
                  key={`${isEventsTask ? 'events' : 'task'}-${task.id}`}
                  className={`task-card hover:shadow-md transition-all duration-300 cursor-pointer ${
                    isEventsTask
                      ? 'border-2 border-yellow-400 bg-gradient-to-r from-yellow-50 to-amber-50 shadow-lg'
                      : ''
                  }`}
                  onClick={() => handleTaskClick(task)}
                  styles={{ body: { padding: '12px' } }}
                >
                  <div className="flex flex-col space-y-2.5">
                    <div className="flex items-center justify-between gap-2">
                      <div className="flex items-center gap-2">
                        <h4 className="text-[15px] font-medium text-gray-800 truncate">{task.taskName}</h4>
                        {isEventsTask && (
                          <div className="flex items-center gap-1 px-2 py-0.5 bg-gradient-to-r from-yellow-400 to-amber-500 text-white text-xs rounded-full font-medium shadow-sm">
                            <svg className="w-3 h-3" viewBox="0 0 24 24" fill="currentColor">
                              <path d="M12 2L9.19 8.63 2 9.24l5.46 4.73L5.82 21 12 17.27 18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2z" />
                            </svg>
                            赛事任务
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="flex flex-wrap items-center gap-2">
                      <Tag className={`rounded-full border ${getTaskStatus(task, assignment).color === 'success' ? 'bg-green-50 text-green-600 border-green-200' :
                        getTaskStatus(task, assignment).color === 'error' ? 'bg-red-50 text-red-600 border-red-200' :
                          getTaskStatus(task, assignment).color === 'warning' ? 'bg-yellow-50 text-yellow-600 border-yellow-200' :
                            getTaskStatus(task, assignment).color === 'processing' ? 'bg-blue-50 text-blue-600 border-blue-200' :
                              'bg-purple-50 text-purple-600 border-purple-200'
                        }`}>
                        {getTaskStatus(task, assignment).text}
                      </Tag>

                      {assignment?.taskScore !== undefined && assignment?.taskScore !== null && (
                        <Tag className="rounded-full bg-blue-50 text-blue-600 border border-blue-200">
                          <div className="flex items-center gap-1.5">
                            <svg className="w-3 h-3" viewBox="0 0 24 24" fill="currentColor">
                              <path d="M12 2L9.19 8.63 2 9.24l5.46 4.73L5.82 21 12 17.27 18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2z" />
                            </svg>
                            评分：{assignment.taskScore}分
                          </div>
                        </Tag>
                      )}
                      <span className="text-gray-500 text-xs ml-auto">
                        {getRemainingTime(task.startDate, task.endDate)}
                      </span>
                    </div>
                  </div>
                </Card>
              );
            })}
          </div>
        ) : (
          <Empty description="暂无任务" />
        )}
      </div>

      {/* 赛事任务模态框 */}
      <EventsTaskModal
        visible={showEventsTaskModal}
        onClose={handleCloseEventsTaskModal}
        onSuccess={handleEventsTaskSuccess}
        task={selectedEventsTask}
      />
    </div>
  );
});

export default TaskList;