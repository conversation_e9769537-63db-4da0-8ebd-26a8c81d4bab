globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/workbench/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./app/providers.tsx":{"*":{"id":"(ssr)/./app/providers.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/(main)/home/<USER>":{"*":{"id":"(ssr)/./app/(main)/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/(main)/layout.tsx":{"*":{"id":"(ssr)/./app/(main)/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/login/page.tsx":{"*":{"id":"(ssr)/./app/login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/workbench/page.tsx":{"*":{"id":"(ssr)/./app/workbench/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/(main)/course-plaza/page.tsx":{"*":{"id":"(ssr)/./app/(main)/course-plaza/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/my-works/page.tsx":{"*":{"id":"(ssr)/./app/my-works/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/(main)/resources/page.tsx":{"*":{"id":"(ssr)/./app/(main)/resources/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"D:\\logicleap\\logicleapweb\\app\\providers.tsx":{"id":"(app-pages-browser)/./app/providers.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\logicleap\\logicleapweb\\node_modules\\next\\font\\google\\target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\logicleap\\logicleapweb\\app\\globals.css":{"id":"(app-pages-browser)/./app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\logicleap\\logicleapweb\\node_modules\\antd\\dist\\reset.css":{"id":"(app-pages-browser)/./node_modules/antd/dist/reset.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\logicleap\\logicleapweb\\node_modules\\next\\dist\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\logicleap\\logicleapweb\\node_modules\\next\\dist\\esm\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\logicleap\\logicleapweb\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\logicleap\\logicleapweb\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\logicleap\\logicleapweb\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\logicleap\\logicleapweb\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\logicleap\\logicleapweb\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\logicleap\\logicleapweb\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\logicleap\\logicleapweb\\node_modules\\next\\dist\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\logicleap\\logicleapweb\\node_modules\\next\\dist\\esm\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\logicleap\\logicleapweb\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\logicleap\\logicleapweb\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\logicleap\\logicleapweb\\app\\(main)\\home\\page.tsx":{"id":"(app-pages-browser)/./app/(main)/home/<USER>","name":"*","chunks":[],"async":false},"D:\\logicleap\\logicleapweb\\app\\(main)\\layout.tsx":{"id":"(app-pages-browser)/./app/(main)/layout.tsx","name":"*","chunks":["app/(main)/layout","static/chunks/app/(main)/layout.js"],"async":false},"D:\\logicleap\\logicleapweb\\app\\login\\page.tsx":{"id":"(app-pages-browser)/./app/login/page.tsx","name":"*","chunks":[],"async":false},"D:\\logicleap\\logicleapweb\\app\\workbench\\page.tsx":{"id":"(app-pages-browser)/./app/workbench/page.tsx","name":"*","chunks":["app/workbench/page","static/chunks/app/workbench/page.js"],"async":false},"D:\\logicleap\\logicleapweb\\app\\(main)\\course-plaza\\page.tsx":{"id":"(app-pages-browser)/./app/(main)/course-plaza/page.tsx","name":"*","chunks":[],"async":false},"D:\\logicleap\\logicleapweb\\app\\my-works\\page.tsx":{"id":"(app-pages-browser)/./app/my-works/page.tsx","name":"*","chunks":[],"async":false},"D:\\logicleap\\logicleapweb\\app\\(main)\\resources\\page.tsx":{"id":"(app-pages-browser)/./app/(main)/resources/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"D:\\logicleap\\logicleapweb\\":[],"D:\\logicleap\\logicleapweb\\app\\page":[],"D:\\logicleap\\logicleapweb\\app\\layout":["static/css/app/layout.css"],"D:\\logicleap\\logicleapweb\\app\\(main)\\layout":["static/css/app/(main)/layout.css"],"D:\\logicleap\\logicleapweb\\app\\workbench\\page":["static/css/app/workbench/page.css"]}}