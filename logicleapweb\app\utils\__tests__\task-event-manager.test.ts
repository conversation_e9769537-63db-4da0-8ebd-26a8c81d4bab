/**
 * TaskEventManager 测试用例
 */

import taskEventManager, { TASK_EVENTS, TaskPublishedEventData } from '../task-event-manager';

describe('TaskEventManager', () => {
  beforeEach(() => {
    // 每个测试前清理所有监听器
    taskEventManager.removeAllListeners();
  });

  afterEach(() => {
    // 每个测试后清理所有监听器
    taskEventManager.removeAllListeners();
  });

  test('应该能够注册和触发事件', () => {
    const mockListener = jest.fn();
    const testData = { message: 'test' };

    // 注册监听器
    taskEventManager.on('test_event', mockListener);

    // 触发事件
    taskEventManager.emit('test_event', testData);

    // 验证监听器被调用
    expect(mockListener).toHaveBeenCalledWith(testData);
    expect(mockListener).toHaveBeenCalledTimes(1);
  });

  test('应该能够移除事件监听器', () => {
    const mockListener = jest.fn();

    // 注册监听器
    taskEventManager.on('test_event', mockListener);
    
    // 移除监听器
    taskEventManager.off('test_event', mockListener);

    // 触发事件
    taskEventManager.emit('test_event', { message: 'test' });

    // 验证监听器没有被调用
    expect(mockListener).not.toHaveBeenCalled();
  });

  test('应该支持多个监听器', () => {
    const mockListener1 = jest.fn();
    const mockListener2 = jest.fn();
    const testData = { message: 'test' };

    // 注册多个监听器
    taskEventManager.on('test_event', mockListener1);
    taskEventManager.on('test_event', mockListener2);

    // 触发事件
    taskEventManager.emit('test_event', testData);

    // 验证所有监听器都被调用
    expect(mockListener1).toHaveBeenCalledWith(testData);
    expect(mockListener2).toHaveBeenCalledWith(testData);
  });

  test('应该正确处理任务发布事件', () => {
    const mockListener = jest.fn();
    const taskData: TaskPublishedEventData = {
      taskId: 123,
      taskName: '测试任务',
      classId: 456,
      className: '测试班级',
      teacherId: 789
    };

    // 注册任务发布事件监听器
    taskEventManager.on(TASK_EVENTS.TASK_PUBLISHED, mockListener);

    // 触发任务发布事件
    taskEventManager.emit(TASK_EVENTS.TASK_PUBLISHED, taskData);

    // 验证监听器被正确调用
    expect(mockListener).toHaveBeenCalledWith(taskData);
  });

  test('应该正确统计监听器数量', () => {
    const mockListener1 = jest.fn();
    const mockListener2 = jest.fn();

    // 初始状态
    expect(taskEventManager.listenerCount('test_event')).toBe(0);

    // 添加监听器
    taskEventManager.on('test_event', mockListener1);
    expect(taskEventManager.listenerCount('test_event')).toBe(1);

    taskEventManager.on('test_event', mockListener2);
    expect(taskEventManager.listenerCount('test_event')).toBe(2);

    // 移除监听器
    taskEventManager.off('test_event', mockListener1);
    expect(taskEventManager.listenerCount('test_event')).toBe(1);
  });

  test('应该处理监听器执行错误', () => {
    const errorListener = jest.fn(() => {
      throw new Error('测试错误');
    });
    const normalListener = jest.fn();

    // 注册监听器
    taskEventManager.on('test_event', errorListener);
    taskEventManager.on('test_event', normalListener);

    // 模拟console.error
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

    // 触发事件
    taskEventManager.emit('test_event', { message: 'test' });

    // 验证错误被捕获并记录
    expect(consoleSpy).toHaveBeenCalled();
    
    // 验证正常监听器仍然被调用
    expect(normalListener).toHaveBeenCalled();

    // 清理
    consoleSpy.mockRestore();
  });

  test('应该能够清理所有监听器', () => {
    const mockListener1 = jest.fn();
    const mockListener2 = jest.fn();

    // 注册监听器
    taskEventManager.on('event1', mockListener1);
    taskEventManager.on('event2', mockListener2);

    // 验证监听器已注册
    expect(taskEventManager.listenerCount('event1')).toBe(1);
    expect(taskEventManager.listenerCount('event2')).toBe(1);

    // 清理所有监听器
    taskEventManager.removeAllListeners();

    // 验证所有监听器已清理
    expect(taskEventManager.listenerCount('event1')).toBe(0);
    expect(taskEventManager.listenerCount('event2')).toBe(0);

    // 触发事件，验证监听器不会被调用
    taskEventManager.emit('event1', {});
    taskEventManager.emit('event2', {});

    expect(mockListener1).not.toHaveBeenCalled();
    expect(mockListener2).not.toHaveBeenCalled();
  });
});
