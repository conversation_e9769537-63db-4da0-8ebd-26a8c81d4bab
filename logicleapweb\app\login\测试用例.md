## 测试用例

### 登录模块测试用例

#### 一、登录方式测试

| 用例编号       | 用例标题           | 项目/模块 | 优先级 | 前置条件                       | 测试步骤                                                     | 测试数据                                 | 预期结果                                                     |
| -------------- | ------------------ | --------- | ------ | ------------------------------ | ------------------------------------------------------------ | ---------------------------------------- | ------------------------------------------------------------ |
| ljfy_login_001 | 验证码登录成功     | 登录      | P0     | 1. 打开登录页面<br/>2. 手机号已注册 | 1. 选择"验证码登录"<br/>2. 输入注册手机号<br/>3. 点击发送验证码<br/>4. 输入正确验证码<br/>5. 勾选同意协议<br/>6. 点击登录按钮 | 手机号: 13800138000<br/>验证码: 123456   | 1. 登录成功<br/>2. 跳转到首页或指定的redirect页面            |
| ljfy_login_002 | 密码登录成功       | 登录      | P0     | 1. 打开登录页面<br/>2. 手机号已注册且设置了密码 | 1. 选择"密码登录"<br/>2. 输入注册手机号<br/>3. 输入正确密码<br/>4. 勾选同意协议<br/>5. 点击登录按钮 | 手机号: 13800138000<br/>密码: Test@123   | 1. 登录成功<br/>2. 跳转到首页或指定的redirect页面            |
| ljfy_login_003 | 学生登录成功       | 登录      | P0     | 1. 打开登录页面<br/>2. 学生账号已存在 | 1. 选择"学生登录"<br/>2. 选择省市区<br/>3. 选择学校<br/>4. 输入学号和密码<br/>5. 勾选同意协议<br/>6. 点击登录按钮 | 省市区: 北京市-东城区<br/>学校: 测试学校<br/>学号: 2023001<br/>密码: 123456 | 1. 登录成功<br/>2. 跳转到首页或指定的redirect页面            |
| ljfy_login_004 | 微信扫码登录成功   | 登录      | P0     | 1. 打开登录页面<br/>2. 微信账号已绑定 | 1. 使用微信扫描二维码<br/>2. 在微信中确认登录                | 已绑定的微信账号                         | 1. 登录成功<br/>2. 跳转到首页或指定的redirect页面            |

#### 二、验证码登录测试

| 用例编号       | 用例标题               | 项目/模块 | 优先级 | 前置条件         | 测试步骤                                                     | 测试数据                                 | 预期结果                                                     |
| -------------- | ---------------------- | --------- | ------ | ---------------- | ------------------------------------------------------------ | ---------------------------------------- | ------------------------------------------------------------ |
| ljfy_login_005 | 验证码登录-手机号为空  | 登录      | P1     | 打开登录页面     | 1. 选择"验证码登录"<br/>2. 不输入手机号<br/>3. 点击发送验证码 | 手机号: 空                               | 1. 提示"请输入手机号"<br/>2. 无法发送验证码                  |
| ljfy_login_006 | 验证码登录-验证码为空  | 登录      | P1     | 打开登录页面     | 1. 选择"验证码登录"<br/>2. 输入手机号<br/>3. 点击发送验证码<br/>4. 不输入验证码<br/>5. 勾选同意协议<br/>6. 点击登录按钮 | 手机号: 13800138000<br/>验证码: 空       | 1. 提示"请输入验证码"<br/>2. 无法登录                        |
| ljfy_login_007 | 验证码登录-验证码错误  | 登录      | P1     | 打开登录页面     | 1. 选择"验证码登录"<br/>2. 输入手机号<br/>3. 点击发送验证码<br/>4. 输入错误验证码<br/>5. 勾选同意协议<br/>6. 点击登录按钮 | 手机号: 13800138000<br/>验证码: 111111   | 1. 提示"验证码错误"<br/>2. 无法登录                          |
| ljfy_login_008 | 验证码登录-未注册手机号 | 登录      | P1     | 打开登录页面     | 1. 选择"验证码登录"<br/>2. 输入未注册手机号<br/>3. 点击发送验证码<br/>4. 输入收到的验证码<br/>5. 勾选同意协议<br/>6. 点击登录按钮 | 手机号: 19900001111<br/>验证码: 接收到的验证码 | 1. 登录成功<br/>2. 自动注册新账号<br/>3. 可能需要设置密码    |
| ljfy_login_009 | 验证码登录-验证码倒计时 | 登录      | P2     | 打开登录页面     | 1. 选择"验证码登录"<br/>2. 输入手机号<br/>3. 点击发送验证码<br/>4. 立即再次点击发送验证码按钮 | 手机号: 13800138000                      | 1. 第一次点击后按钮显示倒计时<br/>2. 倒计时期间按钮不可点击  |

#### 三、密码登录测试

| 用例编号       | 用例标题               | 项目/模块 | 优先级 | 前置条件         | 测试步骤                                                     | 测试数据                                 | 预期结果                                                     |
| -------------- | ---------------------- | --------- | ------ | ---------------- | ------------------------------------------------------------ | ---------------------------------------- | ------------------------------------------------------------ |
| ljfy_login_010 | 密码登录-手机号为空    | 登录      | P1     | 打开登录页面     | 1. 选择"密码登录"<br/>2. 不输入手机号<br/>3. 输入密码<br/>4. 勾选同意协议<br/>5. 点击登录按钮 | 手机号: 空<br/>密码: Test@123            | 1. 提示"请输入手机号"<br/>2. 无法登录                        |
| ljfy_login_011 | 密码登录-密码为空      | 登录      | P1     | 打开登录页面     | 1. 选择"密码登录"<br/>2. 输入手机号<br/>3. 不输入密码<br/>4. 勾选同意协议<br/>5. 点击登录按钮 | 手机号: 13800138000<br/>密码: 空         | 1. 提示"请输入密码"<br/>2. 无法登录                          |
| ljfy_login_012 | 密码登录-密码错误      | 登录      | P1     | 打开登录页面     | 1. 选择"密码登录"<br/>2. 输入手机号<br/>3. 输入错误密码<br/>4. 勾选同意协议<br/>5. 点击登录按钮 | 手机号: 13800138000<br/>密码: Wrong@123  | 1. 提示"密码错误"<br/>2. 无法登录                            |
| ljfy_login_013 | 密码登录-未注册手机号  | 登录      | P1     | 打开登录页面     | 1. 选择"密码登录"<br/>2. 输入未注册手机号<br/>3. 输入密码<br/>4. 勾选同意协议<br/>5. 点击登录按钮 | 手机号: 19900001111<br/>密码: Test@123   | 1. 提示"账号不存在"<br/>2. 无法登录                          |
| ljfy_login_014 | 密码登录-显示/隐藏密码 | 登录      | P2     | 打开登录页面     | 1. 选择"密码登录"<br/>2. 输入手机号和密码<br/>3. 点击密码框右侧的眼睛图标 | 手机号: 13800138000<br/>密码: Test@123   | 1. 点击眼睛图标后密码显示为明文<br/>2. 再次点击后密码显示为密文 |

#### 四、学生登录测试

| 用例编号       | 用例标题                   | 项目/模块 | 优先级 | 前置条件         | 测试步骤                                                     | 测试数据                                 | 预期结果                                                     |
| -------------- | -------------------------- | --------- | ------ | ---------------- | ------------------------------------------------------------ | ---------------------------------------- | ------------------------------------------------------------ |
| ljfy_login_015 | 学生登录-省市区级联选择    | 登录      | P1     | 打开登录页面     | 1. 选择"学生登录"<br/>2. 选择省份<br/>3. 观察市选择框<br/>4. 选择城市<br/>5. 观察区选择框 | 省: 北京市                               | 1. 选择北京市后，不显示市选择框<br/>2. 直接显示区选择框      |
| ljfy_login_016 | 学生登录-非直辖市省市区选择 | 登录      | P1     | 打开登录页面     | 1. 选择"学生登录"<br/>2. 选择非直辖市省份<br/>3. 观察市选择框<br/>4. 选择城市<br/>5. 观察区选择框 | 省: 广东省<br/>市: 广州市                | 1. 选择广东省后，显示广东省下属城市列表<br/>2. 选择广州市后，显示广州市下属区列表 |
| ljfy_login_017 | 学生登录-学校搜索          | 登录      | P1     | 打开登录页面     | 1. 选择"学生登录"<br/>2. 完成省市区选择<br/>3. 在学校输入框中输入关键词 | 省市区: 已选择<br/>学校关键词: "实验"    | 1. 显示包含"实验"的学校列表<br/>2. 可以从列表中选择学校      |
| ljfy_login_018 | 学生登录-学号或密码为空    | 登录      | P1     | 打开登录页面     | 1. 选择"学生登录"<br/>2. 完成省市区和学校选择<br/>3. 不输入学号或密码<br/>4. 勾选同意协议<br/>5. 点击登录按钮 | 学号: 空<br/>密码: 空                    | 1. 提示"请输入学号"或"请输入密码"<br/>2. 无法登录           |
| ljfy_login_019 | 学生登录-学号或密码错误    | 登录      | P1     | 打开登录页面     | 1. 选择"学生登录"<br/>2. 完成省市区和学校选择<br/>3. 输入错误的学号或密码<br/>4. 勾选同意协议<br/>5. 点击登录按钮 | 学号: 2023999<br/>密码: wrong123         | 1. 提示"学号或密码错误"<br/>2. 无法登录                      |

#### 五、微信扫码登录测试

| 用例编号       | 用例标题                 | 项目/模块 | 优先级 | 前置条件         | 测试步骤                                                     | 测试数据         | 预期结果                                                     |
| -------------- | ------------------------ | --------- | ------ | ---------------- | ------------------------------------------------------------ | ---------------- | ------------------------------------------------------------ |
| ljfy_login_020 | 微信扫码-二维码过期刷新  | 登录      | P1     | 打开登录页面     | 1. 观察左侧微信二维码<br/>2. 等待二维码过期（约2分钟）<br/>3. 点击刷新二维码 | -                | 1. 二维码过期后显示"二维码已失效"<br/>2. 点击刷新后显示新的二维码 |
| ljfy_login_021 | 微信扫码-已绑定手机号    | 登录      | P1     | 打开登录页面     | 1. 使用已绑定手机号的微信扫描二维码<br/>2. 在微信中确认登录  | 已绑定的微信账号 | 1. 登录成功<br/>2. 跳转到首页或指定的redirect页面            |
| ljfy_login_022 | 微信扫码-未绑定手机号    | 登录      | P1     | 打开登录页面     | 1. 使用未绑定手机号的微信扫描二维码<br/>2. 在微信中确认登录  | 未绑定的微信账号 | 1. 显示手机号绑定弹窗<br/>2. 完成手机号绑定后登录成功        |
| ljfy_login_023 | 微信扫码-取消确认        | 登录      | P2     | 打开登录页面     | 1. 使用微信扫描二维码<br/>2. 在微信中取消确认登录            | -                | 1. 二维码状态显示"已取消"<br/>2. 需要重新扫码                |

#### 六、登录后流程测试

| 用例编号       | 用例标题                 | 项目/模块 | 优先级 | 前置条件                           | 测试步骤                                                     | 测试数据               | 预期结果                                                     |
| -------------- | ------------------------ | --------- | ------ | ---------------------------------- | ------------------------------------------------------------ | ---------------------- | ------------------------------------------------------------ |
| ljfy_login_024 | 首次登录-设置密码        | 登录      | P0     | 1. 打开登录页面<br/>2. 首次登录账号 | 1. 使用验证码登录未设置密码的账号<br/>2. 在密码设置弹窗中输入密码和确认密码<br/>3. 点击确认 | 密码: NewPass@123      | 1. 密码设置成功<br/>2. 继续登录流程                          |
| ljfy_login_025 | 首次登录-跳过密码设置    | 登录      | P1     | 1. 打开登录页面<br/>2. 首次登录账号 | 1. 使用验证码登录未设置密码的账号<br/>2. 在密码设置弹窗中点击关闭按钮 | -                      | 1. 系统自动设置默认密码123456<br/>2. 继续登录流程            |
| ljfy_login_026 | 登录-绑定手机号          | 登录      | P0     | 1. 打开登录页面<br/>2. 使用未绑定手机号的账号登录 | 1. 登录未绑定手机号的账号<br/>2. 在手机号绑定弹窗中输入手机号<br/>3. 获取验证码并输入<br/>4. 点击确认 | 手机号: 13900001111    | 1. 手机号绑定成功<br/>2. 继续登录流程                        |
| ljfy_login_027 | 登录-跳过手机号绑定      | 登录      | P1     | 1. 打开登录页面<br/>2. 使用未绑定手机号的账号登录 | 1. 登录未绑定手机号的账号<br/>2. 在手机号绑定弹窗中点击跳过  | -                      | 1. 跳过手机号绑定<br/>2. 继续登录流程                        |
| ljfy_login_028 | 登录-角色选择            | 登录      | P0     | 1. 打开登录页面<br/>2. 使用未设置角色的账号登录 | 1. 登录未设置角色的账号<br/>2. 在角色选择弹窗中选择角色<br/>3. 点击确认 | 角色: 学生             | 1. 角色设置成功<br/>2. 继续登录流程                          |
| ljfy_login_029 | 登录-教师认证            | 登录      | P0     | 1. 打开登录页面<br/>2. 选择教师角色 | 1. 登录并选择教师角色<br/>2. 在教师认证弹窗中填写认证信息<br/>3. 点击确认 | 认证信息: 教师资格证等 | 1. 提交认证信息成功<br/>2. 继续登录流程                      |
| ljfy_login_030 | 登录-多账号选择          | 登录      | P1     | 1. 打开登录页面<br/>2. 使用绑定多个账号的手机号登录 | 1. 使用绑定多个账号的手机号登录<br/>2. 在账号选择弹窗中选择一个账号<br/>3. 点击确认 | -                      | 1. 成功选择账号<br/>2. 继续登录流程                          |

#### 七、协议和安全测试

| 用例编号       | 用例标题                 | 项目/模块 | 优先级 | 前置条件         | 测试步骤                                                     | 测试数据         | 预期结果                                                     |
| -------------- | ------------------------ | --------- | ------ | ---------------- | ------------------------------------------------------------ | ---------------- | ------------------------------------------------------------ |
| ljfy_login_031 | 未勾选协议               | 登录      | P1     | 打开登录页面     | 1. 选择任意登录方式<br/>2. 填写正确登录信息<br/>3. 不勾选同意协议<br/>4. 点击登录按钮 | -                | 1. 登录按钮不可点击或点击无反应<br/>2. 无法登录              |
| ljfy_login_032 | 查看服务协议             | 登录      | P2     | 打开登录页面     | 1. 点击"平台服务协议"链接                                    | -                | 1. 弹出协议内容弹窗<br/>2. 可以查看完整协议内容              |
| ljfy_login_033 | 查看隐私政策             | 登录      | P2     | 打开登录页面     | 1. 点击"个人信息保护政策"链接                                | -                | 1. 弹出隐私政策弹窗<br/>2. 可以查看完整隐私政策内容          |
| ljfy_login_034 | 密码安全-SQL注入         | 登录      | P0     | 打开登录页面     | 1. 选择"密码登录"<br/>2. 输入手机号<br/>3. 输入含SQL注入的密码<br/>4. 勾选同意协议<br/>5. 点击登录按钮 | 密码: ' OR 1=1-- | 1. 系统拒绝登录<br/>2. 不存在SQL注入漏洞                     |
| ljfy_login_035 | 密码安全-暴力破解防护    | 登录      | P0     | 打开登录页面     | 1. 选择"密码登录"<br/>2. 输入手机号<br/>3. 连续多次输入错误密码<br/>4. 勾选同意协议<br/>5. 点击登录按钮 | -                | 1. 多次错误后账号被锁定或要求验证码<br/>2. 防止暴力破解      |

#### 八、UI和兼容性测试

| 用例编号       | 用例标题                 | 项目/模块 | 优先级 | 前置条件         | 测试步骤                                                     | 测试数据         | 预期结果                                                     |
| -------------- | ------------------------ | --------- | ------ | ---------------- | ------------------------------------------------------------ | ---------------- | ------------------------------------------------------------ |
| ljfy_login_036 | 响应式布局-手机端        | 登录      | P1     | 使用手机浏览器打开登录页面 | 1. 在手机浏览器中打开登录页面<br/>2. 观察页面布局            | -                | 1. 页面自适应手机屏幕<br/>2. 微信二维码区域隐藏<br/>3. 表单元素正常显示 |
| ljfy_login_037 | 响应式布局-平板端        | 登录      | P2     | 使用平板浏览器打开登录页面 | 1. 在平板浏览器中打开登录页面<br/>2. 观察页面布局            | -                | 1. 页面自适应平板屏幕<br/>2. 所有元素正常显示                |
| ljfy_login_038 | 浏览器兼容性-Chrome      | 登录      | P0     | 使用Chrome浏览器打开登录页面 | 1. 在Chrome浏览器中打开登录页面<br/>2. 完成登录流程          | -                | 1. 页面正常显示<br/>2. 功能正常使用                          |
| ljfy_login_039 | 浏览器兼容性-Safari      | 登录      | P1     | 使用Safari浏览器打开登录页面 | 1. 在Safari浏览器中打开登录页面<br/>2. 完成登录流程          | -                | 1. 页面正常显示<br/>2. 功能正常使用                          |
| ljfy_login_040 | 浏览器兼容性-Firefox     | 登录      | P1     | 使用Firefox浏览器打开登录页面 | 1. 在Firefox浏览器中打开登录页面<br/>2. 完成登录流程         | -                | 1. 页面正常显示<br/>2. 功能正常使用                          |

### 测试覆盖分析

1. **功能覆盖**：测试用例覆盖了所有登录方式（验证码、密码、学生、微信扫码）
2. **流程覆盖**：包含了正常流程和异常流程
3. **角色覆盖**：覆盖了不同角色（学生、教师、普通用户）的登录场景
4. **UI/UX覆盖**：包含了响应式布局和浏览器兼容性测试
5. **安全覆盖**：包含了密码安全、防注入、防暴力破解等安全测试

### 测试执行建议

1. 优先执行P0级别用例，确保核心功能正常
2. 回归测试时重点关注登录流程中的状态转换
3. 自动化测试建议覆盖基本登录流程和表单验证
4. 安全测试建议由专业安全测试人员执行