import React from 'react';
import { StudentDetailPanelProps } from '../types';

const StudentDetailPanel: React.FC<StudentDetailPanelProps> = ({
  selectedStudent,
  currentTemplate,
  onAssignBlocks,
  onAssignPoints,
  onUseKeyPackage,
  onRefreshTemplate
}) => {
  if (!selectedStudent) {
    return (
      <div className="student-detail-placeholder">
        <div className="placeholder-content">
          <div className="placeholder-icon">👥</div>
          <h3>请选择学生</h3>
          <p>点击左侧学生列表中的学生查看详细信息</p>
        </div>
      </div>
    );
  }

  return (
    <div className="student-detail-panel">
      <div className="student-detail-header">
        <div className="student-detail-avatar">
          <div className="avatar-circle">
            {selectedStudent.nickName ? selectedStudent.nickName.charAt(0) : 'S'}
          </div>
        </div>
        <div className="student-detail-info">
          <h3 className="student-detail-name">{selectedStudent.nickName || `学生${selectedStudent.studentNumber || selectedStudent.userId}`}</h3>
          <p className="student-detail-id">学号: {selectedStudent.studentNumber || '无学号'}</p>
          <div className="student-detail-stats">
            <div className="stat-item">
              <span className="stat-label">总积分</span>
              <span className="stat-value">{selectedStudent.totalPoints || 0}</span>
            </div>
            <div className="stat-item">
              <span className="stat-label">可用积分</span>
              <span className="stat-value">{selectedStudent.availablePoints || 0}</span>
            </div>
          </div>
        </div>
      </div>

      <div className="student-detail-content">
        <div className="detail-section">
          <div className="section-header">
            <h4>基本信息</h4>
          </div>
          <div className="section-content">
            <div className="info-row">
              <span className="info-label">姓名</span>
              <span className="info-value">{selectedStudent.nickName || `学生${selectedStudent.studentNumber || selectedStudent.userId}`}</span>
            </div>
            <div className="info-row">
              <span className="info-label">学号</span>
              <span className="info-value">{selectedStudent.studentNumber || '未设置'}</span>
            </div>
            <div className="info-row">
              <span className="info-label">手机号</span>
              <span className="info-value">{selectedStudent.phone || '未设置'}</span>
            </div>
          </div>
        </div>

        <div className="detail-section">
          <div className="section-header">
            <h4>当前模板</h4>
            <div className="template-header">
              <span>当前模板</span>
              {(() => {
                const displayTemplate = selectedStudent?.currentTemplate || currentTemplate;
                return displayTemplate && (
                  <span className={`template-badge ${displayTemplate.isOfficial ? '' : 'custom'}`}>
                    {displayTemplate.isOfficial ? '官方' : '自定义'}
                  </span>
                );
              })()}
            </div>
          </div>
          <div className="template-info">
            <div className="template-icon">🧩</div>
            <span className={!selectedStudent?.currentTemplate && !currentTemplate ? 'template-loading' : ''}>
              {/* 优先显示学生个人模板，如果没有则显示全局模板 */}
              {(() => {
                console.log('当前模板显示逻辑:', {
                  selectedStudentId: selectedStudent?.userId,
                  selectedStudentTemplate: selectedStudent?.currentTemplate,
                  currentTemplate: currentTemplate
                });

                if (selectedStudent?.currentTemplate) {
                  return selectedStudent.currentTemplate.templateName;
                } else if (currentTemplate) {
                  return currentTemplate.templateName;
                } else if (selectedStudent) {
                  return '未分配模板';
                } else {
                  return '请选择学生';
                }
              })()}
            </span>
            {!selectedStudent?.currentTemplate && !currentTemplate && (
              <button
                onClick={async () => {
                  console.log('=== 开始刷新模板状态 ===');
                  onRefreshTemplate();
                }}
                className="refresh-template-btn"
              >
                刷新模板
              </button>
            )}
          </div>
        </div>

        <div className="detail-section">
          <div className="section-header">
            <h4>快速操作</h4>
          </div>
          <div className="quick-actions">
            <button
              className="function-btn publish-task"
              onClick={() => {
                // TODO: 实现发布任务功能
                console.log('发布任务');
              }}
            >
              <div className="function-icon">📝</div>
              <span>发布任务</span>
            </button>
            <button
              className="function-btn distribute-blocks"
              onClick={onAssignBlocks}
            >
              <div className="function-icon">🧩</div>
              <span>分配积木</span>
            </button>
            <button
              className="function-btn distribute-energy"
              onClick={onAssignPoints}
            >
              <div className="function-icon">⚡</div>
              <span>分配能量</span>
            </button>
            <button
              className="function-btn exchange-tokens"
              onClick={onUseKeyPackage}
            >
              <div className="function-icon">🎁</div>
              <span>兑换密钥</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StudentDetailPanel;
