'use client';

import React, { useState } from 'react';
import { X, Upload } from 'lucide-react';
import { courseManagementApi } from '@/lib/api/course-management';
import { ToastProvider } from './ToastManager';
import './CreateSeriesCourseModal.css';
import CourseListEditModal from './CourseListEditModal';

interface CreateSeriesCourseModalProps {
  isVisible: boolean;
  onClose: () => void;
  onSubmit: (data: any) => void;
}

interface FormData {
  title: string;
  description: string;
  coverImage?: File; // 在UI层面是可选的，但提交时必须有
}

// 图片压缩函数
const compressImage = (file: File, quality: number = 0.7, maxWidth: number = 800, maxHeight: number = 600): Promise<File> => {
  return new Promise((resolve) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d')!;
    const img = new Image();

    img.onload = () => {
      // 计算压缩后的尺寸
      let { width, height } = img;

      if (width > maxWidth || height > maxHeight) {
        const ratio = Math.min(maxWidth / width, maxHeight / height);
        width *= ratio;
        height *= ratio;
      }

      canvas.width = width;
      canvas.height = height;

      // 绘制压缩后的图片
      ctx.drawImage(img, 0, 0, width, height);

      // 转换为Blob
      canvas.toBlob((blob) => {
        if (blob) {
          const compressedFile = new File([blob], file.name, {
            type: file.type,
            lastModified: Date.now(),
          });
          resolve(compressedFile);
        } else {
          resolve(file); // 如果压缩失败，返回原文件
        }
      }, file.type, quality);
    };

    img.src = URL.createObjectURL(file);
  });
};

// 上传图片到服务器获取真实URL
const uploadImageToServer = async (file: File): Promise<string> => {
  console.log('开始上传图片到服务器:', file.name);

  // 先压缩图片
  const compressedFile = await compressImage(file, 0.8, 1200, 800);
  console.log('图片压缩完成，原大小:', file.size, '压缩后:', compressedFile.size);

  // 创建FormData
  const formData = new FormData();
  formData.append('file', compressedFile);
  formData.append('type', 'course-cover'); // 标识这是课程封面图片

  try {
    // 发送到图片上传API
    const response = await fetch('/api/v1/upload/image', {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      throw new Error(`上传失败: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();
    console.log('图片上传响应:', result);

    if (result.code === 200 && result.data?.url) {
      console.log('图片上传成功，URL:', result.data.url);
      return result.data.url; // 返回图片URL
    } else {
      throw new Error(result.message || '上传失败');
    }
  } catch (error) {
    console.error('图片上传错误:', error);
    throw error;
  }
};

const CreateSeriesCourseModal: React.FC<CreateSeriesCourseModalProps> = ({
  isVisible,
  onClose,
  onSubmit
}) => {
  const [formData, setFormData] = useState<FormData>({
    title: '',
    description: ''
  });
  const [coverImagePreview, setCoverImagePreview] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [isCourseListModalVisible, setIsCourseListModalVisible] = useState(false);
  const [createdSeriesData, setCreatedSeriesData] = useState<any>(null);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // 处理文件上传
  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // 检查文件类型
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
      if (!allowedTypes.includes(file.type)) {
        alert('请选择 JPG、PNG 或 GIF 格式的图片');
        return;
      }

      // 检查文件大小 (5MB，更严格的限制)
      const maxSize = 5 * 1024 * 1024;
      if (file.size > maxSize) {
        alert('图片大小不能超过 5MB，请选择更小的图片或使用图片压缩工具');
        return;
      }

      // 更新表单数据
      setFormData(prev => ({ ...prev, coverImage: file }));

      // 创建预览URL
      const reader = new FileReader();
      reader.onload = (e) => {
        setCoverImagePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  // 移除封面图片
  const removeCoverImage = () => {
    setFormData(prev => ({ ...prev, coverImage: undefined }));
    setCoverImagePreview(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.title.trim()) {
      alert('请输入系列课程标题');
      return;
    }

    // 检查是否上传了封面图片
    if (!formData.coverImage) {
      alert('请上传系列课程封面图片');
      return;
    }

    setIsSubmitting(true);

    try {
      console.log('开始构建请求数据...');

      // 根据后端错误信息，后端期望纯JSON格式
      // coverImage必须是字符串，不能是文件对象
      let coverImageUrl = '';

      // 上传图片到服务器获取真实URL（必需）
      console.log('开始上传图片文件:', formData.coverImage.name, formData.coverImage.size);

      try {
        setIsUploading(true);
        // 上传图片到服务器
        coverImageUrl = await uploadImageToServer(formData.coverImage);
        console.log('图片上传成功，获得URL:', coverImageUrl);

        // 验证返回的URL是否有效
        if (!coverImageUrl || !coverImageUrl.startsWith('http')) {
          throw new Error('图片上传返回的URL无效');
        }
      } catch (error) {
        console.error('图片上传失败:', error);
        alert('图片上传失败，请重试: ' + (error instanceof Error ? error.message : '未知错误'));
        return; // 上传失败时直接返回，不创建课程
      } finally {
        setIsUploading(false);
      }

      // 构建JSON数据，包含必需字段
      const requestData: any = {
        title: formData.title,
        category: 0,
        coverImage: coverImageUrl // 必需字段：真实图片URL
      };

      // 可选字段：只有用户输入了description才添加
      if (formData.description && formData.description.trim()) {
        requestData.description = formData.description.trim();
      }

      console.log('添加真实图片URL到请求:', coverImageUrl);

      // 注意：不发送 projectMembers 和 tagIds 字段
      // 这些字段可以在后续的编辑功能中添加

      console.log('发送的数据:', requestData);

      console.log('构建的JSON数据:', requestData);

      // 发送JSON请求
      const response = await courseManagementApi.createSeriesCourse(requestData);

      if (response.code === 200) {
        console.log('系列课程创建成功:', response.data);

        // 保存创建的系列课程数据
        setCreatedSeriesData({
          ...response.data,
          coverImage: coverImageUrl,
          title: formData.title
        });

        // 显示课程列表编辑弹窗
        setIsCourseListModalVisible(true);

        // 通知父组件
        onSubmit(response.data);
      } else {
        throw new Error(response.message || '创建失败');
      }
    } catch (error: any) {
      console.error('创建系列课程失败:', error);

      // 显示更详细的错误信息
      let errorMessage = '创建系列课程失败，请重试';
      if (error.response) {
        console.error('错误响应:', error.response.data);
        console.error('错误状态:', error.response.status);
        errorMessage = `请求失败 (${error.response.status}): ${error.response.data?.message || error.response.statusText}`;
      } else if (error.request) {
        console.error('网络错误:', error.request);
        errorMessage = '网络连接失败，请检查网络连接';
      } else {
        console.error('请求配置错误:', error.message);
        errorMessage = error.message;
      }

      alert(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    setFormData({ title: '', description: '' });
    setCoverImagePreview(null);
    setIsSubmitting(false);
    setIsUploading(false);
    setIsCourseListModalVisible(false);
    setCreatedSeriesData(null);
    onClose();
  };

  // 处理课程列表编辑弹窗关闭
  const handleCourseListModalClose = () => {
    setIsCourseListModalVisible(false);
    // 关闭课程列表编辑弹窗时，也关闭创建系列课程弹窗
    handleClose();
  };

  // 处理课程列表保存
  const handleCourseListSave = (data: any) => {
    console.log('保存课程列表数据:', data);
    // TODO: 调用API保存课程列表数据
    alert('课程列表保存成功！');
  };

  if (!isVisible) return null;

  return (
    <ToastProvider>
      <div className="series-modal-overlay">
      <div className="series-modal">
        {/* 模态框头部 */}
        <div className="series-modal-header">
          <h2 className="series-modal-title">创建系列课程</h2>
          <button
            onClick={handleClose}
            className="series-modal-close"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* 模态框内容 */}
        <form onSubmit={handleSubmit} className="series-modal-content">
          {/* 系列课程封面 */}
          <div className="series-cover-section">
            <div className="series-cover-container">
              {coverImagePreview ? (
                <div className="series-cover-preview">
                  <img
                    src={coverImagePreview}
                    alt="课程封面预览"
                    className="series-cover-image"
                  />
                  <button
                    type="button"
                    onClick={removeCoverImage}
                    className="series-cover-remove"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>
              ) : (
                <label className="series-cover-placeholder">
                  <input
                    type="file"
                    accept="image/jpeg,image/jpg,image/png,image/gif"
                    onChange={handleFileUpload}
                    className="series-cover-input"
                  />
                  <Upload className="w-6 h-6 text-gray-400 mb-2" />
                  <div className="series-cover-text">系列课程封面 *</div>
                  <div className="series-cover-hint">点击上传图片（必需）</div>
                </label>
              )}
            </div>
          </div>

          {/* 系列课程标题 */}
          <div className="series-form-group">
            <input
              type="text"
              name="title"
              value={formData.title}
              onChange={handleInputChange}
              placeholder="系列课程标题"
              className="series-title-input"
              required
            />
          </div>

          {/* 创建按钮 */}
          <div className="series-form-actions">
            <button
              type="submit"
              disabled={isSubmitting}
              className={`series-create-btn ${isSubmitting ? 'series-create-btn-loading' : ''}`}
            >
              {isSubmitting ? '创建中...' : '创建系列课程'}
            </button>
          </div>
        </form>
      </div>

      {/* 课程列表编辑弹窗 */}
      {createdSeriesData && (
        <CourseListEditModal
          isVisible={isCourseListModalVisible}
          onClose={handleCourseListModalClose}
          onSave={handleCourseListSave}
          seriesTitle={createdSeriesData.title}
          seriesCoverImage={createdSeriesData.coverImage}
          seriesId={createdSeriesData.id}
        />
      )}
      </div>
    </ToastProvider>
  );
};

export default CreateSeriesCourseModal;
