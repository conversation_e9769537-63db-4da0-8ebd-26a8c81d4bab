/* 自定义提示框样式 */
.custom-alert-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  backdrop-filter: blur(4px);
}

.custom-alert-modal {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  max-width: 400px;
  width: 90%;
  max-height: 90vh;
  overflow: hidden;
  animation: customAlertSlideIn 0.3s ease-out;
}

@keyframes customAlertSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.custom-alert-content {
  padding: 24px;
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.custom-alert-icon {
  flex-shrink: 0;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 4px;
}

.custom-alert-icon-info {
  background-color: #e1f5fe;
  color: #0288d1;
}

.custom-alert-icon-success {
  background-color: #e8f5e8;
  color: #2e7d32;
}

.custom-alert-icon-warning {
  background-color: #fff3e0;
  color: #f57c00;
}

.custom-alert-icon-error {
  background-color: #ffebee;
  color: #d32f2f;
}

.custom-alert-text {
  flex: 1;
  min-width: 0;
}

.custom-alert-title {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
  line-height: 1.4;
}

.custom-alert-message {
  margin: 0;
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  word-wrap: break-word;
}

.custom-alert-actions {
  padding: 16px 24px 24px;
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  border-top: 1px solid #f0f0f0;
}

.custom-alert-btn {
  padding: 8px 20px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  min-width: 80px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.custom-alert-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.custom-alert-btn:active {
  transform: translateY(0);
}

.custom-alert-btn-cancel {
  background-color: #f5f5f5;
  color: #666;
  border-color: #d9d9d9;
}

.custom-alert-btn-cancel:hover {
  background-color: #e8e8e8;
  border-color: #bfbfbf;
}

.custom-alert-btn-confirm {
  background-color: #1890ff;
  color: white;
  border-color: #1890ff;
}

.custom-alert-btn-confirm:hover {
  background-color: #40a9ff;
  border-color: #40a9ff;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .custom-alert-modal {
    margin: 16px;
    width: calc(100% - 32px);
  }
  
  .custom-alert-content {
    padding: 20px;
    gap: 12px;
  }
  
  .custom-alert-icon {
    width: 40px;
    height: 40px;
  }
  
  .custom-alert-title {
    font-size: 16px;
  }
  
  .custom-alert-message {
    font-size: 13px;
  }
  
  .custom-alert-actions {
    padding: 12px 20px 20px;
    flex-direction: column-reverse;
  }
  
  .custom-alert-btn {
    width: 100%;
    min-width: auto;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .custom-alert-modal {
    background: #2a2a2a;
    color: #e0e0e0;
  }
  
  .custom-alert-title {
    color: #ffffff;
  }
  
  .custom-alert-message {
    color: #b0b0b0;
  }
  
  .custom-alert-actions {
    border-top-color: #404040;
  }
  
  .custom-alert-btn-cancel {
    background-color: #404040;
    color: #e0e0e0;
    border-color: #606060;
  }
  
  .custom-alert-btn-cancel:hover {
    background-color: #505050;
    border-color: #707070;
  }
}
