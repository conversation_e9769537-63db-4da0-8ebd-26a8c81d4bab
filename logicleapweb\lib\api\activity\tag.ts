import request from '../../request'

// 活动标签参数类型
export interface ActivityTagParams {
  activityId: number;
  tagIds: number[];
}

// 活动标签API
export const activityTagApi = {
  baseUrl: '/api/v1/activity_tag',
  
  // 添加活动标签关联
  add: (params: ActivityTagParams) => {
    return request.post(`${activityTagApi.baseUrl}/add-tags`, params);
  },

  // 更新活动标签关联
  update: (activityId: number, tagIds: number[]) => {
    return request.put(`${activityTagApi.baseUrl}/edit-tags/${activityId}`, { tagIds });
  },

  // 删除活动标签关联
  delete: (activityId: number) => {
    return request.delete(`${activityTagApi.baseUrl}/remove-tags/${activityId}`);
  },

  // 获取活动的标签ID列表
  getActivityTags: (activityId: number) => {
    return request.get(`${activityTagApi.baseUrl}/get-activity/${activityId}/tags`);
  },

  // 获取标签关联的活动ID列表
  getTagActivities: (tagId: number) => {
    return request.get(`${activityTagApi.baseUrl}/get-tag/${tagId}/activities`);
  },
};

// 标签API
export const tagApi = {
  baseUrl: '/api/v1/tag',
  
  // 获取标签列表
  getList: (params?: {
    page?: number;
    size?: number;
    keyword?: string;
  }) => {
    return request.get(`${tagApi.baseUrl}/listTag`, { params });
  },

  // 创建标签
  create: (params: {
    name: string;
    description?: string;
    color?: string;
  }) => {
    return request.post(`${tagApi.baseUrl}/createTag`, params);
  },

  // 更新标签
  update: (id: number, params: {
    name?: string;
    description?: string;
    color?: string;
  }) => {
    return request.put(`${tagApi.baseUrl}/updateTag/${id}`, params);
  },

  // 删除标签
  delete: (id: number) => {
    return request.delete(`${tagApi.baseUrl}/deleteTag/${id}`);
  },

  // 获取标签详情
  getInfo: (id: number) => {
    return request.get(`${tagApi.baseUrl}/infoTag/${id}`);
  }
};
