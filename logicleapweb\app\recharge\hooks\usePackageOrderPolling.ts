'use client'

import { useState, useEffect, useCallback, useRef } from 'react';
import { getOrderStatus } from '../../../lib/api/package-purchase';
import { message } from 'antd';

export interface PackageOrderPollingOptions {
  orderNo: string;
  onSuccess?: (orderInfo: any) => void;
  onFailed?: (error: string) => void;
  onTimeout?: () => void;
  maxAttempts?: number;
  intervalMs?: number;
  timeoutMs?: number;
}

export interface UsePackageOrderPollingReturn {
  isPolling: boolean;
  attempts: number;
  timeRemaining: number;
  startPolling: (options: PackageOrderPollingOptions) => void;
  stopPolling: () => void;
  status: 'idle' | 'polling' | 'success' | 'failed' | 'timeout';
}

export const usePackageOrderPolling = (): UsePackageOrderPollingReturn => {
  const [isPolling, setIsPolling] = useState(false);
  const [attempts, setAttempts] = useState(0);
  const [timeRemaining, setTimeRemaining] = useState(0);
  const [status, setStatus] = useState<'idle' | 'polling' | 'success' | 'failed' | 'timeout'>('idle');
  
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const countdownRef = useRef<NodeJS.Timeout | null>(null);
  const optionsRef = useRef<PackageOrderPollingOptions | null>(null);

  const clearAllTimers = useCallback(() => {
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current);
      pollingIntervalRef.current = null;
    }
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
    if (countdownRef.current) {
      clearInterval(countdownRef.current);
      countdownRef.current = null;
    }
  }, []);

  const stopPolling = useCallback(() => {
    clearAllTimers();
    setIsPolling(false);
    setAttempts(0);
    setTimeRemaining(0);
    setStatus('idle');
    optionsRef.current = null;
  }, [clearAllTimers]);

  const checkOrderStatus = useCallback(async (orderNo: string) => {
    try {
      console.log(`正在查询套餐订单状态，订单号: ${orderNo}`);
      const result = await getOrderStatus(orderNo);
      console.log('套餐订单状态查询结果:', result);

      if (result) {
        const orderStatus = result.status || 'pending';
        console.log(`套餐订单状态: ${orderStatus}`);

        switch (orderStatus) {
          case 'paid':
            console.log('套餐订单支付成功，停止轮询');
            setStatus('success');
            // 先保存回调函数，再停止轮询
            const successCallback = optionsRef.current?.onSuccess;
            stopPolling();
            if (successCallback) {
              console.log('调用成功回调');
              successCallback(result);
            }
            message.success('支付成功！');
            return true;

          case 'cancelled':
            console.log('套餐订单已取消，停止轮询');
            setStatus('failed');
            // 先保存回调函数，再停止轮询
            const failedCallback = optionsRef.current?.onFailed;
            stopPolling();
            if (failedCallback) {
              failedCallback('订单已取消');
            }
            message.error('订单已取消');
            return true;

          case 'pending':
          default:
            console.log('套餐订单状态为pending，继续轮询');
            // 继续轮询
            return false;
        }
      } else {
        console.warn('查询套餐订单状态失败: 无返回数据');
        return false;
      }
    } catch (error) {
      console.error('查询套餐订单状态异常:', error);
      return false;
    }
  }, [stopPolling]);

  const startPolling = useCallback((options: PackageOrderPollingOptions) => {
    // 停止之前的轮询
    stopPolling();
    
    const {
      orderNo,
      maxAttempts = 180, // 默认最多尝试180次（15分钟，每5秒一次）
      intervalMs = 5000,  // 默认5秒间隔
      timeoutMs = 900000, // 默认15分钟超时
    } = options;
    
    optionsRef.current = options;
    setIsPolling(true);
    setAttempts(0);
    setTimeRemaining(Math.floor(timeoutMs / 1000));
    setStatus('polling');
    
    console.log(`开始轮询套餐订单状态，订单号: ${orderNo}`);
    
    // 立即检查一次
    checkOrderStatus(orderNo);
    
    // 设置轮询定时器
    pollingIntervalRef.current = setInterval(async () => {
      setAttempts(prev => {
        const newAttempts = prev + 1;
        
        // 检查是否超过最大尝试次数
        if (newAttempts >= maxAttempts) {
          setStatus('timeout');
          stopPolling();
          if (options.onTimeout) {
            options.onTimeout();
          }
          message.warning('支付超时，请检查支付状态或重新发起支付');
          return newAttempts;
        }
        
        // 检查订单状态
        checkOrderStatus(orderNo);
        
        return newAttempts;
      });
    }, intervalMs);
    
    // 设置总超时定时器
    timeoutRef.current = setTimeout(() => {
      setStatus('timeout');
      stopPolling();
      if (options.onTimeout) {
        options.onTimeout();
      }
      message.warning('支付超时，请检查支付状态或重新发起支付');
    }, timeoutMs);
    
    // 设置倒计时定时器
    countdownRef.current = setInterval(() => {
      setTimeRemaining(prev => {
        const newTime = prev - 1;
        if (newTime <= 0) {
          return 0;
        }
        return newTime;
      });
    }, 1000);
    
  }, [stopPolling, checkOrderStatus]);

  // 组件卸载时清理定时器
  useEffect(() => {
    return () => {
      clearAllTimers();
    };
  }, [clearAllTimers]);

  return {
    isPolling,
    attempts,
    timeRemaining,
    startPolling,
    stopPolling,
    status,
  };
};
