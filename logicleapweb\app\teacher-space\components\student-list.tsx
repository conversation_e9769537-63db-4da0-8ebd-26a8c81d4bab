'use client'

import { useState, useEffect } from 'react';
import { Avatar, Button, Dropdown, Empty, Progress, Tag, Spin } from 'antd';
import {
  UserOutlined,
  BlockOutlined,
  ThunderboltOutlined,
  FileTextOutlined,
  KeyOutlined,
  DeleteOutlined,
  EllipsisOutlined,
  CheckOutlined,
  AppstoreOutlined,
  GiftOutlined,
  LoadingOutlined
} from '@ant-design/icons';
import ossImageProcessor from '@/lib/utils/OssImageProcessor';

interface Student {
  id: number;
  userId: number;
  studentNumber: string;
  nickName: string;
  avatarUrl: string;
  roleId?: number;
  totalPoints?: number;
  availablePoints: number;
  currentTemplate?: {
    templateId: number;
    templateName: string;
    isOfficial: boolean;
  };
}

interface StudentListProps {
  students: Student[];
  loadingStudents: boolean;
  isMultiSelect: boolean;
  selectedStudents: number[];
  onStudentSelect: (studentId: number) => void;
  onMultiSelect: (student: Student) => void;
  onMouseDown: (student: Student, index: number, e: React.MouseEvent) => void;
  onMouseEnter: (student: Student, index: number) => void;
  onAssignBlocks: (studentId: number) => Promise<void>;
  onAssignPoints: (student: Student) => void;
  onPublishTask: (studentId: number) => void;
  onResetPassword: (studentId: number, password?: string) => void;
  onRemoveStudent: (studentId: number) => void;
  onUseKeyPackage?: (studentId: number) => void;
  onRefreshList?: () => void;
}

export const StudentList: React.FC<StudentListProps> = ({
  students,
  loadingStudents,
  isMultiSelect,
  selectedStudents,
  onStudentSelect,
  onMultiSelect,
  onMouseDown,
  onMouseEnter,
  onAssignBlocks,
  onAssignPoints,
  onPublishTask,
  onResetPassword,
  onRemoveStudent,
  onUseKeyPackage,
  onRefreshList
}) => {
  // 获取头像缩略图的函数
  const getAvatarThumbnail = (url: string) => {
    if (!url) return url;

    try {
      // 使用OSS图片处理器获取头像缩略图
      return ossImageProcessor.getAvatarThumbnail(url, 50, 50);
    } catch (error) {
      console.error('获取头像缩略图失败:', error);
      return url;
    }
  };

  if (loadingStudents) {
    return (
      <div className="flex flex-col items-center justify-center h-[calc(100vh-320px)]">
        <Spin size="large" />
        <div className="mt-4 text-gray-500">正在加载学生列表...</div>
      </div>
    );
  }

  if (students.length === 0) {
    return <Empty description="请选择班级查看学生" />;
  }

  return (
    <div className="border-b border-gray-100 mb-16">
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 px-2">
        {students.map((student, index) => (
          <div
            key={student.id}
            className={`
              relative p-5 rounded-2xl cursor-pointer
              transition-all duration-300 ease-in-out
              transform group
              bg-gradient-to-br from-white to-gray-50/80
              border border-gray-100
              backdrop-blur-sm
              ${isMultiSelect
                ? selectedStudents.includes(student.userId)
                  ? 'ring-2 ring-blue-500 shadow-lg shadow-blue-100/50 from-blue-50/90 to-white'
                  : 'hover:border-blue-200 hover:shadow-md hover:from-blue-50/50 hover:to-white'
                : 'hover:border-gray-200 hover:shadow-lg hover:-translate-y-1 hover:from-blue-50/30 hover:to-white'
              }
            `}
            style={{
              height: '100%',
              minHeight: '180px',
              margin: '0',
              willChange: 'transform, box-shadow'
            }}
            onClick={() => {
              if (!isMultiSelect) {
                onStudentSelect(student.userId);
              } else {
                onMultiSelect(student);
              }
            }}
            onMouseDown={(e) => onMouseDown(student, index, e)}
            onMouseEnter={() => onMouseEnter(student, index)}
          >
            {/* 选中状态指示器 */}
            {isMultiSelect && (
              <div className={`
                absolute -top-2 -right-2 w-6 h-6 
                rounded-full flex items-center justify-center
                transition-all duration-200
                ${selectedStudents.includes(student.userId)
                  ? 'opacity-100 scale-100 bg-gradient-to-br from-blue-500 to-blue-600 text-white shadow-lg shadow-blue-200/50'
                  : 'opacity-0 scale-75 bg-gray-100'
                }
                group-hover:opacity-100 group-hover:scale-100
              `}>
                <CheckOutlined className="text-xs" />
              </div>
            )}

            {/* 学生信息卡片内容 */}
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center gap-3">
                <Avatar
                  size={48}
                  src={student.avatarUrl ? getAvatarThumbnail(student.avatarUrl) : undefined}
                  icon={!student.avatarUrl && <UserOutlined />}
                  className={`
                    transition-all duration-200
                    ${isMultiSelect && selectedStudents.includes(student.userId)
                      ? 'ring-2 ring-blue-300 shadow-md'
                      : 'ring-2 ring-gray-100 group-hover:ring-blue-100'
                    }
                  `}
                  onError={() => {
                    // 当头像加载失败时显示用户图标
                    return true; // 返回true表示使用fallback内容（即icon属性）
                  }}
                />
                <div>
                  <h3 className="text-base font-semibold text-gray-800 leading-tight mb-1">
                    {student.nickName}
                  </h3>
                  <p className="text-xs text-gray-500 font-medium">
                    {student.studentNumber}
                  </p>
                </div>
              </div>

              {/* 操作下拉菜单 */}
              <Dropdown
                menu={{
                  items: [
                    {
                      key: 'assignBlocks',
                      label: (
                        <div className="flex items-center gap-2 py-1.5">
                          <BlockOutlined className="text-blue-500" />
                          <span>分配积木</span>
                        </div>
                      ),
                      onClick: async (e) => {
                        e.domEvent.stopPropagation();
                        try {
                          await onAssignBlocks(student.userId);
                          onRefreshList?.();
                        } catch (error) {
                          console.error('分配积木失败:', error);
                        }
                      }
                    },
                    {
                      key: 'assignPoints',
                      label: (
                        <div className="flex items-center gap-2 py-1.5">
                          <ThunderboltOutlined className="text-yellow-500" />
                          <span>分配能量</span>
                        </div>
                      ),
                      onClick: (e) => {
                        e.domEvent.stopPropagation();
                        onAssignPoints(student);
                      }
                    },
                    {
                      key: 'publishTask',
                      label: (
                        <div className="flex items-center gap-2 py-1.5">
                          <FileTextOutlined className="text-green-500" />
                          <span>发布任务</span>
                        </div>
                      ),
                      onClick: (e) => {
                        e.domEvent.stopPropagation();
                        onPublishTask(student.userId);
                      }
                    },
                    {
                      key: 'useKeyPackage',
                      label: (
                        <div className="flex items-center gap-2 py-1.5">
                          <GiftOutlined className="text-purple-500" />
                          <span>兑换密钥</span>
                        </div>
                      ),
                      onClick: (e) => {
                        e.domEvent.stopPropagation();
                        onUseKeyPackage?.(student.userId);
                      }
                    },
                    {
                      key: 'resetPassword',
                      label: (
                        <div className="flex items-center gap-2 py-1.5">
                          <KeyOutlined className="text-orange-500" />
                          <span>重置密码</span>
                        </div>
                      ),
                      onClick: () => onResetPassword(student.userId)
                    },
                    {
                      key: 'divider',
                      type: 'divider'
                    },
                    {
                      key: 'remove',
                      label: (
                        <div className="flex items-center gap-2 py-1.5">
                          <DeleteOutlined className="text-red-500" />
                          <span>移出班级</span>
                        </div>
                      ),
                      danger: true,
                      onClick: () => onRemoveStudent(student.userId)
                    }
                  ]
                }}
                trigger={['click']}
                placement="bottomRight"
              >
                <Button
                  type="text"
                  className="flex items-center justify-center w-8 h-8 rounded-full hover:bg-gray-100/80 transition-colors"
                  icon={<EllipsisOutlined className="text-gray-400 text-lg" />}
                  onClick={(e) => e.stopPropagation()}
                />
              </Dropdown>
            </div>

            {/* 能量信息 */}
            <div className="space-y-3">
              <div className="bg-gray-50/70 rounded-xl p-3">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-xs font-medium text-gray-500">可用能量</span>
                  <div className="flex items-center gap-1.5">
                    <span className="text-base font-semibold text-green-600">
                      {student.availablePoints || 0}
                    </span>
                    <span className="text-xs text-gray-400">
                      / {Number(student.totalPoints || 0)}
                    </span>
                  </div>
                </div>
                <Progress
                  percent={student.totalPoints ? (Number(student.availablePoints || 0) / Number(student.totalPoints || 0)) * 100 : 0}
                  showInfo={false}
                  strokeColor={{
                    '0%': '#22C55E',
                    '100%': '#16A34A',
                  }}
                  trailColor="#E5E7EB"
                  size={8} // 替换 strokeWidth 为 size
                  className={`${student.totalPoints === 0 ? 'opacity-50' : ''} rounded-full`}
                />
              </div>

              {/* 添加模板使用信息 */}
              <div className="relative group">
                {student.currentTemplate ? (
                  <div className="bg-white rounded-xl p-3 border border-gray-100 hover:border-blue-100 transition-colors">
                    <div className="flex items-center justify-between">
                      <span className="text-xs font-medium text-gray-500">当前模板</span>
                      <Tag
                        color={student.currentTemplate.isOfficial ? "gold" : "blue"}
                        className="m-0 px-2 h-5 flex items-center text-[10px] font-medium"
                      >
                        {student.currentTemplate.isOfficial ? "官方" : "自定义"}
                      </Tag>
                    </div>
                    <div className="flex items-center gap-2 mt-2">
                      {student.currentTemplate.isOfficial ? (
                        <BlockOutlined className="text-amber-500 text-base flex-shrink-0" />
                      ) : (
                        <AppstoreOutlined className="text-blue-500 text-base flex-shrink-0" />
                      )}
                      <div className="text-sm font-medium text-gray-700 truncate">
                        {student.currentTemplate.templateName}
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="bg-gray-50/70 rounded-xl p-3 border border-gray-100/50">
                    <div className="flex items-center justify-between">
                      <span className="text-xs font-medium text-gray-500">使用模板</span>
                    </div>
                    <div className="flex items-center gap-2 mt-2 text-gray-400">
                      <BlockOutlined className="text-gray-300 text-base flex-shrink-0" />
                      <span className="text-sm">未使用任何模板</span>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}; 