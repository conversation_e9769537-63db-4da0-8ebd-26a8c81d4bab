import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, SelectQueryBuilder, In } from 'typeorm';
import { CourseSeries } from '../../../domain/entities/management/course-series.entity';
import { Course } from '../../../domain/entities/management/course.entity';
import { CourseTag } from '../../../domain/entities/marketplace/course-tag.entity';
import { CourseSeriesTag } from '../../../domain/entities/marketplace/course-series-tag.entity';
import {
  GetSeriesListQueryDto,
  SeriesListDataDto,
  SeriesInfoDto,
  TagInfoDto,
  ContentSummaryDto,
  PaginationDto,
  FilterStatsDto
} from '../../../application/dto/marketplace/series-list.dto';
import {
  SeriesDetailDataDto,
  CourseInfoDto,
  DefaultCourseDto
} from '../../../application/dto/marketplace/series-detail.dto';
import {
  CourseDetailDataDto
} from '../../../application/dto/marketplace/course-detail.dto';
import {
  GetTagsListQueryDto,
  TagsListDataDto,
  TagDetailDto,
  TagsPaginationDto
} from '../../../application/dto/marketplace/tags-list.dto';
import {
  CreateTagDto,
  UpdateTagDto,
  TagDetailDataDto
} from '../../../application/dto/marketplace/tag-management.dto';

@Injectable()
export class MarketplaceService {
  private readonly logger = new Logger(MarketplaceService.name);

  constructor(
    @InjectRepository(CourseSeries)
    private readonly courseSeriesRepository: Repository<CourseSeries>,

    @InjectRepository(Course)
    private readonly courseRepository: Repository<Course>,

    @InjectRepository(CourseTag)
    private readonly courseTagRepository: Repository<CourseTag>,

    @InjectRepository(CourseSeriesTag)
    private readonly courseSeriesTagRepository: Repository<CourseSeriesTag>,
  ) { }

  // 批量计算内容摘要，代替循环计算单个
  private async batchCalculateContentSummary(seriesIds: number[]): Promise<Map<number, ContentSummaryDto>> {
    // 1.健壮性校验，如果长度为0直接返回
    if (seriesIds.length === 0) {
      return new Map();
    }

    // 2.一次性查询所有系列的课程数据，将业务压力集中在服务器上
    const allCourses = await this.courseRepository.find({
      where: {
        seriesId: seriesIds.length === 1 ? seriesIds[0] : In(seriesIds),
        status: 1
      },
      select: ['seriesId', 'hasVideo', 'hasDocument', 'hasAudio', 'videoDuration', 'additionalResources']
    });

    // 3.按系列ID分组，坑位挖掘，有就丢，没就挖
    const coursesBySeriesId = new Map<number, any[]>();
    allCourses.forEach(course => {
      if (!coursesBySeriesId.has(course.seriesId)) {
        coursesBySeriesId.set(course.seriesId, []);
      }
      coursesBySeriesId.get(course.seriesId)!.push(course);
    });

    // 4.计算每个系列的内容摘要
    const summaryMap = new Map<number, ContentSummaryDto>();
    seriesIds.forEach(seriesId => {
      // 4.1获取当前系列的所有课程
      const courses = coursesBySeriesId.get(seriesId) || [];
      // 4.2计算内容摘要，查看该系列是否是属于某个特征的系列
      const hasVideo = courses.some(course => course.hasVideo === 1);
      const hasDocument = courses.some(course => course.hasDocument === 1);
      const hasAudio = courses.some(course => course.hasAudio === 1);

      // 4.3计算视频课程数量和文档课程数量
      const videoCourseCount = courses.filter(course => course.hasVideo === 1).length;
      const documentCourseCount = courses.filter(course => course.hasDocument === 1).length;

      // 4.4计算平均视频时长和总资源数量
      const totalVideoDuration = courses
        .filter(course => course.hasVideo === 1)
        .reduce((sum, course) => sum + (course.videoDuration || 0), 0);

      const averageVideoDuration = videoCourseCount > 0 ? Math.round(totalVideoDuration / videoCourseCount) : 0;

      const totalResourcesCount = courses.reduce((sum, course) => {
        const resources = course.additionalResources || [];
        return sum + (Array.isArray(resources) ? resources.length : 0);
      }, 0);

      // 4.5将计算结果存储到Map中
      summaryMap.set(seriesId, {
        hasVideo,
        hasDocument,
        hasAudio,
        videoCourseCount,
        documentCourseCount,
        averageVideoDuration,
        totalResourcesCount
      });
    });

    // 5.返回内容摘要Map
    return summaryMap;
  }

  /**
   * 查询系列课程列表
   */
  async getSeriesList(query: GetSeriesListQueryDto): Promise<SeriesListDataDto> {
    console.log('获取系列课程列表的请求DTO', query);

    // 1.提取query参数 设置默认值
    const { page = 1, pageSize = 10 } = query;

    // 2.构建基础查询（先简化，不连接其他表）
    const queryBuilder = this.courseSeriesRepository
      .createQueryBuilder('series');

    // 3.应用筛选条件
    this.applyFilters(queryBuilder, query);

    // 4.应用排序
    this.applySorting(queryBuilder, query.sortBy);

    // 5.计算总数（在分页前）
    const total = await queryBuilder.getCount();
    console.log('获取系列课程列表的总数', total);

    // 6.计算分页信息
    const totalPages = Math.ceil(total / pageSize);
    const pagination: PaginationDto = {
      page,
      pageSize,
      total,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1
    };

    // 7.应用分页并执行查询
    queryBuilder
      .skip((page - 1) * pageSize)
      .take(pageSize);

    // 8.执行查询获取系列列表
    const seriesList = await queryBuilder.getMany();
    console.log('获取系列课程列表的总数', total);
    // 9.获取系列的标签信息
    const seriesIds = seriesList.map(series => series.id);
    const seriesTagsMap = await this.getSeriesTagsMap(seriesIds);
    // 10.批量计算内容摘要
    const contentSummaryMap = await this.batchCalculateContentSummary(seriesIds);
    // 11.转换为API响应格式，包含标签的枚举对应中文信息
    const list: SeriesInfoDto[] = seriesList.map((series) => {
      const contentSummary = contentSummaryMap.get(series.id) || {
        hasVideo: false,
        hasDocument: false,
        hasAudio: false,
        videoCourseCount: 0,
        documentCourseCount: 0,
        averageVideoDuration: 0,
        totalResourcesCount: 0
      };
      const tags = seriesTagsMap.get(series.id) || [];

      return {
        id: series.id,
        title: series.title,
        description: series.description,
        coverImage: series.coverImage,
        category: series.category,
        categoryLabel: this.getCategoryLabel(series.category),
        status: series.status,
        statusLabel: this.getStatusLabel(series.status),
        projectMembers: series.projectMembers,
        totalCourses: series.totalCourses,
        totalStudents: series.totalStudents,
        contentSummary,
        createdAt: series.createdAt.toISOString(),
        tags
      };
    });

    // 12.获取筛选统计信息，拿到有文档的系列数量，有视频的系列数量
    const filterStats = await this.getFilterStats();

    return {
      list,
      pagination,
      filterStats
    };
  }

  /**
   * 获取系列课程详情
   */
  async getSeriesDetail(seriesId: number): Promise<SeriesDetailDataDto> {
    // 1.先查询系列信息
    const series = await this.courseSeriesRepository.findOne({ where: { id: seriesId } });

    if (!series) {
      throw new Error(`系列课程不存在，ID: ${seriesId}`);
    }

    // 2.基于系列ID查询课程列表
    const courses = await this.courseRepository.find({
      where: { seriesId, status: 1 },
      order: { orderIndex: 'ASC' },
      select: [
        'id', 'title', 'description', 'coverImage', 'orderIndex', 'status',
        'hasVideo', 'hasDocument', 'hasAudio', 'videoDuration', 'videoName',
        'firstTeachingTitle', 'resourcesCount', 'contentConfig', 'teachingInfo', 'additionalResources'
      ]
    });


    // 2.获取标签信息
    const seriesTagsMap = await this.getSeriesTagsMap([seriesId]);
    const tags = seriesTagsMap.get(seriesId) || [];
    console.log(courses);




    // 3.处理课程数据
    const courseList: CourseInfoDto[] = courses.map(course => ({
      id: course.id,
      title: course.title,
      description: course.description,
      coverImage: course.coverImage,
      orderIndex: course.orderIndex,
      status: course.status,
      statusLabel: this.getStatusLabel(course.status),
      hasVideo: course.hasVideo,
      hasDocument: course.hasDocument,
      hasAudio: course.hasAudio,
      videoDuration: course.videoDuration || 0,
      videoDurationLabel: this.formatDuration(course.videoDuration || 0),
      videoName: course.videoName || '',
      firstTeachingTitle: course.firstTeachingTitle || '',
      resourcesCount: course.resourcesCount || 0
    }));

    // 4.构建默认课程（直接使用已查询的数据）
    let defaultCourse: DefaultCourseDto | null = null;
    let currentCourseId = 0;

    if (courses.length > 0) {
      const firstCourse = courses[0];
      currentCourseId = firstCourse.id;

      defaultCourse = {
        ...courseList[0], // 复用已转换的基础数据
        contentConfig: firstCourse.contentConfig || {},
        teachingInfo: Array.isArray(firstCourse.teachingInfo) ? firstCourse.teachingInfo : [],
        additionalResources: Array.isArray(firstCourse.additionalResources) ? firstCourse.additionalResources : []
      };
    }

    // 5.返回结果
    return {
      id: series.id,
      title: series.title,
      description: series.description,
      coverImage: series.coverImage,
      category: series.category,
      categoryLabel: this.getCategoryLabel(series.category),
      status: series.status,
      statusLabel: this.getStatusLabel(series.status),
      projectMembers: series.projectMembers,
      totalCourses: series.totalCourses,
      totalStudents: series.totalStudents,
      creatorId: series.creatorId,
      createdAt: series.createdAt.toISOString(),
      updatedAt: series.updatedAt.toISOString(),
      tags,
      courses: courseList,
      defaultCourse: defaultCourse!,
      currentCourseId
    };
  }

  /**
   * 获取系列中指定课程的详细内容
   */
  async getCourseDetail(seriesId: number, courseId: number): Promise<CourseDetailDataDto> {
    // 1.验证系列是否存在
    const series = await this.courseSeriesRepository.findOne({
      where: { id: seriesId }
    });

    if (!series) {
      throw new Error(`系列课程不存在，ID: ${seriesId}`);
    }

    // 2.查询指定课程详情
    const course = await this.courseRepository.findOne({
      where: {
        id: courseId,
        seriesId: seriesId,
        status: 1 // 只获取已发布的课程
      },
      select: [
        'id', 'title', 'description', 'coverImage', 'orderIndex', 'status',
        'hasVideo', 'hasDocument', 'hasAudio', 'videoDuration', 'videoName',
        'firstTeachingTitle', 'resourcesCount', 'contentConfig', 'teachingInfo', 'additionalResources'
      ]
    });

    if (!course) {
      throw new Error(`课程不存在或未发布，系列ID: ${seriesId}，课程ID: ${courseId}`);
    }

    // 3.构造响应数据
    return {
      id: course.id,
      title: course.title,
      description: course.description,
      coverImage: course.coverImage,
      hasVideo: course.hasVideo,
      hasDocument: course.hasDocument,
      hasAudio: course.hasAudio,
      videoDuration: course.videoDuration || 0,
      videoDurationLabel: this.formatDuration(course.videoDuration || 0),
      videoName: course.videoName || '',
      resourcesCount: course.resourcesCount || 0,
      contentConfig: course.contentConfig || {},
      teachingInfo: Array.isArray(course.teachingInfo) ? course.teachingInfo : [],
      additionalResources: Array.isArray(course.additionalResources) ? course.additionalResources : [],
      orderIndex: course.orderIndex,
      status: course.status,
      statusLabel: this.getStatusLabel(course.status),
      currentCourseId: course.id
    };
  }

  /**
   * 获取课程标签列表
   */
  async getTagsList(query: GetTagsListQueryDto): Promise<TagsListDataDto> {
    // 1.提取query参数，设置默认值
    const { page = 1, pageSize = 50 } = query;

    // 2.构建基础查询
    const queryBuilder = this.courseTagRepository
      .createQueryBuilder('tag');

    // 3.应用筛选条件
    this.applyTagFilters(queryBuilder, query);

    // 4.应用排序（按使用次数倒序，然后按创建时间倒序）
    queryBuilder
      .orderBy('tag.usage_count', 'DESC')
      .addOrderBy('tag.created_at', 'DESC');

    // 5.计算总数（在分页前）
    const total = await queryBuilder.getCount();

    // 6.计算分页信息
    const totalPages = Math.ceil(total / pageSize);
    const pagination: TagsPaginationDto = {
      page,
      pageSize,
      total,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1
    };

    // 7.应用分页并执行查询
    queryBuilder
      .skip((page - 1) * pageSize)
      .take(pageSize);

    // 8.执行查询获取标签列表
    const tagsList = await queryBuilder.getMany();

    // 9.转换数据格式
    const list: TagDetailDto[] = tagsList.map(tag => ({
      id: tag.id,
      name: tag.name,
      color: tag.color,
      category: tag.category,
      categoryLabel: this.getTagCategoryLabel(tag.category),
      description: tag.description || '',
      usageCount: tag.usageCount,
      status: tag.status,
      statusLabel: this.getTagStatusLabel(tag.status),
      createdAt: tag.createdAt.toISOString()
    }));

    return {
      list,
      pagination
    };
  }

  /**
   * 创建标签
   */
  async createTag(createTagDto: CreateTagDto): Promise<TagDetailDataDto> {
    // 上UPDATE排他锁锁，防止并发查出来不存在同时设置到数据库出现重复情况
    
    // 1.检查标签名称是否已存在
    const existingTag = await this.courseTagRepository.findOne({
      where: { name: createTagDto.name }
    });

    if (existingTag) {
      throw new Error(`标签名称 "${createTagDto.name}" 已存在`);
    }

    // 2.创建新标签
    const newTag = this.courseTagRepository.create({
      name: createTagDto.name,
      color: createTagDto.color || '#007bff',
      category: createTagDto.category,
      description: createTagDto.description || '',
      status: createTagDto.status || 1,
      usageCount: 0
    });

    // 3.保存到数据库
    const savedTag = await this.courseTagRepository.save(newTag);

    // 4.返回标签详情
    return {
      id: savedTag.id,
      name: savedTag.name,
      color: savedTag.color,
      category: savedTag.category,
      categoryLabel: this.getTagCategoryLabel(savedTag.category),
      description: savedTag.description || '',
      usageCount: savedTag.usageCount,
      status: savedTag.status,
      statusLabel: this.getTagStatusLabel(savedTag.status),
      createdAt: savedTag.createdAt.toISOString(),
      updatedAt: savedTag.updatedAt.toISOString()
    };
  }

  /**
   * 更新标签
   */
  async updateTag(id: number, updateTagDto: UpdateTagDto): Promise<TagDetailDataDto> {
    // 1.检查标签是否存在
    const existingTag = await this.courseTagRepository.findOne({
      where: { id }
    });

    if (!existingTag) {
      throw new Error(`标签不存在，ID: ${id}`);
    }

    // 2.如果更新名称，检查新名称是否已被其他标签使用
    if (updateTagDto.name && updateTagDto.name !== existingTag.name) {
      const nameConflict = await this.courseTagRepository.findOne({
        where: { name: updateTagDto.name }
      });

      if (nameConflict) {
        throw new Error(`标签名称 "${updateTagDto.name}" 已存在`);
      }
    }

    // 3.更新标签信息
    await this.courseTagRepository.update(id, {
      ...(updateTagDto.name && { name: updateTagDto.name }),
      ...(updateTagDto.color && { color: updateTagDto.color }),
      ...(updateTagDto.category !== undefined && { category: updateTagDto.category }),
      ...(updateTagDto.description !== undefined && { description: updateTagDto.description }),
      ...(updateTagDto.status !== undefined && { status: updateTagDto.status })
    });

    // 4.获取更新后的标签
    const updatedTag = await this.courseTagRepository.findOne({
      where: { id }
    });

    // 5.返回更新后的标签详情
    return {
      id: updatedTag!.id,
      name: updatedTag!.name,
      color: updatedTag!.color,
      category: updatedTag!.category,
      categoryLabel: this.getTagCategoryLabel(updatedTag!.category),
      description: updatedTag!.description || '',
      usageCount: updatedTag!.usageCount,
      status: updatedTag!.status,
      statusLabel: this.getTagStatusLabel(updatedTag!.status),
      createdAt: updatedTag!.createdAt.toISOString(),
      updatedAt: updatedTag!.updatedAt.toISOString()
    };
  }

  /**
   * 删除标签
   */
  async deleteTag(id: number): Promise<void> {
    // 1.检查标签是否存在
    const existingTag = await this.courseTagRepository.findOne({
      where: { id }
    });

    if (!existingTag) {
      throw new Error(`标签不存在，ID: ${id}`);
    }

    // 2.检查标签是否正在被使用
    const usageCount = await this.courseSeriesTagRepository.count({
      where: { tagId: id }
    });

    if (usageCount > 0) {
      throw new Error(`标签正在被 ${usageCount} 个系列课程使用，无法删除`);
    }

    // 3.删除标签
    await this.courseTagRepository.delete(id);
  }

  /**
   * 获取单个标签详情
   */
  async getTagById(id: number): Promise<TagDetailDataDto> {
    // 1.查询标签
    const tag = await this.courseTagRepository.findOne({
      where: { id }
    });

    if (!tag) {
      throw new Error(`标签不存在，ID: ${id}`);
    }

    // 2.返回标签详情
    return {
      id: tag.id,
      name: tag.name,
      color: tag.color,
      category: tag.category,
      categoryLabel: this.getTagCategoryLabel(tag.category),
      description: tag.description || '',
      usageCount: tag.usageCount,
      status: tag.status,
      statusLabel: this.getTagStatusLabel(tag.status),
      createdAt: tag.createdAt.toISOString(),
      updatedAt: tag.updatedAt.toISOString()
    };
  }

  /**
   * 应用标签筛选条件
   */
  private applyTagFilters(queryBuilder: SelectQueryBuilder<CourseTag>, query: GetTagsListQueryDto): void {
    // 1.分类筛选：0=难度，1=类型，2=特色，3=其他
    if (query.category !== undefined) {
      queryBuilder.andWhere('tag.category = :category', { category: query.category });
    }

    // 2.状态筛选：0=禁用，1=启用
    if (query.status !== undefined) {
      queryBuilder.andWhere('tag.status = :status', { status: query.status });
    }

    // 3.关键词搜索：在标签名称中模糊匹配
    if (query.keyword) {
      queryBuilder.andWhere('tag.name LIKE :keyword', { keyword: `%${query.keyword}%` });
    }
  }

  /**
   * 应用筛选条件
   */
  private applyFilters(queryBuilder: SelectQueryBuilder<CourseSeries>, query: GetSeriesListQueryDto): void {
    // 1.若提供了分类，加入查询条件，0为官方，1为社区
    if (query.category !== undefined) {
      queryBuilder.andWhere('series.category = :category', { category: query.category });
    }

    // 2.若提供了状态，加入查询条件，0为草稿，1为发布，2为归档
    if (query.status !== undefined) {
      queryBuilder.andWhere('series.status = :status', { status: query.status });
    }

    // 3.description的模糊搜索关键词
    if (query.keyword) {
      queryBuilder.andWhere(
        '(series.title LIKE :keyword OR series.description LIKE :keyword)',
        { keyword: `%${query.keyword}%` }
      );
    }

    // 4.标签筛选：支持多标签组合筛选（暂时注释，需要重新实现）
    if (query.tagIds) {
      // 4.1 ,分割为数组，去空格转number类型，筛出不为空的number
      const tagIds = query.tagIds.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));
      // 4.2若大于0 加入构造条件
      if (tagIds.length > 0) {
        queryBuilder.andWhere('EXISTS (SELECT 1 FROM course_series_tags cst WHERE cst.series_id = series.id AND cst.tag_id IN (:...tagIds))', { tagIds });
      }
    }

    // 5.内容类型筛选：根据课程内容类型筛选系列
    // 筛选出系列下至少有一门课程（seriesId=series.id）满足hasVideo, hasDocument, hasAudio条件的系列
    if (query.hasVideo !== undefined) {
      queryBuilder.andWhere('EXISTS (SELECT 1 FROM courses c WHERE c.series_id = series.id AND c.status = 1 AND c.has_video = :hasVideo)', { hasVideo: query.hasVideo });
    }

    if (query.hasDocument !== undefined) {
      queryBuilder.andWhere('EXISTS (SELECT 1 FROM courses c WHERE c.series_id = series.id AND c.status = 1 AND c.has_document = :hasDocument)', { hasDocument: query.hasDocument });
    }

    if (query.hasAudio !== undefined) {
      queryBuilder.andWhere('EXISTS (SELECT 1 FROM courses c WHERE c.series_id = series.id AND c.status = 1 AND c.has_audio = :hasAudio)', { hasAudio: query.hasAudio });
    }
  }

  /**
   * 应用排序
   */
  private applySorting(queryBuilder: SelectQueryBuilder<CourseSeries>, sortBy: string = 'latest'): void {
    switch (sortBy) {
      case 'popular':
        // 按热门度排序：学习人数倒序
        queryBuilder.orderBy('series.total_students', 'DESC');
        break;
      case 'duration':
        // 按时长排序：根据系列下课程的平均视频时长排序，连表，系列分组，聚合结果函数计算as 为avgDuration并且根据这个字段排序
        queryBuilder
          .leftJoin('courses', 'durationCourse', 'durationCourse.series_id = series.id AND durationCourse.status = 1 AND durationCourse.has_video = 1')
          .addSelect('COALESCE(AVG(durationCourse.video_duration), 0)', 'avgDuration')
          .groupBy('series.id')
          .orderBy('avgDuration', 'DESC');
        break;
      case 'latest':
      default:
        // 按最新排序：创建时间倒序（默认）
        queryBuilder.orderBy('series.created_at', 'DESC');
        break;
    }
  }

  /**
   * 获取系列标签映射
   */
  private async getSeriesTagsMap(seriesIds: number[]): Promise<Map<number, TagInfoDto[]>> {
    if (seriesIds.length === 0) {
      return new Map();
    }

    // 1.使用原生SQL查询获取标签信息,卡死人版本
    // const seriesTagsQuery = `
    //   SELECT
    //     cst.series_id,
    //     ct.id as tag_id,
    //     ct.name as tag_name,
    //     ct.color as tag_color,
    //     ct.category as tag_category,
    //     SLEEP(1.5) as delay_result  -- 添加1.5秒延迟模拟慢查询
    //   FROM course_series_tags cst
    //   LEFT JOIN course_tags ct ON ct.id = cst.tag_id
    //   WHERE cst.series_id IN (${seriesIds.join(',')})
    //     AND ct.status = 1
    //   ORDER BY cst.series_id, ct.id
    // `;
    // 1.使用原生SQL查询获取标签信息
    const seriesTagsQuery = `
      SELECT
        cst.series_id,
        ct.id as tag_id,
        ct.name as tag_name,
        ct.color as tag_color,
        ct.category as tag_category
      FROM course_series_tags cst
      LEFT JOIN course_tags ct ON ct.id = cst.tag_id
      WHERE cst.series_id IN (${seriesIds.join(',')})
        AND ct.status = 1
      ORDER BY cst.series_id, ct.id
    `;
    // 2.执行sql语句
    const rawResults = await this.courseSeriesRepository.query(seriesTagsQuery);
    // 3，构造map  seriesId->[TagInfoDto]
    const tagsMap = new Map<number, TagInfoDto[]>();

    // 4.遍历结果，构造map
    rawResults.forEach((row: any) => {
      const seriesId = row.series_id;
      // 4.1 如果系列id对应的标签数组不存在，初始化一个空数组
      if (!tagsMap.has(seriesId)) {
        tagsMap.set(seriesId, []);
      }
      // 4.2 如果标签id存在，将标签信息添加到标签数组中
      if (row.tag_id) { // 确保标签存在
        // 4.2.1 确保seriesId对应的标签数组存在，使用语法糖 ！相当于 if(tagsMap.get(seriesId)) 
        // 而？.为可选的意思，！为非空的意思
        tagsMap.get(seriesId)!.push({
          id: row.tag_id,
          name: row.tag_name,
          color: row.tag_color,
          category: row.tag_category,
          categoryLabel: this.getTagCategoryLabel(row.tag_category)
        });
      }
    });

    return tagsMap;
  }


  /**
   * 获取筛选统计信息
   */
  private async getFilterStats(): Promise<FilterStatsDto> {
    // 一次性查询所有统计信息，避免多次数据库调用
    const statsQuery = `
      SELECT 
        COUNT(DISTINCT cs.id) as totalSeries,
        COUNT(DISTINCT CASE WHEN cs.category = 0 THEN cs.id END) as officialCount,
        COUNT(DISTINCT CASE WHEN cs.category = 1 THEN cs.id END) as communityCount,
        COUNT(DISTINCT CASE WHEN c.has_video = 1 AND c.status = 1 THEN cs.id END) as videoSeriesCount,
        COUNT(DISTINCT CASE WHEN c.has_document = 1 AND c.status = 1 THEN cs.id END) as documentSeriesCount
      FROM course_series cs
      LEFT JOIN courses c ON c.series_id = cs.id
    `;

    const [result] = await this.courseSeriesRepository.query(statsQuery);

    return {
      totalSeries: parseInt(result.totalSeries) || 0,
      officialCount: parseInt(result.officialCount) || 0,
      communityCount: parseInt(result.communityCount) || 0,
      videoSeriesCount: parseInt(result.videoSeriesCount) || 0,
      documentSeriesCount: parseInt(result.documentSeriesCount) || 0
    };
  }

  /**
   * 获取分类标签
   */
  private getCategoryLabel(category: number): string {
    const labels = {
      0: '官方',
      1: '社区'
    };
    return labels[category] || '未知';
  }

  /**
   * 获取状态标签
   */
  private getStatusLabel(status: number): string {
    const labels = {
      0: '草稿',
      1: '已发布',
      2: '已归档'
    };
    return labels[status] || '未知';
  }

  /**
   * 获取标签分类标签
   */
  private getTagCategoryLabel(category: number): string {
    const labels = {
      0: '难度',
      1: '类型',
      2: '特色',
      3: '其他'
    };
    return labels[category] || '其他';
  }

  /**
   * 获取标签状态标签
   */
  private getTagStatusLabel(status: number): string {
    const labels = {
      0: '禁用',
      1: '启用'
    };
    return labels[status] || '未知';
  }

  /**
   * 格式化时长（秒转换为可读格式）
   */
  private formatDuration(seconds: number): string {
    if (seconds <= 0) {
      return '0分钟';
    }

    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);

    if (hours > 0) {
      return minutes > 0 ? `${hours}小时${minutes}分钟` : `${hours}小时`;
    } else {
      return `${minutes}分钟`;
    }
  }
}
