import { useState, useCallback } from 'react';

interface AlertOptions {
  title?: string;
  message: string;
  type?: 'info' | 'success' | 'warning' | 'error';
  confirmText?: string;
  cancelText?: string;
  showCancel?: boolean;
  onConfirm?: () => void;
  onCancel?: () => void;
}

interface AlertState {
  isOpen: boolean;
  title?: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  confirmText: string;
  cancelText: string;
  showCancel: boolean;
  onConfirm: () => void;
  onCancel?: () => void;
}

export const useCustomAlert = () => {
  const [alertState, setAlertState] = useState<AlertState>({
    isOpen: false,
    message: '',
    type: 'info',
    confirmText: '确定',
    cancelText: '取消',
    showCancel: false,
    onConfirm: () => {}
  });

  const showAlert = useCallback((options: AlertOptions) => {
    setAlertState({
      isOpen: true,
      title: options.title,
      message: options.message,
      type: options.type || 'info',
      confirmText: options.confirmText || '确定',
      cancelText: options.cancelText || '取消',
      showCancel: options.showCancel || false,
      onConfirm: () => {
        options.onConfirm?.();
        setAlertState(prev => ({ ...prev, isOpen: false }));
      },
      onCancel: options.onCancel ? () => {
        options.onCancel?.();
        setAlertState(prev => ({ ...prev, isOpen: false }));
      } : undefined
    });
  }, []);

  const hideAlert = useCallback(() => {
    setAlertState(prev => ({ ...prev, isOpen: false }));
  }, []);

  // 便捷方法
  const alert = useCallback((message: string, options?: Omit<AlertOptions, 'message'>) => {
    showAlert({ message, ...options });
  }, [showAlert]);

  const confirm = useCallback((message: string, onConfirm: () => void, options?: Omit<AlertOptions, 'message' | 'onConfirm'>) => {
    showAlert({
      message,
      onConfirm,
      showCancel: true,
      ...options
    });
  }, [showAlert]);

  const success = useCallback((message: string, options?: Omit<AlertOptions, 'message' | 'type'>) => {
    showAlert({ message, type: 'success', ...options });
  }, [showAlert]);

  const warning = useCallback((message: string, options?: Omit<AlertOptions, 'message' | 'type'>) => {
    showAlert({ message, type: 'warning', ...options });
  }, [showAlert]);

  const error = useCallback((message: string, options?: Omit<AlertOptions, 'message' | 'type'>) => {
    showAlert({ message, type: 'error', ...options });
  }, [showAlert]);

  return {
    alertState,
    showAlert,
    hideAlert,
    alert,
    confirm,
    success,
    warning,
    error
  };
};
