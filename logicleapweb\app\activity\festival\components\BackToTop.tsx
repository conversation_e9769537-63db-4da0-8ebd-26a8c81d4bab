'use client'

import React, { useState, useEffect } from 'react'
import { ChevronUp } from 'lucide-react'

interface BackToTopProps {
  scrollThreshold?: number; // 滚动多少像素后显示按钮
  bottom?: number; // 按钮距离底部的距离
  right?: number; // 按钮距离右侧的距离
  zIndex?: number; // 按钮的z-index
  forceVisible?: boolean; // 强制显示按钮（用于调试）
}

const BackToTop: React.FC<BackToTopProps> = ({
  scrollThreshold = 300,
  bottom = 80,
  right = 80,
  zIndex = 50,
  forceVisible = false
}) => {
  const [isVisible, setIsVisible] = useState(false);

  // 监听滚动事件，决定是否显示按钮
  useEffect(() => {
    const toggleVisibility = () => {
      // 检查多个可能的滚动容器
      const pageContainer = document.querySelector('.activity-festival-page');
      const body = document.body;
      const documentElement = document.documentElement;

      let scrollTop = 0;

      if (pageContainer && pageContainer.scrollTop > 0) {
        scrollTop = pageContainer.scrollTop;
      } else if (body && body.scrollTop > 0) {
        scrollTop = body.scrollTop;
      } else if (documentElement && documentElement.scrollTop > 0) {
        scrollTop = documentElement.scrollTop;
      } else {
        scrollTop = window.scrollY || window.pageYOffset || 0;
      }

      if (forceVisible || scrollTop > scrollThreshold) {
        setIsVisible(true);
      } else {
        setIsVisible(false);
      }
    };

    // 监听多个滚动事件源
    const pageContainer = document.querySelector('.activity-festival-page');

    if (pageContainer) {
      pageContainer.addEventListener('scroll', toggleVisibility);
    }

    window.addEventListener('scroll', toggleVisibility);
    document.addEventListener('scroll', toggleVisibility);

    // 初始检查
    toggleVisibility();

    // 清理事件监听
    return () => {
      if (pageContainer) {
        pageContainer.removeEventListener('scroll', toggleVisibility);
      }
      window.removeEventListener('scroll', toggleVisibility);
      document.removeEventListener('scroll', toggleVisibility);
    };
  }, [scrollThreshold]);

  // 点击按钮滚动到顶部
  const scrollToTop = () => {
    // 尝试多种滚动方式，确保能够正常工作
    const pageContainer = document.querySelector('.activity-festival-page');

    // 方式1：滚动页面容器
    if (pageContainer) {
      pageContainer.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    }

    // 方式2：滚动body
    if (document.body) {
      document.body.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    }

    // 方式3：滚动document element
    if (document.documentElement) {
      document.documentElement.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    }

    // 方式4：滚动window（备用方案）
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  return (
    <button
      onClick={scrollToTop}
      className={`fixed p-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-full shadow-lg transition-all duration-300 hover:from-blue-700 hover:to-indigo-700 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-blue-400 backdrop-blur-sm ${
        isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10 pointer-events-none'
      }`}
      style={{
        bottom: `${bottom}px`,
        right: `${right}px`,
        zIndex,
        background: isVisible ? 'linear-gradient(135deg, #4766c2, #6c8ae4)' : undefined,
        boxShadow: '0 4px 15px rgba(71, 102, 194, 0.3)'
      }}
      aria-label="回到顶部"
    >
      <ChevronUp className="w-5 h-5" />
    </button>
  );
};

export default BackToTop;
