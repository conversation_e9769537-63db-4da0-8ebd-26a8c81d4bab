import { Module, Global } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { QueryInterceptor } from './interceptor/query-interceptor';
import { DatabaseTestController } from './controller/database-test.controller';
import { databaseMonitorConfig } from 'src/util/database/monitor/config/database-monitor.config';
import { DatabaseMonitorController } from './controller/database-monitor.controller';
import { QueryMonitorService } from './service/query-monitor.service';

@Global()
@Module({
  imports: [
    ConfigModule.forRoot({
      load: [() => databaseMonitorConfig],
    }),
  ],
  providers: [
    QueryMonitorService,
    QueryInterceptor,
  ],
  controllers: [
    DatabaseMonitorController,
    DatabaseTestController,
  ],
  exports: [
    QueryMonitorService,
    QueryInterceptor,
  ],
})
export class DatabaseMonitorModule {}
