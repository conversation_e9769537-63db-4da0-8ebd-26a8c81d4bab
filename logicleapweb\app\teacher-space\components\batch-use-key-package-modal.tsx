'use client'

import { useState, useRef, useMemo } from 'react';
import { Modal, Button, Table, Upload, Space, Typography, Alert, Progress, Tabs, Checkbox, Select, Input, Tooltip, Dropdown, Menu, Avatar } from 'antd';
import {
  InboxOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  DeleteOutlined,
  KeyOutlined,
  GiftOutlined,
  WarningOutlined,
  SearchOutlined,
  DownOutlined,
  UserOutlined,
  EditOutlined,
  InfoOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import type { UploadFile, UploadProps } from 'antd/es/upload/interface';
import * as XLSX from 'xlsx';
import { keyPackageApi } from '@/lib/api/key_package';
import { GetNotification } from 'logic-common/dist/components/Notification';

const { Dragger } = Upload;
const { Text, Title } = Typography;

interface Student {
  userId: number;
  nickName: string;
  avatarUrl?: string;
  studentNumber?: string;
  availablePoints?: number;
  lastLoginTime?: string;
  templateName?: string;
  id?: number;
}

interface BatchUseKeyPackageModalProps {
  open: boolean;
  selectedStudentIds: number[];
  students: Student[];
  onClose: () => void;
  onSuccess: () => void;
  onGoToAssignPoints?: (studentIds: number[]) => void;
}

interface KeyData {
  key: string;
  valid?: boolean;
  message?: string;
  studentId?: number;
  selected?: boolean;
}

export const BatchUseKeyPackageModal: React.FC<BatchUseKeyPackageModalProps> = ({
  open,
  selectedStudentIds,
  students,
  onClose,
  onSuccess,
  onGoToAssignPoints
}) => {
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [keyData, setKeyData] = useState<KeyData[]>([]);
  const [loading, setLoading] = useState(false);
  const [importing, setImporting] = useState(false);
  const [importStep, setImportStep] = useState<'upload' | 'preview' | 'importing' | 'complete'>('upload');
  const [importResults, setImportResults] = useState<{
    success: number;
    failed: number;
    total: number;
  }>({ success: 0, failed: 0, total: 0 });
  const [validationProgress, setValidationProgress] = useState({
    total: 0,
    current: 0,
    validCount: 0,
    invalidCount: 0,
    skippedCount: 0
  });
  const [isSpecialExchange, setIsSpecialExchange] = useState(true);
  const [teacherId, setTeacherId] = useState<number | undefined>(() => {
    const userInfo = localStorage.getItem('user');
    if (userInfo) {
      try {
        const parsed = JSON.parse(userInfo);
        console.log('从本地存储获取的用户信息:', parsed);
        const possibleId = parsed.userId || parsed.teacherId || parsed.id;
        console.log('获取到的教师ID:', possibleId);
        return possibleId ? Number(possibleId) : undefined;
      } catch (e) {
        console.error('解析userInfo失败:', e);
        return undefined;
      }
    }
    return undefined;
  });
  const notification = GetNotification();

  // 创建学生ID到姓名的映射，优化查找性能 (移到组件顶层)
  const studentNameMap = useMemo(() => {
    const map = new Map<number, { name: string, avatar?: string }>();
    students.forEach(student => {
      map.set(student.userId, { name: student.nickName, avatar: student.avatarUrl });
    });
    return map;
  }, [students]);

  // 处理文件上传
  const handleUpload: UploadProps['onChange'] = (info) => {
    setFileList(info.fileList.slice(-1)); // 只保留最后上传的文件

    if (info.file.status === 'done') {
      notification.success(`${info.file.name} 上传成功`);
    } else if (info.file.status === 'error') {
      notification.error(`${info.file.name} 上传失败`);
    }
  };

  // 处理文件解析
  const handleParseFile = async (file: File) => {
    try {
      setImporting(true);

      // 读取Excel文件内容
      const reader = new FileReader();
      reader.onload = async (e) => {
        try {
          const data = new Uint8Array(e.target?.result as ArrayBuffer);
          const workbook = XLSX.read(data, { type: 'array' });

          // 获取第一个工作表
          const worksheet = workbook.Sheets[workbook.SheetNames[0]];

          // 将工作表转换为JSON对象
          const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

          // 忽略第一行，获取第二列的数据作为密钥
          const keys: KeyData[] = jsonData.slice(1).map((row: any) => {
            // 确保行是数组并且有第二个元素
            if (Array.isArray(row) && row.length >= 2) {
              return {
                key: String(row[1]).trim(),
                valid: true
              };
            }
            return {
              key: '',
              valid: false,
              message: '无效的数据格式'
            };
          }).filter(item => item.key !== '');

          console.log(`Excel导入结果: 共读取 ${keys.length} 条密钥数据`);

          // 初始化所有密钥状态
          let allKeys = [...keys];

          // 验证密钥有效性
          console.log(`开始验证密钥: 共 ${allKeys.length} 个待验证密钥`);

          // 初始化验证进度
          setValidationProgress({
            total: allKeys.length,
            current: 0,
            validCount: 0,
            invalidCount: 0,
            skippedCount: 0
          });

          // 跟踪有效密钥数量
          let validKeysCount = 0;

          // 逐个验证密钥
          for (let i = 0; i < allKeys.length; i++) {
            const item = allKeys[i];
            try {
              console.log(`验证密钥 [${i + 1}/${allKeys.length}]: ${item.key}`);
              // 更新验证进度
              setValidationProgress(prev => ({
                ...prev,
                current: i + 1
              }));

              if (!item.key) {
                allKeys[i].valid = false;
                allKeys[i].message = '密钥为空';
                setValidationProgress(prev => ({
                  ...prev,
                  invalidCount: prev.invalidCount + 1
                }));
                continue;
              }

              // 调用验证API - 使用第一个学生ID进行验证
              // 只是验证密钥是否有效，不是实际分配给哪个学生
              const validationUserId = selectedStudentIds[0] || 0;
              const response = await keyPackageApi.validateKey({
                key: item.key,
                userId: validationUserId
              });
              console.log("111111111111", response);

              // 更新验证结果
              if (response.data?.data?.valid === true) {
                allKeys[i].valid = true;
                console.log(`密钥 ${item.key} 验证成功`);

                // 增加有效密钥计数
                validKeysCount++;
                setValidationProgress(prev => ({
                  ...prev,
                  validCount: prev.validCount + 1
                }));

                // 检查已验证的有效密钥数量是否已经满足学生数量
                // 如果有效密钥数量已经等于或超过学生数量，则停止验证剩余的密钥
                if (validKeysCount >= selectedStudentIds.length) {
                  console.log(`已验证到足够的有效密钥(${validKeysCount}/${selectedStudentIds.length}个)，停止验证剩余的密钥。`);
                  // 将剩余未验证的密钥标记为"未验证"
                  for (let j = i + 1; j < allKeys.length; j++) {
                    allKeys[j].valid = undefined;
                    allKeys[j].message = '未验证(已跳过)';
                  }

                  // 计算跳过的密钥数量
                  const skippedCount = allKeys.length - i - 1;

                  // 更新验证进度到全部完成
                  setValidationProgress(prev => ({
                    ...prev,
                    current: allKeys.length,
                    skippedCount: skippedCount
                  }));

                  console.log(`跳过验证剩余 ${skippedCount} 个密钥`);
                  break; // 跳出验证循环
                }
              } else {
                allKeys[i].valid = false;
                const errorMsg = response?.data?.msg || '密钥验证失败';
                allKeys[i].message = errorMsg;
                console.log(`密钥 ${item.key} 验证失败: ${errorMsg}`);
                setValidationProgress(prev => ({
                  ...prev,
                  invalidCount: prev.invalidCount + 1
                }));
              }
            } catch (error: any) {
              // A一处理验证失败的情况
              allKeys[i].valid = false;
              const errorMsg = error.response?.data?.msg || '密钥验证失败';
              allKeys[i].message = errorMsg;
              console.log(`密钥 ${item.key} 验证出错: ${errorMsg}`);
              setValidationProgress(prev => ({
                ...prev,
                invalidCount: prev.invalidCount + 1
              }));
            }
          }

          // 分离有效和无效密钥
          const validKeys = allKeys.filter(k => k.valid === true);
          const invalidKeys = allKeys.filter(k => k.valid === false);
          const skippedKeys = allKeys.filter(k => k.valid === undefined);

          console.log(`验证完成：共 ${allKeys.length} 条密钥，有效 ${validKeys.length} 条，无效 ${invalidKeys.length} 条，跳过 ${skippedKeys.length} 条`);

          // 智能分配密钥给学生
          const assignedKeys: KeyData[] = [];
          const unassignedValidKeys: KeyData[] = [...validKeys];
          const unassignedInvalidKeys: KeyData[] = [...invalidKeys];
          const unassignedSkippedKeys: KeyData[] = [...skippedKeys];

          // 先分配有效密钥给学生
          for (let i = 0; i < selectedStudentIds.length; i++) {
            const studentId = selectedStudentIds[i];

            if (unassignedValidKeys.length > 0) {
              // 有有效密钥可用，分配第一个有效密钥
              const validKey = unassignedValidKeys.shift()!;
              assignedKeys.push({
                ...validKey,
                studentId,
                message: '有效',
                selected: true
              });
            } else {
              // 有效密钥用完，添加一个空密钥记录让用户手动输入
              assignedKeys.push({
                key: '',
                valid: false,
                message: '请手动输入密钥',
                studentId,
                selected: false
              });
            }
          }

          // 添加剩余的未分配密钥
          // 添加剩余的有效密钥（标记为未分配）
          unassignedValidKeys.forEach(key => {
            assignedKeys.push({
              ...key,
              studentId: undefined,
              message: '未分配（多余）',
              selected: false
            });
          });

          // 添加剩余的无效密钥（标记为未分配）
          unassignedInvalidKeys.forEach(key => {
            assignedKeys.push({
              ...key,
              studentId: undefined,
              selected: false
            });
          });

          // 添加跳过验证的密钥（标记为未分配）
          unassignedSkippedKeys.forEach(key => {
            assignedKeys.push({
              ...key,
              studentId: undefined,
              message: key.message || '未验证(已跳过)',
              selected: false
            });
          });

          // 打印分配结果
          console.log('分配结果:', assignedKeys);

          // 统计有多少学生成功分配了有效密钥
          const studentsWithValidKey = assignedKeys.filter(k => k.valid && k.studentId).length;
          console.log(`${studentsWithValidKey}/${selectedStudentIds.length} 名学生分配到有效密钥`);

          // 按无效原因分组并打印
          const invalidByReason: Record<string, KeyData[]> = {};
          assignedKeys.filter(k => !k.valid).forEach(key => {
            const reason = key.message || '未知原因';
            if (!invalidByReason[reason]) {
              invalidByReason[reason] = [];
            }
            invalidByReason[reason].push(key);
          });

          console.log('无效密钥统计:');
          Object.entries(invalidByReason).forEach(([reason, keys]) => {
            console.log(`- ${reason}: ${keys.length} 条`);
          });

          setKeyData(assignedKeys);
          setImportStep('preview');
        } catch (error) {
          console.error('解析Excel文件失败:', error);
          notification.error('解析Excel文件失败，请确保文件格式正确');
        } finally {
          setImporting(false);
        }
      };

      reader.onerror = () => {
        notification.error('文件读取失败');
        setImporting(false);
      };

      reader.readAsArrayBuffer(file);
    } catch (error) {
      console.error('处理文件上传失败:', error);
      setImporting(false);
    }
  };

  // 处理学生选择/取消选择
  const handleToggleStudentSelection = (keyValue: string) => {
    console.log('点击选择/取消选择密钥:', keyValue);
    setKeyData(prevData => {
      // 创建一个全新的数组，确保React检测到状态变化
      return prevData.map(item => {
        if (item.key === keyValue) {
          console.log('更改选择状态:', item.key, '从', item.selected, '到', !item.selected);
          // 返回一个新对象，改变selected状态
          return { ...item, selected: !item.selected };
        }
        return item;
      });
    });
  };

  // 处理手动输入或选择密钥
  const handleManualKeyInput = (studentId: number, value: string | undefined) => {
    // 处理value为undefined的情况
    const inputValue = value || '';

    // 检查是否选择的是已存在于keyData中的密钥（已验证过的）
    const existingKey = keyData.find(item => item.key === inputValue);

    // 检查是否选择的是已分配的密钥
    const alreadyAssignedKey = keyData.find(
      item => item.key === inputValue && item.studentId && item.studentId !== studentId
    );

    setKeyData(prevData => {
      let newData = [...prevData];

      // 如果选择了已分配给其他学生的密钥
      if (alreadyAssignedKey) {
        // 先找到当前学生和被替换学生的记录索引
        const currentStudentIndex = newData.findIndex(
          item => item.studentId === studentId
        );
        const assignedKeyIndex = newData.findIndex(
          item => item.key === inputValue && item.studentId
        );

        if (currentStudentIndex !== -1 && assignedKeyIndex !== -1) {
          // 更新当前学生记录 - 使用选择的密钥，保留其验证状态
          const originalKeyData = newData[assignedKeyIndex];
          newData[currentStudentIndex] = {
            ...originalKeyData, // 复制密钥的信息，包括验证状态
            studentId: studentId,
            selected: originalKeyData.valid === true // 如果密钥有效则自动选中
          };

          // 更新被替换学生的记录 - 变为空白记录
          newData[assignedKeyIndex] = {
            key: '',
            valid: false,
            message: '请手动输入密钥',
            studentId: newData[assignedKeyIndex].studentId,
            selected: false
          };
        }

        return newData;
      }

      // 如果选择的是已经存在于keyData中的密钥（已验证过）
      if (existingKey && existingKey.valid !== undefined) {
        return newData.map(item => {
          if (item.studentId === studentId) {
            return {
              ...item,
              key: inputValue,
              valid: existingKey.valid, // 保留原始验证状态
              message: existingKey.message || (existingKey.valid ? '有效' : '无效'),
              selected: existingKey.valid === true // 如果验证为有效则默认选中
            };
          }
          return item;
        });
      }

      // 处理手动输入的新密钥（需要验证）
      return newData.map(item => {
        if (item.studentId === studentId && (!item.valid || item.valid === undefined)) {
          return {
            ...item,
            key: inputValue,
            // 如果密钥不为空，则标记为待验证
            valid: inputValue.trim() !== '' ? undefined : false,
            message: inputValue.trim() !== '' ? '请验证密钥' : '请手动输入密钥',
            selected: false // 未验证前不允许选择
          };
        }
        return item;
      });
    });
  };

  // 验证手动输入的密钥
  const handleValidateManualKey = async (studentId: number) => {
    const keyItem = keyData.find(item => item.studentId === studentId && item.valid === undefined);
    if (!keyItem || !keyItem.key) return;

    try {
      // 显示加载状态
      setKeyData(prev => prev.map(item =>
        item.studentId === studentId && item.valid === undefined
          ? { ...item, message: '验证中...' }
          : item
      ));

      // 调用验证API
      const response = await keyPackageApi.validateKey({
        key: keyItem.key,
        userId: studentId
      });

      // 更新验证结果
      setKeyData(prev => prev.map(item => {
        if (item.studentId === studentId && item.valid === undefined) {
          // 增加对 response.data 和 response.data.data 的检查
          const isValid = response?.data?.data?.valid === true;
          return {
            ...item,
            valid: isValid,
            selected: isValid, // 如果有效，默认选中
            message: isValid ? '有效' : (response?.data?.msg || response?.data?.data?.message || '密钥无效')
          };
        }
        return item;
      }));

      // 增加对 response.data 和 response.data.data 的检查
      if (response?.data?.data?.valid === true) {
        notification.success('密钥验证成功，已自动选中');
      } else {
        notification.error('密钥无效: ' + (response?.data?.msg || response?.data?.data?.message || ''));
      }
    } catch (error: any) {
      console.error('验证密钥失败:', error);
      notification.error('验证失败: ' + (error.response?.data?.msg || error.response?.data?.message || '未知错误'));
      setKeyData(prev => prev.map(item =>
        item.studentId === studentId && item.valid === undefined
          ? {
            ...item,
            valid: false,
            message: error.response?.data?.msg || error.response?.data?.message || '验证失败'
          }
          : item
      ));
    }
  };

  // 全选/取消全选
  const handleToggleAllSelection = (checked: boolean) => {
    setKeyData(prev => {
      return prev.map(item => {
        if (item.studentId && item.valid) {
          return { ...item, selected: checked };
        }
        return item;
      });
    });
  };

  // 开始批量兑换操作
  const handleBatchUseKeys = async () => {
    if (isSpecialExchange && !teacherId) {
      notification.error('特殊兑换模式下必须提供教师ID，请联系管理员');
      return;
    }

    setLoading(true);
    setImportStep('importing');

    console.log('当前特殊兑换状态:', isSpecialExchange);
    console.log('使用的教师ID:', teacherId);

    let successCount = 0;
    let failedCount = 0;

    // 只处理有效且已分配且已选择的密钥
    const validAssignedKeys = keyData.filter(item => item.valid && item.studentId && item.selected);
    const total = validAssignedKeys.length;

    if (total === 0) {
      notification.warning('没有选择任何有效密钥进行兑换');
      setImportStep('preview');
      setLoading(false);
      return;
    }

    const updatedKeyData = [...keyData];

    // 逐个处理每个密钥
    for (const item of validAssignedKeys) {
      if (!item.studentId || !item.key) continue;

      const keyIndex = keyData.findIndex(k => k.key === item.key);

      try {
        console.log(`兑换密钥: ${item.key} 为学生 ${item.studentId}`);

        // 准备API参数
        const apiParams = {
          key: item.key,
          userId: item.studentId,
          isSpecial: isSpecialExchange
        };

        // 只有在特殊兑换模式下才添加teacherId
        if (isSpecialExchange) {
          if (!teacherId) {
            console.error('特殊兑换模式下未找到教师ID');
            updatedKeyData[keyIndex] = {
              ...item,
              valid: false,
              message: '兑换失败: 特殊兑换需要提供教师ID'
            };
            failedCount++;
            continue;
          }
          // 添加teacherId参数
          Object.assign(apiParams, { teacherId });
        }

        console.log('发送API请求参数:', apiParams);

        // 调用API使用密钥
        const response = await keyPackageApi.useKey(apiParams);
        console.log('API响应:', response);
        if (response.data.code === 200) {
          updatedKeyData[keyIndex] = {
            ...item,
            valid: true,
            message: '兑换成功'
          };
          successCount++;
          console.log(`密钥 ${item.key} 兑换成功`);
          notification.success(`密钥 ${item.key} 兑换成功`);
        } else {
          updatedKeyData[keyIndex] = {
            ...item,
            valid: false,
            message: '兑换失败'
          };
          failedCount++;
          console.log(`密钥 ${item.key} 兑换失败: API响应状态异常`);
          notification.error('兑换失败');
        }
      } catch (error: any) {
        console.error(`密钥 ${item.key} 兑换失败:`, error);
        const errorMessage = error.response?.data?.msg || error.response?.data?.message || '未知错误';
        console.log('API返回的错误信息:', errorMessage);
        updatedKeyData[keyIndex] = {
          ...item,
          valid: false,
          message: '兑换失败: ' + errorMessage
        };
        failedCount++;
        notification.error('兑换失败');
      }
    }

    // 更新状态
    setKeyData(updatedKeyData);
    setImportResults({
      success: successCount,
      failed: failedCount,
      total
    });
    setImportStep('complete');

    if (successCount > 0) {
      notification.success(`成功兑换 ${successCount} 个密钥`);
      onSuccess();
    }
  };

  // 重置状态
  const handleReset = () => {
    setFileList([]);
    setKeyData([]);
    setImportStep('upload');
    setImportResults({ success: 0, failed: 0, total: 0 });
  };

  // 处理关闭
  const handleClose = () => {
    handleReset();
    onClose();
  };

  // 添加处理手动输入模式的函数
  const handleManualMode = () => {
    if (selectedStudentIds.length === 0) {
      notification.warning('请先选择需要兑换密钥的学生');
      return;
    }

    // 为每个选定的学生创建一个空的密钥记录
    const manualKeyData: KeyData[] = [];

    selectedStudentIds.forEach(studentId => {
      manualKeyData.push({
        key: '',
        valid: false,
        message: '请手动输入密钥',
        studentId,
        selected: false
      });
    });

    setKeyData(manualKeyData);
    setImportStep('preview');
    notification.info(`已为 ${selectedStudentIds.length} 名学生准备手动输入界面`);
  };

  // 渲染上传阶段
  const renderUploadStep = () => {
    return (
      <div className="flex flex-col">
        {importing && (
          <div className="mb-4">
            <Alert
              message={
                <div className="flex items-center gap-2">
                  <span className="flex h-4 w-4">
                    <span className="animate-ping absolute h-4 w-4 rounded-full bg-blue-400 opacity-75"></span>
                    <span className="relative rounded-full h-3 w-3 bg-blue-500"></span>
                  </span>
                  <span className="font-medium">正在验证密钥...</span>
                </div>
              }
              description={
                <div className="mt-3">
                  <div className="grid grid-cols-2 gap-x-8 gap-y-2 mb-3 sm:grid-cols-2 xs:grid-cols-1">
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600">总计:</span>
                      <span className="font-medium">{validationProgress.total} 条密钥</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600">进度:</span>
                      <span className="font-medium">{validationProgress.current} / {validationProgress.total}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600">有效:</span>
                      <span className="text-green-600 font-medium">{validationProgress.validCount} 条</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600">无效:</span>
                      <span className="text-red-500 font-medium">{validationProgress.invalidCount} 条</span>
                    </div>
                    {validationProgress.skippedCount > 0 && (
                      <div className="flex items-center justify-between col-span-2">
                        <span className="text-gray-600">已跳过验证:</span>
                        <span className="text-amber-500 font-medium">{validationProgress.skippedCount} 条</span>
                      </div>
                    )}
                  </div>
                  <div className="mt-2">
                    <Progress
                      percent={validationProgress.total ? Math.round((validationProgress.current / validationProgress.total) * 100) : 0}
                      status="active"
                      strokeColor={{ from: '#108ee9', to: '#87d068' }}
                    />
                  </div>
                </div>
              }
              type="info"
              showIcon={false}
              className="border border-blue-100 bg-blue-50/50"
            />
          </div>
        )}

        <div className="relative mb-2">
          <div className="mb-3 text-right">
            <div className="inline-block px-3 py-1.5 rounded-full bg-blue-50 text-blue-700 text-sm font-medium shadow-sm">
              已选择 {selectedStudentIds.length} 名学生
            </div>
          </div>

          <Dragger
            name="file"
            fileList={fileList}
            onChange={handleUpload}
            customRequest={({ file, onSuccess }) => {
              setTimeout(() => {
                onSuccess && onSuccess({});
              }, 0);
            }}
            onRemove={() => {
              setFileList([]);
              return true;
            }}
            beforeUpload={(file) => {
              const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                file.type === 'application/vnd.ms-excel';
              if (!isExcel) {
                notification.error('只能上传Excel文件!');
                return Upload.LIST_IGNORE;
              }

              handleParseFile(file);
              return false;
            }}
            disabled={importing}
            className="bg-gradient-to-b from-blue-50/50 to-white"
            style={{ height: 'auto' }}
          >
            <div className="py-6 px-4 md:p-6 sm:p-4 xs:p-3">
              <p className="ant-upload-drag-icon">
                <KeyOutlined className="text-blue-500 text-4xl md:text-5xl sm:text-4xl xs:text-3xl" />
              </p>
              <p className="ant-upload-text font-medium text-gray-700 text-base md:text-lg sm:text-base xs:text-sm">
                请上传密钥
              </p>
              <p className="ant-upload-hint text-gray-500 text-sm md:text-base sm:text-sm xs:text-xs">
                支持单个Excel文件上传(.xlsx或.xls)，系统将读取Excel文件中的密钥
              </p>
            </div>
          </Dragger>

          {/* 装饰元素 */}
          <div className="absolute -top-2 -right-2 w-20 h-20 pointer-events-none md:block hidden">
            <div className="absolute top-0 right-0 w-4 h-4 bg-blue-100 rounded-full opacity-40"></div>
            <div className="absolute top-1 right-7 w-3 h-3 bg-purple-100 rounded-full opacity-60"></div>
            <div className="absolute top-6 right-2 w-2 h-2 bg-green-100 rounded-full opacity-80"></div>
          </div>
        </div>

        {/* 在下方添加手动输入按钮 */}
        <div className="mt-6 border-t border-gray-100 pt-6">
          <Button
            type="default"
            block
            icon={<EditOutlined />}
            onClick={handleManualMode}
            className="h-12 bg-white hover:bg-blue-50 border border-blue-200 hover:border-blue-300 text-blue-600 hover:text-blue-700 font-medium rounded-xl shadow-sm hover:shadow transition-all duration-300"
            disabled={importing}
          >
            <div className="flex items-center justify-center">
              <span className="mr-1 font-medium">手动输入兑换码</span>
              <span className="text-xs text-gray-500 ml-1">跳过上传，直接手动录入</span>
            </div>
          </Button>
        </div>
      </div>
    );
  };

  // 渲染预览阶段
  const renderPreviewStep = () => {
    const pendingColumns = [
      {
        title: <Checkbox
          onChange={(e) => handleToggleAllSelection(e.target.checked)}
          checked={keyData.filter(k => k.valid && k.studentId).every(k => k.selected)}
          indeterminate={
            keyData.filter(k => k.valid && k.studentId).some(k => k.selected) &&
            !keyData.filter(k => k.valid && k.studentId).every(k => k.selected)
          }
          className="flex items-center whitespace-nowrap"
        >
          <span className="inline-block whitespace-nowrap">全选</span>
        </Checkbox>,
        dataIndex: 'selected',
        key: 'selected',
        width: 80,
        render: (selected: boolean, record: KeyData) => (
          <div className="flex items-center justify-center">
            <Checkbox
              checked={!!record.selected}
              disabled={!record.valid}
              onChange={() => {
                if (record.valid) {
                  console.log('checkbox 点击:', record.key, record.selected);
                  handleToggleStudentSelection(record.key);
                }
              }}
            />
          </div>
        ),
      },
      {
        title: '学生',
        dataIndex: 'studentId',
        key: 'student',
        width: 180,
        render: (studentId: number | undefined) => {
          if (!studentId) return null;
          const studentInfo = studentNameMap.get(studentId);
          return (
            <div className="flex items-center gap-2">
              <Avatar
                size={24}
                src={studentInfo?.avatar}
                icon={!studentInfo?.avatar && <UserOutlined />}
              />
              <span className="whitespace-nowrap truncate" title={studentInfo?.name}>
                {studentInfo?.name || studentId}
              </span>
            </div>
          );
        },
      },
      {
        title: '密钥',
        dataIndex: 'key',
        key: 'key',
        className: 'key-column',
        render: (key: string, record: KeyData) => {
          // 如果是无效密钥或需要手动输入的情况，显示输入/选择界面
          if ((record.valid === false || record.key === '' || record.valid === undefined) && record.studentId) {
            // 获取所有有效且未被当前行或其他需要输入的行占用的密钥
            const availableValidKeys = keyData.filter(k =>
              k.valid &&
              (!k.studentId || k.studentId === record.studentId)
            );

            // 获取所有有效且已被分配给其他行的密钥
            const assignedValidKeys = keyData.filter(k =>
              k.valid && k.studentId && k.studentId !== record.studentId
            );

            // 组合所有可选的密钥选项
            const keyOptions = [
              {
                label: '可用有效密钥',
                options: availableValidKeys.map(k => ({
                  label: `${k.key}${!k.studentId ? ' (来自列表)' : ''}`,
                  value: k.key
                }))
              }
            ];

            // 如果有已分配的密钥也加入选项
            if (assignedValidKeys.length > 0) {
              keyOptions.push({
                label: '已分配密钥 (选择将替换)',
                options: assignedValidKeys.map(k => ({
                  label: `${k.key} (当前分配给学生 ${k.studentId})`,
                  value: k.key
                }))
              });
            }

            return (
              <div className="flex flex-col gap-1">
                <div className="flex items-center gap-1">
                  <Input
                    className="flex-grow"
                    placeholder="手动输入密钥"
                    value={record.key}
                    onChange={(e) => handleManualKeyInput(record.studentId!, e.target.value)}
                    status={record.key && record.valid === undefined ? "warning" : ""}
                    size="middle"
                  />
                  <Button
                    type="primary"
                    onClick={() => handleValidateManualKey(record.studentId!)}
                    className="flex-shrink-0 flex items-center justify-center px-2 rounded-md bg-gradient-to-r from-blue-500 to-blue-600"
                    disabled={!record.key || record.key.trim() === ''} // 按钮的禁用状态仍然依赖于输入框内容
                    size="small" // 改为 small
                  >
                    验证
                  </Button>
                  <Dropdown
                    menu={{
                      items: keyOptions.map(group => ({
                        key: group.label,
                        label: group.label,
                        type: 'group',
                        children: group.options.map(opt => ({
                          key: opt.value,
                          label: opt.label,
                          onClick: () => handleManualKeyInput(record.studentId!, opt.value)
                        }))
                      }))
                    }}
                    trigger={['click']}
                  >
                    <Button size="small" className="flex items-center">
                      选择 <DownOutlined style={{ fontSize: '10px', marginLeft: '4px' }} />
                    </Button>
                  </Dropdown>
                </div>
              </div>
            );
          }
          return <div className="text-ellipsis" title={key}>{key}</div>;
        }
      },
      {
        title: '状态',
        dataIndex: 'valid',
        key: 'valid',
        render: (valid: boolean, record: KeyData) => {
          const isManualInputState = (record.valid === false || record.key === '' || record.valid === undefined) && record.studentId;
          const statusColor = valid ? '#52c41a' : '#ff4d4f';
          const statusIcon = valid ?
            <CheckCircleOutlined style={{ color: statusColor }} /> :
            <Tooltip
              title={record.key && record.key.trim() !== '' ? '请点击"验证"按钮' : '请输入或选择密钥'}
              placement="top"
            >
              <CloseCircleOutlined style={{ color: statusColor, cursor: 'help' }} />
            </Tooltip>;
          const statusText = valid ? '有效' : record.message || '无效';

          return (
            <Space className="flex flex-nowrap">
              {statusIcon}
              <span
                className="text-ellipsis"
                title={statusText}
                style={{ color: statusColor }}
              >
                {statusText}
              </span>
            </Space>
          );
        }
      },
    ];

    const invalidColumns = [
      {
        title: '密钥',
        dataIndex: 'key',
        key: 'key',
        className: 'key-column',
        render: (key: string) => (
          <div className="text-ellipsis" title={key}>{key}</div>
        )
      },
      {
        title: '失效原因',
        dataIndex: 'message',
        key: 'message',
        render: (message: string) => (
          <div className="text-ellipsis text-red-500" title={message || '未知原因'}>
            {message || '未知原因'}
          </div>
        ),
      }
    ];

    const validKeys = keyData.filter(item => item.valid === true);
    const invalidKeys = keyData.filter(item =>
      item.valid === false &&
      item.key !== ''
    );
    const unvalidatedKeys = keyData.filter(item => item.valid === undefined);
    const assignedValidKeys = keyData.filter(item => item.valid && item.studentId);
    const assignedAndSelectedKeys = keyData.filter(item => item.valid && item.studentId && item.selected);

    // 只展示已分配的键值对（学生+密钥）
    const assignedKeysData = keyData.filter(k => k.studentId);

    // 统计无效原因
    const invalidReasons: Record<string, number> = {};
    invalidKeys.forEach(item => {
      const reason = item.message || '未知原因';
      invalidReasons[reason] = (invalidReasons[reason] || 0) + 1;
    });

    // 为未验证密钥定义列
    const unvalidatedColumns = [
      {
        title: '密钥',
        dataIndex: 'key',
        key: 'key',
        className: 'key-column',
        render: (key: string) => (
          <div className="text-ellipsis" title={key}>{key}</div>
        )
      },
      {
        title: '状态信息',
        dataIndex: 'message',
        key: 'message',
        render: (message: string) => (
          <div className="text-ellipsis text-amber-500" title={message || '未验证'}>
            {message || '未验证'}
          </div>
        ),
      }
    ];

    return (
      <>
        <Alert
          message={
            <div className="flex items-center gap-2">
              <GiftOutlined className="text-blue-500" />
              <span className="font-medium">密钥分配预览</span>
            </div>
          }
          description={
            <div className="mt-2">
              <div className="flex flex-wrap gap-2 md:gap-3 mb-2">
                <div className="px-2 md:px-3 py-1 md:py-1.5 bg-blue-50 text-blue-600 rounded-full text-xs md:text-sm font-medium shadow-sm whitespace-nowrap">
                  总共: {keyData.length} 条密钥
                </div>
                <div className="px-2 md:px-3 py-1 md:py-1.5 bg-green-50 text-green-600 rounded-full text-xs md:text-sm font-medium shadow-sm whitespace-nowrap">
                  有效: {validKeys.length} 条
                </div>
                <div className="px-2 md:px-3 py-1 md:py-1.5 bg-red-50 text-red-500 rounded-full text-xs md:text-sm font-medium shadow-sm whitespace-nowrap">
                  无效: {invalidKeys.length} 条
                </div>
                {keyData.filter(k => k.valid === undefined).length > 0 && (
                  <div className="px-2 md:px-3 py-1 md:py-1.5 bg-amber-50 text-amber-600 rounded-full text-xs md:text-sm font-medium shadow-sm whitespace-nowrap">
                    已跳过验证: {keyData.filter(k => k.valid === undefined).length} 条
                  </div>
                )}
              </div>
              <p className="text-sm md:text-base">成功为 <span className="text-green-600 font-medium">{assignedValidKeys.length}</span>/{selectedStudentIds.length} 名学生分配有效密钥。</p>
              <p className="text-sm md:text-base">当前已选择 <span className="text-blue-600 font-medium">{assignedAndSelectedKeys.length}</span> 个有效密钥进行兑换。</p>

              <div className="mt-3 p-3 bg-yellow-50 rounded-lg border border-yellow-100">
                <Checkbox
                  checked={isSpecialExchange}
                  onChange={(e) => setIsSpecialExchange(e.target.checked)}
                  className="text-sm font-medium"
                >
                  <span className="text-amber-700">以特殊权限进行兑换</span>
                </Checkbox>
                <div className="mt-2 text-xs text-amber-600">
                  {isSpecialExchange ? (
                    teacherId ? (
                      <div className="flex items-center">
                        <WarningOutlined className="mr-1" />
                        <span>特殊兑换模式：您将有权限为这些学生分配能量</span>
                      </div>
                    ) : (
                      <div className="flex items-center">
                        <CloseCircleOutlined className="mr-1 text-red-500" />
                        <span className="text-red-500">错误：无法验证您为这些学生的教师，无法使用特殊兑换模式</span>
                      </div>
                    )
                  ) : (
                    <div className="flex items-center">
                      <InfoOutlined className="mr-1" />
                      <span>普通兑换模式：学生自行使用能量，您将无权限为这些学生分配能量</span>
                    </div>
                  )}
                </div>
              </div>

              {Object.keys(invalidReasons).length > 0 && (
                <div className="mt-3 p-2 md:p-3 rounded-lg bg-gray-50">
                  <p className="font-medium flex items-center text-gray-700 text-sm md:text-base">
                    <WarningOutlined className="text-amber-500 mr-1.5" /> 无效原因统计：
                  </p>
                  <ul className="pl-4 mt-1 list-disc text-xs md:text-sm">
                    {Object.entries(invalidReasons).map(([reason, count], index) => (
                      <li key={index} className="text-gray-600">
                        {reason}: <span className="text-red-500 font-medium">{count}</span> 条
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          }
          type="info"
          className="mb-4"
          showIcon={false}
        />

        <Tabs
          defaultActiveKey="pending"
          items={[
            {
              key: 'pending',
              label: (
                <div className="flex items-center gap-1.5">
                  <KeyOutlined />
                  <span className="whitespace-nowrap">待兑换 ({assignedKeysData.length})</span>
                </div>
              ),
              children: (
                <div className="overflow-x-auto">
                  <Table
                    dataSource={assignedKeysData}
                    columns={pendingColumns}
                    rowKey="studentId"
                    pagination={{
                      pageSize: 10,
                      size: "small",
                      responsive: true,
                      className: "text-xs md:text-sm"
                    }}
                    size="small"
                    rowClassName={(record) => !record.valid ? 'bg-gray-50/80' : 'hover:bg-blue-50/30'}
                    scroll={{ x: 'max-content' }}
                    className="min-w-full"
                  />
                </div>
              )
            },
            {
              key: 'invalid',
              label: (
                <div className="flex items-center gap-1.5">
                  <CloseCircleOutlined />
                  <span className="whitespace-nowrap">失效密钥 ({invalidKeys.length})</span>
                </div>
              ),
              children: (
                <div className="overflow-x-auto">
                  <Table
                    dataSource={invalidKeys.map((item, index) => ({ ...item, id: `invalid-${index}` }))}
                    columns={invalidColumns}
                    rowKey="id"
                    pagination={{
                      pageSize: 10,
                      size: "small",
                      responsive: true,
                      className: "text-xs md:text-sm"
                    }}
                    size="small"
                    rowClassName="hover:bg-red-50/30"
                    scroll={{ x: 'max-content' }}
                    className="min-w-full"
                  />
                </div>
              )
            },
            {
              key: 'unvalidated',
              label: (
                <div className="flex items-center gap-1.5">
                  <InfoCircleOutlined />
                  <span className="whitespace-nowrap">未验证密钥 ({unvalidatedKeys.length})</span>
                </div>
              ),
              children: (
                <div className="overflow-x-auto">
                  <Table
                    dataSource={unvalidatedKeys.map((item, index) => ({ ...item, id: `unvalidated-${index}` }))}
                    columns={unvalidatedColumns}
                    rowKey="id"
                    pagination={{
                      pageSize: 10,
                      size: "small",
                      responsive: true,
                      className: "text-xs md:text-sm"
                    }}
                    size="small"
                    rowClassName="hover:bg-amber-50/30"
                    scroll={{ x: 'max-content' }}
                    className="min-w-full"
                  />
                </div>
              )
            }
          ]}
        />
      </>
    );
  };

  // 渲染导入中阶段
  const renderImportingStep = () => {
    return (
      <div className="text-center py-6 md:py-10 px-4 md:px-6">
        <div className="mb-4 md:mb-6 flex flex-col items-center">
          <div className="relative w-12 h-12 md:w-16 md:h-16 mb-3 md:mb-4">
            <div className="absolute inset-0 rounded-full bg-blue-100 animate-ping opacity-25"></div>
            <div className="relative flex items-center justify-center w-12 h-12 md:w-16 md:h-16 rounded-full bg-gradient-to-r from-blue-400 to-purple-400">
              <GiftOutlined className="text-white text-xl md:text-2xl" />
            </div>
          </div>
          <Title level={4} className="text-gradient bg-gradient-to-r from-blue-500 to-purple-500 bg-clip-text text-transparent text-base md:text-xl">
            正在执行批量兑换操作...
          </Title>
        </div>
        <div className="space-y-2">
          <Text type="secondary" className="text-sm md:text-base">请耐心等待，这可能需要一些时间</Text>
          <div>
            <Alert
              message="正在处理中，窗口已锁定"
              description="为确保批量兑换操作的完整性，在处理完成之前，系统已锁定此窗口无法关闭。"
              type="warning"
              showIcon
              className="max-w-md mx-auto mt-4"
            />
          </div>
        </div>
      </div>
    );
  };

  // 渲染完成阶段
  const renderCompleteStep = () => {
    const { success, failed, total } = importResults;

    // 完成阶段的表格列定义
    const completeColumns = [
      {
        title: '密钥',
        dataIndex: 'key',
        key: 'key',
        className: 'key-column',
        render: (key: string) => (
          <div className="text-ellipsis" title={key}>{key}</div>
        )
      },
      {
        title: '学生ID',
        dataIndex: 'studentId',
        key: 'studentId',
        width: 100,
      },
      {
        title: '结果',
        dataIndex: 'message',
        key: 'message',
        render: (message: string, record: KeyData) => (
          <div className="flex items-center">
            {record.valid ?
              <CheckCircleOutlined style={{ color: '#52c41a' }} className="mr-1" /> :
              <CloseCircleOutlined style={{ color: '#ff4d4f' }} className="mr-1" />
            }
            <span
              className="text-ellipsis"
              title={message}
              style={{ color: record.valid ? '#52c41a' : '#ff4d4f' }}
            >
              {message}
            </span>
          </div>
        ),
      },
    ];

    return (
      <>
        <div className="text-center py-4 md:py-8">
          <div className="mb-4 md:mb-6 flex flex-col items-center">
            <div className="w-12 h-12 md:w-16 md:h-16 flex items-center justify-center rounded-full bg-gradient-to-r from-green-400 to-blue-400 mb-3 md:mb-4">
              <CheckCircleOutlined className="text-white text-xl md:text-2xl" />
            </div>
            <Title level={4} className="text-gradient bg-gradient-to-r from-green-500 to-blue-500 bg-clip-text text-transparent text-base md:text-xl">
              批量兑换完成
            </Title>
          </div>
          <div className="mt-2 md:mt-4 max-w-md mx-auto">
            <Alert
              message={
                <div className="py-1">
                  <div className="grid grid-cols-3 gap-2 md:gap-4 text-center my-2">
                    <div className="bg-blue-50 py-2 md:py-3 px-1 md:px-2 rounded-xl">
                      <div className="text-base md:text-lg font-medium text-blue-600">{total}</div>
                      <div className="text-2xs md:text-xs text-gray-500">总共密钥</div>
                    </div>
                    <div className="bg-green-50 py-2 md:py-3 px-1 md:px-2 rounded-xl">
                      <div className="text-base md:text-lg font-medium text-green-600">{success}</div>
                      <div className="text-2xs md:text-xs text-gray-500">兑换成功</div>
                    </div>
                    <div className="bg-red-50 py-2 md:py-3 px-1 md:px-2 rounded-xl">
                      <div className="text-base md:text-lg font-medium text-red-500">{failed}</div>
                      <div className="text-2xs md:text-xs text-gray-500">兑换失败</div>
                    </div>
                  </div>
                </div>
              }
              type={success > 0 ? "success" : "error"}
              showIcon={false}
              className="border-0 bg-transparent"
            />
          </div>
        </div>

        <div className="overflow-x-auto">
          <Table
            dataSource={keyData.filter(item => item.selected).map((item, index) => ({ ...item, id: index }))}
            columns={completeColumns}
            rowKey="id"
            pagination={{
              pageSize: 5,
              size: "small",
              responsive: true,
              className: "text-xs md:text-sm"
            }}
            size="small"
            scroll={{ x: 'max-content' }}
            className="min-w-full"
          />
        </div>
      </>
    );
  };

  // 添加到全局CSS中的响应式类
  const responsiveStyles = `
    @media (max-width: 640px) {
      .text-2xs {
        font-size: 0.625rem;
        line-height: 0.875rem;
      }
    }
  `;

  // 根据当前阶段渲染内容
  const renderContent = () => {
    switch (importStep) {
      case 'upload':
        return renderUploadStep();
      case 'preview':
        return renderPreviewStep();
      case 'importing':
        return renderImportingStep();
      case 'complete':
        return renderCompleteStep();
      default:
        return renderUploadStep();
    }
  };

  return (
    <Modal
      title={
        <div className="flex items-center gap-2 text-lg font-medium text-gradient bg-gradient-to-r from-blue-500 to-purple-500 bg-clip-text text-transparent">
          <KeyOutlined className="text-blue-500" />
          <span>批量兑换密钥</span>
        </div>
      }
      open={open}
      onCancel={importStep === 'importing' ? undefined : handleClose}
      closable={importStep !== 'importing'}
      maskClosable={importStep !== 'importing'}
      footer={
        <>
          {importStep === 'preview' && (
            <div className="flex flex-wrap-reverse sm:flex-nowrap justify-between gap-2 pt-4 border-t border-gray-100">
              <Button
                onClick={handleReset}
                icon={<InboxOutlined />}
                className="flex items-center order-2 sm:order-1 w-full sm:w-auto justify-center"
              >
                重新上传
              </Button>
              <Button
                type="primary"
                onClick={handleBatchUseKeys}
                disabled={keyData.filter(item => item.valid && item.studentId && item.selected).length === 0}
                icon={<GiftOutlined />}
                className="flex items-center order-1 sm:order-2 w-full sm:w-auto justify-center"
              >
                开始兑换 ({keyData.filter(item => item.valid && item.studentId && item.selected).length})
              </Button>
            </div>
          )}

          {importStep === 'complete' && (
            <div className="flex justify-end pt-4 border-t border-gray-100">
              <Button
                type="primary"
                onClick={() => {
                  // 获取成功兑换的学生ID
                  const successStudentIds = keyData
                    .filter(item => item.valid && item.studentId && item.selected && item.message === '兑换成功')
                    .map(item => item.studentId as number);

                  if (successStudentIds.length > 0 && onGoToAssignPoints) {
                    onGoToAssignPoints(successStudentIds);
                  } else {
                    handleClose();
                  }
                }}
                className="w-full sm:w-auto justify-center px-4 md:px-6"
                icon={<GiftOutlined />}
              >
                前往分配
              </Button>
            </div>
          )}
        </>
      }
      width={"85%"}
      centered
      destroyOnClose
      className="key-exchange-modal"
      styles={{
        body: {
          padding: '20px 24px 24px',
          maxHeight: 'calc(80vh - 160px)',
          overflowY: 'auto',
          overflowX: 'hidden'
        }
      }}
      style={{
        maxWidth: '900px',
        margin: '0 auto',
        top: 0
      }}
    >
      <style jsx global>{`
        .key-exchange-modal .ant-modal-content {
          border-radius: 20px;
          box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
          overflow: hidden;
          border: 1px solid rgba(235, 240, 250, 0.6);
          backdrop-filter: blur(10px);
          background-color: rgba(255, 255, 255, 0.98);
        }
        
        .key-exchange-modal .ant-modal-header {
          border-radius: 20px 20px 0 0;
          padding: 22px 24px;
          background: linear-gradient(135deg, rgba(245, 250, 255, 0.8) 0%, rgba(240, 245, 255, 0.8) 100%);
          border-bottom: 1px solid rgba(230, 235, 245, 0.5);
          box-shadow: 0 2px 10px rgba(0, 0, 0, 0.02);
        }
        
        .key-exchange-modal .ant-modal-footer {
          padding: 16px 24px;
          border-top: 1px solid rgba(230, 235, 245, 0.8);
          background: linear-gradient(180deg, rgba(250, 252, 255, 0.8) 0%, rgba(245, 248, 255, 0.8) 100%);
          border-radius: 0 0 20px 20px;
          margin-top: 0;
        }
        
        .key-exchange-modal .ant-modal-close {
          top: 20px;
          right: 20px;
        }
        
        .key-exchange-modal .ant-modal-close-x {
          width: 32px;
          height: 32px;
          line-height: 32px;
          border-radius: 16px;
          background-color: rgba(240, 240, 250, 0.6);
          color: #909090;
          transition: all 0.3s;
        }
        
        .key-exchange-modal .ant-modal-close-x:hover {
          background-color: rgba(230, 230, 250, 0.9);
          color: #666;
          transform: rotate(90deg);
        }
        
        .key-exchange-modal .ant-tabs {
          margin-bottom: 24px;
        }
        
        .key-exchange-modal .ant-tabs-nav {
          margin-bottom: 16px;
          background-color: rgba(245, 247, 255, 0.4);
          padding: 4px;
          border-radius: 12px;
        }
        
        .key-exchange-modal .ant-tabs-tab {
          transition: all 0.3s ease;
          border-radius: 10px;
          padding: 8px 16px;
          margin: 0 4px;
          background: transparent;
        }
        
        .key-exchange-modal .ant-tabs-tab:first-child {
          margin-left: 0;
        }
        
        .key-exchange-modal .ant-tabs-tab:hover {
          color: #1677ff;
          background-color: rgba(235, 240, 255, 0.7);
        }
        
        .key-exchange-modal .ant-tabs-tab-active {
          background: #fff;
          box-shadow: 0 2px 8px rgba(0, 40, 140, 0.08);
        }
        
        .key-exchange-modal .ant-tabs-tab-active .ant-tabs-tab-btn {
          color: #1677ff;
          font-weight: 500;
        }
        
        .key-exchange-modal .ant-tabs-ink-bar {
          display: none;
        }
        
        .key-exchange-modal .ant-alert {
          border-radius: 14px;
          box-shadow: 0 3px 10px rgba(0, 0, 0, 0.04);
          border: none;
          margin-bottom: 20px;
        }
        
        .key-exchange-modal .ant-alert-info {
          background: linear-gradient(120deg, rgba(240, 249, 255, 0.7), rgba(235, 246, 255, 0.7));
          border-left: 4px solid #1890ff;
        }
        
        .key-exchange-modal .ant-table {
          border-radius: 14px;
          overflow: hidden;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }
        
        .key-exchange-modal .ant-table-wrapper {
          border-radius: 14px;
          background: linear-gradient(135deg, #ffffff, #fafbff);
          padding: 2px;
        }
        
        .key-exchange-modal .ant-table-thead > tr > th {
          background: linear-gradient(to bottom, #f5f8ff 0%, #eef2ff 100%);
          color: #415989;
          font-weight: 500;
          border-bottom: 1px solid #eaedf7;
          padding: 14px 16px;
        }
        
        .key-exchange-modal .ant-table-thead > tr > th:first-child {
          border-top-left-radius: 12px;
        }
        
        .key-exchange-modal .ant-table-thead > tr > th:last-child {
          border-top-right-radius: 12px;
        }
        
        .key-exchange-modal .ant-table-tbody > tr > td {
          padding: 12px 16px;
          border-bottom: 1px solid #f0f3fa;
        }
        
        .key-exchange-modal .ant-table-tbody > tr:last-child > td {
          border-bottom: none;
        }
        
        .key-exchange-modal .ant-table-tbody > tr:hover > td {
          background-color: rgba(240, 245, 255, 0.7) !important;
        }
        
        .key-exchange-modal .ant-btn {
          border-radius: 10px;
          transition: all 0.3s ease;
          box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
          height: 40px;
          padding: 0 20px;
        }
        
        .key-exchange-modal .ant-btn-primary {
          background: linear-gradient(135deg, #4096ff 0%, #5582f7 100%);
          border: none;
          color: white;
        }
        
        .key-exchange-modal .ant-btn-primary:not(:disabled):hover {
          background: linear-gradient(135deg, #2e84f0 0%, #4470e8 100%);
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(46, 132, 240, 0.3);
        }
        
        .key-exchange-modal .ant-btn-primary:disabled {
          background: #b9c5e2;
          color: #fff;
          border: none;
        }
        
        .key-exchange-modal .ant-btn-default {
          border: 1px solid #dfe5f1;
          background: #fff;
          color: #566b8f;
        }
        
        .key-exchange-modal .ant-btn-default:hover {
          border-color: #a5b5d8;
          color: #3e5480;
          background: #f9faff;
          transform: translateY(-2px);
          box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
        }
        
        .key-exchange-modal .ant-upload-drag {
          border-radius: 14px;
          border: 2px dashed rgba(200, 210, 240, 0.8);
          background: rgba(250, 252, 255, 0.5);
          transition: all 0.3s ease;
          overflow: hidden;
          min-height: auto !important;
        }
        
        .key-exchange-modal .ant-upload {
          padding: 0 !important;
        }
        
        .key-exchange-modal .ant-upload-drag-container {
          padding: 20px 0;
        }
        
        .key-exchange-modal .ant-upload-drag:not(.ant-upload-disabled):hover {
          border-color: #4096ff;
          background: rgba(245, 250, 255, 0.8);
          transform: translateY(-2px);
          box-shadow: 0 6px 20px rgba(0, 0, 0, 0.06);
        }
        
        .key-exchange-modal .ant-checkbox-checked .ant-checkbox-inner {
          background-color: #4096ff;
          border-color: #4096ff;
        }
        
        .key-exchange-modal .ant-checkbox:hover .ant-checkbox-inner {
          border-color: #4096ff;
        }
        
        .key-exchange-modal .ant-progress-bg {
          background: linear-gradient(90deg, #52c41a 0%, #4096ff 100%);
        }
        
        .key-exchange-modal .ant-progress-text {
          color: #4096ff;
          font-weight: 500;
        }
        
        /* 响应式设计调整 */
        @media (max-width: 768px) {
          .key-exchange-modal .ant-table {
            font-size: 13px;
          }
          
          .key-exchange-modal .ant-table-thead > tr > th,
          .key-exchange-modal .ant-table-tbody > tr > td {
            padding: 10px 12px;
          }
          
          .key-exchange-modal .ant-tabs-tab {
            padding: 8px 12px;
            margin: 0 2px;
          }
          
          .key-exchange-modal .text-ellipsis {
            max-width: 120px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            display: inline-block;
          }
        }
        
        @media (max-width: 575px) {
          .key-exchange-modal .ant-modal-content {
            border-radius: 16px;
          }
          
          .key-exchange-modal .ant-modal-header {
            padding: 16px 20px;
          }
          
          .key-exchange-modal .ant-modal-body {
            padding: 16px 20px 20px;
          }
          
          .key-exchange-modal .ant-table-thead > tr > th,
          .key-exchange-modal .ant-table-tbody > tr > td {
            padding: 8px 10px;
          }
          
          .key-exchange-modal .flex.flex-wrap.gap-3 {
            gap: 0.5rem !important;
          }
          
          .key-exchange-modal .px-3.py-1.5 {
            padding: 0.25rem 0.75rem !important;
          }
          
          .key-exchange-modal .text-ellipsis {
            max-width: 80px;
          }
          
          .key-exchange-modal .ant-pagination-item {
            min-width: 28px;
            height: 28px;
            line-height: 26px;
          }
          
          .key-exchange-modal .ant-pagination-prev,
          .key-exchange-modal .ant-pagination-next,
          .key-exchange-modal .ant-pagination-jump-prev,
          .key-exchange-modal .ant-pagination-jump-next {
            min-width: 28px;
            height: 28px;
            line-height: 28px;
          }
        }
        
        /* 超小屏幕调整 */
        @media (max-width: 375px) {
          .key-exchange-modal .ant-table-thead > tr > th,
          .key-exchange-modal .ant-table-tbody > tr > td {
            padding: 6px 8px;
            font-size: 12px;
          }
          
          .key-exchange-modal .text-ellipsis {
            max-width: 60px;
          }
          
          .key-exchange-modal .text-2xs {
            font-size: 0.625rem;
            line-height: 0.875rem;
          }
        }
      `}</style>
      {renderContent()}
    </Modal>
  );
}; 