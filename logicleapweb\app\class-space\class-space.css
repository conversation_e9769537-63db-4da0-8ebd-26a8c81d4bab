/* 颜色变量 */
:root {
  --primary-color: #3b82f6;
  --primary-light: rgba(59, 130, 246, 0.1);
  --primary-hover: #2563eb;
  --success-color: #10b981;
  --success-light: rgba(16, 185, 129, 0.1);
  --warning-color: #f59e0b;
  --warning-light: rgba(245, 158, 11, 0.1);
  --error-color: #ef4444;
  --error-light: rgba(239, 68, 68, 0.1);
  --gray-light: #f3f4f6;
  --border-color: #e5e7eb;
}

/* 按钮基础样式 */
.btn-modern {
  transition: all 0.3s ease-out !important;
}

/* 返回按钮样式 */
.btn-back {
  position: relative;
  overflow: hidden;
  border: 1px solid #e5e7eb;
  color: #374151;
}

.btn-back:hover {
  border-color: #d1d5db;
}

.btn-back::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    rgba(255,255,255,0) 0%,
    rgba(255,255,255,0.2) 50%,
    rgba(255,255,255,0) 100%
  );
  transition: left 0.5s;
}

.btn-back:hover::before {
  left: 100%;
}

/* 返回按钮图标样式 */
.btn-back .anticon {
  margin-right: 0.25rem !important;
  transition: transform 0.3s !important;
}

/* 返回按钮悬浮时图标动画 */
.btn-back:hover .anticon {
  transform: translateX(-2px) !important;
}

/* 移除按钮点击波纹效果 */
.btn-back::after {
  display: none !important;
}

/* 暗色模式返回按钮样式 */
.dark .btn-back {
  background: rgba(31, 41, 55, 0.9) !important;
  color: #d1d5db !important;
  border-color: #374151 !important;
}

/* 卡片悬浮效果 */
.ant-card-hoverable {
  transition: all 0.3s ease-out !important;
}

.ant-card-hoverable:hover {
  transform: translateY(-4px) !important;
  box-shadow: 0 12px 24px -8px rgba(0, 0, 0, 0.15) !important;
}

/* 轮播图自定义样式 */
.carousel-arrow {
  width: 32px;
  height: 32px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  z-index: 1;
  transition: all 0.3s ease;
}

.carousel-arrow:hover {
  background: rgba(0, 0, 0, 0.5);
  transform: scale(1.1);
}

/* 自定义轮播点样式 */
.custom-dots {
  bottom: 12px !important;
}

.custom-dots li button {
  width: 8px !important;
  height: 8px !important;
  border-radius: 50% !important;
  background: rgba(255, 255, 255, 0.5) !important;
}

.custom-dots li.slick-active button {
  background: white !important;
  width: 24px !important;
  border-radius: 4px !important;
}

/* 内容区域的滚动条样式 */
.overflow-y-auto {
  scrollbar-width: thin;
  scrollbar-color: #CBD5E0 #F1F5F9;
}

.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #F1F5F9;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background-color: #CBD5E0;
  border-radius: 3px;
}

/* Tab样式优化 */
.ant-tabs-nav {
  background: white;
  padding: 4px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.ant-tabs-tab {
  margin: 0 4px !important;
  padding: 8px 16px !important;
  transition: all 0.3s ease;
}

.ant-tabs-tab.ant-tabs-tab-active {
  background: #f0f7ff;
  border-radius: 6px;
}

.ant-tabs-tab:hover {
  background: #f9fafb;
  border-radius: 6px;
}

/* 标题渐变效果 */
.gradient-text {
  background: linear-gradient(135deg, #4f46e5 0%, #3b82f6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 卡片基础样式 */
.card-base {
  @apply bg-white rounded-xl shadow-sm border border-gray-100 transition-all duration-300;
}

.card-base:hover {
  @apply shadow-lg;
  border-color: rgba(99, 102, 241, 0.1);
  background: linear-gradient(to bottom right, white, rgba(248, 250, 252, 0.8));
}

/* 学校卡片样式 */
.school-card {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid #e5e7eb;
  cursor: pointer;
}

.school-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 24px -8px rgba(0, 0, 0, 0.12);
  border-color: #93c5fd;
}

/* 项目网格容器样式 */
.projects-grid {
  display: grid;
  grid-template-columns: repeat(3, minmax(0, 1fr));
  gap: 16px;
  padding: 16px;
  height: 100%;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: #cbd5e0 #f1f5f9;
}

.projects-grid::-webkit-scrollbar {
  width: 6px;
}

.projects-grid::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.projects-grid::-webkit-scrollbar-thumb {
  background-color: #cbd5e0;
  border-radius: 3px;
}

/* 项目卡片基础样式 */
.project-card {
  height: fit-content;
  display: flex;
  flex-direction: column;
  background: white;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  transition: all 0.3s ease;
  margin: 0;
}

.project-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px -4px rgba(0, 0, 0, 0.08);
  border-color: var(--primary-color);
  z-index: 10;
}

.project-card .ant-card-cover {
  @apply h-48 overflow-hidden;
}

.project-card .ant-card-cover img,
.project-card .ant-card-cover > div:first-child {
  @apply rounded-t-2xl;
}

.project-card .ant-card-cover img {
  @apply w-full h-full object-cover transition-transform duration-500;
}

.project-card:hover .ant-card-cover img {
  transform: scale(1.05);
}

.project-card .ant-card-body {
  @apply p-3;
}

.project-card .ant-card-meta-title {
  @apply text-base font-medium mb-2 text-gray-800;
}

.project-card .ant-card-meta-description {
  @apply text-sm text-gray-500;
}

/* 查看作品按钮样式 */
.project-card .view-btn {
  border-radius: 9999px;
  @apply text-sm px-6 h-8 flex items-center justify-center;
  background: var(--primary-color) !important;
  border: none !important;
  transition: all 0.3s ease !important;
  color: white !important;
  font-weight: 500;
  min-width: 88px;
}

.project-card .view-btn:hover {
  background: var(--primary-hover) !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2) !important;
}

.project-card .view-btn:active {
  transform: translateY(0);
}

.project-card .ant-avatar {
  @apply border-2 border-white shadow-sm;
}

/* 任务卡片样式 */
.task-card {
  background: white;
  border-radius: 10px;
  border: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.task-card:hover {
  transform: translateY(-1px);
  border-color: rgba(59, 130, 246, 0.3);
  background: linear-gradient(to bottom right, white, rgba(248, 250, 252, 0.8));
}

.task-card .task-title {
  @apply text-base font-medium text-gray-800 mb-1;
  line-height: 1.3;
}

.task-card .task-description {
  @apply text-sm text-gray-500 mb-1.5 line-clamp-2;
  line-height: 1.4;
}

.task-card .task-meta {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 8px;
}

.task-card .task-tags {
  @apply flex flex-wrap items-center gap-1;
}

/* 标签样式优化 */
.ant-tag {
  height: 24px;
  padding: 0 10px;
  font-size: 12px;
  font-weight: 500;
  line-height: 22px;
  display: inline-flex;
  align-items: center;
  white-space: nowrap;
}

.task-card .ant-tag + .ant-tag {
  margin-left: 0;
}

/* 统一标签颜色 */
.ant-tag-blue {
  background: var(--primary-light) !important;
  color: var(--primary-color) !important;
}

.ant-tag-green {
  background: var(--success-light) !important;
  color: var(--success-color) !important;
}

.ant-tag-orange {
  background: var(--warning-light) !important;
  color: var(--warning-color) !important;
}

.ant-tag-red {
  background: var(--error-light) !important;
  color: var(--error-color) !important;
}

.ant-tag-purple,
.ant-tag-cyan {
  background: var(--primary-light) !important;
  color: var(--primary-color) !important;
}

/* 主内容区域布局 */
.main-content {
  display: flex;
  gap: 1rem;
  width: 100%;
}

.main-content > .col-span-8,
.main-content > .col-span-4 {
  @apply h-full;
  overflow: hidden; /* 防止内容溢出 */
}

/* 内容区域样式 */
.content-area {
  @apply bg-white rounded-xl shadow-sm h-full;
  border: 1px solid rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  height: 100%; /* 改为100%以适应父容器高度 */
  min-height: 0; /* 移除最小高度限制 */
  position: relative;
}

.content-area-header {
  @apply flex justify-between items-center px-4 flex-shrink-0;
  height: 48px;
  background: linear-gradient(to right, rgba(249, 250, 251, 0.8), rgba(248, 250, 252, 0.4));
  border-bottom: 1px solid rgba(0, 0, 0, 0.03);
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
  position: relative;
  z-index: 2;
}

.content-area-title {
  @apply font-medium text-gray-800;
  font-size: 15px;
  position: relative;
  padding-left: 10px;
}

.content-area-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 2px;
  height: 14px;
  background: var(--primary-color);
  border-radius: 2px;
}

.content-area-body {
  @apply flex-1;
  overflow-y: auto;
  height: 100%;
  position: relative;
}

.content-area-body .grid {
  padding: 0 16px;  /* 只为班级项目的网格添加左右内边距 */
}

/* 任务列表容器样式 */
.tasks-container {
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  height: 100%;
  overflow-y: auto;
}

/* 自定义滚动条 */
.tasks-container {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e0 #f1f5f9;
}

.tasks-container::-webkit-scrollbar {
  width: 4px;
}

.tasks-container::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 2px;
}

.tasks-container::-webkit-scrollbar-thumb {
  background-color: #cbd5e0;
  border-radius: 2px;
}

.tasks-container::-webkit-scrollbar-thumb:hover {
  background-color: #a0aec0;
}

/* 评语区域样式 */
.feedback-section {
  @apply mt-2 rounded-md text-xs;
  background: var(--primary-light);
  border-left: 2px solid var(--primary-color);
  padding: 8px 10px;
  position: relative;
  overflow: hidden;
  margin-bottom: 0;
}

.feedback-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(to right,
    rgba(59, 130, 246, 0.1),
    rgba(59, 130, 246, 0.05)
  );
}

.feedback-section p {
  @apply m-0 text-gray-600;
  line-height: 1.4;
}

/* 分页样式 */
.ant-pagination {
  @apply flex justify-center;
  padding: 4px 0;
  margin: 0;
}

.ant-pagination-item {
  border-radius: 6px;
  @apply border-gray-200 transition-all duration-300;
  min-width: 28px !important;
  height: 28px !important;
  line-height: 26px !important;
  margin: 0 2px !important;
}

.ant-pagination-item:hover {
  @apply border-blue-500 text-blue-500;
  transform: translateY(-1px);
}

.ant-pagination-item-active {
  @apply border-blue-500 bg-blue-500 text-white shadow-sm;
  border-color: var(--primary-color) !important;
  background: var(--primary-color) !important;
}

.ant-pagination-prev .ant-pagination-item-link,
.ant-pagination-next .ant-pagination-item-link {
  border-radius: 6px;
  @apply border-gray-200 flex items-center justify-center;
  min-width: 28px !important;
  height: 28px !important;
  margin: 0 2px !important;
}

/* Empty 状态样式 */
.ant-empty {
  @apply py-8;
}

.ant-empty-description {
  @apply text-gray-500;
}

/* 按钮样式 */
.ant-btn {
  @apply font-medium;
  height: 32px;
  transition: all 0.3s ease;
}

.ant-btn.rounded-full {
  border-radius: 9999px;
  @apply px-6;
  min-width: 88px;
}

.ant-btn-primary {
  background: var(--primary-color) !important;
  border: none !important;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.ant-btn-primary:hover {
  background: var(--primary-hover) !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
}

.ant-btn-primary:active {
  transform: translateY(0);
}

/* 头部背景样式 */
.header-bg {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  position: relative;
}

.header-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url('/images/pattern.svg');
  opacity: 0.1;
}

/* 滚动条样式 */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e0 #f1f5f9;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: #cbd5e0;
  border-radius: 3px;
}

/* 模态框样式 */
.ant-modal-content {
  @apply rounded-xl overflow-hidden;
  border: 1px solid #e5e7eb;
}

.ant-modal-header {
  @apply border-b border-gray-100 mb-0;
  padding: 16px 24px;
}

.ant-modal-body {
  @apply p-6;
}

.ant-modal-footer {
  @apply border-t border-gray-100;
  padding: 16px 24px;
}

/* 加载状态样式 */
.ant-spin {
  @apply text-blue-500;
}

.ant-spin-dot-item {
  background-color: currentColor !important;
}

/* 表单样式 */
.ant-form-item-label > label {
  @apply text-gray-700 font-medium;
}

.ant-input {
  @apply rounded-lg border-gray-200;
  height: 36px;
}

.ant-input:hover {
  @apply border-blue-400;
}

.ant-input:focus {
  @apply border-blue-500 ring-1 ring-blue-500;
}

/* Tabs样式 */
.ant-tabs-nav {
  @apply mb-6;
}

.ant-tabs-tab {
  @apply px-4 py-2 text-gray-600;
}

.ant-tabs-tab-active {
  color: var(--primary-color) !important;
}

.ant-tabs-ink-bar {
  background: var(--primary-color) !important;
}

/* 头部布局样式 */
.header-layout {
  position: relative;
  padding: 16px 24px;
  display: flex;
  gap: 16px;
}

.header-avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.header-avatar {
  width: 55px;
  height: 55px;
  border-radius: 12px;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(8px);
}

.header-back-btn {
  padding: 6px 18px;
  font-size: 13px;
  height: auto;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(0, 0, 0, 0.05);
  color: #374151;
  display: flex;
  align-items: center;
  gap: 4px;
  transition: all 0.2s ease;
}

.header-back-btn:hover {
  background: white;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.header-title-section {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.header-title {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.header-subtitle {
  font-size: 14px;
  color: #6b7280;
}

/* 任务筛选栏样式 */
.task-filter-bar,
.filter-item {
  display: none;
}

/* 排序选择器样式 */
.ant-select-small {
  font-size: 13px;
}

.ant-select-small .ant-select-selector {
  border-radius: 6px !important;
  height: 28px !important;
  padding: 0 8px !important;
  border-color: rgba(0, 0, 0, 0.08) !important;
}

.ant-select-small .ant-select-selection-item {
  line-height: 26px !important;
  padding-right: 18px !important;
}

/* 下拉菜单选项样式 */
.ant-select-dropdown {
  padding: 4px !important;
  border-radius: 8px !important;
  border: 1px solid rgba(0, 0, 0, 0.08) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08) !important;
}

.ant-select-item {
  border-radius: 4px !important;
  min-height: 32px !important;
  padding: 6px 8px !important;
}

.ant-select-item-option-selected {
  background-color: rgba(59, 130, 246, 0.08) !important;
  color: #2563eb !important;
  font-weight: 500 !important;
}

.ant-select-item-option-active {
  background-color: rgba(0, 0, 0, 0.02) !important;
}

/* 自定义选择器样式 */
.custom-select {
  background: white;
}

.custom-select .ant-select-selector {
  height: 32px !important;
  border-radius: 8px !important;
  border: 1px solid rgba(0, 0, 0, 0.08) !important;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.02) !important;
  padding: 0 12px !important;
}

.custom-select .ant-select-selection-item {
  line-height: 30px !important;
  font-size: 13px !important;
  color: #374151 !important;
}

.custom-select .ant-select-arrow {
  color: #9CA3AF !important;
  font-size: 12px !important;
  right: 12px !important;
}

/* 下拉菜单样式 */
.custom-select-dropdown {
  padding: 6px !important;
  background: white !important;
  border-radius: 10px !important;
  border: 1px solid rgba(0, 0, 0, 0.08) !important;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08) !important;
}

.custom-select-dropdown .ant-select-item {
  padding: 8px 12px !important;
  border-radius: 6px !important;
  margin-bottom: 2px !important;
}

.custom-select-dropdown .ant-select-item:last-child {
  margin-bottom: 0 !important;
}

.custom-select-dropdown .ant-select-item-option-content {
  font-size: 13px !important;
}

.custom-select-dropdown .ant-select-item-option-selected {
  background: rgba(59, 130, 246, 0.08) !important;
  color: #2563eb !important;
  font-weight: 500 !important;
}

.custom-select-dropdown .ant-select-item-option-active {
  background: rgba(0, 0, 0, 0.02) !important;
}

/* 移除之前的样式 */
.task-filter-bar,
.filter-item,
.task-tabs {
  display: none;
}

/* 排序选择器样式 */
.ant-select-small {
  font-size: 13px;
}

.ant-select-small .ant-select-selector {
  border-radius: 6px !important;
  height: 28px !important;
}

.ant-select-small .ant-select-selection-item {
  line-height: 26px !important;
}

/* 筛选器样式 */
.custom-cascader {
  width: 110px !important;
}

.custom-cascader .ant-select-selector {
  height: 28px !important;
  border-radius: 4px !important;
  border: 1px solid #e5e7eb !important;
  box-shadow: none !important;
  padding: 0 8px !important;
}

.custom-cascader .ant-select-selection-placeholder {
  color: #4b5563 !important;
  font-size: 13px !important;
  line-height: 26px !important;
}

.custom-cascader .ant-select-arrow {
  color: #9ca3af !important;
  right: 8px !important;
}

/* 下拉菜单样式 */
.custom-cascader-dropdown {
  padding: 4px !important;
  background: white !important;
  border-radius: 4px !important;
  border: 1px solid #e5e7eb !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
}

.custom-cascader-dropdown .ant-cascader-menu {
  height: auto !important;
  min-height: 100px;
  max-height: 250px;
  border-right: 1px solid #f3f4f6;
  padding: 2px;
  min-width: 110px !important;
}

.custom-cascader-dropdown .ant-cascader-menu:last-child {
  border-right: none;
}

.custom-cascader-dropdown .ant-cascader-menu-item {
  margin: 1px 0 !important;
  padding: 5px 8px !important;
  border-radius: 2px !important;
  color: #4b5563 !important;
  font-size: 13px !important;
  min-height: 28px !important;
  line-height: 18px !important;
}

.custom-cascader-dropdown .ant-cascader-menu-item:hover {
  background: #f3f4f6 !important;
}

.custom-cascader-dropdown .ant-cascader-menu-item-active {
  color: #1890ff !important;
  background: #e6f4ff !important;
  font-weight: normal !important;
}

/* 移除多余的样式 */
.task-filter-bar,
.filter-item,
.task-tabs {
  display: none;
} 