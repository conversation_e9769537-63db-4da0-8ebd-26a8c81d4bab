import { Logger as TypeOrmLogger, QueryRunner } from 'typeorm';
import { Logger } from '@nestjs/common';
import { QueryInterceptor } from './interceptor/query-interceptor';

export class CustomTypeOrmLogger implements TypeOrmLogger {
  private readonly logger = new Logger('TypeORM');
  private queryInterceptor?: QueryInterceptor;

  constructor(queryInterceptor?: QueryInterceptor) {
    this.queryInterceptor = queryInterceptor;
  }

  /**
   * 记录查询日志
   */
  logQuery(query: string, parameters?: any[], queryRunner?: QueryRunner) {
    // console.log("进入查询拦截器",query);

    let queryId: string | undefined;

    if (this.queryInterceptor) {
      // 获取调用上下文
      const context = this.getQueryContext();
      queryId = this.queryInterceptor.beforeQuery(query, parameters, context);
    }

    // 存储queryId到queryRunner中，以便在查询结束时使用
    if (queryRunner && queryId) {
      (queryRunner as any).__queryId = queryId;
      // 存储开始时间，用于计算执行时间
      (queryRunner as any).__queryStartTime = Date.now();
    }

    // // 记录查询开始日志（仅在调试模式下）
    // this.logger.debug(`执行查询: ${this.formatQuery(query)}`, {
    //   parameters: this.formatParameters(parameters),
    //   queryId
    // });
  }

  /**
   * 记录查询错误
   */
  logQueryError(error: string | Error, query: string, parameters?: any[], queryRunner?: QueryRunner) {
    const errorObj = typeof error === 'string' ? new Error(error) : error;
    
    if (this.queryInterceptor && queryRunner) {
      const queryId = (queryRunner as any).__queryId;
      if (queryId) {
        this.queryInterceptor.afterSlowQuery(queryId, errorObj);
      }
    }

    this.logger.error(`查询执行错误: ${errorObj.message}`, {
      query: this.formatQuery(query),
      parameters: this.formatParameters(parameters),
      error: errorObj.message,
      stack: errorObj.stack
    });
  }

  /**
   * 记录查询慢日志
   */
  logQuerySlow(time: number, query: string, parameters?: any[], queryRunner?: QueryRunner) {
    console.log(`🐌 logQuerySlow 被调用了！执行时间: ${time}ms`);

    if (this.queryInterceptor && queryRunner) {
      const queryId = (queryRunner as any).__queryId;
      if (queryId) {
        console.log(`🎯 调用 afterSlowQuery，queryId: ${queryId}`);
        this.queryInterceptor.afterSlowQuery(queryId);
      } else {
        console.log(`❌ queryId 不存在，无法调用 afterSlowQuery`);
      }
    } else {
      console.log(`❌ queryInterceptor 或 queryRunner 不存在`);
    }

    this.logger.warn(`慢查询检测 [${time}ms]: ${this.formatQuery(query)}`, {
      executionTime: time,
      parameters: this.formatParameters(parameters)
    });
  }

  /**
   * 记录模式构建日志
   */
  logSchemaBuild(message: string) {
    this.logger.log(`Schema: ${message}`);
  }

  /**
   * 记录迁移日志
   */
  logMigration(message: string) {
    this.logger.log(`Migration: ${message}`);
  }

  /**
   * 记录查询成功完成（TypeORM 没有提供这个钩子，需要手动调用）
   */
  logQuerySuccess(query: string, parameters?: any[], queryRunner?: QueryRunner) {
    if (this.queryInterceptor && queryRunner) {
      const queryId = (queryRunner as any).__queryId;
      const startTime = (queryRunner as any).__queryStartTime;

      if (queryId && startTime) {
        // 手动调用 afterSlowQuery，模拟查询完成
        this.queryInterceptor.afterSlowQuery(queryId);

        // 清理临时数据
        delete (queryRunner as any).__queryId;
        delete (queryRunner as any).__queryStartTime;
      }
    }
  }

  /**
   * 记录一般日志
   */
  log(level: 'log' | 'info' | 'warn', message: any, queryRunner?: QueryRunner) {
    switch (level) {
      case 'log':
        this.logger.log(message);
        break;
      case 'info':
        this.logger.log(message);
        break;
      case 'warn':
        this.logger.warn(message);
        break;
    }
  }

  /**
   * 格式化查询字符串
   */
  private formatQuery(query: string): string {
    if (!query) return '';
    
    // 移除多余的空白字符
    const formatted = query.replace(/\s+/g, ' ').trim();
    
    // 限制长度
    const maxLength = 200;
    if (formatted.length > maxLength) {
      return formatted.substring(0, maxLength) + '...';
    }
    
    return formatted;
  }

  /**
   * 格式化参数
   */
  private formatParameters(parameters?: any[]): any[] {
    if (!parameters || parameters.length === 0) return [];
    
    return parameters.map(param => {
      if (typeof param === 'string' && param.length > 50) {
        return param.substring(0, 50) + '...';
      }
      if (typeof param === 'object' && param !== null) {
        try {
          const str = JSON.stringify(param);
          if (str.length > 100) {
            return str.substring(0, 100) + '...';
          }
          return param;
        } catch {
          return '[Object]';
        }
      }
      return param;
    });
  }

  /**
   * 获取查询上下文（调用栈信息）
   */
  private getQueryContext(): string {
    const stack = new Error().stack;
    if (!stack) return '';
    
    const lines = stack.split('\n');
    // 查找第一个不是TypeORM内部的调用
    for (let i = 1; i < lines.length; i++) {
      const line = lines[i];
      if (line && 
          !line.includes('typeorm') && 
          !line.includes('CustomTypeOrmLogger') &&
          !line.includes('QueryInterceptor')) {
        // 提取文件名和行号
        const match = line.match(/at\s+(.+)\s+\((.+):(\d+):(\d+)\)/);
        if (match) {
          const [, functionName, filePath, lineNumber] = match;
          const fileName = filePath.split('/').pop() || filePath.split('\\').pop();
          return `${functionName} (${fileName}:${lineNumber})`;
        }
      }
    }
    
    return 'Unknown';
  }
}
