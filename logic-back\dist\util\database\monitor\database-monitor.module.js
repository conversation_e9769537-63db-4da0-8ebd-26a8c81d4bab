"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseMonitorModule = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const query_interceptor_1 = require("./interceptor/query-interceptor");
const database_test_controller_1 = require("./controller/database-test.controller");
const database_monitor_config_1 = require("./config/database-monitor.config");
const database_monitor_controller_1 = require("./controller/database-monitor.controller");
const query_monitor_service_1 = require("./service/query-monitor.service");
let DatabaseMonitorModule = class DatabaseMonitorModule {
};
exports.DatabaseMonitorModule = DatabaseMonitorModule;
exports.DatabaseMonitorModule = DatabaseMonitorModule = __decorate([
    (0, common_1.Global)(),
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule.forRoot({
                load: [() => database_monitor_config_1.databaseMonitorConfig],
            }),
        ],
        providers: [
            query_monitor_service_1.QueryMonitorService,
            query_interceptor_1.QueryInterceptor,
        ],
        controllers: [
            database_monitor_controller_1.DatabaseMonitorController,
            database_test_controller_1.DatabaseTestController,
        ],
        exports: [
            query_monitor_service_1.QueryMonitorService,
            query_interceptor_1.QueryInterceptor,
        ],
    })
], DatabaseMonitorModule);
//# sourceMappingURL=database-monitor.module.js.map