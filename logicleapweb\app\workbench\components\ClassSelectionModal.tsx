'use client';

import React, { useState, useEffect } from 'react';
import { X, Search, Users, Calendar, ArrowLeft } from 'lucide-react';
import { schoolApi } from '@/lib/api/school';
import { useSelector } from 'react-redux';
import { RootState } from '@/lib/store';
import './SchoolSelectionModal.css';

interface ClassSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onBack: () => void;
  actionType: string;
  selectedSchool: any;
  onClassSelect: (classData: any) => void;
}

const ClassSelectionModal: React.FC<ClassSelectionModalProps> = ({
  isOpen,
  onClose,
  onBack,
  actionType,
  selectedSchool,
  onClassSelect
}) => {
  const [selectedClass, setSelectedClass] = useState<any>(null);
  const [classes, setClasses] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [mounted, setMounted] = useState(false);
  const userId = useSelector((state: RootState) => state.user.userState.userId);

  // 防止水合错误
  useEffect(() => {
    setMounted(true);
  }, []);

  // 获取学校的班级列表
  const fetchSchoolClasses = async (schoolId: number) => {
    try {
      setLoading(true);
      setError(null);

      const response = await schoolApi.getSchoolClasses({
        schoolId,
        page: 1,
        size: 100
      });

      if (response.data.code === 200) {
        const classesData = response.data.data?.list || [];
        setClasses(classesData);
      } else {
        setError(response.data.message || '获取班级列表失败');
      }
    } catch (err) {
      console.error('获取班级列表失败:', err);
      setError('获取班级列表失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 当弹窗打开且有选中学校时获取班级数据
  useEffect(() => {
    if (isOpen && selectedSchool?.id && mounted) {
      fetchSchoolClasses(selectedSchool.id);
    }
  }, [isOpen, selectedSchool, mounted]);

  const handleClassSelect = (classItem: any) => {
    setSelectedClass(classItem);
    // 直接进入模板选择
    onClassSelect(classItem);
  };



  // 防止水合错误，在客户端挂载前不渲染
  if (!mounted || !isOpen) return null;

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="modal-wrapper">
        <button className="modal-close-btn-outside" onClick={onClose}>
          <X size={20} />
        </button>
        <div className="modal-content" onClick={(e) => e.stopPropagation()}>

        <div className="step-indicator">
          <div className="step active">
            <div className="step-number">1</div>
            <div className="step-label">选择班级</div>
          </div>
          {actionType === '发布任务' && (
            <div className="step">
              <div className="step-number">2</div>
              <div className="step-label">发布任务</div>
            </div>
          )}
          {(actionType === '分配积木' || actionType === '分配能量') && (
            <div className="step">
              <div className="step-number">2</div>
              <div className="step-label">能量和模板</div>
            </div>
          )}
          {(actionType === '快速上课' || actionType === '一键上课') && (
            <>
              <div className="step">
                <div className="step-number">2</div>
                <div className="step-label">能量和模板</div>
              </div>
              <div className="step">
                <div className="step-number">3</div>
                <div className="step-label">发布任务</div>
              </div>
            </>
          )}
        </div>

        <div className="modal-content-body">
          <h3 className="section-title">选择上课的班级</h3>

          {loading && (
            <div className="loading-container">
              <div className="loading-spinner"></div>
              <p>正在加载班级列表...</p>
            </div>
          )}

          {error && (
            <div className="error-container">
              <p className="error-message">{error}</p>
              <button className="retry-btn" onClick={() => selectedSchool?.id && fetchSchoolClasses(selectedSchool.id)}>
                重试
              </button>
            </div>
          )}

          {!loading && !error && classes.length === 0 && (
            <div className="empty-container">
              <p>该学校暂无班级</p>
              <p className="empty-hint">请联系管理员创建班级</p>
            </div>
          )}

          {!loading && !error && classes.length > 0 && (
            <div className="schools-grid">
              {classes.map((classItem) => (
                <div
                  key={classItem.id}
                  className={`school-card ${selectedClass?.id === classItem.id ? 'selected' : ''}`}
                  onClick={() => handleClassSelect(classItem)}
                >
                  <div className="school-card-name">{classItem.className}</div>
                  <div className="school-card-location">
                    <Users size={14} />
                    {classItem.studentCount || 0} 名学生
                  </div>
                </div>
              ))}
            </div>
          )}

          <div className="modal-footer">
            <button className="back-btn only" onClick={onBack}>
              上一步
            </button>
          </div>
        </div>
      </div>
      </div>
    </div>
  );
};

export default ClassSelectionModal;
