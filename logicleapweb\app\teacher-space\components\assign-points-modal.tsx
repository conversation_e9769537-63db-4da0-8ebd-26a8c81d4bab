'use client'

import { useState, useEffect, useMemo } from 'react';
import { Modal, Form, Input, InputNumber, Button, Alert, Avatar, Tooltip } from 'antd';
import { InfoCircleOutlined, CheckOutlined, UserOutlined, ThunderboltOutlined, KeyOutlined, ClockCircleOutlined, WarningOutlined } from '@ant-design/icons';
import { Student } from '../types/index';
import { pointsApi } from '@/lib/api/points';
import { packageApi } from '@/lib/api/package';
import dayjs from 'dayjs';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
dayjs.extend(isSameOrBefore);
import { useWindowSize } from '@/hooks/useWindowSize';
import { GetNotification } from 'logic-common/dist/components/Notification';


interface AssignPointsModalProps {
  visible: boolean;
  onCancel: () => void;
  onOk: (params: { availablePoints: number; studentExpiries?: { [id: number]: string | undefined } }) => Promise<void>;
  studentName: string;
  studentId: number;
  userId: number;
  student: Student | null;
  isBatch?: boolean;
  selectedStudents: number[];
  students: Student[];
  onSuccess?: () => void;
  refreshStudentList: () => Promise<void>;
  onGoToRedeemKey?: (studentIds: number[]) => void;
}

export const AssignPointsModal = ({
  visible,
  onCancel,
  onOk,
  studentName,
  studentId,
  userId,
  student,
  isBatch,
  selectedStudents,
  students,
  onSuccess,
  refreshStudentList,
  onGoToRedeemKey
}: AssignPointsModalProps) => {
  const { width: windowWidth } = useWindowSize();
  const [availablePoints, setAvailablePoints] = useState<number | null>(null);
  const [studentPoints, setStudentPoints] = useState<number>(0);
  const [isLoading, setIsLoading] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [studentPointsMap, setStudentPointsMap] = useState<Map<number, number>>(new Map());
  const notification = GetNotification();

  useEffect(() => {
    if (visible) {
      if (isBatch && selectedStudents.length > 0) {
        const fetchStudentPoints = async () => {
          try {
            const response = await pointsApi.getBatchStudentPoints(selectedStudents);
            const pointsMap = new Map();
            console.log("批量积分响应", response);

            if (response.data.code === 200) {
              const data = response.data.data;
              for (const userId in data) {
                const totalPoints = data[userId]?.totalPoints || 0;
                const availablePoints = data[userId]?.availablePoints || 0;
                const remainingPoints = totalPoints - availablePoints;
                pointsMap.set(Number(userId), remainingPoints);
              }
            } else {
              selectedStudents.forEach(sid => pointsMap.set(sid, 0));
              notification.error(response.data.message || '批量获取学生能量失败');
            }

            setStudentPointsMap(pointsMap);
          } catch (error) {
            const pointsMap = new Map();
            selectedStudents.forEach(sid => pointsMap.set(sid, 0));
            setStudentPointsMap(pointsMap);
            notification.error('批量获取学生能量失败');
          }
        };
        fetchStudentPoints();
      } else if (!isBatch && userId) {
        console.log('userId', userId);
        pointsApi.getBatchStudentPoints([userId]).then(response => {
          console.log('response', response);
          if (response.data.code === 200) {
            const userData = response.data.data[userId];
            if (userData) {
              const totalPoints = userData.totalPoints || 0;
              const availablePoints = userData.availablePoints || 0;
              const remainingPoints = totalPoints - availablePoints;
              setStudentPoints(remainingPoints);
            } else {
              setStudentPoints(0);
            }
          } else {
            setStudentPoints(0);
            notification.error('获取学生可用能量失败');
          }
        }).catch(() => {
          setStudentPoints(0);
          notification.error('获取学生可用能量失败');
        });
      }
      setAvailablePoints(null);
      setShowSuccess(false);
    }
  }, [visible, userId, isBatch, selectedStudents, students]);

  const maxAssignablePoints = useMemo(() => {
    if (isBatch) {
      if (studentPointsMap.size === 0) return 0;
      return Math.min(...Array.from(studentPointsMap.values()));
    } else {
      return studentPoints;
    }
  }, [isBatch, studentPoints, studentPointsMap]);

  const isInputExceedingLimit = useMemo(() => {
    return availablePoints !== null && availablePoints > maxAssignablePoints;
  }, [availablePoints, maxAssignablePoints]);

  const handleOk = async () => {
    if (availablePoints === null || availablePoints < 0) {
      notification.error('请输入有效的分配能量数量');
      return;
    }

    if (!isBatch && availablePoints > maxAssignablePoints) {
      notification.error('分配能量不能超过学生剩余的能量');
      return;
    }
    if (isBatch && availablePoints > maxAssignablePoints) {
      notification.error(`分配能量不能超过所选学生中的最低剩余能量 (${maxAssignablePoints})`);
      return;
    }

    setIsLoading(true);
    try {
      let studentExpiries: { [studentId: number]: string | undefined } = {};
      const defaultExpireTime = dayjs().endOf('month').hour(23).minute(59).second(59).toISOString();
      const targetStudentIds = isBatch ? selectedStudents : [userId];

      for (const sid of targetStudentIds) {
        try {
          const packageResponse = await packageApi.getUserCurrentPackage(sid);
          if (packageResponse.data.code === 1000 && packageResponse.data.data?.expireTime) {
            studentExpiries[sid] = packageResponse.data.data.expireTime;
          } else {
            studentExpiries[sid] = defaultExpireTime;
          }
        } catch (error) {
          console.error(`获取学生 ${sid} 套餐失败:`, error);
          studentExpiries[sid] = defaultExpireTime;
        }
      }

      await onOk({
        availablePoints: availablePoints,
        studentExpiries: studentExpiries
      });

      setShowSuccess(true);
      onSuccess?.();

      setTimeout(() => {
        setShowSuccess(false);
        setAvailablePoints(null);
        onCancel();
      }, 1500);

    } catch (error: any) {
      console.error('分配能量失败:', error);
      notification.error(error.response?.data?.message || '分配能量失败');
    } finally {
      setIsLoading(false);
    }
  };

  const modalWidth = useMemo(() => {
    if (windowWidth <= 768) return '95%';
    return 600;
  }, [windowWidth]);

  return (
    <Modal
      title={
        <div className="flex items-center justify-between text-lg font-medium">
          <div className="flex items-center gap-2 text-gradient bg-gradient-to-r from-yellow-500 to-orange-500 bg-clip-text text-transparent">
            <ThunderboltOutlined className="text-yellow-500" />
            <span>{isBatch ? '批量分配能量' : '分配能量'}</span>
          </div>
          <div className="px-3 py-1.5 rounded-full bg-blue-50 text-blue-700 text-sm font-medium shadow-sm">
            {isBatch
              ? `已选择 ${selectedStudents.length} 名学生`
              : student
                ? <div className="flex items-center gap-2">
                  <Avatar size={20} src={student.avatarUrl} icon={<UserOutlined />} />
                  <span>{student.nickName}</span>
                </div>
                : ''
            }
          </div>
        </div>
      }
      open={visible}
      onCancel={() => {
        setAvailablePoints(null);
        onCancel();
      }}
      footer={showSuccess ? null : (
        <div className="flex justify-between items-center gap-2 pt-4 border-t border-gray-100">
          <div className="relative">
            {isInputExceedingLimit && (
              <div className="absolute inset-0 rounded-xl shadow-[0_0_15px_5px_rgba(239,68,68,0.75)] animate-pulse z-0"></div>
            )}
            <Button
              key="redeemKey"
              icon={<KeyOutlined />}
              onClick={() => {
                const targetStudentIds = isBatch ? selectedStudents : student ? [student.userId] : [];
                if (onGoToRedeemKey && targetStudentIds.length > 0) {
                  onGoToRedeemKey(targetStudentIds);
                }
              }}
              className={`relative z-10 h-10 px-5 rounded-xl transition-all duration-300 transform ${isInputExceedingLimit
                ? '!bg-red-500 !text-white !border-red-500 hover:!bg-red-600 hover:!border-red-600'
                : 'text-purple-600 border-purple-200 hover:border-purple-400 hover:text-purple-700'
                }`}
            >
              前往兑换密钥
            </Button>
          </div>
          <div className="flex gap-2">
            <Button
              key="cancel"
              onClick={() => {
                setAvailablePoints(null);
                onCancel();
              }}
              className="h-10 px-5 rounded-xl"
            >
              取消
            </Button>
            <Button
              key="submit"
              type="primary"
              loading={isLoading}
              onClick={handleOk}
              className="h-10 px-5 rounded-xl bg-gradient-to-r from-yellow-500 to-orange-500 border-none text-white hover:from-yellow-600 hover:to-orange-600 disabled:from-gray-300 disabled:to-gray-400"
              disabled={availablePoints === null || availablePoints < 0 || availablePoints > maxAssignablePoints}
            >
              确认分配
            </Button>
          </div>
        </div>
      )}
      width={modalWidth}
      centered
      destroyOnClose
      className="points-modal key-exchange-modal"
      styles={{
        body: {
          padding: '20px 24px 24px',
          maxHeight: 'calc(70vh - 120px)',
          overflowY: 'auto',
          overflowX: 'hidden'
        }
      }}
      style={{
        maxWidth: '900px',
        margin: '0 auto',
        top: 0
      }}
    >
      {showSuccess ? (
        <div className="text-center py-8 md:py-12">
          <div className="mb-4 md:mb-6 flex flex-col items-center">
            <div className="w-12 h-12 md:w-16 md:h-16 flex items-center justify-center rounded-full bg-gradient-to-r from-green-400 to-blue-400 mb-3 md:mb-4">
              <CheckOutlined className="text-white text-xl md:text-2xl" />
            </div>
            <div className="text-lg md:text-xl font-medium text-gradient bg-gradient-to-r from-green-500 to-blue-500 bg-clip-text text-transparent">
              能量分配成功！
            </div>
          </div>
        </div>
      ) : (
        <div className="space-y-5 pt-2 pb-5 px-1">
          {isBatch && (
            <div className="space-y-2">
              <div className="flex justify-between items-center mb-1">
                <div className="text-sm font-medium text-gray-600">学生能量详情：</div>
                {studentPointsMap.size > 0 && (
                  <div className="text-xs text-gray-500 text-right">
                    最低剩余能量: <span className="font-medium text-red-500">{maxAssignablePoints}</span>
                  </div>
                )}
              </div>
              <div className="max-h-[200px] overflow-y-auto pr-2 custom-scrollbar border border-gray-100 rounded-xl p-3 bg-gray-50/50">
                {selectedStudents.map(sid => {
                  const s = students.find(stud => stud.userId === sid);
                  const studentAvailablePoints = studentPointsMap.get(sid);
                  const isInsufficient = studentAvailablePoints !== undefined && availablePoints !== null && studentAvailablePoints < availablePoints;
                  return s ? (
                    <div key={sid} className={`flex justify-between items-center py-1.5 px-2 rounded-md transition-colors ${isInsufficient ? 'bg-red-50/50' : 'hover:bg-blue-50'}`}>
                      <div className="flex items-center gap-2">
                        <Avatar size="small" src={s.avatarUrl} icon={<UserOutlined />} />
                        <span className="text-sm text-gray-700 truncate max-w-[150px]" title={s.nickName}>{s.nickName}</span>
                      </div>
                      <span className={`text-sm font-medium ${studentAvailablePoints === undefined ? 'text-gray-400' : isInsufficient ? 'text-red-500' : 'text-blue-600'}`}>
                        {studentAvailablePoints === undefined ? '加载中...' : `${studentAvailablePoints}`}
                        {isInsufficient && (
                          <Tooltip title="剩余能量不足本次分配">
                            <WarningOutlined className="ml-1.5 text-red-400" />
                          </Tooltip>
                        )}
                      </span>
                    </div>
                  ) : null;
                })}
              </div>
            </div>
          )}

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1.5">分配能量数量</label>
            <div className="relative">
              <InputNumber
                value={availablePoints}
                onChange={(value) => {
                  // 只允许正整数，不允许0和负数
                  if (value === null || value === undefined) {
                    setAvailablePoints(null);
                  } else if (Number(value) > 0) {
                    setAvailablePoints(Number(value));
                  }
                }}
                placeholder="请输入分配数量"
                className={`w-full h-11 rounded-xl text-base focus:border-blue-500 focus:ring-1 focus:ring-blue-500 ${isInputExceedingLimit ? 'text-red-500 border-red-300 focus:border-red-500 focus:ring-red-500' : ''}`}
                min={1}
                controls={false}
                formatter={(value) => value === null ? '' : `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                parser={(value) => {
                  if (!value) return null;
                  const parsed = Number(value.replace(/\D/g, ''));
                  return parsed > 0 ? parsed : null;
                }}
                precision={0}
              />
              <div className="absolute right-3 top-1/2 -translate-y-1/2 text-sm text-gray-400">
                / {maxAssignablePoints} <span className="text-xs">剩余</span>
              </div>
            </div>
            {isInputExceedingLimit && (
              <div className="text-xs text-red-500 mt-1 flex items-center gap-1"><WarningOutlined />可分配积分不足，请前往兑换密钥兑换套餐</div>
            )}
          </div>

          <Alert
            type="info"
            showIcon={false}
            message={
              <div className="flex items-center gap-2 font-medium text-blue-700">
                <InfoCircleOutlined />
                <span>温馨提示</span>
              </div>
            }
            description={
              <div className="mt-2 space-y-1.5 text-xs text-blue-600">
                <div className="flex items-start gap-1.5">
                  <span className="mt-0.5 text-blue-500"><CheckOutlined /></span>
                  <span>请留意学生的可用能量是否足够本次分配。</span>
                </div>
                <div className="flex items-start gap-1.5">
                  <span className="mt-0.5 text-blue-500"><KeyOutlined /></span>
                  <span>分配的能量将从学生的套餐中扣除。</span>
                </div>
                <div className="flex items-start gap-1.5">
                  <span className="mt-0.5 text-blue-500"><ClockCircleOutlined /></span>
                  <span>能量的过期时间与学生当前套餐的过期时间保持一致。</span>
                </div>
              </div>
            }
            className="bg-blue-50/70 border-blue-100 text-blue-700 rounded-xl p-4"
          />
        </div>
      )}
    </Modal>
  );
}; 