'use client'

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useSelector } from 'react-redux';
import { RootState } from '@/lib/store';
import { message, Spin } from 'antd';
import RechargeCards from './components/recharge-cards';
import PlanDetailModal from './components/plan-detail-modal';
import RechargeHistory from './components/recharge-history';
import SuccessModal from './components/success-modal';
import { RechargeOption } from './types';
import { useRecharge } from './hooks/useRecharge';
import { getAvailablePackages } from '../../lib/api/package-purchase';
import { transformPackagesToRechargeOptions } from './utils/dataTransform';
import './styles/recharge.css';

export default function RechargePage() {
  const router = useRouter();
  const user = useSelector((state: RootState) => state.user);
  const { selectedPlan, selectPlan, reset } = useRecharge();

  const [showPlanDetail, setShowPlanDetail] = useState(false);
  const [showHistory, setShowHistory] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [successOrderInfo, setSuccessOrderInfo] = useState<any>(null);

  // 新增状态：套餐数据和加载状态
  const [packages, setPackages] = useState<RechargeOption[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 加载套餐数据
  useEffect(() => {
    const loadPackages = async () => {
      try {
        setLoading(true);
        setError(null);
        const apiPackages = await getAvailablePackages();
        const transformedPackages = transformPackagesToRechargeOptions(apiPackages);

        if (transformedPackages.length === 0) {
          setError('暂无可用套餐');
          setPackages([]);
        } else {
          setPackages(transformedPackages);
        }
      } catch (error) {
        console.error('加载套餐失败:', error);
        setError('加载套餐失败，请稍后重试');
        setPackages([]);
      } finally {
        setLoading(false);
      }
    };

    loadPackages();
  }, []);

  const handleCardClick = (plan: RechargeOption) => {
    selectPlan(plan);
    setShowPlanDetail(true);
  };

  const handlePaymentSuccess = (orderInfo?: any) => {
    setShowPlanDetail(false);

    // 保存订单信息并显示成功弹窗
    setSuccessOrderInfo(orderInfo);
    setShowSuccess(true);

    // 重置选择的计划
    reset();
  };

  const handleBack = () => {
    router.back();
  };

  const handleShowHistory = () => {
    setShowHistory(true);
  };

  return (
    <div className="recharge-page">
      {/* 头部 */}
      <header className="recharge-header">
        <div className="container">
          <div className="header-content">
            <div className="header-left">
              <button className="back-btn" onClick={handleBack}>
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                  <path d="M19 12H5M12 19l-7-7 7-7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </button>
              <div className="header-info">
                <div className="header-icon">
                  ⚡
                </div>
                <div>
                  <h1 className="header-title">套餐充值</h1>
                </div>
              </div>
            </div>
            <button className="history-btn" onClick={handleShowHistory}>
              充值记录
            </button>
          </div>
        </div>
      </header>

      {/* 主要内容 */}
      <main className="recharge-main">
        {/* 充值卡片区域 */}
        <section className="cards-section">
          <div className="container">
            <div className="section-header">
              <h2 className="section-title">选择您的充值套餐</h2>
              <p className="section-subtitle">不同需求，精准匹配</p>
            </div>

            {loading ? (
              <div className="loading-container">
                <Spin size="large" tip="加载套餐中..." />
              </div>
            ) : error ? (
              <div className="error-container">
                <div className="error-content">
                  <p className="error-message">{error}</p>
                  <button
                    className="retry-button"
                    onClick={() => window.location.reload()}
                  >
                    重新加载
                  </button>
                </div>
              </div>
            ) : packages.length === 0 ? (
              <div className="empty-container">
                <p className="empty-message">暂无可用套餐</p>
              </div>
            ) : (
              <RechargeCards packages={packages} onCardClick={handleCardClick} />
            )}
          </div>
        </section>
      </main>

      {/* 模态框 */}
      <PlanDetailModal
        plan={selectedPlan}
        visible={showPlanDetail}
        onClose={() => setShowPlanDetail(false)}
        onSuccess={handlePaymentSuccess}
      />

      <RechargeHistory
        visible={showHistory}
        onClose={() => setShowHistory(false)}
      />

      <SuccessModal
        visible={showSuccess}
        onClose={() => {
          setShowSuccess(false);
          setSuccessOrderInfo(null);
        }}
        plan={selectedPlan}
        orderInfo={successOrderInfo}
      />
    </div>
  );
}
