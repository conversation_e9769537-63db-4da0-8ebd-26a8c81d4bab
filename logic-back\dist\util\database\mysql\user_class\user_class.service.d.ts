import { Repository } from 'typeorm';
import { CreateUserClassDto } from './dto/create-user_class.dto';
import { UpdateUserClassDto } from './dto/update-user_class.dto';
import { UserClass } from './entities/user_class.entity';
export declare class UserClassService {
    private readonly userClassRepository;
    constructor(userClassRepository: Repository<UserClass>);
    create(createUserClassDto: CreateUserClassDto): Promise<UserClass>;
    findAll(): Promise<UserClass[]>;
    findOne(id: number): Promise<UserClass | null>;
    findByIds(ids: number[]): Promise<UserClass[]>;
    findBySchoolId(schoolId: number): Promise<UserClass[]>;
    findByGrade(schoolId: number, grade: string): Promise<UserClass[]>;
    findByTeacherId(teacherId: number): Promise<UserClass[]>;
    findByAssistantTeacherId(assistantTeacherId: number): Promise<UserClass[]>;
    findByInviteCode(inviteCode: string): Promise<UserClass | null>;
    update(id: number, updateUserClassDto: UpdateUserClassDto): Promise<import("typeorm").UpdateResult>;
    regenerateInviteCode(id: number): Promise<{
        inviteCode: string;
    }>;
    remove(id: number): Promise<import("typeorm").DeleteResult>;
    private generateInviteCode;
    getTeacherClasses(classData: any): Promise<UserClass[]>;
}
