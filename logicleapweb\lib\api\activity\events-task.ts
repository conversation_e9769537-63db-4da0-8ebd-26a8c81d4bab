import request from '../../request'

export const eventsTaskApi = {
  baseUrl: '/api/v1/web/events-task',
  
  // 创建赛事任务
  create: (data: {
    userId: number
    eventName: string
    startTime: string
    endTime: string
    instructorName?: string
    schoolName?: string
    contactPerson?: string
    contactPhone?: string
    realName?: string
    idNumber?: string
    affiliatedSchool?: string
    organization?: string
    instructorPhone?: string
    competitionGroup?: string
    registrationFormFile?: string
    workId?: number
    workFile?: string
    workDescription?: string
    creatorId?: number
    remark?: string
  }) => {
    return request.post(eventsTaskApi.baseUrl, data)
  },

  // 获取我的任务列表
  getMyTasks: () => {
    return request.get(eventsTaskApi.baseUrl + '/my-tasks')
  },

  // 获取任务详情
  getDetail: (taskId: number) => {
    return request.get(`${eventsTaskApi.baseUrl}/${taskId}`)
  },

  // 更新任务信息
  update: (taskId: number, data: {
    eventName?: string
    startTime?: string
    endTime?: string
    instructorName?: string
    schoolName?: string
    contactPerson?: string
    contactPhone?: string
    realName?: string
    idNumber?: string
    affiliatedSchool?: string
    organization?: string
    instructorPhone?: string
    competitionGroup?: string
    registrationFormFile?: string
    workId?: number
    workFile?: string
    workDescription?: string
    remark?: string
  }) => {
    return request.patch(`${eventsTaskApi.baseUrl}/${taskId}`, data)
  },

  // 更新任务状态
  updateStatus: (taskId: number, status: number) => {
    return request.patch(`${eventsTaskApi.baseUrl}/${taskId}/status`, { status })
  },

  // 删除任务
  delete: (taskId: number) => {
    return request.delete(`${eventsTaskApi.baseUrl}/${taskId}`)
  },

  // 获取我的任务统计信息
  getMyStatistics: () => {
    return request.get(eventsTaskApi.baseUrl + '/my-tasks/statistics')
  },

  // 获取即将开始的任务
  getUpcomingTasks: () => {
    return request.get(eventsTaskApi.baseUrl + '/my-tasks/upcoming')
  },

  // 获取进行中的任务
  getOngoingTasks: () => {
    return request.get(eventsTaskApi.baseUrl + '/my-tasks/ongoing')
  },

  // 根据状态获取任务
  getTasksByStatus: (status: number) => {
    return request.get(`${eventsTaskApi.baseUrl}/my-tasks/status/${status}`)
  },

  // 搜索任务
  searchTasks: (keyword: string) => {
    return request.get(eventsTaskApi.baseUrl + '/my-tasks/search', { 
      params: { keyword } 
    })
  },

  // 批量更新任务状态
  batchUpdateStatus: (data: {
    taskIds: number[]
    status: number
  }) => {
    return request.post(eventsTaskApi.baseUrl + '/batch/status', data)
  },

  // 提交赛事任务（支持重新提交）
  submit: (taskId: number, data: {
    workId?: number
    workFile?: string
    workDescription?: string
    instructorName?: string
    schoolName?: string
    contactPerson?: string
    contactPhone?: string
    realName?: string
    idNumber?: string
    affiliatedSchool?: string
    organization?: string
    instructorPhone?: string
    competitionGroup?: string
    registrationFormFile?: string
    remark?: string
    isResubmit?: boolean
  }) => {
    return request.post(`${eventsTaskApi.baseUrl}/${taskId}/submit`, data)
  },

  // 检查任务是否可以提交
  checkSubmitStatus: (taskId: number) => {
    return request.get(`${eventsTaskApi.baseUrl}/${taskId}/submit-status`)
  },

  // 获取活动的赛事任务列表
  getActivityTasks: (activityId: number) => {
    return request.get(`${eventsTaskApi.baseUrl}/activity/${activityId}/tasks`)
  },

  // 管理员审核任务状态
  adminReviewTask: (taskId: number, status: number, remark?: string) => {
    return request.patch(`${eventsTaskApi.baseUrl}/${taskId}/admin-review`, { status, remark })
  }
}
