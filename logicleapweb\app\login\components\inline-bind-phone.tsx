'use client'

import { useState } from 'react';
import { Button, Form, Input, message, Modal } from 'antd';
import userApi from '@/lib/api/user';
import { COLORS } from './colors';
import NotificationUtil from '@/lib/utils/notification';
import InlinePhoneConflict from './inline-phone-conflict';
import axios from 'axios';
import request from '@/lib/request';

interface InlineBindPhoneProps {
  onSuccess: (phone: string) => void;
  onSkip?: () => void;
  allowSkip?: boolean;
  registerType?: string;
  openid?: string;
  scene_str?: string;
}

interface UserInfo {
  id: number;
  nickName: string;
  avatarUrl?: string;
  role?: {
    id: number;
    name: string;
  };
  schoolInfo?: {
    province?: string;
    city?: string;
    district?: string;
    schoolName?: string;
  };
  student?: {
    school?: {
      province?: string;
      city?: string;
      district?: string;
      schoolName?: string;
    }
  };
  schoolName?: string;
  registerType?: string;
}

const InlineBindPhone: React.FC<InlineBindPhoneProps> = ({
  onSuccess,
  onSkip,
  allowSkip = true,
  registerType = '',
  openid = '',
  scene_str = ''
}) => {
  const [loading, setLoading] = useState(false);
  const [phone, setPhone] = useState('');
  const [verifyCode, setVerifyCode] = useState('');
  const [countdown, setCountdown] = useState(0);
  const [form] = Form.useForm();

  // 添加手机号冲突相关状态
  const [showConflictModal, setShowConflictModal] = useState(false);
  const [bindUserList, setBindUserList] = useState<UserInfo[]>([]);

  // 发送验证码
  const handleSendCode = async () => {
    try {
      // 先验证手机号
      await form.validateFields(['phone']);
      const phoneValue = form.getFieldValue('phone');

      setLoading(true);
      const response = await userApi.sendVerifyCode(phoneValue);

      if (response.data && response.data.code === 200) {
        message.success('验证码已发送');
        setCountdown(60);

        // 启动倒计时
        const timer = setInterval(() => {
          setCountdown(prev => {
            if (prev <= 1) {
              clearInterval(timer);
              return 0;
            }
            return prev - 1;
          });
        }, 1000);
      } else {
        message.error(response.data?.message || '发送验证码失败');
      }
    } catch (error) {
      console.error('发送验证码失败:', error);
      message.error('请输入有效的手机号');
    } finally {
      setLoading(false);
    }
  };

  // 绑定手机号
  const handleSubmit = async (values: any) => {
    if (!values.phone || !values.verifyCode) {
      NotificationUtil.error('请填写完整信息');
      return;
    }

    try {
      // 1.用户点击了确认绑定，则先校验验证码
      setLoading(true);
      const verifyResponse = await userApi.verifyResponse({
        phone: values.phone,
        code: values.verifyCode
      });
      // 使用类型断言解决类型错误
      const verifyApiResponse = verifyResponse as any;
      if (verifyApiResponse.data && verifyApiResponse.data.code !== 200) {
        NotificationUtil.error(verifyApiResponse.data.message || '验证码错误或已过期，请检查或重新获取');
        setLoading(false);
        return;
      }

      // 2.验证码校验跳过，检查手机号是否已被绑定，先根据手机号查询用户信息
      // 如果存在且，手机号旗下的账号大于0 则弹出冲突弹框
      const bindCheckResponse = await userApi.findAllBindByPhone(values.phone);
      console.log("zww检测手机号绑定响应", bindCheckResponse);

      // 使用类型断言解决类型错误
      const bindCheckApiResponse = bindCheckResponse as any;

      if (bindCheckApiResponse.data && bindCheckApiResponse.data.code === 200) {
        const bindData = bindCheckApiResponse.data.data;
        // 3.如果有绑定记录,显示冲突弹框
        if (bindData && bindData.length > 0) {
          console.log("zww:绑定的手机号旗下已有账号了,即将弹出冲突弹框，!账号个数为:", bindData.length);
          setBindUserList(bindData);
          setShowConflictModal(true);
          setLoading(false);
          return;
        }
      }
      console.log("zww：绑定的手机号旗下无账号，可直接绑定！");

      // 3.如果没有绑定记录，调用绑定接口
      const bindResponse = await userApi.bindPhone({
        phone: values.phone,
        code: values.verifyCode
      });

      console.log("zww:调用绑定手机号接口的响应值", bindResponse);

      // 使用类型断言解决类型错误
      const bindApiResponse = bindResponse as any;

      // 4.判断绑定手机号接口请求结果
      if (bindApiResponse.data && bindApiResponse.data.code === 200) {
        NotificationUtil.success('手机号绑定成功');
        //
        onSuccess(values.phone);
      } else {
        NotificationUtil.error(bindApiResponse.data?.message || '绑定失败，请重试');
      }
    } catch (error: any) {
      console.error('绑定手机号失败:', error);
      NotificationUtil.error(error.response?.data?.message || '绑定失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 处理继续绑定
  const handleContinueBind = async () => {
    try {
      setLoading(true);
      // 1.继续绑定流程，获取手机号和验证码
      const phoneValue = form.getFieldValue('phone');
      const codeValue = form.getFieldValue('verifyCode');


      // 2.调用绑定接口传递要绑定的手机号，服务端根据token获取调用人的id，设置手机号
      const response = await userApi.bindPhone({
        phone: phoneValue,
        code: codeValue
      });

      // 使用类型断言解决类型错误
      const apiResponse = response as any;

      // 3.成功响应，弹出成功弹框后关闭冲突弹框
      if (apiResponse.data && apiResponse.data.code === 200) {
        NotificationUtil.success('手机号绑定成功');
        setShowConflictModal(false);
        //4. 成功回调返回手机号隔开iLoginForm组件的成功回调，更新本地存储和redux
        onSuccess(phoneValue);
      } else {
        NotificationUtil.error(apiResponse.data?.message || '绑定失败，请重试');
      }
    } catch (error: any) {
      console.error('绑定手机号失败:', error);
      NotificationUtil.error(error.response?.data?.message || '绑定失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 处理替换账号
  // 替换账号，传递被替换人的userID和手机号，流程大概为被替换的人phone清空，当前user设置phone
  const handleReplaceAccount = async (selectedUser: any) => {
    console.log("zww：进入替换账号流程");

    try {
      // 1.接收inline-phone-conflict传入的选中用户检测，增强健壮性
      if (!selectedUser) {
        NotificationUtil.warning('未找到选中的账号信息');
        return;
      }
      // 2.判断是否是非学生账号
      const isNonStudent = selectedUser.role?.id !== 1 && selectedUser.role?.name !== '学生';

      // 3.如果是非学生账号，显示确认弹窗
      if (isNonStudent) {
        console.log("zww：被替换的是非学生账号");

        Modal.confirm({
          title: '确认替换账号',
          content: '您选择的是非学生账号，替换后原账号将无法使用该手机号登录。确定要替换吗？',
          okText: '确认替换',
          cancelText: '取消',
          onOk: () => executeReplaceAccount(selectedUser.id),
        });
      } else {
        // 4.学生账号直接替换，无需确认
        executeReplaceAccount(selectedUser.id);
      }
    } catch (error: any) {
      console.error('替换账号失败:', error);
      NotificationUtil.error(error.response?.data?.message || '替换失败，请重试');
      setLoading(false);
    }
  };

  // 执行替换账号操作
  const executeReplaceAccount = async (userId: number) => {
    try {
      console.log("zww:开始执行替换账号操作");

      setLoading(true);
      const phoneValue = form.getFieldValue('phone');
      const codeValue = form.getFieldValue('verifyCode');

      // 1.使用try-catch包装API调用
      try {
        const response = await userApi.bindPhone({
          phone: phoneValue,
          code: codeValue,
          replaceUserId: userId
        });

        // 使用类型断言解决类型错误
        const apiResponse = response as any;

        // 直接访问response.data，不使用可选链
        if (apiResponse.data && apiResponse.data.code === 200) {
          NotificationUtil.success('账号替换成功');
          setShowConflictModal(false);
          onSuccess(phoneValue);
        } else {
          // 处理错误响应
          const errorMessage = apiResponse.data?.message || '替换失败，请重试';
          NotificationUtil.error(errorMessage);
        }
      } catch (apiError: any) {
        // 处理API调用异常
        const errorMessage = apiError.response?.data?.message || '替换失败，请重试';
        NotificationUtil.error(errorMessage);
      }
    } finally {
      setLoading(false);
    }
  };

  // 处理微信绑定到用户
  const handleBindWeixinToUser = async (userId: number) => {
    // 添加二次确认
    Modal.confirm({
      title: '确认绑定',
      content: '您正在将微信账号绑定到已有账号，绑定成功后当前微信账号信息将无法找回，确定要继续吗？',
      okText: '确认绑定',
      cancelText: '取消',
      onOk: async () => {
        try {
          setLoading(true);
          // 1.调用微信绑定API - 迁移openId, 同时需要传入验证码，否则当前api暴露后任何人都能进行迁移
          const codeValue = form.getFieldValue('verifyCode');
          const bindPhone = form.getFieldValue('phone');
          const response = await userApi.transferWeixinOpenid(userId, codeValue, bindPhone);

          // 使用类型断言解决类型错误
          const apiResponse = response as any;
          console.log("zww：调用迁移微信OpenId接口响应", apiResponse);

          if (apiResponse.data && apiResponse.data.code === 200) {

            NotificationUtil.success('微信绑定成功');


            // 2.获取新账号的登录信息
            try {
              console.log("zww：微信绑定账号成功，即将获取绑定目标账号的信息");
              // 保存返回的登录令牌和用户ID
              const targetUserId = apiResponse.data.data.userId;
              const loginToken = apiResponse.data.data.loginToken;
              // 关闭弹窗先，避免后续token变化影响UI操作
              setShowConflictModal(false);
              // 使用封装的API方法获取登录信息和token，用保存的targetUserId
              console.log("zww：微信绑定账号成功，即将获取绑定目标账号的信息，目标用户ID为", targetUserId);
              console.log("zww：微信绑定账号成功，即将获取绑定目标账号的信息，loginToken为", loginToken);
              
              const loginResponse = await userApi.autoLoginByUserId(targetUserId, loginToken);
              // 使用类型断言处理返回结果
              const loginResult = loginResponse as any;
              console.log("zww：绑定目标账号的响应值", loginResult);

              //3.构建新的userData
              if (loginResult.data && loginResult.data.code === 200) {
                console.log("zww：获取微信绑定目标账号的响应值成功！");

                // 保存新的token和用户信息
                const tokenData = loginResult.data.data;
                const userData = {
                  userId: tokenData.userInfo.id,
                  nickName: tokenData.userInfo.nickName || '',
                  avatarUrl: tokenData.userInfo.avatarUrl || '',
                  gender: tokenData.userInfo.gender || 0,
                  phone: tokenData.userInfo.phone || '',
                  isLoggedIn: true,
                  roleId: tokenData.userInfo.roleId,
                  registerType: tokenData.userInfo.register_type || 'weixin',
                  needSetPwd: tokenData.needSetPwd || false,
                  isNewUser: tokenData.isNewUser || false
                };
                console.log('zww：绑定到目标用户的新数据为:', userData);

                localStorage.setItem('token', tokenData.token);
                localStorage.setItem('refreshToken', tokenData.refreshToken || '');
                localStorage.setItem('userId', tokenData.userInfo.id.toString());
                localStorage.setItem('user', JSON.stringify(userData));

                // 调用成功回调，传递userData以便LoginForm继续处理身份绑定和密码设置
                onSuccess(JSON.stringify(userData));
              } else {
                // 如果自动登录失败，提示用户手动登录
                // NotificationUtil.info('请重新登录进入新账号');
                onSuccess('');
              }
            } catch (loginError) {
              console.error('自动登录失败:', loginError);
              // 提示用户重新登录
              NotificationUtil.info('请重新登录进入新账号');
              onSuccess('');
            }
          } else {
            NotificationUtil.error(apiResponse.data?.message || apiResponse.data?.msg || '绑定失败，请稍后重试');
          }
        } catch (error: any) {
          console.error('微信绑定账号失败:', error);
          NotificationUtil.error(error.response?.data?.message || '绑定失败，请稍后重试');
        } finally {
          setLoading(false);
        }
      }
    });
  };

  return (
    <div >
      <div className="text-center mb-6">
        <h2 className={`text-xl font-semibold ${COLORS.text.white}`}>绑定手机号</h2>
        <p className={`mt-2 text-sm ${COLORS.text.white}`}>绑定手机号可以更好地保障您的账号安全</p>
      </div>

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        className="space-y-4"
      >
        <Form.Item
          name="phone"
          rules={[
            { required: true, message: '请输入手机号' },
            { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号' }
          ]}
        >
          <Input
            placeholder="请输入手机号"
            className={`h-12 ${COLORS.bg.primary} border ${COLORS.border.white_dim} rounded-xl ${COLORS.text.primary}`}
            onChange={e => setPhone(e.target.value)}
          />
        </Form.Item>

        <Form.Item
          name="verifyCode"
          rules={[{ required: true, message: '请输入验证码' }]}
        >
          <div className="flex space-x-2">
            <Input
              placeholder="请输入验证码"
              className={`h-12 ${COLORS.bg.primary} border ${COLORS.border.white_dim} rounded-xl ${COLORS.text.primary}`}
              onChange={e => setVerifyCode(e.target.value)}
            />
            <Button
              onClick={handleSendCode}
              disabled={countdown > 0}
              loading={loading && countdown === 0}
              className={`h-12 px-4 ${COLORS.bg.accent} ${COLORS.text.white} rounded-xl ${COLORS.bg.accent_hover} border-0 min-w-[120px]`}
            >
              {countdown > 0 ? `${countdown}秒` : '获取验证码'}
            </Button>
          </div>
        </Form.Item>

        <div className="flex space-x-4 pt-2">
          <Button
            type="primary"
            htmlType="submit"
            loading={loading}
            className={`h-12 w-full ${COLORS.bg.accent} ${COLORS.text.white} rounded-xl ${COLORS.bg.accent_hover} border-0`}
          >
            确认绑定
          </Button>

          {allowSkip && onSkip && (
            <Button
              onClick={onSkip}
              className={`h-12 ${COLORS.bg.transparent} border ${COLORS.border.white_dim} ${COLORS.text.white} rounded-xl ${COLORS.bg.white_dim_hover}`}
            >
              暂不绑定
            </Button>
          )}
        </div>
      </Form>

      {/* 使用封装的手机号冲突组件，传递registerType */}
      <InlinePhoneConflict
        open={showConflictModal}
        onClose={() => setShowConflictModal(false)}
        onContinueBind={handleContinueBind}
        onReplaceAccount={handleReplaceAccount}
        onBindWeixinToUser={handleBindWeixinToUser}
        bindUserList={bindUserList}
        loading={loading}
        registerType={registerType}
      />
    </div>
  );
};

export default InlineBindPhone; 