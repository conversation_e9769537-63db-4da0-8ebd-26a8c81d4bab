import { ExceptionFilter, Catch, ArgumentsHost, HttpException, HttpStatus, Logger } from '@nestjs/common';
import { Response } from 'express';

/**
 * Course 模块专用异常过滤器
 * 统一处理课程相关的异常，减少控制器中的重复代码
 */
@Catch()
export class CourseExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(CourseExceptionFilter.name);

  catch(exception: any, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest();

    // 记录异常日志
    this.logException(exception, request);

    // 处理不同类型的异常
    const result = this.handleException(exception);

    // 返回统一格式的响应
    response.status(result.statusCode).json(result.body);
  }

  /**
   * 记录异常日志
   */
  private logException(exception: any, request: any) {
    const { method, url } = request;
    const message = exception?.message || '未知错误';
    
    this.logger.error(
      `${method} ${url} - ${message}`,
      exception?.stack || exception
    );
  }

  /**
   * 处理异常并返回标准化响应
   */
  private handleException(exception: any) {
    // 1. 处理 NestJS 内置的 HttpException
    if (exception instanceof HttpException) {
      const status = exception.getStatus();
      const message = exception.message;

      return {
        statusCode: status,
        body: this.createErrorResponse(message, status)
      };
    }

    // 2. 处理业务异常（根据错误消息判断）
    const message = exception?.message || '服务器内部错误';

    // 2.1 处理"不存在"类异常 -> 404
    if (message.includes('不存在')) {
      return {
        statusCode: HttpStatus.NOT_FOUND,
        body: this.createErrorResponse(message, 404)
      };
    }

    // 2.2 处理"已存在"类异常 -> 400
    if (message.includes('已存在')) {
      return {
        statusCode: HttpStatus.BAD_REQUEST,
        body: this.createErrorResponse(message, 400)
      };
    }

    // 2.3 处理"正在被使用"类异常 -> 400
    if (message.includes('正在被') && message.includes('使用')) {
      return {
        statusCode: HttpStatus.BAD_REQUEST,
        body: this.createErrorResponse(message, 400)
      };
    }

    // 2.4 处理"无权限"类异常 -> 403
    if (message.includes('无权限') || message.includes('权限不足')) {
      return {
        statusCode: HttpStatus.FORBIDDEN,
        body: this.createErrorResponse(message, 403)
      };
    }

    // 2.5 处理参数验证异常 -> 400
    if (message.includes('不能为空') || message.includes('必须') || message.includes('无效')) {
      return {
        statusCode: HttpStatus.BAD_REQUEST,
        body: this.createErrorResponse(message, 400)
      };
    }

    // 3. 处理数据库相关异常
    if (this.isDatabaseError(exception)) {
      return {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        body: this.createErrorResponse('数据库操作失败，请稍后重试', 500)
      };
    }

    // 4. 默认处理：未知异常 -> 500
    return {
      statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      body: this.createErrorResponse('服务器内部错误，请稍后重试', 500)
    };
  }

  /**
   * 创建错误响应格式（模拟 HttpResponseResultService.error）
   */
  private createErrorResponse(message: string, statusCode: number) {
    return {
      success: false,
      message: message,
      data: null,
      statusCode: statusCode,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * 判断是否为数据库相关异常
   */
  private isDatabaseError(exception: any): boolean {
    const errorCode = exception?.code;
    const errorMessage = exception?.message?.toLowerCase() || '';

    // MySQL 错误码
    const mysqlErrorCodes = ['ER_DUP_ENTRY', 'ER_NO_REFERENCED_ROW', 'ER_ROW_IS_REFERENCED'];
    
    // TypeORM 错误
    const typeormErrors = ['queryrunner', 'connection', 'transaction'];

    return (
      mysqlErrorCodes.includes(errorCode) ||
      typeormErrors.some(keyword => errorMessage.includes(keyword)) ||
      errorMessage.includes('duplicate') ||
      errorMessage.includes('foreign key')
    );
  }
}
