'use client'

import { useState, useEffect } from 'react';
import { Card, Button, Table, Tag, Space, Modal, message, Tooltip } from 'antd';
import { EyeOutlined, EditOutlined, DeleteOutlined, PlusOutlined, FileTextOutlined } from '@ant-design/icons';
import { activityApi } from '@/lib/api/activity';
import { GetNotification } from 'logic-common/dist/components/Notification';
import { useRouter } from 'next/navigation';
import dayjs from 'dayjs';

interface Activity {
  id: number;
  name: string;
  startTime: string;
  endTime: string;
  status: number;
  organizer: string;
  coverImage?: string;
  createdAt: string;
  updatedAt: string;
  competitionGroups?: string;
  registrationForm?: string;
  promotionImage?: string;
}

interface ActivityManagementProps {
  userId?: number;
}

export default function ActivityManagement({ userId }: ActivityManagementProps) {
  const [activities, setActivities] = useState<Activity[]>([]);
  const [loading, setLoading] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  
  const notification = GetNotification();
  const router = useRouter();

  // 获取活动列表
  const fetchActivities = async (page = 1, size = 10) => {
    try {
      setLoading(true);
      // 使用GET接口获取活动列表
      const response = await activityApi.getList({
        page,
        size,
      });

      if (response?.data && response.status === 200) {
        const data = response.data.data || response.data;
        console.log('获取到的活动数据:', data);

        // 处理不同的数据结构
        if (data.list) {
          // 如果有分页结构
          setActivities(data.list || []);
          setPagination({
            current: page,
            pageSize: size,
            total: data.pagination?.total || data.total || 0,
          });
        } else if (Array.isArray(data)) {
          // 如果直接是数组
          setActivities(data);
          setPagination({
            current: page,
            pageSize: size,
            total: data.length,
          });
        } else {
          setActivities([]);
        }
      }
    } catch (error) {
      console.error('获取活动列表失败:', error);
      notification.error('获取活动列表失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchActivities();
  }, []);

  // 处理分页变化
  const handleTableChange = (paginationInfo: any) => {
    fetchActivities(paginationInfo.current, paginationInfo.pageSize);
  };

  // 跳转到活动页面
  const handleViewActivity = () => {
    router.push('/activity');
  };

  // 跳转到活动详情页面
  const handleViewActivityDetail = (activityId: number) => {
    router.push(`/activity/festival/?id=${activityId}`);
  };

  // 跳转到作品审核页面
  const handleViewWorks = (activityId: number) => {
    router.push(`/admin-space/activity-works?activityId=${activityId}`);
  };



  // 删除活动
  const handleDeleteActivity = async (activityId: number) => {
    try {
      const response = await activityApi.delete(activityId);
      if (response?.status === 200) {
        notification.success('删除活动成功');
        fetchActivities(pagination.current, pagination.pageSize);
      } else {
        notification.error('删除活动失败');
      }
    } catch (error) {
      console.error('删除活动失败:', error);
      notification.error('删除活动失败');
    }
  };

  // 表格列定义
  const columns = [
    {
      title: '活动名称',
      dataIndex: 'name',
      key: 'name',
      width: 180,
      ellipsis: true,
    },
    {
      title: '主办方',
      dataIndex: 'organizer',
      key: 'organizer',
      width: 120,
      ellipsis: true,
    },
    {
      title: '参赛组别',
      dataIndex: 'competitionGroups',
      key: 'competitionGroups',
      width: 150,
      ellipsis: true,
      render: (text: string) => {
        if (!text) return <span className="text-gray-400">未设置</span>;
        const groups = text.split(',').filter(g => g.trim());
        if (groups.length <= 2) {
          return groups.join(', ');
        }
        return (
          <Tooltip title={groups.join(', ')}>
            <span>{groups.slice(0, 2).join(', ')} 等{groups.length}个组别</span>
          </Tooltip>
        );
      },
    },
    {
      title: '报名表',
      dataIndex: 'registrationForm',
      key: 'registrationForm',
      width: 100,
      render: (text: string) => {
        if (!text) return <span className="text-gray-400">未设置</span>;

        let formUrl = '';
        if (text.includes('|')) {
          // 新格式：多个配置项用 | 分隔，每项格式为 "名称,文件URL,示例图URL"
          const firstConfig = text.split('|')[0];
          const [name, fileUrl, exampleUrl] = firstConfig.split(',').map(s => s.trim());
          formUrl = fileUrl;
        } else {
          // 兼容旧格式和新的单项格式
          const parts = text.split(',').map(s => s.trim());
          if (parts.length === 2) {
            // 旧格式：文件URL,示例图URL
            formUrl = parts[0];
          } else if (parts.length === 3) {
            // 新格式单项：名称,文件URL,示例图URL
            formUrl = parts[1];
          }
        }

        return formUrl ? (
          <a href={formUrl} target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:text-blue-700">
            查看表格
          </a>
        ) : <span className="text-gray-400">未设置</span>;
      },
    },
    {
      title: '开始时间',
      dataIndex: 'startTime',
      key: 'startTime',
      width: 110,
      render: (text: string) => dayjs(text).format('YYYY-MM-DD'),
    },
    {
      title: '结束时间',
      dataIndex: 'endTime',
      key: 'endTime',
      width: 110,
      render: (text: string) => dayjs(text).format('YYYY-MM-DD'),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status: number) => (
        <Tag color={status === 1 ? 'green' : 'orange'}>
          {status === 1 ? '已发布' : '草稿'}
        </Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 120,
      render: (text: string) => dayjs(text).format('YYYY-MM-DD'),
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (record: Activity) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="link"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => handleViewActivityDetail(record.id)}
            />
          </Tooltip>
          <Tooltip title="查看作品">
            <Button
              type="link"
              size="small"
              icon={<FileTextOutlined />}
              onClick={() => handleViewWorks(record.id)}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Button
              type="link"
              size="small"
              danger
              icon={<DeleteOutlined />}
              onClick={() => {
                Modal.confirm({
                  title: '确认删除',
                  content: `确定要删除活动"${record.name}"吗？`,
                  onOk: () => handleDeleteActivity(record.id),
                });
              }}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <>
      <Card
        title="活动管理"
        extra={
          <Button type="primary" onClick={() => setIsModalVisible(true)}>
            查看全部
          </Button>
        }
        className="shadow-sm"
      >
        <div className="space-y-4">
          <Button block onClick={handleViewActivity} icon={<PlusOutlined />}>
            进入活动管理页面
          </Button>
          <div className="text-sm text-gray-500">
            管理系统中的所有活动，查看学生提交的作品
          </div>
        </div>
      </Card>

      {/* 活动列表模态框 */}
      <Modal
        title="活动管理"
        open={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        width={1000}
        footer={null}
      >
        <Table
          columns={columns}
          dataSource={activities}
          rowKey="id"
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
          onChange={handleTableChange}
          scroll={{ x: 1200 }}
        />
      </Modal>
    </>
  );
}
