/* 学校选择弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(12px);
  /* 阻止背景滚动 */
  overflow: hidden;
  touch-action: none;
  /* 阻止滚轮事件传播到页面 */
  overscroll-behavior: contain;
}

.modal-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-content {
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 24px;
  width: 90%;
  max-width: 520px;
  min-width: 480px;
  height: 600px;
  max-height: 90vh;
  min-height: 500px;
  overflow: hidden;
  box-shadow:
    0 32px 64px rgba(0, 0, 0, 0.12),
    0 0 0 1px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
  animation: modalSlideIn 0.5s cubic-bezier(0.16, 1, 0.3, 1);
  position: relative;
  display: flex;
  flex-direction: column;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-30px) scale(0.9);
    filter: blur(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
    filter: blur(0);
  }
}

.modal-close-btn-outside {
  position: absolute;
  top: -25px;
  right: -38px;
  background: rgba(255, 255, 255, 0.95);
  border: none;
  cursor: pointer;
  padding: 12px;
  border-radius: 50%;
  color: #64748b;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  z-index: 1001;
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid rgba(255, 255, 255, 0.8);
}

.modal-close-btn-outside:hover {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
  transform: scale(1.1) rotate(90deg);
  box-shadow: 0 12px 32px rgba(239, 68, 68, 0.25);
  border-color: rgba(239, 68, 68, 0.2);
}

.step-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px 48px;
  gap: 80px;
  position: relative;
  background: linear-gradient(135deg, rgba(156, 186, 234, 0.5) 0%, rgba(156, 186, 234, 0.5) 100%);
  /* margin: 24px 24px 0 24px; */
  border-radius: 0px;
  backdrop-filter: blur(10px);
  flex-shrink: 0;
}

/* 删除123下面的蓝色连接线 */
/* .step-indicator::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 20%;
  right: 20%;
  height: 3px;
  background: linear-gradient(90deg, #3b82f6 0%, #60a5fa 50%, #93c5fd 100%);
  transform: translateY(-50%);
  z-index: 1;
  border-radius: 2px;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
} */

/* 确保连接线不显示 */
.step-indicator::before {
  display: none !important;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
  position: relative;
  z-index: 2;
  transition: all 0.3s ease;
}

.step-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  border: 3px solid #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 16px;
  color: #64748b;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.step.active .step-number {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border-color: #3b82f6;
  color: white;
  transform: scale(1.1);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
}

.step-label {
  font-size: 13px;
  color: #64748b;
  font-weight: 600;
  text-align: center;
  transition: all 0.3s ease;
}

.step.active .step-label {
  color: #3b82f6;
  transform: translateY(-2px);
}

.modal-content-body {
  padding: 14px 48px 80px 48px;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
  z-index: 0;
}

.section-title {
  font-size: 18px;
  color: #1e293b;
  margin-bottom: 32px;
  font-weight: 600;
  text-align: center;
  position: relative;
  flex-shrink: 0;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, #3b82f6 0%, #60a5fa 100%);
  border-radius: 2px;
}

.schools-grid {
  display: flex;
  flex-direction: column;
  gap: 20px;
  overflow-y: auto;
  height: 450px;
  max-height: 450px;
  padding: 20px 20px 30px 20px;
  margin-bottom: 10px;
}

.schools-grid::-webkit-scrollbar {
  width: 10px;
}

.schools-grid::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.08);
  border-radius: 5px;
  margin: 10px 0;
}

.schools-grid::-webkit-scrollbar-thumb {
  background: rgba(59, 130, 246, 0.4);
  border-radius: 5px;
  transition: background 0.3s ease;
  border: 1px solid rgba(59, 130, 246, 0.1);
}

.schools-grid::-webkit-scrollbar-thumb:hover {
  background: rgba(59, 130, 246, 0.6);
  border-color: rgba(59, 130, 246, 0.2);
}

.school-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
  border: 2px solid transparent;
  border-radius: 16px;
  padding: 20px 16px;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  text-align: center;
  position: relative;
  overflow: visible;
  backdrop-filter: blur(10px);
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.08);
  z-index: 1;
}

.school-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
  border-radius: 18px;
}

.school-card:hover::before {
  opacity: 0.05;
}

.school-card:hover {
  box-shadow: 0 25px 80px rgba(59, 130, 246, 0.25);
  border-color: #3b82f6;
  z-index: 10;
}

.school-card-name {
  font-size: 20px;
  font-weight: 600;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: 0.3px;
  position: relative;
  z-index: 1;
  transition: all 0.3s ease;
  text-align: center;
  display: block;
  width: 100%;
  margin-bottom: 8px;
}



.school-card-location {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  color: #64748B;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  margin-top: 4px;
  opacity: 0.8;
  transition: all 0.3s ease;
}

.school-card:hover .school-card-location {
  opacity: 1;
  color: #475569;
}











.modal-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px 24px;
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  border-radius: 0 0 24px 24px;
  flex-shrink: 0;
}

.back-btn {
  padding: 12px 32px;
  border: 2px solid #3b82f6;
  background: rgba(255, 255, 255, 0.9);
  color: #3b82f6;
  border-radius: 12px;
  cursor: pointer;
  font-size: 15px;
  font-weight: 600;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  /* margin: 0 auto; */
  display: block;
}
.only{
  margin:0 auto;
}
.back-btn:hover {
  background: #3b82f6;
  color: white;
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

/* 移除浮动效果后不再需要额外的间距调整 */

/* 确保滚动容器不会裁剪浮动元素 - 已在主样式中设置 */

/* 优化滚动条在浮动时的表现 */
.schools-grid::-webkit-scrollbar {
  z-index: 999;
}

/* 防止背景元素干扰 */
.modal-content-body::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: -1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .modal-content {
    width: 95%;
    min-width: 320px;
    height: 80vh;
    min-height: 400px;
    margin: 10px;
  }



  .step-indicator {
    padding: 24px 32px;
    gap: 40px;
    margin: 0 16px;
  }

  /* 删除响应式蓝色连接线 */
  /* .step-indicator::before {
    left: 25%;
    right: 25%;
  } */

  .step-number {
    width: 32px;
    height: 32px;
    font-size: 14px;
  }

  .step-label {
    font-size: 11px;
  }

  .modal-content-body {
    padding: 20px 24px 80px 24px;
  }

  .schools-grid {
    padding: 16px 16px 24px 16px;
  }

  .section-title {
    font-size: 16px;
    margin-bottom: 24px;
  }

  .school-card {
    padding: 18px 12px;

  }

  .school-card-name {
    font-size: 18px;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .modal-content {
    width: 98%;
    min-width: 280px;
    height: 85vh;
    min-height: 350px;
  }

  .step-indicator {
    padding: 20px 16px;
    gap: 20px;
    margin: 0 12px;
  }

  .step-number {
    width: 28px;
    height: 28px;
    font-size: 12px;
  }

  .step-label {
    font-size: 10px;
  }

  .modal-content-body {
    padding: 16px 20px 80px 20px;
  }

  .schools-grid {
    padding: 12px 12px 20px 12px;
  }

  .section-title {
    font-size: 14px;
    margin-bottom: 20px;
  }

  .school-card {
    padding: 16px 10px;
    text-align: center;
  }

  .school-card-name {
    font-size: 16px;
  }

  .back-btn {
    padding: 10px 24px;
    font-size: 14px;
  }

  .modal-close-btn-outside {
    top: -15px;
    right: -15px;
    width: 36px;
    height: 36px;
    padding: 8px;
  }
}

@media (min-height: 800px) {
  .modal-content {
    height: 650px;
  }
}

@media (max-height: 600px) {
  .modal-content {
    height: 90vh;
    min-height: 400px;
  }

  .step-indicator {
    padding: 20px 32px;
  }

  .modal-content-body {
    padding: 16px 32px 80px 32px;
  }

  .section-title {
    margin-bottom: 20px;
  }
}




