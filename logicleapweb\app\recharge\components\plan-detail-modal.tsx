'use client'

import { useState, useEffect } from 'react';
import { Modal, Button, Radio, message } from 'antd';
import { RechargeOption, PaymentMethod } from '../types';
import { usePackagePurchase } from '../hooks/usePackagePurchase';
import { usePackageOrderPolling } from '../hooks/usePackageOrderPolling';
import { PaymentChannel } from '../../../lib/api/package-purchase';
import QRCodeGenerator from './QRCodeGenerator';

interface PlanDetailModalProps {
  plan: RechargeOption | null;
  visible: boolean;
  onClose: () => void;
  onSuccess?: (orderInfo?: any) => void;
}

const paymentMethods: PaymentMethod[] = [
  { id: "wechat", name: "微信支付", icon: "💚" },
  { id: "alipay", name: "支付宝", icon: "💙" },
  { id: "unionpay", name: "银联支付", icon: "🏦" },
];

export default function PlanDetailModal({ plan, visible, onClose, onSuccess }: PlanDetailModalProps) {
  const [paymentMethod, setPaymentMethod] = useState("wechat");
  const [showPayment, setShowPayment] = useState(false);
  const [countdown, setCountdown] = useState(900); // 15分钟倒计时
  const [isCreatingPayment, setIsCreatingPayment] = useState(false);
  const [paymentCreated, setPaymentCreated] = useState(false);
  const [qrCodeUrl, setQrCodeUrl] = useState<string>('');
  const [currentOrderNo, setCurrentOrderNo] = useState<string>('');
  const [showExitConfirm, setShowExitConfirm] = useState(false);

  const {
    purchasePackageOrder,
    loading: purchaseLoading,
    error: purchaseError,
    currentOrder,
    cancelPackageOrder,
    clearError
  } = usePackagePurchase();

  const {
    isPolling,
    startPolling,
    stopPolling,
    status: pollingStatus
  } = usePackageOrderPolling();

  useEffect(() => {
    if (!visible) {
      setShowPayment(false);
      setPaymentCreated(false);
      setQrCodeUrl('');
      setCountdown(900);
      setCurrentOrderNo('');
      setShowExitConfirm(false);
      stopPolling();
      return;
    }

    if (showPayment && plan && !paymentCreated && !isCreatingPayment) {
      handleCreatePayment();
    }

    const timer = setInterval(() => {
      setCountdown(prev => {
        if (prev <= 1) {
          clearInterval(timer);
          onClose();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [visible, showPayment, plan, paymentCreated, isCreatingPayment, stopPolling]);

  const handleCreatePayment = async () => {
    if (!plan) return;

    setIsCreatingPayment(true);
    try {
      // 将支付方法转换为PaymentChannel
      const channel: PaymentChannel = paymentMethod === 'wechat' ? 'wechatpay' :
                                     paymentMethod === 'alipay' ? 'alipay' : 'wechatpay';

      const purchaseInfo = await purchasePackageOrder(plan, channel, 'qrcode');
      setPaymentCreated(true);

      // 保存订单号用于取消订单
      if (purchaseInfo.orderNo) {
        setCurrentOrderNo(purchaseInfo.orderNo);
      }

      if (purchaseInfo.qrCode) {
        setQrCodeUrl(purchaseInfo.qrCode);
      }

      startPolling({
        orderNo: purchaseInfo.orderNo,
        onSuccess: (orderInfo: any) => {
          message.success({
            content: '🎉 支付成功！恭喜获得能量点数！',
            duration: 3,
            style: { marginTop: '20vh' },
          });

          onSuccess?.(orderInfo);
          setTimeout(() => {
            onClose();
          }, 500);
        },
        onFailed: (error: any) => {
          console.error('支付失败:', error);
          message.error('支付失败，请重试');
        },
        onTimeout: () => {
          console.warn('支付超时');
          message.warning('支付超时，请检查支付状态');
        },
        maxAttempts: 180,
        intervalMs: 5000,
        timeoutMs: 900000,
      });
    } catch (error) {
      message.error('创建支付订单失败，请重试');
      console.error('创建支付失败:', error);
    } finally {
      setIsCreatingPayment(false);
    }
  };

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const handlePayment = () => {
    setShowPayment(true);
  };

  // 处理关闭弹窗
  const handleClose = () => {
    // 如果已经创建了支付订单，显示确认对话框
    if (paymentCreated && currentOrderNo) {
      setShowExitConfirm(true);
    } else {
      // 如果还没有创建支付订单，直接关闭
      onClose();
    }
  };

  // 处理放弃支付
  const handleAbandonPayment = async () => {
    try {
      if (currentOrderNo) {
        await cancelPackageOrder(currentOrderNo);
        message.success('订单已取消');
      }
      setShowExitConfirm(false);
      onClose();
    } catch (error) {
      console.error('取消订单失败:', error);
      message.error('取消订单失败，请稍后重试');
    }
  };

  // 处理继续支付
  const handleContinuePayment = () => {
    setShowExitConfirm(false);
  };

  if (!plan) return null;

  return (
    <>
      <Modal
        open={visible}
        onCancel={handleClose}
        footer={null}
        width={showPayment ? 900 : 800}
        className="plan-detail-modal dribbble-style"
        centered
      >
      <div className="modal-content">
        {!showPayment ? (
          // 左右布局：套餐详情 + 支付方式
          <div className="layout-container">
            {/* 左侧：套餐详情 */}
            <div className="left-panel">
              {/* 头部 - 横向布局：图标标题 + 价格 */}
              <div className="plan-header">
                <div className="plan-left">
                  <div className="plan-badge">
                    <div className="plan-icon">
                      <span>{plan.icon}</span>
                    </div>
                  </div>
                  <div className="plan-info">
                    <h2 className="plan-title">{plan.title}</h2>
                    <p className="plan-subtitle">{plan.subtitle}</p>
                  </div>
                </div>

                {/* 价格展示 - 右侧，只显示价格 */}
                <div className="price-display">
                  <div className="price-main">
                    <span className="currency">¥</span>
                    <span className="amount">{plan.amount % 1 === 0 ? Math.floor(plan.amount) : plan.amount}</span>
                  </div>
                </div>
              </div>

              {/* 积分信息 - 单独一行 */}
              {plan.bonus > 0 && (
                <div className="bonus-section">
                  <div className="bonus-tag">
                    <span className="bonus-icon">🎁</span>
                    <span>包含 {Math.floor(plan.bonus)} 积分</span>
                  </div>
                </div>
              )}

              {/* 特性列表 - 只在有特性时显示 */}
              {plan.detailedFeatures.length > 0 && (
                <div className="features-list">
                  <h3 className="features-title">套餐详情</h3>
                  <div className="features-simple">
                    {plan.detailedFeatures.map((feature, index) => (
                      <div key={index} className="feature-item">
                        <span className="feature-icon">{feature.icon}</span>
                        <span className="feature-text">
                          <span className="feature-name">{feature.name}</span>
                          <span className="feature-value">{feature.value}</span>
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* 右侧：支付方式 */}
            <div className="right-panel">
              <div className="payment-section">
                <h3 className="payment-title">选择支付方式</h3>
                <p className="payment-subtitle">安全快捷，即时到账</p>

                <div className="payment-methods">
                  <Radio.Group
                    value={paymentMethod}
                    onChange={(e) => setPaymentMethod(e.target.value)}
                    className="payment-options"
                  >
                    {paymentMethods.map((method) => (
                      <div key={method.id} className="payment-option">
                        <Radio value={method.id} className="payment-radio">
                          <div className="method-card">
                            <div className="method-icon">{method.icon}</div>
                            <div className="method-info">
                              <div className="method-name">{method.name}</div>
                              <div className="method-desc">安全便捷</div>
                            </div>
                            <div className="method-check">
                              <div className="check-circle"></div>
                            </div>
                          </div>
                        </Radio>
                      </div>
                    ))}
                  </Radio.Group>
                </div>



                {/* 支付按钮 */}
                <div className="payment-action">
                  <Button
                    type="primary"
                    size="large"
                    className="pay-button"
                    onClick={handlePayment}
                    loading={isCreatingPayment}
                  >
                    <span>立即支付</span>
                    <span className="pay-amount">¥{plan.amount}</span>
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                      <path d="M5 12h14M12 5l7 7-7 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </Button>
                </div>
              </div>
            </div>
          </div>
        ) : (
          // 支付视图：左右布局
          <div className="payment-layout">
            {/* 左侧：订单信息 */}
            <div className="payment-left">
              <div className="payment-header">
                <button className="back-btn" onClick={() => setShowPayment(false)}>
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                    <path d="M19 12H5M12 19l-7-7 7-7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </button>
                <div className="header-info">
                  <h2 className="payment-title">扫码支付</h2>
                  <p className="payment-subtitle">请使用手机扫码完成支付</p>
                </div>
              </div>

              <div className="order-summary">
                <div className="order-header">
                  <h3>订单详情</h3>
                </div>
                <div className="order-item">
                  <div className="item-info">
                    <div className="item-icon">{plan.icon}</div>
                    <div className="item-details">
                      <div className="item-name">{plan.title}</div>
                      <div className="item-desc">{plan.subtitle}</div>
                    </div>
                  </div>
                  <div className="item-price">¥{plan.amount}</div>
                </div>

                <div className="payment-method-info">
                  <div className="method-label">支付方式</div>
                  <div className="method-selected">
                    <span className="method-icon">{paymentMethods.find(m => m.id === paymentMethod)?.icon}</span>
                    <span className="method-name">{paymentMethods.find(m => m.id === paymentMethod)?.name}</span>
                  </div>
                </div>

                <div className="order-total">
                  <div className="total-line">
                    <span>应付金额</span>
                    <span className="total-amount">¥{plan.amount}</span>
                  </div>
                </div>
              </div>

              {/* 状态信息 */}
              <div className="payment-status-section">
                <div className="countdown-info">
                  <div className="countdown-icon">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                      <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"/>
                      <polyline points="12,6 12,12 16,14" stroke="currentColor" strokeWidth="2"/>
                    </svg>
                  </div>
                  <span>支付剩余时间：{formatTime(countdown)}</span>
                </div>
              </div>
            </div>

            {/* 右侧：二维码 */}
            <div className="payment-right">
              <div className="qr-section">
                <div className="qr-header">
                  <h3>扫码支付</h3>
                  <p>请使用{paymentMethods.find((m) => m.id === paymentMethod)?.name}扫描下方二维码</p>
                </div>

                <div className="qr-container">
                  {qrCodeUrl ? (
                    <div className="qr-code-wrapper">
                      <div className="qr-frame">
                        <QRCodeGenerator
                          value={qrCodeUrl}
                          paymentMethod={paymentMethod}
                          className="payment-qr-code"
                        />
                      </div>
                      <div className="qr-tips">
                        <div className="tip-item">
                          <span>1. 打开{paymentMethods.find((m) => m.id === paymentMethod)?.name}APP</span>
                        </div>
                        <div className="tip-item">
                          <span>2. 扫描上方二维码</span>
                        </div>
                        <div className="tip-item">
                          <span>3. 确认支付金额</span>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="qr-loading">
                      {isCreatingPayment ? (
                        <div className="loading-content">
                          <div className="loading-spinner"></div>
                          <p>正在生成支付二维码...</p>
                        </div>
                      ) : (
                        <div className="qr-placeholder">
                          <svg width="120" height="120" viewBox="0 0 24 24" fill="none">
                            <rect x="3" y="3" width="7" height="7" stroke="currentColor" strokeWidth="2"/>
                            <rect x="14" y="3" width="7" height="7" stroke="currentColor" strokeWidth="2"/>
                            <rect x="3" y="14" width="7" height="7" stroke="currentColor" strokeWidth="2"/>
                            <rect x="5" y="5" width="3" height="3" fill="currentColor"/>
                            <rect x="16" y="5" width="3" height="3" fill="currentColor"/>
                            <rect x="5" y="16" width="3" height="3" fill="currentColor"/>
                            <rect x="14" y="14" width="2" height="2" fill="currentColor"/>
                            <rect x="17" y="14" width="2" height="2" fill="currentColor"/>
                            <rect x="14" y="17" width="2" height="2" fill="currentColor"/>
                            <rect x="19" y="17" width="2" height="2" fill="currentColor"/>
                          </svg>
                          <p>等待生成二维码</p>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>


            </div>
          </div>
        )}
      </div>
    </Modal>

    {/* 退出确认对话框 */}
    <Modal
      open={showExitConfirm}
      closable={false}
      maskClosable={false}
      footer={null}
      centered
      width={420}
      className="exit-confirm-modal"
    >
      <div style={{ textAlign: 'center', padding: '32px 24px 24px' }}>
        {/* 图标 */}
        <div style={{
          fontSize: '48px',
          marginBottom: '20px',
          color: '#ff6b35'
        }}>
          ⚠️
        </div>

        {/* 标题 */}
        <div style={{
          fontSize: '20px',
          fontWeight: '600',
          color: '#1f2937',
          marginBottom: '12px'
        }}>
          确认退出支付
        </div>

        {/* 描述 */}
        <div style={{
          color: '#6b7280',
          fontSize: '14px',
          lineHeight: '1.5',
          marginBottom: '32px'
        }}>
          选择"放弃支付"将取消当前订单，您需要重新下单。
        </div>

        {/* 按钮组 */}
        <div style={{
          display: 'flex',
          gap: '12px',
          justifyContent: 'center'
        }}>
          <Button
            key="continue"
            type="primary"
            size="large"
            onClick={handleContinuePayment}
            style={{
              minWidth: '120px',
              height: '44px',
              borderRadius: '8px',
              fontWeight: '600'
            }}
          >
            继续支付
          </Button>
          <Button
            key="abandon"
            danger
            size="large"
            onClick={handleAbandonPayment}
            style={{
              minWidth: '120px',
              height: '44px',
              borderRadius: '8px',
              fontWeight: '600'
            }}
          >
            放弃支付
          </Button>
        </div>
      </div>
    </Modal>
  </>
  );
}
