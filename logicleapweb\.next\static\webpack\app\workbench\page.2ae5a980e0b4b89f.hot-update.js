"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/workbench/page",{

/***/ "(app-pages-browser)/./app/workbench/components/OfficialCourses.css":
/*!******************************************************!*\
  !*** ./app/workbench/components/OfficialCourses.css ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = (\"7ee2d184ab69\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC93b3JrYmVuY2gvY29tcG9uZW50cy9PZmZpY2lhbENvdXJzZXMuY3NzIiwibWFwcGluZ3MiOiI7QUFBQSwrREFBZSxjQUFjO0FBQzdCLElBQUksSUFBVSxJQUFJLGlCQUFpQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9hcHAvd29ya2JlbmNoL2NvbXBvbmVudHMvT2ZmaWNpYWxDb3Vyc2VzLmNzcz80OTQzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiN2VlMmQxODRhYjY5XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/workbench/components/OfficialCourses.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/workbench/components/MainContent.tsx":
/*!**************************************************!*\
  !*** ./app/workbench/components/MainContent.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _QuickActions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./QuickActions */ \"(app-pages-browser)/./app/workbench/components/QuickActions.tsx\");\n/* harmony import */ var _OngoingTasks__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./OngoingTasks */ \"(app-pages-browser)/./app/workbench/components/OngoingTasks.tsx\");\n/* harmony import */ var _TemplateManagement__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./TemplateManagement */ \"(app-pages-browser)/./app/workbench/components/TemplateManagement.tsx\");\n/* harmony import */ var _ClassManagement__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ClassManagement */ \"(app-pages-browser)/./app/workbench/components/ClassManagement.tsx\");\n/* harmony import */ var _ClassDetail__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ClassDetail */ \"(app-pages-browser)/./app/workbench/components/ClassDetail.tsx\");\n/* harmony import */ var _ClassTasks__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ClassTasks */ \"(app-pages-browser)/./app/workbench/components/ClassTasks.tsx\");\n/* harmony import */ var _CourseManagement__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./CourseManagement */ \"(app-pages-browser)/./app/workbench/components/CourseManagement.tsx\");\n/* harmony import */ var _ClassProjects__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./ClassProjects */ \"(app-pages-browser)/./app/workbench/components/ClassProjects.tsx\");\n/* harmony import */ var _OfficialCourses__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./OfficialCourses */ \"(app-pages-browser)/./app/workbench/components/OfficialCourses.tsx\");\n/* harmony import */ var _SchoolSelectionModal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./SchoolSelectionModal */ \"(app-pages-browser)/./app/workbench/components/SchoolSelectionModal.tsx\");\n/* harmony import */ var _ClassSelectionModal__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./ClassSelectionModal */ \"(app-pages-browser)/./app/workbench/components/ClassSelectionModal.tsx\");\n/* harmony import */ var _TemplateSelectionModal__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./TemplateSelectionModal */ \"(app-pages-browser)/./app/workbench/components/TemplateSelectionModal.tsx\");\n/* harmony import */ var _teacher_space_components_modals_create_class_modal__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../../teacher-space/components/modals/create-class-modal */ \"(app-pages-browser)/./app/teacher-space/components/modals/create-class-modal.tsx\");\n/* harmony import */ var _lib_api_school__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/lib/api/school */ \"(app-pages-browser)/./lib/api/school.ts\");\n/* harmony import */ var _lib_api_class__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/lib/api/class */ \"(app-pages-browser)/./lib/api/class.ts\");\n/* harmony import */ var _barrel_optimize_names_Search_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/message/index.js\");\n/* harmony import */ var _icon_park_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @icon-park/react */ \"(app-pages-browser)/./node_modules/@icon-park/react/es/icons/HandUp.js\");\n/* harmony import */ var _icon_park_react_styles_index_css__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @icon-park/react/styles/index.css */ \"(app-pages-browser)/./node_modules/@icon-park/react/styles/index.css\");\n/* harmony import */ var _SchoolSelectionModal_css__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./SchoolSelectionModal.css */ \"(app-pages-browser)/./app/workbench/components/SchoolSelectionModal.css\");\n/* harmony import */ var _contexts_TemplateContext__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ../contexts/TemplateContext */ \"(app-pages-browser)/./app/workbench/contexts/TemplateContext.tsx\");\n/* harmony import */ var _TemplateSelectionModal_css__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./TemplateSelectionModal.css */ \"(app-pages-browser)/./app/workbench/components/TemplateSelectionModal.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst MainContent = (param)=>{\n    let { activeView = \"快速开始\", selectedSchool, userInfo, classes = [], classesLoading = false, classesError = null, onCloseDropdown, onClassesUpdate, onSchoolChange } = param;\n    _s();\n    const [isSchoolModalOpen, setIsSchoolModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isClassModalOpen, setIsClassModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isTemplateModalOpen, setIsTemplateModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isCreateClassModalOpen, setIsCreateClassModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentActionType, setCurrentActionType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [modalSelectedSchool, setModalSelectedSchool] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedClass, setSelectedClass] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showClassDetail, setShowClassDetail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedClassForDetail, setSelectedClassForDetail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 使用模板上下文\n    const { currentTemplate, globalTemplateChangeVersion } = (0,_contexts_TemplateContext__WEBPACK_IMPORTED_MODULE_19__.useTemplate)();\n    // 监听全局模板变化，通知所有班级详情组件刷新\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (globalTemplateChangeVersion > 0 && currentTemplate) {\n            console.log(\"MainContent - 检测到全局模板变化，版本号:\", globalTemplateChangeVersion);\n            console.log(\"MainContent - 新的当前模板:\", currentTemplate);\n        // 这里可以添加通知所有班级组件刷新的逻辑\n        // 由于班级详情组件已经在监听globalTemplateChangeVersion，\n        // 这里主要是为了确保状态同步\n        }\n    }, [\n        globalTemplateChangeVersion,\n        currentTemplate\n    ]);\n    // 监听学校选择变化，强制跳回班级管理页面\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedSchool) {\n            console.log(\"检测到学校变化，强制跳回班级管理页面:\", selectedSchool);\n            // 重置班级详情显示状态，强制显示班级列表\n            setShowClassDetail(false);\n            setSelectedClassForDetail(null);\n            // 通知父组件学校变化\n            if (onSchoolChange) {\n                onSchoolChange(selectedSchool);\n            }\n        }\n    }, [\n        selectedSchool,\n        onSchoolChange\n    ]);\n    const handleQuickStartClick = async function() {\n        let actionType = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"发布任务\";\n        setCurrentActionType(actionType);\n        try {\n            // 获取用户的学校列表\n            const response = await _lib_api_school__WEBPACK_IMPORTED_MODULE_15__.schoolApi.getUserSchools();\n            if (response.data.code === 200) {\n                const schoolsData = response.data.data || [];\n                if (schoolsData.length === 1) {\n                    // 只有一个学校，直接选择并跳到班级选择\n                    setModalSelectedSchool(schoolsData[0]);\n                    setIsClassModalOpen(true);\n                } else if (schoolsData.length > 1) {\n                    // 多个学校，显示学校选择弹窗\n                    setIsSchoolModalOpen(true);\n                } else {\n                    // 没有学校，可以显示提示信息\n                    console.warn(\"用户没有关联的学校\");\n                }\n            }\n        } catch (error) {\n            console.error(\"获取学校列表失败:\", error);\n            // 出错时仍然显示学校选择弹窗\n            setIsSchoolModalOpen(true);\n        }\n    };\n    const handleCloseModal = ()=>{\n        setIsSchoolModalOpen(false);\n        setIsClassModalOpen(false);\n        setIsTemplateModalOpen(false);\n        setModalSelectedSchool(null);\n        setSelectedClass(null);\n        setCurrentActionType(\"\");\n    };\n    const handleSchoolSelect = (school)=>{\n        setModalSelectedSchool(school);\n        setIsSchoolModalOpen(false);\n        setIsClassModalOpen(true);\n    };\n    const handleClassSelect = (classData)=>{\n        setSelectedClass(classData);\n        setIsClassModalOpen(false);\n        setIsTemplateModalOpen(true);\n    };\n    const handleBackToSchool = ()=>{\n        setIsClassModalOpen(false);\n        setIsSchoolModalOpen(true);\n    };\n    const handleBackToClass = ()=>{\n        setIsTemplateModalOpen(false);\n        setIsClassModalOpen(true);\n    };\n    const handleClassClick = (classInfo)=>{\n        console.log(\"点击班级:\", classInfo);\n        setSelectedClassForDetail(classInfo);\n        setShowClassDetail(true);\n    };\n    const handleBackToClassManagement = ()=>{\n        setShowClassDetail(false);\n        setSelectedClassForDetail(null);\n    };\n    // 处理班级信息更新\n    const handleClassInfoUpdate = (updatedClassInfo)=>{\n        // 更新班级列表中对应的班级信息\n        const updatedClasses = classes.map((classItem)=>classItem.id === updatedClassInfo.id ? {\n                ...classItem,\n                ...updatedClassInfo\n            } : classItem);\n        // 通知父组件更新班级列表\n        onClassesUpdate === null || onClassesUpdate === void 0 ? void 0 : onClassesUpdate(updatedClasses);\n        // 同时更新当前选中的班级详情\n        setSelectedClassForDetail(updatedClassInfo);\n    };\n    // 处理班级删除\n    const handleClassDeleted = (deletedClassId)=>{\n        console.log(\"班级已删除:\", deletedClassId);\n        // 从班级列表中移除被删除的班级\n        const updatedClasses = classes.filter((cls)=>cls.id !== deletedClassId);\n        // 通知父组件更新班级列表\n        onClassesUpdate === null || onClassesUpdate === void 0 ? void 0 : onClassesUpdate(updatedClasses);\n    };\n    // 处理添加班级\n    const handleAddClass = ()=>{\n        if (!selectedSchool) {\n            _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"].error(\"请先选择学校\");\n            return;\n        }\n        setIsCreateClassModalOpen(true);\n    };\n    // 处理模板选择确认\n    const handleTemplateConfirm = (taskData)=>{\n        console.log(\"模板选择确认:\", taskData);\n        // 简单关闭弹窗，不执行具体的发布逻辑\n        handleCloseModal();\n    };\n    // 处理创建班级表单提交\n    const handleCreateClass = async (values)=>{\n        if (!selectedSchool) {\n            _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"].error(\"请先选择学校\");\n            return;\n        }\n        if (!(userInfo === null || userInfo === void 0 ? void 0 : userInfo.id)) {\n            _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"].error(\"用户信息不完整，请重新登录\");\n            return;\n        }\n        if (values.className.length > 8) {\n            _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"].error(\"班级名称不能超过8个字符\");\n            return;\n        }\n        try {\n            // 使用 createClass API，需要传递 teacherId\n            const response = await _lib_api_class__WEBPACK_IMPORTED_MODULE_16__.classApi.createClass(selectedSchool.id, values.className, userInfo.id);\n            if (response.data.code === 200) {\n                _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"].success(\"创建班级成功\");\n                setIsCreateClassModalOpen(false);\n                // 刷新班级列表\n                try {\n                    const classesResponse = await _lib_api_class__WEBPACK_IMPORTED_MODULE_16__.classApi.getTeacherClassesSimple(selectedSchool.id);\n                    if (classesResponse.data.code === 200) {\n                        onClassesUpdate === null || onClassesUpdate === void 0 ? void 0 : onClassesUpdate(classesResponse.data.data || []);\n                    }\n                } catch (error) {\n                    console.error(\"刷新班级列表失败:\", error);\n                }\n            } else {\n                _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"].error(response.data.message || \"该班级已存在或创建失败\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"创建班级失败:\", error);\n            _barrel_optimize_names_message_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"].error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"创建班级失败\");\n        }\n    };\n    // 处理创建班级弹窗关闭\n    const handleCreateClassModalClose = ()=>{\n        setIsCreateClassModalOpen(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"main-content relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"main-header\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"search-bar\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Search_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                className: \"search-icon\",\n                                size: 18\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                                lineNumber: 285,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                placeholder: \"搜索课程、任务或学生...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                        lineNumber: 284,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"start-class-btn\",\n                        onClick: ()=>handleQuickStartClick(\"快速上课\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_icon_park_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                theme: \"filled\",\n                                size: 20,\n                                fill: [\n                                    \"#ffffff\"\n                                ],\n                                className: \"start-class-icon\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"快速上课\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                                lineNumber: 290,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                lineNumber: 283,\n                columnNumber: 7\n            }, undefined),\n            activeView === \"模板管理\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TemplateManagement__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                selectedSchool: selectedSchool,\n                userInfo: userInfo\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                lineNumber: 296,\n                columnNumber: 9\n            }, undefined) : activeView === \"班级管理\" ? showClassDetail && selectedClassForDetail && selectedSchool ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ClassDetail__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                classInfo: selectedClassForDetail,\n                selectedSchool: selectedSchool,\n                onBack: handleBackToClassManagement,\n                onClassInfoUpdate: handleClassInfoUpdate,\n                onClassDeleted: handleClassDeleted\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                lineNumber: 299,\n                columnNumber: 11\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ClassManagement__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                selectedSchool: selectedSchool,\n                userInfo: userInfo,\n                classes: classes,\n                classesLoading: classesLoading,\n                classesError: classesError,\n                onClassClick: handleClassClick,\n                onCloseDropdown: onCloseDropdown,\n                onAddClass: handleAddClass\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                lineNumber: 307,\n                columnNumber: 11\n            }, undefined) : activeView === \"班级任务\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ClassTasks__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                lineNumber: 319,\n                columnNumber: 9\n            }, undefined) : activeView === \"官方课程\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_OfficialCourses__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                lineNumber: 321,\n                columnNumber: 9\n            }, undefined) : activeView === \"课程管理\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CourseManagement__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                lineNumber: 323,\n                columnNumber: 9\n            }, undefined) : activeView === \"班级项目\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ClassProjects__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                lineNumber: 325,\n                columnNumber: 9\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_QuickActions__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                        lineNumber: 328,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_OngoingTasks__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                        lineNumber: 329,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SchoolSelectionModal__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                isOpen: isSchoolModalOpen,\n                onClose: handleCloseModal,\n                actionType: currentActionType,\n                onSchoolSelect: handleSchoolSelect\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                lineNumber: 333,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ClassSelectionModal__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                isOpen: isClassModalOpen,\n                onClose: handleCloseModal,\n                onBack: handleBackToSchool,\n                actionType: currentActionType,\n                selectedSchool: modalSelectedSchool,\n                onClassSelect: handleClassSelect\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                lineNumber: 340,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TemplateSelectionModal__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                isOpen: isTemplateModalOpen,\n                onClose: handleCloseModal,\n                onBack: handleBackToClass,\n                onConfirm: handleTemplateConfirm,\n                actionType: currentActionType,\n                selectedSchool: modalSelectedSchool,\n                selectedClass: selectedClass\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                lineNumber: 349,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_teacher_space_components_modals_create_class_modal__WEBPACK_IMPORTED_MODULE_14__.CreateClassModal, {\n                visible: isCreateClassModalOpen,\n                onCancel: handleCreateClassModalClose,\n                onOk: handleCreateClass\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n                lineNumber: 359,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\MainContent.tsx\",\n        lineNumber: 282,\n        columnNumber: 5\n    }, undefined);\n};\n_s(MainContent, \"vuiDOKB9kf9CftAMGz7Fil9059k=\", false, function() {\n    return [\n        _contexts_TemplateContext__WEBPACK_IMPORTED_MODULE_19__.useTemplate\n    ];\n});\n_c = MainContent;\n/* harmony default export */ __webpack_exports__[\"default\"] = (MainContent);\nvar _c;\n$RefreshReg$(_c, \"MainContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/workbench/components/MainContent.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/workbench/components/OfficialCourses.tsx":
/*!******************************************************!*\
  !*** ./app/workbench/components/OfficialCourses.tsx ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_Eye_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,Eye,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_Eye_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,Eye,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_Eye_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,Eye,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_Eye_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,Eye,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _OfficialCourses_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./OfficialCourses.css */ \"(app-pages-browser)/./app/workbench/components/OfficialCourses.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst OfficialCourses = (param)=>{\n    let { onBack } = param;\n    _s();\n    const [isExpanded, setIsExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedCourse, setSelectedCourse] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showCourseDetail, setShowCourseDetail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 模拟官方课程数据\n    const [officialCourses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            title: \"IO\",\n            description: \"基础编程入门课程\",\n            coverImage: \"/images/course-placeholder.png\",\n            category: \"official\",\n            difficulty: \"初级\",\n            duration: \"2小时\",\n            studentCount: 0,\n            viewCount: 0,\n            tags: [\n                \"编程基础\"\n            ]\n        },\n        {\n            id: 2,\n            title: \"1\",\n            description: \"进阶编程课程\",\n            coverImage: \"/images/course-placeholder.png\",\n            category: \"official\",\n            difficulty: \"中级\",\n            duration: \"3小时\",\n            studentCount: 0,\n            viewCount: 0,\n            tags: [\n                \"进阶编程\"\n            ]\n        },\n        {\n            id: 3,\n            title: \"Node.js实现开发系列\",\n            description: \"Node.js后端开发完整教程\",\n            coverImage: \"/images/course-placeholder.png\",\n            category: \"official\",\n            difficulty: \"高级\",\n            duration: \"8小时\",\n            studentCount: 0,\n            viewCount: 0,\n            tags: [\n                \"Node.js\",\n                \"后端开发\"\n            ]\n        }\n    ]);\n    // 模拟社区课程数据\n    const [communityCourses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 4,\n            title: \"古色琴声于子\",\n            description: \"音乐编程创作课程\",\n            coverImage: \"/images/course-placeholder.png\",\n            category: \"community\",\n            difficulty: \"中级\",\n            duration: \"4小时\",\n            studentCount: 0,\n            viewCount: 0,\n            tags: [\n                \"音乐\",\n                \"创作\"\n            ]\n        },\n        {\n            id: 5,\n            title: \"000\",\n            description: \"基础算法课程\",\n            coverImage: \"/images/course-placeholder.png\",\n            category: \"community\",\n            difficulty: \"初级\",\n            duration: \"2小时\",\n            studentCount: 0,\n            viewCount: 0,\n            tags: [\n                \"算法\"\n            ]\n        },\n        {\n            id: 6,\n            title: \"游戏系列\",\n            description: \"游戏开发入门教程\",\n            coverImage: \"/images/course-placeholder.png\",\n            category: \"community\",\n            difficulty: \"中级\",\n            duration: \"6小时\",\n            studentCount: 150,\n            viewCount: 0,\n            tags: [\n                \"游戏开发\"\n            ]\n        }\n    ]);\n    // 处理展开全部\n    const handleExpandAll = ()=>{\n        setIsExpanded(true);\n    };\n    // 处理返回\n    const handleBack = ()=>{\n        if (showCourseDetail) {\n            setShowCourseDetail(false);\n            setSelectedCourse(null);\n        } else if (isExpanded) {\n            setIsExpanded(false);\n        } else {\n            onBack === null || onBack === void 0 ? void 0 : onBack();\n        }\n    };\n    // 处理课程点击\n    const handleCourseClick = (course)=>{\n        setSelectedCourse(course);\n        setShowCourseDetail(true);\n    };\n    // 渲染课程卡片\n    const renderCourseCard = (course)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"course-card\",\n            onClick: ()=>handleCourseClick(course),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"course-cover\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"course-placeholder\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Eye_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"course-icon\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"course-info\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"course-title\",\n                            children: course.title\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"course-meta\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"course-difficulty\",\n                                    children: course.difficulty\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 11\n                                }, undefined),\n                                course.studentCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"course-students\",\n                                    children: [\n                                        course.studentCount,\n                                        \"人学习\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                    lineNumber: 144,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, course.id, true, {\n            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n            lineNumber: 134,\n            columnNumber: 5\n        }, undefined);\n    if (showCourseDetail && selectedCourse) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"official-courses-container\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"course-detail-header\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"back-button\",\n                            onClick: handleBack,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Eye_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    size: 20\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"返回\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            children: \"课程详情\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                    lineNumber: 159,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"course-detail-content\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"course-detail-cover\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"course-detail-placeholder\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Eye_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"course-detail-icon\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                            lineNumber: 168,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"course-detail-info\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    children: selectedCourse.title\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"course-description\",\n                                    children: selectedCourse.description\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"course-stats\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"stat-item\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Eye_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                                                    lineNumber: 180,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        selectedCourse.studentCount,\n                                                        \" 人学习\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                                                    lineNumber: 181,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"stat-item\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Eye_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        selectedCourse.viewCount,\n                                                        \" 次观看\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"course-tags\",\n                                    children: selectedCourse.tags.map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"course-tag\",\n                                            children: tag\n                                        }, index, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"course-actions\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"start-course-btn\",\n                                            children: \"开始学习\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"preview-course-btn\",\n                                            children: \"预览课程\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                    lineNumber: 167,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n            lineNumber: 158,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (isExpanded) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"official-courses-container expanded\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"expanded-header\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"back-button\",\n                            onClick: handleBack,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Eye_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    size: 20\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"返回\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                            lineNumber: 209,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            children: \"全部课程\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                            lineNumber: 213,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                    lineNumber: 208,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"expanded-content\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"course-section\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"section-title\",\n                                    children: \"官方课程\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"courses-grid\",\n                                    children: officialCourses.map(renderCourseCard)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                            lineNumber: 217,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"course-section\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"section-title\",\n                                    children: \"社区课程\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"courses-grid\",\n                                    children: communityCourses.map(renderCourseCard)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                                    lineNumber: 226,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                            lineNumber: 224,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                    lineNumber: 216,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n            lineNumber: 207,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"official-courses-container\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"courses-header\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        children: \"官方课程\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                        lineNumber: 238,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"expand-all-btn\",\n                        onClick: handleExpandAll,\n                        children: \"展开全部\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                lineNumber: 237,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"courses-preview\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"course-section\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"section-title\",\n                                children: \"官方课程\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"courses-grid preview\",\n                                children: officialCourses.slice(0, 3).map(renderCourseCard)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                        lineNumber: 245,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"course-section\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"section-title\",\n                                children: \"社区课程\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                                lineNumber: 253,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"courses-grid preview\",\n                                children: communityCourses.slice(0, 3).map(renderCourseCard)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                        lineNumber: 252,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n                lineNumber: 244,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\OfficialCourses.tsx\",\n        lineNumber: 236,\n        columnNumber: 5\n    }, undefined);\n};\n_s(OfficialCourses, \"VOinyAeif85qoTk+nommja+DwO8=\");\n_c = OfficialCourses;\n/* harmony default export */ __webpack_exports__[\"default\"] = (OfficialCourses);\nvar _c;\n$RefreshReg$(_c, \"OfficialCourses\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/workbench/components/OfficialCourses.tsx\n"));

/***/ })

});