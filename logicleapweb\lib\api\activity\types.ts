// 活动相关的详细类型定义

// 活动状态枚举
export enum ActivityStatus {
  DRAFT = 'draft',
  PUBLISHED = 'published', 
  ONGOING = 'ongoing',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled'
}

// 报名状态枚举
export enum RegistrationStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  CANCELLED = 'cancelled'
}

// 任务状态枚举
export enum TaskStatus {
  PENDING = 0,         // 待开始
  IN_PROGRESS = 1,     // 进行中
  SUBMITTED = 2,       // 已提交
  REVIEWED = 3,        // 已审核
  REJECTED = 4         // 审核不通过
}

// 活动标签
export interface ActivityTag {
  id: number
  name: string
  color: string
}

// 创建活动请求
export interface CreateActivityRequest {
  name: string
  description?: string
  startTime: string
  endTime: string
  registrationStartTime: string
  registrationEndTime: string
  maxParticipants?: number
  bannerImage?: string
  tags?: ActivityTag[]
  organizer?: string
  location?: string
  requirements?: string
  prizes?: string
  contactInfo?: string
}

// 更新活动请求
export interface UpdateActivityRequest extends Partial<CreateActivityRequest> {}

// 活动报名请求
export interface SubmitRegistrationRequest {
  activityId: number
  agreementAccepted: boolean
  parentConsentAccepted: boolean
  parentSignaturePath: string
  signatureTime?: string
  signatureIp?: string
  remark?: string
}

// 上传签名请求
export interface UploadSignatureRequest {
  signatureData: string
  activityId: number
}

// 添加作品到活动请求
export interface AddWorksToActivityRequest {
  works: {
    workId: number
    isAwarded?: boolean
    category?: string
    sort?: number
  }[]
}

// 设置获奖作品请求
export interface SetAwardedWorksRequest {
  workIds: number[]
}

// 审核报名请求
export interface ReviewRegistrationRequest {
  status: RegistrationStatus.APPROVED | RegistrationStatus.REJECTED
  reviewReason?: string
}

// 批量审核报名请求
export interface BatchReviewRequest extends ReviewRegistrationRequest {
  submitIds: number[]
}

// 创建赛事任务请求
export interface CreateEventsTaskRequest {
  userId: number
  eventName: string
  startTime: string
  endTime: string
  instructorName?: string
  schoolName?: string
  contactPerson?: string
  contactPhone?: string
  realName?: string
  idNumber?: string
  affiliatedSchool?: string
  organization?: string
  instructorPhone?: string
  competitionGroup?: string
  workId?: number
  workFile?: string
  workDescription?: string
  creatorId?: number
  remark?: string
}

// 更新赛事任务请求
export interface UpdateEventsTaskRequest extends Partial<Omit<CreateEventsTaskRequest, 'userId'>> {}

// 更新任务状态请求
export interface UpdateTaskStatusRequest {
  status: TaskStatus
}

// 批量更新任务状态请求
export interface BatchUpdateTaskStatusRequest {
  taskIds: number[]
  status: TaskStatus
}

// API响应基础结构
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}

// 分页响应
export interface PaginatedResponse<T> {
  list: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

// 分页请求参数
export interface PaginationParams {
  page?: number
  pageSize?: number
}

// 搜索参数
export interface SearchParams extends PaginationParams {
  keyword?: string
}

// 活动列表查询参数
export interface ActivityListParams extends SearchParams {
  status?: ActivityStatus
}

// 报名列表查询参数
export interface RegistrationListParams extends SearchParams {
  status?: RegistrationStatus
}

// 文件上传响应
export interface UploadResponse {
  url: string
  filename: string
  size: number
  mimeType: string
}
