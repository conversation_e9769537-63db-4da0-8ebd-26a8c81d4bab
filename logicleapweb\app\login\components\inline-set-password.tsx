'use client'

import { useState } from 'react';
import { Button, Form, Input, message } from 'antd';
import { Eye, EyeOff } from 'lucide-react';
import { COLORS } from './colors';
import userApi from '@/lib/api/user';

interface InlineSetPasswordProps {
  onSuccess: () => void;
  allowSkip?: boolean;
  phone?: string; // 保留phone作为可选参数以保持向后兼容
}

const InlineSetPassword: React.FC<InlineSetPasswordProps> = ({ 
  onSuccess, 
  allowSkip = false,
  phone
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const handleSubmit = async (values: any) => {
    try {
      if (values.password !== values.confirmPassword) {
        message.error('两次输入的密码不一致');
        return;
      }

      setLoading(true);
      // 从本地存储获取userId
      const userJson = localStorage.getItem('user');
      let userId = 0;
      if (userJson) {
        const userData = JSON.parse(userJson);
        userId = userData.userId || 0;
      }
      
      // 使用userId设置密码
      const response = await userApi.initPwd({
        userId: userId,
        password: values.password
      });
      
      if (response.data && response.data.code === 200) {
        message.success('密码设置成功');
        onSuccess();
      } else {
        message.error(response.data?.message || '密码设置失败');
      }
    } catch (error) {
      console.error('设置密码失败:', error);
      message.error('设置密码失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 跳过设置密码
  const handleSkip = async () => {
    try {
      setLoading(true);
      // 从本地存储获取userId
      const userJson = localStorage.getItem('user');
      let userId = 0;
      if (userJson) {
        const userData = JSON.parse(userJson);
        userId = userData.userId || 0;
      }
      
      // 设置默认密码 123456
      const response = await userApi.initPwd({
        userId: userId,
        password: '123456'
      });
      
      if (response.data && response.data.code === 200) {
        message.success('已为您设置默认密码: 123456');
        onSuccess();
      } else {
        message.error(response.data?.message || '密码设置失败');
      }
    } catch (error) {
      console.error('设置默认密码失败:', error);
      message.error('设置默认密码失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="w-full">
      <div className="text-center mb-6">
        <h2 className={`text-xl font-semibold ${COLORS.text.white}`}>设置密码</h2>
        <p className={`mt-2 text-sm ${COLORS.text.light_white}`}>设置密码可以更方便地登录账号</p>
      </div>
      
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        className="space-y-4"
      >
        <Form.Item
          name="password"
          rules={[
            { required: true, message: '请输入密码' },
            { min: 6, message: '密码长度不能少于6位' }
          ]}
        >
          <div className="relative">
            <Input 
              type={showPassword ? 'text' : 'password'}
              placeholder="请输入密码" 
              className={`h-12 ${COLORS.bg.white_dim} border ${COLORS.border.white_dim} rounded-xl ${COLORS.text.white}`}
            />
            <button 
              type="button" 
              className={`absolute right-3 top-1/2 -translate-y-1/2 ${COLORS.text.light_primary} hover:${COLORS.text.white}`}
              onClick={() => setShowPassword(!showPassword)}
            >
              {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
            </button>
          </div>
        </Form.Item>
        
        <Form.Item
          name="confirmPassword"
          rules={[
            { required: true, message: '请再次输入密码' },
            { min: 6, message: '密码长度不能少于6位' }
          ]}
        >
          <div className="relative">
            <Input 
              type={showConfirmPassword ? 'text' : 'password'}
              placeholder="请再次输入密码" 
              className={`h-12 ${COLORS.bg.white_dim} border ${COLORS.border.white_dim} rounded-xl ${COLORS.text.white}`}
            />
            <button 
              type="button" 
              className={`absolute right-3 top-1/2 -translate-y-1/2 ${COLORS.text.light_primary} hover:${COLORS.text.white}`}
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
            >
              {showConfirmPassword ? <EyeOff size={18} /> : <Eye size={18} />}
            </button>
          </div>
        </Form.Item>
        
        <div className="flex space-x-4 pt-2">
          <Button
            type="primary"
            htmlType="submit"
            loading={loading}
            className={`h-12 w-full ${COLORS.bg.accent} ${COLORS.text.white} rounded-xl ${COLORS.bg.accent_hover} border-0`}
          >
            确认设置
          </Button>
          
          {allowSkip && (
            <Button 
              onClick={handleSkip}
              className="h-12 bg-transparent border border-white/30 text-white rounded-xl hover:bg-white/10"
            >
              使用默认密码
            </Button>
          )}
        </div>
      </Form>
    </div>
  );
};

export default InlineSetPassword; 