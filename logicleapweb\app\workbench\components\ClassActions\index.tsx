import React, { useState, useRef, useEffect } from 'react';
import { Plus, Settings, ChevronDown, Blocks, Zap, Trash2, Download } from 'lucide-react';
import { ClassActionsProps } from '../types';

const ClassActions: React.FC<ClassActionsProps> = ({
  classInfo,
  selectedStudentIds,
  onAddStudent,
  onSelectAll,
  onBatchAction,
  onSettingsMenuItemClick
}) => {
  const [isSettingsDropdownOpen, setIsSettingsDropdownOpen] = useState(false);
  const [isBatchActionsDropdownOpen, setIsBatchActionsDropdownOpen] = useState(false);
  const settingsDropdownRef = useRef<HTMLDivElement>(null);
  const batchActionsDropdownRef = useRef<HTMLDivElement>(null);

  // 点击外部关闭下拉菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (settingsDropdownRef.current && !settingsDropdownRef.current.contains(event.target as Node)) {
        setIsSettingsDropdownOpen(false);
      }
      if (batchActionsDropdownRef.current && !batchActionsDropdownRef.current.contains(event.target as Node)) {
        setIsBatchActionsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className="class-actions">
      <div className="actions-left">
        <h2>学生</h2>
        <button
          className="add-student-btn"
          onClick={onAddStudent}
          title="添加学生"
        >
          <Plus size={16} />
        </button>
      </div>

      <div className="actions-right">
        <button
          className="select-all-btn"
          onClick={onSelectAll}
        >
          全选
        </button>

        {/* 批量操作按钮 */}
        {selectedStudentIds.length > 0 && (
          <div className="batch-actions-dropdown" ref={batchActionsDropdownRef}>
            <button
              className="batch-actions-btn"
              onClick={() => setIsBatchActionsDropdownOpen(!isBatchActionsDropdownOpen)}
            >
              批量操作 ({selectedStudentIds.length})
              <ChevronDown size={16} />
            </button>
            {isBatchActionsDropdownOpen && (
              <div className="batch-actions-menu">
                <button
                  className="batch-action-item"
                  onClick={() => onBatchAction('batchAssignBlocks')}
                >
                  <Blocks size={16} />
                  <span>批量分配积木</span>
                </button>
                <button
                  className="batch-action-item"
                  onClick={() => onBatchAction('batchAssignPoints')}
                >
                  <Zap size={16} />
                  <span>批量分配能量</span>
                </button>
                <button
                  className="batch-action-item"
                  onClick={() => onBatchAction('batchDelete')}
                >
                  <Trash2 size={16} />
                  <span>批量移出班级</span>
                </button>
                <button
                  className="batch-action-item"
                  onClick={() => onBatchAction('batchExport')}
                >
                  <Download size={16} />
                  <span>批量导出</span>
                </button>
              </div>
            )}
          </div>
        )}

        {/* 设置下拉菜单 */}
        <div className="settings-dropdown" ref={settingsDropdownRef}>
          <button
            className="settings-btn"
            onClick={() => setIsSettingsDropdownOpen(!isSettingsDropdownOpen)}
            title="设置"
          >
            <Settings size={16} />
          </button>
          {isSettingsDropdownOpen && (
            <div className="settings-dropdown-menu">
              <div
                className="dropdown-menu-item"
                onClick={() => onSettingsMenuItemClick('editClass')}
              >
                <div className="dropdown-menu-item-icon">
                  <Settings size={16} style={{ color: '#6b7280' }} />
                </div>
                <span>编辑班级</span>
              </div>

              <div
                className="dropdown-menu-item"
                onClick={() => onSettingsMenuItemClick('importStudents')}
              >
                <div className="dropdown-menu-item-icon">
                  <Download size={16} style={{ color: '#10b981' }} />
                </div>
                <span>导入学生</span>
              </div>

              <div
                className="dropdown-menu-item"
                onClick={() => onSettingsMenuItemClick('generateInviteCode')}
              >
                <div className="dropdown-menu-item-icon">
                  <Plus size={16} style={{ color: '#f59e0b' }} />
                </div>
                <span>生成邀请码</span>
              </div>

              <div
                className="dropdown-menu-item"
                onClick={() => onSettingsMenuItemClick('assignBlocks')}
              >
                <div className="dropdown-menu-item-icon">
                  <Blocks size={16} style={{ color: '#3b82f6' }} />
                </div>
                <span>分配积木</span>
              </div>

              <div className="dropdown-menu-divider"></div>

              <div
                className="dropdown-menu-item"
                onClick={() => onSettingsMenuItemClick('transferClass')}
              >
                <div className="dropdown-menu-item-icon">
                  <Settings size={16} style={{ color: '#ef4444' }} />
                </div>
                <span>转让管理</span>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ClassActions;
