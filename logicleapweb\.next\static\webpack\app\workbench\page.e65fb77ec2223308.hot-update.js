"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/workbench/page",{

/***/ "(app-pages-browser)/./app/workbench/components/CourseListEditModal.tsx":
/*!**********************************************************!*\
  !*** ./app/workbench/components/CourseListEditModal.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FileText,Plus,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FileText,Plus,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FileText,Plus,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FileText,Plus,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FileText,Plus,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _lib_api_course_management__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api/course-management */ \"(app-pages-browser)/./lib/api/course-management.ts\");\n/* harmony import */ var _lib_api_course__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api/course */ \"(app-pages-browser)/./lib/api/course.ts\");\n/* harmony import */ var _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api/upload */ \"(app-pages-browser)/./lib/api/upload.ts\");\n/* harmony import */ var _lib_api_works__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/api/works */ \"(app-pages-browser)/./lib/api/works.ts\");\n/* harmony import */ var _barrel_optimize_names_Select_antd__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Select!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/select/index.js\");\n/* harmony import */ var logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\");\n/* harmony import */ var _TemplatePickerModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./TemplatePickerModal */ \"(app-pages-browser)/./app/workbench/components/TemplatePickerModal.tsx\");\n/* harmony import */ var _CourseListEditModal_css__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./CourseListEditModal.css */ \"(app-pages-browser)/./app/workbench/components/CourseListEditModal.css\");\n/* harmony import */ var _NewPublishTaskModal_css__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./NewPublishTaskModal.css */ \"(app-pages-browser)/./app/workbench/components/NewPublishTaskModal.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n// 获取系列课程详情\nconst fetchSeriesDetail = async (seriesId)=>{\n    console.log(\"\\uD83D\\uDD0D 获取系列课程详情，seriesId:\", seriesId);\n    console.log(\"\\uD83D\\uDD17 调用API: GET /api/v1/course-management/series/{seriesId}\");\n    const response = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getSeriesDetail(seriesId);\n    console.log(\"\\uD83D\\uDCE1 系列详情API响应:\", response);\n    return response.data;\n};\n// API调用函数 - 获取系列下的所有课程（包含未发布和已发布）\nconst fetchCourseList = async (seriesId)=>{\n    console.log(\"\\uD83D\\uDD0D 获取系列课程列表，seriesId:\", seriesId);\n    console.log(\"\\uD83D\\uDD17 调用API: GET /api/v1/course-management/series/{seriesId}/courses\");\n    // 使用课程管理API获取所有状态的课程\n    const response = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getManagementSeriesCourses(seriesId, {\n        page: 1,\n        pageSize: 100\n    });\n    console.log(\"\\uD83D\\uDCE1 API响应:\", response);\n    return response.data;\n};\n// 获取课程详情\nconst fetchCourseDetail = async (seriesId, courseId)=>{\n    return await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getCourseMarketplaceDetail(seriesId, courseId);\n};\n// 获取课程标签\nconst fetchCourseTags = async ()=>{\n    try {\n        console.log(\"\\uD83D\\uDD0D 开始调用 courseApi.getCourseTags\");\n        const result = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getCourseTags({\n            page: 1,\n            pageSize: 100,\n            status: 1 // 只获取启用的标签\n        });\n        console.log(\"\\uD83D\\uDD0D courseApi.getCourseTags 返回结果:\", result);\n        return result;\n    } catch (error) {\n        console.error(\"\\uD83D\\uDD0D courseApi.getCourseTags 调用失败:\", error);\n        throw error;\n    }\n};\nconst CourseListEditModal = (param)=>{\n    let { isVisible, onClose, onSave, seriesTitle, seriesCoverImage, seriesId = 123 // 默认值，实际使用时应该传入真实的seriesId\n     } = param;\n    var _getSelectedCourse, _getSelectedCourse1, _getSelectedCourse2, _getSelectedCourse3, _courseDetail_contentConfig_video, _courseDetail_contentConfig, _courseDetail_contentConfig_document, _courseDetail_contentConfig1, _courseList_find, _courseList_find1;\n    _s();\n    const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_6__.GetNotification)();\n    const [courseList, setCourseList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [rightPanelType, setRightPanelType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"none\");\n    const [selectedCourseId, setSelectedCourseId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [uploadingFiles, setUploadingFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [isPublishing, setIsPublishing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isCreating, setIsCreating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPublishingSeries, setIsPublishingSeries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [seriesStatus, setSeriesStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0); // 0=草稿，1=已发布，2=已归档\n    // 删除确认弹窗状态\n    const [deleteConfirmVisible, setDeleteConfirmVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [courseToDelete, setCourseToDelete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isDeleting, setIsDeleting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 模板选择弹窗状态\n    const [isTemplatePickerOpen, setIsTemplatePickerOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingTitle, setEditingTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(seriesTitle);\n    const [courseGoals, setCourseGoals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [courseObjectives, setCourseObjectives] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [projectMembers, setProjectMembers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // 作品相关状态\n    const [works, setWorks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loadingWorks, setLoadingWorks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedWorkIds, setSelectedWorkIds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // 能量输入验证状态\n    const [energyError, setEnergyError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // 时间选择状态\n    const [showDurationPicker, setShowDurationPicker] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 课程标签相关状态\n    const [courseTags, setCourseTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedTags, setSelectedTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [tagsLoading, setTagsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 课程详细编辑状态\n    const [courseDetail, setCourseDetail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: \"\",\n        description: \"\",\n        coverImage: \"\",\n        videoUrl: \"\",\n        videoName: \"\",\n        isVideoEnabled: false,\n        attachmentUrl: \"\",\n        attachmentName: \"\",\n        isAttachmentEnabled: false,\n        teachingMaterials: [],\n        // 支持teachingInfo结构\n        teachingInfo: [],\n        // 支持contentConfig结构\n        contentConfig: {\n            hasVideo: 0,\n            hasDocument: 0,\n            hasAudio: 0,\n            video: {\n                url: \"\",\n                name: \"\"\n            },\n            document: {\n                url: \"\",\n                name: \"\"\n            },\n            audio: {\n                url: \"\",\n                name: \"\"\n            }\n        },\n        courseContent: {\n            topic: \"\",\n            content: \"\"\n        },\n        isOneKeyOpen: false,\n        isDistributionEnabled: false,\n        distributionReward: \"\",\n        selectedTemplate: {\n            id: null,\n            name: \"\"\n        },\n        isDistributionWater: false,\n        requiredEnergy: \"\",\n        energyAmount: \"\",\n        isDistributionLimit: false,\n        distributionConditions: {\n            inviteCount: \"\",\n            taskCount: \"\",\n            experience: \"\"\n        },\n        isDistributionTime: false,\n        distributionTimeConditions: {\n            startTime: \"\",\n            endTime: \"\"\n        },\n        distributionMaterials: [],\n        // 任务配置相关状态\n        taskConfig: {\n            taskName: \"\",\n            taskDuration: \"\",\n            taskDescription: \"\",\n            selfAssessmentItems: [\n                \"\"\n            ],\n            referenceWorks: [],\n            referenceResources: []\n        }\n    });\n    // 获取课程列表数据\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isVisible && seriesId) {\n            // 检查用户登录状态\n            const token = localStorage.getItem(\"token\");\n            console.log(\"\\uD83D\\uDD10 检查登录状态，token存在:\", !!token);\n            console.log(\"\\uD83D\\uDD0D seriesId:\", seriesId);\n            if (!token) {\n                console.error(\"❌ 用户未登录，无法获取课程列表\");\n                // 设置空列表，显示空状态\n                setCourseList([]);\n                setLoading(false);\n                return;\n            }\n            loadCourseList();\n            loadCourseTags();\n            loadSeriesDetail();\n            loadUserWorks();\n        }\n    }, [\n        isVisible,\n        seriesId\n    ]);\n    const loadCourseList = async ()=>{\n        try {\n            setLoading(true);\n            console.log(\"\\uD83D\\uDD0D 开始加载课程列表，seriesId:\", seriesId);\n            const response = await fetchCourseList(seriesId);\n            console.log(\"\\uD83D\\uDCE1 API响应:\", response);\n            if (response.code === 200) {\n                console.log(\"✅ 课程列表数据:\", response.data);\n                const courses = response.data.list || [];\n                console.log(\"✅ 解析的课程数组:\", courses);\n                console.log(\"\\uD83D\\uDCCA 课程ID详情:\", courses.map((c)=>({\n                        id: c.id,\n                        type: typeof c.id,\n                        title: c.title,\n                        status: c.status\n                    })));\n                console.log(\"\\uD83D\\uDCCA 课程状态统计:\", {\n                    total: courses.length,\n                    draft: courses.filter((c)=>c.status === 0).length,\n                    published: courses.filter((c)=>c.status === 1).length\n                });\n                setCourseList(courses);\n            } else {\n                console.error(\"❌ API返回错误:\", response);\n                setCourseList([]);\n            }\n        } catch (error) {\n            var _error_response, _error_response1, _error_response2;\n            console.error(\"❌ 加载课程列表失败:\", error);\n            // 检查是否是认证错误\n            if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 401) {\n                console.error(\"\\uD83D\\uDD10 认证失败，用户未登录或token已过期\");\n            } else if (((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status) === 403) {\n                console.error(\"\\uD83D\\uDEAB 权限不足，无法访问该系列课程\");\n            } else if (((_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.status) === 404) {\n                console.error(\"\\uD83D\\uDCED 系列课程不存在，seriesId:\", seriesId);\n            } else {\n                console.error(\"\\uD83D\\uDD27 其他错误:\", error.message);\n            }\n            setCourseList([]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 加载系列课程详情\n    const loadSeriesDetail = async ()=>{\n        try {\n            console.log(\"\\uD83D\\uDD0D 开始加载系列课程详情，seriesId:\", seriesId);\n            const response = await fetchSeriesDetail(seriesId);\n            console.log(\"\\uD83D\\uDCE1 系列详情响应:\", response);\n            if (response.code === 200 && response.data) {\n                const seriesData = response.data;\n                console.log(\"✅ 系列课程详情:\", seriesData);\n                setSeriesStatus(seriesData.status || 0);\n                console.log(\"\\uD83D\\uDCCA 系列课程状态:\", seriesData.status, \"(0=草稿，1=已发布，2=已归档)\");\n            } else {\n                console.error(\"❌ 获取系列详情失败:\", response.message);\n            }\n        } catch (error) {\n            console.error(\"❌ 加载系列详情异常:\", error);\n        }\n    };\n    // 加载用户作品数据\n    const loadUserWorks = async ()=>{\n        try {\n            var _response_data;\n            setLoadingWorks(true);\n            // 从localStorage获取用户ID\n            const userId = localStorage.getItem(\"userId\") || \"2896\"; // 默认使用2896\n            const response = await _lib_api_works__WEBPACK_IMPORTED_MODULE_5__.worksApi.getTeacherWorks(Number(userId), 1, 1000);\n            // 检查多种可能的数据结构\n            let worksList = [];\n            if (((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.code) === 200) {\n                var _response_data_data, _response_data1, _response_data2;\n                // 情况1: response.data.data.list\n                if ((_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : (_response_data_data = _response_data1.data) === null || _response_data_data === void 0 ? void 0 : _response_data_data.list) {\n                    worksList = response.data.data.list;\n                } else if (Array.isArray((_response_data2 = response.data) === null || _response_data2 === void 0 ? void 0 : _response_data2.data)) {\n                    worksList = response.data.data;\n                } else if (Array.isArray(response.data)) {\n                    worksList = response.data;\n                }\n            } else if (Array.isArray(response)) {\n                worksList = response;\n            }\n            setWorks(worksList);\n        } catch (error) {\n            console.error(\"加载作品数据失败:\", error);\n            setWorks([]);\n        } finally{\n            setLoadingWorks(false);\n        }\n    };\n    // 处理作品选择\n    const handleWorkSelect = (workId)=>{\n        if (selectedWorkIds.includes(workId)) {\n            // 取消选中\n            setSelectedWorkIds((prev)=>prev.filter((id)=>id !== workId));\n        } else {\n            // 选中\n            setSelectedWorkIds((prev)=>[\n                    ...prev,\n                    workId\n                ]);\n        }\n    };\n    // 鼠标滚轮事件处理 - 将垂直滚轮转换为水平滚动\n    const handleWheel = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e)=>{\n        const container = e.currentTarget;\n        const { scrollWidth, clientWidth } = container;\n        // 检查是否可以滚动\n        if (scrollWidth <= clientWidth) {\n            return; // 内容不够长，不需要滚动\n        }\n        // 阻止默认的垂直滚动行为\n        e.preventDefault();\n        // 将垂直滚轮转换为水平滚动\n        container.scrollLeft += e.deltaY;\n    }, []);\n    // 处理能量输入变化\n    const handleEnergyChange = (value)=>{\n        setCourseDetail((prev)=>({\n                ...prev,\n                requiredEnergy: value\n            }));\n        // 验证输入值\n        const numValue = parseFloat(value);\n        if (value && (isNaN(numValue) || numValue < 0)) {\n            setEnergyError(\"分配能力值不能为负数\");\n        } else {\n            setEnergyError(\"\");\n        }\n    };\n    // 处理时间选择\n    const handleDurationSelect = (hours, label)=>{\n        setCourseDetail((prev)=>({\n                ...prev,\n                taskConfig: {\n                    ...prev.taskConfig,\n                    taskDuration: \"\".concat(hours, \"小时 (\").concat(label, \")\")\n                }\n            }));\n        setShowDurationPicker(false);\n    };\n    // 点击外部关闭时间选择器\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleClickOutside = (event)=>{\n            const target = event.target;\n            if (!target.closest(\".duration-input-wrapper\")) {\n                setShowDurationPicker(false);\n            }\n        };\n        if (showDurationPicker) {\n            document.addEventListener(\"mousedown\", handleClickOutside);\n            return ()=>document.removeEventListener(\"mousedown\", handleClickOutside);\n        }\n    }, [\n        showDurationPicker\n    ]);\n    // 加载课程标签\n    const loadCourseTags = async ()=>{\n        try {\n            setTagsLoading(true);\n            console.log(\"\\uD83C\\uDFF7️ 开始加载课程标签\");\n            const response = await fetchCourseTags();\n            console.log(\"\\uD83D\\uDCE1 标签API完整响应:\", response);\n            // 检查响应结构\n            if (response && response.data) {\n                console.log(\"\\uD83D\\uDCCA 响应数据:\", response.data);\n                let tags = [];\n                // 处理标准的API响应格式 (response.data.list) - 根据实际API响应\n                if (response.data.list && Array.isArray(response.data.list)) {\n                    tags = response.data.list;\n                    console.log(\"✅ 从 data.list 解析到标签:\", tags.length, \"个\");\n                } else if (Array.isArray(response.data)) {\n                    tags = response.data;\n                    console.log(\"✅ 从 data 数组解析到标签:\", tags.length, \"个\");\n                } else if (response.data.data && response.data.data.list && Array.isArray(response.data.data.list)) {\n                    tags = response.data.data.list;\n                    console.log(\"✅ 从 data.data.list 解析到标签:\", tags.length, \"个\");\n                }\n                // 验证标签数据格式\n                console.log(\"\\uD83D\\uDD0D 原始标签数据:\", tags);\n                console.log(\"\\uD83D\\uDD0D 标签数据类型检查:\");\n                tags.forEach((tag, index)=>{\n                    var _tag_name;\n                    console.log(\"标签\".concat(index, \":\"), {\n                        tag,\n                        hasTag: !!tag,\n                        idType: typeof (tag === null || tag === void 0 ? void 0 : tag.id),\n                        nameType: typeof (tag === null || tag === void 0 ? void 0 : tag.name),\n                        nameValue: tag === null || tag === void 0 ? void 0 : tag.name,\n                        nameNotEmpty: (tag === null || tag === void 0 ? void 0 : (_tag_name = tag.name) === null || _tag_name === void 0 ? void 0 : _tag_name.trim()) !== \"\"\n                    });\n                });\n                const validTags = tags.filter((tag)=>{\n                    const isValid = tag && typeof tag.id === \"number\" && typeof tag.name === \"string\" && tag.name.trim() !== \"\";\n                    if (!isValid) {\n                        console.log(\"❌ 无效标签:\", tag, {\n                            hasTag: !!tag,\n                            idType: typeof (tag === null || tag === void 0 ? void 0 : tag.id),\n                            nameType: typeof (tag === null || tag === void 0 ? void 0 : tag.name),\n                            nameValue: tag === null || tag === void 0 ? void 0 : tag.name\n                        });\n                    }\n                    return isValid;\n                });\n                console.log(\"✅ 有效标签数量:\", validTags.length);\n                console.log(\"✅ 有效标签详情:\", validTags);\n                if (validTags.length > 0) {\n                    setCourseTags(validTags);\n                    console.log(\"✅ 成功设置真实标签数据\");\n                    return;\n                } else {\n                    console.warn(\"⚠️ 没有有效的标签数据\");\n                }\n            } else {\n                console.warn(\"⚠️ API响应格式不正确:\", response);\n            }\n            // 如果没有真实数据，设置空数组\n            console.log(\"\\uD83D\\uDCED 没有标签数据，设置空数组\");\n            setCourseTags([]);\n        } catch (error) {\n            var _error_response, _error_response1, _error_response2;\n            console.error(\"❌ 加载课程标签失败:\", error);\n            console.error(\"❌ 错误详情:\", {\n                message: error.message,\n                status: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status,\n                statusText: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.statusText,\n                data: (_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.data\n            });\n            // 发生错误时设置空数组\n            setCourseTags([]);\n        } finally{\n            setTagsLoading(false);\n        }\n    };\n    // 添加新课程\n    const addNewCourse = ()=>{\n        const newCourse = {\n            id: Date.now(),\n            seriesId: seriesId,\n            title: \"第\".concat(courseList.length + 1, \"课 - 新课时\"),\n            description: \"\",\n            coverImage: \"\",\n            orderIndex: courseList.length + 1,\n            status: 0,\n            statusLabel: \"草稿\",\n            hasVideo: 0,\n            hasDocument: 0,\n            hasAudio: 0,\n            videoDuration: 0,\n            videoDurationLabel: \"\",\n            videoName: \"\",\n            firstTeachingTitle: \"\",\n            resourcesCount: 0,\n            createdAt: new Date().toISOString(),\n            updatedAt: new Date().toISOString()\n        };\n        setCourseList([\n            ...courseList,\n            newCourse\n        ]);\n        // 自动选中新添加的课程\n        showCoursePanel(newCourse.id);\n    };\n    // 显示删除确认弹窗\n    const showDeleteConfirm = (id)=>{\n        setCourseToDelete(id);\n        setDeleteConfirmVisible(true);\n    };\n    // 确认删除课程\n    const confirmDeleteCourse = async ()=>{\n        if (!courseToDelete) return;\n        try {\n            setIsDeleting(true);\n            // 调用删除API\n            await _lib_api_course_management__WEBPACK_IMPORTED_MODULE_2__.courseManagementApi.deleteCourse(courseToDelete);\n            // 从列表中移除课程\n            setCourseList(courseList.filter((course)=>course.id !== courseToDelete));\n            // 如果删除的是当前选中的课程，清空右侧面板\n            if (selectedCourseId === courseToDelete) {\n                setRightPanelType(\"none\");\n                setSelectedCourseId(null);\n            }\n            // 关闭确认弹窗\n            setDeleteConfirmVisible(false);\n            setCourseToDelete(null);\n            // 显示成功提示\n            notification.success(\"课程已成功删除\");\n        } catch (error) {\n            console.error(\"删除课程失败:\", error);\n            notification.error(\"删除课程失败，请重试\");\n        } finally{\n            setIsDeleting(false);\n        }\n    };\n    // 取消删除\n    const cancelDelete = ()=>{\n        if (isDeleting) return; // 正在删除时不允许取消\n        setDeleteConfirmVisible(false);\n        setCourseToDelete(null);\n    };\n    // 更新课程标题\n    const updateCourseTitle = (id, newTitle)=>{\n        setCourseList(courseList.map((course)=>course.id === id ? {\n                ...course,\n                title: newTitle\n            } : course));\n    };\n    // 处理课程封面上传\n    const handleCoverUpload = async (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (file) {\n            // 检查文件类型\n            const allowedTypes = [\n                \"image/jpeg\",\n                \"image/jpg\",\n                \"image/png\",\n                \"image/gif\"\n            ];\n            if (!allowedTypes.includes(file.type)) {\n                notification.error(\"请选择 JPG、PNG 或 GIF 格式的图片文件\");\n                return;\n            }\n            // 检查文件大小 (10MB)\n            if (file.size > 10 * 1024 * 1024) {\n                notification.error(\"文件大小不能超过 10MB\");\n                return;\n            }\n            try {\n                console.log(\"\\uD83D\\uDCE4 开始上传课程封面:\", file.name);\n                // 添加到上传中的文件列表\n                setUploadingFiles((prev)=>new Set(prev).add(\"cover\"));\n                // 先显示预览图片\n                const previewUrl = URL.createObjectURL(file);\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        coverImage: previewUrl\n                    }));\n                // 上传到OSS\n                const imageUrl = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n                console.log(\"✅ 课程封面上传成功:\", imageUrl);\n                // 更新课程详情中的封面为真实URL\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        coverImage: imageUrl\n                    }));\n                // 同时更新课程列表中的封面\n                if (selectedCourseId) {\n                    setCourseList((prev)=>prev.map((course)=>course.id === selectedCourseId ? {\n                                ...course,\n                                coverImage: imageUrl\n                            } : course));\n                }\n                alert(\"课程封面上传成功！\");\n            } catch (error) {\n                console.error(\"❌ 课程封面上传失败:\", error);\n                notification.error(\"课程封面上传失败，请重试\");\n                // 上传失败时清除预览图片\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        coverImage: \"\"\n                    }));\n            } finally{\n                // 从上传中的文件列表移除\n                setUploadingFiles((prev)=>{\n                    const newSet = new Set(prev);\n                    newSet.delete(\"cover\");\n                    return newSet;\n                });\n            }\n        }\n    };\n    // 处理视频上传\n    const handleVideoUpload = async (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (file) {\n            // 检查文件类型\n            const allowedTypes = [\n                \"video/mp4\",\n                \"video/avi\",\n                \"video/mov\",\n                \"video/wmv\",\n                \"video/flv\"\n            ];\n            if (!allowedTypes.includes(file.type)) {\n                alert(\"请选择 MP4、AVI、MOV、WMV 或 FLV 格式的视频文件\");\n                return;\n            }\n            // 检查文件大小 (100MB)\n            if (file.size > 100 * 1024 * 1024) {\n                alert(\"视频文件大小不能超过 100MB\");\n                return;\n            }\n            try {\n                console.log(\"\\uD83D\\uDCE4 开始上传课程视频:\", file.name);\n                // 添加到上传中的文件列表\n                setUploadingFiles((prev)=>new Set(prev).add(\"video\"));\n                // 先显示预览视频\n                const previewUrl = URL.createObjectURL(file);\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        contentConfig: {\n                            ...prev.contentConfig,\n                            video: {\n                                url: previewUrl,\n                                name: file.name\n                            }\n                        }\n                    }));\n                // 上传到OSS\n                const videoUrl = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n                console.log(\"✅ 课程视频上传成功:\", videoUrl);\n                // 更新课程详情中的视频信息为真实URL\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        contentConfig: {\n                            ...prev.contentConfig,\n                            video: {\n                                url: videoUrl,\n                                name: file.name\n                            }\n                        }\n                    }));\n                // 同时更新课程列表中的视频信息\n                if (selectedCourseId) {\n                    setCourseList((prev)=>prev.map((course)=>course.id === selectedCourseId ? {\n                                ...course,\n                                contentConfig: {\n                                    ...course.contentConfig,\n                                    video: {\n                                        url: videoUrl,\n                                        name: file.name\n                                    }\n                                }\n                            } : course));\n                }\n                alert(\"课程视频上传成功！\");\n            } catch (error) {\n                console.error(\"❌ 课程视频上传失败:\", error);\n                alert(\"课程视频上传失败，请重试\");\n                // 上传失败时清除视频信息\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        contentConfig: {\n                            ...prev.contentConfig,\n                            video: {\n                                url: \"\",\n                                name: \"\"\n                            }\n                        }\n                    }));\n            } finally{\n                // 从上传中的文件列表移除\n                setUploadingFiles((prev)=>{\n                    const newSet = new Set(prev);\n                    newSet.delete(\"video\");\n                    return newSet;\n                });\n            }\n        }\n    };\n    // 触发视频文件选择\n    const triggerVideoUpload = ()=>{\n        const input = document.createElement(\"input\");\n        input.type = \"file\";\n        input.accept = \"video/mp4,video/avi,video/mov,video/wmv,video/flv\";\n        input.onchange = (e)=>handleVideoUpload(e);\n        input.click();\n    };\n    // 处理附件上传\n    const handleAttachmentUpload = async (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (file) {\n            // 检查文件类型\n            const allowedTypes = [\n                \"application/pdf\",\n                \"application/msword\",\n                \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\",\n                \"application/vnd.ms-excel\",\n                \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\",\n                \"application/vnd.ms-powerpoint\",\n                \"application/vnd.openxmlformats-officedocument.presentationml.presentation\",\n                \"text/plain\",\n                \"image/jpeg\",\n                \"image/png\",\n                \"image/gif\"\n            ];\n            if (!allowedTypes.includes(file.type)) {\n                alert(\"请选择支持的文件格式：PDF、DOC、DOCX、XLS、XLSX、PPT、PPTX、TXT、JPG、PNG、GIF\");\n                return;\n            }\n            // 检查文件大小 (10MB)\n            if (file.size > 10 * 1024 * 1024) {\n                alert(\"文件大小不能超过 10MB\");\n                return;\n            }\n            try {\n                console.log(\"\\uD83D\\uDCE4 开始上传课程附件:\", file.name);\n                // 添加到上传中的文件列表\n                setUploadingFiles((prev)=>new Set(prev).add(\"document\"));\n                // 上传到OSS\n                const documentUrl = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n                console.log(\"✅ 课程附件上传成功:\", documentUrl);\n                // 更新课程详情中的附件信息\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        contentConfig: {\n                            ...prev.contentConfig,\n                            hasDocument: 1,\n                            document: {\n                                url: documentUrl,\n                                name: file.name\n                            }\n                        }\n                    }));\n                alert(\"课程附件上传成功！\");\n            } catch (error) {\n                console.error(\"❌ 课程附件上传失败:\", error);\n                alert(\"课程附件上传失败，请重试\");\n            } finally{\n                // 从上传中的文件列表移除\n                setUploadingFiles((prev)=>{\n                    const newSet = new Set(prev);\n                    newSet.delete(\"document\");\n                    return newSet;\n                });\n            }\n        }\n    };\n    // 触发附件文件选择\n    const triggerAttachmentUpload = ()=>{\n        const input = document.createElement(\"input\");\n        input.type = \"file\";\n        input.accept = \".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.jpg,.png,.gif\";\n        input.onchange = (e)=>handleAttachmentUpload(e);\n        input.click();\n    };\n    // 处理教学附件上传\n    const handleTeachingMaterialUpload = async (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (file) {\n            // 检查文件类型\n            const allowedTypes = [\n                \"application/pdf\",\n                \"application/msword\",\n                \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\",\n                \"application/vnd.ms-excel\",\n                \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\",\n                \"application/vnd.ms-powerpoint\",\n                \"application/vnd.openxmlformats-officedocument.presentationml.presentation\",\n                \"text/plain\",\n                \"image/jpeg\",\n                \"image/png\",\n                \"image/gif\"\n            ];\n            if (!allowedTypes.includes(file.type)) {\n                alert(\"请选择支持的文件格式：PDF、DOC、DOCX、XLS、XLSX、PPT、PPTX、TXT、JPG、PNG、GIF\");\n                return;\n            }\n            // 检查文件大小 (10MB)\n            if (file.size > 10 * 1024 * 1024) {\n                alert(\"文件大小不能超过 10MB\");\n                return;\n            }\n            try {\n                console.log(\"\\uD83D\\uDCE4 开始上传教学材料:\", file.name);\n                // 添加到上传中的文件列表\n                setUploadingFiles((prev)=>new Set(prev).add(\"teaching-\".concat(Date.now())));\n                // 上传到OSS\n                const materialUrl = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n                console.log(\"✅ 教学材料上传成功:\", materialUrl);\n                // 添加到教学附件列表\n                const newMaterial = {\n                    type: file.type,\n                    name: file.name,\n                    url: materialUrl\n                };\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        teachingMaterials: [\n                            ...prev.teachingMaterials,\n                            newMaterial\n                        ]\n                    }));\n                alert(\"教学材料上传成功！\");\n            } catch (error) {\n                console.error(\"❌ 教学材料上传失败:\", error);\n                alert(\"教学材料上传失败，请重试\");\n            } finally{\n                // 从上传中的文件列表移除\n                setUploadingFiles((prev)=>{\n                    const newSet = new Set(prev);\n                    // 移除所有teaching-开头的项目\n                    Array.from(newSet).forEach((item)=>{\n                        if (item.startsWith(\"teaching-\")) {\n                            newSet.delete(item);\n                        }\n                    });\n                    return newSet;\n                });\n            }\n        }\n    };\n    // 触发教学附件文件选择\n    const triggerTeachingMaterialUpload = ()=>{\n        const input = document.createElement(\"input\");\n        input.type = \"file\";\n        input.accept = \".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.jpg,.png,.gif\";\n        input.onchange = (e)=>handleTeachingMaterialUpload(e);\n        input.click();\n    };\n    // 删除教学附件\n    const removeTeachingMaterial = (index)=>{\n        setCourseDetail((prev)=>({\n                ...prev,\n                teachingMaterials: prev.teachingMaterials.filter((_, i)=>i !== index)\n            }));\n    };\n    // 课程内容管理函数\n    const addTeachingInfoItem = ()=>{\n        setCourseDetail((prev)=>({\n                ...prev,\n                teachingInfo: [\n                    ...prev.teachingInfo,\n                    {\n                        title: \"\",\n                        content: \"\"\n                    }\n                ]\n            }));\n    };\n    const removeTeachingInfoItem = (index)=>{\n        setCourseDetail((prev)=>({\n                ...prev,\n                teachingInfo: prev.teachingInfo.filter((_, i)=>i !== index)\n            }));\n    };\n    const updateTeachingInfoTitle = (index, title)=>{\n        setCourseDetail((prev)=>{\n            const newTeachingInfo = [\n                ...prev.teachingInfo\n            ];\n            newTeachingInfo[index] = {\n                ...newTeachingInfo[index],\n                title\n            };\n            return {\n                ...prev,\n                teachingInfo: newTeachingInfo\n            };\n        });\n    };\n    const updateTeachingInfoContent = (index, content)=>{\n        setCourseDetail((prev)=>{\n            const newTeachingInfo = [\n                ...prev.teachingInfo\n            ];\n            newTeachingInfo[index] = {\n                ...newTeachingInfo[index],\n                content\n            };\n            return {\n                ...prev,\n                teachingInfo: newTeachingInfo\n            };\n        });\n    };\n    // 将UI格式的teachingInfo转换为API格式\n    const convertTeachingInfoForAPI = (teachingInfo)=>{\n        return teachingInfo.map((info)=>({\n                title: info.title,\n                content: info.content.split(\"\\n\").filter((line)=>line.trim()) // 按换行分割并过滤空行\n            }));\n    };\n    // 检查是否有未上传完成的文件（blob URL）\n    const checkForBlobUrls = (course)=>{\n        var _course_contentConfig_video, _course_contentConfig, _course_contentConfig_document, _course_contentConfig1;\n        const issues = [];\n        // 检查封面图片\n        if (course.coverImage && course.coverImage.startsWith(\"blob:\")) {\n            issues.push(\"课程封面图片\");\n        }\n        // 检查视频文件\n        if (((_course_contentConfig = course.contentConfig) === null || _course_contentConfig === void 0 ? void 0 : (_course_contentConfig_video = _course_contentConfig.video) === null || _course_contentConfig_video === void 0 ? void 0 : _course_contentConfig_video.url) && course.contentConfig.video.url.startsWith(\"blob:\")) {\n            issues.push(\"课程视频\");\n        }\n        // 检查文档附件\n        if (((_course_contentConfig1 = course.contentConfig) === null || _course_contentConfig1 === void 0 ? void 0 : (_course_contentConfig_document = _course_contentConfig1.document) === null || _course_contentConfig_document === void 0 ? void 0 : _course_contentConfig_document.url) && course.contentConfig.document.url.startsWith(\"blob:\")) {\n            issues.push(\"课程文档\");\n        }\n        // 检查教学材料\n        if (course.additionalResources) {\n            course.additionalResources.forEach((resource, index)=>{\n                if (resource.url && resource.url.startsWith(\"blob:\")) {\n                    issues.push(\"教学材料\".concat(index + 1));\n                }\n            });\n        }\n        return issues;\n    };\n    // 保存单个课程到后端\n    const saveCourse = async (course)=>{\n        try {\n            var _course_contentConfig_video, _course_contentConfig, _course_contentConfig_document, _course_contentConfig1;\n            // 验证必要的数据\n            if (!course.title || course.title.trim() === \"\") {\n                throw new Error(\"课程标题不能为空\");\n            }\n            // 检查是否有未上传完成的文件\n            const blobIssues = checkForBlobUrls(course);\n            if (blobIssues.length > 0) {\n                throw new Error(\"以下文件尚未上传完成，请等待上传完成后再保存：\".concat(blobIssues.join(\"、\")));\n            }\n            // 准备课程数据\n            const courseData = {\n                seriesId: seriesId,\n                title: course.title,\n                description: course.description || \"\",\n                coverImage: course.coverImage || \"\",\n                hasVideo: ((_course_contentConfig = course.contentConfig) === null || _course_contentConfig === void 0 ? void 0 : (_course_contentConfig_video = _course_contentConfig.video) === null || _course_contentConfig_video === void 0 ? void 0 : _course_contentConfig_video.url) ? 1 : 0,\n                hasDocument: ((_course_contentConfig1 = course.contentConfig) === null || _course_contentConfig1 === void 0 ? void 0 : (_course_contentConfig_document = _course_contentConfig1.document) === null || _course_contentConfig_document === void 0 ? void 0 : _course_contentConfig_document.url) ? 1 : 0,\n                hasAudio: 0,\n                videoDuration: 0,\n                contentConfig: course.contentConfig || {},\n                teachingInfo: convertTeachingInfoForAPI(course.teachingInfo || []),\n                additionalResources: course.additionalResources || [],\n                orderIndex: course.orderIndex || 1\n            };\n            console.log(\"\\uD83D\\uDCBE 准备保存课程数据:\", courseData);\n            let result;\n            // 判断是新课程还是更新课程\n            if (course.id && course.id > 1000000) {\n                // 新课程，使用创建API\n                console.log(\"\\uD83D\\uDCE4 创建新课程\");\n                const { data: response } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.createCourse(courseData);\n                result = response;\n                console.log(\"✅ 课程创建成功\");\n            } else {\n                // 现有课程，使用更新API\n                console.log(\"\\uD83D\\uDCE4 更新现有课程:\", course.id);\n                const { data: response } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.updateCourse(course.id, courseData);\n                result = response;\n                console.log(\"✅ 课程更新成功\");\n            }\n            return result;\n        } catch (error) {\n            console.error(\"❌ 保存课程失败:\", error);\n            throw error;\n        }\n    };\n    // 发布选中的课程\n    const handlePublishCourse = async ()=>{\n        if (!selectedCourseId) {\n            alert(\"请先选择要发布的课程\");\n            return;\n        }\n        const selectedCourse = courseList.find((course)=>course.id === selectedCourseId);\n        if (!selectedCourse) {\n            alert(\"未找到选中的课程\");\n            return;\n        }\n        // 检查课程是否已经发布\n        if (selectedCourse.status === 1) {\n            alert(\"该课程已经发布，无需重复发布\");\n            return;\n        }\n        try {\n            setIsPublishing(true);\n            console.log(\"\\uD83D\\uDCE4 开始发布课程\");\n            console.log(\"\\uD83D\\uDCE4 selectedCourseId:\", selectedCourseId, \"类型:\", typeof selectedCourseId);\n            console.log(\"\\uD83D\\uDCE4 课程信息:\", selectedCourse);\n            console.log(\"\\uD83D\\uDCE4 课程ID字段:\", selectedCourse.id, \"类型:\", typeof selectedCourse.id);\n            console.log(\"\\uD83D\\uDCE4 课程列表中的所有ID:\", courseList.map((c)=>({\n                    id: c.id,\n                    type: typeof c.id,\n                    title: c.title\n                })));\n            // 确保使用正确的课程ID\n            const courseIdToPublish = selectedCourse.id;\n            console.log(\"\\uD83D\\uDCE4 即将发布的课程ID:\", courseIdToPublish, \"类型:\", typeof courseIdToPublish);\n            const { data: response } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.publishCourse(courseIdToPublish);\n            if (response.code === 200) {\n                console.log(\"✅ 课程发布成功:\", response.data);\n                alert(\"课程发布成功！\");\n                // 刷新课程列表以更新状态\n                await loadCourseList();\n            } else {\n                console.error(\"❌ 发布课程失败:\", response.message);\n                alert(response.message || \"发布课程失败\");\n            }\n        } catch (error) {\n            var _error_response, _error_response_data, _error_response1;\n            console.error(\"❌ 发布课程失败:\", error);\n            console.error(\"❌ 错误详情:\", (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data);\n            // 处理具体的错误信息\n            if ((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : (_error_response_data = _error_response1.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) {\n                alert(error.response.data.message);\n            } else if (error.message) {\n                alert(error.message);\n            } else {\n                alert(\"发布课程失败，请重试\");\n            }\n        } finally{\n            setIsPublishing(false);\n        }\n    };\n    // 保存课程列表\n    const handleSave = async ()=>{\n        try {\n            setIsCreating(true);\n            // 检查是否有文件正在上传\n            if (uploadingFiles.size > 0) {\n                alert(\"有文件正在上传中，请等待上传完成后再保存\");\n                setIsCreating(false);\n                return;\n            }\n            console.log(\"\\uD83D\\uDCBE 开始保存课程列表\");\n            // 如果有选中的课程且在编辑状态，先保存当前课程\n            if (selectedCourseId && rightPanelType === \"course\") {\n                const selectedCourse = courseList.find((c)=>c.id === selectedCourseId);\n                if (selectedCourse) {\n                    var _courseDetail_teachingMaterials;\n                    // 更新课程数据\n                    const updatedCourse = {\n                        ...selectedCourse,\n                        title: courseDetail.title,\n                        description: courseDetail.description,\n                        coverImage: courseDetail.coverImage,\n                        hasVideo: courseDetail.isVideoEnabled ? 1 : 0,\n                        hasDocument: courseDetail.isAttachmentEnabled ? 1 : 0,\n                        contentConfig: courseDetail.contentConfig,\n                        teachingInfo: courseDetail.teachingInfo,\n                        additionalResources: ((_courseDetail_teachingMaterials = courseDetail.teachingMaterials) === null || _courseDetail_teachingMaterials === void 0 ? void 0 : _courseDetail_teachingMaterials.map((material)=>({\n                                title: material.name,\n                                url: material.url,\n                                description: material.name\n                            }))) || []\n                    };\n                    // 先更新课程列表中的数据\n                    setCourseList((prev)=>prev.map((course)=>course.id === selectedCourseId ? updatedCourse : course));\n                    // 然后保存到后端\n                    const savedCourseResult = await saveCourse(updatedCourse);\n                    // 如果课程有任务配置，发送课程设置请求\n                    if (courseDetail.taskConfig && (courseDetail.isDistributionWater || courseDetail.isOneKeyOpen)) {\n                        try {\n                            var _savedCourseResult_data;\n                            // 获取真实的课程ID\n                            let realCourseId = selectedCourseId;\n                            // 如果是新创建的课程，使用返回的真实ID\n                            if (selectedCourse.id > 1000000 && (savedCourseResult === null || savedCourseResult === void 0 ? void 0 : (_savedCourseResult_data = savedCourseResult.data) === null || _savedCourseResult_data === void 0 ? void 0 : _savedCourseResult_data.id)) {\n                                realCourseId = savedCourseResult.data.id;\n                                console.log(\"\\uD83C\\uDD94 使用新创建课程的真实ID:\", realCourseId);\n                            }\n                            // 1. 先创建任务模板（如果有任务配置）\n                            if (courseDetail.taskConfig.taskName || courseDetail.taskConfig.taskDescription) {\n                                console.log(\"\\uD83D\\uDCDD 开始创建任务模板...\");\n                                // 构建任务模板数据\n                                const taskTemplateData = {\n                                    taskName: courseDetail.taskConfig.taskName || \"\",\n                                    taskDescription: courseDetail.taskConfig.taskDescription || \"\",\n                                    durationDays: courseDetail.taskConfig.taskDuration ? parseInt(courseDetail.taskConfig.taskDuration) : 7,\n                                    attachments: courseDetail.taskConfig.referenceResources.map((resource)=>({\n                                            title: resource.name,\n                                            url: resource.url || \"\",\n                                            type: resource.type === \"file\" ? \"file\" : \"document\"\n                                        })),\n                                    workIdsStr: courseDetail.taskConfig.referenceWorks.map((_, index)=>\"\".concat(200 + index)).join(\",\"),\n                                    selfAssessmentItems: courseDetail.taskConfig.selfAssessmentItems.filter((item)=>item.trim() !== \"\") // 过滤空项\n                                    .map((content, index)=>({\n                                            content,\n                                            sequence: index + 1\n                                        })),\n                                    status: 1\n                                };\n                                console.log(\"\\uD83D\\uDCE4 发送任务模板创建请求到课程ID:\", realCourseId);\n                                console.log(\"\\uD83D\\uDCE4 任务模板数据:\", taskTemplateData);\n                                await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.createCourseTaskTemplate(realCourseId, taskTemplateData);\n                                console.log(\"✅ 任务模板创建成功\");\n                            }\n                            // 2. 然后发送课程设置请求\n                            const settingsData = {\n                                templateId: courseDetail.selectedTemplate.id || null,\n                                requiredPoints: courseDetail.requiredEnergy ? parseInt(courseDetail.requiredEnergy) : 0,\n                                autoCreateTasks: courseDetail.isOneKeyOpen ? 1 : 0\n                            };\n                            console.log(\"\\uD83D\\uDCE4 发送课程设置请求到课程ID:\", realCourseId);\n                            console.log(\"\\uD83D\\uDCE4 设置数据:\", settingsData);\n                            await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.setCourseSettings(realCourseId, settingsData);\n                            console.log(\"✅ 课程设置保存成功\");\n                        } catch (settingsError) {\n                            console.error(\"❌ 课程设置或任务模板创建失败:\", settingsError);\n                        // 不阻断主流程，只记录错误\n                        }\n                    }\n                }\n            }\n            // 保存系列课程信息\n            const data = {\n                title: editingTitle,\n                courseGoals,\n                courseObjectives,\n                courseList\n            };\n            onSave(data);\n            onClose();\n            console.log(\"✅ 课程列表保存完成\");\n        } catch (error) {\n            console.error(\"❌ 保存失败:\", error);\n            alert(\"保存失败: \".concat(error.message || \"请重试\"));\n        } finally{\n            setIsCreating(false);\n        }\n    };\n    // 发布系列课程\n    const handlePublish = async ()=>{\n        // 如果系列已发布，不执行任何操作\n        if (seriesStatus === 1) {\n            return;\n        }\n        try {\n            setIsPublishingSeries(true);\n            // 检查是否有课程\n            if (courseList.length === 0) {\n                alert(\"发布失败：课程系列中至少需要包含一个课程\");\n                return;\n            }\n            console.log(\"\\uD83D\\uDCE4 开始发布系列课程，系列ID:\", seriesId);\n            const { data: response } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.publishCourseSeries(seriesId);\n            if (response.code === 200) {\n                console.log(\"✅ 系列课程发布成功:\", response.data);\n                // 构建成功消息\n                const publishData = response.data;\n                let successMessage = '系列课程\"'.concat(publishData.title, '\"发布成功！');\n                // 如果有发布统计信息，添加到消息中\n                if (publishData.publishStats) {\n                    const stats = publishData.publishStats;\n                    successMessage += \"\\n\\n发布统计：\\n• 总课程数：\".concat(publishData.totalCourses, \"\\n• 已发布课程：\").concat(publishData.publishedCourses, \"\\n• 视频课程：\").concat(stats.videoCourseCount, \"个\\n• 文档课程：\").concat(stats.documentCourseCount, \"个\\n• 总资源数：\").concat(stats.totalResourcesCount, \"个\");\n                    if (stats.totalVideoDuration > 0) {\n                        const durationMinutes = Math.round(stats.totalVideoDuration / 60);\n                        successMessage += \"\\n• 视频总时长：\".concat(durationMinutes, \"分钟\");\n                    }\n                }\n                alert(successMessage);\n                // 更新系列状态为已发布\n                setSeriesStatus(1);\n                // 刷新课程列表以更新状态\n                await loadCourseList();\n                // 通知父组件刷新数据\n                onSave({\n                    type: \"publish_series\",\n                    seriesId: seriesId,\n                    message: \"系列课程发布成功\"\n                });\n            } else {\n                console.error(\"❌ 发布系列课程失败:\", response.message);\n                alert(response.message || \"发布系列课程失败\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"❌ 发布系列课程出错:\", error);\n            // 处理具体的错误信息\n            let errorMessage = \"发布系列课程失败\";\n            if ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) {\n                errorMessage = error.response.data.message;\n            } else if (error.message) {\n                errorMessage = error.message;\n            }\n            alert(errorMessage);\n        } finally{\n            setIsPublishingSeries(false);\n        }\n    };\n    // 退出编辑模式 - 保存数据并关闭\n    const handleExitEdit = ()=>{\n        handleSave();\n    };\n    // 显示设置面板\n    const showSettingsPanel = ()=>{\n        setRightPanelType(\"settings\");\n        setSelectedCourseId(null);\n    };\n    // 处理模板选择\n    const handleTemplateSelect = (template)=>{\n        setCourseDetail((prev)=>({\n                ...prev,\n                selectedTemplate: {\n                    id: template.id,\n                    name: template.templateName\n                }\n            }));\n        setIsTemplatePickerOpen(false);\n    };\n    // 打开模板选择弹窗\n    const handleOpenTemplatePicker = ()=>{\n        setIsTemplatePickerOpen(true);\n    };\n    // 显示课程编辑面板\n    const showCoursePanel = async (courseId)=>{\n        setRightPanelType(\"course\");\n        setSelectedCourseId(courseId);\n        // 如果作品数据还没有加载，重新加载\n        if (works.length === 0 && !loadingWorks) {\n            loadUserWorks();\n        }\n        // 获取选中的课程并更新courseDetail状态\n        const selectedCourse = courseList.find((course)=>course.id === courseId);\n        console.log(\"\\uD83C\\uDFAF 找到的课程:\", selectedCourse);\n        if (selectedCourse) {\n            try {\n                console.log(\"\\uD83D\\uDD04 获取课程详情，seriesId:\", seriesId, \"courseId:\", courseId);\n                // 获取真实的课程详情数据\n                const { data: res } = await fetchCourseDetail(seriesId, courseId);\n                if (res.code === 200 && res.data) {\n                    var _courseDetailData_additionalResources, _courseDetailData_teachingInfo;\n                    const courseDetailData = res.data;\n                    console.log(\"✅ 获取到课程详情:\", courseDetailData);\n                    // 将真实的 additionalResources 映射到 teachingMaterials\n                    const teachingMaterials = ((_courseDetailData_additionalResources = courseDetailData.additionalResources) === null || _courseDetailData_additionalResources === void 0 ? void 0 : _courseDetailData_additionalResources.map((resource)=>({\n                            type: \"application/octet-stream\",\n                            name: resource.title || resource.name || \"附件\",\n                            url: resource.url\n                        }))) || [];\n                    // 将API返回的teachingInfo数组格式转换为新的格式\n                    const mappedTeachingInfo = ((_courseDetailData_teachingInfo = courseDetailData.teachingInfo) === null || _courseDetailData_teachingInfo === void 0 ? void 0 : _courseDetailData_teachingInfo.map((info)=>({\n                            title: info.title || \"\",\n                            content: Array.isArray(info.content) ? info.content.join(\"\\n\") : info.content || \"\"\n                        }))) || [];\n                    console.log(\"\\uD83D\\uDCCE 映射的教学附件:\", teachingMaterials);\n                    console.log(\"\\uD83D\\uDCDA 映射的教学信息:\", mappedTeachingInfo);\n                    setCourseDetail((prev)=>{\n                        var _courseDetailData_contentConfig_video, _courseDetailData_contentConfig, _courseDetailData_contentConfig_video1, _courseDetailData_contentConfig1, _courseDetailData_contentConfig_document, _courseDetailData_contentConfig2, _courseDetailData_contentConfig_document1, _courseDetailData_contentConfig3;\n                        return {\n                            ...prev,\n                            title: courseDetailData.title,\n                            description: courseDetailData.description,\n                            coverImage: courseDetailData.coverImage || \"\",\n                            isVideoEnabled: courseDetailData.hasVideo === 1,\n                            isAttachmentEnabled: courseDetailData.hasDocument === 1,\n                            contentConfig: courseDetailData.contentConfig || {},\n                            teachingInfo: mappedTeachingInfo,\n                            teachingMaterials: teachingMaterials,\n                            videoUrl: ((_courseDetailData_contentConfig = courseDetailData.contentConfig) === null || _courseDetailData_contentConfig === void 0 ? void 0 : (_courseDetailData_contentConfig_video = _courseDetailData_contentConfig.video) === null || _courseDetailData_contentConfig_video === void 0 ? void 0 : _courseDetailData_contentConfig_video.url) || \"\",\n                            videoName: ((_courseDetailData_contentConfig1 = courseDetailData.contentConfig) === null || _courseDetailData_contentConfig1 === void 0 ? void 0 : (_courseDetailData_contentConfig_video1 = _courseDetailData_contentConfig1.video) === null || _courseDetailData_contentConfig_video1 === void 0 ? void 0 : _courseDetailData_contentConfig_video1.name) || \"\",\n                            attachmentUrl: ((_courseDetailData_contentConfig2 = courseDetailData.contentConfig) === null || _courseDetailData_contentConfig2 === void 0 ? void 0 : (_courseDetailData_contentConfig_document = _courseDetailData_contentConfig2.document) === null || _courseDetailData_contentConfig_document === void 0 ? void 0 : _courseDetailData_contentConfig_document.url) || \"\",\n                            attachmentName: ((_courseDetailData_contentConfig3 = courseDetailData.contentConfig) === null || _courseDetailData_contentConfig3 === void 0 ? void 0 : (_courseDetailData_contentConfig_document1 = _courseDetailData_contentConfig3.document) === null || _courseDetailData_contentConfig_document1 === void 0 ? void 0 : _courseDetailData_contentConfig_document1.name) || \"\"\n                        };\n                    });\n                } else {\n                    console.error(\"❌ 获取课程详情失败:\", res.message);\n                    // 使用基础数据作为后备\n                    setCourseDetail((prev)=>({\n                            ...prev,\n                            title: selectedCourse.title,\n                            description: selectedCourse.description,\n                            coverImage: selectedCourse.coverImage || \"\",\n                            isVideoEnabled: selectedCourse.hasVideo === 1,\n                            isAttachmentEnabled: selectedCourse.hasDocument === 1,\n                            teachingMaterials: [] // 清空附件列表\n                        }));\n                }\n            } catch (error) {\n                console.error(\"❌ 获取课程详情异常:\", error);\n                // 使用基础数据作为后备\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        title: selectedCourse.title,\n                        description: selectedCourse.description,\n                        coverImage: selectedCourse.coverImage || \"\",\n                        isVideoEnabled: selectedCourse.hasVideo === 1,\n                        isAttachmentEnabled: selectedCourse.hasDocument === 1,\n                        teachingMaterials: [] // 清空附件列表\n                    }));\n            }\n        }\n    };\n    // 获取选中的课程\n    const getSelectedCourse = ()=>{\n        return courseList.find((course)=>course.id === selectedCourseId);\n    };\n    if (!isVisible) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"course-list-modal-overlay\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"course-list-modal\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"course-list-header\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"course-list-title-section\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"course-list-title\",\n                                        children: \"课程列表\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 1452,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"course-list-actions\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: showSettingsPanel,\n                                                className: \"course-list-settings-btn \".concat(rightPanelType === \"settings\" ? \"active\" : \"\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1458,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1454,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: addNewCourse,\n                                                className: \"course-list-add-btn\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1461,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1460,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 1453,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                lineNumber: 1451,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onClose,\n                                className: \"course-list-close-btn\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 1466,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                lineNumber: 1465,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                        lineNumber: 1450,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"course-list-content\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"course-list-sidebar\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"course-list-items\",\n                                    children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"course-list-loading\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"加载中...\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 1477,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 1476,\n                                        columnNumber: 17\n                                    }, undefined) : courseList.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"course-list-empty\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"course-list-empty-icon\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"w-12 h-12 text-gray-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1482,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1481,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"course-list-empty-title\",\n                                                children: \"暂无课时\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1484,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"course-list-empty-description\",\n                                                children: \"点击右上角的 + 按钮添加第一个课时\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1485,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: addNewCourse,\n                                                className: \"course-list-empty-btn\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1492,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    \"添加课时\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1488,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 1480,\n                                        columnNumber: 17\n                                    }, undefined) : courseList.map((course)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"course-list-item \".concat(selectedCourseId === course.id ? \"active\" : \"\"),\n                                            onClick: ()=>showCoursePanel(course.id),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-list-item-content\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"course-list-item-text\",\n                                                            children: course.title\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1504,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"course-status-badge \".concat(course.status === 1 ? \"published\" : \"draft\"),\n                                                            children: course.status === 1 ? \"已发布\" : \"未发布\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1505,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1503,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: (e)=>{\n                                                        e.stopPropagation();\n                                                        showDeleteConfirm(course.id);\n                                                    },\n                                                    className: \"course-list-item-delete\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"w-3 h-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1516,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1509,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, course.id, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 1498,\n                                            columnNumber: 19\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 1474,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                lineNumber: 1473,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"course-list-edit-area\",\n                                children: [\n                                    rightPanelType === \"none\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"course-edit-empty\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"course-edit-empty-icon\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"w-16 h-16 text-gray-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1529,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1528,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"course-edit-empty-title\",\n                                                children: \"无课程详情\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1531,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"course-edit-empty-description\",\n                                                children: \"点击左侧课程或设置按钮查看详情\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1532,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 1527,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    rightPanelType === \"settings\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"course-series-cover\",\n                                                children: seriesCoverImage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: seriesCoverImage,\n                                                    alt: \"系列课程封面\",\n                                                    className: \"course-series-cover-image\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1543,\n                                                    columnNumber: 21\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-series-cover-placeholder\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"系列课程封面\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1550,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1549,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1541,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"course-edit-form\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"course-edit-field\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"course-edit-label\",\n                                                                children: \"系列课程标题\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1559,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                value: editingTitle,\n                                                                onChange: (e)=>setEditingTitle(e.target.value),\n                                                                className: \"course-edit-input\",\n                                                                placeholder: \"请输入系列课程标题\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1560,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1558,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"course-edit-field\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"course-edit-label\",\n                                                                children: \"课程标签\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1571,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Select_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                mode: \"multiple\",\n                                                                style: {\n                                                                    width: \"100%\"\n                                                                },\n                                                                placeholder: \"请选择课程标签\",\n                                                                value: selectedTags,\n                                                                onChange: setSelectedTags,\n                                                                loading: tagsLoading,\n                                                                options: courseTags.map((tag)=>{\n                                                                    console.log(\"\\uD83C\\uDFF7️ 渲染标签选项:\", tag);\n                                                                    return {\n                                                                        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            style: {\n                                                                                color: tag.color\n                                                                            },\n                                                                            children: tag.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1583,\n                                                                            columnNumber: 29\n                                                                        }, void 0),\n                                                                        value: tag.id\n                                                                    };\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1572,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    fontSize: \"12px\",\n                                                                    color: \"#666\",\n                                                                    marginTop: \"4px\"\n                                                                },\n                                                                children: [\n                                                                    \"调试: 当前标签数量 \",\n                                                                    courseTags.length,\n                                                                    \", 加载状态: \",\n                                                                    tagsLoading ? \"是\" : \"否\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1592,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1570,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"course-edit-field\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"course-edit-label\",\n                                                                children: \"课程项目成员\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1599,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                value: projectMembers,\n                                                                onChange: (e)=>setProjectMembers(e.target.value),\n                                                                className: \"course-edit-input\",\n                                                                placeholder: \"请输入项目成员，如：张老师、李助教、王同学\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1600,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1598,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1556,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true),\n                                    rightPanelType === \"course\" && getSelectedCourse() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"course-detail-edit\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-detail-top\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"course-detail-cover\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"course-cover-upload-area\",\n                                                                    onClick: ()=>{\n                                                                        var _document_getElementById;\n                                                                        return (_document_getElementById = document.getElementById(\"cover-upload-input\")) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.click();\n                                                                    },\n                                                                    children: courseDetail.coverImage || ((_getSelectedCourse = getSelectedCourse()) === null || _getSelectedCourse === void 0 ? void 0 : _getSelectedCourse.coverImage) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                        src: courseDetail.coverImage || ((_getSelectedCourse1 = getSelectedCourse()) === null || _getSelectedCourse1 === void 0 ? void 0 : _getSelectedCourse1.coverImage),\n                                                                        alt: \"课程封面\",\n                                                                        className: \"course-cover-image\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1624,\n                                                                        columnNumber: 27\n                                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"course-cover-placeholder\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"点击上传课程封面\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1631,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1630,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1619,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    id: \"cover-upload-input\",\n                                                                    type: \"file\",\n                                                                    accept: \"image/jpeg,image/jpg,image/png,image/gif\",\n                                                                    onChange: handleCoverUpload,\n                                                                    style: {\n                                                                        display: \"none\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1635,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"one-click-class-btn\",\n                                                                    onClick: ()=>{\n                                                                        // TODO: 实现一键上课功能\n                                                                        console.log(\"一键上课按钮被点击\");\n                                                                        notification.info(\"一键上课功能开发中...\");\n                                                                    },\n                                                                    children: \"一键上课\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1643,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1618,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"course-detail-basic\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"course-detail-field\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            children: \"课程标题\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1656,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            value: courseDetail.title || ((_getSelectedCourse2 = getSelectedCourse()) === null || _getSelectedCourse2 === void 0 ? void 0 : _getSelectedCourse2.title) || \"\",\n                                                                            onChange: (e)=>{\n                                                                                setCourseDetail((prev)=>({\n                                                                                        ...prev,\n                                                                                        title: e.target.value\n                                                                                    }));\n                                                                                updateCourseTitle(selectedCourseId, e.target.value);\n                                                                            },\n                                                                            placeholder: \"请输入课程标题\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1657,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1655,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"course-detail-field\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            children: \"课程介绍\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1668,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                            value: courseDetail.description || ((_getSelectedCourse3 = getSelectedCourse()) === null || _getSelectedCourse3 === void 0 ? void 0 : _getSelectedCourse3.description) || \"\",\n                                                                            onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                        ...prev,\n                                                                                        description: e.target.value\n                                                                                    })),\n                                                                            placeholder: \"请输入课程介绍\",\n                                                                            rows: 3\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1669,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1667,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1654,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1617,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-detail-section\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            children: \"课程资源\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1681,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"course-resource-item\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"resource-header-right\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"课程视频\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1686,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"switch\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: \"checkbox\",\n                                                                                    checked: courseDetail.isVideoEnabled,\n                                                                                    onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                ...prev,\n                                                                                                isVideoEnabled: e.target.checked\n                                                                                            }))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1688,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"slider\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1693,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1687,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1685,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                courseDetail.isVideoEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"video-content-area\",\n                                                                    children: ((_courseDetail_contentConfig = courseDetail.contentConfig) === null || _courseDetail_contentConfig === void 0 ? void 0 : (_courseDetail_contentConfig_video = _courseDetail_contentConfig.video) === null || _courseDetail_contentConfig_video === void 0 ? void 0 : _courseDetail_contentConfig_video.url) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"video-info-section\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"video-preview\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                                                                    className: \"video-thumbnail\",\n                                                                                    controls: true,\n                                                                                    poster: courseDetail.coverImage,\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                                                                                            src: courseDetail.contentConfig.video.url,\n                                                                                            type: \"video/mp4\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1707,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined),\n                                                                                        \"您的浏览器不支持视频播放\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1702,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1701,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"video-name-centered\",\n                                                                                children: courseDetail.contentConfig.video.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1711,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"upload-btn-horizontal\",\n                                                                                onClick: triggerVideoUpload,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"重新上传\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1713,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1712,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1700,\n                                                                        columnNumber: 29\n                                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"video-upload-section\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"video-placeholder-centered\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"play-icon\",\n                                                                                    children: \"▶\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1719,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1718,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"upload-btn-horizontal\",\n                                                                                onClick: triggerVideoUpload,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"上传视频\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1722,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1721,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1717,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1697,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1684,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"course-resource-item\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"resource-header-right\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"课程附件\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1733,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"switch\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: \"checkbox\",\n                                                                                    checked: courseDetail.isAttachmentEnabled,\n                                                                                    onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                ...prev,\n                                                                                                isAttachmentEnabled: e.target.checked\n                                                                                            }))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1735,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"slider\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1740,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1734,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1732,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                courseDetail.isAttachmentEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"attachment-content-area\",\n                                                                    children: ((_courseDetail_contentConfig1 = courseDetail.contentConfig) === null || _courseDetail_contentConfig1 === void 0 ? void 0 : (_courseDetail_contentConfig_document = _courseDetail_contentConfig1.document) === null || _courseDetail_contentConfig_document === void 0 ? void 0 : _courseDetail_contentConfig_document.url) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"attachment-info-section\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"attachment-preview\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"document-icon\",\n                                                                                        children: \"\\uD83D\\uDCC4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1749,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"attachment-details\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"attachment-name\",\n                                                                                            children: courseDetail.contentConfig.document.name\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1751,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1750,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1748,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"upload-btn-horizontal\",\n                                                                                onClick: triggerAttachmentUpload,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"重新上传\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1755,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1754,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1747,\n                                                                        columnNumber: 29\n                                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"attachment-upload-section\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            className: \"upload-btn-horizontal\",\n                                                                            onClick: triggerAttachmentUpload,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"上传附件\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1761,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1760,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1759,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1744,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1731,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"course-resource-item\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"resource-header-simple\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"教学附件\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1772,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1771,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"teaching-materials\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            className: \"add-material-btn\",\n                                                                            onClick: triggerTeachingMaterialUpload,\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"+\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1776,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"上传\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1777,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1775,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        courseDetail.teachingMaterials && courseDetail.teachingMaterials.length > 0 ? courseDetail.teachingMaterials.map((material, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"material-item\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"material-name\",\n                                                                                        onClick: ()=>{\n                                                                                            if (material.url) {\n                                                                                                window.open(material.url, \"_blank\");\n                                                                                            }\n                                                                                        },\n                                                                                        style: {\n                                                                                            cursor: material.url ? \"pointer\" : \"default\",\n                                                                                            color: material.url ? \"#1890ff\" : \"inherit\",\n                                                                                            textDecoration: material.url ? \"underline\" : \"none\"\n                                                                                        },\n                                                                                        title: material.url ? \"点击下载附件\" : material.name,\n                                                                                        children: [\n                                                                                            \"\\uD83D\\uDCCE \",\n                                                                                            material.name\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1782,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        className: \"remove-material-btn\",\n                                                                                        onClick: ()=>removeTeachingMaterial(index),\n                                                                                        title: \"删除附件\",\n                                                                                        children: \"\\xd7\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1798,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, index, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1781,\n                                                                                columnNumber: 29\n                                                                            }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"empty-materials-hint\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                style: {\n                                                                                    color: \"#999\",\n                                                                                    fontSize: \"14px\"\n                                                                                },\n                                                                                children: \"暂无教学附件\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1809,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1808,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1774,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1770,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1680,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-detail-section\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"section-header\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    children: \"课程内容\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1819,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"add-content-section-btn\",\n                                                                    onClick: addTeachingInfoItem,\n                                                                    title: \"添加课程内容\",\n                                                                    children: \"+ 添加课程内容\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1820,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1818,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"course-content-area\",\n                                                            children: courseDetail.teachingInfo && courseDetail.teachingInfo.length > 0 ? courseDetail.teachingInfo.map((info, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"teaching-info-card\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"card-header\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"card-title\",\n                                                                                    children: [\n                                                                                        \"课程内容 \",\n                                                                                        index + 1\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1833,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    className: \"remove-card-btn\",\n                                                                                    onClick: ()=>removeTeachingInfoItem(index),\n                                                                                    title: \"删除此内容\",\n                                                                                    children: \"\\xd7\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1834,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1832,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"card-content\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"input-group\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                            children: \"标题\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1844,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                            type: \"text\",\n                                                                                            value: info.title,\n                                                                                            onChange: (e)=>updateTeachingInfoTitle(index, e.target.value),\n                                                                                            placeholder: \"请输入标题，如：教学目标、教学方法等\",\n                                                                                            className: \"title-input\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1845,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1843,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"input-group\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                            children: \"内容\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1854,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                                            value: info.content,\n                                                                                            onChange: (e)=>updateTeachingInfoContent(index, e.target.value),\n                                                                                            placeholder: \"请输入具体内容，多个内容项可用换行分隔\",\n                                                                                            className: \"content-textarea\",\n                                                                                            rows: 4\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1855,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1853,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1842,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, index, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1831,\n                                                                    columnNumber: 27\n                                                                }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"empty-content-hint\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: \"暂无课程内容，点击右上角按钮添加\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1868,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1867,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1828,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1817,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-detail-section\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"one-key-section\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"one-key-item\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"开始上课\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1878,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"switch\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"checkbox\",\n                                                                                checked: courseDetail.isOneKeyOpen,\n                                                                                onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                            ...prev,\n                                                                                            isOneKeyOpen: e.target.checked\n                                                                                        }))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1880,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"slider\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1885,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1879,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1877,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            courseDetail.isOneKeyOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"one-key-item\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"分配积木\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1892,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"switch\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                        type: \"checkbox\",\n                                                                                        checked: courseDetail.isDistributionEnabled,\n                                                                                        onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    isDistributionEnabled: e.target.checked\n                                                                                                }))\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1894,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"slider\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1899,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1893,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            courseDetail.isDistributionEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"block-template-section\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        className: \"select-template-btn\",\n                                                                                        onClick: handleOpenTemplatePicker,\n                                                                                        children: \"选择积木模板\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1903,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"selected-template-display\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            children: courseDetail.selectedTemplate.name || \"选中的模板名字\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1910,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1909,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1902,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1891,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"one-key-item\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"分配能量\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1917,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"switch\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                        type: \"checkbox\",\n                                                                                        checked: courseDetail.isDistributionWater,\n                                                                                        onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    isDistributionWater: e.target.checked\n                                                                                                }))\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1919,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"slider\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1924,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1918,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1916,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    courseDetail.isDistributionWater && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"energy-input-section\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: \"需要能量：\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1931,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                        type: \"text\",\n                                                                                        value: courseDetail.requiredEnergy || \"\",\n                                                                                        onChange: (e)=>handleEnergyChange(e.target.value),\n                                                                                        placeholder: \"请输入需要的能量值\",\n                                                                                        className: \"energy-input \".concat(energyError ? \"error\" : \"\")\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1932,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1930,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            energyError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"error-message\",\n                                                                                children: energyError\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1941,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1929,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"one-key-item\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"分配任务\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1949,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"switch\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                        type: \"checkbox\",\n                                                                                        checked: courseDetail.isDistributionLimit,\n                                                                                        onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    isDistributionLimit: e.target.checked\n                                                                                                }))\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1951,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"slider\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1956,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1950,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1948,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    courseDetail.isDistributionLimit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"task-config-form\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"task-config-row\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"task-config-field\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                                children: \"任务名称:\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                lineNumber: 1966,\n                                                                                                columnNumber: 35\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                type: \"text\",\n                                                                                                value: courseDetail.taskConfig.taskName,\n                                                                                                onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                            ...prev,\n                                                                                                            taskConfig: {\n                                                                                                                ...prev.taskConfig,\n                                                                                                                taskName: e.target.value\n                                                                                                            }\n                                                                                                        })),\n                                                                                                placeholder: \"请输入任务名称\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                lineNumber: 1967,\n                                                                                                columnNumber: 35\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1965,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"task-config-field\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                                children: \"任务持续天数:\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                lineNumber: 1978,\n                                                                                                columnNumber: 35\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"duration-input-wrapper\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                        type: \"text\",\n                                                                                                        value: courseDetail.taskConfig.taskDuration,\n                                                                                                        onClick: ()=>setShowDurationPicker(true),\n                                                                                                        placeholder: \"请选择时间\",\n                                                                                                        readOnly: true\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                        lineNumber: 1980,\n                                                                                                        columnNumber: 37\n                                                                                                    }, undefined),\n                                                                                                    showDurationPicker && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"duration-picker\",\n                                                                                                        children: [\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                className: \"duration-options\",\n                                                                                                                children: [\n                                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                                        onClick: ()=>handleDurationSelect(1, \"1小时\"),\n                                                                                                                        children: \"1小时\"\n                                                                                                                    }, void 0, false, {\n                                                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                        lineNumber: 1990,\n                                                                                                                        columnNumber: 43\n                                                                                                                    }, undefined),\n                                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                                        onClick: ()=>handleDurationSelect(6, \"6小时\"),\n                                                                                                                        children: \"6小时\"\n                                                                                                                    }, void 0, false, {\n                                                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                        lineNumber: 1991,\n                                                                                                                        columnNumber: 43\n                                                                                                                    }, undefined),\n                                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                                        onClick: ()=>handleDurationSelect(12, \"12小时\"),\n                                                                                                                        children: \"12小时\"\n                                                                                                                    }, void 0, false, {\n                                                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                        lineNumber: 1992,\n                                                                                                                        columnNumber: 43\n                                                                                                                    }, undefined),\n                                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                                        onClick: ()=>handleDurationSelect(24, \"1天\"),\n                                                                                                                        children: \"1天\"\n                                                                                                                    }, void 0, false, {\n                                                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                        lineNumber: 1993,\n                                                                                                                        columnNumber: 43\n                                                                                                                    }, undefined),\n                                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                                        onClick: ()=>handleDurationSelect(168, \"7天\"),\n                                                                                                                        children: \"7天\"\n                                                                                                                    }, void 0, false, {\n                                                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                        lineNumber: 1994,\n                                                                                                                        columnNumber: 43\n                                                                                                                    }, undefined)\n                                                                                                                ]\n                                                                                                            }, void 0, true, {\n                                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                lineNumber: 1989,\n                                                                                                                columnNumber: 41\n                                                                                                            }, undefined),\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                                className: \"close-picker\",\n                                                                                                                onClick: ()=>setShowDurationPicker(false),\n                                                                                                                children: \"\\xd7\"\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                lineNumber: 1996,\n                                                                                                                columnNumber: 41\n                                                                                                            }, undefined)\n                                                                                                        ]\n                                                                                                    }, void 0, true, {\n                                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                        lineNumber: 1988,\n                                                                                                        columnNumber: 39\n                                                                                                    }, undefined)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                lineNumber: 1979,\n                                                                                                columnNumber: 35\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1977,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1964,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"task-config-field task-config-full\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        children: \"任务描述:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 2010,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                                        value: courseDetail.taskConfig.taskDescription,\n                                                                                        onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    taskConfig: {\n                                                                                                        ...prev.taskConfig,\n                                                                                                        taskDescription: e.target.value\n                                                                                                    }\n                                                                                                })),\n                                                                                        placeholder: \"请输入任务描述\",\n                                                                                        rows: 4\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 2011,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 2009,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"task-config-field task-config-full\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        children: [\n                                                                                            \"任务自评项: \",\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"item-number\",\n                                                                                                children: courseDetail.taskConfig.selfAssessmentItems.length\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                lineNumber: 2024,\n                                                                                                columnNumber: 47\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 2024,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    courseDetail.taskConfig.selfAssessmentItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"self-assessment-item\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                type: \"text\",\n                                                                                                value: item,\n                                                                                                onChange: (e)=>{\n                                                                                                    const newItems = [\n                                                                                                        ...courseDetail.taskConfig.selfAssessmentItems\n                                                                                                    ];\n                                                                                                    newItems[index] = e.target.value;\n                                                                                                    setCourseDetail((prev)=>({\n                                                                                                            ...prev,\n                                                                                                            taskConfig: {\n                                                                                                                ...prev.taskConfig,\n                                                                                                                selfAssessmentItems: newItems\n                                                                                                            }\n                                                                                                        }));\n                                                                                                },\n                                                                                                placeholder: \"请输入自评项内容\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                lineNumber: 2027,\n                                                                                                columnNumber: 37\n                                                                                            }, undefined)\n                                                                                        }, index, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 2026,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        type: \"button\",\n                                                                                        className: \"add-assessment-btn\",\n                                                                                        onClick: ()=>setCourseDetail((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    taskConfig: {\n                                                                                                        ...prev.taskConfig,\n                                                                                                        selfAssessmentItems: [\n                                                                                                            ...prev.taskConfig.selfAssessmentItems,\n                                                                                                            \"\"\n                                                                                                        ]\n                                                                                                    }\n                                                                                                })),\n                                                                                        children: \"+\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 2042,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 2023,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"task-config-field task-config-full\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        children: \"任务参考作品:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 2059,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"reference-works-section\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"works-section\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                    className: \"help-text\",\n                                                                                                    children: \"选择作品作为任务参考资料（可多选）\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                    lineNumber: 2062,\n                                                                                                    columnNumber: 37\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"relative works-scroll-wrapper\",\n                                                                                                    style: {\n                                                                                                        minHeight: \"200px\",\n                                                                                                        cursor: \"grab\",\n                                                                                                        userSelect: \"none\"\n                                                                                                    },\n                                                                                                    children: loadingWorks ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"loading-container\",\n                                                                                                        children: [\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                className: \"loading-spinner\"\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                lineNumber: 2073,\n                                                                                                                columnNumber: 43\n                                                                                                            }, undefined),\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                                children: \"加载中...\"\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                lineNumber: 2074,\n                                                                                                                columnNumber: 43\n                                                                                                            }, undefined)\n                                                                                                        ]\n                                                                                                    }, void 0, true, {\n                                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                        lineNumber: 2072,\n                                                                                                        columnNumber: 41\n                                                                                                    }, undefined) : works.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"works-horizontal-scroll\",\n                                                                                                        onWheel: handleWheel,\n                                                                                                        children: works.map((work)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                className: \"work-card \".concat(selectedWorkIds.includes(work.id) ? \"selected\" : \"\"),\n                                                                                                                onClick: ()=>handleWorkSelect(work.id),\n                                                                                                                children: [\n                                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                        className: \"work-image\",\n                                                                                                                        children: [\n                                                                                                                            work.coverImage || work.screenShotImage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                                                                src: work.coverImage || work.screenShotImage,\n                                                                                                                                alt: work.title,\n                                                                                                                                onError: (e)=>{\n                                                                                                                                    const target = e.currentTarget;\n                                                                                                                                    target.style.display = \"none\";\n                                                                                                                                    const nextElement = target.nextElementSibling;\n                                                                                                                                    if (nextElement) {\n                                                                                                                                        nextElement.style.display = \"flex\";\n                                                                                                                                    }\n                                                                                                                                }\n                                                                                                                            }, void 0, false, {\n                                                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                                lineNumber: 2089,\n                                                                                                                                columnNumber: 51\n                                                                                                                            }, undefined) : null,\n                                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                                className: \"work-placeholder\",\n                                                                                                                                style: {\n                                                                                                                                    display: work.coverImage || work.screenShotImage ? \"none\" : \"flex\"\n                                                                                                                                },\n                                                                                                                                children: \"作品\"\n                                                                                                                            }, void 0, false, {\n                                                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                                lineNumber: 2102,\n                                                                                                                                columnNumber: 49\n                                                                                                                            }, undefined)\n                                                                                                                        ]\n                                                                                                                    }, void 0, true, {\n                                                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                        lineNumber: 2087,\n                                                                                                                        columnNumber: 47\n                                                                                                                    }, undefined),\n                                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                        className: \"work-title\",\n                                                                                                                        children: work.title || work.name || work.workName || \"未命名作品\"\n                                                                                                                    }, void 0, false, {\n                                                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                        lineNumber: 2106,\n                                                                                                                        columnNumber: 47\n                                                                                                                    }, undefined),\n                                                                                                                    selectedWorkIds.includes(work.id) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                        className: \"selected-indicator\",\n                                                                                                                        children: \"✓\"\n                                                                                                                    }, void 0, false, {\n                                                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                        lineNumber: 2108,\n                                                                                                                        columnNumber: 49\n                                                                                                                    }, undefined)\n                                                                                                                ]\n                                                                                                            }, work.id, true, {\n                                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                lineNumber: 2082,\n                                                                                                                columnNumber: 45\n                                                                                                            }, undefined))\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                        lineNumber: 2077,\n                                                                                                        columnNumber: 41\n                                                                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"empty-placeholder\",\n                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                            className: \"empty-text\",\n                                                                                                            children: \"暂无作品\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                            lineNumber: 2115,\n                                                                                                            columnNumber: 43\n                                                                                                        }, undefined)\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                        lineNumber: 2114,\n                                                                                                        columnNumber: 41\n                                                                                                    }, undefined)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                    lineNumber: 2063,\n                                                                                                    columnNumber: 37\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 2061,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 2060,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 2058,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"task-config-field task-config-full\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        children: \"任务参考资源:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 2125,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"reference-resources-section\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"reference-resources-grid\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                    type: \"button\",\n                                                                                                    className: \"upload-resource-btn\",\n                                                                                                    onClick: ()=>{\n                                                                                                        // 触发文件上传\n                                                                                                        const input = document.createElement(\"input\");\n                                                                                                        input.type = \"file\";\n                                                                                                        input.accept = \".pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx,.txt,.jpg,.png,.gif\";\n                                                                                                        input.onchange = (e)=>{\n                                                                                                            var _e_target_files;\n                                                                                                            const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n                                                                                                            if (file) {\n                                                                                                                setCourseDetail((prev)=>({\n                                                                                                                        ...prev,\n                                                                                                                        taskConfig: {\n                                                                                                                            ...prev.taskConfig,\n                                                                                                                            referenceResources: [\n                                                                                                                                ...prev.taskConfig.referenceResources,\n                                                                                                                                {\n                                                                                                                                    type: \"file\",\n                                                                                                                                    name: file.name\n                                                                                                                                }\n                                                                                                                            ]\n                                                                                                                        }\n                                                                                                                    }));\n                                                                                                            }\n                                                                                                        };\n                                                                                                        input.click();\n                                                                                                    },\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                                            size: 24\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                            lineNumber: 2154,\n                                                                                                            columnNumber: 39\n                                                                                                        }, undefined),\n                                                                                                        \"上传\"\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                    lineNumber: 2128,\n                                                                                                    columnNumber: 37\n                                                                                                }, undefined),\n                                                                                                courseDetail.taskConfig.referenceResources.map((resource, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"reference-resource-item\",\n                                                                                                        children: [\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                                children: resource.name\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                lineNumber: 2159,\n                                                                                                                columnNumber: 41\n                                                                                                            }, undefined),\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                                type: \"button\",\n                                                                                                                className: \"remove-resource-btn\",\n                                                                                                                onClick: ()=>{\n                                                                                                                    const newResources = courseDetail.taskConfig.referenceResources.filter((_, i)=>i !== index);\n                                                                                                                    setCourseDetail((prev)=>({\n                                                                                                                            ...prev,\n                                                                                                                            taskConfig: {\n                                                                                                                                ...prev.taskConfig,\n                                                                                                                                referenceResources: newResources\n                                                                                                                            }\n                                                                                                                        }));\n                                                                                                                },\n                                                                                                                children: \"\\xd7\"\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                lineNumber: 2160,\n                                                                                                                columnNumber: 41\n                                                                                                            }, undefined)\n                                                                                                        ]\n                                                                                                    }, index, true, {\n                                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                        lineNumber: 2158,\n                                                                                                        columnNumber: 39\n                                                                                                    }, undefined))\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 2127,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 2126,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 2124,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1962,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1876,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1875,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 1615,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                lineNumber: 1525,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                        lineNumber: 1471,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"course-list-footer\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"course-list-footer-left\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handlePublish,\n                                    className: \"course-list-btn course-list-btn-publish\",\n                                    disabled: courseList.length === 0 || isPublishingSeries || seriesStatus === 1,\n                                    title: seriesStatus === 1 ? \"系列课程已发布\" : courseList.length === 0 ? \"发布失败：课程系列中至少需要包含一个课程\" : isPublishingSeries ? \"正在发布系列课程...\" : \"发布系列课程\",\n                                    children: seriesStatus === 1 ? \"已发布\" : isPublishingSeries ? \"正在发布...\" : \"发布系列课程\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 2193,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                lineNumber: 2192,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"course-list-footer-right\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleExitEdit,\n                                        className: \"course-list-btn course-list-btn-exit\",\n                                        children: \"退出编辑模式\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 2216,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handlePublishCourse,\n                                        className: \"course-list-btn course-list-btn-publish-course\",\n                                        disabled: !selectedCourseId || ((_courseList_find = courseList.find((c)=>c.id === selectedCourseId)) === null || _courseList_find === void 0 ? void 0 : _courseList_find.status) === 1 || isPublishing,\n                                        title: !selectedCourseId ? \"请先选择要发布的课程\" : ((_courseList_find1 = courseList.find((c)=>c.id === selectedCourseId)) === null || _courseList_find1 === void 0 ? void 0 : _courseList_find1.status) === 1 ? \"该课程已发布\" : isPublishing ? \"正在发布课程...\" : \"发布选中的课程\",\n                                        children: isPublishing ? \"正在发布...\" : \"发布课程\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 2219,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleSave,\n                                        className: \"course-list-btn course-list-btn-save\",\n                                        disabled: uploadingFiles.size > 0 || isCreating || courseList.length === 0,\n                                        title: courseList.length === 0 ? \"请先添加课程内容\" : uploadingFiles.size > 0 ? \"有文件正在上传中，请等待上传完成\" : isCreating ? courseList.some((c)=>c.id > 1000000) ? \"正在创建课程...\" : \"正在保存课程...\" : courseList.some((c)=>c.id > 1000000) ? \"创建课程\" : \"保存课程\",\n                                        children: uploadingFiles.size > 0 ? \"上传中...\" : isCreating ? courseList.some((c)=>c.id > 1000000) ? \"正在创建...\" : \"正在保存...\" : courseList.some((c)=>c.id > 1000000) ? \"创建课程\" : \"保存课程\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 2235,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                lineNumber: 2215,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                        lineNumber: 2191,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                lineNumber: 1448,\n                columnNumber: 7\n            }, undefined),\n            deleteConfirmVisible && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"modal-overlay\",\n                onClick: cancelDelete,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"delete-confirm-modal\",\n                    onClick: (e)=>e.stopPropagation(),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"delete-confirm-header\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    children: \"确认删除\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 2265,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: cancelDelete,\n                                    className: \"close-btn\",\n                                    disabled: isDeleting,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 2271,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 2266,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                            lineNumber: 2264,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"delete-confirm-content\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: isDeleting ? \"正在删除课程，请稍候...\" : \"确定要删除这个课程吗？删除后无法恢复。\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 2275,\n                                    columnNumber: 15\n                                }, undefined),\n                                isDeleting && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"delete-loading\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"loading-spinner\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 2283,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 2282,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                            lineNumber: 2274,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"delete-confirm-footer\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: cancelDelete,\n                                    className: \"cancel-btn\",\n                                    disabled: isDeleting,\n                                    children: \"取消\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 2288,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: confirmDeleteCourse,\n                                    className: \"confirm-btn\",\n                                    disabled: isDeleting,\n                                    children: isDeleting ? \"正在删除...\" : \"确认删除\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 2295,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                            lineNumber: 2287,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                    lineNumber: 2263,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                lineNumber: 2262,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TemplatePickerModal__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                isOpen: isTemplatePickerOpen,\n                onClose: ()=>setIsTemplatePickerOpen(false),\n                onTemplateSelect: handleTemplateSelect\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                lineNumber: 2308,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n        lineNumber: 1447,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CourseListEditModal, \"AqV9qJeo+atHtBhMm8SrUtJrNGY=\");\n_c = CourseListEditModal;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CourseListEditModal);\nvar _c;\n$RefreshReg$(_c, \"CourseListEditModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC93b3JrYmVuY2gvY29tcG9uZW50cy9Db3Vyc2VMaXN0RWRpdE1vZGFsLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUVnRTtBQUNLO0FBQ0g7QUFDckI7QUFDQTtBQUNGO0FBQ2I7QUFDOEM7QUFDcEI7QUFDckI7QUFDQTtBQWlFbkMsV0FBVztBQUNYLE1BQU1nQixvQkFBb0IsT0FBT0M7SUFDL0JDLFFBQVFDLEdBQUcsQ0FBQyxtQ0FBeUJGO0lBQ3JDQyxRQUFRQyxHQUFHLENBQUM7SUFFWixNQUFNQyxXQUFXLE1BQU1WLHNEQUFTQSxDQUFDVyxlQUFlLENBQUNKO0lBQ2pEQyxRQUFRQyxHQUFHLENBQUMsMkJBQWlCQztJQUM3QixPQUFPQSxTQUFTRSxJQUFJO0FBQ3RCO0FBRUEsa0NBQWtDO0FBQ2xDLE1BQU1DLGtCQUFrQixPQUFPTjtJQUM3QkMsUUFBUUMsR0FBRyxDQUFDLG1DQUF5QkY7SUFDckNDLFFBQVFDLEdBQUcsQ0FBQztJQUVaLHFCQUFxQjtJQUNyQixNQUFNQyxXQUFXLE1BQU1WLHNEQUFTQSxDQUFDYywwQkFBMEIsQ0FBQ1AsVUFBVTtRQUNwRVEsTUFBTTtRQUNOQyxVQUFVO0lBRVo7SUFFQVIsUUFBUUMsR0FBRyxDQUFDLHVCQUFhQztJQUN6QixPQUFPQSxTQUFTRSxJQUFJO0FBQ3RCO0FBRUEsU0FBUztBQUNULE1BQU1LLG9CQUFvQixPQUFPVixVQUFrQlc7SUFDakQsT0FBTyxNQUFNbEIsc0RBQVNBLENBQUNtQiwwQkFBMEIsQ0FBQ1osVUFBVVc7QUFDOUQ7QUFFQSxTQUFTO0FBQ1QsTUFBTUUsa0JBQWtCO0lBQ3RCLElBQUk7UUFDRlosUUFBUUMsR0FBRyxDQUFDO1FBQ1osTUFBTVksU0FBUyxNQUFNckIsc0RBQVNBLENBQUNzQixhQUFhLENBQUM7WUFDM0NQLE1BQU07WUFDTkMsVUFBVTtZQUNWTyxRQUFRLEVBQUUsV0FBVztRQUN2QjtRQUNBZixRQUFRQyxHQUFHLENBQUMsOENBQW9DWTtRQUNoRCxPQUFPQTtJQUNULEVBQUUsT0FBT0csT0FBTztRQUNkaEIsUUFBUWdCLEtBQUssQ0FBQyw4Q0FBb0NBO1FBQ2xELE1BQU1BO0lBQ1I7QUFDRjtBQUVBLE1BQU1DLHNCQUEwRDtRQUFDLEVBQy9EQyxTQUFTLEVBQ1RDLE9BQU8sRUFDUEMsTUFBTSxFQUNOQyxXQUFXLEVBQ1hDLGdCQUFnQixFQUNoQnZCLFdBQVcsSUFBSSwyQkFBMkI7SUFBNUIsRUFDZjtRQWs5Q21Ed0Isb0JBRVFBLHFCQWtDTEEscUJBV01BLHFCQTZCbENDLG1DQUFBQSw2QkErQ0FBLHNDQUFBQSw4QkE0ZGtCQyxrQkFJekJBOztJQTVpRWxCLE1BQU1DLGVBQWU5QiwwRkFBZUE7SUFFcEMsTUFBTSxDQUFDNkIsWUFBWUUsY0FBYyxHQUFHNUMsK0NBQVFBLENBQWUsRUFBRTtJQUM3RCxNQUFNLENBQUM2QyxTQUFTQyxXQUFXLEdBQUc5QywrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUMrQyxnQkFBZ0JDLGtCQUFrQixHQUFHaEQsK0NBQVFBLENBQWlDO0lBQ3JGLE1BQU0sQ0FBQ2lELGtCQUFrQkMsb0JBQW9CLEdBQUdsRCwrQ0FBUUEsQ0FBZ0I7SUFDeEUsTUFBTSxDQUFDbUQsZ0JBQWdCQyxrQkFBa0IsR0FBR3BELCtDQUFRQSxDQUFjLElBQUlxRDtJQUN0RSxNQUFNLENBQUNDLGNBQWNDLGdCQUFnQixHQUFHdkQsK0NBQVFBLENBQUM7SUFDakQsTUFBTSxDQUFDd0QsWUFBWUMsY0FBYyxHQUFHekQsK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDMEQsb0JBQW9CQyxzQkFBc0IsR0FBRzNELCtDQUFRQSxDQUFDO0lBQzdELE1BQU0sQ0FBQzRELGNBQWNDLGdCQUFnQixHQUFHN0QsK0NBQVFBLENBQVMsSUFBSSxtQkFBbUI7SUFFaEYsV0FBVztJQUNYLE1BQU0sQ0FBQzhELHNCQUFzQkMsd0JBQXdCLEdBQUcvRCwrQ0FBUUEsQ0FBQztJQUNqRSxNQUFNLENBQUNnRSxnQkFBZ0JDLGtCQUFrQixHQUFHakUsK0NBQVFBLENBQWdCO0lBQ3BFLE1BQU0sQ0FBQ2tFLFlBQVlDLGNBQWMsR0FBR25FLCtDQUFRQSxDQUFDO0lBRTdDLFdBQVc7SUFDWCxNQUFNLENBQUNvRSxzQkFBc0JDLHdCQUF3QixHQUFHckUsK0NBQVFBLENBQUM7SUFFakUsTUFBTSxDQUFDc0UsY0FBY0MsZ0JBQWdCLEdBQUd2RSwrQ0FBUUEsQ0FBQ3NDO0lBQ2pELE1BQU0sQ0FBQ2tDLGFBQWFDLGVBQWUsR0FBR3pFLCtDQUFRQSxDQUFDO0lBQy9DLE1BQU0sQ0FBQzBFLGtCQUFrQkMsb0JBQW9CLEdBQUczRSwrQ0FBUUEsQ0FBQztJQUN6RCxNQUFNLENBQUM0RSxnQkFBZ0JDLGtCQUFrQixHQUFHN0UsK0NBQVFBLENBQUM7SUFFckQsU0FBUztJQUNULE1BQU0sQ0FBQzhFLE9BQU9DLFNBQVMsR0FBRy9FLCtDQUFRQSxDQUFRLEVBQUU7SUFDNUMsTUFBTSxDQUFDZ0YsY0FBY0MsZ0JBQWdCLEdBQUdqRiwrQ0FBUUEsQ0FBQztJQUNqRCxNQUFNLENBQUNrRixpQkFBaUJDLG1CQUFtQixHQUFHbkYsK0NBQVFBLENBQVcsRUFBRTtJQUVuRSxXQUFXO0lBQ1gsTUFBTSxDQUFDb0YsYUFBYUMsZUFBZSxHQUFHckYsK0NBQVFBLENBQVM7SUFFdkQsU0FBUztJQUNULE1BQU0sQ0FBQ3NGLG9CQUFvQkMsc0JBQXNCLEdBQUd2RiwrQ0FBUUEsQ0FBQztJQUU3RCxXQUFXO0lBQ1gsTUFBTSxDQUFDd0YsWUFBWUMsY0FBYyxHQUFHekYsK0NBQVFBLENBQWMsRUFBRTtJQUM1RCxNQUFNLENBQUMwRixjQUFjQyxnQkFBZ0IsR0FBRzNGLCtDQUFRQSxDQUFXLEVBQUU7SUFDN0QsTUFBTSxDQUFDNEYsYUFBYUMsZUFBZSxHQUFHN0YsK0NBQVFBLENBQUM7SUFFL0MsV0FBVztJQUNYLE1BQU0sQ0FBQ3lDLGNBQWNxRCxnQkFBZ0IsR0FBRzlGLCtDQUFRQSxDQUFDO1FBQy9DK0YsT0FBTztRQUNQQyxhQUFhO1FBQ2JDLFlBQVk7UUFDWkMsVUFBVTtRQUNWQyxXQUFXO1FBQ1hDLGdCQUFnQjtRQUNoQkMsZUFBZTtRQUNmQyxnQkFBZ0I7UUFDaEJDLHFCQUFxQjtRQUNyQkMsbUJBQW1CLEVBQUU7UUFDckIsbUJBQW1CO1FBQ25CQyxjQUFjLEVBQUU7UUFDaEIsb0JBQW9CO1FBQ3BCQyxlQUFlO1lBQ2JDLFVBQVU7WUFDVkMsYUFBYTtZQUNiQyxVQUFVO1lBQ1ZDLE9BQU87Z0JBQUVDLEtBQUs7Z0JBQUlDLE1BQU07WUFBRztZQUMzQkMsVUFBVTtnQkFBRUYsS0FBSztnQkFBSUMsTUFBTTtZQUFHO1lBQzlCRSxPQUFPO2dCQUFFSCxLQUFLO2dCQUFJQyxNQUFNO1lBQUc7UUFDN0I7UUFDQUcsZUFBZTtZQUNiQyxPQUFPO1lBQ1BDLFNBQVM7UUFDWDtRQUNBQyxjQUFjO1FBQ2RDLHVCQUF1QjtRQUN2QkMsb0JBQW9CO1FBQ3BCQyxrQkFBa0I7WUFDaEJDLElBQUk7WUFDSlYsTUFBTTtRQUNSO1FBQ0FXLHFCQUFxQjtRQUNyQkMsZ0JBQWdCO1FBQ2hCQyxjQUFjO1FBQ2RDLHFCQUFxQjtRQUNyQkMsd0JBQXdCO1lBQ3RCQyxhQUFhO1lBQ2JDLFdBQVc7WUFDWEMsWUFBWTtRQUNkO1FBQ0FDLG9CQUFvQjtRQUNwQkMsNEJBQTRCO1lBQzFCQyxXQUFXO1lBQ1hDLFNBQVM7UUFDWDtRQUNBQyx1QkFBdUIsRUFBRTtRQUN6QixXQUFXO1FBQ1hDLFlBQVk7WUFDVkMsVUFBVTtZQUNWQyxjQUFjO1lBQ2RDLGlCQUFpQjtZQUNqQkMscUJBQXFCO2dCQUFDO2FBQUc7WUFDekJDLGdCQUFnQixFQUFFO1lBQ2xCQyxvQkFBb0IsRUFBRTtRQUN4QjtJQUNGO0lBRUEsV0FBVztJQUNYN0ksZ0RBQVNBLENBQUM7UUFDUixJQUFJa0MsYUFBYW5CLFVBQVU7WUFDekIsV0FBVztZQUNYLE1BQU0rSCxRQUFRQyxhQUFhQyxPQUFPLENBQUM7WUFDbkNoSSxRQUFRQyxHQUFHLENBQUMsZ0NBQXNCLENBQUMsQ0FBQzZIO1lBQ3BDOUgsUUFBUUMsR0FBRyxDQUFDLDBCQUFnQkY7WUFFNUIsSUFBSSxDQUFDK0gsT0FBTztnQkFDVjlILFFBQVFnQixLQUFLLENBQUM7Z0JBQ2QsY0FBYztnQkFDZFcsY0FBYyxFQUFFO2dCQUNoQkUsV0FBVztnQkFDWDtZQUNGO1lBRUFvRztZQUNBQztZQUNBQztZQUNBQztRQUNGO0lBQ0YsR0FBRztRQUFDbEg7UUFBV25CO0tBQVM7SUFFeEIsTUFBTWtJLGlCQUFpQjtRQUNyQixJQUFJO1lBQ0ZwRyxXQUFXO1lBQ1g3QixRQUFRQyxHQUFHLENBQUMsbUNBQXlCRjtZQUVyQyxNQUFNRyxXQUFXLE1BQU1HLGdCQUFnQk47WUFDdkNDLFFBQVFDLEdBQUcsQ0FBQyx1QkFBYUM7WUFFekIsSUFBSUEsU0FBU21JLElBQUksS0FBSyxLQUFLO2dCQUN6QnJJLFFBQVFDLEdBQUcsQ0FBQyxhQUFhQyxTQUFTRSxJQUFJO2dCQUN0QyxNQUFNa0ksVUFBVXBJLFNBQVNFLElBQUksQ0FBQ21JLElBQUksSUFBSSxFQUFFO2dCQUN4Q3ZJLFFBQVFDLEdBQUcsQ0FBQyxjQUFjcUk7Z0JBQzFCdEksUUFBUUMsR0FBRyxDQUFDLHdCQUFjcUksUUFBUUUsR0FBRyxDQUFDLENBQUNDLElBQVk7d0JBQ2pEaEMsSUFBSWdDLEVBQUVoQyxFQUFFO3dCQUNSaUMsTUFBTSxPQUFPRCxFQUFFaEMsRUFBRTt3QkFDakIzQixPQUFPMkQsRUFBRTNELEtBQUs7d0JBQ2QvRCxRQUFRMEgsRUFBRTFILE1BQU07b0JBQ2xCO2dCQUNBZixRQUFRQyxHQUFHLENBQUMsd0JBQWM7b0JBQ3hCMEksT0FBT0wsUUFBUU0sTUFBTTtvQkFDckJDLE9BQU9QLFFBQVFRLE1BQU0sQ0FBQyxDQUFDTCxJQUFXQSxFQUFFMUgsTUFBTSxLQUFLLEdBQUc2SCxNQUFNO29CQUN4REcsV0FBV1QsUUFBUVEsTUFBTSxDQUFDLENBQUNMLElBQVdBLEVBQUUxSCxNQUFNLEtBQUssR0FBRzZILE1BQU07Z0JBQzlEO2dCQUNBakgsY0FBYzJHO1lBQ2hCLE9BQU87Z0JBQ0x0SSxRQUFRZ0IsS0FBSyxDQUFDLGNBQWNkO2dCQUM1QnlCLGNBQWMsRUFBRTtZQUNsQjtRQUNGLEVBQUUsT0FBT1gsT0FBWTtnQkFJZkEsaUJBRU9BLGtCQUVBQTtZQVBYaEIsUUFBUWdCLEtBQUssQ0FBQyxlQUFlQTtZQUU3QixZQUFZO1lBQ1osSUFBSUEsRUFBQUEsa0JBQUFBLE1BQU1kLFFBQVEsY0FBZGMsc0NBQUFBLGdCQUFnQkQsTUFBTSxNQUFLLEtBQUs7Z0JBQ2xDZixRQUFRZ0IsS0FBSyxDQUFDO1lBQ2hCLE9BQU8sSUFBSUEsRUFBQUEsbUJBQUFBLE1BQU1kLFFBQVEsY0FBZGMsdUNBQUFBLGlCQUFnQkQsTUFBTSxNQUFLLEtBQUs7Z0JBQ3pDZixRQUFRZ0IsS0FBSyxDQUFDO1lBQ2hCLE9BQU8sSUFBSUEsRUFBQUEsbUJBQUFBLE1BQU1kLFFBQVEsY0FBZGMsdUNBQUFBLGlCQUFnQkQsTUFBTSxNQUFLLEtBQUs7Z0JBQ3pDZixRQUFRZ0IsS0FBSyxDQUFDLGtDQUF3QmpCO1lBQ3hDLE9BQU87Z0JBQ0xDLFFBQVFnQixLQUFLLENBQUMsc0JBQVlBLE1BQU1nSSxPQUFPO1lBQ3pDO1lBRUFySCxjQUFjLEVBQUU7UUFDbEIsU0FBVTtZQUNSRSxXQUFXO1FBQ2I7SUFDRjtJQUVBLFdBQVc7SUFDWCxNQUFNc0csbUJBQW1CO1FBQ3ZCLElBQUk7WUFDRm5JLFFBQVFDLEdBQUcsQ0FBQyxxQ0FBMkJGO1lBRXZDLE1BQU1HLFdBQVcsTUFBTUosa0JBQWtCQztZQUN6Q0MsUUFBUUMsR0FBRyxDQUFDLHdCQUFjQztZQUUxQixJQUFJQSxTQUFTbUksSUFBSSxLQUFLLE9BQU9uSSxTQUFTRSxJQUFJLEVBQUU7Z0JBQzFDLE1BQU02SSxhQUFhL0ksU0FBU0UsSUFBSTtnQkFDaENKLFFBQVFDLEdBQUcsQ0FBQyxhQUFhZ0o7Z0JBQ3pCckcsZ0JBQWdCcUcsV0FBV2xJLE1BQU0sSUFBSTtnQkFDckNmLFFBQVFDLEdBQUcsQ0FBQyx3QkFBY2dKLFdBQVdsSSxNQUFNLEVBQUU7WUFDL0MsT0FBTztnQkFDTGYsUUFBUWdCLEtBQUssQ0FBQyxlQUFlZCxTQUFTOEksT0FBTztZQUMvQztRQUNGLEVBQUUsT0FBT2hJLE9BQVk7WUFDbkJoQixRQUFRZ0IsS0FBSyxDQUFDLGVBQWVBO1FBQy9CO0lBQ0Y7SUFFQSxXQUFXO0lBQ1gsTUFBTW9ILGdCQUFnQjtRQUNwQixJQUFJO2dCQVdFbEk7WUFWSjhELGdCQUFnQjtZQUVoQixzQkFBc0I7WUFDdEIsTUFBTWtGLFNBQVNuQixhQUFhQyxPQUFPLENBQUMsYUFBYSxRQUFRLFdBQVc7WUFFcEUsTUFBTTlILFdBQVcsTUFBTVIsb0RBQVFBLENBQUN5SixlQUFlLENBQUNDLE9BQU9GLFNBQVMsR0FBRztZQUVuRSxjQUFjO1lBQ2QsSUFBSUcsWUFBWSxFQUFFO1lBRWxCLElBQUluSixFQUFBQSxpQkFBQUEsU0FBU0UsSUFBSSxjQUFiRixxQ0FBQUEsZUFBZW1JLElBQUksTUFBSyxLQUFLO29CQUUzQm5JLHFCQUFBQSxpQkFJbUJBO2dCQUx2QiwrQkFBK0I7Z0JBQy9CLEtBQUlBLGtCQUFBQSxTQUFTRSxJQUFJLGNBQWJGLHVDQUFBQSxzQkFBQUEsZ0JBQWVFLElBQUksY0FBbkJGLDBDQUFBQSxvQkFBcUJxSSxJQUFJLEVBQUU7b0JBQzdCYyxZQUFZbkosU0FBU0UsSUFBSSxDQUFDQSxJQUFJLENBQUNtSSxJQUFJO2dCQUNyQyxPQUVLLElBQUllLE1BQU1DLE9BQU8sRUFBQ3JKLGtCQUFBQSxTQUFTRSxJQUFJLGNBQWJGLHNDQUFBQSxnQkFBZUUsSUFBSSxHQUFHO29CQUMzQ2lKLFlBQVluSixTQUFTRSxJQUFJLENBQUNBLElBQUk7Z0JBQ2hDLE9BRUssSUFBSWtKLE1BQU1DLE9BQU8sQ0FBQ3JKLFNBQVNFLElBQUksR0FBRztvQkFDckNpSixZQUFZbkosU0FBU0UsSUFBSTtnQkFDM0I7WUFDRixPQUVLLElBQUlrSixNQUFNQyxPQUFPLENBQUNySixXQUFXO2dCQUNoQ21KLFlBQVluSjtZQUNkO1lBRUE0RCxTQUFTdUY7UUFDWCxFQUFFLE9BQU9ySSxPQUFPO1lBQ2RoQixRQUFRZ0IsS0FBSyxDQUFDLGFBQWFBO1lBQzNCOEMsU0FBUyxFQUFFO1FBQ2IsU0FBVTtZQUNSRSxnQkFBZ0I7UUFDbEI7SUFDRjtJQUVBLFNBQVM7SUFDVCxNQUFNd0YsbUJBQW1CLENBQUNDO1FBQ3hCLElBQUl4RixnQkFBZ0J5RixRQUFRLENBQUNELFNBQVM7WUFDcEMsT0FBTztZQUNQdkYsbUJBQW1CeUYsQ0FBQUEsT0FBUUEsS0FBS2IsTUFBTSxDQUFDckMsQ0FBQUEsS0FBTUEsT0FBT2dEO1FBQ3RELE9BQU87WUFDTCxLQUFLO1lBQ0x2RixtQkFBbUJ5RixDQUFBQSxPQUFRO3VCQUFJQTtvQkFBTUY7aUJBQU87UUFDOUM7SUFDRjtJQUVBLDBCQUEwQjtJQUMxQixNQUFNRyxjQUFjM0ssa0RBQVdBLENBQUMsQ0FBQzRLO1FBQy9CLE1BQU1DLFlBQVlELEVBQUVFLGFBQWE7UUFDakMsTUFBTSxFQUFFQyxXQUFXLEVBQUVDLFdBQVcsRUFBRSxHQUFHSDtRQUVyQyxXQUFXO1FBQ1gsSUFBSUUsZUFBZUMsYUFBYTtZQUM5QixRQUFRLGNBQWM7UUFDeEI7UUFFQSxjQUFjO1FBQ2RKLEVBQUVLLGNBQWM7UUFFaEIsZUFBZTtRQUNmSixVQUFVSyxVQUFVLElBQUlOLEVBQUVPLE1BQU07SUFDbEMsR0FBRyxFQUFFO0lBRUwsV0FBVztJQUNYLE1BQU1DLHFCQUFxQixDQUFDQztRQUMxQnpGLGdCQUFnQjhFLENBQUFBLE9BQVM7Z0JBQUUsR0FBR0EsSUFBSTtnQkFBRWhELGdCQUFnQjJEO1lBQU07UUFFMUQsUUFBUTtRQUNSLE1BQU1DLFdBQVdDLFdBQVdGO1FBQzVCLElBQUlBLFNBQVVHLENBQUFBLE1BQU1GLGFBQWFBLFdBQVcsSUFBSTtZQUM5Q25HLGVBQWU7UUFDakIsT0FBTztZQUNMQSxlQUFlO1FBQ2pCO0lBQ0Y7SUFFQSxTQUFTO0lBQ1QsTUFBTXNHLHVCQUF1QixDQUFDQyxPQUFlQztRQUMzQy9GLGdCQUFnQjhFLENBQUFBLE9BQVM7Z0JBQ3ZCLEdBQUdBLElBQUk7Z0JBQ1BwQyxZQUFZO29CQUFFLEdBQUdvQyxLQUFLcEMsVUFBVTtvQkFBRUUsY0FBYyxHQUFlbUQsT0FBWkQsT0FBTSxRQUFZLE9BQU5DLE9BQU07Z0JBQUc7WUFDMUU7UUFDQXRHLHNCQUFzQjtJQUN4QjtJQUlBLGNBQWM7SUFDZHRGLGdEQUFTQSxDQUFDO1FBQ1IsTUFBTTZMLHFCQUFxQixDQUFDQztZQUMxQixNQUFNQyxTQUFTRCxNQUFNQyxNQUFNO1lBQzNCLElBQUksQ0FBQ0EsT0FBT0MsT0FBTyxDQUFDLDRCQUE0QjtnQkFDOUMxRyxzQkFBc0I7WUFDeEI7UUFDRjtRQUVBLElBQUlELG9CQUFvQjtZQUN0QjJCLFNBQVNpRixnQkFBZ0IsQ0FBQyxhQUFhSjtZQUN2QyxPQUFPLElBQU03RSxTQUFTa0YsbUJBQW1CLENBQUMsYUFBYUw7UUFDekQ7SUFDRixHQUFHO1FBQUN4RztLQUFtQjtJQUV2QixTQUFTO0lBQ1QsTUFBTTZELGlCQUFpQjtRQUNyQixJQUFJO1lBQ0Z0RCxlQUFlO1lBQ2Y1RSxRQUFRQyxHQUFHLENBQUM7WUFJWixNQUFNQyxXQUFXLE1BQU1VO1lBQ3ZCWixRQUFRQyxHQUFHLENBQUMsMkJBQWlCQztZQUU3QixTQUFTO1lBQ1QsSUFBSUEsWUFBWUEsU0FBU0UsSUFBSSxFQUFFO2dCQUM3QkosUUFBUUMsR0FBRyxDQUFDLHNCQUFZQyxTQUFTRSxJQUFJO2dCQUVyQyxJQUFJK0ssT0FBb0IsRUFBRTtnQkFFMUIsZ0RBQWdEO2dCQUNoRCxJQUFJakwsU0FBU0UsSUFBSSxDQUFDbUksSUFBSSxJQUFJZSxNQUFNQyxPQUFPLENBQUNySixTQUFTRSxJQUFJLENBQUNtSSxJQUFJLEdBQUc7b0JBQzNENEMsT0FBT2pMLFNBQVNFLElBQUksQ0FBQ21JLElBQUk7b0JBQ3pCdkksUUFBUUMsR0FBRyxDQUFDLHdCQUF3QmtMLEtBQUt2QyxNQUFNLEVBQUU7Z0JBQ25ELE9BRUssSUFBSVUsTUFBTUMsT0FBTyxDQUFDckosU0FBU0UsSUFBSSxHQUFHO29CQUNyQytLLE9BQU9qTCxTQUFTRSxJQUFJO29CQUNwQkosUUFBUUMsR0FBRyxDQUFDLHFCQUFxQmtMLEtBQUt2QyxNQUFNLEVBQUU7Z0JBQ2hELE9BRUssSUFBSTFJLFNBQVNFLElBQUksQ0FBQ0EsSUFBSSxJQUFJRixTQUFTRSxJQUFJLENBQUNBLElBQUksQ0FBQ21JLElBQUksSUFBSWUsTUFBTUMsT0FBTyxDQUFDckosU0FBU0UsSUFBSSxDQUFDQSxJQUFJLENBQUNtSSxJQUFJLEdBQUc7b0JBQ2hHNEMsT0FBT2pMLFNBQVNFLElBQUksQ0FBQ0EsSUFBSSxDQUFDbUksSUFBSTtvQkFDOUJ2SSxRQUFRQyxHQUFHLENBQUMsNkJBQTZCa0wsS0FBS3ZDLE1BQU0sRUFBRTtnQkFDeEQ7Z0JBRUEsV0FBVztnQkFDWDVJLFFBQVFDLEdBQUcsQ0FBQyx3QkFBY2tMO2dCQUMxQm5MLFFBQVFDLEdBQUcsQ0FBQztnQkFDWmtMLEtBQUtDLE9BQU8sQ0FBQyxDQUFDQyxLQUFLQzt3QkFPREQ7b0JBTmhCckwsUUFBUUMsR0FBRyxDQUFDLEtBQVcsT0FBTnFMLE9BQU0sTUFBSTt3QkFDekJEO3dCQUNBRSxRQUFRLENBQUMsQ0FBQ0Y7d0JBQ1ZHLFFBQVEsUUFBT0gsZ0JBQUFBLDBCQUFBQSxJQUFLNUUsRUFBRTt3QkFDdEJnRixVQUFVLFFBQU9KLGdCQUFBQSwwQkFBQUEsSUFBS3RGLElBQUk7d0JBQzFCMkYsU0FBUyxFQUFFTCxnQkFBQUEsMEJBQUFBLElBQUt0RixJQUFJO3dCQUNwQjRGLGNBQWNOLENBQUFBLGdCQUFBQSwyQkFBQUEsWUFBQUEsSUFBS3RGLElBQUksY0FBVHNGLGdDQUFBQSxVQUFXTyxJQUFJLFFBQU87b0JBQ3RDO2dCQUNGO2dCQUVBLE1BQU1DLFlBQVlWLEtBQUtyQyxNQUFNLENBQUN1QyxDQUFBQTtvQkFDNUIsTUFBTVMsVUFBVVQsT0FDZCxPQUFPQSxJQUFJNUUsRUFBRSxLQUFLLFlBQ2xCLE9BQU80RSxJQUFJdEYsSUFBSSxLQUFLLFlBQ3BCc0YsSUFBSXRGLElBQUksQ0FBQzZGLElBQUksT0FBTztvQkFFdEIsSUFBSSxDQUFDRSxTQUFTO3dCQUNaOUwsUUFBUUMsR0FBRyxDQUFDLFdBQVdvTCxLQUFLOzRCQUMxQkUsUUFBUSxDQUFDLENBQUNGOzRCQUNWRyxRQUFRLFFBQU9ILGdCQUFBQSwwQkFBQUEsSUFBSzVFLEVBQUU7NEJBQ3RCZ0YsVUFBVSxRQUFPSixnQkFBQUEsMEJBQUFBLElBQUt0RixJQUFJOzRCQUMxQjJGLFNBQVMsRUFBRUwsZ0JBQUFBLDBCQUFBQSxJQUFLdEYsSUFBSTt3QkFDdEI7b0JBQ0Y7b0JBRUEsT0FBTytGO2dCQUNUO2dCQUVBOUwsUUFBUUMsR0FBRyxDQUFDLGFBQWE0TCxVQUFVakQsTUFBTTtnQkFDekM1SSxRQUFRQyxHQUFHLENBQUMsYUFBYTRMO2dCQUV6QixJQUFJQSxVQUFVakQsTUFBTSxHQUFHLEdBQUc7b0JBQ3hCcEUsY0FBY3FIO29CQUNkN0wsUUFBUUMsR0FBRyxDQUFDO29CQUNaO2dCQUNGLE9BQU87b0JBQ0xELFFBQVErTCxJQUFJLENBQUM7Z0JBQ2Y7WUFDRixPQUFPO2dCQUNML0wsUUFBUStMLElBQUksQ0FBQyxrQkFBa0I3TDtZQUNqQztZQUVBLGlCQUFpQjtZQUNqQkYsUUFBUUMsR0FBRyxDQUFDO1lBQ1p1RSxjQUFjLEVBQUU7UUFFbEIsRUFBRSxPQUFPeEQsT0FBWTtnQkFJVEEsaUJBQ0lBLGtCQUNOQTtZQUxSaEIsUUFBUWdCLEtBQUssQ0FBQyxlQUFlQTtZQUM3QmhCLFFBQVFnQixLQUFLLENBQUMsV0FBVztnQkFDdkJnSSxTQUFTaEksTUFBTWdJLE9BQU87Z0JBQ3RCakksTUFBTSxHQUFFQyxrQkFBQUEsTUFBTWQsUUFBUSxjQUFkYyxzQ0FBQUEsZ0JBQWdCRCxNQUFNO2dCQUM5QmlMLFVBQVUsR0FBRWhMLG1CQUFBQSxNQUFNZCxRQUFRLGNBQWRjLHVDQUFBQSxpQkFBZ0JnTCxVQUFVO2dCQUN0QzVMLElBQUksR0FBRVksbUJBQUFBLE1BQU1kLFFBQVEsY0FBZGMsdUNBQUFBLGlCQUFnQlosSUFBSTtZQUM1QjtZQUVBLGFBQWE7WUFDYm9FLGNBQWMsRUFBRTtRQUNsQixTQUFVO1lBQ1JJLGVBQWU7UUFDakI7SUFDRjtJQUVBLFFBQVE7SUFDUixNQUFNcUgsZUFBZTtRQUNuQixNQUFNQyxZQUF3QjtZQUM1QnpGLElBQUkwRixLQUFLQyxHQUFHO1lBQ1pyTSxVQUFVQTtZQUNWK0UsT0FBTyxJQUEwQixPQUF0QnJELFdBQVdtSCxNQUFNLEdBQUcsR0FBRTtZQUNqQzdELGFBQWE7WUFDYkMsWUFBWTtZQUNacUgsWUFBWTVLLFdBQVdtSCxNQUFNLEdBQUc7WUFDaEM3SCxRQUFRO1lBQ1J1TCxhQUFhO1lBQ2I1RyxVQUFVO1lBQ1ZDLGFBQWE7WUFDYkMsVUFBVTtZQUNWMkcsZUFBZTtZQUNmQyxvQkFBb0I7WUFDcEJ0SCxXQUFXO1lBQ1h1SCxvQkFBb0I7WUFDcEJDLGdCQUFnQjtZQUNoQkMsV0FBVyxJQUFJUixPQUFPUyxXQUFXO1lBQ2pDQyxXQUFXLElBQUlWLE9BQU9TLFdBQVc7UUFDbkM7UUFDQWpMLGNBQWM7ZUFBSUY7WUFBWXlLO1NBQVU7UUFDeEMsYUFBYTtRQUNiWSxnQkFBZ0JaLFVBQVV6RixFQUFFO0lBQzlCO0lBRUEsV0FBVztJQUNYLE1BQU1zRyxvQkFBb0IsQ0FBQ3RHO1FBQ3pCekQsa0JBQWtCeUQ7UUFDbEIzRCx3QkFBd0I7SUFDMUI7SUFFQSxTQUFTO0lBQ1QsTUFBTWtLLHNCQUFzQjtRQUMxQixJQUFJLENBQUNqSyxnQkFBZ0I7UUFFckIsSUFBSTtZQUNGRyxjQUFjO1lBRWQsVUFBVTtZQUNWLE1BQU0zRCwyRUFBbUJBLENBQUMwTixZQUFZLENBQUNsSztZQUV2QyxXQUFXO1lBQ1hwQixjQUFjRixXQUFXcUgsTUFBTSxDQUFDb0UsQ0FBQUEsU0FBVUEsT0FBT3pHLEVBQUUsS0FBSzFEO1lBRXhELHVCQUF1QjtZQUN2QixJQUFJZixxQkFBcUJlLGdCQUFnQjtnQkFDdkNoQixrQkFBa0I7Z0JBQ2xCRSxvQkFBb0I7WUFDdEI7WUFFQSxTQUFTO1lBQ1RhLHdCQUF3QjtZQUN4QkUsa0JBQWtCO1lBRWxCLFNBQVM7WUFDVHRCLGFBQWF5TCxPQUFPLENBQUM7UUFDdkIsRUFBRSxPQUFPbk0sT0FBTztZQUNkaEIsUUFBUWdCLEtBQUssQ0FBQyxXQUFXQTtZQUN6QlUsYUFBYVYsS0FBSyxDQUFDO1FBQ3JCLFNBQVU7WUFDUmtDLGNBQWM7UUFDaEI7SUFDRjtJQUVBLE9BQU87SUFDUCxNQUFNa0ssZUFBZTtRQUNuQixJQUFJbkssWUFBWSxRQUFRLGFBQWE7UUFDckNILHdCQUF3QjtRQUN4QkUsa0JBQWtCO0lBQ3BCO0lBRUEsU0FBUztJQUNULE1BQU1xSyxvQkFBb0IsQ0FBQzVHLElBQVk2RztRQUNyQzNMLGNBQWNGLFdBQVcrRyxHQUFHLENBQUMwRSxDQUFBQSxTQUMzQkEsT0FBT3pHLEVBQUUsS0FBS0EsS0FBSztnQkFBRSxHQUFHeUcsTUFBTTtnQkFBRXBJLE9BQU93STtZQUFTLElBQUlKO0lBRXhEO0lBRUEsV0FBVztJQUNYLE1BQU1LLG9CQUFvQixPQUFPekM7WUFDbEJBO1FBQWIsTUFBTTBDLFFBQU8xQyxzQkFBQUEsTUFBTUMsTUFBTSxDQUFDMEMsS0FBSyxjQUFsQjNDLDBDQUFBQSxtQkFBb0IsQ0FBQyxFQUFFO1FBQ3BDLElBQUkwQyxNQUFNO1lBQ1IsU0FBUztZQUNULE1BQU1FLGVBQWU7Z0JBQUM7Z0JBQWM7Z0JBQWE7Z0JBQWE7YUFBWTtZQUMxRSxJQUFJLENBQUNBLGFBQWFoRSxRQUFRLENBQUM4RCxLQUFLOUUsSUFBSSxHQUFHO2dCQUNyQ2hILGFBQWFWLEtBQUssQ0FBQztnQkFDbkI7WUFDRjtZQUVBLGdCQUFnQjtZQUNoQixJQUFJd00sS0FBS0csSUFBSSxHQUFHLEtBQUssT0FBTyxNQUFNO2dCQUNoQ2pNLGFBQWFWLEtBQUssQ0FBQztnQkFDbkI7WUFDRjtZQUVBLElBQUk7Z0JBQ0ZoQixRQUFRQyxHQUFHLENBQUMsMEJBQWdCdU4sS0FBS3pILElBQUk7Z0JBRXJDLGNBQWM7Z0JBQ2Q1RCxrQkFBa0J3SCxDQUFBQSxPQUFRLElBQUl2SCxJQUFJdUgsTUFBTWlFLEdBQUcsQ0FBQztnQkFFNUMsVUFBVTtnQkFDVixNQUFNQyxhQUFhQyxJQUFJQyxlQUFlLENBQUNQO2dCQUN2QzNJLGdCQUFnQjhFLENBQUFBLE9BQVM7d0JBQUUsR0FBR0EsSUFBSTt3QkFBRTNFLFlBQVk2STtvQkFBVztnQkFFM0QsU0FBUztnQkFDVCxNQUFNRyxXQUFXLE1BQU12TyxzREFBU0EsQ0FBQ3dPLFdBQVcsQ0FBQ1Q7Z0JBQzdDeE4sUUFBUUMsR0FBRyxDQUFDLGVBQWUrTjtnQkFFM0IsbUJBQW1CO2dCQUNuQm5KLGdCQUFnQjhFLENBQUFBLE9BQVM7d0JBQUUsR0FBR0EsSUFBSTt3QkFBRTNFLFlBQVlnSjtvQkFBUztnQkFFekQsZUFBZTtnQkFDZixJQUFJaE0sa0JBQWtCO29CQUNwQkwsY0FBY2dJLENBQUFBLE9BQVFBLEtBQUtuQixHQUFHLENBQUMwRSxDQUFBQSxTQUM3QkEsT0FBT3pHLEVBQUUsS0FBS3pFLG1CQUFtQjtnQ0FBRSxHQUFHa0wsTUFBTTtnQ0FBRWxJLFlBQVlnSjs0QkFBUyxJQUFJZDtnQkFFM0U7Z0JBRUFnQixNQUFNO1lBQ1IsRUFBRSxPQUFPbE4sT0FBTztnQkFDZGhCLFFBQVFnQixLQUFLLENBQUMsZUFBZUE7Z0JBQzdCVSxhQUFhVixLQUFLLENBQUM7Z0JBRW5CLGNBQWM7Z0JBQ2Q2RCxnQkFBZ0I4RSxDQUFBQSxPQUFTO3dCQUFFLEdBQUdBLElBQUk7d0JBQUUzRSxZQUFZO29CQUFHO1lBQ3JELFNBQVU7Z0JBQ1IsY0FBYztnQkFDZDdDLGtCQUFrQndILENBQUFBO29CQUNoQixNQUFNd0UsU0FBUyxJQUFJL0wsSUFBSXVIO29CQUN2QndFLE9BQU9DLE1BQU0sQ0FBQztvQkFDZCxPQUFPRDtnQkFDVDtZQUNGO1FBQ0Y7SUFDRjtJQUVBLFNBQVM7SUFDVCxNQUFNRSxvQkFBb0IsT0FBT3ZEO1lBQ2xCQTtRQUFiLE1BQU0wQyxRQUFPMUMsc0JBQUFBLE1BQU1DLE1BQU0sQ0FBQzBDLEtBQUssY0FBbEIzQywwQ0FBQUEsbUJBQW9CLENBQUMsRUFBRTtRQUNwQyxJQUFJMEMsTUFBTTtZQUNSLFNBQVM7WUFDVCxNQUFNRSxlQUFlO2dCQUFDO2dCQUFhO2dCQUFhO2dCQUFhO2dCQUFhO2FBQVk7WUFDdEYsSUFBSSxDQUFDQSxhQUFhaEUsUUFBUSxDQUFDOEQsS0FBSzlFLElBQUksR0FBRztnQkFDckN3RixNQUFNO2dCQUNOO1lBQ0Y7WUFFQSxpQkFBaUI7WUFDakIsSUFBSVYsS0FBS0csSUFBSSxHQUFHLE1BQU0sT0FBTyxNQUFNO2dCQUNqQ08sTUFBTTtnQkFDTjtZQUNGO1lBRUEsSUFBSTtnQkFDRmxPLFFBQVFDLEdBQUcsQ0FBQywwQkFBZ0J1TixLQUFLekgsSUFBSTtnQkFFckMsY0FBYztnQkFDZDVELGtCQUFrQndILENBQUFBLE9BQVEsSUFBSXZILElBQUl1SCxNQUFNaUUsR0FBRyxDQUFDO2dCQUU1QyxVQUFVO2dCQUNWLE1BQU1DLGFBQWFDLElBQUlDLGVBQWUsQ0FBQ1A7Z0JBQ3ZDM0ksZ0JBQWdCOEUsQ0FBQUEsT0FBUzt3QkFDdkIsR0FBR0EsSUFBSTt3QkFDUGxFLGVBQWU7NEJBQ2IsR0FBR2tFLEtBQUtsRSxhQUFhOzRCQUNyQkksT0FBTztnQ0FDTEMsS0FBSytIO2dDQUNMOUgsTUFBTXlILEtBQUt6SCxJQUFJOzRCQUNqQjt3QkFDRjtvQkFDRjtnQkFFQSxTQUFTO2dCQUNULE1BQU1kLFdBQVcsTUFBTXhGLHNEQUFTQSxDQUFDd08sV0FBVyxDQUFDVDtnQkFDN0N4TixRQUFRQyxHQUFHLENBQUMsZUFBZWdGO2dCQUUzQixxQkFBcUI7Z0JBQ3JCSixnQkFBZ0I4RSxDQUFBQSxPQUFTO3dCQUN2QixHQUFHQSxJQUFJO3dCQUNQbEUsZUFBZTs0QkFDYixHQUFHa0UsS0FBS2xFLGFBQWE7NEJBQ3JCSSxPQUFPO2dDQUNMQyxLQUFLYjtnQ0FDTGMsTUFBTXlILEtBQUt6SCxJQUFJOzRCQUNqQjt3QkFDRjtvQkFDRjtnQkFFQSxpQkFBaUI7Z0JBQ2pCLElBQUkvRCxrQkFBa0I7b0JBQ3BCTCxjQUFjZ0ksQ0FBQUEsT0FBUUEsS0FBS25CLEdBQUcsQ0FBQzBFLENBQUFBLFNBQzdCQSxPQUFPekcsRUFBRSxLQUFLekUsbUJBQW1CO2dDQUMvQixHQUFHa0wsTUFBTTtnQ0FDVHpILGVBQWU7b0NBQ2IsR0FBR3lILE9BQU96SCxhQUFhO29DQUN2QkksT0FBTzt3Q0FDTEMsS0FBS2I7d0NBQ0xjLE1BQU15SCxLQUFLekgsSUFBSTtvQ0FDakI7Z0NBQ0Y7NEJBQ0YsSUFBSW1IO2dCQUVSO2dCQUVBZ0IsTUFBTTtZQUNSLEVBQUUsT0FBT2xOLE9BQU87Z0JBQ2RoQixRQUFRZ0IsS0FBSyxDQUFDLGVBQWVBO2dCQUM3QmtOLE1BQU07Z0JBRU4sY0FBYztnQkFDZHJKLGdCQUFnQjhFLENBQUFBLE9BQVM7d0JBQ3ZCLEdBQUdBLElBQUk7d0JBQ1BsRSxlQUFlOzRCQUNiLEdBQUdrRSxLQUFLbEUsYUFBYTs0QkFDckJJLE9BQU87Z0NBQ0xDLEtBQUs7Z0NBQ0xDLE1BQU07NEJBQ1I7d0JBQ0Y7b0JBQ0Y7WUFDRixTQUFVO2dCQUNSLGNBQWM7Z0JBQ2Q1RCxrQkFBa0J3SCxDQUFBQTtvQkFDaEIsTUFBTXdFLFNBQVMsSUFBSS9MLElBQUl1SDtvQkFDdkJ3RSxPQUFPQyxNQUFNLENBQUM7b0JBQ2QsT0FBT0Q7Z0JBQ1Q7WUFDRjtRQUNGO0lBQ0Y7SUFFQSxXQUFXO0lBQ1gsTUFBTUcscUJBQXFCO1FBQ3pCLE1BQU1DLFFBQVF2SSxTQUFTd0ksYUFBYSxDQUFDO1FBQ3JDRCxNQUFNN0YsSUFBSSxHQUFHO1FBQ2I2RixNQUFNRSxNQUFNLEdBQUc7UUFDZkYsTUFBTUcsUUFBUSxHQUFHLENBQUM3RSxJQUFNd0Usa0JBQWtCeEU7UUFDMUMwRSxNQUFNSSxLQUFLO0lBQ2I7SUFFQSxTQUFTO0lBQ1QsTUFBTUMseUJBQXlCLE9BQU85RDtZQUN2QkE7UUFBYixNQUFNMEMsUUFBTzFDLHNCQUFBQSxNQUFNQyxNQUFNLENBQUMwQyxLQUFLLGNBQWxCM0MsMENBQUFBLG1CQUFvQixDQUFDLEVBQUU7UUFDcEMsSUFBSTBDLE1BQU07WUFDUixTQUFTO1lBQ1QsTUFBTUUsZUFBZTtnQkFDbkI7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7YUFDRDtZQUVELElBQUksQ0FBQ0EsYUFBYWhFLFFBQVEsQ0FBQzhELEtBQUs5RSxJQUFJLEdBQUc7Z0JBQ3JDd0YsTUFBTTtnQkFDTjtZQUNGO1lBRUEsZ0JBQWdCO1lBQ2hCLElBQUlWLEtBQUtHLElBQUksR0FBRyxLQUFLLE9BQU8sTUFBTTtnQkFDaENPLE1BQU07Z0JBQ047WUFDRjtZQUVBLElBQUk7Z0JBQ0ZsTyxRQUFRQyxHQUFHLENBQUMsMEJBQWdCdU4sS0FBS3pILElBQUk7Z0JBRXJDLGNBQWM7Z0JBQ2Q1RCxrQkFBa0J3SCxDQUFBQSxPQUFRLElBQUl2SCxJQUFJdUgsTUFBTWlFLEdBQUcsQ0FBQztnQkFFNUMsU0FBUztnQkFDVCxNQUFNaUIsY0FBYyxNQUFNcFAsc0RBQVNBLENBQUN3TyxXQUFXLENBQUNUO2dCQUNoRHhOLFFBQVFDLEdBQUcsQ0FBQyxlQUFlNE87Z0JBRTNCLGVBQWU7Z0JBQ2ZoSyxnQkFBZ0I4RSxDQUFBQSxPQUFTO3dCQUN2QixHQUFHQSxJQUFJO3dCQUNQbEUsZUFBZTs0QkFDYixHQUFHa0UsS0FBS2xFLGFBQWE7NEJBQ3JCRSxhQUFhOzRCQUNiSyxVQUFVO2dDQUNSRixLQUFLK0k7Z0NBQ0w5SSxNQUFNeUgsS0FBS3pILElBQUk7NEJBQ2pCO3dCQUNGO29CQUNGO2dCQUVBbUksTUFBTTtZQUNSLEVBQUUsT0FBT2xOLE9BQU87Z0JBQ2RoQixRQUFRZ0IsS0FBSyxDQUFDLGVBQWVBO2dCQUM3QmtOLE1BQU07WUFDUixTQUFVO2dCQUNSLGNBQWM7Z0JBQ2QvTCxrQkFBa0J3SCxDQUFBQTtvQkFDaEIsTUFBTXdFLFNBQVMsSUFBSS9MLElBQUl1SDtvQkFDdkJ3RSxPQUFPQyxNQUFNLENBQUM7b0JBQ2QsT0FBT0Q7Z0JBQ1Q7WUFDRjtRQUNGO0lBQ0Y7SUFFQSxXQUFXO0lBQ1gsTUFBTVcsMEJBQTBCO1FBQzlCLE1BQU1QLFFBQVF2SSxTQUFTd0ksYUFBYSxDQUFDO1FBQ3JDRCxNQUFNN0YsSUFBSSxHQUFHO1FBQ2I2RixNQUFNRSxNQUFNLEdBQUc7UUFDZkYsTUFBTUcsUUFBUSxHQUFHLENBQUM3RSxJQUFNK0UsdUJBQXVCL0U7UUFDL0MwRSxNQUFNSSxLQUFLO0lBQ2I7SUFFQSxXQUFXO0lBQ1gsTUFBTUksK0JBQStCLE9BQU9qRTtZQUM3QkE7UUFBYixNQUFNMEMsUUFBTzFDLHNCQUFBQSxNQUFNQyxNQUFNLENBQUMwQyxLQUFLLGNBQWxCM0MsMENBQUFBLG1CQUFvQixDQUFDLEVBQUU7UUFDcEMsSUFBSTBDLE1BQU07WUFDUixTQUFTO1lBQ1QsTUFBTUUsZUFBZTtnQkFDbkI7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7YUFDRDtZQUVELElBQUksQ0FBQ0EsYUFBYWhFLFFBQVEsQ0FBQzhELEtBQUs5RSxJQUFJLEdBQUc7Z0JBQ3JDd0YsTUFBTTtnQkFDTjtZQUNGO1lBRUEsZ0JBQWdCO1lBQ2hCLElBQUlWLEtBQUtHLElBQUksR0FBRyxLQUFLLE9BQU8sTUFBTTtnQkFDaENPLE1BQU07Z0JBQ047WUFDRjtZQUVBLElBQUk7Z0JBQ0ZsTyxRQUFRQyxHQUFHLENBQUMsMEJBQWdCdU4sS0FBS3pILElBQUk7Z0JBRXJDLGNBQWM7Z0JBQ2Q1RCxrQkFBa0J3SCxDQUFBQSxPQUFRLElBQUl2SCxJQUFJdUgsTUFBTWlFLEdBQUcsQ0FBQyxZQUF1QixPQUFYekIsS0FBS0MsR0FBRztnQkFFaEUsU0FBUztnQkFDVCxNQUFNNEMsY0FBYyxNQUFNdlAsc0RBQVNBLENBQUN3TyxXQUFXLENBQUNUO2dCQUNoRHhOLFFBQVFDLEdBQUcsQ0FBQyxlQUFlK087Z0JBRTNCLFlBQVk7Z0JBQ1osTUFBTUMsY0FBYztvQkFDbEJ2RyxNQUFNOEUsS0FBSzlFLElBQUk7b0JBQ2YzQyxNQUFNeUgsS0FBS3pILElBQUk7b0JBQ2ZELEtBQUtrSjtnQkFDUDtnQkFFQW5LLGdCQUFnQjhFLENBQUFBLE9BQVM7d0JBQ3ZCLEdBQUdBLElBQUk7d0JBQ1BwRSxtQkFBbUI7K0JBQUlvRSxLQUFLcEUsaUJBQWlCOzRCQUFFMEo7eUJBQVk7b0JBQzdEO2dCQUVBZixNQUFNO1lBQ1IsRUFBRSxPQUFPbE4sT0FBTztnQkFDZGhCLFFBQVFnQixLQUFLLENBQUMsZUFBZUE7Z0JBQzdCa04sTUFBTTtZQUNSLFNBQVU7Z0JBQ1IsY0FBYztnQkFDZC9MLGtCQUFrQndILENBQUFBO29CQUNoQixNQUFNd0UsU0FBUyxJQUFJL0wsSUFBSXVIO29CQUN2QixxQkFBcUI7b0JBQ3JCTCxNQUFNNEYsSUFBSSxDQUFDZixRQUFRL0MsT0FBTyxDQUFDK0QsQ0FBQUE7d0JBQ3pCLElBQUlBLEtBQUtDLFVBQVUsQ0FBQyxjQUFjOzRCQUNoQ2pCLE9BQU9DLE1BQU0sQ0FBQ2U7d0JBQ2hCO29CQUNGO29CQUNBLE9BQU9oQjtnQkFDVDtZQUNGO1FBQ0Y7SUFDRjtJQUVBLGFBQWE7SUFDYixNQUFNa0IsZ0NBQWdDO1FBQ3BDLE1BQU1kLFFBQVF2SSxTQUFTd0ksYUFBYSxDQUFDO1FBQ3JDRCxNQUFNN0YsSUFBSSxHQUFHO1FBQ2I2RixNQUFNRSxNQUFNLEdBQUc7UUFDZkYsTUFBTUcsUUFBUSxHQUFHLENBQUM3RSxJQUFNa0YsNkJBQTZCbEY7UUFDckQwRSxNQUFNSSxLQUFLO0lBQ2I7SUFFQSxTQUFTO0lBQ1QsTUFBTVcseUJBQXlCLENBQUNoRTtRQUM5QnpHLGdCQUFnQjhFLENBQUFBLE9BQVM7Z0JBQ3ZCLEdBQUdBLElBQUk7Z0JBQ1BwRSxtQkFBbUJvRSxLQUFLcEUsaUJBQWlCLENBQUN1RCxNQUFNLENBQUMsQ0FBQ3lHLEdBQUdDLElBQU1BLE1BQU1sRTtZQUNuRTtJQUNGO0lBRUEsV0FBVztJQUNYLE1BQU1tRSxzQkFBc0I7UUFDMUI1SyxnQkFBZ0I4RSxDQUFBQSxPQUFTO2dCQUN2QixHQUFHQSxJQUFJO2dCQUNQbkUsY0FBYzt1QkFBSW1FLEtBQUtuRSxZQUFZO29CQUFFO3dCQUFFVixPQUFPO3dCQUFJc0IsU0FBUztvQkFBRztpQkFBRTtZQUNsRTtJQUNGO0lBRUEsTUFBTXNKLHlCQUF5QixDQUFDcEU7UUFDOUJ6RyxnQkFBZ0I4RSxDQUFBQSxPQUFTO2dCQUN2QixHQUFHQSxJQUFJO2dCQUNQbkUsY0FBY21FLEtBQUtuRSxZQUFZLENBQUNzRCxNQUFNLENBQUMsQ0FBQ3lHLEdBQUdDLElBQU1BLE1BQU1sRTtZQUN6RDtJQUNGO0lBRUEsTUFBTXFFLDBCQUEwQixDQUFDckUsT0FBZXhHO1FBQzlDRCxnQkFBZ0I4RSxDQUFBQTtZQUNkLE1BQU1pRyxrQkFBa0I7bUJBQUlqRyxLQUFLbkUsWUFBWTthQUFDO1lBQzlDb0ssZUFBZSxDQUFDdEUsTUFBTSxHQUFHO2dCQUFFLEdBQUdzRSxlQUFlLENBQUN0RSxNQUFNO2dCQUFFeEc7WUFBTTtZQUM1RCxPQUFPO2dCQUFFLEdBQUc2RSxJQUFJO2dCQUFFbkUsY0FBY29LO1lBQWdCO1FBQ2xEO0lBQ0Y7SUFFQSxNQUFNQyw0QkFBNEIsQ0FBQ3ZFLE9BQWVsRjtRQUNoRHZCLGdCQUFnQjhFLENBQUFBO1lBQ2QsTUFBTWlHLGtCQUFrQjttQkFBSWpHLEtBQUtuRSxZQUFZO2FBQUM7WUFDOUNvSyxlQUFlLENBQUN0RSxNQUFNLEdBQUc7Z0JBQUUsR0FBR3NFLGVBQWUsQ0FBQ3RFLE1BQU07Z0JBQUVsRjtZQUFRO1lBQzlELE9BQU87Z0JBQUUsR0FBR3VELElBQUk7Z0JBQUVuRSxjQUFjb0s7WUFBZ0I7UUFDbEQ7SUFDRjtJQUVBLDZCQUE2QjtJQUM3QixNQUFNRSw0QkFBNEIsQ0FBQ3RLO1FBQ2pDLE9BQU9BLGFBQWFnRCxHQUFHLENBQUN1SCxDQUFBQSxPQUFTO2dCQUMvQmpMLE9BQU9pTCxLQUFLakwsS0FBSztnQkFDakJzQixTQUFTMkosS0FBSzNKLE9BQU8sQ0FBQzRKLEtBQUssQ0FBQyxNQUFNbEgsTUFBTSxDQUFDbUgsQ0FBQUEsT0FBUUEsS0FBS3JFLElBQUksSUFBSSxhQUFhO1lBQzdFO0lBQ0Y7SUFFQSwwQkFBMEI7SUFDMUIsTUFBTXNFLG1CQUFtQixDQUFDaEQ7WUFTcEJBLDZCQUFBQSx1QkFLQUEsZ0NBQUFBO1FBYkosTUFBTWlELFNBQVMsRUFBRTtRQUVqQixTQUFTO1FBQ1QsSUFBSWpELE9BQU9sSSxVQUFVLElBQUlrSSxPQUFPbEksVUFBVSxDQUFDb0ssVUFBVSxDQUFDLFVBQVU7WUFDOURlLE9BQU9DLElBQUksQ0FBQztRQUNkO1FBRUEsU0FBUztRQUNULElBQUlsRCxFQUFBQSx3QkFBQUEsT0FBT3pILGFBQWEsY0FBcEJ5SCw2Q0FBQUEsOEJBQUFBLHNCQUFzQnJILEtBQUssY0FBM0JxSCxrREFBQUEsNEJBQTZCcEgsR0FBRyxLQUFJb0gsT0FBT3pILGFBQWEsQ0FBQ0ksS0FBSyxDQUFDQyxHQUFHLENBQUNzSixVQUFVLENBQUMsVUFBVTtZQUMxRmUsT0FBT0MsSUFBSSxDQUFDO1FBQ2Q7UUFFQSxTQUFTO1FBQ1QsSUFBSWxELEVBQUFBLHlCQUFBQSxPQUFPekgsYUFBYSxjQUFwQnlILDhDQUFBQSxpQ0FBQUEsdUJBQXNCbEgsUUFBUSxjQUE5QmtILHFEQUFBQSwrQkFBZ0NwSCxHQUFHLEtBQUlvSCxPQUFPekgsYUFBYSxDQUFDTyxRQUFRLENBQUNGLEdBQUcsQ0FBQ3NKLFVBQVUsQ0FBQyxVQUFVO1lBQ2hHZSxPQUFPQyxJQUFJLENBQUM7UUFDZDtRQUVBLFNBQVM7UUFDVCxJQUFJbEQsT0FBT21ELG1CQUFtQixFQUFFO1lBQzlCbkQsT0FBT21ELG1CQUFtQixDQUFDakYsT0FBTyxDQUFDLENBQUNrRixVQUFVaEY7Z0JBQzVDLElBQUlnRixTQUFTeEssR0FBRyxJQUFJd0ssU0FBU3hLLEdBQUcsQ0FBQ3NKLFVBQVUsQ0FBQyxVQUFVO29CQUNwRGUsT0FBT0MsSUFBSSxDQUFDLE9BQWlCLE9BQVY5RSxRQUFRO2dCQUM3QjtZQUNGO1FBQ0Y7UUFFQSxPQUFPNkU7SUFDVDtJQUVBLFlBQVk7SUFDWixNQUFNSSxhQUFhLE9BQU9yRDtRQUN4QixJQUFJO2dCQWtCVUEsNkJBQUFBLHVCQUNHQSxnQ0FBQUE7WUFsQmYsVUFBVTtZQUNWLElBQUksQ0FBQ0EsT0FBT3BJLEtBQUssSUFBSW9JLE9BQU9wSSxLQUFLLENBQUM4RyxJQUFJLE9BQU8sSUFBSTtnQkFDL0MsTUFBTSxJQUFJNEUsTUFBTTtZQUNsQjtZQUVBLGdCQUFnQjtZQUNoQixNQUFNQyxhQUFhUCxpQkFBaUJoRDtZQUNwQyxJQUFJdUQsV0FBVzdILE1BQU0sR0FBRyxHQUFHO2dCQUN6QixNQUFNLElBQUk0SCxNQUFNLDBCQUErQyxPQUFyQkMsV0FBV0MsSUFBSSxDQUFDO1lBQzVEO1lBRUEsU0FBUztZQUNULE1BQU1DLGFBQWE7Z0JBQ2pCNVEsVUFBVUE7Z0JBQ1YrRSxPQUFPb0ksT0FBT3BJLEtBQUs7Z0JBQ25CQyxhQUFhbUksT0FBT25JLFdBQVcsSUFBSTtnQkFDbkNDLFlBQVlrSSxPQUFPbEksVUFBVSxJQUFJO2dCQUNqQ1UsVUFBVXdILEVBQUFBLHdCQUFBQSxPQUFPekgsYUFBYSxjQUFwQnlILDZDQUFBQSw4QkFBQUEsc0JBQXNCckgsS0FBSyxjQUEzQnFILGtEQUFBQSw0QkFBNkJwSCxHQUFHLElBQUcsSUFBSTtnQkFDakRILGFBQWF1SCxFQUFBQSx5QkFBQUEsT0FBT3pILGFBQWEsY0FBcEJ5SCw4Q0FBQUEsaUNBQUFBLHVCQUFzQmxILFFBQVEsY0FBOUJrSCxxREFBQUEsK0JBQWdDcEgsR0FBRyxJQUFHLElBQUk7Z0JBQ3ZERixVQUFVO2dCQUNWMkcsZUFBZTtnQkFDZjlHLGVBQWV5SCxPQUFPekgsYUFBYSxJQUFJLENBQUM7Z0JBQ3hDRCxjQUFjc0ssMEJBQTBCNUMsT0FBTzFILFlBQVksSUFBSSxFQUFFO2dCQUNqRTZLLHFCQUFxQm5ELE9BQU9tRCxtQkFBbUIsSUFBSSxFQUFFO2dCQUNyRGhFLFlBQVlhLE9BQU9iLFVBQVUsSUFBSTtZQUNuQztZQUVBck0sUUFBUUMsR0FBRyxDQUFDLDBCQUFnQjBRO1lBRTVCLElBQUk5UDtZQUVKLGVBQWU7WUFDZixJQUFJcU0sT0FBT3pHLEVBQUUsSUFBSXlHLE9BQU96RyxFQUFFLEdBQUcsU0FBUztnQkFDcEMsY0FBYztnQkFDZHpHLFFBQVFDLEdBQUcsQ0FBQztnQkFDWixNQUFNLEVBQUVHLE1BQU1GLFFBQVEsRUFBRSxHQUFHLE1BQU1WLHNEQUFTQSxDQUFDb1IsWUFBWSxDQUFDRDtnQkFDeEQ5UCxTQUFTWDtnQkFDVEYsUUFBUUMsR0FBRyxDQUFDO1lBQ2QsT0FBTztnQkFDTCxlQUFlO2dCQUNmRCxRQUFRQyxHQUFHLENBQUMsd0JBQWNpTixPQUFPekcsRUFBRTtnQkFDbkMsTUFBTSxFQUFFckcsTUFBTUYsUUFBUSxFQUFFLEdBQUcsTUFBTVYsc0RBQVNBLENBQUNxUixZQUFZLENBQUMzRCxPQUFPekcsRUFBRSxFQUFFa0s7Z0JBQ25FOVAsU0FBU1g7Z0JBQ1RGLFFBQVFDLEdBQUcsQ0FBQztZQUNkO1lBRUEsT0FBT1k7UUFDVCxFQUFFLE9BQU9HLE9BQU87WUFDZGhCLFFBQVFnQixLQUFLLENBQUMsYUFBYUE7WUFDM0IsTUFBTUE7UUFDUjtJQUNGO0lBRUEsVUFBVTtJQUNWLE1BQU04UCxzQkFBc0I7UUFDMUIsSUFBSSxDQUFDOU8sa0JBQWtCO1lBQ3JCa00sTUFBTTtZQUNOO1FBQ0Y7UUFFQSxNQUFNNkMsaUJBQWlCdFAsV0FBV3VQLElBQUksQ0FBQzlELENBQUFBLFNBQVVBLE9BQU96RyxFQUFFLEtBQUt6RTtRQUMvRCxJQUFJLENBQUMrTyxnQkFBZ0I7WUFDbkI3QyxNQUFNO1lBQ047UUFDRjtRQUVBLGFBQWE7UUFDYixJQUFJNkMsZUFBZWhRLE1BQU0sS0FBSyxHQUFHO1lBQy9CbU4sTUFBTTtZQUNOO1FBQ0Y7UUFFQSxJQUFJO1lBQ0Y1TCxnQkFBZ0I7WUFDaEJ0QyxRQUFRQyxHQUFHLENBQUM7WUFDWkQsUUFBUUMsR0FBRyxDQUFDLGtDQUF3QitCLGtCQUFrQixPQUFPLE9BQU9BO1lBQ3BFaEMsUUFBUUMsR0FBRyxDQUFDLHNCQUFZOFE7WUFDeEIvUSxRQUFRQyxHQUFHLENBQUMsd0JBQWM4USxlQUFldEssRUFBRSxFQUFFLE9BQU8sT0FBT3NLLGVBQWV0SyxFQUFFO1lBQzVFekcsUUFBUUMsR0FBRyxDQUFDLDRCQUFrQndCLFdBQVcrRyxHQUFHLENBQUNDLENBQUFBLElBQU07b0JBQUVoQyxJQUFJZ0MsRUFBRWhDLEVBQUU7b0JBQUVpQyxNQUFNLE9BQU9ELEVBQUVoQyxFQUFFO29CQUFFM0IsT0FBTzJELEVBQUUzRCxLQUFLO2dCQUFDO1lBRWpHLGNBQWM7WUFDZCxNQUFNbU0sb0JBQW9CRixlQUFldEssRUFBRTtZQUMzQ3pHLFFBQVFDLEdBQUcsQ0FBQywyQkFBaUJnUixtQkFBbUIsT0FBTyxPQUFPQTtZQUU5RCxNQUFNLEVBQUU3USxNQUFNRixRQUFRLEVBQUUsR0FBRyxNQUFNVixzREFBU0EsQ0FBQzBSLGFBQWEsQ0FBQ0Q7WUFFekQsSUFBSS9RLFNBQVNtSSxJQUFJLEtBQUssS0FBSztnQkFDekJySSxRQUFRQyxHQUFHLENBQUMsYUFBYUMsU0FBU0UsSUFBSTtnQkFDdEM4TixNQUFNO2dCQUVOLGNBQWM7Z0JBQ2QsTUFBTWpHO1lBQ1IsT0FBTztnQkFDTGpJLFFBQVFnQixLQUFLLENBQUMsYUFBYWQsU0FBUzhJLE9BQU87Z0JBQzNDa0YsTUFBTWhPLFNBQVM4SSxPQUFPLElBQUk7WUFDNUI7UUFDRixFQUFFLE9BQU9oSSxPQUFZO2dCQUVNQSxpQkFHckJBLHNCQUFBQTtZQUpKaEIsUUFBUWdCLEtBQUssQ0FBQyxhQUFhQTtZQUMzQmhCLFFBQVFnQixLQUFLLENBQUMsWUFBV0Esa0JBQUFBLE1BQU1kLFFBQVEsY0FBZGMsc0NBQUFBLGdCQUFnQlosSUFBSTtZQUU3QyxZQUFZO1lBQ1osS0FBSVksbUJBQUFBLE1BQU1kLFFBQVEsY0FBZGMsd0NBQUFBLHVCQUFBQSxpQkFBZ0JaLElBQUksY0FBcEJZLDJDQUFBQSxxQkFBc0JnSSxPQUFPLEVBQUU7Z0JBQ2pDa0YsTUFBTWxOLE1BQU1kLFFBQVEsQ0FBQ0UsSUFBSSxDQUFDNEksT0FBTztZQUNuQyxPQUFPLElBQUloSSxNQUFNZ0ksT0FBTyxFQUFFO2dCQUN4QmtGLE1BQU1sTixNQUFNZ0ksT0FBTztZQUNyQixPQUFPO2dCQUNMa0YsTUFBTTtZQUNSO1FBQ0YsU0FBVTtZQUNSNUwsZ0JBQWdCO1FBQ2xCO0lBQ0Y7SUFFQSxTQUFTO0lBQ1QsTUFBTTZPLGFBQWE7UUFDakIsSUFBSTtZQUNGM08sY0FBYztZQUNkLGNBQWM7WUFDZCxJQUFJTixlQUFleUwsSUFBSSxHQUFHLEdBQUc7Z0JBQzNCTyxNQUFNO2dCQUNOMUwsY0FBYztnQkFDZDtZQUNGO1lBRUF4QyxRQUFRQyxHQUFHLENBQUM7WUFFWix5QkFBeUI7WUFDekIsSUFBSStCLG9CQUFvQkYsbUJBQW1CLFVBQVU7Z0JBQ25ELE1BQU1pUCxpQkFBaUJ0UCxXQUFXdVAsSUFBSSxDQUFDdkksQ0FBQUEsSUFBS0EsRUFBRWhDLEVBQUUsS0FBS3pFO2dCQUNyRCxJQUFJK08sZ0JBQWdCO3dCQVdLdlA7b0JBVnZCLFNBQVM7b0JBQ1QsTUFBTTRQLGdCQUFnQjt3QkFDcEIsR0FBR0wsY0FBYzt3QkFDakJqTSxPQUFPdEQsYUFBYXNELEtBQUs7d0JBQ3pCQyxhQUFhdkQsYUFBYXVELFdBQVc7d0JBQ3JDQyxZQUFZeEQsYUFBYXdELFVBQVU7d0JBQ25DVSxVQUFVbEUsYUFBYTJELGNBQWMsR0FBRyxJQUFJO3dCQUM1Q1EsYUFBYW5FLGFBQWE4RCxtQkFBbUIsR0FBRyxJQUFJO3dCQUNwREcsZUFBZWpFLGFBQWFpRSxhQUFhO3dCQUN6Q0QsY0FBY2hFLGFBQWFnRSxZQUFZO3dCQUN2QzZLLHFCQUFxQjdPLEVBQUFBLGtDQUFBQSxhQUFhK0QsaUJBQWlCLGNBQTlCL0Qsc0RBQUFBLGdDQUFnQ2dILEdBQUcsQ0FBQzZJLENBQUFBLFdBQWE7Z0NBQ3BFdk0sT0FBT3VNLFNBQVN0TCxJQUFJO2dDQUNwQkQsS0FBS3VMLFNBQVN2TCxHQUFHO2dDQUNqQmYsYUFBYXNNLFNBQVN0TCxJQUFJOzRCQUM1QixRQUFPLEVBQUU7b0JBQ1g7b0JBRUEsY0FBYztvQkFDZHBFLGNBQWNnSSxDQUFBQSxPQUFRQSxLQUFLbkIsR0FBRyxDQUFDMEUsQ0FBQUEsU0FDN0JBLE9BQU96RyxFQUFFLEtBQUt6RSxtQkFBbUJvUCxnQkFBZ0JsRTtvQkFHbkQsVUFBVTtvQkFDVixNQUFNb0Usb0JBQW9CLE1BQU1mLFdBQVdhO29CQUUzQyxxQkFBcUI7b0JBQ3JCLElBQUk1UCxhQUFhK0YsVUFBVSxJQUFLL0YsQ0FBQUEsYUFBYWtGLG1CQUFtQixJQUFJbEYsYUFBYTZFLFlBQVksR0FBRzt3QkFDOUYsSUFBSTtnQ0FLaUNpTDs0QkFKbkMsWUFBWTs0QkFDWixJQUFJQyxlQUFldlA7NEJBRW5CLHNCQUFzQjs0QkFDdEIsSUFBSStPLGVBQWV0SyxFQUFFLEdBQUcsWUFBVzZLLDhCQUFBQSx5Q0FBQUEsMEJBQUFBLGtCQUFtQmxSLElBQUksY0FBdkJrUiw4Q0FBQUEsd0JBQXlCN0ssRUFBRSxHQUFFO2dDQUM5RDhLLGVBQWVELGtCQUFrQmxSLElBQUksQ0FBQ3FHLEVBQUU7Z0NBQ3hDekcsUUFBUUMsR0FBRyxDQUFDLDhCQUFvQnNSOzRCQUNsQzs0QkFFQSxzQkFBc0I7NEJBQ3RCLElBQUkvUCxhQUFhK0YsVUFBVSxDQUFDQyxRQUFRLElBQUloRyxhQUFhK0YsVUFBVSxDQUFDRyxlQUFlLEVBQUU7Z0NBQy9FMUgsUUFBUUMsR0FBRyxDQUFDO2dDQUVaLFdBQVc7Z0NBQ1gsTUFBTXVSLG1CQUFtQjtvQ0FDdkJoSyxVQUFVaEcsYUFBYStGLFVBQVUsQ0FBQ0MsUUFBUSxJQUFJO29DQUM5Q0UsaUJBQWlCbEcsYUFBYStGLFVBQVUsQ0FBQ0csZUFBZSxJQUFJO29DQUM1RCtKLGNBQWNqUSxhQUFhK0YsVUFBVSxDQUFDRSxZQUFZLEdBQUdpSyxTQUFTbFEsYUFBYStGLFVBQVUsQ0FBQ0UsWUFBWSxJQUFJO29DQUN0R2tLLGFBQWFuUSxhQUFhK0YsVUFBVSxDQUFDTSxrQkFBa0IsQ0FBQ1csR0FBRyxDQUFDLENBQUM4SCxXQUFjOzRDQUN6RXhMLE9BQU93TCxTQUFTdkssSUFBSTs0Q0FDcEJELEtBQUt3SyxTQUFTeEssR0FBRyxJQUFJOzRDQUNyQjRDLE1BQU00SCxTQUFTNUgsSUFBSSxLQUFLLFNBQVMsU0FBUzt3Q0FDNUM7b0NBQ0FrSixZQUFZcFEsYUFBYStGLFVBQVUsQ0FBQ0ssY0FBYyxDQUFDWSxHQUFHLENBQUMsQ0FBQytHLEdBQUdqRSxRQUFVLEdBQWUsT0FBWixNQUFNQSxRQUFTb0YsSUFBSSxDQUFDO29DQUM1Ri9JLHFCQUFxQm5HLGFBQWErRixVQUFVLENBQUNJLG1CQUFtQixDQUM3RG1CLE1BQU0sQ0FBQ3FHLENBQUFBLE9BQVFBLEtBQUt2RCxJQUFJLE9BQU8sSUFBSSxPQUFPO3FDQUMxQ3BELEdBQUcsQ0FBQyxDQUFDcEMsU0FBU2tGLFFBQVc7NENBQ3hCbEY7NENBQ0F5TCxVQUFVdkcsUUFBUTt3Q0FDcEI7b0NBQ0Z2SyxRQUFRO2dDQUNWO2dDQUVBZixRQUFRQyxHQUFHLENBQUMsaUNBQXVCc1I7Z0NBQ25DdlIsUUFBUUMsR0FBRyxDQUFDLHdCQUFjdVI7Z0NBRTFCLE1BQU1oUyxzREFBU0EsQ0FBQ3NTLHdCQUF3QixDQUFDUCxjQUFjQztnQ0FDdkR4UixRQUFRQyxHQUFHLENBQUM7NEJBQ2Q7NEJBRUEsZ0JBQWdCOzRCQUNoQixNQUFNOFIsZUFBZTtnQ0FDbkJDLFlBQVl4USxhQUFhZ0YsZ0JBQWdCLENBQUNDLEVBQUUsSUFBSTtnQ0FDaER3TCxnQkFBZ0J6USxhQUFhbUYsY0FBYyxHQUFHK0ssU0FBU2xRLGFBQWFtRixjQUFjLElBQUk7Z0NBQ3RGdUwsaUJBQWlCMVEsYUFBYTZFLFlBQVksR0FBRyxJQUFJOzRCQUNuRDs0QkFFQXJHLFFBQVFDLEdBQUcsQ0FBQywrQkFBcUJzUjs0QkFDakN2UixRQUFRQyxHQUFHLENBQUMsc0JBQVk4Ujs0QkFDeEIsTUFBTXZTLHNEQUFTQSxDQUFDMlMsaUJBQWlCLENBQUNaLGNBQWNROzRCQUNoRC9SLFFBQVFDLEdBQUcsQ0FBQzt3QkFDZCxFQUFFLE9BQU9tUyxlQUFvQjs0QkFDM0JwUyxRQUFRZ0IsS0FBSyxDQUFDLG9CQUFvQm9SO3dCQUNsQyxlQUFlO3dCQUNqQjtvQkFDRjtnQkFDRjtZQUNGO1lBRUEsV0FBVztZQUNYLE1BQU1oUyxPQUFPO2dCQUNYMEUsT0FBT3pCO2dCQUNQRTtnQkFDQUU7Z0JBQ0FoQztZQUNGO1lBRUFMLE9BQU9oQjtZQUNQZTtZQUVBbkIsUUFBUUMsR0FBRyxDQUFDO1FBQ2QsRUFBRSxPQUFPZSxPQUFZO1lBQ25CaEIsUUFBUWdCLEtBQUssQ0FBQyxXQUFXQTtZQUN6QmtOLE1BQU0sU0FBZ0MsT0FBdkJsTixNQUFNZ0ksT0FBTyxJQUFJO1FBQ2xDLFNBQVU7WUFDUnhHLGNBQWM7UUFDaEI7SUFDRjtJQUVBLFNBQVM7SUFDVCxNQUFNNlAsZ0JBQWdCO1FBQ3BCLGtCQUFrQjtRQUNsQixJQUFJMVAsaUJBQWlCLEdBQUc7WUFDdEI7UUFDRjtRQUVBLElBQUk7WUFDRkQsc0JBQXNCO1lBRXRCLFVBQVU7WUFDVixJQUFJakIsV0FBV21ILE1BQU0sS0FBSyxHQUFHO2dCQUMzQnNGLE1BQU07Z0JBQ047WUFDRjtZQUVBbE8sUUFBUUMsR0FBRyxDQUFDLCtCQUFxQkY7WUFFakMsTUFBTSxFQUFFSyxNQUFNRixRQUFRLEVBQUUsR0FBRyxNQUFNVixzREFBU0EsQ0FBQzhTLG1CQUFtQixDQUFDdlM7WUFFL0QsSUFBSUcsU0FBU21JLElBQUksS0FBSyxLQUFLO2dCQUN6QnJJLFFBQVFDLEdBQUcsQ0FBQyxlQUFlQyxTQUFTRSxJQUFJO2dCQUV4QyxTQUFTO2dCQUNULE1BQU1tUyxjQUFjclMsU0FBU0UsSUFBSTtnQkFDakMsSUFBSW9TLGlCQUFpQixRQUEwQixPQUFsQkQsWUFBWXpOLEtBQUssRUFBQztnQkFFL0MsbUJBQW1CO2dCQUNuQixJQUFJeU4sWUFBWUUsWUFBWSxFQUFFO29CQUM1QixNQUFNQyxRQUFRSCxZQUFZRSxZQUFZO29CQUN0Q0Qsa0JBQWtCLHFCQUEwREQsT0FBckNBLFlBQVlJLFlBQVksRUFBQyxjQUFvREQsT0FBeENILFlBQVlLLGdCQUFnQixFQUFDLGFBQThDRixPQUFuQ0EsTUFBTUcsZ0JBQWdCLEVBQUMsY0FBa0RILE9BQXRDQSxNQUFNSSxtQkFBbUIsRUFBQyxjQUFzQyxPQUExQkosTUFBTUssbUJBQW1CLEVBQUM7b0JBRXZOLElBQUlMLE1BQU1NLGtCQUFrQixHQUFHLEdBQUc7d0JBQ2hDLE1BQU1DLGtCQUFrQkMsS0FBS0MsS0FBSyxDQUFDVCxNQUFNTSxrQkFBa0IsR0FBRzt3QkFDOURSLGtCQUFrQixhQUE2QixPQUFoQlMsaUJBQWdCO29CQUNqRDtnQkFDRjtnQkFFQS9FLE1BQU1zRTtnQkFFTixhQUFhO2dCQUNiNVAsZ0JBQWdCO2dCQUVoQixjQUFjO2dCQUNkLE1BQU1xRjtnQkFFTixZQUFZO2dCQUNaN0csT0FBTztvQkFDTHNILE1BQU07b0JBQ04zSSxVQUFVQTtvQkFDVmlKLFNBQVM7Z0JBQ1g7WUFDRixPQUFPO2dCQUNMaEosUUFBUWdCLEtBQUssQ0FBQyxlQUFlZCxTQUFTOEksT0FBTztnQkFDN0NrRixNQUFNaE8sU0FBUzhJLE9BQU8sSUFBSTtZQUM1QjtRQUNGLEVBQUUsT0FBT2hJLE9BQVk7Z0JBS2ZBLHNCQUFBQTtZQUpKaEIsUUFBUWdCLEtBQUssQ0FBQyxlQUFlQTtZQUU3QixZQUFZO1lBQ1osSUFBSW9TLGVBQWU7WUFDbkIsS0FBSXBTLGtCQUFBQSxNQUFNZCxRQUFRLGNBQWRjLHVDQUFBQSx1QkFBQUEsZ0JBQWdCWixJQUFJLGNBQXBCWSwyQ0FBQUEscUJBQXNCZ0ksT0FBTyxFQUFFO2dCQUNqQ29LLGVBQWVwUyxNQUFNZCxRQUFRLENBQUNFLElBQUksQ0FBQzRJLE9BQU87WUFDNUMsT0FBTyxJQUFJaEksTUFBTWdJLE9BQU8sRUFBRTtnQkFDeEJvSyxlQUFlcFMsTUFBTWdJLE9BQU87WUFDOUI7WUFFQWtGLE1BQU1rRjtRQUNSLFNBQVU7WUFDUjFRLHNCQUFzQjtRQUN4QjtJQUNGO0lBRUEsbUJBQW1CO0lBQ25CLE1BQU0yUSxpQkFBaUI7UUFDckJsQztJQUNGO0lBRUEsU0FBUztJQUNULE1BQU1tQyxvQkFBb0I7UUFDeEJ2UixrQkFBa0I7UUFDbEJFLG9CQUFvQjtJQUN0QjtJQUVBLFNBQVM7SUFDVCxNQUFNc1IsdUJBQXVCLENBQUNDO1FBQzVCM08sZ0JBQWdCOEUsQ0FBQUEsT0FBUztnQkFDdkIsR0FBR0EsSUFBSTtnQkFDUG5ELGtCQUFrQjtvQkFDaEJDLElBQUkrTSxTQUFTL00sRUFBRTtvQkFDZlYsTUFBTXlOLFNBQVNDLFlBQVk7Z0JBQzdCO1lBQ0Y7UUFDQXJRLHdCQUF3QjtJQUMxQjtJQUVBLFdBQVc7SUFDWCxNQUFNc1EsMkJBQTJCO1FBQy9CdFEsd0JBQXdCO0lBQzFCO0lBRUEsV0FBVztJQUNYLE1BQU0wSixrQkFBa0IsT0FBT3BNO1FBQzdCcUIsa0JBQWtCO1FBQ2xCRSxvQkFBb0J2QjtRQUVwQixtQkFBbUI7UUFDbkIsSUFBSW1ELE1BQU0rRSxNQUFNLEtBQUssS0FBSyxDQUFDN0UsY0FBYztZQUN2Q3FFO1FBQ0Y7UUFFQSwyQkFBMkI7UUFDM0IsTUFBTTJJLGlCQUFpQnRQLFdBQVd1UCxJQUFJLENBQUM5RCxDQUFBQSxTQUFVQSxPQUFPekcsRUFBRSxLQUFLL0Y7UUFDL0RWLFFBQVFDLEdBQUcsQ0FBQyx1QkFBYThRO1FBRXpCLElBQUlBLGdCQUFnQjtZQUNsQixJQUFJO2dCQUNGL1EsUUFBUUMsR0FBRyxDQUFDLGlDQUF1QkYsVUFBVSxhQUFhVztnQkFFMUQsY0FBYztnQkFDZCxNQUFNLEVBQUVOLE1BQU11VCxHQUFHLEVBQUUsR0FBRyxNQUFNbFQsa0JBQWtCVixVQUFVVztnQkFFeEQsSUFBSWlULElBQUl0TCxJQUFJLEtBQUssT0FBT3NMLElBQUl2VCxJQUFJLEVBQUU7d0JBS053VCx1Q0FPQ0E7b0JBWDNCLE1BQU1BLG1CQUFtQkQsSUFBSXZULElBQUk7b0JBQ2pDSixRQUFRQyxHQUFHLENBQUMsY0FBYzJUO29CQUUxQixpREFBaUQ7b0JBQ2pELE1BQU1yTyxvQkFBb0JxTyxFQUFBQSx3Q0FBQUEsaUJBQWlCdkQsbUJBQW1CLGNBQXBDdUQsNERBQUFBLHNDQUFzQ3BMLEdBQUcsQ0FBQyxDQUFDOEgsV0FBbUI7NEJBQ3RGNUgsTUFBTTs0QkFDTjNDLE1BQU11SyxTQUFTeEwsS0FBSyxJQUFJd0wsU0FBU3ZLLElBQUksSUFBSTs0QkFDekNELEtBQUt3SyxTQUFTeEssR0FBRzt3QkFDbkIsUUFBTyxFQUFFO29CQUVULGlDQUFpQztvQkFDakMsTUFBTStOLHFCQUFxQkQsRUFBQUEsaUNBQUFBLGlCQUFpQnBPLFlBQVksY0FBN0JvTyxxREFBQUEsK0JBQStCcEwsR0FBRyxDQUFDLENBQUN1SCxPQUFlOzRCQUM1RWpMLE9BQU9pTCxLQUFLakwsS0FBSyxJQUFJOzRCQUNyQnNCLFNBQVNrRCxNQUFNQyxPQUFPLENBQUN3RyxLQUFLM0osT0FBTyxJQUFJMkosS0FBSzNKLE9BQU8sQ0FBQ3NLLElBQUksQ0FBQyxRQUFTWCxLQUFLM0osT0FBTyxJQUFJO3dCQUNwRixRQUFPLEVBQUU7b0JBRVRwRyxRQUFRQyxHQUFHLENBQUMseUJBQWVzRjtvQkFDM0J2RixRQUFRQyxHQUFHLENBQUMseUJBQWU0VDtvQkFFM0JoUCxnQkFBZ0I4RSxDQUFBQTs0QkFVSmlLLHVDQUFBQSxpQ0FDQ0Esd0NBQUFBLGtDQUNJQSwwQ0FBQUEsa0NBQ0NBLDJDQUFBQTsrQkFiTzs0QkFDdkIsR0FBR2pLLElBQUk7NEJBQ1A3RSxPQUFPOE8saUJBQWlCOU8sS0FBSzs0QkFDN0JDLGFBQWE2TyxpQkFBaUI3TyxXQUFXOzRCQUN6Q0MsWUFBWTRPLGlCQUFpQjVPLFVBQVUsSUFBSTs0QkFDM0NHLGdCQUFnQnlPLGlCQUFpQmxPLFFBQVEsS0FBSzs0QkFDOUNKLHFCQUFxQnNPLGlCQUFpQmpPLFdBQVcsS0FBSzs0QkFDdERGLGVBQWVtTyxpQkFBaUJuTyxhQUFhLElBQUksQ0FBQzs0QkFDbERELGNBQWNxTzs0QkFDZHRPLG1CQUFtQkE7NEJBQ25CTixVQUFVMk8sRUFBQUEsa0NBQUFBLGlCQUFpQm5PLGFBQWEsY0FBOUJtTyx1REFBQUEsd0NBQUFBLGdDQUFnQy9OLEtBQUssY0FBckMrTiw0REFBQUEsc0NBQXVDOU4sR0FBRyxLQUFJOzRCQUN4RFosV0FBVzBPLEVBQUFBLG1DQUFBQSxpQkFBaUJuTyxhQUFhLGNBQTlCbU8sd0RBQUFBLHlDQUFBQSxpQ0FBZ0MvTixLQUFLLGNBQXJDK04sNkRBQUFBLHVDQUF1QzdOLElBQUksS0FBSTs0QkFDMURYLGVBQWV3TyxFQUFBQSxtQ0FBQUEsaUJBQWlCbk8sYUFBYSxjQUE5Qm1PLHdEQUFBQSwyQ0FBQUEsaUNBQWdDNU4sUUFBUSxjQUF4QzROLCtEQUFBQSx5Q0FBMEM5TixHQUFHLEtBQUk7NEJBQ2hFVCxnQkFBZ0J1TyxFQUFBQSxtQ0FBQUEsaUJBQWlCbk8sYUFBYSxjQUE5Qm1PLHdEQUFBQSw0Q0FBQUEsaUNBQWdDNU4sUUFBUSxjQUF4QzROLGdFQUFBQSwwQ0FBMEM3TixJQUFJLEtBQUk7d0JBQ3BFOztnQkFDRixPQUFPO29CQUNML0YsUUFBUWdCLEtBQUssQ0FBQyxlQUFlMlMsSUFBSTNLLE9BQU87b0JBQ3hDLGFBQWE7b0JBQ2JuRSxnQkFBZ0I4RSxDQUFBQSxPQUFTOzRCQUN2QixHQUFHQSxJQUFJOzRCQUNQN0UsT0FBT2lNLGVBQWVqTSxLQUFLOzRCQUMzQkMsYUFBYWdNLGVBQWVoTSxXQUFXOzRCQUN2Q0MsWUFBWStMLGVBQWUvTCxVQUFVLElBQUk7NEJBQ3pDRyxnQkFBZ0I0TCxlQUFlckwsUUFBUSxLQUFLOzRCQUM1Q0oscUJBQXFCeUwsZUFBZXBMLFdBQVcsS0FBSzs0QkFDcERKLG1CQUFtQixFQUFFLENBQUMsU0FBUzt3QkFDakM7Z0JBQ0Y7WUFDRixFQUFFLE9BQU92RSxPQUFPO2dCQUNkaEIsUUFBUWdCLEtBQUssQ0FBQyxlQUFlQTtnQkFDN0IsYUFBYTtnQkFDYjZELGdCQUFnQjhFLENBQUFBLE9BQVM7d0JBQ3ZCLEdBQUdBLElBQUk7d0JBQ1A3RSxPQUFPaU0sZUFBZWpNLEtBQUs7d0JBQzNCQyxhQUFhZ00sZUFBZWhNLFdBQVc7d0JBQ3ZDQyxZQUFZK0wsZUFBZS9MLFVBQVUsSUFBSTt3QkFDekNHLGdCQUFnQjRMLGVBQWVyTCxRQUFRLEtBQUs7d0JBQzVDSixxQkFBcUJ5TCxlQUFlcEwsV0FBVyxLQUFLO3dCQUNwREosbUJBQW1CLEVBQUUsQ0FBQyxTQUFTO29CQUNqQztZQUNGO1FBQ0Y7SUFDRjtJQUVBLFVBQVU7SUFDVixNQUFNaEUsb0JBQW9CO1FBQ3hCLE9BQU9FLFdBQVd1UCxJQUFJLENBQUM5RCxDQUFBQSxTQUFVQSxPQUFPekcsRUFBRSxLQUFLekU7SUFDakQ7SUFFQSxJQUFJLENBQUNkLFdBQVcsT0FBTztJQUV2QixxQkFDRSw4REFBQzRTO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDRDtnQkFBSUMsV0FBVTs7a0NBRWIsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDQzt3Q0FBR0QsV0FBVTtrREFBb0I7Ozs7OztrREFDbEMsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0U7Z0RBQ0NDLFNBQVNaO2dEQUNUUyxXQUFXLDRCQUEwRSxPQUE5Q2pTLG1CQUFtQixhQUFhLFdBQVc7MERBRWxGLDRFQUFDM0MsOEdBQVFBO29EQUFDNFUsV0FBVTs7Ozs7Ozs7Ozs7MERBRXRCLDhEQUFDRTtnREFBT0MsU0FBU2pJO2dEQUFjOEgsV0FBVTswREFDdkMsNEVBQUMzVSw4R0FBSUE7b0RBQUMyVSxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FJdEIsOERBQUNFO2dDQUFPQyxTQUFTL1M7Z0NBQVM0UyxXQUFVOzBDQUNsQyw0RUFBQzdVLDhHQUFDQTtvQ0FBQzZVLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQUtqQiw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUViLDhEQUFDRDtnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQ0Q7b0NBQUlDLFdBQVU7OENBQ1puUyx3QkFDQyw4REFBQ2tTO3dDQUFJQyxXQUFVO2tEQUNiLDRFQUFDSTtzREFBRTs7Ozs7Ozs7OztvREFFSDFTLFdBQVdtSCxNQUFNLEtBQUssa0JBQ3hCLDhEQUFDa0w7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDRDtnREFBSUMsV0FBVTswREFDYiw0RUFBQzFVLDhHQUFRQTtvREFBQzBVLFdBQVU7Ozs7Ozs7Ozs7OzBEQUV0Qiw4REFBQ0s7Z0RBQUdMLFdBQVU7MERBQTBCOzs7Ozs7MERBQ3hDLDhEQUFDSTtnREFBRUosV0FBVTswREFBZ0M7Ozs7OzswREFHN0MsOERBQUNFO2dEQUNDQyxTQUFTakk7Z0RBQ1Q4SCxXQUFVOztrRUFFViw4REFBQzNVLDhHQUFJQTt3REFBQzJVLFdBQVU7Ozs7OztvREFBWTs7Ozs7Ozs7Ozs7O29EQUtoQ3RTLFdBQVcrRyxHQUFHLENBQUMsQ0FBQzBFLHVCQUNkLDhEQUFDNEc7NENBRUNDLFdBQVcsb0JBQW1FLE9BQS9DL1IscUJBQXFCa0wsT0FBT3pHLEVBQUUsR0FBRyxXQUFXOzRDQUMzRXlOLFNBQVMsSUFBTXBILGdCQUFnQkksT0FBT3pHLEVBQUU7OzhEQUV4Qyw4REFBQ3FOO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ007NERBQUtOLFdBQVU7c0VBQXlCN0csT0FBT3BJLEtBQUs7Ozs7OztzRUFDckQsOERBQUN1UDs0REFBS04sV0FBVyx1QkFBbUUsT0FBNUM3RyxPQUFPbk0sTUFBTSxLQUFLLElBQUksY0FBYztzRUFDekVtTSxPQUFPbk0sTUFBTSxLQUFLLElBQUksUUFBUTs7Ozs7Ozs7Ozs7OzhEQUduQyw4REFBQ2tUO29EQUNDQyxTQUFTLENBQUNySzt3REFDUkEsRUFBRXlLLGVBQWU7d0RBQ2pCdkgsa0JBQWtCRyxPQUFPekcsRUFBRTtvREFDN0I7b0RBQ0FzTixXQUFVOzhEQUVWLDRFQUFDN1UsOEdBQUNBO3dEQUFDNlUsV0FBVTs7Ozs7Ozs7Ozs7OzJDQWpCVjdHLE9BQU96RyxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7MENBMEJ4Qiw4REFBQ3FOO2dDQUFJQyxXQUFVOztvQ0FDWmpTLG1CQUFtQix3QkFDbEIsOERBQUNnUzt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEO2dEQUFJQyxXQUFVOzBEQUNiLDRFQUFDelUsOEdBQVFBO29EQUFDeVUsV0FBVTs7Ozs7Ozs7Ozs7MERBRXRCLDhEQUFDSztnREFBR0wsV0FBVTswREFBMEI7Ozs7OzswREFDeEMsOERBQUNJO2dEQUFFSixXQUFVOzBEQUFnQzs7Ozs7Ozs7Ozs7O29DQU1oRGpTLG1CQUFtQiw0QkFDbEI7OzBEQUVFLDhEQUFDZ1M7Z0RBQUlDLFdBQVU7MERBQ1p6UyxpQ0FDQyw4REFBQ2lUO29EQUNDQyxLQUFLbFQ7b0RBQ0xtVCxLQUFJO29EQUNKVixXQUFVOzs7Ozs4RUFHWiw4REFBQ0Q7b0RBQUlDLFdBQVU7OERBQ2IsNEVBQUNNO2tFQUFLOzs7Ozs7Ozs7Ozs7Ozs7OzBEQU1aLDhEQUFDUDtnREFBSUMsV0FBVTs7a0VBRWIsOERBQUNEO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ25KO2dFQUFNbUosV0FBVTswRUFBb0I7Ozs7OzswRUFDckMsOERBQUN4RjtnRUFDQzdGLE1BQUs7Z0VBQ0w0QixPQUFPakg7Z0VBQ1BxUixVQUFVLENBQUM3SyxJQUFNdkcsZ0JBQWdCdUcsRUFBRWtCLE1BQU0sQ0FBQ1QsS0FBSztnRUFDL0N5SixXQUFVO2dFQUNWWSxhQUFZOzs7Ozs7Ozs7Ozs7a0VBS2hCLDhEQUFDYjt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUNuSjtnRUFBTW1KLFdBQVU7MEVBQW9COzs7Ozs7MEVBQ3JDLDhEQUFDcFUsMkVBQU1BO2dFQUNMaVYsTUFBSztnRUFDTEMsT0FBTztvRUFBRUMsT0FBTztnRUFBTztnRUFDdkJILGFBQVk7Z0VBQ1pySyxPQUFPN0Y7Z0VBQ1BpUSxVQUFVaFE7Z0VBQ1Y5QyxTQUFTK0M7Z0VBQ1RvUSxTQUFTeFEsV0FBV2lFLEdBQUcsQ0FBQzZDLENBQUFBO29FQUN0QnJMLFFBQVFDLEdBQUcsQ0FBQyx5QkFBZW9MO29FQUMzQixPQUFPO3dFQUNMVCxxQkFDRSw4REFBQ3lKOzRFQUFLUSxPQUFPO2dGQUFFRyxPQUFPM0osSUFBSTJKLEtBQUs7NEVBQUM7c0ZBQzdCM0osSUFBSXRGLElBQUk7Ozs7Ozt3RUFHYnVFLE9BQU9lLElBQUk1RSxFQUFFO29FQUNmO2dFQUNGOzs7Ozs7MEVBR0YsOERBQUNxTjtnRUFBSWUsT0FBTztvRUFBRUksVUFBVTtvRUFBUUQsT0FBTztvRUFBUUUsV0FBVztnRUFBTTs7b0VBQUc7b0VBQ3JEM1EsV0FBV3FFLE1BQU07b0VBQUM7b0VBQVNqRSxjQUFjLE1BQU07Ozs7Ozs7Ozs7Ozs7a0VBSy9ELDhEQUFDbVA7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDbko7Z0VBQU1tSixXQUFVOzBFQUFvQjs7Ozs7OzBFQUNyQyw4REFBQ3hGO2dFQUNDN0YsTUFBSztnRUFDTDRCLE9BQU8zRztnRUFDUCtRLFVBQVUsQ0FBQzdLLElBQU1qRyxrQkFBa0JpRyxFQUFFa0IsTUFBTSxDQUFDVCxLQUFLO2dFQUNqRHlKLFdBQVU7Z0VBQ1ZZLGFBQVk7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O29DQU9yQjdTLG1CQUFtQixZQUFZUCxxQ0FDOUI7a0RBRUUsNEVBQUN1Uzs0Q0FBSUMsV0FBVTs7OERBRWIsOERBQUNEO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ0Q7NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDRDtvRUFDQ0MsV0FBVTtvRUFDVkcsU0FBUzs0RUFBTWxPO2dGQUFBQSwyQkFBQUEsU0FBU21QLGNBQWMsQ0FBQyxtQ0FBeEJuUCwrQ0FBQUEseUJBQStDMkksS0FBSzs7OEVBRWxFbk4sYUFBYXdELFVBQVUsTUFBSXpELHFCQUFBQSxpQ0FBQUEseUNBQUFBLG1CQUFxQnlELFVBQVUsa0JBQ3pELDhEQUFDdVA7d0VBQ0NDLEtBQUtoVCxhQUFhd0QsVUFBVSxNQUFJekQsc0JBQUFBLGlDQUFBQSwwQ0FBQUEsb0JBQXFCeUQsVUFBVTt3RUFDL0R5UCxLQUFJO3dFQUNKVixXQUFVOzs7OztrR0FHWiw4REFBQ0Q7d0VBQUlDLFdBQVU7a0ZBQ2IsNEVBQUNNO3NGQUFLOzs7Ozs7Ozs7Ozs7Ozs7OzhFQUlaLDhEQUFDOUY7b0VBQ0M5SCxJQUFHO29FQUNIaUMsTUFBSztvRUFDTCtGLFFBQU87b0VBQ1BpRyxVQUFVbkg7b0VBQ1ZzSCxPQUFPO3dFQUFFTyxTQUFTO29FQUFPOzs7Ozs7OEVBRzNCLDhEQUFDbkI7b0VBQ0NGLFdBQVU7b0VBQ1ZHLFNBQVM7d0VBQ1AsaUJBQWlCO3dFQUNqQmxVLFFBQVFDLEdBQUcsQ0FBQzt3RUFDWnlCLGFBQWFxTyxJQUFJLENBQUM7b0VBQ3BCOzhFQUNEOzs7Ozs7Ozs7Ozs7c0VBSUgsOERBQUMrRDs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUNEO29FQUFJQyxXQUFVOztzRkFDYiw4REFBQ25KO3NGQUFNOzs7Ozs7c0ZBQ1AsOERBQUMyRDs0RUFDQzdGLE1BQUs7NEVBQ0w0QixPQUFPOUksYUFBYXNELEtBQUssTUFBSXZELHNCQUFBQSxpQ0FBQUEsMENBQUFBLG9CQUFxQnVELEtBQUssS0FBSTs0RUFDM0Q0UCxVQUFVLENBQUM3SztnRkFDVGhGLGdCQUFnQjhFLENBQUFBLE9BQVM7d0ZBQUUsR0FBR0EsSUFBSTt3RkFBRTdFLE9BQU8rRSxFQUFFa0IsTUFBTSxDQUFDVCxLQUFLO29GQUFDO2dGQUMxRCtDLGtCQUFrQnJMLGtCQUFtQjZILEVBQUVrQixNQUFNLENBQUNULEtBQUs7NEVBQ3JEOzRFQUNBcUssYUFBWTs7Ozs7Ozs7Ozs7OzhFQUdoQiw4REFBQ2I7b0VBQUlDLFdBQVU7O3NGQUNiLDhEQUFDbko7c0ZBQU07Ozs7OztzRkFDUCw4REFBQ3lLOzRFQUNDL0ssT0FBTzlJLGFBQWF1RCxXQUFXLE1BQUl4RCxzQkFBQUEsaUNBQUFBLDBDQUFBQSxvQkFBcUJ3RCxXQUFXLEtBQUk7NEVBQ3ZFMlAsVUFBVSxDQUFDN0ssSUFBTWhGLGdCQUFnQjhFLENBQUFBLE9BQVM7d0ZBQUUsR0FBR0EsSUFBSTt3RkFBRTVFLGFBQWE4RSxFQUFFa0IsTUFBTSxDQUFDVCxLQUFLO29GQUFDOzRFQUNqRnFLLGFBQVk7NEVBQ1pXLE1BQU07Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4REFPZCw4REFBQ3hCO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ0s7c0VBQUc7Ozs7OztzRUFHSiw4REFBQ047NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDRDtvRUFBSUMsV0FBVTs7c0ZBQ2IsOERBQUNNO3NGQUFLOzs7Ozs7c0ZBQ04sOERBQUN6Sjs0RUFBTW1KLFdBQVU7OzhGQUNmLDhEQUFDeEY7b0ZBQ0M3RixNQUFLO29GQUNMNk0sU0FBUy9ULGFBQWEyRCxjQUFjO29GQUNwQ3VQLFVBQVUsQ0FBQzdLLElBQU1oRixnQkFBZ0I4RSxDQUFBQSxPQUFTO2dHQUFFLEdBQUdBLElBQUk7Z0dBQUV4RSxnQkFBZ0IwRSxFQUFFa0IsTUFBTSxDQUFDd0ssT0FBTzs0RkFBQzs7Ozs7OzhGQUV4Riw4REFBQ2xCO29GQUFLTixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Z0VBR25CdlMsYUFBYTJELGNBQWMsa0JBQzFCLDhEQUFDMk87b0VBQUlDLFdBQVU7OEVBRVp2UyxFQUFBQSw4QkFBQUEsYUFBYWlFLGFBQWEsY0FBMUJqRSxtREFBQUEsb0NBQUFBLDRCQUE0QnFFLEtBQUssY0FBakNyRSx3REFBQUEsa0NBQW1Dc0UsR0FBRyxrQkFDckMsOERBQUNnTzt3RUFBSUMsV0FBVTs7MEZBQ2IsOERBQUNEO2dGQUFJQyxXQUFVOzBGQUNiLDRFQUFDbE87b0ZBQ0NrTyxXQUFVO29GQUNWeUIsUUFBUTtvRkFDUkMsUUFBUWpVLGFBQWF3RCxVQUFVOztzR0FFL0IsOERBQUMwUTs0RkFBT2xCLEtBQUtoVCxhQUFhaUUsYUFBYSxDQUFDSSxLQUFLLENBQUNDLEdBQUc7NEZBQUU0QyxNQUFLOzs7Ozs7d0ZBQWM7Ozs7Ozs7Ozs7OzswRkFJMUUsOERBQUNvTDtnRkFBSUMsV0FBVTswRkFBdUJ2UyxhQUFhaUUsYUFBYSxDQUFDSSxLQUFLLENBQUNFLElBQUk7Ozs7OzswRkFDM0UsOERBQUNrTztnRkFBT0YsV0FBVTtnRkFBd0JHLFNBQVM1RjswRkFDakQsNEVBQUMrRjs4RkFBSzs7Ozs7Ozs7Ozs7Ozs7OztrR0FJViw4REFBQ1A7d0VBQUlDLFdBQVU7OzBGQUNiLDhEQUFDRDtnRkFBSUMsV0FBVTswRkFDYiw0RUFBQ0Q7b0ZBQUlDLFdBQVU7OEZBQVk7Ozs7Ozs7Ozs7OzBGQUU3Qiw4REFBQ0U7Z0ZBQU9GLFdBQVU7Z0ZBQXdCRyxTQUFTNUY7MEZBQ2pELDRFQUFDK0Y7OEZBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0VBU2xCLDhEQUFDUDs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUNEO29FQUFJQyxXQUFVOztzRkFDYiw4REFBQ007c0ZBQUs7Ozs7OztzRkFDTiw4REFBQ3pKOzRFQUFNbUosV0FBVTs7OEZBQ2YsOERBQUN4RjtvRkFDQzdGLE1BQUs7b0ZBQ0w2TSxTQUFTL1QsYUFBYThELG1CQUFtQjtvRkFDekNvUCxVQUFVLENBQUM3SyxJQUFNaEYsZ0JBQWdCOEUsQ0FBQUEsT0FBUztnR0FBRSxHQUFHQSxJQUFJO2dHQUFFckUscUJBQXFCdUUsRUFBRWtCLE1BQU0sQ0FBQ3dLLE9BQU87NEZBQUM7Ozs7Ozs4RkFFN0YsOERBQUNsQjtvRkFBS04sV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7O2dFQUduQnZTLGFBQWE4RCxtQkFBbUIsa0JBQy9CLDhEQUFDd087b0VBQUlDLFdBQVU7OEVBRVp2UyxFQUFBQSwrQkFBQUEsYUFBYWlFLGFBQWEsY0FBMUJqRSxvREFBQUEsdUNBQUFBLDZCQUE0QndFLFFBQVEsY0FBcEN4RSwyREFBQUEscUNBQXNDc0UsR0FBRyxrQkFDeEMsOERBQUNnTzt3RUFBSUMsV0FBVTs7MEZBQ2IsOERBQUNEO2dGQUFJQyxXQUFVOztrR0FDYiw4REFBQ0Q7d0ZBQUlDLFdBQVU7a0dBQWdCOzs7Ozs7a0dBQy9CLDhEQUFDRDt3RkFBSUMsV0FBVTtrR0FDYiw0RUFBQ0Q7NEZBQUlDLFdBQVU7c0dBQW1CdlMsYUFBYWlFLGFBQWEsQ0FBQ08sUUFBUSxDQUFDRCxJQUFJOzs7Ozs7Ozs7Ozs7Ozs7OzswRkFHOUUsOERBQUNrTztnRkFBT0YsV0FBVTtnRkFBd0JHLFNBQVNwRjswRkFDakQsNEVBQUN1Rjs4RkFBSzs7Ozs7Ozs7Ozs7Ozs7OztrR0FJViw4REFBQ1A7d0VBQUlDLFdBQVU7a0ZBQ2IsNEVBQUNFOzRFQUFPRixXQUFVOzRFQUF3QkcsU0FBU3BGO3NGQUNqRCw0RUFBQ3VGOzBGQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0VBU2xCLDhEQUFDUDs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUNEO29FQUFJQyxXQUFVOzhFQUNiLDRFQUFDTTtrRkFBSzs7Ozs7Ozs7Ozs7OEVBRVIsOERBQUNQO29FQUFJQyxXQUFVOztzRkFDYiw4REFBQ0U7NEVBQU9GLFdBQVU7NEVBQW1CRyxTQUFTN0U7OzhGQUM1Qyw4REFBQ2dGOzhGQUFLOzs7Ozs7OEZBQ04sOERBQUNBOzhGQUFLOzs7Ozs7Ozs7Ozs7d0VBRVA3UyxhQUFhK0QsaUJBQWlCLElBQUkvRCxhQUFhK0QsaUJBQWlCLENBQUNxRCxNQUFNLEdBQUcsSUFDekVwSCxhQUFhK0QsaUJBQWlCLENBQUNpRCxHQUFHLENBQUMsQ0FBQzZJLFVBQVUvRixzQkFDNUMsOERBQUN3STtnRkFBZ0JDLFdBQVU7O2tHQUN6Qiw4REFBQ007d0ZBQ0NOLFdBQVU7d0ZBQ1ZHLFNBQVM7NEZBQ1AsSUFBSTdDLFNBQVN2TCxHQUFHLEVBQUU7Z0dBQ2hCNlAsT0FBT0MsSUFBSSxDQUFDdkUsU0FBU3ZMLEdBQUcsRUFBRTs0RkFDNUI7d0ZBQ0Y7d0ZBQ0ErTyxPQUFPOzRGQUNMZ0IsUUFBUXhFLFNBQVN2TCxHQUFHLEdBQUcsWUFBWTs0RkFDbkNrUCxPQUFPM0QsU0FBU3ZMLEdBQUcsR0FBRyxZQUFZOzRGQUNsQ2dRLGdCQUFnQnpFLFNBQVN2TCxHQUFHLEdBQUcsY0FBYzt3RkFDL0M7d0ZBQ0FoQixPQUFPdU0sU0FBU3ZMLEdBQUcsR0FBRyxXQUFXdUwsU0FBU3RMLElBQUk7OzRGQUMvQzs0RkFDS3NMLFNBQVN0TCxJQUFJOzs7Ozs7O2tHQUVuQiw4REFBQ2tPO3dGQUNDRixXQUFVO3dGQUNWRyxTQUFTLElBQU01RSx1QkFBdUJoRTt3RkFDdEN4RyxPQUFNO2tHQUNQOzs7Ozs7OytFQXJCT3dHOzs7OzJHQTJCWiw4REFBQ3dJOzRFQUFJQyxXQUFVO3NGQUNiLDRFQUFDTTtnRkFBS1EsT0FBTztvRkFBRUcsT0FBTztvRkFBUUMsVUFBVTtnRkFBTzswRkFBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OERBUTVELDhEQUFDbkI7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDRDs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUNLOzhFQUFHOzs7Ozs7OEVBQ0osOERBQUNIO29FQUNDRixXQUFVO29FQUNWRyxTQUFTekU7b0VBQ1QzSyxPQUFNOzhFQUNQOzs7Ozs7Ozs7Ozs7c0VBSUgsOERBQUNnUDs0REFBSUMsV0FBVTtzRUFDWnZTLGFBQWFnRSxZQUFZLElBQUloRSxhQUFhZ0UsWUFBWSxDQUFDb0QsTUFBTSxHQUFHLElBQy9EcEgsYUFBYWdFLFlBQVksQ0FBQ2dELEdBQUcsQ0FBQyxDQUFDdUgsTUFBTXpFLHNCQUNuQyw4REFBQ3dJO29FQUFnQkMsV0FBVTs7c0ZBQ3pCLDhEQUFDRDs0RUFBSUMsV0FBVTs7OEZBQ2IsOERBQUNNO29GQUFLTixXQUFVOzt3RkFBYTt3RkFBTXpJLFFBQVE7Ozs7Ozs7OEZBQzNDLDhEQUFDMkk7b0ZBQ0NGLFdBQVU7b0ZBQ1ZHLFNBQVMsSUFBTXhFLHVCQUF1QnBFO29GQUN0Q3hHLE9BQU07OEZBQ1A7Ozs7Ozs7Ozs7OztzRkFJSCw4REFBQ2dQOzRFQUFJQyxXQUFVOzs4RkFDYiw4REFBQ0Q7b0ZBQUlDLFdBQVU7O3NHQUNiLDhEQUFDbko7c0dBQU07Ozs7OztzR0FDUCw4REFBQzJEOzRGQUNDN0YsTUFBSzs0RkFDTDRCLE9BQU95RixLQUFLakwsS0FBSzs0RkFDakI0UCxVQUFVLENBQUM3SyxJQUFNOEYsd0JBQXdCckUsT0FBT3pCLEVBQUVrQixNQUFNLENBQUNULEtBQUs7NEZBQzlEcUssYUFBWTs0RkFDWlosV0FBVTs7Ozs7Ozs7Ozs7OzhGQUdkLDhEQUFDRDtvRkFBSUMsV0FBVTs7c0dBQ2IsOERBQUNuSjtzR0FBTTs7Ozs7O3NHQUNQLDhEQUFDeUs7NEZBQ0MvSyxPQUFPeUYsS0FBSzNKLE9BQU87NEZBQ25Cc08sVUFBVSxDQUFDN0ssSUFBTWdHLDBCQUEwQnZFLE9BQU96QixFQUFFa0IsTUFBTSxDQUFDVCxLQUFLOzRGQUNoRXFLLGFBQVk7NEZBQ1paLFdBQVU7NEZBQ1Z1QixNQUFNOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O21FQTdCSmhLOzs7OytGQW9DWiw4REFBQ3dJO2dFQUFJQyxXQUFVOzBFQUNiLDRFQUFDSTs4RUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4REFPWCw4REFBQ0w7b0RBQUlDLFdBQVU7OERBQ2IsNEVBQUNEO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ0Q7Z0VBQUlDLFdBQVU7O2tGQUNiLDhEQUFDTTtrRkFBSzs7Ozs7O2tGQUNOLDhEQUFDeko7d0VBQU1tSixXQUFVOzswRkFDZiw4REFBQ3hGO2dGQUNDN0YsTUFBSztnRkFDTDZNLFNBQVMvVCxhQUFhNkUsWUFBWTtnRkFDbENxTyxVQUFVLENBQUM3SyxJQUFNaEYsZ0JBQWdCOEUsQ0FBQUEsT0FBUzs0RkFBRSxHQUFHQSxJQUFJOzRGQUFFdEQsY0FBY3dELEVBQUVrQixNQUFNLENBQUN3SyxPQUFPO3dGQUFDOzs7Ozs7MEZBRXRGLDhEQUFDbEI7Z0ZBQUtOLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs0REFJbkJ2UyxhQUFhNkUsWUFBWSxrQkFDeEI7O2tGQUNFLDhEQUFDeU47d0VBQUlDLFdBQVU7OzBGQUNiLDhEQUFDTTswRkFBSzs7Ozs7OzBGQUNOLDhEQUFDeko7Z0ZBQU1tSixXQUFVOztrR0FDZiw4REFBQ3hGO3dGQUNDN0YsTUFBSzt3RkFDTDZNLFNBQVMvVCxhQUFhOEUscUJBQXFCO3dGQUMzQ29PLFVBQVUsQ0FBQzdLLElBQU1oRixnQkFBZ0I4RSxDQUFBQSxPQUFTO29HQUFFLEdBQUdBLElBQUk7b0dBQUVyRCx1QkFBdUJ1RCxFQUFFa0IsTUFBTSxDQUFDd0ssT0FBTztnR0FBQzs7Ozs7O2tHQUUvRiw4REFBQ2xCO3dGQUFLTixXQUFVOzs7Ozs7Ozs7Ozs7NEVBRWpCdlMsYUFBYThFLHFCQUFxQixrQkFDakMsOERBQUN3TjtnRkFBSUMsV0FBVTs7a0dBQ2IsOERBQUNFO3dGQUNDRixXQUFVO3dGQUNWRyxTQUFTUjtrR0FDVjs7Ozs7O2tHQUdELDhEQUFDSTt3RkFBSUMsV0FBVTtrR0FDYiw0RUFBQ007c0dBQU03UyxhQUFhZ0YsZ0JBQWdCLENBQUNULElBQUksSUFBSTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0ZBTXJELDhEQUFDK047d0VBQUlDLFdBQVU7OzBGQUNiLDhEQUFDTTswRkFBSzs7Ozs7OzBGQUNOLDhEQUFDeko7Z0ZBQU1tSixXQUFVOztrR0FDZiw4REFBQ3hGO3dGQUNDN0YsTUFBSzt3RkFDTDZNLFNBQVMvVCxhQUFha0YsbUJBQW1CO3dGQUN6Q2dPLFVBQVUsQ0FBQzdLLElBQU1oRixnQkFBZ0I4RSxDQUFBQSxPQUFTO29HQUFFLEdBQUdBLElBQUk7b0dBQUVqRCxxQkFBcUJtRCxFQUFFa0IsTUFBTSxDQUFDd0ssT0FBTztnR0FBQzs7Ozs7O2tHQUU3Riw4REFBQ2xCO3dGQUFLTixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7b0VBSW5CdlMsYUFBYWtGLG1CQUFtQixrQkFDL0IsOERBQUNvTjt3RUFBSUMsV0FBVTs7MEZBQ2IsOERBQUNEOztrR0FDQyw4REFBQ087a0dBQUs7Ozs7OztrR0FDTiw4REFBQzlGO3dGQUNDN0YsTUFBSzt3RkFDTDRCLE9BQU85SSxhQUFhbUYsY0FBYyxJQUFJO3dGQUN0QytOLFVBQVUsQ0FBQzdLLElBQU1RLG1CQUFtQlIsRUFBRWtCLE1BQU0sQ0FBQ1QsS0FBSzt3RkFDbERxSyxhQUFZO3dGQUNaWixXQUFXLGdCQUEyQyxPQUEzQjVQLGNBQWMsVUFBVTs7Ozs7Ozs7Ozs7OzRFQUd0REEsNkJBQ0MsOERBQUMyUDtnRkFBSUMsV0FBVTswRkFDWjVQOzs7Ozs7Ozs7Ozs7a0ZBTVQsOERBQUMyUDt3RUFBSUMsV0FBVTs7MEZBQ2IsOERBQUNNOzBGQUFLOzs7Ozs7MEZBQ04sOERBQUN6SjtnRkFBTW1KLFdBQVU7O2tHQUNmLDhEQUFDeEY7d0ZBQ0M3RixNQUFLO3dGQUNMNk0sU0FBUy9ULGFBQWFxRixtQkFBbUI7d0ZBQ3pDNk4sVUFBVSxDQUFDN0ssSUFBTWhGLGdCQUFnQjhFLENBQUFBLE9BQVM7b0dBQUUsR0FBR0EsSUFBSTtvR0FBRTlDLHFCQUFxQmdELEVBQUVrQixNQUFNLENBQUN3SyxPQUFPO2dHQUFDOzs7Ozs7a0dBRTdGLDhEQUFDbEI7d0ZBQUtOLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7OztvRUFLbkJ2UyxhQUFhcUYsbUJBQW1CLGtCQUMvQiw4REFBQ2lOO3dFQUFJQyxXQUFVOzswRkFFYiw4REFBQ0Q7Z0ZBQUlDLFdBQVU7O2tHQUNiLDhEQUFDRDt3RkFBSUMsV0FBVTs7MEdBQ2IsOERBQUNuSjswR0FBTTs7Ozs7OzBHQUNQLDhEQUFDMkQ7Z0dBQ0M3RixNQUFLO2dHQUNMNEIsT0FBTzlJLGFBQWErRixVQUFVLENBQUNDLFFBQVE7Z0dBQ3ZDa04sVUFBVSxDQUFDN0ssSUFBTWhGLGdCQUFnQjhFLENBQUFBLE9BQVM7NEdBQ3hDLEdBQUdBLElBQUk7NEdBQ1BwQyxZQUFZO2dIQUFFLEdBQUdvQyxLQUFLcEMsVUFBVTtnSEFBRUMsVUFBVXFDLEVBQUVrQixNQUFNLENBQUNULEtBQUs7NEdBQUM7d0dBQzdEO2dHQUNBcUssYUFBWTs7Ozs7Ozs7Ozs7O2tHQUdoQiw4REFBQ2I7d0ZBQUlDLFdBQVU7OzBHQUNiLDhEQUFDbko7MEdBQU07Ozs7OzswR0FDUCw4REFBQ2tKO2dHQUFJQyxXQUFVOztrSEFDYiw4REFBQ3hGO3dHQUNDN0YsTUFBSzt3R0FDTDRCLE9BQU85SSxhQUFhK0YsVUFBVSxDQUFDRSxZQUFZO3dHQUMzQ3lNLFNBQVMsSUFBTTVQLHNCQUFzQjt3R0FDckNxUSxhQUFZO3dHQUNab0IsUUFBUTs7Ozs7O29HQUVUMVIsb0NBQ0MsOERBQUN5UDt3R0FBSUMsV0FBVTs7MEhBQ2IsOERBQUNEO2dIQUFJQyxXQUFVOztrSUFDYiw4REFBQ0U7d0hBQU9DLFNBQVMsSUFBTXhKLHFCQUFxQixHQUFHO2tJQUFROzs7Ozs7a0lBQ3ZELDhEQUFDdUo7d0hBQU9DLFNBQVMsSUFBTXhKLHFCQUFxQixHQUFHO2tJQUFROzs7Ozs7a0lBQ3ZELDhEQUFDdUo7d0hBQU9DLFNBQVMsSUFBTXhKLHFCQUFxQixJQUFJO2tJQUFTOzs7Ozs7a0lBQ3pELDhEQUFDdUo7d0hBQU9DLFNBQVMsSUFBTXhKLHFCQUFxQixJQUFJO2tJQUFPOzs7Ozs7a0lBQ3ZELDhEQUFDdUo7d0hBQU9DLFNBQVMsSUFBTXhKLHFCQUFxQixLQUFLO2tJQUFPOzs7Ozs7Ozs7Ozs7MEhBRTFELDhEQUFDdUo7Z0hBQ0NGLFdBQVU7Z0hBQ1ZHLFNBQVMsSUFBTTVQLHNCQUFzQjswSEFDdEM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswRkFVWCw4REFBQ3dQO2dGQUFJQyxXQUFVOztrR0FDYiw4REFBQ25KO2tHQUFNOzs7Ozs7a0dBQ1AsOERBQUN5Szt3RkFDQy9LLE9BQU85SSxhQUFhK0YsVUFBVSxDQUFDRyxlQUFlO3dGQUM5Q2dOLFVBQVUsQ0FBQzdLLElBQU1oRixnQkFBZ0I4RSxDQUFBQSxPQUFTO29HQUN4QyxHQUFHQSxJQUFJO29HQUNQcEMsWUFBWTt3R0FBRSxHQUFHb0MsS0FBS3BDLFVBQVU7d0dBQUVHLGlCQUFpQm1DLEVBQUVrQixNQUFNLENBQUNULEtBQUs7b0dBQUM7Z0dBQ3BFO3dGQUNBcUssYUFBWTt3RkFDWlcsTUFBTTs7Ozs7Ozs7Ozs7OzBGQUtWLDhEQUFDeEI7Z0ZBQUlDLFdBQVU7O2tHQUNiLDhEQUFDbko7OzRGQUFNOzBHQUFPLDhEQUFDeUo7Z0dBQUtOLFdBQVU7MEdBQWV2UyxhQUFhK0YsVUFBVSxDQUFDSSxtQkFBbUIsQ0FBQ2lCLE1BQU07Ozs7Ozs7Ozs7OztvRkFDOUZwSCxhQUFhK0YsVUFBVSxDQUFDSSxtQkFBbUIsQ0FBQ2EsR0FBRyxDQUFDLENBQUMyRyxNQUFNN0Qsc0JBQ3RELDhEQUFDd0k7NEZBQWdCQyxXQUFVO3NHQUN6Qiw0RUFBQ3hGO2dHQUNDN0YsTUFBSztnR0FDTDRCLE9BQU82RTtnR0FDUHVGLFVBQVUsQ0FBQzdLO29HQUNULE1BQU1tTSxXQUFXOzJHQUFJeFUsYUFBYStGLFVBQVUsQ0FBQ0ksbUJBQW1CO3FHQUFDO29HQUNqRXFPLFFBQVEsQ0FBQzFLLE1BQU0sR0FBR3pCLEVBQUVrQixNQUFNLENBQUNULEtBQUs7b0dBQ2hDekYsZ0JBQWdCOEUsQ0FBQUEsT0FBUzs0R0FDdkIsR0FBR0EsSUFBSTs0R0FDUHBDLFlBQVk7Z0hBQUUsR0FBR29DLEtBQUtwQyxVQUFVO2dIQUFFSSxxQkFBcUJxTzs0R0FBUzt3R0FDbEU7Z0dBQ0Y7Z0dBQ0FyQixhQUFZOzs7Ozs7MkZBWk5ySjs7Ozs7a0dBZ0JaLDhEQUFDMkk7d0ZBQ0N2TCxNQUFLO3dGQUNMcUwsV0FBVTt3RkFDVkcsU0FBUyxJQUFNclAsZ0JBQWdCOEUsQ0FBQUEsT0FBUztvR0FDdEMsR0FBR0EsSUFBSTtvR0FDUHBDLFlBQVk7d0dBQ1YsR0FBR29DLEtBQUtwQyxVQUFVO3dHQUNsQkkscUJBQXFCOytHQUFJZ0MsS0FBS3BDLFVBQVUsQ0FBQ0ksbUJBQW1COzRHQUFFO3lHQUFHO29HQUNuRTtnR0FDRjtrR0FDRDs7Ozs7Ozs7Ozs7OzBGQU1ILDhEQUFDbU07Z0ZBQUlDLFdBQVU7O2tHQUNiLDhEQUFDbko7a0dBQU07Ozs7OztrR0FDUCw4REFBQ2tKO3dGQUFJQyxXQUFVO2tHQUNiLDRFQUFDRDs0RkFBSUMsV0FBVTs7OEdBQ2IsOERBQUNJO29HQUFFSixXQUFVOzhHQUFZOzs7Ozs7OEdBQ3pCLDhEQUFDRDtvR0FDQ0MsV0FBVTtvR0FDVmMsT0FBTzt3R0FDTG9CLFdBQVc7d0dBQ1hKLFFBQVE7d0dBQ1JLLFlBQVk7b0dBQ2Q7OEdBRUNuUyw2QkFDQyw4REFBQytQO3dHQUFJQyxXQUFVOzswSEFDYiw4REFBQ0Q7Z0hBQUlDLFdBQVU7Ozs7OzswSEFDZiw4REFBQ007MEhBQUs7Ozs7Ozs7Ozs7O29IQUVOeFEsTUFBTStFLE1BQU0sR0FBRyxrQkFDakIsOERBQUNrTDt3R0FDQ0MsV0FBVTt3R0FDVm9DLFNBQVN2TTtrSEFFUi9GLE1BQU0yRSxHQUFHLENBQUMsQ0FBQzROLHFCQUNWLDhEQUFDdEM7Z0hBRUNDLFdBQVcsYUFBaUUsT0FBcEQ5UCxnQkFBZ0J5RixRQUFRLENBQUMwTSxLQUFLM1AsRUFBRSxJQUFJLGFBQWE7Z0hBQ3pFeU4sU0FBUyxJQUFNMUssaUJBQWlCNE0sS0FBSzNQLEVBQUU7O2tJQUV2Qyw4REFBQ3FOO3dIQUFJQyxXQUFVOzs0SEFDWnFDLEtBQUtwUixVQUFVLElBQUlvUixLQUFLQyxlQUFlLGlCQUN0Qyw4REFBQzlCO2dJQUNDQyxLQUFLNEIsS0FBS3BSLFVBQVUsSUFBSW9SLEtBQUtDLGVBQWU7Z0lBQzVDNUIsS0FBSzJCLEtBQUt0UixLQUFLO2dJQUNmd1IsU0FBUyxDQUFDek07b0lBQ1IsTUFBTWtCLFNBQVNsQixFQUFFRSxhQUFhO29JQUM5QmdCLE9BQU84SixLQUFLLENBQUNPLE9BQU8sR0FBRztvSUFDdkIsTUFBTW1CLGNBQWN4TCxPQUFPeUwsa0JBQWtCO29JQUM3QyxJQUFJRCxhQUFhO3dJQUNmQSxZQUFZMUIsS0FBSyxDQUFDTyxPQUFPLEdBQUc7b0lBQzlCO2dJQUNGOzs7Ozs0SUFFQTswSUFDSiw4REFBQ3RCO2dJQUFJQyxXQUFVO2dJQUFtQmMsT0FBTztvSUFBRU8sU0FBU2dCLEtBQUtwUixVQUFVLElBQUlvUixLQUFLQyxlQUFlLEdBQUcsU0FBUztnSUFBTzswSUFBRzs7Ozs7Ozs7Ozs7O2tJQUluSCw4REFBQ3ZDO3dIQUFJQyxXQUFVO2tJQUFjcUMsS0FBS3RSLEtBQUssSUFBSXNSLEtBQUtyUSxJQUFJLElBQUlxUSxLQUFLSyxRQUFRLElBQUk7Ozs7OztvSEFDeEV4UyxnQkFBZ0J5RixRQUFRLENBQUMwTSxLQUFLM1AsRUFBRSxtQkFDL0IsOERBQUNxTjt3SEFBSUMsV0FBVTtrSUFBcUI7Ozs7Ozs7K0dBekJqQ3FDLEtBQUszUCxFQUFFOzs7Ozs7Ozs7a0lBK0JsQiw4REFBQ3FOO3dHQUFJQyxXQUFVO2tIQUNiLDRFQUFDRDs0R0FBSUMsV0FBVTtzSEFBYTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBGQVN4Qyw4REFBQ0Q7Z0ZBQUlDLFdBQVU7O2tHQUNiLDhEQUFDbko7a0dBQU07Ozs7OztrR0FDUCw4REFBQ2tKO3dGQUFJQyxXQUFVO2tHQUNiLDRFQUFDRDs0RkFBSUMsV0FBVTs7OEdBQ2IsOERBQUNFO29HQUNDdkwsTUFBSztvR0FDTHFMLFdBQVU7b0dBQ1ZHLFNBQVM7d0dBQ1AsU0FBUzt3R0FDVCxNQUFNM0YsUUFBUXZJLFNBQVN3SSxhQUFhLENBQUM7d0dBQ3JDRCxNQUFNN0YsSUFBSSxHQUFHO3dHQUNiNkYsTUFBTUUsTUFBTSxHQUFHO3dHQUNmRixNQUFNRyxRQUFRLEdBQUcsQ0FBQzdFO2dIQUNIOzRHQUFiLE1BQU0yRCxRQUFPLG9CQUFHekMsTUFBTSxDQUFzQjBDLEtBQUssY0FBcEMscURBQXNDLENBQUMsRUFBRTs0R0FDdEQsSUFBSUQsTUFBTTtnSEFDUjNJLGdCQUFnQjhFLENBQUFBLE9BQVM7d0hBQ3ZCLEdBQUdBLElBQUk7d0hBQ1BwQyxZQUFZOzRIQUNWLEdBQUdvQyxLQUFLcEMsVUFBVTs0SEFDbEJNLG9CQUFvQjttSUFDZjhCLEtBQUtwQyxVQUFVLENBQUNNLGtCQUFrQjtnSUFDckM7b0lBQUVhLE1BQU07b0lBQVEzQyxNQUFNeUgsS0FBS3pILElBQUk7Z0lBQUM7NkhBQ2pDO3dIQUNIO29IQUNGOzRHQUNGO3dHQUNGO3dHQUNBd0ksTUFBTUksS0FBSztvR0FDYjs7c0hBRUEsOERBQUN2UCw4R0FBSUE7NEdBQUN1TyxNQUFNOzs7Ozs7d0dBQU07Ozs7Ozs7Z0dBR25Cbk0sYUFBYStGLFVBQVUsQ0FBQ00sa0JBQWtCLENBQUNXLEdBQUcsQ0FBQyxDQUFDOEgsVUFBVWhGLHNCQUN6RCw4REFBQ3dJO3dHQUFnQkMsV0FBVTs7MEhBQ3pCLDhEQUFDTTswSEFBTS9ELFNBQVN2SyxJQUFJOzs7Ozs7MEhBQ3BCLDhEQUFDa087Z0hBQ0N2TCxNQUFLO2dIQUNMcUwsV0FBVTtnSEFDVkcsU0FBUztvSEFDUCxNQUFNd0MsZUFBZWxWLGFBQWErRixVQUFVLENBQUNNLGtCQUFrQixDQUFDaUIsTUFBTSxDQUFDLENBQUN5RyxHQUFHQyxJQUFNQSxNQUFNbEU7b0hBQ3ZGekcsZ0JBQWdCOEUsQ0FBQUEsT0FBUzs0SEFDdkIsR0FBR0EsSUFBSTs0SEFDUHBDLFlBQVk7Z0lBQUUsR0FBR29DLEtBQUtwQyxVQUFVO2dJQUFFTSxvQkFBb0I2Tzs0SEFBYTt3SEFDckU7Z0hBQ0Y7MEhBQ0Q7Ozs7Ozs7dUdBWk9wTDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQWlDeEMsOERBQUN3STt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNEO2dDQUFJQyxXQUFVOzBDQUNiLDRFQUFDRTtvQ0FDQ0MsU0FBUzdCO29DQUNUMEIsV0FBVTtvQ0FDVjRDLFVBQVVsVixXQUFXbUgsTUFBTSxLQUFLLEtBQUtuRyxzQkFBc0JFLGlCQUFpQjtvQ0FDNUVtQyxPQUNFbkMsaUJBQWlCLElBQ2IsWUFDQWxCLFdBQVdtSCxNQUFNLEtBQUssSUFDcEIseUJBQ0FuRyxxQkFDRSxnQkFDQTs4Q0FHVEUsaUJBQWlCLElBQ2QsUUFDQUYscUJBQ0UsWUFDQTs7Ozs7Ozs7Ozs7MENBSVYsOERBQUNxUjtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNFO3dDQUFPQyxTQUFTYjt3Q0FBZ0JVLFdBQVU7a0RBQXVDOzs7Ozs7a0RBR2xGLDhEQUFDRTt3Q0FDQ0MsU0FBU3BEO3dDQUNUaUQsV0FBVTt3Q0FDVjRDLFVBQVUsQ0FBQzNVLG9CQUFvQlAsRUFBQUEsbUJBQUFBLFdBQVd1UCxJQUFJLENBQUN2SSxDQUFBQSxJQUFLQSxFQUFFaEMsRUFBRSxLQUFLekUsK0JBQTlCUCx1Q0FBQUEsaUJBQWlEVixNQUFNLE1BQUssS0FBS3NCO3dDQUNoR3lDLE9BQ0UsQ0FBQzlDLG1CQUNHLGVBQ0FQLEVBQUFBLG9CQUFBQSxXQUFXdVAsSUFBSSxDQUFDdkksQ0FBQUEsSUFBS0EsRUFBRWhDLEVBQUUsS0FBS3pFLCtCQUE5QlAsd0NBQUFBLGtCQUFpRFYsTUFBTSxNQUFLLElBQzFELFdBQ0FzQixlQUNFLGNBQ0E7a0RBR1RBLGVBQWUsWUFBWTs7Ozs7O2tEQUU5Qiw4REFBQzRSO3dDQUNDQyxTQUFTL0M7d0NBQ1Q0QyxXQUFVO3dDQUNWNEMsVUFBVXpVLGVBQWV5TCxJQUFJLEdBQUcsS0FBS3BMLGNBQWNkLFdBQVdtSCxNQUFNLEtBQUs7d0NBQ3pFOUQsT0FDRXJELFdBQVdtSCxNQUFNLEtBQUssSUFDbEIsYUFDQTFHLGVBQWV5TCxJQUFJLEdBQUcsSUFDcEIscUJBQ0FwTCxhQUNHZCxXQUFXbVYsSUFBSSxDQUFDbk8sQ0FBQUEsSUFBS0EsRUFBRWhDLEVBQUUsR0FBRyxXQUFXLGNBQWMsY0FDckRoRixXQUFXbVYsSUFBSSxDQUFDbk8sQ0FBQUEsSUFBS0EsRUFBRWhDLEVBQUUsR0FBRyxXQUFXLFNBQVM7a0RBRzFEdkUsZUFBZXlMLElBQUksR0FBRyxJQUNuQixXQUNBcEwsYUFDR2QsV0FBV21WLElBQUksQ0FBQ25PLENBQUFBLElBQUtBLEVBQUVoQyxFQUFFLEdBQUcsV0FBVyxZQUFZLFlBQ25EaEYsV0FBV21WLElBQUksQ0FBQ25PLENBQUFBLElBQUtBLEVBQUVoQyxFQUFFLEdBQUcsV0FBVyxTQUFTOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7WUFROUQ1RCxzQ0FDQyw4REFBQ2lSO2dCQUFJQyxXQUFVO2dCQUFnQkcsU0FBUzlHOzBCQUN0Qyw0RUFBQzBHO29CQUFJQyxXQUFVO29CQUF1QkcsU0FBUyxDQUFDckssSUFBTUEsRUFBRXlLLGVBQWU7O3NDQUNyRSw4REFBQ1I7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDSzs4Q0FBRzs7Ozs7OzhDQUNKLDhEQUFDSDtvQ0FDQ0MsU0FBUzlHO29DQUNUMkcsV0FBVTtvQ0FDVjRDLFVBQVUxVDs4Q0FFViw0RUFBQy9ELDhHQUFDQTt3Q0FBQzZVLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQUdqQiw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDSTs4Q0FDRWxSLGFBQ0csa0JBQ0E7Ozs7OztnQ0FHTEEsNEJBQ0MsOERBQUM2UTtvQ0FBSUMsV0FBVTs4Q0FDYiw0RUFBQ0Q7d0NBQUlDLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQUlyQiw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDRTtvQ0FDQ0MsU0FBUzlHO29DQUNUMkcsV0FBVTtvQ0FDVjRDLFVBQVUxVDs4Q0FDWDs7Ozs7OzhDQUdELDhEQUFDZ1I7b0NBQ0NDLFNBQVNsSDtvQ0FDVCtHLFdBQVU7b0NBQ1Y0QyxVQUFVMVQ7OENBRVRBLGFBQWEsWUFBWTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBUXBDLDhEQUFDcEQsNERBQW1CQTtnQkFDbEJnWCxRQUFRMVQ7Z0JBQ1JoQyxTQUFTLElBQU1pQyx3QkFBd0I7Z0JBQ3ZDMFQsa0JBQWtCdkQ7Ozs7Ozs7Ozs7OztBQUkxQjtHQTdvRU10UztLQUFBQTtBQStvRU4sK0RBQWVBLG1CQUFtQkEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9hcHAvd29ya2JlbmNoL2NvbXBvbmVudHMvQ291cnNlTGlzdEVkaXRNb2RhbC50c3g/MmQ4ZiJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0LCB1c2VDYWxsYmFjayB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IFgsIFNldHRpbmdzLCBQbHVzLCBCb29rT3BlbiwgRmlsZVRleHQgfSBmcm9tICdsdWNpZGUtcmVhY3QnO1xuaW1wb3J0IHsgY291cnNlTWFuYWdlbWVudEFwaSB9IGZyb20gJ0AvbGliL2FwaS9jb3Vyc2UtbWFuYWdlbWVudCc7XG5pbXBvcnQgeyBjb3Vyc2VBcGkgfSBmcm9tICdAL2xpYi9hcGkvY291cnNlJztcbmltcG9ydCB7IHVwbG9hZEFwaSB9IGZyb20gJ0AvbGliL2FwaS91cGxvYWQnO1xuaW1wb3J0IHsgd29ya3NBcGkgfSBmcm9tICdAL2xpYi9hcGkvd29ya3MnO1xuaW1wb3J0IHsgU2VsZWN0IH0gZnJvbSAnYW50ZCc7XG5pbXBvcnQgeyBHZXROb3RpZmljYXRpb24gfSBmcm9tICdsb2dpYy1jb21tb24vZGlzdC9jb21wb25lbnRzL05vdGlmaWNhdGlvbic7XG5pbXBvcnQgVGVtcGxhdGVQaWNrZXJNb2RhbCBmcm9tICcuL1RlbXBsYXRlUGlja2VyTW9kYWwnO1xuaW1wb3J0ICcuL0NvdXJzZUxpc3RFZGl0TW9kYWwuY3NzJztcbmltcG9ydCAnLi9OZXdQdWJsaXNoVGFza01vZGFsLmNzcyc7XG5cbmludGVyZmFjZSBDb3Vyc2VJdGVtIHtcbiAgaWQ6IG51bWJlcjtcbiAgc2VyaWVzSWQ6IG51bWJlcjtcbiAgdGl0bGU6IHN0cmluZztcbiAgZGVzY3JpcHRpb246IHN0cmluZztcbiAgY292ZXJJbWFnZTogc3RyaW5nO1xuICBvcmRlckluZGV4OiBudW1iZXI7XG4gIHN0YXR1czogbnVtYmVyO1xuICBzdGF0dXNMYWJlbDogc3RyaW5nO1xuICBoYXNWaWRlbzogbnVtYmVyO1xuICBoYXNEb2N1bWVudDogbnVtYmVyO1xuICBoYXNBdWRpbzogbnVtYmVyO1xuICB2aWRlb0R1cmF0aW9uOiBudW1iZXI7XG4gIHZpZGVvRHVyYXRpb25MYWJlbDogc3RyaW5nO1xuICB2aWRlb05hbWU6IHN0cmluZztcbiAgZmlyc3RUZWFjaGluZ1RpdGxlOiBzdHJpbmc7XG4gIHJlc291cmNlc0NvdW50OiBudW1iZXI7XG4gIGNyZWF0ZWRBdDogc3RyaW5nO1xuICB1cGRhdGVkQXQ6IHN0cmluZztcbiAgLy8g5paw5aKe5a2X5q6155So5LqO5a2Y5YKo5a6M5pW055qE6K++56iL6K+m5oOFXG4gIGNvbnRlbnRDb25maWc/OiBhbnk7XG4gIHRlYWNoaW5nSW5mbz86IGFueVtdO1xuICBhZGRpdGlvbmFsUmVzb3VyY2VzPzogQXJyYXk8e1xuICAgIHRpdGxlOiBzdHJpbmc7XG4gICAgdXJsOiBzdHJpbmc7XG4gICAgZGVzY3JpcHRpb246IHN0cmluZztcbiAgfT47XG59XG5cbmludGVyZmFjZSBDb3Vyc2VUYWcge1xuICBpZDogbnVtYmVyO1xuICBuYW1lOiBzdHJpbmc7XG4gIGNvbG9yOiBzdHJpbmc7XG4gIGNhdGVnb3J5OiBudW1iZXI7XG4gIGRlc2NyaXB0aW9uOiBzdHJpbmc7XG4gIHN0YXR1czogbnVtYmVyO1xufVxuXG5pbnRlcmZhY2UgQXBpUmVzcG9uc2Uge1xuICBjb2RlOiBudW1iZXI7XG4gIG1lc3NhZ2U6IHN0cmluZztcbiAgZGF0YToge1xuICAgIGxpc3Q6IENvdXJzZUl0ZW1bXTtcbiAgICBwYWdpbmF0aW9uOiB7XG4gICAgICBwYWdlOiBudW1iZXI7XG4gICAgICBwYWdlU2l6ZTogbnVtYmVyO1xuICAgICAgdG90YWw6IG51bWJlcjtcbiAgICAgIHRvdGFsUGFnZXM6IG51bWJlcjtcbiAgICAgIGhhc05leHQ6IGJvb2xlYW47XG4gICAgICBoYXNQcmV2OiBib29sZWFuO1xuICAgIH07XG4gIH07XG59XG5cbmludGVyZmFjZSBDb3Vyc2VMaXN0RWRpdE1vZGFsUHJvcHMge1xuICBpc1Zpc2libGU6IGJvb2xlYW47XG4gIG9uQ2xvc2U6ICgpID0+IHZvaWQ7XG4gIG9uU2F2ZTogKGRhdGE6IGFueSkgPT4gdm9pZDtcbiAgc2VyaWVzVGl0bGU6IHN0cmluZztcbiAgc2VyaWVzQ292ZXJJbWFnZT86IHN0cmluZztcbiAgc2VyaWVzSWQ6IG51bWJlcjtcbn1cblxuLy8g6I635Y+W57O75YiX6K++56iL6K+m5oOFXG5jb25zdCBmZXRjaFNlcmllc0RldGFpbCA9IGFzeW5jIChzZXJpZXNJZDogbnVtYmVyKTogUHJvbWlzZTxhbnk+ID0+IHtcbiAgY29uc29sZS5sb2coJ/CflI0g6I635Y+W57O75YiX6K++56iL6K+m5oOF77yMc2VyaWVzSWQ6Jywgc2VyaWVzSWQpO1xuICBjb25zb2xlLmxvZygn8J+UlyDosIPnlKhBUEk6IEdFVCAvYXBpL3YxL2NvdXJzZS1tYW5hZ2VtZW50L3Nlcmllcy97c2VyaWVzSWR9Jyk7XG5cbiAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBjb3Vyc2VBcGkuZ2V0U2VyaWVzRGV0YWlsKHNlcmllc0lkKTtcbiAgY29uc29sZS5sb2coJ/Cfk6Eg57O75YiX6K+m5oOFQVBJ5ZON5bqUOicsIHJlc3BvbnNlKTtcbiAgcmV0dXJuIHJlc3BvbnNlLmRhdGE7XG59O1xuXG4vLyBBUEnosIPnlKjlh73mlbAgLSDojrflj5bns7vliJfkuIvnmoTmiYDmnInor77nqIvvvIjljIXlkKvmnKrlj5HluIPlkozlt7Llj5HluIPvvIlcbmNvbnN0IGZldGNoQ291cnNlTGlzdCA9IGFzeW5jIChzZXJpZXNJZDogbnVtYmVyKTogUHJvbWlzZTxBcGlSZXNwb25zZT4gPT4ge1xuICBjb25zb2xlLmxvZygn8J+UjSDojrflj5bns7vliJfor77nqIvliJfooajvvIxzZXJpZXNJZDonLCBzZXJpZXNJZCk7XG4gIGNvbnNvbGUubG9nKCfwn5SXIOiwg+eUqEFQSTogR0VUIC9hcGkvdjEvY291cnNlLW1hbmFnZW1lbnQvc2VyaWVzL3tzZXJpZXNJZH0vY291cnNlcycpO1xuXG4gIC8vIOS9v+eUqOivvueoi+euoeeQhkFQSeiOt+WPluaJgOacieeKtuaAgeeahOivvueoi1xuICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGNvdXJzZUFwaS5nZXRNYW5hZ2VtZW50U2VyaWVzQ291cnNlcyhzZXJpZXNJZCwge1xuICAgIHBhZ2U6IDEsXG4gICAgcGFnZVNpemU6IDEwMCxcbiAgICAvLyDkuI3kvKBzdGF0dXPlj4LmlbDvvIzojrflj5bmiYDmnInnirbmgIHnmoTor77nqIvvvIhzdGF0dXM9MOacquWPkeW4g++8jHN0YXR1cz0x5bey5Y+R5biD77yJXG4gIH0pO1xuXG4gIGNvbnNvbGUubG9nKCfwn5OhIEFQSeWTjeW6lDonLCByZXNwb25zZSk7XG4gIHJldHVybiByZXNwb25zZS5kYXRhO1xufTtcblxuLy8g6I635Y+W6K++56iL6K+m5oOFXG5jb25zdCBmZXRjaENvdXJzZURldGFpbCA9IGFzeW5jIChzZXJpZXNJZDogbnVtYmVyLCBjb3Vyc2VJZDogbnVtYmVyKSA9PiB7XG4gIHJldHVybiBhd2FpdCBjb3Vyc2VBcGkuZ2V0Q291cnNlTWFya2V0cGxhY2VEZXRhaWwoc2VyaWVzSWQsIGNvdXJzZUlkKTtcbn07XG5cbi8vIOiOt+WPluivvueoi+agh+etvlxuY29uc3QgZmV0Y2hDb3Vyc2VUYWdzID0gYXN5bmMgKCk6IFByb21pc2U8YW55PiA9PiB7XG4gIHRyeSB7XG4gICAgY29uc29sZS5sb2coJ/CflI0g5byA5aeL6LCD55SoIGNvdXJzZUFwaS5nZXRDb3Vyc2VUYWdzJyk7XG4gICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgY291cnNlQXBpLmdldENvdXJzZVRhZ3Moe1xuICAgICAgcGFnZTogMSxcbiAgICAgIHBhZ2VTaXplOiAxMDAsXG4gICAgICBzdGF0dXM6IDEgLy8g5Y+q6I635Y+W5ZCv55So55qE5qCH562+XG4gICAgfSk7XG4gICAgY29uc29sZS5sb2coJ/CflI0gY291cnNlQXBpLmdldENvdXJzZVRhZ3Mg6L+U5Zue57uT5p6cOicsIHJlc3VsdCk7XG4gICAgcmV0dXJuIHJlc3VsdDtcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCfwn5SNIGNvdXJzZUFwaS5nZXRDb3Vyc2VUYWdzIOiwg+eUqOWksei0pTonLCBlcnJvcik7XG4gICAgdGhyb3cgZXJyb3I7XG4gIH1cbn07XG5cbmNvbnN0IENvdXJzZUxpc3RFZGl0TW9kYWw6IFJlYWN0LkZDPENvdXJzZUxpc3RFZGl0TW9kYWxQcm9wcz4gPSAoe1xuICBpc1Zpc2libGUsXG4gIG9uQ2xvc2UsXG4gIG9uU2F2ZSxcbiAgc2VyaWVzVGl0bGUsXG4gIHNlcmllc0NvdmVySW1hZ2UsXG4gIHNlcmllc0lkID0gMTIzIC8vIOm7mOiupOWAvO+8jOWunumZheS9v+eUqOaXtuW6lOivpeS8oOWFpeecn+WunueahHNlcmllc0lkXG59KSA9PiB7XG4gIGNvbnN0IG5vdGlmaWNhdGlvbiA9IEdldE5vdGlmaWNhdGlvbigpO1xuXG4gIGNvbnN0IFtjb3Vyc2VMaXN0LCBzZXRDb3Vyc2VMaXN0XSA9IHVzZVN0YXRlPENvdXJzZUl0ZW1bXT4oW10pO1xuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtyaWdodFBhbmVsVHlwZSwgc2V0UmlnaHRQYW5lbFR5cGVdID0gdXNlU3RhdGU8J25vbmUnIHwgJ3NldHRpbmdzJyB8ICdjb3Vyc2UnPignbm9uZScpO1xuICBjb25zdCBbc2VsZWN0ZWRDb3Vyc2VJZCwgc2V0U2VsZWN0ZWRDb3Vyc2VJZF0gPSB1c2VTdGF0ZTxudW1iZXIgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW3VwbG9hZGluZ0ZpbGVzLCBzZXRVcGxvYWRpbmdGaWxlc10gPSB1c2VTdGF0ZTxTZXQ8c3RyaW5nPj4obmV3IFNldCgpKTtcbiAgY29uc3QgW2lzUHVibGlzaGluZywgc2V0SXNQdWJsaXNoaW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2lzQ3JlYXRpbmcsIHNldElzQ3JlYXRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbaXNQdWJsaXNoaW5nU2VyaWVzLCBzZXRJc1B1Ymxpc2hpbmdTZXJpZXNdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbc2VyaWVzU3RhdHVzLCBzZXRTZXJpZXNTdGF0dXNdID0gdXNlU3RhdGU8bnVtYmVyPigwKTsgLy8gMD3ojYnnqL/vvIwxPeW3suWPkeW4g++8jDI95bey5b2S5qGjXG5cbiAgLy8g5Yig6Zmk56Gu6K6k5by556qX54q25oCBXG4gIGNvbnN0IFtkZWxldGVDb25maXJtVmlzaWJsZSwgc2V0RGVsZXRlQ29uZmlybVZpc2libGVdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbY291cnNlVG9EZWxldGUsIHNldENvdXJzZVRvRGVsZXRlXSA9IHVzZVN0YXRlPG51bWJlciB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbaXNEZWxldGluZywgc2V0SXNEZWxldGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG5cbiAgLy8g5qih5p2/6YCJ5oup5by556qX54q25oCBXG4gIGNvbnN0IFtpc1RlbXBsYXRlUGlja2VyT3Blbiwgc2V0SXNUZW1wbGF0ZVBpY2tlck9wZW5dID0gdXNlU3RhdGUoZmFsc2UpO1xuXG4gIGNvbnN0IFtlZGl0aW5nVGl0bGUsIHNldEVkaXRpbmdUaXRsZV0gPSB1c2VTdGF0ZShzZXJpZXNUaXRsZSk7XG4gIGNvbnN0IFtjb3Vyc2VHb2Fscywgc2V0Q291cnNlR29hbHNdID0gdXNlU3RhdGUoJycpO1xuICBjb25zdCBbY291cnNlT2JqZWN0aXZlcywgc2V0Q291cnNlT2JqZWN0aXZlc10gPSB1c2VTdGF0ZSgnJyk7XG4gIGNvbnN0IFtwcm9qZWN0TWVtYmVycywgc2V0UHJvamVjdE1lbWJlcnNdID0gdXNlU3RhdGUoJycpO1xuXG4gIC8vIOS9nOWTgeebuOWFs+eKtuaAgVxuICBjb25zdCBbd29ya3MsIHNldFdvcmtzXSA9IHVzZVN0YXRlPGFueVtdPihbXSk7XG4gIGNvbnN0IFtsb2FkaW5nV29ya3MsIHNldExvYWRpbmdXb3Jrc10gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtzZWxlY3RlZFdvcmtJZHMsIHNldFNlbGVjdGVkV29ya0lkc10gPSB1c2VTdGF0ZTxudW1iZXJbXT4oW10pO1xuXG4gIC8vIOiDvemHj+i+k+WFpemqjOivgeeKtuaAgVxuICBjb25zdCBbZW5lcmd5RXJyb3IsIHNldEVuZXJneUVycm9yXSA9IHVzZVN0YXRlPHN0cmluZz4oJycpO1xuXG4gIC8vIOaXtumXtOmAieaLqeeKtuaAgVxuICBjb25zdCBbc2hvd0R1cmF0aW9uUGlja2VyLCBzZXRTaG93RHVyYXRpb25QaWNrZXJdID0gdXNlU3RhdGUoZmFsc2UpO1xuXG4gIC8vIOivvueoi+agh+etvuebuOWFs+eKtuaAgVxuICBjb25zdCBbY291cnNlVGFncywgc2V0Q291cnNlVGFnc10gPSB1c2VTdGF0ZTxDb3Vyc2VUYWdbXT4oW10pO1xuICBjb25zdCBbc2VsZWN0ZWRUYWdzLCBzZXRTZWxlY3RlZFRhZ3NdID0gdXNlU3RhdGU8bnVtYmVyW10+KFtdKTtcbiAgY29uc3QgW3RhZ3NMb2FkaW5nLCBzZXRUYWdzTG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG5cbiAgLy8g6K++56iL6K+m57uG57yW6L6R54q25oCBXG4gIGNvbnN0IFtjb3Vyc2VEZXRhaWwsIHNldENvdXJzZURldGFpbF0gPSB1c2VTdGF0ZSh7XG4gICAgdGl0bGU6ICcnLFxuICAgIGRlc2NyaXB0aW9uOiAnJyxcbiAgICBjb3ZlckltYWdlOiAnJyxcbiAgICB2aWRlb1VybDogJycsXG4gICAgdmlkZW9OYW1lOiAnJyxcbiAgICBpc1ZpZGVvRW5hYmxlZDogZmFsc2UsXG4gICAgYXR0YWNobWVudFVybDogJycsXG4gICAgYXR0YWNobWVudE5hbWU6ICcnLFxuICAgIGlzQXR0YWNobWVudEVuYWJsZWQ6IGZhbHNlLFxuICAgIHRlYWNoaW5nTWF0ZXJpYWxzOiBbXSBhcyB7IHR5cGU6IHN0cmluZzsgbmFtZTogc3RyaW5nOyB1cmw6IHN0cmluZyB9W10sXG4gICAgLy8g5pSv5oyBdGVhY2hpbmdJbmZv57uT5p6EXG4gICAgdGVhY2hpbmdJbmZvOiBbXSBhcyB7IHRpdGxlOiBzdHJpbmc7IGNvbnRlbnQ6IHN0cmluZyB9W10sXG4gICAgLy8g5pSv5oyBY29udGVudENvbmZpZ+e7k+aehFxuICAgIGNvbnRlbnRDb25maWc6IHtcbiAgICAgIGhhc1ZpZGVvOiAwLFxuICAgICAgaGFzRG9jdW1lbnQ6IDAsXG4gICAgICBoYXNBdWRpbzogMCxcbiAgICAgIHZpZGVvOiB7IHVybDogJycsIG5hbWU6ICcnIH0sXG4gICAgICBkb2N1bWVudDogeyB1cmw6ICcnLCBuYW1lOiAnJyB9LFxuICAgICAgYXVkaW86IHsgdXJsOiAnJywgbmFtZTogJycgfVxuICAgIH0sXG4gICAgY291cnNlQ29udGVudDoge1xuICAgICAgdG9waWM6ICcnLFxuICAgICAgY29udGVudDogJydcbiAgICB9LFxuICAgIGlzT25lS2V5T3BlbjogZmFsc2UsXG4gICAgaXNEaXN0cmlidXRpb25FbmFibGVkOiBmYWxzZSxcbiAgICBkaXN0cmlidXRpb25SZXdhcmQ6ICcnLFxuICAgIHNlbGVjdGVkVGVtcGxhdGU6IHtcbiAgICAgIGlkOiBudWxsLFxuICAgICAgbmFtZTogJydcbiAgICB9LFxuICAgIGlzRGlzdHJpYnV0aW9uV2F0ZXI6IGZhbHNlLFxuICAgIHJlcXVpcmVkRW5lcmd5OiAnJyxcbiAgICBlbmVyZ3lBbW91bnQ6ICcnLFxuICAgIGlzRGlzdHJpYnV0aW9uTGltaXQ6IGZhbHNlLFxuICAgIGRpc3RyaWJ1dGlvbkNvbmRpdGlvbnM6IHtcbiAgICAgIGludml0ZUNvdW50OiAnJyxcbiAgICAgIHRhc2tDb3VudDogJycsXG4gICAgICBleHBlcmllbmNlOiAnJ1xuICAgIH0sXG4gICAgaXNEaXN0cmlidXRpb25UaW1lOiBmYWxzZSxcbiAgICBkaXN0cmlidXRpb25UaW1lQ29uZGl0aW9uczoge1xuICAgICAgc3RhcnRUaW1lOiAnJyxcbiAgICAgIGVuZFRpbWU6ICcnXG4gICAgfSxcbiAgICBkaXN0cmlidXRpb25NYXRlcmlhbHM6IFtdIGFzIHsgdHlwZTogc3RyaW5nOyBuYW1lOiBzdHJpbmcgfVtdLFxuICAgIC8vIOS7u+WKoemFjee9ruebuOWFs+eKtuaAgVxuICAgIHRhc2tDb25maWc6IHtcbiAgICAgIHRhc2tOYW1lOiAnJyxcbiAgICAgIHRhc2tEdXJhdGlvbjogJycsXG4gICAgICB0YXNrRGVzY3JpcHRpb246ICcnLFxuICAgICAgc2VsZkFzc2Vzc21lbnRJdGVtczogWycnXSxcbiAgICAgIHJlZmVyZW5jZVdvcmtzOiBbXSBhcyB7IHR5cGU6IHN0cmluZzsgbmFtZTogc3RyaW5nIH1bXSxcbiAgICAgIHJlZmVyZW5jZVJlc291cmNlczogW10gYXMgeyB0eXBlOiBzdHJpbmc7IG5hbWU6IHN0cmluZzsgdXJsPzogc3RyaW5nIH1bXVxuICAgIH1cbiAgfSk7XG5cbiAgLy8g6I635Y+W6K++56iL5YiX6KGo5pWw5o2uXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKGlzVmlzaWJsZSAmJiBzZXJpZXNJZCkge1xuICAgICAgLy8g5qOA5p+l55So5oi355m75b2V54q25oCBXG4gICAgICBjb25zdCB0b2tlbiA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCd0b2tlbicpO1xuICAgICAgY29uc29sZS5sb2coJ/CflJAg5qOA5p+l55m75b2V54q25oCB77yMdG9rZW7lrZjlnKg6JywgISF0b2tlbik7XG4gICAgICBjb25zb2xlLmxvZygn8J+UjSBzZXJpZXNJZDonLCBzZXJpZXNJZCk7XG5cbiAgICAgIGlmICghdG9rZW4pIHtcbiAgICAgICAgY29uc29sZS5lcnJvcign4p2MIOeUqOaIt+acqueZu+W9le+8jOaXoOazleiOt+WPluivvueoi+WIl+ihqCcpO1xuICAgICAgICAvLyDorr7nva7nqbrliJfooajvvIzmmL7npLrnqbrnirbmgIFcbiAgICAgICAgc2V0Q291cnNlTGlzdChbXSk7XG4gICAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG5cbiAgICAgIGxvYWRDb3Vyc2VMaXN0KCk7XG4gICAgICBsb2FkQ291cnNlVGFncygpO1xuICAgICAgbG9hZFNlcmllc0RldGFpbCgpO1xuICAgICAgbG9hZFVzZXJXb3JrcygpO1xuICAgIH1cbiAgfSwgW2lzVmlzaWJsZSwgc2VyaWVzSWRdKTtcblxuICBjb25zdCBsb2FkQ291cnNlTGlzdCA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgc2V0TG9hZGluZyh0cnVlKTtcbiAgICAgIGNvbnNvbGUubG9nKCfwn5SNIOW8gOWni+WKoOi9veivvueoi+WIl+ihqO+8jHNlcmllc0lkOicsIHNlcmllc0lkKTtcblxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaENvdXJzZUxpc3Qoc2VyaWVzSWQpO1xuICAgICAgY29uc29sZS5sb2coJ/Cfk6EgQVBJ5ZON5bqUOicsIHJlc3BvbnNlKTtcblxuICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkge1xuICAgICAgICBjb25zb2xlLmxvZygn4pyFIOivvueoi+WIl+ihqOaVsOaNrjonLCByZXNwb25zZS5kYXRhKTtcbiAgICAgICAgY29uc3QgY291cnNlcyA9IHJlc3BvbnNlLmRhdGEubGlzdCB8fCBbXTtcbiAgICAgICAgY29uc29sZS5sb2coJ+KchSDop6PmnpDnmoTor77nqIvmlbDnu4Q6JywgY291cnNlcyk7XG4gICAgICAgIGNvbnNvbGUubG9nKCfwn5OKIOivvueoi0lE6K+m5oOFOicsIGNvdXJzZXMubWFwKChjOiBhbnkpID0+ICh7XG4gICAgICAgICAgaWQ6IGMuaWQsXG4gICAgICAgICAgdHlwZTogdHlwZW9mIGMuaWQsXG4gICAgICAgICAgdGl0bGU6IGMudGl0bGUsXG4gICAgICAgICAgc3RhdHVzOiBjLnN0YXR1c1xuICAgICAgICB9KSkpO1xuICAgICAgICBjb25zb2xlLmxvZygn8J+TiiDor77nqIvnirbmgIHnu5/orqE6Jywge1xuICAgICAgICAgIHRvdGFsOiBjb3Vyc2VzLmxlbmd0aCxcbiAgICAgICAgICBkcmFmdDogY291cnNlcy5maWx0ZXIoKGM6IGFueSkgPT4gYy5zdGF0dXMgPT09IDApLmxlbmd0aCxcbiAgICAgICAgICBwdWJsaXNoZWQ6IGNvdXJzZXMuZmlsdGVyKChjOiBhbnkpID0+IGMuc3RhdHVzID09PSAxKS5sZW5ndGhcbiAgICAgICAgfSk7XG4gICAgICAgIHNldENvdXJzZUxpc3QoY291cnNlcyk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBjb25zb2xlLmVycm9yKCfinYwgQVBJ6L+U5Zue6ZSZ6K+vOicsIHJlc3BvbnNlKTtcbiAgICAgICAgc2V0Q291cnNlTGlzdChbXSk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgY29uc29sZS5lcnJvcign4p2MIOWKoOi9veivvueoi+WIl+ihqOWksei0pTonLCBlcnJvcik7XG5cbiAgICAgIC8vIOajgOafpeaYr+WQpuaYr+iupOivgemUmeivr1xuICAgICAgaWYgKGVycm9yLnJlc3BvbnNlPy5zdGF0dXMgPT09IDQwMSkge1xuICAgICAgICBjb25zb2xlLmVycm9yKCfwn5SQIOiupOivgeWksei0pe+8jOeUqOaIt+acqueZu+W9leaIlnRva2Vu5bey6L+H5pyfJyk7XG4gICAgICB9IGVsc2UgaWYgKGVycm9yLnJlc3BvbnNlPy5zdGF0dXMgPT09IDQwMykge1xuICAgICAgICBjb25zb2xlLmVycm9yKCfwn5qrIOadg+mZkOS4jei2s++8jOaXoOazleiuv+mXruivpeezu+WIl+ivvueoiycpO1xuICAgICAgfSBlbHNlIGlmIChlcnJvci5yZXNwb25zZT8uc3RhdHVzID09PSA0MDQpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcign8J+TrSDns7vliJfor77nqIvkuI3lrZjlnKjvvIxzZXJpZXNJZDonLCBzZXJpZXNJZCk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBjb25zb2xlLmVycm9yKCfwn5SnIOWFtuS7lumUmeivrzonLCBlcnJvci5tZXNzYWdlKTtcbiAgICAgIH1cblxuICAgICAgc2V0Q291cnNlTGlzdChbXSk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICAvLyDliqDovb3ns7vliJfor77nqIvor6bmg4VcbiAgY29uc3QgbG9hZFNlcmllc0RldGFpbCA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc29sZS5sb2coJ/CflI0g5byA5aeL5Yqg6L2957O75YiX6K++56iL6K+m5oOF77yMc2VyaWVzSWQ6Jywgc2VyaWVzSWQpO1xuXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoU2VyaWVzRGV0YWlsKHNlcmllc0lkKTtcbiAgICAgIGNvbnNvbGUubG9nKCfwn5OhIOezu+WIl+ivpuaDheWTjeW6lDonLCByZXNwb25zZSk7XG5cbiAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDAgJiYgcmVzcG9uc2UuZGF0YSkge1xuICAgICAgICBjb25zdCBzZXJpZXNEYXRhID0gcmVzcG9uc2UuZGF0YTtcbiAgICAgICAgY29uc29sZS5sb2coJ+KchSDns7vliJfor77nqIvor6bmg4U6Jywgc2VyaWVzRGF0YSk7XG4gICAgICAgIHNldFNlcmllc1N0YXR1cyhzZXJpZXNEYXRhLnN0YXR1cyB8fCAwKTtcbiAgICAgICAgY29uc29sZS5sb2coJ/Cfk4og57O75YiX6K++56iL54q25oCBOicsIHNlcmllc0RhdGEuc3RhdHVzLCAnKDA96I2J56i/77yMMT3lt7Llj5HluIPvvIwyPeW3suW9kuahoyknKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDojrflj5bns7vliJfor6bmg4XlpLHotKU6JywgcmVzcG9uc2UubWVzc2FnZSk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgY29uc29sZS5lcnJvcign4p2MIOWKoOi9veezu+WIl+ivpuaDheW8guW4uDonLCBlcnJvcik7XG4gICAgfVxuICB9O1xuXG4gIC8vIOWKoOi9veeUqOaIt+S9nOWTgeaVsOaNrlxuICBjb25zdCBsb2FkVXNlcldvcmtzID0gYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBzZXRMb2FkaW5nV29ya3ModHJ1ZSk7XG5cbiAgICAgIC8vIOS7jmxvY2FsU3RvcmFnZeiOt+WPlueUqOaIt0lEXG4gICAgICBjb25zdCB1c2VySWQgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgndXNlcklkJykgfHwgJzI4OTYnOyAvLyDpu5jorqTkvb/nlKgyODk2XG5cbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgd29ya3NBcGkuZ2V0VGVhY2hlcldvcmtzKE51bWJlcih1c2VySWQpLCAxLCAxMDAwKTtcblxuICAgICAgLy8g5qOA5p+l5aSa56eN5Y+v6IO955qE5pWw5o2u57uT5p6EXG4gICAgICBsZXQgd29ya3NMaXN0ID0gW107XG5cbiAgICAgIGlmIChyZXNwb25zZS5kYXRhPy5jb2RlID09PSAyMDApIHtcbiAgICAgICAgLy8g5oOF5Ya1MTogcmVzcG9uc2UuZGF0YS5kYXRhLmxpc3RcbiAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGE/LmRhdGE/Lmxpc3QpIHtcbiAgICAgICAgICB3b3Jrc0xpc3QgPSByZXNwb25zZS5kYXRhLmRhdGEubGlzdDtcbiAgICAgICAgfVxuICAgICAgICAvLyDmg4XlhrUyOiByZXNwb25zZS5kYXRhLmRhdGEgKOebtOaOpeaYr+aVsOe7hClcbiAgICAgICAgZWxzZSBpZiAoQXJyYXkuaXNBcnJheShyZXNwb25zZS5kYXRhPy5kYXRhKSkge1xuICAgICAgICAgIHdvcmtzTGlzdCA9IHJlc3BvbnNlLmRhdGEuZGF0YTtcbiAgICAgICAgfVxuICAgICAgICAvLyDmg4XlhrUzOiByZXNwb25zZS5kYXRhICjnm7TmjqXmmK/mlbDnu4QpXG4gICAgICAgIGVsc2UgaWYgKEFycmF5LmlzQXJyYXkocmVzcG9uc2UuZGF0YSkpIHtcbiAgICAgICAgICB3b3Jrc0xpc3QgPSByZXNwb25zZS5kYXRhO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgICAvLyDmg4XlhrU0OiDnm7TmjqXmo4Dmn6VyZXNwb25zZeaYr+WQpuaYr+aVsOe7hFxuICAgICAgZWxzZSBpZiAoQXJyYXkuaXNBcnJheShyZXNwb25zZSkpIHtcbiAgICAgICAgd29ya3NMaXN0ID0gcmVzcG9uc2U7XG4gICAgICB9XG5cbiAgICAgIHNldFdvcmtzKHdvcmtzTGlzdCk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+WKoOi9veS9nOWTgeaVsOaNruWksei0pTonLCBlcnJvcik7XG4gICAgICBzZXRXb3JrcyhbXSk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldExvYWRpbmdXb3JrcyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIC8vIOWkhOeQhuS9nOWTgemAieaLqVxuICBjb25zdCBoYW5kbGVXb3JrU2VsZWN0ID0gKHdvcmtJZDogbnVtYmVyKSA9PiB7XG4gICAgaWYgKHNlbGVjdGVkV29ya0lkcy5pbmNsdWRlcyh3b3JrSWQpKSB7XG4gICAgICAvLyDlj5bmtojpgInkuK1cbiAgICAgIHNldFNlbGVjdGVkV29ya0lkcyhwcmV2ID0+IHByZXYuZmlsdGVyKGlkID0+IGlkICE9PSB3b3JrSWQpKTtcbiAgICB9IGVsc2Uge1xuICAgICAgLy8g6YCJ5LitXG4gICAgICBzZXRTZWxlY3RlZFdvcmtJZHMocHJldiA9PiBbLi4ucHJldiwgd29ya0lkXSk7XG4gICAgfVxuICB9O1xuXG4gIC8vIOm8oOagh+a7mui9ruS6i+S7tuWkhOeQhiAtIOWwhuWeguebtOa7mui9rui9rOaNouS4uuawtOW5s+a7muWKqFxuICBjb25zdCBoYW5kbGVXaGVlbCA9IHVzZUNhbGxiYWNrKChlOiBSZWFjdC5XaGVlbEV2ZW50PEhUTUxEaXZFbGVtZW50PikgPT4ge1xuICAgIGNvbnN0IGNvbnRhaW5lciA9IGUuY3VycmVudFRhcmdldDtcbiAgICBjb25zdCB7IHNjcm9sbFdpZHRoLCBjbGllbnRXaWR0aCB9ID0gY29udGFpbmVyO1xuXG4gICAgLy8g5qOA5p+l5piv5ZCm5Y+v5Lul5rua5YqoXG4gICAgaWYgKHNjcm9sbFdpZHRoIDw9IGNsaWVudFdpZHRoKSB7XG4gICAgICByZXR1cm47IC8vIOWGheWuueS4jeWkn+mVv++8jOS4jemcgOimgea7muWKqFxuICAgIH1cblxuICAgIC8vIOmYu+atoum7mOiupOeahOWeguebtOa7muWKqOihjOS4ulxuICAgIGUucHJldmVudERlZmF1bHQoKTtcblxuICAgIC8vIOWwhuWeguebtOa7mui9rui9rOaNouS4uuawtOW5s+a7muWKqFxuICAgIGNvbnRhaW5lci5zY3JvbGxMZWZ0ICs9IGUuZGVsdGFZO1xuICB9LCBbXSk7XG5cbiAgLy8g5aSE55CG6IO96YeP6L6T5YWl5Y+Y5YyWXG4gIGNvbnN0IGhhbmRsZUVuZXJneUNoYW5nZSA9ICh2YWx1ZTogc3RyaW5nKSA9PiB7XG4gICAgc2V0Q291cnNlRGV0YWlsKHByZXYgPT4gKHsgLi4ucHJldiwgcmVxdWlyZWRFbmVyZ3k6IHZhbHVlIH0pKTtcblxuICAgIC8vIOmqjOivgei+k+WFpeWAvFxuICAgIGNvbnN0IG51bVZhbHVlID0gcGFyc2VGbG9hdCh2YWx1ZSk7XG4gICAgaWYgKHZhbHVlICYmIChpc05hTihudW1WYWx1ZSkgfHwgbnVtVmFsdWUgPCAwKSkge1xuICAgICAgc2V0RW5lcmd5RXJyb3IoJ+WIhumFjeiDveWKm+WAvOS4jeiDveS4uui0n+aVsCcpO1xuICAgIH0gZWxzZSB7XG4gICAgICBzZXRFbmVyZ3lFcnJvcignJyk7XG4gICAgfVxuICB9O1xuXG4gIC8vIOWkhOeQhuaXtumXtOmAieaLqVxuICBjb25zdCBoYW5kbGVEdXJhdGlvblNlbGVjdCA9IChob3VyczogbnVtYmVyLCBsYWJlbDogc3RyaW5nKSA9PiB7XG4gICAgc2V0Q291cnNlRGV0YWlsKHByZXYgPT4gKHtcbiAgICAgIC4uLnByZXYsXG4gICAgICB0YXNrQ29uZmlnOiB7IC4uLnByZXYudGFza0NvbmZpZywgdGFza0R1cmF0aW9uOiBgJHtob3Vyc33lsI/ml7YgKCR7bGFiZWx9KWAgfVxuICAgIH0pKTtcbiAgICBzZXRTaG93RHVyYXRpb25QaWNrZXIoZmFsc2UpO1xuICB9O1xuXG5cblxuICAvLyDngrnlh7vlpJbpg6jlhbPpl63ml7bpl7TpgInmi6nlmahcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBoYW5kbGVDbGlja091dHNpZGUgPSAoZXZlbnQ6IE1vdXNlRXZlbnQpID0+IHtcbiAgICAgIGNvbnN0IHRhcmdldCA9IGV2ZW50LnRhcmdldCBhcyBFbGVtZW50O1xuICAgICAgaWYgKCF0YXJnZXQuY2xvc2VzdCgnLmR1cmF0aW9uLWlucHV0LXdyYXBwZXInKSkge1xuICAgICAgICBzZXRTaG93RHVyYXRpb25QaWNrZXIoZmFsc2UpO1xuICAgICAgfVxuICAgIH07XG5cbiAgICBpZiAoc2hvd0R1cmF0aW9uUGlja2VyKSB7XG4gICAgICBkb2N1bWVudC5hZGRFdmVudExpc3RlbmVyKCdtb3VzZWRvd24nLCBoYW5kbGVDbGlja091dHNpZGUpO1xuICAgICAgcmV0dXJuICgpID0+IGRvY3VtZW50LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ21vdXNlZG93bicsIGhhbmRsZUNsaWNrT3V0c2lkZSk7XG4gICAgfVxuICB9LCBbc2hvd0R1cmF0aW9uUGlja2VyXSk7XG5cbiAgLy8g5Yqg6L296K++56iL5qCH562+XG4gIGNvbnN0IGxvYWRDb3Vyc2VUYWdzID0gYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBzZXRUYWdzTG9hZGluZyh0cnVlKTtcbiAgICAgIGNvbnNvbGUubG9nKCfwn4+377iPIOW8gOWni+WKoOi9veivvueoi+agh+etvicpO1xuXG5cblxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaENvdXJzZVRhZ3MoKTtcbiAgICAgIGNvbnNvbGUubG9nKCfwn5OhIOagh+etvkFQSeWujOaVtOWTjeW6lDonLCByZXNwb25zZSk7XG5cbiAgICAgIC8vIOajgOafpeWTjeW6lOe7k+aehFxuICAgICAgaWYgKHJlc3BvbnNlICYmIHJlc3BvbnNlLmRhdGEpIHtcbiAgICAgICAgY29uc29sZS5sb2coJ/Cfk4og5ZON5bqU5pWw5o2uOicsIHJlc3BvbnNlLmRhdGEpO1xuXG4gICAgICAgIGxldCB0YWdzOiBDb3Vyc2VUYWdbXSA9IFtdO1xuXG4gICAgICAgIC8vIOWkhOeQhuagh+WHhueahEFQSeWTjeW6lOagvOW8jyAocmVzcG9uc2UuZGF0YS5saXN0KSAtIOagueaNruWunumZhUFQSeWTjeW6lFxuICAgICAgICBpZiAocmVzcG9uc2UuZGF0YS5saXN0ICYmIEFycmF5LmlzQXJyYXkocmVzcG9uc2UuZGF0YS5saXN0KSkge1xuICAgICAgICAgIHRhZ3MgPSByZXNwb25zZS5kYXRhLmxpc3Q7XG4gICAgICAgICAgY29uc29sZS5sb2coJ+KchSDku44gZGF0YS5saXN0IOino+aekOWIsOagh+etvjonLCB0YWdzLmxlbmd0aCwgJ+S4qicpO1xuICAgICAgICB9XG4gICAgICAgIC8vIOWkhOeQhuebtOaOpeaVsOe7hOagvOW8jyAocmVzcG9uc2UuZGF0YSlcbiAgICAgICAgZWxzZSBpZiAoQXJyYXkuaXNBcnJheShyZXNwb25zZS5kYXRhKSkge1xuICAgICAgICAgIHRhZ3MgPSByZXNwb25zZS5kYXRhO1xuICAgICAgICAgIGNvbnNvbGUubG9nKCfinIUg5LuOIGRhdGEg5pWw57uE6Kej5p6Q5Yiw5qCH562+OicsIHRhZ3MubGVuZ3RoLCAn5LiqJyk7XG4gICAgICAgIH1cbiAgICAgICAgLy8g5aSE55CG5bWM5aWX55qEQVBJ5ZON5bqU5qC85byPIChyZXNwb25zZS5kYXRhLmRhdGEubGlzdCkgLSDlpIfnlKjmoLzlvI9cbiAgICAgICAgZWxzZSBpZiAocmVzcG9uc2UuZGF0YS5kYXRhICYmIHJlc3BvbnNlLmRhdGEuZGF0YS5saXN0ICYmIEFycmF5LmlzQXJyYXkocmVzcG9uc2UuZGF0YS5kYXRhLmxpc3QpKSB7XG4gICAgICAgICAgdGFncyA9IHJlc3BvbnNlLmRhdGEuZGF0YS5saXN0O1xuICAgICAgICAgIGNvbnNvbGUubG9nKCfinIUg5LuOIGRhdGEuZGF0YS5saXN0IOino+aekOWIsOagh+etvjonLCB0YWdzLmxlbmd0aCwgJ+S4qicpO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8g6aqM6K+B5qCH562+5pWw5o2u5qC85byPXG4gICAgICAgIGNvbnNvbGUubG9nKCfwn5SNIOWOn+Wni+agh+etvuaVsOaNrjonLCB0YWdzKTtcbiAgICAgICAgY29uc29sZS5sb2coJ/CflI0g5qCH562+5pWw5o2u57G75Z6L5qOA5p+lOicpO1xuICAgICAgICB0YWdzLmZvckVhY2goKHRhZywgaW5kZXgpID0+IHtcbiAgICAgICAgICBjb25zb2xlLmxvZyhg5qCH562+JHtpbmRleH06YCwge1xuICAgICAgICAgICAgdGFnLFxuICAgICAgICAgICAgaGFzVGFnOiAhIXRhZyxcbiAgICAgICAgICAgIGlkVHlwZTogdHlwZW9mIHRhZz8uaWQsXG4gICAgICAgICAgICBuYW1lVHlwZTogdHlwZW9mIHRhZz8ubmFtZSxcbiAgICAgICAgICAgIG5hbWVWYWx1ZTogdGFnPy5uYW1lLFxuICAgICAgICAgICAgbmFtZU5vdEVtcHR5OiB0YWc/Lm5hbWU/LnRyaW0oKSAhPT0gJydcbiAgICAgICAgICB9KTtcbiAgICAgICAgfSk7XG5cbiAgICAgICAgY29uc3QgdmFsaWRUYWdzID0gdGFncy5maWx0ZXIodGFnID0+IHtcbiAgICAgICAgICBjb25zdCBpc1ZhbGlkID0gdGFnICYmXG4gICAgICAgICAgICB0eXBlb2YgdGFnLmlkID09PSAnbnVtYmVyJyAmJlxuICAgICAgICAgICAgdHlwZW9mIHRhZy5uYW1lID09PSAnc3RyaW5nJyAmJlxuICAgICAgICAgICAgdGFnLm5hbWUudHJpbSgpICE9PSAnJztcblxuICAgICAgICAgIGlmICghaXNWYWxpZCkge1xuICAgICAgICAgICAgY29uc29sZS5sb2coJ+KdjCDml6DmlYjmoIfnrb46JywgdGFnLCB7XG4gICAgICAgICAgICAgIGhhc1RhZzogISF0YWcsXG4gICAgICAgICAgICAgIGlkVHlwZTogdHlwZW9mIHRhZz8uaWQsXG4gICAgICAgICAgICAgIG5hbWVUeXBlOiB0eXBlb2YgdGFnPy5uYW1lLFxuICAgICAgICAgICAgICBuYW1lVmFsdWU6IHRhZz8ubmFtZVxuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgcmV0dXJuIGlzVmFsaWQ7XG4gICAgICAgIH0pO1xuXG4gICAgICAgIGNvbnNvbGUubG9nKCfinIUg5pyJ5pWI5qCH562+5pWw6YePOicsIHZhbGlkVGFncy5sZW5ndGgpO1xuICAgICAgICBjb25zb2xlLmxvZygn4pyFIOacieaViOagh+etvuivpuaDhTonLCB2YWxpZFRhZ3MpO1xuXG4gICAgICAgIGlmICh2YWxpZFRhZ3MubGVuZ3RoID4gMCkge1xuICAgICAgICAgIHNldENvdXJzZVRhZ3ModmFsaWRUYWdzKTtcbiAgICAgICAgICBjb25zb2xlLmxvZygn4pyFIOaIkOWKn+iuvue9ruecn+Wunuagh+etvuaVsOaNricpO1xuICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICBjb25zb2xlLndhcm4oJ+KaoO+4jyDmsqHmnInmnInmlYjnmoTmoIfnrb7mlbDmja4nKTtcbiAgICAgICAgfVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgY29uc29sZS53YXJuKCfimqDvuI8gQVBJ5ZON5bqU5qC85byP5LiN5q2j56GuOicsIHJlc3BvbnNlKTtcbiAgICAgIH1cblxuICAgICAgLy8g5aaC5p6c5rKh5pyJ55yf5a6e5pWw5o2u77yM6K6+572u56m65pWw57uEXG4gICAgICBjb25zb2xlLmxvZygn8J+TrSDmsqHmnInmoIfnrb7mlbDmja7vvIzorr7nva7nqbrmlbDnu4QnKTtcbiAgICAgIHNldENvdXJzZVRhZ3MoW10pO1xuXG4gICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgY29uc29sZS5lcnJvcign4p2MIOWKoOi9veivvueoi+agh+etvuWksei0pTonLCBlcnJvcik7XG4gICAgICBjb25zb2xlLmVycm9yKCfinYwg6ZSZ6K+v6K+m5oOFOicsIHtcbiAgICAgICAgbWVzc2FnZTogZXJyb3IubWVzc2FnZSxcbiAgICAgICAgc3RhdHVzOiBlcnJvci5yZXNwb25zZT8uc3RhdHVzLFxuICAgICAgICBzdGF0dXNUZXh0OiBlcnJvci5yZXNwb25zZT8uc3RhdHVzVGV4dCxcbiAgICAgICAgZGF0YTogZXJyb3IucmVzcG9uc2U/LmRhdGFcbiAgICAgIH0pO1xuXG4gICAgICAvLyDlj5HnlJ/plJnor6/ml7borr7nva7nqbrmlbDnu4RcbiAgICAgIHNldENvdXJzZVRhZ3MoW10pO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRUYWdzTG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIC8vIOa3u+WKoOaWsOivvueoi1xuICBjb25zdCBhZGROZXdDb3Vyc2UgPSAoKSA9PiB7XG4gICAgY29uc3QgbmV3Q291cnNlOiBDb3Vyc2VJdGVtID0ge1xuICAgICAgaWQ6IERhdGUubm93KCksXG4gICAgICBzZXJpZXNJZDogc2VyaWVzSWQsXG4gICAgICB0aXRsZTogYOesrCR7Y291cnNlTGlzdC5sZW5ndGggKyAxfeivviAtIOaWsOivvuaXtmAsXG4gICAgICBkZXNjcmlwdGlvbjogJycsXG4gICAgICBjb3ZlckltYWdlOiAnJyxcbiAgICAgIG9yZGVySW5kZXg6IGNvdXJzZUxpc3QubGVuZ3RoICsgMSxcbiAgICAgIHN0YXR1czogMCxcbiAgICAgIHN0YXR1c0xhYmVsOiAn6I2J56i/JyxcbiAgICAgIGhhc1ZpZGVvOiAwLFxuICAgICAgaGFzRG9jdW1lbnQ6IDAsXG4gICAgICBoYXNBdWRpbzogMCxcbiAgICAgIHZpZGVvRHVyYXRpb246IDAsXG4gICAgICB2aWRlb0R1cmF0aW9uTGFiZWw6ICcnLFxuICAgICAgdmlkZW9OYW1lOiAnJyxcbiAgICAgIGZpcnN0VGVhY2hpbmdUaXRsZTogJycsXG4gICAgICByZXNvdXJjZXNDb3VudDogMCxcbiAgICAgIGNyZWF0ZWRBdDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgICAgdXBkYXRlZEF0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICB9O1xuICAgIHNldENvdXJzZUxpc3QoWy4uLmNvdXJzZUxpc3QsIG5ld0NvdXJzZV0pO1xuICAgIC8vIOiHquWKqOmAieS4reaWsOa3u+WKoOeahOivvueoi1xuICAgIHNob3dDb3Vyc2VQYW5lbChuZXdDb3Vyc2UuaWQpO1xuICB9O1xuXG4gIC8vIOaYvuekuuWIoOmZpOehruiupOW8ueeql1xuICBjb25zdCBzaG93RGVsZXRlQ29uZmlybSA9IChpZDogbnVtYmVyKSA9PiB7XG4gICAgc2V0Q291cnNlVG9EZWxldGUoaWQpO1xuICAgIHNldERlbGV0ZUNvbmZpcm1WaXNpYmxlKHRydWUpO1xuICB9O1xuXG4gIC8vIOehruiupOWIoOmZpOivvueoi1xuICBjb25zdCBjb25maXJtRGVsZXRlQ291cnNlID0gYXN5bmMgKCkgPT4ge1xuICAgIGlmICghY291cnNlVG9EZWxldGUpIHJldHVybjtcblxuICAgIHRyeSB7XG4gICAgICBzZXRJc0RlbGV0aW5nKHRydWUpO1xuXG4gICAgICAvLyDosIPnlKjliKDpmaRBUElcbiAgICAgIGF3YWl0IGNvdXJzZU1hbmFnZW1lbnRBcGkuZGVsZXRlQ291cnNlKGNvdXJzZVRvRGVsZXRlKTtcblxuICAgICAgLy8g5LuO5YiX6KGo5Lit56e76Zmk6K++56iLXG4gICAgICBzZXRDb3Vyc2VMaXN0KGNvdXJzZUxpc3QuZmlsdGVyKGNvdXJzZSA9PiBjb3Vyc2UuaWQgIT09IGNvdXJzZVRvRGVsZXRlKSk7XG5cbiAgICAgIC8vIOWmguaenOWIoOmZpOeahOaYr+W9k+WJjemAieS4reeahOivvueoi++8jOa4heepuuWPs+S+p+mdouadv1xuICAgICAgaWYgKHNlbGVjdGVkQ291cnNlSWQgPT09IGNvdXJzZVRvRGVsZXRlKSB7XG4gICAgICAgIHNldFJpZ2h0UGFuZWxUeXBlKCdub25lJyk7XG4gICAgICAgIHNldFNlbGVjdGVkQ291cnNlSWQobnVsbCk7XG4gICAgICB9XG5cbiAgICAgIC8vIOWFs+mXreehruiupOW8ueeql1xuICAgICAgc2V0RGVsZXRlQ29uZmlybVZpc2libGUoZmFsc2UpO1xuICAgICAgc2V0Q291cnNlVG9EZWxldGUobnVsbCk7XG5cbiAgICAgIC8vIOaYvuekuuaIkOWKn+aPkOekulxuICAgICAgbm90aWZpY2F0aW9uLnN1Y2Nlc3MoJ+ivvueoi+W3suaIkOWKn+WIoOmZpCcpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfliKDpmaTor77nqIvlpLHotKU6JywgZXJyb3IpO1xuICAgICAgbm90aWZpY2F0aW9uLmVycm9yKCfliKDpmaTor77nqIvlpLHotKXvvIzor7fph43or5UnKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0SXNEZWxldGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIC8vIOWPlua2iOWIoOmZpFxuICBjb25zdCBjYW5jZWxEZWxldGUgPSAoKSA9PiB7XG4gICAgaWYgKGlzRGVsZXRpbmcpIHJldHVybjsgLy8g5q2j5Zyo5Yig6Zmk5pe25LiN5YWB6K645Y+W5raIXG4gICAgc2V0RGVsZXRlQ29uZmlybVZpc2libGUoZmFsc2UpO1xuICAgIHNldENvdXJzZVRvRGVsZXRlKG51bGwpO1xuICB9O1xuXG4gIC8vIOabtOaWsOivvueoi+agh+mimFxuICBjb25zdCB1cGRhdGVDb3Vyc2VUaXRsZSA9IChpZDogbnVtYmVyLCBuZXdUaXRsZTogc3RyaW5nKSA9PiB7XG4gICAgc2V0Q291cnNlTGlzdChjb3Vyc2VMaXN0Lm1hcChjb3Vyc2UgPT5cbiAgICAgIGNvdXJzZS5pZCA9PT0gaWQgPyB7IC4uLmNvdXJzZSwgdGl0bGU6IG5ld1RpdGxlIH0gOiBjb3Vyc2VcbiAgICApKTtcbiAgfTtcblxuICAvLyDlpITnkIbor77nqIvlsIHpnaLkuIrkvKBcbiAgY29uc3QgaGFuZGxlQ292ZXJVcGxvYWQgPSBhc3luYyAoZXZlbnQ6IFJlYWN0LkNoYW5nZUV2ZW50PEhUTUxJbnB1dEVsZW1lbnQ+KSA9PiB7XG4gICAgY29uc3QgZmlsZSA9IGV2ZW50LnRhcmdldC5maWxlcz8uWzBdO1xuICAgIGlmIChmaWxlKSB7XG4gICAgICAvLyDmo4Dmn6Xmlofku7bnsbvlnotcbiAgICAgIGNvbnN0IGFsbG93ZWRUeXBlcyA9IFsnaW1hZ2UvanBlZycsICdpbWFnZS9qcGcnLCAnaW1hZ2UvcG5nJywgJ2ltYWdlL2dpZiddO1xuICAgICAgaWYgKCFhbGxvd2VkVHlwZXMuaW5jbHVkZXMoZmlsZS50eXBlKSkge1xuICAgICAgICBub3RpZmljYXRpb24uZXJyb3IoJ+ivt+mAieaLqSBKUEfjgIFQTkcg5oiWIEdJRiDmoLzlvI/nmoTlm77niYfmlofku7YnKTtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuXG4gICAgICAvLyDmo4Dmn6Xmlofku7blpKflsI8gKDEwTUIpXG4gICAgICBpZiAoZmlsZS5zaXplID4gMTAgKiAxMDI0ICogMTAyNCkge1xuICAgICAgICBub3RpZmljYXRpb24uZXJyb3IoJ+aWh+S7tuWkp+Wwj+S4jeiDvei2hei/hyAxME1CJyk7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cblxuICAgICAgdHJ5IHtcbiAgICAgICAgY29uc29sZS5sb2coJ/Cfk6Qg5byA5aeL5LiK5Lyg6K++56iL5bCB6Z2iOicsIGZpbGUubmFtZSk7XG5cbiAgICAgICAgLy8g5re75Yqg5Yiw5LiK5Lyg5Lit55qE5paH5Lu25YiX6KGoXG4gICAgICAgIHNldFVwbG9hZGluZ0ZpbGVzKHByZXYgPT4gbmV3IFNldChwcmV2KS5hZGQoJ2NvdmVyJykpO1xuXG4gICAgICAgIC8vIOWFiOaYvuekuumihOiniOWbvueJh1xuICAgICAgICBjb25zdCBwcmV2aWV3VXJsID0gVVJMLmNyZWF0ZU9iamVjdFVSTChmaWxlKTtcbiAgICAgICAgc2V0Q291cnNlRGV0YWlsKHByZXYgPT4gKHsgLi4ucHJldiwgY292ZXJJbWFnZTogcHJldmlld1VybCB9KSk7XG5cbiAgICAgICAgLy8g5LiK5Lyg5YiwT1NTXG4gICAgICAgIGNvbnN0IGltYWdlVXJsID0gYXdhaXQgdXBsb2FkQXBpLnVwbG9hZFRvT3NzKGZpbGUpO1xuICAgICAgICBjb25zb2xlLmxvZygn4pyFIOivvueoi+WwgemdouS4iuS8oOaIkOWKnzonLCBpbWFnZVVybCk7XG5cbiAgICAgICAgLy8g5pu05paw6K++56iL6K+m5oOF5Lit55qE5bCB6Z2i5Li655yf5a6eVVJMXG4gICAgICAgIHNldENvdXJzZURldGFpbChwcmV2ID0+ICh7IC4uLnByZXYsIGNvdmVySW1hZ2U6IGltYWdlVXJsIH0pKTtcblxuICAgICAgICAvLyDlkIzml7bmm7TmlrDor77nqIvliJfooajkuK3nmoTlsIHpnaJcbiAgICAgICAgaWYgKHNlbGVjdGVkQ291cnNlSWQpIHtcbiAgICAgICAgICBzZXRDb3Vyc2VMaXN0KHByZXYgPT4gcHJldi5tYXAoY291cnNlID0+XG4gICAgICAgICAgICBjb3Vyc2UuaWQgPT09IHNlbGVjdGVkQ291cnNlSWQgPyB7IC4uLmNvdXJzZSwgY292ZXJJbWFnZTogaW1hZ2VVcmwgfSA6IGNvdXJzZVxuICAgICAgICAgICkpO1xuICAgICAgICB9XG5cbiAgICAgICAgYWxlcnQoJ+ivvueoi+WwgemdouS4iuS8oOaIkOWKn++8gScpO1xuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcign4p2MIOivvueoi+WwgemdouS4iuS8oOWksei0pTonLCBlcnJvcik7XG4gICAgICAgIG5vdGlmaWNhdGlvbi5lcnJvcign6K++56iL5bCB6Z2i5LiK5Lyg5aSx6LSl77yM6K+36YeN6K+VJyk7XG5cbiAgICAgICAgLy8g5LiK5Lyg5aSx6LSl5pe25riF6Zmk6aKE6KeI5Zu+54mHXG4gICAgICAgIHNldENvdXJzZURldGFpbChwcmV2ID0+ICh7IC4uLnByZXYsIGNvdmVySW1hZ2U6ICcnIH0pKTtcbiAgICAgIH0gZmluYWxseSB7XG4gICAgICAgIC8vIOS7juS4iuS8oOS4reeahOaWh+S7tuWIl+ihqOenu+mZpFxuICAgICAgICBzZXRVcGxvYWRpbmdGaWxlcyhwcmV2ID0+IHtcbiAgICAgICAgICBjb25zdCBuZXdTZXQgPSBuZXcgU2V0KHByZXYpO1xuICAgICAgICAgIG5ld1NldC5kZWxldGUoJ2NvdmVyJyk7XG4gICAgICAgICAgcmV0dXJuIG5ld1NldDtcbiAgICAgICAgfSk7XG4gICAgICB9XG4gICAgfVxuICB9O1xuXG4gIC8vIOWkhOeQhuinhumikeS4iuS8oFxuICBjb25zdCBoYW5kbGVWaWRlb1VwbG9hZCA9IGFzeW5jIChldmVudDogUmVhY3QuQ2hhbmdlRXZlbnQ8SFRNTElucHV0RWxlbWVudD4pID0+IHtcbiAgICBjb25zdCBmaWxlID0gZXZlbnQudGFyZ2V0LmZpbGVzPy5bMF07XG4gICAgaWYgKGZpbGUpIHtcbiAgICAgIC8vIOajgOafpeaWh+S7tuexu+Wei1xuICAgICAgY29uc3QgYWxsb3dlZFR5cGVzID0gWyd2aWRlby9tcDQnLCAndmlkZW8vYXZpJywgJ3ZpZGVvL21vdicsICd2aWRlby93bXYnLCAndmlkZW8vZmx2J107XG4gICAgICBpZiAoIWFsbG93ZWRUeXBlcy5pbmNsdWRlcyhmaWxlLnR5cGUpKSB7XG4gICAgICAgIGFsZXJ0KCfor7fpgInmi6kgTVA044CBQVZJ44CBTU9W44CBV01WIOaIliBGTFYg5qC85byP55qE6KeG6aKR5paH5Lu2Jyk7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cblxuICAgICAgLy8g5qOA5p+l5paH5Lu25aSn5bCPICgxMDBNQilcbiAgICAgIGlmIChmaWxlLnNpemUgPiAxMDAgKiAxMDI0ICogMTAyNCkge1xuICAgICAgICBhbGVydCgn6KeG6aKR5paH5Lu25aSn5bCP5LiN6IO96LaF6L+HIDEwME1CJyk7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cblxuICAgICAgdHJ5IHtcbiAgICAgICAgY29uc29sZS5sb2coJ/Cfk6Qg5byA5aeL5LiK5Lyg6K++56iL6KeG6aKROicsIGZpbGUubmFtZSk7XG5cbiAgICAgICAgLy8g5re75Yqg5Yiw5LiK5Lyg5Lit55qE5paH5Lu25YiX6KGoXG4gICAgICAgIHNldFVwbG9hZGluZ0ZpbGVzKHByZXYgPT4gbmV3IFNldChwcmV2KS5hZGQoJ3ZpZGVvJykpO1xuXG4gICAgICAgIC8vIOWFiOaYvuekuumihOiniOinhumikVxuICAgICAgICBjb25zdCBwcmV2aWV3VXJsID0gVVJMLmNyZWF0ZU9iamVjdFVSTChmaWxlKTtcbiAgICAgICAgc2V0Q291cnNlRGV0YWlsKHByZXYgPT4gKHtcbiAgICAgICAgICAuLi5wcmV2LFxuICAgICAgICAgIGNvbnRlbnRDb25maWc6IHtcbiAgICAgICAgICAgIC4uLnByZXYuY29udGVudENvbmZpZyxcbiAgICAgICAgICAgIHZpZGVvOiB7XG4gICAgICAgICAgICAgIHVybDogcHJldmlld1VybCxcbiAgICAgICAgICAgICAgbmFtZTogZmlsZS5uYW1lXG4gICAgICAgICAgICB9XG4gICAgICAgICAgfVxuICAgICAgICB9KSk7XG5cbiAgICAgICAgLy8g5LiK5Lyg5YiwT1NTXG4gICAgICAgIGNvbnN0IHZpZGVvVXJsID0gYXdhaXQgdXBsb2FkQXBpLnVwbG9hZFRvT3NzKGZpbGUpO1xuICAgICAgICBjb25zb2xlLmxvZygn4pyFIOivvueoi+inhumikeS4iuS8oOaIkOWKnzonLCB2aWRlb1VybCk7XG5cbiAgICAgICAgLy8g5pu05paw6K++56iL6K+m5oOF5Lit55qE6KeG6aKR5L+h5oGv5Li655yf5a6eVVJMXG4gICAgICAgIHNldENvdXJzZURldGFpbChwcmV2ID0+ICh7XG4gICAgICAgICAgLi4ucHJldixcbiAgICAgICAgICBjb250ZW50Q29uZmlnOiB7XG4gICAgICAgICAgICAuLi5wcmV2LmNvbnRlbnRDb25maWcsXG4gICAgICAgICAgICB2aWRlbzoge1xuICAgICAgICAgICAgICB1cmw6IHZpZGVvVXJsLFxuICAgICAgICAgICAgICBuYW1lOiBmaWxlLm5hbWVcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG4gICAgICAgIH0pKTtcblxuICAgICAgICAvLyDlkIzml7bmm7TmlrDor77nqIvliJfooajkuK3nmoTop4bpopHkv6Hmga9cbiAgICAgICAgaWYgKHNlbGVjdGVkQ291cnNlSWQpIHtcbiAgICAgICAgICBzZXRDb3Vyc2VMaXN0KHByZXYgPT4gcHJldi5tYXAoY291cnNlID0+XG4gICAgICAgICAgICBjb3Vyc2UuaWQgPT09IHNlbGVjdGVkQ291cnNlSWQgPyB7XG4gICAgICAgICAgICAgIC4uLmNvdXJzZSxcbiAgICAgICAgICAgICAgY29udGVudENvbmZpZzoge1xuICAgICAgICAgICAgICAgIC4uLmNvdXJzZS5jb250ZW50Q29uZmlnLFxuICAgICAgICAgICAgICAgIHZpZGVvOiB7XG4gICAgICAgICAgICAgICAgICB1cmw6IHZpZGVvVXJsLFxuICAgICAgICAgICAgICAgICAgbmFtZTogZmlsZS5uYW1lXG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9IDogY291cnNlXG4gICAgICAgICAgKSk7XG4gICAgICAgIH1cblxuICAgICAgICBhbGVydCgn6K++56iL6KeG6aKR5LiK5Lyg5oiQ5Yqf77yBJyk7XG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCfinYwg6K++56iL6KeG6aKR5LiK5Lyg5aSx6LSlOicsIGVycm9yKTtcbiAgICAgICAgYWxlcnQoJ+ivvueoi+inhumikeS4iuS8oOWksei0pe+8jOivt+mHjeivlScpO1xuXG4gICAgICAgIC8vIOS4iuS8oOWksei0peaXtua4hemZpOinhumikeS/oeaBr1xuICAgICAgICBzZXRDb3Vyc2VEZXRhaWwocHJldiA9PiAoe1xuICAgICAgICAgIC4uLnByZXYsXG4gICAgICAgICAgY29udGVudENvbmZpZzoge1xuICAgICAgICAgICAgLi4ucHJldi5jb250ZW50Q29uZmlnLFxuICAgICAgICAgICAgdmlkZW86IHtcbiAgICAgICAgICAgICAgdXJsOiAnJyxcbiAgICAgICAgICAgICAgbmFtZTogJydcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG4gICAgICAgIH0pKTtcbiAgICAgIH0gZmluYWxseSB7XG4gICAgICAgIC8vIOS7juS4iuS8oOS4reeahOaWh+S7tuWIl+ihqOenu+mZpFxuICAgICAgICBzZXRVcGxvYWRpbmdGaWxlcyhwcmV2ID0+IHtcbiAgICAgICAgICBjb25zdCBuZXdTZXQgPSBuZXcgU2V0KHByZXYpO1xuICAgICAgICAgIG5ld1NldC5kZWxldGUoJ3ZpZGVvJyk7XG4gICAgICAgICAgcmV0dXJuIG5ld1NldDtcbiAgICAgICAgfSk7XG4gICAgICB9XG4gICAgfVxuICB9O1xuXG4gIC8vIOinpuWPkeinhumikeaWh+S7tumAieaLqVxuICBjb25zdCB0cmlnZ2VyVmlkZW9VcGxvYWQgPSAoKSA9PiB7XG4gICAgY29uc3QgaW5wdXQgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCdpbnB1dCcpO1xuICAgIGlucHV0LnR5cGUgPSAnZmlsZSc7XG4gICAgaW5wdXQuYWNjZXB0ID0gJ3ZpZGVvL21wNCx2aWRlby9hdmksdmlkZW8vbW92LHZpZGVvL3dtdix2aWRlby9mbHYnO1xuICAgIGlucHV0Lm9uY2hhbmdlID0gKGUpID0+IGhhbmRsZVZpZGVvVXBsb2FkKGUgYXMgYW55KTtcbiAgICBpbnB1dC5jbGljaygpO1xuICB9O1xuXG4gIC8vIOWkhOeQhumZhOS7tuS4iuS8oFxuICBjb25zdCBoYW5kbGVBdHRhY2htZW50VXBsb2FkID0gYXN5bmMgKGV2ZW50OiBSZWFjdC5DaGFuZ2VFdmVudDxIVE1MSW5wdXRFbGVtZW50PikgPT4ge1xuICAgIGNvbnN0IGZpbGUgPSBldmVudC50YXJnZXQuZmlsZXM/LlswXTtcbiAgICBpZiAoZmlsZSkge1xuICAgICAgLy8g5qOA5p+l5paH5Lu257G75Z6LXG4gICAgICBjb25zdCBhbGxvd2VkVHlwZXMgPSBbXG4gICAgICAgICdhcHBsaWNhdGlvbi9wZGYnLFxuICAgICAgICAnYXBwbGljYXRpb24vbXN3b3JkJyxcbiAgICAgICAgJ2FwcGxpY2F0aW9uL3ZuZC5vcGVueG1sZm9ybWF0cy1vZmZpY2Vkb2N1bWVudC53b3JkcHJvY2Vzc2luZ21sLmRvY3VtZW50JyxcbiAgICAgICAgJ2FwcGxpY2F0aW9uL3ZuZC5tcy1leGNlbCcsXG4gICAgICAgICdhcHBsaWNhdGlvbi92bmQub3BlbnhtbGZvcm1hdHMtb2ZmaWNlZG9jdW1lbnQuc3ByZWFkc2hlZXRtbC5zaGVldCcsXG4gICAgICAgICdhcHBsaWNhdGlvbi92bmQubXMtcG93ZXJwb2ludCcsXG4gICAgICAgICdhcHBsaWNhdGlvbi92bmQub3BlbnhtbGZvcm1hdHMtb2ZmaWNlZG9jdW1lbnQucHJlc2VudGF0aW9ubWwucHJlc2VudGF0aW9uJyxcbiAgICAgICAgJ3RleHQvcGxhaW4nLFxuICAgICAgICAnaW1hZ2UvanBlZycsXG4gICAgICAgICdpbWFnZS9wbmcnLFxuICAgICAgICAnaW1hZ2UvZ2lmJ1xuICAgICAgXTtcblxuICAgICAgaWYgKCFhbGxvd2VkVHlwZXMuaW5jbHVkZXMoZmlsZS50eXBlKSkge1xuICAgICAgICBhbGVydCgn6K+36YCJ5oup5pSv5oyB55qE5paH5Lu25qC85byP77yaUERG44CBRE9D44CBRE9DWOOAgVhMU+OAgVhMU1jjgIFQUFTjgIFQUFRY44CBVFhU44CBSlBH44CBUE5H44CBR0lGJyk7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cblxuICAgICAgLy8g5qOA5p+l5paH5Lu25aSn5bCPICgxME1CKVxuICAgICAgaWYgKGZpbGUuc2l6ZSA+IDEwICogMTAyNCAqIDEwMjQpIHtcbiAgICAgICAgYWxlcnQoJ+aWh+S7tuWkp+Wwj+S4jeiDvei2hei/hyAxME1CJyk7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cblxuICAgICAgdHJ5IHtcbiAgICAgICAgY29uc29sZS5sb2coJ/Cfk6Qg5byA5aeL5LiK5Lyg6K++56iL6ZmE5Lu2OicsIGZpbGUubmFtZSk7XG5cbiAgICAgICAgLy8g5re75Yqg5Yiw5LiK5Lyg5Lit55qE5paH5Lu25YiX6KGoXG4gICAgICAgIHNldFVwbG9hZGluZ0ZpbGVzKHByZXYgPT4gbmV3IFNldChwcmV2KS5hZGQoJ2RvY3VtZW50JykpO1xuXG4gICAgICAgIC8vIOS4iuS8oOWIsE9TU1xuICAgICAgICBjb25zdCBkb2N1bWVudFVybCA9IGF3YWl0IHVwbG9hZEFwaS51cGxvYWRUb09zcyhmaWxlKTtcbiAgICAgICAgY29uc29sZS5sb2coJ+KchSDor77nqIvpmYTku7bkuIrkvKDmiJDlip86JywgZG9jdW1lbnRVcmwpO1xuXG4gICAgICAgIC8vIOabtOaWsOivvueoi+ivpuaDheS4reeahOmZhOS7tuS/oeaBr1xuICAgICAgICBzZXRDb3Vyc2VEZXRhaWwocHJldiA9PiAoe1xuICAgICAgICAgIC4uLnByZXYsXG4gICAgICAgICAgY29udGVudENvbmZpZzoge1xuICAgICAgICAgICAgLi4ucHJldi5jb250ZW50Q29uZmlnLFxuICAgICAgICAgICAgaGFzRG9jdW1lbnQ6IDEsXG4gICAgICAgICAgICBkb2N1bWVudDoge1xuICAgICAgICAgICAgICB1cmw6IGRvY3VtZW50VXJsLFxuICAgICAgICAgICAgICBuYW1lOiBmaWxlLm5hbWVcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG4gICAgICAgIH0pKTtcblxuICAgICAgICBhbGVydCgn6K++56iL6ZmE5Lu25LiK5Lyg5oiQ5Yqf77yBJyk7XG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCfinYwg6K++56iL6ZmE5Lu25LiK5Lyg5aSx6LSlOicsIGVycm9yKTtcbiAgICAgICAgYWxlcnQoJ+ivvueoi+mZhOS7tuS4iuS8oOWksei0pe+8jOivt+mHjeivlScpO1xuICAgICAgfSBmaW5hbGx5IHtcbiAgICAgICAgLy8g5LuO5LiK5Lyg5Lit55qE5paH5Lu25YiX6KGo56e76ZmkXG4gICAgICAgIHNldFVwbG9hZGluZ0ZpbGVzKHByZXYgPT4ge1xuICAgICAgICAgIGNvbnN0IG5ld1NldCA9IG5ldyBTZXQocHJldik7XG4gICAgICAgICAgbmV3U2V0LmRlbGV0ZSgnZG9jdW1lbnQnKTtcbiAgICAgICAgICByZXR1cm4gbmV3U2V0O1xuICAgICAgICB9KTtcbiAgICAgIH1cbiAgICB9XG4gIH07XG5cbiAgLy8g6Kem5Y+R6ZmE5Lu25paH5Lu26YCJ5oupXG4gIGNvbnN0IHRyaWdnZXJBdHRhY2htZW50VXBsb2FkID0gKCkgPT4ge1xuICAgIGNvbnN0IGlucHV0ID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnaW5wdXQnKTtcbiAgICBpbnB1dC50eXBlID0gJ2ZpbGUnO1xuICAgIGlucHV0LmFjY2VwdCA9ICcucGRmLC5kb2MsLmRvY3gsLnhscywueGxzeCwucHB0LC5wcHR4LC50eHQsLmpwZywucG5nLC5naWYnO1xuICAgIGlucHV0Lm9uY2hhbmdlID0gKGUpID0+IGhhbmRsZUF0dGFjaG1lbnRVcGxvYWQoZSBhcyBhbnkpO1xuICAgIGlucHV0LmNsaWNrKCk7XG4gIH07XG5cbiAgLy8g5aSE55CG5pWZ5a2m6ZmE5Lu25LiK5LygXG4gIGNvbnN0IGhhbmRsZVRlYWNoaW5nTWF0ZXJpYWxVcGxvYWQgPSBhc3luYyAoZXZlbnQ6IFJlYWN0LkNoYW5nZUV2ZW50PEhUTUxJbnB1dEVsZW1lbnQ+KSA9PiB7XG4gICAgY29uc3QgZmlsZSA9IGV2ZW50LnRhcmdldC5maWxlcz8uWzBdO1xuICAgIGlmIChmaWxlKSB7XG4gICAgICAvLyDmo4Dmn6Xmlofku7bnsbvlnotcbiAgICAgIGNvbnN0IGFsbG93ZWRUeXBlcyA9IFtcbiAgICAgICAgJ2FwcGxpY2F0aW9uL3BkZicsXG4gICAgICAgICdhcHBsaWNhdGlvbi9tc3dvcmQnLFxuICAgICAgICAnYXBwbGljYXRpb24vdm5kLm9wZW54bWxmb3JtYXRzLW9mZmljZWRvY3VtZW50LndvcmRwcm9jZXNzaW5nbWwuZG9jdW1lbnQnLFxuICAgICAgICAnYXBwbGljYXRpb24vdm5kLm1zLWV4Y2VsJyxcbiAgICAgICAgJ2FwcGxpY2F0aW9uL3ZuZC5vcGVueG1sZm9ybWF0cy1vZmZpY2Vkb2N1bWVudC5zcHJlYWRzaGVldG1sLnNoZWV0JyxcbiAgICAgICAgJ2FwcGxpY2F0aW9uL3ZuZC5tcy1wb3dlcnBvaW50JyxcbiAgICAgICAgJ2FwcGxpY2F0aW9uL3ZuZC5vcGVueG1sZm9ybWF0cy1vZmZpY2Vkb2N1bWVudC5wcmVzZW50YXRpb25tbC5wcmVzZW50YXRpb24nLFxuICAgICAgICAndGV4dC9wbGFpbicsXG4gICAgICAgICdpbWFnZS9qcGVnJyxcbiAgICAgICAgJ2ltYWdlL3BuZycsXG4gICAgICAgICdpbWFnZS9naWYnXG4gICAgICBdO1xuXG4gICAgICBpZiAoIWFsbG93ZWRUeXBlcy5pbmNsdWRlcyhmaWxlLnR5cGUpKSB7XG4gICAgICAgIGFsZXJ0KCfor7fpgInmi6nmlK/mjIHnmoTmlofku7bmoLzlvI/vvJpQREbjgIFET0PjgIFET0NY44CBWExT44CBWExTWOOAgVBQVOOAgVBQVFjjgIFUWFTjgIFKUEfjgIFQTkfjgIFHSUYnKTtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuXG4gICAgICAvLyDmo4Dmn6Xmlofku7blpKflsI8gKDEwTUIpXG4gICAgICBpZiAoZmlsZS5zaXplID4gMTAgKiAxMDI0ICogMTAyNCkge1xuICAgICAgICBhbGVydCgn5paH5Lu25aSn5bCP5LiN6IO96LaF6L+HIDEwTUInKTtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuXG4gICAgICB0cnkge1xuICAgICAgICBjb25zb2xlLmxvZygn8J+TpCDlvIDlp4vkuIrkvKDmlZnlrabmnZDmlpk6JywgZmlsZS5uYW1lKTtcblxuICAgICAgICAvLyDmt7vliqDliLDkuIrkvKDkuK3nmoTmlofku7bliJfooahcbiAgICAgICAgc2V0VXBsb2FkaW5nRmlsZXMocHJldiA9PiBuZXcgU2V0KHByZXYpLmFkZChgdGVhY2hpbmctJHtEYXRlLm5vdygpfWApKTtcblxuICAgICAgICAvLyDkuIrkvKDliLBPU1NcbiAgICAgICAgY29uc3QgbWF0ZXJpYWxVcmwgPSBhd2FpdCB1cGxvYWRBcGkudXBsb2FkVG9Pc3MoZmlsZSk7XG4gICAgICAgIGNvbnNvbGUubG9nKCfinIUg5pWZ5a2m5p2Q5paZ5LiK5Lyg5oiQ5YqfOicsIG1hdGVyaWFsVXJsKTtcblxuICAgICAgICAvLyDmt7vliqDliLDmlZnlrabpmYTku7bliJfooahcbiAgICAgICAgY29uc3QgbmV3TWF0ZXJpYWwgPSB7XG4gICAgICAgICAgdHlwZTogZmlsZS50eXBlLFxuICAgICAgICAgIG5hbWU6IGZpbGUubmFtZSxcbiAgICAgICAgICB1cmw6IG1hdGVyaWFsVXJsXG4gICAgICAgIH07XG5cbiAgICAgICAgc2V0Q291cnNlRGV0YWlsKHByZXYgPT4gKHtcbiAgICAgICAgICAuLi5wcmV2LFxuICAgICAgICAgIHRlYWNoaW5nTWF0ZXJpYWxzOiBbLi4ucHJldi50ZWFjaGluZ01hdGVyaWFscywgbmV3TWF0ZXJpYWxdXG4gICAgICAgIH0pKTtcblxuICAgICAgICBhbGVydCgn5pWZ5a2m5p2Q5paZ5LiK5Lyg5oiQ5Yqf77yBJyk7XG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCfinYwg5pWZ5a2m5p2Q5paZ5LiK5Lyg5aSx6LSlOicsIGVycm9yKTtcbiAgICAgICAgYWxlcnQoJ+aVmeWtpuadkOaWmeS4iuS8oOWksei0pe+8jOivt+mHjeivlScpO1xuICAgICAgfSBmaW5hbGx5IHtcbiAgICAgICAgLy8g5LuO5LiK5Lyg5Lit55qE5paH5Lu25YiX6KGo56e76ZmkXG4gICAgICAgIHNldFVwbG9hZGluZ0ZpbGVzKHByZXYgPT4ge1xuICAgICAgICAgIGNvbnN0IG5ld1NldCA9IG5ldyBTZXQocHJldik7XG4gICAgICAgICAgLy8g56e76Zmk5omA5pyJdGVhY2hpbmct5byA5aS055qE6aG555uuXG4gICAgICAgICAgQXJyYXkuZnJvbShuZXdTZXQpLmZvckVhY2goaXRlbSA9PiB7XG4gICAgICAgICAgICBpZiAoaXRlbS5zdGFydHNXaXRoKCd0ZWFjaGluZy0nKSkge1xuICAgICAgICAgICAgICBuZXdTZXQuZGVsZXRlKGl0ZW0pO1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH0pO1xuICAgICAgICAgIHJldHVybiBuZXdTZXQ7XG4gICAgICAgIH0pO1xuICAgICAgfVxuICAgIH1cbiAgfTtcblxuICAvLyDop6blj5HmlZnlrabpmYTku7bmlofku7bpgInmi6lcbiAgY29uc3QgdHJpZ2dlclRlYWNoaW5nTWF0ZXJpYWxVcGxvYWQgPSAoKSA9PiB7XG4gICAgY29uc3QgaW5wdXQgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCdpbnB1dCcpO1xuICAgIGlucHV0LnR5cGUgPSAnZmlsZSc7XG4gICAgaW5wdXQuYWNjZXB0ID0gJy5wZGYsLmRvYywuZG9jeCwueGxzLC54bHN4LC5wcHQsLnBwdHgsLnR4dCwuanBnLC5wbmcsLmdpZic7XG4gICAgaW5wdXQub25jaGFuZ2UgPSAoZSkgPT4gaGFuZGxlVGVhY2hpbmdNYXRlcmlhbFVwbG9hZChlIGFzIGFueSk7XG4gICAgaW5wdXQuY2xpY2soKTtcbiAgfTtcblxuICAvLyDliKDpmaTmlZnlrabpmYTku7ZcbiAgY29uc3QgcmVtb3ZlVGVhY2hpbmdNYXRlcmlhbCA9IChpbmRleDogbnVtYmVyKSA9PiB7XG4gICAgc2V0Q291cnNlRGV0YWlsKHByZXYgPT4gKHtcbiAgICAgIC4uLnByZXYsXG4gICAgICB0ZWFjaGluZ01hdGVyaWFsczogcHJldi50ZWFjaGluZ01hdGVyaWFscy5maWx0ZXIoKF8sIGkpID0+IGkgIT09IGluZGV4KVxuICAgIH0pKTtcbiAgfTtcblxuICAvLyDor77nqIvlhoXlrrnnrqHnkIblh73mlbBcbiAgY29uc3QgYWRkVGVhY2hpbmdJbmZvSXRlbSA9ICgpID0+IHtcbiAgICBzZXRDb3Vyc2VEZXRhaWwocHJldiA9PiAoe1xuICAgICAgLi4ucHJldixcbiAgICAgIHRlYWNoaW5nSW5mbzogWy4uLnByZXYudGVhY2hpbmdJbmZvLCB7IHRpdGxlOiAnJywgY29udGVudDogJycgfV1cbiAgICB9KSk7XG4gIH07XG5cbiAgY29uc3QgcmVtb3ZlVGVhY2hpbmdJbmZvSXRlbSA9IChpbmRleDogbnVtYmVyKSA9PiB7XG4gICAgc2V0Q291cnNlRGV0YWlsKHByZXYgPT4gKHtcbiAgICAgIC4uLnByZXYsXG4gICAgICB0ZWFjaGluZ0luZm86IHByZXYudGVhY2hpbmdJbmZvLmZpbHRlcigoXywgaSkgPT4gaSAhPT0gaW5kZXgpXG4gICAgfSkpO1xuICB9O1xuXG4gIGNvbnN0IHVwZGF0ZVRlYWNoaW5nSW5mb1RpdGxlID0gKGluZGV4OiBudW1iZXIsIHRpdGxlOiBzdHJpbmcpID0+IHtcbiAgICBzZXRDb3Vyc2VEZXRhaWwocHJldiA9PiB7XG4gICAgICBjb25zdCBuZXdUZWFjaGluZ0luZm8gPSBbLi4ucHJldi50ZWFjaGluZ0luZm9dO1xuICAgICAgbmV3VGVhY2hpbmdJbmZvW2luZGV4XSA9IHsgLi4ubmV3VGVhY2hpbmdJbmZvW2luZGV4XSwgdGl0bGUgfTtcbiAgICAgIHJldHVybiB7IC4uLnByZXYsIHRlYWNoaW5nSW5mbzogbmV3VGVhY2hpbmdJbmZvIH07XG4gICAgfSk7XG4gIH07XG5cbiAgY29uc3QgdXBkYXRlVGVhY2hpbmdJbmZvQ29udGVudCA9IChpbmRleDogbnVtYmVyLCBjb250ZW50OiBzdHJpbmcpID0+IHtcbiAgICBzZXRDb3Vyc2VEZXRhaWwocHJldiA9PiB7XG4gICAgICBjb25zdCBuZXdUZWFjaGluZ0luZm8gPSBbLi4ucHJldi50ZWFjaGluZ0luZm9dO1xuICAgICAgbmV3VGVhY2hpbmdJbmZvW2luZGV4XSA9IHsgLi4ubmV3VGVhY2hpbmdJbmZvW2luZGV4XSwgY29udGVudCB9O1xuICAgICAgcmV0dXJuIHsgLi4ucHJldiwgdGVhY2hpbmdJbmZvOiBuZXdUZWFjaGluZ0luZm8gfTtcbiAgICB9KTtcbiAgfTtcblxuICAvLyDlsIZVSeagvOW8j+eahHRlYWNoaW5nSW5mb+i9rOaNouS4ukFQSeagvOW8j1xuICBjb25zdCBjb252ZXJ0VGVhY2hpbmdJbmZvRm9yQVBJID0gKHRlYWNoaW5nSW5mbzogeyB0aXRsZTogc3RyaW5nOyBjb250ZW50OiBzdHJpbmcgfVtdKSA9PiB7XG4gICAgcmV0dXJuIHRlYWNoaW5nSW5mby5tYXAoaW5mbyA9PiAoe1xuICAgICAgdGl0bGU6IGluZm8udGl0bGUsXG4gICAgICBjb250ZW50OiBpbmZvLmNvbnRlbnQuc3BsaXQoJ1xcbicpLmZpbHRlcihsaW5lID0+IGxpbmUudHJpbSgpKSAvLyDmjInmjaLooYzliIblibLlubbov4fmu6TnqbrooYxcbiAgICB9KSk7XG4gIH07XG5cbiAgLy8g5qOA5p+l5piv5ZCm5pyJ5pyq5LiK5Lyg5a6M5oiQ55qE5paH5Lu277yIYmxvYiBVUkzvvIlcbiAgY29uc3QgY2hlY2tGb3JCbG9iVXJscyA9IChjb3Vyc2U6IENvdXJzZUl0ZW0pID0+IHtcbiAgICBjb25zdCBpc3N1ZXMgPSBbXTtcblxuICAgIC8vIOajgOafpeWwgemdouWbvueJh1xuICAgIGlmIChjb3Vyc2UuY292ZXJJbWFnZSAmJiBjb3Vyc2UuY292ZXJJbWFnZS5zdGFydHNXaXRoKCdibG9iOicpKSB7XG4gICAgICBpc3N1ZXMucHVzaCgn6K++56iL5bCB6Z2i5Zu+54mHJyk7XG4gICAgfVxuXG4gICAgLy8g5qOA5p+l6KeG6aKR5paH5Lu2XG4gICAgaWYgKGNvdXJzZS5jb250ZW50Q29uZmlnPy52aWRlbz8udXJsICYmIGNvdXJzZS5jb250ZW50Q29uZmlnLnZpZGVvLnVybC5zdGFydHNXaXRoKCdibG9iOicpKSB7XG4gICAgICBpc3N1ZXMucHVzaCgn6K++56iL6KeG6aKRJyk7XG4gICAgfVxuXG4gICAgLy8g5qOA5p+l5paH5qGj6ZmE5Lu2XG4gICAgaWYgKGNvdXJzZS5jb250ZW50Q29uZmlnPy5kb2N1bWVudD8udXJsICYmIGNvdXJzZS5jb250ZW50Q29uZmlnLmRvY3VtZW50LnVybC5zdGFydHNXaXRoKCdibG9iOicpKSB7XG4gICAgICBpc3N1ZXMucHVzaCgn6K++56iL5paH5qGjJyk7XG4gICAgfVxuXG4gICAgLy8g5qOA5p+l5pWZ5a2m5p2Q5paZXG4gICAgaWYgKGNvdXJzZS5hZGRpdGlvbmFsUmVzb3VyY2VzKSB7XG4gICAgICBjb3Vyc2UuYWRkaXRpb25hbFJlc291cmNlcy5mb3JFYWNoKChyZXNvdXJjZSwgaW5kZXgpID0+IHtcbiAgICAgICAgaWYgKHJlc291cmNlLnVybCAmJiByZXNvdXJjZS51cmwuc3RhcnRzV2l0aCgnYmxvYjonKSkge1xuICAgICAgICAgIGlzc3Vlcy5wdXNoKGDmlZnlrabmnZDmlpkke2luZGV4ICsgMX1gKTtcbiAgICAgICAgfVxuICAgICAgfSk7XG4gICAgfVxuXG4gICAgcmV0dXJuIGlzc3VlcztcbiAgfTtcblxuICAvLyDkv53lrZjljZXkuKror77nqIvliLDlkI7nq69cbiAgY29uc3Qgc2F2ZUNvdXJzZSA9IGFzeW5jIChjb3Vyc2U6IENvdXJzZUl0ZW0pID0+IHtcbiAgICB0cnkge1xuICAgICAgLy8g6aqM6K+B5b+F6KaB55qE5pWw5o2uXG4gICAgICBpZiAoIWNvdXJzZS50aXRsZSB8fCBjb3Vyc2UudGl0bGUudHJpbSgpID09PSAnJykge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ+ivvueoi+agh+mimOS4jeiDveS4uuepuicpO1xuICAgICAgfVxuXG4gICAgICAvLyDmo4Dmn6XmmK/lkKbmnInmnKrkuIrkvKDlrozmiJDnmoTmlofku7ZcbiAgICAgIGNvbnN0IGJsb2JJc3N1ZXMgPSBjaGVja0ZvckJsb2JVcmxzKGNvdXJzZSk7XG4gICAgICBpZiAoYmxvYklzc3Vlcy5sZW5ndGggPiAwKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihg5Lul5LiL5paH5Lu25bCa5pyq5LiK5Lyg5a6M5oiQ77yM6K+3562J5b6F5LiK5Lyg5a6M5oiQ5ZCO5YaN5L+d5a2Y77yaJHtibG9iSXNzdWVzLmpvaW4oJ+OAgScpfWApO1xuICAgICAgfVxuXG4gICAgICAvLyDlh4blpIfor77nqIvmlbDmja5cbiAgICAgIGNvbnN0IGNvdXJzZURhdGEgPSB7XG4gICAgICAgIHNlcmllc0lkOiBzZXJpZXNJZCxcbiAgICAgICAgdGl0bGU6IGNvdXJzZS50aXRsZSxcbiAgICAgICAgZGVzY3JpcHRpb246IGNvdXJzZS5kZXNjcmlwdGlvbiB8fCAnJyxcbiAgICAgICAgY292ZXJJbWFnZTogY291cnNlLmNvdmVySW1hZ2UgfHwgJycsXG4gICAgICAgIGhhc1ZpZGVvOiBjb3Vyc2UuY29udGVudENvbmZpZz8udmlkZW8/LnVybCA/IDEgOiAwLFxuICAgICAgICBoYXNEb2N1bWVudDogY291cnNlLmNvbnRlbnRDb25maWc/LmRvY3VtZW50Py51cmwgPyAxIDogMCxcbiAgICAgICAgaGFzQXVkaW86IDAsXG4gICAgICAgIHZpZGVvRHVyYXRpb246IDAsXG4gICAgICAgIGNvbnRlbnRDb25maWc6IGNvdXJzZS5jb250ZW50Q29uZmlnIHx8IHt9LFxuICAgICAgICB0ZWFjaGluZ0luZm86IGNvbnZlcnRUZWFjaGluZ0luZm9Gb3JBUEkoY291cnNlLnRlYWNoaW5nSW5mbyB8fCBbXSksXG4gICAgICAgIGFkZGl0aW9uYWxSZXNvdXJjZXM6IGNvdXJzZS5hZGRpdGlvbmFsUmVzb3VyY2VzIHx8IFtdLFxuICAgICAgICBvcmRlckluZGV4OiBjb3Vyc2Uub3JkZXJJbmRleCB8fCAxXG4gICAgICB9O1xuXG4gICAgICBjb25zb2xlLmxvZygn8J+SviDlh4blpIfkv53lrZjor77nqIvmlbDmja46JywgY291cnNlRGF0YSk7XG5cbiAgICAgIGxldCByZXN1bHQ6IGFueTtcblxuICAgICAgLy8g5Yik5pat5piv5paw6K++56iL6L+Y5piv5pu05paw6K++56iLXG4gICAgICBpZiAoY291cnNlLmlkICYmIGNvdXJzZS5pZCA+IDEwMDAwMDApIHtcbiAgICAgICAgLy8g5paw6K++56iL77yM5L2/55So5Yib5bu6QVBJXG4gICAgICAgIGNvbnNvbGUubG9nKCfwn5OkIOWIm+W7uuaWsOivvueoiycpO1xuICAgICAgICBjb25zdCB7IGRhdGE6IHJlc3BvbnNlIH0gPSBhd2FpdCBjb3Vyc2VBcGkuY3JlYXRlQ291cnNlKGNvdXJzZURhdGEpO1xuICAgICAgICByZXN1bHQgPSByZXNwb25zZTtcbiAgICAgICAgY29uc29sZS5sb2coJ+KchSDor77nqIvliJvlu7rmiJDlip8nKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIC8vIOeOsOacieivvueoi++8jOS9v+eUqOabtOaWsEFQSVxuICAgICAgICBjb25zb2xlLmxvZygn8J+TpCDmm7TmlrDnjrDmnInor77nqIs6JywgY291cnNlLmlkKTtcbiAgICAgICAgY29uc3QgeyBkYXRhOiByZXNwb25zZSB9ID0gYXdhaXQgY291cnNlQXBpLnVwZGF0ZUNvdXJzZShjb3Vyc2UuaWQsIGNvdXJzZURhdGEpO1xuICAgICAgICByZXN1bHQgPSByZXNwb25zZTtcbiAgICAgICAgY29uc29sZS5sb2coJ+KchSDor77nqIvmm7TmlrDmiJDlip8nKTtcbiAgICAgIH1cblxuICAgICAgcmV0dXJuIHJlc3VsdDtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcign4p2MIOS/neWtmOivvueoi+Wksei0pTonLCBlcnJvcik7XG4gICAgICB0aHJvdyBlcnJvcjtcbiAgICB9XG4gIH07XG5cbiAgLy8g5Y+R5biD6YCJ5Lit55qE6K++56iLXG4gIGNvbnN0IGhhbmRsZVB1Ymxpc2hDb3Vyc2UgPSBhc3luYyAoKSA9PiB7XG4gICAgaWYgKCFzZWxlY3RlZENvdXJzZUlkKSB7XG4gICAgICBhbGVydCgn6K+35YWI6YCJ5oup6KaB5Y+R5biD55qE6K++56iLJyk7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgY29uc3Qgc2VsZWN0ZWRDb3Vyc2UgPSBjb3Vyc2VMaXN0LmZpbmQoY291cnNlID0+IGNvdXJzZS5pZCA9PT0gc2VsZWN0ZWRDb3Vyc2VJZCk7XG4gICAgaWYgKCFzZWxlY3RlZENvdXJzZSkge1xuICAgICAgYWxlcnQoJ+acquaJvuWIsOmAieS4reeahOivvueoiycpO1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIC8vIOajgOafpeivvueoi+aYr+WQpuW3sue7j+WPkeW4g1xuICAgIGlmIChzZWxlY3RlZENvdXJzZS5zdGF0dXMgPT09IDEpIHtcbiAgICAgIGFsZXJ0KCfor6Xor77nqIvlt7Lnu4/lj5HluIPvvIzml6DpnIDph43lpI3lj5HluIMnKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICB0cnkge1xuICAgICAgc2V0SXNQdWJsaXNoaW5nKHRydWUpO1xuICAgICAgY29uc29sZS5sb2coJ/Cfk6Qg5byA5aeL5Y+R5biD6K++56iLJyk7XG4gICAgICBjb25zb2xlLmxvZygn8J+TpCBzZWxlY3RlZENvdXJzZUlkOicsIHNlbGVjdGVkQ291cnNlSWQsICfnsbvlnos6JywgdHlwZW9mIHNlbGVjdGVkQ291cnNlSWQpO1xuICAgICAgY29uc29sZS5sb2coJ/Cfk6Qg6K++56iL5L+h5oGvOicsIHNlbGVjdGVkQ291cnNlKTtcbiAgICAgIGNvbnNvbGUubG9nKCfwn5OkIOivvueoi0lE5a2X5q61OicsIHNlbGVjdGVkQ291cnNlLmlkLCAn57G75Z6LOicsIHR5cGVvZiBzZWxlY3RlZENvdXJzZS5pZCk7XG4gICAgICBjb25zb2xlLmxvZygn8J+TpCDor77nqIvliJfooajkuK3nmoTmiYDmnIlJRDonLCBjb3Vyc2VMaXN0Lm1hcChjID0+ICh7IGlkOiBjLmlkLCB0eXBlOiB0eXBlb2YgYy5pZCwgdGl0bGU6IGMudGl0bGUgfSkpKTtcblxuICAgICAgLy8g56Gu5L+d5L2/55So5q2j56Gu55qE6K++56iLSURcbiAgICAgIGNvbnN0IGNvdXJzZUlkVG9QdWJsaXNoID0gc2VsZWN0ZWRDb3Vyc2UuaWQ7XG4gICAgICBjb25zb2xlLmxvZygn8J+TpCDljbPlsIblj5HluIPnmoTor77nqItJRDonLCBjb3Vyc2VJZFRvUHVibGlzaCwgJ+exu+WeizonLCB0eXBlb2YgY291cnNlSWRUb1B1Ymxpc2gpO1xuXG4gICAgICBjb25zdCB7IGRhdGE6IHJlc3BvbnNlIH0gPSBhd2FpdCBjb3Vyc2VBcGkucHVibGlzaENvdXJzZShjb3Vyc2VJZFRvUHVibGlzaCk7XG5cbiAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDApIHtcbiAgICAgICAgY29uc29sZS5sb2coJ+KchSDor77nqIvlj5HluIPmiJDlip86JywgcmVzcG9uc2UuZGF0YSk7XG4gICAgICAgIGFsZXJ0KCfor77nqIvlj5HluIPmiJDlip/vvIEnKTtcblxuICAgICAgICAvLyDliLfmlrDor77nqIvliJfooajku6Xmm7TmlrDnirbmgIFcbiAgICAgICAgYXdhaXQgbG9hZENvdXJzZUxpc3QoKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDlj5HluIPor77nqIvlpLHotKU6JywgcmVzcG9uc2UubWVzc2FnZSk7XG4gICAgICAgIGFsZXJ0KHJlc3BvbnNlLm1lc3NhZ2UgfHwgJ+WPkeW4g+ivvueoi+Wksei0pScpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDlj5HluIPor77nqIvlpLHotKU6JywgZXJyb3IpO1xuICAgICAgY29uc29sZS5lcnJvcign4p2MIOmUmeivr+ivpuaDhTonLCBlcnJvci5yZXNwb25zZT8uZGF0YSk7XG5cbiAgICAgIC8vIOWkhOeQhuWFt+S9k+eahOmUmeivr+S/oeaBr1xuICAgICAgaWYgKGVycm9yLnJlc3BvbnNlPy5kYXRhPy5tZXNzYWdlKSB7XG4gICAgICAgIGFsZXJ0KGVycm9yLnJlc3BvbnNlLmRhdGEubWVzc2FnZSk7XG4gICAgICB9IGVsc2UgaWYgKGVycm9yLm1lc3NhZ2UpIHtcbiAgICAgICAgYWxlcnQoZXJyb3IubWVzc2FnZSk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBhbGVydCgn5Y+R5biD6K++56iL5aSx6LSl77yM6K+36YeN6K+VJyk7XG4gICAgICB9XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldElzUHVibGlzaGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIC8vIOS/neWtmOivvueoi+WIl+ihqFxuICBjb25zdCBoYW5kbGVTYXZlID0gYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBzZXRJc0NyZWF0aW5nKHRydWUpO1xuICAgICAgLy8g5qOA5p+l5piv5ZCm5pyJ5paH5Lu25q2j5Zyo5LiK5LygXG4gICAgICBpZiAodXBsb2FkaW5nRmlsZXMuc2l6ZSA+IDApIHtcbiAgICAgICAgYWxlcnQoJ+acieaWh+S7tuato+WcqOS4iuS8oOS4re+8jOivt+etieW+heS4iuS8oOWujOaIkOWQjuWGjeS/neWtmCcpO1xuICAgICAgICBzZXRJc0NyZWF0aW5nKGZhbHNlKTtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuXG4gICAgICBjb25zb2xlLmxvZygn8J+SviDlvIDlp4vkv53lrZjor77nqIvliJfooagnKTtcblxuICAgICAgLy8g5aaC5p6c5pyJ6YCJ5Lit55qE6K++56iL5LiU5Zyo57yW6L6R54q25oCB77yM5YWI5L+d5a2Y5b2T5YmN6K++56iLXG4gICAgICBpZiAoc2VsZWN0ZWRDb3Vyc2VJZCAmJiByaWdodFBhbmVsVHlwZSA9PT0gJ2NvdXJzZScpIHtcbiAgICAgICAgY29uc3Qgc2VsZWN0ZWRDb3Vyc2UgPSBjb3Vyc2VMaXN0LmZpbmQoYyA9PiBjLmlkID09PSBzZWxlY3RlZENvdXJzZUlkKTtcbiAgICAgICAgaWYgKHNlbGVjdGVkQ291cnNlKSB7XG4gICAgICAgICAgLy8g5pu05paw6K++56iL5pWw5o2uXG4gICAgICAgICAgY29uc3QgdXBkYXRlZENvdXJzZSA9IHtcbiAgICAgICAgICAgIC4uLnNlbGVjdGVkQ291cnNlLFxuICAgICAgICAgICAgdGl0bGU6IGNvdXJzZURldGFpbC50aXRsZSxcbiAgICAgICAgICAgIGRlc2NyaXB0aW9uOiBjb3Vyc2VEZXRhaWwuZGVzY3JpcHRpb24sXG4gICAgICAgICAgICBjb3ZlckltYWdlOiBjb3Vyc2VEZXRhaWwuY292ZXJJbWFnZSxcbiAgICAgICAgICAgIGhhc1ZpZGVvOiBjb3Vyc2VEZXRhaWwuaXNWaWRlb0VuYWJsZWQgPyAxIDogMCxcbiAgICAgICAgICAgIGhhc0RvY3VtZW50OiBjb3Vyc2VEZXRhaWwuaXNBdHRhY2htZW50RW5hYmxlZCA/IDEgOiAwLFxuICAgICAgICAgICAgY29udGVudENvbmZpZzogY291cnNlRGV0YWlsLmNvbnRlbnRDb25maWcsXG4gICAgICAgICAgICB0ZWFjaGluZ0luZm86IGNvdXJzZURldGFpbC50ZWFjaGluZ0luZm8sXG4gICAgICAgICAgICBhZGRpdGlvbmFsUmVzb3VyY2VzOiBjb3Vyc2VEZXRhaWwudGVhY2hpbmdNYXRlcmlhbHM/Lm1hcChtYXRlcmlhbCA9PiAoe1xuICAgICAgICAgICAgICB0aXRsZTogbWF0ZXJpYWwubmFtZSxcbiAgICAgICAgICAgICAgdXJsOiBtYXRlcmlhbC51cmwsXG4gICAgICAgICAgICAgIGRlc2NyaXB0aW9uOiBtYXRlcmlhbC5uYW1lXG4gICAgICAgICAgICB9KSkgfHwgW11cbiAgICAgICAgICB9O1xuXG4gICAgICAgICAgLy8g5YWI5pu05paw6K++56iL5YiX6KGo5Lit55qE5pWw5o2uXG4gICAgICAgICAgc2V0Q291cnNlTGlzdChwcmV2ID0+IHByZXYubWFwKGNvdXJzZSA9PlxuICAgICAgICAgICAgY291cnNlLmlkID09PSBzZWxlY3RlZENvdXJzZUlkID8gdXBkYXRlZENvdXJzZSA6IGNvdXJzZVxuICAgICAgICAgICkpO1xuXG4gICAgICAgICAgLy8g54S25ZCO5L+d5a2Y5Yiw5ZCO56uvXG4gICAgICAgICAgY29uc3Qgc2F2ZWRDb3Vyc2VSZXN1bHQgPSBhd2FpdCBzYXZlQ291cnNlKHVwZGF0ZWRDb3Vyc2UpO1xuXG4gICAgICAgICAgLy8g5aaC5p6c6K++56iL5pyJ5Lu75Yqh6YWN572u77yM5Y+R6YCB6K++56iL6K6+572u6K+35rGCXG4gICAgICAgICAgaWYgKGNvdXJzZURldGFpbC50YXNrQ29uZmlnICYmIChjb3Vyc2VEZXRhaWwuaXNEaXN0cmlidXRpb25XYXRlciB8fCBjb3Vyc2VEZXRhaWwuaXNPbmVLZXlPcGVuKSkge1xuICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgLy8g6I635Y+W55yf5a6e55qE6K++56iLSURcbiAgICAgICAgICAgICAgbGV0IHJlYWxDb3Vyc2VJZCA9IHNlbGVjdGVkQ291cnNlSWQ7XG5cbiAgICAgICAgICAgICAgLy8g5aaC5p6c5piv5paw5Yib5bu655qE6K++56iL77yM5L2/55So6L+U5Zue55qE55yf5a6eSURcbiAgICAgICAgICAgICAgaWYgKHNlbGVjdGVkQ291cnNlLmlkID4gMTAwMDAwMCAmJiBzYXZlZENvdXJzZVJlc3VsdD8uZGF0YT8uaWQpIHtcbiAgICAgICAgICAgICAgICByZWFsQ291cnNlSWQgPSBzYXZlZENvdXJzZVJlc3VsdC5kYXRhLmlkO1xuICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCfwn4aUIOS9v+eUqOaWsOWIm+W7uuivvueoi+eahOecn+WunklEOicsIHJlYWxDb3Vyc2VJZCk7XG4gICAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgICAvLyAxLiDlhYjliJvlu7rku7vliqHmqKHmnb/vvIjlpoLmnpzmnInku7vliqHphY3nva7vvIlcbiAgICAgICAgICAgICAgaWYgKGNvdXJzZURldGFpbC50YXNrQ29uZmlnLnRhc2tOYW1lIHx8IGNvdXJzZURldGFpbC50YXNrQ29uZmlnLnRhc2tEZXNjcmlwdGlvbikge1xuICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5OdIOW8gOWni+WIm+W7uuS7u+WKoeaooeadvy4uLicpO1xuXG4gICAgICAgICAgICAgICAgLy8g5p6E5bu65Lu75Yqh5qih5p2/5pWw5o2uXG4gICAgICAgICAgICAgICAgY29uc3QgdGFza1RlbXBsYXRlRGF0YSA9IHtcbiAgICAgICAgICAgICAgICAgIHRhc2tOYW1lOiBjb3Vyc2VEZXRhaWwudGFza0NvbmZpZy50YXNrTmFtZSB8fCAnJyxcbiAgICAgICAgICAgICAgICAgIHRhc2tEZXNjcmlwdGlvbjogY291cnNlRGV0YWlsLnRhc2tDb25maWcudGFza0Rlc2NyaXB0aW9uIHx8ICcnLFxuICAgICAgICAgICAgICAgICAgZHVyYXRpb25EYXlzOiBjb3Vyc2VEZXRhaWwudGFza0NvbmZpZy50YXNrRHVyYXRpb24gPyBwYXJzZUludChjb3Vyc2VEZXRhaWwudGFza0NvbmZpZy50YXNrRHVyYXRpb24pIDogNyxcbiAgICAgICAgICAgICAgICAgIGF0dGFjaG1lbnRzOiBjb3Vyc2VEZXRhaWwudGFza0NvbmZpZy5yZWZlcmVuY2VSZXNvdXJjZXMubWFwKChyZXNvdXJjZSkgPT4gKHtcbiAgICAgICAgICAgICAgICAgICAgdGl0bGU6IHJlc291cmNlLm5hbWUsXG4gICAgICAgICAgICAgICAgICAgIHVybDogcmVzb3VyY2UudXJsIHx8ICcnLFxuICAgICAgICAgICAgICAgICAgICB0eXBlOiByZXNvdXJjZS50eXBlID09PSAnZmlsZScgPyAnZmlsZScgOiAnZG9jdW1lbnQnXG4gICAgICAgICAgICAgICAgICB9KSksXG4gICAgICAgICAgICAgICAgICB3b3JrSWRzU3RyOiBjb3Vyc2VEZXRhaWwudGFza0NvbmZpZy5yZWZlcmVuY2VXb3Jrcy5tYXAoKF8sIGluZGV4KSA9PiBgJHsyMDAgKyBpbmRleH1gKS5qb2luKCcsJyksIC8vIOaooeaLn+S9nOWTgUlEXG4gICAgICAgICAgICAgICAgICBzZWxmQXNzZXNzbWVudEl0ZW1zOiBjb3Vyc2VEZXRhaWwudGFza0NvbmZpZy5zZWxmQXNzZXNzbWVudEl0ZW1zXG4gICAgICAgICAgICAgICAgICAgIC5maWx0ZXIoaXRlbSA9PiBpdGVtLnRyaW0oKSAhPT0gJycpIC8vIOi/h+a7pOepuumhuVxuICAgICAgICAgICAgICAgICAgICAubWFwKChjb250ZW50LCBpbmRleCkgPT4gKHtcbiAgICAgICAgICAgICAgICAgICAgICBjb250ZW50LFxuICAgICAgICAgICAgICAgICAgICAgIHNlcXVlbmNlOiBpbmRleCArIDFcbiAgICAgICAgICAgICAgICAgICAgfSkpLFxuICAgICAgICAgICAgICAgICAgc3RhdHVzOiAxXG4gICAgICAgICAgICAgICAgfTtcblxuICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5OkIOWPkemAgeS7u+WKoeaooeadv+WIm+W7uuivt+axguWIsOivvueoi0lEOicsIHJlYWxDb3Vyc2VJZCk7XG4gICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ/Cfk6Qg5Lu75Yqh5qih5p2/5pWw5o2uOicsIHRhc2tUZW1wbGF0ZURhdGEpO1xuXG4gICAgICAgICAgICAgICAgYXdhaXQgY291cnNlQXBpLmNyZWF0ZUNvdXJzZVRhc2tUZW1wbGF0ZShyZWFsQ291cnNlSWQsIHRhc2tUZW1wbGF0ZURhdGEpO1xuICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCfinIUg5Lu75Yqh5qih5p2/5Yib5bu65oiQ5YqfJyk7XG4gICAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgICAvLyAyLiDnhLblkI7lj5HpgIHor77nqIvorr7nva7or7fmsYJcbiAgICAgICAgICAgICAgY29uc3Qgc2V0dGluZ3NEYXRhID0ge1xuICAgICAgICAgICAgICAgIHRlbXBsYXRlSWQ6IGNvdXJzZURldGFpbC5zZWxlY3RlZFRlbXBsYXRlLmlkIHx8IG51bGwsIC8vIOS9v+eUqOmAieaLqeeahOaooeadv0lEXG4gICAgICAgICAgICAgICAgcmVxdWlyZWRQb2ludHM6IGNvdXJzZURldGFpbC5yZXF1aXJlZEVuZXJneSA/IHBhcnNlSW50KGNvdXJzZURldGFpbC5yZXF1aXJlZEVuZXJneSkgOiAwLFxuICAgICAgICAgICAgICAgIGF1dG9DcmVhdGVUYXNrczogY291cnNlRGV0YWlsLmlzT25lS2V5T3BlbiA/IDEgOiAwXG4gICAgICAgICAgICAgIH07XG5cbiAgICAgICAgICAgICAgY29uc29sZS5sb2coJ/Cfk6Qg5Y+R6YCB6K++56iL6K6+572u6K+35rGC5Yiw6K++56iLSUQ6JywgcmVhbENvdXJzZUlkKTtcbiAgICAgICAgICAgICAgY29uc29sZS5sb2coJ/Cfk6Qg6K6+572u5pWw5o2uOicsIHNldHRpbmdzRGF0YSk7XG4gICAgICAgICAgICAgIGF3YWl0IGNvdXJzZUFwaS5zZXRDb3Vyc2VTZXR0aW5ncyhyZWFsQ291cnNlSWQsIHNldHRpbmdzRGF0YSk7XG4gICAgICAgICAgICAgIGNvbnNvbGUubG9nKCfinIUg6K++56iL6K6+572u5L+d5a2Y5oiQ5YqfJyk7XG4gICAgICAgICAgICB9IGNhdGNoIChzZXR0aW5nc0Vycm9yOiBhbnkpIHtcbiAgICAgICAgICAgICAgY29uc29sZS5lcnJvcign4p2MIOivvueoi+iuvue9ruaIluS7u+WKoeaooeadv+WIm+W7uuWksei0pTonLCBzZXR0aW5nc0Vycm9yKTtcbiAgICAgICAgICAgICAgLy8g5LiN6Zi75pat5Li75rWB56iL77yM5Y+q6K6w5b2V6ZSZ6K+vXG4gICAgICAgICAgICB9XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIC8vIOS/neWtmOezu+WIl+ivvueoi+S/oeaBr1xuICAgICAgY29uc3QgZGF0YSA9IHtcbiAgICAgICAgdGl0bGU6IGVkaXRpbmdUaXRsZSxcbiAgICAgICAgY291cnNlR29hbHMsXG4gICAgICAgIGNvdXJzZU9iamVjdGl2ZXMsXG4gICAgICAgIGNvdXJzZUxpc3RcbiAgICAgIH07XG5cbiAgICAgIG9uU2F2ZShkYXRhKTtcbiAgICAgIG9uQ2xvc2UoKTtcblxuICAgICAgY29uc29sZS5sb2coJ+KchSDor77nqIvliJfooajkv53lrZjlrozmiJAnKTtcbiAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfinYwg5L+d5a2Y5aSx6LSlOicsIGVycm9yKTtcbiAgICAgIGFsZXJ0KGDkv53lrZjlpLHotKU6ICR7ZXJyb3IubWVzc2FnZSB8fCAn6K+36YeN6K+VJ31gKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0SXNDcmVhdGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIC8vIOWPkeW4g+ezu+WIl+ivvueoi1xuICBjb25zdCBoYW5kbGVQdWJsaXNoID0gYXN5bmMgKCkgPT4ge1xuICAgIC8vIOWmguaenOezu+WIl+W3suWPkeW4g++8jOS4jeaJp+ihjOS7u+S9leaTjeS9nFxuICAgIGlmIChzZXJpZXNTdGF0dXMgPT09IDEpIHtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICB0cnkge1xuICAgICAgc2V0SXNQdWJsaXNoaW5nU2VyaWVzKHRydWUpO1xuXG4gICAgICAvLyDmo4Dmn6XmmK/lkKbmnInor77nqItcbiAgICAgIGlmIChjb3Vyc2VMaXN0Lmxlbmd0aCA9PT0gMCkge1xuICAgICAgICBhbGVydCgn5Y+R5biD5aSx6LSl77ya6K++56iL57O75YiX5Lit6Iez5bCR6ZyA6KaB5YyF5ZCr5LiA5Liq6K++56iLJyk7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cblxuICAgICAgY29uc29sZS5sb2coJ/Cfk6Qg5byA5aeL5Y+R5biD57O75YiX6K++56iL77yM57O75YiXSUQ6Jywgc2VyaWVzSWQpO1xuXG4gICAgICBjb25zdCB7IGRhdGE6IHJlc3BvbnNlIH0gPSBhd2FpdCBjb3Vyc2VBcGkucHVibGlzaENvdXJzZVNlcmllcyhzZXJpZXNJZCk7XG5cbiAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDApIHtcbiAgICAgICAgY29uc29sZS5sb2coJ+KchSDns7vliJfor77nqIvlj5HluIPmiJDlip86JywgcmVzcG9uc2UuZGF0YSk7XG5cbiAgICAgICAgLy8g5p6E5bu65oiQ5Yqf5raI5oGvXG4gICAgICAgIGNvbnN0IHB1Ymxpc2hEYXRhID0gcmVzcG9uc2UuZGF0YTtcbiAgICAgICAgbGV0IHN1Y2Nlc3NNZXNzYWdlID0gYOezu+WIl+ivvueoi1wiJHtwdWJsaXNoRGF0YS50aXRsZX1cIuWPkeW4g+aIkOWKn++8gWA7XG5cbiAgICAgICAgLy8g5aaC5p6c5pyJ5Y+R5biD57uf6K6h5L+h5oGv77yM5re75Yqg5Yiw5raI5oGv5LitXG4gICAgICAgIGlmIChwdWJsaXNoRGF0YS5wdWJsaXNoU3RhdHMpIHtcbiAgICAgICAgICBjb25zdCBzdGF0cyA9IHB1Ymxpc2hEYXRhLnB1Ymxpc2hTdGF0cztcbiAgICAgICAgICBzdWNjZXNzTWVzc2FnZSArPSBgXFxuXFxu5Y+R5biD57uf6K6h77yaXFxu4oCiIOaAu+ivvueoi+aVsO+8miR7cHVibGlzaERhdGEudG90YWxDb3Vyc2VzfVxcbuKAoiDlt7Llj5HluIPor77nqIvvvJoke3B1Ymxpc2hEYXRhLnB1Ymxpc2hlZENvdXJzZXN9XFxu4oCiIOinhumikeivvueoi++8miR7c3RhdHMudmlkZW9Db3Vyc2VDb3VudH3kuKpcXG7igKIg5paH5qGj6K++56iL77yaJHtzdGF0cy5kb2N1bWVudENvdXJzZUNvdW50feS4qlxcbuKAoiDmgLvotYTmupDmlbDvvJoke3N0YXRzLnRvdGFsUmVzb3VyY2VzQ291bnR95LiqYDtcblxuICAgICAgICAgIGlmIChzdGF0cy50b3RhbFZpZGVvRHVyYXRpb24gPiAwKSB7XG4gICAgICAgICAgICBjb25zdCBkdXJhdGlvbk1pbnV0ZXMgPSBNYXRoLnJvdW5kKHN0YXRzLnRvdGFsVmlkZW9EdXJhdGlvbiAvIDYwKTtcbiAgICAgICAgICAgIHN1Y2Nlc3NNZXNzYWdlICs9IGBcXG7igKIg6KeG6aKR5oC75pe26ZW/77yaJHtkdXJhdGlvbk1pbnV0ZXN95YiG6ZKfYDtcbiAgICAgICAgICB9XG4gICAgICAgIH1cblxuICAgICAgICBhbGVydChzdWNjZXNzTWVzc2FnZSk7XG5cbiAgICAgICAgLy8g5pu05paw57O75YiX54q25oCB5Li65bey5Y+R5biDXG4gICAgICAgIHNldFNlcmllc1N0YXR1cygxKTtcblxuICAgICAgICAvLyDliLfmlrDor77nqIvliJfooajku6Xmm7TmlrDnirbmgIFcbiAgICAgICAgYXdhaXQgbG9hZENvdXJzZUxpc3QoKTtcblxuICAgICAgICAvLyDpgJrnn6XniLbnu4Tku7bliLfmlrDmlbDmja5cbiAgICAgICAgb25TYXZlKHtcbiAgICAgICAgICB0eXBlOiAncHVibGlzaF9zZXJpZXMnLFxuICAgICAgICAgIHNlcmllc0lkOiBzZXJpZXNJZCxcbiAgICAgICAgICBtZXNzYWdlOiAn57O75YiX6K++56iL5Y+R5biD5oiQ5YqfJ1xuICAgICAgICB9KTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDlj5HluIPns7vliJfor77nqIvlpLHotKU6JywgcmVzcG9uc2UubWVzc2FnZSk7XG4gICAgICAgIGFsZXJ0KHJlc3BvbnNlLm1lc3NhZ2UgfHwgJ+WPkeW4g+ezu+WIl+ivvueoi+Wksei0pScpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDlj5HluIPns7vliJfor77nqIvlh7rplJk6JywgZXJyb3IpO1xuXG4gICAgICAvLyDlpITnkIblhbfkvZPnmoTplJnor6/kv6Hmga9cbiAgICAgIGxldCBlcnJvck1lc3NhZ2UgPSAn5Y+R5biD57O75YiX6K++56iL5aSx6LSlJztcbiAgICAgIGlmIChlcnJvci5yZXNwb25zZT8uZGF0YT8ubWVzc2FnZSkge1xuICAgICAgICBlcnJvck1lc3NhZ2UgPSBlcnJvci5yZXNwb25zZS5kYXRhLm1lc3NhZ2U7XG4gICAgICB9IGVsc2UgaWYgKGVycm9yLm1lc3NhZ2UpIHtcbiAgICAgICAgZXJyb3JNZXNzYWdlID0gZXJyb3IubWVzc2FnZTtcbiAgICAgIH1cblxuICAgICAgYWxlcnQoZXJyb3JNZXNzYWdlKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0SXNQdWJsaXNoaW5nU2VyaWVzKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgLy8g6YCA5Ye657yW6L6R5qih5byPIC0g5L+d5a2Y5pWw5o2u5bm25YWz6ZetXG4gIGNvbnN0IGhhbmRsZUV4aXRFZGl0ID0gKCkgPT4ge1xuICAgIGhhbmRsZVNhdmUoKTtcbiAgfTtcblxuICAvLyDmmL7npLrorr7nva7pnaLmnb9cbiAgY29uc3Qgc2hvd1NldHRpbmdzUGFuZWwgPSAoKSA9PiB7XG4gICAgc2V0UmlnaHRQYW5lbFR5cGUoJ3NldHRpbmdzJyk7XG4gICAgc2V0U2VsZWN0ZWRDb3Vyc2VJZChudWxsKTtcbiAgfTtcblxuICAvLyDlpITnkIbmqKHmnb/pgInmi6lcbiAgY29uc3QgaGFuZGxlVGVtcGxhdGVTZWxlY3QgPSAodGVtcGxhdGU6IGFueSkgPT4ge1xuICAgIHNldENvdXJzZURldGFpbChwcmV2ID0+ICh7XG4gICAgICAuLi5wcmV2LFxuICAgICAgc2VsZWN0ZWRUZW1wbGF0ZToge1xuICAgICAgICBpZDogdGVtcGxhdGUuaWQsXG4gICAgICAgIG5hbWU6IHRlbXBsYXRlLnRlbXBsYXRlTmFtZVxuICAgICAgfVxuICAgIH0pKTtcbiAgICBzZXRJc1RlbXBsYXRlUGlja2VyT3BlbihmYWxzZSk7XG4gIH07XG5cbiAgLy8g5omT5byA5qih5p2/6YCJ5oup5by556qXXG4gIGNvbnN0IGhhbmRsZU9wZW5UZW1wbGF0ZVBpY2tlciA9ICgpID0+IHtcbiAgICBzZXRJc1RlbXBsYXRlUGlja2VyT3Blbih0cnVlKTtcbiAgfTtcblxuICAvLyDmmL7npLror77nqIvnvJbovpHpnaLmnb9cbiAgY29uc3Qgc2hvd0NvdXJzZVBhbmVsID0gYXN5bmMgKGNvdXJzZUlkOiBudW1iZXIpID0+IHtcbiAgICBzZXRSaWdodFBhbmVsVHlwZSgnY291cnNlJyk7XG4gICAgc2V0U2VsZWN0ZWRDb3Vyc2VJZChjb3Vyc2VJZCk7XG5cbiAgICAvLyDlpoLmnpzkvZzlk4HmlbDmja7ov5jmsqHmnInliqDovb3vvIzph43mlrDliqDovb1cbiAgICBpZiAod29ya3MubGVuZ3RoID09PSAwICYmICFsb2FkaW5nV29ya3MpIHtcbiAgICAgIGxvYWRVc2VyV29ya3MoKTtcbiAgICB9XG5cbiAgICAvLyDojrflj5bpgInkuK3nmoTor77nqIvlubbmm7TmlrBjb3Vyc2VEZXRhaWznirbmgIFcbiAgICBjb25zdCBzZWxlY3RlZENvdXJzZSA9IGNvdXJzZUxpc3QuZmluZChjb3Vyc2UgPT4gY291cnNlLmlkID09PSBjb3Vyc2VJZCk7XG4gICAgY29uc29sZS5sb2coJ/Cfjq8g5om+5Yiw55qE6K++56iLOicsIHNlbGVjdGVkQ291cnNlKTtcblxuICAgIGlmIChzZWxlY3RlZENvdXJzZSkge1xuICAgICAgdHJ5IHtcbiAgICAgICAgY29uc29sZS5sb2coJ/CflIQg6I635Y+W6K++56iL6K+m5oOF77yMc2VyaWVzSWQ6Jywgc2VyaWVzSWQsICdjb3Vyc2VJZDonLCBjb3Vyc2VJZCk7XG5cbiAgICAgICAgLy8g6I635Y+W55yf5a6e55qE6K++56iL6K+m5oOF5pWw5o2uXG4gICAgICAgIGNvbnN0IHsgZGF0YTogcmVzIH0gPSBhd2FpdCBmZXRjaENvdXJzZURldGFpbChzZXJpZXNJZCwgY291cnNlSWQpO1xuXG4gICAgICAgIGlmIChyZXMuY29kZSA9PT0gMjAwICYmIHJlcy5kYXRhKSB7XG4gICAgICAgICAgY29uc3QgY291cnNlRGV0YWlsRGF0YSA9IHJlcy5kYXRhO1xuICAgICAgICAgIGNvbnNvbGUubG9nKCfinIUg6I635Y+W5Yiw6K++56iL6K+m5oOFOicsIGNvdXJzZURldGFpbERhdGEpO1xuXG4gICAgICAgICAgLy8g5bCG55yf5a6e55qEIGFkZGl0aW9uYWxSZXNvdXJjZXMg5pig5bCE5YiwIHRlYWNoaW5nTWF0ZXJpYWxzXG4gICAgICAgICAgY29uc3QgdGVhY2hpbmdNYXRlcmlhbHMgPSBjb3Vyc2VEZXRhaWxEYXRhLmFkZGl0aW9uYWxSZXNvdXJjZXM/Lm1hcCgocmVzb3VyY2U6IGFueSkgPT4gKHtcbiAgICAgICAgICAgIHR5cGU6ICdhcHBsaWNhdGlvbi9vY3RldC1zdHJlYW0nLCAvLyDpu5jorqTnsbvlnotcbiAgICAgICAgICAgIG5hbWU6IHJlc291cmNlLnRpdGxlIHx8IHJlc291cmNlLm5hbWUgfHwgJ+mZhOS7ticsXG4gICAgICAgICAgICB1cmw6IHJlc291cmNlLnVybFxuICAgICAgICAgIH0pKSB8fCBbXTtcblxuICAgICAgICAgIC8vIOWwhkFQSei/lOWbnueahHRlYWNoaW5nSW5mb+aVsOe7hOagvOW8j+i9rOaNouS4uuaWsOeahOagvOW8j1xuICAgICAgICAgIGNvbnN0IG1hcHBlZFRlYWNoaW5nSW5mbyA9IGNvdXJzZURldGFpbERhdGEudGVhY2hpbmdJbmZvPy5tYXAoKGluZm86IGFueSkgPT4gKHtcbiAgICAgICAgICAgIHRpdGxlOiBpbmZvLnRpdGxlIHx8ICcnLFxuICAgICAgICAgICAgY29udGVudDogQXJyYXkuaXNBcnJheShpbmZvLmNvbnRlbnQpID8gaW5mby5jb250ZW50LmpvaW4oJ1xcbicpIDogKGluZm8uY29udGVudCB8fCAnJylcbiAgICAgICAgICB9KSkgfHwgW107XG5cbiAgICAgICAgICBjb25zb2xlLmxvZygn8J+TjiDmmKDlsITnmoTmlZnlrabpmYTku7Y6JywgdGVhY2hpbmdNYXRlcmlhbHMpO1xuICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5OaIOaYoOWwhOeahOaVmeWtpuS/oeaBrzonLCBtYXBwZWRUZWFjaGluZ0luZm8pO1xuXG4gICAgICAgICAgc2V0Q291cnNlRGV0YWlsKHByZXYgPT4gKHtcbiAgICAgICAgICAgIC4uLnByZXYsXG4gICAgICAgICAgICB0aXRsZTogY291cnNlRGV0YWlsRGF0YS50aXRsZSxcbiAgICAgICAgICAgIGRlc2NyaXB0aW9uOiBjb3Vyc2VEZXRhaWxEYXRhLmRlc2NyaXB0aW9uLFxuICAgICAgICAgICAgY292ZXJJbWFnZTogY291cnNlRGV0YWlsRGF0YS5jb3ZlckltYWdlIHx8ICcnLFxuICAgICAgICAgICAgaXNWaWRlb0VuYWJsZWQ6IGNvdXJzZURldGFpbERhdGEuaGFzVmlkZW8gPT09IDEsXG4gICAgICAgICAgICBpc0F0dGFjaG1lbnRFbmFibGVkOiBjb3Vyc2VEZXRhaWxEYXRhLmhhc0RvY3VtZW50ID09PSAxLFxuICAgICAgICAgICAgY29udGVudENvbmZpZzogY291cnNlRGV0YWlsRGF0YS5jb250ZW50Q29uZmlnIHx8IHt9LFxuICAgICAgICAgICAgdGVhY2hpbmdJbmZvOiBtYXBwZWRUZWFjaGluZ0luZm8sIC8vIOS9v+eUqOaYoOWwhOWQjueahOaVmeWtpuS/oeaBr1xuICAgICAgICAgICAgdGVhY2hpbmdNYXRlcmlhbHM6IHRlYWNoaW5nTWF0ZXJpYWxzLCAvLyDkvb/nlKjnnJ/lrp7nmoTpmYTku7bmlbDmja5cbiAgICAgICAgICAgIHZpZGVvVXJsOiBjb3Vyc2VEZXRhaWxEYXRhLmNvbnRlbnRDb25maWc/LnZpZGVvPy51cmwgfHwgJycsXG4gICAgICAgICAgICB2aWRlb05hbWU6IGNvdXJzZURldGFpbERhdGEuY29udGVudENvbmZpZz8udmlkZW8/Lm5hbWUgfHwgJycsXG4gICAgICAgICAgICBhdHRhY2htZW50VXJsOiBjb3Vyc2VEZXRhaWxEYXRhLmNvbnRlbnRDb25maWc/LmRvY3VtZW50Py51cmwgfHwgJycsXG4gICAgICAgICAgICBhdHRhY2htZW50TmFtZTogY291cnNlRGV0YWlsRGF0YS5jb250ZW50Q29uZmlnPy5kb2N1bWVudD8ubmFtZSB8fCAnJ1xuICAgICAgICAgIH0pKTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICBjb25zb2xlLmVycm9yKCfinYwg6I635Y+W6K++56iL6K+m5oOF5aSx6LSlOicsIHJlcy5tZXNzYWdlKTtcbiAgICAgICAgICAvLyDkvb/nlKjln7rnoYDmlbDmja7kvZzkuLrlkI7lpIdcbiAgICAgICAgICBzZXRDb3Vyc2VEZXRhaWwocHJldiA9PiAoe1xuICAgICAgICAgICAgLi4ucHJldixcbiAgICAgICAgICAgIHRpdGxlOiBzZWxlY3RlZENvdXJzZS50aXRsZSxcbiAgICAgICAgICAgIGRlc2NyaXB0aW9uOiBzZWxlY3RlZENvdXJzZS5kZXNjcmlwdGlvbixcbiAgICAgICAgICAgIGNvdmVySW1hZ2U6IHNlbGVjdGVkQ291cnNlLmNvdmVySW1hZ2UgfHwgJycsXG4gICAgICAgICAgICBpc1ZpZGVvRW5hYmxlZDogc2VsZWN0ZWRDb3Vyc2UuaGFzVmlkZW8gPT09IDEsXG4gICAgICAgICAgICBpc0F0dGFjaG1lbnRFbmFibGVkOiBzZWxlY3RlZENvdXJzZS5oYXNEb2N1bWVudCA9PT0gMSxcbiAgICAgICAgICAgIHRlYWNoaW5nTWF0ZXJpYWxzOiBbXSAvLyDmuIXnqbrpmYTku7bliJfooahcbiAgICAgICAgICB9KSk7XG4gICAgICAgIH1cbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDojrflj5bor77nqIvor6bmg4XlvILluLg6JywgZXJyb3IpO1xuICAgICAgICAvLyDkvb/nlKjln7rnoYDmlbDmja7kvZzkuLrlkI7lpIdcbiAgICAgICAgc2V0Q291cnNlRGV0YWlsKHByZXYgPT4gKHtcbiAgICAgICAgICAuLi5wcmV2LFxuICAgICAgICAgIHRpdGxlOiBzZWxlY3RlZENvdXJzZS50aXRsZSxcbiAgICAgICAgICBkZXNjcmlwdGlvbjogc2VsZWN0ZWRDb3Vyc2UuZGVzY3JpcHRpb24sXG4gICAgICAgICAgY292ZXJJbWFnZTogc2VsZWN0ZWRDb3Vyc2UuY292ZXJJbWFnZSB8fCAnJyxcbiAgICAgICAgICBpc1ZpZGVvRW5hYmxlZDogc2VsZWN0ZWRDb3Vyc2UuaGFzVmlkZW8gPT09IDEsXG4gICAgICAgICAgaXNBdHRhY2htZW50RW5hYmxlZDogc2VsZWN0ZWRDb3Vyc2UuaGFzRG9jdW1lbnQgPT09IDEsXG4gICAgICAgICAgdGVhY2hpbmdNYXRlcmlhbHM6IFtdIC8vIOa4heepuumZhOS7tuWIl+ihqFxuICAgICAgICB9KSk7XG4gICAgICB9XG4gICAgfVxuICB9O1xuXG4gIC8vIOiOt+WPlumAieS4reeahOivvueoi1xuICBjb25zdCBnZXRTZWxlY3RlZENvdXJzZSA9ICgpID0+IHtcbiAgICByZXR1cm4gY291cnNlTGlzdC5maW5kKGNvdXJzZSA9PiBjb3Vyc2UuaWQgPT09IHNlbGVjdGVkQ291cnNlSWQpO1xuICB9O1xuXG4gIGlmICghaXNWaXNpYmxlKSByZXR1cm4gbnVsbDtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiY291cnNlLWxpc3QtbW9kYWwtb3ZlcmxheVwiPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb3Vyc2UtbGlzdC1tb2RhbFwiPlxuICAgICAgICB7Lyog5aS06YOoICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvdXJzZS1saXN0LWhlYWRlclwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY291cnNlLWxpc3QtdGl0bGUtc2VjdGlvblwiPlxuICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cImNvdXJzZS1saXN0LXRpdGxlXCI+6K++56iL5YiX6KGoPC9oMj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY291cnNlLWxpc3QtYWN0aW9uc1wiPlxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgb25DbGljaz17c2hvd1NldHRpbmdzUGFuZWx9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgY291cnNlLWxpc3Qtc2V0dGluZ3MtYnRuICR7cmlnaHRQYW5lbFR5cGUgPT09ICdzZXR0aW5ncycgPyAnYWN0aXZlJyA6ICcnfWB9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8U2V0dGluZ3MgY2xhc3NOYW1lPVwidy00IGgtNFwiIC8+XG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICA8YnV0dG9uIG9uQ2xpY2s9e2FkZE5ld0NvdXJzZX0gY2xhc3NOYW1lPVwiY291cnNlLWxpc3QtYWRkLWJ0blwiPlxuICAgICAgICAgICAgICAgIDxQbHVzIGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPlxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDxidXR0b24gb25DbGljaz17b25DbG9zZX0gY2xhc3NOYW1lPVwiY291cnNlLWxpc3QtY2xvc2UtYnRuXCI+XG4gICAgICAgICAgICA8WCBjbGFzc05hbWU9XCJ3LTYgaC02XCIgLz5cbiAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIOS4u+imgeWGheWuuSAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb3Vyc2UtbGlzdC1jb250ZW50XCI+XG4gICAgICAgICAgey8qIOW3puS+p+ivvueoi+WIl+ihqCAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvdXJzZS1saXN0LXNpZGViYXJcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY291cnNlLWxpc3QtaXRlbXNcIj5cbiAgICAgICAgICAgICAge2xvYWRpbmcgPyAoXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb3Vyc2UtbGlzdC1sb2FkaW5nXCI+XG4gICAgICAgICAgICAgICAgICA8cD7liqDovb3kuK0uLi48L3A+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICkgOiBjb3Vyc2VMaXN0Lmxlbmd0aCA9PT0gMCA/IChcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvdXJzZS1saXN0LWVtcHR5XCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvdXJzZS1saXN0LWVtcHR5LWljb25cIj5cbiAgICAgICAgICAgICAgICAgICAgPEJvb2tPcGVuIGNsYXNzTmFtZT1cInctMTIgaC0xMiB0ZXh0LWdyYXktMzAwXCIgLz5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cImNvdXJzZS1saXN0LWVtcHR5LXRpdGxlXCI+5pqC5peg6K++5pe2PC9oMz5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cImNvdXJzZS1saXN0LWVtcHR5LWRlc2NyaXB0aW9uXCI+XG4gICAgICAgICAgICAgICAgICAgIOeCueWHu+WPs+S4iuinkueahCArIOaMiemSrua3u+WKoOesrOS4gOS4quivvuaXtlxuICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXthZGROZXdDb3Vyc2V9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImNvdXJzZS1saXN0LWVtcHR5LWJ0blwiXG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIDxQbHVzIGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPlxuICAgICAgICAgICAgICAgICAgICDmt7vliqDor77ml7ZcbiAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgIGNvdXJzZUxpc3QubWFwKChjb3Vyc2UpID0+IChcbiAgICAgICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICAgICAga2V5PXtjb3Vyc2UuaWR9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGNvdXJzZS1saXN0LWl0ZW0gJHtzZWxlY3RlZENvdXJzZUlkID09PSBjb3Vyc2UuaWQgPyAnYWN0aXZlJyA6ICcnfWB9XG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNob3dDb3Vyc2VQYW5lbChjb3Vyc2UuaWQpfVxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvdXJzZS1saXN0LWl0ZW0tY29udGVudFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImNvdXJzZS1saXN0LWl0ZW0tdGV4dFwiPntjb3Vyc2UudGl0bGV9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17YGNvdXJzZS1zdGF0dXMtYmFkZ2UgJHtjb3Vyc2Uuc3RhdHVzID09PSAxID8gJ3B1Ymxpc2hlZCcgOiAnZHJhZnQnfWB9PlxuICAgICAgICAgICAgICAgICAgICAgICAge2NvdXJzZS5zdGF0dXMgPT09IDEgPyAn5bey5Y+R5biDJyA6ICfmnKrlj5HluIMnfVxuICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgZS5zdG9wUHJvcGFnYXRpb24oKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIHNob3dEZWxldGVDb25maXJtKGNvdXJzZS5pZCk7XG4gICAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJjb3Vyc2UtbGlzdC1pdGVtLWRlbGV0ZVwiXG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICA8WCBjbGFzc05hbWU9XCJ3LTMgaC0zXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICApKVxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7Lyog5Y+z5L6n57yW6L6R5Yy65Z+fICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY291cnNlLWxpc3QtZWRpdC1hcmVhXCI+XG4gICAgICAgICAgICB7cmlnaHRQYW5lbFR5cGUgPT09ICdub25lJyAmJiAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY291cnNlLWVkaXQtZW1wdHlcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvdXJzZS1lZGl0LWVtcHR5LWljb25cIj5cbiAgICAgICAgICAgICAgICAgIDxGaWxlVGV4dCBjbGFzc05hbWU9XCJ3LTE2IGgtMTYgdGV4dC1ncmF5LTMwMFwiIC8+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cImNvdXJzZS1lZGl0LWVtcHR5LXRpdGxlXCI+5peg6K++56iL6K+m5oOFPC9oMz5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJjb3Vyc2UtZWRpdC1lbXB0eS1kZXNjcmlwdGlvblwiPlxuICAgICAgICAgICAgICAgICAg54K55Ye75bem5L6n6K++56iL5oiW6K6+572u5oyJ6ZKu5p+l55yL6K+m5oOFXG4gICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgIHtyaWdodFBhbmVsVHlwZSA9PT0gJ3NldHRpbmdzJyAmJiAoXG4gICAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgICAgey8qIOezu+WIl+ivvueoi+WwgemdoiAqL31cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvdXJzZS1zZXJpZXMtY292ZXJcIj5cbiAgICAgICAgICAgICAgICAgIHtzZXJpZXNDb3ZlckltYWdlID8gKFxuICAgICAgICAgICAgICAgICAgICA8aW1nXG4gICAgICAgICAgICAgICAgICAgICAgc3JjPXtzZXJpZXNDb3ZlckltYWdlfVxuICAgICAgICAgICAgICAgICAgICAgIGFsdD1cIuezu+WIl+ivvueoi+WwgemdolwiXG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiY291cnNlLXNlcmllcy1jb3Zlci1pbWFnZVwiXG4gICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvdXJzZS1zZXJpZXMtY292ZXItcGxhY2Vob2xkZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj7ns7vliJfor77nqIvlsIHpnaI8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgIHsvKiDns7vliJforr7nva7ooajljZUgKi99XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb3Vyc2UtZWRpdC1mb3JtXCI+XG4gICAgICAgICAgICAgICAgICB7Lyog57O75YiX6K++56iL5qCH6aKYICovfVxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb3Vyc2UtZWRpdC1maWVsZFwiPlxuICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiY291cnNlLWVkaXQtbGFiZWxcIj7ns7vliJfor77nqIvmoIfpopg8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2VkaXRpbmdUaXRsZX1cbiAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldEVkaXRpbmdUaXRsZShlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiY291cnNlLWVkaXQtaW5wdXRcIlxuICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwi6K+36L6T5YWl57O75YiX6K++56iL5qCH6aKYXCJcbiAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICB7Lyog6K++56iL5qCH562+ICovfVxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb3Vyc2UtZWRpdC1maWVsZFwiPlxuICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiY291cnNlLWVkaXQtbGFiZWxcIj7or77nqIvmoIfnrb48L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICA8U2VsZWN0XG4gICAgICAgICAgICAgICAgICAgICAgbW9kZT1cIm11bHRpcGxlXCJcbiAgICAgICAgICAgICAgICAgICAgICBzdHlsZT17eyB3aWR0aDogJzEwMCUnIH19XG4gICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLor7fpgInmi6nor77nqIvmoIfnrb5cIlxuICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtzZWxlY3RlZFRhZ3N9XG4gICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9e3NldFNlbGVjdGVkVGFnc31cbiAgICAgICAgICAgICAgICAgICAgICBsb2FkaW5nPXt0YWdzTG9hZGluZ31cbiAgICAgICAgICAgICAgICAgICAgICBvcHRpb25zPXtjb3Vyc2VUYWdzLm1hcCh0YWcgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ/Cfj7fvuI8g5riy5p+T5qCH562+6YCJ6aG5OicsIHRhZyk7XG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICBsYWJlbDogKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIHN0eWxlPXt7IGNvbG9yOiB0YWcuY29sb3IgfX0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7dGFnLm5hbWV9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICApLFxuICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZTogdGFnLmlkXG4gICAgICAgICAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgICAgICAgICAgIH0pfVxuICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICB7Lyog6LCD6K+V5L+h5oGvICovfVxuICAgICAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGZvbnRTaXplOiAnMTJweCcsIGNvbG9yOiAnIzY2NicsIG1hcmdpblRvcDogJzRweCcgfX0+XG4gICAgICAgICAgICAgICAgICAgICAg6LCD6K+VOiDlvZPliY3moIfnrb7mlbDph48ge2NvdXJzZVRhZ3MubGVuZ3RofSwg5Yqg6L2954q25oCBOiB7dGFnc0xvYWRpbmcgPyAn5pivJyA6ICflkKYnfVxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICB7Lyog6K++56iL6aG555uu5oiQ5ZGYICovfVxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb3Vyc2UtZWRpdC1maWVsZFwiPlxuICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiY291cnNlLWVkaXQtbGFiZWxcIj7or77nqIvpobnnm67miJDlkZg8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3Byb2plY3RNZW1iZXJzfVxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0UHJvamVjdE1lbWJlcnMoZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImNvdXJzZS1lZGl0LWlucHV0XCJcbiAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIuivt+i+k+WFpemhueebruaIkOWRmO+8jOWmgu+8muW8oOiAgeW4iOOAgeadjuWKqeaVmeOAgeeOi+WQjOWtplwiXG4gICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC8+XG4gICAgICAgICAgICApfVxuXG4gICAgICAgICAgICB7cmlnaHRQYW5lbFR5cGUgPT09ICdjb3Vyc2UnICYmIGdldFNlbGVjdGVkQ291cnNlKCkgJiYgKFxuICAgICAgICAgICAgICA8PlxuICAgICAgICAgICAgICAgIHsvKiDor77nqIvor6bnu4bnvJbovpHnlYzpnaIgKi99XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb3Vyc2UtZGV0YWlsLWVkaXRcIj5cbiAgICAgICAgICAgICAgICAgIHsvKiDpobbpg6jljLrln5/vvJror77nqIvlsIHpnaLlkozln7rmnKzkv6Hmga8gKi99XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvdXJzZS1kZXRhaWwtdG9wXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY291cnNlLWRldGFpbC1jb3ZlclwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImNvdXJzZS1jb3Zlci11cGxvYWQtYXJlYVwiXG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBkb2N1bWVudC5nZXRFbGVtZW50QnlJZCgnY292ZXItdXBsb2FkLWlucHV0Jyk/LmNsaWNrKCl9XG4gICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAge2NvdXJzZURldGFpbC5jb3ZlckltYWdlIHx8IGdldFNlbGVjdGVkQ291cnNlKCk/LmNvdmVySW1hZ2UgPyAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxpbWdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzcmM9e2NvdXJzZURldGFpbC5jb3ZlckltYWdlIHx8IGdldFNlbGVjdGVkQ291cnNlKCk/LmNvdmVySW1hZ2V9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYWx0PVwi6K++56iL5bCB6Z2iXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJjb3Vyc2UtY292ZXItaW1hZ2VcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb3Vyc2UtY292ZXItcGxhY2Vob2xkZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj7ngrnlh7vkuIrkvKDor77nqIvlsIHpnaI8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgIGlkPVwiY292ZXItdXBsb2FkLWlucHV0XCJcbiAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJmaWxlXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIGFjY2VwdD1cImltYWdlL2pwZWcsaW1hZ2UvanBnLGltYWdlL3BuZyxpbWFnZS9naWZcIlxuICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZUNvdmVyVXBsb2FkfVxuICAgICAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3sgZGlzcGxheTogJ25vbmUnIH19XG4gICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICB7Lyog5LiA6ZSu5LiK6K++5oyJ6ZKuICovfVxuICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm9uZS1jbGljay1jbGFzcy1idG5cIlxuICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAvLyBUT0RPOiDlrp7njrDkuIDplK7kuIror77lip/og71cbiAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ+S4gOmUruS4iuivvuaMiemSruiiq+eCueWHuycpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICBub3RpZmljYXRpb24uaW5mbygn5LiA6ZSu5LiK6K++5Yqf6IO95byA5Y+R5LitLi4uJyk7XG4gICAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgIOS4gOmUruS4iuivvlxuICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb3Vyc2UtZGV0YWlsLWJhc2ljXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb3Vyc2UtZGV0YWlsLWZpZWxkXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8bGFiZWw+6K++56iL5qCH6aKYPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtjb3Vyc2VEZXRhaWwudGl0bGUgfHwgZ2V0U2VsZWN0ZWRDb3Vyc2UoKT8udGl0bGUgfHwgJyd9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNldENvdXJzZURldGFpbChwcmV2ID0+ICh7IC4uLnByZXYsIHRpdGxlOiBlLnRhcmdldC52YWx1ZSB9KSk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdXBkYXRlQ291cnNlVGl0bGUoc2VsZWN0ZWRDb3Vyc2VJZCEsIGUudGFyZ2V0LnZhbHVlKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLor7fovpPlhaXor77nqIvmoIfpophcIlxuICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvdXJzZS1kZXRhaWwtZmllbGRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbD7or77nqIvku4vnu408L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHRleHRhcmVhXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtjb3Vyc2VEZXRhaWwuZGVzY3JpcHRpb24gfHwgZ2V0U2VsZWN0ZWRDb3Vyc2UoKT8uZGVzY3JpcHRpb24gfHwgJyd9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0Q291cnNlRGV0YWlsKHByZXYgPT4gKHsgLi4ucHJldiwgZGVzY3JpcHRpb246IGUudGFyZ2V0LnZhbHVlIH0pKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLor7fovpPlhaXor77nqIvku4vnu41cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICByb3dzPXszfVxuICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgey8qIOivvueoi+i1hOa6kOWMuuWfnyAqL31cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY291cnNlLWRldGFpbC1zZWN0aW9uXCI+XG4gICAgICAgICAgICAgICAgICAgIDxoMz7or77nqIvotYTmupA8L2gzPlxuXG4gICAgICAgICAgICAgICAgICAgIHsvKiDor77nqIvop4bpopEgKi99XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY291cnNlLXJlc291cmNlLWl0ZW1cIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlc291cmNlLWhlYWRlci1yaWdodFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+6K++56iL6KeG6aKRPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cInN3aXRjaFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwiY2hlY2tib3hcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNoZWNrZWQ9e2NvdXJzZURldGFpbC5pc1ZpZGVvRW5hYmxlZH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldENvdXJzZURldGFpbChwcmV2ID0+ICh7IC4uLnByZXYsIGlzVmlkZW9FbmFibGVkOiBlLnRhcmdldC5jaGVja2VkIH0pKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwic2xpZGVyXCI+PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICB7Y291cnNlRGV0YWlsLmlzVmlkZW9FbmFibGVkICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidmlkZW8tY29udGVudC1hcmVhXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHsvKiDmmL7npLrnnJ/lrp7op4bpopHkv6Hmga8gKi99XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtjb3Vyc2VEZXRhaWwuY29udGVudENvbmZpZz8udmlkZW8/LnVybCA/IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInZpZGVvLWluZm8tc2VjdGlvblwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ2aWRlby1wcmV2aWV3XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDx2aWRlb1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInZpZGVvLXRodW1ibmFpbFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29udHJvbHNcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwb3N0ZXI9e2NvdXJzZURldGFpbC5jb3ZlckltYWdlfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNvdXJjZSBzcmM9e2NvdXJzZURldGFpbC5jb250ZW50Q29uZmlnLnZpZGVvLnVybH0gdHlwZT1cInZpZGVvL21wNFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg5oKo55qE5rWP6KeI5Zmo5LiN5pSv5oyB6KeG6aKR5pKt5pS+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvdmlkZW8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidmlkZW8tbmFtZS1jZW50ZXJlZFwiPntjb3Vyc2VEZXRhaWwuY29udGVudENvbmZpZy52aWRlby5uYW1lfTwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9XCJ1cGxvYWQtYnRuLWhvcml6b250YWxcIiBvbkNsaWNrPXt0cmlnZ2VyVmlkZW9VcGxvYWR9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj7ph43mlrDkuIrkvKA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInZpZGVvLXVwbG9hZC1zZWN0aW9uXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInZpZGVvLXBsYWNlaG9sZGVyLWNlbnRlcmVkXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicGxheS1pY29uXCI+4pa2PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b24gY2xhc3NOYW1lPVwidXBsb2FkLWJ0bi1ob3Jpem9udGFsXCIgb25DbGljaz17dHJpZ2dlclZpZGVvVXBsb2FkfT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+5LiK5Lyg6KeG6aKRPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICB7Lyog6K++56iL6ZmE5Lu2ICovfVxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvdXJzZS1yZXNvdXJjZS1pdGVtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZXNvdXJjZS1oZWFkZXItcmlnaHRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPuivvueoi+mZhOS7tjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJzd2l0Y2hcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImNoZWNrYm94XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjaGVja2VkPXtjb3Vyc2VEZXRhaWwuaXNBdHRhY2htZW50RW5hYmxlZH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldENvdXJzZURldGFpbChwcmV2ID0+ICh7IC4uLnByZXYsIGlzQXR0YWNobWVudEVuYWJsZWQ6IGUudGFyZ2V0LmNoZWNrZWQgfSkpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJzbGlkZXJcIj48L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIHtjb3Vyc2VEZXRhaWwuaXNBdHRhY2htZW50RW5hYmxlZCAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImF0dGFjaG1lbnQtY29udGVudC1hcmVhXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHsvKiDmmL7npLrnnJ/lrp7pmYTku7bkv6Hmga8gKi99XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtjb3Vyc2VEZXRhaWwuY29udGVudENvbmZpZz8uZG9jdW1lbnQ/LnVybCA/IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImF0dGFjaG1lbnQtaW5mby1zZWN0aW9uXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImF0dGFjaG1lbnQtcHJldmlld1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImRvY3VtZW50LWljb25cIj7wn5OEPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYXR0YWNobWVudC1kZXRhaWxzXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhdHRhY2htZW50LW5hbWVcIj57Y291cnNlRGV0YWlsLmNvbnRlbnRDb25maWcuZG9jdW1lbnQubmFtZX08L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b24gY2xhc3NOYW1lPVwidXBsb2FkLWJ0bi1ob3Jpem9udGFsXCIgb25DbGljaz17dHJpZ2dlckF0dGFjaG1lbnRVcGxvYWR9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj7ph43mlrDkuIrkvKA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImF0dGFjaG1lbnQtdXBsb2FkLXNlY3Rpb25cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b24gY2xhc3NOYW1lPVwidXBsb2FkLWJ0bi1ob3Jpem9udGFsXCIgb25DbGljaz17dHJpZ2dlckF0dGFjaG1lbnRVcGxvYWR9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj7kuIrkvKDpmYTku7Y8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgIHsvKiDmlZnlrabpmYTku7YgKi99XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY291cnNlLXJlc291cmNlLWl0ZW1cIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlc291cmNlLWhlYWRlci1zaW1wbGVcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPuaVmeWtpumZhOS7tjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRlYWNoaW5nLW1hdGVyaWFsc1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9XCJhZGQtbWF0ZXJpYWwtYnRuXCIgb25DbGljaz17dHJpZ2dlclRlYWNoaW5nTWF0ZXJpYWxVcGxvYWR9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj4rPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj7kuIrkvKA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtjb3Vyc2VEZXRhaWwudGVhY2hpbmdNYXRlcmlhbHMgJiYgY291cnNlRGV0YWlsLnRlYWNoaW5nTWF0ZXJpYWxzLmxlbmd0aCA+IDAgPyAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNvdXJzZURldGFpbC50ZWFjaGluZ01hdGVyaWFscy5tYXAoKG1hdGVyaWFsLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYga2V5PXtpbmRleH0gY2xhc3NOYW1lPVwibWF0ZXJpYWwtaXRlbVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwibWF0ZXJpYWwtbmFtZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAobWF0ZXJpYWwudXJsKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB3aW5kb3cub3BlbihtYXRlcmlhbC51cmwsICdfYmxhbmsnKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY3Vyc29yOiBtYXRlcmlhbC51cmwgPyAncG9pbnRlcicgOiAnZGVmYXVsdCcsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I6IG1hdGVyaWFsLnVybCA/ICcjMTg5MGZmJyA6ICdpbmhlcml0JyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0ZXh0RGVjb3JhdGlvbjogbWF0ZXJpYWwudXJsID8gJ3VuZGVybGluZScgOiAnbm9uZSdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU9e21hdGVyaWFsLnVybCA/ICfngrnlh7vkuIvovb3pmYTku7YnIDogbWF0ZXJpYWwubmFtZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg8J+TjiB7bWF0ZXJpYWwubmFtZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicmVtb3ZlLW1hdGVyaWFsLWJ0blwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHJlbW92ZVRlYWNoaW5nTWF0ZXJpYWwoaW5kZXgpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aXRsZT1cIuWIoOmZpOmZhOS7tlwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIMOXXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgKSlcbiAgICAgICAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZW1wdHktbWF0ZXJpYWxzLWhpbnRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBzdHlsZT17eyBjb2xvcjogJyM5OTknLCBmb250U2l6ZTogJzE0cHgnIH19PuaaguaXoOaVmeWtpumZhOS7tjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICB7Lyog6K++56iL5YaF5a655Yy65Z+fICovfVxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb3Vyc2UtZGV0YWlsLXNlY3Rpb25cIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzZWN0aW9uLWhlYWRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxoMz7or77nqIvlhoXlrrk8L2gzPlxuICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImFkZC1jb250ZW50LXNlY3Rpb24tYnRuXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2FkZFRlYWNoaW5nSW5mb0l0ZW19XG4gICAgICAgICAgICAgICAgICAgICAgICB0aXRsZT1cIua3u+WKoOivvueoi+WGheWuuVwiXG4gICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgKyDmt7vliqDor77nqIvlhoXlrrlcbiAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY291cnNlLWNvbnRlbnQtYXJlYVwiPlxuICAgICAgICAgICAgICAgICAgICAgIHtjb3Vyc2VEZXRhaWwudGVhY2hpbmdJbmZvICYmIGNvdXJzZURldGFpbC50ZWFjaGluZ0luZm8ubGVuZ3RoID4gMCA/IChcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvdXJzZURldGFpbC50ZWFjaGluZ0luZm8ubWFwKChpbmZvLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGtleT17aW5kZXh9IGNsYXNzTmFtZT1cInRlYWNoaW5nLWluZm8tY2FyZFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY2FyZC1oZWFkZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImNhcmQtdGl0bGVcIj7or77nqIvlhoXlrrkge2luZGV4ICsgMX08L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInJlbW92ZS1jYXJkLWJ0blwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHJlbW92ZVRlYWNoaW5nSW5mb0l0ZW0oaW5kZXgpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aXRsZT1cIuWIoOmZpOatpOWGheWuuVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIMOXXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNhcmQtY29udGVudFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJpbnB1dC1ncm91cFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8bGFiZWw+5qCH6aKYPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtpbmZvLnRpdGxlfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gdXBkYXRlVGVhY2hpbmdJbmZvVGl0bGUoaW5kZXgsIGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIuivt+i+k+WFpeagh+mimO+8jOWmgu+8muaVmeWtpuebruagh+OAgeaVmeWtpuaWueazleetiVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGl0bGUtaW5wdXRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImlucHV0LWdyb3VwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbD7lhoXlrrk8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8dGV4dGFyZWFcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17aW5mby5jb250ZW50fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gdXBkYXRlVGVhY2hpbmdJbmZvQ29udGVudChpbmRleCwgZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwi6K+36L6T5YWl5YW35L2T5YaF5a6577yM5aSa5Liq5YaF5a656aG55Y+v55So5o2i6KGM5YiG6ZqUXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJjb250ZW50LXRleHRhcmVhXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByb3dzPXs0fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgKSlcbiAgICAgICAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJlbXB0eS1jb250ZW50LWhpbnRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHA+5pqC5peg6K++56iL5YaF5a6577yM54K55Ye75Y+z5LiK6KeS5oyJ6ZKu5re75YqgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgey8qIOmHjeaWsOS4iuivviAqL31cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY291cnNlLWRldGFpbC1zZWN0aW9uXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwib25lLWtleS1zZWN0aW9uXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJvbmUta2V5LWl0ZW1cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPuW8gOWni+S4iuivvjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJzd2l0Y2hcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImNoZWNrYm94XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjaGVja2VkPXtjb3Vyc2VEZXRhaWwuaXNPbmVLZXlPcGVufVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0Q291cnNlRGV0YWlsKHByZXYgPT4gKHsgLi4ucHJldiwgaXNPbmVLZXlPcGVuOiBlLnRhcmdldC5jaGVja2VkIH0pKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwic2xpZGVyXCI+PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICAgIHtjb3Vyc2VEZXRhaWwuaXNPbmVLZXlPcGVuICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwib25lLWtleS1pdGVtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+5YiG6YWN56ev5pyoPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJzd2l0Y2hcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwiY2hlY2tib3hcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjaGVja2VkPXtjb3Vyc2VEZXRhaWwuaXNEaXN0cmlidXRpb25FbmFibGVkfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldENvdXJzZURldGFpbChwcmV2ID0+ICh7IC4uLnByZXYsIGlzRGlzdHJpYnV0aW9uRW5hYmxlZDogZS50YXJnZXQuY2hlY2tlZCB9KSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwic2xpZGVyXCI+PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge2NvdXJzZURldGFpbC5pc0Rpc3RyaWJ1dGlvbkVuYWJsZWQgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJibG9jay10ZW1wbGF0ZS1zZWN0aW9uXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJzZWxlY3QtdGVtcGxhdGUtYnRuXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVPcGVuVGVtcGxhdGVQaWNrZXJ9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICDpgInmi6nnp6/mnKjmqKHmnb9cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic2VsZWN0ZWQtdGVtcGxhdGUtZGlzcGxheVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPntjb3Vyc2VEZXRhaWwuc2VsZWN0ZWRUZW1wbGF0ZS5uYW1lIHx8ICfpgInkuK3nmoTmqKHmnb/lkI3lrZcnfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm9uZS1rZXktaXRlbVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPuWIhumFjeiDvemHjzwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwic3dpdGNoXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImNoZWNrYm94XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2hlY2tlZD17Y291cnNlRGV0YWlsLmlzRGlzdHJpYnV0aW9uV2F0ZXJ9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0Q291cnNlRGV0YWlsKHByZXYgPT4gKHsgLi4ucHJldiwgaXNEaXN0cmlidXRpb25XYXRlcjogZS50YXJnZXQuY2hlY2tlZCB9KSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwic2xpZGVyXCI+PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtjb3Vyc2VEZXRhaWwuaXNEaXN0cmlidXRpb25XYXRlciAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJlbmVyZ3ktaW5wdXQtc2VjdGlvblwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+6ZyA6KaB6IO96YeP77yaPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2NvdXJzZURldGFpbC5yZXF1aXJlZEVuZXJneSB8fCAnJ31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IGhhbmRsZUVuZXJneUNoYW5nZShlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLor7fovpPlhaXpnIDopoHnmoTog73ph4/lgLxcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGVuZXJneS1pbnB1dCAke2VuZXJneUVycm9yID8gJ2Vycm9yJyA6ICcnfWB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtlbmVyZ3lFcnJvciAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZXJyb3ItbWVzc2FnZVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtlbmVyZ3lFcnJvcn1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwib25lLWtleS1pdGVtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+5YiG6YWN5Lu75YqhPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJzd2l0Y2hcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwiY2hlY2tib3hcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjaGVja2VkPXtjb3Vyc2VEZXRhaWwuaXNEaXN0cmlidXRpb25MaW1pdH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRDb3Vyc2VEZXRhaWwocHJldiA9PiAoeyAuLi5wcmV2LCBpc0Rpc3RyaWJ1dGlvbkxpbWl0OiBlLnRhcmdldC5jaGVja2VkIH0pKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJzbGlkZXJcIj48L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgey8qIOS7u+WKoemFjee9ruihqOWNlSAqL31cbiAgICAgICAgICAgICAgICAgICAgICAgICAge2NvdXJzZURldGFpbC5pc0Rpc3RyaWJ1dGlvbkxpbWl0ICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRhc2stY29uZmlnLWZvcm1cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsvKiDku7vliqHlkI3np7DlkozmjIHnu63lpKnmlbAgKi99XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRhc2stY29uZmlnLXJvd1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRhc2stY29uZmlnLWZpZWxkXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGxhYmVsPuS7u+WKoeWQjeensDo8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2NvdXJzZURldGFpbC50YXNrQ29uZmlnLnRhc2tOYW1lfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRDb3Vyc2VEZXRhaWwocHJldiA9PiAoe1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuLi5wcmV2LFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0YXNrQ29uZmlnOiB7IC4uLnByZXYudGFza0NvbmZpZywgdGFza05hbWU6IGUudGFyZ2V0LnZhbHVlIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwi6K+36L6T5YWl5Lu75Yqh5ZCN56ewXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0YXNrLWNvbmZpZy1maWVsZFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbD7ku7vliqHmjIHnu63lpKnmlbA6PC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImR1cmF0aW9uLWlucHV0LXdyYXBwZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtjb3Vyc2VEZXRhaWwudGFza0NvbmZpZy50YXNrRHVyYXRpb259XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dEdXJhdGlvblBpY2tlcih0cnVlKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLor7fpgInmi6nml7bpl7RcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZWFkT25seVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtzaG93RHVyYXRpb25QaWNrZXIgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImR1cmF0aW9uLXBpY2tlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZHVyYXRpb24tb3B0aW9uc1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvbiBvbkNsaWNrPXsoKSA9PiBoYW5kbGVEdXJhdGlvblNlbGVjdCgxLCAnMeWwj+aXticpfT4x5bCP5pe2PC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uIG9uQ2xpY2s9eygpID0+IGhhbmRsZUR1cmF0aW9uU2VsZWN0KDYsICc25bCP5pe2Jyl9PjblsI/ml7Y8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b24gb25DbGljaz17KCkgPT4gaGFuZGxlRHVyYXRpb25TZWxlY3QoMTIsICcxMuWwj+aXticpfT4xMuWwj+aXtjwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvbiBvbkNsaWNrPXsoKSA9PiBoYW5kbGVEdXJhdGlvblNlbGVjdCgyNCwgJzHlpKknKX0+MeWkqTwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvbiBvbkNsaWNrPXsoKSA9PiBoYW5kbGVEdXJhdGlvblNlbGVjdCgxNjgsICc35aSpJyl9PjflpKk8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJjbG9zZS1waWNrZXJcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2hvd0R1cmF0aW9uUGlja2VyKGZhbHNlKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICDDl1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsvKiDku7vliqHmj4/ov7AgKi99XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRhc2stY29uZmlnLWZpZWxkIHRhc2stY29uZmlnLWZ1bGxcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGxhYmVsPuS7u+WKoeaPj+i/sDo8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8dGV4dGFyZWFcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Y291cnNlRGV0YWlsLnRhc2tDb25maWcudGFza0Rlc2NyaXB0aW9ufVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0Q291cnNlRGV0YWlsKHByZXYgPT4gKHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC4uLnByZXYsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0YXNrQ29uZmlnOiB7IC4uLnByZXYudGFza0NvbmZpZywgdGFza0Rlc2NyaXB0aW9uOiBlLnRhcmdldC52YWx1ZSB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSkpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwi6K+36L6T5YWl5Lu75Yqh5o+P6L+wXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByb3dzPXs0fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsvKiDku7vliqHoh6ror4TpobkgKi99XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRhc2stY29uZmlnLWZpZWxkIHRhc2stY29uZmlnLWZ1bGxcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGxhYmVsPuS7u+WKoeiHquivhOmhuTogPHNwYW4gY2xhc3NOYW1lPVwiaXRlbS1udW1iZXJcIj57Y291cnNlRGV0YWlsLnRhc2tDb25maWcuc2VsZkFzc2Vzc21lbnRJdGVtcy5sZW5ndGh9PC9zcGFuPjwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtjb3Vyc2VEZXRhaWwudGFza0NvbmZpZy5zZWxmQXNzZXNzbWVudEl0ZW1zLm1hcCgoaXRlbSwgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGtleT17aW5kZXh9IGNsYXNzTmFtZT1cInNlbGYtYXNzZXNzbWVudC1pdGVtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17aXRlbX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgbmV3SXRlbXMgPSBbLi4uY291cnNlRGV0YWlsLnRhc2tDb25maWcuc2VsZkFzc2Vzc21lbnRJdGVtc107XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbmV3SXRlbXNbaW5kZXhdID0gZS50YXJnZXQudmFsdWU7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0Q291cnNlRGV0YWlsKHByZXYgPT4gKHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC4uLnByZXYsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0YXNrQ29uZmlnOiB7IC4uLnByZXYudGFza0NvbmZpZywgc2VsZkFzc2Vzc21lbnRJdGVtczogbmV3SXRlbXMgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLor7fovpPlhaXoh6ror4TpobnlhoXlrrlcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJhZGQtYXNzZXNzbWVudC1idG5cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldENvdXJzZURldGFpbChwcmV2ID0+ICh7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuLi5wcmV2LFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGFza0NvbmZpZzoge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuLi5wcmV2LnRhc2tDb25maWcsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNlbGZBc3Nlc3NtZW50SXRlbXM6IFsuLi5wcmV2LnRhc2tDb25maWcuc2VsZkFzc2Vzc21lbnRJdGVtcywgJyddXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSkpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgK1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Lyog5Lu75Yqh5Y+C6ICD5L2c5ZOBICovfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0YXNrLWNvbmZpZy1maWVsZCB0YXNrLWNvbmZpZy1mdWxsXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbD7ku7vliqHlj4LogIPkvZzlk4E6PC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWZlcmVuY2Utd29ya3Mtc2VjdGlvblwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwid29ya3Mtc2VjdGlvblwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwiaGVscC10ZXh0XCI+6YCJ5oup5L2c5ZOB5L2c5Li65Lu75Yqh5Y+C6ICD6LWE5paZ77yI5Y+v5aSa6YCJ77yJPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJyZWxhdGl2ZSB3b3Jrcy1zY3JvbGwtd3JhcHBlclwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWluSGVpZ2h0OiAnMjAwcHgnLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGN1cnNvcjogJ2dyYWInLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHVzZXJTZWxlY3Q6ICdub25lJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7bG9hZGluZ1dvcmtzID8gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibG9hZGluZy1jb250YWluZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibG9hZGluZy1zcGlubmVyXCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj7liqDovb3kuK0uLi48L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkgOiB3b3Jrcy5sZW5ndGggPiAwID8gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cIndvcmtzLWhvcml6b250YWwtc2Nyb2xsXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uV2hlZWw9e2hhbmRsZVdoZWVsfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHt3b3Jrcy5tYXAoKHdvcmspID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGtleT17d29yay5pZH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2B3b3JrLWNhcmQgJHtzZWxlY3RlZFdvcmtJZHMuaW5jbHVkZXMod29yay5pZCkgPyAnc2VsZWN0ZWQnIDogJyd9YH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVXb3JrU2VsZWN0KHdvcmsuaWQpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3b3JrLWltYWdlXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7d29yay5jb3ZlckltYWdlIHx8IHdvcmsuc2NyZWVuU2hvdEltYWdlID8gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aW1nXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3JjPXt3b3JrLmNvdmVySW1hZ2UgfHwgd29yay5zY3JlZW5TaG90SW1hZ2V9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYWx0PXt3b3JrLnRpdGxlfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uRXJyb3I9eyhlKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCB0YXJnZXQgPSBlLmN1cnJlbnRUYXJnZXQgYXMgSFRNTEltYWdlRWxlbWVudDtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRhcmdldC5zdHlsZS5kaXNwbGF5ID0gJ25vbmUnO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgbmV4dEVsZW1lbnQgPSB0YXJnZXQubmV4dEVsZW1lbnRTaWJsaW5nIGFzIEhUTUxFbGVtZW50O1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKG5leHRFbGVtZW50KSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG5leHRFbGVtZW50LnN0eWxlLmRpc3BsYXkgPSAnZmxleCc7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkgOiBudWxsfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3b3JrLXBsYWNlaG9sZGVyXCIgc3R5bGU9e3sgZGlzcGxheTogd29yay5jb3ZlckltYWdlIHx8IHdvcmsuc2NyZWVuU2hvdEltYWdlID8gJ25vbmUnIDogJ2ZsZXgnIH19PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICDkvZzlk4FcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwid29yay10aXRsZVwiPnt3b3JrLnRpdGxlIHx8IHdvcmsubmFtZSB8fCB3b3JrLndvcmtOYW1lIHx8ICfmnKrlkb3lkI3kvZzlk4EnfTwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtzZWxlY3RlZFdvcmtJZHMuaW5jbHVkZXMod29yay5pZCkgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzZWxlY3RlZC1pbmRpY2F0b3JcIj7inJM8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZW1wdHktcGxhY2Vob2xkZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZW1wdHktdGV4dFwiPuaaguaXoOS9nOWTgTwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsvKiDku7vliqHlj4LogIPotYTmupAgKi99XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRhc2stY29uZmlnLWZpZWxkIHRhc2stY29uZmlnLWZ1bGxcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGxhYmVsPuS7u+WKoeWPguiAg+i1hOa6kDo8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlZmVyZW5jZS1yZXNvdXJjZXMtc2VjdGlvblwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVmZXJlbmNlLXJlc291cmNlcy1ncmlkXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ1cGxvYWQtcmVzb3VyY2UtYnRuXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIOinpuWPkeaWh+S7tuS4iuS8oFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGlucHV0ID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnaW5wdXQnKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpbnB1dC50eXBlID0gJ2ZpbGUnO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlucHV0LmFjY2VwdCA9ICcucGRmLC5kb2MsLmRvY3gsLnBwdCwucHB0eCwueGxzLC54bHN4LC50eHQsLmpwZywucG5nLC5naWYnO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlucHV0Lm9uY2hhbmdlID0gKGUpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGZpbGUgPSAoZS50YXJnZXQgYXMgSFRNTElucHV0RWxlbWVudCkuZmlsZXM/LlswXTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmIChmaWxlKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNldENvdXJzZURldGFpbChwcmV2ID0+ICh7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4ucHJldixcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0YXNrQ29uZmlnOiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuLi5wcmV2LnRhc2tDb25maWcsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZWZlcmVuY2VSZXNvdXJjZXM6IFtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4ucHJldi50YXNrQ29uZmlnLnJlZmVyZW5jZVJlc291cmNlcyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgeyB0eXBlOiAnZmlsZScsIG5hbWU6IGZpbGUubmFtZSB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBdXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KSk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpbnB1dC5jbGljaygpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8UGx1cyBzaXplPXsyNH0gLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg5LiK5LygXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtjb3Vyc2VEZXRhaWwudGFza0NvbmZpZy5yZWZlcmVuY2VSZXNvdXJjZXMubWFwKChyZXNvdXJjZSwgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBrZXk9e2luZGV4fSBjbGFzc05hbWU9XCJyZWZlcmVuY2UtcmVzb3VyY2UtaXRlbVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPntyZXNvdXJjZS5uYW1lfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInJlbW92ZS1yZXNvdXJjZS1idG5cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBuZXdSZXNvdXJjZXMgPSBjb3Vyc2VEZXRhaWwudGFza0NvbmZpZy5yZWZlcmVuY2VSZXNvdXJjZXMuZmlsdGVyKChfLCBpKSA9PiBpICE9PSBpbmRleCk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNldENvdXJzZURldGFpbChwcmV2ID0+ICh7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4ucHJldixcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0YXNrQ29uZmlnOiB7IC4uLnByZXYudGFza0NvbmZpZywgcmVmZXJlbmNlUmVzb3VyY2VzOiBuZXdSZXNvdXJjZXMgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KSk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIMOXXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICA8Lz5cbiAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8Lz5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiDlupXpg6jmjInpkq4gKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY291cnNlLWxpc3QtZm9vdGVyXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb3Vyc2UtbGlzdC1mb290ZXItbGVmdFwiPlxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVQdWJsaXNofVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJjb3Vyc2UtbGlzdC1idG4gY291cnNlLWxpc3QtYnRuLXB1Ymxpc2hcIlxuICAgICAgICAgICAgICBkaXNhYmxlZD17Y291cnNlTGlzdC5sZW5ndGggPT09IDAgfHwgaXNQdWJsaXNoaW5nU2VyaWVzIHx8IHNlcmllc1N0YXR1cyA9PT0gMX1cbiAgICAgICAgICAgICAgdGl0bGU9e1xuICAgICAgICAgICAgICAgIHNlcmllc1N0YXR1cyA9PT0gMVxuICAgICAgICAgICAgICAgICAgPyAn57O75YiX6K++56iL5bey5Y+R5biDJ1xuICAgICAgICAgICAgICAgICAgOiBjb3Vyc2VMaXN0Lmxlbmd0aCA9PT0gMFxuICAgICAgICAgICAgICAgICAgICA/ICflj5HluIPlpLHotKXvvJror77nqIvns7vliJfkuK3oh7PlsJHpnIDopoHljIXlkKvkuIDkuKror77nqIsnXG4gICAgICAgICAgICAgICAgICAgIDogaXNQdWJsaXNoaW5nU2VyaWVzXG4gICAgICAgICAgICAgICAgICAgICAgPyAn5q2j5Zyo5Y+R5biD57O75YiX6K++56iLLi4uJ1xuICAgICAgICAgICAgICAgICAgICAgIDogJ+WPkeW4g+ezu+WIl+ivvueoiydcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICB7c2VyaWVzU3RhdHVzID09PSAxXG4gICAgICAgICAgICAgICAgPyAn5bey5Y+R5biDJ1xuICAgICAgICAgICAgICAgIDogaXNQdWJsaXNoaW5nU2VyaWVzXG4gICAgICAgICAgICAgICAgICA/ICfmraPlnKjlj5HluIMuLi4nXG4gICAgICAgICAgICAgICAgICA6ICflj5HluIPns7vliJfor77nqIsnXG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY291cnNlLWxpc3QtZm9vdGVyLXJpZ2h0XCI+XG4gICAgICAgICAgICA8YnV0dG9uIG9uQ2xpY2s9e2hhbmRsZUV4aXRFZGl0fSBjbGFzc05hbWU9XCJjb3Vyc2UtbGlzdC1idG4gY291cnNlLWxpc3QtYnRuLWV4aXRcIj5cbiAgICAgICAgICAgICAg6YCA5Ye657yW6L6R5qih5byPXG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlUHVibGlzaENvdXJzZX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiY291cnNlLWxpc3QtYnRuIGNvdXJzZS1saXN0LWJ0bi1wdWJsaXNoLWNvdXJzZVwiXG4gICAgICAgICAgICAgIGRpc2FibGVkPXshc2VsZWN0ZWRDb3Vyc2VJZCB8fCBjb3Vyc2VMaXN0LmZpbmQoYyA9PiBjLmlkID09PSBzZWxlY3RlZENvdXJzZUlkKT8uc3RhdHVzID09PSAxIHx8IGlzUHVibGlzaGluZ31cbiAgICAgICAgICAgICAgdGl0bGU9e1xuICAgICAgICAgICAgICAgICFzZWxlY3RlZENvdXJzZUlkXG4gICAgICAgICAgICAgICAgICA/ICfor7flhYjpgInmi6nopoHlj5HluIPnmoTor77nqIsnXG4gICAgICAgICAgICAgICAgICA6IGNvdXJzZUxpc3QuZmluZChjID0+IGMuaWQgPT09IHNlbGVjdGVkQ291cnNlSWQpPy5zdGF0dXMgPT09IDFcbiAgICAgICAgICAgICAgICAgICAgPyAn6K+l6K++56iL5bey5Y+R5biDJ1xuICAgICAgICAgICAgICAgICAgICA6IGlzUHVibGlzaGluZ1xuICAgICAgICAgICAgICAgICAgICAgID8gJ+ato+WcqOWPkeW4g+ivvueoiy4uLidcbiAgICAgICAgICAgICAgICAgICAgICA6ICflj5HluIPpgInkuK3nmoTor77nqIsnXG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAge2lzUHVibGlzaGluZyA/ICfmraPlnKjlj5HluIMuLi4nIDogJ+WPkeW4g+ivvueoiyd9XG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlU2F2ZX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiY291cnNlLWxpc3QtYnRuIGNvdXJzZS1saXN0LWJ0bi1zYXZlXCJcbiAgICAgICAgICAgICAgZGlzYWJsZWQ9e3VwbG9hZGluZ0ZpbGVzLnNpemUgPiAwIHx8IGlzQ3JlYXRpbmcgfHwgY291cnNlTGlzdC5sZW5ndGggPT09IDB9XG4gICAgICAgICAgICAgIHRpdGxlPXtcbiAgICAgICAgICAgICAgICBjb3Vyc2VMaXN0Lmxlbmd0aCA9PT0gMFxuICAgICAgICAgICAgICAgICAgPyAn6K+35YWI5re75Yqg6K++56iL5YaF5a65J1xuICAgICAgICAgICAgICAgICAgOiB1cGxvYWRpbmdGaWxlcy5zaXplID4gMFxuICAgICAgICAgICAgICAgICAgICA/ICfmnInmlofku7bmraPlnKjkuIrkvKDkuK3vvIzor7fnrYnlvoXkuIrkvKDlrozmiJAnXG4gICAgICAgICAgICAgICAgICAgIDogaXNDcmVhdGluZ1xuICAgICAgICAgICAgICAgICAgICAgID8gKGNvdXJzZUxpc3Quc29tZShjID0+IGMuaWQgPiAxMDAwMDAwKSA/ICfmraPlnKjliJvlu7ror77nqIsuLi4nIDogJ+ato+WcqOS/neWtmOivvueoiy4uLicpXG4gICAgICAgICAgICAgICAgICAgICAgOiAoY291cnNlTGlzdC5zb21lKGMgPT4gYy5pZCA+IDEwMDAwMDApID8gJ+WIm+W7uuivvueoiycgOiAn5L+d5a2Y6K++56iLJylcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICB7dXBsb2FkaW5nRmlsZXMuc2l6ZSA+IDBcbiAgICAgICAgICAgICAgICA/ICfkuIrkvKDkuK0uLi4nXG4gICAgICAgICAgICAgICAgOiBpc0NyZWF0aW5nXG4gICAgICAgICAgICAgICAgICA/IChjb3Vyc2VMaXN0LnNvbWUoYyA9PiBjLmlkID4gMTAwMDAwMCkgPyAn5q2j5Zyo5Yib5bu6Li4uJyA6ICfmraPlnKjkv53lrZguLi4nKVxuICAgICAgICAgICAgICAgICAgOiAoY291cnNlTGlzdC5zb21lKGMgPT4gYy5pZCA+IDEwMDAwMDApID8gJ+WIm+W7uuivvueoiycgOiAn5L+d5a2Y6K++56iLJylcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiDliKDpmaTnoa7orqTlvLnnqpcgKi99XG4gICAgICB7ZGVsZXRlQ29uZmlybVZpc2libGUgJiYgKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1vZGFsLW92ZXJsYXlcIiBvbkNsaWNrPXtjYW5jZWxEZWxldGV9PlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZGVsZXRlLWNvbmZpcm0tbW9kYWxcIiBvbkNsaWNrPXsoZSkgPT4gZS5zdG9wUHJvcGFnYXRpb24oKX0+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImRlbGV0ZS1jb25maXJtLWhlYWRlclwiPlxuICAgICAgICAgICAgICA8aDM+56Gu6K6k5Yig6ZmkPC9oMz5cbiAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2NhbmNlbERlbGV0ZX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJjbG9zZS1idG5cIlxuICAgICAgICAgICAgICAgIGRpc2FibGVkPXtpc0RlbGV0aW5nfVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPFggY2xhc3NOYW1lPVwidy00IGgtNFwiIC8+XG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImRlbGV0ZS1jb25maXJtLWNvbnRlbnRcIj5cbiAgICAgICAgICAgICAgPHA+XG4gICAgICAgICAgICAgICAge2lzRGVsZXRpbmdcbiAgICAgICAgICAgICAgICAgID8gJ+ato+WcqOWIoOmZpOivvueoi++8jOivt+eojeWAmS4uLidcbiAgICAgICAgICAgICAgICAgIDogJ+ehruWumuimgeWIoOmZpOi/meS4quivvueoi+WQl++8n+WIoOmZpOWQjuaXoOazleaBouWkjeOAgidcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAge2lzRGVsZXRpbmcgJiYgKFxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZGVsZXRlLWxvYWRpbmdcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibG9hZGluZy1zcGlubmVyXCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZGVsZXRlLWNvbmZpcm0tZm9vdGVyXCI+XG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXtjYW5jZWxEZWxldGV9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiY2FuY2VsLWJ0blwiXG4gICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2lzRGVsZXRpbmd9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICDlj5bmtohcbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXtjb25maXJtRGVsZXRlQ291cnNlfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImNvbmZpcm0tYnRuXCJcbiAgICAgICAgICAgICAgICBkaXNhYmxlZD17aXNEZWxldGluZ31cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIHtpc0RlbGV0aW5nID8gJ+ato+WcqOWIoOmZpC4uLicgOiAn56Gu6K6k5Yig6ZmkJ31cbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuXG4gICAgICB7Lyog5qih5p2/6YCJ5oup5by556qXICovfVxuICAgICAgPFRlbXBsYXRlUGlja2VyTW9kYWxcbiAgICAgICAgaXNPcGVuPXtpc1RlbXBsYXRlUGlja2VyT3Blbn1cbiAgICAgICAgb25DbG9zZT17KCkgPT4gc2V0SXNUZW1wbGF0ZVBpY2tlck9wZW4oZmFsc2UpfVxuICAgICAgICBvblRlbXBsYXRlU2VsZWN0PXtoYW5kbGVUZW1wbGF0ZVNlbGVjdH1cbiAgICAgIC8+XG4gICAgPC9kaXY+XG4gICk7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBDb3Vyc2VMaXN0RWRpdE1vZGFsO1xuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJ1c2VDYWxsYmFjayIsIlgiLCJTZXR0aW5ncyIsIlBsdXMiLCJCb29rT3BlbiIsIkZpbGVUZXh0IiwiY291cnNlTWFuYWdlbWVudEFwaSIsImNvdXJzZUFwaSIsInVwbG9hZEFwaSIsIndvcmtzQXBpIiwiU2VsZWN0IiwiR2V0Tm90aWZpY2F0aW9uIiwiVGVtcGxhdGVQaWNrZXJNb2RhbCIsImZldGNoU2VyaWVzRGV0YWlsIiwic2VyaWVzSWQiLCJjb25zb2xlIiwibG9nIiwicmVzcG9uc2UiLCJnZXRTZXJpZXNEZXRhaWwiLCJkYXRhIiwiZmV0Y2hDb3Vyc2VMaXN0IiwiZ2V0TWFuYWdlbWVudFNlcmllc0NvdXJzZXMiLCJwYWdlIiwicGFnZVNpemUiLCJmZXRjaENvdXJzZURldGFpbCIsImNvdXJzZUlkIiwiZ2V0Q291cnNlTWFya2V0cGxhY2VEZXRhaWwiLCJmZXRjaENvdXJzZVRhZ3MiLCJyZXN1bHQiLCJnZXRDb3Vyc2VUYWdzIiwic3RhdHVzIiwiZXJyb3IiLCJDb3Vyc2VMaXN0RWRpdE1vZGFsIiwiaXNWaXNpYmxlIiwib25DbG9zZSIsIm9uU2F2ZSIsInNlcmllc1RpdGxlIiwic2VyaWVzQ292ZXJJbWFnZSIsImdldFNlbGVjdGVkQ291cnNlIiwiY291cnNlRGV0YWlsIiwiY291cnNlTGlzdCIsIm5vdGlmaWNhdGlvbiIsInNldENvdXJzZUxpc3QiLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsInJpZ2h0UGFuZWxUeXBlIiwic2V0UmlnaHRQYW5lbFR5cGUiLCJzZWxlY3RlZENvdXJzZUlkIiwic2V0U2VsZWN0ZWRDb3Vyc2VJZCIsInVwbG9hZGluZ0ZpbGVzIiwic2V0VXBsb2FkaW5nRmlsZXMiLCJTZXQiLCJpc1B1Ymxpc2hpbmciLCJzZXRJc1B1Ymxpc2hpbmciLCJpc0NyZWF0aW5nIiwic2V0SXNDcmVhdGluZyIsImlzUHVibGlzaGluZ1NlcmllcyIsInNldElzUHVibGlzaGluZ1NlcmllcyIsInNlcmllc1N0YXR1cyIsInNldFNlcmllc1N0YXR1cyIsImRlbGV0ZUNvbmZpcm1WaXNpYmxlIiwic2V0RGVsZXRlQ29uZmlybVZpc2libGUiLCJjb3Vyc2VUb0RlbGV0ZSIsInNldENvdXJzZVRvRGVsZXRlIiwiaXNEZWxldGluZyIsInNldElzRGVsZXRpbmciLCJpc1RlbXBsYXRlUGlja2VyT3BlbiIsInNldElzVGVtcGxhdGVQaWNrZXJPcGVuIiwiZWRpdGluZ1RpdGxlIiwic2V0RWRpdGluZ1RpdGxlIiwiY291cnNlR29hbHMiLCJzZXRDb3Vyc2VHb2FscyIsImNvdXJzZU9iamVjdGl2ZXMiLCJzZXRDb3Vyc2VPYmplY3RpdmVzIiwicHJvamVjdE1lbWJlcnMiLCJzZXRQcm9qZWN0TWVtYmVycyIsIndvcmtzIiwic2V0V29ya3MiLCJsb2FkaW5nV29ya3MiLCJzZXRMb2FkaW5nV29ya3MiLCJzZWxlY3RlZFdvcmtJZHMiLCJzZXRTZWxlY3RlZFdvcmtJZHMiLCJlbmVyZ3lFcnJvciIsInNldEVuZXJneUVycm9yIiwic2hvd0R1cmF0aW9uUGlja2VyIiwic2V0U2hvd0R1cmF0aW9uUGlja2VyIiwiY291cnNlVGFncyIsInNldENvdXJzZVRhZ3MiLCJzZWxlY3RlZFRhZ3MiLCJzZXRTZWxlY3RlZFRhZ3MiLCJ0YWdzTG9hZGluZyIsInNldFRhZ3NMb2FkaW5nIiwic2V0Q291cnNlRGV0YWlsIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsImNvdmVySW1hZ2UiLCJ2aWRlb1VybCIsInZpZGVvTmFtZSIsImlzVmlkZW9FbmFibGVkIiwiYXR0YWNobWVudFVybCIsImF0dGFjaG1lbnROYW1lIiwiaXNBdHRhY2htZW50RW5hYmxlZCIsInRlYWNoaW5nTWF0ZXJpYWxzIiwidGVhY2hpbmdJbmZvIiwiY29udGVudENvbmZpZyIsImhhc1ZpZGVvIiwiaGFzRG9jdW1lbnQiLCJoYXNBdWRpbyIsInZpZGVvIiwidXJsIiwibmFtZSIsImRvY3VtZW50IiwiYXVkaW8iLCJjb3Vyc2VDb250ZW50IiwidG9waWMiLCJjb250ZW50IiwiaXNPbmVLZXlPcGVuIiwiaXNEaXN0cmlidXRpb25FbmFibGVkIiwiZGlzdHJpYnV0aW9uUmV3YXJkIiwic2VsZWN0ZWRUZW1wbGF0ZSIsImlkIiwiaXNEaXN0cmlidXRpb25XYXRlciIsInJlcXVpcmVkRW5lcmd5IiwiZW5lcmd5QW1vdW50IiwiaXNEaXN0cmlidXRpb25MaW1pdCIsImRpc3RyaWJ1dGlvbkNvbmRpdGlvbnMiLCJpbnZpdGVDb3VudCIsInRhc2tDb3VudCIsImV4cGVyaWVuY2UiLCJpc0Rpc3RyaWJ1dGlvblRpbWUiLCJkaXN0cmlidXRpb25UaW1lQ29uZGl0aW9ucyIsInN0YXJ0VGltZSIsImVuZFRpbWUiLCJkaXN0cmlidXRpb25NYXRlcmlhbHMiLCJ0YXNrQ29uZmlnIiwidGFza05hbWUiLCJ0YXNrRHVyYXRpb24iLCJ0YXNrRGVzY3JpcHRpb24iLCJzZWxmQXNzZXNzbWVudEl0ZW1zIiwicmVmZXJlbmNlV29ya3MiLCJyZWZlcmVuY2VSZXNvdXJjZXMiLCJ0b2tlbiIsImxvY2FsU3RvcmFnZSIsImdldEl0ZW0iLCJsb2FkQ291cnNlTGlzdCIsImxvYWRDb3Vyc2VUYWdzIiwibG9hZFNlcmllc0RldGFpbCIsImxvYWRVc2VyV29ya3MiLCJjb2RlIiwiY291cnNlcyIsImxpc3QiLCJtYXAiLCJjIiwidHlwZSIsInRvdGFsIiwibGVuZ3RoIiwiZHJhZnQiLCJmaWx0ZXIiLCJwdWJsaXNoZWQiLCJtZXNzYWdlIiwic2VyaWVzRGF0YSIsInVzZXJJZCIsImdldFRlYWNoZXJXb3JrcyIsIk51bWJlciIsIndvcmtzTGlzdCIsIkFycmF5IiwiaXNBcnJheSIsImhhbmRsZVdvcmtTZWxlY3QiLCJ3b3JrSWQiLCJpbmNsdWRlcyIsInByZXYiLCJoYW5kbGVXaGVlbCIsImUiLCJjb250YWluZXIiLCJjdXJyZW50VGFyZ2V0Iiwic2Nyb2xsV2lkdGgiLCJjbGllbnRXaWR0aCIsInByZXZlbnREZWZhdWx0Iiwic2Nyb2xsTGVmdCIsImRlbHRhWSIsImhhbmRsZUVuZXJneUNoYW5nZSIsInZhbHVlIiwibnVtVmFsdWUiLCJwYXJzZUZsb2F0IiwiaXNOYU4iLCJoYW5kbGVEdXJhdGlvblNlbGVjdCIsImhvdXJzIiwibGFiZWwiLCJoYW5kbGVDbGlja091dHNpZGUiLCJldmVudCIsInRhcmdldCIsImNsb3Nlc3QiLCJhZGRFdmVudExpc3RlbmVyIiwicmVtb3ZlRXZlbnRMaXN0ZW5lciIsInRhZ3MiLCJmb3JFYWNoIiwidGFnIiwiaW5kZXgiLCJoYXNUYWciLCJpZFR5cGUiLCJuYW1lVHlwZSIsIm5hbWVWYWx1ZSIsIm5hbWVOb3RFbXB0eSIsInRyaW0iLCJ2YWxpZFRhZ3MiLCJpc1ZhbGlkIiwid2FybiIsInN0YXR1c1RleHQiLCJhZGROZXdDb3Vyc2UiLCJuZXdDb3Vyc2UiLCJEYXRlIiwibm93Iiwib3JkZXJJbmRleCIsInN0YXR1c0xhYmVsIiwidmlkZW9EdXJhdGlvbiIsInZpZGVvRHVyYXRpb25MYWJlbCIsImZpcnN0VGVhY2hpbmdUaXRsZSIsInJlc291cmNlc0NvdW50IiwiY3JlYXRlZEF0IiwidG9JU09TdHJpbmciLCJ1cGRhdGVkQXQiLCJzaG93Q291cnNlUGFuZWwiLCJzaG93RGVsZXRlQ29uZmlybSIsImNvbmZpcm1EZWxldGVDb3Vyc2UiLCJkZWxldGVDb3Vyc2UiLCJjb3Vyc2UiLCJzdWNjZXNzIiwiY2FuY2VsRGVsZXRlIiwidXBkYXRlQ291cnNlVGl0bGUiLCJuZXdUaXRsZSIsImhhbmRsZUNvdmVyVXBsb2FkIiwiZmlsZSIsImZpbGVzIiwiYWxsb3dlZFR5cGVzIiwic2l6ZSIsImFkZCIsInByZXZpZXdVcmwiLCJVUkwiLCJjcmVhdGVPYmplY3RVUkwiLCJpbWFnZVVybCIsInVwbG9hZFRvT3NzIiwiYWxlcnQiLCJuZXdTZXQiLCJkZWxldGUiLCJoYW5kbGVWaWRlb1VwbG9hZCIsInRyaWdnZXJWaWRlb1VwbG9hZCIsImlucHV0IiwiY3JlYXRlRWxlbWVudCIsImFjY2VwdCIsIm9uY2hhbmdlIiwiY2xpY2siLCJoYW5kbGVBdHRhY2htZW50VXBsb2FkIiwiZG9jdW1lbnRVcmwiLCJ0cmlnZ2VyQXR0YWNobWVudFVwbG9hZCIsImhhbmRsZVRlYWNoaW5nTWF0ZXJpYWxVcGxvYWQiLCJtYXRlcmlhbFVybCIsIm5ld01hdGVyaWFsIiwiZnJvbSIsIml0ZW0iLCJzdGFydHNXaXRoIiwidHJpZ2dlclRlYWNoaW5nTWF0ZXJpYWxVcGxvYWQiLCJyZW1vdmVUZWFjaGluZ01hdGVyaWFsIiwiXyIsImkiLCJhZGRUZWFjaGluZ0luZm9JdGVtIiwicmVtb3ZlVGVhY2hpbmdJbmZvSXRlbSIsInVwZGF0ZVRlYWNoaW5nSW5mb1RpdGxlIiwibmV3VGVhY2hpbmdJbmZvIiwidXBkYXRlVGVhY2hpbmdJbmZvQ29udGVudCIsImNvbnZlcnRUZWFjaGluZ0luZm9Gb3JBUEkiLCJpbmZvIiwic3BsaXQiLCJsaW5lIiwiY2hlY2tGb3JCbG9iVXJscyIsImlzc3VlcyIsInB1c2giLCJhZGRpdGlvbmFsUmVzb3VyY2VzIiwicmVzb3VyY2UiLCJzYXZlQ291cnNlIiwiRXJyb3IiLCJibG9iSXNzdWVzIiwiam9pbiIsImNvdXJzZURhdGEiLCJjcmVhdGVDb3Vyc2UiLCJ1cGRhdGVDb3Vyc2UiLCJoYW5kbGVQdWJsaXNoQ291cnNlIiwic2VsZWN0ZWRDb3Vyc2UiLCJmaW5kIiwiY291cnNlSWRUb1B1Ymxpc2giLCJwdWJsaXNoQ291cnNlIiwiaGFuZGxlU2F2ZSIsInVwZGF0ZWRDb3Vyc2UiLCJtYXRlcmlhbCIsInNhdmVkQ291cnNlUmVzdWx0IiwicmVhbENvdXJzZUlkIiwidGFza1RlbXBsYXRlRGF0YSIsImR1cmF0aW9uRGF5cyIsInBhcnNlSW50IiwiYXR0YWNobWVudHMiLCJ3b3JrSWRzU3RyIiwic2VxdWVuY2UiLCJjcmVhdGVDb3Vyc2VUYXNrVGVtcGxhdGUiLCJzZXR0aW5nc0RhdGEiLCJ0ZW1wbGF0ZUlkIiwicmVxdWlyZWRQb2ludHMiLCJhdXRvQ3JlYXRlVGFza3MiLCJzZXRDb3Vyc2VTZXR0aW5ncyIsInNldHRpbmdzRXJyb3IiLCJoYW5kbGVQdWJsaXNoIiwicHVibGlzaENvdXJzZVNlcmllcyIsInB1Ymxpc2hEYXRhIiwic3VjY2Vzc01lc3NhZ2UiLCJwdWJsaXNoU3RhdHMiLCJzdGF0cyIsInRvdGFsQ291cnNlcyIsInB1Ymxpc2hlZENvdXJzZXMiLCJ2aWRlb0NvdXJzZUNvdW50IiwiZG9jdW1lbnRDb3Vyc2VDb3VudCIsInRvdGFsUmVzb3VyY2VzQ291bnQiLCJ0b3RhbFZpZGVvRHVyYXRpb24iLCJkdXJhdGlvbk1pbnV0ZXMiLCJNYXRoIiwicm91bmQiLCJlcnJvck1lc3NhZ2UiLCJoYW5kbGVFeGl0RWRpdCIsInNob3dTZXR0aW5nc1BhbmVsIiwiaGFuZGxlVGVtcGxhdGVTZWxlY3QiLCJ0ZW1wbGF0ZSIsInRlbXBsYXRlTmFtZSIsImhhbmRsZU9wZW5UZW1wbGF0ZVBpY2tlciIsInJlcyIsImNvdXJzZURldGFpbERhdGEiLCJtYXBwZWRUZWFjaGluZ0luZm8iLCJkaXYiLCJjbGFzc05hbWUiLCJoMiIsImJ1dHRvbiIsIm9uQ2xpY2siLCJwIiwiaDMiLCJzcGFuIiwic3RvcFByb3BhZ2F0aW9uIiwiaW1nIiwic3JjIiwiYWx0Iiwib25DaGFuZ2UiLCJwbGFjZWhvbGRlciIsIm1vZGUiLCJzdHlsZSIsIndpZHRoIiwib3B0aW9ucyIsImNvbG9yIiwiZm9udFNpemUiLCJtYXJnaW5Ub3AiLCJnZXRFbGVtZW50QnlJZCIsImRpc3BsYXkiLCJ0ZXh0YXJlYSIsInJvd3MiLCJjaGVja2VkIiwiY29udHJvbHMiLCJwb3N0ZXIiLCJzb3VyY2UiLCJ3aW5kb3ciLCJvcGVuIiwiY3Vyc29yIiwidGV4dERlY29yYXRpb24iLCJyZWFkT25seSIsIm5ld0l0ZW1zIiwibWluSGVpZ2h0IiwidXNlclNlbGVjdCIsIm9uV2hlZWwiLCJ3b3JrIiwic2NyZWVuU2hvdEltYWdlIiwib25FcnJvciIsIm5leHRFbGVtZW50IiwibmV4dEVsZW1lbnRTaWJsaW5nIiwid29ya05hbWUiLCJuZXdSZXNvdXJjZXMiLCJkaXNhYmxlZCIsInNvbWUiLCJpc09wZW4iLCJvblRlbXBsYXRlU2VsZWN0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/workbench/components/CourseListEditModal.tsx\n"));

/***/ })

});