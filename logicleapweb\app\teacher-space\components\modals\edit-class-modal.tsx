import { Modal, Form, Input, Button } from 'antd';
import { useEffect, useState } from 'react';

interface EditClassModalProps {
  visible: boolean;
  onCancel: () => void;
  onOk: (values: { className: string }) => Promise<void>;
  initialValues?: {
    className: string;
  };
}

export const EditClassModal: React.FC<EditClassModalProps> = ({
  visible,
  onCancel,
  onOk,
  initialValues
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  // 只在Modal打开时设置一次初始值
  useEffect(() => {
    if (visible && initialValues) {
      form.setFieldsValue(initialValues);
    }
    if (!visible) {
      setLoading(false); // 关闭弹窗时重置loading状态
    }
  }, [visible]);  // 只依赖visible，避免initialValues变化时重置表单

  // 处理表单提交
  const handleSubmit = async (values: { className: string }) => {
    if (loading) return; // 防止重复提交

    setLoading(true);
    try {
      await onOk(values);
    } catch (error) {
      console.error('提交失败:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title="编辑班级"
      open={visible}
      onCancel={() => {
        onCancel();
        form.resetFields();
      }}
      footer={null}
      centered
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
      >
        <Form.Item
          label="班级名称"
          name="className"
          rules={[{ required: true, message: '请输入班级名称' }]}
        >
          <Input
            placeholder="请输入班级名称"
            maxLength={8}
            showCount
            disabled={loading}
          />
        </Form.Item>

        <Form.Item className="mb-0 text-right">
          <Button
            type="default"
            className="mr-2"
            disabled={loading}
            onClick={() => {
              onCancel();
              form.resetFields();
            }}
          >
            取消
          </Button>
          <Button
            type="primary"
            htmlType="submit"
            loading={loading}
          >
            确定
          </Button>
        </Form.Item>
      </Form>
    </Modal>
  );
}; 