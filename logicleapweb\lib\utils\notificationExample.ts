import NotificationUtil from './notification';

/**
 * 通知工具类使用示例
 */
export function showNotificationExamples() {
  // 显示成功消息
  NotificationUtil.success('操作成功完成');
  
  // 显示错误消息
  NotificationUtil.error('发生错误，请重试');
  
  // 显示警告消息
  NotificationUtil.warning('请注意，这是一个警告');
  
  // 显示信息消息
  NotificationUtil.info('这是一条提示信息');
  
  // 使用自定义类型和时长
  NotificationUtil.show('自定义消息', 'success', 5000); // 显示5秒
}

/**
 * 在API调用中使用示例
 */
export async function apiCallExample() {
  try {
    // 模拟API调用
    const response = await fetch('/api/example');
    const data = await response.json();
    
    if (data.success) {
      NotificationUtil.success('数据获取成功');
      return data;
    } else {
      NotificationUtil.error(data.message || '获取数据失败');
      return null;
    }
  } catch (error) {
    NotificationUtil.error('请求失败，请检查网络连接');
    console.error('API调用错误:', error);
    return null;
  }
}

/**
 * 在表单提交中使用示例
 */
export function handleFormSubmit(formData: any) {
  // 表单验证
  if (!formData.name) {
    NotificationUtil.warning('请填写姓名');
    return false;
  }
  
  // 提交表单逻辑...
  console.log('提交表单:', formData);
  
  // 提交成功
  NotificationUtil.success('表单提交成功');
  return true;
} 