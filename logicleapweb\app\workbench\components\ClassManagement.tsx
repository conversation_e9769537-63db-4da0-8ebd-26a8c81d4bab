'use client';

import React from 'react';
import { Settings, Users, Plus } from 'lucide-react';
import './ClassManagement.css';

interface ClassInfo {
  id: number;
  className: string;
  schoolId: number;
  teacherId: number;
  studentCount: number;
  isAssistant?: boolean;
}

interface School {
  id: number;
  schoolName: string;
  province?: string;
  city?: string;
  district?: string;
  createTime?: string;
  updateTime?: string;
}

interface ClassManagementProps {
  selectedSchool?: School | null;
  userInfo?: {
    id: number;
    nickName: string;
    [key: string]: any;
  };
  classes?: ClassInfo[];
  classesLoading?: boolean;
  classesError?: string | null;
  onCloseDropdown?: () => void;
  onClassClick?: (classInfo: ClassInfo) => void;
  onAddClass?: () => void;
}

const ClassManagement: React.FC<ClassManagementProps> = ({
  selectedSchool,
  userInfo,
  classes: propClasses = [],
  classesLoading: propClassesLoading = false,
  classesError: propClassesError = null,
  onCloseDropdown,
  onClassClick,
  onAddClass
}) => {
  // 使用传递的props数据，而不是本地状态
  const classes = propClasses;
  const loading = propClassesLoading;
  const error = propClassesError;

  // 数据现在从props传递，不需要本地获取逻辑

  // 如果没有选中学校，显示提示
  if (!selectedSchool) {
    return (
      <div className="class-management-container">
        <div className="class-management-content">
          <div className="no-school-selected">
            <Users size={48} className="no-school-icon" />
            <h3>请选择学校</h3>
            <p>请在左侧班级管理菜单中选择一个学校来查看您管理的班级</p>
          </div>
        </div>
      </div>
    );
  }

  // 处理点击事件，但不关闭左侧下拉菜单（保持下拉菜单打开状态）
  const handleContentClick = () => {
    // 在班级管理页面中，不关闭下拉菜单，让用户可以方便地切换学校
    // 注释掉原来的关闭逻辑
    // const event = new CustomEvent('closeDropdown');
    // document.dispatchEvent(event);
  };

  return (
    <div className="class-management-container" onClick={handleContentClick}>
      <div className="class-management-content">
        {/* 页面标题 */}
        <div className="class-management-header">
          <h2>班级管理</h2>
          <button className="add-class-btn" onClick={onAddClass}>
            <Plus size={18} />
            添加班级
          </button>
        </div>

        {/* 加载状态 */}
        {loading && (
          <div className="loading-container">
            <div className="loading-spinner"></div>
            <p>正在加载班级信息...</p>
          </div>
        )}

        {/* 错误状态 */}
        {error && (
          <div className="error-container">
            <p className="error-message">{error}</p>
          </div>
        )}

        {/* 班级列表 */}
        {!loading && !error && (
          <div className="classes-grid">
            {classes.length > 0 ? (
              classes.map((classInfo) => (
                <div
                  key={classInfo.id}
                  className="class-card"
                  onClick={() => onClassClick?.(classInfo)}
                  style={{ cursor: 'pointer' }}
                >
                  <div className="class-card-header">
                    <div className="school-tag">
                      {selectedSchool.schoolName}
                    </div>
                    <Settings className="settings-icon" size={16} />
                  </div>

                  <div className="class-card-content">
                    <h3 className="class-name">{classInfo.className}</h3>

                    <div className="student-count-section">
                      <div className="student-count-number">
                        {classInfo.studentCount}
                      </div>
                      <div className="student-count-label">
                        班级学生数
                      </div>
                    </div>
                  </div>

                  {classInfo.isAssistant && (
                    <div className="assistant-badge">
                      助教
                    </div>
                  )}
                </div>
              ))
            ) : (
              <div className="no-classes">
                <Users size={48} className="no-classes-icon" />
                <h3>暂无班级</h3>
                <p>您在 {selectedSchool.schoolName} 还没有管理任何班级</p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default ClassManagement;
