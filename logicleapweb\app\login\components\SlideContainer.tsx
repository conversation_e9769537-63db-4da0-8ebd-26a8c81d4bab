'use client'

import React, { useState, useEffect } from 'react';

interface SlideContainerProps {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
  slideDirection?: 'right-to-left' | 'left-to-right';
  beforeClose?: () => void; // 添加关闭前的回调函数
}

const SlideContainer: React.FC<SlideContainerProps> = ({
  isOpen,
  onClose,
  children,
  slideDirection = 'right-to-left',
  beforeClose
}) => {
  const [isAnimating, setIsAnimating] = useState(false);
  const [shouldRender, setShouldRender] = useState(false);

  // 处理打开动画
  useEffect(() => {
    if (isOpen) {
      setShouldRender(true);
      setIsAnimating(true);
    } else if (shouldRender) {
      // 开始关闭动画
      setIsAnimating(false);
      // 动画结束后移除组件
      const timer = setTimeout(() => {
        setShouldRender(false);
        // if (beforeClose) beforeClose();
      }, 300); // 动画持续时间
      return () => clearTimeout(timer);
    }
  }, [isOpen, shouldRender, beforeClose]);

  const handleClose = () => {
    onClose();
  };

  if (!shouldRender) return null;

  // 根据滑动方向设置类名
  const contentClassName = isAnimating
    ? slideDirection === 'right-to-left'
      ? 'slide-in-right'
      : 'slide-in-left'
    : slideDirection === 'right-to-left'
      ? 'slide-out-left'
      : 'slide-out-right';

  return (
    <div className="slide-container" >
      <div 
        className={`slide-content flex items-center justify-center ${contentClassName} min-h-[510px] min-w-[900px] `}
        onClick={(e) => e.stopPropagation()} // 防止点击内容区域关闭
      >
        {children}
      </div>
    </div>
  );
};

export default SlideContainer; 