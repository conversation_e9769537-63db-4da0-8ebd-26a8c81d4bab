import request from '../request';

// 支付API基础路径
const PAYMENT_BASE_URL = '/v1/payment';

export type PaymentChannel = 'wechatpay' | 'alipay' | 'unionpay';
export type PaymentMode = 'qrcode' | 'h5' | 'app';

export interface CreatePaymentRequest {
  userId: string;
  amount: number;
  subject: string;
  description?: string;
  channel: PaymentChannel;
  paymentMode: PaymentMode;
  clientIp: string;
}

export interface CreatePaymentResponse {
  success: boolean;
  orderNo?: string;
  qrCode?: string;
  payUrl?: string;
  errorMessage?: string;
  code?: number;
  msg?: string;
  data?: {
    orderNo: string;
    qrCode: string;
    extraData?: {
      expireTime: number;
    };
  };
}

export interface QueryPaymentResponse {
  success: boolean;
  status: 'pending' | 'success' | 'failed';
  orderNo: string;
  amount: number;
  errorMessage?: string;
}

// 充值套餐接口
export interface RechargePackage {
  id: string;
  title: string;
  amount: number;
  bonus: number;
  description: string;
  popular?: boolean;
}

export interface RechargeRecord {
  id: string;
  userId: string;
  packageId: string;
  amount: number;
  bonus: number;
  status: 'pending' | 'success' | 'failed';
  paymentMethod: string;
  orderNo: string;
  createTime: string;
  completeTime?: string;
}

export const paymentApi = {
  // 创建支付订单
  async createPayment(data: CreatePaymentRequest): Promise<CreatePaymentResponse> {
    try {
      const response = await request.post(`${PAYMENT_BASE_URL}/create`, data);
      const result = response.data;

      // 处理后端返回的数据格式
      if (result.code === 200 && result.data) {
        return {
          success: true,
          orderNo: result.data.orderNo,
          qrCode: result.data.qrCode,
          ...result
        };
      } else {
        return {
          success: false,
          errorMessage: result.msg || '创建支付订单失败'
        };
      }
    } catch (error) {
      console.error('创建支付订单失败:', error);
      return {
        success: false,
        errorMessage: '创建支付订单失败'
      };
    }
  },

  // 查询支付状态
  async queryPayment(orderNo: string): Promise<QueryPaymentResponse> {
    try {
      const response = await request.get(`${PAYMENT_BASE_URL}/query/${orderNo}`);
      const result = response.data;

      // 处理后端返回的数据格式
      if (result.code === 200 && result.data) {
        return {
          success: true,
          status: result.data.status,
          orderNo: result.data.orderNo,
          amount: result.data.amount,
        };
      } else {
        return {
          success: false,
          status: 'failed',
          orderNo,
          amount: 0,
          errorMessage: result.msg || '查询支付状态失败'
        };
      }
    } catch (error) {
      console.error('查询支付状态失败:', error);
      return {
        success: false,
        status: 'failed',
        orderNo,
        amount: 0,
        errorMessage: '查询支付状态失败'
      };
    }
  },

  // 获取充值套餐列表
  async getRechargePackages(): Promise<RechargePackage[]> {
    try {
      const response = await request.get(`${PAYMENT_BASE_URL}/packages`);
      return response.data || [];
    } catch (error) {
      console.error('获取充值套餐失败:', error);
      return [];
    }
  },

  // 获取充值记录
  async getRechargeHistory(userId: string): Promise<RechargeRecord[]> {
    try {
      const response = await request.get(`${PAYMENT_BASE_URL}/history/${userId}`);
      return response.data || [];
    } catch (error) {
      console.error('获取充值记录失败:', error);
      return [];
    }
  },

  // 创建充值订单（专门用于充值）
  async createRechargeOrder(data: {
    userId: string;
    packageId: string;
    amount: number;
    paymentMethod: string;
  }): Promise<CreatePaymentResponse> {
    try {
      const response = await request.post(`${PAYMENT_BASE_URL}/recharge`, data);
      return response.data;
    } catch (error) {
      console.error('创建充值订单失败:', error);
      return {
        success: false,
        errorMessage: '创建充值订单失败'
      };
    }
  }
};
