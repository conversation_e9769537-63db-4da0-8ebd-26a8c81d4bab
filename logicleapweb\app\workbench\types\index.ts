export interface Student {
  id: number;
  userId: number;
  studentNumber: string;
  nickName: string;
  avatarUrl: string;
  roleId?: number;
  totalPoints?: number;
  availablePoints: number;
  schoolId: number;
  classId: number;
  currentTemplate?: {
    templateId: number;
    templateName: string;
    isOfficial?: boolean;
  };
}

export interface Assignment {
  id: number;
  taskId: number;
  studentId: number;
  taskStatus: number;
  submitTime?: Date;
  taskScore?: number;
  feedback?: string;
  workId?: number;
  returnCount?: number;
}

export interface TaskSelfAssessmentItem {
  id: number;
  taskId: number;
  content: string;
  sequence: number;
  scoreSum: number;
  ratedCount: number;
}

export interface Task {
  id: number;
  taskName: string;
  taskDescription: string;
  taskType: number;
  priority: number;
  startDate: Date;
  endDate: Date;
  taskContent: string;
  studentIds?: number[];
  allowLateSubmission: boolean;
  lateSubmissionPolicy?: {
    deductionPerDay: number;
    maxDeductionDays: number;
    minScore: number;
  };
  attachments: Array<{
    name: string;
    url: string;
    type: string;
    size: number;
  }>;
  references?: Array<{
    title: string;
    description: string;
    url: string;
    type: string;
  }>;
  templateId: number;
  workId?: number;
  workIdsStr?: string;
  referenceWork?: {
    id: number;
    title: string;
    description?: string;
    coverImage?: string;
    screenShotImage?: string;
    content?: string;
  };
  assignments?: Assignment[];
  selfAssessmentItems?: TaskSelfAssessmentItem[];
}

export interface Project {
  // 项目相关的类型定义
}

export interface AddStudentForm {
  studentNumber: string;
  nickName: string;
  password: string | undefined;
} 

export interface School {
  id: number;
  province: string;
  city: string;
  district: string;
  schoolName: string;
  stats?: {
    classCount: number;
    studentCount: number;
    teacherCount: number;
  };
  joinTime?: string;
}

export interface Class {
  id: number;
  grade: string;
  className: string;
  studentCount: number;
  isAssistant?: boolean;
  assistantTeacherId?: number;
  teacherId?: number;
}

export interface PermissionTemplate {
  id: number;
  roleId: number;
  userId: number;
  templateName: string;
  templateDescription: string;
  isDefault: boolean;
  createTime: string;
  updateTime: string;
  isOfficial?: boolean;
}

export interface SearchedTeacher {
  id: number;
  nickName: string;
  phone: string;
  avatarUrl?: string;
}

export interface PermissionTemplateDisplay {
  id: number;
  templateName: string;
  templateDescription: string;
  createTime: string;
  isDefault: boolean;
  lastModified?: string;
  isOfficial?: boolean;
}
