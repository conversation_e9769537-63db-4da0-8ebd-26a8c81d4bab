'use client'

import { useState, useCallback } from 'react';
import { RechargeOption, PaymentMethod, PaymentInfo, RechargeRecord } from '../types';
import { paymentApi, PaymentChannel, PaymentMode } from '../../../lib/api/payment';
import { getMyOrders } from '../../../lib/api/package-purchase';
import { useSelector } from 'react-redux';
import { RootState } from '../../../lib/store';

export interface UseRechargeReturn {
  // 状态
  selectedPlan: RechargeOption | null;
  paymentMethod: string;
  paymentInfo: PaymentInfo | null;
  rechargeHistory: RechargeRecord[];
  loading: boolean;
  error: string | null;

  // 操作方法
  selectPlan: (plan: RechargeOption) => void;
  setPaymentMethod: (method: string) => void;
  createPayment: (plan: RechargeOption, method: string) => Promise<PaymentInfo>;
  checkPaymentStatus: (orderId: string) => Promise<'pending' | 'success' | 'failed'>;
  getRechargeHistory: () => Promise<RechargeRecord[]>;
  clearError: () => void;
  reset: () => void;
}

export function useRecharge(): UseRechargeReturn {
  const [selectedPlan, setSelectedPlan] = useState<RechargeOption | null>(null);
  const [paymentMethod, setPaymentMethodState] = useState<string>('wechat');
  const [paymentInfo, setPaymentInfo] = useState<PaymentInfo | null>(null);
  const [rechargeHistory, setRechargeHistory] = useState<RechargeRecord[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 获取用户状态
  const userState = useSelector((state: RootState) => state.user.userState);

  // 获取真实用户ID的函数
  const getRealUserId = useCallback((): string => {
    // 1. 优先从Redux状态获取
    if (userState.userId) {
      return userState.userId.toString();
    }

    // 2. 从localStorage获取用户信息
    const userData = localStorage.getItem('user');
    if (userData) {
      try {
        const parsedUser = JSON.parse(userData);
        if (parsedUser.userId || parsedUser.id) {
          return (parsedUser.userId || parsedUser.id).toString();
        }
      } catch (e) {
        console.error('解析用户信息失败:', e);
      }
    }

    // 3. 从localStorage直接获取userId
    const storedUserId = localStorage.getItem('userId');
    if (storedUserId) {
      return storedUserId;
    }

    // 4. 如果都没有，返回测试用户ID（开发环境）
    console.warn('未找到真实用户ID，使用测试用户ID');
    return 'test_user';
  }, [userState.userId]);

  const selectPlan = useCallback((plan: RechargeOption) => {
    setSelectedPlan(plan);
    setError(null);
  }, []);

  const setPaymentMethod = useCallback((method: string) => {
    setPaymentMethodState(method);
  }, []);

  const createPayment = useCallback(async (plan: RechargeOption, method: string): Promise<PaymentInfo> => {
    setLoading(true);
    setError(null);

    try {
      // 调用真实的支付API
      const channel: PaymentChannel = method === 'wechat' ? 'wechatpay' :
                                     method === 'alipay' ? 'alipay' : 'wechatpay';

      // 获取真实用户ID
      const realUserId = getRealUserId();
      console.log('创建支付订单，用户ID:', realUserId);

      const result = await paymentApi.createPayment({
        userId: realUserId,
        amount: plan.amount,
        subject: `充值 - ${plan.title}`,
        description: `充值套餐：${plan.title}，金额：¥${plan.amount}`,
        channel,
        paymentMode: 'qrcode' as PaymentMode,
        clientIp: '127.0.0.1', // 这里应该获取真实IP
      });

      if (result.success && result.orderNo) {
        const paymentInfo: PaymentInfo = {
          orderId: result.orderNo,
          amount: plan.amount,
          method,
          status: 'pending',
          createTime: new Date(),
          qrCode: result.qrCode, // 从API响应中获取二维码
        };

        setPaymentInfo(paymentInfo);
        return paymentInfo;
      } else {
        throw new Error(result.errorMessage || '创建支付订单失败');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '创建支付订单失败';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [getRealUserId]);

  const checkPaymentStatus = useCallback(async (orderId: string): Promise<'pending' | 'success' | 'failed'> => {
    try {
      // 调用真实的支付状态查询API
      const result = await paymentApi.queryPayment(orderId);

      if (result.success) {
        return result.status;
      } else {
        return 'failed';
      }
    } catch (err) {
      setError('检查支付状态失败');
      return 'failed';
    }
  }, []);

  const getRechargeHistory = useCallback(async (): Promise<RechargeRecord[]> => {
    setLoading(true);
    setError(null);

    try {
      // 调用套餐订单API获取我的订单列表
      const orderData = await getMyOrders(1, 50); // 获取前50条记录

      if (orderData && orderData.data) {
        // 转换套餐订单数据格式为充值记录格式
        const history: RechargeRecord[] = orderData.data.map((order: any) => ({
          id: order.orderNo,
          amount: order.price || order.amount || 0, // 后端返回的是price字段，兼容amount字段
          bonus: 0, // 套餐订单没有赠送积分概念，设为0
          method: 'package', // 标识为套餐购买
          status: order.status === 'paid' ? 'success' :
                 order.status === 'cancelled' ? 'cancelled' : // 保持cancelled状态
                 order.status === 'pending' ? 'pending' : 'failed',
          createTime: new Date(order.createTime),
          completeTime: order.paidTime ? new Date(order.paidTime) : undefined,
          packageName: order.packageName, // 添加套餐名称
        }));

        setRechargeHistory(history);
        return history;
      } else {
        // 如果没有数据，返回空数组
        setRechargeHistory([]);
        return [];
      }
    } catch (err) {
      console.error('获取套餐订单失败:', err);

      // 如果API调用失败，返回模拟数据
      const mockHistory: RechargeRecord[] = [
        {
          id: 'PKG_001',
          amount: 0.01,
          bonus: 0,
          method: 'package',
          status: 'success',
          createTime: new Date(Date.now() - 86400000), // 1天前
          completeTime: new Date(Date.now() - 86400000 + 300000), // 5分钟后完成
          packageName: '超级能量包',
        },
        {
          id: 'PKG_002',
          amount: 100,
          bonus: 0,
          method: 'package',
          status: 'success',
          createTime: new Date(Date.now() - 172800000), // 2天前
          completeTime: new Date(Date.now() - 172800000 + 180000), // 3分钟后完成
          packageName: '月度套餐',
        },
      ];

      setRechargeHistory(mockHistory);
      return mockHistory;
    } finally {
      setLoading(false);
    }
  }, []);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const reset = useCallback(() => {
    setSelectedPlan(null);
    setPaymentMethodState('wechat');
    setPaymentInfo(null);
    setError(null);
  }, []);

  return {
    // 状态
    selectedPlan,
    paymentMethod,
    paymentInfo,
    rechargeHistory,
    loading,
    error,

    // 操作方法
    selectPlan,
    setPaymentMethod,
    createPayment,
    checkPaymentStatus,
    getRechargeHistory,
    clearError,
    reset,
  };
}
