import { QueryInterceptor } from '../interceptor/query-interceptor';
import { QueryMonitorService, SlowQueryRecord, QueryMetrics } from '../service/query-monitor.service';
export declare class DatabaseMonitorController {
    private readonly queryMonitorService;
    private readonly queryInterceptor;
    constructor(queryMonitorService: QueryMonitorService, queryInterceptor: QueryInterceptor);
    getSlowQueries(limit?: number): Promise<{
        code: number;
        message: string;
        data: SlowQueryRecord[] | null;
    }>;
    getQueryMetrics(): Promise<{
        code: number;
        message: string;
        data: (QueryMetrics & {
            activeQueries: number;
        }) | null;
    }>;
    getActiveQueries(): Promise<{
        code: number;
        message: string;
        data: any[];
    }>;
    getConfig(): Promise<{
        code: number;
        message: string;
        data: any;
    }>;
    updateConfig(config: {
        enableDatabaseMonitoring?: boolean;
        lightweightMode?: boolean;
        slowQueryThreshold?: number;
        enableSlowQueryLogging?: boolean;
        enableQueryMetrics?: boolean;
        enableStackTrace?: boolean;
        samplingRate?: number;
        asyncSlowQueryProcessing?: boolean;
        maxSlowQueryRecords?: number;
    }): Promise<{
        code: number;
        message: string;
        data: any;
    }>;
    resetMetrics(): Promise<{
        code: number;
        message: string;
        data: null;
    }>;
    clearSlowQueries(): Promise<{
        code: number;
        message: string;
        data: null;
    }>;
    cleanupTimeoutQueries(body: {
        timeoutMs?: number;
    }): Promise<{
        code: number;
        message: string;
        data: null;
    }>;
    enableMonitoring(): Promise<{
        code: number;
        message: string;
        data: any;
    }>;
    disableMonitoring(): Promise<{
        code: number;
        message: string;
        data: any;
    }>;
    toggleLightweightMode(body: {
        enabled: boolean;
    }): Promise<{
        code: number;
        message: string;
        data: any;
    }>;
    healthCheck(): Promise<{
        code: number;
        message: string;
        data: {
            status: string;
            timestamp: string;
            metrics: QueryMetrics | null;
            activeQueries: number;
            config: any;
        };
    }>;
}
