'use client'

import React, { useState, useEffect } from 'react';
import { Modal, Button, Tag, Form, Input, Popconfirm, Radio, Carousel } from 'antd';
import { InfoCircleOutlined, CheckOutlined, CloseOutlined } from '@ant-design/icons';
import { worksApi } from '@/lib/api/works';
import { selectUserState } from '@/lib/store';
import { useSelector } from 'react-redux';
import Image from 'next/image';
import { activityApi, SubmissionStatus, ActivityType, ContentType } from '@/lib/api/activity';
import { GetNotification } from 'logic-common/dist/components/Notification';

// 引入CarouselArrow组件
const CustomArrow = ({ type, onClick }: { type: 'prev' | 'next', onClick?: React.MouseEventHandler<Element> }) => (
  <div
    className={`custom-arrow ${type}-arrow`}
    onClick={onClick}
    style={{
      position: 'absolute',
      zIndex: 1,
      top: '50%',
      transform: 'translateY(-50%)',
      width: '32px',
      height: '32px',
      backgroundColor: 'rgba(255, 255, 255, 0.8)',
      borderRadius: '12px',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      cursor: 'pointer',
      boxShadow: '0 2px 6px rgba(0, 0, 0, 0.1)',
      ...(type === 'prev' ? { left: '8px' } : { right: '8px' })
    }}
  >
    {type === 'prev' ? (
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
        <polyline points="15 18 9 12 15 6"></polyline>
      </svg>
    ) : (
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
        <polyline points="9 18 15 12 9 6"></polyline>
      </svg>
    )}
  </div>
);

// 图片标签组件
const ImageLabel = ({ text }: { text: string }) => (
  <div style={{
    position: 'absolute',
    bottom: '8px',
    right: '8px',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    color: 'white',
    padding: '2px 8px',
    borderRadius: '12px',
    fontSize: '0.75rem',
  }}>
    {text}
  </div>
);

// 添加描述模态框组件
interface AddDescriptionModalProps {
  visible: boolean;
  workId: number;
  workTitle: string;
  onClose: () => void;
  onSaved: () => void;
}

const AddDescriptionModal = ({
  visible,
  workId,
  workTitle,
  onClose,
  onSaved
}: AddDescriptionModalProps) => {
  const [form] = Form.useForm();
  const [saving, setSaving] = useState(false);
  const notification = GetNotification();

  useEffect(() => {
    if (visible) {
      form.resetFields();
    }
  }, [visible, form]);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setSaving(true);

      // 调用更新作品的API
      const response = await worksApi.updateWork(workId, {
        description: values.description
      });

      if (response?.status === 200 || response?.data?.code === 200) {
        notification.success('作品描述添加成功');
        onSaved();
      } else {
        throw new Error(response?.data?.message || '保存失败');
      }
    } catch (error) {
      console.error('保存描述失败:', error);
      notification.error('保存描述失败，请稍后重试');
    } finally {
      setSaving(false);
    }
  };

  return (
    <Modal
      title={
        <div className="flex items-center gap-2">
          <div className="bg-blue-500 rounded-lg p-1.5 shadow-sm flex items-center justify-center w-6 h-6">
            <span className="text-white text-xs">✏️</span>
          </div>
          <span className="text-sm font-semibold text-blue-700">添加作品描述</span>
        </div>
      }
      open={visible}
      onCancel={onClose}
      centered
      footer={[
        <Button key="cancel" onClick={onClose} className="rounded-full px-4 py-1 h-auto hover:bg-gray-50 font-medium">
          取消
        </Button>,
        <Button
          key="save"
          type="primary"
          loading={saving}
          onClick={handleSubmit}
          className="rounded-full px-4 py-1 h-auto bg-blue-500 hover:bg-blue-600 border-none font-medium"
        >
          保存描述
        </Button>
      ]}
      width={450}
      className="description-modal"
    >
      <div className="py-3">
        <div className="mb-3 bg-blue-50 p-3 rounded-lg border border-blue-100 shadow-sm">
          <div className="font-medium mb-1 text-xs text-blue-700">作品: {workTitle}</div>
          <div className="text-xs text-gray-600 leading-relaxed">
            添加作品描述有助于评委更好地了解您的创作理念和作品亮点
          </div>
        </div>
        <Form form={form} layout="vertical">
          <Form.Item
            name="description"
            rules={[{ required: true, message: '请输入作品描述' }]}
          >
            <Input.TextArea
              rows={3}
              placeholder="请描述您的作品，例如创作灵感、功能特点等..."
              maxLength={200}
              showCount
              className="rounded-lg text-sm"
              style={{ resize: 'none' }}
            />
          </Form.Item>
        </Form>
      </div>
    </Modal>
  );
};

interface ActivitySubmitModalProps {
  visible: boolean;
  activityId: number;
  activityTitle: string;
  activityType?: number; // 添加活动类型属性
  onClose: () => void;
  onSubmitted: () => void;
  onRefreshWorks?: () => void; // 添加刷新作品的回调
}

export default function ActivitySubmitModal({
  visible,
  activityId,
  activityTitle,
  activityType = ActivityType.WORK, // 默认为作品活动
  onClose,
  onSubmitted,
  onRefreshWorks
}: ActivitySubmitModalProps) {
  const [form] = Form.useForm();
  const [selectedWorkId, setSelectedWorkId] = useState<number | null>(null);
  const [submitting, setSubmitting] = useState(false);
  const [works, setWorks] = useState<any[]>([]);
  const userState = useSelector(selectUserState);
  const notification = GetNotification();

  // 添加内容类型选择状态，根据活动类型设置默认值
  const [contentType, setContentType] = useState<number>(
    activityType === ActivityType.IMAGE ? ContentType.IMAGE : ContentType.WORK
  );

  // 添加描述模态框状态
  const [descriptionModalVisible, setDescriptionModalVisible] = useState(false);
  const [selectedWork, setSelectedWork] = useState<any>(null);

  // 用户报名状态
  const [userSubmission, setUserSubmission] = useState<any>(null);
  const [loadingSubmission, setLoadingSubmission] = useState(false);
  const [cancelling, setCancelling] = useState(false);

  // 重置表单状态
  const resetForm = () => {
    setSelectedWorkId(null);
    setSelectedWork(null);
  };

  // 获取内容列表（根据contentType决定获取作品或图片）
  const fetchContentList = async () => {
    try {
      if (contentType === 1) {
        // 获取作品列表
        const { data: response } = await worksApi.getList({
          type: 1, // 图形化作品
          page: 1,
          size: 100
        });

        if (response.code === 200) {
          setWorks(response.data.list || []);
        }
      } else if (contentType === 2) {
        // 获取图片列表，添加userId参数只获取当前用户的图片
        console.log('userState', userState);
        const userId = userState?.userId;
        if (!userId) {
          notification.error('用户未登录');
          return;
        }

        const { data: response } = await worksApi.getAllWorks({
          page: 1,
          size: 100,
          userId // 添加userId参数，确保只获取当前用户的图片
        });
        if (response.code === 200) {
          setWorks(response.data.list || []);
        }
      }
    } catch (error) {
      console.error('获取内容列表失败:', error);
      notification.error('获取内容列表失败');
    }
  };

  // 获取用户的报名状态
  const fetchUserSubmission = async () => {
    if (!activityId || !userState?.isLoggedIn) return;

    try {
      setLoadingSubmission(true);
      const response = await activityApi.getUserSubmissionStatus(activityId);

      if (response?.status === 200 && response.data?.data) {
        // 检查是否已提交
        const submitted = response.data.data.submitted;
        if (submitted) {
          // 获取用户在此活动的提交详情
          const submissionResponse = await activityApi.getActivityWorks(activityId, {
            userId: userState.id,
            status: SubmissionStatus.SUBMITTED // 查找已提交状态的记录
          });

          if (submissionResponse?.status === 200 && submissionResponse.data?.length > 0) {
            const submission = submissionResponse.data[0];
            // 设置内容类型和已选择的作品ID
            setContentType(submission.contentType || 1);
            setSelectedWorkId(submission.workId);
            // 设置用户报名状态
            setUserSubmission({
              id: submission.id,
              status: submission.status,
              activityId: activityId,
              workId: submission.workId,
              contentType: submission.contentType
            });

            // 根据contentType获取对应类型的内容列表
            await fetchContentList();
          }
        } else {
          setUserSubmission(null);
        }
      } else {
        setUserSubmission(null);
      }
    } catch (error) {
      console.error('获取用户报名状态失败:', error);
      setUserSubmission(null);
    } finally {
      setLoadingSubmission(false);
    }
  };

  // 当模态框打开时获取作品列表和用户报名状态
  useEffect(() => {
    if (visible) {
      resetForm();
      fetchUserSubmission();
    }
  }, [visible, activityId, userState]);

  // 当内容类型变更时，获取对应的内容列表
  useEffect(() => {
    if (visible && !userSubmission) {
      fetchContentList();
    }
  }, [contentType, visible, userSubmission]);

  // 当活动类型改变时重置内容类型
  useEffect(() => {
    if (activityType === ActivityType.WORK) {
      setContentType(ContentType.WORK);
    } else if (activityType === ActivityType.IMAGE) {
      setContentType(ContentType.IMAGE);
    }
    // OTHER类型活动允许任意内容类型，保持用户选择
  }, [activityType]);

  // 处理内容类型切换
  const handleContentTypeChange = (e: any) => {
    setContentType(e.target.value);
    // 切换类型时重置选择
    setSelectedWorkId(null);
    setSelectedWork(null);
  };

  // 处理作品选择
  const handleWorkSelect = (work: any) => {
    // 如果用户已报名，不允许选择其他作品
    if (userSubmission && userSubmission.status !== SubmissionStatus.CANCELLED) {
      return;
    }

    if (selectedWorkId === work.id) {
      setSelectedWorkId(null);
      setSelectedWork(null);
    } else {
      setSelectedWorkId(work.id);
      setSelectedWork(work);
    }
  };

  // 描述保存后的回调
  const handleDescriptionSaved = async () => {
    setDescriptionModalVisible(false);
    // 重新获取作品列表，更新描述
    await fetchContentList();
  };

  // 处理取消报名
  const handleCancelSubmission = async () => {
    if (!userSubmission) {
      notification.error('未找到报名记录');
      return;
    }

    try {
      setCancelling(true);
      // 调用取消报名API
      const response = await activityApi.cancelSubmission(userSubmission.id);

      if (response?.status === 200) {
        notification.success('已成功取消报名');
        // 重置状态
        setUserSubmission(null);
        onSubmitted();
        // 调用刷新作品列表的回调
        if (onRefreshWorks) {
          onRefreshWorks();
        }
        onClose();
      } else {
        throw new Error(response?.data?.message || '取消失败');
      }
    } catch (error) {
      console.error('取消报名失败:', error);
      notification.error('取消报名失败，请稍后重试');
    } finally {
      setCancelling(false);
    }
  };

  // 提交报名
  const handleSubmit = async () => {
    if (!selectedWorkId) {
      notification.error('请选择要报名的作品');
      return;
    }

    // 只有作品类型才需要检查描述
    if (contentType === 1) {
      const selectedWork = works.find(work => work.id === selectedWorkId);
      if (!selectedWork?.description) {
        // 只显示警告，不再阻止提交
        notification.warning('建议为作品添加描述，这有助于评委更好地了解您的作品');
      }
    }

    try {
      setSubmitting(true);

      // 调用API提交活动报名，添加contentType
      const response = await activityApi.submitWork({
        activityId,
        workId: selectedWorkId,
        contentType: contentType
      });

      if (response?.status === 200) {
        // notification.success('提交成功');
        onSubmitted();
        // 调用刷新作品列表的回调
        if (onRefreshWorks) {
          onRefreshWorks();
        }
        onClose();
      } else {
        throw new Error(response.data?.message || '提交失败');
      }
    } catch (error) {
      console.error('提交失败:', error);
      notification.error('提交失败，请稍后重试');
    } finally {
      setSubmitting(false);
    }
  };

  // 渲染模态框标题
  const renderModalTitle = () => {
    if (userSubmission && userSubmission.status !== SubmissionStatus.CANCELLED) {
      return (
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <span className="text-3xl">📋</span>
            <span className="text-xl font-semibold text-blue-700">我的报名信息</span>
          </div>
          <Tag color="success" className="rounded-full px-3 text-sm ml-2">已报名</Tag>
        </div>
      );
    }
    return (
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <span className="text-3xl">📝</span>
          <span className="text-xl font-semibold text-blue-700">活动报名</span>
        </div>
      </div>
    );
  };

  // 渲染模态框底部按钮
  const renderModalFooter = () => {
    // 如果正在加载报名状态，显示加载中
    if (loadingSubmission) {
      return [
        <Button key="cancel" onClick={onClose} className="rounded-full px-5 py-1.5 h-auto hover:bg-gray-50 font-medium">
          关闭
        </Button>
      ];
    }

    // 如果用户已报名，显示取消报名按钮
    if (userSubmission && userSubmission.status !== SubmissionStatus.CANCELLED) {
      return [
        <Button key="cancel" onClick={onClose} className="rounded-full px-5 py-1.5 h-auto hover:bg-gray-50 font-medium">
          关闭
        </Button>,
        <Popconfirm
          key="cancelPopconfirm"
          title="确认取消报名"
          description="您确定要取消活动报名吗？取消后可以重新报名。"
          onConfirm={handleCancelSubmission}
          okText="确认取消"
          okButtonProps={{ danger: true, className: "rounded-full" }}
          cancelText="返回"
          cancelButtonProps={{ className: "rounded-full" }}
        >
          <Button
            key="cancelSubmit"
            danger
            loading={cancelling}
            className="rounded-full px-5 py-1.5 h-auto font-medium"
          >
            取消报名
          </Button>
        </Popconfirm>
      ];
    }

    // 默认显示报名按钮
    return [
      <Button key="cancel" onClick={onClose} className="rounded-full px-5 py-1.5 h-auto hover:bg-gray-50 font-medium">
        取消
      </Button>,
      <Button
        key="submit"
        type="primary"
        loading={submitting}
        onClick={handleSubmit}
        className="rounded-full px-5 py-1.5 h-auto bg-blue-500 hover:bg-blue-600 border-none font-medium"
        disabled={!selectedWorkId}
      >
        提交报名
      </Button>
    ];
  };

  // 渲染内容类型选择区域
  const renderContentTypeSelector = () => {
    // 如果用户已报名或已取消，不显示选择器
    if (userSubmission && userSubmission.status !== SubmissionStatus.CANCELLED) {
      return null;
    }

  };

  return (
    <>
      <Modal
        title={renderModalTitle()}
        open={visible}
        onCancel={onClose}
        centered
        footer={renderModalFooter()}
        width={900}
        className="activity-submit-modal"
        modalRender={(modal) => (
          <div style={{
            margin: '2vh 0',
            display: 'flex',
            flexDirection: 'column',
            height: 'fit-content',
            maxHeight: '96vh'
          }}>
            {modal}
          </div>
        )}
        style={{
          maxWidth: '90vw',
          top: 0,
          paddingBottom: 0
        }}
        styles={{
          body: {
            padding: '20px',
            maxHeight: 'calc(96vh - 110px)',
            overflow: 'hidden',
            borderRadius: '16px'
          }
        }}
      >
        <div className="flex gap-4">
          {/* 左侧说明区域 */}
          <div className="w-[30%] flex flex-col" style={{ maxHeight: 'calc(96vh - 180px)' }}>
            {/* 活动报名指南 */}
            <div className="flex-1 bg-blue-50 rounded-xl border border-blue-100 p-4 overflow-y-auto relative">
              <div className="flex items-center gap-2 mb-3">
                <div className="bg-blue-500 rounded-xl p-1.5 shadow-sm flex items-center justify-center w-6 h-6">
                  <svg className="w-3.5 h-3.5 text-white" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 16V12M12 8H12.01M22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                  </svg>
                </div>
                <h3 className="text-blue-700 font-semibold text-sm">活动报名指南</h3>
              </div>

              <div className="space-y-2.5">

                {/* 活动要求 */}
                <div className="bg-white rounded-xl p-2.5 shadow-sm">
                  <div className="flex items-start gap-2">
                    <div className="bg-blue-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs shrink-0 mt-0.5 shadow-sm">1</div>
                    <div>
                      <div className="text-xs font-medium text-blue-700 mb-1">活动要求</div>
                      <div className="text-xs text-gray-600 leading-relaxed">
                        {activityType === ActivityType.WORK && '本活动仅支持提交作品，请从您的作品中选择一个参赛'}
                        {activityType === ActivityType.IMAGE && '本活动仅支持提交图片，请从您的图片中选择一张参赛'}
                        {activityType === ActivityType.OTHER && '请选择要参赛的内容类型及作品，每人限选一个'}
                      </div>
                    </div>
                  </div>
                </div>

                {/* 参赛规则 */}
                <div className="bg-white rounded-xl p-2.5 shadow-sm">
                  <div className="flex items-start gap-2">
                    <div className="bg-blue-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs shrink-0 mt-0.5 shadow-sm">2</div>
                    <div>
                      <div className="text-xs font-medium text-blue-700 mb-1">参赛规则</div>
                      <div className="text-xs text-gray-600">
                        <ul className="space-y-1">
                          <li className="flex items-start">
                            <div className="text-blue-500 mr-1.5 font-bold">•</div>
                            <span>每个用户仅限提交一个作品</span>
                          </li>
                          <li className="flex items-start">
                            <div className="text-blue-500 mr-1.5 font-bold">•</div>
                            <span>提交后可在个人中心查看报名状态</span>
                          </li>
                          <li className="flex items-start">
                            <div className="text-blue-500 mr-1.5 font-bold">•</div>
                            <span>获奖结果将通过站内消息通知</span>
                          </li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>

                {/* 作品提示 */}
                <div className="bg-amber-50 rounded-xl p-2.5 shadow-sm">
                  <div className="flex items-start gap-2">
                    <div className="bg-amber-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs shrink-0 mt-0.5 shadow-sm">3</div>
                    <div>
                      <div className="text-xs font-medium text-amber-700 mb-1">作品提示</div>
                      <div className="text-xs text-amber-800">
                        <ul className="space-y-1">
                          <li className="flex items-start">
                            <div className="text-amber-500 mr-1.5 font-bold">•</div>
                            <span>参赛期间请保持作品公开状态</span>
                          </li>
                          <li className="flex items-start">
                            <div className="text-amber-500 mr-1.5 font-bold">•</div>
                            <span>添加详细的作品描述有助于评委了解</span>
                          </li>
                          <li className="flex items-start">
                            <div className="text-amber-500 mr-1.5 font-bold">•</div>
                            <span>获得更多社区互动提高获奖机会</span>
                          </li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>

                {/* 用户报名状态提示 */}
                {userSubmission && userSubmission.status !== SubmissionStatus.CANCELLED && (
                  <div className="bg-green-50 rounded-xl p-2.5 shadow-sm">
                    <div className="flex items-start gap-2">
                      <div className="bg-green-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs shrink-0 mt-0.5 shadow-sm">
                        ✓
                      </div>
                      <div>
                        <div className="text-xs font-medium text-green-700 mb-1">报名状态</div>
                        <div className="text-xs text-green-600 leading-relaxed">
                          您已成功报名此活动，可随时取消报名并重新选择
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* 右侧作品选择区域 */}
          <div className="w-[70%] flex flex-col" style={{ maxHeight: 'calc(96vh - 180px)' }}>
            <div className="flex-1 bg-indigo-50 rounded-xl border border-indigo-100 p-4 overflow-y-auto relative">
              <div className="flex items-center gap-2 mb-3">
                <div className="bg-indigo-500 rounded-xl p-1.5 shadow-sm flex items-center justify-center w-6 h-6">
                  <span className="text-white text-xs">🎯</span>
                </div>
                <h3 className="text-indigo-700 font-semibold text-sm">
                  {userSubmission && userSubmission.status !== SubmissionStatus.CANCELLED
                    ? "您的参赛内容"
                    : "选择参赛作品"}
                </h3>
              </div>

              {/* 替换内容类型选择 */}
              {renderContentTypeSelector()}

              <div className="space-y-3 pr-1" style={{ maxHeight: 'calc(96vh - 250px)' }}>
                {works.length > 0 ? (
                  works.map(work => {
                    const isSelected = selectedWorkId === work.id;
                    const hasNoDescription = contentType === ContentType.WORK && !work.description;

                    // 如果已报名，只显示已报名的作品
                    if (userSubmission &&
                      userSubmission.status !== SubmissionStatus.CANCELLED &&
                      work.id !== userSubmission.workId) {
                      return null;
                    }

                    return (
                      <div
                        key={work.id}
                        className={`p-3 rounded-xl transition-all duration-300 shadow-sm bg-white
                          ${isSelected
                            ? 'border-indigo-400 bg-indigo-50/80 shadow-md'
                            : 'border border-gray-200 hover:border-indigo-300 hover:shadow'}
                          ${userSubmission && userSubmission.status !== SubmissionStatus.CANCELLED ? '' : 'cursor-pointer'}`}
                        onClick={() => handleWorkSelect(work)}
                      >
                        <div className="flex justify-between items-center mb-2">
                          <div className="flex items-center gap-2">
                            <div className={`w-4 h-4 rounded-full border transition-colors flex items-center justify-center
                              ${isSelected ? 'border-indigo-500 bg-indigo-500' : 'border-gray-300'}`}
                            >
                              {isSelected && (
                                <CheckOutlined className="text-[8px] text-white" />
                              )}
                            </div>
                            <span className={`text-xs font-medium ${isSelected ? 'text-indigo-600' : 'text-gray-600'}`}>
                              {userSubmission && userSubmission.status !== SubmissionStatus.CANCELLED
                                ? '已报名内容'
                                : isSelected ? '已选择' : '点击选择'}
                            </span>
                          </div>

                          <div className="flex gap-1.5">
                            {hasNoDescription && (
                              <Tag color="blue" className="rounded-full text-xs px-2 py-0">
                                建议添加描述
                              </Tag>
                            )}

                            {contentType === ContentType.IMAGE && (
                              <Tag color="purple" className="rounded-full text-xs px-2 py-0">
                                图片
                              </Tag>
                            )}

                            {userSubmission && userSubmission.status !== SubmissionStatus.CANCELLED && (
                              <Tag color="success" className="rounded-full text-xs px-2 py-0">
                                参赛中
                              </Tag>
                            )}
                          </div>
                        </div>

                        <div className="relative mb-2 h-[160px] bg-gray-50 rounded-xl overflow-hidden border border-gray-100">
                          {contentType === ContentType.WORK ? (
                            <Carousel
                              dots={{ className: 'custom-dots !z-[1]' }}
                              arrows
                              prevArrow={<CustomArrow type="prev" />}
                              nextArrow={<CustomArrow type="next" />}
                            >
                              {work.coverImage && (
                                <div className="h-[160px] relative">
                                  <img
                                    src={work.coverImage}
                                    alt={`${work.title} 封面`}
                                    className="w-full h-full object-contain rounded-xl"
                                  />
                                  <ImageLabel text="作品封面" />
                                </div>
                              )}
                              {work.screenShotImage && (
                                <div className="h-[160px] relative">
                                  <img
                                    src={work.screenShotImage}
                                    alt={`${work.title} 截图`}
                                    className="w-full h-full object-contain rounded-xl"
                                  />
                                  <ImageLabel text="作品截图" />
                                </div>
                              )}
                            </Carousel>
                          ) : (
                            <div className="h-[160px] relative">
                              <img
                                src={work.backupImagePath}
                                alt={work.name || "图片"}
                                className="w-full h-full object-contain rounded-xl"
                              />
                            </div>
                          )}
                        </div>

                        <div className="flex justify-between items-start mb-1.5">
                          <h4 className={`text-sm font-medium ${isSelected ? 'text-indigo-700' : 'text-gray-700'}`}>
                            {contentType === ContentType.WORK ? work.title : (work.name || "未命名图片")}
                          </h4>
                          {contentType === ContentType.WORK && (
                            <div className="flex items-center gap-1.5">
                              {!work.description && isSelected && (
                                <Button
                                  type="primary"
                                  size="small"
                                  className="rounded-full px-2 py-0 h-auto text-xs bg-indigo-500 hover:bg-indigo-600 border-none"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    setSelectedWork(work);
                                    setDescriptionModalVisible(true);
                                  }}
                                >
                                  添加描述
                                </Button>
                              )}
                              <Tag color={work.status === 0 ? 'default' : 'success'} className="rounded-full text-xs px-2 py-0">
                                {work.status === 0 ? '未发布' : '已发布'}
                              </Tag>
                            </div>
                          )}
                        </div>

                        {contentType === ContentType.WORK ? (
                          <div className="bg-gray-50 p-1.5 rounded-xl border border-gray-100 max-h-[40px] overflow-hidden">
                            <p className="text-xs text-gray-500 line-clamp-2">
                              {work.description || (
                                <span className="text-orange-500">暂无描述（建议添加描述以便评委更好了解）</span>
                              )}
                            </p>
                          </div>
                        ) : (
                          <div className="bg-gray-50 p-1.5 rounded-xl border border-gray-100 max-h-[40px] overflow-hidden">
                            <p className="text-xs text-gray-500 line-clamp-2">
                              {work.prompt || "图片内容"}
                            </p>
                          </div>
                        )}

                        <div className="text-xs text-gray-400 mt-1.5">
                          创建时间：{new Date(work.createTime).toLocaleString()}
                        </div>
                      </div>
                    );
                  })
                ) : (
                  <div className="flex flex-col items-center justify-center h-[calc(96vh-350px)] text-center bg-white rounded-xl border border-indigo-100 p-8 shadow-sm">
                    <div className="text-4xl mb-4">{contentType === ContentType.WORK ? '🎨' : '📷'}</div>
                    <div className="text-indigo-700 mb-2 font-medium text-base">
                      {contentType === ContentType.WORK ? '您还没有创建任何作品' : '您还没有创作任何图片'}
                    </div>
                    <div className="text-gray-500 mb-6 text-sm max-w-md">
                      {contentType === ContentType.WORK
                        ? '请先创建作品后再参加活动，去创建一个精彩的作品吧！'
                        : '请先在创作页面生成图片后再参加活动，去创建一幅精美的图片吧！'}
                    </div>
                    <Button
                      type="primary"
                      className="rounded-full px-5 bg-indigo-500 border-none h-9 shadow-md"
                      onClick={() => {
                        window.location.href = '/scratch';
                      }}
                    >
                      立即创作
                    </Button>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </Modal>

      {/* 添加描述模态框 */}
      {selectedWork && contentType === ContentType.WORK && (
        <AddDescriptionModal
          visible={descriptionModalVisible}
          workId={selectedWork.id}
          workTitle={selectedWork.title}
          onClose={() => setDescriptionModalVisible(false)}
          onSaved={handleDescriptionSaved}
        />
      )}
    </>
  );
} 