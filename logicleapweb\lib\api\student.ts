/*
 * @Author: Zwww <EMAIL>
 * @Date: 2025-04-30 16:32:51
 * @LastEditors: Zwww <EMAIL>
 * @LastEditTime: 2025-05-09 15:32:22
 * @FilePath: \sourceCode\logicleapweb\lib\api\student.ts
 * @Description: 
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
 */
import request from '../request';

export const studentApi = {
  // 重置学生密码 okok
  resetPassword: (studentIds: number[], password: string) => {
    return request.post('/api/user-auth/student/reset-password', {
      studentIds,
      password
    });
  },
  //  导出学生
  exportStudents: (classId: number) => {
    return request.post('/api/user-student/export', {
      classId
    });
  },
  // ... other APIs ...     okok
  getStudentInfo: (userId: number) => {
    return request.get(`/api/user-student/user/${userId}`);
  },
  // 搜索学生  okok
  searchStudents: (classId: number, keyword: string) => {
    return request.get('/api/user-student/search', {
      params: { classId, keyword }
    });
  },

  // 批量匹配学生信息  涉及class  待定
  matchStudents: (data: {
    students: {
      studentId?: string;
      schoolId?: string;
      classId?: string;
      studentNumber?: string;
    }[]
  }) => {
    return request.post('/app/user/student/match', data);
  },

  // okok
  getCurrentTemplate: (userIds: number[]) => {
    return request.post(`/api/user/srch/templates/batch-current`, {
      userIds
    });
  },

  // 批量获取学生信息（包括模板、积分等所有数据）
  getStudentsBatchInfo: (userIds: number[]) => {
    return request.post('/api/user-student/batch-info', {
      userIds
    });
  },

  // 根据学生ID列表获取学生基本信息（用于任务详情显示真实姓名）
  matchStudentsByIds: (studentIds: number[]) => {
    return request.post('/api/user-student/match-by-ids', {
      studentIds
    });
  }
}; 