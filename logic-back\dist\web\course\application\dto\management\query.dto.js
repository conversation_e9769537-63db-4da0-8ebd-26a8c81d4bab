"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GetSeriesCoursesQueryDto = exports.GetMyCourseSeriesQueryDto = void 0;
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const swagger_1 = require("@nestjs/swagger");
class GetMyCourseSeriesQueryDto {
    page = 1;
    pageSize = 10;
    status;
    keyword;
}
exports.GetMyCourseSeriesQueryDto = GetMyCourseSeriesQueryDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '页码，默认1',
        required: false,
        minimum: 1,
        default: 1,
        example: 1
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(1),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], GetMyCourseSeriesQueryDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '每页数量，默认10',
        required: false,
        minimum: 1,
        maximum: 100,
        default: 10,
        example: 10
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(1),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], GetMyCourseSeriesQueryDto.prototype, "pageSize", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '状态筛选：0=草稿，1=已发布，2=已归档',
        required: false,
        enum: [0, 1, 2],
        example: 0
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], GetMyCourseSeriesQueryDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '搜索关键词',
        required: false,
        maxLength: 100,
        example: 'JavaScript'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], GetMyCourseSeriesQueryDto.prototype, "keyword", void 0);
class GetSeriesCoursesQueryDto {
    page = 1;
    pageSize = 20;
    status;
}
exports.GetSeriesCoursesQueryDto = GetSeriesCoursesQueryDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '页码，默认1',
        required: false,
        minimum: 1,
        default: 1,
        example: 1
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(1),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], GetSeriesCoursesQueryDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '每页数量，默认20',
        required: false,
        minimum: 1,
        maximum: 100,
        default: 20,
        example: 20
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(1),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], GetSeriesCoursesQueryDto.prototype, "pageSize", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '状态筛选：0=草稿，1=已发布，2=已归档',
        required: false,
        enum: [0, 1, 2],
        example: 0
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], GetSeriesCoursesQueryDto.prototype, "status", void 0);
//# sourceMappingURL=query.dto.js.map