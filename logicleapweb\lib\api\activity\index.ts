// 活动相关API统一导出
export { activityApi, type ActivityParams, type ActivityQueryParams } from './activity'
export {
  activityRegistrationApi,
  type SubmitRegistrationData,
  type UploadSignatureData,
  type AddWorksData
} from './registration'
export { eventsTaskApi } from './events-task'
export { activityTagApi, tagApi, type ActivityTagParams } from './tag'
export { activityAuditApi, participationAuditApi } from './audit'

// 常量导出
export * from './constants'

// 活动相关API统一导出

// 类型定义
export interface Activity {
  id: number
  name: string
  description?: string
  startTime: string
  endTime: string
  registrationStartTime: string
  registrationEndTime: string
  maxParticipants?: number
  currentParticipants: number
  bannerImage?: string
  tags?: Array<{ id: number; name: string; color: string }>
  organizer?: string
  location?: string
  requirements?: string
  prizes?: string
  contactInfo?: string
  status: 'draft' | 'published' | 'ongoing' | 'completed' | 'cancelled'
  creatorId: number
  createTime: string
  updateTime: string
  competitionGroups?: string
  registrationForm?: string
  promotionImage?: string
  attachmentFiles?: string
}

export interface ActivityRegistration {
  id: number
  activityId: number
  userId: number
  userName: string
  userPhone: string
  agreementAccepted: boolean
  parentConsentAccepted: boolean
  parentSignaturePath: string
  signatureTime: string
  signatureIp: string
  status: 'pending' | 'approved' | 'rejected' | 'cancelled'
  reviewerId?: number
  reviewTime?: string
  reviewReason?: string
  createTime: string
  updateTime: string
  remark?: string
}

export interface EventsTask {
  id: number
  userId: number
  eventName: string
  startTime: string
  endTime: string
  workId?: number
  workFile?: string
  workDescription?: string
  instructorName?: string
  schoolName?: string
  contactPerson?: string
  contactPhone?: string
  status: 0 | 1 | 2 | 3 // 0-待开始 1-进行中 2-已完成 3-已取消
  creatorId?: number
  createTime: string
  updateTime: string
  remark?: string
}

export interface ActivityStatistics {
  totalActivities: number
  ongoingActivities: number
  completedActivities: number
  totalRegistrations: number
  pendingRegistrations: number
  approvedRegistrations: number
  rejectedRegistrations: number
}

export interface RegistrationStatistics {
  total: number
  pending: number
  approved: number
  rejected: number
  cancelled: number
  dailyStats: Array<{
    date: string
    count: number
  }>
}

export interface TaskStatistics {
  total: number
  pending: number
  inProgress: number
  completed: number
  cancelled: number
  withWork: number
  withFile: number
}
