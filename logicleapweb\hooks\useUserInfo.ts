import { useState, useEffect, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '@/lib/store';
import { setUser } from '@/lib/store';
import { userApi, UserInfo } from '@/lib/api/user';

interface UseUserInfoReturn {
  userInfo: UserInfo | null;
  loading: boolean;
  error: string | null;
  refreshUserInfo: () => Promise<void>;
  updateUserInfo: (updates: Partial<UserInfo>) => Promise<void>;
  updateAvatar: (avatarUrl: string) => Promise<void>;
}

export const useUserInfo = (): UseUserInfoReturn => {
  const dispatch = useDispatch();
  const userState = useSelector((state: RootState) => state.user.userState);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 从Redux状态获取用户信息
  const getUserInfo = useCallback((): UserInfo | null => {
    if (userState.userId) {
      return {
        id: userState.userId,
        gender: userState.gender || 0,
        phone: userState.phone || '',
        nickName: userState.nickName || '',
        avatarUrl: userState.avatarUrl || '',
        introduction: userState.introduction || '',
        createTime: userState.createTime || '',
        roleId: userState.roleId || 0,
        role: userState.role || null,
        roles: userState.roles || []
      } as UserInfo;
    }

    return null;
  }, [userState]);

  // 刷新用户信息
  const refreshUserInfo = useCallback(async () => {
    const currentUser = getUserInfo();
    if (!currentUser?.id) {
      setError('用户未登录');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const response = await userApi.getUserInfo(currentUser.id);
      
      if (response.code === 200 && response.data) {
        // 更新Redux状态
        dispatch(setUser({
          userId: response.data.id,
          nickName: response.data.nickName,
          avatarUrl: response.data.avatarUrl,
          introduction: response.data.introduction,
          gender: response.data.gender,
          phone: response.data.phone,
          createTime: response.data.createTime,
          roleId: response.data.roleId,
          role: response.data.role,
          roles: response.data.roles
        }));
      } else {
        throw new Error(response.message || '获取用户信息失败');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '获取用户信息失败';
      setError(errorMessage);
      console.error('刷新用户信息失败:', err);
    } finally {
      setLoading(false);
    }
  }, [getUserInfo, dispatch]);

  // 更新用户信息
  const updateUserInfo = useCallback(async (updates: Partial<UserInfo>) => {
    const currentUser = getUserInfo();
    if (!currentUser?.id) {
      setError('用户未登录');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const response = await userApi.updateProfile(currentUser.id, {
        id: currentUser.id,
        ...updates
      });

      if (response.data.code === 200) {
        // 更新Redux状态
        dispatch(setUser({
          ...userState,
          ...updates
        }));
      } else {
        throw new Error(response.data.message || '更新用户信息失败');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '更新用户信息失败';
      setError(errorMessage);
      console.error('更新用户信息失败:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [getUserInfo, userState, dispatch]);

  // 更新头像
  const updateAvatar = useCallback(async (avatarUrl: string) => {
    const currentUser = getUserInfo();
    if (!currentUser?.id) {
      setError('用户未登录');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const response = await userApi.updateAvatar(currentUser.id, {
        id: currentUser.id,
        avatarUrl: avatarUrl
      });

      if (response.data.code === 200) {
        // 更新Redux状态
        dispatch(setUser({
          ...userState,
          avatarUrl: avatarUrl
        }));
      } else {
        throw new Error(response.data.message || '更新头像失败');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '更新头像失败';
      setError(errorMessage);
      console.error('更新头像失败:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [getUserInfo, userState, dispatch]);

  return {
    userInfo: getUserInfo(),
    loading,
    error,
    refreshUserInfo,
    updateUserInfo,
    updateAvatar
  };
};
