'use client';

import React, { useState, useEffect } from 'react';
import { Plus, BookOpen, Upload, X, Image } from 'lucide-react';
import CreateSeriesCourseModal from './CreateSeriesCourseModal';
import CourseListEditModal from './CourseListEditModal';
import { courseManagementApi, SeriesCourseResponse } from '@/lib/api/course-management';
import './CourseManagement.css';
import './CourseManagement.css';

interface Course {
  id: number;
  title: string;
  description: string;
  coverImage: string;
  status: 'draft' | 'published' | 'offline';
  statusLabel: string;
  totalCourses: number;
  totalStudents: number;
  createTime: string;
}

const CourseManagement = () => {
  const [isCreateModalVisible, setIsCreateModalVisible] = useState(false);
  const [isSeriesModalVisible, setIsSeriesModalVisible] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    category: '',
    tags: ''
  });

  // 课程数据状态
  const [courses, setCourses] = useState<Course[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 课程列表编辑弹窗状态
  const [isCourseListModalVisible, setIsCourseListModalVisible] = useState(false);
  const [selectedCourse, setSelectedCourse] = useState<Course | null>(null);

  // 获取课程列表
  const fetchCourses = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await courseManagementApi.getMySeries({
        page: 1,
        pageSize: 20
      });

      if (response.code === 200) {
        // 转换API数据格式为本地Course格式
        const coursesData = response.data.list.map((item: SeriesCourseResponse): Course => ({
          id: item.id,
          title: item.title,
          description: item.description,
          coverImage: item.coverImage,
          status: item.status === 0 ? 'draft' : item.status === 1 ? 'published' : 'offline',
          statusLabel: item.statusLabel,
          totalCourses: item.totalCourses,
          totalStudents: item.totalStudents,
          createTime: new Date(item.createdAt).toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit'
          }).replace(/\//g, '.')
        }));

        setCourses(coursesData);
        console.log('课程列表加载成功:', coursesData);
      } else {
        throw new Error(response.message || '获取课程列表失败');
      }
    } catch (error) {
      console.error('获取课程列表失败:', error);
      setError(error instanceof Error ? error.message : '获取课程列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 组件挂载时获取课程列表
  useEffect(() => {
    fetchCourses();
  }, []);

  // 处理创建课程
  const handleCreateCourse = async (e?: React.FormEvent) => {
    if (e) e.preventDefault();

    // 检查标题是否为空
    if (!formData.title.trim()) {
      alert('请输入系列课程标题');
      return;
    }

    try {
      console.log('创建系列课程数据:', formData);

      // 添加新课程到列表
      const newCourse: Course = {
        id: courses.length + 1,
        title: formData.title,
        description: `创建于${new Date().getFullYear()}.${String(new Date().getMonth() + 1).padStart(2, '0')}.${String(new Date().getDate()).padStart(2, '0')}`,
        coverImage: '/api/placeholder/280/160',
        status: 'draft',
        createTime: `${new Date().getFullYear()}.${String(new Date().getMonth() + 1).padStart(2, '0')}.${String(new Date().getDate()).padStart(2, '0')}`
      };

      setCourses(prev => [newCourse, ...prev]);
      alert('系列课程创建成功！');
      setIsCreateModalVisible(false);
      setFormData({ title: '', description: '', category: '', tags: '' });
    } catch (error) {
      console.error('创建系列课程失败:', error);
      alert('创建系列课程失败，请重试');
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // 处理系列课程创建
  const handleCreateSeriesCourse = async (data: any) => {
    try {
      console.log('创建系列课程成功:', data);

      // 刷新课程列表以获取最新数据
      await fetchCourses();
      console.log('课程列表已刷新');
    } catch (error) {
      console.error('刷新课程列表失败:', error);
    }
  };

  // 处理课程卡片点击
  const handleCourseCardClick = (course: Course) => {
    console.log('🎯 点击课程卡片:', course);
    console.log('🔍 传递的seriesId:', course.id);
    setSelectedCourse(course);
    setIsCourseListModalVisible(true);
  };

  // 处理课程列表弹窗关闭
  const handleCourseListModalClose = async () => {
    setIsCourseListModalVisible(false);
    setSelectedCourse(null);

    // 关闭弹窗时刷新课程列表，确保状态同步
    try {
      await fetchCourses();
      console.log('✅ 弹窗关闭后课程列表已刷新');
    } catch (error) {
      console.error('❌ 弹窗关闭后刷新课程列表失败:', error);
    }
  };

  // 处理课程列表保存
  const handleCourseListSave = async (data: any) => {
    console.log('保存课程列表数据:', data);

    try {
      // 刷新课程列表以获取最新数据（包括状态更新）
      await fetchCourses();
      console.log('✅ 课程列表已刷新，状态已更新');
    } catch (error) {
      console.error('❌ 刷新课程列表失败:', error);
    }
  };

  // 获取状态按钮样式和文本
  const getStatusButton = (status: Course['status']) => {
    switch (status) {
      case 'draft':
        return {
          text: '草稿',
          className: 'px-4 py-1 bg-gray-100 text-gray-600 rounded-full text-sm border border-gray-300'
        };
      case 'published':
        return {
          text: '发布',
          className: 'px-4 py-1 bg-blue-100 text-blue-600 rounded-full text-sm border border-blue-300'
        };
      case 'offline':
        return {
          text: '下架',
          className: 'px-4 py-1 bg-orange-100 text-orange-600 rounded-full text-sm border border-orange-300'
        };
    }
  };

  return (
    <div className="h-full bg-gray-50">
      {/* 头部区域 */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          {/* 左上角标题 */}
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-lg flex items-center justify-center">
              <BookOpen className="w-5 h-5 text-white" />
            </div>
            <h1 className="text-2xl font-bold text-gray-900">课程管理</h1>
          </div>

          {/* 右上角创建课程按钮 */}
          <button
            onClick={() => setIsSeriesModalVisible(true)}
            className="create-course-btn"
          >
            <div className="create-course-btn-icon">
              <Plus className="w-3 h-3" />
            </div>
            创建课程
          </button>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="p-6">
        {loading ? (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse">
                <BookOpen className="w-8 h-8 text-gray-400" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">加载中...</h3>
              <p className="text-gray-500">正在获取课程列表</p>
            </div>
          </div>
        ) : error ? (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <BookOpen className="w-8 h-8 text-red-400" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">加载失败</h3>
              <p className="text-gray-500 mb-6">{error}</p>
              <button
                onClick={fetchCourses}
                className="flex items-center gap-2 px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200 font-medium mx-auto"
              >
                重新加载
              </button>
            </div>
          </div>
        ) : courses.length === 0 ? (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <BookOpen className="w-8 h-8 text-gray-400" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">暂无课程</h3>
              <p className="text-gray-500 mb-6">您还没有创建任何课程，点击右上角按钮开始创建您的第一个课程</p>
              <button
                onClick={() => setIsSeriesModalVisible(true)}
                className="flex items-center gap-2 px-6 py-3 bg-orange-500 hover:bg-orange-600 text-white rounded-lg transition-colors duration-200 font-medium mx-auto"
              >
                <Plus className="w-4 h-4" />
                创建第一个课程
              </button>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {courses.map((course) => {
              const statusButton = getStatusButton(course.status);
              return (
                <div
                  key={course.id}
                  className="course-card cursor-pointer hover:shadow-lg transition-shadow duration-200"
                  onClick={() => handleCourseCardClick(course)}
                >
                  {/* 课程封面 */}
                  <div className="course-cover">
                    {course.coverImage ? (
                      // 有封面图片时显示背景图（包括占位符）
                      <div
                        className="w-full h-full"
                        style={{
                          backgroundImage: `url(${course.coverImage})`,
                          backgroundSize: 'cover',
                          backgroundPosition: 'center',
                          backgroundRepeat: 'no-repeat'
                        }}
                      />
                    ) : (
                      // 没有封面时显示灰色背景和图标
                      <div className="w-full h-full bg-gray-100 flex items-center justify-center">
                        <Image className="w-12 h-12 text-gray-400" />
                      </div>
                    )}
                  </div>

                  {/* 课程信息 */}
                  <div className="course-info">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex-1">
                        <h3 className="course-title">{course.title}</h3>
                      </div>
                      <button className={statusButton.className}>
                        {statusButton.text}
                      </button>
                    </div>

                    {/* 课程统计信息 */}
                    <div className="flex items-center justify-between text-xs text-gray-500 pt-2 border-t border-gray-100">
                      <div className="flex items-center gap-4">
                        <span>课程 {course.totalCourses || 0}</span>
                      </div>
                      <span>{course.createTime}</span>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>

      {/* 创建系列课程模态框 */}
      {isCreateModalVisible && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-md mx-4">
            {/* 模态框头部 */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h2 className="text-xl font-semibold text-gray-900">创建系列课程</h2>
              <button
                onClick={() => {
                  setIsCreateModalVisible(false);
                  setFormData({ title: '', description: '', category: '', tags: '' });
                }}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X className="w-6 h-6" />
              </button>
            </div>

            {/* 模态框内容 */}
            <div className="p-6 space-y-6">
              {/* 系列课程封面 */}
              <div className="flex justify-center">
                <div className="w-48 h-32 border-2 border-gray-300 rounded-lg flex items-center justify-center bg-gray-50">
                  <span className="text-gray-500 text-sm">系列课程封面</span>
                </div>
              </div>

              {/* 系列课程标题输入框 */}
              <div>
                <input
                  type="text"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  placeholder="系列课程标题"
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-colors text-center"
                />
              </div>

              {/* 创建按钮 */}
              <div className="flex justify-center">
                <button
                  onClick={handleCreateCourse}
                  className="px-8 py-2 bg-white border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-200"
                >
                  创建系列课程
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 系列课程创建模态框 */}
      <CreateSeriesCourseModal
        isVisible={isSeriesModalVisible}
        onClose={() => setIsSeriesModalVisible(false)}
        onSubmit={handleCreateSeriesCourse}
      />

      {/* 课程列表编辑弹窗 */}
      {selectedCourse && (
        <CourseListEditModal
          isVisible={isCourseListModalVisible}
          onClose={handleCourseListModalClose}
          onSave={handleCourseListSave}
          seriesTitle={selectedCourse.title}
          seriesCoverImage={selectedCourse.coverImage}
          seriesId={selectedCourse.id}
        />
      )}
    </div>
  );
};

export default CourseManagement;
