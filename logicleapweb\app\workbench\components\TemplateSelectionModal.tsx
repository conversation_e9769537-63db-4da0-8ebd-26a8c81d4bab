'use client';

import React, { useState, useEffect } from 'react';
import { X, ArrowLeft, Users, CheckCircle } from 'lucide-react';
import { pointsApi } from '@/lib/api/points';
import { packageApi } from '@/lib/api/package';
import { classApi } from '@/lib/api/class';
import { useSelector } from 'react-redux';
import { RootState } from '@/lib/store';
import { GetNotification } from 'logic-common/dist/components/Notification';
import TemplatePickerModal from './TemplatePickerModal';
import taskApi, { TaskType, Priority } from '@/lib/api/task';
import { worksApi } from '@/lib/api/works';
import { batchAddUserJoinRole } from '@/lib/api/role';
import { BatchUseKeyPackageModal } from './BatchUseKeyPackageModal';
import { Carousel } from 'antd';
import dayjs from 'dayjs';
import './TemplatePickerModal.css';
import './NewPublishTaskModal.css';
import './TemplateSelectionModal.css';

interface Student {
  id: number;
  userId: number;
  studentNumber: string;
  nickName: string;
  avatarUrl?: string;
  classId: number;
}

interface TemplateSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onBack: () => void;
  onConfirm?: (taskData: any) => void;
  actionType: string;
  selectedSchool: any;
  selectedClass: any;
}

// 图片标签组件
const ImageLabel: React.FC<{ text: string }> = ({ text }) => (
  <div className="work-image-overlay">
    <span className="work-image-label">{text}</span>
  </div>
);

const TemplateSelectionModal: React.FC<TemplateSelectionModalProps> = ({
  isOpen,
  onClose,
  onBack,
  onConfirm,
  actionType,
  selectedSchool,
  selectedClass
}) => {
  const [mounted, setMounted] = useState(false);
  const [hoveredOption, setHoveredOption] = useState<string>('');
  // 移除教师能量相关状态，因为不需要检查教师能量池
  const [isTemplatePickerOpen, setIsTemplatePickerOpen] = useState(false);

  const [isBatchUseKeyPackageModalOpen, setIsBatchUseKeyPackageModalOpen] = useState(false);

  // 学生相关状态
  const [students, setStudents] = useState<Student[]>([]);
  const [loadingStudents, setLoadingStudents] = useState(false);
  const [studentPointsMap, setStudentPointsMap] = useState<Map<number, number>>(new Map());
  const [loadingStudentPoints, setLoadingStudentPoints] = useState(false);

  // 存储模态框数据的状态
  const [modalData, setModalData] = useState({
    selectedDistribution: '',
    assignEnergyAmount: '', // 分配按钮的能量数值
    distributeEnergyAmount: '', // 分配至按钮的能量数值
    selectedTemplate: null as any,
    selectedStudents: [] as number[], // 默认为所有学生
  });

  // 输入验证错误状态
  const [inputErrors, setInputErrors] = useState({
    assignEnergyError: '',
    distributeEnergyError: ''
  });

  // 当前步骤状态
  const [currentStep, setCurrentStep] = useState<'template' | 'publish'>('template');

  // 发布任务相关状态
  const [taskData, setTaskData] = useState({
    taskName: '',
    taskDescription: '',
    selfAssessmentItems: [] as string[],
    startTime: '',
    endTime: ''
  });

  // 快捷时间选择器状态
  const [showQuickTimeSelector, setShowQuickTimeSelector] = useState<'start' | 'end' | null>(null);

  // 点击外部关闭快捷时间选择器
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (showQuickTimeSelector && !target.closest('.time-field-container')) {
        setShowQuickTimeSelector(null);
      }
    };

    if (showQuickTimeSelector) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [showQuickTimeSelector]);

  // 获取当前日期时间的函数
  const getCurrentDateTime = () => {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    return `${year}-${month}-${day}T${hours}:${minutes}`;
  };

  // 获取24小时后的日期时间
  const getNextDayDateTime = () => {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const year = tomorrow.getFullYear();
    const month = String(tomorrow.getMonth() + 1).padStart(2, '0');
    const day = String(tomorrow.getDate()).padStart(2, '0');
    const hours = String(tomorrow.getHours()).padStart(2, '0');
    const minutes = String(tomorrow.getMinutes()).padStart(2, '0');
    return `${year}-${month}-${day}T${hours}:${minutes}`;
  };

  // 快捷时间选择函数
  const handleQuickTimeSelect = (duration: string, type: 'start' | 'end') => {
    const now = new Date();
    let targetTime: Date;

    if (type === 'start' || duration === '现在') {
      // 开始时间或"现在"选项设置为当前时间
      targetTime = now;
    } else {
      // 结束时间根据持续时间计算
      // 如果已有开始时间，基于开始时间计算；否则基于当前时间
      const baseTime = taskData.startTime ? new Date(taskData.startTime) : now;
      targetTime = new Date(baseTime);

      switch (duration) {
        case '1小时':
          targetTime.setHours(targetTime.getHours() + 1);
          break;
        case '6小时':
          targetTime.setHours(targetTime.getHours() + 6);
          break;
        case '12小时':
          targetTime.setHours(targetTime.getHours() + 12);
          break;
        case '1天':
          targetTime.setDate(targetTime.getDate() + 1);
          break;
        case '7天':
          targetTime.setDate(targetTime.getDate() + 7);
          break;
        default:
          targetTime.setHours(targetTime.getHours() + 1);
      }

      // 如果没有开始时间，同时设置开始时间为当前时间
      if (!taskData.startTime) {
        const startYear = now.getFullYear();
        const startMonth = String(now.getMonth() + 1).padStart(2, '0');
        const startDay = String(now.getDate()).padStart(2, '0');
        const startHours = String(now.getHours()).padStart(2, '0');
        const startMinutes = String(now.getMinutes()).padStart(2, '0');
        const startTimeString = `${startYear}-${startMonth}-${startDay}T${startHours}:${startMinutes}`;

        setTaskData(prev => ({
          ...prev,
          startTime: startTimeString,
          endTime: `${targetTime.getFullYear()}-${String(targetTime.getMonth() + 1).padStart(2, '0')}-${String(targetTime.getDate()).padStart(2, '0')}T${String(targetTime.getHours()).padStart(2, '0')}:${String(targetTime.getMinutes()).padStart(2, '0')}`
        }));
        setShowQuickTimeSelector(null);
        return;
      }
    }

    const year = targetTime.getFullYear();
    const month = String(targetTime.getMonth() + 1).padStart(2, '0');
    const day = String(targetTime.getDate()).padStart(2, '0');
    const hours = String(targetTime.getHours()).padStart(2, '0');
    const minutes = String(targetTime.getMinutes()).padStart(2, '0');
    const timeString = `${year}-${month}-${day}T${hours}:${minutes}`;

    if (type === 'start') {
      setTaskData(prev => ({ ...prev, startTime: timeString }));
    } else {
      setTaskData(prev => ({ ...prev, endTime: timeString }));
    }

    setShowQuickTimeSelector(null);
  };

  // 格式化时间显示
  const formatTimeDisplay = (timeString: string) => {
    if (!timeString) return '';
    const date = new Date(timeString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    return `${year}年${month}月${day}日 ${hours}:${minutes}`;
  };
  const [activeTab, setActiveTab] = useState<'task' | 'resources'>('task');
  const [works, setWorks] = useState<any[]>([]);
  const [allWorks, setAllWorks] = useState<any[]>([]); // 存储所有作品数据，用于前端分页
  const [selectedWorkIds, setSelectedWorkIds] = useState<number[]>([]);
  const [attachments, setAttachments] = useState<File[]>([]);
  const [loadingWorks, setLoadingWorks] = useState(false);
  const [page, setPage] = useState(1);

  // 发布任务加载状态
  const [isPublishing, setIsPublishing] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);

  // 鼠标拖拽滚动状态
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, scrollLeft: 0 });
  const pageSize = 10;

  // 阻止背景页面滚动
  useEffect(() => {
    if (isOpen) {
      // 保存原始的 overflow 样式
      const originalStyle = window.getComputedStyle(document.body).overflow;
      // 阻止背景滚动
      document.body.style.overflow = 'hidden';

      return () => {
        // 恢复原始样式
        document.body.style.overflow = originalStyle;
      };
    }
  }, [isOpen]);

  // 修复图片URL，确保有正确的协议前缀
  const fixImageUrl = (url: string | undefined): string => {
    if (!url) return "/images/xiaoluo-default.webp";

    // 如果已经有协议前缀，直接返回
    if (url.startsWith('http://') || url.startsWith('https://')) {
      return url;
    }

    // 如果是阿里云OSS URL但缺少协议前缀，添加https://
    if (url.includes('aliyuncs.com') || url.includes('logicleap.oss')) {
      return `https://${url}`;
    }

    // 如果是相对路径，保持原样
    if (url.startsWith('/')) {
      return url;
    }

    // 其他情况，添加https://
    return `https://${url}`;
  };

  // 重置作品状态
  const resetWorksState = () => {
    setWorks([]);
    setAllWorks([]);
    setPage(1);
    setHasMore(true);
    setSelectedWorkIds([]);
  };

  // 获取作品列表（与NewPublishTaskModal保持一致）
  const fetchWorks = async () => {
    if (loadingWorks) return;

    setLoadingWorks(true);
    try {
      // 获取当前用户ID
      const userData = localStorage.getItem('user');
      const userId = userData ? JSON.parse(userData).userId : null;

      if (!userId) {
        console.error('用户未登录');
        setWorks([]);
        return;
      }

      // 使用与NewPublishTaskModal相同的API
      const response = await worksApi.getTeacherWorks(userId, 1, 1000);

      // 处理多层嵌套的数据结构
      let newWorks: any[] = [];

      if (response && response.data) {
        if (response.data.data?.data?.list) {
          // 双层data嵌套
          newWorks = response.data.data.data.list || [];
        } else if (response.data.data?.list) {
          // 单层data嵌套
          newWorks = response.data.data.list || [];
        } else if (Array.isArray(response.data.data)) {
          // data是数组
          newWorks = response.data.data;
        } else if (Array.isArray(response.data)) {
          // 直接是数组
          newWorks = response.data;
        }
      }

      console.log('获取到的作品数据:', newWorks);
      if (newWorks.length > 0) {
        console.log('第一个作品的数据结构:', newWorks[0]);
        console.log('第一个作品的标题字段:', {
          title: newWorks[0].title,
          name: newWorks[0].name,
          workName: newWorks[0].workName
        });
      }
      setWorks(newWorks);
    } catch (error) {
      console.error('获取作品失败:', error);
      setWorks([]);
    } finally {
      setLoadingWorks(false);
    }
  };

  // 移除加载更多功能，因为现在显示所有作品

  // 选择作品（支持多选）
  const handleSelectWork = (workId: number) => {
    if (selectedWorkIds.includes(workId)) {
      // 取消选中
      setSelectedWorkIds(prev => prev.filter(id => id !== workId));
    } else {
      // 选中
      setSelectedWorkIds(prev => [...prev, workId]);
    }
  };

  // 处理水平滚动并阻止外部滚动
  const handleWheelScroll = (e: React.WheelEvent<HTMLDivElement>) => {
    // 只阻止事件传播，不调用preventDefault避免被动监听器警告
    e.stopPropagation();

    // 查找实际的滚动容器
    const scrollContainer = e.currentTarget.querySelector('.works-horizontal-scroll') as HTMLDivElement;

    if (!scrollContainer) {
      return;
    }

    const { scrollLeft, scrollWidth, clientWidth } = scrollContainer;

    // 检查是否有可滚动内容
    const hasScrollableContent = scrollWidth > clientWidth;

    // 只有当有可滚动内容时才进行水平滚动
    if (hasScrollableContent && e.deltaY !== 0) {
      // 增加滚动速度倍数，让滚动更流畅
      const scrollMultiplier = 4; // 进一步增加滚动速度
      const deltaX = e.deltaY * scrollMultiplier;

      // 计算新的滚动位置
      const newScrollLeft = scrollLeft + deltaX;
      const maxScrollLeft = scrollWidth - clientWidth;

      // 限制滚动范围并应用平滑滚动
      const targetScrollLeft = Math.max(0, Math.min(newScrollLeft, maxScrollLeft));

      // 使用 requestAnimationFrame 实现更平滑的滚动
      requestAnimationFrame(() => {
        scrollContainer.scrollLeft = targetScrollLeft;
      });
    }
  };

  // 处理鼠标拖拽滚动
  const handleMouseDown = (e: React.MouseEvent<HTMLDivElement>) => {
    const scrollContainer = e.currentTarget.querySelector('.works-horizontal-scroll') as HTMLDivElement;
    if (!scrollContainer) return;

    setIsDragging(true);
    setDragStart({
      x: e.pageX - scrollContainer.offsetLeft,
      scrollLeft: scrollContainer.scrollLeft
    });

    // 改变鼠标样式
    e.currentTarget.style.cursor = 'grabbing';
  };

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!isDragging) return;

    e.preventDefault(); // 鼠标事件可以安全地使用preventDefault
    const scrollContainer = e.currentTarget.querySelector('.works-horizontal-scroll') as HTMLDivElement;
    if (!scrollContainer) return;

    const x = e.pageX - scrollContainer.offsetLeft;
    const walk = (x - dragStart.x) * 2; // 调整拖拽速度
    scrollContainer.scrollLeft = dragStart.scrollLeft - walk;
  };

  const handleMouseUp = (e: React.MouseEvent<HTMLDivElement>) => {
    setIsDragging(false);
    e.currentTarget.style.cursor = 'grab';
  };

  const handleMouseLeave = (e: React.MouseEvent<HTMLDivElement>) => {
    setIsDragging(false);
    e.currentTarget.style.cursor = 'grab';
  };

  // 当切换到发布任务步骤且选择资源标签页时获取作品列表
  useEffect(() => {
    if (currentStep === 'publish' && activeTab === 'resources' && mounted) {
      resetWorksState();
      fetchWorks();
    }
  }, [currentStep, activeTab, mounted]);

  // 清空所有数据的函数
  const clearAllData = () => {
    // 重置步骤状态
    setCurrentStep('template');

    // 重置模态框数据
    setModalData({
      selectedDistribution: '',
      assignEnergyAmount: '',
      distributeEnergyAmount: '',
      selectedTemplate: null,
      selectedStudents: []
    });

    // 重置错误状态
    setInputErrors({
      assignEnergyError: '',
      distributeEnergyError: ''
    });

    // 重置任务数据
    setTaskData({
      taskName: '',
      taskDescription: '',
      selfAssessmentItems: [],
      startTime: '',
      endTime: ''
    });

    // 重置其他状态
    setActiveTab('task');
    setWorks([]);
    setAllWorks([]);
    setSelectedWorkIds([]);
    setAttachments([]);
    setHoveredOption('');
    setIsTemplatePickerOpen(false);
    setIsBatchUseKeyPackageModalOpen(false);
    setPage(1);
    setHasMore(true);
    setLoadingMore(false);

    // 重置学生相关数据
    setStudents([]);
    setStudentPointsMap(new Map());
  };

  // 监听模态框关闭，清空数据
  useEffect(() => {
    if (!isOpen) {
      clearAllData();
    }
  }, [isOpen]);

  // 组件卸载时清空数据
  useEffect(() => {
    return () => {
      clearAllData();
    };
  }, []);



  const userId = useSelector((state: RootState) => state.user.userState.userId);
  const notification = GetNotification();

  // 防止水合错误
  useEffect(() => {
    setMounted(true);
  }, []);

  // 移除获取教师可分配能量的函数，因为不需要检查教师能量池

  // 获取班级学生列表和能量信息（并行处理）
  const fetchClassStudents = async () => {
    if (!selectedClass?.id || !mounted) return;

    setLoadingStudents(true);
    setLoadingStudentPoints(true); // 提前设置能量加载状态

    try {
      const response = await classApi.getClassStudents(selectedClass.id);
      if (response.data.code === 200) {
        const studentsData = response.data.data || [];
        setStudents(studentsData);

        // 立即并行获取学生能量信息，不等待学生列表完全处理完
        if (studentsData.length > 0) {
          // 不等待，立即开始获取能量信息
          fetchStudentPoints(studentsData.map((s: Student) => s.userId));
        } else {
          setLoadingStudentPoints(false); // 没有学生时立即结束能量加载状态
        }
      } else {
        console.error('获取学生列表失败:', response.data.message);
        notification.error('获取学生列表失败');
        setStudents([]);
        setLoadingStudentPoints(false);
      }
    } catch (error) {
      console.error('获取学生列表失败:', error);
      notification.error('获取学生列表失败');
      setStudents([]);
      setLoadingStudentPoints(false);
    } finally {
      setLoadingStudents(false);
    }
  };

  // 获取学生能量信息（优化版本）
  const fetchStudentPoints = async (userIds: number[]) => {
    if (userIds.length === 0) {
      setLoadingStudentPoints(false);
      return;
    }

    try {
      console.log("开始获取学生能量信息，userIds:", userIds);
      console.log("使用API端点:", `${pointsApi.basePointUrl}/batch-total`);

      // 设置3秒超时，避免长时间等待影响用户体验
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('请求超时')), 3000);
      });

      const apiPromise = pointsApi.getBatchStudentPoints(userIds);
      const response = await Promise.race([apiPromise, timeoutPromise]) as any;

      const pointsMap = new Map<number, number>();
      console.log("批量积分响应", response);

      if (response.data.code === 200) {
        const data = response.data.data;
        // 遍历返回的学生积分数据
        for (const userId in data) {
          const studentData = data[userId];
          if (studentData) {
            const totalPoints = studentData.totalPoints || 0;
            const availablePoints = studentData.availablePoints || 0;
            // 修正计算逻辑：与 assign-points-modal.tsx 保持一致
            // totalPoints: 总积分, availablePoints: 已使用积分
            // 可分配积分 = 总积分 - 已使用积分
            const remainingPoints = totalPoints - availablePoints;
            pointsMap.set(Number(userId), Math.max(0, remainingPoints));
          } else {
            pointsMap.set(Number(userId), 0);
          }
        }

        // 确保所有请求的用户都有数据
        userIds.forEach(uid => {
          if (!pointsMap.has(uid)) {
            pointsMap.set(uid, 0);
          }
        });

        setStudentPointsMap(pointsMap);
      } else {
        // API返回错误，设置默认值但不显示错误提示（避免干扰用户体验）
        userIds.forEach(uid => pointsMap.set(uid, 0));
        setStudentPointsMap(pointsMap);
        console.warn('获取学生能量信息失败，使用默认值:', response.data.message);
      }
    } catch (error: any) {
      console.error('批量获取学生能量失败:', error);

      // 设置默认值，让用户可以继续操作
      const pointsMap = new Map<number, number>();
      userIds.forEach(uid => pointsMap.set(uid, 0));
      setStudentPointsMap(pointsMap);

      // 根据错误类型决定是否显示提示
      if (error.message === '请求超时') {
        console.warn('获取能量信息超时，使用默认值，用户可继续操作');
        // 不显示错误提示，避免干扰用户体验
      } else if (error.response?.status >= 500) {
        console.warn('服务器错误，使用默认能量值');
        // 服务器错误时也不显示提示，让用户可以继续操作
      } else if (error.response?.status === 403) {
        // 权限问题可能需要用户知道
        notification.warning('暂无法获取能量信息，已设置默认值');
      }
      // 其他错误也不显示提示，保持良好的用户体验
    } finally {
      setLoadingStudentPoints(false);
    }
  };



  // 移除获取教师能量的 useEffect

  useEffect(() => {
    if (isOpen && selectedClass) {
      fetchClassStudents();
      // 重置模态框数据
      setModalData({
        selectedDistribution: '',
        assignEnergyAmount: '',
        distributeEnergyAmount: '',
        selectedTemplate: null,
        selectedStudents: [], // 将在获取学生列表后设置为所有学生
      });

      // 重置错误状态
      setInputErrors({
        assignEnergyError: '',
        distributeEnergyError: ''
      });

      // 禁用body滚动
      document.body.style.overflow = 'hidden';
    } else {
      // 恢复body滚动
      document.body.style.overflow = '';
    }

    // 清理函数：组件卸载时恢复滚动
    return () => {
      document.body.style.overflow = '';
    };
  }, [isOpen, selectedClass, mounted]);

  // 当学生列表加载完成后，自动选择所有学生
  useEffect(() => {
    if (students.length > 0) {
      setModalData(prev => ({
        ...prev,
        selectedStudents: students.map(s => s.userId)
      }));
    }
  }, [students]);

  const distributionOptions = [
    { id: 'none', label: '不分配', description: '不进行任何分配' },
    { id: 'assign', label: '分配', description: '分配给学生', hasInput: true },
    { id: 'distribute', label: '分配至', description: '分配到指定位置', hasInput: true }
  ];

  const handleDistributionSelect = (optionId: string) => {
    setModalData(prev => ({
      ...prev,
      selectedDistribution: optionId
    }));
  };

  const handleTemplateSelect = (template: any) => {
    setModalData(prev => ({
      ...prev,
      selectedTemplate: template
    }));
  };

  const handleCancelTemplate = () => {
    setModalData(prev => ({
      ...prev,
      selectedTemplate: null,
      selectedDistribution: ''
    }));
  };

  const handleTemplatePickerOpen = () => {
    setIsTemplatePickerOpen(true);
  };

  const handleTemplatePickerClose = () => {
    setIsTemplatePickerOpen(false);
  };

  // 处理批量兑换密钥模态框
  const handleBatchUseKeyPackageModalOpen = () => {
    setIsBatchUseKeyPackageModalOpen(true);
  };

  const handleBatchUseKeyPackageModalClose = () => {
    setIsBatchUseKeyPackageModalOpen(false);
  };

  const handleBatchUseKeyPackageSuccess = () => {
    // 兑换成功后重新获取学生能量信息
    if (students.length > 0) {
      fetchStudentPoints(students.map(s => s.userId));
    }
    notification.success('密钥兑换成功！');
  };

  // 获取当前选中分配方式对应的能量数值
  const getCurrentEnergyAmount = () => {
    if (modalData.selectedDistribution === 'assign') {
      return modalData.assignEnergyAmount;
    } else if (modalData.selectedDistribution === 'distribute') {
      return modalData.distributeEnergyAmount;
    }
    return '';
  };

  // 计算所有学生的最低可分配能量
  const getMinAvailablePoints = () => {
    if (modalData.selectedStudents.length === 0) return 0;

    const selectedStudentPoints = modalData.selectedStudents.map(studentId =>
      studentPointsMap.get(studentId) || 0
    );

    return Math.min(...selectedStudentPoints);
  };

  // 获取当前分配方式的提示信息
  const getEnergyDisplayInfo = () => {
    if (modalData.selectedDistribution === 'assign' || modalData.selectedDistribution === 'distribute') {
      return {
        label: '最低可分配能量',
        value: getMinAvailablePoints()
      };
    }
    return { label: '', value: 0 };
  };

  const handleNext = () => {
    console.log('选择的分配方式:', modalData.selectedDistribution);
    console.log('选择的模板:', modalData.selectedTemplate);

    // 如果选择了分配能量，需要检查学生积分是否足够
    if (modalData.selectedDistribution === 'assign' || modalData.selectedDistribution === 'distribute') {
      const currentEnergyAmount = getCurrentEnergyAmount();
      console.log('分配能量数量:', currentEnergyAmount);

      // 检查是否有输入错误
      const errorKey = modalData.selectedDistribution === 'assign' ? 'assignEnergyError' : 'distributeEnergyError';
      if (inputErrors[errorKey as keyof typeof inputErrors]) {
        notification.error('请修正输入错误后再继续');
        return;
      }

      // 检查能量数量是否有效
      const energyAmountNum = Number(currentEnergyAmount);
      if (!currentEnergyAmount || energyAmountNum <= 0) {
        notification.error('请输入有效的分配能量数量');
        return;
      }

      if (modalData.selectedDistribution === 'assign') {
        // "分配"按钮：检查选中学生的可分配能量是否足够
        const insufficientStudents = modalData.selectedStudents.filter(studentId => {
          const studentAvailablePoints = studentPointsMap.get(studentId);
          return studentAvailablePoints !== undefined && studentAvailablePoints < energyAmountNum;
        });

        if (insufficientStudents.length > 0) {
          const insufficientNames = insufficientStudents.map(studentId => {
            const student = students.find(s => s.userId === studentId);
            const availablePoints = studentPointsMap.get(studentId) || 0;
            return `${student?.nickName || `学生${studentId}`}(可分配: ${availablePoints})`;
          }).join('、');

          notification.error(`积分不足：以下学生的可分配能量不足 ${energyAmountNum}：${insufficientNames}`);
          return;
        }
      } else if (modalData.selectedDistribution === 'distribute') {
        // "分配至"按钮：检查需要补充能量的学生
        const studentsNeedingEnergy = modalData.selectedStudents.filter(studentId => {
          const currentPoints = studentPointsMap.get(studentId) || 0;
          return currentPoints < energyAmountNum;
        });

        if (studentsNeedingEnergy.length > 0) {
          // 检查这些学生是否有足够的可分配能量来达到目标值
          const insufficientStudents = studentsNeedingEnergy.filter(studentId => {
            const currentPoints = studentPointsMap.get(studentId) || 0;
            const neededPoints = energyAmountNum - currentPoints;
            const studentAvailablePoints = studentPointsMap.get(studentId);
            return studentAvailablePoints !== undefined && studentAvailablePoints < neededPoints;
          });

          if (insufficientStudents.length > 0) {
            const insufficientNames = insufficientStudents.map(studentId => {
              const student = students.find(s => s.userId === studentId);
              const currentPoints = studentPointsMap.get(studentId) || 0;
              const neededPoints = energyAmountNum - currentPoints;
              const availablePoints = studentPointsMap.get(studentId) || 0;
              return `${student?.nickName || `学生${studentId}`}(需要: ${neededPoints}, 可分配: ${availablePoints})`;
            }).join('、');

            notification.error(`积分不足：以下学生无法达到目标能量值 ${energyAmountNum}：${insufficientNames}`);
            return;
          }
        }
      }
    }

    // 根据操作类型决定下一步
    if (actionType === '发布任务' || actionType === '快速上课') {
      // 发布任务或快速上课：切换到发布任务步骤
      setTaskData(prev => ({
        ...prev,
        startTime: '',
        endTime: ''
      }));
      setCurrentStep('publish');
    } else {
      // 分配积木或分配能量：直接完成操作并关闭弹窗
      console.log(`${actionType}操作完成`, {
        selectedDistribution: modalData.selectedDistribution,
        selectedTemplate: modalData.selectedTemplate,
        selectedStudents: modalData.selectedStudents,
        energyAmount: getCurrentEnergyAmount()
      });

      // 这里可以调用相应的API来执行分配操作
      // TODO: 实现分配积木和分配能量的API调用

      notification.success(`${actionType}完成！`);
      clearAllData();
      onClose();
    }
  };

  const handlePrevious = () => {
    if (currentStep === 'publish') {
      // 从发布任务步骤返回时清空日期框
      setTaskData(prev => ({
        ...prev,
        startTime: '',
        endTime: ''
      }));
      setCurrentStep('template');
    } else {
      onBack();
    }
  };



  // 确认发布（与NewPublishTaskModal保持一致）
  const handleConfirm = () => {
    if (!taskData.taskName.trim()) {
      notification.error('请输入任务名称');
      return;
    }

    const finalTaskData = {
      ...taskData,
      selectedWorkIds,
      attachments,
      modalData
    };

    // 如果有onConfirm回调，调用它；否则执行原有的发布逻辑
    if (onConfirm) {
      onConfirm(finalTaskData);
    } else {
      // 保留原有的发布逻辑作为后备
      handlePublishTaskOk();
    }
  };

  const handlePublishTaskOk = async () => {
    // 防止重复点击
    if (isPublishing) {
      return;
    }

    try {
      setIsPublishing(true);

      // 验证必填字段
      if (!taskData.taskName.trim()) {
        notification.error('请输入任务名称');
        setIsPublishing(false);
        return;
      }

      console.log('发布任务:', taskData);
      console.log('选中的作品ID:', selectedWorkIds);
      console.log('模态框数据:', modalData);

      // 获取当前用户信息
      const userData = localStorage.getItem('user');
      const user = userData ? JSON.parse(userData) : null;
      const teacherId = user?.userId;

      if (!teacherId) {
        notification.error('未找到用户信息');
        setIsPublishing(false);
        return;
      }

      // 处理时间
      const startDate = taskData.startTime ? new Date(taskData.startTime) : new Date();
      const endDate = taskData.endTime ? new Date(taskData.endTime) : new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);

      // 构建任务发布参数
      const taskParams = {
        taskName: taskData.taskName,
        taskDescription: taskData.taskDescription || '',
        taskType: TaskType.GRAPHIC, // 默认为图形化任务
        priority: Priority.NORMAL, // 默认为普通优先级
        startDate: startDate,
        endDate: endDate,
        taskContent: taskData.taskDescription || '', // 使用任务描述作为内容
        attachments: attachments.map(file => file.name) || [], // 转换为文件名数组
        isPublic: 1, // 公开任务
        allowLateSubmission: false,
        studentIds: modalData.selectedStudents,
        classId: selectedClass?.id,
        templateId: modalData.selectedTemplate?.id,
        workIds: selectedWorkIds.length > 0 ? selectedWorkIds : undefined, // 传递作品ID数组
        selfAssessmentItems: taskData.selfAssessmentItems.filter(item => item.trim() !== '') || [] // 过滤空的自评项
      };

      console.log('任务发布参数:', taskParams);
      console.log('作品ID数组:', taskParams.workIds);

      // 准备并行请求数组
      const requests: Promise<any>[] = [];

      // 1. 任务发布请求（必须执行）
      requests.push(taskApi.publishTask(taskParams));

      // 2. 能量分配请求（如果需要）
      let energyRequest: Promise<any> | null = null;
      const currentEnergyAmount = getCurrentEnergyAmount();
      if ((modalData.selectedDistribution === 'assign' || modalData.selectedDistribution === 'distribute') && currentEnergyAmount) {
        const targetAmount = Number(currentEnergyAmount);
        const defaultExpireTime = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString();

        if (modalData.selectedDistribution === 'assign') {
          // "分配"按钮：给每个学生分配固定数量的能量
          const studentExpiries: { [studentId: number]: string } = {};
          modalData.selectedStudents.forEach(studentId => {
            studentExpiries[studentId] = defaultExpireTime;
          });

          energyRequest = pointsApi.batchAssignPermission({
            availablePoints: targetAmount,
            studentExpiries,
            remark: `任务发布 - ${taskData.taskName}`
          });
          requests.push(energyRequest);
        } else if (modalData.selectedDistribution === 'distribute') {
          // "分配至"按钮：将学生能量补充到目标值
          const energyRequests: Promise<any>[] = [];

          modalData.selectedStudents.forEach(studentId => {
            const currentPoints = studentPointsMap.get(studentId) || 0;
            const neededPoints = targetAmount - currentPoints;

            // 只有当学生当前能量小于目标值时才分配
            if (neededPoints > 0) {
              const studentExpiries: { [studentId: number]: string } = {};
              studentExpiries[studentId] = defaultExpireTime;

              const request = pointsApi.batchAssignPermission({
                availablePoints: neededPoints,
                studentExpiries,
                remark: `任务发布 - ${taskData.taskName} (补充至${targetAmount})`
              });
              energyRequests.push(request);
            }
          });

          // 将所有能量分配请求添加到主请求列表
          requests.push(...energyRequests);
        }
      }

      // 3. 模板分配请求（如果需要）
      let templateRequest: Promise<any> | null = null;
      if (modalData.selectedTemplate) {
        const users = modalData.selectedStudents.map(studentId => ({
          userId: studentId,
          roleId: 1,
          templateId: modalData.selectedTemplate.id,
          originalTemplateId: modalData.selectedTemplate.originalTemplateId || modalData.selectedTemplate.id
        }));

        templateRequest = batchAddUserJoinRole({ users });
        requests.push(templateRequest);
      }

      // 并行执行所有请求
      const results = await Promise.allSettled(requests);

      // 处理任务发布结果
      const taskResult = results[0];
      if (taskResult.status === 'fulfilled' && taskResult.value.data.code === 200) {
        // 显示成功发布任务的提示
        notification.success('🎉 任务发布成功！学生可以开始学习了');
      } else {
        const errorMsg = taskResult.status === 'fulfilled'
          ? taskResult.value.data.message || '任务发布失败'
          : '任务发布失败';
        notification.error(errorMsg);
        setIsPublishing(false);
        return; // 任务发布失败则直接返回
      }

      // 处理能量分配结果
      let resultIndex = 1;
      if (energyRequest) {
        const energyResult = results[resultIndex];
        if (energyResult.status === 'fulfilled' && energyResult.value.data.code === 200) {
          notification.success('能量分配完成！');
        } else {
          console.error('能量分配失败:', energyResult);
          notification.warning('能量分配失败');
        }
        resultIndex++;
      }

      // 处理模板分配结果
      if (templateRequest) {
        const templateResult = results[resultIndex];
        if (templateResult.status === 'fulfilled' && templateResult.value.data.code === 200) {
          notification.success('模板分配完成！');
        } else {
          console.error('模板分配失败:', templateResult);
          notification.warning('模板分配失败');
        }
      }

      // 延迟关闭弹窗，让用户能看到成功提示
      setTimeout(() => {
        setIsPublishing(false);
        clearAllData();
        onClose();
      }, 800);
    } catch (error) {
      console.error('发布任务失败:', error);
      notification.error('任务发布失败，请重试');
      setIsPublishing(false);
    }
  };

  // 防止水合错误，在客户端挂载前不渲染
  if (!mounted || !isOpen) return null;

  return (
    <div
      className="modal-overlay"
      onWheel={(e) => {
        // 只阻止事件传播，依赖CSS控制滚动行为
        e.stopPropagation();
      }}
      onTouchMove={(e) => {
        // 只阻止事件传播，不调用preventDefault避免被动监听器警告
        e.stopPropagation();
      }}
    >
      <div className="modal-wrapper">
        <button className="modal-close-btn-outside" onClick={() => {
          clearAllData();
          onClose();
        }}>
          <X size={20} />
        </button>
        <div className="modal-content template-selection-modal" data-step={currentStep}>

        {/* 步骤指示器 */}
        <div className="step-indicator">
          <div className="step completed">
            <div className="step-number">1</div>
            <div className="step-label">选择班级</div>
          </div>
          <div className={`step ${currentStep === 'template' ? 'active' : 'completed'}`}>
            <div className="step-number">2</div>
            <div className="step-label">能量和模板</div>
          </div>
          {(actionType === '发布任务' || actionType === '快速上课') && (
            <div className={`step ${currentStep === 'publish' ? 'active' : ''}`}>
              <div className="step-number">3</div>
              <div className="step-label">发布任务</div>
            </div>
          )}
        </div>

        {/* 内容区域 */}
        <div className="modal-content-body">
          {currentStep === 'template' ? (
            <>
              <div className="modal-content-header">
                <h3 className="section-title">为学生分配能量</h3>
              </div>

              <div className="modal-content-scrollable">
                {/* 分配选项 */}
                <div className="distribution-options">
                {distributionOptions.map((option) => (
                  <div
                    key={option.id}
                    className={`distribution-card ${modalData.selectedDistribution === option.id ? 'selected' : ''}`}
                    onClick={() => handleDistributionSelect(option.id)}
                    onMouseEnter={() => setHoveredOption(option.id)}
                    onMouseLeave={() => setHoveredOption('')}
                  >
                    <div className="distribution-label">
                      {(() => {
                        const currentAmount = option.id === 'assign' ? modalData.assignEnergyAmount :
                                            option.id === 'distribute' ? modalData.distributeEnergyAmount : '';
                        return option.hasInput && currentAmount && Number(currentAmount) > 0 && modalData.selectedDistribution === option.id
                          ? `${option.label} ${currentAmount}能量`
                          : option.label;
                      })()}
                    </div>
                    {option.hasInput && hoveredOption === option.id && (
                      <div className="energy-input-container">
                        <input
                          type="number"
                          className="energy-input"
                          placeholder={option.id === 'assign' ? '输入能量' : '输入目标值'}
                          value={option.id === 'assign' ? modalData.assignEnergyAmount : modalData.distributeEnergyAmount}
                          min="1"
                          onChange={(e) => {
                            const value = e.target.value;
                            const updateKey = option.id === 'assign' ? 'assignEnergyAmount' : 'distributeEnergyAmount';
                            const errorKey = option.id === 'assign' ? 'assignEnergyError' : 'distributeEnergyError';

                            // 清除之前的错误
                            setInputErrors(prev => ({ ...prev, [errorKey]: '' }));

                            // 允许空值或正整数
                            if (value === '') {
                              setModalData(prev => ({ ...prev, [updateKey]: value }));
                            } else {
                              const numValue = Number(value);
                              if (Number.isInteger(numValue)) {
                                if (numValue < 1) {
                                  // 设置错误提示
                                  setInputErrors(prev => ({ ...prev, [errorKey]: '输入能量不能低于1' }));
                                  setModalData(prev => ({ ...prev, [updateKey]: value }));
                                } else {
                                  // 有效输入
                                  setModalData(prev => ({ ...prev, [updateKey]: value }));
                                  // 输入数字时自动选中当前悬停的分配按钮
                                  setModalData(prev => ({ ...prev, selectedDistribution: option.id }));
                                }
                              }
                            }
                          }}
                          onClick={(e) => {
                            e.stopPropagation();
                            // 点击输入框时自动选中当前悬停的分配按钮
                            setModalData(prev => ({ ...prev, selectedDistribution: option.id }));
                          }}
                        />
                        {/* 错误提示 */}
                        {(() => {
                          const errorKey = option.id === 'assign' ? 'assignEnergyError' : 'distributeEnergyError';
                          const errorMessage = inputErrors[errorKey as keyof typeof inputErrors];
                          return errorMessage && (
                            <div style={{
                              color: '#ef4444',
                              fontSize: '12px',
                              marginTop: '4px',
                              textAlign: 'center'
                            }}>
                              {errorMessage}
                            </div>
                          );
                        })()}
                      </div>
                    )}
                  </div>
                ))}
              </div>

              {/* 显示能量分配信息 */}
              {(modalData.selectedDistribution === 'assign' || modalData.selectedDistribution === 'distribute') && modalData.selectedStudents.length > 0 && (
                <div className="min-available-energy">
                  {loadingStudentPoints ? (
                    <span style={{ color: '#6b7280', fontStyle: 'italic' }}>
                      ⏳ 正在获取能量信息...
                    </span>
                  ) : (
                    (() => {
                      const displayInfo = getEnergyDisplayInfo();
                      return <span>{displayInfo.label}: {displayInfo.value}</span>;
                    })()
                  )}
                </div>
              )}

              {/* 错误提示 */}
              {(modalData.selectedDistribution === 'assign' || modalData.selectedDistribution === 'distribute') && getCurrentEnergyAmount() && !loadingStudentPoints && (
                (() => {
                  const energyAmountNum = Number(getCurrentEnergyAmount());

                  // 根据分配方式进行不同的验证
                  let shouldShowError = false;
                  let errorMessage = '';

                  if (modalData.selectedDistribution === 'assign') {
                    const minAvailable = getMinAvailablePoints();
                    if (energyAmountNum > minAvailable) {
                      shouldShowError = true;
                      errorMessage = '可分配积分不足';
                    }
                  } else if (modalData.selectedDistribution === 'distribute') {
                    // 对于"分配至"，检查是否有学生无法达到目标值
                    const studentsNeedingEnergy = modalData.selectedStudents.filter(studentId => {
                      const currentPoints = studentPointsMap.get(studentId) || 0;
                      return currentPoints < energyAmountNum;
                    });

                    const insufficientStudents = studentsNeedingEnergy.filter(studentId => {
                      const currentPoints = studentPointsMap.get(studentId) || 0;
                      const neededPoints = energyAmountNum - currentPoints;
                      const studentAvailablePoints = studentPointsMap.get(studentId);
                      return studentAvailablePoints !== undefined && studentAvailablePoints < neededPoints;
                    });

                    if (insufficientStudents.length > 0) {
                      shouldShowError = true;
                      errorMessage = '部分学生积分不足以达到目标值';
                    }
                  }

                  if (shouldShowError) {
                    return (
                      <div style={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        gap: '12px',
                        marginTop: '8px',
                        marginBottom: '15px'
                      }}>
                        <span style={{
                          color: '#ef4444',
                          fontSize: '14px'
                        }}>
                          {errorMessage}
                        </span>
                        <button
                          style={{
                            background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',
                            color: 'white',
                            border: 'none',
                            borderRadius: '20px',
                            padding: '6px 16px',
                            fontSize: '12px',
                            cursor: 'pointer',
                            display: 'flex',
                            alignItems: 'center',
                            gap: '4px',
                            boxShadow: '0 2px 8px rgba(239, 68, 68, 0.3)',
                            transition: 'all 0.3s ease'
                          }}
                          onMouseEnter={(e) => {
                            e.currentTarget.style.transform = 'translateY(-1px)';
                            e.currentTarget.style.boxShadow = '0 4px 12px rgba(239, 68, 68, 0.4)';
                          }}
                          onMouseLeave={(e) => {
                            e.currentTarget.style.transform = 'translateY(0)';
                            e.currentTarget.style.boxShadow = '0 2px 8px rgba(239, 68, 68, 0.3)';
                          }}
                          onClick={() => {
                            handleBatchUseKeyPackageModalOpen();
                          }}
                        >
                          🔍 前往兑换密钥
                        </button>
                      </div>
                    );
                  }
                  return null;
                })()
              )}

              {/* 模板选择区域 */}
              <div className="template-selection-area">
                <div
                  className={`template-placeholder ${modalData.selectedTemplate ? 'selected' : ''}`}
                  onClick={handleTemplatePickerOpen}
                >
                  {modalData.selectedTemplate ? modalData.selectedTemplate.templateName || modalData.selectedTemplate.name : '点击为学生选择模板'}
                  {modalData.selectedTemplate && (
                    <button
                      className="cancel-template-btn"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleCancelTemplate();
                      }}
                    >
                      ×
                    </button>
                  )}
                </div>
              </div>
              </div>
            </>
          ) : (
            // 发布任务步骤的内容
            <div className="publish-task-content">
              {/* 标签页切换 */}
              <div className="tab-switcher">
                <button
                  className={`tab-btn ${activeTab === 'task' ? 'active' : ''}`}
                  onClick={() => setActiveTab('task')}
                >
                  任务信息
                </button>
                <button
                  className={`tab-btn ${activeTab === 'resources' ? 'active' : ''}`}
                  onClick={() => setActiveTab('resources')}
                >
                  资源与附件
                </button>
              </div>

              <div className="modal-content-scrollable">
                {activeTab === 'task' ? (
                  <div className="task-info-tab">
                    <div className="form-group">
                      <input
                        type="text"
                        className="form-input"
                        placeholder="任务名称"
                        value={taskData.taskName}
                        onChange={(e) => setTaskData(prev => ({ ...prev, taskName: e.target.value }))}
                      />
                    </div>

                    <div className="form-group">
                      <textarea
                        className="form-textarea"
                        placeholder="任务描述"
                        value={taskData.taskDescription}
                        onChange={(e) => setTaskData(prev => ({ ...prev, taskDescription: e.target.value }))}
                        rows={4}
                      />
                    </div>

                    <div className="form-group">
                      <div className="self-assessment-section">
                        {taskData.selfAssessmentItems.length === 0 ? (
                          <button
                            type="button"
                            className="add-self-assessment-btn"
                            onClick={() => setTaskData(prev => ({ ...prev, selfAssessmentItems: [''] }))}
                          >
                            + 添加自评项
                          </button>
                        ) : (
                          <>
                            <label className="form-label">自评项</label>
                            {taskData.selfAssessmentItems.map((item, index) => (
                              <div key={index} className="self-assessment-item">
                                <input
                                  type="text"
                                  className="form-input"
                                  placeholder={`自评项 ${index + 1}`}
                                  value={item}
                                  onChange={(e) => {
                                    const newItems = [...taskData.selfAssessmentItems];
                                    newItems[index] = e.target.value;
                                    setTaskData(prev => ({ ...prev, selfAssessmentItems: newItems }));
                                  }}
                                />
                                <button
                                  type="button"
                                  className="remove-btn"
                                  onClick={() => {
                                    const newItems = taskData.selfAssessmentItems.filter((_, i) => i !== index);
                                    setTaskData(prev => ({ ...prev, selfAssessmentItems: newItems }));
                                  }}
                                >
                                  ×
                                </button>
                              </div>
                            ))}
                            <button
                              type="button"
                              className="add-btn"
                              onClick={() => setTaskData(prev => ({ ...prev, selfAssessmentItems: [...prev.selfAssessmentItems, ''] }))}
                            >
                              + 添加自评项
                            </button>
                          </>
                        )}
                      </div>
                    </div>

                    {/* 时间设置 */}
                    <div className="form-group">
                      <div className="time-settings">
                        <div className="time-row">
                          <div className="time-field-container">
                            <div
                              className={`time-field ${taskData.startTime ? 'has-selected-date' : ''}`}
                              onClick={(e) => {
                                e.stopPropagation();
                                setShowQuickTimeSelector(showQuickTimeSelector === 'start' ? null : 'start');
                              }}
                            >
                              <input
                                type="text"
                                className="form-input"
                                value={formatTimeDisplay(taskData.startTime)}
                                placeholder="点击设置开始时间"
                                readOnly
                              />
                            </div>
                            {showQuickTimeSelector === 'start' && (
                              <div className="quick-time-selector" onClick={(e) => e.stopPropagation()}>
                                <button
                                  className="quick-time-btn"
                                  onClick={() => handleQuickTimeSelect('现在', 'start')}
                                >
                                  现在
                                </button>
                              </div>
                            )}
                          </div>
                          <div className="time-field-container">
                            <div
                              className={`time-field ${taskData.endTime ? 'has-selected-date' : ''}`}
                              onClick={(e) => {
                                e.stopPropagation();
                                setShowQuickTimeSelector(showQuickTimeSelector === 'end' ? null : 'end');
                              }}
                            >
                              <input
                                type="text"
                                className="form-input"
                                value={formatTimeDisplay(taskData.endTime)}
                                placeholder="点击设置结束时间"
                                readOnly
                              />
                            </div>
                            {showQuickTimeSelector === 'end' && (
                              <div className="quick-time-selector" onClick={(e) => e.stopPropagation()}>
                                <button
                                  className="quick-time-btn"
                                  onClick={() => handleQuickTimeSelect('1小时', 'end')}
                                >
                                  1小时
                                </button>
                                <button
                                  className="quick-time-btn"
                                  onClick={() => handleQuickTimeSelect('6小时', 'end')}
                                >
                                  6小时
                                </button>
                                <button
                                  className="quick-time-btn"
                                  onClick={() => handleQuickTimeSelect('12小时', 'end')}
                                >
                                  12小时
                                </button>
                                <button
                                  className="quick-time-btn"
                                  onClick={() => handleQuickTimeSelect('1天', 'end')}
                                >
                                  1天
                                </button>
                                <button
                                  className="quick-time-btn"
                                  onClick={() => handleQuickTimeSelect('7天', 'end')}
                                >
                                  7天
                                </button>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="resources-tab">
                    <div className="works-section">
                      <h4>选择作品</h4>
                      <p className="help-text">选择作品作为任务参考资料（可多选）</p>
                      <div
                        className="relative works-scroll-wrapper"
                        onWheel={handleWheelScroll}
                        onMouseDown={handleMouseDown}
                        onMouseMove={handleMouseMove}
                        onMouseUp={handleMouseUp}
                        onMouseLeave={handleMouseLeave}
                        style={{
                          minHeight: '200px',
                          cursor: 'grab',
                          userSelect: 'none'
                        }}
                      >
                        {loadingWorks ? (
                          <div
                            className="loading-container"
                            style={{ minHeight: '200px' }}
                          >
                            <div className="loading-spinner"></div>
                            <span>加载中...</span>
                          </div>
                        ) : works.length > 0 ? (
                          <div className="works-horizontal-scroll">
                            {works.map((work) => (
                              <div
                                key={work.id}
                                className={`work-card ${selectedWorkIds.includes(work.id) ? 'selected' : ''}`}
                                onClick={() => handleSelectWork(work.id)}
                              >
                                <div className="work-image">
                                  {work.coverImage || work.screenShotImage ? (
                                    <img
                                      src={fixImageUrl(work.coverImage || work.screenShotImage)}
                                      alt={work.title}
                                      onError={(e) => {
                                        e.currentTarget.style.display = 'none';
                                        const nextElement = e.currentTarget.nextElementSibling as HTMLElement;
                                        if (nextElement) {
                                          nextElement.style.display = 'flex';
                                        }
                                      }}
                                    />
                                  ) : null}
                                  <div className="work-placeholder" style={{ display: work.coverImage || work.screenShotImage ? 'none' : 'flex' }}>
                                    作品
                                  </div>
                                </div>
                                <div className="work-title">{work.title || work.name || work.workName || '未命名作品'}</div>
                                {selectedWorkIds.includes(work.id) && (
                                  <div className="selected-indicator">✓</div>
                                )}
                              </div>
                            ))}
                          </div>
                        ) : (
                          <div className="empty-placeholder">
                            <div className="empty-text">作品列表</div>
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="attachments-section">
                      <h4>附件上传</h4>
                      <div className="upload-area">
                        <input
                          type="file"
                          multiple
                          accept=".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt"
                          onChange={(e) => {
                            if (e.target.files) {
                              const files = Array.from(e.target.files);
                              const validFiles: File[] = [];
                              const invalidFiles: string[] = [];

                              // 支持的文件格式
                              const allowedTypes = [
                                'image/jpeg', 'image/jpg', 'image/png', 'image/gif',
                                'application/pdf',
                                'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                                'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                                'application/vnd.ms-powerpoint', 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
                                'text/plain'
                              ];

                              // 文件扩展名检查（作为备用验证）
                              const allowedExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt'];

                              files.forEach(file => {
                                // 检查文件大小（10MB = 10 * 1024 * 1024 bytes）
                                if (file.size > 10 * 1024 * 1024) {
                                  invalidFiles.push(`${file.name}：文件大小超过10MB`);
                                  return;
                                }

                                // 检查文件类型
                                const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
                                const isValidType = allowedTypes.includes(file.type) || allowedExtensions.includes(fileExtension);

                                if (!isValidType) {
                                  invalidFiles.push(`${file.name}：不支持的文件格式`);
                                  return;
                                }

                                validFiles.push(file);
                              });

                              // 添加有效文件
                              if (validFiles.length > 0) {
                                setAttachments(prev => [...prev, ...validFiles]);
                              }

                              // 显示错误信息
                              if (invalidFiles.length > 0) {
                                alert(`以下文件无法上传：\n${invalidFiles.join('\n')}`);
                              }

                              // 重置input的value，确保可以重复选择相同文件
                              e.target.value = '';
                            }
                          }}
                          style={{ display: 'none' }}
                          id="file-upload"
                        />
                        <label htmlFor="file-upload" className="upload-btn">
                          +
                        </label>
                        <span className="file-format-info">
                          支持 jpg、png、gif、pdf、doc、docx、xls、xlsx、ppt、pptx、txt 格式，单个文件大小不超过10MB
                        </span>
                      </div>
                      {attachments.length > 0 && (
                        <div className="attachments-list">
                          {attachments.map((file, index) => (
                            <div key={index} className="attachment-item">
                              <span className="file-name">{file.name}</span>
                              <button
                                onClick={() => setAttachments(prev => prev.filter((_, i) => i !== index))}
                                className="remove-attachment-btn"
                              >
                                ×
                              </button>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* 底部按钮 */}
          <div className="modal-footer">
            <button className="prev-btn" onClick={handlePrevious}>
              上一步
            </button>
            <button
              className={`next-btn ${
                (currentStep === 'template' && (modalData.selectedTemplate || modalData.selectedDistribution)) ||
                (currentStep === 'publish' && taskData.taskName.trim() && !isPublishing)
                ? 'enabled' : 'disabled'
              } ${isPublishing ? 'publishing' : ''}`}
              onClick={currentStep === 'template' ? handleNext : handleConfirm}
              disabled={
                currentStep === 'template'
                  ? (!modalData.selectedTemplate && !modalData.selectedDistribution)
                  : !taskData.taskName.trim() || isPublishing
              }
            >
              {currentStep === 'template' ? (
                (actionType === '发布任务' || actionType === '快速上课') ? '下一步' : '完成'
              ) : (
                isPublishing ? (
                  <span className="publishing-content">
                    <span className="spinner"></span>
                    发布中...
                  </span>
                ) : '开始上课'
              )}
            </button>
          </div>
        </div>
      </div>

      {/* 模板选择弹窗 */}
      <TemplatePickerModal
        isOpen={isTemplatePickerOpen}
        onClose={handleTemplatePickerClose}
        onTemplateSelect={handleTemplateSelect}
      />

      {/* 批量兑换密钥模态框 */}
      <BatchUseKeyPackageModal
        open={isBatchUseKeyPackageModalOpen}
        selectedStudentIds={modalData.selectedStudents}
        students={students}
        onClose={handleBatchUseKeyPackageModalClose}
        onSuccess={handleBatchUseKeyPackageSuccess}
      />
      </div>
    </div>
  );
};

export default TemplateSelectionModal;
