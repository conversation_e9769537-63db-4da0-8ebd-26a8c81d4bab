"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/workbench/page",{

/***/ "(app-pages-browser)/./app/workbench/components/CourseListEditModal.tsx":
/*!**********************************************************!*\
  !*** ./app/workbench/components/CourseListEditModal.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FileText,Plus,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FileText,Plus,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FileText,Plus,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FileText,Plus,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FileText,Plus,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _lib_api_course_management__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api/course-management */ \"(app-pages-browser)/./lib/api/course-management.ts\");\n/* harmony import */ var _lib_api_course__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api/course */ \"(app-pages-browser)/./lib/api/course.ts\");\n/* harmony import */ var _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api/upload */ \"(app-pages-browser)/./lib/api/upload.ts\");\n/* harmony import */ var _lib_api_works__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/api/works */ \"(app-pages-browser)/./lib/api/works.ts\");\n/* harmony import */ var _barrel_optimize_names_Select_antd__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Select!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/select/index.js\");\n/* harmony import */ var logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\");\n/* harmony import */ var _TemplatePickerModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./TemplatePickerModal */ \"(app-pages-browser)/./app/workbench/components/TemplatePickerModal.tsx\");\n/* harmony import */ var _CourseListEditModal_css__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./CourseListEditModal.css */ \"(app-pages-browser)/./app/workbench/components/CourseListEditModal.css\");\n/* harmony import */ var _NewPublishTaskModal_css__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./NewPublishTaskModal.css */ \"(app-pages-browser)/./app/workbench/components/NewPublishTaskModal.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n// 获取系列课程详情\nconst fetchSeriesDetail = async (seriesId)=>{\n    console.log(\"\\uD83D\\uDD0D 获取系列课程详情，seriesId:\", seriesId);\n    console.log(\"\\uD83D\\uDD17 调用API: GET /api/v1/course-management/series/{seriesId}\");\n    const response = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getSeriesDetail(seriesId);\n    console.log(\"\\uD83D\\uDCE1 系列详情API响应:\", response);\n    return response.data;\n};\n// API调用函数 - 获取系列下的所有课程（包含未发布和已发布）\nconst fetchCourseList = async (seriesId)=>{\n    console.log(\"\\uD83D\\uDD0D 获取系列课程列表，seriesId:\", seriesId);\n    console.log(\"\\uD83D\\uDD17 调用API: GET /api/v1/course-management/series/{seriesId}/courses\");\n    // 使用课程管理API获取所有状态的课程\n    const response = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getManagementSeriesCourses(seriesId, {\n        page: 1,\n        pageSize: 100\n    });\n    console.log(\"\\uD83D\\uDCE1 API响应:\", response);\n    return response.data;\n};\n// 获取课程详情\nconst fetchCourseDetail = async (seriesId, courseId)=>{\n    return await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getCourseMarketplaceDetail(seriesId, courseId);\n};\n// 获取课程标签\nconst fetchCourseTags = async ()=>{\n    try {\n        console.log(\"\\uD83D\\uDD0D 开始调用 courseApi.getCourseTags\");\n        const result = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getCourseTags({\n            page: 1,\n            pageSize: 100,\n            status: 1 // 只获取启用的标签\n        });\n        console.log(\"\\uD83D\\uDD0D courseApi.getCourseTags 返回结果:\", result);\n        return result;\n    } catch (error) {\n        console.error(\"\\uD83D\\uDD0D courseApi.getCourseTags 调用失败:\", error);\n        throw error;\n    }\n};\nconst CourseListEditModal = (param)=>{\n    let { isVisible, onClose, onSave, seriesTitle, seriesCoverImage, seriesId = 123 // 默认值，实际使用时应该传入真实的seriesId\n     } = param;\n    var _getSelectedCourse, _getSelectedCourse1, _getSelectedCourse2, _getSelectedCourse3, _courseDetail_contentConfig_video, _courseDetail_contentConfig, _courseDetail_contentConfig_document, _courseDetail_contentConfig1, _courseList_find, _courseList_find1;\n    _s();\n    const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_6__.GetNotification)();\n    const [courseList, setCourseList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [rightPanelType, setRightPanelType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"none\");\n    const [selectedCourseId, setSelectedCourseId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [uploadingFiles, setUploadingFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [isPublishing, setIsPublishing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isCreating, setIsCreating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPublishingSeries, setIsPublishingSeries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [seriesStatus, setSeriesStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0); // 0=草稿，1=已发布，2=已归档\n    // 删除确认弹窗状态\n    const [deleteConfirmVisible, setDeleteConfirmVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [courseToDelete, setCourseToDelete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isDeleting, setIsDeleting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 模板选择弹窗状态\n    const [isTemplatePickerOpen, setIsTemplatePickerOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingTitle, setEditingTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(seriesTitle);\n    const [courseGoals, setCourseGoals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [courseObjectives, setCourseObjectives] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [projectMembers, setProjectMembers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // 作品相关状态\n    const [works, setWorks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loadingWorks, setLoadingWorks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedWorkIds, setSelectedWorkIds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // 能量输入验证状态\n    const [energyError, setEnergyError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // 时间选择状态\n    const [showDurationPicker, setShowDurationPicker] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 课程标签相关状态\n    const [courseTags, setCourseTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedTags, setSelectedTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [tagsLoading, setTagsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 课程详细编辑状态\n    const [courseDetail, setCourseDetail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: \"\",\n        description: \"\",\n        coverImage: \"\",\n        videoUrl: \"\",\n        videoName: \"\",\n        isVideoEnabled: false,\n        attachmentUrl: \"\",\n        attachmentName: \"\",\n        isAttachmentEnabled: false,\n        teachingMaterials: [],\n        // 支持teachingInfo结构\n        teachingInfo: [],\n        // 支持contentConfig结构\n        contentConfig: {\n            hasVideo: 0,\n            hasDocument: 0,\n            hasAudio: 0,\n            video: {\n                url: \"\",\n                name: \"\"\n            },\n            document: {\n                url: \"\",\n                name: \"\"\n            },\n            audio: {\n                url: \"\",\n                name: \"\"\n            }\n        },\n        courseContent: {\n            topic: \"\",\n            content: \"\"\n        },\n        isOneKeyOpen: false,\n        isDistributionEnabled: false,\n        distributionReward: \"\",\n        selectedTemplate: {\n            id: null,\n            name: \"\"\n        },\n        isDistributionWater: false,\n        requiredEnergy: \"\",\n        energyAmount: \"\",\n        isDistributionLimit: false,\n        distributionConditions: {\n            inviteCount: \"\",\n            taskCount: \"\",\n            experience: \"\"\n        },\n        isDistributionTime: false,\n        distributionTimeConditions: {\n            startTime: \"\",\n            endTime: \"\"\n        },\n        distributionMaterials: [],\n        // 任务配置相关状态\n        taskConfig: {\n            taskName: \"\",\n            taskDuration: \"\",\n            taskDescription: \"\",\n            selfAssessmentItems: [\n                \"\"\n            ],\n            referenceWorks: [],\n            referenceResources: []\n        }\n    });\n    // 获取课程列表数据\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isVisible && seriesId) {\n            // 检查用户登录状态\n            const token = localStorage.getItem(\"token\");\n            console.log(\"\\uD83D\\uDD10 检查登录状态，token存在:\", !!token);\n            console.log(\"\\uD83D\\uDD0D seriesId:\", seriesId);\n            if (!token) {\n                console.error(\"❌ 用户未登录，无法获取课程列表\");\n                // 设置空列表，显示空状态\n                setCourseList([]);\n                setLoading(false);\n                return;\n            }\n            loadCourseList();\n            loadCourseTags();\n            loadSeriesDetail();\n            loadUserWorks();\n        }\n    }, [\n        isVisible,\n        seriesId\n    ]);\n    const loadCourseList = async ()=>{\n        try {\n            setLoading(true);\n            console.log(\"\\uD83D\\uDD0D 开始加载课程列表，seriesId:\", seriesId);\n            const response = await fetchCourseList(seriesId);\n            console.log(\"\\uD83D\\uDCE1 API响应:\", response);\n            if (response.code === 200) {\n                console.log(\"✅ 课程列表数据:\", response.data);\n                const courses = response.data.list || [];\n                console.log(\"✅ 解析的课程数组:\", courses);\n                console.log(\"\\uD83D\\uDCCA 课程ID详情:\", courses.map((c)=>({\n                        id: c.id,\n                        type: typeof c.id,\n                        title: c.title,\n                        status: c.status\n                    })));\n                console.log(\"\\uD83D\\uDCCA 课程状态统计:\", {\n                    total: courses.length,\n                    draft: courses.filter((c)=>c.status === 0).length,\n                    published: courses.filter((c)=>c.status === 1).length\n                });\n                setCourseList(courses);\n            } else {\n                console.error(\"❌ API返回错误:\", response);\n                setCourseList([]);\n            }\n        } catch (error) {\n            var _error_response, _error_response1, _error_response2;\n            console.error(\"❌ 加载课程列表失败:\", error);\n            // 检查是否是认证错误\n            if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 401) {\n                console.error(\"\\uD83D\\uDD10 认证失败，用户未登录或token已过期\");\n            } else if (((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status) === 403) {\n                console.error(\"\\uD83D\\uDEAB 权限不足，无法访问该系列课程\");\n            } else if (((_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.status) === 404) {\n                console.error(\"\\uD83D\\uDCED 系列课程不存在，seriesId:\", seriesId);\n            } else {\n                console.error(\"\\uD83D\\uDD27 其他错误:\", error.message);\n            }\n            setCourseList([]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 加载系列课程详情\n    const loadSeriesDetail = async ()=>{\n        try {\n            console.log(\"\\uD83D\\uDD0D 开始加载系列课程详情，seriesId:\", seriesId);\n            const response = await fetchSeriesDetail(seriesId);\n            console.log(\"\\uD83D\\uDCE1 系列详情响应:\", response);\n            if (response.code === 200 && response.data) {\n                const seriesData = response.data;\n                console.log(\"✅ 系列课程详情:\", seriesData);\n                setSeriesStatus(seriesData.status || 0);\n                console.log(\"\\uD83D\\uDCCA 系列课程状态:\", seriesData.status, \"(0=草稿，1=已发布，2=已归档)\");\n            } else {\n                console.error(\"❌ 获取系列详情失败:\", response.message);\n            }\n        } catch (error) {\n            console.error(\"❌ 加载系列详情异常:\", error);\n        }\n    };\n    // 加载用户作品数据\n    const loadUserWorks = async ()=>{\n        try {\n            var _response_data;\n            setLoadingWorks(true);\n            // 从localStorage获取用户ID\n            const userId = localStorage.getItem(\"userId\") || \"2896\"; // 默认使用2896\n            const response = await _lib_api_works__WEBPACK_IMPORTED_MODULE_5__.worksApi.getTeacherWorks(Number(userId), 1, 1000);\n            // 检查多种可能的数据结构\n            let worksList = [];\n            if (((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.code) === 200) {\n                var _response_data_data, _response_data1, _response_data2;\n                // 情况1: response.data.data.list\n                if ((_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : (_response_data_data = _response_data1.data) === null || _response_data_data === void 0 ? void 0 : _response_data_data.list) {\n                    worksList = response.data.data.list;\n                } else if (Array.isArray((_response_data2 = response.data) === null || _response_data2 === void 0 ? void 0 : _response_data2.data)) {\n                    worksList = response.data.data;\n                } else if (Array.isArray(response.data)) {\n                    worksList = response.data;\n                }\n            } else if (Array.isArray(response)) {\n                worksList = response;\n            }\n            setWorks(worksList);\n        } catch (error) {\n            console.error(\"加载作品数据失败:\", error);\n            setWorks([]);\n        } finally{\n            setLoadingWorks(false);\n        }\n    };\n    // 处理作品选择\n    const handleWorkSelect = (workId)=>{\n        if (selectedWorkIds.includes(workId)) {\n            // 取消选中\n            setSelectedWorkIds((prev)=>prev.filter((id)=>id !== workId));\n        } else {\n            // 选中\n            setSelectedWorkIds((prev)=>[\n                    ...prev,\n                    workId\n                ]);\n        }\n    };\n    // 鼠标滚轮事件处理 - 将垂直滚轮转换为水平滚动\n    const handleWheel = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e)=>{\n        const container = e.currentTarget;\n        const { scrollWidth, clientWidth } = container;\n        // 检查是否可以滚动\n        if (scrollWidth <= clientWidth) {\n            return; // 内容不够长，不需要滚动\n        }\n        // 阻止默认的垂直滚动行为\n        e.preventDefault();\n        // 将垂直滚轮转换为水平滚动\n        container.scrollLeft += e.deltaY;\n    }, []);\n    // 处理能量输入变化\n    const handleEnergyChange = (value)=>{\n        setCourseDetail((prev)=>({\n                ...prev,\n                requiredEnergy: value\n            }));\n        // 验证输入值\n        const numValue = parseFloat(value);\n        if (value && (isNaN(numValue) || numValue < 0)) {\n            setEnergyError(\"分配能力值不能为负数\");\n        } else {\n            setEnergyError(\"\");\n        }\n    };\n    // 处理时间选择\n    const handleDurationSelect = (hours, label)=>{\n        setCourseDetail((prev)=>({\n                ...prev,\n                taskConfig: {\n                    ...prev.taskConfig,\n                    taskDuration: \"\".concat(hours, \"小时 (\").concat(label, \")\")\n                }\n            }));\n        setShowDurationPicker(false);\n    };\n    // 点击外部关闭时间选择器\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleClickOutside = (event)=>{\n            const target = event.target;\n            if (!target.closest(\".duration-input-wrapper\")) {\n                setShowDurationPicker(false);\n            }\n        };\n        if (showDurationPicker) {\n            document.addEventListener(\"mousedown\", handleClickOutside);\n            return ()=>document.removeEventListener(\"mousedown\", handleClickOutside);\n        }\n    }, [\n        showDurationPicker\n    ]);\n    // 加载课程标签\n    const loadCourseTags = async ()=>{\n        try {\n            setTagsLoading(true);\n            console.log(\"\\uD83C\\uDFF7️ 开始加载课程标签\");\n            const response = await fetchCourseTags();\n            console.log(\"\\uD83D\\uDCE1 标签API完整响应:\", response);\n            // 检查响应结构\n            if (response && response.data) {\n                console.log(\"\\uD83D\\uDCCA 响应数据:\", response.data);\n                let tags = [];\n                // 处理标准的API响应格式 (response.data.list) - 根据实际API响应\n                if (response.data.list && Array.isArray(response.data.list)) {\n                    tags = response.data.list;\n                    console.log(\"✅ 从 data.list 解析到标签:\", tags.length, \"个\");\n                } else if (Array.isArray(response.data)) {\n                    tags = response.data;\n                    console.log(\"✅ 从 data 数组解析到标签:\", tags.length, \"个\");\n                } else if (response.data.data && response.data.data.list && Array.isArray(response.data.data.list)) {\n                    tags = response.data.data.list;\n                    console.log(\"✅ 从 data.data.list 解析到标签:\", tags.length, \"个\");\n                }\n                // 验证标签数据格式\n                console.log(\"\\uD83D\\uDD0D 原始标签数据:\", tags);\n                console.log(\"\\uD83D\\uDD0D 标签数据类型检查:\");\n                tags.forEach((tag, index)=>{\n                    var _tag_name;\n                    console.log(\"标签\".concat(index, \":\"), {\n                        tag,\n                        hasTag: !!tag,\n                        idType: typeof (tag === null || tag === void 0 ? void 0 : tag.id),\n                        nameType: typeof (tag === null || tag === void 0 ? void 0 : tag.name),\n                        nameValue: tag === null || tag === void 0 ? void 0 : tag.name,\n                        nameNotEmpty: (tag === null || tag === void 0 ? void 0 : (_tag_name = tag.name) === null || _tag_name === void 0 ? void 0 : _tag_name.trim()) !== \"\"\n                    });\n                });\n                const validTags = tags.filter((tag)=>{\n                    const isValid = tag && typeof tag.id === \"number\" && typeof tag.name === \"string\" && tag.name.trim() !== \"\";\n                    if (!isValid) {\n                        console.log(\"❌ 无效标签:\", tag, {\n                            hasTag: !!tag,\n                            idType: typeof (tag === null || tag === void 0 ? void 0 : tag.id),\n                            nameType: typeof (tag === null || tag === void 0 ? void 0 : tag.name),\n                            nameValue: tag === null || tag === void 0 ? void 0 : tag.name\n                        });\n                    }\n                    return isValid;\n                });\n                console.log(\"✅ 有效标签数量:\", validTags.length);\n                console.log(\"✅ 有效标签详情:\", validTags);\n                if (validTags.length > 0) {\n                    setCourseTags(validTags);\n                    console.log(\"✅ 成功设置真实标签数据\");\n                    return;\n                } else {\n                    console.warn(\"⚠️ 没有有效的标签数据\");\n                }\n            } else {\n                console.warn(\"⚠️ API响应格式不正确:\", response);\n            }\n            // 如果没有真实数据，设置空数组\n            console.log(\"\\uD83D\\uDCED 没有标签数据，设置空数组\");\n            setCourseTags([]);\n        } catch (error) {\n            var _error_response, _error_response1, _error_response2;\n            console.error(\"❌ 加载课程标签失败:\", error);\n            console.error(\"❌ 错误详情:\", {\n                message: error.message,\n                status: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status,\n                statusText: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.statusText,\n                data: (_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.data\n            });\n            // 发生错误时设置空数组\n            setCourseTags([]);\n        } finally{\n            setTagsLoading(false);\n        }\n    };\n    // 添加新课程\n    const addNewCourse = ()=>{\n        const newCourse = {\n            id: Date.now(),\n            seriesId: seriesId,\n            title: \"第\".concat(courseList.length + 1, \"课 - 新课时\"),\n            description: \"\",\n            coverImage: \"\",\n            orderIndex: courseList.length + 1,\n            status: 0,\n            statusLabel: \"草稿\",\n            hasVideo: 0,\n            hasDocument: 0,\n            hasAudio: 0,\n            videoDuration: 0,\n            videoDurationLabel: \"\",\n            videoName: \"\",\n            firstTeachingTitle: \"\",\n            resourcesCount: 0,\n            createdAt: new Date().toISOString(),\n            updatedAt: new Date().toISOString()\n        };\n        setCourseList([\n            ...courseList,\n            newCourse\n        ]);\n        // 自动选中新添加的课程\n        showCoursePanel(newCourse.id);\n    };\n    // 显示删除确认弹窗\n    const showDeleteConfirm = (id)=>{\n        setCourseToDelete(id);\n        setDeleteConfirmVisible(true);\n    };\n    // 确认删除课程\n    const confirmDeleteCourse = async ()=>{\n        if (!courseToDelete) return;\n        try {\n            setIsDeleting(true);\n            // 调用删除API\n            await _lib_api_course_management__WEBPACK_IMPORTED_MODULE_2__.courseManagementApi.deleteCourse(courseToDelete);\n            // 从列表中移除课程\n            setCourseList(courseList.filter((course)=>course.id !== courseToDelete));\n            // 如果删除的是当前选中的课程，清空右侧面板\n            if (selectedCourseId === courseToDelete) {\n                setRightPanelType(\"none\");\n                setSelectedCourseId(null);\n            }\n            // 关闭确认弹窗\n            setDeleteConfirmVisible(false);\n            setCourseToDelete(null);\n            // 显示成功提示\n            notification.success(\"课程已成功删除\");\n        } catch (error) {\n            console.error(\"删除课程失败:\", error);\n            notification.error(\"删除课程失败，请重试\");\n        } finally{\n            setIsDeleting(false);\n        }\n    };\n    // 取消删除\n    const cancelDelete = ()=>{\n        if (isDeleting) return; // 正在删除时不允许取消\n        setDeleteConfirmVisible(false);\n        setCourseToDelete(null);\n    };\n    // 更新课程标题\n    const updateCourseTitle = (id, newTitle)=>{\n        setCourseList(courseList.map((course)=>course.id === id ? {\n                ...course,\n                title: newTitle\n            } : course));\n    };\n    // 处理课程封面上传\n    const handleCoverUpload = async (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (file) {\n            // 检查文件类型\n            const allowedTypes = [\n                \"image/jpeg\",\n                \"image/jpg\",\n                \"image/png\",\n                \"image/gif\"\n            ];\n            if (!allowedTypes.includes(file.type)) {\n                notification.error(\"请选择 JPG、PNG 或 GIF 格式的图片文件\");\n                return;\n            }\n            // 检查文件大小 (10MB)\n            if (file.size > 10 * 1024 * 1024) {\n                notification.error(\"文件大小不能超过 10MB\");\n                return;\n            }\n            try {\n                console.log(\"\\uD83D\\uDCE4 开始上传课程封面:\", file.name);\n                // 添加到上传中的文件列表\n                setUploadingFiles((prev)=>new Set(prev).add(\"cover\"));\n                // 先显示预览图片\n                const previewUrl = URL.createObjectURL(file);\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        coverImage: previewUrl\n                    }));\n                // 上传到OSS\n                const imageUrl = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n                console.log(\"✅ 课程封面上传成功:\", imageUrl);\n                // 更新课程详情中的封面为真实URL\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        coverImage: imageUrl\n                    }));\n                // 同时更新课程列表中的封面\n                if (selectedCourseId) {\n                    setCourseList((prev)=>prev.map((course)=>course.id === selectedCourseId ? {\n                                ...course,\n                                coverImage: imageUrl\n                            } : course));\n                }\n                alert(\"课程封面上传成功！\");\n            } catch (error) {\n                console.error(\"❌ 课程封面上传失败:\", error);\n                notification.error(\"课程封面上传失败，请重试\");\n                // 上传失败时清除预览图片\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        coverImage: \"\"\n                    }));\n            } finally{\n                // 从上传中的文件列表移除\n                setUploadingFiles((prev)=>{\n                    const newSet = new Set(prev);\n                    newSet.delete(\"cover\");\n                    return newSet;\n                });\n            }\n        }\n    };\n    // 处理视频上传\n    const handleVideoUpload = async (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (file) {\n            // 检查文件类型\n            const allowedTypes = [\n                \"video/mp4\",\n                \"video/avi\",\n                \"video/mov\",\n                \"video/wmv\",\n                \"video/flv\"\n            ];\n            if (!allowedTypes.includes(file.type)) {\n                alert(\"请选择 MP4、AVI、MOV、WMV 或 FLV 格式的视频文件\");\n                return;\n            }\n            // 检查文件大小 (100MB)\n            if (file.size > 100 * 1024 * 1024) {\n                alert(\"视频文件大小不能超过 100MB\");\n                return;\n            }\n            try {\n                console.log(\"\\uD83D\\uDCE4 开始上传课程视频:\", file.name);\n                // 添加到上传中的文件列表\n                setUploadingFiles((prev)=>new Set(prev).add(\"video\"));\n                // 先显示预览视频\n                const previewUrl = URL.createObjectURL(file);\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        contentConfig: {\n                            ...prev.contentConfig,\n                            video: {\n                                url: previewUrl,\n                                name: file.name\n                            }\n                        }\n                    }));\n                // 上传到OSS\n                const videoUrl = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n                console.log(\"✅ 课程视频上传成功:\", videoUrl);\n                // 更新课程详情中的视频信息为真实URL\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        contentConfig: {\n                            ...prev.contentConfig,\n                            video: {\n                                url: videoUrl,\n                                name: file.name\n                            }\n                        }\n                    }));\n                // 同时更新课程列表中的视频信息\n                if (selectedCourseId) {\n                    setCourseList((prev)=>prev.map((course)=>course.id === selectedCourseId ? {\n                                ...course,\n                                contentConfig: {\n                                    ...course.contentConfig,\n                                    video: {\n                                        url: videoUrl,\n                                        name: file.name\n                                    }\n                                }\n                            } : course));\n                }\n                alert(\"课程视频上传成功！\");\n            } catch (error) {\n                console.error(\"❌ 课程视频上传失败:\", error);\n                alert(\"课程视频上传失败，请重试\");\n                // 上传失败时清除视频信息\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        contentConfig: {\n                            ...prev.contentConfig,\n                            video: {\n                                url: \"\",\n                                name: \"\"\n                            }\n                        }\n                    }));\n            } finally{\n                // 从上传中的文件列表移除\n                setUploadingFiles((prev)=>{\n                    const newSet = new Set(prev);\n                    newSet.delete(\"video\");\n                    return newSet;\n                });\n            }\n        }\n    };\n    // 触发视频文件选择\n    const triggerVideoUpload = ()=>{\n        const input = document.createElement(\"input\");\n        input.type = \"file\";\n        input.accept = \"video/mp4,video/avi,video/mov,video/wmv,video/flv\";\n        input.onchange = (e)=>handleVideoUpload(e);\n        input.click();\n    };\n    // 处理附件上传\n    const handleAttachmentUpload = async (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (file) {\n            // 检查文件类型\n            const allowedTypes = [\n                \"application/pdf\",\n                \"application/msword\",\n                \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\",\n                \"application/vnd.ms-excel\",\n                \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\",\n                \"application/vnd.ms-powerpoint\",\n                \"application/vnd.openxmlformats-officedocument.presentationml.presentation\",\n                \"text/plain\",\n                \"image/jpeg\",\n                \"image/png\",\n                \"image/gif\"\n            ];\n            if (!allowedTypes.includes(file.type)) {\n                alert(\"请选择支持的文件格式：PDF、DOC、DOCX、XLS、XLSX、PPT、PPTX、TXT、JPG、PNG、GIF\");\n                return;\n            }\n            // 检查文件大小 (10MB)\n            if (file.size > 10 * 1024 * 1024) {\n                alert(\"文件大小不能超过 10MB\");\n                return;\n            }\n            try {\n                console.log(\"\\uD83D\\uDCE4 开始上传课程附件:\", file.name);\n                // 添加到上传中的文件列表\n                setUploadingFiles((prev)=>new Set(prev).add(\"document\"));\n                // 上传到OSS\n                const documentUrl = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n                console.log(\"✅ 课程附件上传成功:\", documentUrl);\n                // 更新课程详情中的附件信息\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        contentConfig: {\n                            ...prev.contentConfig,\n                            hasDocument: 1,\n                            document: {\n                                url: documentUrl,\n                                name: file.name\n                            }\n                        }\n                    }));\n                alert(\"课程附件上传成功！\");\n            } catch (error) {\n                console.error(\"❌ 课程附件上传失败:\", error);\n                alert(\"课程附件上传失败，请重试\");\n            } finally{\n                // 从上传中的文件列表移除\n                setUploadingFiles((prev)=>{\n                    const newSet = new Set(prev);\n                    newSet.delete(\"document\");\n                    return newSet;\n                });\n            }\n        }\n    };\n    // 触发附件文件选择\n    const triggerAttachmentUpload = ()=>{\n        const input = document.createElement(\"input\");\n        input.type = \"file\";\n        input.accept = \".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.jpg,.png,.gif\";\n        input.onchange = (e)=>handleAttachmentUpload(e);\n        input.click();\n    };\n    // 处理教学附件上传\n    const handleTeachingMaterialUpload = async (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (file) {\n            // 检查文件类型\n            const allowedTypes = [\n                \"application/pdf\",\n                \"application/msword\",\n                \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\",\n                \"application/vnd.ms-excel\",\n                \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\",\n                \"application/vnd.ms-powerpoint\",\n                \"application/vnd.openxmlformats-officedocument.presentationml.presentation\",\n                \"text/plain\",\n                \"image/jpeg\",\n                \"image/png\",\n                \"image/gif\"\n            ];\n            if (!allowedTypes.includes(file.type)) {\n                alert(\"请选择支持的文件格式：PDF、DOC、DOCX、XLS、XLSX、PPT、PPTX、TXT、JPG、PNG、GIF\");\n                return;\n            }\n            // 检查文件大小 (10MB)\n            if (file.size > 10 * 1024 * 1024) {\n                alert(\"文件大小不能超过 10MB\");\n                return;\n            }\n            try {\n                console.log(\"\\uD83D\\uDCE4 开始上传教学材料:\", file.name);\n                // 添加到上传中的文件列表\n                setUploadingFiles((prev)=>new Set(prev).add(\"teaching-\".concat(Date.now())));\n                // 上传到OSS\n                const materialUrl = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n                console.log(\"✅ 教学材料上传成功:\", materialUrl);\n                // 添加到教学附件列表\n                const newMaterial = {\n                    type: file.type,\n                    name: file.name,\n                    url: materialUrl\n                };\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        teachingMaterials: [\n                            ...prev.teachingMaterials,\n                            newMaterial\n                        ]\n                    }));\n                alert(\"教学材料上传成功！\");\n            } catch (error) {\n                console.error(\"❌ 教学材料上传失败:\", error);\n                alert(\"教学材料上传失败，请重试\");\n            } finally{\n                // 从上传中的文件列表移除\n                setUploadingFiles((prev)=>{\n                    const newSet = new Set(prev);\n                    // 移除所有teaching-开头的项目\n                    Array.from(newSet).forEach((item)=>{\n                        if (item.startsWith(\"teaching-\")) {\n                            newSet.delete(item);\n                        }\n                    });\n                    return newSet;\n                });\n            }\n        }\n    };\n    // 触发教学附件文件选择\n    const triggerTeachingMaterialUpload = ()=>{\n        const input = document.createElement(\"input\");\n        input.type = \"file\";\n        input.accept = \".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.jpg,.png,.gif\";\n        input.onchange = (e)=>handleTeachingMaterialUpload(e);\n        input.click();\n    };\n    // 删除教学附件\n    const removeTeachingMaterial = (index)=>{\n        setCourseDetail((prev)=>({\n                ...prev,\n                teachingMaterials: prev.teachingMaterials.filter((_, i)=>i !== index)\n            }));\n    };\n    // 课程内容管理函数\n    const addTeachingInfoItem = ()=>{\n        setCourseDetail((prev)=>({\n                ...prev,\n                teachingInfo: [\n                    ...prev.teachingInfo,\n                    {\n                        title: \"\",\n                        content: \"\"\n                    }\n                ]\n            }));\n    };\n    const removeTeachingInfoItem = (index)=>{\n        setCourseDetail((prev)=>({\n                ...prev,\n                teachingInfo: prev.teachingInfo.filter((_, i)=>i !== index)\n            }));\n    };\n    const updateTeachingInfoTitle = (index, title)=>{\n        setCourseDetail((prev)=>{\n            const newTeachingInfo = [\n                ...prev.teachingInfo\n            ];\n            newTeachingInfo[index] = {\n                ...newTeachingInfo[index],\n                title\n            };\n            return {\n                ...prev,\n                teachingInfo: newTeachingInfo\n            };\n        });\n    };\n    const updateTeachingInfoContent = (index, content)=>{\n        setCourseDetail((prev)=>{\n            const newTeachingInfo = [\n                ...prev.teachingInfo\n            ];\n            newTeachingInfo[index] = {\n                ...newTeachingInfo[index],\n                content\n            };\n            return {\n                ...prev,\n                teachingInfo: newTeachingInfo\n            };\n        });\n    };\n    // 将UI格式的teachingInfo转换为API格式\n    const convertTeachingInfoForAPI = (teachingInfo)=>{\n        return teachingInfo.map((info)=>({\n                title: info.title,\n                content: info.content.split(\"\\n\").filter((line)=>line.trim()) // 按换行分割并过滤空行\n            }));\n    };\n    // 检查是否有未上传完成的文件（blob URL）\n    const checkForBlobUrls = (course)=>{\n        var _course_contentConfig_video, _course_contentConfig, _course_contentConfig_document, _course_contentConfig1;\n        const issues = [];\n        // 检查封面图片\n        if (course.coverImage && course.coverImage.startsWith(\"blob:\")) {\n            issues.push(\"课程封面图片\");\n        }\n        // 检查视频文件\n        if (((_course_contentConfig = course.contentConfig) === null || _course_contentConfig === void 0 ? void 0 : (_course_contentConfig_video = _course_contentConfig.video) === null || _course_contentConfig_video === void 0 ? void 0 : _course_contentConfig_video.url) && course.contentConfig.video.url.startsWith(\"blob:\")) {\n            issues.push(\"课程视频\");\n        }\n        // 检查文档附件\n        if (((_course_contentConfig1 = course.contentConfig) === null || _course_contentConfig1 === void 0 ? void 0 : (_course_contentConfig_document = _course_contentConfig1.document) === null || _course_contentConfig_document === void 0 ? void 0 : _course_contentConfig_document.url) && course.contentConfig.document.url.startsWith(\"blob:\")) {\n            issues.push(\"课程文档\");\n        }\n        // 检查教学材料\n        if (course.additionalResources) {\n            course.additionalResources.forEach((resource, index)=>{\n                if (resource.url && resource.url.startsWith(\"blob:\")) {\n                    issues.push(\"教学材料\".concat(index + 1));\n                }\n            });\n        }\n        return issues;\n    };\n    // 保存单个课程到后端\n    const saveCourse = async (course)=>{\n        try {\n            var _course_contentConfig_video, _course_contentConfig, _course_contentConfig_document, _course_contentConfig1;\n            // 验证必要的数据\n            if (!course.title || course.title.trim() === \"\") {\n                throw new Error(\"课程标题不能为空\");\n            }\n            // 检查是否有未上传完成的文件\n            const blobIssues = checkForBlobUrls(course);\n            if (blobIssues.length > 0) {\n                throw new Error(\"以下文件尚未上传完成，请等待上传完成后再保存：\".concat(blobIssues.join(\"、\")));\n            }\n            // 准备课程数据\n            const courseData = {\n                seriesId: seriesId,\n                title: course.title,\n                description: course.description || \"\",\n                coverImage: course.coverImage || \"\",\n                hasVideo: ((_course_contentConfig = course.contentConfig) === null || _course_contentConfig === void 0 ? void 0 : (_course_contentConfig_video = _course_contentConfig.video) === null || _course_contentConfig_video === void 0 ? void 0 : _course_contentConfig_video.url) ? 1 : 0,\n                hasDocument: ((_course_contentConfig1 = course.contentConfig) === null || _course_contentConfig1 === void 0 ? void 0 : (_course_contentConfig_document = _course_contentConfig1.document) === null || _course_contentConfig_document === void 0 ? void 0 : _course_contentConfig_document.url) ? 1 : 0,\n                hasAudio: 0,\n                videoDuration: 0,\n                contentConfig: course.contentConfig || {},\n                teachingInfo: convertTeachingInfoForAPI(course.teachingInfo || []),\n                additionalResources: course.additionalResources || [],\n                orderIndex: course.orderIndex || 1\n            };\n            console.log(\"\\uD83D\\uDCBE 准备保存课程数据:\", courseData);\n            let result;\n            // 判断是新课程还是更新课程\n            if (course.id && course.id > 1000000) {\n                // 新课程，使用创建API\n                console.log(\"\\uD83D\\uDCE4 创建新课程\");\n                const { data: response } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.createCourse(courseData);\n                result = response;\n                console.log(\"✅ 课程创建成功\");\n            } else {\n                // 现有课程，使用更新API\n                console.log(\"\\uD83D\\uDCE4 更新现有课程:\", course.id);\n                const { data: response } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.updateCourse(course.id, courseData);\n                result = response;\n                console.log(\"✅ 课程更新成功\");\n            }\n            return result;\n        } catch (error) {\n            console.error(\"❌ 保存课程失败:\", error);\n            throw error;\n        }\n    };\n    // 发布选中的课程\n    const handlePublishCourse = async ()=>{\n        if (!selectedCourseId) {\n            alert(\"请先选择要发布的课程\");\n            return;\n        }\n        const selectedCourse = courseList.find((course)=>course.id === selectedCourseId);\n        if (!selectedCourse) {\n            alert(\"未找到选中的课程\");\n            return;\n        }\n        // 检查课程是否已经发布\n        if (selectedCourse.status === 1) {\n            alert(\"该课程已经发布，无需重复发布\");\n            return;\n        }\n        try {\n            setIsPublishing(true);\n            console.log(\"\\uD83D\\uDCE4 开始发布课程\");\n            console.log(\"\\uD83D\\uDCE4 selectedCourseId:\", selectedCourseId, \"类型:\", typeof selectedCourseId);\n            console.log(\"\\uD83D\\uDCE4 课程信息:\", selectedCourse);\n            console.log(\"\\uD83D\\uDCE4 课程ID字段:\", selectedCourse.id, \"类型:\", typeof selectedCourse.id);\n            console.log(\"\\uD83D\\uDCE4 课程列表中的所有ID:\", courseList.map((c)=>({\n                    id: c.id,\n                    type: typeof c.id,\n                    title: c.title\n                })));\n            // 确保使用正确的课程ID\n            const courseIdToPublish = selectedCourse.id;\n            console.log(\"\\uD83D\\uDCE4 即将发布的课程ID:\", courseIdToPublish, \"类型:\", typeof courseIdToPublish);\n            const { data: response } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.publishCourse(courseIdToPublish);\n            if (response.code === 200) {\n                console.log(\"✅ 课程发布成功:\", response.data);\n                alert(\"课程发布成功！\");\n                // 刷新课程列表以更新状态\n                await loadCourseList();\n            } else {\n                console.error(\"❌ 发布课程失败:\", response.message);\n                alert(response.message || \"发布课程失败\");\n            }\n        } catch (error) {\n            var _error_response, _error_response_data, _error_response1;\n            console.error(\"❌ 发布课程失败:\", error);\n            console.error(\"❌ 错误详情:\", (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data);\n            // 处理具体的错误信息\n            if ((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : (_error_response_data = _error_response1.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) {\n                alert(error.response.data.message);\n            } else if (error.message) {\n                alert(error.message);\n            } else {\n                alert(\"发布课程失败，请重试\");\n            }\n        } finally{\n            setIsPublishing(false);\n        }\n    };\n    // 保存课程列表\n    const handleSave = async ()=>{\n        try {\n            setIsCreating(true);\n            // 检查是否有文件正在上传\n            if (uploadingFiles.size > 0) {\n                alert(\"有文件正在上传中，请等待上传完成后再保存\");\n                setIsCreating(false);\n                return;\n            }\n            console.log(\"\\uD83D\\uDCBE 开始保存课程列表\");\n            // 如果有选中的课程且在编辑状态，先保存当前课程\n            if (selectedCourseId && rightPanelType === \"course\") {\n                const selectedCourse = courseList.find((c)=>c.id === selectedCourseId);\n                if (selectedCourse) {\n                    var _courseDetail_teachingMaterials;\n                    // 更新课程数据\n                    const updatedCourse = {\n                        ...selectedCourse,\n                        title: courseDetail.title,\n                        description: courseDetail.description,\n                        coverImage: courseDetail.coverImage,\n                        hasVideo: courseDetail.isVideoEnabled ? 1 : 0,\n                        hasDocument: courseDetail.isAttachmentEnabled ? 1 : 0,\n                        contentConfig: courseDetail.contentConfig,\n                        teachingInfo: courseDetail.teachingInfo,\n                        additionalResources: ((_courseDetail_teachingMaterials = courseDetail.teachingMaterials) === null || _courseDetail_teachingMaterials === void 0 ? void 0 : _courseDetail_teachingMaterials.map((material)=>({\n                                title: material.name,\n                                url: material.url,\n                                description: material.name\n                            }))) || []\n                    };\n                    // 先更新课程列表中的数据\n                    setCourseList((prev)=>prev.map((course)=>course.id === selectedCourseId ? updatedCourse : course));\n                    // 然后保存到后端\n                    const savedCourseResult = await saveCourse(updatedCourse);\n                    // 如果课程有任务配置，发送课程设置请求\n                    if (courseDetail.taskConfig && (courseDetail.isDistributionWater || courseDetail.isOneKeyOpen)) {\n                        try {\n                            var _savedCourseResult_data;\n                            // 获取真实的课程ID\n                            let realCourseId = selectedCourseId;\n                            // 如果是新创建的课程，使用返回的真实ID\n                            if (selectedCourse.id > 1000000 && (savedCourseResult === null || savedCourseResult === void 0 ? void 0 : (_savedCourseResult_data = savedCourseResult.data) === null || _savedCourseResult_data === void 0 ? void 0 : _savedCourseResult_data.id)) {\n                                realCourseId = savedCourseResult.data.id;\n                                console.log(\"\\uD83C\\uDD94 使用新创建课程的真实ID:\", realCourseId);\n                            }\n                            const settingsData = {\n                                templateId: courseDetail.selectedTemplate ? 1 : null,\n                                requiredPoints: courseDetail.requiredEnergy ? parseInt(courseDetail.requiredEnergy) : 0,\n                                autoCreateTasks: courseDetail.isOneKeyOpen ? 1 : 0\n                            };\n                            console.log(\"\\uD83D\\uDCE4 发送课程设置请求到课程ID:\", realCourseId);\n                            console.log(\"\\uD83D\\uDCE4 设置数据:\", settingsData);\n                            await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.setCourseSettings(realCourseId, settingsData);\n                            console.log(\"✅ 课程设置保存成功\");\n                        } catch (settingsError) {\n                            console.error(\"❌ 课程设置保存失败:\", settingsError);\n                        // 不阻断主流程，只记录错误\n                        }\n                    }\n                }\n            }\n            // 保存系列课程信息\n            const data = {\n                title: editingTitle,\n                courseGoals,\n                courseObjectives,\n                courseList\n            };\n            onSave(data);\n            onClose();\n            console.log(\"✅ 课程列表保存完成\");\n        } catch (error) {\n            console.error(\"❌ 保存失败:\", error);\n            alert(\"保存失败: \".concat(error.message || \"请重试\"));\n        } finally{\n            setIsCreating(false);\n        }\n    };\n    // 发布系列课程\n    const handlePublish = async ()=>{\n        // 如果系列已发布，不执行任何操作\n        if (seriesStatus === 1) {\n            return;\n        }\n        try {\n            setIsPublishingSeries(true);\n            // 检查是否有课程\n            if (courseList.length === 0) {\n                alert(\"发布失败：课程系列中至少需要包含一个课程\");\n                return;\n            }\n            console.log(\"\\uD83D\\uDCE4 开始发布系列课程，系列ID:\", seriesId);\n            const { data: response } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.publishCourseSeries(seriesId);\n            if (response.code === 200) {\n                console.log(\"✅ 系列课程发布成功:\", response.data);\n                // 构建成功消息\n                const publishData = response.data;\n                let successMessage = '系列课程\"'.concat(publishData.title, '\"发布成功！');\n                // 如果有发布统计信息，添加到消息中\n                if (publishData.publishStats) {\n                    const stats = publishData.publishStats;\n                    successMessage += \"\\n\\n发布统计：\\n• 总课程数：\".concat(publishData.totalCourses, \"\\n• 已发布课程：\").concat(publishData.publishedCourses, \"\\n• 视频课程：\").concat(stats.videoCourseCount, \"个\\n• 文档课程：\").concat(stats.documentCourseCount, \"个\\n• 总资源数：\").concat(stats.totalResourcesCount, \"个\");\n                    if (stats.totalVideoDuration > 0) {\n                        const durationMinutes = Math.round(stats.totalVideoDuration / 60);\n                        successMessage += \"\\n• 视频总时长：\".concat(durationMinutes, \"分钟\");\n                    }\n                }\n                alert(successMessage);\n                // 更新系列状态为已发布\n                setSeriesStatus(1);\n                // 刷新课程列表以更新状态\n                await loadCourseList();\n                // 通知父组件刷新数据\n                onSave({\n                    type: \"publish_series\",\n                    seriesId: seriesId,\n                    message: \"系列课程发布成功\"\n                });\n            } else {\n                console.error(\"❌ 发布系列课程失败:\", response.message);\n                alert(response.message || \"发布系列课程失败\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"❌ 发布系列课程出错:\", error);\n            // 处理具体的错误信息\n            let errorMessage = \"发布系列课程失败\";\n            if ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) {\n                errorMessage = error.response.data.message;\n            } else if (error.message) {\n                errorMessage = error.message;\n            }\n            alert(errorMessage);\n        } finally{\n            setIsPublishingSeries(false);\n        }\n    };\n    // 退出编辑模式 - 保存数据并关闭\n    const handleExitEdit = ()=>{\n        handleSave();\n    };\n    // 显示设置面板\n    const showSettingsPanel = ()=>{\n        setRightPanelType(\"settings\");\n        setSelectedCourseId(null);\n    };\n    // 处理模板选择\n    const handleTemplateSelect = (template)=>{\n        setCourseDetail((prev)=>({\n                ...prev,\n                selectedTemplate: {\n                    id: template.id,\n                    name: template.templateName\n                }\n            }));\n        setIsTemplatePickerOpen(false);\n    };\n    // 打开模板选择弹窗\n    const handleOpenTemplatePicker = ()=>{\n        setIsTemplatePickerOpen(true);\n    };\n    // 显示课程编辑面板\n    const showCoursePanel = async (courseId)=>{\n        setRightPanelType(\"course\");\n        setSelectedCourseId(courseId);\n        // 如果作品数据还没有加载，重新加载\n        if (works.length === 0 && !loadingWorks) {\n            loadUserWorks();\n        }\n        // 获取选中的课程并更新courseDetail状态\n        const selectedCourse = courseList.find((course)=>course.id === courseId);\n        console.log(\"\\uD83C\\uDFAF 找到的课程:\", selectedCourse);\n        if (selectedCourse) {\n            try {\n                console.log(\"\\uD83D\\uDD04 获取课程详情，seriesId:\", seriesId, \"courseId:\", courseId);\n                // 获取真实的课程详情数据\n                const { data: res } = await fetchCourseDetail(seriesId, courseId);\n                if (res.code === 200 && res.data) {\n                    var _courseDetailData_additionalResources, _courseDetailData_teachingInfo;\n                    const courseDetailData = res.data;\n                    console.log(\"✅ 获取到课程详情:\", courseDetailData);\n                    // 将真实的 additionalResources 映射到 teachingMaterials\n                    const teachingMaterials = ((_courseDetailData_additionalResources = courseDetailData.additionalResources) === null || _courseDetailData_additionalResources === void 0 ? void 0 : _courseDetailData_additionalResources.map((resource)=>({\n                            type: \"application/octet-stream\",\n                            name: resource.title || resource.name || \"附件\",\n                            url: resource.url\n                        }))) || [];\n                    // 将API返回的teachingInfo数组格式转换为新的格式\n                    const mappedTeachingInfo = ((_courseDetailData_teachingInfo = courseDetailData.teachingInfo) === null || _courseDetailData_teachingInfo === void 0 ? void 0 : _courseDetailData_teachingInfo.map((info)=>({\n                            title: info.title || \"\",\n                            content: Array.isArray(info.content) ? info.content.join(\"\\n\") : info.content || \"\"\n                        }))) || [];\n                    console.log(\"\\uD83D\\uDCCE 映射的教学附件:\", teachingMaterials);\n                    console.log(\"\\uD83D\\uDCDA 映射的教学信息:\", mappedTeachingInfo);\n                    setCourseDetail((prev)=>{\n                        var _courseDetailData_contentConfig_video, _courseDetailData_contentConfig, _courseDetailData_contentConfig_video1, _courseDetailData_contentConfig1, _courseDetailData_contentConfig_document, _courseDetailData_contentConfig2, _courseDetailData_contentConfig_document1, _courseDetailData_contentConfig3;\n                        return {\n                            ...prev,\n                            title: courseDetailData.title,\n                            description: courseDetailData.description,\n                            coverImage: courseDetailData.coverImage || \"\",\n                            isVideoEnabled: courseDetailData.hasVideo === 1,\n                            isAttachmentEnabled: courseDetailData.hasDocument === 1,\n                            contentConfig: courseDetailData.contentConfig || {},\n                            teachingInfo: mappedTeachingInfo,\n                            teachingMaterials: teachingMaterials,\n                            videoUrl: ((_courseDetailData_contentConfig = courseDetailData.contentConfig) === null || _courseDetailData_contentConfig === void 0 ? void 0 : (_courseDetailData_contentConfig_video = _courseDetailData_contentConfig.video) === null || _courseDetailData_contentConfig_video === void 0 ? void 0 : _courseDetailData_contentConfig_video.url) || \"\",\n                            videoName: ((_courseDetailData_contentConfig1 = courseDetailData.contentConfig) === null || _courseDetailData_contentConfig1 === void 0 ? void 0 : (_courseDetailData_contentConfig_video1 = _courseDetailData_contentConfig1.video) === null || _courseDetailData_contentConfig_video1 === void 0 ? void 0 : _courseDetailData_contentConfig_video1.name) || \"\",\n                            attachmentUrl: ((_courseDetailData_contentConfig2 = courseDetailData.contentConfig) === null || _courseDetailData_contentConfig2 === void 0 ? void 0 : (_courseDetailData_contentConfig_document = _courseDetailData_contentConfig2.document) === null || _courseDetailData_contentConfig_document === void 0 ? void 0 : _courseDetailData_contentConfig_document.url) || \"\",\n                            attachmentName: ((_courseDetailData_contentConfig3 = courseDetailData.contentConfig) === null || _courseDetailData_contentConfig3 === void 0 ? void 0 : (_courseDetailData_contentConfig_document1 = _courseDetailData_contentConfig3.document) === null || _courseDetailData_contentConfig_document1 === void 0 ? void 0 : _courseDetailData_contentConfig_document1.name) || \"\"\n                        };\n                    });\n                } else {\n                    console.error(\"❌ 获取课程详情失败:\", res.message);\n                    // 使用基础数据作为后备\n                    setCourseDetail((prev)=>({\n                            ...prev,\n                            title: selectedCourse.title,\n                            description: selectedCourse.description,\n                            coverImage: selectedCourse.coverImage || \"\",\n                            isVideoEnabled: selectedCourse.hasVideo === 1,\n                            isAttachmentEnabled: selectedCourse.hasDocument === 1,\n                            teachingMaterials: [] // 清空附件列表\n                        }));\n                }\n            } catch (error) {\n                console.error(\"❌ 获取课程详情异常:\", error);\n                // 使用基础数据作为后备\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        title: selectedCourse.title,\n                        description: selectedCourse.description,\n                        coverImage: selectedCourse.coverImage || \"\",\n                        isVideoEnabled: selectedCourse.hasVideo === 1,\n                        isAttachmentEnabled: selectedCourse.hasDocument === 1,\n                        teachingMaterials: [] // 清空附件列表\n                    }));\n            }\n        }\n    };\n    // 获取选中的课程\n    const getSelectedCourse = ()=>{\n        return courseList.find((course)=>course.id === selectedCourseId);\n    };\n    if (!isVisible) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"course-list-modal-overlay\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"course-list-modal\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"course-list-header\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"course-list-title-section\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"course-list-title\",\n                                        children: \"课程列表\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 1420,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"course-list-actions\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: showSettingsPanel,\n                                                className: \"course-list-settings-btn \".concat(rightPanelType === \"settings\" ? \"active\" : \"\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1426,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1422,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: addNewCourse,\n                                                className: \"course-list-add-btn\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1429,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1428,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 1421,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                lineNumber: 1419,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onClose,\n                                className: \"course-list-close-btn\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 1434,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                lineNumber: 1433,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                        lineNumber: 1418,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"course-list-content\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"course-list-sidebar\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"course-list-items\",\n                                    children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"course-list-loading\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"加载中...\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 1445,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 1444,\n                                        columnNumber: 17\n                                    }, undefined) : courseList.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"course-list-empty\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"course-list-empty-icon\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"w-12 h-12 text-gray-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1450,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1449,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"course-list-empty-title\",\n                                                children: \"暂无课时\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1452,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"course-list-empty-description\",\n                                                children: \"点击右上角的 + 按钮添加第一个课时\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1453,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: addNewCourse,\n                                                className: \"course-list-empty-btn\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1460,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    \"添加课时\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1456,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 1448,\n                                        columnNumber: 17\n                                    }, undefined) : courseList.map((course)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"course-list-item \".concat(selectedCourseId === course.id ? \"active\" : \"\"),\n                                            onClick: ()=>showCoursePanel(course.id),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-list-item-content\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"course-list-item-text\",\n                                                            children: course.title\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1472,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"course-status-badge \".concat(course.status === 1 ? \"published\" : \"draft\"),\n                                                            children: course.status === 1 ? \"已发布\" : \"未发布\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1473,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1471,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: (e)=>{\n                                                        e.stopPropagation();\n                                                        showDeleteConfirm(course.id);\n                                                    },\n                                                    className: \"course-list-item-delete\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"w-3 h-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1484,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1477,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, course.id, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 1466,\n                                            columnNumber: 19\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 1442,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                lineNumber: 1441,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"course-list-edit-area\",\n                                children: [\n                                    rightPanelType === \"none\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"course-edit-empty\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"course-edit-empty-icon\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"w-16 h-16 text-gray-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1497,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1496,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"course-edit-empty-title\",\n                                                children: \"无课程详情\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1499,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"course-edit-empty-description\",\n                                                children: \"点击左侧课程或设置按钮查看详情\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1500,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 1495,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    rightPanelType === \"settings\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"course-series-cover\",\n                                                children: seriesCoverImage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: seriesCoverImage,\n                                                    alt: \"系列课程封面\",\n                                                    className: \"course-series-cover-image\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1511,\n                                                    columnNumber: 21\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-series-cover-placeholder\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"系列课程封面\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1518,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1517,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1509,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"course-edit-form\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"course-edit-field\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"course-edit-label\",\n                                                                children: \"系列课程标题\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1527,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                value: editingTitle,\n                                                                onChange: (e)=>setEditingTitle(e.target.value),\n                                                                className: \"course-edit-input\",\n                                                                placeholder: \"请输入系列课程标题\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1528,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1526,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"course-edit-field\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"course-edit-label\",\n                                                                children: \"课程标签\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1539,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Select_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                mode: \"multiple\",\n                                                                style: {\n                                                                    width: \"100%\"\n                                                                },\n                                                                placeholder: \"请选择课程标签\",\n                                                                value: selectedTags,\n                                                                onChange: setSelectedTags,\n                                                                loading: tagsLoading,\n                                                                options: courseTags.map((tag)=>{\n                                                                    console.log(\"\\uD83C\\uDFF7️ 渲染标签选项:\", tag);\n                                                                    return {\n                                                                        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            style: {\n                                                                                color: tag.color\n                                                                            },\n                                                                            children: tag.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1551,\n                                                                            columnNumber: 29\n                                                                        }, void 0),\n                                                                        value: tag.id\n                                                                    };\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1540,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    fontSize: \"12px\",\n                                                                    color: \"#666\",\n                                                                    marginTop: \"4px\"\n                                                                },\n                                                                children: [\n                                                                    \"调试: 当前标签数量 \",\n                                                                    courseTags.length,\n                                                                    \", 加载状态: \",\n                                                                    tagsLoading ? \"是\" : \"否\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1560,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1538,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"course-edit-field\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"course-edit-label\",\n                                                                children: \"课程项目成员\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1567,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                value: projectMembers,\n                                                                onChange: (e)=>setProjectMembers(e.target.value),\n                                                                className: \"course-edit-input\",\n                                                                placeholder: \"请输入项目成员，如：张老师、李助教、王同学\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1568,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1566,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1524,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true),\n                                    rightPanelType === \"course\" && getSelectedCourse() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"course-detail-edit\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-detail-top\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"course-detail-cover\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"course-cover-upload-area\",\n                                                                    onClick: ()=>{\n                                                                        var _document_getElementById;\n                                                                        return (_document_getElementById = document.getElementById(\"cover-upload-input\")) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.click();\n                                                                    },\n                                                                    children: courseDetail.coverImage || ((_getSelectedCourse = getSelectedCourse()) === null || _getSelectedCourse === void 0 ? void 0 : _getSelectedCourse.coverImage) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                        src: courseDetail.coverImage || ((_getSelectedCourse1 = getSelectedCourse()) === null || _getSelectedCourse1 === void 0 ? void 0 : _getSelectedCourse1.coverImage),\n                                                                        alt: \"课程封面\",\n                                                                        className: \"course-cover-image\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1592,\n                                                                        columnNumber: 27\n                                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"course-cover-placeholder\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"点击上传课程封面\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1599,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1598,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1587,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    id: \"cover-upload-input\",\n                                                                    type: \"file\",\n                                                                    accept: \"image/jpeg,image/jpg,image/png,image/gif\",\n                                                                    onChange: handleCoverUpload,\n                                                                    style: {\n                                                                        display: \"none\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1603,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"one-click-class-btn\",\n                                                                    onClick: ()=>{\n                                                                        // TODO: 实现一键上课功能\n                                                                        console.log(\"一键上课按钮被点击\");\n                                                                        notification.info(\"一键上课功能开发中...\");\n                                                                    },\n                                                                    children: \"一键上课\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1611,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1586,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"course-detail-basic\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"course-detail-field\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            children: \"课程标题\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1624,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            value: courseDetail.title || ((_getSelectedCourse2 = getSelectedCourse()) === null || _getSelectedCourse2 === void 0 ? void 0 : _getSelectedCourse2.title) || \"\",\n                                                                            onChange: (e)=>{\n                                                                                setCourseDetail((prev)=>({\n                                                                                        ...prev,\n                                                                                        title: e.target.value\n                                                                                    }));\n                                                                                updateCourseTitle(selectedCourseId, e.target.value);\n                                                                            },\n                                                                            placeholder: \"请输入课程标题\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1625,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1623,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"course-detail-field\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            children: \"课程介绍\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1636,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                            value: courseDetail.description || ((_getSelectedCourse3 = getSelectedCourse()) === null || _getSelectedCourse3 === void 0 ? void 0 : _getSelectedCourse3.description) || \"\",\n                                                                            onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                        ...prev,\n                                                                                        description: e.target.value\n                                                                                    })),\n                                                                            placeholder: \"请输入课程介绍\",\n                                                                            rows: 3\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1637,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1635,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1622,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1585,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-detail-section\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            children: \"课程资源\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1649,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"course-resource-item\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"resource-header-right\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"课程视频\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1654,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"switch\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: \"checkbox\",\n                                                                                    checked: courseDetail.isVideoEnabled,\n                                                                                    onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                ...prev,\n                                                                                                isVideoEnabled: e.target.checked\n                                                                                            }))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1656,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"slider\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1661,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1655,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1653,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                courseDetail.isVideoEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"video-content-area\",\n                                                                    children: ((_courseDetail_contentConfig = courseDetail.contentConfig) === null || _courseDetail_contentConfig === void 0 ? void 0 : (_courseDetail_contentConfig_video = _courseDetail_contentConfig.video) === null || _courseDetail_contentConfig_video === void 0 ? void 0 : _courseDetail_contentConfig_video.url) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"video-info-section\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"video-preview\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                                                                    className: \"video-thumbnail\",\n                                                                                    controls: true,\n                                                                                    poster: courseDetail.coverImage,\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                                                                                            src: courseDetail.contentConfig.video.url,\n                                                                                            type: \"video/mp4\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1675,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined),\n                                                                                        \"您的浏览器不支持视频播放\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1670,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1669,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"video-name-centered\",\n                                                                                children: courseDetail.contentConfig.video.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1679,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"upload-btn-horizontal\",\n                                                                                onClick: triggerVideoUpload,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"重新上传\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1681,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1680,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1668,\n                                                                        columnNumber: 29\n                                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"video-upload-section\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"video-placeholder-centered\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"play-icon\",\n                                                                                    children: \"▶\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1687,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1686,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"upload-btn-horizontal\",\n                                                                                onClick: triggerVideoUpload,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"上传视频\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1690,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1689,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1685,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1665,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1652,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"course-resource-item\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"resource-header-right\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"课程附件\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1701,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"switch\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: \"checkbox\",\n                                                                                    checked: courseDetail.isAttachmentEnabled,\n                                                                                    onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                ...prev,\n                                                                                                isAttachmentEnabled: e.target.checked\n                                                                                            }))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1703,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"slider\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1708,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1702,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1700,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                courseDetail.isAttachmentEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"attachment-content-area\",\n                                                                    children: ((_courseDetail_contentConfig1 = courseDetail.contentConfig) === null || _courseDetail_contentConfig1 === void 0 ? void 0 : (_courseDetail_contentConfig_document = _courseDetail_contentConfig1.document) === null || _courseDetail_contentConfig_document === void 0 ? void 0 : _courseDetail_contentConfig_document.url) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"attachment-info-section\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"attachment-preview\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"document-icon\",\n                                                                                        children: \"\\uD83D\\uDCC4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1717,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"attachment-details\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"attachment-name\",\n                                                                                            children: courseDetail.contentConfig.document.name\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1719,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1718,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1716,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"upload-btn-horizontal\",\n                                                                                onClick: triggerAttachmentUpload,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"重新上传\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1723,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1722,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1715,\n                                                                        columnNumber: 29\n                                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"attachment-upload-section\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            className: \"upload-btn-horizontal\",\n                                                                            onClick: triggerAttachmentUpload,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"上传附件\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1729,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1728,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1727,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1712,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1699,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"course-resource-item\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"resource-header-simple\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"教学附件\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1740,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1739,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"teaching-materials\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            className: \"add-material-btn\",\n                                                                            onClick: triggerTeachingMaterialUpload,\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"+\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1744,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"上传\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1745,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1743,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        courseDetail.teachingMaterials && courseDetail.teachingMaterials.length > 0 ? courseDetail.teachingMaterials.map((material, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"material-item\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"material-name\",\n                                                                                        onClick: ()=>{\n                                                                                            if (material.url) {\n                                                                                                window.open(material.url, \"_blank\");\n                                                                                            }\n                                                                                        },\n                                                                                        style: {\n                                                                                            cursor: material.url ? \"pointer\" : \"default\",\n                                                                                            color: material.url ? \"#1890ff\" : \"inherit\",\n                                                                                            textDecoration: material.url ? \"underline\" : \"none\"\n                                                                                        },\n                                                                                        title: material.url ? \"点击下载附件\" : material.name,\n                                                                                        children: [\n                                                                                            \"\\uD83D\\uDCCE \",\n                                                                                            material.name\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1750,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        className: \"remove-material-btn\",\n                                                                                        onClick: ()=>removeTeachingMaterial(index),\n                                                                                        title: \"删除附件\",\n                                                                                        children: \"\\xd7\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1766,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, index, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1749,\n                                                                                columnNumber: 29\n                                                                            }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"empty-materials-hint\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                style: {\n                                                                                    color: \"#999\",\n                                                                                    fontSize: \"14px\"\n                                                                                },\n                                                                                children: \"暂无教学附件\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1777,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1776,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1742,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1738,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1648,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-detail-section\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"section-header\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    children: \"课程内容\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1787,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"add-content-section-btn\",\n                                                                    onClick: addTeachingInfoItem,\n                                                                    title: \"添加课程内容\",\n                                                                    children: \"+ 添加课程内容\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1788,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1786,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"course-content-area\",\n                                                            children: courseDetail.teachingInfo && courseDetail.teachingInfo.length > 0 ? courseDetail.teachingInfo.map((info, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"teaching-info-card\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"card-header\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"card-title\",\n                                                                                    children: [\n                                                                                        \"课程内容 \",\n                                                                                        index + 1\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1801,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    className: \"remove-card-btn\",\n                                                                                    onClick: ()=>removeTeachingInfoItem(index),\n                                                                                    title: \"删除此内容\",\n                                                                                    children: \"\\xd7\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1802,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1800,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"card-content\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"input-group\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                            children: \"标题\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1812,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                            type: \"text\",\n                                                                                            value: info.title,\n                                                                                            onChange: (e)=>updateTeachingInfoTitle(index, e.target.value),\n                                                                                            placeholder: \"请输入标题，如：教学目标、教学方法等\",\n                                                                                            className: \"title-input\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1813,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1811,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"input-group\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                            children: \"内容\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1822,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                                            value: info.content,\n                                                                                            onChange: (e)=>updateTeachingInfoContent(index, e.target.value),\n                                                                                            placeholder: \"请输入具体内容，多个内容项可用换行分隔\",\n                                                                                            className: \"content-textarea\",\n                                                                                            rows: 4\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1823,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1821,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1810,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, index, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1799,\n                                                                    columnNumber: 27\n                                                                }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"empty-content-hint\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: \"暂无课程内容，点击右上角按钮添加\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1836,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1835,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1796,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1785,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-detail-section\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"one-key-section\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"one-key-item\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"开始上课\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1846,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"switch\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"checkbox\",\n                                                                                checked: courseDetail.isOneKeyOpen,\n                                                                                onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                            ...prev,\n                                                                                            isOneKeyOpen: e.target.checked\n                                                                                        }))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1848,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"slider\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1853,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1847,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1845,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            courseDetail.isOneKeyOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"one-key-item\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"分配积木\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1860,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"switch\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                        type: \"checkbox\",\n                                                                                        checked: courseDetail.isDistributionEnabled,\n                                                                                        onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    isDistributionEnabled: e.target.checked\n                                                                                                }))\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1862,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"slider\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1867,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1861,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            courseDetail.isDistributionEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"block-template-section\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        className: \"select-template-btn\",\n                                                                                        onClick: handleOpenTemplatePicker,\n                                                                                        children: \"选择积木模板\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1871,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"selected-template-display\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            children: courseDetail.selectedTemplate.name || \"选中的模板名字\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1878,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1877,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1870,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1859,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"one-key-item\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"分配能量\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1885,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"switch\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                        type: \"checkbox\",\n                                                                                        checked: courseDetail.isDistributionWater,\n                                                                                        onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    isDistributionWater: e.target.checked\n                                                                                                }))\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1887,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"slider\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1892,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1886,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1884,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    courseDetail.isDistributionWater && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"energy-input-section\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: \"需要能量：\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1899,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                        type: \"text\",\n                                                                                        value: courseDetail.requiredEnergy || \"\",\n                                                                                        onChange: (e)=>handleEnergyChange(e.target.value),\n                                                                                        placeholder: \"请输入需要的能量值\",\n                                                                                        className: \"energy-input \".concat(energyError ? \"error\" : \"\")\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1900,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1898,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            energyError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"error-message\",\n                                                                                children: energyError\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1909,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1897,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"one-key-item\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"分配任务\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1917,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"switch\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                        type: \"checkbox\",\n                                                                                        checked: courseDetail.isDistributionLimit,\n                                                                                        onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    isDistributionLimit: e.target.checked\n                                                                                                }))\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1919,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"slider\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1924,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1918,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1916,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    courseDetail.isDistributionLimit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"task-config-form\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"task-config-row\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"task-config-field\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                                children: \"任务名称:\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                lineNumber: 1934,\n                                                                                                columnNumber: 35\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                type: \"text\",\n                                                                                                value: courseDetail.taskConfig.taskName,\n                                                                                                onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                            ...prev,\n                                                                                                            taskConfig: {\n                                                                                                                ...prev.taskConfig,\n                                                                                                                taskName: e.target.value\n                                                                                                            }\n                                                                                                        })),\n                                                                                                placeholder: \"请输入任务名称\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                lineNumber: 1935,\n                                                                                                columnNumber: 35\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1933,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"task-config-field\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                                children: \"任务持续天数:\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                lineNumber: 1946,\n                                                                                                columnNumber: 35\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"duration-input-wrapper\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                        type: \"text\",\n                                                                                                        value: courseDetail.taskConfig.taskDuration,\n                                                                                                        onClick: ()=>setShowDurationPicker(true),\n                                                                                                        placeholder: \"请选择时间\",\n                                                                                                        readOnly: true\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                        lineNumber: 1948,\n                                                                                                        columnNumber: 37\n                                                                                                    }, undefined),\n                                                                                                    showDurationPicker && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"duration-picker\",\n                                                                                                        children: [\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                className: \"duration-options\",\n                                                                                                                children: [\n                                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                                        onClick: ()=>handleDurationSelect(1, \"1小时\"),\n                                                                                                                        children: \"1小时\"\n                                                                                                                    }, void 0, false, {\n                                                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                        lineNumber: 1958,\n                                                                                                                        columnNumber: 43\n                                                                                                                    }, undefined),\n                                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                                        onClick: ()=>handleDurationSelect(6, \"6小时\"),\n                                                                                                                        children: \"6小时\"\n                                                                                                                    }, void 0, false, {\n                                                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                        lineNumber: 1959,\n                                                                                                                        columnNumber: 43\n                                                                                                                    }, undefined),\n                                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                                        onClick: ()=>handleDurationSelect(12, \"12小时\"),\n                                                                                                                        children: \"12小时\"\n                                                                                                                    }, void 0, false, {\n                                                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                        lineNumber: 1960,\n                                                                                                                        columnNumber: 43\n                                                                                                                    }, undefined),\n                                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                                        onClick: ()=>handleDurationSelect(24, \"1天\"),\n                                                                                                                        children: \"1天\"\n                                                                                                                    }, void 0, false, {\n                                                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                        lineNumber: 1961,\n                                                                                                                        columnNumber: 43\n                                                                                                                    }, undefined),\n                                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                                        onClick: ()=>handleDurationSelect(168, \"7天\"),\n                                                                                                                        children: \"7天\"\n                                                                                                                    }, void 0, false, {\n                                                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                        lineNumber: 1962,\n                                                                                                                        columnNumber: 43\n                                                                                                                    }, undefined)\n                                                                                                                ]\n                                                                                                            }, void 0, true, {\n                                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                lineNumber: 1957,\n                                                                                                                columnNumber: 41\n                                                                                                            }, undefined),\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                                className: \"close-picker\",\n                                                                                                                onClick: ()=>setShowDurationPicker(false),\n                                                                                                                children: \"\\xd7\"\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                lineNumber: 1964,\n                                                                                                                columnNumber: 41\n                                                                                                            }, undefined)\n                                                                                                        ]\n                                                                                                    }, void 0, true, {\n                                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                        lineNumber: 1956,\n                                                                                                        columnNumber: 39\n                                                                                                    }, undefined)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                lineNumber: 1947,\n                                                                                                columnNumber: 35\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1945,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1932,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"task-config-field task-config-full\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        children: \"任务描述:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1978,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                                        value: courseDetail.taskConfig.taskDescription,\n                                                                                        onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    taskConfig: {\n                                                                                                        ...prev.taskConfig,\n                                                                                                        taskDescription: e.target.value\n                                                                                                    }\n                                                                                                })),\n                                                                                        placeholder: \"请输入任务描述\",\n                                                                                        rows: 4\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1979,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1977,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"task-config-field task-config-full\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        children: [\n                                                                                            \"任务自评项: \",\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"item-number\",\n                                                                                                children: courseDetail.taskConfig.selfAssessmentItems.length\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                lineNumber: 1992,\n                                                                                                columnNumber: 47\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1992,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    courseDetail.taskConfig.selfAssessmentItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"self-assessment-item\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                type: \"text\",\n                                                                                                value: item,\n                                                                                                onChange: (e)=>{\n                                                                                                    const newItems = [\n                                                                                                        ...courseDetail.taskConfig.selfAssessmentItems\n                                                                                                    ];\n                                                                                                    newItems[index] = e.target.value;\n                                                                                                    setCourseDetail((prev)=>({\n                                                                                                            ...prev,\n                                                                                                            taskConfig: {\n                                                                                                                ...prev.taskConfig,\n                                                                                                                selfAssessmentItems: newItems\n                                                                                                            }\n                                                                                                        }));\n                                                                                                },\n                                                                                                placeholder: \"请输入自评项内容\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                lineNumber: 1995,\n                                                                                                columnNumber: 37\n                                                                                            }, undefined)\n                                                                                        }, index, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1994,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        type: \"button\",\n                                                                                        className: \"add-assessment-btn\",\n                                                                                        onClick: ()=>setCourseDetail((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    taskConfig: {\n                                                                                                        ...prev.taskConfig,\n                                                                                                        selfAssessmentItems: [\n                                                                                                            ...prev.taskConfig.selfAssessmentItems,\n                                                                                                            \"\"\n                                                                                                        ]\n                                                                                                    }\n                                                                                                })),\n                                                                                        children: \"+\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 2010,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1991,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"task-config-field task-config-full\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        children: \"任务参考作品:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 2027,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"reference-works-section\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"works-section\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                    className: \"help-text\",\n                                                                                                    children: \"选择作品作为任务参考资料（可多选）\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                    lineNumber: 2030,\n                                                                                                    columnNumber: 37\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"relative works-scroll-wrapper\",\n                                                                                                    style: {\n                                                                                                        minHeight: \"200px\",\n                                                                                                        cursor: \"grab\",\n                                                                                                        userSelect: \"none\"\n                                                                                                    },\n                                                                                                    children: loadingWorks ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"loading-container\",\n                                                                                                        children: [\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                className: \"loading-spinner\"\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                lineNumber: 2041,\n                                                                                                                columnNumber: 43\n                                                                                                            }, undefined),\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                                children: \"加载中...\"\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                lineNumber: 2042,\n                                                                                                                columnNumber: 43\n                                                                                                            }, undefined)\n                                                                                                        ]\n                                                                                                    }, void 0, true, {\n                                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                        lineNumber: 2040,\n                                                                                                        columnNumber: 41\n                                                                                                    }, undefined) : works.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"works-horizontal-scroll\",\n                                                                                                        onWheel: handleWheel,\n                                                                                                        children: works.map((work)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                className: \"work-card \".concat(selectedWorkIds.includes(work.id) ? \"selected\" : \"\"),\n                                                                                                                onClick: ()=>handleWorkSelect(work.id),\n                                                                                                                children: [\n                                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                        className: \"work-image\",\n                                                                                                                        children: [\n                                                                                                                            work.coverImage || work.screenShotImage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                                                                src: work.coverImage || work.screenShotImage,\n                                                                                                                                alt: work.title,\n                                                                                                                                onError: (e)=>{\n                                                                                                                                    const target = e.currentTarget;\n                                                                                                                                    target.style.display = \"none\";\n                                                                                                                                    const nextElement = target.nextElementSibling;\n                                                                                                                                    if (nextElement) {\n                                                                                                                                        nextElement.style.display = \"flex\";\n                                                                                                                                    }\n                                                                                                                                }\n                                                                                                                            }, void 0, false, {\n                                                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                                lineNumber: 2057,\n                                                                                                                                columnNumber: 51\n                                                                                                                            }, undefined) : null,\n                                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                                className: \"work-placeholder\",\n                                                                                                                                style: {\n                                                                                                                                    display: work.coverImage || work.screenShotImage ? \"none\" : \"flex\"\n                                                                                                                                },\n                                                                                                                                children: \"作品\"\n                                                                                                                            }, void 0, false, {\n                                                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                                lineNumber: 2070,\n                                                                                                                                columnNumber: 49\n                                                                                                                            }, undefined)\n                                                                                                                        ]\n                                                                                                                    }, void 0, true, {\n                                                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                        lineNumber: 2055,\n                                                                                                                        columnNumber: 47\n                                                                                                                    }, undefined),\n                                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                        className: \"work-title\",\n                                                                                                                        children: work.title || work.name || work.workName || \"未命名作品\"\n                                                                                                                    }, void 0, false, {\n                                                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                        lineNumber: 2074,\n                                                                                                                        columnNumber: 47\n                                                                                                                    }, undefined),\n                                                                                                                    selectedWorkIds.includes(work.id) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                                        className: \"selected-indicator\",\n                                                                                                                        children: \"✓\"\n                                                                                                                    }, void 0, false, {\n                                                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                        lineNumber: 2076,\n                                                                                                                        columnNumber: 49\n                                                                                                                    }, undefined)\n                                                                                                                ]\n                                                                                                            }, work.id, true, {\n                                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                lineNumber: 2050,\n                                                                                                                columnNumber: 45\n                                                                                                            }, undefined))\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                        lineNumber: 2045,\n                                                                                                        columnNumber: 41\n                                                                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"empty-placeholder\",\n                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                            className: \"empty-text\",\n                                                                                                            children: \"暂无作品\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                            lineNumber: 2083,\n                                                                                                            columnNumber: 43\n                                                                                                        }, undefined)\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                        lineNumber: 2082,\n                                                                                                        columnNumber: 41\n                                                                                                    }, undefined)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                    lineNumber: 2031,\n                                                                                                    columnNumber: 37\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 2029,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 2028,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 2026,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"task-config-field task-config-full\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        children: \"任务参考资源:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 2093,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"reference-resources-section\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"reference-resources-grid\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                    type: \"button\",\n                                                                                                    className: \"upload-resource-btn\",\n                                                                                                    onClick: ()=>{\n                                                                                                        // 触发文件上传\n                                                                                                        const input = document.createElement(\"input\");\n                                                                                                        input.type = \"file\";\n                                                                                                        input.accept = \".pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx,.txt,.jpg,.png,.gif\";\n                                                                                                        input.onchange = (e)=>{\n                                                                                                            var _e_target_files;\n                                                                                                            const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n                                                                                                            if (file) {\n                                                                                                                setCourseDetail((prev)=>({\n                                                                                                                        ...prev,\n                                                                                                                        taskConfig: {\n                                                                                                                            ...prev.taskConfig,\n                                                                                                                            referenceResources: [\n                                                                                                                                ...prev.taskConfig.referenceResources,\n                                                                                                                                {\n                                                                                                                                    type: \"file\",\n                                                                                                                                    name: file.name\n                                                                                                                                }\n                                                                                                                            ]\n                                                                                                                        }\n                                                                                                                    }));\n                                                                                                            }\n                                                                                                        };\n                                                                                                        input.click();\n                                                                                                    },\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                                            size: 24\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                            lineNumber: 2122,\n                                                                                                            columnNumber: 39\n                                                                                                        }, undefined),\n                                                                                                        \"上传\"\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                    lineNumber: 2096,\n                                                                                                    columnNumber: 37\n                                                                                                }, undefined),\n                                                                                                courseDetail.taskConfig.referenceResources.map((resource, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"reference-resource-item\",\n                                                                                                        children: [\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                                children: resource.name\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                lineNumber: 2127,\n                                                                                                                columnNumber: 41\n                                                                                                            }, undefined),\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                                type: \"button\",\n                                                                                                                className: \"remove-resource-btn\",\n                                                                                                                onClick: ()=>{\n                                                                                                                    const newResources = courseDetail.taskConfig.referenceResources.filter((_, i)=>i !== index);\n                                                                                                                    setCourseDetail((prev)=>({\n                                                                                                                            ...prev,\n                                                                                                                            taskConfig: {\n                                                                                                                                ...prev.taskConfig,\n                                                                                                                                referenceResources: newResources\n                                                                                                                            }\n                                                                                                                        }));\n                                                                                                                },\n                                                                                                                children: \"\\xd7\"\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                lineNumber: 2128,\n                                                                                                                columnNumber: 41\n                                                                                                            }, undefined)\n                                                                                                        ]\n                                                                                                    }, index, true, {\n                                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                        lineNumber: 2126,\n                                                                                                        columnNumber: 39\n                                                                                                    }, undefined))\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 2095,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 2094,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 2092,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1930,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1844,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1843,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 1583,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                lineNumber: 1493,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                        lineNumber: 1439,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"course-list-footer\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"course-list-footer-left\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handlePublish,\n                                    className: \"course-list-btn course-list-btn-publish\",\n                                    disabled: courseList.length === 0 || isPublishingSeries || seriesStatus === 1,\n                                    title: seriesStatus === 1 ? \"系列课程已发布\" : courseList.length === 0 ? \"发布失败：课程系列中至少需要包含一个课程\" : isPublishingSeries ? \"正在发布系列课程...\" : \"发布系列课程\",\n                                    children: seriesStatus === 1 ? \"已发布\" : isPublishingSeries ? \"正在发布...\" : \"发布系列课程\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 2161,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                lineNumber: 2160,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"course-list-footer-right\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleExitEdit,\n                                        className: \"course-list-btn course-list-btn-exit\",\n                                        children: \"退出编辑模式\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 2184,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handlePublishCourse,\n                                        className: \"course-list-btn course-list-btn-publish-course\",\n                                        disabled: !selectedCourseId || ((_courseList_find = courseList.find((c)=>c.id === selectedCourseId)) === null || _courseList_find === void 0 ? void 0 : _courseList_find.status) === 1 || isPublishing,\n                                        title: !selectedCourseId ? \"请先选择要发布的课程\" : ((_courseList_find1 = courseList.find((c)=>c.id === selectedCourseId)) === null || _courseList_find1 === void 0 ? void 0 : _courseList_find1.status) === 1 ? \"该课程已发布\" : isPublishing ? \"正在发布课程...\" : \"发布选中的课程\",\n                                        children: isPublishing ? \"正在发布...\" : \"发布课程\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 2187,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleSave,\n                                        className: \"course-list-btn course-list-btn-save\",\n                                        disabled: uploadingFiles.size > 0 || isCreating || courseList.length === 0,\n                                        title: courseList.length === 0 ? \"请先添加课程内容\" : uploadingFiles.size > 0 ? \"有文件正在上传中，请等待上传完成\" : isCreating ? courseList.some((c)=>c.id > 1000000) ? \"正在创建课程...\" : \"正在保存课程...\" : courseList.some((c)=>c.id > 1000000) ? \"创建课程\" : \"保存课程\",\n                                        children: uploadingFiles.size > 0 ? \"上传中...\" : isCreating ? courseList.some((c)=>c.id > 1000000) ? \"正在创建...\" : \"正在保存...\" : courseList.some((c)=>c.id > 1000000) ? \"创建课程\" : \"保存课程\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 2203,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                lineNumber: 2183,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                        lineNumber: 2159,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                lineNumber: 1416,\n                columnNumber: 7\n            }, undefined),\n            deleteConfirmVisible && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"modal-overlay\",\n                onClick: cancelDelete,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"delete-confirm-modal\",\n                    onClick: (e)=>e.stopPropagation(),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"delete-confirm-header\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    children: \"确认删除\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 2233,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: cancelDelete,\n                                    className: \"close-btn\",\n                                    disabled: isDeleting,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 2239,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 2234,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                            lineNumber: 2232,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"delete-confirm-content\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: isDeleting ? \"正在删除课程，请稍候...\" : \"确定要删除这个课程吗？删除后无法恢复。\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 2243,\n                                    columnNumber: 15\n                                }, undefined),\n                                isDeleting && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"delete-loading\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"loading-spinner\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 2251,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 2250,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                            lineNumber: 2242,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"delete-confirm-footer\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: cancelDelete,\n                                    className: \"cancel-btn\",\n                                    disabled: isDeleting,\n                                    children: \"取消\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 2256,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: confirmDeleteCourse,\n                                    className: \"confirm-btn\",\n                                    disabled: isDeleting,\n                                    children: isDeleting ? \"正在删除...\" : \"确认删除\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 2263,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                            lineNumber: 2255,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                    lineNumber: 2231,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                lineNumber: 2230,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TemplatePickerModal__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                isOpen: isTemplatePickerOpen,\n                onClose: ()=>setIsTemplatePickerOpen(false),\n                onTemplateSelect: handleTemplateSelect\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                lineNumber: 2276,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n        lineNumber: 1415,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CourseListEditModal, \"AqV9qJeo+atHtBhMm8SrUtJrNGY=\");\n_c = CourseListEditModal;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CourseListEditModal);\nvar _c;\n$RefreshReg$(_c, \"CourseListEditModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/workbench/components/CourseListEditModal.tsx\n"));

/***/ })

});