"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/workbench/page",{

/***/ "(app-pages-browser)/./app/workbench/components/CourseListEditModal.tsx":
/*!**********************************************************!*\
  !*** ./app/workbench/components/CourseListEditModal.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FileText,Plus,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FileText,Plus,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FileText,Plus,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FileText,Plus,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FileText,Plus,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _lib_api_course_management__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api/course-management */ \"(app-pages-browser)/./lib/api/course-management.ts\");\n/* harmony import */ var _lib_api_course__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api/course */ \"(app-pages-browser)/./lib/api/course.ts\");\n/* harmony import */ var _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api/upload */ \"(app-pages-browser)/./lib/api/upload.ts\");\n/* harmony import */ var _barrel_optimize_names_Select_antd__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Select!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/select/index.js\");\n/* harmony import */ var logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\");\n/* harmony import */ var _CourseListEditModal_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./CourseListEditModal.css */ \"(app-pages-browser)/./app/workbench/components/CourseListEditModal.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n// 获取系列课程详情\nconst fetchSeriesDetail = async (seriesId)=>{\n    console.log(\"\\uD83D\\uDD0D 获取系列课程详情，seriesId:\", seriesId);\n    console.log(\"\\uD83D\\uDD17 调用API: GET /api/v1/course-management/series/{seriesId}\");\n    const response = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getSeriesDetail(seriesId);\n    console.log(\"\\uD83D\\uDCE1 系列详情API响应:\", response);\n    return response.data;\n};\n// API调用函数 - 获取系列下的所有课程（包含未发布和已发布）\nconst fetchCourseList = async (seriesId)=>{\n    console.log(\"\\uD83D\\uDD0D 获取系列课程列表，seriesId:\", seriesId);\n    console.log(\"\\uD83D\\uDD17 调用API: GET /api/v1/course-management/series/{seriesId}/courses\");\n    // 使用课程管理API获取所有状态的课程\n    const response = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getManagementSeriesCourses(seriesId, {\n        page: 1,\n        pageSize: 100\n    });\n    console.log(\"\\uD83D\\uDCE1 API响应:\", response);\n    return response.data;\n};\n// 获取课程详情\nconst fetchCourseDetail = async (seriesId, courseId)=>{\n    return await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getCourseMarketplaceDetail(seriesId, courseId);\n};\n// 获取课程标签\nconst fetchCourseTags = async ()=>{\n    try {\n        console.log(\"\\uD83D\\uDD0D 开始调用 courseApi.getCourseTags\");\n        const result = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getCourseTags({\n            page: 1,\n            pageSize: 100,\n            status: 1 // 只获取启用的标签\n        });\n        console.log(\"\\uD83D\\uDD0D courseApi.getCourseTags 返回结果:\", result);\n        return result;\n    } catch (error) {\n        console.error(\"\\uD83D\\uDD0D courseApi.getCourseTags 调用失败:\", error);\n        throw error;\n    }\n};\nconst CourseListEditModal = (param)=>{\n    let { isVisible, onClose, onSave, seriesTitle, seriesCoverImage, seriesId = 123 // 默认值，实际使用时应该传入真实的seriesId\n     } = param;\n    var _getSelectedCourse, _getSelectedCourse1, _getSelectedCourse2, _getSelectedCourse3, _courseDetail_contentConfig_video, _courseDetail_contentConfig, _courseDetail_contentConfig_document, _courseDetail_contentConfig1, _courseList_find, _courseList_find1;\n    _s();\n    const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_5__.GetNotification)();\n    const [courseList, setCourseList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [rightPanelType, setRightPanelType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"none\");\n    const [selectedCourseId, setSelectedCourseId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [uploadingFiles, setUploadingFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [isPublishing, setIsPublishing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isCreating, setIsCreating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPublishingSeries, setIsPublishingSeries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [seriesStatus, setSeriesStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0); // 0=草稿，1=已发布，2=已归档\n    // 删除确认弹窗状态\n    const [deleteConfirmVisible, setDeleteConfirmVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [courseToDelete, setCourseToDelete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isDeleting, setIsDeleting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingTitle, setEditingTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(seriesTitle);\n    const [courseGoals, setCourseGoals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [courseObjectives, setCourseObjectives] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [projectMembers, setProjectMembers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // 课程标签相关状态\n    const [courseTags, setCourseTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedTags, setSelectedTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [tagsLoading, setTagsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 课程详细编辑状态\n    const [courseDetail, setCourseDetail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: \"\",\n        description: \"\",\n        coverImage: \"\",\n        videoUrl: \"\",\n        videoName: \"\",\n        isVideoEnabled: false,\n        attachmentUrl: \"\",\n        attachmentName: \"\",\n        isAttachmentEnabled: false,\n        teachingMaterials: [],\n        // 支持teachingInfo结构\n        teachingInfo: [],\n        // 支持contentConfig结构\n        contentConfig: {\n            hasVideo: 0,\n            hasDocument: 0,\n            hasAudio: 0,\n            video: {\n                url: \"\",\n                name: \"\"\n            },\n            document: {\n                url: \"\",\n                name: \"\"\n            },\n            audio: {\n                url: \"\",\n                name: \"\"\n            }\n        },\n        courseContent: {\n            topic: \"\",\n            content: \"\"\n        },\n        isOneKeyOpen: false,\n        isDistributionEnabled: false,\n        distributionReward: \"\",\n        selectedTemplate: \"\",\n        isDistributionWater: false,\n        requiredEnergy: \"\",\n        energyAmount: \"\",\n        isDistributionLimit: false,\n        distributionConditions: {\n            inviteCount: \"\",\n            taskCount: \"\",\n            experience: \"\"\n        },\n        isDistributionTime: false,\n        distributionTimeConditions: {\n            startTime: \"\",\n            endTime: \"\"\n        },\n        distributionMaterials: [],\n        // 任务配置相关状态\n        taskConfig: {\n            taskName: \"\",\n            taskDuration: \"\",\n            taskDescription: \"\",\n            selfAssessmentItems: [\n                \"\"\n            ],\n            referenceWorks: [],\n            referenceResources: []\n        }\n    });\n    // 获取课程列表数据\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isVisible && seriesId) {\n            // 检查用户登录状态\n            const token = localStorage.getItem(\"token\");\n            console.log(\"\\uD83D\\uDD10 检查登录状态，token存在:\", !!token);\n            console.log(\"\\uD83D\\uDD0D seriesId:\", seriesId);\n            if (!token) {\n                console.error(\"❌ 用户未登录，无法获取课程列表\");\n                // 设置空列表，显示空状态\n                setCourseList([]);\n                setLoading(false);\n                return;\n            }\n            loadCourseList();\n            loadCourseTags();\n            loadSeriesDetail();\n        }\n    }, [\n        isVisible,\n        seriesId\n    ]);\n    const loadCourseList = async ()=>{\n        try {\n            setLoading(true);\n            console.log(\"\\uD83D\\uDD0D 开始加载课程列表，seriesId:\", seriesId);\n            const response = await fetchCourseList(seriesId);\n            console.log(\"\\uD83D\\uDCE1 API响应:\", response);\n            if (response.code === 200) {\n                console.log(\"✅ 课程列表数据:\", response.data);\n                const courses = response.data.list || [];\n                console.log(\"✅ 解析的课程数组:\", courses);\n                console.log(\"\\uD83D\\uDCCA 课程ID详情:\", courses.map((c)=>({\n                        id: c.id,\n                        type: typeof c.id,\n                        title: c.title,\n                        status: c.status\n                    })));\n                console.log(\"\\uD83D\\uDCCA 课程状态统计:\", {\n                    total: courses.length,\n                    draft: courses.filter((c)=>c.status === 0).length,\n                    published: courses.filter((c)=>c.status === 1).length\n                });\n                setCourseList(courses);\n            } else {\n                console.error(\"❌ API返回错误:\", response);\n                setCourseList([]);\n            }\n        } catch (error) {\n            var _error_response, _error_response1, _error_response2;\n            console.error(\"❌ 加载课程列表失败:\", error);\n            // 检查是否是认证错误\n            if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 401) {\n                console.error(\"\\uD83D\\uDD10 认证失败，用户未登录或token已过期\");\n            } else if (((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status) === 403) {\n                console.error(\"\\uD83D\\uDEAB 权限不足，无法访问该系列课程\");\n            } else if (((_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.status) === 404) {\n                console.error(\"\\uD83D\\uDCED 系列课程不存在，seriesId:\", seriesId);\n            } else {\n                console.error(\"\\uD83D\\uDD27 其他错误:\", error.message);\n            }\n            setCourseList([]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 加载系列课程详情\n    const loadSeriesDetail = async ()=>{\n        try {\n            console.log(\"\\uD83D\\uDD0D 开始加载系列课程详情，seriesId:\", seriesId);\n            const response = await fetchSeriesDetail(seriesId);\n            console.log(\"\\uD83D\\uDCE1 系列详情响应:\", response);\n            if (response.code === 200 && response.data) {\n                const seriesData = response.data;\n                console.log(\"✅ 系列课程详情:\", seriesData);\n                setSeriesStatus(seriesData.status || 0);\n                console.log(\"\\uD83D\\uDCCA 系列课程状态:\", seriesData.status, \"(0=草稿，1=已发布，2=已归档)\");\n            } else {\n                console.error(\"❌ 获取系列详情失败:\", response.message);\n            }\n        } catch (error) {\n            console.error(\"❌ 加载系列详情异常:\", error);\n        }\n    };\n    // 加载课程标签\n    const loadCourseTags = async ()=>{\n        try {\n            setTagsLoading(true);\n            console.log(\"\\uD83C\\uDFF7️ 开始加载课程标签\");\n            const response = await fetchCourseTags();\n            console.log(\"\\uD83D\\uDCE1 标签API完整响应:\", response);\n            // 检查响应结构\n            if (response && response.data) {\n                console.log(\"\\uD83D\\uDCCA 响应数据:\", response.data);\n                let tags = [];\n                // 处理标准的API响应格式 (response.data.list) - 根据实际API响应\n                if (response.data.list && Array.isArray(response.data.list)) {\n                    tags = response.data.list;\n                    console.log(\"✅ 从 data.list 解析到标签:\", tags.length, \"个\");\n                } else if (Array.isArray(response.data)) {\n                    tags = response.data;\n                    console.log(\"✅ 从 data 数组解析到标签:\", tags.length, \"个\");\n                } else if (response.data.data && response.data.data.list && Array.isArray(response.data.data.list)) {\n                    tags = response.data.data.list;\n                    console.log(\"✅ 从 data.data.list 解析到标签:\", tags.length, \"个\");\n                }\n                // 验证标签数据格式\n                console.log(\"\\uD83D\\uDD0D 原始标签数据:\", tags);\n                console.log(\"\\uD83D\\uDD0D 标签数据类型检查:\");\n                tags.forEach((tag, index)=>{\n                    var _tag_name;\n                    console.log(\"标签\".concat(index, \":\"), {\n                        tag,\n                        hasTag: !!tag,\n                        idType: typeof (tag === null || tag === void 0 ? void 0 : tag.id),\n                        nameType: typeof (tag === null || tag === void 0 ? void 0 : tag.name),\n                        nameValue: tag === null || tag === void 0 ? void 0 : tag.name,\n                        nameNotEmpty: (tag === null || tag === void 0 ? void 0 : (_tag_name = tag.name) === null || _tag_name === void 0 ? void 0 : _tag_name.trim()) !== \"\"\n                    });\n                });\n                const validTags = tags.filter((tag)=>{\n                    const isValid = tag && typeof tag.id === \"number\" && typeof tag.name === \"string\" && tag.name.trim() !== \"\";\n                    if (!isValid) {\n                        console.log(\"❌ 无效标签:\", tag, {\n                            hasTag: !!tag,\n                            idType: typeof (tag === null || tag === void 0 ? void 0 : tag.id),\n                            nameType: typeof (tag === null || tag === void 0 ? void 0 : tag.name),\n                            nameValue: tag === null || tag === void 0 ? void 0 : tag.name\n                        });\n                    }\n                    return isValid;\n                });\n                console.log(\"✅ 有效标签数量:\", validTags.length);\n                console.log(\"✅ 有效标签详情:\", validTags);\n                if (validTags.length > 0) {\n                    setCourseTags(validTags);\n                    console.log(\"✅ 成功设置真实标签数据\");\n                    return;\n                } else {\n                    console.warn(\"⚠️ 没有有效的标签数据\");\n                }\n            } else {\n                console.warn(\"⚠️ API响应格式不正确:\", response);\n            }\n            // 如果没有真实数据，设置空数组\n            console.log(\"\\uD83D\\uDCED 没有标签数据，设置空数组\");\n            setCourseTags([]);\n        } catch (error) {\n            var _error_response, _error_response1, _error_response2;\n            console.error(\"❌ 加载课程标签失败:\", error);\n            console.error(\"❌ 错误详情:\", {\n                message: error.message,\n                status: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status,\n                statusText: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.statusText,\n                data: (_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.data\n            });\n            // 发生错误时设置空数组\n            setCourseTags([]);\n        } finally{\n            setTagsLoading(false);\n        }\n    };\n    // 添加新课程\n    const addNewCourse = ()=>{\n        const newCourse = {\n            id: Date.now(),\n            seriesId: seriesId,\n            title: \"第\".concat(courseList.length + 1, \"课 - 新课时\"),\n            description: \"\",\n            coverImage: \"\",\n            orderIndex: courseList.length + 1,\n            status: 0,\n            statusLabel: \"草稿\",\n            hasVideo: 0,\n            hasDocument: 0,\n            hasAudio: 0,\n            videoDuration: 0,\n            videoDurationLabel: \"\",\n            videoName: \"\",\n            firstTeachingTitle: \"\",\n            resourcesCount: 0,\n            createdAt: new Date().toISOString(),\n            updatedAt: new Date().toISOString()\n        };\n        setCourseList([\n            ...courseList,\n            newCourse\n        ]);\n        // 自动选中新添加的课程\n        showCoursePanel(newCourse.id);\n    };\n    // 显示删除确认弹窗\n    const showDeleteConfirm = (id)=>{\n        setCourseToDelete(id);\n        setDeleteConfirmVisible(true);\n    };\n    // 确认删除课程\n    const confirmDeleteCourse = async ()=>{\n        if (!courseToDelete) return;\n        try {\n            setIsDeleting(true);\n            // 调用删除API\n            await _lib_api_course_management__WEBPACK_IMPORTED_MODULE_2__.courseManagementApi.deleteCourse(courseToDelete);\n            // 从列表中移除课程\n            setCourseList(courseList.filter((course)=>course.id !== courseToDelete));\n            // 如果删除的是当前选中的课程，清空右侧面板\n            if (selectedCourseId === courseToDelete) {\n                setRightPanelType(\"none\");\n                setSelectedCourseId(null);\n            }\n            // 关闭确认弹窗\n            setDeleteConfirmVisible(false);\n            setCourseToDelete(null);\n            // 显示成功提示\n            notification.success(\"课程已成功删除\");\n        } catch (error) {\n            console.error(\"删除课程失败:\", error);\n            notification.error(\"删除课程失败，请重试\");\n        } finally{\n            setIsDeleting(false);\n        }\n    };\n    // 取消删除\n    const cancelDelete = ()=>{\n        if (isDeleting) return; // 正在删除时不允许取消\n        setDeleteConfirmVisible(false);\n        setCourseToDelete(null);\n    };\n    // 更新课程标题\n    const updateCourseTitle = (id, newTitle)=>{\n        setCourseList(courseList.map((course)=>course.id === id ? {\n                ...course,\n                title: newTitle\n            } : course));\n    };\n    // 处理课程封面上传\n    const handleCoverUpload = async (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (file) {\n            // 检查文件类型\n            const allowedTypes = [\n                \"image/jpeg\",\n                \"image/jpg\",\n                \"image/png\",\n                \"image/gif\"\n            ];\n            if (!allowedTypes.includes(file.type)) {\n                notification.error(\"请选择 JPG、PNG 或 GIF 格式的图片文件\");\n                return;\n            }\n            // 检查文件大小 (10MB)\n            if (file.size > 10 * 1024 * 1024) {\n                notification.error(\"文件大小不能超过 10MB\");\n                return;\n            }\n            try {\n                console.log(\"\\uD83D\\uDCE4 开始上传课程封面:\", file.name);\n                // 添加到上传中的文件列表\n                setUploadingFiles((prev)=>new Set(prev).add(\"cover\"));\n                // 先显示预览图片\n                const previewUrl = URL.createObjectURL(file);\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        coverImage: previewUrl\n                    }));\n                // 上传到OSS\n                const imageUrl = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n                console.log(\"✅ 课程封面上传成功:\", imageUrl);\n                // 更新课程详情中的封面为真实URL\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        coverImage: imageUrl\n                    }));\n                // 同时更新课程列表中的封面\n                if (selectedCourseId) {\n                    setCourseList((prev)=>prev.map((course)=>course.id === selectedCourseId ? {\n                                ...course,\n                                coverImage: imageUrl\n                            } : course));\n                }\n                alert(\"课程封面上传成功！\");\n            } catch (error) {\n                console.error(\"❌ 课程封面上传失败:\", error);\n                notification.error(\"课程封面上传失败，请重试\");\n                // 上传失败时清除预览图片\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        coverImage: \"\"\n                    }));\n            } finally{\n                // 从上传中的文件列表移除\n                setUploadingFiles((prev)=>{\n                    const newSet = new Set(prev);\n                    newSet.delete(\"cover\");\n                    return newSet;\n                });\n            }\n        }\n    };\n    // 处理视频上传\n    const handleVideoUpload = async (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (file) {\n            // 检查文件类型\n            const allowedTypes = [\n                \"video/mp4\",\n                \"video/avi\",\n                \"video/mov\",\n                \"video/wmv\",\n                \"video/flv\"\n            ];\n            if (!allowedTypes.includes(file.type)) {\n                alert(\"请选择 MP4、AVI、MOV、WMV 或 FLV 格式的视频文件\");\n                return;\n            }\n            // 检查文件大小 (100MB)\n            if (file.size > 100 * 1024 * 1024) {\n                alert(\"视频文件大小不能超过 100MB\");\n                return;\n            }\n            try {\n                console.log(\"\\uD83D\\uDCE4 开始上传课程视频:\", file.name);\n                // 添加到上传中的文件列表\n                setUploadingFiles((prev)=>new Set(prev).add(\"video\"));\n                // 先显示预览视频\n                const previewUrl = URL.createObjectURL(file);\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        contentConfig: {\n                            ...prev.contentConfig,\n                            video: {\n                                url: previewUrl,\n                                name: file.name\n                            }\n                        }\n                    }));\n                // 上传到OSS\n                const videoUrl = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n                console.log(\"✅ 课程视频上传成功:\", videoUrl);\n                // 更新课程详情中的视频信息为真实URL\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        contentConfig: {\n                            ...prev.contentConfig,\n                            video: {\n                                url: videoUrl,\n                                name: file.name\n                            }\n                        }\n                    }));\n                // 同时更新课程列表中的视频信息\n                if (selectedCourseId) {\n                    setCourseList((prev)=>prev.map((course)=>course.id === selectedCourseId ? {\n                                ...course,\n                                contentConfig: {\n                                    ...course.contentConfig,\n                                    video: {\n                                        url: videoUrl,\n                                        name: file.name\n                                    }\n                                }\n                            } : course));\n                }\n                alert(\"课程视频上传成功！\");\n            } catch (error) {\n                console.error(\"❌ 课程视频上传失败:\", error);\n                alert(\"课程视频上传失败，请重试\");\n                // 上传失败时清除视频信息\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        contentConfig: {\n                            ...prev.contentConfig,\n                            video: {\n                                url: \"\",\n                                name: \"\"\n                            }\n                        }\n                    }));\n            } finally{\n                // 从上传中的文件列表移除\n                setUploadingFiles((prev)=>{\n                    const newSet = new Set(prev);\n                    newSet.delete(\"video\");\n                    return newSet;\n                });\n            }\n        }\n    };\n    // 触发视频文件选择\n    const triggerVideoUpload = ()=>{\n        const input = document.createElement(\"input\");\n        input.type = \"file\";\n        input.accept = \"video/mp4,video/avi,video/mov,video/wmv,video/flv\";\n        input.onchange = (e)=>handleVideoUpload(e);\n        input.click();\n    };\n    // 处理附件上传\n    const handleAttachmentUpload = async (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (file) {\n            // 检查文件类型\n            const allowedTypes = [\n                \"application/pdf\",\n                \"application/msword\",\n                \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\",\n                \"application/vnd.ms-excel\",\n                \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\",\n                \"application/vnd.ms-powerpoint\",\n                \"application/vnd.openxmlformats-officedocument.presentationml.presentation\",\n                \"text/plain\",\n                \"image/jpeg\",\n                \"image/png\",\n                \"image/gif\"\n            ];\n            if (!allowedTypes.includes(file.type)) {\n                alert(\"请选择支持的文件格式：PDF、DOC、DOCX、XLS、XLSX、PPT、PPTX、TXT、JPG、PNG、GIF\");\n                return;\n            }\n            // 检查文件大小 (10MB)\n            if (file.size > 10 * 1024 * 1024) {\n                alert(\"文件大小不能超过 10MB\");\n                return;\n            }\n            try {\n                console.log(\"\\uD83D\\uDCE4 开始上传课程附件:\", file.name);\n                // 添加到上传中的文件列表\n                setUploadingFiles((prev)=>new Set(prev).add(\"document\"));\n                // 上传到OSS\n                const documentUrl = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n                console.log(\"✅ 课程附件上传成功:\", documentUrl);\n                // 更新课程详情中的附件信息\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        contentConfig: {\n                            ...prev.contentConfig,\n                            hasDocument: 1,\n                            document: {\n                                url: documentUrl,\n                                name: file.name\n                            }\n                        }\n                    }));\n                alert(\"课程附件上传成功！\");\n            } catch (error) {\n                console.error(\"❌ 课程附件上传失败:\", error);\n                alert(\"课程附件上传失败，请重试\");\n            } finally{\n                // 从上传中的文件列表移除\n                setUploadingFiles((prev)=>{\n                    const newSet = new Set(prev);\n                    newSet.delete(\"document\");\n                    return newSet;\n                });\n            }\n        }\n    };\n    // 触发附件文件选择\n    const triggerAttachmentUpload = ()=>{\n        const input = document.createElement(\"input\");\n        input.type = \"file\";\n        input.accept = \".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.jpg,.png,.gif\";\n        input.onchange = (e)=>handleAttachmentUpload(e);\n        input.click();\n    };\n    // 处理教学附件上传\n    const handleTeachingMaterialUpload = async (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (file) {\n            // 检查文件类型\n            const allowedTypes = [\n                \"application/pdf\",\n                \"application/msword\",\n                \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\",\n                \"application/vnd.ms-excel\",\n                \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\",\n                \"application/vnd.ms-powerpoint\",\n                \"application/vnd.openxmlformats-officedocument.presentationml.presentation\",\n                \"text/plain\",\n                \"image/jpeg\",\n                \"image/png\",\n                \"image/gif\"\n            ];\n            if (!allowedTypes.includes(file.type)) {\n                alert(\"请选择支持的文件格式：PDF、DOC、DOCX、XLS、XLSX、PPT、PPTX、TXT、JPG、PNG、GIF\");\n                return;\n            }\n            // 检查文件大小 (10MB)\n            if (file.size > 10 * 1024 * 1024) {\n                alert(\"文件大小不能超过 10MB\");\n                return;\n            }\n            try {\n                console.log(\"\\uD83D\\uDCE4 开始上传教学材料:\", file.name);\n                // 添加到上传中的文件列表\n                setUploadingFiles((prev)=>new Set(prev).add(\"teaching-\".concat(Date.now())));\n                // 上传到OSS\n                const materialUrl = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n                console.log(\"✅ 教学材料上传成功:\", materialUrl);\n                // 添加到教学附件列表\n                const newMaterial = {\n                    type: file.type,\n                    name: file.name,\n                    url: materialUrl\n                };\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        teachingMaterials: [\n                            ...prev.teachingMaterials,\n                            newMaterial\n                        ]\n                    }));\n                alert(\"教学材料上传成功！\");\n            } catch (error) {\n                console.error(\"❌ 教学材料上传失败:\", error);\n                alert(\"教学材料上传失败，请重试\");\n            } finally{\n                // 从上传中的文件列表移除\n                setUploadingFiles((prev)=>{\n                    const newSet = new Set(prev);\n                    // 移除所有teaching-开头的项目\n                    Array.from(newSet).forEach((item)=>{\n                        if (item.startsWith(\"teaching-\")) {\n                            newSet.delete(item);\n                        }\n                    });\n                    return newSet;\n                });\n            }\n        }\n    };\n    // 触发教学附件文件选择\n    const triggerTeachingMaterialUpload = ()=>{\n        const input = document.createElement(\"input\");\n        input.type = \"file\";\n        input.accept = \".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.jpg,.png,.gif\";\n        input.onchange = (e)=>handleTeachingMaterialUpload(e);\n        input.click();\n    };\n    // 删除教学附件\n    const removeTeachingMaterial = (index)=>{\n        setCourseDetail((prev)=>({\n                ...prev,\n                teachingMaterials: prev.teachingMaterials.filter((_, i)=>i !== index)\n            }));\n    };\n    // 课程内容管理函数\n    const addTeachingInfoItem = ()=>{\n        setCourseDetail((prev)=>({\n                ...prev,\n                teachingInfo: [\n                    ...prev.teachingInfo,\n                    {\n                        title: \"\",\n                        content: \"\"\n                    }\n                ]\n            }));\n    };\n    const removeTeachingInfoItem = (index)=>{\n        setCourseDetail((prev)=>({\n                ...prev,\n                teachingInfo: prev.teachingInfo.filter((_, i)=>i !== index)\n            }));\n    };\n    const updateTeachingInfoTitle = (index, title)=>{\n        setCourseDetail((prev)=>{\n            const newTeachingInfo = [\n                ...prev.teachingInfo\n            ];\n            newTeachingInfo[index] = {\n                ...newTeachingInfo[index],\n                title\n            };\n            return {\n                ...prev,\n                teachingInfo: newTeachingInfo\n            };\n        });\n    };\n    const updateTeachingInfoContent = (index, content)=>{\n        setCourseDetail((prev)=>{\n            const newTeachingInfo = [\n                ...prev.teachingInfo\n            ];\n            newTeachingInfo[index] = {\n                ...newTeachingInfo[index],\n                content\n            };\n            return {\n                ...prev,\n                teachingInfo: newTeachingInfo\n            };\n        });\n    };\n    // 将UI格式的teachingInfo转换为API格式\n    const convertTeachingInfoForAPI = (teachingInfo)=>{\n        return teachingInfo.map((info)=>({\n                title: info.title,\n                content: info.content.split(\"\\n\").filter((line)=>line.trim()) // 按换行分割并过滤空行\n            }));\n    };\n    // 检查是否有未上传完成的文件（blob URL）\n    const checkForBlobUrls = (course)=>{\n        var _course_contentConfig_video, _course_contentConfig, _course_contentConfig_document, _course_contentConfig1;\n        const issues = [];\n        // 检查封面图片\n        if (course.coverImage && course.coverImage.startsWith(\"blob:\")) {\n            issues.push(\"课程封面图片\");\n        }\n        // 检查视频文件\n        if (((_course_contentConfig = course.contentConfig) === null || _course_contentConfig === void 0 ? void 0 : (_course_contentConfig_video = _course_contentConfig.video) === null || _course_contentConfig_video === void 0 ? void 0 : _course_contentConfig_video.url) && course.contentConfig.video.url.startsWith(\"blob:\")) {\n            issues.push(\"课程视频\");\n        }\n        // 检查文档附件\n        if (((_course_contentConfig1 = course.contentConfig) === null || _course_contentConfig1 === void 0 ? void 0 : (_course_contentConfig_document = _course_contentConfig1.document) === null || _course_contentConfig_document === void 0 ? void 0 : _course_contentConfig_document.url) && course.contentConfig.document.url.startsWith(\"blob:\")) {\n            issues.push(\"课程文档\");\n        }\n        // 检查教学材料\n        if (course.additionalResources) {\n            course.additionalResources.forEach((resource, index)=>{\n                if (resource.url && resource.url.startsWith(\"blob:\")) {\n                    issues.push(\"教学材料\".concat(index + 1));\n                }\n            });\n        }\n        return issues;\n    };\n    // 保存单个课程到后端\n    const saveCourse = async (course)=>{\n        try {\n            var _course_contentConfig_video, _course_contentConfig, _course_contentConfig_document, _course_contentConfig1;\n            // 验证必要的数据\n            if (!course.title || course.title.trim() === \"\") {\n                throw new Error(\"课程标题不能为空\");\n            }\n            // 检查是否有未上传完成的文件\n            const blobIssues = checkForBlobUrls(course);\n            if (blobIssues.length > 0) {\n                throw new Error(\"以下文件尚未上传完成，请等待上传完成后再保存：\".concat(blobIssues.join(\"、\")));\n            }\n            // 准备课程数据\n            const courseData = {\n                seriesId: seriesId,\n                title: course.title,\n                description: course.description || \"\",\n                coverImage: course.coverImage || \"\",\n                hasVideo: ((_course_contentConfig = course.contentConfig) === null || _course_contentConfig === void 0 ? void 0 : (_course_contentConfig_video = _course_contentConfig.video) === null || _course_contentConfig_video === void 0 ? void 0 : _course_contentConfig_video.url) ? 1 : 0,\n                hasDocument: ((_course_contentConfig1 = course.contentConfig) === null || _course_contentConfig1 === void 0 ? void 0 : (_course_contentConfig_document = _course_contentConfig1.document) === null || _course_contentConfig_document === void 0 ? void 0 : _course_contentConfig_document.url) ? 1 : 0,\n                hasAudio: 0,\n                videoDuration: 0,\n                contentConfig: course.contentConfig || {},\n                teachingInfo: convertTeachingInfoForAPI(course.teachingInfo || []),\n                additionalResources: course.additionalResources || [],\n                orderIndex: course.orderIndex || 1\n            };\n            console.log(\"\\uD83D\\uDCBE 准备保存课程数据:\", courseData);\n            let result;\n            // 判断是新课程还是更新课程\n            if (course.id && course.id > 1000000) {\n                // 新课程，使用创建API\n                console.log(\"\\uD83D\\uDCE4 创建新课程\");\n                const { data: response } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.createCourse(courseData);\n                result = response;\n                console.log(\"✅ 课程创建成功\");\n            } else {\n                // 现有课程，使用更新API\n                console.log(\"\\uD83D\\uDCE4 更新现有课程:\", course.id);\n                const { data: response } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.updateCourse(course.id, courseData);\n                result = response;\n                console.log(\"✅ 课程更新成功\");\n            }\n            return result;\n        } catch (error) {\n            console.error(\"❌ 保存课程失败:\", error);\n            throw error;\n        }\n    };\n    // 发布选中的课程\n    const handlePublishCourse = async ()=>{\n        if (!selectedCourseId) {\n            alert(\"请先选择要发布的课程\");\n            return;\n        }\n        const selectedCourse = courseList.find((course)=>course.id === selectedCourseId);\n        if (!selectedCourse) {\n            alert(\"未找到选中的课程\");\n            return;\n        }\n        // 检查课程是否已经发布\n        if (selectedCourse.status === 1) {\n            alert(\"该课程已经发布，无需重复发布\");\n            return;\n        }\n        try {\n            setIsPublishing(true);\n            console.log(\"\\uD83D\\uDCE4 开始发布课程\");\n            console.log(\"\\uD83D\\uDCE4 selectedCourseId:\", selectedCourseId, \"类型:\", typeof selectedCourseId);\n            console.log(\"\\uD83D\\uDCE4 课程信息:\", selectedCourse);\n            console.log(\"\\uD83D\\uDCE4 课程ID字段:\", selectedCourse.id, \"类型:\", typeof selectedCourse.id);\n            console.log(\"\\uD83D\\uDCE4 课程列表中的所有ID:\", courseList.map((c)=>({\n                    id: c.id,\n                    type: typeof c.id,\n                    title: c.title\n                })));\n            // 确保使用正确的课程ID\n            const courseIdToPublish = selectedCourse.id;\n            console.log(\"\\uD83D\\uDCE4 即将发布的课程ID:\", courseIdToPublish, \"类型:\", typeof courseIdToPublish);\n            const { data: response } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.publishCourse(courseIdToPublish);\n            if (response.code === 200) {\n                console.log(\"✅ 课程发布成功:\", response.data);\n                alert(\"课程发布成功！\");\n                // 刷新课程列表以更新状态\n                await loadCourseList();\n            } else {\n                console.error(\"❌ 发布课程失败:\", response.message);\n                alert(response.message || \"发布课程失败\");\n            }\n        } catch (error) {\n            var _error_response, _error_response_data, _error_response1;\n            console.error(\"❌ 发布课程失败:\", error);\n            console.error(\"❌ 错误详情:\", (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data);\n            // 处理具体的错误信息\n            if ((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : (_error_response_data = _error_response1.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) {\n                alert(error.response.data.message);\n            } else if (error.message) {\n                alert(error.message);\n            } else {\n                alert(\"发布课程失败，请重试\");\n            }\n        } finally{\n            setIsPublishing(false);\n        }\n    };\n    // 保存课程列表\n    const handleSave = async ()=>{\n        try {\n            setIsCreating(true);\n            // 检查是否有文件正在上传\n            if (uploadingFiles.size > 0) {\n                alert(\"有文件正在上传中，请等待上传完成后再保存\");\n                setIsCreating(false);\n                return;\n            }\n            console.log(\"\\uD83D\\uDCBE 开始保存课程列表\");\n            // 如果有选中的课程且在编辑状态，先保存当前课程\n            if (selectedCourseId && rightPanelType === \"course\") {\n                const selectedCourse = courseList.find((c)=>c.id === selectedCourseId);\n                if (selectedCourse) {\n                    var _courseDetail_teachingMaterials;\n                    // 更新课程数据\n                    const updatedCourse = {\n                        ...selectedCourse,\n                        title: courseDetail.title,\n                        description: courseDetail.description,\n                        coverImage: courseDetail.coverImage,\n                        hasVideo: courseDetail.isVideoEnabled ? 1 : 0,\n                        hasDocument: courseDetail.isAttachmentEnabled ? 1 : 0,\n                        contentConfig: courseDetail.contentConfig,\n                        teachingInfo: courseDetail.teachingInfo,\n                        additionalResources: ((_courseDetail_teachingMaterials = courseDetail.teachingMaterials) === null || _courseDetail_teachingMaterials === void 0 ? void 0 : _courseDetail_teachingMaterials.map((material)=>({\n                                title: material.name,\n                                url: material.url,\n                                description: material.name\n                            }))) || []\n                    };\n                    // 先更新课程列表中的数据\n                    setCourseList((prev)=>prev.map((course)=>course.id === selectedCourseId ? updatedCourse : course));\n                    // 然后保存到后端\n                    await saveCourse(updatedCourse);\n                }\n            }\n            // 保存系列课程信息\n            const data = {\n                title: editingTitle,\n                courseGoals,\n                courseObjectives,\n                courseList\n            };\n            onSave(data);\n            onClose();\n            console.log(\"✅ 课程列表保存完成\");\n        } catch (error) {\n            console.error(\"❌ 保存失败:\", error);\n            alert(\"保存失败: \".concat(error.message || \"请重试\"));\n        } finally{\n            setIsCreating(false);\n        }\n    };\n    // 发布系列课程\n    const handlePublish = async ()=>{\n        // 如果系列已发布，不执行任何操作\n        if (seriesStatus === 1) {\n            return;\n        }\n        try {\n            setIsPublishingSeries(true);\n            // 检查是否有课程\n            if (courseList.length === 0) {\n                alert(\"发布失败：课程系列中至少需要包含一个课程\");\n                return;\n            }\n            console.log(\"\\uD83D\\uDCE4 开始发布系列课程，系列ID:\", seriesId);\n            const { data: response } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.publishCourseSeries(seriesId);\n            if (response.code === 200) {\n                console.log(\"✅ 系列课程发布成功:\", response.data);\n                // 构建成功消息\n                const publishData = response.data;\n                let successMessage = '系列课程\"'.concat(publishData.title, '\"发布成功！');\n                // 如果有发布统计信息，添加到消息中\n                if (publishData.publishStats) {\n                    const stats = publishData.publishStats;\n                    successMessage += \"\\n\\n发布统计：\\n• 总课程数：\".concat(publishData.totalCourses, \"\\n• 已发布课程：\").concat(publishData.publishedCourses, \"\\n• 视频课程：\").concat(stats.videoCourseCount, \"个\\n• 文档课程：\").concat(stats.documentCourseCount, \"个\\n• 总资源数：\").concat(stats.totalResourcesCount, \"个\");\n                    if (stats.totalVideoDuration > 0) {\n                        const durationMinutes = Math.round(stats.totalVideoDuration / 60);\n                        successMessage += \"\\n• 视频总时长：\".concat(durationMinutes, \"分钟\");\n                    }\n                }\n                alert(successMessage);\n                // 更新系列状态为已发布\n                setSeriesStatus(1);\n                // 刷新课程列表以更新状态\n                await loadCourseList();\n                // 通知父组件刷新数据\n                onSave({\n                    type: \"publish_series\",\n                    seriesId: seriesId,\n                    message: \"系列课程发布成功\"\n                });\n            } else {\n                console.error(\"❌ 发布系列课程失败:\", response.message);\n                alert(response.message || \"发布系列课程失败\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"❌ 发布系列课程出错:\", error);\n            // 处理具体的错误信息\n            let errorMessage = \"发布系列课程失败\";\n            if ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) {\n                errorMessage = error.response.data.message;\n            } else if (error.message) {\n                errorMessage = error.message;\n            }\n            alert(errorMessage);\n        } finally{\n            setIsPublishingSeries(false);\n        }\n    };\n    // 退出编辑模式 - 保存数据并关闭\n    const handleExitEdit = ()=>{\n        handleSave();\n    };\n    // 显示设置面板\n    const showSettingsPanel = ()=>{\n        setRightPanelType(\"settings\");\n        setSelectedCourseId(null);\n    };\n    // 显示课程编辑面板\n    const showCoursePanel = async (courseId)=>{\n        console.log(\"\\uD83C\\uDFAF showCoursePanel 被调用\");\n        console.log(\"\\uD83C\\uDFAF 传入的courseId:\", courseId, \"类型:\", typeof courseId);\n        console.log(\"\\uD83C\\uDFAF 当前课程列表:\", courseList.map((c)=>({\n                id: c.id,\n                type: typeof c.id,\n                title: c.title\n            })));\n        setRightPanelType(\"course\");\n        setSelectedCourseId(courseId);\n        // 获取选中的课程并更新courseDetail状态\n        const selectedCourse = courseList.find((course)=>course.id === courseId);\n        console.log(\"\\uD83C\\uDFAF 找到的课程:\", selectedCourse);\n        if (selectedCourse) {\n            try {\n                console.log(\"\\uD83D\\uDD04 获取课程详情，seriesId:\", seriesId, \"courseId:\", courseId);\n                // 获取真实的课程详情数据\n                const { data: res } = await fetchCourseDetail(seriesId, courseId);\n                if (res.code === 200 && res.data) {\n                    var _courseDetailData_additionalResources, _courseDetailData_teachingInfo;\n                    const courseDetailData = res.data;\n                    console.log(\"✅ 获取到课程详情:\", courseDetailData);\n                    // 将真实的 additionalResources 映射到 teachingMaterials\n                    const teachingMaterials = ((_courseDetailData_additionalResources = courseDetailData.additionalResources) === null || _courseDetailData_additionalResources === void 0 ? void 0 : _courseDetailData_additionalResources.map((resource)=>({\n                            type: \"application/octet-stream\",\n                            name: resource.title || resource.name || \"附件\",\n                            url: resource.url\n                        }))) || [];\n                    // 将API返回的teachingInfo数组格式转换为新的格式\n                    const mappedTeachingInfo = ((_courseDetailData_teachingInfo = courseDetailData.teachingInfo) === null || _courseDetailData_teachingInfo === void 0 ? void 0 : _courseDetailData_teachingInfo.map((info)=>({\n                            title: info.title || \"\",\n                            content: Array.isArray(info.content) ? info.content.join(\"\\n\") : info.content || \"\"\n                        }))) || [];\n                    console.log(\"\\uD83D\\uDCCE 映射的教学附件:\", teachingMaterials);\n                    console.log(\"\\uD83D\\uDCDA 映射的教学信息:\", mappedTeachingInfo);\n                    setCourseDetail((prev)=>{\n                        var _courseDetailData_contentConfig_video, _courseDetailData_contentConfig, _courseDetailData_contentConfig_video1, _courseDetailData_contentConfig1, _courseDetailData_contentConfig_document, _courseDetailData_contentConfig2, _courseDetailData_contentConfig_document1, _courseDetailData_contentConfig3;\n                        return {\n                            ...prev,\n                            title: courseDetailData.title,\n                            description: courseDetailData.description,\n                            coverImage: courseDetailData.coverImage || \"\",\n                            isVideoEnabled: courseDetailData.hasVideo === 1,\n                            isAttachmentEnabled: courseDetailData.hasDocument === 1,\n                            contentConfig: courseDetailData.contentConfig || {},\n                            teachingInfo: mappedTeachingInfo,\n                            teachingMaterials: teachingMaterials,\n                            videoUrl: ((_courseDetailData_contentConfig = courseDetailData.contentConfig) === null || _courseDetailData_contentConfig === void 0 ? void 0 : (_courseDetailData_contentConfig_video = _courseDetailData_contentConfig.video) === null || _courseDetailData_contentConfig_video === void 0 ? void 0 : _courseDetailData_contentConfig_video.url) || \"\",\n                            videoName: ((_courseDetailData_contentConfig1 = courseDetailData.contentConfig) === null || _courseDetailData_contentConfig1 === void 0 ? void 0 : (_courseDetailData_contentConfig_video1 = _courseDetailData_contentConfig1.video) === null || _courseDetailData_contentConfig_video1 === void 0 ? void 0 : _courseDetailData_contentConfig_video1.name) || \"\",\n                            attachmentUrl: ((_courseDetailData_contentConfig2 = courseDetailData.contentConfig) === null || _courseDetailData_contentConfig2 === void 0 ? void 0 : (_courseDetailData_contentConfig_document = _courseDetailData_contentConfig2.document) === null || _courseDetailData_contentConfig_document === void 0 ? void 0 : _courseDetailData_contentConfig_document.url) || \"\",\n                            attachmentName: ((_courseDetailData_contentConfig3 = courseDetailData.contentConfig) === null || _courseDetailData_contentConfig3 === void 0 ? void 0 : (_courseDetailData_contentConfig_document1 = _courseDetailData_contentConfig3.document) === null || _courseDetailData_contentConfig_document1 === void 0 ? void 0 : _courseDetailData_contentConfig_document1.name) || \"\"\n                        };\n                    });\n                } else {\n                    console.error(\"❌ 获取课程详情失败:\", res.message);\n                    // 使用基础数据作为后备\n                    setCourseDetail((prev)=>({\n                            ...prev,\n                            title: selectedCourse.title,\n                            description: selectedCourse.description,\n                            coverImage: selectedCourse.coverImage || \"\",\n                            isVideoEnabled: selectedCourse.hasVideo === 1,\n                            isAttachmentEnabled: selectedCourse.hasDocument === 1,\n                            teachingMaterials: [] // 清空附件列表\n                        }));\n                }\n            } catch (error) {\n                console.error(\"❌ 获取课程详情异常:\", error);\n                // 使用基础数据作为后备\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        title: selectedCourse.title,\n                        description: selectedCourse.description,\n                        coverImage: selectedCourse.coverImage || \"\",\n                        isVideoEnabled: selectedCourse.hasVideo === 1,\n                        isAttachmentEnabled: selectedCourse.hasDocument === 1,\n                        teachingMaterials: [] // 清空附件列表\n                    }));\n            }\n        }\n    };\n    // 获取选中的课程\n    const getSelectedCourse = ()=>{\n        return courseList.find((course)=>course.id === selectedCourseId);\n    };\n    if (!isVisible) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"course-list-modal-overlay\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"course-list-modal\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"course-list-header\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"course-list-title-section\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"course-list-title\",\n                                        children: \"课程列表\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 1245,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"course-list-actions\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: showSettingsPanel,\n                                                className: \"course-list-settings-btn \".concat(rightPanelType === \"settings\" ? \"active\" : \"\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1251,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1247,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: addNewCourse,\n                                                className: \"course-list-add-btn\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1254,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1253,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 1246,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                lineNumber: 1244,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onClose,\n                                className: \"course-list-close-btn\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 1259,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                lineNumber: 1258,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                        lineNumber: 1243,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"course-list-content\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"course-list-sidebar\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"course-list-items\",\n                                    children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"course-list-loading\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"加载中...\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 1270,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 1269,\n                                        columnNumber: 17\n                                    }, undefined) : courseList.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"course-list-empty\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"course-list-empty-icon\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"w-12 h-12 text-gray-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1275,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1274,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"course-list-empty-title\",\n                                                children: \"暂无课时\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1277,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"course-list-empty-description\",\n                                                children: \"点击右上角的 + 按钮添加第一个课时\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1278,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: addNewCourse,\n                                                className: \"course-list-empty-btn\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1285,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    \"添加课时\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1281,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 1273,\n                                        columnNumber: 17\n                                    }, undefined) : courseList.map((course)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"course-list-item \".concat(selectedCourseId === course.id ? \"active\" : \"\"),\n                                            onClick: ()=>showCoursePanel(course.id),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-list-item-content\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"course-list-item-text\",\n                                                            children: course.title\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1297,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"course-status-badge \".concat(course.status === 1 ? \"published\" : \"draft\"),\n                                                            children: course.status === 1 ? \"已发布\" : \"未发布\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1298,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1296,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: (e)=>{\n                                                        e.stopPropagation();\n                                                        showDeleteConfirm(course.id);\n                                                    },\n                                                    className: \"course-list-item-delete\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"w-3 h-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1309,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1302,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, course.id, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 1291,\n                                            columnNumber: 19\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 1267,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                lineNumber: 1266,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"course-list-edit-area\",\n                                children: [\n                                    rightPanelType === \"none\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"course-edit-empty\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"course-edit-empty-icon\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"w-16 h-16 text-gray-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1322,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1321,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"course-edit-empty-title\",\n                                                children: \"无课程详情\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1324,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"course-edit-empty-description\",\n                                                children: \"点击左侧课程或设置按钮查看详情\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1325,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 1320,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    rightPanelType === \"settings\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"course-series-cover\",\n                                                children: seriesCoverImage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: seriesCoverImage,\n                                                    alt: \"系列课程封面\",\n                                                    className: \"course-series-cover-image\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1336,\n                                                    columnNumber: 21\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-series-cover-placeholder\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"系列课程封面\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1343,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1342,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1334,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"course-edit-form\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"course-edit-field\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"course-edit-label\",\n                                                                children: \"系列课程标题\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1352,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                value: editingTitle,\n                                                                onChange: (e)=>setEditingTitle(e.target.value),\n                                                                className: \"course-edit-input\",\n                                                                placeholder: \"请输入系列课程标题\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1353,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1351,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"course-edit-field\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"course-edit-label\",\n                                                                children: \"课程标签\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1364,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Select_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                mode: \"multiple\",\n                                                                style: {\n                                                                    width: \"100%\"\n                                                                },\n                                                                placeholder: \"请选择课程标签\",\n                                                                value: selectedTags,\n                                                                onChange: setSelectedTags,\n                                                                loading: tagsLoading,\n                                                                options: courseTags.map((tag)=>{\n                                                                    console.log(\"\\uD83C\\uDFF7️ 渲染标签选项:\", tag);\n                                                                    return {\n                                                                        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            style: {\n                                                                                color: tag.color\n                                                                            },\n                                                                            children: tag.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1376,\n                                                                            columnNumber: 29\n                                                                        }, void 0),\n                                                                        value: tag.id\n                                                                    };\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1365,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    fontSize: \"12px\",\n                                                                    color: \"#666\",\n                                                                    marginTop: \"4px\"\n                                                                },\n                                                                children: [\n                                                                    \"调试: 当前标签数量 \",\n                                                                    courseTags.length,\n                                                                    \", 加载状态: \",\n                                                                    tagsLoading ? \"是\" : \"否\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1385,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1363,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"course-edit-field\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"course-edit-label\",\n                                                                children: \"课程项目成员\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1392,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                value: projectMembers,\n                                                                onChange: (e)=>setProjectMembers(e.target.value),\n                                                                className: \"course-edit-input\",\n                                                                placeholder: \"请输入项目成员，如：张老师、李助教、王同学\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1393,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1391,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1349,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true),\n                                    rightPanelType === \"course\" && getSelectedCourse() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"course-detail-edit\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-detail-top\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"course-detail-cover\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"course-cover-upload-area\",\n                                                                    onClick: ()=>{\n                                                                        var _document_getElementById;\n                                                                        return (_document_getElementById = document.getElementById(\"cover-upload-input\")) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.click();\n                                                                    },\n                                                                    children: courseDetail.coverImage || ((_getSelectedCourse = getSelectedCourse()) === null || _getSelectedCourse === void 0 ? void 0 : _getSelectedCourse.coverImage) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                        src: courseDetail.coverImage || ((_getSelectedCourse1 = getSelectedCourse()) === null || _getSelectedCourse1 === void 0 ? void 0 : _getSelectedCourse1.coverImage),\n                                                                        alt: \"课程封面\",\n                                                                        className: \"course-cover-image\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1417,\n                                                                        columnNumber: 27\n                                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"course-cover-placeholder\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"点击上传课程封面\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1424,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1423,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1412,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    id: \"cover-upload-input\",\n                                                                    type: \"file\",\n                                                                    accept: \"image/jpeg,image/jpg,image/png,image/gif\",\n                                                                    onChange: handleCoverUpload,\n                                                                    style: {\n                                                                        display: \"none\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1428,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"one-click-class-btn\",\n                                                                    onClick: ()=>{\n                                                                        // TODO: 实现一键上课功能\n                                                                        console.log(\"一键上课按钮被点击\");\n                                                                        notification.info(\"一键上课功能开发中...\");\n                                                                    },\n                                                                    children: \"一键上课\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1436,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1411,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"course-detail-basic\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"course-detail-field\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            children: \"课程标题\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1449,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            value: courseDetail.title || ((_getSelectedCourse2 = getSelectedCourse()) === null || _getSelectedCourse2 === void 0 ? void 0 : _getSelectedCourse2.title) || \"\",\n                                                                            onChange: (e)=>{\n                                                                                setCourseDetail((prev)=>({\n                                                                                        ...prev,\n                                                                                        title: e.target.value\n                                                                                    }));\n                                                                                updateCourseTitle(selectedCourseId, e.target.value);\n                                                                            },\n                                                                            placeholder: \"请输入课程标题\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1450,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1448,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"course-detail-field\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            children: \"课程介绍\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1461,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                            value: courseDetail.description || ((_getSelectedCourse3 = getSelectedCourse()) === null || _getSelectedCourse3 === void 0 ? void 0 : _getSelectedCourse3.description) || \"\",\n                                                                            onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                        ...prev,\n                                                                                        description: e.target.value\n                                                                                    })),\n                                                                            placeholder: \"请输入课程介绍\",\n                                                                            rows: 3\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1462,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1460,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1447,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1410,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-detail-section\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            children: \"课程资源\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1474,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"course-resource-item\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"resource-header-right\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"课程视频\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1479,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"switch\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: \"checkbox\",\n                                                                                    checked: courseDetail.isVideoEnabled,\n                                                                                    onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                ...prev,\n                                                                                                isVideoEnabled: e.target.checked\n                                                                                            }))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1481,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"slider\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1486,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1480,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1478,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                courseDetail.isVideoEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"video-content-area\",\n                                                                    children: ((_courseDetail_contentConfig = courseDetail.contentConfig) === null || _courseDetail_contentConfig === void 0 ? void 0 : (_courseDetail_contentConfig_video = _courseDetail_contentConfig.video) === null || _courseDetail_contentConfig_video === void 0 ? void 0 : _courseDetail_contentConfig_video.url) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"video-info-section\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"video-preview\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                                                                    className: \"video-thumbnail\",\n                                                                                    controls: true,\n                                                                                    poster: courseDetail.coverImage,\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                                                                                            src: courseDetail.contentConfig.video.url,\n                                                                                            type: \"video/mp4\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1500,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined),\n                                                                                        \"您的浏览器不支持视频播放\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1495,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1494,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"video-name-centered\",\n                                                                                children: courseDetail.contentConfig.video.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1504,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"upload-btn-horizontal\",\n                                                                                onClick: triggerVideoUpload,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"重新上传\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1506,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1505,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1493,\n                                                                        columnNumber: 29\n                                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"video-upload-section\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"video-placeholder-centered\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"play-icon\",\n                                                                                    children: \"▶\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1512,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1511,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"upload-btn-horizontal\",\n                                                                                onClick: triggerVideoUpload,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"上传视频\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1515,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1514,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1510,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1490,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1477,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"course-resource-item\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"resource-header-right\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"课程附件\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1526,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"switch\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: \"checkbox\",\n                                                                                    checked: courseDetail.isAttachmentEnabled,\n                                                                                    onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                ...prev,\n                                                                                                isAttachmentEnabled: e.target.checked\n                                                                                            }))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1528,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"slider\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1533,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1527,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1525,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                courseDetail.isAttachmentEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"attachment-content-area\",\n                                                                    children: ((_courseDetail_contentConfig1 = courseDetail.contentConfig) === null || _courseDetail_contentConfig1 === void 0 ? void 0 : (_courseDetail_contentConfig_document = _courseDetail_contentConfig1.document) === null || _courseDetail_contentConfig_document === void 0 ? void 0 : _courseDetail_contentConfig_document.url) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"attachment-info-section\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"attachment-preview\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"document-icon\",\n                                                                                        children: \"\\uD83D\\uDCC4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1542,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"attachment-details\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"attachment-name\",\n                                                                                            children: courseDetail.contentConfig.document.name\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1544,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1543,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1541,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"upload-btn-horizontal\",\n                                                                                onClick: triggerAttachmentUpload,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"重新上传\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1548,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1547,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1540,\n                                                                        columnNumber: 29\n                                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"attachment-upload-section\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            className: \"upload-btn-horizontal\",\n                                                                            onClick: triggerAttachmentUpload,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"上传附件\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1554,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1553,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1552,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1537,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1524,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"course-resource-item\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"resource-header-simple\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"教学附件\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1565,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1564,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"teaching-materials\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            className: \"add-material-btn\",\n                                                                            onClick: triggerTeachingMaterialUpload,\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"+\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1569,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"上传\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1570,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1568,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        courseDetail.teachingMaterials && courseDetail.teachingMaterials.length > 0 ? courseDetail.teachingMaterials.map((material, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"material-item\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"material-name\",\n                                                                                        onClick: ()=>{\n                                                                                            if (material.url) {\n                                                                                                window.open(material.url, \"_blank\");\n                                                                                            }\n                                                                                        },\n                                                                                        style: {\n                                                                                            cursor: material.url ? \"pointer\" : \"default\",\n                                                                                            color: material.url ? \"#1890ff\" : \"inherit\",\n                                                                                            textDecoration: material.url ? \"underline\" : \"none\"\n                                                                                        },\n                                                                                        title: material.url ? \"点击下载附件\" : material.name,\n                                                                                        children: [\n                                                                                            \"\\uD83D\\uDCCE \",\n                                                                                            material.name\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1575,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        className: \"remove-material-btn\",\n                                                                                        onClick: ()=>removeTeachingMaterial(index),\n                                                                                        title: \"删除附件\",\n                                                                                        children: \"\\xd7\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1591,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, index, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1574,\n                                                                                columnNumber: 29\n                                                                            }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"empty-materials-hint\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                style: {\n                                                                                    color: \"#999\",\n                                                                                    fontSize: \"14px\"\n                                                                                },\n                                                                                children: \"暂无教学附件\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1602,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1601,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1567,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1563,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1473,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-detail-section\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"section-header\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    children: \"课程内容\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1612,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"add-content-section-btn\",\n                                                                    onClick: addTeachingInfoItem,\n                                                                    title: \"添加课程内容\",\n                                                                    children: \"+ 添加课程内容\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1613,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1611,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"course-content-area\",\n                                                            children: courseDetail.teachingInfo && courseDetail.teachingInfo.length > 0 ? courseDetail.teachingInfo.map((info, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"teaching-info-card\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"card-header\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"card-title\",\n                                                                                    children: [\n                                                                                        \"课程内容 \",\n                                                                                        index + 1\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1626,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    className: \"remove-card-btn\",\n                                                                                    onClick: ()=>removeTeachingInfoItem(index),\n                                                                                    title: \"删除此内容\",\n                                                                                    children: \"\\xd7\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1627,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1625,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"card-content\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"input-group\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                            children: \"标题\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1637,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                            type: \"text\",\n                                                                                            value: info.title,\n                                                                                            onChange: (e)=>updateTeachingInfoTitle(index, e.target.value),\n                                                                                            placeholder: \"请输入标题，如：教学目标、教学方法等\",\n                                                                                            className: \"title-input\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1638,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1636,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"input-group\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                            children: \"内容\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1647,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                                            value: info.content,\n                                                                                            onChange: (e)=>updateTeachingInfoContent(index, e.target.value),\n                                                                                            placeholder: \"请输入具体内容，多个内容项可用换行分隔\",\n                                                                                            className: \"content-textarea\",\n                                                                                            rows: 4\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1648,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1646,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1635,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, index, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1624,\n                                                                    columnNumber: 27\n                                                                }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"empty-content-hint\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: \"暂无课程内容，点击右上角按钮添加\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1661,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1660,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1621,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1610,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-detail-section\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"one-key-section\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"one-key-item\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"重新上课\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1671,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"switch\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"checkbox\",\n                                                                                checked: courseDetail.isOneKeyOpen,\n                                                                                onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                            ...prev,\n                                                                                            isOneKeyOpen: e.target.checked\n                                                                                        }))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1673,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"slider\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1678,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1672,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1670,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            courseDetail.isOneKeyOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"one-key-item\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"分配积木\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1685,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"switch\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                        type: \"checkbox\",\n                                                                                        checked: courseDetail.isDistributionEnabled,\n                                                                                        onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    isDistributionEnabled: e.target.checked\n                                                                                                }))\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1687,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"slider\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1692,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1686,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            courseDetail.isDistributionEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"block-template-section\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        className: \"select-template-btn\",\n                                                                                        children: \"选择积木模板\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1696,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"selected-template-display\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            children: courseDetail.selectedTemplate || \"选中的模板名字\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1700,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1699,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1695,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1684,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"one-key-item\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"分配能量\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1707,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"switch\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                        type: \"checkbox\",\n                                                                                        checked: courseDetail.isDistributionWater,\n                                                                                        onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    isDistributionWater: e.target.checked\n                                                                                                }))\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1709,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"slider\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1714,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1708,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1706,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    courseDetail.isDistributionWater && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"energy-input-section\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"需要能量：\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1720,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"text\",\n                                                                                value: courseDetail.requiredEnergy || \"\",\n                                                                                onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                            ...prev,\n                                                                                            requiredEnergy: e.target.value\n                                                                                        })),\n                                                                                placeholder: \"请输入需要的能量值\",\n                                                                                className: \"energy-input\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1721,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1719,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"one-key-item\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"分配任务\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1732,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"switch\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                        type: \"checkbox\",\n                                                                                        checked: courseDetail.isDistributionLimit,\n                                                                                        onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    isDistributionLimit: e.target.checked\n                                                                                                }))\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1734,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"slider\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1739,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1733,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1731,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    courseDetail.isDistributionLimit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"task-config-form\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"task-config-row\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"task-config-field\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                                children: \"任务名称:\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                lineNumber: 1749,\n                                                                                                columnNumber: 35\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                type: \"text\",\n                                                                                                value: courseDetail.taskConfig.taskName,\n                                                                                                onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                            ...prev,\n                                                                                                            taskConfig: {\n                                                                                                                ...prev.taskConfig,\n                                                                                                                taskName: e.target.value\n                                                                                                            }\n                                                                                                        })),\n                                                                                                placeholder: \"请输入任务名称\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                lineNumber: 1750,\n                                                                                                columnNumber: 35\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1748,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"task-config-field\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                                children: \"任务持续天数:\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                lineNumber: 1761,\n                                                                                                columnNumber: 35\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                type: \"number\",\n                                                                                                value: courseDetail.taskConfig.taskDuration,\n                                                                                                onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                            ...prev,\n                                                                                                            taskConfig: {\n                                                                                                                ...prev.taskConfig,\n                                                                                                                taskDuration: e.target.value\n                                                                                                            }\n                                                                                                        })),\n                                                                                                placeholder: \"请输入天数\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                lineNumber: 1762,\n                                                                                                columnNumber: 35\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1760,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1747,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"task-config-field task-config-full\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        children: \"任务描述:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1776,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                                        value: courseDetail.taskConfig.taskDescription,\n                                                                                        onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    taskConfig: {\n                                                                                                        ...prev.taskConfig,\n                                                                                                        taskDescription: e.target.value\n                                                                                                    }\n                                                                                                })),\n                                                                                        placeholder: \"请输入任务描述\",\n                                                                                        rows: 4\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1777,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1775,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"task-config-field task-config-full\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        children: [\n                                                                                            \"任务自评项: \",\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"item-number\",\n                                                                                                children: courseDetail.taskConfig.selfAssessmentItems.length\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                lineNumber: 1790,\n                                                                                                columnNumber: 47\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1790,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    courseDetail.taskConfig.selfAssessmentItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"self-assessment-item\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                type: \"text\",\n                                                                                                value: item,\n                                                                                                onChange: (e)=>{\n                                                                                                    const newItems = [\n                                                                                                        ...courseDetail.taskConfig.selfAssessmentItems\n                                                                                                    ];\n                                                                                                    newItems[index] = e.target.value;\n                                                                                                    setCourseDetail((prev)=>({\n                                                                                                            ...prev,\n                                                                                                            taskConfig: {\n                                                                                                                ...prev.taskConfig,\n                                                                                                                selfAssessmentItems: newItems\n                                                                                                            }\n                                                                                                        }));\n                                                                                                },\n                                                                                                placeholder: \"请输入自评项内容\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                lineNumber: 1793,\n                                                                                                columnNumber: 37\n                                                                                            }, undefined)\n                                                                                        }, index, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1792,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        type: \"button\",\n                                                                                        className: \"add-assessment-btn\",\n                                                                                        onClick: ()=>setCourseDetail((prev)=>({\n                                                                                                    ...prev,\n                                                                                                    taskConfig: {\n                                                                                                        ...prev.taskConfig,\n                                                                                                        selfAssessmentItems: [\n                                                                                                            ...prev.taskConfig.selfAssessmentItems,\n                                                                                                            \"\"\n                                                                                                        ]\n                                                                                                    }\n                                                                                                })),\n                                                                                        children: \"+\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1808,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1789,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"task-config-field task-config-full\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        children: \"任务参考作品:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1825,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"reference-works-section\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                type: \"button\",\n                                                                                                className: \"select-works-btn\",\n                                                                                                children: \"选择作品\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                lineNumber: 1827,\n                                                                                                columnNumber: 35\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"reference-works-grid\",\n                                                                                                children: [\n                                                                                                    courseDetail.taskConfig.referenceWorks.map((work, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                            className: \"reference-work-item\",\n                                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                                children: work.name || \"作品\"\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                lineNumber: 1831,\n                                                                                                                columnNumber: 41\n                                                                                                            }, undefined)\n                                                                                                        }, index, false, {\n                                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                            lineNumber: 1830,\n                                                                                                            columnNumber: 39\n                                                                                                        }, undefined)),\n                                                                                                    Array.from({\n                                                                                                        length: Math.max(0, 3 - courseDetail.taskConfig.referenceWorks.length)\n                                                                                                    }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                            className: \"reference-work-item empty\"\n                                                                                                        }, \"empty-\".concat(index), false, {\n                                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                            lineNumber: 1836,\n                                                                                                            columnNumber: 39\n                                                                                                        }, undefined))\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                lineNumber: 1828,\n                                                                                                columnNumber: 35\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1826,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1824,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"task-config-field task-config-full\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        children: \"任务参考资源:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1844,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"reference-resources-section\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"reference-resources-grid\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                    type: \"button\",\n                                                                                                    className: \"upload-resource-btn\",\n                                                                                                    onClick: ()=>{\n                                                                                                        // 触发文件上传\n                                                                                                        const input = document.createElement(\"input\");\n                                                                                                        input.type = \"file\";\n                                                                                                        input.accept = \".pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx,.txt,.jpg,.png,.gif\";\n                                                                                                        input.onchange = (e)=>{\n                                                                                                            var _e_target_files;\n                                                                                                            const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n                                                                                                            if (file) {\n                                                                                                                setCourseDetail((prev)=>({\n                                                                                                                        ...prev,\n                                                                                                                        taskConfig: {\n                                                                                                                            ...prev.taskConfig,\n                                                                                                                            referenceResources: [\n                                                                                                                                ...prev.taskConfig.referenceResources,\n                                                                                                                                {\n                                                                                                                                    type: \"file\",\n                                                                                                                                    name: file.name\n                                                                                                                                }\n                                                                                                                            ]\n                                                                                                                        }\n                                                                                                                    }));\n                                                                                                            }\n                                                                                                        };\n                                                                                                        input.click();\n                                                                                                    },\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                                                            size: 24\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                            lineNumber: 1873,\n                                                                                                            columnNumber: 39\n                                                                                                        }, undefined),\n                                                                                                        \"上传\"\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                    lineNumber: 1847,\n                                                                                                    columnNumber: 37\n                                                                                                }, undefined),\n                                                                                                courseDetail.taskConfig.referenceResources.map((resource, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"reference-resource-item\",\n                                                                                                        children: [\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                                children: resource.name\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                lineNumber: 1878,\n                                                                                                                columnNumber: 41\n                                                                                                            }, undefined),\n                                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                                type: \"button\",\n                                                                                                                className: \"remove-resource-btn\",\n                                                                                                                onClick: ()=>{\n                                                                                                                    const newResources = courseDetail.taskConfig.referenceResources.filter((_, i)=>i !== index);\n                                                                                                                    setCourseDetail((prev)=>({\n                                                                                                                            ...prev,\n                                                                                                                            taskConfig: {\n                                                                                                                                ...prev.taskConfig,\n                                                                                                                                referenceResources: newResources\n                                                                                                                            }\n                                                                                                                        }));\n                                                                                                                },\n                                                                                                                children: \"\\xd7\"\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                                lineNumber: 1879,\n                                                                                                                columnNumber: 41\n                                                                                                            }, undefined)\n                                                                                                        ]\n                                                                                                    }, index, true, {\n                                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                        lineNumber: 1877,\n                                                                                                        columnNumber: 39\n                                                                                                    }, undefined))\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1846,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1845,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1843,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1745,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1669,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1668,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 1408,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                lineNumber: 1318,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                        lineNumber: 1264,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"course-list-footer\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"course-list-footer-left\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handlePublish,\n                                    className: \"course-list-btn course-list-btn-publish\",\n                                    disabled: courseList.length === 0 || isPublishingSeries || seriesStatus === 1,\n                                    title: seriesStatus === 1 ? \"系列课程已发布\" : courseList.length === 0 ? \"发布失败：课程系列中至少需要包含一个课程\" : isPublishingSeries ? \"正在发布系列课程...\" : \"发布系列课程\",\n                                    children: seriesStatus === 1 ? \"已发布\" : isPublishingSeries ? \"正在发布...\" : \"发布系列课程\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 1912,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                lineNumber: 1911,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"course-list-footer-right\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleExitEdit,\n                                        className: \"course-list-btn course-list-btn-exit\",\n                                        children: \"退出编辑模式\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 1935,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handlePublishCourse,\n                                        className: \"course-list-btn course-list-btn-publish-course\",\n                                        disabled: !selectedCourseId || ((_courseList_find = courseList.find((c)=>c.id === selectedCourseId)) === null || _courseList_find === void 0 ? void 0 : _courseList_find.status) === 1 || isPublishing,\n                                        title: !selectedCourseId ? \"请先选择要发布的课程\" : ((_courseList_find1 = courseList.find((c)=>c.id === selectedCourseId)) === null || _courseList_find1 === void 0 ? void 0 : _courseList_find1.status) === 1 ? \"该课程已发布\" : isPublishing ? \"正在发布课程...\" : \"发布选中的课程\",\n                                        children: isPublishing ? \"正在发布...\" : \"发布课程\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 1938,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleSave,\n                                        className: \"course-list-btn course-list-btn-save\",\n                                        disabled: uploadingFiles.size > 0 || isCreating || courseList.length === 0,\n                                        title: courseList.length === 0 ? \"请先添加课程内容\" : uploadingFiles.size > 0 ? \"有文件正在上传中，请等待上传完成\" : isCreating ? courseList.some((c)=>c.id > 1000000) ? \"正在创建课程...\" : \"正在保存课程...\" : courseList.some((c)=>c.id > 1000000) ? \"创建课程\" : \"保存课程\",\n                                        children: uploadingFiles.size > 0 ? \"上传中...\" : isCreating ? courseList.some((c)=>c.id > 1000000) ? \"正在创建...\" : \"正在保存...\" : courseList.some((c)=>c.id > 1000000) ? \"创建课程\" : \"保存课程\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 1954,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                lineNumber: 1934,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                        lineNumber: 1910,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                lineNumber: 1241,\n                columnNumber: 7\n            }, undefined),\n            deleteConfirmVisible && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"modal-overlay\",\n                onClick: cancelDelete,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"delete-confirm-modal\",\n                    onClick: (e)=>e.stopPropagation(),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"delete-confirm-header\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    children: \"确认删除\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 1984,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: cancelDelete,\n                                    className: \"close-btn\",\n                                    disabled: isDeleting,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 1990,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 1985,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                            lineNumber: 1983,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"delete-confirm-content\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: isDeleting ? \"正在删除课程，请稍候...\" : \"确定要删除这个课程吗？删除后无法恢复。\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 1994,\n                                    columnNumber: 15\n                                }, undefined),\n                                isDeleting && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"delete-loading\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"loading-spinner\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 2002,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 2001,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                            lineNumber: 1993,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"delete-confirm-footer\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: cancelDelete,\n                                    className: \"cancel-btn\",\n                                    disabled: isDeleting,\n                                    children: \"取消\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 2007,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: confirmDeleteCourse,\n                                    className: \"confirm-btn\",\n                                    disabled: isDeleting,\n                                    children: isDeleting ? \"正在删除...\" : \"确认删除\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 2014,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                            lineNumber: 2006,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                    lineNumber: 1982,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                lineNumber: 1981,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n        lineNumber: 1240,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CourseListEditModal, \"L0tFYCIcSAxHLrtHgY5Dz8pHNyM=\");\n_c = CourseListEditModal;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CourseListEditModal);\nvar _c;\n$RefreshReg$(_c, \"CourseListEditModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/workbench/components/CourseListEditModal.tsx\n"));

/***/ })

});