"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/workbench/page",{

/***/ "(app-pages-browser)/./app/workbench/components/CourseListEditModal.tsx":
/*!**********************************************************!*\
  !*** ./app/workbench/components/CourseListEditModal.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FileText,Plus,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FileText,Plus,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FileText,Plus,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FileText,Plus,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FileText,Plus,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _lib_api_course__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api/course */ \"(app-pages-browser)/./lib/api/course.ts\");\n/* harmony import */ var _lib_api_upload__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api/upload */ \"(app-pages-browser)/./lib/api/upload.ts\");\n/* harmony import */ var _barrel_optimize_names_Select_antd__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Select!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/select/index.js\");\n/* harmony import */ var _CourseListEditModal_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./CourseListEditModal.css */ \"(app-pages-browser)/./app/workbench/components/CourseListEditModal.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// 获取系列课程详情\nconst fetchSeriesDetail = async (seriesId)=>{\n    console.log(\"\\uD83D\\uDD0D 获取系列课程详情，seriesId:\", seriesId);\n    console.log(\"\\uD83D\\uDD17 调用API: GET /api/v1/course-management/series/{seriesId}\");\n    const response = await _lib_api_course__WEBPACK_IMPORTED_MODULE_2__.courseApi.getSeriesDetail(seriesId);\n    console.log(\"\\uD83D\\uDCE1 系列详情API响应:\", response);\n    return response.data;\n};\n// API调用函数 - 获取系列下的所有课程（包含未发布和已发布）\nconst fetchCourseList = async (seriesId)=>{\n    console.log(\"\\uD83D\\uDD0D 获取系列课程列表，seriesId:\", seriesId);\n    console.log(\"\\uD83D\\uDD17 调用API: GET /api/v1/course-management/series/{seriesId}/courses\");\n    // 使用课程管理API获取所有状态的课程\n    const response = await _lib_api_course__WEBPACK_IMPORTED_MODULE_2__.courseApi.getManagementSeriesCourses(seriesId, {\n        page: 1,\n        pageSize: 100\n    });\n    console.log(\"\\uD83D\\uDCE1 API响应:\", response);\n    return response.data;\n};\n// 获取课程详情\nconst fetchCourseDetail = async (seriesId, courseId)=>{\n    return await _lib_api_course__WEBPACK_IMPORTED_MODULE_2__.courseApi.getCourseMarketplaceDetail(seriesId, courseId);\n};\n// 获取课程标签\nconst fetchCourseTags = async ()=>{\n    try {\n        console.log(\"\\uD83D\\uDD0D 开始调用 courseApi.getCourseTags\");\n        const result = await _lib_api_course__WEBPACK_IMPORTED_MODULE_2__.courseApi.getCourseTags({\n            page: 1,\n            pageSize: 100,\n            status: 1 // 只获取启用的标签\n        });\n        console.log(\"\\uD83D\\uDD0D courseApi.getCourseTags 返回结果:\", result);\n        return result;\n    } catch (error) {\n        console.error(\"\\uD83D\\uDD0D courseApi.getCourseTags 调用失败:\", error);\n        throw error;\n    }\n};\nconst CourseListEditModal = (param)=>{\n    let { isVisible, onClose, onSave, seriesTitle, seriesCoverImage, seriesId = 123 // 默认值，实际使用时应该传入真实的seriesId\n     } = param;\n    var _getSelectedCourse, _getSelectedCourse1, _getSelectedCourse2, _getSelectedCourse3, _courseDetail_contentConfig_video, _courseDetail_contentConfig, _courseDetail_contentConfig_document, _courseDetail_contentConfig1, _courseList_find, _courseList_find1;\n    _s();\n    const [courseList, setCourseList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [rightPanelType, setRightPanelType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"none\");\n    const [selectedCourseId, setSelectedCourseId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [uploadingFiles, setUploadingFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [isPublishing, setIsPublishing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isCreating, setIsCreating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPublishingSeries, setIsPublishingSeries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [seriesStatus, setSeriesStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0); // 0=草稿，1=已发布，2=已归档\n    const [editingTitle, setEditingTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(seriesTitle);\n    const [courseGoals, setCourseGoals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [courseObjectives, setCourseObjectives] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [projectMembers, setProjectMembers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // 课程标签相关状态\n    const [courseTags, setCourseTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedTags, setSelectedTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [tagsLoading, setTagsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 课程详细编辑状态\n    const [courseDetail, setCourseDetail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: \"\",\n        description: \"\",\n        coverImage: \"\",\n        videoUrl: \"\",\n        videoName: \"\",\n        isVideoEnabled: false,\n        attachmentUrl: \"\",\n        attachmentName: \"\",\n        isAttachmentEnabled: false,\n        teachingMaterials: [],\n        // 支持teachingInfo结构\n        teachingInfo: [],\n        // 支持contentConfig结构\n        contentConfig: {\n            hasVideo: 0,\n            hasDocument: 0,\n            hasAudio: 0,\n            video: {\n                url: \"\",\n                name: \"\"\n            },\n            document: {\n                url: \"\",\n                name: \"\"\n            },\n            audio: {\n                url: \"\",\n                name: \"\"\n            }\n        },\n        courseContent: {\n            topic: \"\",\n            content: \"\"\n        },\n        isOneKeyOpen: false,\n        isDistributionEnabled: false,\n        distributionReward: \"\",\n        selectedTemplate: \"\",\n        isDistributionWater: false,\n        requiredEnergy: \"\",\n        energyAmount: \"\",\n        isDistributionLimit: false,\n        distributionConditions: {\n            inviteCount: \"\",\n            taskCount: \"\",\n            experience: \"\"\n        },\n        isDistributionTime: false,\n        distributionTimeConditions: {\n            startTime: \"\",\n            endTime: \"\"\n        },\n        distributionMaterials: [],\n        // 任务配置相关状态\n        taskConfig: {\n            taskName: \"\",\n            taskDuration: \"\",\n            taskDescription: \"\",\n            selfAssessmentItems: [\n                \"\"\n            ],\n            referenceWorks: [],\n            referenceResources: []\n        }\n    });\n    // 获取课程列表数据\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isVisible && seriesId) {\n            // 检查用户登录状态\n            const token = localStorage.getItem(\"token\");\n            console.log(\"\\uD83D\\uDD10 检查登录状态，token存在:\", !!token);\n            console.log(\"\\uD83D\\uDD0D seriesId:\", seriesId);\n            if (!token) {\n                console.error(\"❌ 用户未登录，无法获取课程列表\");\n                // 设置空列表，显示空状态\n                setCourseList([]);\n                setLoading(false);\n                return;\n            }\n            loadCourseList();\n            loadCourseTags();\n            loadSeriesDetail();\n        }\n    }, [\n        isVisible,\n        seriesId\n    ]);\n    const loadCourseList = async ()=>{\n        try {\n            setLoading(true);\n            console.log(\"\\uD83D\\uDD0D 开始加载课程列表，seriesId:\", seriesId);\n            const response = await fetchCourseList(seriesId);\n            console.log(\"\\uD83D\\uDCE1 API响应:\", response);\n            if (response.code === 200) {\n                console.log(\"✅ 课程列表数据:\", response.data);\n                const courses = response.data.list || [];\n                console.log(\"✅ 解析的课程数组:\", courses);\n                console.log(\"\\uD83D\\uDCCA 课程ID详情:\", courses.map((c)=>({\n                        id: c.id,\n                        type: typeof c.id,\n                        title: c.title,\n                        status: c.status\n                    })));\n                console.log(\"\\uD83D\\uDCCA 课程状态统计:\", {\n                    total: courses.length,\n                    draft: courses.filter((c)=>c.status === 0).length,\n                    published: courses.filter((c)=>c.status === 1).length\n                });\n                setCourseList(courses);\n            } else {\n                console.error(\"❌ API返回错误:\", response);\n                setCourseList([]);\n            }\n        } catch (error) {\n            var _error_response, _error_response1, _error_response2;\n            console.error(\"❌ 加载课程列表失败:\", error);\n            // 检查是否是认证错误\n            if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 401) {\n                console.error(\"\\uD83D\\uDD10 认证失败，用户未登录或token已过期\");\n            } else if (((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status) === 403) {\n                console.error(\"\\uD83D\\uDEAB 权限不足，无法访问该系列课程\");\n            } else if (((_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.status) === 404) {\n                console.error(\"\\uD83D\\uDCED 系列课程不存在，seriesId:\", seriesId);\n            } else {\n                console.error(\"\\uD83D\\uDD27 其他错误:\", error.message);\n            }\n            setCourseList([]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 加载课程标签\n    const loadCourseTags = async ()=>{\n        try {\n            setTagsLoading(true);\n            console.log(\"\\uD83C\\uDFF7️ 开始加载课程标签\");\n            const response = await fetchCourseTags();\n            console.log(\"\\uD83D\\uDCE1 标签API完整响应:\", response);\n            // 检查响应结构\n            if (response && response.data) {\n                console.log(\"\\uD83D\\uDCCA 响应数据:\", response.data);\n                let tags = [];\n                // 处理标准的API响应格式 (response.data.list) - 根据实际API响应\n                if (response.data.list && Array.isArray(response.data.list)) {\n                    tags = response.data.list;\n                    console.log(\"✅ 从 data.list 解析到标签:\", tags.length, \"个\");\n                } else if (Array.isArray(response.data)) {\n                    tags = response.data;\n                    console.log(\"✅ 从 data 数组解析到标签:\", tags.length, \"个\");\n                } else if (response.data.data && response.data.data.list && Array.isArray(response.data.data.list)) {\n                    tags = response.data.data.list;\n                    console.log(\"✅ 从 data.data.list 解析到标签:\", tags.length, \"个\");\n                }\n                // 验证标签数据格式\n                console.log(\"\\uD83D\\uDD0D 原始标签数据:\", tags);\n                console.log(\"\\uD83D\\uDD0D 标签数据类型检查:\");\n                tags.forEach((tag, index)=>{\n                    var _tag_name;\n                    console.log(\"标签\".concat(index, \":\"), {\n                        tag,\n                        hasTag: !!tag,\n                        idType: typeof (tag === null || tag === void 0 ? void 0 : tag.id),\n                        nameType: typeof (tag === null || tag === void 0 ? void 0 : tag.name),\n                        nameValue: tag === null || tag === void 0 ? void 0 : tag.name,\n                        nameNotEmpty: (tag === null || tag === void 0 ? void 0 : (_tag_name = tag.name) === null || _tag_name === void 0 ? void 0 : _tag_name.trim()) !== \"\"\n                    });\n                });\n                const validTags = tags.filter((tag)=>{\n                    const isValid = tag && typeof tag.id === \"number\" && typeof tag.name === \"string\" && tag.name.trim() !== \"\";\n                    if (!isValid) {\n                        console.log(\"❌ 无效标签:\", tag, {\n                            hasTag: !!tag,\n                            idType: typeof (tag === null || tag === void 0 ? void 0 : tag.id),\n                            nameType: typeof (tag === null || tag === void 0 ? void 0 : tag.name),\n                            nameValue: tag === null || tag === void 0 ? void 0 : tag.name\n                        });\n                    }\n                    return isValid;\n                });\n                console.log(\"✅ 有效标签数量:\", validTags.length);\n                console.log(\"✅ 有效标签详情:\", validTags);\n                if (validTags.length > 0) {\n                    setCourseTags(validTags);\n                    console.log(\"✅ 成功设置真实标签数据\");\n                    return;\n                } else {\n                    console.warn(\"⚠️ 没有有效的标签数据\");\n                }\n            } else {\n                console.warn(\"⚠️ API响应格式不正确:\", response);\n            }\n            // 如果没有真实数据，设置空数组\n            console.log(\"\\uD83D\\uDCED 没有标签数据，设置空数组\");\n            setCourseTags([]);\n        } catch (error) {\n            var _error_response, _error_response1, _error_response2;\n            console.error(\"❌ 加载课程标签失败:\", error);\n            console.error(\"❌ 错误详情:\", {\n                message: error.message,\n                status: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status,\n                statusText: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.statusText,\n                data: (_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.data\n            });\n            // 发生错误时设置空数组\n            setCourseTags([]);\n        } finally{\n            setTagsLoading(false);\n        }\n    };\n    // 添加新课程\n    const addNewCourse = ()=>{\n        const newCourse = {\n            id: Date.now(),\n            seriesId: seriesId,\n            title: \"第\".concat(courseList.length + 1, \"课 - 新课时\"),\n            description: \"\",\n            coverImage: \"\",\n            orderIndex: courseList.length + 1,\n            status: 0,\n            statusLabel: \"草稿\",\n            hasVideo: 0,\n            hasDocument: 0,\n            hasAudio: 0,\n            videoDuration: 0,\n            videoDurationLabel: \"\",\n            videoName: \"\",\n            firstTeachingTitle: \"\",\n            resourcesCount: 0,\n            createdAt: new Date().toISOString(),\n            updatedAt: new Date().toISOString()\n        };\n        setCourseList([\n            ...courseList,\n            newCourse\n        ]);\n        // 自动选中新添加的课程\n        showCoursePanel(newCourse.id);\n    };\n    // 删除课程\n    const deleteCourse = (id)=>{\n        setCourseList(courseList.filter((course)=>course.id !== id));\n    };\n    // 更新课程标题\n    const updateCourseTitle = (id, newTitle)=>{\n        setCourseList(courseList.map((course)=>course.id === id ? {\n                ...course,\n                title: newTitle\n            } : course));\n    };\n    // 处理课程封面上传\n    const handleCoverUpload = async (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (file) {\n            // 检查文件类型\n            const allowedTypes = [\n                \"image/jpeg\",\n                \"image/jpg\",\n                \"image/png\",\n                \"image/gif\"\n            ];\n            if (!allowedTypes.includes(file.type)) {\n                alert(\"请选择 JPG、PNG 或 GIF 格式的图片文件\");\n                return;\n            }\n            // 检查文件大小 (10MB)\n            if (file.size > 10 * 1024 * 1024) {\n                alert(\"文件大小不能超过 10MB\");\n                return;\n            }\n            try {\n                console.log(\"\\uD83D\\uDCE4 开始上传课程封面:\", file.name);\n                // 添加到上传中的文件列表\n                setUploadingFiles((prev)=>new Set(prev).add(\"cover\"));\n                // 先显示预览图片\n                const previewUrl = URL.createObjectURL(file);\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        coverImage: previewUrl\n                    }));\n                // 上传到OSS\n                const imageUrl = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_3__.uploadApi.uploadToOss(file);\n                console.log(\"✅ 课程封面上传成功:\", imageUrl);\n                // 更新课程详情中的封面为真实URL\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        coverImage: imageUrl\n                    }));\n                // 同时更新课程列表中的封面\n                if (selectedCourseId) {\n                    setCourseList((prev)=>prev.map((course)=>course.id === selectedCourseId ? {\n                                ...course,\n                                coverImage: imageUrl\n                            } : course));\n                }\n                alert(\"课程封面上传成功！\");\n            } catch (error) {\n                console.error(\"❌ 课程封面上传失败:\", error);\n                alert(\"课程封面上传失败，请重试\");\n                // 上传失败时清除预览图片\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        coverImage: \"\"\n                    }));\n            } finally{\n                // 从上传中的文件列表移除\n                setUploadingFiles((prev)=>{\n                    const newSet = new Set(prev);\n                    newSet.delete(\"cover\");\n                    return newSet;\n                });\n            }\n        }\n    };\n    // 处理视频上传\n    const handleVideoUpload = async (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (file) {\n            // 检查文件类型\n            const allowedTypes = [\n                \"video/mp4\",\n                \"video/avi\",\n                \"video/mov\",\n                \"video/wmv\",\n                \"video/flv\"\n            ];\n            if (!allowedTypes.includes(file.type)) {\n                alert(\"请选择 MP4、AVI、MOV、WMV 或 FLV 格式的视频文件\");\n                return;\n            }\n            // 检查文件大小 (100MB)\n            if (file.size > 100 * 1024 * 1024) {\n                alert(\"视频文件大小不能超过 100MB\");\n                return;\n            }\n            try {\n                console.log(\"\\uD83D\\uDCE4 开始上传课程视频:\", file.name);\n                // 添加到上传中的文件列表\n                setUploadingFiles((prev)=>new Set(prev).add(\"video\"));\n                // 先显示预览视频\n                const previewUrl = URL.createObjectURL(file);\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        contentConfig: {\n                            ...prev.contentConfig,\n                            video: {\n                                url: previewUrl,\n                                name: file.name\n                            }\n                        }\n                    }));\n                // 上传到OSS\n                const videoUrl = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_3__.uploadApi.uploadToOss(file);\n                console.log(\"✅ 课程视频上传成功:\", videoUrl);\n                // 更新课程详情中的视频信息为真实URL\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        contentConfig: {\n                            ...prev.contentConfig,\n                            video: {\n                                url: videoUrl,\n                                name: file.name\n                            }\n                        }\n                    }));\n                // 同时更新课程列表中的视频信息\n                if (selectedCourseId) {\n                    setCourseList((prev)=>prev.map((course)=>course.id === selectedCourseId ? {\n                                ...course,\n                                contentConfig: {\n                                    ...course.contentConfig,\n                                    video: {\n                                        url: videoUrl,\n                                        name: file.name\n                                    }\n                                }\n                            } : course));\n                }\n                alert(\"课程视频上传成功！\");\n            } catch (error) {\n                console.error(\"❌ 课程视频上传失败:\", error);\n                alert(\"课程视频上传失败，请重试\");\n                // 上传失败时清除视频信息\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        contentConfig: {\n                            ...prev.contentConfig,\n                            video: {\n                                url: \"\",\n                                name: \"\"\n                            }\n                        }\n                    }));\n            } finally{\n                // 从上传中的文件列表移除\n                setUploadingFiles((prev)=>{\n                    const newSet = new Set(prev);\n                    newSet.delete(\"video\");\n                    return newSet;\n                });\n            }\n        }\n    };\n    // 触发视频文件选择\n    const triggerVideoUpload = ()=>{\n        const input = document.createElement(\"input\");\n        input.type = \"file\";\n        input.accept = \"video/mp4,video/avi,video/mov,video/wmv,video/flv\";\n        input.onchange = (e)=>handleVideoUpload(e);\n        input.click();\n    };\n    // 处理附件上传\n    const handleAttachmentUpload = async (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (file) {\n            // 检查文件类型\n            const allowedTypes = [\n                \"application/pdf\",\n                \"application/msword\",\n                \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\",\n                \"application/vnd.ms-excel\",\n                \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\",\n                \"application/vnd.ms-powerpoint\",\n                \"application/vnd.openxmlformats-officedocument.presentationml.presentation\",\n                \"text/plain\",\n                \"image/jpeg\",\n                \"image/png\",\n                \"image/gif\"\n            ];\n            if (!allowedTypes.includes(file.type)) {\n                alert(\"请选择支持的文件格式：PDF、DOC、DOCX、XLS、XLSX、PPT、PPTX、TXT、JPG、PNG、GIF\");\n                return;\n            }\n            // 检查文件大小 (10MB)\n            if (file.size > 10 * 1024 * 1024) {\n                alert(\"文件大小不能超过 10MB\");\n                return;\n            }\n            try {\n                console.log(\"\\uD83D\\uDCE4 开始上传课程附件:\", file.name);\n                // 添加到上传中的文件列表\n                setUploadingFiles((prev)=>new Set(prev).add(\"document\"));\n                // 上传到OSS\n                const documentUrl = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_3__.uploadApi.uploadToOss(file);\n                console.log(\"✅ 课程附件上传成功:\", documentUrl);\n                // 更新课程详情中的附件信息\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        contentConfig: {\n                            ...prev.contentConfig,\n                            hasDocument: 1,\n                            document: {\n                                url: documentUrl,\n                                name: file.name\n                            }\n                        }\n                    }));\n                alert(\"课程附件上传成功！\");\n            } catch (error) {\n                console.error(\"❌ 课程附件上传失败:\", error);\n                alert(\"课程附件上传失败，请重试\");\n            } finally{\n                // 从上传中的文件列表移除\n                setUploadingFiles((prev)=>{\n                    const newSet = new Set(prev);\n                    newSet.delete(\"document\");\n                    return newSet;\n                });\n            }\n        }\n    };\n    // 触发附件文件选择\n    const triggerAttachmentUpload = ()=>{\n        const input = document.createElement(\"input\");\n        input.type = \"file\";\n        input.accept = \".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.jpg,.png,.gif\";\n        input.onchange = (e)=>handleAttachmentUpload(e);\n        input.click();\n    };\n    // 处理教学附件上传\n    const handleTeachingMaterialUpload = async (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (file) {\n            // 检查文件类型\n            const allowedTypes = [\n                \"application/pdf\",\n                \"application/msword\",\n                \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\",\n                \"application/vnd.ms-excel\",\n                \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\",\n                \"application/vnd.ms-powerpoint\",\n                \"application/vnd.openxmlformats-officedocument.presentationml.presentation\",\n                \"text/plain\",\n                \"image/jpeg\",\n                \"image/png\",\n                \"image/gif\"\n            ];\n            if (!allowedTypes.includes(file.type)) {\n                alert(\"请选择支持的文件格式：PDF、DOC、DOCX、XLS、XLSX、PPT、PPTX、TXT、JPG、PNG、GIF\");\n                return;\n            }\n            // 检查文件大小 (10MB)\n            if (file.size > 10 * 1024 * 1024) {\n                alert(\"文件大小不能超过 10MB\");\n                return;\n            }\n            try {\n                console.log(\"\\uD83D\\uDCE4 开始上传教学材料:\", file.name);\n                // 添加到上传中的文件列表\n                setUploadingFiles((prev)=>new Set(prev).add(\"teaching-\".concat(Date.now())));\n                // 上传到OSS\n                const materialUrl = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_3__.uploadApi.uploadToOss(file);\n                console.log(\"✅ 教学材料上传成功:\", materialUrl);\n                // 添加到教学附件列表\n                const newMaterial = {\n                    type: file.type,\n                    name: file.name,\n                    url: materialUrl\n                };\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        teachingMaterials: [\n                            ...prev.teachingMaterials,\n                            newMaterial\n                        ]\n                    }));\n                alert(\"教学材料上传成功！\");\n            } catch (error) {\n                console.error(\"❌ 教学材料上传失败:\", error);\n                alert(\"教学材料上传失败，请重试\");\n            } finally{\n                // 从上传中的文件列表移除\n                setUploadingFiles((prev)=>{\n                    const newSet = new Set(prev);\n                    // 移除所有teaching-开头的项目\n                    Array.from(newSet).forEach((item)=>{\n                        if (item.startsWith(\"teaching-\")) {\n                            newSet.delete(item);\n                        }\n                    });\n                    return newSet;\n                });\n            }\n        }\n    };\n    // 触发教学附件文件选择\n    const triggerTeachingMaterialUpload = ()=>{\n        const input = document.createElement(\"input\");\n        input.type = \"file\";\n        input.accept = \".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.jpg,.png,.gif\";\n        input.onchange = (e)=>handleTeachingMaterialUpload(e);\n        input.click();\n    };\n    // 删除教学附件\n    const removeTeachingMaterial = (index)=>{\n        setCourseDetail((prev)=>({\n                ...prev,\n                teachingMaterials: prev.teachingMaterials.filter((_, i)=>i !== index)\n            }));\n    };\n    // 课程内容管理函数\n    const addTeachingInfoItem = ()=>{\n        setCourseDetail((prev)=>({\n                ...prev,\n                teachingInfo: [\n                    ...prev.teachingInfo,\n                    {\n                        title: \"\",\n                        content: \"\"\n                    }\n                ]\n            }));\n    };\n    const removeTeachingInfoItem = (index)=>{\n        setCourseDetail((prev)=>({\n                ...prev,\n                teachingInfo: prev.teachingInfo.filter((_, i)=>i !== index)\n            }));\n    };\n    const updateTeachingInfoTitle = (index, title)=>{\n        setCourseDetail((prev)=>{\n            const newTeachingInfo = [\n                ...prev.teachingInfo\n            ];\n            newTeachingInfo[index] = {\n                ...newTeachingInfo[index],\n                title\n            };\n            return {\n                ...prev,\n                teachingInfo: newTeachingInfo\n            };\n        });\n    };\n    const updateTeachingInfoContent = (index, content)=>{\n        setCourseDetail((prev)=>{\n            const newTeachingInfo = [\n                ...prev.teachingInfo\n            ];\n            newTeachingInfo[index] = {\n                ...newTeachingInfo[index],\n                content\n            };\n            return {\n                ...prev,\n                teachingInfo: newTeachingInfo\n            };\n        });\n    };\n    // 将UI格式的teachingInfo转换为API格式\n    const convertTeachingInfoForAPI = (teachingInfo)=>{\n        return teachingInfo.map((info)=>({\n                title: info.title,\n                content: info.content.split(\"\\n\").filter((line)=>line.trim()) // 按换行分割并过滤空行\n            }));\n    };\n    // 检查是否有未上传完成的文件（blob URL）\n    const checkForBlobUrls = (course)=>{\n        var _course_contentConfig_video, _course_contentConfig, _course_contentConfig_document, _course_contentConfig1;\n        const issues = [];\n        // 检查封面图片\n        if (course.coverImage && course.coverImage.startsWith(\"blob:\")) {\n            issues.push(\"课程封面图片\");\n        }\n        // 检查视频文件\n        if (((_course_contentConfig = course.contentConfig) === null || _course_contentConfig === void 0 ? void 0 : (_course_contentConfig_video = _course_contentConfig.video) === null || _course_contentConfig_video === void 0 ? void 0 : _course_contentConfig_video.url) && course.contentConfig.video.url.startsWith(\"blob:\")) {\n            issues.push(\"课程视频\");\n        }\n        // 检查文档附件\n        if (((_course_contentConfig1 = course.contentConfig) === null || _course_contentConfig1 === void 0 ? void 0 : (_course_contentConfig_document = _course_contentConfig1.document) === null || _course_contentConfig_document === void 0 ? void 0 : _course_contentConfig_document.url) && course.contentConfig.document.url.startsWith(\"blob:\")) {\n            issues.push(\"课程文档\");\n        }\n        // 检查教学材料\n        if (course.additionalResources) {\n            course.additionalResources.forEach((resource, index)=>{\n                if (resource.url && resource.url.startsWith(\"blob:\")) {\n                    issues.push(\"教学材料\".concat(index + 1));\n                }\n            });\n        }\n        return issues;\n    };\n    // 保存单个课程到后端\n    const saveCourse = async (course)=>{\n        try {\n            var _course_contentConfig_video, _course_contentConfig, _course_contentConfig_document, _course_contentConfig1;\n            // 验证必要的数据\n            if (!course.title || course.title.trim() === \"\") {\n                throw new Error(\"课程标题不能为空\");\n            }\n            // 检查是否有未上传完成的文件\n            const blobIssues = checkForBlobUrls(course);\n            if (blobIssues.length > 0) {\n                throw new Error(\"以下文件尚未上传完成，请等待上传完成后再保存：\".concat(blobIssues.join(\"、\")));\n            }\n            // 准备课程数据\n            const courseData = {\n                seriesId: seriesId,\n                title: course.title,\n                description: course.description || \"\",\n                coverImage: course.coverImage || \"\",\n                hasVideo: ((_course_contentConfig = course.contentConfig) === null || _course_contentConfig === void 0 ? void 0 : (_course_contentConfig_video = _course_contentConfig.video) === null || _course_contentConfig_video === void 0 ? void 0 : _course_contentConfig_video.url) ? 1 : 0,\n                hasDocument: ((_course_contentConfig1 = course.contentConfig) === null || _course_contentConfig1 === void 0 ? void 0 : (_course_contentConfig_document = _course_contentConfig1.document) === null || _course_contentConfig_document === void 0 ? void 0 : _course_contentConfig_document.url) ? 1 : 0,\n                hasAudio: 0,\n                videoDuration: 0,\n                contentConfig: course.contentConfig || {},\n                teachingInfo: convertTeachingInfoForAPI(course.teachingInfo || []),\n                additionalResources: course.additionalResources || [],\n                orderIndex: course.orderIndex || 1\n            };\n            console.log(\"\\uD83D\\uDCBE 准备保存课程数据:\", courseData);\n            let result;\n            // 判断是新课程还是更新课程\n            if (course.id && course.id > 1000000) {\n                // 新课程，使用创建API\n                console.log(\"\\uD83D\\uDCE4 创建新课程\");\n                const { data: response } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_2__.courseApi.createCourse(courseData);\n                result = response;\n                console.log(\"✅ 课程创建成功\");\n            } else {\n                // 现有课程，使用更新API\n                console.log(\"\\uD83D\\uDCE4 更新现有课程:\", course.id);\n                const { data: response } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_2__.courseApi.updateCourse(course.id, courseData);\n                result = response;\n                console.log(\"✅ 课程更新成功\");\n            }\n            return result;\n        } catch (error) {\n            console.error(\"❌ 保存课程失败:\", error);\n            throw error;\n        }\n    };\n    // 发布选中的课程\n    const handlePublishCourse = async ()=>{\n        if (!selectedCourseId) {\n            alert(\"请先选择要发布的课程\");\n            return;\n        }\n        const selectedCourse = courseList.find((course)=>course.id === selectedCourseId);\n        if (!selectedCourse) {\n            alert(\"未找到选中的课程\");\n            return;\n        }\n        // 检查课程是否已经发布\n        if (selectedCourse.status === 1) {\n            alert(\"该课程已经发布，无需重复发布\");\n            return;\n        }\n        try {\n            setIsPublishing(true);\n            console.log(\"\\uD83D\\uDCE4 开始发布课程\");\n            console.log(\"\\uD83D\\uDCE4 selectedCourseId:\", selectedCourseId, \"类型:\", typeof selectedCourseId);\n            console.log(\"\\uD83D\\uDCE4 课程信息:\", selectedCourse);\n            console.log(\"\\uD83D\\uDCE4 课程ID字段:\", selectedCourse.id, \"类型:\", typeof selectedCourse.id);\n            console.log(\"\\uD83D\\uDCE4 课程列表中的所有ID:\", courseList.map((c)=>({\n                    id: c.id,\n                    type: typeof c.id,\n                    title: c.title\n                })));\n            // 确保使用正确的课程ID\n            const courseIdToPublish = selectedCourse.id;\n            console.log(\"\\uD83D\\uDCE4 即将发布的课程ID:\", courseIdToPublish, \"类型:\", typeof courseIdToPublish);\n            const { data: response } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_2__.courseApi.publishCourse(courseIdToPublish);\n            if (response.code === 200) {\n                console.log(\"✅ 课程发布成功:\", response.data);\n                alert(\"课程发布成功！\");\n                // 刷新课程列表以更新状态\n                await loadCourseList();\n            } else {\n                console.error(\"❌ 发布课程失败:\", response.message);\n                alert(response.message || \"发布课程失败\");\n            }\n        } catch (error) {\n            var _error_response, _error_response_data, _error_response1;\n            console.error(\"❌ 发布课程失败:\", error);\n            console.error(\"❌ 错误详情:\", (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data);\n            // 处理具体的错误信息\n            if ((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : (_error_response_data = _error_response1.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) {\n                alert(error.response.data.message);\n            } else if (error.message) {\n                alert(error.message);\n            } else {\n                alert(\"发布课程失败，请重试\");\n            }\n        } finally{\n            setIsPublishing(false);\n        }\n    };\n    // 保存课程列表\n    const handleSave = async ()=>{\n        try {\n            setIsCreating(true);\n            // 检查是否有文件正在上传\n            if (uploadingFiles.size > 0) {\n                alert(\"有文件正在上传中，请等待上传完成后再保存\");\n                setIsCreating(false);\n                return;\n            }\n            console.log(\"\\uD83D\\uDCBE 开始保存课程列表\");\n            // 如果有选中的课程且在编辑状态，先保存当前课程\n            if (selectedCourseId && rightPanelType === \"course\") {\n                const selectedCourse = courseList.find((c)=>c.id === selectedCourseId);\n                if (selectedCourse) {\n                    var _courseDetail_teachingMaterials;\n                    // 更新课程数据\n                    const updatedCourse = {\n                        ...selectedCourse,\n                        title: courseDetail.title,\n                        description: courseDetail.description,\n                        coverImage: courseDetail.coverImage,\n                        hasVideo: courseDetail.isVideoEnabled ? 1 : 0,\n                        hasDocument: courseDetail.isAttachmentEnabled ? 1 : 0,\n                        contentConfig: courseDetail.contentConfig,\n                        teachingInfo: courseDetail.teachingInfo,\n                        additionalResources: ((_courseDetail_teachingMaterials = courseDetail.teachingMaterials) === null || _courseDetail_teachingMaterials === void 0 ? void 0 : _courseDetail_teachingMaterials.map((material)=>({\n                                title: material.name,\n                                url: material.url,\n                                description: material.name\n                            }))) || []\n                    };\n                    // 先更新课程列表中的数据\n                    setCourseList((prev)=>prev.map((course)=>course.id === selectedCourseId ? updatedCourse : course));\n                    // 然后保存到后端\n                    await saveCourse(updatedCourse);\n                }\n            }\n            // 保存系列课程信息\n            const data = {\n                title: editingTitle,\n                courseGoals,\n                courseObjectives,\n                courseList\n            };\n            onSave(data);\n            onClose();\n            console.log(\"✅ 课程列表保存完成\");\n        } catch (error) {\n            console.error(\"❌ 保存失败:\", error);\n            alert(\"保存失败: \".concat(error.message || \"请重试\"));\n        } finally{\n            setIsCreating(false);\n        }\n    };\n    // 发布系列课程\n    const handlePublish = async ()=>{\n        try {\n            setIsPublishingSeries(true);\n            // 检查是否有课程\n            if (courseList.length === 0) {\n                alert(\"发布失败：课程系列中至少需要包含一个课程\");\n                return;\n            }\n            console.log(\"\\uD83D\\uDCE4 开始发布系列课程，系列ID:\", seriesId);\n            const { data: response } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_2__.courseApi.publishCourseSeries(seriesId);\n            if (response.code === 200) {\n                console.log(\"✅ 系列课程发布成功:\", response.data);\n                // 构建成功消息\n                const publishData = response.data;\n                let successMessage = '系列课程\"'.concat(publishData.title, '\"发布成功！');\n                // 如果有发布统计信息，添加到消息中\n                if (publishData.publishStats) {\n                    const stats = publishData.publishStats;\n                    successMessage += \"\\n\\n发布统计：\\n• 总课程数：\".concat(publishData.totalCourses, \"\\n• 已发布课程：\").concat(publishData.publishedCourses, \"\\n• 视频课程：\").concat(stats.videoCourseCount, \"个\\n• 文档课程：\").concat(stats.documentCourseCount, \"个\\n• 总资源数：\").concat(stats.totalResourcesCount, \"个\");\n                    if (stats.totalVideoDuration > 0) {\n                        const durationMinutes = Math.round(stats.totalVideoDuration / 60);\n                        successMessage += \"\\n• 视频总时长：\".concat(durationMinutes, \"分钟\");\n                    }\n                }\n                alert(successMessage);\n                // 刷新课程列表以更新状态\n                await loadCourseList();\n            } else {\n                console.error(\"❌ 发布系列课程失败:\", response.message);\n                alert(response.message || \"发布系列课程失败\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"❌ 发布系列课程出错:\", error);\n            // 处理具体的错误信息\n            let errorMessage = \"发布系列课程失败\";\n            if ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) {\n                errorMessage = error.response.data.message;\n            } else if (error.message) {\n                errorMessage = error.message;\n            }\n            alert(errorMessage);\n        } finally{\n            setIsPublishingSeries(false);\n        }\n    };\n    // 退出编辑模式 - 保存数据并关闭\n    const handleExitEdit = ()=>{\n        handleSave();\n    };\n    // 显示设置面板\n    const showSettingsPanel = ()=>{\n        setRightPanelType(\"settings\");\n        setSelectedCourseId(null);\n    };\n    // 显示课程编辑面板\n    const showCoursePanel = async (courseId)=>{\n        console.log(\"\\uD83C\\uDFAF showCoursePanel 被调用\");\n        console.log(\"\\uD83C\\uDFAF 传入的courseId:\", courseId, \"类型:\", typeof courseId);\n        console.log(\"\\uD83C\\uDFAF 当前课程列表:\", courseList.map((c)=>({\n                id: c.id,\n                type: typeof c.id,\n                title: c.title\n            })));\n        setRightPanelType(\"course\");\n        setSelectedCourseId(courseId);\n        // 获取选中的课程并更新courseDetail状态\n        const selectedCourse = courseList.find((course)=>course.id === courseId);\n        console.log(\"\\uD83C\\uDFAF 找到的课程:\", selectedCourse);\n        if (selectedCourse) {\n            try {\n                console.log(\"\\uD83D\\uDD04 获取课程详情，seriesId:\", seriesId, \"courseId:\", courseId);\n                // 获取真实的课程详情数据\n                const { data: res } = await fetchCourseDetail(seriesId, courseId);\n                if (res.code === 200 && res.data) {\n                    var _courseDetailData_additionalResources, _courseDetailData_teachingInfo;\n                    const courseDetailData = res.data;\n                    console.log(\"✅ 获取到课程详情:\", courseDetailData);\n                    // 将真实的 additionalResources 映射到 teachingMaterials\n                    const teachingMaterials = ((_courseDetailData_additionalResources = courseDetailData.additionalResources) === null || _courseDetailData_additionalResources === void 0 ? void 0 : _courseDetailData_additionalResources.map((resource)=>({\n                            type: \"application/octet-stream\",\n                            name: resource.title || resource.name || \"附件\",\n                            url: resource.url\n                        }))) || [];\n                    // 将API返回的teachingInfo数组格式转换为新的格式\n                    const mappedTeachingInfo = ((_courseDetailData_teachingInfo = courseDetailData.teachingInfo) === null || _courseDetailData_teachingInfo === void 0 ? void 0 : _courseDetailData_teachingInfo.map((info)=>({\n                            title: info.title || \"\",\n                            content: Array.isArray(info.content) ? info.content.join(\"\\n\") : info.content || \"\"\n                        }))) || [];\n                    console.log(\"\\uD83D\\uDCCE 映射的教学附件:\", teachingMaterials);\n                    console.log(\"\\uD83D\\uDCDA 映射的教学信息:\", mappedTeachingInfo);\n                    setCourseDetail((prev)=>{\n                        var _courseDetailData_contentConfig_video, _courseDetailData_contentConfig, _courseDetailData_contentConfig_video1, _courseDetailData_contentConfig1, _courseDetailData_contentConfig_document, _courseDetailData_contentConfig2, _courseDetailData_contentConfig_document1, _courseDetailData_contentConfig3;\n                        return {\n                            ...prev,\n                            title: courseDetailData.title,\n                            description: courseDetailData.description,\n                            coverImage: courseDetailData.coverImage || \"\",\n                            isVideoEnabled: courseDetailData.hasVideo === 1,\n                            isAttachmentEnabled: courseDetailData.hasDocument === 1,\n                            contentConfig: courseDetailData.contentConfig || {},\n                            teachingInfo: mappedTeachingInfo,\n                            teachingMaterials: teachingMaterials,\n                            videoUrl: ((_courseDetailData_contentConfig = courseDetailData.contentConfig) === null || _courseDetailData_contentConfig === void 0 ? void 0 : (_courseDetailData_contentConfig_video = _courseDetailData_contentConfig.video) === null || _courseDetailData_contentConfig_video === void 0 ? void 0 : _courseDetailData_contentConfig_video.url) || \"\",\n                            videoName: ((_courseDetailData_contentConfig1 = courseDetailData.contentConfig) === null || _courseDetailData_contentConfig1 === void 0 ? void 0 : (_courseDetailData_contentConfig_video1 = _courseDetailData_contentConfig1.video) === null || _courseDetailData_contentConfig_video1 === void 0 ? void 0 : _courseDetailData_contentConfig_video1.name) || \"\",\n                            attachmentUrl: ((_courseDetailData_contentConfig2 = courseDetailData.contentConfig) === null || _courseDetailData_contentConfig2 === void 0 ? void 0 : (_courseDetailData_contentConfig_document = _courseDetailData_contentConfig2.document) === null || _courseDetailData_contentConfig_document === void 0 ? void 0 : _courseDetailData_contentConfig_document.url) || \"\",\n                            attachmentName: ((_courseDetailData_contentConfig3 = courseDetailData.contentConfig) === null || _courseDetailData_contentConfig3 === void 0 ? void 0 : (_courseDetailData_contentConfig_document1 = _courseDetailData_contentConfig3.document) === null || _courseDetailData_contentConfig_document1 === void 0 ? void 0 : _courseDetailData_contentConfig_document1.name) || \"\"\n                        };\n                    });\n                } else {\n                    console.error(\"❌ 获取课程详情失败:\", res.message);\n                    // 使用基础数据作为后备\n                    setCourseDetail((prev)=>({\n                            ...prev,\n                            title: selectedCourse.title,\n                            description: selectedCourse.description,\n                            coverImage: selectedCourse.coverImage || \"\",\n                            isVideoEnabled: selectedCourse.hasVideo === 1,\n                            isAttachmentEnabled: selectedCourse.hasDocument === 1,\n                            teachingMaterials: [] // 清空附件列表\n                        }));\n                }\n            } catch (error) {\n                console.error(\"❌ 获取课程详情异常:\", error);\n                // 使用基础数据作为后备\n                setCourseDetail((prev)=>({\n                        ...prev,\n                        title: selectedCourse.title,\n                        description: selectedCourse.description,\n                        coverImage: selectedCourse.coverImage || \"\",\n                        isVideoEnabled: selectedCourse.hasVideo === 1,\n                        isAttachmentEnabled: selectedCourse.hasDocument === 1,\n                        teachingMaterials: [] // 清空附件列表\n                    }));\n            }\n        }\n    };\n    // 获取选中的课程\n    const getSelectedCourse = ()=>{\n        return courseList.find((course)=>course.id === selectedCourseId);\n    };\n    if (!isVisible) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"course-list-modal-overlay\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"course-list-modal\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"course-list-header\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"course-list-title-section\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"course-list-title\",\n                                    children: \"课程列表\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 1160,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"course-list-actions\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: showSettingsPanel,\n                                            className: \"course-list-settings-btn \".concat(rightPanelType === \"settings\" ? \"active\" : \"\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1166,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 1162,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: addNewCourse,\n                                            className: \"course-list-add-btn\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1169,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 1168,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 1161,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                            lineNumber: 1159,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"course-list-close-btn\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"w-6 h-6\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                lineNumber: 1174,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                            lineNumber: 1173,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                    lineNumber: 1158,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"course-list-content\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"course-list-sidebar\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"course-list-items\",\n                                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"course-list-loading\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"加载中...\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 1185,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 1184,\n                                    columnNumber: 17\n                                }, undefined) : courseList.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"course-list-empty\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"course-list-empty-icon\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-12 h-12 text-gray-300\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1190,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 1189,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"course-list-empty-title\",\n                                            children: \"暂无课时\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 1192,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"course-list-empty-description\",\n                                            children: \"点击右上角的 + 按钮添加第一个课时\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 1193,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: addNewCourse,\n                                            className: \"course-list-empty-btn\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1200,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                \"添加课时\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 1196,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 1188,\n                                    columnNumber: 17\n                                }, undefined) : courseList.map((course)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"course-list-item \".concat(selectedCourseId === course.id ? \"active\" : \"\"),\n                                        onClick: ()=>showCoursePanel(course.id),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"course-list-item-content\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"course-list-item-text\",\n                                                        children: course.title\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1212,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"course-status-badge \".concat(course.status === 1 ? \"published\" : \"draft\"),\n                                                        children: course.status === 1 ? \"已发布\" : \"未发布\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1213,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1211,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                    deleteCourse(course.id);\n                                                },\n                                                className: \"course-list-item-delete\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-3 h-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1224,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1217,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, course.id, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 1206,\n                                        columnNumber: 19\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                lineNumber: 1182,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                            lineNumber: 1181,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"course-list-edit-area\",\n                            children: [\n                                rightPanelType === \"none\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"course-edit-empty\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"course-edit-empty-icon\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-16 h-16 text-gray-300\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1237,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 1236,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"course-edit-empty-title\",\n                                            children: \"无课程详情\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 1239,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"course-edit-empty-description\",\n                                            children: \"点击左侧课程或设置按钮查看详情\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 1240,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 1235,\n                                    columnNumber: 15\n                                }, undefined),\n                                rightPanelType === \"settings\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"course-series-cover\",\n                                            children: seriesCoverImage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: seriesCoverImage,\n                                                alt: \"系列课程封面\",\n                                                className: \"course-series-cover-image\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1251,\n                                                columnNumber: 21\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"course-series-cover-placeholder\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"系列课程封面\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1258,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1257,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 1249,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"course-edit-form\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-edit-field\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"course-edit-label\",\n                                                            children: \"系列课程标题\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1267,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: editingTitle,\n                                                            onChange: (e)=>setEditingTitle(e.target.value),\n                                                            className: \"course-edit-input\",\n                                                            placeholder: \"请输入系列课程标题\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1268,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1266,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-edit-field\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"course-edit-label\",\n                                                            children: \"课程标签\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1279,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Select_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            mode: \"multiple\",\n                                                            style: {\n                                                                width: \"100%\"\n                                                            },\n                                                            placeholder: \"请选择课程标签\",\n                                                            value: selectedTags,\n                                                            onChange: setSelectedTags,\n                                                            loading: tagsLoading,\n                                                            options: courseTags.map((tag)=>{\n                                                                console.log(\"\\uD83C\\uDFF7️ 渲染标签选项:\", tag);\n                                                                return {\n                                                                    label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            color: tag.color\n                                                                        },\n                                                                        children: tag.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1291,\n                                                                        columnNumber: 29\n                                                                    }, void 0),\n                                                                    value: tag.id\n                                                                };\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1280,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                fontSize: \"12px\",\n                                                                color: \"#666\",\n                                                                marginTop: \"4px\"\n                                                            },\n                                                            children: [\n                                                                \"调试: 当前标签数量 \",\n                                                                courseTags.length,\n                                                                \", 加载状态: \",\n                                                                tagsLoading ? \"是\" : \"否\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1300,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1278,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-edit-field\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"course-edit-label\",\n                                                            children: \"课程项目成员\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1307,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: projectMembers,\n                                                            onChange: (e)=>setProjectMembers(e.target.value),\n                                                            className: \"course-edit-input\",\n                                                            placeholder: \"请输入项目成员，如：张老师、李助教、王同学\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1308,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1306,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 1264,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true),\n                                rightPanelType === \"course\" && getSelectedCourse() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"course-detail-edit\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"course-detail-top\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"course-detail-cover\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"course-cover-upload-area\",\n                                                                onClick: ()=>{\n                                                                    var _document_getElementById;\n                                                                    return (_document_getElementById = document.getElementById(\"cover-upload-input\")) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.click();\n                                                                },\n                                                                children: courseDetail.coverImage || ((_getSelectedCourse = getSelectedCourse()) === null || _getSelectedCourse === void 0 ? void 0 : _getSelectedCourse.coverImage) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                    src: courseDetail.coverImage || ((_getSelectedCourse1 = getSelectedCourse()) === null || _getSelectedCourse1 === void 0 ? void 0 : _getSelectedCourse1.coverImage),\n                                                                    alt: \"课程封面\",\n                                                                    className: \"course-cover-image\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1332,\n                                                                    columnNumber: 27\n                                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"course-cover-placeholder\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"点击上传课程封面\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1339,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1338,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1327,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                id: \"cover-upload-input\",\n                                                                type: \"file\",\n                                                                accept: \"image/jpeg,image/jpg,image/png,image/gif\",\n                                                                onChange: handleCoverUpload,\n                                                                style: {\n                                                                    display: \"none\"\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1343,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1326,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"course-detail-basic\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"course-detail-field\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        children: \"课程标题\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1353,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        value: courseDetail.title || ((_getSelectedCourse2 = getSelectedCourse()) === null || _getSelectedCourse2 === void 0 ? void 0 : _getSelectedCourse2.title) || \"\",\n                                                                        onChange: (e)=>{\n                                                                            setCourseDetail((prev)=>({\n                                                                                    ...prev,\n                                                                                    title: e.target.value\n                                                                                }));\n                                                                            updateCourseTitle(selectedCourseId, e.target.value);\n                                                                        },\n                                                                        placeholder: \"请输入课程标题\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1354,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1352,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"course-detail-field\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        children: \"课程介绍\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1365,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                        value: courseDetail.description || ((_getSelectedCourse3 = getSelectedCourse()) === null || _getSelectedCourse3 === void 0 ? void 0 : _getSelectedCourse3.description) || \"\",\n                                                                        onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                    ...prev,\n                                                                                    description: e.target.value\n                                                                                })),\n                                                                        placeholder: \"请输入课程介绍\",\n                                                                        rows: 3\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1366,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1364,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1351,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1325,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"course-detail-section\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        children: \"课程资源\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1378,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"course-resource-item\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"resource-header-right\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"课程视频\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1383,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"switch\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"checkbox\",\n                                                                                checked: courseDetail.isVideoEnabled,\n                                                                                onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                            ...prev,\n                                                                                            isVideoEnabled: e.target.checked\n                                                                                        }))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1385,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"slider\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1390,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1384,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1382,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            courseDetail.isVideoEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"video-content-area\",\n                                                                children: ((_courseDetail_contentConfig = courseDetail.contentConfig) === null || _courseDetail_contentConfig === void 0 ? void 0 : (_courseDetail_contentConfig_video = _courseDetail_contentConfig.video) === null || _courseDetail_contentConfig_video === void 0 ? void 0 : _courseDetail_contentConfig_video.url) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"video-info-section\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"video-preview\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                                                                className: \"video-thumbnail\",\n                                                                                controls: true,\n                                                                                poster: courseDetail.coverImage,\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                                                                                        src: courseDetail.contentConfig.video.url,\n                                                                                        type: \"video/mp4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1404,\n                                                                                        columnNumber: 35\n                                                                                    }, undefined),\n                                                                                    \"您的浏览器不支持视频播放\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1399,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1398,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"video-name-centered\",\n                                                                            children: courseDetail.contentConfig.video.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1408,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            className: \"upload-btn-horizontal\",\n                                                                            onClick: triggerVideoUpload,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"重新上传\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1410,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1409,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1397,\n                                                                    columnNumber: 29\n                                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"video-upload-section\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"video-placeholder-centered\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"play-icon\",\n                                                                                children: \"▶\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1416,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1415,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            className: \"upload-btn-horizontal\",\n                                                                            onClick: triggerVideoUpload,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"上传视频\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1419,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1418,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1414,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1394,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1381,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"course-resource-item\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"resource-header-right\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"课程附件\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1430,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"switch\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"checkbox\",\n                                                                                checked: courseDetail.isAttachmentEnabled,\n                                                                                onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                            ...prev,\n                                                                                            isAttachmentEnabled: e.target.checked\n                                                                                        }))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1432,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"slider\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1437,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1431,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1429,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            courseDetail.isAttachmentEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"attachment-content-area\",\n                                                                children: ((_courseDetail_contentConfig1 = courseDetail.contentConfig) === null || _courseDetail_contentConfig1 === void 0 ? void 0 : (_courseDetail_contentConfig_document = _courseDetail_contentConfig1.document) === null || _courseDetail_contentConfig_document === void 0 ? void 0 : _courseDetail_contentConfig_document.url) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"attachment-info-section\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"attachment-preview\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"document-icon\",\n                                                                                    children: \"\\uD83D\\uDCC4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1446,\n                                                                                    columnNumber: 33\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"attachment-details\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"attachment-name\",\n                                                                                        children: courseDetail.contentConfig.document.name\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1448,\n                                                                                        columnNumber: 35\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1447,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1445,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            className: \"upload-btn-horizontal\",\n                                                                            onClick: triggerAttachmentUpload,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"重新上传\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1452,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1451,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1444,\n                                                                    columnNumber: 29\n                                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"attachment-upload-section\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"upload-btn-horizontal\",\n                                                                        onClick: triggerAttachmentUpload,\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"上传附件\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1458,\n                                                                            columnNumber: 33\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1457,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1456,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1441,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1428,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"course-resource-item\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"resource-header-simple\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"教学附件\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1469,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1468,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"teaching-materials\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"add-material-btn\",\n                                                                        onClick: triggerTeachingMaterialUpload,\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"+\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1473,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"上传\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1474,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1472,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    courseDetail.teachingMaterials && courseDetail.teachingMaterials.length > 0 ? courseDetail.teachingMaterials.map((material, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"material-item\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"material-name\",\n                                                                                    onClick: ()=>{\n                                                                                        if (material.url) {\n                                                                                            window.open(material.url, \"_blank\");\n                                                                                        }\n                                                                                    },\n                                                                                    style: {\n                                                                                        cursor: material.url ? \"pointer\" : \"default\",\n                                                                                        color: material.url ? \"#1890ff\" : \"inherit\",\n                                                                                        textDecoration: material.url ? \"underline\" : \"none\"\n                                                                                    },\n                                                                                    title: material.url ? \"点击下载附件\" : material.name,\n                                                                                    children: [\n                                                                                        \"\\uD83D\\uDCCE \",\n                                                                                        material.name\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1479,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    className: \"remove-material-btn\",\n                                                                                    onClick: ()=>removeTeachingMaterial(index),\n                                                                                    title: \"删除附件\",\n                                                                                    children: \"\\xd7\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1495,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, index, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1478,\n                                                                            columnNumber: 29\n                                                                        }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"empty-materials-hint\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            style: {\n                                                                                color: \"#999\",\n                                                                                fontSize: \"14px\"\n                                                                            },\n                                                                            children: \"暂无教学附件\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1506,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1505,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1471,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1467,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1377,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"course-detail-section\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"section-header\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                children: \"课程内容\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1516,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"add-content-section-btn\",\n                                                                onClick: addTeachingInfoItem,\n                                                                title: \"添加课程内容\",\n                                                                children: \"+ 添加课程内容\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1517,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1515,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"course-content-area\",\n                                                        children: courseDetail.teachingInfo && courseDetail.teachingInfo.length > 0 ? courseDetail.teachingInfo.map((info, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"teaching-info-card\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"card-header\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"card-title\",\n                                                                                children: [\n                                                                                    \"课程内容 \",\n                                                                                    index + 1\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1530,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"remove-card-btn\",\n                                                                                onClick: ()=>removeTeachingInfoItem(index),\n                                                                                title: \"删除此内容\",\n                                                                                children: \"\\xd7\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1531,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1529,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"card-content\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"input-group\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        children: \"标题\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1541,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                        type: \"text\",\n                                                                                        value: info.title,\n                                                                                        onChange: (e)=>updateTeachingInfoTitle(index, e.target.value),\n                                                                                        placeholder: \"请输入标题，如：教学目标、教学方法等\",\n                                                                                        className: \"title-input\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1542,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1540,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"input-group\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        children: \"内容\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1551,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                                        value: info.content,\n                                                                                        onChange: (e)=>updateTeachingInfoContent(index, e.target.value),\n                                                                                        placeholder: \"请输入具体内容，多个内容项可用换行分隔\",\n                                                                                        className: \"content-textarea\",\n                                                                                        rows: 4\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1552,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                lineNumber: 1550,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 1539,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1528,\n                                                                columnNumber: 27\n                                                            }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"empty-content-hint\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"暂无课程内容，点击右上角按钮添加\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 1565,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1564,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                        lineNumber: 1525,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1514,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"course-detail-section\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"one-key-section\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"one-key-item\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"重新上课\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1575,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"switch\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"checkbox\",\n                                                                            checked: courseDetail.isOneKeyOpen,\n                                                                            onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                        ...prev,\n                                                                                        isOneKeyOpen: e.target.checked\n                                                                                    }))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1577,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"slider\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1582,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1576,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 1574,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        courseDetail.isOneKeyOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"one-key-item\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"分配积木\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1589,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"switch\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: \"checkbox\",\n                                                                                    checked: courseDetail.isDistributionEnabled,\n                                                                                    onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                ...prev,\n                                                                                                isDistributionEnabled: e.target.checked\n                                                                                            }))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1591,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"slider\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1596,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1590,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        courseDetail.isDistributionEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"block-template-section\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    className: \"select-template-btn\",\n                                                                                    children: \"选择积木模板\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1600,\n                                                                                    columnNumber: 33\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"selected-template-display\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: courseDetail.selectedTemplate || \"选中的模板名字\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1604,\n                                                                                        columnNumber: 35\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1603,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1599,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1588,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"one-key-item\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"分配能量\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1611,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"switch\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: \"checkbox\",\n                                                                                    checked: courseDetail.isDistributionWater,\n                                                                                    onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                ...prev,\n                                                                                                isDistributionWater: e.target.checked\n                                                                                            }))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1613,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"slider\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1618,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1612,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1610,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                courseDetail.isDistributionWater && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"energy-input-section\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"需要能量：\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1624,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            value: courseDetail.requiredEnergy || \"\",\n                                                                            onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                        ...prev,\n                                                                                        requiredEnergy: e.target.value\n                                                                                    })),\n                                                                            placeholder: \"请输入需要的能量值\",\n                                                                            className: \"energy-input\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1625,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1623,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"one-key-item\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"分配任务\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1636,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"switch\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: \"checkbox\",\n                                                                                    checked: courseDetail.isDistributionLimit,\n                                                                                    onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                ...prev,\n                                                                                                isDistributionLimit: e.target.checked\n                                                                                            }))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1638,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"slider\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1643,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1637,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1635,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                courseDetail.isDistributionLimit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"task-config-form\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"task-config-row\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"task-config-field\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                            children: \"任务名称:\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1653,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                            type: \"text\",\n                                                                                            value: courseDetail.taskConfig.taskName,\n                                                                                            onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        taskConfig: {\n                                                                                                            ...prev.taskConfig,\n                                                                                                            taskName: e.target.value\n                                                                                                        }\n                                                                                                    })),\n                                                                                            placeholder: \"请输入任务名称\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1654,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1652,\n                                                                                    columnNumber: 33\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"task-config-field\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                            children: \"任务持续天数:\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1665,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                            type: \"number\",\n                                                                                            value: courseDetail.taskConfig.taskDuration,\n                                                                                            onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        taskConfig: {\n                                                                                                            ...prev.taskConfig,\n                                                                                                            taskDuration: e.target.value\n                                                                                                        }\n                                                                                                    })),\n                                                                                            placeholder: \"请输入天数\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1666,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1664,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1651,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"task-config-field task-config-full\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                    children: \"任务描述:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1680,\n                                                                                    columnNumber: 33\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                                    value: courseDetail.taskConfig.taskDescription,\n                                                                                    onChange: (e)=>setCourseDetail((prev)=>({\n                                                                                                ...prev,\n                                                                                                taskConfig: {\n                                                                                                    ...prev.taskConfig,\n                                                                                                    taskDescription: e.target.value\n                                                                                                }\n                                                                                            })),\n                                                                                    placeholder: \"请输入任务描述\",\n                                                                                    rows: 4\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1681,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1679,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"task-config-field task-config-full\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                    children: [\n                                                                                        \"任务自评项: \",\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"item-number\",\n                                                                                            children: courseDetail.taskConfig.selfAssessmentItems.length\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1694,\n                                                                                            columnNumber: 47\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1694,\n                                                                                    columnNumber: 33\n                                                                                }, undefined),\n                                                                                courseDetail.taskConfig.selfAssessmentItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"self-assessment-item\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                            type: \"text\",\n                                                                                            value: item,\n                                                                                            onChange: (e)=>{\n                                                                                                const newItems = [\n                                                                                                    ...courseDetail.taskConfig.selfAssessmentItems\n                                                                                                ];\n                                                                                                newItems[index] = e.target.value;\n                                                                                                setCourseDetail((prev)=>({\n                                                                                                        ...prev,\n                                                                                                        taskConfig: {\n                                                                                                            ...prev.taskConfig,\n                                                                                                            selfAssessmentItems: newItems\n                                                                                                        }\n                                                                                                    }));\n                                                                                            },\n                                                                                            placeholder: \"请输入自评项内容\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1697,\n                                                                                            columnNumber: 37\n                                                                                        }, undefined)\n                                                                                    }, index, false, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1696,\n                                                                                        columnNumber: 35\n                                                                                    }, undefined)),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    type: \"button\",\n                                                                                    className: \"add-assessment-btn\",\n                                                                                    onClick: ()=>setCourseDetail((prev)=>({\n                                                                                                ...prev,\n                                                                                                taskConfig: {\n                                                                                                    ...prev.taskConfig,\n                                                                                                    selfAssessmentItems: [\n                                                                                                        ...prev.taskConfig.selfAssessmentItems,\n                                                                                                        \"\"\n                                                                                                    ]\n                                                                                                }\n                                                                                            })),\n                                                                                    children: \"+\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1712,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1693,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"task-config-field task-config-full\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                    children: \"任务参考作品:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1729,\n                                                                                    columnNumber: 33\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"reference-works-section\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                            type: \"button\",\n                                                                                            className: \"select-works-btn\",\n                                                                                            children: \"选择作品\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1731,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"reference-works-grid\",\n                                                                                            children: [\n                                                                                                courseDetail.taskConfig.referenceWorks.map((work, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"reference-work-item\",\n                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                            children: work.name || \"作品\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                            lineNumber: 1735,\n                                                                                                            columnNumber: 41\n                                                                                                        }, undefined)\n                                                                                                    }, index, false, {\n                                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                        lineNumber: 1734,\n                                                                                                        columnNumber: 39\n                                                                                                    }, undefined)),\n                                                                                                Array.from({\n                                                                                                    length: Math.max(0, 3 - courseDetail.taskConfig.referenceWorks.length)\n                                                                                                }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"reference-work-item empty\"\n                                                                                                    }, \"empty-\".concat(index), false, {\n                                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                        lineNumber: 1740,\n                                                                                                        columnNumber: 39\n                                                                                                    }, undefined))\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                            lineNumber: 1732,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1730,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1728,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"task-config-field task-config-full\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                    children: \"任务参考资源:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1748,\n                                                                                    columnNumber: 33\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"reference-resources-section\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"reference-resources-grid\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                type: \"button\",\n                                                                                                className: \"upload-resource-btn\",\n                                                                                                onClick: ()=>{\n                                                                                                    // 触发文件上传\n                                                                                                    const input = document.createElement(\"input\");\n                                                                                                    input.type = \"file\";\n                                                                                                    input.accept = \".pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx,.txt,.jpg,.png,.gif\";\n                                                                                                    input.onchange = (e)=>{\n                                                                                                        var _e_target_files;\n                                                                                                        const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n                                                                                                        if (file) {\n                                                                                                            setCourseDetail((prev)=>({\n                                                                                                                    ...prev,\n                                                                                                                    taskConfig: {\n                                                                                                                        ...prev.taskConfig,\n                                                                                                                        referenceResources: [\n                                                                                                                            ...prev.taskConfig.referenceResources,\n                                                                                                                            {\n                                                                                                                                type: \"file\",\n                                                                                                                                name: file.name\n                                                                                                                            }\n                                                                                                                        ]\n                                                                                                                    }\n                                                                                                                }));\n                                                                                                        }\n                                                                                                    };\n                                                                                                    input.click();\n                                                                                                },\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                                                        size: 24\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                        lineNumber: 1777,\n                                                                                                        columnNumber: 39\n                                                                                                    }, undefined),\n                                                                                                    \"上传\"\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                lineNumber: 1751,\n                                                                                                columnNumber: 37\n                                                                                            }, undefined),\n                                                                                            courseDetail.taskConfig.referenceResources.map((resource, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"reference-resource-item\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                            children: resource.name\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                            lineNumber: 1782,\n                                                                                                            columnNumber: 41\n                                                                                                        }, undefined),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                            type: \"button\",\n                                                                                                            className: \"remove-resource-btn\",\n                                                                                                            onClick: ()=>{\n                                                                                                                const newResources = courseDetail.taskConfig.referenceResources.filter((_, i)=>i !== index);\n                                                                                                                setCourseDetail((prev)=>({\n                                                                                                                        ...prev,\n                                                                                                                        taskConfig: {\n                                                                                                                            ...prev.taskConfig,\n                                                                                                                            referenceResources: newResources\n                                                                                                                        }\n                                                                                                                    }));\n                                                                                                            },\n                                                                                                            children: \"\\xd7\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                            lineNumber: 1783,\n                                                                                                            columnNumber: 41\n                                                                                                        }, undefined)\n                                                                                                    ]\n                                                                                                }, index, true, {\n                                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                                    lineNumber: 1781,\n                                                                                                    columnNumber: 39\n                                                                                                }, undefined))\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                        lineNumber: 1750,\n                                                                                        columnNumber: 35\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                                    lineNumber: 1749,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                            lineNumber: 1747,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 1649,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 1573,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 1572,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 1323,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                            lineNumber: 1233,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                    lineNumber: 1179,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"course-list-footer\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"course-list-footer-left\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handlePublish,\n                                className: \"course-list-btn course-list-btn-publish\",\n                                disabled: courseList.length === 0 || isPublishingSeries,\n                                title: courseList.length === 0 ? \"发布失败：课程系列中至少需要包含一个课程\" : isPublishingSeries ? \"正在发布系列课程...\" : \"发布系列课程\",\n                                children: isPublishingSeries ? \"正在发布...\" : \"发布系列课程\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                lineNumber: 1816,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                            lineNumber: 1815,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"course-list-footer-right\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleExitEdit,\n                                    className: \"course-list-btn course-list-btn-exit\",\n                                    children: \"退出编辑模式\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 1832,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handlePublishCourse,\n                                    className: \"course-list-btn course-list-btn-publish-course\",\n                                    disabled: !selectedCourseId || ((_courseList_find = courseList.find((c)=>c.id === selectedCourseId)) === null || _courseList_find === void 0 ? void 0 : _courseList_find.status) === 1 || isPublishing,\n                                    title: !selectedCourseId ? \"请先选择要发布的课程\" : ((_courseList_find1 = courseList.find((c)=>c.id === selectedCourseId)) === null || _courseList_find1 === void 0 ? void 0 : _courseList_find1.status) === 1 ? \"该课程已发布\" : isPublishing ? \"正在发布课程...\" : \"发布选中的课程\",\n                                    children: isPublishing ? \"正在发布...\" : \"发布课程\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 1835,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleSave,\n                                    className: \"course-list-btn course-list-btn-save\",\n                                    disabled: uploadingFiles.size > 0 || isCreating || courseList.length === 0,\n                                    title: courseList.length === 0 ? \"请先添加课程内容\" : uploadingFiles.size > 0 ? \"有文件正在上传中，请等待上传完成\" : isCreating ? courseList.some((c)=>c.id > 1000000) ? \"正在创建课程...\" : \"正在保存课程...\" : courseList.some((c)=>c.id > 1000000) ? \"创建课程\" : \"保存课程\",\n                                    children: uploadingFiles.size > 0 ? \"上传中...\" : isCreating ? courseList.some((c)=>c.id > 1000000) ? \"正在创建...\" : \"正在保存...\" : courseList.some((c)=>c.id > 1000000) ? \"创建课程\" : \"保存课程\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 1851,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                            lineNumber: 1831,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                    lineNumber: 1814,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n            lineNumber: 1156,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n        lineNumber: 1155,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CourseListEditModal, \"sK6vbhs2N7KT+KkkqAe23qjTung=\");\n_c = CourseListEditModal;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CourseListEditModal);\nvar _c;\n$RefreshReg$(_c, \"CourseListEditModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/workbench/components/CourseListEditModal.tsx\n"));

/***/ })

});