'use client'

import React from 'react';
import { Input, Select } from 'antd';
import type { UploadFile } from 'antd';
import styles from './EventsTaskModal.module.css';

interface CompetitionFormProps {
  // 参赛信息
  realName: string;
  setRealName: (value: string) => void;
  idNumber: string;
  setIdNumber: (value: string) => void;
  competitionGroup: string;
  setCompetitionGroup: (value: string) => void;
  affiliatedSchool: string;
  setAffiliatedSchool: (value: string) => void;
  organization: string;
  setOrganization: (value: string) => void;
  availableGroups: string[];
  
  // 联系信息
  contactPerson: string;
  setContactPerson: (value: string) => void;
  contactPhone: string;
  setContactPhone: (value: string) => void;
  instructorName: string;
  setInstructorName: (value: string) => void;
  instructorPhone: string;
  setInstructorPhone: (value: string) => void;
  
  // 作品信息（仅上传模式需要）
  workDescription?: string;
  setWorkDescription?: (value: string) => void;
  showWorkDescription?: boolean;
  
  // 报名表
  activityData: any;
  registrationFormFileList: UploadFile[];
  setRegistrationFormFileList: (files: UploadFile[]) => void;
  uploadApi: any;

  // 验证错误
  validationErrors: Record<string, boolean>;
  setValidationErrors: (errors: Record<string, boolean> | ((prev: Record<string, boolean>) => Record<string, boolean>)) => void;
}

const CompetitionForm: React.FC<CompetitionFormProps> = ({
  realName,
  setRealName,
  idNumber,
  setIdNumber,
  competitionGroup,
  setCompetitionGroup,
  affiliatedSchool,
  setAffiliatedSchool,
  organization,
  setOrganization,
  availableGroups,
  contactPerson,
  setContactPerson,
  contactPhone,
  setContactPhone,
  instructorName,
  setInstructorName,
  instructorPhone,
  setInstructorPhone,
  workDescription,
  setWorkDescription,
  showWorkDescription = false,
  activityData,
  registrationFormFileList,
  setRegistrationFormFileList,
  uploadApi,
  validationErrors,
  setValidationErrors,
}) => {
  return (
    <div>
      {/* 作品信息 - 仅上传模式显示 */}
      {showWorkDescription && workDescription !== undefined && setWorkDescription && (
        <div className={styles.formGroup}>
          <div className={styles.formGroupHeader}>
            <h3>作品信息</h3>
            <p>请简要介绍您的作品</p>
          </div>

          <div className={styles.formSection}>
            <label>作品介绍</label>
            <Input.TextArea
              value={workDescription}
              onChange={(e) => setWorkDescription(e.target.value)}
              placeholder="请简要介绍作品的创意、技术特点或亮点..."
              rows={3}
              className="w-full"
            />
          </div>
        </div>
      )}

      {/* 参赛信息 */}
      <div className={styles.formGroup}>
        <div className={styles.formGroupHeader}>
          <h3>参赛信息</h3>
          <p>请填写参赛相关信息</p>
        </div>

        <div className={styles.formRow}>
          <div className={styles.formSection}>
            <label>
              <span style={{ color: '#ff4d4f', marginRight: '4px' }}>*</span>
              真实姓名
            </label>
            <Input
              value={realName}
              onChange={(e) => {
                setRealName(e.target.value);
                if (validationErrors.realName) {
                  setValidationErrors(prev => ({ ...prev, realName: false }));
                }
              }}
              placeholder="请输入真实姓名"
              status={validationErrors.realName ? 'error' : undefined}
            />
          </div>
          <div className={styles.formSection}>
            <label>
              <span style={{ color: '#ff4d4f', marginRight: '4px' }}>*</span>
              证件号
            </label>
            <Input
              value={idNumber}
              onChange={(e) => {
                const value = e.target.value;
                // 只允许数字和字母X（身份证最后一位可能是X）
                const filteredValue = value.replace(/[^0-9Xx]/g, '').toUpperCase();
                setIdNumber(filteredValue);
                if (validationErrors.idNumber) {
                  setValidationErrors(prev => ({ ...prev, idNumber: false }));
                }
              }}
              placeholder="请输入18位身份证号"
              status={validationErrors.idNumber ? 'error' : undefined}
              maxLength={18}
            />
            {validationErrors.idNumber && (
              <div style={{ color: '#ff4d4f', fontSize: '12px', marginTop: '4px' }}>
                请输入正确的18位身份证号码格式
              </div>
            )}
            {!validationErrors.idNumber && idNumber && idNumber.length < 18 && (
              <div style={{ color: '#faad14', fontSize: '12px', marginTop: '4px' }}>
                身份证号码需要18位，当前已输入{idNumber.length}位
              </div>
            )}
          </div>
        </div>

        <div className={styles.formRow}>
          <div className={styles.formSection}>
            <label>
              <span style={{ color: '#ff4d4f', marginRight: '4px' }}>*</span>
              参赛组别
            </label>
            <Select
              value={competitionGroup || undefined}
              onChange={(value) => {
                setCompetitionGroup(value || '');
                if (validationErrors.competitionGroup) {
                  setValidationErrors(prev => ({ ...prev, competitionGroup: false }));
                }
              }}
              placeholder="请选择参赛组别"
              status={validationErrors.competitionGroup ? 'error' : undefined}
              className="w-full"
              allowClear
            >
              {availableGroups.map((group) => (
                <Select.Option key={group} value={group}>
                  {group}
                </Select.Option>
              ))}
            </Select>
          </div>
          <div className={styles.formSection}>
            <label>
              <span style={{ color: '#ff4d4f', marginRight: '4px' }}>*</span>
              所属学校
            </label>
            <Input
              value={affiliatedSchool}
              onChange={(e) => {
                setAffiliatedSchool(e.target.value);
                if (validationErrors.affiliatedSchool) {
                  setValidationErrors(prev => ({ ...prev, affiliatedSchool: false }));
                }
              }}
              placeholder="请输入所属学校"
              status={validationErrors.affiliatedSchool ? 'error' : undefined}
            />
          </div>
        </div>

        <div className={styles.formSection}>
          <label>
            <span style={{ color: '#ff4d4f', marginRight: '4px' }}>*</span>
            机构单位
          </label>
          <Input
            value={organization}
            onChange={(e) => {
              setOrganization(e.target.value);
              if (validationErrors.organization) {
                setValidationErrors(prev => ({ ...prev, organization: false }));
              }
            }}
            placeholder="请输入机构单位"
            status={validationErrors.organization ? 'error' : undefined}
          />
        </div>
      </div>

      {/* 联系信息 */}
      <div className={styles.formGroup}>
        <div className={styles.formGroupHeader}>
          <h3>联系信息</h3>
          <p>请提供联系方式，便于赛事组织方与您沟通</p>
        </div>

        <div className={styles.formRow}>
          <div className={styles.formSection}>
            <label>
              <span style={{ color: '#ff4d4f', marginRight: '4px' }}>*</span>
              联系人
            </label>
            <Input
              value={contactPerson}
              onChange={(e) => {
                setContactPerson(e.target.value);
                if (validationErrors.contactPerson) {
                  setValidationErrors(prev => ({ ...prev, contactPerson: false }));
                }
              }}
              placeholder="请输入联系人姓名"
              status={validationErrors.contactPerson ? 'error' : undefined}
            />
          </div>
          <div className={styles.formSection}>
            <label>
              <span style={{ color: '#ff4d4f', marginRight: '4px' }}>*</span>
              联系电话
            </label>
            <Input
              value={contactPhone}
              onChange={(e) => {
                const value = e.target.value;
                // 只允许数字和连字符
                const filteredValue = value.replace(/[^0-9-]/g, '');
                setContactPhone(filteredValue);
                if (validationErrors.contactPhone) {
                  setValidationErrors(prev => ({ ...prev, contactPhone: false }));
                }
              }}
              placeholder="请输入联系电话"
              status={validationErrors.contactPhone ? 'error' : undefined}
              maxLength={13}
            />
            {validationErrors.contactPhone && (
              <div style={{ color: '#ff4d4f', fontSize: '12px', marginTop: '4px' }}>
                请输入正确的手机号码格式
              </div>
            )}
            {!validationErrors.contactPhone && contactPhone && contactPhone.length > 0 && contactPhone.length < 11 && (
              <div style={{ color: '#faad14', fontSize: '12px', marginTop: '4px' }}>
                手机号码通常为11位数字
              </div>
            )}
          </div>
        </div>

        <div className={styles.formRow}>
          <div className={styles.formSection}>
            <label>指导老师（选填）</label>
            <Input
              value={instructorName}
              onChange={(e) => {
                setInstructorName(e.target.value);
                if (validationErrors.instructorName) {
                  setValidationErrors(prev => ({ ...prev, instructorName: false }));
                }
              }}
              placeholder="如有指导老师，请输入姓名"
              status={validationErrors.instructorName ? 'error' : undefined}
            />
          </div>
          <div className={styles.formSection}>
            <label>指导老师电话（选填）</label>
            <Input
              value={instructorPhone}
              onChange={(e) => {
                const value = e.target.value;
                // 只允许数字和连字符
                const filteredValue = value.replace(/[^0-9-]/g, '');
                setInstructorPhone(filteredValue);
                if (validationErrors.instructorPhone) {
                  setValidationErrors(prev => ({ ...prev, instructorPhone: false }));
                }
              }}
              placeholder="如有指导老师，请输入电话"
              status={validationErrors.instructorPhone ? 'error' : undefined}
              maxLength={13}
            />
            {validationErrors.instructorPhone && (
              <div style={{ color: '#ff4d4f', fontSize: '12px', marginTop: '4px' }}>
                请输入正确的电话号码格式
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CompetitionForm;
