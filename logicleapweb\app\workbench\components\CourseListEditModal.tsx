'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { X, Settings, Plus, BookOpen, FileText } from 'lucide-react';
import { courseManagementApi } from '@/lib/api/course-management';
import { courseApi } from '@/lib/api/course';
import { uploadApi } from '@/lib/api/upload';
import { worksApi } from '@/lib/api/works';
import { Select } from 'antd';
import { GetNotification } from 'logic-common/dist/components/Notification';
import TemplatePickerModal from './TemplatePickerModal';
import './CourseListEditModal.css';
import './NewPublishTaskModal.css';

interface CourseItem {
  id: number;
  seriesId: number;
  title: string;
  description: string;
  coverImage: string;
  orderIndex: number;
  status: number;
  statusLabel: string;
  hasVideo: number;
  hasDocument: number;
  hasAudio: number;
  videoDuration: number;
  videoDurationLabel: string;
  videoName: string;
  firstTeachingTitle: string;
  resourcesCount: number;
  createdAt: string;
  updatedAt: string;
  // 新增字段用于存储完整的课程详情
  contentConfig?: any;
  teachingInfo?: any[];
  additionalResources?: Array<{
    title: string;
    url: string;
    description: string;
  }>;
}

interface CourseTag {
  id: number;
  name: string;
  color: string;
  category: number;
  description: string;
  status: number;
}

interface ApiResponse {
  code: number;
  message: string;
  data: {
    list: CourseItem[];
    pagination: {
      page: number;
      pageSize: number;
      total: number;
      totalPages: number;
      hasNext: boolean;
      hasPrev: boolean;
    };
  };
}

interface CourseListEditModalProps {
  isVisible: boolean;
  onClose: () => void;
  onSave: (data: any) => void;
  seriesTitle: string;
  seriesCoverImage?: string;
  seriesId: number;
}

// 获取系列课程详情
const fetchSeriesDetail = async (seriesId: number): Promise<any> => {
  console.log('🔍 获取系列课程详情，seriesId:', seriesId);
  console.log('🔗 调用API: GET /api/v1/course-management/series/{seriesId}');

  const response = await courseApi.getSeriesDetail(seriesId);
  console.log('📡 系列详情API响应:', response);
  return response.data;
};

// API调用函数 - 获取系列下的所有课程（包含未发布和已发布）
const fetchCourseList = async (seriesId: number): Promise<ApiResponse> => {
  console.log('🔍 获取系列课程列表，seriesId:', seriesId);
  console.log('🔗 调用API: GET /api/v1/course-management/series/{seriesId}/courses');

  // 使用课程管理API获取所有状态的课程
  const response = await courseApi.getManagementSeriesCourses(seriesId, {
    page: 1,
    pageSize: 100,
    // 不传status参数，获取所有状态的课程（status=0未发布，status=1已发布）
  });

  console.log('📡 API响应:', response);
  return response.data;
};

// 获取课程详情
const fetchCourseDetail = async (seriesId: number, courseId: number) => {
  return await courseApi.getCourseMarketplaceDetail(seriesId, courseId);
};

// 获取课程标签
const fetchCourseTags = async (): Promise<any> => {
  try {
    console.log('🔍 开始调用 courseApi.getCourseTags');
    const result = await courseApi.getCourseTags({
      page: 1,
      pageSize: 100,
      status: 1 // 只获取启用的标签
    });
    console.log('🔍 courseApi.getCourseTags 返回结果:', result);
    return result;
  } catch (error) {
    console.error('🔍 courseApi.getCourseTags 调用失败:', error);
    throw error;
  }
};

const CourseListEditModal: React.FC<CourseListEditModalProps> = ({
  isVisible,
  onClose,
  onSave,
  seriesTitle,
  seriesCoverImage,
  seriesId = 123 // 默认值，实际使用时应该传入真实的seriesId
}) => {
  const notification = GetNotification();

  const [courseList, setCourseList] = useState<CourseItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [rightPanelType, setRightPanelType] = useState<'none' | 'settings' | 'course'>('none');
  const [selectedCourseId, setSelectedCourseId] = useState<number | null>(null);
  const [uploadingFiles, setUploadingFiles] = useState<Set<string>>(new Set());
  const [isPublishing, setIsPublishing] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [isPublishingSeries, setIsPublishingSeries] = useState(false);
  const [seriesStatus, setSeriesStatus] = useState<number>(0); // 0=草稿，1=已发布，2=已归档

  // 删除确认弹窗状态
  const [deleteConfirmVisible, setDeleteConfirmVisible] = useState(false);
  const [courseToDelete, setCourseToDelete] = useState<number | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  // 模板选择弹窗状态
  const [isTemplatePickerOpen, setIsTemplatePickerOpen] = useState(false);

  const [editingTitle, setEditingTitle] = useState(seriesTitle);
  const [courseGoals, setCourseGoals] = useState('');
  const [courseObjectives, setCourseObjectives] = useState('');
  const [projectMembers, setProjectMembers] = useState('');

  // 作品相关状态
  const [works, setWorks] = useState<any[]>([]);
  const [loadingWorks, setLoadingWorks] = useState(false);
  const [selectedWorkIds, setSelectedWorkIds] = useState<number[]>([]);

  // 能量输入验证状态
  const [energyError, setEnergyError] = useState<string>('');

  // 时间选择状态
  const [showDurationPicker, setShowDurationPicker] = useState(false);

  // 课程标签相关状态
  const [courseTags, setCourseTags] = useState<CourseTag[]>([]);
  const [selectedTags, setSelectedTags] = useState<number[]>([]);
  const [tagsLoading, setTagsLoading] = useState(false);

  // 课程详细编辑状态
  const [courseDetail, setCourseDetail] = useState({
    title: '',
    description: '',
    coverImage: '',
    videoUrl: '',
    videoName: '',
    isVideoEnabled: false,
    attachmentUrl: '',
    attachmentName: '',
    isAttachmentEnabled: false,
    teachingMaterials: [] as { type: string; name: string; url: string }[],
    // 支持teachingInfo结构
    teachingInfo: [] as { title: string; content: string }[],
    // 支持contentConfig结构
    contentConfig: {
      hasVideo: 0,
      hasDocument: 0,
      hasAudio: 0,
      video: { url: '', name: '' },
      document: { url: '', name: '' },
      audio: { url: '', name: '' }
    },
    courseContent: {
      topic: '',
      content: ''
    },
    isOneKeyOpen: false,
    isDistributionEnabled: false,
    distributionReward: '',
    selectedTemplate: {
      id: null,
      name: ''
    },
    isDistributionWater: false,
    requiredEnergy: '',
    energyAmount: '',
    isDistributionLimit: false,
    distributionConditions: {
      inviteCount: '',
      taskCount: '',
      experience: ''
    },
    isDistributionTime: false,
    distributionTimeConditions: {
      startTime: '',
      endTime: ''
    },
    distributionMaterials: [] as { type: string; name: string }[],
    // 任务配置相关状态
    taskConfig: {
      taskName: '',
      taskDuration: '',
      taskDescription: '',
      selfAssessmentItems: [''],
      referenceWorks: [] as { id: number; title: string; selected: boolean }[],
      referenceResources: [] as { type: string; name: string; url?: string }[]
    }
  });

  // 获取课程列表数据
  useEffect(() => {
    if (isVisible && seriesId) {
      // 检查用户登录状态
      const token = localStorage.getItem('token');
      console.log('🔐 检查登录状态，token存在:', !!token);
      console.log('🔍 seriesId:', seriesId);

      if (!token) {
        console.error('❌ 用户未登录，无法获取课程列表');
        // 设置空列表，显示空状态
        setCourseList([]);
        setLoading(false);
        return;
      }

      loadCourseList();
      loadCourseTags();
      loadSeriesDetail();
      loadUserWorks();
    }
  }, [isVisible, seriesId]);

  const loadCourseList = async () => {
    try {
      setLoading(true);
      console.log('🔍 开始加载课程列表，seriesId:', seriesId);

      const response = await fetchCourseList(seriesId);
      console.log('📡 API响应:', response);

      if (response.code === 200) {
        console.log('✅ 课程列表数据:', response.data);
        const courses = response.data.list || [];
        console.log('✅ 解析的课程数组:', courses);
        console.log('📊 课程ID详情:', courses.map((c: any) => ({
          id: c.id,
          type: typeof c.id,
          title: c.title,
          status: c.status
        })));
        console.log('📊 课程状态统计:', {
          total: courses.length,
          draft: courses.filter((c: any) => c.status === 0).length,
          published: courses.filter((c: any) => c.status === 1).length
        });
        setCourseList(courses);
      } else {
        console.error('❌ API返回错误:', response);
        setCourseList([]);
      }
    } catch (error: any) {
      console.error('❌ 加载课程列表失败:', error);

      // 检查是否是认证错误
      if (error.response?.status === 401) {
        console.error('🔐 认证失败，用户未登录或token已过期');
      } else if (error.response?.status === 403) {
        console.error('🚫 权限不足，无法访问该系列课程');
      } else if (error.response?.status === 404) {
        console.error('📭 系列课程不存在，seriesId:', seriesId);
      } else {
        console.error('🔧 其他错误:', error.message);
      }

      setCourseList([]);
    } finally {
      setLoading(false);
    }
  };

  // 加载系列课程详情
  const loadSeriesDetail = async () => {
    try {
      console.log('🔍 开始加载系列课程详情，seriesId:', seriesId);

      const response = await fetchSeriesDetail(seriesId);
      console.log('📡 系列详情响应:', response);

      if (response.code === 200 && response.data) {
        const seriesData = response.data;
        console.log('✅ 系列课程详情:', seriesData);
        setSeriesStatus(seriesData.status || 0);
        console.log('📊 系列课程状态:', seriesData.status, '(0=草稿，1=已发布，2=已归档)');
      } else {
        console.error('❌ 获取系列详情失败:', response.message);
      }
    } catch (error: any) {
      console.error('❌ 加载系列详情异常:', error);
    }
  };

  // 加载用户作品数据
  const loadUserWorks = async () => {
    try {
      setLoadingWorks(true);

      // 从localStorage获取用户ID
      const userId = localStorage.getItem('userId') || '2896'; // 默认使用2896

      const response = await worksApi.getTeacherWorks(Number(userId), 1, 1000);

      // 检查多种可能的数据结构
      let worksList = [];

      if (response.data?.code === 200) {
        // 情况1: response.data.data.list
        if (response.data?.data?.list) {
          worksList = response.data.data.list;
        }
        // 情况2: response.data.data (直接是数组)
        else if (Array.isArray(response.data?.data)) {
          worksList = response.data.data;
        }
        // 情况3: response.data (直接是数组)
        else if (Array.isArray(response.data)) {
          worksList = response.data;
        }
      }
      // 情况4: 直接检查response是否是数组
      else if (Array.isArray(response)) {
        worksList = response;
      }

      setWorks(worksList);
    } catch (error) {
      console.error('加载作品数据失败:', error);
      setWorks([]);
    } finally {
      setLoadingWorks(false);
    }
  };

  // 处理作品选择
  const handleWorkSelect = (workId: number) => {
    if (selectedWorkIds.includes(workId)) {
      // 取消选中
      setSelectedWorkIds(prev => prev.filter(id => id !== workId));
    } else {
      // 选中
      setSelectedWorkIds(prev => [...prev, workId]);
    }
  };

  // 鼠标滚轮事件处理 - 将垂直滚轮转换为水平滚动
  const handleWheel = useCallback((e: React.WheelEvent<HTMLDivElement>) => {
    const container = e.currentTarget;
    const { scrollWidth, clientWidth } = container;

    // 检查是否可以滚动
    if (scrollWidth <= clientWidth) {
      return; // 内容不够长，不需要滚动
    }

    // 阻止默认的垂直滚动行为
    e.preventDefault();

    // 将垂直滚轮转换为水平滚动
    container.scrollLeft += e.deltaY;
  }, []);

  // 处理能量输入变化
  const handleEnergyChange = (value: string) => {
    setCourseDetail(prev => ({ ...prev, requiredEnergy: value }));

    // 验证输入值
    const numValue = parseFloat(value);
    if (value && (isNaN(numValue) || numValue < 0)) {
      setEnergyError('分配能力值不能为负数');
    } else {
      setEnergyError('');
    }
  };

  // 处理时间选择
  const handleDurationSelect = (hours: number, label: string) => {
    setCourseDetail(prev => ({
      ...prev,
      taskConfig: { ...prev.taskConfig, taskDuration: `${hours}小时 (${label})` }
    }));
    setShowDurationPicker(false);
  };



  // 点击外部关闭时间选择器
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (!target.closest('.duration-input-wrapper')) {
        setShowDurationPicker(false);
      }
    };

    if (showDurationPicker) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [showDurationPicker]);

  // 加载课程标签
  const loadCourseTags = async () => {
    try {
      setTagsLoading(true);
      console.log('🏷️ 开始加载课程标签');



      const response = await fetchCourseTags();
      console.log('📡 标签API完整响应:', response);

      // 检查响应结构
      if (response && response.data) {
        console.log('📊 响应数据:', response.data);

        let tags: CourseTag[] = [];

        // 处理标准的API响应格式 (response.data.list) - 根据实际API响应
        if (response.data.list && Array.isArray(response.data.list)) {
          tags = response.data.list;
          console.log('✅ 从 data.list 解析到标签:', tags.length, '个');
        }
        // 处理直接数组格式 (response.data)
        else if (Array.isArray(response.data)) {
          tags = response.data;
          console.log('✅ 从 data 数组解析到标签:', tags.length, '个');
        }
        // 处理嵌套的API响应格式 (response.data.data.list) - 备用格式
        else if (response.data.data && response.data.data.list && Array.isArray(response.data.data.list)) {
          tags = response.data.data.list;
          console.log('✅ 从 data.data.list 解析到标签:', tags.length, '个');
        }

        // 验证标签数据格式
        console.log('🔍 原始标签数据:', tags);
        console.log('🔍 标签数据类型检查:');
        tags.forEach((tag, index) => {
          console.log(`标签${index}:`, {
            tag,
            hasTag: !!tag,
            idType: typeof tag?.id,
            nameType: typeof tag?.name,
            nameValue: tag?.name,
            nameNotEmpty: tag?.name?.trim() !== ''
          });
        });

        const validTags = tags.filter(tag => {
          const isValid = tag &&
            typeof tag.id === 'number' &&
            typeof tag.name === 'string' &&
            tag.name.trim() !== '';

          if (!isValid) {
            console.log('❌ 无效标签:', tag, {
              hasTag: !!tag,
              idType: typeof tag?.id,
              nameType: typeof tag?.name,
              nameValue: tag?.name
            });
          }

          return isValid;
        });

        console.log('✅ 有效标签数量:', validTags.length);
        console.log('✅ 有效标签详情:', validTags);

        if (validTags.length > 0) {
          setCourseTags(validTags);
          console.log('✅ 成功设置真实标签数据');
          return;
        } else {
          console.warn('⚠️ 没有有效的标签数据');
        }
      } else {
        console.warn('⚠️ API响应格式不正确:', response);
      }

      // 如果没有真实数据，设置空数组
      console.log('📭 没有标签数据，设置空数组');
      setCourseTags([]);

    } catch (error: any) {
      console.error('❌ 加载课程标签失败:', error);
      console.error('❌ 错误详情:', {
        message: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data
      });

      // 发生错误时设置空数组
      setCourseTags([]);
    } finally {
      setTagsLoading(false);
    }
  };

  // 添加新课程
  const addNewCourse = () => {
    const newCourse: CourseItem = {
      id: Date.now(),
      seriesId: seriesId,
      title: `第${courseList.length + 1}课 - 新课时`,
      description: '',
      coverImage: '',
      orderIndex: courseList.length + 1,
      status: 0,
      statusLabel: '草稿',
      hasVideo: 0,
      hasDocument: 0,
      hasAudio: 0,
      videoDuration: 0,
      videoDurationLabel: '',
      videoName: '',
      firstTeachingTitle: '',
      resourcesCount: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    setCourseList([...courseList, newCourse]);
    // 自动选中新添加的课程
    showCoursePanel(newCourse.id);
  };

  // 显示删除确认弹窗
  const showDeleteConfirm = (id: number) => {
    setCourseToDelete(id);
    setDeleteConfirmVisible(true);
  };

  // 确认删除课程
  const confirmDeleteCourse = async () => {
    if (!courseToDelete) return;

    try {
      setIsDeleting(true);

      // 调用删除API
      await courseManagementApi.deleteCourse(courseToDelete);

      // 从列表中移除课程
      setCourseList(courseList.filter(course => course.id !== courseToDelete));

      // 如果删除的是当前选中的课程，清空右侧面板
      if (selectedCourseId === courseToDelete) {
        setRightPanelType('none');
        setSelectedCourseId(null);
      }

      // 关闭确认弹窗
      setDeleteConfirmVisible(false);
      setCourseToDelete(null);

      // 显示成功提示
      notification.success('课程已成功删除');
    } catch (error) {
      console.error('删除课程失败:', error);
      notification.error('删除课程失败，请重试');
    } finally {
      setIsDeleting(false);
    }
  };

  // 取消删除
  const cancelDelete = () => {
    if (isDeleting) return; // 正在删除时不允许取消
    setDeleteConfirmVisible(false);
    setCourseToDelete(null);
  };

  // 更新课程标题
  const updateCourseTitle = (id: number, newTitle: string) => {
    setCourseList(courseList.map(course =>
      course.id === id ? { ...course, title: newTitle } : course
    ));
  };

  // 处理课程封面上传
  const handleCoverUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // 检查文件类型
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
      if (!allowedTypes.includes(file.type)) {
        notification.error('请选择 JPG、PNG 或 GIF 格式的图片文件');
        return;
      }

      // 检查文件大小 (10MB)
      if (file.size > 10 * 1024 * 1024) {
        notification.error('文件大小不能超过 10MB');
        return;
      }

      try {
        console.log('📤 开始上传课程封面:', file.name);

        // 添加到上传中的文件列表
        setUploadingFiles(prev => new Set(prev).add('cover'));

        // 先显示预览图片
        const previewUrl = URL.createObjectURL(file);
        setCourseDetail(prev => ({ ...prev, coverImage: previewUrl }));

        // 上传到OSS
        const imageUrl = await uploadApi.uploadToOss(file);
        console.log('✅ 课程封面上传成功:', imageUrl);

        // 更新课程详情中的封面为真实URL
        setCourseDetail(prev => ({ ...prev, coverImage: imageUrl }));

        // 同时更新课程列表中的封面
        if (selectedCourseId) {
          setCourseList(prev => prev.map(course =>
            course.id === selectedCourseId ? { ...course, coverImage: imageUrl } : course
          ));
        }

        alert('课程封面上传成功！');
      } catch (error) {
        console.error('❌ 课程封面上传失败:', error);
        notification.error('课程封面上传失败，请重试');

        // 上传失败时清除预览图片
        setCourseDetail(prev => ({ ...prev, coverImage: '' }));
      } finally {
        // 从上传中的文件列表移除
        setUploadingFiles(prev => {
          const newSet = new Set(prev);
          newSet.delete('cover');
          return newSet;
        });
      }
    }
  };

  // 处理视频上传
  const handleVideoUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // 检查文件类型
      const allowedTypes = ['video/mp4', 'video/avi', 'video/mov', 'video/wmv', 'video/flv'];
      if (!allowedTypes.includes(file.type)) {
        alert('请选择 MP4、AVI、MOV、WMV 或 FLV 格式的视频文件');
        return;
      }

      // 检查文件大小 (100MB)
      if (file.size > 100 * 1024 * 1024) {
        alert('视频文件大小不能超过 100MB');
        return;
      }

      try {
        console.log('📤 开始上传课程视频:', file.name);

        // 添加到上传中的文件列表
        setUploadingFiles(prev => new Set(prev).add('video'));

        // 先显示预览视频
        const previewUrl = URL.createObjectURL(file);
        setCourseDetail(prev => ({
          ...prev,
          contentConfig: {
            ...prev.contentConfig,
            video: {
              url: previewUrl,
              name: file.name
            }
          }
        }));

        // 上传到OSS
        const videoUrl = await uploadApi.uploadToOss(file);
        console.log('✅ 课程视频上传成功:', videoUrl);

        // 更新课程详情中的视频信息为真实URL
        setCourseDetail(prev => ({
          ...prev,
          contentConfig: {
            ...prev.contentConfig,
            video: {
              url: videoUrl,
              name: file.name
            }
          }
        }));

        // 同时更新课程列表中的视频信息
        if (selectedCourseId) {
          setCourseList(prev => prev.map(course =>
            course.id === selectedCourseId ? {
              ...course,
              contentConfig: {
                ...course.contentConfig,
                video: {
                  url: videoUrl,
                  name: file.name
                }
              }
            } : course
          ));
        }

        alert('课程视频上传成功！');
      } catch (error) {
        console.error('❌ 课程视频上传失败:', error);
        alert('课程视频上传失败，请重试');

        // 上传失败时清除视频信息
        setCourseDetail(prev => ({
          ...prev,
          contentConfig: {
            ...prev.contentConfig,
            video: {
              url: '',
              name: ''
            }
          }
        }));
      } finally {
        // 从上传中的文件列表移除
        setUploadingFiles(prev => {
          const newSet = new Set(prev);
          newSet.delete('video');
          return newSet;
        });
      }
    }
  };

  // 触发视频文件选择
  const triggerVideoUpload = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'video/mp4,video/avi,video/mov,video/wmv,video/flv';
    input.onchange = (e) => handleVideoUpload(e as any);
    input.click();
  };

  // 处理附件上传
  const handleAttachmentUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // 检查文件类型
      const allowedTypes = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-powerpoint',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'text/plain',
        'image/jpeg',
        'image/png',
        'image/gif'
      ];

      if (!allowedTypes.includes(file.type)) {
        alert('请选择支持的文件格式：PDF、DOC、DOCX、XLS、XLSX、PPT、PPTX、TXT、JPG、PNG、GIF');
        return;
      }

      // 检查文件大小 (10MB)
      if (file.size > 10 * 1024 * 1024) {
        alert('文件大小不能超过 10MB');
        return;
      }

      try {
        console.log('📤 开始上传课程附件:', file.name);

        // 添加到上传中的文件列表
        setUploadingFiles(prev => new Set(prev).add('document'));

        // 上传到OSS
        const documentUrl = await uploadApi.uploadToOss(file);
        console.log('✅ 课程附件上传成功:', documentUrl);

        // 更新课程详情中的附件信息
        setCourseDetail(prev => ({
          ...prev,
          contentConfig: {
            ...prev.contentConfig,
            hasDocument: 1,
            document: {
              url: documentUrl,
              name: file.name
            }
          }
        }));

        alert('课程附件上传成功！');
      } catch (error) {
        console.error('❌ 课程附件上传失败:', error);
        alert('课程附件上传失败，请重试');
      } finally {
        // 从上传中的文件列表移除
        setUploadingFiles(prev => {
          const newSet = new Set(prev);
          newSet.delete('document');
          return newSet;
        });
      }
    }
  };

  // 触发附件文件选择
  const triggerAttachmentUpload = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.jpg,.png,.gif';
    input.onchange = (e) => handleAttachmentUpload(e as any);
    input.click();
  };

  // 处理教学附件上传
  const handleTeachingMaterialUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // 检查文件类型
      const allowedTypes = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-powerpoint',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'text/plain',
        'image/jpeg',
        'image/png',
        'image/gif'
      ];

      if (!allowedTypes.includes(file.type)) {
        alert('请选择支持的文件格式：PDF、DOC、DOCX、XLS、XLSX、PPT、PPTX、TXT、JPG、PNG、GIF');
        return;
      }

      // 检查文件大小 (10MB)
      if (file.size > 10 * 1024 * 1024) {
        alert('文件大小不能超过 10MB');
        return;
      }

      try {
        console.log('📤 开始上传教学材料:', file.name);

        // 添加到上传中的文件列表
        setUploadingFiles(prev => new Set(prev).add(`teaching-${Date.now()}`));

        // 上传到OSS
        const materialUrl = await uploadApi.uploadToOss(file);
        console.log('✅ 教学材料上传成功:', materialUrl);

        // 添加到教学附件列表
        const newMaterial = {
          type: file.type,
          name: file.name,
          url: materialUrl
        };

        setCourseDetail(prev => ({
          ...prev,
          teachingMaterials: [...prev.teachingMaterials, newMaterial]
        }));

        alert('教学材料上传成功！');
      } catch (error) {
        console.error('❌ 教学材料上传失败:', error);
        alert('教学材料上传失败，请重试');
      } finally {
        // 从上传中的文件列表移除
        setUploadingFiles(prev => {
          const newSet = new Set(prev);
          // 移除所有teaching-开头的项目
          Array.from(newSet).forEach(item => {
            if (item.startsWith('teaching-')) {
              newSet.delete(item);
            }
          });
          return newSet;
        });
      }
    }
  };

  // 触发教学附件文件选择
  const triggerTeachingMaterialUpload = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.jpg,.png,.gif';
    input.onchange = (e) => handleTeachingMaterialUpload(e as any);
    input.click();
  };

  // 删除教学附件
  const removeTeachingMaterial = (index: number) => {
    setCourseDetail(prev => ({
      ...prev,
      teachingMaterials: prev.teachingMaterials.filter((_, i) => i !== index)
    }));
  };

  // 课程内容管理函数
  const addTeachingInfoItem = () => {
    setCourseDetail(prev => ({
      ...prev,
      teachingInfo: [...prev.teachingInfo, { title: '', content: '' }]
    }));
  };

  const removeTeachingInfoItem = (index: number) => {
    setCourseDetail(prev => ({
      ...prev,
      teachingInfo: prev.teachingInfo.filter((_, i) => i !== index)
    }));
  };

  const updateTeachingInfoTitle = (index: number, title: string) => {
    setCourseDetail(prev => {
      const newTeachingInfo = [...prev.teachingInfo];
      newTeachingInfo[index] = { ...newTeachingInfo[index], title };
      return { ...prev, teachingInfo: newTeachingInfo };
    });
  };

  const updateTeachingInfoContent = (index: number, content: string) => {
    setCourseDetail(prev => {
      const newTeachingInfo = [...prev.teachingInfo];
      newTeachingInfo[index] = { ...newTeachingInfo[index], content };
      return { ...prev, teachingInfo: newTeachingInfo };
    });
  };

  // 将UI格式的teachingInfo转换为API格式
  const convertTeachingInfoForAPI = (teachingInfo: { title: string; content: string }[]) => {
    return teachingInfo.map(info => ({
      title: info.title,
      content: info.content.split('\n').filter(line => line.trim()) // 按换行分割并过滤空行
    }));
  };

  // 检查是否有未上传完成的文件（blob URL）
  const checkForBlobUrls = (course: CourseItem) => {
    const issues = [];

    // 检查封面图片
    if (course.coverImage && course.coverImage.startsWith('blob:')) {
      issues.push('课程封面图片');
    }

    // 检查视频文件
    if (course.contentConfig?.video?.url && course.contentConfig.video.url.startsWith('blob:')) {
      issues.push('课程视频');
    }

    // 检查文档附件
    if (course.contentConfig?.document?.url && course.contentConfig.document.url.startsWith('blob:')) {
      issues.push('课程文档');
    }

    // 检查教学材料
    if (course.additionalResources) {
      course.additionalResources.forEach((resource, index) => {
        if (resource.url && resource.url.startsWith('blob:')) {
          issues.push(`教学材料${index + 1}`);
        }
      });
    }

    return issues;
  };

  // 保存单个课程到后端
  const saveCourse = async (course: CourseItem) => {
    try {
      // 验证必要的数据
      if (!course.title || course.title.trim() === '') {
        throw new Error('课程标题不能为空');
      }

      // 检查是否有未上传完成的文件
      const blobIssues = checkForBlobUrls(course);
      if (blobIssues.length > 0) {
        throw new Error(`以下文件尚未上传完成，请等待上传完成后再保存：${blobIssues.join('、')}`);
      }

      // 准备课程数据
      const courseData = {
        seriesId: seriesId,
        title: course.title,
        description: course.description || '',
        coverImage: course.coverImage || '',
        hasVideo: course.contentConfig?.video?.url ? 1 : 0,
        hasDocument: course.contentConfig?.document?.url ? 1 : 0,
        hasAudio: 0,
        videoDuration: 0,
        contentConfig: course.contentConfig || {},
        teachingInfo: convertTeachingInfoForAPI(course.teachingInfo || []),
        additionalResources: course.additionalResources || [],
        orderIndex: course.orderIndex || 1
      };

      console.log('💾 准备保存课程数据:', courseData);

      let result: any;

      // 判断是新课程还是更新课程
      if (course.id && course.id > 1000000) {
        // 新课程，使用创建API
        console.log('📤 创建新课程');
        const { data: response } = await courseApi.createCourse(courseData);
        result = response;
        console.log('✅ 课程创建成功');
      } else {
        // 现有课程，使用更新API
        console.log('📤 更新现有课程:', course.id);
        const { data: response } = await courseApi.updateCourse(course.id, courseData);
        result = response;
        console.log('✅ 课程更新成功');
      }

      return result;
    } catch (error) {
      console.error('❌ 保存课程失败:', error);
      throw error;
    }
  };

  // 发布选中的课程
  const handlePublishCourse = async () => {
    if (!selectedCourseId) {
      alert('请先选择要发布的课程');
      return;
    }

    const selectedCourse = courseList.find(course => course.id === selectedCourseId);
    if (!selectedCourse) {
      alert('未找到选中的课程');
      return;
    }

    // 检查课程是否已经发布
    if (selectedCourse.status === 1) {
      alert('该课程已经发布，无需重复发布');
      return;
    }

    try {
      setIsPublishing(true);
      console.log('📤 开始发布课程');
      console.log('📤 selectedCourseId:', selectedCourseId, '类型:', typeof selectedCourseId);
      console.log('📤 课程信息:', selectedCourse);
      console.log('📤 课程ID字段:', selectedCourse.id, '类型:', typeof selectedCourse.id);
      console.log('📤 课程列表中的所有ID:', courseList.map(c => ({ id: c.id, type: typeof c.id, title: c.title })));

      // 确保使用正确的课程ID
      const courseIdToPublish = selectedCourse.id;
      console.log('📤 即将发布的课程ID:', courseIdToPublish, '类型:', typeof courseIdToPublish);

      const { data: response } = await courseApi.publishCourse(courseIdToPublish);

      if (response.code === 200) {
        console.log('✅ 课程发布成功:', response.data);
        alert('课程发布成功！');

        // 刷新课程列表以更新状态
        await loadCourseList();
      } else {
        console.error('❌ 发布课程失败:', response.message);
        alert(response.message || '发布课程失败');
      }
    } catch (error: any) {
      console.error('❌ 发布课程失败:', error);
      console.error('❌ 错误详情:', error.response?.data);

      // 处理具体的错误信息
      if (error.response?.data?.message) {
        alert(error.response.data.message);
      } else if (error.message) {
        alert(error.message);
      } else {
        alert('发布课程失败，请重试');
      }
    } finally {
      setIsPublishing(false);
    }
  };

  // 保存课程列表
  const handleSave = async () => {
    try {
      setIsCreating(true);
      // 检查是否有文件正在上传
      if (uploadingFiles.size > 0) {
        alert('有文件正在上传中，请等待上传完成后再保存');
        setIsCreating(false);
        return;
      }

      console.log('💾 开始保存课程列表');

      // 如果有选中的课程且在编辑状态，先保存当前课程
      if (selectedCourseId && rightPanelType === 'course') {
        const selectedCourse = courseList.find(c => c.id === selectedCourseId);
        if (selectedCourse) {
          // 更新课程数据
          const updatedCourse = {
            ...selectedCourse,
            title: courseDetail.title,
            description: courseDetail.description,
            coverImage: courseDetail.coverImage,
            hasVideo: courseDetail.isVideoEnabled ? 1 : 0,
            hasDocument: courseDetail.isAttachmentEnabled ? 1 : 0,
            contentConfig: courseDetail.contentConfig,
            teachingInfo: courseDetail.teachingInfo,
            additionalResources: courseDetail.teachingMaterials?.map(material => ({
              title: material.name,
              url: material.url,
              description: material.name
            })) || []
          };

          // 先更新课程列表中的数据
          setCourseList(prev => prev.map(course =>
            course.id === selectedCourseId ? updatedCourse : course
          ));

          // 然后保存到后端
          const savedCourseResult = await saveCourse(updatedCourse);

          // 如果课程有任务配置，发送课程设置请求
          if (courseDetail.taskConfig && (courseDetail.isDistributionWater || courseDetail.isOneKeyOpen)) {
            try {
              // 获取真实的课程ID
              let realCourseId = selectedCourseId;

              // 如果是新创建的课程，使用返回的真实ID
              if (selectedCourse.id > 1000000 && savedCourseResult?.data?.id) {
                realCourseId = savedCourseResult.data.id;
                console.log('🆔 使用新创建课程的真实ID:', realCourseId);
              }

              // 1. 先创建任务模板（如果有任务配置）
              if (courseDetail.taskConfig.taskName || courseDetail.taskConfig.taskDescription) {
                console.log('📝 开始创建任务模板...');

                // 构建任务模板数据
                const taskTemplateData = {
                  taskName: courseDetail.taskConfig.taskName || '',
                  taskDescription: courseDetail.taskConfig.taskDescription || '',
                  durationDays: courseDetail.taskConfig.taskDuration ? parseInt(courseDetail.taskConfig.taskDuration) : 7,
                  attachments: courseDetail.taskConfig.referenceResources.map((resource) => ({
                    title: resource.name,
                    url: resource.url || '',
                    type: resource.type === 'file' ? 'file' : 'document'
                  })),
                  workIdsStr: courseDetail.taskConfig.referenceWorks.map((_, index) => `${200 + index}`).join(','), // 模拟作品ID
                  selfAssessmentItems: courseDetail.taskConfig.selfAssessmentItems
                    .filter(item => item.trim() !== '') // 过滤空项
                    .map((content, index) => ({
                      content,
                      sequence: index + 1
                    }))
                };

                console.log('📤 发送任务模板创建请求到课程ID:', realCourseId);
                console.log('📤 任务模板数据:', taskTemplateData);

                await courseApi.createCourseTaskTemplate(realCourseId, taskTemplateData);
                console.log('✅ 任务模板创建成功');
              }

              // 2. 然后发送课程设置请求
              const settingsData = {
                templateId: courseDetail.selectedTemplate.id || null, // 使用选择的模板ID
                requiredPoints: courseDetail.requiredEnergy ? parseInt(courseDetail.requiredEnergy) : 0,
                autoCreateTasks: courseDetail.isOneKeyOpen ? 1 : 0
              };

              console.log('📤 发送课程设置请求到课程ID:', realCourseId);
              console.log('📤 设置数据:', settingsData);
              await courseApi.setCourseSettings(realCourseId, settingsData);
              console.log('✅ 课程设置保存成功');
            } catch (settingsError: any) {
              console.error('❌ 课程设置或任务模板创建失败:', settingsError);
              // 不阻断主流程，只记录错误
            }
          }
        }
      }

      // 保存系列课程信息
      const data = {
        title: editingTitle,
        courseGoals,
        courseObjectives,
        courseList
      };

      onSave(data);
      onClose();

      console.log('✅ 课程列表保存完成');
    } catch (error: any) {
      console.error('❌ 保存失败:', error);
      alert(`保存失败: ${error.message || '请重试'}`);
    } finally {
      setIsCreating(false);
    }
  };

  // 发布系列课程
  const handlePublish = async () => {
    // 如果系列已发布，不执行任何操作
    if (seriesStatus === 1) {
      return;
    }

    try {
      setIsPublishingSeries(true);

      // 检查是否有课程
      if (courseList.length === 0) {
        alert('发布失败：课程系列中至少需要包含一个课程');
        return;
      }

      console.log('📤 开始发布系列课程，系列ID:', seriesId);

      const { data: response } = await courseApi.publishCourseSeries(seriesId);

      if (response.code === 200) {
        console.log('✅ 系列课程发布成功:', response.data);

        // 构建成功消息
        const publishData = response.data;
        let successMessage = `系列课程"${publishData.title}"发布成功！`;

        // 如果有发布统计信息，添加到消息中
        if (publishData.publishStats) {
          const stats = publishData.publishStats;
          successMessage += `\n\n发布统计：\n• 总课程数：${publishData.totalCourses}\n• 已发布课程：${publishData.publishedCourses}\n• 视频课程：${stats.videoCourseCount}个\n• 文档课程：${stats.documentCourseCount}个\n• 总资源数：${stats.totalResourcesCount}个`;

          if (stats.totalVideoDuration > 0) {
            const durationMinutes = Math.round(stats.totalVideoDuration / 60);
            successMessage += `\n• 视频总时长：${durationMinutes}分钟`;
          }
        }

        alert(successMessage);

        // 更新系列状态为已发布
        setSeriesStatus(1);

        // 刷新课程列表以更新状态
        await loadCourseList();

        // 通知父组件刷新数据
        onSave({
          type: 'publish_series',
          seriesId: seriesId,
          message: '系列课程发布成功'
        });
      } else {
        console.error('❌ 发布系列课程失败:', response.message);
        alert(response.message || '发布系列课程失败');
      }
    } catch (error: any) {
      console.error('❌ 发布系列课程出错:', error);

      // 处理具体的错误信息
      let errorMessage = '发布系列课程失败';
      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      alert(errorMessage);
    } finally {
      setIsPublishingSeries(false);
    }
  };

  // 退出编辑模式 - 保存数据并关闭
  const handleExitEdit = () => {
    handleSave();
  };

  // 显示设置面板
  const showSettingsPanel = () => {
    setRightPanelType('settings');
    setSelectedCourseId(null);
  };

  // 处理模板选择
  const handleTemplateSelect = (template: any) => {
    setCourseDetail(prev => ({
      ...prev,
      selectedTemplate: {
        id: template.id,
        name: template.templateName
      }
    }));
    setIsTemplatePickerOpen(false);
  };

  // 打开模板选择弹窗
  const handleOpenTemplatePicker = () => {
    setIsTemplatePickerOpen(true);
  };

  // 显示课程编辑面板
  const showCoursePanel = async (courseId: number) => {
    setRightPanelType('course');
    setSelectedCourseId(courseId);

    // 如果作品数据还没有加载，重新加载
    if (works.length === 0 && !loadingWorks) {
      loadUserWorks();
    }

    // 获取选中的课程并更新courseDetail状态
    const selectedCourse = courseList.find(course => course.id === courseId);
    console.log('🎯 找到的课程:', selectedCourse);

    if (selectedCourse) {
      try {
        console.log('🔄 获取课程详情，seriesId:', seriesId, 'courseId:', courseId);

        // 获取真实的课程详情数据
        const { data: res } = await fetchCourseDetail(seriesId, courseId);

        if (res.code === 200 && res.data) {
          const courseDetailData = res.data;
          console.log('✅ 获取到课程详情:', courseDetailData);

          // 将真实的 additionalResources 映射到 teachingMaterials
          const teachingMaterials = courseDetailData.additionalResources?.map((resource: any) => ({
            type: 'application/octet-stream', // 默认类型
            name: resource.title || resource.name || '附件',
            url: resource.url
          })) || [];

          // 将API返回的teachingInfo数组格式转换为新的格式
          const mappedTeachingInfo = courseDetailData.teachingInfo?.map((info: any) => ({
            title: info.title || '',
            content: Array.isArray(info.content) ? info.content.join('\n') : (info.content || '')
          })) || [];

          console.log('📎 映射的教学附件:', teachingMaterials);
          console.log('📚 映射的教学信息:', mappedTeachingInfo);

          // 获取课程设置信息
          let courseSettings = null;
          let taskTemplateDetail = null;
          try {
            console.log('🔄 获取课程设置信息，courseId:', courseId);
            const { data: settingsRes } = await courseApi.getCourseSettings(courseId);
            if (settingsRes.code === 200 && settingsRes.data) {
              courseSettings = settingsRes.data;
              console.log('✅ 获取到课程设置信息:', courseSettings);

              // 如果有任务模板，获取第一个任务模板的详情
              if (courseSettings.taskTemplates?.length > 0) {
                const firstTemplate = courseSettings.taskTemplates[0];
                try {
                  console.log('🔄 获取任务模板详情，templateId:', firstTemplate.id);
                  const { data: templateRes } = await courseApi.getTaskTemplateDetail(courseId, firstTemplate.id);
                  if (templateRes.code === 200 && templateRes.data) {
                    taskTemplateDetail = templateRes.data;
                    console.log('✅ 获取到任务模板详情:', taskTemplateDetail);
                  }
                } catch (templateError) {
                  console.warn('⚠️ 获取任务模板详情失败:', templateError);
                }
              }
            }
          } catch (settingsError) {
            console.warn('⚠️ 获取课程设置信息失败:', settingsError);
          }

          setCourseDetail(prev => ({
            ...prev,
            title: courseDetailData.title,
            description: courseDetailData.description,
            coverImage: courseDetailData.coverImage || '',
            isVideoEnabled: courseDetailData.hasVideo === 1,
            isAttachmentEnabled: courseDetailData.hasDocument === 1,
            contentConfig: courseDetailData.contentConfig || {},
            teachingInfo: mappedTeachingInfo, // 使用映射后的教学信息
            teachingMaterials: teachingMaterials, // 使用真实的附件数据
            videoUrl: courseDetailData.contentConfig?.video?.url || '',
            videoName: courseDetailData.contentConfig?.video?.name || '',
            attachmentUrl: courseDetailData.contentConfig?.document?.url || '',
            attachmentName: courseDetailData.contentConfig?.document?.name || '',
            // 填充课程设置信息
            ...(courseSettings && {
              // 模板信息
              selectedTemplate: courseSettings.settings?.templateId ? {
                id: courseSettings.settings.templateId,
                name: courseSettings.settings.templateName || '未知模板'
              } : null,
              // 能量设置
              requiredEnergy: courseSettings.settings?.requiredPoints?.toString() || '',
              // 开关状态
              isDistributionWater: courseSettings.settings?.requiredPoints > 0,
              isOneKeyOpen: courseSettings.settings?.autoCreateTasks === 1,
              // 任务配置
              taskConfig: courseSettings.taskTemplates?.length > 0 ? {
                taskName: courseSettings.taskTemplates[0].taskName || '',
                taskDescription: courseSettings.taskTemplates[0].taskDescription || '',
                taskDuration: courseSettings.taskTemplates[0].durationDays?.toString() || '7',
                // 从任务模板详情获取完整信息
                referenceResources: taskTemplateDetail?.attachments?.map((attachment: any) => ({
                  name: attachment.title || attachment.name || '附件',
                  url: attachment.url || '',
                  type: attachment.type || 'file'
                })) || [],
                referenceWorks: taskTemplateDetail?.workIdsStr ?
                  taskTemplateDetail.workIdsStr.split(',').map((workId: string) => ({
                    id: parseInt(workId.trim()),
                    title: `作品${workId.trim()}`, // 临时标题，实际应该从作品列表获取
                    selected: true
                  })) : [],
                selfAssessmentItems: taskTemplateDetail?.selfAssessmentItems?.map((item: any) =>
                  item.content || item
                ) || []
              } : prev.taskConfig
            })
          }));
        } else {
          console.error('❌ 获取课程详情失败:', res.message);
          // 使用基础数据作为后备
          setCourseDetail(prev => ({
            ...prev,
            title: selectedCourse.title,
            description: selectedCourse.description,
            coverImage: selectedCourse.coverImage || '',
            isVideoEnabled: selectedCourse.hasVideo === 1,
            isAttachmentEnabled: selectedCourse.hasDocument === 1,
            teachingMaterials: [] // 清空附件列表
          }));
        }
      } catch (error) {
        console.error('❌ 获取课程详情异常:', error);
        // 使用基础数据作为后备
        setCourseDetail(prev => ({
          ...prev,
          title: selectedCourse.title,
          description: selectedCourse.description,
          coverImage: selectedCourse.coverImage || '',
          isVideoEnabled: selectedCourse.hasVideo === 1,
          isAttachmentEnabled: selectedCourse.hasDocument === 1,
          teachingMaterials: [] // 清空附件列表
        }));
      }
    }
  };

  // 获取选中的课程
  const getSelectedCourse = () => {
    return courseList.find(course => course.id === selectedCourseId);
  };

  if (!isVisible) return null;

  return (
    <div className="course-list-modal-overlay">
      <div className="course-list-modal">
        {/* 头部 */}
        <div className="course-list-header">
          <div className="course-list-title-section">
            <h2 className="course-list-title">课程列表</h2>
            <div className="course-list-actions">
              <button
                onClick={showSettingsPanel}
                className={`course-list-settings-btn ${rightPanelType === 'settings' ? 'active' : ''}`}
              >
                <Settings className="w-4 h-4" />
              </button>
              <button onClick={addNewCourse} className="course-list-add-btn">
                <Plus className="w-4 h-4" />
              </button>
            </div>
          </div>
          <button onClick={onClose} className="course-list-close-btn">
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* 主要内容 */}
        <div className="course-list-content">
          {/* 左侧课程列表 */}
          <div className="course-list-sidebar">
            <div className="course-list-items">
              {loading ? (
                <div className="course-list-loading">
                  <p>加载中...</p>
                </div>
              ) : courseList.length === 0 ? (
                <div className="course-list-empty">
                  <div className="course-list-empty-icon">
                    <BookOpen className="w-12 h-12 text-gray-300" />
                  </div>
                  <h3 className="course-list-empty-title">暂无课时</h3>
                  <p className="course-list-empty-description">
                    点击右上角的 + 按钮添加第一个课时
                  </p>
                  <button
                    onClick={addNewCourse}
                    className="course-list-empty-btn"
                  >
                    <Plus className="w-4 h-4" />
                    添加课时
                  </button>
                </div>
              ) : (
                courseList.map((course) => (
                  <div
                    key={course.id}
                    className={`course-list-item ${selectedCourseId === course.id ? 'active' : ''}`}
                    onClick={() => showCoursePanel(course.id)}
                  >
                    <div className="course-list-item-content">
                      <span className="course-list-item-text">{course.title}</span>
                      <span className={`course-status-badge ${course.status === 1 ? 'published' : 'draft'}`}>
                        {course.status === 1 ? '已发布' : '未发布'}
                      </span>
                    </div>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        showDeleteConfirm(course.id);
                      }}
                      className="course-list-item-delete"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </div>
                ))
              )}
            </div>
          </div>

          {/* 右侧编辑区域 */}
          <div className="course-list-edit-area">
            {rightPanelType === 'none' && (
              <div className="course-edit-empty">
                <div className="course-edit-empty-icon">
                  <FileText className="w-16 h-16 text-gray-300" />
                </div>
                <h3 className="course-edit-empty-title">无课程详情</h3>
                <p className="course-edit-empty-description">
                  点击左侧课程或设置按钮查看详情
                </p>
              </div>
            )}

            {rightPanelType === 'settings' && (
              <>
                {/* 系列课程封面 */}
                <div className="course-series-cover">
                  {seriesCoverImage ? (
                    <img
                      src={seriesCoverImage}
                      alt="系列课程封面"
                      className="course-series-cover-image"
                    />
                  ) : (
                    <div className="course-series-cover-placeholder">
                      <span>系列课程封面</span>
                    </div>
                  )}
                </div>

                {/* 系列设置表单 */}
                <div className="course-edit-form">
                  {/* 系列课程标题 */}
                  <div className="course-edit-field">
                    <label className="course-edit-label">系列课程标题</label>
                    <input
                      type="text"
                      value={editingTitle}
                      onChange={(e) => setEditingTitle(e.target.value)}
                      className="course-edit-input"
                      placeholder="请输入系列课程标题"
                    />
                  </div>

                  {/* 课程标签 */}
                  <div className="course-edit-field">
                    <label className="course-edit-label">课程标签</label>
                    <Select
                      mode="multiple"
                      style={{ width: '100%' }}
                      placeholder="请选择课程标签"
                      value={selectedTags}
                      onChange={setSelectedTags}
                      loading={tagsLoading}
                      options={courseTags.map(tag => {
                        console.log('🏷️ 渲染标签选项:', tag);
                        return {
                          label: (
                            <span style={{ color: tag.color }}>
                              {tag.name}
                            </span>
                          ),
                          value: tag.id
                        };
                      })}
                    />
                    {/* 调试信息 */}
                    <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
                      调试: 当前标签数量 {courseTags.length}, 加载状态: {tagsLoading ? '是' : '否'}
                    </div>
                  </div>

                  {/* 课程项目成员 */}
                  <div className="course-edit-field">
                    <label className="course-edit-label">课程项目成员</label>
                    <input
                      type="text"
                      value={projectMembers}
                      onChange={(e) => setProjectMembers(e.target.value)}
                      className="course-edit-input"
                      placeholder="请输入项目成员，如：张老师、李助教、王同学"
                    />
                  </div>
                </div>
              </>
            )}

            {rightPanelType === 'course' && getSelectedCourse() && (
              <>
                {/* 课程详细编辑界面 */}
                <div className="course-detail-edit">
                  {/* 顶部区域：课程封面和基本信息 */}
                  <div className="course-detail-top">
                    <div className="course-detail-cover">
                      <div
                        className="course-cover-upload-area"
                        onClick={() => document.getElementById('cover-upload-input')?.click()}
                      >
                        {courseDetail.coverImage || getSelectedCourse()?.coverImage ? (
                          <img
                            src={courseDetail.coverImage || getSelectedCourse()?.coverImage}
                            alt="课程封面"
                            className="course-cover-image"
                          />
                        ) : (
                          <div className="course-cover-placeholder">
                            <span>点击上传课程封面</span>
                          </div>
                        )}
                      </div>
                      <input
                        id="cover-upload-input"
                        type="file"
                        accept="image/jpeg,image/jpg,image/png,image/gif"
                        onChange={handleCoverUpload}
                        style={{ display: 'none' }}
                      />
                      {/* 一键上课按钮 */}
                      <button
                        className="one-click-class-btn"
                        onClick={() => {
                          // TODO: 实现一键上课功能
                          console.log('一键上课按钮被点击');
                          notification.info('一键上课功能开发中...');
                        }}
                      >
                        一键上课
                      </button>
                    </div>
                    <div className="course-detail-basic">
                      <div className="course-detail-field">
                        <label>课程标题</label>
                        <input
                          type="text"
                          value={courseDetail.title || getSelectedCourse()?.title || ''}
                          onChange={(e) => {
                            setCourseDetail(prev => ({ ...prev, title: e.target.value }));
                            updateCourseTitle(selectedCourseId!, e.target.value);
                          }}
                          placeholder="请输入课程标题"
                        />
                      </div>
                      <div className="course-detail-field">
                        <label>课程介绍</label>
                        <textarea
                          value={courseDetail.description || getSelectedCourse()?.description || ''}
                          onChange={(e) => setCourseDetail(prev => ({ ...prev, description: e.target.value }))}
                          placeholder="请输入课程介绍"
                          rows={3}
                        />
                      </div>
                    </div>
                  </div>

                  {/* 课程资源区域 */}
                  <div className="course-detail-section">
                    <h3>课程资源</h3>

                    {/* 课程视频 */}
                    <div className="course-resource-item">
                      <div className="resource-header-right">
                        <span>课程视频</span>
                        <label className="switch">
                          <input
                            type="checkbox"
                            checked={courseDetail.isVideoEnabled}
                            onChange={(e) => setCourseDetail(prev => ({ ...prev, isVideoEnabled: e.target.checked }))}
                          />
                          <span className="slider"></span>
                        </label>
                      </div>
                      {courseDetail.isVideoEnabled && (
                        <div className="video-content-area">
                          {/* 显示真实视频信息 */}
                          {courseDetail.contentConfig?.video?.url ? (
                            <div className="video-info-section">
                              <div className="video-preview">
                                <video
                                  className="video-thumbnail"
                                  controls
                                  poster={courseDetail.coverImage}
                                >
                                  <source src={courseDetail.contentConfig.video.url} type="video/mp4" />
                                  您的浏览器不支持视频播放
                                </video>
                              </div>
                              <div className="video-name-centered">{courseDetail.contentConfig.video.name}</div>
                              <button className="upload-btn-horizontal" onClick={triggerVideoUpload}>
                                <span>重新上传</span>
                              </button>
                            </div>
                          ) : (
                            <div className="video-upload-section">
                              <div className="video-placeholder-centered">
                                <div className="play-icon">▶</div>
                              </div>
                              <button className="upload-btn-horizontal" onClick={triggerVideoUpload}>
                                <span>上传视频</span>
                              </button>
                            </div>
                          )}
                        </div>
                      )}
                    </div>

                    {/* 课程附件 */}
                    <div className="course-resource-item">
                      <div className="resource-header-right">
                        <span>课程附件</span>
                        <label className="switch">
                          <input
                            type="checkbox"
                            checked={courseDetail.isAttachmentEnabled}
                            onChange={(e) => setCourseDetail(prev => ({ ...prev, isAttachmentEnabled: e.target.checked }))}
                          />
                          <span className="slider"></span>
                        </label>
                      </div>
                      {courseDetail.isAttachmentEnabled && (
                        <div className="attachment-content-area">
                          {/* 显示真实附件信息 */}
                          {courseDetail.contentConfig?.document?.url ? (
                            <div className="attachment-info-section">
                              <div className="attachment-preview">
                                <div className="document-icon">📄</div>
                                <div className="attachment-details">
                                  <div className="attachment-name">{courseDetail.contentConfig.document.name}</div>
                                </div>
                              </div>
                              <button className="upload-btn-horizontal" onClick={triggerAttachmentUpload}>
                                <span>重新上传</span>
                              </button>
                            </div>
                          ) : (
                            <div className="attachment-upload-section">
                              <button className="upload-btn-horizontal" onClick={triggerAttachmentUpload}>
                                <span>上传附件</span>
                              </button>
                            </div>
                          )}
                        </div>
                      )}
                    </div>

                    {/* 教学附件 */}
                    <div className="course-resource-item">
                      <div className="resource-header-simple">
                        <span>教学附件</span>
                      </div>
                      <div className="teaching-materials">
                        <button className="add-material-btn" onClick={triggerTeachingMaterialUpload}>
                          <span>+</span>
                          <span>上传</span>
                        </button>
                        {courseDetail.teachingMaterials && courseDetail.teachingMaterials.length > 0 ? (
                          courseDetail.teachingMaterials.map((material, index) => (
                            <div key={index} className="material-item">
                              <span
                                className="material-name"
                                onClick={() => {
                                  if (material.url) {
                                    window.open(material.url, '_blank');
                                  }
                                }}
                                style={{
                                  cursor: material.url ? 'pointer' : 'default',
                                  color: material.url ? '#1890ff' : 'inherit',
                                  textDecoration: material.url ? 'underline' : 'none'
                                }}
                                title={material.url ? '点击下载附件' : material.name}
                              >
                                📎 {material.name}
                              </span>
                              <button
                                className="remove-material-btn"
                                onClick={() => removeTeachingMaterial(index)}
                                title="删除附件"
                              >
                                ×
                              </button>
                            </div>
                          ))
                        ) : (
                          <div className="empty-materials-hint">
                            <span style={{ color: '#999', fontSize: '14px' }}>暂无教学附件</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* 课程内容区域 */}
                  <div className="course-detail-section">
                    <div className="section-header">
                      <h3>课程内容</h3>
                      <button
                        className="add-content-section-btn"
                        onClick={addTeachingInfoItem}
                        title="添加课程内容"
                      >
                        + 添加课程内容
                      </button>
                    </div>
                    <div className="course-content-area">
                      {courseDetail.teachingInfo && courseDetail.teachingInfo.length > 0 ? (
                        courseDetail.teachingInfo.map((info, index) => (
                          <div key={index} className="teaching-info-card">
                            <div className="card-header">
                              <span className="card-title">课程内容 {index + 1}</span>
                              <button
                                className="remove-card-btn"
                                onClick={() => removeTeachingInfoItem(index)}
                                title="删除此内容"
                              >
                                ×
                              </button>
                            </div>
                            <div className="card-content">
                              <div className="input-group">
                                <label>标题</label>
                                <input
                                  type="text"
                                  value={info.title}
                                  onChange={(e) => updateTeachingInfoTitle(index, e.target.value)}
                                  placeholder="请输入标题，如：教学目标、教学方法等"
                                  className="title-input"
                                />
                              </div>
                              <div className="input-group">
                                <label>内容</label>
                                <textarea
                                  value={info.content}
                                  onChange={(e) => updateTeachingInfoContent(index, e.target.value)}
                                  placeholder="请输入具体内容，多个内容项可用换行分隔"
                                  className="content-textarea"
                                  rows={4}
                                />
                              </div>
                            </div>
                          </div>
                        ))
                      ) : (
                        <div className="empty-content-hint">
                          <p>暂无课程内容，点击右上角按钮添加</p>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* 重新上课 */}
                  <div className="course-detail-section">
                    <div className="one-key-section">
                      <div className="one-key-item">
                        <span>开始上课</span>
                        <label className="switch">
                          <input
                            type="checkbox"
                            checked={courseDetail.isOneKeyOpen}
                            onChange={(e) => setCourseDetail(prev => ({ ...prev, isOneKeyOpen: e.target.checked }))}
                          />
                          <span className="slider"></span>
                        </label>
                      </div>

                      {courseDetail.isOneKeyOpen && (
                        <>
                          <div className="one-key-item">
                            <span>分配积木</span>
                            <label className="switch">
                              <input
                                type="checkbox"
                                checked={courseDetail.isDistributionEnabled}
                                onChange={(e) => setCourseDetail(prev => ({ ...prev, isDistributionEnabled: e.target.checked }))}
                              />
                              <span className="slider"></span>
                            </label>
                            {courseDetail.isDistributionEnabled && (
                              <div className="block-template-section">
                                <button
                                  className="select-template-btn"
                                  onClick={handleOpenTemplatePicker}
                                >
                                  选择积木模板
                                </button>
                                <div className="selected-template-display">
                                  <span>{courseDetail.selectedTemplate.name || '选中的模板名字'}</span>
                                </div>
                              </div>
                            )}
                          </div>

                          <div className="one-key-item">
                            <span>分配能量</span>
                            <label className="switch">
                              <input
                                type="checkbox"
                                checked={courseDetail.isDistributionWater}
                                onChange={(e) => setCourseDetail(prev => ({ ...prev, isDistributionWater: e.target.checked }))}
                              />
                              <span className="slider"></span>
                            </label>
                          </div>

                          {courseDetail.isDistributionWater && (
                            <div className="energy-input-section">
                              <div>
                                <span>需要能量：</span>
                                <input
                                  type="text"
                                  value={courseDetail.requiredEnergy || ''}
                                  onChange={(e) => handleEnergyChange(e.target.value)}
                                  placeholder="请输入需要的能量值"
                                  className={`energy-input ${energyError ? 'error' : ''}`}
                                />
                              </div>
                              {energyError && (
                                <div className="error-message">
                                  {energyError}
                                </div>
                              )}
                            </div>
                          )}

                          <div className="one-key-item">
                            <span>分配任务</span>
                            <label className="switch">
                              <input
                                type="checkbox"
                                checked={courseDetail.isDistributionLimit}
                                onChange={(e) => setCourseDetail(prev => ({ ...prev, isDistributionLimit: e.target.checked }))}
                              />
                              <span className="slider"></span>
                            </label>
                          </div>

                          {/* 任务配置表单 */}
                          {courseDetail.isDistributionLimit && (
                            <div className="task-config-form">
                              {/* 任务名称和持续天数 */}
                              <div className="task-config-row">
                                <div className="task-config-field">
                                  <label>任务名称:</label>
                                  <input
                                    type="text"
                                    value={courseDetail.taskConfig.taskName}
                                    onChange={(e) => setCourseDetail(prev => ({
                                      ...prev,
                                      taskConfig: { ...prev.taskConfig, taskName: e.target.value }
                                    }))}
                                    placeholder="请输入任务名称"
                                  />
                                </div>
                                <div className="task-config-field">
                                  <label>任务持续天数:</label>
                                  <div className="duration-input-wrapper">
                                    <input
                                      type="text"
                                      value={courseDetail.taskConfig.taskDuration}
                                      onClick={() => setShowDurationPicker(true)}
                                      placeholder="请选择时间"
                                      readOnly
                                    />
                                    {showDurationPicker && (
                                      <div className="duration-picker">
                                        <div className="duration-options">
                                          <button onClick={() => handleDurationSelect(1, '1小时')}>1小时</button>
                                          <button onClick={() => handleDurationSelect(6, '6小时')}>6小时</button>
                                          <button onClick={() => handleDurationSelect(12, '12小时')}>12小时</button>
                                          <button onClick={() => handleDurationSelect(24, '1天')}>1天</button>
                                          <button onClick={() => handleDurationSelect(168, '7天')}>7天</button>
                                        </div>
                                        <button
                                          className="close-picker"
                                          onClick={() => setShowDurationPicker(false)}
                                        >
                                          ×
                                        </button>
                                      </div>
                                    )}
                                  </div>
                                </div>
                              </div>

                              {/* 任务描述 */}
                              <div className="task-config-field task-config-full">
                                <label>任务描述:</label>
                                <textarea
                                  value={courseDetail.taskConfig.taskDescription}
                                  onChange={(e) => setCourseDetail(prev => ({
                                    ...prev,
                                    taskConfig: { ...prev.taskConfig, taskDescription: e.target.value }
                                  }))}
                                  placeholder="请输入任务描述"
                                  rows={4}
                                />
                              </div>

                              {/* 任务自评项 */}
                              <div className="task-config-field task-config-full">
                                <label>任务自评项: <span className="item-number">{courseDetail.taskConfig.selfAssessmentItems.length}</span></label>
                                {courseDetail.taskConfig.selfAssessmentItems.map((item, index) => (
                                  <div key={index} className="self-assessment-item">
                                    <input
                                      type="text"
                                      value={item}
                                      onChange={(e) => {
                                        const newItems = [...courseDetail.taskConfig.selfAssessmentItems];
                                        newItems[index] = e.target.value;
                                        setCourseDetail(prev => ({
                                          ...prev,
                                          taskConfig: { ...prev.taskConfig, selfAssessmentItems: newItems }
                                        }));
                                      }}
                                      placeholder="请输入自评项内容"
                                    />
                                  </div>
                                ))}
                                <button
                                  type="button"
                                  className="add-assessment-btn"
                                  onClick={() => setCourseDetail(prev => ({
                                    ...prev,
                                    taskConfig: {
                                      ...prev.taskConfig,
                                      selfAssessmentItems: [...prev.taskConfig.selfAssessmentItems, '']
                                    }
                                  }))}
                                >
                                  +
                                </button>
                              </div>

                              {/* 任务参考作品 */}
                              <div className="task-config-field task-config-full">
                                <label>任务参考作品:</label>
                                <div className="reference-works-section">
                                  <div className="works-section">
                                    <p className="help-text">选择作品作为任务参考资料（可多选）</p>
                                    <div
                                      className="relative works-scroll-wrapper"
                                      style={{
                                        minHeight: '200px',
                                        cursor: 'grab',
                                        userSelect: 'none'
                                      }}
                                    >
                                      {loadingWorks ? (
                                        <div className="loading-container">
                                          <div className="loading-spinner"></div>
                                          <span>加载中...</span>
                                        </div>
                                      ) : works.length > 0 ? (
                                        <div
                                          className="works-horizontal-scroll"
                                          onWheel={handleWheel}
                                        >
                                          {works.map((work) => (
                                            <div
                                              key={work.id}
                                              className={`work-card ${selectedWorkIds.includes(work.id) ? 'selected' : ''}`}
                                              onClick={() => handleWorkSelect(work.id)}
                                            >
                                              <div className="work-image">
                                                {work.coverImage || work.screenShotImage ? (
                                                  <img
                                                    src={work.coverImage || work.screenShotImage}
                                                    alt={work.title}
                                                    onError={(e) => {
                                                      const target = e.currentTarget as HTMLImageElement;
                                                      target.style.display = 'none';
                                                      const nextElement = target.nextElementSibling as HTMLElement;
                                                      if (nextElement) {
                                                        nextElement.style.display = 'flex';
                                                      }
                                                    }}
                                                  />
                                                ) : null}
                                                <div className="work-placeholder" style={{ display: work.coverImage || work.screenShotImage ? 'none' : 'flex' }}>
                                                  作品
                                                </div>
                                              </div>
                                              <div className="work-title">{work.title || work.name || work.workName || '未命名作品'}</div>
                                              {selectedWorkIds.includes(work.id) && (
                                                <div className="selected-indicator">✓</div>
                                              )}
                                            </div>
                                          ))}
                                        </div>
                                      ) : (
                                        <div className="empty-placeholder">
                                          <div className="empty-text">暂无作品</div>
                                        </div>
                                      )}
                                    </div>
                                  </div>
                                </div>
                              </div>

                              {/* 任务参考资源 */}
                              <div className="task-config-field task-config-full">
                                <label>任务参考资源:</label>
                                <div className="reference-resources-section">
                                  <div className="reference-resources-grid">
                                    <button
                                      type="button"
                                      className="upload-resource-btn"
                                      onClick={() => {
                                        // 触发文件上传
                                        const input = document.createElement('input');
                                        input.type = 'file';
                                        input.accept = '.pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx,.txt,.jpg,.png,.gif';
                                        input.onchange = (e) => {
                                          const file = (e.target as HTMLInputElement).files?.[0];
                                          if (file) {
                                            setCourseDetail(prev => ({
                                              ...prev,
                                              taskConfig: {
                                                ...prev.taskConfig,
                                                referenceResources: [
                                                  ...prev.taskConfig.referenceResources,
                                                  { type: 'file', name: file.name }
                                                ]
                                              }
                                            }));
                                          }
                                        };
                                        input.click();
                                      }}
                                    >
                                      <Plus size={24} />
                                      上传
                                    </button>
                                    {courseDetail.taskConfig.referenceResources.map((resource, index) => (
                                      <div key={index} className="reference-resource-item">
                                        <span>{resource.name}</span>
                                        <button
                                          type="button"
                                          className="remove-resource-btn"
                                          onClick={() => {
                                            const newResources = courseDetail.taskConfig.referenceResources.filter((_, i) => i !== index);
                                            setCourseDetail(prev => ({
                                              ...prev,
                                              taskConfig: { ...prev.taskConfig, referenceResources: newResources }
                                            }));
                                          }}
                                        >
                                          ×
                                        </button>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              </div>
                            </div>
                          )}
                        </>
                      )}
                    </div>
                  </div>
                </div>
              </>
            )}
          </div>
        </div>

        {/* 底部按钮 */}
        <div className="course-list-footer">
          <div className="course-list-footer-left">
            <button
              onClick={handlePublish}
              className="course-list-btn course-list-btn-publish"
              disabled={courseList.length === 0 || isPublishingSeries || seriesStatus === 1}
              title={
                seriesStatus === 1
                  ? '系列课程已发布'
                  : courseList.length === 0
                    ? '发布失败：课程系列中至少需要包含一个课程'
                    : isPublishingSeries
                      ? '正在发布系列课程...'
                      : '发布系列课程'
              }
            >
              {seriesStatus === 1
                ? '已发布'
                : isPublishingSeries
                  ? '正在发布...'
                  : '发布系列课程'
              }
            </button>
          </div>
          <div className="course-list-footer-right">
            <button onClick={handleExitEdit} className="course-list-btn course-list-btn-exit">
              退出编辑模式
            </button>
            <button
              onClick={handlePublishCourse}
              className="course-list-btn course-list-btn-publish-course"
              disabled={!selectedCourseId || courseList.find(c => c.id === selectedCourseId)?.status === 1 || isPublishing}
              title={
                !selectedCourseId
                  ? '请先选择要发布的课程'
                  : courseList.find(c => c.id === selectedCourseId)?.status === 1
                    ? '该课程已发布'
                    : isPublishing
                      ? '正在发布课程...'
                      : '发布选中的课程'
              }
            >
              {isPublishing ? '正在发布...' : '发布课程'}
            </button>
            <button
              onClick={handleSave}
              className="course-list-btn course-list-btn-save"
              disabled={uploadingFiles.size > 0 || isCreating || courseList.length === 0}
              title={
                courseList.length === 0
                  ? '请先添加课程内容'
                  : uploadingFiles.size > 0
                    ? '有文件正在上传中，请等待上传完成'
                    : isCreating
                      ? (courseList.some(c => c.id > 1000000) ? '正在创建课程...' : '正在保存课程...')
                      : (courseList.some(c => c.id > 1000000) ? '创建课程' : '保存课程')
              }
            >
              {uploadingFiles.size > 0
                ? '上传中...'
                : isCreating
                  ? (courseList.some(c => c.id > 1000000) ? '正在创建...' : '正在保存...')
                  : (courseList.some(c => c.id > 1000000) ? '创建课程' : '保存课程')
              }
            </button>
          </div>
        </div>
      </div>

      {/* 删除确认弹窗 */}
      {deleteConfirmVisible && (
        <div className="modal-overlay" onClick={cancelDelete}>
          <div className="delete-confirm-modal" onClick={(e) => e.stopPropagation()}>
            <div className="delete-confirm-header">
              <h3>确认删除</h3>
              <button
                onClick={cancelDelete}
                className="close-btn"
                disabled={isDeleting}
              >
                <X className="w-4 h-4" />
              </button>
            </div>
            <div className="delete-confirm-content">
              <p>
                {isDeleting
                  ? '正在删除课程，请稍候...'
                  : '确定要删除这个课程吗？删除后无法恢复。'
                }
              </p>
              {isDeleting && (
                <div className="delete-loading">
                  <div className="loading-spinner"></div>
                </div>
              )}
            </div>
            <div className="delete-confirm-footer">
              <button
                onClick={cancelDelete}
                className="cancel-btn"
                disabled={isDeleting}
              >
                取消
              </button>
              <button
                onClick={confirmDeleteCourse}
                className="confirm-btn"
                disabled={isDeleting}
              >
                {isDeleting ? '正在删除...' : '确认删除'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 模板选择弹窗 */}
      <TemplatePickerModal
        isOpen={isTemplatePickerOpen}
        onClose={() => setIsTemplatePickerOpen(false)}
        onTemplateSelect={handleTemplateSelect}
      />
    </div>
  );
};

export default CourseListEditModal;
