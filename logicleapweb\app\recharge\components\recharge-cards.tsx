'use client'

import { useState } from 'react';
import { RechargeOption } from '../types';
import { getValidityLabel, getValidityTagClass } from '../utils/dataTransform';

interface RechargeCardsProps {
  packages?: RechargeOption[]; // 可选的套餐数据，如果不传则使用默认数据
  onCardClick: (plan: RechargeOption) => void;
}

// 图标映射函数 - 支持emoji图标显示
const getIconComponent = (iconType: string) => {
  // 直接显示emoji或其他字符作为图标
  return <span className="emoji-icon">{iconType}</span>;
};

// 价格格式化函数
const formatPrice = (price: number): string => {
  if (price < 0.01) {
    return price.toFixed(4);
  } else if (price < 1) {
    return price.toFixed(3);
  } else {
    return price.toFixed(2);
  }
};

const rechargeOptions: RechargeOption[] = [
  {
    id: "test",
    title: "测试充值",
    subtitle: "开发测试专用",
    amount: 0.01,
    bonus: 10,
    icon: "🧪",
    features: ["立即到账", "安全支付", "测试专用", "赠送10积分"],
    detailedFeatures: [
      { name: "充值金额", value: "¥0.01", icon: "💳" },
      { name: "赠送积分", value: "10积分", icon: "⚡" },
      { name: "到账时间", value: "即时到账", icon: "⏰" },
      { name: "安全保障", value: "银行级加密", icon: "🛡️" },
      { name: "客服支持", value: "24小时在线", icon: "🎧" },
    ],
    popular: true,
    gradient: "from-green-500 via-emerald-600 to-teal-700",
    bgGradient: "from-green-50 to-emerald-100",
  },
  {
    id: "basic",
    title: "基础充值",
    subtitle: "适合轻度用户",
    amount: 100,
    bonus: 10,
    icon: "⚡",
    features: ["立即到账", "安全支付", "24小时客服"],
    detailedFeatures: [
      { name: "充值金额", value: "¥100", icon: "💳" },
      { name: "到账时间", value: "即时到账", icon: "⏰" },
      { name: "安全保障", value: "银行级加密", icon: "🛡️" },
    ],
    popular: false,
    gradient: "from-blue-500 via-blue-600 to-indigo-700",
    bgGradient: "from-blue-50 to-indigo-100",
  },
  {
    id: "standard",
    title: "标准充值",
    subtitle: "性价比之选",
    amount: 350,
    originalAmount: 500,
    bonus: 50,
    icon: "🎁",
    features: ["立即到账", "安全支付", "24小时客服", "赠送50积分", "专属客服"],
    detailedFeatures: [
      { name: "充值金额", value: "¥350", icon: "💳" },
      { name: "赠送积分", value: "50积分", icon: "⚡" },
      { name: "到账时间", value: "即时到账", icon: "⏰" },
      { name: "专属客服", value: "优先响应", icon: "🎧" },
      { name: "安全保障", value: "银行级加密", icon: "🛡️" },
    ],
    popular: false,
    gradient: "from-purple-500 via-purple-600 to-pink-600",
    bgGradient: "from-purple-50 to-pink-100",
  },
  {
    id: "premium",
    title: "超值充值",
    subtitle: "最受欢迎",
    amount: 700,
    originalAmount: 1000,
    bonus: 150,
    icon: "👑",
    features: ["立即到账", "安全支付", "24小时客服", "赠送150积分", "专属客服", "VIP标识"],
    detailedFeatures: [
      { name: "充值金额", value: "¥700", icon: "💳" },
      { name: "赠送积分", value: "150积分", icon: "⚡" },
      { name: "VIP特权", value: "专属标识", icon: "👑" },
      { name: "到账时间", value: "即时到账", icon: "⏰" },
      { name: "专属客服", value: "1对1服务", icon: "🎧" },
      { name: "安全保障", value: "银行级加密", icon: "🛡️" },
    ],
    popular: true,
    gradient: "from-orange-500 via-red-500 to-pink-600",
    bgGradient: "from-orange-50 to-red-100",
  },
  {
    id: "ultimate",
    title: "至尊充值",
    subtitle: "尊享体验",
    amount: 2000,
    bonus: 400,
    icon: "⭐",
    features: ["立即到账", "安全支付", "24小时客服", "赠送400积分", "专属客服", "VIP标识", "专属礼品"],
    detailedFeatures: [
      { name: "充值金额", value: "¥2000", icon: "💳" },
      { name: "赠送积分", value: "400积分", icon: "⚡" },
      { name: "专属礼品", value: "限量周边", icon: "⭐" },
      { name: "VIP特权", value: "至尊标识", icon: "👑" },
      { name: "到账时间", value: "即时到账", icon: "⏰" },
      { name: "专属客服", value: "专人服务", icon: "🎧" },
      { name: "安全保障", value: "银行级加密", icon: "🛡️" },
    ],
    popular: false,
    gradient: "from-yellow-500 via-orange-500 to-red-600",
    bgGradient: "from-yellow-50 to-orange-100",
  },
];

export default function RechargeCards({ packages, onCardClick }: RechargeCardsProps) {
  const [hoveredCard, setHoveredCard] = useState<string | null>(null);

  // 使用传入的packages数据，如果没有则使用默认数据
  const displayOptions = packages || rechargeOptions;

  return (
    <div className="modern-cards-container">
      {displayOptions.map((option, index) => (
        <div
          key={option.id}
          className={`modern-recharge-card ${hoveredCard === option.id ? 'hovered' : ''}`}
          onClick={() => onCardClick(option)}
          onMouseEnter={() => setHoveredCard(option.id)}
          onMouseLeave={() => setHoveredCard(null)}
          style={{ animationDelay: `${index * 0.1}s` }}
        >
          {/* 卡片背景装饰 */}
          <div className="card-decoration">
            <div className="decoration-circle decoration-circle-1"></div>
            <div className="decoration-circle decoration-circle-2"></div>
            <div className="decoration-line"></div>
          </div>

          {/* 有效期标签 - 右上角 */}
          {option.validityDays && (
            <div className={`validity-tag ${getValidityTagClass(option.validityDays)}`}>
              {getValidityLabel(option.validityDays)}
            </div>
          )}

          {/* 卡片主体内容 */}
          <div className="card-main-content">
            {/* 标题区域 - 参考布局：简洁的图标+标题 */}
            <div className="card-header">
              <div className="header-icon">
                <div className="icon-wrapper">
                  {getIconComponent(option.icon)}
                </div>
              </div>
              <h3 className="card-title">{option.title}</h3>
            </div>

            {/* 主要价格 - 参考布局：大字号突出显示 */}
            <div className="main-price">
              <div className="price-display">
                <div className="main-price-section">
                  <span className="currency">¥</span>
                  <span className="amount">{option.amount % 1 === 0 ? Math.floor(option.amount) : option.amount}</span>
                </div>
                {(() => {
                  const discountRate = option.discountRate ? parseFloat(String(option.discountRate)) : 0;
                  const originalAmount = option.originalAmount ? parseFloat(String(option.originalAmount)) : 0;

                  const hasDiscount = discountRate > 0;
                  const hasOriginalAmount = originalAmount > 0;

                  console.log('套餐数据调试:', {
                    title: option.title,
                    amount: option.amount,
                    originalAmount: option.originalAmount,
                    discountRate: option.discountRate,
                    hasOriginal: hasOriginalAmount,
                    hasDiscount: hasDiscount,
                    shouldShowDiscount: hasDiscount || hasOriginalAmount
                  });

                  return (hasDiscount || hasOriginalAmount) && (
                    <div className="discount-section">
                      {hasDiscount && (
                        <div className="discount-tag">
                          {parseFloat((discountRate * 10).toFixed(2))}折
                        </div>
                      )}
                      {hasOriginalAmount && (
                        <div className="original-amount">¥{originalAmount % 1 === 0 ? Math.floor(originalAmount) : originalAmount}</div>
                      )}
                    </div>
                  );
                })()}
              </div>
            </div>

            {/* 积分信息 - 简化设计 */}
            {option.bonus > 0 && (
              <div className="points-info">
                <span className="points-amount">{Math.floor(option.bonus)}</span>
                <span className="points-label">积分</span>
              </div>
            )}

            {/* 副标题信息 - 显示每天价格和每积分价格 */}
            <div className="price-subtitle">
              {option.bonus > 0 && option.validityDays && (
                <span>
                  每天¥{formatPrice(option.amount / option.validityDays)} ·
                  ¥{formatPrice(option.amount / option.bonus)}/积分
                </span>
              )}
            </div>

            {/* 特性列表 - 只在有特性时显示 */}
            {option.detailedFeatures.length > 0 && (
              <div className="features-section">
                <div className="features-list">
                  {option.detailedFeatures.slice(0, 3).map((feature, featureIndex) => (
                    <div key={featureIndex} className="feature-row">
                      <div className="feature-left">
                        <div className="feature-icon-wrapper">
                          <span className="feature-emoji">{feature.icon}</span>
                        </div>
                        <span className="feature-label">{feature.name}</span>
                      </div>
                      <span className="feature-detail">{feature.value}</span>
                    </div>
                  ))}
                </div>

                {/* 剩余特性提示 */}
                {option.detailedFeatures.length > 3 ? (
                  <div className="remaining-features">
                    还有 {option.detailedFeatures.length - 3} 条套餐信息
                  </div>
                ) : option.detailedFeatures.length === 3 ? (
                  <div className="view-details">
                    点击查看详细
                  </div>
                ) : null}
              </div>
            )}

            {/* 底部操作按钮 */}
            <div className="card-action">
              <button className="select-button">
                <span>选择套餐</span>
                <svg className="arrow-icon" viewBox="0 0 24 24" fill="none">
                  <path d="M5 12h14M12 5l7 7-7 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </button>
            </div>
          </div>

          {/* 悬停效果 */}
          <div className="hover-overlay"></div>
        </div>
      ))}
    </div>
  );
}
