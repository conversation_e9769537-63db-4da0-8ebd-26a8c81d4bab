export declare const databaseMonitorConfig: {
    database: {
        enableDatabaseMonitoring: boolean;
        lightweightMode: boolean;
        enableSlowQueryLogging: boolean;
        enableQueryMetrics: boolean;
        enableStackTrace: boolean;
        maxSlowQueryRecords: number;
        queryTimeoutMs: number;
        asyncSlowQueryProcessing: boolean;
        alerts: {
            slowQueryAlertThreshold: number;
            criticalSlowQueryThreshold: number;
            enableEmailAlert: boolean;
            enableDingTalkAlert: boolean;
            email: {
                recipients: string[];
                subject: string;
            };
            dingTalk: {
                webhook: string;
                secret: string;
            };
        };
    };
};
