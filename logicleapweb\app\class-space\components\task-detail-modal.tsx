'use client'

import React, { useState, useEffect } from 'react';
import { Modal, Button, Tag, Progress, Space, Radio, Empty, Card, Spin, Checkbox, Carousel } from 'antd';
import { InfoCircleOutlined, DownloadOutlined, FileOutlined, FileImageOutlined, FilePdfOutlined, FileTextOutlined, EyeOutlined } from '@ant-design/icons';
import { Task, TaskStatus } from '@/lib/api/task';
import { ExtendedTask, TaskAssignment } from '../types';
import { worksApi } from '@/lib/api/works';
import dayjs from 'dayjs';
import taskApi from '@/lib/api/task';
import { getUserRoleInfo, addUserJoinRole } from '@/lib/api/role';
import { useRouter } from 'next/navigation';
import { GetNotification } from 'logic-common/dist/components/Notification';
import { FileText, Eye, Download } from 'lucide-react';
import { ImageLabel } from './class-projects';
import { CustomArrow } from './class-projects';
import { viewWork } from '../../../lib/utils/view-work-modal';

interface TaskDetailModalProps {
  visible: boolean;
  task: ExtendedTask | null;
  loading: boolean;
  onClose: () => void;
  onStartTask: () => void;
  onTaskSubmitted: () => void;
  onOpenSubmitModal: () => void;
}

export default function TaskDetailModal({
  visible,
  task,
  loading,
  onClose,
  onStartTask,
  onTaskSubmitted,
  onOpenSubmitModal
}: TaskDetailModalProps) {
  const router = useRouter();
  const [showStartOptions, setShowStartOptions] = useState(false);
  const [startOption, setStartOption] = useState<'new' | 'existing' | 'reference' | 'multipleReference'>('new');
  const [showWorkSelector, setShowWorkSelector] = useState(false);
  const [works, setWorks] = useState<any[]>([]);
  const [selectedWorkId, setSelectedWorkId] = useState<number | null>(null);
  const [selectedReferenceWorkId, setSelectedReferenceWorkId] = useState<number | null>(null);
  const [showReferenceSelector, setShowReferenceSelector] = useState(false);
  const notification = GetNotification();

  // 检查是否有参考作品并设置默认选项
  useEffect(() => {
    if (task) {
      const hasReferenceWork = !!(
        (task as any)?.referenceWork?.id || 
        (task as any)?.workId || 
        (task as any)?.workIdsStr ||
        (task as any)?.referenceWorks?.length
      );
      
      if (hasReferenceWork) {
        setStartOption('reference');
      }
    }
  }, [task]);

  // 判断任务是否过期
  const isTaskExpired = (endDate: Date | string) => {
    return new Date() > new Date(endDate);
  };

  // 判断任务是否已开始
  const isTaskStarted = (startDate?: string) => {
    if (!startDate) return true;
    return new Date() > new Date(startDate);
  };

  // 计算剩余时间
  const getRemainingTime = (startDate: string | Date | undefined, endDate: string | Date) => {
    const now = new Date();
    const start = startDate ? new Date(startDate) : now;
    const end = new Date(endDate);

    if (startDate && now < start) {
      const totalDiff = end.getTime() - start.getTime();
      const days = Math.floor(totalDiff / (1000 * 60 * 60 * 24));
      const hours = Math.floor((totalDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      const minutes = Math.floor((totalDiff % (1000 * 60 * 60)) / (1000 * 60));

      let result = '总时长: ';
      if (days > 0) result += `${days}天`;
      if (hours > 0) result += `${hours}小时`;
      if (minutes > 0) result += `${minutes}分钟`;
      return result || '不到1分钟';
    }

    const remainingDiff = end.getTime() - now.getTime();
    if (remainingDiff <= 0) return '已过期';

    const days = Math.floor(remainingDiff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((remainingDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((remainingDiff % (1000 * 60 * 60)) / (1000 * 60));

    let result = `剩余: ${days}天 ${hours}小时 ${minutes}分钟`;
    return result || '不到1分钟';
  };

  // 获取任务状态
  const getTaskStatus = (task: ExtendedTask, assignment?: TaskAssignment) => {
    if (isTaskExpired(task.endDate)) {
      return { color: 'error', text: '已过期' };
    }

    if (assignment) {
      if (assignment.taskStatus === TaskStatus.COMPLETED) {
        return { color: 'success', text: '已完成' };
      }
      if (assignment.taskStatus === TaskStatus.RE_DO) {
        return { color: 'warning', text: '需要修改' };
      }
    }

    if (isTaskStarted(task.startDate?.toString())) {
      return { color: 'processing', text: '进行中' };
    }

    return { color: 'purple', text: '未开始' };
  };

  // 获取作品列表
  const fetchWorks = async () => {
    try {
      const { data: response } = await worksApi.getList({
        type: 1,
        page: 1,
        size: 100
      });

      if (response.code === 200) {
        setWorks(response.data || []);
      }
    } catch (error) {
      console.error('获取作品列表失败:', error);
      notification.error('获取作品列表失败');
    }
  };

  // 查看作品
  const handleViewWork = async (workId: number) => {
    try {
      // 确保当前任务存在并且是当前登录用户的任务
      if (!task || !task.assignments || task.assignments.length === 0) {
        notification.error('无法查看作品：任务信息不完整');
        return;
      }

      // 获取当前用户ID
      const userData = localStorage.getItem('user');
      const currentUser = userData ? JSON.parse(userData) : null;
      const currentUserId = currentUser?.userId;

      // 检查任务是否属于当前用户
      const userAssignment = task.assignments.find(assignment => assignment.studentId === currentUserId);
      if (!userAssignment) {
        notification.error('无法查看作品：无权限');
        return;
      }

      // 确保要查看的作品ID与当前用户的提交相符
      if (userAssignment.workId !== workId) {
        notification.error('无法查看作品：作品ID不匹配');
        return;
      }

      const loadingNotification = notification.loading('正在加载作品...');
      const response = await taskApi.getStudentWork(userAssignment.id);

      if (response.data.code === 200 && response.data.data.workId) {
        const workResponse = await worksApi.getDetail(response.data.data.workId);

        if (workResponse?.data.code === 200 && workResponse.data.data?.content) {
          loadingNotification?.close();
          viewWork({
            content: workResponse.data.data.content,
            workId: response.data.data.workId
          });
        } else {
          loadingNotification?.close();
          notification.error('获取作品详情失败');
        }
      } else {
        loadingNotification?.close();
        notification.warning('该学生尚未提交作品');
      }
    } catch (error) {
      console.error('获取作品失败:', error);
      notification.error('获取作品失败');
    }
  };

  // 在组件内添加附件处理函数
  const handlePreviewAttachment = (url: string) => {
    const fileExt = url.split('.').pop()?.toLowerCase() || '';

    if (['jpg', 'jpeg', 'png', 'gif'].includes(fileExt)) {
      // 图片预览
      Modal.info({
        title: '预览附件',
        width: 800,
        content: (
          <div className="flex justify-center">
            <img src={url} alt="附件预览" className="max-w-full max-h-[600px] object-contain" />
          </div>
        ),
        okText: '关闭'
      });
    } else if (fileExt === 'pdf') {
      // PDF预览 - 在新窗口打开
      window.open(url, '_blank');
    } else if (fileExt === 'txt') {
      // 文本文件预览
      fetch(url)
        .then(response => response.text())
        .then(content => {
          Modal.info({
            title: '预览附件',
            width: 800,
            content: (
              <pre className="whitespace-pre-wrap max-h-[600px] overflow-y-auto">
                {content}
              </pre>
            ),
            okText: '关闭'
          });
        })
        .catch(error => {
          notification.error('预览文件失败');
          console.error('预览文件失败:', error);
        });
    } else {
      // 其他类型文件直接下载
      const link = document.createElement('a');
      link.href = url;
      link.download = url.split('/').pop() || 'download';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  // 修改handleStartTask函数为简单的显示选项
  const handleStartTask = () => {
    setShowStartOptions(true);
  };

  // 修改handleConfirmStart函数
  const handleConfirmStart = async () => {
    try {
      // 先检查模板
      if (task?.templateId && task.assignments?.[0]?.studentId) {
        // 获取用户当前的角色信息
        const roleInfo = await getUserRoleInfo(task.assignments[0].studentId);

        // 只有当用户没有模板ID或模板ID与任务要求不同时才更新
        if (!roleInfo?.data?.data?.templateId || roleInfo.data.data.templateId !== task.templateId) {
          localStorage.setItem('taskTemplateId', String(task.templateId));

          await addUserJoinRole({
            userId: task.assignments[0].studentId,
            roleId: 1,
            templateId: task.templateId,
          });
        }
      }

      // 获取完整的用户状态
      const userData = localStorage.getItem('user');
      const fullUserState = userData ? JSON.parse(userData) : null;

      if (startOption === 'new') {
        // 新建作品
        viewWork({
          content: '', // 新建作品，content 为空
          workId: undefined,
          userId: task?.assignments?.[0]?.studentId,
          isNew: true,
          userState: fullUserState
        });
        onClose();
        setShowStartOptions(false);
      } else if (startOption === 'existing') {
        setShowWorkSelector(true);
      } else if (startOption === 'reference') {
        // 判断是单个参考作品还是多个参考作品
        if ((task as any)?.workIdsStr && (task as any).workIdsStr.includes(',')) {
          // 有多个参考作品，打开参考作品选择弹窗
          setShowReferenceSelector(true);
        } else {
          // 单个参考作品，直接打开
          handleOpenReferenceWork();
        }
      } else if (startOption === 'multipleReference') {
        // 这个选项保留，但主要功能已经合并到 reference 选项中
        handleOpenMultipleReferenceWorks();
      }
    } catch (error) {
      console.error('操作失败:', error);
      notification.error('操作失败');
    }
  };

  // 修改handleSelectWork函数
  const handleSelectWork = async () => {
    if (!selectedWorkId) {
      notification.warning('请选择一个作品');
      return;
    }

    try {
      // 先检查模板
      if (task?.templateId && task.assignments?.[0]?.studentId) {
        // 获取用户当前的角色信息
        const roleInfo = await getUserRoleInfo(task.assignments[0].studentId);

        // 只有当用户没有模板ID或模板ID与任务要求不同时才更新
        if (!roleInfo?.data?.data?.templateId || roleInfo.data.data.templateId !== task.templateId) {
          localStorage.setItem('taskTemplateId', String(task.templateId));

          await addUserJoinRole({
            userId: task.assignments[0].studentId,
            roleId: 1,
            templateId: task.templateId,
          });
        }
      }

      // 获取作品内容
      const response = await worksApi.getDetail(selectedWorkId);

      if (response?.data.code === 200) {
        const content = response?.data.data.content;
        if (!content) {
          console.error('作品内容为空');
          notification.error('作品内容获取失败');
          return;
        }

        // 获取完整的用户状态
        const userData = localStorage.getItem('user');
        const fullUserState = userData ? JSON.parse(userData) : null;

        // 使用 viewWork 打开作品
        viewWork({
          content: content,
          workId: selectedWorkId,
          userId: task?.assignments?.[0]?.studentId,
          userState: fullUserState
        });

        onClose();
        setShowWorkSelector(false);
      } else {
        notification.error(response?.data.message || '获取作品详情失败');
      }
    } catch (error) {
      console.error('操作失败:', error);
      notification.error('操作失败');
    }
  };

  // 添加从参考作品打开的处理函数
  const handleOpenReferenceWork = async () => {
    // 检查是否存在参考作品
    if (!(task as any)?.referenceWork?.id && !(task as any)?.workId && !(task as any)?.workIdsStr) {
      notification.warning('该任务没有参考作品');
      return;
    }

    try {
      // 获取作品ID
      let workId = (task as any)?.referenceWork?.id || (task as any)?.workId;
      
      // 如果有workIdsStr，尝试获取第一个ID，判断是否合法，如果合法，则使用第一个ID，
      // 但这个是备用的，因为参考作品可能不止一个
      if (!workId && (task as any)?.workIdsStr) {
        const workIds = (task as any).workIdsStr.split(',');
        if (workIds.length > 0) {
          workId = parseInt(workIds[0], 10);
        }
      }

      if (!workId) {
        console.error('无法获取参考作品ID:', task);
        notification.warning('无法获取参考作品ID');
        return;
      }

      console.log('尝试打开参考作品，ID:', workId);
      const loadingNotification = notification.loading('正在加载参考作品...');
      
      // 获取作品内容
      const response = await worksApi.getDetail(workId);
      console.log('参考作品详情响应:', response);

      if (response?.data.code === 200) {
        const content = response?.data.data.content;
        if (!content) {
          console.error('参考作品内容为空, 响应数据:', response.data);
          loadingNotification?.close();
          notification.error('参考作品内容获取失败');
          return;
        }

        // 获取完整的用户状态
        const userData = localStorage.getItem('user');
        const currentUser = userData ? JSON.parse(userData) : null;
        console.log('当前用户信息:', currentUser);

        // 使用 viewWork 打开作品
        loadingNotification?.close();
        viewWork({
          content: content,
          workId: workId,
          userId: task?.assignments?.[0]?.studentId || currentUser?.userId,
          userState: currentUser,
          isReferenceWork: true // 标记为参考作品，可能需要只读模式
        });

        onClose();
        setShowStartOptions(false);
      } else {
        console.error('获取参考作品详情失败:', response?.data);
        loadingNotification?.close();
        notification.error(response?.data.message || '获取参考作品详情失败');
      }
    } catch (error) {
      console.error('打开参考作品失败，详细错误:', error);
      notification.error('打开参考作品失败，请稍后重试');
    }
  };

  // 修改handleOpenMultipleReferenceWorks函数，完全移除参考作品检查
  const handleOpenMultipleReferenceWorks = async () => {
    if (!selectedReferenceWorkId) {
      console.error('未选择参考作品ID');
      notification.warning('请先选择一个参考作品');
      return;
    }

    try {
      console.log('尝试打开多个参考作品中的一个，ID:', selectedReferenceWorkId);
      const loadingNotification = notification.loading('正在加载参考作品...');
      
      // 获取作品内容
      const response = await worksApi.getDetail(selectedReferenceWorkId);
      console.log('参考作品详情响应:', response);

      if (response?.data.code === 200) {
        const content = response?.data.data.content;
        if (!content) {
          console.error('参考作品内容为空, 响应数据:', response.data);
          loadingNotification?.close();
          notification.error('参考作品内容获取失败');
          return;
        }

        // 获取完整的用户状态
        const userData = localStorage.getItem('user');
        const currentUser = userData ? JSON.parse(userData) : null;
        console.log('当前用户信息:', currentUser);

        // 使用 viewWork 打开作品
        loadingNotification?.close();
        viewWork({
          content: content,
          workId: selectedReferenceWorkId,
          userId: task?.assignments?.[0]?.studentId || currentUser?.userId,
          userState: currentUser,
          isReferenceWork: true // 标记为参考作品，可能需要只读模式
        });

        onClose();
        setShowReferenceSelector(false);
        setShowStartOptions(false);
      } else {
        console.error('获取参考作品详情失败:', response?.data);
        loadingNotification?.close();
        notification.error(response?.data.message || '获取参考作品详情失败');
      }
    } catch (error) {
      console.error('打开参考作品失败，详细错误:', error);
      notification.error('打开参考作品失败，请稍后重试');
    }
  };

  return (
    <>
      <Modal
        title={
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <span>任务详情</span>
              {task && task.assignments?.[0] && (
                <Tag className={`rounded-full text-sm border-none ${getTaskStatus(task, task.assignments[0]).color === 'success' ? 'bg-green-50 text-green-600' :
                  getTaskStatus(task, task.assignments[0]).color === 'error' ? 'bg-red-50 text-red-600' :
                    getTaskStatus(task, task.assignments[0]).color === 'warning' ? 'bg-yellow-50 text-yellow-600' :
                      getTaskStatus(task, task.assignments[0]).color === 'processing' ? 'bg-blue-50 text-blue-600' :
                        'bg-purple-50 text-purple-600'
                  }`}>
                  <div className="flex items-center gap-2">
                    <div className={`w-2 h-2 rounded-full ${getTaskStatus(task, task.assignments[0]).color === 'success' ? 'bg-green-600' :
                      getTaskStatus(task, task.assignments[0]).color === 'error' ? 'bg-red-600' :
                        getTaskStatus(task, task.assignments[0]).color === 'warning' ? 'bg-yellow-600' :
                          getTaskStatus(task, task.assignments[0]).color === 'processing' ? 'bg-blue-600' :
                            'bg-purple-600'
                      }`}></div>
                    {getTaskStatus(task, task.assignments[0]).text}
                  </div>
                </Tag>
              )}
            </div>
          </div>
        }
        open={visible}
        onCancel={onClose}
        centered
        footer={
          <div className="w-full flex justify-end">
            {(() => {
              // 获取当前用户ID
              const userData = localStorage.getItem('user');
              const currentUser = userData ? JSON.parse(userData) : null;
              const currentUserId = currentUser?.userId;
              console.log(task?.assignments);

              // 检查任务是否属于当前用户
              const userAssignment = task?.assignments?.find(assignment =>
                assignment.studentId === currentUserId
              );

              if (!userAssignment) {
                return null; // 如果任务不属于当前用户，不显示任何按钮
              }


              // 准备显示的按钮
              const buttons = [];

              // 如果有作品且状态为已完成，显示查看作品按钮
              if (userAssignment.workId && userAssignment.taskStatus === TaskStatus.COMPLETED) {
                buttons.push(
                  <Button
                    key="view"
                    type="primary"
                    onClick={() => handleViewWork(userAssignment.workId!)}
                    className="rounded-full ml-2"
                  >
                    查看作品
                  </Button>
                );
              }

              // 如果任务未过期且已开始且未被评分，显示提交和开始按钮
              if (task?.endDate && !isTaskExpired(task.endDate) &&
                isTaskStarted(task.startDate?.toString()) &&
                userAssignment.taskScore === null) {

                buttons.push(
                  <Button
                    key="submit"
                    onClick={onOpenSubmitModal}
                    className="rounded-full"
                  >
                    {userAssignment.taskStatus === TaskStatus.COMPLETED ? '重新提交' : '提交作品'}
                  </Button>
                );



                buttons.push(
                  <Button
                    key="start"
                    type="primary"
                    onClick={handleStartTask}
                    className="ml-2 rounded-full"
                  >
                    开始任务
                  </Button>
                );
              }

              return buttons;
            })()}
          </div>
        }
        width={800}
        modalRender={(modal) => (
          <div style={{
            margin: '5vh 0',
            display: 'flex',
            flexDirection: 'column',
            height: 'fit-content',
            maxHeight: '90vh'
          }}>
            {modal}
          </div>
        )}
        style={{
          maxWidth: '90vw',
          top: 0,
          paddingBottom: 0
        }}
        className="task-detail-modal"
        styles={{
          mask: {
            backgroundColor: 'rgba(0, 0, 0, 0.15)',
            backdropFilter: 'blur(6px)'
          },
          content: {
            borderRadius: '18px',
            padding: '0',
            overflow: 'hidden',
            boxShadow: '0 12px 40px rgba(99, 102, 241, 0.15), 0 4px 12px rgba(161, 116, 245, 0.05)',
            maxHeight: '90vh',
            minHeight: '400px',
            minWidth: '600px',
            display: 'flex',
            flexDirection: 'column',
            margin: '5vh auto',
            background: '#ffffff',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(167, 207, 253, 0.3)'
          },
          header: {
            padding: '20px 24px',
            marginBottom: '0',
            background: '#ffffff',
            borderBottom: '1px solid rgba(147, 197, 253, 0.2)',
            flex: 'none',
            fontSize: '16px',
            fontWeight: '500',
            color: '#4338ca'
          },
          body: {
            padding: '24px',
            flex: '1 1 auto',
            minHeight: '0',
            overflow: 'auto',
            background: '#ffffff'
          },
          footer: {
            borderTop: '1px solid rgba(147, 197, 253, 0.2)',
            padding: '16px 24px',
            background: '#ffffff'
          }
        }}
        styles={{
          body: {
            padding: '24px',
            maxHeight: 'calc(90vh - 110px)',
            overflow: 'auto'
          }
        }}
      >
        {loading ? (
          <div className="flex justify-center py-8">
            <Spin />
          </div>
        ) : task ? (
          <div className="space-y-4">
            {/* 任务详情内容 */}
            <div className="flex justify-between items-center mb-4">
              <div className="flex items-center gap-2">
                <h2 className="text-xl font-bold">{task.taskName}</h2>
              </div>
            </div>

            {task.taskDescription && (
              <div className="bg-white p-4 rounded-xl mb-4 border border-blue-100 shadow-sm">
                <h3 className="font-semibold mb-2 text-indigo-700">任务描述</h3>
                <p className="text-gray-600 whitespace-pre-wrap">{task.taskDescription}</p>
              </div>
            )}

            <div className="bg-white p-4 rounded-xl mb-4 border border-amber-100 shadow-sm">
              <div className="flex flex-row items-start justify-between">
                <div className="flex-1">
                  <h3 className="font-semibold mb-2 text-amber-700">开始时间</h3>
                  <p>{task.startDate ? new Date(task.startDate).toLocaleString() : '立即开始'}</p>
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold mb-2 text-amber-700">截止日期</h3>
                  <p>{new Date(task.endDate).toLocaleString()}</p>
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold mb-2 text-amber-700">剩余时间</h3>
                  <p>{getRemainingTime(task.startDate, task.endDate)}</p>
                </div>
              </div>
            </div>

            {task.taskContent && (
              <div className="bg-white p-4 rounded-xl mb-4 border border-emerald-100 shadow-sm">
                <h3 className="font-semibold mb-2 text-emerald-700">任务内容</h3>
                <div className="text-gray-600 whitespace-pre-wrap">{task.taskContent}</div>
              </div>
            )}

            {/* 任务附件 */}
            {task.attachments && task.attachments.length > 0 && (
              <div className="bg-white p-4 rounded-xl mb-4 border border-pink-100 shadow-sm">
                <h3 className="font-semibold mb-2 text-rose-700">任务附件</h3>
                <div className="space-y-2">
                  {Array.isArray(task.attachments) ? (
                    task.attachments.map((attachment, index) => {
                      const fileName = typeof attachment === 'string'
                        ? attachment.split('/').pop()
                        : attachment.name || `附件${index + 1}`;
                      const url = typeof attachment === 'string' ? attachment : attachment.url;

                      return (
                        <div key={index} className="flex items-center justify-between p-3 bg-white rounded-lg border border-pink-100 hover:shadow-md transition-all hover:border-pink-200">
                          <div className="flex items-center gap-2">
                            <FileText className="text-pink-500" />
                            <span className="text-sm">{fileName}</span>
                          </div>
                          <div className="flex gap-2">
                            <Button
                              type="text"
                              icon={<Download />}
                              className="rounded-full hover:bg-pink-50 hover:text-pink-500"
                              onClick={() => {
                                const link = document.createElement('a');
                                link.href = url;
                                link.download = fileName;
                                document.body.appendChild(link);
                                link.click();
                                document.body.removeChild(link);
                              }}
                            >
                              下载
                            </Button>
                          </div>
                        </div>
                      );
                    })
                  ) : (
                    task.attachments.trim() && task.attachments.split(/[,;\s]+/).filter(url => url.trim()).map((url, index) => {
                      const fileName = url.split('/').pop() || `附件${index + 1}`;

                      return (
                        <div key={url} className="flex items-center justify-between p-3 bg-white rounded-lg border border-pink-100 hover:shadow-md transition-all hover:border-pink-200">
                          <div className="flex items-center gap-2">
                            <FileText className="text-pink-500" />
                            <span className="text-sm">{fileName}</span>
                          </div>
                          <div className="flex gap-2">
                            <Button
                              type="text"
                              icon={<Download />}
                              className="rounded-full hover:bg-pink-50 hover:text-pink-500"
                              onClick={() => {
                                const link = document.createElement('a');
                                link.href = url;
                                link.download = fileName;
                                document.body.appendChild(link);
                                link.click();
                                document.body.removeChild(link);
                              }}
                            >
                              下载
                            </Button>
                          </div>
                        </div>
                      );
                    })
                  )}
                </div>
              </div>
            )}

            {/* 参考作品区域 */}
            {((task as any)?.referenceWork || ((task as any)?.referenceWorks && (task as any).referenceWorks.length > 0)) && (
              <div className="bg-white p-4 rounded-xl mb-4 border border-indigo-100 shadow-sm">
                <h3 className="font-semibold mb-2 text-indigo-700">任务参考作品</h3>
                <div className="space-y-2">
                  {/* 单个参考作品 */}
                  {(task as any)?.referenceWork && !(task as any)?.referenceWorks && (
                    <div 
                      className="flex items-center p-3 bg-white rounded-lg border border-indigo-100 hover:shadow-md transition-all hover:border-indigo-300 cursor-pointer relative group"
                      onClick={handleOpenReferenceWork}
                    >
                      <div className="flex items-center gap-2 w-full">
                        {(task as any).referenceWork.coverImage && (
                          <img 
                            src={(task as any).referenceWork.coverImage} 
                            alt="参考作品" 
                            className="w-12 h-12 object-cover rounded"
                          />
                        )}
                        <div className="flex-grow">
                          <span className="text-sm font-medium">{(task as any).referenceWork.title || '参考作品'}</span>
                          {(task as any).referenceWork.description && (
                            <p className="text-xs text-gray-500 mt-1">{(task as any).referenceWork.description}</p>
                          )}
                        </div>
                        <div className="absolute inset-0 bg-indigo-500 bg-opacity-0 group-hover:bg-opacity-5 transition-all rounded-lg flex items-center justify-center">
                          <div className="opacity-0 group-hover:opacity-100 transition-opacity bg-indigo-600 text-white px-3 py-1 rounded-full text-xs font-medium shadow-md">
                            点击开始创作
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* 多个参考作品 */}
                  {(task as any)?.referenceWorks && (task as any).referenceWorks.length > 0 && (
                    <div className="overflow-x-auto pb-2">
                      <div className="flex space-x-4" style={{ minWidth: 'max-content' }}>
                        {(task as any).referenceWorks.map((work: any) => (
                          <div
                            key={work.id}
                            className="p-4 rounded-xl border border-gray-200 bg-white transition-all flex-shrink-0 w-[280px] cursor-pointer hover:shadow-lg hover:border-indigo-300 relative group"
                            onClick={async () => {
                              try {
                                console.log('直接打开参考作品，ID:', work.id);
                                const loadingNotification = notification.loading('正在加载参考作品...');
                                
                                // 获取作品内容
                                const response = await worksApi.getDetail(work.id);
                                console.log('参考作品详情响应:', response);

                                if (response?.data.code === 200) {
                                  const content = response?.data.data.content;
                                  if (!content) {
                                    console.error('参考作品内容为空, 响应数据:', response.data);
                                    loadingNotification?.close();
                                    notification.error('参考作品内容获取失败');
                                    return;
                                  }

                                  // 获取完整的用户状态
                                  const userData = localStorage.getItem('user');
                                  const currentUser = userData ? JSON.parse(userData) : null;

                                  // 使用 viewWork 打开作品
                                  loadingNotification?.close();
                                  viewWork({
                                    content: content,
                                    workId: work.id,
                                    userId: task?.assignments?.[0]?.studentId || currentUser?.userId,
                                    userState: currentUser,
                                    isReferenceWork: true // 标记为参考作品，可能需要只读模式
                                  });

                                  onClose();
                                } else {
                                  console.error('获取参考作品详情失败:', response?.data);
                                  loadingNotification?.close();
                                  notification.error(response?.data.message || '获取参考作品详情失败');
                                }
                              } catch (error) {
                                console.error('打开参考作品失败，详细错误:', error);
                                notification.error('打开参考作品失败，请稍后重试');
                              }
                            }}
                          >
                            <div className="relative mb-3 h-[180px] rounded-lg overflow-hidden border border-indigo-100 shadow-sm">
                              {work.coverImage && (
                                <img
                                  src={work.coverImage}
                                  alt={`${work.title} 封面`}
                                  className="w-full h-full object-contain rounded-lg"
                                />
                              )}
                              <div className="absolute inset-0 bg-gradient-to-t from-indigo-600/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity flex items-end justify-center pb-2">
                                <div className="bg-indigo-600 text-white px-3 py-1 rounded-full text-xs font-medium shadow-md">
                                  点击开始创作
                                </div>
                              </div>
                            </div>
                            <div className="flex justify-between items-start">
                              <h4 className="font-medium text-sm group-hover:text-indigo-600 transition-colors">{work.title}</h4>
                            </div>
                            <p className="text-xs text-gray-500 mt-1">{work.description || '暂无描述'}</p>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            {task.assignments?.[0] && (task.assignments[0].taskScore !== undefined &&
              task.assignments[0].taskScore !== null || task.assignments[0].feedback) && (
                <div className="border-t border-indigo-100 pt-4 mt-4">
                  <h3 className="font-medium text-indigo-800 mb-3">任务状态</h3>
                  <div className="space-y-4 bg-white p-4 rounded-xl border border-indigo-100 shadow-sm">
                    {task.assignments[0].taskScore !== undefined &&
                      task.assignments[0].taskScore !== null && (
                        <div className="flex items-center gap-2 text-indigo-600">
                          <svg className="w-5 h-5 text-yellow-500" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2L9.19 8.63 2 9.24l5.46 4.73L5.82 21 12 17.27 18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2z" />
                          </svg>
                          <span className="font-medium">
                            评分：{task.assignments[0].taskScore}分
                          </span>
                        </div>
                      )}

                    {task.assignments[0].feedback && (
                      <div className="mt-3">
                        <p className="font-medium text-indigo-700 mb-2">教师评语</p>
                        <div className="bg-white p-3 rounded-lg border border-indigo-100 text-gray-600 hover:shadow-md transition-all">
                          {task.assignments[0].feedback}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}
          </div>
        ) : (
          <Empty description="未找到任务详情" />
        )}
      </Modal>

      {/* 添加开始任务选项对话框 */}
      <Modal
        title={<span className="text-indigo-700 font-medium">开始任务</span>}
        open={showStartOptions}
        onCancel={() => setShowStartOptions(false)}
        footer={[
          <Button key="cancel" onClick={() => setShowStartOptions(false)}>取消</Button>,
          <Button
            key="submit"
            type="primary"
            onClick={handleConfirmStart}
          >
            确认
          </Button>
        ]}
      >
        <div className="py-4">
          <p className="text-gray-600 mb-4">请选择开始任务的方式：</p>
          <Radio.Group
            value={startOption}
            onChange={e => setStartOption(e.target.value)}
          >
            <Space direction="vertical">
              <Radio value="new">
                <div>
                  <div className="font-medium text-indigo-700">新建作品</div>
                  <div className="text-sm text-gray-500 ml-6">从空白项目开始创作新作品</div>
                </div>
              </Radio>
              <Radio value="existing">
                <div>
                  <div className="font-medium text-indigo-700">从我的空间打开</div>
                  <div className="text-sm text-gray-500 ml-6">从已有的作品中继续创作</div>
                </div>
              </Radio>
              <Radio value="reference">
                <div>
                  <div className="font-medium text-indigo-700">从任务参考作品打开</div>
                  <div className="text-sm text-gray-500 ml-6">从教师提供的参考作品开始创作</div>
                </div>
              </Radio>
            </Space>
          </Radio.Group>
        </div>
      </Modal>

      {/* 添加参考作品选择弹框 */}
      <Modal
        title={<span className="text-indigo-700 font-medium">任务参考作品</span>}
        open={showReferenceSelector}
        onCancel={() => setShowReferenceSelector(false)}
        footer={[
          <Button key="cancel" onClick={() => setShowReferenceSelector(false)}>取消</Button>,
          <Button 
            key="submit" 
            type="primary" 
            disabled={!selectedReferenceWorkId} 
            onClick={handleOpenMultipleReferenceWorks}
          >
            确认
          </Button>
        ]}
        width={800}
        styles={{
          body: {
            padding: '16px',
            maxHeight: 'calc(100vh - 200px)',
            overflow: 'auto'
          }
        }}
      >
        <div className="py-2">
          <h3 className="font-medium text-lg mb-4">选择要打开的参考作品</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {(task as any)?.referenceWorks ? (
              (task as any).referenceWorks.map((work: any) => (
                <div
                  key={work.id}
                  className={`border rounded-lg p-4 transition-all cursor-pointer flex ${
                    selectedReferenceWorkId === work.id 
                      ? 'border-indigo-500 bg-indigo-50 shadow-md' 
                      : 'border-gray-200 hover:border-indigo-300 hover:shadow-md'
                  }`}
                  onClick={() => setSelectedReferenceWorkId(work.id)}
                >
                  <div className="flex-shrink-0 mr-3">
                    {work.coverImage ? (
                      <img 
                        src={work.coverImage} 
                        alt={work.title} 
                        className="w-16 h-16 object-cover rounded-md"
                      />
                    ) : (
                      <div className="w-16 h-16 bg-gray-100 rounded-md flex items-center justify-center">
                        <FileText size={24} className="text-gray-400" />
                      </div>
                    )}
                  </div>
                  <div className="flex-grow">
                    <h4 className="font-medium text-indigo-700">{work.title}</h4>
                    <p className="text-sm text-gray-500 line-clamp-2">{work.description || '暂无描述'}</p>
                  </div>
                </div>
              ))
            ) : (
              <div className="col-span-2">
                <Empty description="没有找到参考作品" />
              </div>
            )}
          </div>
        </div>
      </Modal>

      {/* 添加作品选择弹框 */}
      <Modal
        title={<span className="text-indigo-700 font-medium">选择作品</span>}
        open={showWorkSelector}
        onCancel={() => setShowWorkSelector(false)}
        onOk={handleSelectWork}
        okText="开始创作"
        cancelText="取消"
        width={800}
        styles={{
          mask: {
            backgroundColor: 'rgba(0, 0, 0, 0.15)',
            backdropFilter: 'blur(6px)'
          },
          content: {
            borderRadius: '18px',
            overflow: 'hidden',
            boxShadow: '0 12px 40px rgba(99, 102, 241, 0.15), 0 4px 12px rgba(161, 116, 245, 0.05)',
            background: '#ffffff',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(167, 207, 253, 0.3)'
          },
          header: {
            background: '#ffffff',
            borderBottom: '1px solid rgba(147, 197, 253, 0.2)'
          },
          body: {
            background: '#ffffff'
          },
          footer: {
            background: '#ffffff',
            borderTop: '1px solid rgba(147, 197, 253, 0.2)'
          }
        }}
      >
        <div className="py-4">
          <p className="text-gray-600 mb-4">请选择要继续创作的作品：</p>
          <div className="overflow-x-auto pb-4">
            <div className="flex space-x-4" style={{ minWidth: 'max-content' }}>
              {works.map(work => (
                <div
                  key={work.id}
                  className={`p-4 rounded-xl border transition-all flex-shrink-0 w-[280px] ${
                    selectedWorkId === work.id
                      ? 'border-indigo-400 bg-indigo-50 shadow-md'
                      : 'border-gray-200 hover:border-indigo-200 hover:shadow-md bg-white'
                  }`}
                  onClick={() => setSelectedWorkId(work.id)}
                >
                  <div className="relative mb-3 h-[180px] rounded-lg overflow-hidden border border-indigo-100 shadow-sm">
                    <Carousel
                      dots={{ className: 'custom-dots !z-[1]' }}
                      arrows
                      prevArrow={<CustomArrow type="prev" />}
                      nextArrow={<CustomArrow type="next" />}
                    >
                      {work.coverImage && (
                        <div className="h-[180px] relative">
                          <img
                            src={work.coverImage}
                            alt={`${work.title} 封面`}
                            className="w-full h-full object-contain rounded-lg"
                          />
                          <ImageLabel text="作品封面" />
                        </div>
                      )}
                      {work.screenShotImage && (
                        <div className="h-[180px] relative">
                          <img
                            src={work.screenShotImage}
                            alt={`${work.title} 截图`}
                            className="w-full h-full object-contain rounded-lg"
                          />
                          <ImageLabel text="作品截图" />
                        </div>
                      )}
                    </Carousel>
                  </div>

                  <div className="flex justify-between items-start">
                    <h4 className="font-medium text-sm text-indigo-700">{work.title}</h4>
                    <Tag color={work.status === 0 ? 'default' : 'success'} className="rounded-full px-3">
                      {work.status === 0 ? '未发布' : '已发布'}
                    </Tag>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">{work.description || '暂无描述'}</p>
                  <div className="text-xs text-gray-400 mt-1">
                    创建时间：{new Date(work.createTime).toLocaleString()}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </Modal>
    </>
  );
} 