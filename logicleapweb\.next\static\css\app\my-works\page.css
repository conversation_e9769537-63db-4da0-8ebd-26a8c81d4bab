/*!***********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./app/my-works/carousel.css ***!
  \***********************************************************************************************************************************************************************************************************************************************************************/
/* 轮播图容器 */
.ant-carousel {
  position: relative;
}

/* 轮播图内容 */
.ant-carousel .slick-slide {
  position: relative;
  height: 100%;
}

/* 箭头样式 */
.custom-arrow { 
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 32px;
  height: 32px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  z-index: 1;
  cursor: pointer;
  transition: all 0.3s;
}

.custom-arrow:hover {
  background: rgba(0, 0, 0, 0.5);
}

.custom-arrow.prev {
  left: 10px;
}

.custom-arrow.next {
  right: 10px;
}

/* 轮播图指示点样式 */
.ant-carousel .slick-dots {
  position: absolute;
  bottom: 8px !important;
  z-index: 10 !important;
}

.ant-carousel .slick-dots li {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  transition: all 0.3s;
  cursor: pointer !important;
  margin: 0 4px;
  width: 16px !important;
  height: 3px !important;
}

.ant-carousel .slick-dots li.slick-active {
  background: white;
  width: 24px !important;
}

.ant-carousel .slick-dots li button {
  width: 100% !important;
  height: 100% !important;
  opacity: 0 !important;
  padding: 0 !important;
  cursor: pointer !important;
}

/* 添加点击效果 */
.ant-carousel .slick-dots li:hover {
  background: rgba(255, 255, 255, 0.5);
  transform: scale(1.1);
}

.ant-carousel .slick-dots li.slick-active:hover {
  transform: none;
}

/* 移除可能冲突的样式 */
.custom-dots {
  display: none !important;
}

.custom-dots li {
  display: inline-block;
  margin: 0 4px;
}

.custom-dots li button {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  border: none;
  padding: 0;
  cursor: pointer;
  transition: all 0.3s;
}

.custom-dots li.slick-active button {
  background: white;
  transform: scale(1.2);
}

/* 按钮基础样式 */
.btn-modern {
  transition: all 0.3s ease-out !important;
}

/* 主操作按钮样式（创建作品等） */
.btn-create {
  background: linear-gradient(to right, #3b82f6, #2563eb) !important;
  color: white !important;
  border-radius: 9999px !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1) !important;
}

.btn-create:hover {
  background: linear-gradient(to right, #2563eb, #1d4ed8) !important;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1) !important;
  transform: translateY(-2px) !important;
}

/* 返回按钮样式 */
.btn-back {
  background: rgba(255, 255, 255, 0.9) !important;
  color: #4b5563 !important;
  backdrop-filter: blur(8px) !important;
  border: 1px solid #f3f4f6 !important;
  border-radius: 9999px !important;
  padding: 0.375rem 1rem !important;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) !important;
  font-size: 0.875rem !important;
}

/* 返回按钮图标样式 */
.btn-back .anticon {
  margin-right: 0.25rem !important;
  transition: transform 0.3s !important;
}

/* 返回按钮悬浮时图标动画 */
.btn-back:hover .anticon {
  transform: translateX(-2px) !important;
}

/* 移除按钮点击波纹效果 */
.btn-back::after {
  display: none !important;
}

/* 轮播图箭头按钮样式 */
.carousel-arrow {
  background: rgba(0, 0, 0, 0.2) !important;
  backdrop-filter: blur(8px) !important;
  color: white !important;
  border-radius: 9999px !important;
  padding: 0.5rem !important;
  transition: all 0.3s !important;
}

.carousel-arrow:hover {
  background: rgba(0, 0, 0, 0.4) !important;
  transform: scale(1.1) !important;
}

/* 卡片内按钮样式 */
.ant-card-actions .btn-modern {
  border: none !important;
  outline: none !important;
  background: transparent !important;
  color: #64748b !important;
  font-size: 0.875rem !important;
  transition: color 0.2s ease-in-out !important;
  height: 100% !important;
  width: 100% !important;
  margin: 0 !important;
  padding: 12px 0 !important;
}

/* 移除按钮点击波纹效果 */
.ant-card-actions .btn-modern::after {
  display: none !important;
}

.ant-card-actions .ant-wave {
  display: none !important;
}

/* 卡片内按钮悬浮效果 */
.ant-card-actions .btn-modern:hover {
  color: #3b82f6 !important;
}

/* 卡片内编辑按钮特殊样式 */
.ant-card-actions .btn-edit:hover {
  color: #3b82f6 !important;
}

/* 卡片内删除按钮特殊样式 */
.ant-card-actions .btn-delete:hover {
  color: #ef4444 !important;
}

/* 卡片操作区域样式优化 */
.ant-card-actions {
  background: #f8fafc !important;
  border-top: 1px solid #e2e8f0 !important;
}

/* 移除卡片操作区域的内部分隔线 */
.ant-card-actions > li {
  margin: 0 !important;
  border: none !important;
}

.ant-card-actions > li:not(:last-child) {
  border-right: none !important;
}

/* 暗色模式适配 */
.dark .ant-card-actions {
  background: #1e293b !important;
  border-top: 1px solid #334155 !important;
}

.dark .ant-card-actions .btn-modern {
  color: #94a3b8 !important;
}

.dark .ant-card-actions .btn-modern:hover {
  color: #60a5fa !important;
}

.dark .ant-card-actions .btn-edit:hover {
  color: #60a5fa !important;
}

.dark .ant-card-actions .btn-delete:hover {
  color: #f87171 !important;
}

/* 暗色模式返回按钮样式 */
.dark .btn-back {
  background: rgba(31, 41, 55, 0.9) !important;
  color: #d1d5db !important;
  border-color: #374151 !important;
}

/* 自定义轮播图样式 */
.ant-carousel .slick-dots {
  position: absolute;
  bottom: 8px !important;
}

.ant-carousel .slick-dots li {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  transition: all 0.3s;
}

.ant-carousel .slick-dots li.slick-active {
  background: white;
  width: 24px !important;
}

.ant-carousel .slick-dots li button {
  display: none !important;
}

/* 自定义箭头样式 */
.carousel-arrow {
  width: 32px;
  height: 32px;
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(4px);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  transition: all 0.3s;
  opacity: 0;
  z-index: 2;
}

.ant-carousel:hover .carousel-arrow {
  opacity: 1;
}

.carousel-arrow:hover {
  background: rgba(0, 0, 0, 0.4);
}   
