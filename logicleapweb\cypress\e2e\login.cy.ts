describe('用户登录功能测试', () => {
  beforeEach(() => {
    // 访问登录页面
    cy.visit('/login');
  });

  it('应该显示登录表单', () => {
    // 验证登录表单元素存在
    cy.get('form').should('exist');
    cy.get('input[name="phone"]').should('exist');
    cy.get('input[name="password"]').should('exist');
    cy.get('button[type="submit"]').should('exist');
  });

  it('输入错误的凭据应显示错误消息', () => {
    // 输入错误的手机号和密码
    cy.get('input[name="phone"]').type('13800138000');
    cy.get('input[name="password"]').type('wrongpassword');
    cy.get('button[type="submit"]').click();

    // 验证错误消息显示
    cy.contains('用户名或密码错误').should('be.visible');
  });

  it('输入正确的凭据应成功登录', () => {
    // 使用测试账号
    cy.get('input[name="phone"]').type(Cypress.env('TEST_USER_PHONE'));
    cy.get('input[name="password"]').type(Cypress.env('TEST_USER_PASSWORD'));
    cy.get('button[type="submit"]').click();

    // 验证登录成功后重定向到首页
    cy.url().should('include', '/dashboard');
    
    // 验证用户信息已加载
    cy.get('[data-testid="user-profile"]').should('exist');
  });

  it('应该能够使用"记住我"功能', () => {
    // 勾选"记住我"
    cy.get('input[name="phone"]').type(Cypress.env('TEST_USER_PHONE'));
    cy.get('input[name="password"]').type(Cypress.env('TEST_USER_PASSWORD'));
    cy.get('input[type="checkbox"]').check();
    cy.get('button[type="submit"]').click();

    // 验证登录成功
    cy.url().should('include', '/dashboard');

    // 关闭浏览器会话
    cy.clearCookies();
    cy.reload();

    // 验证用户仍然登录
    cy.url().should('include', '/dashboard');
  });
}); 