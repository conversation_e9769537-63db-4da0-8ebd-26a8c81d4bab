/* 课程列表编辑弹窗样式 */

/* 删除确认弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
}

.delete-confirm-modal {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  width: 400px;
  max-width: 90vw;
}

.delete-confirm-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.delete-confirm-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.delete-confirm-header .close-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  color: #999;
  transition: color 0.2s;
}

.delete-confirm-header .close-btn:hover {
  color: #666;
}

.delete-confirm-content {
  padding: 20px 24px;
}

.delete-confirm-content p {
  margin: 0;
  color: #666;
  line-height: 1.5;
}

.delete-confirm-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 24px 20px;
  border-top: 1px solid #f0f0f0;
}

.delete-confirm-footer .cancel-btn {
  padding: 8px 16px;
  border: 1px solid #d9d9d9;
  background: white;
  color: #666;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.delete-confirm-footer .cancel-btn:hover {
  border-color: #40a9ff;
  color: #40a9ff;
}

.delete-confirm-footer .confirm-btn {
  padding: 8px 16px;
  border: 1px solid #ff4d4f;
  background: #ff4d4f;
  color: white;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.delete-confirm-footer .confirm-btn:hover {
  background: #ff7875;
  border-color: #ff7875;
}

/* 禁用状态样式 */
.delete-confirm-header .close-btn:disabled,
.delete-confirm-footer .cancel-btn:disabled,
.delete-confirm-footer .confirm-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

/* 删除加载动画 */
.delete-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 16px;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #ff4d4f;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
.course-list-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.course-list-modal {
  background: white;
  border-radius: 16px;
  width: 90%;
  max-width: 1200px;
  height: 85vh;
  max-height: 800px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

/* 头部样式 */
.course-list-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.course-list-title-section {
  display: flex;
  align-items: center;
  gap: 16px;
}

.course-list-title {
  font-size: 20px;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.course-list-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.course-list-settings-btn,
.course-list-add-btn {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.course-list-settings-btn {
  background: #f3f4f6;
  color: #6b7280;
}

.course-list-settings-btn:hover {
  background: #e5e7eb;
  color: #374151;
}

.course-list-add-btn {
  background: #3b82f6;
  color: white;
}

.course-list-add-btn:hover {
  background: #2563eb;
}

.course-list-close-btn {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  border: none;
  background: #f3f4f6;
  color: #6b7280;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.course-list-close-btn:hover {
  background: #e5e7eb;
  color: #374151;
}

/* 主要内容区域 */
.course-list-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* 左侧课程列表 */
.course-list-sidebar {
  width: 280px;
  background: #f9fafb;
  border-right: 1px solid #e5e7eb;
  overflow-y: auto;
}

.course-list-items {
  padding: 16px;
}

.course-list-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.course-list-item-content {
  display: flex;
  align-items: center;
  flex: 1;
  gap: 8px;
}

.course-list-item:hover {
  border-color: #3b82f6;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
}

.course-list-item-text {
  font-size: 14px;
  color: #374151;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 课程状态标注 */
.course-status-badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
  flex-shrink: 0;
}

.course-status-badge.published {
  background: #dcfce7;
  color: #166534;
  border: 1px solid #bbf7d0;
}

.course-status-badge.draft {
  background: #fef3c7;
  color: #92400e;
  border: 1px solid #fde68a;
}

.course-list-item-delete {
  width: 20px;
  height: 20px;
  border-radius: 4px;
  border: none;
  background: #f3f4f6;
  color: #6b7280;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  opacity: 0;
}

.course-list-item:hover .course-list-item-delete {
  opacity: 1;
}

.course-list-item-delete:hover {
  background: #fee2e2;
  color: #dc2626;
}

/* 右侧编辑区域 */
.course-list-edit-area {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* 系列课程封面 */
.course-series-cover {
  width: 200px;
  height: 120px;
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.course-series-cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.course-series-cover-placeholder {
  color: #6b7280;
  font-size: 14px;
  text-align: center;
}

/* 编辑表单 */
.course-edit-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.course-edit-field {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.course-edit-label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.course-edit-input {
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  color: #374151;
  transition: border-color 0.2s ease;
}

.course-edit-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.course-edit-input-group {
  display: flex;
  gap: 8px;
  align-items: center;
}

.course-edit-input-small {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  color: #374151;
  transition: border-color 0.2s ease;
}

.course-edit-input-small:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.course-edit-add-btn {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  border: 1px solid #d1d5db;
  background: white;
  color: #6b7280;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.course-edit-add-btn:hover {
  border-color: #3b82f6;
  color: #3b82f6;
}

/* 底部按钮 */
.course-list-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
}

.course-list-footer-left {
  display: flex;
  align-items: center;
}

.course-list-footer-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.course-list-btn {
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.course-list-btn-publish {
  background: #10b981;
  color: white;
}

.course-list-btn-publish:hover {
  background: #059669;
}

.course-list-btn-publish {
  background: #10b981;
  color: white;
}

.course-list-btn-publish:hover {
  background: #059669;
}

/* 发布课程按钮 */
.course-list-btn-publish-course {
  background: #52c41a;
  color: white;
}

.course-list-btn-publish-course:hover:not(:disabled) {
  background: #73d13d;
}

.course-list-btn-publish-course:disabled {
  background: #d9d9d9;
  color: #999;
  cursor: not-allowed;
}

.course-list-btn-exit {
  background: #6b7280;
  color: white;
}

.course-list-btn-exit:hover {
  background: #4b5563;
}

.course-list-btn-save {
  background: #3b82f6;
  color: white;
}

.course-list-btn-save:hover {
  background: #2563eb;
}

/* 设置按钮激活状态 */
.course-list-settings-btn.active {
  background: #3b82f6;
  color: white;
}

.course-list-settings-btn.active:hover {
  background: #2563eb;
}

/* 课程项目激活状态 */
.course-list-item.active {
  border-color: #3b82f6;
  background: #eff6ff;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.15);
}

/* 左侧课程列表空状态 */
.course-list-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  height: 100%;
  min-height: 300px;
}

.course-list-empty-icon {
  margin-bottom: 16px;
}

.course-list-empty-title {
  font-size: 18px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 8px 0;
}

.course-list-empty-description {
  font-size: 14px;
  color: #6b7280;
  margin: 0 0 24px 0;
  line-height: 1.5;
}

.course-list-empty-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.course-list-empty-btn:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

/* 加载状态 */
.course-list-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  color: #6b7280;
  font-size: 14px;
}

/* 右侧编辑区域空状态 */
.course-edit-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 400px;
  text-align: center;
  padding: 40px 20px;
}

.course-edit-empty-icon {
  margin-bottom: 20px;
}

.course-edit-empty-title {
  font-size: 20px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 12px 0;
}

.course-edit-empty-description {
  font-size: 16px;
  color: #6b7280;
  margin: 0;
  line-height: 1.5;
}

/* 新增表单元素样式 */
.course-edit-textarea {
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  color: #374151;
  font-family: inherit;
  resize: vertical;
  min-height: 100px;
  transition: border-color 0.2s ease;
}

.course-edit-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.course-edit-select {
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  color: #374151;
  background: white;
  cursor: pointer;
  transition: border-color 0.2s ease;
}

.course-edit-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.course-edit-unit {
  padding: 8px 12px;
  color: #6b7280;
  font-size: 14px;
  white-space: nowrap;
}

/* 课程状态标签 */
.course-status-label {
  display: inline-block;
  padding: 4px 12px;
  background: #f3f4f6;
  color: #6b7280;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
}

/* 视频时长显示 */
.course-video-duration {
  color: #374151;
  font-size: 14px;
  font-weight: 500;
}

/* 课程详细编辑界面 */
.course-detail-edit {
  padding: 24px;
  height: 100%;
  overflow-y: auto;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

/* 顶部区域 */
.course-detail-top {
  display: flex;
  gap: 20px;
  margin-bottom: 32px;
  background: white;
  padding: 24px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(226, 232, 240, 0.8);
}

.course-detail-cover {
  flex-shrink: 0;
  width: 140px;
  height: 100px;
  position: relative;
  margin-top: 50px;
}

/* 课程封面上传区域 */
.course-cover-upload-area {
  width: 100%;
  height: 100%;
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.course-cover-upload-area:hover {
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.course-cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center center;
  border-radius: 12px;
  transition: all 0.3s ease;
  position: absolute;
  top: 0;
  left: 0;
}

.course-cover-upload-area:hover .course-cover-image {
  filter: brightness(0.9);
}

.course-cover-placeholder {
  width: 100%;
  height: 100%;
  border: 2px dashed #cbd5e1;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #64748b;
  font-size: 13px;
  font-weight: 500;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  transition: all 0.3s ease;
}

.course-cover-upload-area:hover .course-cover-placeholder {
  border-color: #3b82f6;
  color: #3b82f6;
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
}

/* 一键上课按钮 */
.one-click-class-btn {
  width: 100%;
  margin-top: 12px;
  padding: 10px 16px;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

.one-click-class-btn:hover {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
}

.one-click-class-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(16, 185, 129, 0.3);
}

.course-detail-basic {
  flex: 1;
}

.course-detail-field {
  margin-bottom: 16px;
}

.course-detail-field label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  letter-spacing: 0.025em;
}

.course-detail-field input,
.course-detail-field textarea {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 10px;
  font-size: 14px;
  background: white;
  transition: all 0.3s ease;
  font-family: inherit;
}

.course-detail-field input:focus,
.course-detail-field textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

.course-detail-field textarea {
  resize: vertical;
  min-height: 80px;
}

/* 课程详细区域 */
.course-detail-section {
  margin-bottom: 28px;
  background: white;
  border: 1px solid rgba(226, 232, 240, 0.8);
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.course-detail-section:hover {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.course-detail-section h3 {
  margin: 0 0 20px 0;
  font-size: 18px;
  font-weight: 700;
  color: #1e293b;
  position: relative;
  padding-left: 16px;
}

.course-detail-section h3::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 20px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border-radius: 2px;
}

/* 课程资源 */
.course-resource-item {
  margin-bottom: 20px;
  padding: 16px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.course-resource-item:hover {
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  border-color: #3b82f6;
}

.resource-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.resource-header-right {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.resource-header-simple {
  margin-bottom: 12px;
}

.resource-header span,
.resource-header-right span,
.resource-header-simple span {
  font-size: 15px;
  font-weight: 600;
  color: #374151;
  letter-spacing: 0.025em;
}

/* 开关样式 */
.switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 28px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 28px;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.slider:before {
  position: absolute;
  content: "";
  height: 22px;
  width: 22px;
  left: 3px;
  bottom: 3px;
  background: white;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

input:checked + .slider {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
}

input:checked + .slider:before {
  transform: translateX(22px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.slider:hover {
  transform: scale(1.05);
}

/* 视频内容区域 */
.video-content-area {
  margin-top: 8px;
}

/* 视频信息区域 */
.video-info-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  align-items: center;
}

.video-preview {
  display: flex;
  justify-content: center;
  width: 100%;
}

.video-thumbnail {
  width: 100%;
  max-width: 300px;
  height: 180px;
  border-radius: 8px;
  object-fit: cover;
  background: #000;
}

/* 居中的视频名称 */
.video-name-centered {
  font-weight: 600;
  color: #1e293b;
  font-size: 14px;
  text-align: center;
  max-width: 100%;
  word-break: break-word;
}

/* 横向排列的重新上传按钮 */
.upload-btn-horizontal {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 10px 20px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border: none;
  border-radius: 10px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  min-width: 120px;
}

.upload-btn-horizontal:hover {
  background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

.upload-btn-horizontal span {
  white-space: nowrap;
}

/* 初始上传按钮样式 */
.upload-btn {
  padding: 10px 20px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border: none;
  border-radius: 10px;
  cursor: pointer;
  font-size: 13px;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.upload-btn:hover {
  background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

/* 视频上传区域 */
.video-upload-area {
  display: flex;
  gap: 16px;
  align-items: center;
  margin-top: 8px;
}

.video-placeholder {
  width: 140px;
  height: 90px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px dashed #cbd5e1;
  transition: all 0.3s ease;
  cursor: pointer;
}

.video-placeholder:hover {
  border-color: #3b82f6;
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  transform: scale(1.02);
}

.play-icon {
  font-size: 28px;
  color: #64748b;
  transition: all 0.3s ease;
}

.video-placeholder:hover .play-icon {
  color: #3b82f6;
  transform: scale(1.1);
}

/* 新的视频上传区域 - 垂直布局 */
.video-upload-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
  align-items: center;
  margin-top: 8px;
  padding: 20px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

/* 居中的视频占位符 */
.video-placeholder-centered {
  width: 200px;
  height: 120px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px dashed #cbd5e1;
  transition: all 0.3s ease;
  cursor: pointer;
}

.video-placeholder-centered:hover {
  border-color: #3b82f6;
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  transform: scale(1.02);
}

.video-placeholder-centered .play-icon {
  font-size: 32px;
  color: #64748b;
  transition: all 0.3s ease;
}

.video-placeholder-centered:hover .play-icon {
  color: #3b82f6;
  transform: scale(1.1);
}



/* 附件内容区域 */
.attachment-content-area {
  margin-top: 8px;
}

/* 附件信息区域 */
.attachment-info-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 16px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.attachment-preview {
  display: flex;
  align-items: center;
  gap: 12px;
}

.document-icon {
  font-size: 32px;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  border-radius: 8px;
}

.attachment-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.attachment-name {
  font-weight: 600;
  color: #1e293b;
  font-size: 14px;
}

.attachment-url {
  font-size: 12px;
  color: #64748b;
  word-break: break-all;
}

/* 附件区域 */
.attachment-area {
  display: flex;
  gap: 12px;
  align-items: center;
}

.attachment-area input {
  flex: 1;
}

/* 新的附件上传区域 */
.attachment-upload-section {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  margin-top: 8px;
}

/* 教学附件 */
.teaching-materials {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  margin-top: 8px;
}

/* 附件名称样式 */
.material-name {
  transition: all 0.2s ease;
  border-radius: 4px;
  padding: 2px 4px;
}

.material-name:hover {
  background-color: #f0f8ff;
  transform: translateY(-1px);
}

/* 空状态提示 */
.empty-materials-hint {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  background-color: #fafafa;
  margin-left: 12px;
  flex: 1;
}

/* 课程内容区域样式 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.add-content-section-btn {
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.add-content-section-btn:hover {
  background: #40a9ff;
  transform: translateY(-1px);
}

.teaching-info-card {
  background: #ffffff;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  margin-bottom: 16px;
  overflow: hidden;
  transition: all 0.2s ease;
}

.teaching-info-card:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e8e8e8;
}

.card-title {
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.remove-card-btn {
  background: #ff4d4f;
  color: white;
  border: none;
  border-radius: 4px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 16px;
  line-height: 1;
  transition: all 0.2s ease;
}

.remove-card-btn:hover {
  background: #ff7875;
  transform: scale(1.1);
}

.card-content {
  padding: 16px;
}

.input-group {
  margin-bottom: 16px;
}

.input-group:last-child {
  margin-bottom: 0;
}

.input-group label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.title-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  font-size: 14px;
  transition: all 0.2s ease;
}

.title-input:focus {
  border-color: #1890ff;
  outline: none;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.content-textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  font-size: 14px;
  resize: vertical;
  min-height: 80px;
  font-family: inherit;
  transition: all 0.2s ease;
}

.content-textarea:focus {
  border-color: #1890ff;
  outline: none;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.empty-content-hint {
  text-align: center;
  padding: 40px 20px;
  color: #999;
  background: #fafafa;
  border: 1px dashed #d9d9d9;
  border-radius: 8px;
}

.empty-content-hint p {
  margin: 0;
  font-size: 14px;
}

.add-material-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  border: 2px dashed #cbd5e1;
  border-radius: 12px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  cursor: pointer;
  font-size: 12px;
  font-weight: 600;
  color: #64748b;
  transition: all 0.3s ease;
}

.add-material-btn:hover {
  border-color: #3b82f6;
  color: #3b82f6;
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  transform: scale(1.05);
}

.material-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  text-align: center;
  padding: 8px;
  line-height: 1.3;
  color: #475569;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
  position: relative;
  flex-direction: column;
}

.material-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.material-item span {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
}

.remove-material-btn {
  position: absolute;
  top: 4px;
  right: 4px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: none;
  background: rgba(239, 68, 68, 0.9);
  color: white;
  font-size: 12px;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  opacity: 0;
  transform: scale(0.8);
}

.material-item:hover .remove-material-btn {
  opacity: 1;
  transform: scale(1);
}

.remove-material-btn:hover {
  background: rgba(220, 38, 38, 1);
  transform: scale(1.1);
}

/* 课程内容区域 */
.course-content-area {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 教学信息项 */
.teaching-info-item {
  padding: 16px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.teaching-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.content-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.content-item input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.content-item input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.add-content-btn {
  padding: 8px 16px;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  align-self: flex-start;
}

.add-content-btn:hover {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  transform: translateY(-1px);
}

.content-field {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.content-field label {
  font-size: 14px;
  color: #333;
}

.content-field input,
.content-field textarea {
  padding: 8px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 14px;
}

/* 一键开课 */
.one-key-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.one-key-item {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

/* 一键开课主项目 - 两端对齐 */
.one-key-item:first-child {
  justify-content: space-between;
}

/* 子选项 - 开关紧贴文字 */
.one-key-item:not(:first-child) {
  justify-content: flex-start;
  gap: 12px;
}

.one-key-item:hover {
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  border-color: #3b82f6;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.one-key-item span {
  font-size: 15px;
  font-weight: 600;
  color: #374151;
  letter-spacing: 0.025em;
}

/* 积木模板选择区域 */
.block-template-section {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-left: 16px;
}

.select-template-btn {
  padding: 8px 16px;
  background: #f8fafc;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #475569;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.select-template-btn:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
  color: #334155;
}

.selected-template-display {
  flex: 1;
  padding: 8px 16px;
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  min-width: 200px;
  max-width: 300px;
}

.selected-template-display span {
  font-size: 14px;
  font-weight: 400;
  color: #6b7280;
}



/* 能量输入区域 */
.energy-input-section {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 20px;
  margin-left: 20px;
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  border-radius: 10px;
  border: 1px solid #bfdbfe;
}

.energy-input-section span {
  font-size: 14px;
  font-weight: 600;
  color: #1e40af;
  white-space: nowrap;
}

.energy-input {
  flex: 1;
  padding: 8px 12px;
  border: 2px solid #bfdbfe;
  border-radius: 8px;
  font-size: 14px;
  background: white;
  transition: all 0.3s ease;
}

.energy-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 任务配置表单样式 */
.task-config-form {
  margin-top: 20px;
  padding: 20px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.task-config-row {
  display: flex;
  gap: 20px;
  margin-bottom: 16px;
}

.task-config-field {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.task-config-field.task-config-full {
  width: 100%;
  margin-bottom: 16px;
}

.task-config-field label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  display: flex;
  align-items: center;
  gap: 8px;
}

.item-number {
  background: #3b82f6;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
}

.task-config-field input,
.task-config-field textarea {
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s;
}

.task-config-field input:focus,
.task-config-field textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.task-config-field textarea {
  resize: vertical;
  min-height: 80px;
}

/* 自评项样式 */
.self-assessment-item {
  margin-bottom: 8px;
}

.self-assessment-item input {
  width: 100%;
}

.add-assessment-btn {
  width: 100%;
  padding: 12px;
  border: 2px dashed #d1d5db;
  background: transparent;
  border-radius: 6px;
  color: #6b7280;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.2s;
}

.add-assessment-btn:hover {
  border-color: #3b82f6;
  color: #3b82f6;
  background: rgba(59, 130, 246, 0.05);
}

/* 参考作品样式 */
.reference-works-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.select-works-btn {
  align-self: flex-start;
  padding: 8px 16px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.select-works-btn:hover {
  background: #2563eb;
}

.reference-works-grid {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.reference-work-item {
  width: 120px;
  height: 80px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  font-size: 12px;
  color: #6b7280;
  text-align: center;
  padding: 8px;
}

.reference-work-item.empty {
  border: 2px dashed #d1d5db;
  background: #f9fafb;
}

/* 参考资源样式 */
.reference-resources-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.reference-resources-grid {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.upload-resource-btn {
  width: 80px;
  height: 80px;
  border: 2px dashed #d1d5db;
  background: #f9fafb;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 12px;
  color: #6b7280;
  gap: 4px;
}

.upload-resource-btn:hover {
  border-color: #3b82f6;
  color: #3b82f6;
  background: rgba(59, 130, 246, 0.05);
}

.reference-resource-item {
  position: relative;
  width: 80px;
  height: 80px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  color: #374151;
  text-align: center;
  padding: 4px;
  word-break: break-all;
}

.remove-resource-btn {
  position: absolute;
  top: -6px;
  right: -6px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #ef4444;
  color: white;
  border: none;
  font-size: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.remove-resource-btn:hover {
  background: #dc2626;
}
