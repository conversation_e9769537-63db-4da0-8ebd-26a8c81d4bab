import auth from "@/lib/auth";
import { Modal } from "antd";
import { useState, useEffect } from "react";
import { userApi } from "@/lib/api/user";
import SlideCaptcha from './slide-captcha';
import { X } from 'lucide-react';
import { useSelector, useDispatch } from 'react-redux';
import { RootState, setUser } from '@/lib/store';
import request from '@/lib/request';
import { GetNotification } from 'logic-common/dist/components/Notification';
import RoleSelectionModalForNewUser from '../app/components/userAuth/RoleSelectionModalForNewUser';

const notification = GetNotification();

const ModifyPhoneModal = ({
  isOpen,
  onClose,
  currentPhone,
  onSuccess
}: {
  isOpen: boolean;
  onClose: () => void;
  currentPhone: string;
  onSuccess: () => void;
}) => {
  const [newPhone, setNewPhone] = useState('');
  const [smsCode, setSmsCode] = useState('');
  const [countdown, setCountdown] = useState(0);
  const [loading, setLoading] = useState(false);
  const [showCaptcha, setShowCaptcha] = useState(false);
  const [error, setError] = useState('');
  const [bindUserList, setBindUserList] = useState<any[]>([]);
  const [showBindModal, setShowBindModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState<any>(null);
  const [showReplaceConfirmModal, setShowReplaceConfirmModal] = useState(false);
  const [agreeToTerms, setAgreeToTerms] = useState(false);
  // 获取当前登录用户ID和注册类型
  const userId = useSelector((state: RootState) => state.user.userState.userId);
  const registerType = useSelector((state: RootState) => state.user.userState.registerType);
  const roleId = useSelector((state: RootState) => state.user.userState.roleId);
  // 新增状态：角色选择弹框
  const [showRoleModal, setShowRoleModal] = useState(false);
  const dispatch = useDispatch();

  useEffect(() => {
    console.log("ModifyPhoneModal - 组件初始化");
    console.log("当前用户信息 - 注册类型:", registerType, "角色ID:", roleId, "用户ID:", userId);
  }, [registerType, selectedUser, roleId, userId]);

  // 根据注册类型获取标题和描述文本
  const getBindModalTitle = () => {
    if (registerType === 'weixin') {
      return "绑定到目标账户";
    } else {
      return "绑定手机号";
    }
  };

  const getBindModalDescription = () => {
    if (registerType === 'weixin') {
      return "将微信绑定到选定的账户";
    } else {
      return `该手机号下已存在${bindUserList.length}个账号`;
    }
  };


  const handleSendCode = async () => {
    if (!newPhone) {
      setError('请输入手机号');
      return;
    }

    if (!/^1[3-9]\d{9}$/.test(newPhone)) {
      setError('请输入有效的手机号');
      return;
    }

    // 显示滑动验证
    setShowCaptcha(true);
  };

  // 滑动验证成功后发送验证码
  const handleCaptchaSuccess = async () => {
    try {
      let response;
      // 如果此时有当前号码说明是修改。没有则说明是新增绑定
      if (currentPhone) {
        response = await userApi.sendVerifyCodeForUpdatePhone(newPhone);
      } else {
        response = await userApi.sendVerifyCode(newPhone);
      }

      if (response.data && response.data.code === 200) {
        // 即使 code 为 200，也要检查 success 字段
        if (response.data.success === false) {
          // success 明确为 false 时表示业务失败
          notification.error(response.data.message || response.data.msg || '发送验证码失败');
          setError(response.data.message || response.data.msg || '发送验证码失败');
        } else {
          // code 为 200 且 success 不为 false 时才是成功
          notification.success('验证码已发送');
          setCountdown(60);
          const timer = setInterval(() => {
            setCountdown(prev => {
              if (prev <= 1) {
                clearInterval(timer);
                return 0;
              }
              return prev - 1;
            });
          }, 1000);
        }
      } else if (response.data && response.data.success === true) {
        // success 明确为 true 时也是成功
        notification.success('验证码已发送');
        setCountdown(60);
        const timer = setInterval(() => {
          setCountdown(prev => {
            if (prev <= 1) {
              clearInterval(timer);
              return 0;
            }
            return prev - 1;
          });
        }, 1000);
      } else {
        notification.error(response.data?.message || response.data?.msg || '发送验证码失败');
        setError(response.data?.message || response.data?.msg || '发送验证码失败');
      }
    } catch (error: any) {
      console.error('发送验证码失败:', error);
      const errorMsg = error.response?.data?.message || error.response?.data?.msg || '发送验证码失败，请稍后再试';
      notification.error(errorMsg);
      setError(errorMsg);
    } finally {
      setShowCaptcha(false);
    }
  };

  const handleSubmit = async () => {
    if (!newPhone || !smsCode) {
      notification.error('请填写完整信息');
      return;
    }

    try {
      setLoading(true);

      // 验证验证码
      try {
        // 专门用于验证验证码的请求
        const verifyResponse = await userApi.verifyResponse({
          phone: newPhone,
          code: smsCode
        });

        // 如果服务器返回错误
        if (verifyResponse.data && verifyResponse.data.code !== 200) {
          notification.error(verifyResponse.data.message || '验证码错误');
          setLoading(false);
          return;
        }
      } catch (error) {
        // 如果服务器没有提供验证验证码的接口，我们会继续执行绑定操作
        // 后端会在绑定过程中验证验证码
        console.log('验证码验证接口未找到，将在绑定操作中验证验证码');
      }

      let response;

      if (currentPhone) {
        // 修改手机号
        response = await userApi.updatePhone(newPhone, smsCode);
      } else {
        // 查询所有绑定
        const { data: res } = await userApi.findAllBindByPhone(newPhone);
        console.log("查询手机号绑定结果:", res);

        if (res.data && Array.isArray(res.data) && res.data.length > 0) {
          console.log("发现已绑定账号:", res.data);
          setBindUserList(res.data);
          setShowBindModal(true);
          setLoading(false);
          return;
        }

        // 如果手机号不存在其他账号，且当前是微信注册类型，检查roleId是否为999
        console.log("绑定手机号 - 用户类型:", registerType, "角色ID:", roleId);
        if (registerType === 'weixin' && roleId === 999) {
          console.log("检测到微信用户roleId为999，需要选择身份");

          // 正常绑定手机号
          response = await userApi.bindPhone({ phone: newPhone, code: smsCode });

          // 如果绑定成功
          if (response.data && response.data.code === 200) {
            notification.success('手机号绑定成功，请选择您的身份');

            // 关闭手机号绑定弹窗
            onClose();
            setNewPhone('');
            setSmsCode('');

            // 确保相关状态被重置
            setShowBindModal(false);
            setBindUserList([]);


            // 调用onSuccess回调，通知父组件绑定成功
            if (onSuccess) onSuccess();

            // 提前返回，不再执行后续代码
            return;
          }
        } else {
          // 正常绑定流程
          response = await userApi.bindPhone({ phone: newPhone, code: smsCode });
        }
      }

      if (response.data.code === 200) {
        notification.success(currentPhone ? '修改成功' : '绑定成功');
        onSuccess();
        onClose();
        setNewPhone('');
        setSmsCode('');
      } else {
        notification.error(response.data?.message || '操作失败，请重试');
      }
    } catch (error) {
      console.error('操作失败:', error);
      notification.error('操作失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 继续绑定
  const handleContinueBind = async () => {
    try {
      setLoading(true);


      const response = await userApi.bindPhone({ phone: newPhone, code: smsCode });
      if (response.data.code === 200) {
        notification.success('绑定成功');
        onSuccess();
        onClose();
        setShowBindModal(false);
        setNewPhone('');
        setSmsCode('');
      } else {
        notification.error(response.data?.message || '操作失败，请重试');
      }
    } catch (error) {
      notification.error('操作失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 选择/取消选择账号
  const handleSelectUser = (user: any) => {
    // 如果已选中，则取消选中；否则选中该账号
    if (selectedUser && selectedUser.id === user.id) {
      setSelectedUser(null);
    } else {
      setSelectedUser(user);
    }
  };

  // 确认替换绑定
  const handleConfirmReplace = async () => {
    // 判断是否是学生账号
    const isStudent = selectedUser?.role?.code === '1' || selectedUser?.role?.name === '学生';

    // 如果不是学生账号，需要检查协议同意状态
    if (!isStudent && !agreeToTerms) {
      notification.warning('请先阅读并同意服务协议和隐私政策');
      return;
    }

    try {
      setLoading(true);
      setShowReplaceConfirmModal(false);
      // 这里不进行验证码验证了，前面已经收到验证码点击确定绑定已经验证过了
      // 调用API进行替换绑定，传递被替换的用户ID
      const response = await userApi.bindPhone({
        phone: newPhone,
        code: smsCode,
        replaceUserId: selectedUser.id // 传递要替换的用户ID
      });

      if (response.data.code === 200) {
        // // 使用从Redux获取的用户ID
        // if (userId) {
        //   // 替换绑定成功后，更新绑定信息
        //   try {
        //     await userApi.updateBindingAfterRebind({
        //       oldUserId: selectedUser.id,
        //       newUserId: userId,
        //       phone: newPhone
        //     });
        //   } catch (updateError) {
        //     console.error('更新绑定信息失败:', updateError);
        //     // 这里不影响主流程，只记录错误
        //   }
        // }

        notification.success('替换绑定成功');
        onSuccess();
        onClose();
        setShowBindModal(false);
        setNewPhone('');
        setSmsCode('');
        setSelectedUser(null);
        setAgreeToTerms(false);
      } else {
        notification.error(response.data?.message || '修改失败，请重试');
      }
    } catch (error) {
      console.error('替换绑定失败:', error);
      notification.error('替换绑定失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 开始替换流程
  const handleReplaceAccount = () => {
    if (!selectedUser) {
      notification.warning('请先选择一个账号进行替换');
      return;
    }

    // 判断是否是非学生账号
    const isNonStudent = selectedUser.role?.code !== '1' && selectedUser.role?.name !== '学生';

    // 如果是非学生账号，显示确认弹窗
    if (isNonStudent) {
      setShowReplaceConfirmModal(true);
      setAgreeToTerms(false); // 重置协议同意状态
    } else {
      // 学生账号直接替换，无需同意协议
      handleConfirmReplace();
    }
  };



  // 更换手机号
  const handleChangePhone = () => {
    setShowBindModal(false);
    setNewPhone('');
    setSmsCode('');
    setSelectedUser(null);

  };

  // 绑定微信账号方法
  const handleBindWeixinToUserInfo = async () => {
    if (!selectedUser) {
      notification.warning('请先选择一个账号');
      return;
    }

    Modal.confirm({
      title: '确认绑定',
      content: '您正在将微信账号绑定到已有账号，绑定成功后当前微信账号信息将无法找回，确定要继续吗？',
      okText: '确认绑定',
      cancelText: '取消',
      onOk: async () => {
        try {
          setLoading(true);

          // 调用后端接口，传递当前用户ID和目标用户ID
          const response = await userApi.transferWeixinOpenid(selectedUser.id, smsCode, newPhone);
          // 使用类型断言解决类型错误
          console.log("zww：微信绑定手机号响应", response);
          const apiResponse = response as any;
          if (apiResponse.data && apiResponse.data.code === 200) {
            notification.success('微信绑定手机号成功');

            // 保存返回的登录令牌和用户ID
            const targetUserId = apiResponse.data.data.userId;
            const loginToken = apiResponse.data.data.loginToken;

            // 获取新账号的登录信息
            try {
              const loginResponse = await userApi.autoLoginByUserId(targetUserId, loginToken);
              // 使用类型断言处理返回结果
              const loginResult = loginResponse as any;
              console.log("zww：绑定目标账号的响应值", loginResult);

              // 关闭弹窗，在获取登录信息之后执行UI操作
              onSuccess();
              onClose();
              setShowBindModal(false);
              setSelectedUser(null);

              if (loginResult.data && loginResult.data.code === 200) {
                // 保token和用户信息
                const tokenData = loginResult.data.data;
                const userData = {
                  userId: tokenData.userInfo.id,
                  nickName: tokenData.userInfo.nickName || '',
                  avatarUrl: tokenData.userInfo.avatarUrl || '',
                  gender: tokenData.userInfo.gender || 0,
                  phone: tokenData.userInfo.phone || '',
                  isLoggedIn: true,
                  roleId: tokenData.userInfo.roleId
                };
                console.log('切换用户:', userData);

                localStorage.setItem('token', tokenData.token);
                localStorage.setItem('refreshToken', tokenData.refreshToken);
                localStorage.setItem('userId', tokenData.userId.toString());
                localStorage.setItem('user', JSON.stringify(userData));
                // const lcuser = localStorage.getItem('user')
                // alert("切换账号的lcuser:" + lcuser)
                // 如果有user对象，也保存它，同时判断新账号是否需要身份认证,后续刷新页面时会自动判断
                if (userData) {
                  if (userData.roleId === 999) {
                    localStorage.setItem('needRoleAuth', 'true')
                  }

                }

                // 延迟一下刷新页面，让通知能够显示
                setTimeout(() => {
                  window.location.reload();
                }, 500);
              } else {
                // 如果自动登录失败，提示用户手动登录
                notification.info('请重新登录进入新账号');
              }
            } catch (loginError) {
              console.error('自动登录失败:', loginError);
              // 提示用户重新登录
              notification.info('请重新登录进入新账号');
            }
          } else {
            notification.error(apiResponse.data?.message || apiResponse.data?.msg || '绑定失败，请稍后重试2');
          }
        } catch (error: any) {
          console.error('微信绑定失败:', error);
          notification.error(error.response?.data?.message || '绑定失败，请稍后重试1');
        } finally {
          setLoading(false);
        }
      }
    });
  };

  return (
    <>
      <Modal
        title={currentPhone ? "修改手机号" : "绑定手机号"}
        open={isOpen}
        onCancel={(e) => {
          e?.stopPropagation();
          console.log("取消绑定手机号弹窗");

          // 先关闭当前弹窗
          onClose();
          setNewPhone('');
          setSmsCode('');

          // 检查是否需要显示角色选择弹窗
          console.log("开始检查是否需要显示角色选择弹窗");

        }}
        onOk={handleSubmit}
        okText={currentPhone ? "确认修改" : "确认绑定"}
        cancelText="取消"
        confirmLoading={loading}
        centered
        maskClosable={false}  // 修改为false，防止点击蒙层意外关闭
        styles={{
          mask: {
            backgroundColor: 'rgba(0, 0, 0, 0.45)'
          }
        }}
      >
        <div className="space-y-4" onClick={e => e.stopPropagation()}>
          {
            currentPhone ? (
              <>
                <div>
                  <div className="text-sm text-gray-500 mb-1">当前手机号</div>
                  <div className="text-gray-400">{currentPhone}</div>
                </div>

                <div>
                  <div className="text-sm text-gray-500 mb-1">新手机号</div>
                  <input
                    type="text"
                    value={newPhone}
                    onChange={(e) => setNewPhone(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="请输入新手机号"
                  />
                </div>
              </>
            ) : (
              <div>
                <div className="text-sm text-gray-500 mb-1">手机号</div>
                <input
                  type="text"
                  value={newPhone}
                  onChange={(e) => setNewPhone(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="请输入手机号"
                />
              </div>
            )
          }
          <div>
            <div className="text-sm text-gray-500 mb-1">验证码</div>
            <div className="flex gap-2">
              <input
                type="text"
                value={smsCode}
                onChange={(e) => setSmsCode(e.target.value)}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="请输入验证码"
              />
              <button
                onClick={handleSendCode}
                disabled={countdown > 0}
                className={`px-4 py-2 rounded-lg text-white ${countdown > 0
                  ? 'bg-gray-300 cursor-not-allowed'
                  : 'bg-blue-500 hover:bg-blue-600'
                  }`}
              >
                {countdown > 0 ? `${countdown}s` : '获取验证码'}
              </button>
            </div>
          </div>


        </div>
      </Modal>

      {/* 绑定冲突弹窗 */}
      <Modal
        open={showBindModal}
        footer={null}
        onCancel={() => {
          setShowBindModal(false);
        }}
        width={1100}
        centered
      >
        <div className="flex flex-col items-center p-4">
          <h2 className="text-2xl font-semibold mb-2">{getBindModalTitle()}</h2>
          <div className="mb-2">{getBindModalDescription()}</div>
          <div className="mb-4">
            {registerType === 'weixin'
              ? selectedUser
                ? "您已选择一个账号进行绑定"
                : "请点击一个账号进行绑定"
              : bindUserList.length < 3
                ? selectedUser
                  ? "您已选择一个账号进行替换绑定"
                  : "您可以点击账号进行替换绑定，或直接点击下方继续绑定"
                : "同一个手机号下最多绑定三个账号，请点击一个账号进行替换绑定"}
          </div>
          <div className="flex gap-6 mb-6 flex-wrap justify-center">
            {bindUserList.map((user, idx) => (
              <div
                key={idx}
                className={`relative bg-blue-50 rounded-2xl border-4 ${selectedUser?.id === user.id ? 'border-blue-500 ring-4 ring-blue-300' : 'border-blue-300'} flex flex-col items-center px-8 py-6 min-w-[260px] max-w-[300px] shadow-md cursor-pointer transition-all hover:shadow-lg`}
                style={{ boxSizing: 'border-box' }}
                onClick={() => handleSelectUser(user)}
              >
                {/* 右上角角色标签 */}
                {user.role?.name && (
                  <div className="absolute -top-4 right-4 bg-white border-2 border-blue-300 rounded-xl px-4 py-1 text-xs text-gray-600 font-semibold shadow" style={{ zIndex: 2 }}>
                    {user.role.name}
                  </div>
                )}
                {/* 选中标记 */}
                {selectedUser?.id === user.id && (
                  <div className="absolute -top-4 -left-4 bg-blue-500 rounded-full w-8 h-8 flex items-center justify-center text-white shadow-md" style={{ zIndex: 2 }}>
                    ✓
                  </div>
                )}
                {/* 头像 */}
                <div className="w-20 h-20 rounded-full bg-gray-100 mb-3 flex items-center justify-center border-2 border-gray-300 overflow-hidden">
                  {user.avatarUrl ? (
                    <img src={user.avatarUrl} alt="avatar" className="w-full h-full object-cover rounded-full" />
                  ) : null}
                </div>

                {/* 昵称 */}
                <div className="text-2xl font-semibold mb-1 text-gray-800">{user.nickName}</div>
                {/* 地区 */}
                <div className="text-base text-gray-700 mb-1">
                  {user.schoolInfo?.province || user.student?.school?.province || ''}
                  {user.schoolInfo?.city || user.student?.school?.city || ''}
                  {user.schoolInfo?.district || user.student?.school?.district || ''}
                </div>
                {/* 学校名 */}
                <div className="text-3xl font-bold text-blue-900 mb-1">
                  {user.schoolInfo?.schoolName || user.student?.school?.schoolName || user.schoolName || ''}
                </div>

                {/* 点击替换提示 */}
                <div className="mt-2 text-sm text-blue-600">
                  {selectedUser?.id === user.id ? '点击取消选择' : '点击选择此账号'}
                </div>
              </div>
            ))}
          </div>

          {registerType === 'weixin' && (
            <div className="flex gap-8 w-full justify-center">
              <button
                className={`px-8 py-2 rounded-lg border transition ${!selectedUser
                  ? "border-gray-300 text-gray-400 bg-gray-100 cursor-not-allowed" // 禁用状态样式
                  : "border-blue-500 text-blue-600 bg-white hover:bg-blue-50" // 正常状态样式
                  }`}
                onClick={handleBindWeixinToUserInfo}
                disabled={!selectedUser}
              >
                绑定账号
              </button>
            </div>
          )}
          {registerType !== 'weixin' && (
            <div className="flex gap-8 w-full justify-center">
              <button
                className="px-8 py-2 rounded-lg border border-blue-500 text-blue-600 bg-white hover:bg-blue-50 transition"
                onClick={handleChangePhone}
              >更换手机号
              </button>

              {selectedUser ? (
                <div className="flex flex-col items-center">
                  <button
                    className="px-8 py-2 rounded-lg bg-green-500 text-white hover:bg-green-600 transition"
                    onClick={handleReplaceAccount}
                    disabled={loading}
                  >替换账号</button>
                </div>
              ) : (
                <div className="flex flex-col items-center">

                  <button
                    className={`px-8 py-2 rounded-lg ${bindUserList.length >= 3 ? 'bg-gray-400 cursor-not-allowed' : 'bg-blue-500 hover:bg-blue-600'} text-white transition`}
                    onClick={() => {
                      Modal.confirm({
                        title: '确认继续绑定',
                        content: '绑定成功后，当前密码会与当前已绑定的手机号密码同步。确定继续吗？',
                        okText: '确认绑定',
                        cancelText: '取消',
                        onOk: handleContinueBind
                      });
                    }}
                    disabled={loading || bindUserList.length >= 3}
                  >继续绑定</button>
                </div>
              )}
            </div>
          )}
        </div>
      </Modal>

      {/* 非学生账号替换确认弹窗 */}
      <Modal
        open={showReplaceConfirmModal}
        footer={null}
        onCancel={() => setShowReplaceConfirmModal(false)}
        width={520}
        centered
        styles={{
          mask: {
            backgroundColor: 'rgba(0, 0, 0, 0.45)'
          },
          content: {
            padding: 0,
            borderRadius: '16px',
            overflow: 'hidden'
          },
          body: {
            padding: 0,
            background: '#F5F9FF'
          }
        }}
      >
        <div className="bg-[#F5F9FF] p-8">
          <div className="text-center">
            <h2 className="text-3xl font-bold mb-6">替换绑定</h2>

            {/* 文字说明部分 - 添加红色边框 */}
            <div className="border  rounded-md p-5 mb-6 text-center">
              <p className="text-base leading-7">
                检查到您当前被替换账号为非学生账号，进行替换绑定将会彻底注销掉被替换的账号。
              </p>
              <p className="text-base leading-7 mt-4">
                请注意并考虑好您账号中资产，以及作品信息的风险再进行操作，或者前往该账号进行账号改绑。
              </p>
            </div>

            {/* 协议同意部分 - 无边框 */}
            <div className="rounded py-2 px-2 mb-6 flex items-center">
              <input
                type="checkbox"
                id="agree-terms"
                checked={agreeToTerms}
                onChange={() => setAgreeToTerms(!agreeToTerms)}
                className="w-5 h-5 mr-2 cursor-pointer"
                style={{ accentColor: '#4096ff' }}
              />
              <label htmlFor="agree-terms" className="text-sm cursor-pointer">
                我已经认真考虑并同意
                <a href="#" className="text-blue-500 hover:underline ">《平台服务协议》</a>
                和
                <a href="#" className="text-blue-500 hover:underline ">《个人信息保护政策》</a>
              </label>
            </div>

            <button
              className={`w-full py-3 rounded-lg ${!agreeToTerms ? 'bg-gray-300 text-gray-600 cursor-not-allowed' : 'bg-[#4B96F8] text-white hover:bg-blue-600'} transition font-medium text-lg`}
              onClick={handleConfirmReplace}
              disabled={loading || !agreeToTerms}
            >
              确定
            </button>
          </div>
        </div>
      </Modal>

      {/* 角色选择弹框 - 确保它渲染在DOM的最外层 */}
      {showRoleModal && (
        <RoleSelectionModalForNewUser
          onSuccess={() => {
            console.log("角色选择成功，关闭角色选择弹窗");
            setShowRoleModal(false);
            // 刷新用户信息
            if (userId) {
              console.log("开始获取最新用户信息");
              userApi.getUserInfo(userId).then(response => {
                if (response && response.code === 200 && response.data) {
                  console.log("获取到新的用户信息:", response.data);
                  const updatedUserInfo = {
                    userId: response.data.id,
                    nickName: response.data.nickName,
                    avatarUrl: response.data.avatarUrl,
                    gender: response.data.gender,
                    phone: response.data.phone,
                    introduction: response.data.introduction,
                    createTime: response.data.createTime,
                    isLoggedIn: true,
                    roleId: response.data.roleId,
                    role: response.data.role,
                    roles: response.data.roles
                  };

                  console.log("更新Redux中的用户信息");
                  dispatch(setUser(updatedUserInfo));

                  // 延迟刷新页面以确保用户信息已更新
                  console.log("准备刷新页面应用新的用户角色");
                  setTimeout(() => {
                    console.log("刷新页面");
                    window.location.reload();
                  }, 300);
                }
              }).catch(error => {
                console.error('获取用户信息失败:', error);
                // 即使获取失败，也刷新页面以应用可能的变更
                setTimeout(() => {
                  window.location.reload();
                }, 300);
              });
            } else {
              console.log("未找到用户ID，直接刷新页面");
              setTimeout(() => {
                window.location.reload();
              }, 300);
            }
          }}
          handleCloseRsmfnModal={() => {
            console.log("用户关闭角色选择弹窗");
            setShowRoleModal(false);
            // 用户关闭时也刷新页面
            setTimeout(() => {
              window.location.reload();
            }, 300);
          }}
        />
      )}

      <SlideCaptcha
        isOpen={showCaptcha}
        onClose={() => setShowCaptcha(false)}
        onSuccess={handleCaptchaSuccess}
      />
    </>
  );
};

export default ModifyPhoneModal;
